﻿using CashierAPI.Controllers;
using GamesEngine.Business;
using GamesEngine.Finance;
using GamesEngine.Games.Lotto;
using GamesEngine.PurchaseOrders;
using GamesEngine.Settings;
using GamesEngineMocks;
using GamesEngineTests.Unit_Tests.Games.Lotto;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using Puppeteer.EventSourcing;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using town.connectors;
using town.connectors.commons;
using town.connectors.drivers.fiero;
using Unit.Games.Tools;
using static GamesEngine.Business.WholePaymentProcessor;
using static GamesEngine.Finance.Currencies;
using static GamesEngine.Finance.PaymentChannels;
using static town.connectors.CustomSettings;
using Fragment_an_Authorization = Connectors.town.connectors.drivers.hades.Fragment_an_Authorization;

namespace GamesEngineTests.Unit_Tests.Business
{
    [TestClass]
    public class CashierTests
    {
        private string sqlStringConection = "";//"persistsecurityinfo=True;port=3306;Server=localhost;Database=cashier1;user id=*********;password=*********;SslMode=none";
        CashierMocks mocks = new CashierMocks();

        [TestMethod]
        public async Task SendMessagesToCashierAsync()
        {
            if (string.IsNullOrEmpty(sqlStringConection)) Assert.Inconclusive("MySQL string connection its required.");

            Company company = new Company();

            Integration.UseKafka = true;
            QueueInMemory queue = new QueueInMemory();
            Integration.Kafka = queue;

            Db bd = new Db();
            bd.MySQL = sqlStringConection;
            bd.DBSelected = DatabaseType.MySQL.ToString();
            Integration.Db = bd;

            DBDairy dbDairy = new DBDairy();
            dbDairy.DBSelected = DatabaseType.MySQL.ToString();
            Integration.DbDairy = dbDairy;

            DateTime now = new DateTime(2021, 09, 17, 9, 00, 00);

            Movements.Storage = new MovementStorageMySQL(sqlStringConection);
            Movements.Storage.CreateAuthorizationSequenceIfNotExists();
            Movements.Storage.CreateUserMovementsTableIfNotExists();
            CashierAPI.Settings.DbSelected = DatabaseType.MySQL.ToString();
            CashierAPI.Settings.MySQL = sqlStringConection;
            CashierAPI.CashierAPI.Initialize(mocks);
            CashierAPI.CashierAPI.Cashier.Release(0);

            Integration.MarkMovementsTableAsCreated(CODES.USD.ToString());
            Movements.Storage.CreateAuthorizationSequenceIfNotExists();
            Movements.Storage.CreateUserMovementsTableIfNotExists();

            var store = company.Sales.CreateStore(1, "Test Store");
            store.MakeCurrent();
            company.Accounting = new MockAccounting();
            var lotteries = company.Lotto900();
            var state = lotteries.GetOrCreateState("VA", "Virginia", now, "Bart");
            LotteryPick<Pick3> lottery = (LotteryPick<Pick3>)lotteries.GetOrCreateLottery(3, state);
            for (int i = 0; i <= 6; i++) lottery.Every(10, 20, i, now, "Bart");
            var state2 = lotteries.GetOrCreateState("GA", "Georgia", now, "Bart");
            LotteryPick<Pick3> lottery2 = (LotteryPick<Pick3>)lotteries.GetOrCreateLottery(3, state2);
            for (int i = 0; i <= 6; i++) lottery2.Every(9, 20, i, now, "Bart");

            var itIsThePresent = true;
            var schedule = lottery.GetScheduleId(1);
            schedule.Update(itIsThePresent, "Virginia morning", "0123456", 10, 20, now, "Game Admin");
            var schedule2 = lottery2.GetScheduleId(2);
            schedule2.Update(itIsThePresent, "Georgia morning", "0123456", 9, 20, now, "Game Admin");

            //var customer = company.CreateCustomer("TST1", PaymentProcessors.Agents.ZEUS);
            //var player = customer.Player;
            string states = "VA";
            string hours = "10:20 AM";
            string strIsPresentPlayerBeforeToCloseStore = "true";
            string strUseNextDate = "true";
            string dates = "09/17/2021";
            string withFireballs = "false";
            string numbers = "";
            string selectionMode = "MultipleInputSingleAmount";
            string pickNumber = "3";
            decimal ticketAmount = 1m;
            string[] includedNumbersForInputs = new string[] { "119", "110", "111", "112", "113", "114", "115", "116", "117", "118" };
            string gameType = "Boxed";

            //var manualLottoReward = company.Campaigns.CreateGiftCardCampaign(1, now, "Manual", Currencies.CODES.FP);
            //manualLottoReward.Give(true, customer, now, 10000m, "Gift", "N/A", Currencies.CODES.FP, store);

            var ticketPick3StraightProd = lotteries.GetOrCreateProductById(1);
            ticketPick3StraightProd.Description = "Ticket pick 3 straight";
            ticketPick3StraightProd.Price = 1;

            var domain = company.Sales.CreateDomain(itIsThePresent, 1, "localhost", Agents.TEST_BOOK_DGS);
            company.Sales.CurrentStore.Add(domain);
            ExcludeSubtickets excludedSubtickets = new ExcludeSubtickets();
            int ticketNumber = 10000;
            Random random = new Random();
            for (int index = 0; index < 10; index ++)
            {
                var customer = company.CreateCustomer($"TST{index}", Agents.TEST_BOOK_DGS);
                var player = customer.Player;
                NextDatesAccumulator nextDatesAccumulator = new NextDatesAccumulator(lotteries, domain, now, pickNumber, dates, states, hours, strIsPresentPlayerBeforeToCloseStore, strUseNextDate);
                var orderNumber = company.IdentityOrderNumber;
                OrderCart myOrder = company.NewOrder(customer, orderNumber, Coinage.Coin(CODES.USD), orderNumber);
                int indexNumber1 = random.Next(includedNumbersForInputs.Length);
                int indexNumber2 = random.Next(includedNumbersForInputs.Length);
                if (indexNumber1 == indexNumber2)
                {
                    if (indexNumber2 == includedNumbersForInputs.Length - 1) indexNumber2 = 0;
                    else indexNumber2++;
                }
                var includedNumbers = $"{includedNumbersForInputs[indexNumber1]},{includedNumbersForInputs[indexNumber2]}";
                ticketAmount = index % 2 == 0 ? 1.5m : 0.5m;
                Order order = company.CreateTicketOrder(lotteries, player, states, dates, withFireballs, numbers, selectionMode, ticketAmount, includedNumbers, excludedSubtickets, gameType, myOrder, ticketPick3StraightProd, nextDatesAccumulator, domain);
                company.AddOrders(myOrder);
                var lowBetId = company.IdentitytBetNumber;
                var highBetId = lowBetId + myOrder.CountOfItems - 1;

                ticketNumber++; //var ticketNumber = Integration.DGSZeusAccountingServices.DeductAmount(customer.AccountNumber, order.Total(), "lock description", $"lock{index}");
                company.PurchaseTickets(itIsThePresent, myOrder, now, ticketNumber, domain, lowBetId, 0, order.Number);
                var numbersAmount = includedNumbers.Split(',').Length;
                lotteries.CreateWagers(itIsThePresent, lowBetId, highBetId, 1, numbersAmount, ticketNumber, order.Number, now);
            }

            var drawDate = new DateTime(2021, 09, 17, 10, 20, 00);
            var drawDate2 = new DateTime(2021, 09, 18, 10, 20, 00);
            var drawDate3 = new DateTime(2021, 09, 19, 10, 20, 00);
            var drawDate4 = new DateTime(2021, 09, 20, 10, 20, 00);
            now = new DateTime(2021, 09, 17, 11, 00, 00);
            bool notifyEvent = false;
            var report = lottery.DrawPicks(drawDate, "111", now, itIsThePresent, "Bart", notifyEvent);
            report = lottery.ConfirmDraw(drawDate, now, "Bart", itIsThePresent);
            await DeserializeAsync(queue);

            /*lottery.Regrade(itIsThePresent, drawDate, now,  "Bart");
            lottery.ConfirmDraw(drawDate, now, "Bart", itIsThePresent);
            lottery.LotteryDraw(drawDate, "222", now, itIsThePresent, "Bart");
            lottery.ConfirmDraw(drawDate, now, "Bart", itIsThePresent);
            lottery.SetNoAction(itIsThePresent, drawDate, now, "Bart");
            lottery.ConfirmDraw(drawDate, now, "Bart", itIsThePresent);*/
        }

        async Task DeserializeAsync(QueueInMemory queue)
        {
            var countMessages = queue.Count(queue.TopicForFragmentsCreation);
            for (int i = 0; i < countMessages; i++)
            {
                var msg = queue.Dequeue(queue.TopicForFragmentsCreation).ToString();
                DeserializeTopicForFragmentsCreation(msg);
            }

            countMessages = queue.Count(queue.TopicForDeposits);
            for (int i = 0; i < countMessages; i++)
            {
                var msg = queue.Dequeue(queue.TopicForDeposits).ToString();
                DeserializeTopicForDeposits(msg);
            }

            countMessages = queue.Count(queue.TopicForWithdrawals);
            for (int i = 0; i < countMessages; i++)
            {
                var msg = queue.Dequeue(queue.TopicForWithdrawals).ToString();
                DeserializeTopicForWithdrawals(msg);
            }

            countMessages = queue.Count(queue.TopicForFragmentPaymentsForAll);
            //Assert.AreEqual(2, countMessages);
            for (int i = 0; i < countMessages; i++)
            {
                var msg = queue.Dequeue(queue.TopicForFragmentPaymentsForAll).ToString();
                DeserializeFragmentPayments(msg);
            }

            countMessages = queue.Count(queue.TopicForFragmentPaymentsForWinners);
            //Assert.AreEqual(2, countMessages);
            for (int i = 0; i < countMessages; i++)
            {
                var msg = queue.Dequeue(queue.TopicForFragmentPaymentsForWinners).ToString();
                DeserializeFragmentPayments(msg);
            }

            countMessages = queue.Count(queue.TopicForMovements);
            for (int i = 0; i < countMessages; i++)
            {
                var msg = queue.Dequeue(queue.TopicForMovements).ToString();
                var consumer = new FakeMovementsConsumer();
                consumer.DeserializeTopicForMovements(msg);
            }

            var tasks = new List<Task>();
            for (int j = 0; j < 10; j++)
            {
                var index = j;
                tasks.Add(Task.Run(() => {
                    var count = queue.Count($"{queue.TopicForMovements}_{index}");
                    var consumer = new FakeMovementsConsumer();
                    for (int i = 0; i < count; i++)
                    {
                        var msg = queue.Dequeue($"{queue.TopicForMovements}_{index}").ToString();
                        consumer.DeserializeTopicForMovements(msg);
                    }
                }
                ));
            }
            await Task.WhenAll(tasks);

            countMessages = queue.Count(queue.TopicForDeposits);
            for (int i = 0; i < countMessages; i++)
            {
                var msg = queue.Dequeue(queue.TopicForDeposits).ToString();
                DeserializeTopicForDeposits(msg);
            }

            countMessages = queue.Count(queue.TopicForWithdrawals);
            for (int i = 0; i < countMessages; i++)
            {
                var msg = queue.Dequeue(queue.TopicForWithdrawals).ToString();
                DeserializeTopicForWithdrawals(msg);
            }

            countMessages = queue.Count(queue.TopicForFragmentPaymentsForAll);
            for (int i = 0; i < countMessages; i++)
            {
                var msg = queue.Dequeue(queue.TopicForFragmentPaymentsForAll).ToString();
                DeserializeFragmentPayments(msg);
            }

            countMessages = queue.Count(queue.TopicForFragmentPaymentsForWinners);
            for (int i = 0; i < countMessages; i++)
            {
                var msg = queue.Dequeue(queue.TopicForFragmentPaymentsForWinners).ToString();
                DeserializeFragmentPayments(msg);
            }

            countMessages = queue.Count(queue.TopicForMovements);
            for (int i = 0; i < countMessages; i++)
            {
                var msg = queue.Dequeue(queue.TopicForMovements).ToString();
                var consumer = new FakeMovementsConsumer();
                consumer.DeserializeTopicForMovements(msg);
            }

            tasks = new List<Task>();
            for (int j = 0; j < 10; j++)
            {
                var index = j;
                tasks.Add(Task.Run(() => {
                    var count = queue.Count($"{queue.TopicForMovements}_{index}");
                    var consumer = new FakeMovementsConsumer();
                    for (int i = 0; i < count; i++)
                    {
                        var msg = queue.Dequeue($"{queue.TopicForMovements}_{index}").ToString();
                        consumer.DeserializeTopicForMovements(msg);
                    }
                }
                ));
            }
            await Task.WhenAll(tasks);
            int a = 0;
        }


        [TestMethod]
        public void SendMessagesToKenoCashier()
        {
            if (string.IsNullOrEmpty(sqlStringConection)) Assert.Inconclusive("MySQL string connection its required.");

            CurrenciesTest.AddCurrencies();

            Integration.UseKafka = true;
            QueueInMemory queue = new QueueInMemory();
            Integration.Kafka = queue;

            Db bd = new Db();
            bd.MySQL = sqlStringConection;
            bd.DBSelected = DatabaseType.MySQL.ToString();
            Integration.Db = bd;

            DBDairy dbDairy = new DBDairy();
            dbDairy.DBSelected = DatabaseType.MySQL.ToString();
            Integration.DbDairy = dbDairy;

            DateTime now = new DateTime(2019, 04, 17, 9, 0, 0);

            Movements.Storage = new MovementStorageMySQL(sqlStringConection);
            Movements.Storage.CreateAuthorizationSequenceIfNotExists();
            CashierAPI.Settings.DbSelected = DatabaseType.MySQL.ToString();
            CashierAPI.Settings.MySQL = sqlStringConection;
            CashierAPI.CashierAPI.Initialize(mocks);
            CashierAPI.CashierAPI.Cashier.Release(0);
            Integration.MarkMovementsTableAsCreated(CODES.USD.ToString());
            Movements.Storage.CreateAuthorizationSequenceIfNotExists();
            Movements.Storage.CreateUserMovementsTableIfNotExists();

            var itIsThePresent = true;
           
            decimal ticketAmount = 0.25m;
            List<int> selection = new List<int> { 1, 2, 3, 4, 5, 6, 7, 8, 9, 10 };
            bool multiplier = false;
            bool bulleye = false;
            Product kenoSingleProd, kenoWithMultiplierAndBulleyeProd, kenoWithMultiplierProd, kenoWithBullEyeProd = null;

            Company company = new Company();
            company.Sales.CreateStore(1, "Test Store").MakeCurrent();
            company.Accounting = new MockAccounting();
            var lotto900 = company.Lotto900();

            kenoSingleProd = lotto900.GetOrCreateProductById(9);
            kenoSingleProd.Description = "Simple Keno Ticket.";
            kenoSingleProd.Price = 1;
            kenoWithMultiplierProd = lotto900.GetOrCreateProductById(10);
            kenoWithMultiplierProd.Description = "Keno Ticket with multiplier.";
            kenoWithMultiplierProd.Price = 0.25m;
            kenoWithBullEyeProd = lotto900.GetOrCreateProductById(11);
            kenoWithBullEyeProd.Description = "Keno Ticket with bulleye.";
            kenoWithBullEyeProd.Price = 0.25m;
            kenoWithMultiplierAndBulleyeProd = lotto900.GetOrCreateProductById(12);
            kenoWithMultiplierAndBulleyeProd.Description = "Keno Ticket with bulleye.";
            kenoWithMultiplierAndBulleyeProd.Price = 0.25m;

            var domain = company.Sales.CreateDomain(false, 1, "localhost", Agents.INSIDER);
            var betRangesKeno = lotto900.BetRangesKeno;
            betRangesKeno.Add(now, domain, 0.75m, 0.25m, "Admin");

            var presetKeno = lotto900.PresetBetAmountsForKeno;
            presetKeno.Add(0.5m, "Admin", now);

            LotteryKeno lotteryKeno = lotto900.GetKeno();
            lotteryKeno.Every("*", "[0,10,20,30,40,50]", "*", now, "Admin");


            var player = company.GetOrCreateCustomerById("NO562430373").Player;
            var customer = company.CustomerByPlayer(player);
            var ticketNumber = *********;
            var tempDate = now;
            for (int index = 0; index < 5; index++)
            {
                var nextDraw = lotto900.NextPendingAndNoRegradedSchedulesAtForKeno(tempDate);
                int orderNumber = company.IdentityOrderNumber;
                OrderCart myOrder = company.NewOrder(customer, orderNumber, Coinage.Coin(Currencies.CODES.USD), orderNumber);
                Order order = company.CreateKenoOrder(player, selection, multiplier, bulleye, ticketAmount, nextDraw.IdPrefix, tempDate, myOrder, domain, kenoSingleProd, kenoWithMultiplierProd, kenoWithBullEyeProd, kenoWithMultiplierAndBulleyeProd);
                company.AddOrders(myOrder);
                var lowBetId = index + 1;
                var highBetId = lowBetId + myOrder.CountOfItems - 1;
                company.PurchaseTickets(itIsThePresent, myOrder, tempDate, ticketNumber + index, domain, lowBetId, 0, order.Number);
                lotto900.CreateWagers(itIsThePresent, lowBetId, highBetId, 1, myOrder.CountOfItems * 2, ticketNumber + index, order.Number, tempDate);
                tempDate = tempDate.AddMinutes(10);
            }

            multiplier = true;
            bulleye = true;
            var player2 = company.GetOrCreateCustomerById("*********").Player;
            var customer2 = company.CustomerByPlayer(player2);
            ticketNumber = *********;
            tempDate = now;
            for (int index = 0; index < 5; index++)
            {
                var nextDraw = lotto900.NextPendingAndNoRegradedSchedulesAtForKeno(tempDate);
                int orderNumber = company.IdentityOrderNumber;
                OrderCart myOrder = company.NewOrder(customer2, orderNumber, Coinage.Coin(Currencies.CODES.USD), orderNumber);
                Order order = company.CreateKenoOrder(player2, selection, multiplier, bulleye, ticketAmount, nextDraw.IdPrefix, tempDate, myOrder, domain, kenoSingleProd, kenoWithMultiplierProd, kenoWithBullEyeProd, kenoWithMultiplierAndBulleyeProd);
                company.AddOrders(myOrder);
                var lowBetId = index + 1;
                var highBetId = lowBetId + myOrder.CountOfItems - 1;
                company.PurchaseTickets(itIsThePresent, myOrder, tempDate, ticketNumber + index, domain, lowBetId, 0, order.Number);
                lotto900.CreateWagers(itIsThePresent, lowBetId, highBetId, 1, myOrder.CountOfItems * 2, ticketNumber + index, order.Number, tempDate);
                tempDate = tempDate.AddMinutes(10);
            }

            var drawDate = now.AddMinutes(10);
            var report = lotteryKeno.DrawKeno(KenoNextDraw.Date2PrefixId(drawDate), new int[] { 6, 16, 26, 36, 46, 56, 66, 76, 50, 15, 25, 35, 45, 55, 65, 75, 40, 14, 24, 34 }, 2, 6, now, itIsThePresent, "Bart");
            report = lotteryKeno.ConfirmDraw(drawDate, now, "Bart");

            drawDate = drawDate.AddMinutes(10);
            report = lotteryKeno.DrawKeno(KenoNextDraw.Date2PrefixId(drawDate), new int[] { 6, 16, 26, 36, 46, 56, 66, 76, 50, 15, 25, 35, 45, 55, 65, 75, 40, 14, 24, 34 }, 2, 6, now, itIsThePresent, "Bart");
            report = lotteryKeno.ConfirmDraw(drawDate, now, "Bart");

            drawDate = drawDate.AddMinutes(10);
            report = lotteryKeno.DrawKeno(KenoNextDraw.Date2PrefixId(drawDate), new int[] { 11, 16, 26, 36, 46, 56, 66, 76, 50, 15, 25, 35, 45, 55, 65, 75, 40, 14, 24, 34 }, 2, 11, now, itIsThePresent, "Bart");
            report = lotteryKeno.ConfirmDraw(drawDate, now, "Bart");

            var countMessages = queue.Count(queue.TopicForFragmentsCreation);
            //Assert.AreEqual(2, countMessages);
            for (int i = 0; i < countMessages; i++)
            {
                var msg = queue.Dequeue(queue.TopicForFragmentsCreation).ToString();
                DeserializeTopicForFragmentsCreation(msg);
            }

            countMessages = queue.Count(queue.TopicForDeposits);
            //Assert.AreEqual(2, countMessages);
            for (int i = 0; i < countMessages; i++)
            {
                var msg = queue.Dequeue(queue.TopicForDeposits).ToString();
                DeserializeTopicForDeposits(msg);
            }

            countMessages = queue.Count(queue.TopicForWithdrawals);
            //Assert.AreEqual(1, countMessages);
            for (int i = 0; i < countMessages; i++)
            {
                var msg = queue.Dequeue(queue.TopicForWithdrawals).ToString();
                DeserializeTopicForWithdrawals(msg);
            }

        }

        void DeserializeTopicForFragmentsCreation(string msg) //it must be equal to FragmentCreationConsumer
        {
            FragmentsCreationBody fragmentMessage = new FragmentsCreationBody(msg);

            Integration.MarkFragmentsTableAsCreated(fragmentMessage.AtAddress);
            Integration.MarkMovementsTableAsCreated(fragmentMessage.AtAddress, Coinage.Coin(fragmentMessage.CurrencyCode));
            
            bool wasPayedInDollars = fragmentMessage.CurrencyCode == CODES.USD.ToString();
            PostFreeFormWagerCollectionSuccessResponse result;
            var coin = Coinage.Coin(fragmentMessage.CurrencyCode);
            if (wasPayedInDollars)
            {
                PaymentChannels.DepositAndLock(DateTime.Now, CashierAPI.CashierAPI.Cashier, fragmentMessage);
                var fragments = Array.ConvertAll(fragmentMessage.Fragments, new Converter<Fragment, PostFreeFormWager>(ApiController.FragmentToWager));
                PaymentProcessor paymentProcessor = WholePaymentProcessor.Instance().SearchFragmentProcessor(typeof(town.connectors.drivers.fiero.Fragment_an_Authorization));
                using (RecordSet recordSet = paymentProcessor.GetRecordSet())
                {
                    recordSet.SetParameter("actor", CashierAPI.CashierAPI.Cashier);
                    recordSet.SetParameter("customerId", fragmentMessage.AtAddress);
                    recordSet.SetParameter("ticketNumber", fragmentMessage.AuthorizationNumber);
                    recordSet.SetParameter("wagers", fragments);
                    result = paymentProcessor.Execute<PostFreeFormWagerCollectionSuccessResponse>(DateTime.Now, recordSet);
                }
            }
            else
            {
                var wagers = Array.ConvertAll(fragmentMessage.Fragments, new Converter<Fragment, PostFreeFormWager>(ApiController.FragmentToWager));
                var paymentProcessor = WholePaymentProcessor.Instance().SearchFragmentProcessor();
                result = Fragment_an_Authorization.CreateFakePostFreeFormWagerCollectionResponse(wagers, 1);

                var numbers = result.Wagers.Select(x => int.Parse(x.WagerNumber));
                int theLowestFragmentNumber = numbers.Min();
                int theHighestFragmentNumber = numbers.Max();

                paymentProcessor = WholePaymentProcessor.Instance().SearchFragmentProcessor(typeof(FragmentAnAuthorizationAppended));
                using (RecordSet recordSet = paymentProcessor.GetRecordSet())
                {
                    recordSet.SetParameter("actor", CashierAPI.CashierAPI.Cashier);
                    recordSet.SetParameter("atAddress", fragmentMessage.AtAddress);
                    recordSet.SetParameter("authorizationNumber", fragmentMessage.AuthorizationNumber);
                    recordSet.SetParameter("theLowestFragmentNumber", theLowestFragmentNumber);
                    recordSet.SetParameter("theHighestFragmentNumber", theHighestFragmentNumber);
                    recordSet.SetParameter("fragmentsChunks", fragmentMessage.FragmentsChunks);
                    paymentProcessor.Execute<object>(DateTime.Now, recordSet);
                }
            }

            var wagerNumbers = result.Wagers.Select(x => int.Parse(x.WagerNumber));
            int theLowestWagerNumber = wagerNumbers.Min();
            int theHighestWagerNumber = wagerNumbers.Max();

            FragmentCreationResponse fragmentLockResponse = new FragmentCreationResponse(
                fragmentMessage.TheLowestBetId,
                fragmentMessage.TheHighestBetId,
                theLowestWagerNumber,
                theHighestWagerNumber,
                fragmentMessage.AuthorizationNumber,
                fragmentMessage.OrderNumber
            );
        }
        class FakeMovementsConsumer
        {
            static long elapsed = 0;
            static int messageCount = 0;
            static int counter = 0;
            static SemaphoreSlim _block = new SemaphoreSlim(10);
            MovementsDeserialitor movementsDeserialitor = new MovementsDeserialitor();

            public FakeMovementsConsumer()
            {
                PrepareDataTables();
            }

            internal void DeserializeTopicForMovements(string msg) //it must be equal to MovementsConsumer 
            {
                /*_block.Wait();
                var timer = new Stopwatch();

                try
                {
                    counter = Interlocked.Increment(ref counter);
                    Debug.WriteLine($"Current count: {counter}");

                    timer.Start();
                    int whosId = Users.NO_USER;
                    movementsDeserialitor.Separate(msg);

                    if (movementsDeserialitor.Who != Users.ME)
                    {
                        whosId = Movements.Storage.InsertUserIfNotExists(movementsDeserialitor.Store, movementsDeserialitor.Who);
                    }

                    Movements.Storage.SaveBalanceMovements(true, whosId, movementsDeserialitor.Movements);
                    movementsDeserialitor.Clear();
                }
                finally
                {
                    timer.Stop();
                    elapsed += timer.ElapsedMilliseconds;
                    messageCount++;
                    Debug.WriteLine($"messageCount: {messageCount} elapsed: {elapsed}");
                    counter = Interlocked.Decrement(ref counter);
                    _block.Release();
                }*/

                int whosId = Users.NO_USER;
                movementsDeserialitor.Separate(msg);
                if (movementsDeserialitor.Who != Users.ME)
                {
                    whosId = Movements.Storage.InsertUserIfNotExists(movementsDeserialitor.StoreId, movementsDeserialitor.Who);
                }

                Movements.Storage.SaveBalanceMovements(true, whosId, movementsDeserialitor.Movements, movementTableByAccount, movementTableByCurrency);
                movementsDeserialitor.Clear();
            }

            DataTable movementTableByAccount = new DataTable();
            DataTable movementTableByCurrency = new DataTable();
            void PrepareDataTables()
            {
                movementTableByAccount.Columns.Add("DAY", typeof(string));
                movementTableByAccount.Columns.Add("SOURCE", typeof(int));
                movementTableByAccount.Columns.Add("MOVEMENT", typeof(string));
                movementTableByAccount.Columns.Add("AMOUNT", typeof(decimal));
                movementTableByAccount.Columns.Add("NEWBALANCE", typeof(decimal));
                var dataColumn = movementTableByAccount.Columns.Add("WHO", typeof(int));
                dataColumn.AllowDBNull = true;
                movementTableByAccount.Columns.Add("DOCUMENTNUMBER", typeof(string));
                movementTableByAccount.Columns.Add("STORE", typeof(int));
                movementTableByAccount.Columns.Add("REFERENCE", typeof(string));
                movementTableByAccount.Columns.Add("CONCEPT", typeof(string));
                movementTableByAccount.Columns.Add("NEWLOCKBALANCE", typeof(decimal));
                movementTableByAccount.Columns.Add("ACCOUNT_NUMBER", typeof(string));

                movementTableByCurrency.Columns.Add("DAY", typeof(string));
                movementTableByCurrency.Columns.Add("ATADDRESS", typeof(string));
                movementTableByCurrency.Columns.Add("SOURCE", typeof(int));
                movementTableByCurrency.Columns.Add("CURRENCY", typeof(string));
                movementTableByCurrency.Columns.Add("MOVEMENT", typeof(string));
                movementTableByCurrency.Columns.Add("AMOUNT", typeof(decimal));
                movementTableByCurrency.Columns.Add("NEWBALANCE", typeof(decimal));
                dataColumn = movementTableByCurrency.Columns.Add("WHO", typeof(int));
                dataColumn.AllowDBNull = true;
                movementTableByCurrency.Columns.Add("DOCUMENTNUMBER", typeof(string));
                movementTableByCurrency.Columns.Add("STORE", typeof(int));
                movementTableByCurrency.Columns.Add("REFERENCE", typeof(string));
                movementTableByCurrency.Columns.Add("CONCEPT", typeof(string));
                movementTableByCurrency.Columns.Add("NEWLOCKBALANCE", typeof(decimal));
                movementTableByCurrency.Columns.Add("ACCOUNT_NUMBER", typeof(string));
            }
        }


        void DeserializeTopicForDeposits(string msg) //it must be equal to DepositsConsumer 
        {
            string[] messages = KafkaMessages.Split(msg);
            foreach (string codedMessage in messages)
            {
                string command = "";
                int authorization = 0;

                DepositMessage message = new DepositMessage(codedMessage);
                PaymentChannels.Deposit(true, DateTime.Now, CashierAPI.CashierAPI.Cashier, message, out authorization);
            }
        }

        void DeserializeTopicForWithdrawals(string msg) //it must be equal to WithdrawalsConsumer 
        {
            string[] messages = KafkaMessages.Split(msg);
            foreach (string codedMessage in messages)
            {
                string command = "";
                int authorization = 0;
                WithdrawMessage message = new WithdrawMessage(codedMessage);
                PaymentChannels.WithDraw(DateTime.Now, CashierAPI.CashierAPI.Cashier, message, out authorization);
            }
        }

        void DeserializeFragmentPayments(string comp) //it must be equal to FragmentPaymentsConsumer
        {
            string msg = FragmentPaymentCompressor.Expand(comp);

            FragmentPaymentMessages payFragments = new FragmentPaymentMessages(msg);
            GradeFreeFormWagersResponse finalResponse = new GradeFreeFormWagersResponse();

            string who = payFragments.Owner;
            int storeId = payFragments.StoreId;

            foreach (var currencyCode in payFragments.Currencies())
            {
                var fragments = payFragments.MessagesBy(currencyCode);
                PaymentProcessor paymentProcessor = WholePaymentProcessor.Instance().SearchGradeProcessorBy(typeof(town.connectors.drivers.fiero.Grade));
                using (RecordSet recordSet = paymentProcessor.GetRecordSet())
                {
                    recordSet.SetParameter("who", who);
                    recordSet.SetParameter("payFragments", payFragments);
                    recordSet.SetParameter("gradeFreeFormWagers", fragments);
                    recordSet.SetParameter("actor", CashierAPI.CashierAPI.Cashier);
                    var temp = paymentProcessor.Execute<GradeFreeFormWagersResponse>(DateTime.Now, recordSet);
                    finalResponse.Merge(temp);
                }
            }

            if (finalResponse.Wagers != null && finalResponse.Wagers.Length > 0)
            {
                FragmentPaymentsWithProblemsResponse fragmentsWithProblems = new FragmentPaymentsWithProblemsResponse();
                foreach (var wager in finalResponse.Wagers)
                {
                    if (!wager.IsValidTicketNumber)
                    {
                        fragmentsWithProblems.Add(wager.TicketNumber, wager.WagerNumber);
                    }
                }

                if (fragmentsWithProblems.HasItems()) Integration.Kafka.Send(true, Integration.Kafka.TopicForFragmentPaymentsCallback(storeId), fragmentsWithProblems);
            }
        }
    }
}