﻿using GamesEngine;
using GamesEngine.Business;
using GamesEngine.Domains;
using GamesEngine.Exchange;
using GamesEngine.Exchange.Batch;
using GamesEngine.Finance;
using GamesEngine.PurchaseOrders;
using GamesEngine.Settings;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using System;
using System.Collections.Generic;
using System.Linq;
using Unit.Games.Tools;
using static GamesEngine.Exchange.BalanceMessage;
using GamesEngine.Domains;
using static GamesEngineTests.Custodian.GuardianTest;

using static GamesEngine.Exchange.Batch.BatchSet;
using GamesEngineTests.Unit_Tests.Games.Lotto;
using town.connectors.drivers;
using static GamesEngine.Business.WholePaymentProcessor;
using static GamesEngine.Business.WholePaymentMethods;
using GamesEngineTests.Custodian;
using System.Reflection;

namespace GamesEngineTests.Exchange
{

	[TestClass]
	public class BatchTest
	{
		[TestInitialize]
		public void Initialize()
		{
			typeof(WholePaymentProcessor).GetField("_wholePaymentProcessor", System.Reflection.BindingFlags.NonPublic | BindingFlags.Static).SetValue(null, null);
		}

		internal static void AddrealAccountToTheMarktplace(Marketplace marketplace)
		{
			var accounts = new SetOfBalances();
			accounts.AddAccountIn("100-09-0987653-01", new Dollar(5000000));
			accounts.AddAccountOut("100-09-0987653-02", new Dollar(5000000));
			accounts.AddAccountIn("E249628", new Btc(3000));
			accounts.AddAccountOut("6F25325", new Btc(40));
			accounts.AddAccountIn("42C6000", new Eth(100));
			accounts.AddAccountOut("42C5385", new Eth(100));
			marketplace.RealAccounts = accounts;
		}

		internal static BatchTransactions SetInitialSettings(Marketplace marketplace, DateTime today1, decimal threeHundredBitcoinCost, Domain[] domains, out MarketplaceBatch viernes, out AgentBatch agencyA)
		{
			AddrealAccountToTheMarktplace(marketplace);
			var agent = (GamesEngine.Exchange.Agent)marketplace.SearchAgent("CR/A");
			if (!agent.ExistsCashier("Juan")) agent.AddCashier("Juan");

			int batchNumber = marketplace.Batches.IdentityBatchNumber;
			viernes = marketplace.Batches.AddNewBatch(today1, today1, batchNumber, "Exchange Owner", "Batch para el 05/22/2020");
			viernes.InitialAmount(new Dollar(5000000), "Exchange Owner");
			viernes.InitialAmount(new Btc(300), "Exchange Owner");
			viernes.InitialAmount(new Eth(10), "Exchange Owner");
			viernes.Open(true, today1, "N/A");

			agencyA = viernes.AddAgent("A", today1);
			agencyA.ReceiveAmount(new Dollar(5000000), new Dollar(5000000), "Exchange Owner");
			agencyA.ReceiveAmount(new Btc(300), new Dollar(threeHundredBitcoinCost), "Exchange Owner");// Aqui defino cuanto me costo el BTC
			agencyA.ReceiveAmount(new Eth(10), new Dollar(10 * 1), "Exchange Owner");
			agencyA.Open(true, today1, "N/A");

			AgentBatch agentA = (AgentBatch)marketplace.SearchAgentBatch(agencyA.FullName, batchNumber);
			var agentJuan = agentA.AddAgent("Juan", today1);
			agentA.AssignFunds("Juan", batchNumber, new Dollar(5000000), "A", today1);
			agentA.AssignFunds("Juan", batchNumber, new Btc(200), "A", today1);
			agentA.AssignFunds("Juan", batchNumber, new Eth(5), "A", today1);

			Assert.AreEqual( agentJuan, (AgentBatch)marketplace.SearchAgentBatch(agencyA.FullName+ "/Juan", batchNumber));
			BatchTransactions juanTransactions = agentJuan.BatchTransactions;
			agentJuan.Open(true, today1, "N/A");

			Assert.AreEqual(1, marketplace.Batches.BatchesOpen(today1).Count());


			var processors = marketplace.PaymentProcessors();

			if (!processors.ExistsId("TEST-Cash-TestDepositUSD-USD-1")
				&& !processors.ExistsId("TEST-Cash-TestWithdrawalUSD-USD-1"))
			{
				var entity = (Entity)marketplace.Company.System.Entities.First();
				var paymentMethod = (ProcessorPaymentMethod)marketplace.Company.System.PaymentMethods.First();
				var coin = marketplace.Company.System.Coins.SearchByIsoCode("USD");
				var transaction = (ProcessorTransaction)marketplace.Company.System.TransactionTypes.First();
				var transactions = new GroupOFTransactions();
				transactions.Add(transaction);
				var processor = new PaymentProcessor(12, $"{nameof(PaymentProcessor)} for {entity.Name}.", entity, paymentMethod, coin, transactions, new UT_USD_Driver());
				//processors.Add(new UT_USD_Driver("12", nameof(UT_USD_Driver)));
				processors.Add(processor);

				coin = marketplace.Company.System.Coins.SearchByIsoCode("BTC");
				processor = new PaymentProcessor(13, $"{nameof(PaymentProcessor)} for {entity.Name}.", entity, paymentMethod, coin, transactions, new UT_BTC_Driver());
			}

			return juanTransactions;
		}

		internal static BatchTransactions SetInitialSettings(Marketplace marketplace, DateTime today1, Domain[] domains, out MarketplaceBatch viernes, out AgentBatch agencyA)
		{
			decimal threeHundredBitcoinCost = 2850000;
			return SetInitialSettings(marketplace, today1, threeHundredBitcoinCost, domains, out viernes, out agencyA );
		}

		internal static BatchTransactions SetInitialSettings(Marketplace marketplace, DateTime today1, Domain[] domains)
		{
			decimal threeHundredBitcoinCost = 2850000;
			MarketplaceBatch viernes;
			AgentBatch agencyA;
			return SetInitialSettings(marketplace, today1, threeHundredBitcoinCost, domains, out viernes, out agencyA);
		}

		[TestMethod]
		public void SetOfBalancesCreation()
		{
			#region Setup
			DateTime today1 = DateTime.Now;
			Company company = new Company();
			CurrenciesTest.AddCurrencies();
			company.Sales.CreateStore(1, "Test Store").MakeCurrent();
			Marketplace marketplace = new Marketplace(company, "CR");

			int batchNumber = marketplace.Batches.IdentityBatchNumber;
			MarketplaceBatch viernes = marketplace.Batches.AddNewBatch(today1, today1, batchNumber, "Exchange Owner", "Batch para el 05/22/2020");
			#endregion

			try
			{
				viernes.InitialAmount(new Dollar(5000000), "Exchange Owner");
				Assert.Fail($"This must fail, cuz {nameof(SetOfBalances)} its not properly confirmed");
			}
			catch (GameEngineException e){}
			try
			{
				viernes.InitialAmount(new Btc(300), "Exchange Owner");
				Assert.Fail($"This must fail, cuz {nameof(SetOfBalances)} its not properly confirmed");
			}
			catch (GameEngineException e) { }
			try
			{
				viernes.InitialAmount(new Eth(10), "Exchange Owner");
				Assert.Fail($"This must fail, cuz {nameof(SetOfBalances)} its not properly confirmed");
			}
			catch (GameEngineException e) { }
			try
			{
				viernes.Open(true, today1, "N/A");
				Assert.Fail($"This must fail, cuz {nameof(SetOfBalances)} its not properly confirmed");
			}
			catch (GameEngineException e) { }

			var accounts = new SetOfBalances();
			accounts.AddAccountIn("100-09-0987653-01", new Dollar(5000000));
			accounts.AddAccountOut("100-09-0987653-01", new Dollar(5000000));

			#region Same BTC wallet
			accounts.AddAccountIn("6F25325", new Btc(4));
			accounts.AddAccountOut("6F25325", new Btc(4));
			#endregion

			#region Clean previous wallet
			try
			{
				accounts.AddAccountIn("***************", new Dollar(5000000));
				Assert.Fail("Must fail, cuz dollar account should be the same one.");
			}
			catch (GameEngineException e)
			{ 
			}
			accounts.RemoveAccountIn(Coinage.Coin(Currencies.CODES.USD), "100-09-0987653-01");
			accounts.RemoveAccountOut(Coinage.Coin(Currencies.CODES.USD), "100-09-0987653-01");
			accounts.AddAccountIn("100-09-0987653-01", new Dollar(5000000));
			accounts.AddAccountOut("100-09-0987653-01", new Dollar(5000000));
			#endregion

			#region Distinct BTC wallet
			try
			{
				accounts.AddAccountIn("6F25400", new Btc(4));
				accounts.AddAccountOut("6F25325", new Btc(4));
				Assert.Fail("Must fail, cuz account in BTC its already defined.");
			}
			catch (GameEngineException e)
			{
			}
			#endregion

			marketplace.RealAccounts = accounts;

			accounts.AddAccountIn("100-09-********-453453453453434501", new Eth(5000000));
			accounts.AddAccountOut("100-09-098765335345345435345345-01", new Eth(5000000));

			viernes.InitialAmount(new Dollar(5000000), "Exchange Owner");
			viernes.InitialAmount(new Btc(300), "Exchange Owner");
			viernes.InitialAmount(new Eth(10), "Exchange Owner");
			viernes.Open(true, today1, "N/A");

			AgentBatch agencyA = viernes.AddAgent("A", today1);
			agencyA.ReceiveAmount(new Dollar(5000000), new Dollar(5000000), "Exchange Owner");
			agencyA.ReceiveAmount(new Btc(300), new Dollar(300000), "Exchange Owner");
			agencyA.ReceiveAmount(new Eth(10), new Dollar(10 * 1), "Exchange Owner");
			agencyA.Open(true, today1, "N/A");

			AgentBatch juan = agencyA.AddAgent("Juan", today1);
			agencyA.AssignFunds("Juan", batchNumber, new Dollar(5000000), "A", today1);
			agencyA.AssignFunds("Juan", batchNumber, new Btc(200), "A", today1);
			agencyA.AssignFunds("Juan", batchNumber, new Eth(5), "A", today1);

			AgentBatch agentJuan = (AgentBatch)marketplace.SearchAgentBatch(juan.FullName, batchNumber);
			BatchTransactions juanTransactions = agentJuan.BatchTransactions;
			juan.Open(true, today1, "N/A");
		}

		[TestMethod]
		public void Deposit_ChangeInBatchBalance()
		{
			bool itIsThePresent = false;
			DateTime today1 = DateTime.Now;
			DateTime today2 = today1.AddMilliseconds(1);
			Company company = new Company();
			CurrenciesTest.AddCurrencies();
			var store = company.Sales.CreateStore(1, "Test Store");
			store.MakeCurrent();
			string ced="abc123";string accountNumber = "B15461";Customer customer=company.GetOrCreateCustomerById(accountNumber);customer.Identifier=ced;
			CustomerAccount bitcoinAccount = customer.CreateNewAccountFor(Coinage.Coin(Currencies.CODES.BTC));
			Marketplace marketplace = new Marketplace(company, "CR");
			var a = (GamesEngine.Exchange.Agent)marketplace.AddAgent("A");
			var b = a.AddCashier("Juan");
			string employeeName = "Juan";
			Domain domain = company.Sales.CreateDomain(false, 1, "localhost", PaymentChannels.Agents.INSIDER);
			company.Sales.CurrentStore.Add(domain);
			GuardianTest.LoadProcessors(company);

			#region rate #1
			RegularConversionSpread rate1 = marketplace.CreateConversionSpread(1, today1, itIsThePresent, Coinage.Coin(Currencies.CODES.USD), Coinage.Coin(Currencies.CODES.BTC), 60m, 30m, employeeName);
			Assert.AreEqual(rate1, marketplace.ConversionSpreadAt(today1, Coinage.Coin(Currencies.CODES.USD), Coinage.Coin(Currencies.CODES.BTC)));
			#endregion

			MarketplaceBatch marketPLaceBatch;
			AgentBatch agencyBatch;
			BatchTransactions juanTransactions = SetInitialSettings(marketplace, today1, new Domain[] { domain }, out marketPLaceBatch, out agencyBatch);
			BatchSet agentBatch = juanTransactions.Batch();
			string agentPath = a.FullName;

			int id = marketplace.NewTransationNumber();
			var entityId = 5;
			var transactionType = marketplace.Company.System.TransactionTypes.SearchByName<ProcessorTransaction>(TransactionType.Deposit.ToString());
			int authorizationNumber = PaymentChannels.Deposit(
				itIsThePresent,
				Coinage.Coin(Currencies.CODES.USD), 
				domain.Url,
				transactionType,
				new DespositBody(
					accountNumber, 
					ced,
					100,
					"deposit test", 
					today1,
					"", 
					"cris",
					domain, "CR/A", PaymentMethod.Cash, entityId, store.Id, string.Empty, string.Empty, string.Empty, string.Empty, string.Empty, 0)).AuthorizationId;

			DepositTransaction transaction1 = (DepositTransaction)marketplace.From(id, bitcoinAccount, agentPath, employeeName, domain).Deposit(today1, itIsThePresent, new Dollar(100), employeeName, (PaymentProcessor)marketplace.Company.System.PaymentProcessor.First(), 1);
			DepositTransaction transaction = (DepositTransaction)marketplace.FindDraftTransaction(transaction1.Id, agentPath, employeeName);

			Assert.AreEqual(transaction1, transaction);

			BatchAccount marketplaceInBlancesResults = marketPLaceBatch.SelfReceptionAccounts(Coinage.Coin(Currencies.CODES.USD));
			BatchAccount agencyInBalances = agencyBatch.SelfReceptionAccounts(Coinage.Coin(Currencies.CODES.USD));
			BatchAccount agentInBalances = agentBatch.SelfReceptionAccounts(Coinage.Coin(Currencies.CODES.USD));
			BatchAccount marketplaceOutBlancesResults = marketPLaceBatch.SaleAccount(Coinage.Coin(Currencies.CODES.BTC));
			BatchAccount agencyOutBalances = agencyBatch.SaleAccount(Coinage.Coin(Currencies.CODES.BTC));
			BatchAccount agentOutBalances = agentBatch.SaleAccount(Coinage.Coin(Currencies.CODES.BTC));

			Assert.AreEqual(new Dollar(0m), agencyInBalances.Initial);
			Assert.AreEqual(new Dollar(0m), agencyInBalances.Available);
			Assert.AreEqual(new Dollar(100m), agencyInBalances.Locked);
			Assert.AreEqual(new Dollar(0m), agencyInBalances.LockedAccumulated);
			Assert.AreEqual(new Dollar(0m), agencyInBalances.SpendAccumulated);

			Assert.AreEqual(new Btc(300m), agencyOutBalances.Initial);
			Assert.AreEqual(new Btc(100m), agencyOutBalances.Available);
			Assert.AreEqual(new Btc(0m), agencyOutBalances.Locked);
			Assert.AreEqual(new Btc(0.83333333m), agencyOutBalances.LockedAccumulated);
			Assert.AreEqual(new Btc(0m), agencyOutBalances.SpendAccumulated);

			Assert.AreEqual(marketplaceInBlancesResults.Available, agencyInBalances.Available);
			Assert.AreEqual(marketplaceInBlancesResults.Locked, agencyInBalances.Locked);
			Assert.AreEqual(marketplaceInBlancesResults.LockedAccumulated, agencyInBalances.LockedAccumulated);
			Assert.AreEqual(marketplaceInBlancesResults.Initial, agencyInBalances.Initial);
			Assert.AreEqual(marketplaceInBlancesResults.SpendAccumulated, agencyInBalances.SpendAccumulated);

			Assert.AreEqual(marketplaceOutBlancesResults.LockedAccumulated, agencyOutBalances.LockedAccumulated);
			Assert.AreEqual(marketplaceOutBlancesResults.SpendAccumulated, agencyOutBalances.SpendAccumulated);

			Assert.AreEqual(marketplaceOutBlancesResults.LockedAccumulated, agentOutBalances.LockedAccumulated);
			Assert.AreEqual(marketplaceOutBlancesResults.SpendAccumulated, agentOutBalances.SpendAccumulated);

			TransactionCompleted result1 = marketplace.Approve(today1, itIsThePresent, transaction1, 1, employeeName);
		
			Assert.AreEqual(new Dollar(0m), agencyInBalances.Initial);
			Assert.AreEqual(new Dollar(100m), agencyInBalances.Available);
			Assert.AreEqual(new Dollar(0m), agencyInBalances.Locked);
			Assert.AreEqual(new Dollar(0m), agencyInBalances.LockedAccumulated);
			Assert.AreEqual(new Dollar(0m), agencyInBalances.SpendAccumulated);

			Assert.AreEqual(new Btc(300m), agencyOutBalances.Initial);
			Assert.AreEqual(new Btc(100m), agencyOutBalances.Available);
			Assert.AreEqual(new Btc(0m), agencyOutBalances.Locked);
			Assert.AreEqual(new Btc(0m), agencyOutBalances.LockedAccumulated);
			Assert.AreEqual(new Btc(0.83333333m), agencyOutBalances.SpendAccumulated);

			Assert.AreEqual(marketplaceInBlancesResults.Available, agencyInBalances.Available);
			Assert.AreEqual(marketplaceInBlancesResults.Locked, agencyInBalances.Locked);
			Assert.AreEqual(marketplaceInBlancesResults.LockedAccumulated, agencyInBalances.LockedAccumulated);
			Assert.AreEqual(marketplaceInBlancesResults.Initial, agencyInBalances.Initial);
			Assert.AreEqual(marketplaceInBlancesResults.SpendAccumulated, agencyInBalances.SpendAccumulated);

			Assert.AreEqual(marketplaceOutBlancesResults.LockedAccumulated, agencyOutBalances.LockedAccumulated);
			Assert.AreEqual(marketplaceOutBlancesResults.SpendAccumulated, agencyOutBalances.SpendAccumulated);

			Assert.AreEqual(marketplaceOutBlancesResults.LockedAccumulated, agentOutBalances.LockedAccumulated);
			Assert.AreEqual(marketplaceOutBlancesResults.SpendAccumulated, agentOutBalances.SpendAccumulated);

			//driver = PaymentProcessorsAndActionsByDomains.Instance().SearchFor(Coinage.Coin(Currencies.CODES.USD), domain, marketplace.Company.System.TransactionTypes.SearchByName<ProcessorTransaction>(TransactionType.Deposit.ToString())).First();
			//authorizationNumber = driver.Deposit(itIsThePresent, new DespositBody(accountNumber, ced, 100, "", today1, "", "cris", domain)).AuthorizationId;


			transactionType = marketplace.Company.System.TransactionTypes.SearchByName<ProcessorTransaction>(TransactionType.Deposit.ToString());
			authorizationNumber = PaymentChannels.Deposit(
				itIsThePresent,
				Coinage.Coin(Currencies.CODES.USD),
				domain,
				transactionType,
				new DespositBody(
					accountNumber,
					ced,
					100,
					"deposit test",
					today1,
					"",
					"cris",
					domain)).AuthorizationId;
			DepositTransaction transaction2 = (DepositTransaction)marketplace.From(id, bitcoinAccount, agentPath, "Juan", domain).Deposit(today1, itIsThePresent, new Dollar(100), employeeName, (PaymentProcessor)marketplace.Company.System.PaymentProcessor.First(), 1);

			Assert.AreEqual(new Dollar(0m), agencyInBalances.Initial);
			Assert.AreEqual(new Dollar(100m), agencyInBalances.Available);
			Assert.AreEqual(new Dollar(100m), agencyInBalances.Locked);
			Assert.AreEqual(new Dollar(0m), agencyInBalances.LockedAccumulated);
			Assert.AreEqual(new Dollar(0m), agencyInBalances.SpendAccumulated);

			Assert.AreEqual(new Btc(300m), agencyOutBalances.Initial);
			Assert.AreEqual(new Btc(100m), agencyOutBalances.Available);
			Assert.AreEqual(new Btc(0m), agencyOutBalances.Locked);
			Assert.AreEqual(new Btc(0.83333333m), agencyOutBalances.LockedAccumulated);
			Assert.AreEqual(new Btc(0.83333333m), agencyOutBalances.SpendAccumulated);

			Assert.AreEqual(marketplaceInBlancesResults.Available, agencyInBalances.Available);
			Assert.AreEqual(marketplaceInBlancesResults.Locked, agencyInBalances.Locked);
			Assert.AreEqual(marketplaceInBlancesResults.LockedAccumulated, agencyInBalances.LockedAccumulated);
			Assert.AreEqual(marketplaceInBlancesResults.Initial, agencyInBalances.Initial);
			Assert.AreEqual(marketplaceInBlancesResults.SpendAccumulated, agencyInBalances.SpendAccumulated);

			Assert.AreEqual(marketplaceOutBlancesResults.LockedAccumulated, agencyOutBalances.LockedAccumulated);
			Assert.AreEqual(marketplaceOutBlancesResults.SpendAccumulated, agencyOutBalances.SpendAccumulated);

			Assert.AreEqual(marketplaceOutBlancesResults.LockedAccumulated, agentOutBalances.LockedAccumulated);
			Assert.AreEqual(marketplaceOutBlancesResults.SpendAccumulated, agentOutBalances.SpendAccumulated);

			IEnumerable<MarketplaceBatch> marketplaceBatches = marketplace.Batches.BatchesReady(today1);
			foreach (MarketplaceBatch ready in marketplaceBatches)
			{
				foreach (BatchAccount outAccount in ready.InitialAccounts())
				{
					if (outAccount.SpendAccumulated.CurrencyCode == Currencies.CODES.BTC.ToString())
					{
						Currency lockedAccumulated = outAccount.LockedAccumulated;
						Currency spendAccumulated = outAccount.SpendAccumulated;
						Assert.AreEqual(new Btc(0.83333333m), lockedAccumulated);
						Assert.AreEqual(new Btc(0.83333333m), spendAccumulated);
					}
				}
			}

			TransactionCompleted result2 = marketplace.Approve(today1, itIsThePresent, transaction2, 2, employeeName);

			Assert.AreEqual(new Dollar(0m), agencyInBalances.Initial);
			Assert.AreEqual(new Dollar(200m), agencyInBalances.Available);
			Assert.AreEqual(new Dollar(0m), agencyInBalances.Locked);
			Assert.AreEqual(new Dollar(0m), agencyInBalances.LockedAccumulated);
			Assert.AreEqual(new Dollar(0m), agencyInBalances.SpendAccumulated);

			Assert.AreEqual(new Btc(300m), agencyOutBalances.Initial);
			Assert.AreEqual(new Btc(100m), agencyOutBalances.Available);
			Assert.AreEqual(new Btc(0m), agencyOutBalances.Locked);
			Assert.AreEqual(new Btc(0m), agencyOutBalances.LockedAccumulated);
			Assert.AreEqual(new Btc(1.66666666m), agencyOutBalances.SpendAccumulated);

			Assert.AreEqual(marketplaceInBlancesResults.Available, agencyInBalances.Available);
			Assert.AreEqual(marketplaceInBlancesResults.Locked, agencyInBalances.Locked);
			Assert.AreEqual(marketplaceInBlancesResults.LockedAccumulated, agencyInBalances.LockedAccumulated);
			Assert.AreEqual(marketplaceInBlancesResults.Initial, agencyInBalances.Initial);
			Assert.AreEqual(marketplaceInBlancesResults.SpendAccumulated, agencyInBalances.SpendAccumulated);

			Assert.AreEqual(marketplaceOutBlancesResults.LockedAccumulated, agencyOutBalances.LockedAccumulated);
			Assert.AreEqual(marketplaceOutBlancesResults.SpendAccumulated, agencyOutBalances.SpendAccumulated);

			marketplaceBatches = marketplace.Batches.BatchesReady(today1);
			foreach (MarketplaceBatch ready in marketplaceBatches)
			{
				foreach (BatchAccount outAccount in ready.InitialAccounts())
				{
					if (outAccount.SpendAccumulated.CurrencyCode == Currencies.CODES.BTC.ToString())
					{
						Currency lockedAccumulated = outAccount.LockedAccumulated;
						Currency spendAccumulated = outAccount.SpendAccumulated;
						Assert.AreEqual(new Btc(0m), lockedAccumulated);
						Assert.AreEqual(new Btc(1.66666666m), spendAccumulated);
						Assert.AreEqual(new Btc(1.66666666m), spendAccumulated);
					}
				}
			}
		}

		[TestMethod]
		public void WithDraw()
		{
			DateTime today1 = DateTime.Now;
			bool itIsThePresent = false;
			DateTime today2 = today1.AddMilliseconds(1);
			Company company = new Company();
			CurrenciesTest.AddCurrencies();
			company.Sales.CreateStore(1, "Test Store").MakeCurrent();
			string ced="abc123";string accountNumber = "B15461";Customer customer=company.GetOrCreateCustomerById(accountNumber);customer.Identifier=ced;
			CustomerAccount dollarAccount = customer.CreateNewAccountFor(Coinage.Coin(Currencies.CODES.USD));
			CustomerAccount bitcoinAccount = customer.CreateNewAccountFor(Coinage.Coin(Currencies.CODES.BTC));
			Marketplace marketplace = new Marketplace(company, "CR");
			var a = (GamesEngine.Exchange.Agent)marketplace.AddAgent("A");
			marketplace.RegisterNewAccount(1, dollarAccount);
			marketplace.RegisterNewAccount(2, bitcoinAccount);
			string employeeName = "Juan";
			Domain domain = company.Sales.CreateDomain(false, 1, "localhost", PaymentChannels.Agents.INSIDER);
			company.Sales.CurrentStore.Add(domain);
			GuardianTest.LoadProcessors(company);

			#region rate #1
			RegularConversionSpread rate1 = marketplace.CreateConversionSpread(1, today1, itIsThePresent, Coinage.Coin(Currencies.CODES.USD), Coinage.Coin(Currencies.CODES.BTC), 60m, 30m, employeeName);
			Assert.AreEqual(rate1, marketplace.ConversionSpreadAt(today1, Coinage.Coin(Currencies.CODES.USD), Coinage.Coin(Currencies.CODES.BTC)));
			#endregion

			#region rate #2
			RegularConversionSpread rate2 = marketplace.CreateConversionSpread(2, today1, itIsThePresent, Coinage.Coin(Currencies.CODES.BTC), Coinage.Coin(Currencies.CODES.USD), 10001, 10000, employeeName);
			Assert.AreEqual(rate2, marketplace.ConversionSpreadAt(today1, Coinage.Coin(Currencies.CODES.BTC), Coinage.Coin(Currencies.CODES.USD)));
			#endregion


			MarketplaceBatch marketPLaceBatch;
			AgentBatch agencyBatch;
			BatchTransactions juanTransactions = SetInitialSettings(marketplace, today1, new Domain[] { domain }, out marketPLaceBatch, out agencyBatch);
			BatchSet agentBatch = juanTransactions.Batch();
			string agentPath = agencyBatch.FullName;

			int id = marketplace.NewTransationNumber();

			var processor = marketplace.Company.System.PaymentProcessor.SearchWithdrawalProcessorBy("USD");
			WithdrawalTransaction transaction1 = (WithdrawalTransaction)marketplace.From(id, dollarAccount, agentPath, "Juan", domain).Withdraw(today1, itIsThePresent, new Dollar(100), 1, employeeName, "1-11", "", new NoFeeUSD(), processor, 1);

			BatchAccount marketplaceInBlancesResults = marketPLaceBatch.SelfReceptionAccounts(Coinage.Coin(Currencies.CODES.USD));
			BatchAccount agencyInBalances = agencyBatch.SelfReceptionAccounts(Coinage.Coin(Currencies.CODES.USD));
			BatchAccount agentInBalances = agentBatch.SelfReceptionAccounts(Coinage.Coin(Currencies.CODES.USD));
			BatchAccount marketplaceOutBlancesResults = marketPLaceBatch.SaleAccount(Coinage.Coin(Currencies.CODES.USD));
			BatchAccount agencyOutBalances = agencyBatch.SaleAccount(Coinage.Coin(Currencies.CODES.USD));
			BatchAccount agentOutBalances = agentBatch.SaleAccount(Coinage.Coin(Currencies.CODES.USD));

			Assert.AreEqual(new Dollar(0m), agencyInBalances.Initial);
			Assert.AreEqual(new Dollar(0m), agencyInBalances.Available);
			Assert.AreEqual(new Dollar(0m), agencyInBalances.Locked);
			Assert.AreEqual(new Dollar(0m), agencyInBalances.LockedAccumulated);
			Assert.AreEqual(new Dollar(0m), agencyInBalances.SpendAccumulated);

			Assert.AreEqual(new Dollar(5000000m), agencyOutBalances.Initial);
			Assert.AreEqual(new Dollar(0m), agencyOutBalances.Available);
			Assert.AreEqual(new Dollar(0m), agencyOutBalances.Locked);
			Assert.AreEqual(new Dollar(100m), agencyOutBalances.LockedAccumulated);
			Assert.AreEqual(new Dollar(0m), agencyOutBalances.SpendAccumulated);

			Assert.AreEqual(marketplaceInBlancesResults.Available, agencyInBalances.Available);
			Assert.AreEqual(marketplaceInBlancesResults.Locked, agencyInBalances.Locked);
			Assert.AreEqual(marketplaceInBlancesResults.LockedAccumulated, agencyInBalances.LockedAccumulated);
			Assert.AreEqual(marketplaceInBlancesResults.Initial, agencyInBalances.Initial);
			Assert.AreEqual(marketplaceInBlancesResults.SpendAccumulated, agencyInBalances.SpendAccumulated);

			Assert.AreEqual(marketplaceOutBlancesResults.LockedAccumulated, agencyOutBalances.LockedAccumulated);
			Assert.AreEqual(marketplaceOutBlancesResults.SpendAccumulated, agencyOutBalances.SpendAccumulated);

			Assert.AreEqual(marketplaceOutBlancesResults.LockedAccumulated, agentOutBalances.LockedAccumulated);
			Assert.AreEqual(marketplaceOutBlancesResults.SpendAccumulated, agentOutBalances.SpendAccumulated);

			var journalEntryNumber = marketplace.NewJournalEntryNumber();
			TransactionCompleted result1 = transaction1.Approve(today1, itIsThePresent, employeeName, journalEntryNumber);

			marketplaceInBlancesResults = marketPLaceBatch.SelfReceptionAccounts(Coinage.Coin(Currencies.CODES.USD));
			agencyInBalances = agencyBatch.SelfReceptionAccounts(Coinage.Coin(Currencies.CODES.USD));
			marketplaceOutBlancesResults = marketPLaceBatch.SaleAccount(Coinage.Coin(Currencies.CODES.USD));
			agencyOutBalances = agencyBatch.SaleAccount(Coinage.Coin(Currencies.CODES.USD));

			Assert.AreEqual(new Dollar(0m), agencyInBalances.Initial);
			Assert.AreEqual(new Dollar(0m), agencyInBalances.Available);
			Assert.AreEqual(new Dollar(0m), agencyInBalances.Locked);
			Assert.AreEqual(new Dollar(0m), agencyInBalances.LockedAccumulated);
			Assert.AreEqual(new Dollar(0m), agencyInBalances.SpendAccumulated);

			Assert.AreEqual(new Dollar(5000000m), agencyOutBalances.Initial);
			Assert.AreEqual(new Dollar(0m), agencyOutBalances.Available);
			Assert.AreEqual(new Dollar(0m), agencyOutBalances.Locked);
			Assert.AreEqual(new Dollar(0m), agencyOutBalances.LockedAccumulated);
			Assert.AreEqual(new Dollar(100m), agencyOutBalances.SpendAccumulated);

			Assert.AreEqual(marketplaceInBlancesResults.Available, agencyInBalances.Available);
			Assert.AreEqual(marketplaceInBlancesResults.Locked, agencyInBalances.Locked);
			Assert.AreEqual(marketplaceInBlancesResults.LockedAccumulated, agencyInBalances.LockedAccumulated);
			Assert.AreEqual(marketplaceInBlancesResults.Initial, agencyInBalances.Initial);
			Assert.AreEqual(marketplaceInBlancesResults.SpendAccumulated, agencyInBalances.SpendAccumulated);

			Assert.AreEqual(marketplaceOutBlancesResults.LockedAccumulated, agencyOutBalances.LockedAccumulated);
			Assert.AreEqual(marketplaceOutBlancesResults.SpendAccumulated, agencyOutBalances.SpendAccumulated);

			Assert.AreEqual(marketplaceOutBlancesResults.LockedAccumulated, agentOutBalances.LockedAccumulated);
			Assert.AreEqual(marketplaceOutBlancesResults.SpendAccumulated, agentOutBalances.SpendAccumulated);
		}


		[TestMethod]
		public void Transfer_with_different_target_currency()
		{
			DateTime today1 = DateTime.Now;
			bool itIsThePresent = false;
			DateTime today2 = today1.AddMilliseconds(1);
			Company company = new Company();
			CurrenciesTest.AddCurrencies();
			company.Sales.CreateStore(1, "Test Store").MakeCurrent();
			string ced="abc123";string accountNumber = "B15461";Customer customer=company.GetOrCreateCustomerById(accountNumber);customer.Identifier=ced;
			string ced2="abc1234";string accountNumber2 = "B15462";Customer toCustomer=company.GetOrCreateCustomerById(accountNumber2);toCustomer.Identifier=ced2;
			CustomerAccount dollarAccount = customer.CreateNewAccountFor(Coinage.Coin(Currencies.CODES.USD));
			CustomerAccount bitcoinAccount = toCustomer.CreateNewAccountFor(Coinage.Coin(Currencies.CODES.BTC));
			Marketplace marketplace = new Marketplace(company, "CR");
			var a = (GamesEngine.Exchange.Agent)marketplace.AddAgent("A");
			var b = a.AddCashier("Juan");
			marketplace.RegisterNewAccount(1, dollarAccount);
			marketplace.RegisterNewAccount(2, bitcoinAccount);
			string employeeName = "Juan";
			Domain domain = company.Sales.CreateDomain(false, 1, "localhost", PaymentChannels.Agents.INSIDER);
			company.Sales.CurrentStore.Add(domain);
			GuardianTest.LoadProcessors(company);

			#region rate #1
			RegularConversionSpread rate1 = marketplace.CreateConversionSpread(1, today1, itIsThePresent, Coinage.Coin(Currencies.CODES.USD), Coinage.Coin(Currencies.CODES.BTC), 60m, 30m, employeeName);
			Assert.AreEqual(rate1, marketplace.ConversionSpreadAt(today1, Coinage.Coin(Currencies.CODES.USD), Coinage.Coin(Currencies.CODES.BTC)));
			#endregion

			MarketplaceBatch marketPLaceBatch;
			AgentBatch agencyBatch;
			BatchTransactions juanTransactions = SetInitialSettings(marketplace, today1, new Domain[] { domain }, out marketPLaceBatch, out agencyBatch);
			BatchSet agentBatch = juanTransactions.Batch();
			string agentPath = a.FullName;

			int id = marketplace.NewTransationNumber();

			TransferTransaction transaction1 = (TransferTransaction)marketplace.From(id, dollarAccount, agentPath, "Juan", domain).TransferTo(today1, itIsThePresent, new Dollar(100), 1, bitcoinAccount, employeeName, (PaymentProcessor)marketplace.Company.System.PaymentProcessor.First(), 1);

			BatchAccount marketplaceInBlancesResults = marketPLaceBatch.SelfReceptionAccounts(Coinage.Coin(Currencies.CODES.USD));
			BatchAccount agencyInBalances = agencyBatch.SelfReceptionAccounts(Coinage.Coin(Currencies.CODES.USD));
			BatchAccount agentInBalances = agentBatch.SelfReceptionAccounts(Coinage.Coin(Currencies.CODES.USD));
			BatchAccount marketplaceOutBlancesResults = marketPLaceBatch.SaleAccount(Coinage.Coin(Currencies.CODES.BTC));
			BatchAccount agencyOutBalances = agencyBatch.SaleAccount(Coinage.Coin(Currencies.CODES.BTC));
			BatchAccount agentOutBalances = agentBatch.SaleAccount(Coinage.Coin(Currencies.CODES.BTC));

			Assert.AreEqual(new Dollar(0m), agencyInBalances.Initial);
			Assert.AreEqual(new Dollar(0m), agencyInBalances.Available);
			Assert.AreEqual(new Dollar(100m), agencyInBalances.Locked);
			Assert.AreEqual(new Dollar(0m), agencyInBalances.LockedAccumulated);
			Assert.AreEqual(new Dollar(0m), agencyInBalances.SpendAccumulated);

			Assert.AreEqual(new Btc(300m), agencyOutBalances.Initial);
			Assert.AreEqual(new Btc(100m), agencyOutBalances.Available);
			Assert.AreEqual(new Btc(0m), agencyOutBalances.Locked);
			Assert.AreEqual(new Btc(0.83333333m), agencyOutBalances.LockedAccumulated);
			Assert.AreEqual(new Btc(0m), agencyOutBalances.SpendAccumulated);

			Assert.AreEqual(marketplaceInBlancesResults.Available, agencyInBalances.Available);
			Assert.AreEqual(marketplaceInBlancesResults.Locked, agencyInBalances.Locked);
			Assert.AreEqual(marketplaceInBlancesResults.LockedAccumulated, agencyInBalances.LockedAccumulated);
			Assert.AreEqual(marketplaceInBlancesResults.Initial, agencyInBalances.Initial);
			Assert.AreEqual(marketplaceInBlancesResults.SpendAccumulated, agencyInBalances.SpendAccumulated);

			Assert.AreEqual(marketplaceOutBlancesResults.LockedAccumulated, agencyOutBalances.LockedAccumulated);
			Assert.AreEqual(marketplaceOutBlancesResults.SpendAccumulated, agencyOutBalances.SpendAccumulated);

			Assert.AreEqual(marketplaceOutBlancesResults.LockedAccumulated, agentOutBalances.LockedAccumulated);
			Assert.AreEqual(marketplaceOutBlancesResults.SpendAccumulated, agentOutBalances.SpendAccumulated);

			var journalEntryNumber = marketplace.NewJournalEntryNumber();
			TransactionCompleted result1 = transaction1.Approve(today1, itIsThePresent, employeeName, journalEntryNumber);

			Assert.AreEqual(new Dollar(0m), agencyInBalances.Initial);
			Assert.AreEqual(new Dollar(100m), agencyInBalances.Available);
			Assert.AreEqual(new Dollar(0m), agencyInBalances.Locked);
			Assert.AreEqual(new Dollar(0m), agencyInBalances.LockedAccumulated);
			Assert.AreEqual(new Dollar(0m), agencyInBalances.SpendAccumulated);

			Assert.AreEqual(new Btc(300m), agencyOutBalances.Initial);
			Assert.AreEqual(new Btc(100m), agencyOutBalances.Available);
			Assert.AreEqual(new Btc(0m), agencyOutBalances.Locked);
			Assert.AreEqual(new Btc(0m), agencyOutBalances.LockedAccumulated);
			Assert.AreEqual(new Btc(0.83333333m), agencyOutBalances.SpendAccumulated);

			Assert.AreEqual(marketplaceInBlancesResults.Available, agencyInBalances.Available);
			Assert.AreEqual(marketplaceInBlancesResults.Locked, agencyInBalances.Locked);
			Assert.AreEqual(marketplaceInBlancesResults.LockedAccumulated, agencyInBalances.LockedAccumulated);
			Assert.AreEqual(marketplaceInBlancesResults.Initial, agencyInBalances.Initial);
			Assert.AreEqual(marketplaceInBlancesResults.SpendAccumulated, agencyInBalances.SpendAccumulated);

			Assert.AreEqual(marketplaceOutBlancesResults.LockedAccumulated, agencyOutBalances.LockedAccumulated);
			Assert.AreEqual(marketplaceOutBlancesResults.SpendAccumulated, agencyOutBalances.SpendAccumulated);

			Assert.AreEqual(marketplaceOutBlancesResults.LockedAccumulated, agentOutBalances.LockedAccumulated);
			Assert.AreEqual(marketplaceOutBlancesResults.SpendAccumulated, agentOutBalances.SpendAccumulated);

		}

		[TestMethod]
		public void Transfer_with_same_default_target_currency()
		{
			DateTime today1 = DateTime.Now;
			bool itIsThePresent = false;
			DateTime today2 = today1.AddMilliseconds(1);
			Company company = new Company();
			CurrenciesTest.AddCurrencies();
			company.Sales.CreateStore(1, "Test Store").MakeCurrent();
			string ced="abc123";string accountNumber = "B15461";Customer customer=company.GetOrCreateCustomerById(accountNumber);customer.Identifier=ced;
			string ced2="abc1234";string accountNumber2 = "B15462";Customer toCustomer=company.GetOrCreateCustomerById(accountNumber2);toCustomer.Identifier=ced2;
			CustomerAccount dollarAccount = customer.CreateNewAccountFor(Coinage.Coin(Currencies.CODES.USD));
			CustomerAccount targetAccount = toCustomer.FindAccountByCurrency(Coinage.Coin(Currencies.CODES.USD));
			Marketplace marketplace = new Marketplace(company, "CR");
			var a = (GamesEngine.Exchange.Agent)marketplace.AddAgent("A");
			var b = a.AddCashier("Juan");
			marketplace.RegisterNewAccount(1, dollarAccount);
			marketplace.RegisterNewAccount(2, targetAccount);
			string employeeName = "Juan";
			Domain domain = company.Sales.CreateDomain(false, 1, "localhost", PaymentChannels.Agents.INSIDER);
			company.Sales.CurrentStore.Add(domain);
			GuardianTest.LoadProcessors(company);

			#region rate #1
			RegularConversionSpread rate1 = marketplace.CreateConversionSpread(1, today1, itIsThePresent, Coinage.Coin(Currencies.CODES.USD), Coinage.Coin(Currencies.CODES.BTC), 60m, 30m, employeeName);
			Assert.AreEqual(rate1, marketplace.ConversionSpreadAt(today1, Coinage.Coin(Currencies.CODES.USD), Coinage.Coin(Currencies.CODES.BTC)));
			#endregion

			MarketplaceBatch marketPLaceBatch;
			AgentBatch agencyBatch;
			BatchTransactions juanTransactions = SetInitialSettings(marketplace, today1, new Domain[] { domain }, out marketPLaceBatch, out agencyBatch);
			BatchSet agentBatch = juanTransactions.Batch();
			string agentPath = a.FullName;

			int id = marketplace.NewTransationNumber();

			TransferTransaction transaction1 = (TransferTransaction)marketplace.From(id, dollarAccount, agentPath, "Juan", domain).TransferTo(today1, itIsThePresent, new Dollar(100), 1, toCustomer, employeeName, (PaymentProcessor)marketplace.Company.System.PaymentProcessor.First(), 1);

			BatchAccount marketplaceInBlancesResults = marketPLaceBatch.SelfReceptionAccounts(Coinage.Coin(Currencies.CODES.USD));
			BatchAccount agencyInBalances = agencyBatch.SelfReceptionAccounts(Coinage.Coin(Currencies.CODES.USD));
			BatchAccount marketplaceOutBlancesResults = marketPLaceBatch.SaleAccount(Coinage.Coin(Currencies.CODES.USD));
			BatchAccount agencyOutBalances = agencyBatch.SaleAccount(Coinage.Coin(Currencies.CODES.USD));

			Assert.AreEqual(new Dollar(0m), agencyInBalances.Initial);
			Assert.AreEqual(new Dollar(0m), agencyInBalances.Available);
			Assert.AreEqual(new Dollar(0m), agencyInBalances.Locked);
			Assert.AreEqual(new Dollar(0m), agencyInBalances.LockedAccumulated);
			Assert.AreEqual(new Dollar(0m), agencyInBalances.SpendAccumulated);

			Assert.AreEqual(new Dollar(5000000m), agencyOutBalances.Initial);
			Assert.AreEqual(new Dollar(0m), agencyOutBalances.Available);
			Assert.AreEqual(new Dollar(0m), agencyOutBalances.Locked);
			Assert.AreEqual(new Dollar(0m), agencyOutBalances.LockedAccumulated);
			Assert.AreEqual(new Dollar(0m), agencyOutBalances.SpendAccumulated);

			Assert.AreEqual(marketplaceInBlancesResults.Available, agencyInBalances.Available);
			Assert.AreEqual(marketplaceInBlancesResults.Locked, agencyInBalances.Locked);
			Assert.AreEqual(marketplaceInBlancesResults.LockedAccumulated, agencyInBalances.LockedAccumulated);
			Assert.AreEqual(marketplaceInBlancesResults.Initial, agencyInBalances.Initial);
			Assert.AreEqual(marketplaceInBlancesResults.SpendAccumulated, agencyInBalances.SpendAccumulated);

			Assert.AreEqual(marketplaceOutBlancesResults.LockedAccumulated, agencyOutBalances.LockedAccumulated);
			Assert.AreEqual(marketplaceOutBlancesResults.SpendAccumulated, agencyOutBalances.SpendAccumulated);

			var journalEntryNumber = marketplace.NewJournalEntryNumber();
			TransactionCompleted result1 = transaction1.Approve(today1, itIsThePresent, employeeName, journalEntryNumber);

			Assert.AreEqual(new Dollar(0m), agencyInBalances.Initial);
			Assert.AreEqual(new Dollar(0m), agencyInBalances.Available);
			Assert.AreEqual(new Dollar(0m), agencyInBalances.Locked);
			Assert.AreEqual(new Dollar(0m), agencyInBalances.LockedAccumulated);
			Assert.AreEqual(new Dollar(0m), agencyInBalances.SpendAccumulated);

			Assert.AreEqual(new Dollar(5000000m), agencyOutBalances.Initial);
			Assert.AreEqual(new Dollar(0m), agencyOutBalances.Available);
			Assert.AreEqual(new Dollar(0m), agencyOutBalances.Locked);
			Assert.AreEqual(new Dollar(0m), agencyOutBalances.LockedAccumulated);
			Assert.AreEqual(new Dollar(0m), agencyOutBalances.SpendAccumulated);

			Assert.AreEqual(marketplaceInBlancesResults.Available, agencyInBalances.Available);
			Assert.AreEqual(marketplaceInBlancesResults.Locked, agencyInBalances.Locked);
			Assert.AreEqual(marketplaceInBlancesResults.LockedAccumulated, agencyInBalances.LockedAccumulated);
			Assert.AreEqual(marketplaceInBlancesResults.Initial, agencyInBalances.Initial);
			Assert.AreEqual(marketplaceInBlancesResults.SpendAccumulated, agencyInBalances.SpendAccumulated);

			Assert.AreEqual(marketplaceOutBlancesResults.LockedAccumulated, agencyOutBalances.LockedAccumulated);
			Assert.AreEqual(marketplaceOutBlancesResults.SpendAccumulated, agencyOutBalances.SpendAccumulated);
		}

		[TestMethod]
		public void BatchNegativeProfitUSD2BTC()
		{
			/*
			3 BTC me costaron 28 500
			1BTc me costo $9 700 


			BTC/USD 9 600.00
			BTC/USD 9 600.01
			Rate  0.***********

			$10 -> BTC
			Comsion ->0.*********** * 100 = %  $0.01
			Net = 9.********** -> $ 9.99

			Converted = BTC 0.********

			BTC/USD 9 700 - BTC/USD 9 600.01 = -99.99  por cada BTC vendido.
			 *  0.******** * -99.99 =  -0.********** -> -0.10
			 *  
			 */
			bool itIsThePresent = false;
			DateTime today1 = DateTime.Now;
			DateTime today2 = today1.AddMilliseconds(1);
			Company company = new Company();
			CurrenciesTest.AddCurrencies();
			company.Sales.CreateStore(1, "Test Store").MakeCurrent();
			string ced="abc123";string accountNumber = "B15461";Customer customer=company.GetOrCreateCustomerById(accountNumber);customer.Identifier=ced;
			CustomerAccount bitcoinAccount = customer.CreateNewAccountFor(Coinage.Coin(Currencies.CODES.BTC));
			CustomerAccount dollarAccount = customer.CreateNewAccountFor(Coinage.Coin(Currencies.CODES.USD));
			Marketplace marketplace = new Marketplace(company, "CR");
			var a = (GamesEngine.Exchange.Agent)marketplace.AddAgent("A");
			var b = a.AddCashier("Juan");
			string employeeName = "Juan";
			var domain = company.Sales.CreateDomain(false, 1, "localhost", PaymentChannels.Agents.INSIDER);
			company.Sales.CurrentStore.Add(domain);
			GuardianTest.LoadProcessors(company);

			#region rate #1
			RegularConversionSpread rate1 = marketplace.CreateConversionSpread(1, today1, itIsThePresent, Coinage.Coin(Currencies.CODES.USD), Coinage.Coin(Currencies.CODES.BTC), 9600.01m, 9600.00m, employeeName);
			Assert.AreEqual(rate1, marketplace.ConversionSpreadAt(today1, Coinage.Coin(Currencies.CODES.USD), Coinage.Coin(Currencies.CODES.BTC)));
			#endregion

			MarketplaceBatch marketPLaceBatch;
			AgentBatch agencyBatch;
			BatchTransactions juanTransactions = SetInitialSettings(marketplace, today1, 2910000m, new Domain[] { domain }, out marketPLaceBatch, out agencyBatch);
			BatchSet agentBatch = juanTransactions.Batch();
			string agentPath = a.FullName;

			int id2 = marketplace.NewTransationNumber();

			var transactionType = marketplace.Company.System.TransactionTypes.SearchByName<ProcessorTransaction>(TransactionType.Deposit.ToString());
			int authorizationNumber = PaymentChannels.Deposit(
				itIsThePresent,
				Coinage.Coin(Currencies.CODES.USD),
				domain,
				transactionType,
				new DespositBody(
					accountNumber, 
					ced, 
					100,
					"deposit test",
					today1,
					"",
					"cris", 
					domain)
				).AuthorizationId;


			DepositTransaction transaction2 = (DepositTransaction)marketplace.From(id2, bitcoinAccount, agentPath, employeeName, domain).Deposit(today1, itIsThePresent, new Dollar(10), employeeName, (PaymentProcessor)marketplace.Company.System.PaymentProcessor.First(), 1);
			DepositTransaction transaction22 = (DepositTransaction)marketplace.FindDraftTransaction(transaction2.Id, agentPath, employeeName);

			Assert.AreEqual(transaction2, transaction22);

			BatchAccount marketplaceInBlancesResults = marketPLaceBatch.SelfReceptionAccounts(Coinage.Coin(Currencies.CODES.USD));
			BatchAccount agencyInBalances = agencyBatch.SelfReceptionAccounts(Coinage.Coin(Currencies.CODES.USD));
			BatchAccount marketplaceOutBlancesResults = marketPLaceBatch.SaleAccount(Coinage.Coin(Currencies.CODES.BTC));
			BatchAccount agencyOutBalances = agencyBatch.SaleAccount(Coinage.Coin(Currencies.CODES.BTC));

			Assert.AreEqual(new Dollar(0m), agencyInBalances.Initial);
			Assert.AreEqual(new Dollar(0m), agencyInBalances.Available);
			Assert.AreEqual(new Dollar(10m), agencyInBalances.Locked);
			Assert.AreEqual(new Dollar(0m), agencyInBalances.LockedAccumulated);
			Assert.AreEqual(new Dollar(0m), agencyInBalances.SpendAccumulated);

			Assert.AreEqual(new Btc(300m), agencyOutBalances.Initial);
			Assert.AreEqual(new Btc(100m), agencyOutBalances.Available);
			Assert.AreEqual(new Btc(0m), agencyOutBalances.Locked);
			Assert.AreEqual(new Btc(0.********m), agencyOutBalances.LockedAccumulated);
			Assert.AreEqual(new Btc(0m), agencyOutBalances.SpendAccumulated);

			Assert.AreEqual(marketplaceInBlancesResults.Available, agencyInBalances.Available);
			Assert.AreEqual(marketplaceInBlancesResults.Locked, agencyInBalances.Locked);
			Assert.AreEqual(marketplaceInBlancesResults.LockedAccumulated, agencyInBalances.LockedAccumulated);
			Assert.AreEqual(marketplaceInBlancesResults.Initial, agencyInBalances.Initial);
			Assert.AreEqual(marketplaceInBlancesResults.SpendAccumulated, agencyInBalances.SpendAccumulated);

			Assert.AreEqual(marketplaceOutBlancesResults.LockedAccumulated, agencyOutBalances.LockedAccumulated);
			Assert.AreEqual(marketplaceOutBlancesResults.SpendAccumulated, agencyOutBalances.SpendAccumulated);

			TransactionCompleted result2 = marketplace.Approve(today1, itIsThePresent, transaction2, 2, employeeName);

			Assert.AreEqual(new Dollar(0m), agencyInBalances.Initial);
			Assert.AreEqual(new Dollar(10m), agencyInBalances.Available);
			Assert.AreEqual(new Dollar(0m), agencyInBalances.Locked);
			Assert.AreEqual(new Dollar(0m), agencyInBalances.LockedAccumulated);
			Assert.AreEqual(new Dollar(0m), agencyInBalances.SpendAccumulated);

			Assert.AreEqual(new Btc(300m), agencyOutBalances.Initial);
			Assert.AreEqual(new Btc(100m), agencyOutBalances.Available);
			Assert.AreEqual(new Btc(0m), agencyOutBalances.Locked);
			Assert.AreEqual(new Btc(0m), agencyOutBalances.LockedAccumulated);
			Assert.AreEqual(new Btc(0.********m), agencyOutBalances.SpendAccumulated);

			Assert.AreEqual(marketplaceInBlancesResults.Available, agencyInBalances.Available);
			Assert.AreEqual(marketplaceInBlancesResults.Locked, agencyInBalances.Locked);
			Assert.AreEqual(marketplaceInBlancesResults.LockedAccumulated, agencyInBalances.LockedAccumulated);
			Assert.AreEqual(marketplaceInBlancesResults.Initial, agencyInBalances.Initial);
			Assert.AreEqual(marketplaceInBlancesResults.SpendAccumulated, agencyInBalances.SpendAccumulated);

			Assert.AreEqual(marketplaceOutBlancesResults.LockedAccumulated, agencyOutBalances.LockedAccumulated);
			Assert.AreEqual(marketplaceOutBlancesResults.SpendAccumulated, agencyOutBalances.SpendAccumulated);
		}


		[TestMethod]
		public void CloseInBatchBalance()
		{
			Company company = new Company();
			CurrenciesTest.AddCurrencies();
			Movements.Storage = new MovementStorageMemory();
			//Integration.UseKafka = false;
			QueueInMemory queue = new QueueInMemory();
			Integration.Kafka = queue;

			bool itIsThePresent = false;
			DateTime today1 = DateTime.Now;
			DateTime today2 = today1.AddMilliseconds(1);
			
			company.Sales.CreateStore(1, "Test Store").MakeCurrent();
			string ced="abc123";string accountNumber = "B15461";Customer customer=company.GetOrCreateCustomerById(accountNumber);customer.Identifier=ced;
			CustomerAccount bitcoinAccount = customer.CreateNewAccountFor(Coinage.Coin(Currencies.CODES.BTC));
			Marketplace marketplace = new Marketplace(company, "CR");
			var a = (GamesEngine.Exchange.Agent)marketplace.AddAgent("A");
			var b = a.AddCashier("Juan");
			string employeeName = "Juan";
			Domain domain = company.Sales.CreateDomain(false, 1, "localhost", PaymentChannels.Agents.INSIDER);
			company.Sales.CurrentStore.Add(domain);
			GuardianTest.LoadProcessors(company);

			#region rate #1
			RegularConversionSpread rate1 = marketplace.CreateConversionSpread(1, today1, itIsThePresent, Coinage.Coin(Currencies.CODES.USD), Coinage.Coin(Currencies.CODES.BTC), 60m, 30m, employeeName);
			Assert.AreEqual(rate1, marketplace.ConversionSpreadAt(today1, Coinage.Coin(Currencies.CODES.USD), Coinage.Coin(Currencies.CODES.BTC)));
			#endregion

			MarketplaceBatch marketPLaceBatch;
			AgentBatch agencyBatch;
			BatchTransactions juanTransactions = SetInitialSettings(marketplace, today1, new Domain[] { domain }, out marketPLaceBatch, out agencyBatch);
			BatchSet agentBatch = juanTransactions.Batch();
			string agentPath = a.FullName;

			int id = marketplace.NewTransationNumber();

			var transactionType = marketplace.Company.System.TransactionTypes.SearchByName<ProcessorTransaction>(TransactionType.Deposit.ToString());
			int authorizationNumber = PaymentChannels.Deposit(
				itIsThePresent,
				Coinage.Coin(Currencies.CODES.USD),
				domain,
				transactionType,
				new DespositBody(
					accountNumber, 
					ced, 
					100,
					"deposit test", 
					today1,
					"", 
					"cris",
					domain)
				).AuthorizationId;
			Assert.AreEqual(1, authorizationNumber);

			DepositTransaction transaction1 = (DepositTransaction)marketplace.From(id, bitcoinAccount, agentPath, employeeName, domain).Deposit(today1, itIsThePresent, new Dollar(100), employeeName, (PaymentProcessor)marketplace.Company.System.PaymentProcessor.First(), 1);
			DepositTransaction transaction = (DepositTransaction)marketplace.FindDraftTransaction(juanTransactions, transaction1.Id);

			TransactionCompleted result1 = marketplace.Approve(today1, itIsThePresent, transaction1, 1, employeeName);

			transactionType = marketplace.Company.System.TransactionTypes.SearchByName<ProcessorTransaction>(TransactionType.Deposit.ToString());
			authorizationNumber = PaymentChannels.Deposit(
				itIsThePresent,
				Coinage.Coin(Currencies.CODES.USD),
				domain,
				transactionType,
				new DespositBody(
					accountNumber, 
					ced,
					100,
					"deposit test", 
					today1,
					"",
					"cris",
					domain)
				).AuthorizationId;
			Assert.AreEqual(2, authorizationNumber);

			DepositTransaction transaction2 = (DepositTransaction)marketplace.From(id, bitcoinAccount, agentPath, "Juan", domain).Deposit(today1, itIsThePresent, new Dollar(100), employeeName, (PaymentProcessor)marketplace.Company.System.PaymentProcessor.First(), 1);
			
			try
			{
				marketplace.SearchAgentBatch("CR").Close(itIsThePresent, today1, "N/A");
				Assert.Fail("It's not possible to close a batch with open nodes.");
			}
			catch (GameEngineException e){}
			try
			{
				marketplace.SearchAgentBatch("CR/A").Close(itIsThePresent, today1, "N/A");
				Assert.Fail("It's not possible to close a batch with open nodes.");
			}
			catch (GameEngineException e){}

			try
			{
				BatchSet juanbatch = marketplace.SearchAgentBatch("CR/A/Juan");
				juanbatch.Close(itIsThePresent, today1, "N/A");
				Assert.Fail("It's not possible to close a batch with draft transactions.");
			}
			catch (GameEngineException e)
			{
				Assert.AreEqual("There are draft transactions pending.", e.Message);
			}

			TransactionCompleted result2 = marketplace.Approve(today1, itIsThePresent, transaction2, 2, employeeName);

			BatchSet batch = marketplace.SearchAgentBatch("CR/A/Juan");
			batch.Close(itIsThePresent, today1, "N/A");

			try
			{
				marketplace.SearchAgentBatch("CR/A/Juan").Close(itIsThePresent, today1, "N/A");
				Assert.Fail("It's not possible to close a batch already closed.");
			}
			catch (GameEngineException e) 
			{
				Assert.AreEqual("You could not close a batch that is already closed", e.Message);
			}

			marketplace.SearchAgentBatch("CR/A").Close(itIsThePresent, today1, "N/A");
			marketplace.SearchAgentBatch("CR").Close(itIsThePresent, today1, "N/A");

			Assert.AreEqual(1, marketplace.Batches.BatchesClosed(today1).Count());
			Assert.AreEqual(BatchStatus.CLOSE, marketplace.SearchAgentBatch("CR").Status);

			try
			{
				marketplace.SearchAgentBatch("CR").Verify(itIsThePresent, today1, "N/A");
				Assert.Fail("It's not possible to verify a batch with unverified nodes.");
			}
			catch (GameEngineException e) { }

			try
			{
				marketplace.SearchAgentBatch("CR/A").Verify(itIsThePresent, today1, "N/A");
				Assert.Fail("It's not possible to verify a batch with unverified nodes.");
			}
			catch (GameEngineException e) { }

			itIsThePresent = true;

			marketplace.SearchAgentBatch("CR/A/Juan").Verify(itIsThePresent, today1, "N/A");
			marketplace.SearchAgentBatch("CR/A").Verify(itIsThePresent, today1, "N/A");
			marketplace.SearchAgentBatch("CR").Verify(itIsThePresent, today1, "N/A");

			string messageSt = queue.Dequeue(queue.TopicForBalances).ToString();
			string[] messages = KafkaMessages.Split(messageSt);
			BalanceMessage message = new BalanceMessage(messages[0]);
			List<BatchBalancesMessage>  lista = message.Details;
			Assert.AreEqual(1, lista.Count);
			message = new BalanceMessage(messages[1]);
			lista = message.Details;
			Assert.AreEqual(3, lista.Count);

			messageSt = queue.Dequeue(queue.TopicForBalances).ToString();
			messages = KafkaMessages.Split(messageSt);
			message = new BalanceMessage(messages[0]);
			lista = message.Details;
			Assert.AreEqual(1, lista.Count);
			message = new BalanceMessage(messages[1]);
			lista = message.Details;
			Assert.AreEqual(3, lista.Count);

			messageSt = queue.Dequeue(queue.TopicForBalances).ToString();
			messages = KafkaMessages.Split(messageSt);
			message = new BalanceMessage(messages[0]);
			lista = message.Details;
			Assert.AreEqual(1, lista.Count);
			message = new BalanceMessage(messages[1]);
			lista = message.Details;
			Assert.AreEqual(3, lista.Count);

			Assert.AreEqual(0, queue.Count(queue.TopicForBalances));
		}

		[TestMethod]
		public void ValidateCanOpenBatch()
		{
			DateTime today1 = DateTime.Now;
			Company company = new Company();
			CurrenciesTest.AddCurrencies();
			company.Sales.CreateStore(1, "Test Store").MakeCurrent();
			Marketplace marketplace = new Marketplace(company, "CR");
			var accounts = new SetOfBalances();
			accounts.AddAccountIn("100-09-0987653-01", new Dollar(5000000));
			accounts.AddAccountOut("100-09-0987653-02", new Dollar(5000000));
			accounts.AddAccountIn("E249628", new Btc(3000));
			accounts.AddAccountOut("6F25325", new Btc(40));
			accounts.AddAccountIn("42C6000", new Eth(100));
			accounts.AddAccountOut("42C5385", new Eth(100));
			marketplace.RealAccounts = accounts;

			int batchNumber = marketplace.Batches.IdentityBatchNumber;
			MarketplaceBatch viernes = marketplace.Batches.AddNewBatch(today1, today1, batchNumber, "Exchange Owner", "Batch para el 05/22/2020");
			viernes.InitialAmount(new Dollar(5000000), "Exchange Owner");
			viernes.InitialAmount(new Btc(300), "Exchange Owner");
			viernes.InitialAmount(new Eth(10), "Exchange Owner");

			Assert.IsFalse(viernes.ExistsAgent("A"));
			AgentBatch agencyA = viernes.AddAgent("A", today1);
			Assert.IsTrue(viernes.ExistsAgent("A"));

			agencyA.ReceiveAmount(new Dollar(5000000), new Dollar(5000000), "Exchange Owner");
			agencyA.ReceiveAmount(new Btc(300), new Dollar(2850000), "Exchange Owner");// Aqui defino cuanto me costo el BTC
			agencyA.ReceiveAmount(new Eth(10), new Dollar(10 * 1), "Exchange Owner");
			Assert.IsFalse(agencyA.CanOpen());
			viernes.Open(true, today1, "N/A");
			Assert.IsTrue(agencyA.CanOpen());
			agencyA.Open(true, today1, "N/A");

			AgentBatch agentA = (AgentBatch)marketplace.SearchAgentBatch(agencyA.FullName, batchNumber);

			AgentBatch juan = agencyA.AddAgent("Juan", today1);
			Assert.IsTrue(agencyA.CanAssignFunds(new Dollar(500)));
			Assert.IsFalse(agencyA.CanAssignFunds(new Btc(500)));
			agencyA.AssignFunds("Juan", batchNumber, new Dollar(5000000), "A", today1);
			agencyA.AssignFunds("Juan", batchNumber, new Btc(200), "A", today1);
			agencyA.AssignFunds("Juan", batchNumber, new Eth(5), "A", today1);

			AgentBatch agentJuan = (AgentBatch)marketplace.SearchAgentBatch(juan.FullName, batchNumber);
			BatchTransactions juanTransactions = agentJuan.BatchTransactions;
			Assert.IsTrue(agentJuan.CanOpen());
			agentJuan.Open(true, today1, "N/A");
		}
	}
}
