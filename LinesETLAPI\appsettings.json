{
  "version": "",
  "WebProfanityKey": "c5860a1489a1d8a6c56c01e3bae02936",
  "WebProfanityUrl": "https://api1.webpurify.com/services/rest/?method=webpurify.live.replace&api_key={0}&text={1}&format=json&replacesymbol=*",
  "CashierUrl": "http://localhost:5000/",
  "DBDairy": {
    "ConnectionStrings": {
      "MySQL": "persistsecurityinfo=True;port=3306;Server=mysql;Database=linesetl;user id=root;password=*********;SslMode=none",
      "SQLServer": "data source=sql.qa4.exchange.com;initial catalog=exch_test_02;user id=sa;password=***********;multipleactiveresultsets=True"
    },
    "DBSelected": ""
  },
  "ActionsBeforeRecovering": "",
  "MockToStart": 0,
  "Security": {
    "ForceAuthority": false,
    "exchange-owner": {
      "Api": "http://security.qa9.ncubo.com:8080/auth/admin/realms/exchange-owner",
      "Authority": "http://security.qa9.ncubo.com:8080/auth/realms/exchange-owner",
      "Audience": "servers",
      "Certificate": "MIICqzCCAZMCBgFzxD2vNzANBgkqhkiG9w0BAQsFADAZMRcwFQYDVQQDDA5leGNoYW5nZS1hZG1pbjAeFw0yMDA4MDYxNDQ1NDRaFw0zMDA4MDYxNDQ3MjRaMBkxFzAVBgNVBAMMDmV4Y2hhbmdlLWFkbWluMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAumOWnzwF1IeoBEazCy+XPOtBd5rtngX7iS46e4jrJR43ku1BafOoZGc3bxULT8KsyLOt5BAgbFnaO9dLt7kAdmpZD12HwpPpIbo2q8BXwDMXOpk5rUAemUoK7oKCO0yAuz0Sa1JdLw2+O0SenFXmXZWOP9YpTXDi2aBhl6aFy9u9JoBsWViu8b5al2RWD2pHJg4XeF/gCer87psgCHnGmwN5tp7UsHiHIuvLgOgqHJhJTpBQcbusz9GpY712UEAQ8KvI/cwSJLNsZ2CCVLG0QpBS5SPXoCmXJNpmn7tflfCashCV7xKNR5v8lKXRjLxWbgHgz1gXRotoFmRUUahUoQIDAQABMA0GCSqGSIb3DQEBCwUAA4IBAQCCkhk0uuo2oyYCRM2rfBvQJiInAhK0tm/GGXMUDCahraBqySQLSS2HncDwD1MHNhZuF0e6Bpblj6nVo+1yCn+Z9Kp99RiZBjACLVJEsrTTApe5pnI35t6nL4Qlm2kRVRoJNe4gHf1Nai/oEci2rViTh8xdSoAFAhAgQD8cCkk6OB8ZAAl1BRtyEqohySWxqnzj++6fYGy25giFphUG8UBXr4KdbOt5NPynnWrR69lreFZga56iA27+S0X02xozNMrI2z+S2HZu7wwXi+1+5yjLv28K5bya2TDFmcfv0hqc6LF+8ESITPXs4PtUfv5pEJosYVHBOLC1xxh4rTFlRy8n",
      "PublicKey": "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAumOWnzwF1IeoBEazCy+XPOtBd5rtngX7iS46e4jrJR43ku1BafOoZGc3bxULT8KsyLOt5BAgbFnaO9dLt7kAdmpZD12HwpPpIbo2q8BXwDMXOpk5rUAemUoK7oKCO0yAuz0Sa1JdLw2+O0SenFXmXZWOP9YpTXDi2aBhl6aFy9u9JoBsWViu8b5al2RWD2pHJg4XeF/gCer87psgCHnGmwN5tp7UsHiHIuvLgOgqHJhJTpBQcbusz9GpY712UEAQ8KvI/cwSJLNsZ2CCVLG0QpBS5SPXoCmXJNpmn7tflfCashCV7xKNR5v8lKXRjLxWbgHgz1gXRotoFmRUUahUoQIDAQAB",
      "DefaultUser": "exchangeadmin",
      "DefaultPassword": "A6m1n2020"
    },
    "exchange-agents": {
      "Api": "http://security.qa9.ncubo.com:8080/auth/admin/realms/exchange-agents",
      "Authority": "http://security.qa9.ncubo.com:8080/auth/realms/exchange-agents",
      "Audience": "servers",
      "Certificate": "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAkrHFxDeT7GygWatIgZlL5Bkva3aOSfCZ6gLE8DTPl75RVJaUt2E9qoyJtWfjjFn5VeZs6MBG9dGEaHBUWhTvswMFZlPYHPld83xJEw91++uP4TcAEPg/NHWGIXgl7tO+ox6QAALrLq9r7scSuF5+thayi53W9qX6V8Twnvc8SSskAwfUx8mfUu5lin35YqJcyJHWZWYELrA8raL/SU6Zd4C0wfa5tpnAyWSJWb4X355/y8zESjs3kAPMzI6EdVkYSP4ob3r2bAHnQKAw09gf+fSt+z6x40dCfqZlB/oUXTNtAD1twFOq1XPZWNbhuh6Y+Mrzv7AZ6oCCy7ZxyYhFZQIDAQAB",
      "PublicKey": "MIICrzCCAZcCBgFzxD4LYTANBgkqhkiG9w0BAQsFADAbMRkwFwYDVQQDDBBleGNoYW5nZS1jYXNoaWVyMB4XDTIwMDgwNjE0NDYwN1oXDTMwMDgwNjE0NDc0N1owGzEZMBcGA1UEAwwQZXhjaGFuZ2UtY2FzaGllcjCCASIwDQYJKoZIhvcNAQEBBQADggEPADCCAQoCggEBAJKxxcQ3k+xsoFmrSIGZS+QZL2t2jknwmeoCxPA0z5e+UVSWlLdhPaqMibVn44xZ+VXmbOjARvXRhGhwVFoU77MDBWZT2Bz5XfN8SRMPdfvrj+E3ABD4PzR1hiF4Je7TvqMekAAC6y6va+7HErhefrYWsoud1val+lfE8J73PEkrJAMH1MfJn1LuZYp9+WKiXMiR1mVmBC6wPK2i/0lOmXeAtMH2ubaZwMlkiVm+F9+ef8vMxEo7N5ADzMyOhHVZGEj+KG969mwB50CgMNPYH/n0rfs+seNHQn6mZQf6FF0zbQA9bcBTqtVz2VjW4boemPjK87+wGeqAgsu2ccmIRWUCAwEAATANBgkqhkiG9w0BAQsFAAOCAQEAZf75iBF+n7jOml22HWHzLLUZOUjrxeeC92PiM7bDrsS8e75Y77AQDLsqHL4SN80rkz8f41qL//KD8+G+yDskR3TsKXxz7oibZkozq+Nta9KnvgT2luasOK8PjOJo6vp9ldcso6NilbiUOYSU4DqcGbtLVKp4EqcJSDQkkf8V4m4JQ83v267oU7etUjQIbDsTZ2YrLSbjTAr5rSfTIO2MX7hMAwB0kznTOe8YcOsuT0oeqH/vPKQ8+KBblp3yA4f71AKV0IYF6GtIAa6PVvPvf+ghmkPP9JE8TCNpnZlHqLUcWNQDMaIt0b3xeyOv6WkBOqkjEzWnNJW2cXd7aaOipA==",
      "DefaultUser": "exchangeadmin",
      "DefaultPassword": "A6m1n2020"
    },
    "exchange-fiero": {
      "Api": "http://security.qa9.ncubo.com:8080/auth/admin/realms/exchange-fiero",
      "Authority": "http://security.qa9.ncubo.com:8080/auth/realms/exchange-fiero",
      "Audience": "servers",
      "Certificate": "MIICqzCCAZMCBgFzxD5F5TANBgkqhkiG9w0BAQsFADAZMRcwFQYDVQQDDA5leGNoYW5nZS1maWVybzAeFw0yMDA4MDYxNDQ2MjJaFw0zMDA4MDYxNDQ4MDJaMBkxFzAVBgNVBAMMDmV4Y2hhbmdlLWZpZXJvMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEApjMzASV1AF9gBrUSqDiEPxy911lZyDnTFULtOeTzBz70K6z4TbgWwDX3OupCNX/5EaWkFYDSKw2iwbVSaQKVEtVSqKkg3xcj3Ss4bkW8qMscGPTI/xqoNe3FeVeqpI/V5bkclaZWrdv4Mj58G/J4qPyH241BHxIZzAnLQX8eyQTIRgA+Dv4hvj8mmbHeKZhOXUhgdINfa/CBTUOJcgCFabK8xMk/JwVqCUc7/RsYrC9kxyVpe1ZRuNqOVSsOfcVk6JYKdBe29WDDiR+kW20hqcupiJWiUj/NmfgOl8nuq9tiUhLYyaPA/MKi+vQ+jJv1h5brIgJe4oGMDUOQ0zA0fQIDAQABMA0GCSqGSIb3DQEBCwUAA4IBAQA4AtMhmzXfL/cI92ieJmojE7vh2pRvb51B3BIOAqadAle0kjFU//gFd4GTUZpVfHAbVXZGmWogmTfvolAveJvJpR1ziyjHeuvLctl5nKlzkJLxcEsfn2flSTiBTCKJ67gJgb5YE5Kv1KCQzWruxgivdm/KS9e7grpfsMACYXVl/lRBwinDb1oA8zEFDs9bosK4BIuvZEQ4f8JLhpIVx/GAjqlIRKYjAHiF0Y2Py5LcbZt3MqMx1MxQlNpQJkothluq4+b6riooKudxy0ZM/FYjp/p8vJr8CLXRD1CwXCntC+dJ8OgS85b6COFoyOrV5ySJW5a6SCXFoconFD37bAHD",
      "PublicKey": "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEApjMzASV1AF9gBrUSqDiEPxy911lZyDnTFULtOeTzBz70K6z4TbgWwDX3OupCNX/5EaWkFYDSKw2iwbVSaQKVEtVSqKkg3xcj3Ss4bkW8qMscGPTI/xqoNe3FeVeqpI/V5bkclaZWrdv4Mj58G/J4qPyH241BHxIZzAnLQX8eyQTIRgA+Dv4hvj8mmbHeKZhOXUhgdINfa/CBTUOJcgCFabK8xMk/JwVqCUc7/RsYrC9kxyVpe1ZRuNqOVSsOfcVk6JYKdBe29WDDiR+kW20hqcupiJWiUj/NmfgOl8nuq9tiUhLYyaPA/MKi+vQ+jJv1h5brIgJe4oGMDUOQ0zA0fQIDAQAB",
      "DefaultUser": "exchangeadmin",
      "DefaultPassword": "A6m1n2020"
    }
  },
  "Logging": {
    "IncludeScopes": false,
    "Debug": {
      "LogLevel": {
        "Default": "Warning"
      }
    },
    "Console": {
      "LogLevel": {
        "Default": "Warning"
      }
    }
  },
  "BIIntegration": {
    "useKafka": true,
    "useDb": false,
    "kafka": {
      "server": "queue.production:9092",
      "topicForLottoGrading": "LottoGrading",
      "topicForTransacctions": "Transactions",
      "topicForGrades": "LottoGrades",
      "topicForDrawings": "LottoDrawings",
      "topicForRecents": "Recents",
      "topicForDomains": "Domains",
      "topicForStoreRegistration": "StoreRegistration",
      "topicForCustomSettings": "CustomSettings",
      "topicForCatalog": "Catalog",
      "topicForCustomers": "Customers",
      "topicForNotifications": "Notifications",
      "topicForProfanity": "Profanity",
      "topicForScheduler": "Scheduler",
      "topicForPrizes": "LottoPrizes",
      "topicForFragments": "Fragments",
      "topicForPayFragments": "PayFragments",
      "topicForMovements": "Movements",
      "topicForDeposits": "Deposits",
      "topicForWithdrawals": "Withdrawals",
      "topicForChallenges": "Challenges",
      "topicForBalances": "Balances",
      "topicForIncomingOperations": "IncomingOperations",
      "topicForOutgoingOperations": "OutgoingOperations",
      "topicForGuardian": "guardian",
      "topicForLinesTags": "LinesTags",
      "topicForLinesScores": "LinesScores",
      "topicForLinesGrading": "LinesGrading",
      "topicForLinesETL": "EtlInfo",
      "group": "games"
    },
    "DBHistorical": {
      "ConnectionStrings": {
        "MySQL": "persistsecurityinfo=True;port=3306;Server=mysql;Database=linesetl;user id=root;password=*********;SslMode=none",
        "SQLServer": "data source=localhost;initial catalog=test;user id=sa;password=**************;multipleactiveresultsets=True"
      },
      "DBSelected": "MySQL"
    }
  },
  "ErrorsSender": {
    "SmtpServer": "smtp.gmail.com",
    "SmtpPort": 587,
    "SmtpUsername": "<EMAIL>",
    "SmtpPassword": "Rdiaz123",
    "UseSender": true,
    "SendTo": ""
  },
  "ElasticApm": {
    "useApm": false,
    "SecretToken": "",
    "ServerUrls": "http://apm:8200", //Set custom APM Server URL (default: http://localhost:8200)
    "ServiceName": "LinesETLAPI", //allowed characters: a-z, A-Z, 0-9, -, _, and space. Default is the entry assembly of the application
    "Environment": "production" // Set the service environment
  },
  "MessageOriginId": 6
}