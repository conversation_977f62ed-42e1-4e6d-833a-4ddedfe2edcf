﻿using GamesEngine.Bets;
using GamesEngine.Marketing.Campaigns.Base;
using Puppeteer.EventSourcing.Libraries;
using Puppeteer.EventSourcing;
using System;
using System.Collections.Generic;
using System.Linq;
using static GamesEngine.Marketing.Campaigns.ActionerStreak;
using GamesEngine.Messaging;
using GamesEngine.RealTime.Events;
using GamesEngine.RealTime;
using GamesEngine.Finance;
using GamesEngine.Business;
using GamesEngine.PurchaseOrders;
using GamesEngine.Settings;
using static GamesEngine.Marketing.Campaigns.RewardBased;
using GamesEngine.Time;

namespace GamesEngine.Marketing.Campaigns.Rewarders
{
    internal class SpinWheelRewarder : CampaignRewarder
    {
        private List<StepSpin> spinStages;

        private SpinWheel[] spinWheels;
        private byte spinWheelSections;
        internal static int DEFAULT_SEGMENTS = 8;

        internal IEnumerable<StepSpin> SpinStages => spinStages;

        internal SpinWheel[] SpinWheels
        {
            get
            {
                return this.spinWheels;
            }
            set
            {
                this.spinWheels = value;
            }
        }

        public SpinWheelRewarder(CampaignBase campaignBase) : base(campaignBase)
        {
            spinStages = new List<StepSpin>();

            if (Campaign is ActionerStreak streakCampaign)
            {
                int maxLevels = streakCampaign.MaxLevels;
                if (maxLevels <= 0) throw new ArgumentException(nameof(maxLevels));

                spinWheels = new SpinWheel[maxLevels];
                spinWheelSections = (byte)DEFAULT_SEGMENTS;

                var spinWheelReward = this;
                for (int i = 0; i < maxLevels; i++)
                {
                    var spinWheelSegmentLabels = new string[spinWheelReward.SpinWheelSections];
                    var spinWheelSegmentAmountReward = new decimal[spinWheelReward.SpinWheelSections];
                    for (int j = 0; j < spinWheelReward.SpinWheelSections; j++)
                    {
                        spinWheelSegmentLabels[j] = $"Segment {j} level {i}";
                        spinWheelSegmentAmountReward[j] = j + 1;
                    }
                    var rewardDefault = new SpinWheelSettings(spinWheelSegmentLabels, spinWheelSegmentAmountReward, spinWheelSections);

                    AddSpinWheel(i, rewardDefault, $"label level {i}");
                }
            }
            else if (Campaign is OnTheSpot spinKickCampaign)
            {
                SpinWheel campaignSpinWheel = spinKickCampaign.DEFAULT_SPIN_WHEEL;
                int totalSegments = campaignSpinWheel.SpinWheelSettings.RewardsAmount.Length;
                spinWheelSections = (byte)totalSegments;
                spinWheels = new SpinWheel[] { campaignSpinWheel };
            }
        }

        internal int TotalSpins { get; set; }

        internal override int TotalPlayers
        {
            get
            {
                return playersPurchase.Count;
            }
        }

        internal IEnumerable<SpinWheel> AllSpinWheels()
        {
            return spinWheels;
        }

        internal SpinWheel SpinWheel(int levelIndex)
        {
            if (levelIndex < 0 || levelIndex >= TotalOfLevels) throw new ArgumentException(nameof(levelIndex));
            return spinWheels[levelIndex];
        }

        internal bool AreSpinStagesSetup(out string message)
        {
            if (spinStages == null)
            {
                message = $"The spin stages is null.";
                return false;
            }

            if (!spinStages.Any())
            {
                message = $"The spin stages does not contain any record.";
                return false;
            }

            foreach (StepSpin spinStage in spinStages)
            {
                if (!spinStage.WasModified)
                {
                    message = $"The spin stage with title {spinStage.Title} have not been set yet.";
                    return false;
                }
            }
            message = "";
            return true;
        }

        internal bool AreSpinWheelsSetup(out string message)
        {
            if (spinWheels == null)
            {
                message = $"The spin wheels is null.";
                return false;
            }

            if (!spinWheels.Any())
            {
                message = $"The spin wheels does not contain any record.";
                return false;
            }

            foreach (SpinWheel spinWheel in spinWheels)
            {
                if (!spinWheel.WasModified)
                {
                    message = $"The spin wheels with title {spinWheel.Title} have not been set yet.";
                    return false;
                }
            }
            message = "";
            return true;
        }

        internal int TotalOfLevels => spinWheels.Length;

        internal int SpinWheelSections
        {
            get
            {
                return spinWheelSections;
            }
            set
            {
                if (value <= 0 || value > 255) throw new ArgumentException(nameof(spinWheelSections));
                spinWheelSections = (byte)value;
            }
        }

        internal void ModifySpinWheel(DateTime now, string titleOfLevel, int level, int daysOnStreak)
        {
            if (level < 0) throw new ArgumentException(nameof(level));
            if (daysOnStreak <= 0) throw new ArgumentException(nameof(daysOnStreak));
            if (level < 0 || level > Campaign.MaxLevels) throw new GameEngineException("Can not use level highier than max level or less than 0");

            ModifySpinWheel(level, titleOfLevel, daysOnStreak);
        }

        internal void ModifySpinWheel(string[] spinWheelSegmentLabelsReward, decimal[] spinWheelSegmentAmountReward, string titleOfLevel, int level, int streakGoal)
        {
            if (spinWheelSegmentLabelsReward == null) throw new ArgumentException(nameof(spinWheelSegmentLabelsReward));
            if (spinWheelSegmentAmountReward == null) throw new ArgumentException(nameof(spinWheelSegmentAmountReward));
            if (spinWheelSegmentLabelsReward.Length < 2 || spinWheelSegmentAmountReward.Length < 2) throw new GameEngineException("Need at least 2 segments.");
            if (spinWheelSegmentLabelsReward.Length != spinWheelSegmentAmountReward.Length) throw new GameEngineException("Segments are not the same size.");
            if (level < 0 || level > Campaign.MaxLevels) throw new GameEngineException("Can not use level highier than max level or less than 0");
            if (streakGoal <= 0) throw new ArgumentException($"Cannot be less than zero {nameof(streakGoal)}.");

            foreach (var label in spinWheelSegmentLabelsReward)
            {
                if (string.IsNullOrWhiteSpace(label)) throw new GameEngineException("Can not assing empty or null a label"); ;
            }
            foreach (var rewardAmount in spinWheelSegmentAmountReward)
            {
                if (rewardAmount <= 0) throw new GameEngineException("Can not assing 0 or less than 0 a reward");
            }

            int spinWheelSections = spinWheelSegmentAmountReward.Length;
            SpinWheelSections = spinWheelSections;

            SpinWheel spinWheel = LevelSpinWheel(level);
            if (spinWheel == null)
            {
                var rewardSettings = new SpinWheelSettings(spinWheelSegmentLabelsReward, spinWheelSegmentAmountReward, SpinWheelSections);
                CreateSpinWheel(level, rewardSettings, titleOfLevel, streakGoal);
            }
            else
            {
                var rewardSettings = new SpinWheelSettings(spinWheelSegmentLabelsReward, spinWheelSegmentAmountReward, SpinWheelSections);
                ModifySpinWheel(level, rewardSettings, titleOfLevel, streakGoal);
            }
        }

        internal void ModifySpinWheel(string[] spinWheelSegmentLabelsReward, decimal[] spinWheelSegmentAmountReward, int level)
        {
            if (spinWheelSegmentLabelsReward == null) throw new ArgumentException(nameof(spinWheelSegmentLabelsReward));
            if (spinWheelSegmentAmountReward == null) throw new ArgumentException(nameof(spinWheelSegmentAmountReward));
            if (spinWheelSegmentLabelsReward.Length < 2 || spinWheelSegmentAmountReward.Length < 2) throw new GameEngineException("Need at least 2 segments.");
            if (spinWheelSegmentLabelsReward.Length != spinWheelSegmentAmountReward.Length) throw new GameEngineException("Segments are not the same size.");
            if (level < 0 || level > Campaign.MaxLevels) throw new GameEngineException("Can not use level highier than max level or less than 0");
            SpinWheel spinWheel = LevelSpinWheel(level);
            if (spinWheel == null) throw new GameEngineException($"SpinWheel has not been created before.");
            string titleOfLevel = spinWheel.Title;
            int streakGoal = spinWheel.StreakGoal;
            ModifySpinWheel(spinWheelSegmentLabelsReward, spinWheelSegmentAmountReward, titleOfLevel, level, streakGoal);
        }

        internal void ModifySpinWheelSegments(DateTime now, int segments)
        {
            if (Campaign.HasStarted) throw new GameEngineException($"Streak Campaign {Campaign.PromotionNumber} has started yet. Can not change level.");
            if (segments < 2) throw new ArgumentException(nameof(segments));

            ModifySpinWheelSegments(segments);
            SpinWheelSections = segments;
        }

        internal void AddSpinWheel(int levelIndex, SpinWheelSettings spinWheelSettings, string spinWheelTitle)
        {
            if (spinWheelSettings == null) throw new ArgumentException(nameof(spinWheelSettings));
            if (levelIndex < 0 || levelIndex >= TotalOfLevels) throw new ArgumentException(nameof(levelIndex));
            if (spinWheelSettings.TotalLabels == 0) throw new ArgumentException(nameof(spinWheelSettings));
            if (string.IsNullOrWhiteSpace(spinWheelTitle)) throw new ArgumentException(nameof(spinWheelTitle));
            const int DEFAULT_DAYS_STREAK = 1;

            SpinWheel spinWheel = spinWheels[levelIndex];
            if (spinWheel == null)
            {
                spinWheels[levelIndex] = new SpinWheel(levelIndex, spinWheelTitle, spinWheelSettings, DEFAULT_DAYS_STREAK);
                AddDefaultSpinStage(levelIndex);
            }
            else
            {
                throw new GameEngineException("Index Spin Wheel, was already added");
            }
        }

        internal void CreateSpinWheel(int levelIndex, SpinWheelSettings spinWheelSettings, string spinWheelTitle, int streakGoal)
        {
            if (spinWheelSettings == null) throw new ArgumentException(nameof(spinWheelSettings));
            if (levelIndex < 0) throw new ArgumentException(nameof(levelIndex));
            if (streakGoal <= 0) throw new ArgumentException(nameof(streakGoal));
            if (spinWheelSettings.TotalLabels == 0) throw new ArgumentException(nameof(spinWheelSettings));
            if (string.IsNullOrWhiteSpace(spinWheelTitle)) throw new ArgumentException(nameof(spinWheelTitle));

            spinWheels[levelIndex] = new SpinWheel(levelIndex, spinWheelTitle, spinWheelSettings, streakGoal);
        }

        internal void ModifySpinWheel(int levelIndex, string spinWheelTitle, int daysStreak)
        {
            if (levelIndex < 0) throw new ArgumentException(nameof(levelIndex));
            if (daysStreak <= 0) throw new ArgumentException(nameof(daysStreak));
            if (string.IsNullOrWhiteSpace(spinWheelTitle)) throw new ArgumentException(nameof(spinWheelTitle));

            spinWheels[levelIndex].UpdateDaysStreakTitleOfLevel(spinWheelTitle, daysStreak);
            spinWheels[levelIndex].WasModified = true;
        }

        internal void ModifySpinWheel(int levelIndex, SpinWheelSettings spinWheelSettings, string spinWheelTitle, int streakGoal)
        {
            if (spinWheelSettings == null) throw new ArgumentException(nameof(spinWheelSettings));
            if (string.IsNullOrWhiteSpace(spinWheelTitle)) throw new ArgumentException(nameof(spinWheelTitle));
            if (spinWheelSettings.TotalLabels == 0) throw new ArgumentException(nameof(spinWheelSettings));
            if (streakGoal <= 0) throw new ArgumentException(nameof(streakGoal));

            spinWheels[levelIndex].Title = spinWheelTitle;
            spinWheels[levelIndex].StreakGoal = streakGoal;
            spinWheels[levelIndex].SpinWheelSettings = spinWheelSettings;
            spinWheels[levelIndex].WasModified = true;
        }

        internal SpinWheel LevelSpinWheel(int level)
        {
            if (level < 0 || level >= TotalOfLevels) throw new ArgumentException(nameof(level));

            SpinWheel spinWheel = spinWheels[level];
            return spinWheel;
        }

        internal void ModifyLevel(int levels)
        {
            bool forceCreate = false;
            if (spinWheels == null)
            {
                spinWheels = new SpinWheel[levels];
                spinWheelSections = (byte)DEFAULT_SEGMENTS;
                forceCreate = true;
            }

            if (spinWheels.Length > levels)
            {
                Array.Resize(ref spinWheels, levels);
                if (spinStages != null)
                {
                    int currentSize = spinStages.Count;
                    int totalToRemove = currentSize - levels;
                    if (totalToRemove > 0)
                    {
                        spinStages.RemoveRange(levels, totalToRemove);
                    }
                }
            }
            else if (spinWheels.Length < levels)
            {
                var lastLength = spinWheels.Length;
                Array.Resize(ref spinWheels, levels);

                for (int i = lastLength; i < levels; i++)
                {
                    var spinWheelSegmentLabels = new string[SpinWheelSections];
                    var spinWheelSegmentAmountReward = new decimal[SpinWheelSections];
                    for (int j = 0; j < SpinWheelSections; j++)
                    {
                        spinWheelSegmentLabels[j] = $"Segement {j} level {i}";
                        spinWheelSegmentAmountReward[j] = j + 1;
                    }
                    var rewardDefault = new SpinWheelSettings(spinWheelSegmentLabels, spinWheelSegmentAmountReward, spinWheelSections);

                    AddSpinWheel(i, rewardDefault, $"label level {i}");
                }
            }
            else if (forceCreate)
            {
                for (int i = 0; i < levels; i++)
                {
                    var spinWheelSegmentLabels = new string[SpinWheelSections];
                    var spinWheelSegmentAmountReward = new decimal[SpinWheelSections];
                    for (int j = 0; j < SpinWheelSections; j++)
                    {
                        spinWheelSegmentLabels[j] = $"Segement {j} level {i}";
                        spinWheelSegmentAmountReward[j] = j + 1;
                    }
                    var rewardDefault = new SpinWheelSettings(spinWheelSegmentLabels, spinWheelSegmentAmountReward, spinWheelSections);

                    AddSpinWheel(i, rewardDefault, $"label level {i}");
                }
            }
        }

        internal void ModifySpinWheelSegments(int segments)
        {
            if (segments < 2) throw new ArgumentException(nameof(segments));

            if (spinWheelSections > segments)
            {
                SpinWheelSections = segments;
                for (int i = 0; i < spinWheels.Length; i++)
                {
                    spinWheels[i].SpinWheelSettings.ModifyArraysLength(segments);
                }

            }
            else if (spinWheelSections < segments)
            {
                int totalLabels = spinWheelSections;

                for (int i = 0; i < spinWheels.Length; i++)
                {
                    spinWheels[i].SpinWheelSettings.ModifyArraysLength(segments);
                    var rewardsAmount = spinWheels[i].SpinWheelSettings.RewardsAmount;
                    var labels = spinWheels[i].SpinWheelSettings.Labels;

                    for (int j = totalLabels; j < segments; j++)
                    {
                        labels[j] = $"Segement {j} level {i}";
                        rewardsAmount[j] = j + 1;
                    }

                    spinWheels[i].SpinWheelSettings.AddSegments(labels, rewardsAmount);
                }
            }

        }

        internal override bool IsConfigured()
        {
            if (Campaign is ActionerStreak)
            {
                return AreSpinWheelsSetup(out _);
            }
            else if (Campaign is RewardBased)
            {
                return AreSpinWheelsSetup(out _) && AreSpinStagesSetup(out _);
            }
            else if (Campaign is OnTheSpot)
            {
                return AreSpinWheelsSetup(out _);
            }
            else
            {
                throw new GameEngineException("The campaign is not supported yet.");
            }
        }

        internal void SetupStepStage(List<decimal> lowerLimits, List<decimal> upperLimits)
        {
            if (Campaign is not RewardBased rewardBased) throw new GameEngineException("The campaign is not supported yet in this reward.");
            decimal minPurchaseAllowed = rewardBased.MinPurchaseAllowed;

            string errorMessage = SpinWheelRewarder.CheckStepStage(minPurchaseAllowed, lowerLimits, upperLimits);
            if (!string.IsNullOrEmpty(errorMessage)) throw new GameEngineException(errorMessage);

            spinStages.Clear();
            int totalSteps = lowerLimits.Count;
            for (int i = 0; i < totalSteps; i++)
            {

                decimal lower = lowerLimits[i];
                decimal upper = upperLimits[i];

                SpinWheel spinWheel = LevelSpinWheel(i);
                StepSpin step = new StepSpin(lower, upper);
                if (i == totalSteps - 1)
                {
                    step.Title = $"More than ${step.LowerLimit}";
                }
                else
                {
                    step.Title = $"Between ${step.LowerLimit} to ${step.UpperLimit}";
                }
                spinWheel.Title = step.Title;

                step.WasModified = true;
                spinStages.Add(step);
            }
        }

        internal void SetupSpinWheel(string[] spinWheelSegmentLabelsReward, decimal[] spinWheelSegmentAmountReward, string titleOfLevel, int level, int streakGoal)
        {
            if (spinWheelSegmentLabelsReward == null) throw new ArgumentException(nameof(spinWheelSegmentLabelsReward));
            if (spinWheelSegmentAmountReward == null) throw new ArgumentException(nameof(spinWheelSegmentAmountReward));
            if (spinWheelSegmentLabelsReward.Length < 2 || spinWheelSegmentAmountReward.Length < 2) throw new GameEngineException("Need at least 2 segments.");
            if (spinWheelSegmentLabelsReward.Length != spinWheelSegmentAmountReward.Length) throw new GameEngineException("Segments are not the same size.");
            if (level < 0 || level > Campaign.MaxLevels) throw new GameEngineException("Can not use level highier than max level or less than 0");
            if (streakGoal <= 0) throw new ArgumentException($"Cannot be less than zero {nameof(streakGoal)}.");

            foreach (var label in spinWheelSegmentLabelsReward)
            {
                if (string.IsNullOrWhiteSpace(label)) throw new GameEngineException("Can not assing empty or null a label"); ;
            }
            foreach (var rewardAmount in spinWheelSegmentAmountReward)
            {
                if (rewardAmount <= 0) throw new GameEngineException("Can not assing 0 or less than 0 a reward");
            }

            int spinWheelSections = spinWheelSegmentAmountReward.Length;
            SpinWheelSections = spinWheelSections;

            SpinWheel spinWheel = LevelSpinWheel(level);
            if (spinWheel == null)
            {
                var rewardSettings = new SpinWheelSettings(spinWheelSegmentLabelsReward, spinWheelSegmentAmountReward, SpinWheelSections);
                CreateSpinWheel(level, rewardSettings, titleOfLevel, streakGoal);
            }
            else
            {
                var rewardSettings = new SpinWheelSettings(spinWheelSegmentLabelsReward, spinWheelSegmentAmountReward, SpinWheelSections);
                ModifySpinWheel(level, rewardSettings, titleOfLevel, streakGoal);
            }
        }

        internal static string CheckStepStage(decimal minPurchaseAllowed, List<decimal> lowerLimits, List<decimal> upperLimits)
        {
            if (lowerLimits == null) return $"The {lowerLimits} list cannot be Null.";
            if (upperLimits == null) return $"The {upperLimits} list cannot be Null.";
            if (!lowerLimits.Any() || !upperLimits.Any()) return $"The setup list does not contain any records.";
            if (lowerLimits.Count != upperLimits.Count) return "The size of lists are different.";

            decimal firstStage = lowerLimits.First();
            if (firstStage != minPurchaseAllowed) return $"The first stage {firstStage} does not match with the min purchase allowed {minPurchaseAllowed}.";

            decimal lastLimit = 0;
            for (int i = 0; i < lowerLimits.Count; i++)
            {
                decimal lower = lowerLimits[i];
                decimal upper = upperLimits[i];
                if (upper <= lower) return $"The level {i + 1}, variable {nameof(upper)}={upper} needs to be greater than  variable {nameof(lower)}={lower}.";
                if (upper <= lastLimit) return $"The level {i + 1}, variable {nameof(upper)}={upper} needs to be greater than variable {nameof(lastLimit)}={lastLimit}.";

                if (i == 0)
                {
                    lastLimit = upper;
                    continue;
                }

                if (lastLimit != lower)
                {
                    return $"The current limit {nameof(lower)}={lower} in level {i + 1} does not match with previous limit {nameof(lastLimit)}={lastLimit} level {i}.";
                }

                lastLimit = upper;
            }
            return string.Empty;
        }

        internal void AddDefaultSpinStage(int stageIndex)
        {
            StepSpin step = new StepSpin(0, 0);

            if (Campaign is RewardBased rewardBased)
            {
                if (!spinStages.Any())
                {
                    step.LowerLimit = rewardBased.MinPurchaseAllowed;
                }
            }

            int titleIndex = spinStages.Count + 1;
            step.Title = $"Default {titleIndex}";

            StepSpin stepSpin = spinStages.ElementAtOrDefault(stageIndex);
            if (stepSpin == null)
            {
                spinStages.Add(step);
            }
            else
            {
                spinStages[stageIndex] = step;
            }

        }

        internal void CalculatePlayerSpins(bool itIsThePresent, DateTime now, Player player, decimal purchaseAmount, Subscriber from)
        {
            if (now == DateTime.MinValue) throw new GameEngineException("The date cannot be the default value.");
            if (player == null) throw new GameEngineException("The player cannot be null.");
            if (purchaseAmount < 0) throw new GameEngineException("The purchase amount cannot be less than 0.");
            if (from == null) throw new GameEngineException("The subscriber cannot be null.");

            if (!IsConfigured()) return;

            StepSpin stepSpin = StageSpin(purchaseAmount);
            if (stepSpin == null) throw new GameEngineException("The step spin is null.");

            int indexStepSpin = spinStages.IndexOf(stepSpin);
            if (indexStepSpin == -1) throw new GameEngineException("The index of the step spin was not found.");

            SpinWheel spinWheel = spinWheels[indexStepSpin];
            spinWheel.PrizePlayer(player);

            if (itIsThePresent)
            {
                string currency = Campaign.AllowedCurrency;
                player.AddMessage(from, $"{Campaign.Name}: Congratulations you won an spin in you purchase of {currency}:{purchaseAmount}");
            }
        }

        internal StepSpin StageSpin(decimal amount)
        {
            if (amount < 0) throw new ArgumentException(nameof(amount));
            if (!spinStages.Any()) throw new GameEngineException("Spin Stages or Ranges are not setup yet.");

            StepSpin lastStepSpin = spinStages.LastOrDefault();
            if(lastStepSpin == null) throw new GameEngineException("The last spin stages is null.");

            decimal topLimit = lastStepSpin.UpperLimit;
            if (amount >= topLimit)
            {
                return lastStepSpin;
            }

            StepSpin result = spinStages.Find(stage => amount >= stage.LowerLimit && amount < stage.UpperLimit);
            return result;
        }

        internal int PlayerMaxLevel(Player player)
        {
            int maxLevel = -1;

            foreach (SpinWheel spinWheel in spinWheels.Reverse())
            {
                if (spinWheel.AvaliableSpins(player) > 0)
                {
                    maxLevel = spinWheel.SpinWheelLevel;
                    break;
                }
            }

            return maxLevel;
        }

        internal int GenerateRamdomSection(int playerLevel)
        {
            if (playerLevel < 0 || playerLevel >= TotalOfLevels) throw new ArgumentException(nameof(playerLevel));
            return LevelSpinWheel(playerLevel).GenerateRamdomSection();
        }

        internal SpinResult Spin(bool itIsThePresent, DateTime now, int playerLevel, Player player, int index, string who, int storeId, int domainId)
        {
            if (player == null) throw new ArgumentException(nameof(player));
            if (playerLevel <= 0 && playerLevel > 255) throw new ArgumentException(nameof(player));
            if (now == DateTime.MinValue) throw new ArgumentException(nameof(now));
            if (storeId <= 0) throw new GameEngineException($"The Store ID {storeId} is invalid.");
            if (string.IsNullOrWhiteSpace(who)) throw new GameEngineException($"Who is Invalid.");

            string campaignCountryCode = Campaign.TimeZone;
            string serverCountryCode = Campaign.Company.TimeZone;
            DateTime fixedTzNow = TimeZoneConverter.Instance.ConvertTime(now, serverCountryCode, campaignCountryCode);
            if (fixedTzNow < Campaign.StartDate) throw new GameEngineException("It is not allowed to register a purchase in the campaign if it has not started yet");

            string exceptionMessage = Campaign.IsApplicableAtMainAction(fixedTzNow, player, 0, storeId);
            if (!string.IsNullOrEmpty(exceptionMessage)) throw new GameEngineException($"Not applicable action: " + exceptionMessage);

            if (playerLevel >= Campaign.MaxLevels) throw new GameEngineException($"Streak campaign Max Level is {Campaign.MaxLevels - 1}. The Player is Spining out of limit level allow.");

            SpinWheel spinWheel = LevelSpinWheel(playerLevel);
            SpinResult spinResult = spinWheel.SpinTheWheel(player, index);

            if (Campaign is ActionerStreak actionerStreak)
            {
                StreakBaseRules streakScale = actionerStreak.StreakScale;
                if (streakScale is StreakAmountRules amountScale)
                {
                    amountScale.UpdateCalculateRights(player);
                }
            }

            if (Campaign.Budget.CurrencyCode == Currencies.CODES.FP.ToString() || Campaign.Budget.CurrencyCode == Currencies.CODES.KRWFP.ToString())
            {
                Campaign.IncrementGivenTimesNumber();

                decimal amount = spinResult.PrizeValue;
                Currency currency = Currency.Factory(Campaign.Budget.CurrencyCode, amount);
                Campaign.Budget.Disburse(currency);

                Customer customer = player.Customer;
                Campaign.RegisterBeneficiary(customer);
            }

            string reference = $"{Campaign.PromotionNumber}-{fixedTzNow.ToString("yy")}{fixedTzNow.ToString("HHmmss")}{fixedTzNow.DayOfYear.ToString("000")}{fixedTzNow.ToString("yy")}";
            if (reference.Length > Movement.MAX_REFERENCE_LENGTH) reference = reference.Substring(0, Movement.MAX_REFERENCE_LENGTH);

            if (itIsThePresent)
            {
                string accountNumber = Campaign.Budget.CurrencyCode;
                using (KafkaMessagesBuffer buffer = new KafkaMessagesBuffer(itIsThePresent, Integration.Kafka.TopicForDeposits))
                {
                    DepositMessage deposit = new DepositMessage(
                        player.Customer.AccountNumber,
                        Campaign.PromotionNumber,
                        Coinage.Coin(Campaign.Budget.CurrencyCode),
                        storeId,
                        spinResult.PrizeValue,
                        Campaign.Name,
                        Campaign.Description,
                        reference,
                        accountNumber,
                        WholePaymentProcessor.NoPaymentProcessor,
                        string.Empty,
                        player.Agent,
                        Campaign.Name
                        );
                    buffer.Send(deposit);
                }

                SaleCampaignEvent campaignEvent = new SpinRewardEvent(fixedTzNow, Campaign.PromotionNumber, Campaign.CampaignType, Campaign.RewardType, Campaign.ScaleType, player.Id, spinResult.PrizeValue);
                PlatformMonitor.GetInstance().WhenNewEvent(campaignEvent);
            }

            Campaign.TotalAmountDeposited += spinResult.PrizeValue;
            spinWheel.TotalGiveAmount += spinResult.PrizeValue;

            TotalSpins++;

            Campaign.StoreCampaignReward(itIsThePresent, player.Customer.AccountNumber, spinResult.PrizeIndex, spinResult.PrizeLabel, spinResult.PrizeValue, playerLevel, fixedTzNow, storeId, domainId);

            return spinResult;
        }

        internal int HasAvailableSpins(Player player)
        {
            if (player == null) throw new ArgumentException(nameof(player));

            int availableSpins = 0;
            for (int i = 0; i < spinWheels.Length; i++)
            {
                availableSpins += LevelSpinWheel(i).AvaliableSpins(player);
            }
            return availableSpins;
        }

        internal bool HasAvailableSpin(Player playerId, int playerLevel)
        {
            if (playerId == null) throw new ArgumentException(nameof(playerId));
            if (playerLevel < 0 || playerLevel > Campaign.MaxLevels) throw new GameEngineException($"Can not send a highier level than {Campaign.MaxLevels} or less than 0");

            return LevelSpinWheel(playerLevel).AvaliableSpins(playerId) > 0;
        }

        internal override void Clear()
        {
            foreach (SpinWheel spinWheel in AllSpinWheels())
            {
                spinWheel.Clear();
            }
        }

        List<AvailableSpinByLevel> availableSpinByLevels = new List<AvailableSpinByLevel>();
        internal IEnumerable<AvailableSpinByLevel> AvailableSpins(Player player)
        {
            if (player == null) throw new ArgumentException(nameof(player));

            int startIndex = AllSpinWheels().Count() - 1;
            CampaignScaleType scaleTypeValue = Campaign.ScaleTypeValue;

            availableSpinByLevels.Clear();
            for (int wheelIndex = startIndex; wheelIndex >= 0; wheelIndex--)
            {
                int availableSpins = LevelSpinWheel(wheelIndex).AvaliableSpins(player);
                if (availableSpins > 0)
                {
                    if (scaleTypeValue == CampaignScaleType.AmountCounter)
                    {
                        for (int spinIndex = 0; spinIndex < availableSpins; spinIndex++)
                        {
                            availableSpinByLevels.Add(new AvailableSpinByLevel(1, wheelIndex));
                        }
                    }
                    else
                    {
                        availableSpinByLevels.Add(new AvailableSpinByLevel(availableSpins, wheelIndex));
                    }
                }
            }
            return availableSpinByLevels;
        }

        internal override decimal TotalAmountByPlayer(Player player)
        {
            if (player == null) throw new ArgumentException(nameof(player));

            decimal totalPurchase;
            if (playersPurchase.TryGetValue(player, out totalPurchase))
            {
                return totalPurchase;
            }
            return 0;
        }

        internal override CampaignRewarder Copy(CampaignBase campaignBase)
        {
            SpinWheelRewarder copyRewarder = new SpinWheelRewarder(campaignBase);

            copyRewarder.spinWheelSections = spinWheelSections;
            foreach (StepSpin stepSpin in spinStages)
            {
                copyRewarder.spinStages.Add(stepSpin.Copy());
            }
            for (int i = 0; i < spinWheels.Length; i++)
            {   
                copyRewarder.spinWheels[i] = spinWheels[i].Copy();
            }
            return copyRewarder;
        }

        internal override decimal TotalAmountDepositedByPlayer(Player player)
        {
            if (player == null) throw new ArgumentException(nameof(player));

            decimal totalDeposited = 0;
            for (int i = 0; i < spinWheels.Length; i++)
            {
                totalDeposited += spinWheels[i].DepositedAmount(player);
            }
            return totalDeposited;
        }
    }

    [Puppet]
    internal class SpinWheel : Objeto
    {
        private Dictionary<Player, SpinData> spinDataByPlayer = new Dictionary<Player, SpinData>();
        private SpinWheelSettings spinWheelSettings;
        private string title;
        private int spinWheelLevel = -1;
        private int streakGoal = 0;//DAYS OR AMOUNTS
        private bool wasModified = false;

        internal int TotalSpins { get; set; }
        internal decimal TotalGiveAmount { get; set; }

        internal string Title
        {
            get
            {
                return title;
            }
            set
            {
                this.title = value;
            }
        }
        internal int SpinWheelLevel { get { return spinWheelLevel; } }

        internal int TotalPlayers { get { return spinDataByPlayer.Keys.Count; } }

        internal SpinWheelSettings SpinWheelSettings
        {
            get
            {
                return spinWheelSettings;
            }
            set
            {
                spinWheelSettings = value;
            }
        }
        internal bool WasModified
        {
            get
            {
                return wasModified;
            }
            set
            {
                wasModified = value;
            }
        }
        internal int StreakGoal
        {
            get
            {
                return streakGoal;
            }
            set
            {
                this.streakGoal = value;
            }
        }

        internal int TotalOfSegments
        {
            get
            {
                if (spinWheelSettings == null) throw new GameEngineException($"The {nameof(spinWheelSettings)} variable must be initize during the constructor of {nameof(SpinWheel)}. Please check");
                return spinWheelSettings.TotalLabels;
            }
        }

        private SpinWheel() { }

        internal SpinWheel(int spinWheelLevel, string title, SpinWheelSettings spinWheelSettings, int streakGoal)
        {
            if (spinWheelSettings == null) throw new ArgumentException(nameof(spinWheelSettings));
            if (spinWheelLevel < 0) throw new ArgumentException(nameof(spinWheelLevel));
            if (string.IsNullOrWhiteSpace(title)) throw new ArgumentException(nameof(title));
            if (streakGoal <= 0) throw new ArgumentException(nameof(streakGoal));

            this.spinWheelSettings = spinWheelSettings;
            this.title = title;
            this.spinWheelLevel = spinWheelLevel;
            this.streakGoal = streakGoal;
        }

        private static Random numberGenerator = new Random();
        internal SpinResult SpinTheWheel(Player playerId, int index)
        {
            if (playerId == null) throw new ArgumentException(nameof(playerId));
            if (spinWheelSettings == null) throw new GameEngineException($"Main array of prizes is null {nameof(spinWheelSettings)}");
            if (spinWheelLevel == -1) throw new GameEngineException($"The Wheel does not have a assigned level {nameof(spinWheelLevel)}");

            SpinData spinData;
            if (!spinDataByPlayer.TryGetValue(playerId, out spinData))
            {
                throw new GameEngineException($"Player has never been prize, no data spins for this player {playerId} was found on this wheel level {spinWheelLevel}");
            }

            if (spinData.AvaliableSpins == 0)
            {
                throw new GameEngineException($"Player does not have avaliable spins in this wheel in level {spinWheelLevel}");
            }

            string prizeLabel = spinWheelSettings.Labels[index];
            decimal prizeValue = spinWheelSettings.RewardsAmount[index];

            spinData.UseSpinChance();
            spinData.AppendTotalDeposited(prizeValue);

            spinDataByPlayer[playerId] = spinData;
            return new SpinResult(index, prizeLabel, prizeValue);
        }

        internal int GenerateRamdomSection()
        {
            return numberGenerator.Next(0, spinWheelSettings.TotalLabels);
        }

        internal int AvaliableSpins(Player playerId)
        {
            if (playerId == null) throw new ArgumentException(nameof(playerId));
            SpinData spinData;
            if (spinDataByPlayer.TryGetValue(playerId, out spinData))
            {
                return spinData.AvaliableSpins;
            }
            return 0;
        }

        internal int SpinTimes(Player player)
        {
            if (player == null) throw new ArgumentException(nameof(player));
            SpinData spinData;
            if (spinDataByPlayer.TryGetValue(player, out spinData))
            {
                return spinData.SpinCounter;
            }
            return 0;
        }

        internal decimal DepositedAmount(Player player)
        {
            if (player == null) throw new ArgumentException(nameof(player));
            SpinData spinData;
            if (spinDataByPlayer.TryGetValue(player, out spinData))
            {
                return spinData.TotalDeposited;
            }
            return 0;
        }

        internal void PrizePlayer(Player player)
        {
            if (player == null) throw new ArgumentException(nameof(player));
            SpinData spinData;
            if (!spinDataByPlayer.TryGetValue(player, out spinData))
            {
                spinData = new SpinData(1);
                spinDataByPlayer.Add(player, spinData);
                TotalSpins++;
                return;
            }

            spinData.AssignSpinChance();
            spinDataByPlayer[player] = spinData;

            TotalSpins++;
        }

        internal void PopPlayerPrize(Player player)
        {
            if (player == null) throw new ArgumentException(nameof(player));

            SpinData spinData;
            if (spinDataByPlayer.TryGetValue(player, out spinData))
            {
                spinData.ClearSpins();
                spinDataByPlayer[player] = spinData;
            }

            TotalSpins--;
        }

        internal SpinWheel Copy()
        {
            SpinWheel copy = new SpinWheel();
            copy.title = title;
            copy.spinWheelLevel = spinWheelLevel;
            copy.streakGoal = streakGoal;
            copy.wasModified = wasModified;
            copy.spinWheelSettings = spinWheelSettings.Copy();

            return copy;
        }

        internal void Clear()
        {
            for (int index = 0; index < this.spinDataByPlayer.Count; index++)
            {
                Player player = this.spinDataByPlayer.Keys.ElementAt(index);
                SpinData playerData = this.spinDataByPlayer[player];
                playerData.ClearSpins();
                this.spinDataByPlayer[player] = playerData;
            }
        }

        internal void UpdateDaysStreakTitleOfLevel(string spinWheelTitle, int streakGoal)
        {
            if (streakGoal <= 0) throw new ArgumentException(nameof(streakGoal));
            if (string.IsNullOrWhiteSpace(spinWheelTitle)) throw new ArgumentException(nameof(spinWheelTitle));

            Title = spinWheelTitle;
            StreakGoal = streakGoal;
        }

        internal bool IsParticipating(Player player)
        {
            if (player == null) throw new ArgumentException(nameof(player));
            return spinDataByPlayer.ContainsKey(player);
        }

        private struct SpinData
        {
            private byte avaliableSpins;
            private int spinCounter;
            private decimal totalDeposited;

            internal byte AvaliableSpins { get { return avaliableSpins; } }
            internal int SpinCounter { get { return spinCounter; } }
            internal decimal TotalDeposited { get { return totalDeposited; } }

            internal SpinData(int avaliableSpins)
            {
                if (avaliableSpins < 0 || avaliableSpins > 255) throw new ArgumentException(nameof(avaliableSpins));

                this.avaliableSpins = (byte)avaliableSpins;
                this.spinCounter = 0;
                this.totalDeposited = 0;
            }

            internal void UseSpinChance()
            {
                int totalChances = avaliableSpins - 1;
                if (totalChances >= 0)
                {
                    avaliableSpins = (byte)totalChances;
                    spinCounter++;
                }
                else
                {
                    throw new GameEngineException("Cannot use less spins than limit of 0");
                }
            }

            internal void AssignSpinChance()
            {
                int totalChances = avaliableSpins + 1;
                if (totalChances <= 255)
                {
                    avaliableSpins = (byte)totalChances;
                }
                else
                {
                    throw new GameEngineException("The available spin possibilities have reached the limit of 255 available per level");
                }
            }

            internal void ClearSpins()
            {
                avaliableSpins = 0;
            }

            internal void AppendTotalDeposited(decimal prizeValue)
            {
                if (prizeValue <= 0) throw new GameEngineException("Prize value must be greater than 0");
                totalDeposited += prizeValue;
            }
        }
    }

    [Puppet]
    internal class SpinWheelSettings : Objeto
    {
        private string[] labels;
        private decimal[] rewardsAmount;

        internal int TotalLabels { get { return labels.Length; } }
        internal string[] Labels
        {
            get
            {
                return labels;
            }
            set
            {
                labels = value;
            }
        }
        internal decimal[] RewardsAmount
        {
            get
            {
                return rewardsAmount;
            }
            set
            {
                rewardsAmount = value;
            }
        }

        internal SpinWheelSettings(string[] labels, decimal[] rewardsAmount)
        {
            if (labels == null) throw new ArgumentException(nameof(labels));
            if (rewardsAmount.Length != labels.Length) throw new GameEngineException("Can not create SpinWheel if do not sent all reward amounts");
            foreach (var label in labels)
            {
                if (string.IsNullOrWhiteSpace(label)) throw new GameEngineException("Can not assing empty or null a label"); ;
            }
            foreach (var rewardAmount in rewardsAmount)
            {
                if (rewardAmount <= 0) throw new GameEngineException("Can not assing 0 or less than 0 a reward");
            }

            this.labels = labels;
            this.rewardsAmount = rewardsAmount;
        }

        internal SpinWheelSettings(string[] labels, decimal[] rewardsAmount, int spinWheelSection)
        {
            if (labels == null) throw new ArgumentException(nameof(labels));
            if (labels.Length != spinWheelSection) throw new GameEngineException("Can not create SpinWheel if do not sent all labels");
            if (rewardsAmount.Length != spinWheelSection) throw new GameEngineException("Can not create SpinWheel if do not sent all reward amounts");
            foreach (var label in labels)
            {
                if (string.IsNullOrWhiteSpace(label)) throw new GameEngineException("Can not assing empty or null a label"); ;
            }
            foreach (var rewardAmount in rewardsAmount)
            {
                if (rewardAmount <= 0) throw new GameEngineException("Can not assing 0 or less than 0 a reward");
            }

            this.labels = labels;
            this.rewardsAmount = rewardsAmount;
        }

        internal void ModifyArraysLength(int segments)
        {
            Array.Resize(ref labels, segments);
            Array.Resize(ref rewardsAmount, segments);
        }

        internal void AddSegments(string[] labels, decimal[] rewardsAmount)
        {
            if (labels.Length != TotalLabels) throw new ArgumentException(nameof(labels));
            if (rewardsAmount.Length != TotalLabels) throw new ArgumentException(nameof(rewardsAmount));

            this.Labels = labels;
            this.RewardsAmount = rewardsAmount;
        }

        internal SpinWheelSettings Copy()
        {
            string[] newLabels = (string[])labels.Clone();
            decimal[] newRewardsAmount = (decimal[])rewardsAmount.Clone();
            SpinWheelSettings copy = new SpinWheelSettings(newLabels, newRewardsAmount, TotalLabels);
            return copy;
        }
    }
}
