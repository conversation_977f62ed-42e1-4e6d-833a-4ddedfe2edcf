﻿using GamesEngine;
using GamesEngine.Business;
using GamesEngine.Domains;
using GamesEngine.Exchange;
using GamesEngine.Finance;
using GamesEngine.Settings;
using GamesEngineMocks;
using Puppeteer.EventSourcing;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Text;
using town.connectors.commons;
using town.connectors.drivers;
using static GamesEngine.Business.WholePaymentProcessor;
using static GamesEngine.Exchange.PaymentProcessorsAndActionsByDomains;
using static GamesEngine.Finance.PaymentChannels;

namespace GamesEngineMocks
{
	public class ExchangeMockGenerator : Mock
	{
		public Puppeteer.EventSourcing.Actor actor;
		public ExchangeMockGenerator(Puppeteer.EventSourcing.Actor actor)
		{
			this.actor = actor;
			string commonGeneralScript = $@"
				company = Company();
				coin = company.System.Coins.Add(0, 'FP', 'FP', 2, 'F', 'free play', {CoinType.Digital});
				coin.Visible = true;
				coin.Enabled = true;
				coin = company.System.Coins.Add(2, 'USD', '$', 2, '$', 'dollar', {CoinType.Fiat});
				coin.Visible = true;
				coin.Enabled = true;
				coin = company.System.Coins.Add(3, 'BTC', 'BTC', 8, '₿', 'bitcoin', {CoinType.Crypt});
				coin.Visible = true;
				coin.Enabled = true;
				company.System.Coins.Add(7, 'ETH', 'ETH', 8, 'Ξ', 'ethereum', {CoinType.Crypt});
				
				store = company.Sales.CreateStore(1,'Picks Store');
				store.Alias = 'lotto';
                store = company.Sales.CreateStore(2,'Brackets Store 2019');
				store.Alias = 'mm19';
                store = company.Sales.CreateStore(3,'Brackets Store 2020');
				store.Alias = 'mm20';
                store = company.Sales.CreateStore(4,'Fiero Wallet');
				store.Alias = 'fiero';
				store.MakeCurrent();
                store = company.Sales.CreateStore(5,'Ladybet Store');
				store.Alias = 'ladybet';
                store = company.Sales.CreateStore(6,'Brackets Store 2021');
				store.Alias = 'mm21';
				store = company.Sales.CreateStore(7,'Keno Store');
				store.Alias = 'keno';

				companyIDf2401f84xa94ax4db3xaec4x83f5361730ae = company;
				BTC2USDProd = company.GetOrCreateProductById(1);
				BTC2USDProd.Description = 'BTC to USD';
				BTC2USDProd.Price = 1;
				USD2BTCProd = company.GetOrCreateProductById(2);
				USD2BTCProd.Description = 'USD to BTC';
				USD2BTCProd.Price = 1;
				ETH2USDProd = company.GetOrCreateProductById(3);
				ETH2USDProd.Description = 'ETH to USD';
				ETH2USDProd.Price = 1;
				USD2ETHProd = company.GetOrCreateProductById(4);
				USD2ETHProd.Description = 'USD to ETH';
				USD2ETHProd.Price = 1;
				BTC2ETHProd = company.GetOrCreateProductById(5);
				BTC2ETHProd.Description = 'BTC to ETH';
				BTC2ETHProd.Price = 1;
				ETH2BTCProd = company.GetOrCreateProductById(6);
				ETH2BTCProd.Description = 'ETH to BTC';
				ETH2BTCProd.Price = 1;

				marketplace = MarketPlace(company, 'CR');
				guardian = Guardian(company);
				profiles = guardian.Profiles();
				Eval('profileId = '+profiles.NextProfileId()+';');
				generalManagerProfile = profiles.CreateProfile(itIsThePresent, profileId, 'General Manager');

				Eval('profileId = '+profiles.NextProfileId()+';');
				financialManagerProfile = profiles.CreateProfile(itIsThePresent, profileId, 'Financial Manager');

				Eval('profileId = '+profiles.NextProfileId()+';');
				ownerProfile = profiles.CreateOwnerProfile(itIsThePresent, profileId, 'Owner');

				Eval('profileId = '+profiles.NextProfileId()+';');
				agentProfile = profiles.CreateProfile(itIsThePresent, profileId, 'Agent/Cashier');

				Eval('id = '+marketplace.UserTypes.NextConsecutive()+';');
				marketplace.UserTypes.Add(id, 'Manager');
				Eval('id = '+marketplace.UserTypes.NextConsecutive()+';');
				marketplace.UserTypes.Add(id, 'Cashier');
				
				cartagoAgent = marketplace.AddAgent('Cartago');
				cartagoAgent.AddCashier('cajero1');
				elGuarcoAgent = cartagoAgent.AddAgent('El Guarco');
				elGuarcoAgent.AddCashier('cajeroQA1');
				elGuarcoAgent.AddCashier('cajeroQA2');
				cartagoAgent.AddAgent('Turrialba');
				agent1 = marketplace.AddAgent('1');
				user = agent1.AddCashier('ccajero');
				agent1.AssignProfile(user, agentProfile);
				user = agent1.AddCashier('ccajeroqa');
				agent1.AssignProfile(user, agentProfile);
				user = agent1.AddCashier('ccashiertest');
				agent1.AssignProfile(user, agentProfile);
				agent1.AddManager('managerPro');
				agent11 = agent1.AddAgent('11');
				agent11.AddCashier('cashierTest1');
				agent11.AddCashier('cashierTest2');
				agent1.AddAgent('12');
				marketplace.AddAgent('San José');
				marketplace.AddAgent('Heredia');

				riskRatings = RiskRatings();

				{{
					accounts = SetOfBalances();
					accounts.AddAccountIn('100-09-0987653-01', Dollar(5000000));
					accounts.AddAccountOut('100-09-0987653-01', Dollar(5000000));
					accounts.AddAccountIn('mrrPx6UBHChhtT9KwuKY4AuQPs1R8A1aXc', Btc(3000));
					accounts.AddAccountOut('mrrPx6UBHChhtT9KwuKY4AuQPs1R8A1aXc', Btc(40));
					accounts.AddAccountIn('42C6000', ETH(100));
					accounts.AddAccountOut('42C5385', ETH(100));
					marketplace.RealAccounts = accounts;
				}}

				riskRating = riskRatings.NewRiskRating('Type 2050392', 'A Description - AUTO', 1);
				
				localhostDomain = company.Sales.CreateDomain(false, 1, 'localhost', {Agents.INSIDER});
				company.Sales.CurrentStore.Add(localhostDomain);
				qaDomain = company.Sales.CreateDomain(false, 2, 'nodo.qatest.ncubo.com', {Agents.INSIDER});
				company.Sales.CurrentStore.Add(qaDomain);

				transactionOutcomes = marketplace.TransactionOutcomes();
				transactionOutcomes.CreatesOutcomesFor(localhostDomain);
				transactionOutcomes.CreatesOutcomesFor(qaDomain);

				agent1.AssignToCurrentStore(localhostDomain);
				agent1.AssignToCurrentStore(qaDomain);
				agent1.Assign(1, 'Picks Store', localhostDomain);
				agent1.Assign(1, qaDomain);
				agent1.EnableDomain(1, localhostDomain);
				agent1.EnableDomain(1, qaDomain);

				customSettings = CustomSettingsCollection(company);
				{{
					company.System.Entities.Add(1, 'Apolo');
					artemisEntity = company.System.Entities.Add(2, 'Artemis');
					zeusEntity = company.System.Entities.Add(3, 'Zeus');
					fieroEntity = company.System.Entities.Add(4, 'Fiero');
					fieroEntity.Visible = true;
					fieroEntity.Enabled = true;
					hadesEntity = company.System.Entities.Add(5, 'Hades');
					hadesEntity.Visible = true;
					hadesEntity.Enabled = true;
					consignmentEntity = company.System.Entities.Add(6, 'Consignment');
					consignmentEntity.Visible = true;
					consignmentEntity.Enabled = true;

					company.System.Tenants.Add(1, 'Apolo');
					artemisTenant = company.System.Tenants.Add(2, 'Artemis');
					zeusTenant = company.System.Tenants.Add(3, 'Zeus');
					insiderTenant = company.System.Tenants.Add(4, 'Insider');
					hadesTenant = company.System.Tenants.Add(5, 'Hades');
					hadesTenant.MakeCurrent();

					asiPM = company.System.PaymentMethods.Add(1, 'A.S.I.');
					dgsPM = company.System.PaymentMethods.Add(2, 'D.G.S.');
					dgsV2PM = company.System.PaymentMethods.Add(3, 'D.G.S. v2');
					credirCardPM = company.System.PaymentMethods.Add(4, 'Credit card');
					blockChainPM = company.System.PaymentMethods.Add(5, 'Blockchain');
					moneyTransferPM = company.System.PaymentMethods.Add(6, 'Money transfer');
					asiSimulatorPM = company.System.PaymentMethods.Add(7, 'A.S.I. Simulator');
					dsgSimulatorPM = company.System.PaymentMethods.Add(8, 'D.G.S. Simulator');
					pm = company.System.PaymentMethods.Add(9, '{PaymentMethod.Cash.ToString()}');
					pm.Visible = true;
					pm.Enabled = true;
					pm = company.System.PaymentMethods.Add(10, '{PaymentMethod.Bank.ToString()}');
					pm.Visible = true;
					pm.Enabled = true;
					pm = company.System.PaymentMethods.Add(11, '{PaymentMethod.Creditcard.ToString()}');
					pm.Visible = true;
					pm.Enabled = true;
					pm = company.System.PaymentMethods.Add(12, '{PaymentMethod.FinancialServices.ToString()}');
					pm.Visible = true;
					pm.Enabled = true;
					pm = company.System.PaymentMethods.Add(13, '{PaymentMethod.ThirdParty.ToString()}');
					pm.Visible = true;
					pm.Enabled = true;
					pm = company.System.PaymentMethods.Add(14, '{PaymentMethod.Secrets.ToString()}');
					pm.Visible = true;
					pm.Enabled = true;
					pm = company.System.PaymentMethods.Add(15, '{PaymentMethod.P2PTransfer.ToString()}');
					pm.Visible = true;
					pm.Enabled = true;
					pm.AddProvider(1,'MoneyGram');
					pm.AddProvider(2,'Rio Money Transfer');
					pm.AddProvider(3,'Sigue');
					pm.AddProvider(4,'Boss Revolution Money Transfer');
					pm.AddProvider(5,'Delgado Travel');
					pm.AddProvider(6,'Intermex International Money Transfer');
					pm.AddProvider(7,'Maxi Money Services');
					pm.AddProvider(8,'Barri Financial Group');
					pm.AddProvider(9,'La Nacional');
					pm.AddProvider(10,'Dinex');
					pm.AddProvider(11,'Remitly');

					depositTransactionType = company.System.TransactionTypes.Add(1, '{TransactionType.Deposit.ToString()}');
					depositTransactionType.Description = 'A sum of money placed in an account';
					withDrawalTransactionType = company.System.TransactionTypes.Add(2, '{TransactionType.Withdrawal.ToString()}');
					withDrawalTransactionType.Description = 'A sum of money take out of an account';
					transferTransactionType = company.System.TransactionTypes.Add(3, '{TransactionType.Transfer.ToString()}');
					transferTransactionType.Description = 'A sum of money is sent from one account to another';
					creditNoteTransactionType = company.System.TransactionTypes.Add(4, '{TransactionType.CreditNote.ToString()}');
					creditNoteTransactionType.Description = 'It is a document used by a vendor to inform the buyer of mistake on an order';
					debitNoteTransactionType = company.System.TransactionTypes.Add(5, '{TransactionType.DebitNote.ToString()}');
					debitNoteTransactionType.Description = 'It is a document used by a vendor to inform the buyer of current debt obligations';
					saleTransactionType = company.System.TransactionTypes.Add(6, '{TransactionType.Sale.ToString()}');
					saleTransactionType.Description = 'The exchange of a commodity for money';
					depositAndLockTransactionType = company.System.TransactionTypes.Add(7, '{TransactionType.Deposit.ToString()} then Lock');
					depositAndLockTransactionType.Description = 'Add and Lock money placed in an account';

					depositTransactionType.Visible = true;
					depositTransactionType.Enabled = true;
					withDrawalTransactionType.Visible = true;
					withDrawalTransactionType.Enabled = true;
					transferTransactionType.Visible = true;
					transferTransactionType.Enabled = true;
					debitNoteTransactionType.Visible = true;
					debitNoteTransactionType.Enabled = true;
					creditNoteTransactionType.Visible = true;
					creditNoteTransactionType.Enabled = true;
					saleTransactionType.Visible = true;
					saleTransactionType.Enabled = true;
					depositAndLockTransactionType.Visible = true;
					depositAndLockTransactionType.Enabled = true;

					cs = customSettings.AddFixedParameter(Now, 'CashierDriver.url', 'http://cashierapi:5000/'); 
					cs.Description = 'Url Cashier';
					cs = customSettings.AddFixedParameter(Now, 'CompanyBaseUrlServices', 'http://cashierapi:5000/'); 
					cs.Description = 'It is the base production url';
					cs = customSettings.AddFixedParameter(Now, 'TokenSystemId', '3aab83fb'); 
					cs.Description = 'App\'s token id';
					cs = customSettings.AddSecretParameter(Now, 'TokenSystemPassword', '38f845e977af7f2cd6313c5a9d35e4de7f353405100e7748ab15b410eb53d886');
					cs.Description = 'Password for token';
					cs = customSettings.AddFixedParameter(Now, 'SendWagers', true); 
					cs.Description = 'To send wagers to third party';
					cs.Enabled = false;
					cs = customSettings.AddFixedParameter(Now, 'CompanySystemId', 'N/A'); 
					cs.Description = 'company id';
					cs = customSettings.AddSecretParameter(Now, 'CompanySystemPassword', 'N/A'); 
					cs.Description = 'password';
					cs = customSettings.AddFixedParameter(Now, 'NeedsUniqueIdentifierForPaymentHub', 'PRODUCTION_DOES_NOT_NEED_IT'); 
					cs.Description = 'Setting';
					cs = customSettings.AddFixedParameter(Now, 'CompanyClerkId', 'Administrator'); 
					cs.Description = 'Clerk id';

					cs = customSettings.AddVariableParameter('Input$Provider'); 
					cs.Description = 'Consignment provider';
					cs = customSettings.AddVariableParameter('KYC$DOB'); 
					cs.Description = 'Birthdate from KYC';
					cs = customSettings.AddSecretParameter(Now, 'KYCUsername', 'testUser1');
					cs.Description = 'KYC Username for fields';
					cs = customSettings.AddSecretParameter(Now, 'KYCPassword', '12345');
					cs.Description = 'KYC Password for fields';
				}}
			";
			Perform(commonGeneralScript);
		}

		public ExchangeMockGenerator(Puppeteer.EventSourcing.Actor actor, string script)
		{
			this.actor = actor;
			Perform(script);
		}

		public String Perform(string script)
		{
			string result;
			try
			{
#pragma warning disable VSTHRD002 // Avoid problematic synchronous waits
				result = actor.PerformCmdAsync(script, IpAddress.DEFAULT, UserInLog.ANONYMOUS).Result;
#pragma warning restore VSTHRD002 // Avoid problematic synchronous waits
				result = CheckForAssert(result);
			}
			catch (Exception e)
			{
				throw e.InnerException;
			}
			return result;
		}

		private int cont2 = 0;
		Dictionary<string, string> custommersById = new Dictionary<string, string>();
		public const string ACCOUNT_NUMBER_1 = "*********";
		public const string ACCOUNT_NUMBER_2 = "*********";
		public ExchangeMockGenerator CreatePlayers()
		{
			custommersById.Add(ACCOUNT_NUMBER_1, "ID012345678901234567890123456789012345");
			custommersById.Add(ACCOUNT_NUMBER_2, "iCDlytS7kCxVPfgc0uSVvfJDtkgoUtRUtOSb09HFuQk=");
			var agent = Agents.INSIDER;
			foreach (var accountById in custommersById)
			{
				cont2 += 1;
				StringBuilder linesToCreatePlayer = new StringBuilder();
				linesToCreatePlayer.Append($@"
                    customer = company.CreateCustomer('NO{accountById.Key}',{agent});
					customer.Identifier='10{cont2}';
                    playerNO{accountById.Key} = customer.Player;
                    player = customer.Player;
                    {{
                        customer.ReplicateInOtherNodes(itIsThePresent, true, Now, 'NickName User', 'https://intranet.ncubo.com/ResourcesQA5/api/file/IDDefault/Lotto/Lotto/avatars/avatar2.png');
                    }}
					{{
						Eval('id =' + marketplace.NextAccountConsecutive() + ';');
						marketplace.RegisterNewAccount(id, customer.FindAccountByCurrency('{Currencies.CODES.USD}'));
						Eval('id =' + marketplace.NextAccountConsecutive() + ';');
						marketplace.RegisterNewAccount(id, customer.FindAccountByCurrency('{Currencies.CODES.BTC}'));
					}}
                ");
				var result = Perform(linesToCreatePlayer.ToString());
			}
			return this;
		}

		private int cont = 0;
		public ExchangeMockGenerator CreateCustomer(string accountNumber, string identificationNumber)
		{
			var commandForIdentifier = string.IsNullOrWhiteSpace(identificationNumber) ? string.Empty : $"customer.Identifier='{identificationNumber}';";

			var result = $@"
				customer = company.GetOrCreateCustomerById('{accountNumber}');
				{commandForIdentifier}
				player = customer.Player;
				playerID18acac20991e437e9529a441452f6b7{cont} = player;
				{{
					Eval('id =' + marketplace.NextAccountConsecutive() + ';');
					marketplace.RegisterNewAccount(id, customer.FindAccountByCurrency('{Currencies.CODES.USD}'));
					Eval('id =' + marketplace.NextAccountConsecutive() + ';');
					marketplace.RegisterNewAccount(id, customer.FindAccountByCurrency('{Currencies.CODES.BTC}'));
				}}

                ";
			Perform(result);
			return this;
		}

		public ExchangeMockGenerator CreateRate(string fromCurrencyCode, string toCurrencyCode, decimal salePrice, decimal purchasePrice, bool itIsThePresent = false)
		{
			var result = $@"
				{{
					Eval('conversionSpreadNumber =' + marketplace.NewConversionSpreadNumber() + ';');
					rate = marketplace.CreateConversionSpread(conversionSpreadNumber, Now, {itIsThePresent}, '{fromCurrencyCode}', '{toCurrencyCode}', {salePrice}, {purchasePrice}, 'ccajero');
				}}
                ";
			Perform(result);
			return this;
		}

		public ExchangeMockGenerator CreateADraftDeposit(string identificationDocumentNumber, Currencies.CODES currencyCode, decimal amount, string path, string employeeName, string domainUrl, PaymentMethod method, int entityId)
		{
			var paymentMethod = WholePaymentProcessor.Instance().SearchPaymentMethodByName(method.ToString());
			var transaction = WholePaymentProcessor.Instance().SearchTransactionByName(TransactionType.Deposit.ToString());
			var coin = Coinage.Coin(currencyCode);
			var voucher = 908868;
			var result = $@"
			{{
				domain = company.Sales.DomainFrom('{domainUrl}');
				customer = company.CustomerByIdentifier('{identificationDocumentNumber}');
				defaultAccount = customer.FindAccountByCurrency('{currencyCode.ToString()}');
				Eval('transactionNumber =' + marketplace.NewTransationNumber() + ';');
				processor = company.System.PaymentProcessor.SearchProcessorWithHigherVersion({entityId}, {paymentMethod.Id}, {transaction.Id}, {coin.Id});
				processorAccountId = guardian.Accounts().SearchByProcessor(processor.ProcessorKey).Id;
				transaction = marketplace.From(transactionNumber, defaultAccount, '{path}', '{employeeName}', domain).Deposit(Now, itIsThePresent, Currency('{currencyCode.ToString()}',{amount}), '{employeeName}', 'Depositor Test', '{voucher}', 'voucherurl.com', 'test description', processor, processorAccountId);
				
				print transactionNumber authorizationId;
				print transaction.BatchTransactions.TransactionsNumber batchNumber;
				print Now now;
			}}
                ";
			Perform(result);
			return this;
		}

		public ExchangeMockGenerator AproveTransaction(string accountNumber, string identificationDocumentNumber, Coin currencyCode, decimal amount, int TransactionNumber, string path, string userName, string domainUrl, 
			PaymentMethod paymentMethod, int entityId, int storeId)
		{
			var response = MakeDeposit(accountNumber, identificationDocumentNumber, currencyCode, amount, domainUrl, paymentMethod, entityId, storeId, path);

			var result = $@"
				{{
					transaction = marketplace.FindDraftTransaction({TransactionNumber}, '{path}', '{userName}');
					Eval('journalEntryNumber =' + marketplace.NewJournalEntryNumber() + ';');
					account = guardian.Accounts().SearchByProcessor('{response.ProcessorKeyUsed}');
					transactionCompleted = transaction.Approve(Now, itIsThePresent, {response.AuthorizationId}, '{userName}', journalEntryNumber);
					result = transactionCompleted.Result;
					print result.Net.Sign netsign;
					print result.Net.Value net;
					print result.Net.ToDisplayFormat() netFormatted;
					print result.Gross.Sign grosssign;
					print result.Gross.Value gross;
					print result.Gross.ToDisplayFormat() grossFormatted;
					print result.Profit.Sign profitsign;
					print result.Profit.Value profit;
					print result.Profit.ToDisplayFormat() profitFormatted;
					print result.Amount.Sign amountsign;
					print result.Amount.Value amount;
					print result.Amount.ToDisplayFormat() amountFormatted;
				}}
            ";
			Perform(result);
			return this;
		}

		public DespositResponse MakeDeposit(string accountNumber, string identificationDocumentNumber, Coin currencyCode, decimal amount, string domainUrl, PaymentMethod paymentMethod, int entityId, int storeId, string path)
		{
			var transaction = WholePaymentProcessor.Instance().SearchTransactionByName(TransactionType.Deposit.ToString());
			var response = PaymentChannels.Deposit(true,
				currencyCode,
				domainUrl,
				transaction,
				new DespositBody(
					accountNumber,
					identificationDocumentNumber,
					amount,
					$"{nameof(TransactionType.Deposit)} mock",
					DateTime.Now,
					string.Empty,
					"ccajero",
					new Domain(false, 1, domainUrl, PaymentChannels.Agents.INSIDER),
					path,
					paymentMethod,
					entityId,
					storeId,
					string.Empty,
					string.Empty,
					string.Empty,
					string.Empty,
					string.Empty,
					0
					)
				);
			return response;
		}

		public ExchangeMockGenerator CreateBatches()
		{
			var result = $@"
				batchNumber = 100001;
				marketplaceBatch = marketplace.AddNewBatch(Now, Now.Fecha(), batchNumber, 'N/A', 'batchAuto - NO USAR!');
				marketplaceBatch.InitialAmount(Dollar(1500000), 'N/A');
				marketplaceBatch.InitialAmount(Btc(5), 'N/A');
				marketplaceBatch.InitialAmount(Eth(5), 'N/A');
				
				marketplaceBatch = marketplace.FindMarketplaceBatch(batchNumber);
				batch = marketplaceBatch.AddAgent('1', Now);
				batch.ReceiveAmount(Dollar(150000), Dollar(150000), 'N/A');
				batch.ReceiveAmount(Btc(10.5), Dollar(92510.76), 'N/A');
				batch.ReceiveAmount(Eth(10.5), Dollar(2390.24), 'N/A');
				
				agencyBatch = marketplace.SearchAgentBatch('CR/1', batchNumber);
				agentBatch1 = agencyBatch.AddAgent('ccajero', Now);
				agencyBatch.AssignFunds('ccajero', batchNumber, Dollar(7500), 'N/A', Now);
				agencyBatch.AssignFunds('ccajero', batchNumber, Btc(1.5), 'N/A', Now);
				agencyBatch.AssignFunds('ccajero', batchNumber, Eth(1.5), 'N/A', Now);
				agencyBatch.AssignFunds('ccajero', batchNumber, Dollar(7500), 'N/A', Now);

				agentBatch2 = agencyBatch.AddAgent('ccajeroqa', Now);
				agencyBatch.AssignFunds('ccajeroqa', batchNumber, Dollar(7200), 'N/A', Now);
				agencyBatch.AssignFunds('ccajeroqa', batchNumber, Btc(1.1), 'N/A', Now);
				agencyBatch.AssignFunds('ccajeroqa', batchNumber, Eth(1.1), 'N/A', Now);

				agentBatch3 = agencyBatch.AddAgent('ccashiertest', Now);
				agencyBatch.AssignFunds('ccashiertest', batchNumber, Dollar(300), 'N/A', Now);
				agencyBatch.AssignFunds('ccashiertest', batchNumber, Btc(1.1), 'N/A', Now);
				agencyBatch.AssignFunds('ccashiertest', batchNumber, Eth(1.1), 'N/A', Now);

				marketplace.SearchAgentBatch('CR').Open(itIsThePresent, Now, 'ccajero');
				marketplace.SearchAgentBatch('CR/1').Open(itIsThePresent, Now, 'ccajero');
				agentBatch1.Open(itIsThePresent, Now, 'ccajero');
				agentBatch2.Open(itIsThePresent, Now, 'ccajero');
				agentBatch3.Open(itIsThePresent, Now, 'ccajero');
                ";
			Perform(result);
			return this;
		}

		public ExchangeMockGenerator SetAmountRange(string path, string cashierName, string customerNumber)
		{
			var transactionTypes = new List<string>()
					{
						TransactionType.Deposit.ToString(),
						TransactionType.Withdrawal.ToString(),
						TransactionType.Transfer.ToString(),
						TransactionType.CreditNote.ToString(),
						TransactionType.DebitNote.ToString(),
						TransactionType.Sale.ToString()
					};
			var transactionTypesAsText = string.Join(",", transactionTypes.Select(type => $"'{type.ToString()}'").ToArray());
			var result = $@"
				agent = marketplace.SearchAgent('{path}');
				cashier = agent.SearchCashier('{cashierName}');
				agentAmountRanges = AgentAmountRanges();
				riskRating = riskRatings.FindRiskRating('Type 2050392');
				amountRangeByCurrencies = agentAmountRanges.CreateAmountRangeByCurrencies(riskRating, {{{transactionTypesAsText}}});
				amountRangeByCurrencies.SetAmountRange(Dollar(800), Dollar(0.5), Now, 'N/A');
				amountRangeByCurrencies.SetAmountRange(Btc(0.6), Btc(0.00000523), Now, 'N/A');
				cashier.SetAmountRanges(agentAmountRanges);

				customer = company.CustomerByIdentifier('{customerNumber}');
				customer.Approve();
                ";
			Perform(result);
			return this;
		}
	}
}

