﻿using ExternalServices;
using GamesEngine.Business;
using GamesEngine.Games.Lotto;
using GamesEngine.Gameboards;
using GamesEngine.Settings;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using Puppeteer.EventSourcing;
using System;
using System.Collections.Generic;
using System.Linq;
using Unit.Games.Tools;
using static GamesEngine.Gameboards.Lotto.Ticket;
using GamesEngine.Location;
using GamesEngine.Time;
using GamesEngine.Domains;
using GamesEngine.PurchaseOrders;
using GamesEngine.Finance;
using static GamesEngine.Finance.PaymentChannels;
using static GamesEngine.Business.ArtemisBehavior;
using GamesEngine.MessageQueuing;

namespace GamesEngineTests.Unit_Tests.Games.Lotto
{
	[TestClass]
    public class ReceiverOfHistoricalTest
    {
        private string sqlStringConection = "";//"persistsecurityinfo=True;port=3306;Server=localhost;Database=lottobi2;user id=*********;password=*********;SslMode=none";//"data source=localhost; initial catalog=testbug; Integrated Security=SSPI;multipleactiveresultsets=False";

        [TestMethod]
        public void AccumulateAndUpdateRecordsForReports_bug5753_InsertDraw()
        {
            if (string.IsNullOrEmpty(sqlStringConection)) Assert.Inconclusive("SQLServer string connection its required.");


            Integration.UseKafka = true;
            QueueInMemory queue = new QueueInMemory();
            Integration.Kafka = queue;

            Db bd = new Db();
            bd.MySQL = sqlStringConection;
            bd.DBSelected = DatabaseType.MySQL.ToString();
            Integration.Db = bd;

            DBDairy dbDairy = new DBDairy();
            dbDairy.DBSelected = DatabaseType.MySQL.ToString();
            Integration.DbDairy = dbDairy;

            var receiver = new ReceiverOfHistoricalPicks();
            receiver.InitHistorical(HistoricalDatabaseType.MySQL, Integration.Db.MySQL);

            Company company = new Company();
            company.Sales.CreateStore(1, "Test Store").MakeCurrent();
            var lotteries = company.Lotto900();
            var s = new State("GA", "Georgia");
            var lottery = lotteries.GetOrCreateLottery(3, s);
            DateTime now = new DateTime(2019, 03, 29, 11, 12, 13);
            for (int i = 0; i <= 6; i++) lottery.Every(10, 20, i, now, "Game Admin");

            var schedule = lottery.GetScheduleId(1);
            schedule.Update(true, "Georgia morning", "0123456", 10, 20, now, "Game Admin");

            // incoming ticket #1
            string draw = "012";
            int fireball = LotteryDraw.WITHOUT_FIREBALL;
            string gradedby = "Bart";
            decimal prize = 150m;
            string state = "GA";
            var drawDate1 = new DateTime(2020, 04, 17, 10, 20, 00);
            string account = "NO*********";
            string ticket = "P3B[0,0,2]";
            int count = 1;
            decimal amount = 0.25m;
            Selection selection = Selection.BALLS;
            GameboardStatus grading = GameboardStatus.GRADED;
            int drawingId = 1;
            int uniqueDrawingId = 1;
            TypeNumberSequence position = TypeNumberSequence.ENDING;
            string drawingName = "GA Morning";
            var creationDate = new DateTime(2020, 04, 17, 9, 20, 00);
            int orderNumber = 1234;
            int ticketNumber = *********;
            string subtickets = "(0-0-2,1)";
            decimal profit = 0.25m;
            int prizeVersion = 1;
            int domainId = 1;
            string domainUrl = "localhost";
            int currencyId = 1;
            var loser1 = new LoserInfo(company, draw, fireball, gradedby, state, drawDate1.Hour, drawDate1.Minute, drawDate1.Year, drawDate1.Month, drawDate1.Day, account, ticket, count, amount, selection, grading, drawingId, position, drawingName, creationDate, orderNumber, ticketNumber, subtickets, profit, prizeVersion, domainId, domainUrl, currencyId);
            var losers = new List<LoserInfo>() { loser1 };
            receiver.ReceiveLoser(loser1);
            receiver.AccumulateAndUpdateRecordsForReports(drawDate1);
            receiver.EndReception();

            // incoming ticket #2
            drawDate1 = new DateTime(2020, 04, 18, 10, 20, 00);
            creationDate = new DateTime(2020, 04, 18, 9, 20, 00);
            ticket = "P3B[0,1,2]";
            subtickets = "(0-1-2,1)";
            profit = -149.75m;
            ticketNumber = *********;
            var winner1 = new WinnerInfo(company, draw, fireball, gradedby, prize, state, drawDate1.Hour, drawDate1.Minute, drawDate1.Year, drawDate1.Month, drawDate1.Day, account, ticket, count, amount, selection, grading, drawingId, uniqueDrawingId, position, drawingName, creationDate, orderNumber, ticketNumber, subtickets, profit, prizeVersion, domainId, domainUrl, currencyId);
            var winners = new List<WinnerInfo>() { winner1 };
            receiver.ReceiveWinner(winner1);
            receiver.AccumulateAndUpdateRecordsForReports(drawDate1);
            receiver.EndReception();

            // generating report directly from service
            var lotto900 = company.Lotto900();
            var startDate = new DateTime(2020, 04, 17, 0, 0, 00);
            var endDate = new DateTime(2020, 04, 19, 0, 0, 00);
            now = new DateTime(2020, 04, 19, 11, 0, 00);
            var report = lotto900.Reports.GenerateTotalProfitByDrawingReport(startDate, endDate, now, "all", "all", "all");

            // incoming ticket #3
            state = "TN";
            drawDate1 = new DateTime(2020, 04, 18, 11, 20, 00);
            creationDate = new DateTime(2020, 04, 18, 9, 30, 00);
            ticket = "P3B[2,1,2]";
            subtickets = "(2-1-2,1)";
            profit = 0.25m;
            ticketNumber = *********;
            loser1 = new LoserInfo(company, draw, fireball, gradedby, state, drawDate1.Hour, drawDate1.Minute, drawDate1.Year, drawDate1.Month, drawDate1.Day, account, ticket, count, amount, selection, grading, drawingId, position, drawingName, creationDate, orderNumber, ticketNumber, subtickets, profit, prizeVersion, domainId, domainUrl, currencyId);
            losers = new List<LoserInfo>() { loser1 };
            receiver.ReceiveLoser(loser1);
            receiver.AccumulateAndUpdateRecordsForReports(drawDate1);
            receiver.EndReception();

            // incoming ticket #4
            drawDate1 = new DateTime(2020, 04, 19, 11, 20, 00);
            creationDate = new DateTime(2020, 04, 19, 9, 30, 00);
            ticket = "P3B[0,0,2]";
            subtickets = "(0-0-2,1)";
            profit = 0.25m;
            ticketNumber = *********;
            loser1 = new LoserInfo(company, draw, fireball, gradedby, state, drawDate1.Hour, drawDate1.Minute, drawDate1.Year, drawDate1.Month, drawDate1.Day, account, ticket, count, amount, selection, grading, drawingId, position, drawingName, creationDate, orderNumber, ticketNumber, subtickets, profit, prizeVersion, domainId, domainUrl, currencyId);
            losers = new List<LoserInfo>() { loser1 };
            receiver.ReceiveLoser(loser1);
            receiver.AccumulateAndUpdateRecordsForReports(drawDate1);
            receiver.EndReception();

            // incoming ticket #5
            drawDate1 = new DateTime(2020, 04, 20, 11, 20, 00);
            creationDate = new DateTime(2020, 04, 20, 9, 30, 00);
            ticket = "P3B[0,0,2]";
            subtickets = "(0-0-2,1)";
            profit = 0.25m;
            ticketNumber = *********;
            loser1 = new LoserInfo(company, draw, fireball, gradedby, state, drawDate1.Hour, drawDate1.Minute, drawDate1.Year, drawDate1.Month, drawDate1.Day, account, ticket, count, amount, selection, grading, drawingId, position, drawingName, creationDate, orderNumber, ticketNumber, subtickets, profit, prizeVersion, domainId, domainUrl, currencyId);
            losers = new List<LoserInfo>() { loser1 };
            receiver.ReceiveLoser(loser1);
            receiver.AccumulateAndUpdateRecordsForReports(drawDate1);
            receiver.EndReception();
        }

        [TestMethod]
        public void AccumulateAndUpdateRecordsForReports_bug5753_UpdateDraw()
        {
            if (string.IsNullOrEmpty(sqlStringConection)) Assert.Inconclusive("SQLServer string connection its required.");


            //Integration.UseKafka = true;
            QueueInMemory queue = new QueueInMemory();
            Integration.Kafka = queue;

            Db bd = new Db();
            bd.SQLServer = sqlStringConection;
            bd.DBSelected = DatabaseType.SQLServer.ToString();
            Integration.Db = bd;

            DBDairy dbDairy = new DBDairy();
            dbDairy.DBSelected = DatabaseType.SQLServer.ToString();
            Integration.DbDairy = dbDairy;

            var receiver = new ReceiverOfHistoricalPicks();
            receiver.InitHistorical(HistoricalDatabaseType.SQLServer, Integration.Db.SQLServer);

            // incoming ticket #1
            Company company = new Company();
            company.Sales.CreateStore(1, "Test Store").MakeCurrent();
            string draw = "012";
            int fireball = LotteryDraw.WITHOUT_FIREBALL;
            string gradedby = "Bart";
            decimal prize = 150m;
            string state = "GA";
            var drawDate1 = new DateTime(2020, 04, 17, 10, 20, 00);
            string account = "*********";
            string ticket = "P3B[0,0,2]";
            int count = 1;
            decimal amount = 0.25m;
            Selection selection = Selection.BALLS;
            GameboardStatus grading = GameboardStatus.GRADED;
            int drawingId = 1;
            int uniqueDrawingId = 1;
            TypeNumberSequence position = TypeNumberSequence.ENDING;
            string drawingName = "GA Morning";
            var creationDate = new DateTime(2020, 04, 17, 9, 20, 00);
            int orderNumber = 1234;
            int ticketNumber = *********;
            string subtickets = "(0-0-2,1)";
            decimal profit = 0.25m;
            int prizeVersion = 1;
            int domainId = 1;
            string domainUrl = "localhost";
            int currencyId = 1;
            var loser1 = new LoserInfo(company, draw, fireball, gradedby, state, drawDate1.Hour, drawDate1.Minute, drawDate1.Year, drawDate1.Month, drawDate1.Day, account, ticket, count, amount, selection, grading, drawingId, position, drawingName, creationDate, orderNumber, ticketNumber, subtickets, profit, prizeVersion, domainId, domainUrl, currencyId);
            var losers = new List<LoserInfo>() { loser1 };
            receiver.ReceiveLoser(loser1);
            receiver.AccumulateAndUpdateRecordsForReports(drawDate1);
            receiver.EndReception();

            // incoming ticket #2
            drawDate1 = new DateTime(2020, 04, 18, 10, 20, 00);
            creationDate = new DateTime(2020, 04, 18, 9, 20, 00);
            ticket = "P3B[0,1,2]";
            subtickets = "(0-1-2,1)";
            profit = -149.75m;
            ticketNumber = *********;
            var winner1 = new WinnerInfo(company, draw, fireball, gradedby, prize, state, drawDate1.Hour, drawDate1.Minute, drawDate1.Year, drawDate1.Month, drawDate1.Day, account, ticket, count, amount, selection, grading, drawingId, uniqueDrawingId, position, drawingName, creationDate, orderNumber, ticketNumber, subtickets, profit, prizeVersion, domainId, domainUrl, currencyId);
            var winners = new List<WinnerInfo>() { winner1 };
            receiver.ReceiveWinner(winner1);
            receiver.AccumulateAndUpdateRecordsForReports(drawDate1);
            receiver.EndReception();

            // generating report directly from service
            var lotto900 = company.Lotto900();
            var startDate = new DateTime(2020, 04, 17, 0, 0, 00);
            var endDate = new DateTime(2020, 04, 19, 0, 0, 00);
            var now = new DateTime(2020, 04, 19, 11, 0, 00);
            var report = lotto900.Reports.GenerateTotalProfitByDrawingReport(startDate, endDate, now, "all", "all", "all");

            // incoming ticket #3
            drawDate1 = new DateTime(2020, 04, 18, 10, 20, 00);
            creationDate = new DateTime(2020, 04, 18, 9, 20, 00);
            ticket = "P3B[2,1,2]";
            subtickets = "(2-1-2,1)";
            profit = 0.25m;
            ticketNumber = *********;
            loser1 = new LoserInfo(company, draw, fireball, gradedby, state, drawDate1.Hour, drawDate1.Minute, drawDate1.Year, drawDate1.Month, drawDate1.Day, account, ticket, count, amount, selection, grading, drawingId, position, drawingName, creationDate, orderNumber, ticketNumber, subtickets, profit, prizeVersion, domainId, domainUrl, currencyId);
            losers = new List<LoserInfo>() { loser1 };
            receiver.ReceiveLoser(loser1);
            receiver.AccumulateAndUpdateRecordsForReports(drawDate1);
            receiver.EndReception();

            // incoming ticket #4
            state = "TN";
            drawDate1 = new DateTime(2020, 04, 19, 11, 20, 00);
            creationDate = new DateTime(2020, 04, 19, 9, 30, 00);
            ticket = "P3B[0,0,2]";
            subtickets = "(0-0-2,1)";
            profit = 0.25m;
            ticketNumber = *********;
            loser1 = new LoserInfo(company, draw, fireball, gradedby, state, drawDate1.Hour, drawDate1.Minute, drawDate1.Year, drawDate1.Month, drawDate1.Day, account, ticket, count, amount, selection, grading, drawingId, position, drawingName, creationDate, orderNumber, ticketNumber, subtickets, profit, prizeVersion, domainId, domainUrl, currencyId);
            losers = new List<LoserInfo>() { loser1 };
            receiver.ReceiveLoser(loser1);
            receiver.AccumulateAndUpdateRecordsForReports(drawDate1);
            receiver.EndReception();

            // incoming ticket #5
            drawDate1 = new DateTime(2020, 04, 20, 11, 20, 00);
            creationDate = new DateTime(2020, 04, 20, 9, 30, 00);
            ticket = "P3B[0,0,2]";
            subtickets = "(0-0-2,1)";
            profit = 0.25m;
            ticketNumber = *********;
            loser1 = new LoserInfo(company, draw, fireball, gradedby, state, drawDate1.Hour, drawDate1.Minute, drawDate1.Year, drawDate1.Month, drawDate1.Day, account, ticket, count, amount, selection, grading, drawingId, position, drawingName, creationDate, orderNumber, ticketNumber, subtickets, profit, prizeVersion, domainId, domainUrl, currencyId);
            losers = new List<LoserInfo>() { loser1 };
            receiver.ReceiveLoser(loser1);
            receiver.AccumulateAndUpdateRecordsForReports(drawDate1);
            receiver.EndReception();
        }

        [TestMethod]
        public void AccumulateAndUpdateRecordsForReports_bug5753_GenerateDailyTotalProfit()
        {
            if (string.IsNullOrEmpty(sqlStringConection)) Assert.Inconclusive("SQLServer string connection its required.");


            //Integration.UseKafka = true;
            QueueInMemory queue = new QueueInMemory();
            Integration.Kafka = queue;

            Db bd = new Db();
            bd.SQLServer = sqlStringConection;
            bd.DBSelected = DatabaseType.SQLServer.ToString();
            Integration.Db = bd;

            DBDairy dbDairy = new DBDairy();
            dbDairy.DBSelected = DatabaseType.SQLServer.ToString();
            Integration.DbDairy = dbDairy;

            var receiver = new ReceiverOfHistoricalPicks();
            receiver.InitHistorical(HistoricalDatabaseType.SQLServer, Integration.Db.SQLServer);

            // incoming ticket #1
            Company company = new Company();
            company.Sales.CreateStore(1, "Test Store").MakeCurrent();
            string draw = "012";
            int fireball = LotteryDraw.WITHOUT_FIREBALL;
            string gradedby = "Bart";
            decimal prize = 150m;
            string state = "GA";
            var drawDate1 = new DateTime(2020, 04, 17, 10, 20, 00);
            string account = "*********";
            string ticket = "P3B[0,0,2]";
            int count = 1;
            decimal amount = 0.25m;
            Selection selection = Selection.BALLS;
            GameboardStatus grading = GameboardStatus.GRADED;
            int drawingId = 1;
            int uniqueDrawingId = 1;
            TypeNumberSequence position = TypeNumberSequence.ENDING;
            string drawingName = "GA Morning";
            var creationDate = new DateTime(2020, 04, 17, 9, 20, 00);
            int orderNumber = 1234;
            int ticketNumber = *********;
            string subtickets = "(0-0-2,1)";
            decimal profit = 0.25m;
            int prizeVersion = 1;
            int domainId = 1;
            string domainUrl = "localhost";
            int currencyId = 1;
            var loser1 = new LoserInfo(company, draw, fireball, gradedby, state, drawDate1.Hour, drawDate1.Minute, drawDate1.Year, drawDate1.Month, drawDate1.Day, account, ticket, count, amount, selection, grading, drawingId, position, drawingName, creationDate, orderNumber, ticketNumber, subtickets, profit, prizeVersion, domainId, domainUrl, currencyId);
            var losers = new List<LoserInfo>() { loser1 };
            receiver.ReceiveLoser(loser1);
            receiver.AccumulateAndUpdateRecordsForReports(drawDate1);
            receiver.EndReception();

            // incoming ticket #2
            drawDate1 = new DateTime(2020, 04, 18, 10, 20, 00);
            creationDate = new DateTime(2020, 04, 18, 9, 20, 00);
            ticket = "P3B[0,1,2]";
            subtickets = "(0-1-2,1)";
            profit = -149.75m;
            ticketNumber = *********;
            var winner1 = new WinnerInfo(company, draw, fireball, gradedby, prize, state, drawDate1.Hour, drawDate1.Minute, drawDate1.Year, drawDate1.Month, drawDate1.Day, account, ticket, count, amount, selection, grading, drawingId, uniqueDrawingId, position, drawingName, creationDate, orderNumber, ticketNumber, subtickets, profit, prizeVersion, domainId, domainUrl, currencyId);
            var winners = new List<WinnerInfo>() { winner1 };
            receiver.ReceiveWinner(winner1);
            receiver.AccumulateAndUpdateRecordsForReports(drawDate1);
            receiver.EndReception();

            // generating report directly from service
            var lotto900 = company.Lotto900();
            var startDate = new DateTime(2020, 04, 17, 0, 0, 00);
            var endDate = new DateTime(2020, 04, 19, 0, 0, 00);
            var now = new DateTime(2020, 04, 19, 11, 0, 00);
            var report = lotto900.Reports.GenerateDailyTotalProfit(startDate, endDate, now, "all", "all", currencyId);

            // incoming ticket #3
            drawDate1 = new DateTime(2020, 04, 18, 10, 20, 00);
            creationDate = new DateTime(2020, 04, 18, 9, 20, 00);
            ticket = "P3B[2,1,2]";
            subtickets = "(2-1-2,1)";
            profit = 0.25m;
            ticketNumber = *********;
            loser1 = new LoserInfo(company, draw, fireball, gradedby, state, drawDate1.Hour, drawDate1.Minute, drawDate1.Year, drawDate1.Month, drawDate1.Day, account, ticket, count, amount, selection, grading, drawingId, position, drawingName, creationDate, orderNumber, ticketNumber, subtickets, profit, prizeVersion, domainId, domainUrl, currencyId);
            losers = new List<LoserInfo>() { loser1 };
            receiver.ReceiveLoser(loser1);
            receiver.AccumulateAndUpdateRecordsForReports(drawDate1);
            receiver.EndReception();

            // incoming ticket #4
            state = "TN";
            drawDate1 = new DateTime(2020, 04, 19, 11, 20, 00);
            creationDate = new DateTime(2020, 04, 19, 9, 30, 00);
            ticket = "P3B[0,0,2]";
            subtickets = "(0-0-2,1)";
            profit = 0.25m;
            ticketNumber = *********;
            loser1 = new LoserInfo(company, draw, fireball, gradedby, state, drawDate1.Hour, drawDate1.Minute, drawDate1.Year, drawDate1.Month, drawDate1.Day, account, ticket, count, amount, selection, grading, drawingId, position, drawingName, creationDate, orderNumber, ticketNumber, subtickets, profit, prizeVersion, domainId, domainUrl, currencyId);
            losers = new List<LoserInfo>() { loser1 };
            receiver.ReceiveLoser(loser1);
            receiver.AccumulateAndUpdateRecordsForReports(drawDate1);
            receiver.EndReception();

            // incoming ticket #5
            drawDate1 = new DateTime(2020, 04, 20, 11, 20, 00);
            creationDate = new DateTime(2020, 04, 20, 9, 30, 00);
            ticket = "P3B[0,0,2]";
            subtickets = "(0-0-2,1)";
            profit = 0.25m;
            ticketNumber = *********;
            loser1 = new LoserInfo(company, draw, fireball, gradedby, state, drawDate1.Hour, drawDate1.Minute, drawDate1.Year, drawDate1.Month, drawDate1.Day, account, ticket, count, amount, selection, grading, drawingId, position, drawingName, creationDate, orderNumber, ticketNumber, subtickets, profit, prizeVersion, domainId, domainUrl, currencyId);
            losers = new List<LoserInfo>() { loser1 };
            receiver.ReceiveLoser(loser1);
            receiver.AccumulateAndUpdateRecordsForReports(drawDate1);
            receiver.EndReception();
        }

        [TestMethod]
        public void GradeAndRegradeSendingLosersAndWinnerToDB()
        {
            if (string.IsNullOrEmpty(sqlStringConection)) Assert.Inconclusive("MySQL string connection its required.");

            Integration.UseKafka = true;
            QueueInMemory queue = new QueueInMemory();
            Integration.Kafka = queue;
            CurrenciesTest.AddCurrencies();

            Db bd = new Db();
            bd.MySQL = sqlStringConection;
            bd.DBSelected = DatabaseType.MySQL.ToString();
            Integration.Db = bd;

            DBDairy dbDairy = new DBDairy();
            dbDairy.DBSelected = DatabaseType.MySQL.ToString();
            Integration.DbDairy = dbDairy;

            Company company = new Company();
            company.Sales.CreateStore(1, "Test Store").MakeCurrent();
            company.Accounting = new MockAccounting();
            var lotteries = company.Lotto900();
            DateTime now = new DateTime(2021, 09, 17, 9, 00, 00);
            State state = lotteries.GetOrCreateState("VA", "Virginia", now, "Bart");
            LotteryPick<Pick3> lottery = (LotteryPick<Pick3>)lotteries.GetOrCreateLottery(3, state);
            for (int i = 0; i <= 6; i++) lottery.Every(10, 20, i, now, "Bart");
            State state2 = lotteries.GetOrCreateState("GA", "Georgia", now, "Bart");
            LotteryPick<Pick3> lottery2 = (LotteryPick<Pick3>)lotteries.GetOrCreateLottery(3, state2);
            for (int i = 0; i <= 6; i++) lottery2.Every(9, 20, i, now, "Bart");

            var itIsThePresent = true;
            var schedule = lottery.GetScheduleId(1);
            schedule.Update(itIsThePresent, "Virginia morning", "0123456", 10, 20, now, "Game Admin");
            var schedule2 = lottery2.GetScheduleId(2);
            schedule2.Update(itIsThePresent, "Georgia morning", "0123456", 9, 20, now, "Game Admin");

            var receiver = new ReceiverOfHistoricalPicks();
            receiver.InitHistorical(HistoricalDatabaseType.MySQL, Integration.Db.MySQL);

            var countMessages = queue.Count(queue.TopicForDrawings);
            Assert.AreEqual(2, countMessages);
            var msg = queue.Dequeue(queue.TopicForDrawings).ToString();
            DeserializeMessages(receiver, msg);

            var player = company.CreateCustomer("NO*********", Agents.ARTEMIS).Player;
            string states = "VA,GA";
            string hours = "10:20 AM,9:20 AM";
            string strIsPresentPlayerBeforeToCloseStore = "true,true";
            string strUseNextDate = "true,true";
            string dates = "09/17/2021,09/18/2021,09/19/2021";
            string withFireballs = "false,false,false";
            string numbers = "";
            string selectionMode = "MultipleInputSingleAmount";
            string pickNumber = "3";
            decimal ticketAmount = 2.5m;
            string[] includedNumbersForInputs = new string[] { "011", "110", "111", "112", "113", "114", "115", "116", "117", "118" };
            string gameType = "Boxed";

            var ticketPick3StraightProd = lotteries.GetOrCreateProductById(1);
            ticketPick3StraightProd.Description = "Ticket pick 3 straight";
            ticketPick3StraightProd.Price = 1;

            var domain = company.Sales.CreateDomain(itIsThePresent, 1, "localhost", Agents.ARTEMIS);

            countMessages = queue.Count(queue.TopicForDomains);
            Assert.AreEqual(1, countMessages);
            msg = queue.Dequeue(queue.TopicForDomains).ToString();
            DeserializeMessages(receiver, msg);


            countMessages = queue.Count(queue.TopicForDomains);
            Assert.AreEqual(1, countMessages);
            msg = queue.Dequeue(queue.TopicForDomains).ToString();
            DeserializeMessages(receiver, msg);

            company.Sales.CurrentStore.Add(domain);
            ExcludeSubtickets excludedSubtickets = new ExcludeSubtickets();
            var customer = company.CustomerByPlayer(player);
            customer.ReplicateInOtherNodes(itIsThePresent, true, now, "nick", "avatar", 1, "affi1");
            var ticketNumber = *********;
            Random random = new Random();
            for (int index = 0; index < 10; index++)
            {
                NextDatesAccumulator nextDatesAccumulator = new NextDatesAccumulator(lotteries, domain, now, pickNumber, dates, states, hours, strIsPresentPlayerBeforeToCloseStore, strUseNextDate);
                var orderNumber = company.IdentityOrderNumber;
                OrderCart myOrder = company.NewOrder(customer, orderNumber, Coinage.Coin(Currencies.CODES.USD), orderNumber);
                int indexNumber1 = random.Next(includedNumbersForInputs.Length);
                int indexNumber2 = random.Next(includedNumbersForInputs.Length);
                if (indexNumber1 == indexNumber2)
                {
                    if (indexNumber2 == includedNumbersForInputs.Length - 1) indexNumber2 = 0;
                    else indexNumber2++;
                }

                var includedNumbers = $"{includedNumbersForInputs[indexNumber1]},{includedNumbersForInputs[indexNumber2]}";
                Order order = company.CreateTicketOrder(lotteries, player, states, dates, withFireballs, numbers, selectionMode, ticketAmount, includedNumbers, excludedSubtickets, gameType, myOrder, ticketPick3StraightProd, nextDatesAccumulator, domain);
                company.AddOrders(myOrder);
                var lowBetId = company.IdentitytBetNumber;
                var highBetId = lowBetId + myOrder.CountOfItems - 1;
                company.PurchaseTickets(itIsThePresent, myOrder, now, ticketNumber + index, domain, lowBetId, 0, order.Number);
                var numbersAmount = includedNumbers.Split(',').Length;
                lotteries.CreateWagers(itIsThePresent, lowBetId, highBetId, 1, numbersAmount, ticketNumber + index, order.Number, now);
            }
            
            var player2 = company.CreateCustomer("NO562430374", Agents.ARTEMIS).Player;
            var customer2 = company.CustomerByPlayer(player2);
            customer2.ReplicateInOtherNodes(itIsThePresent, true, now, "nick", "avatar", 2, "affi2");
            ticketNumber = *********;
            for (int index = 0; index < 10; index++)
            {
                NextDatesAccumulator nextDatesAccumulator = new NextDatesAccumulator(lotteries, domain, now, pickNumber, dates, states, hours, strIsPresentPlayerBeforeToCloseStore, strUseNextDate);
                var orderNumber = company.IdentityOrderNumber;
                OrderCart myOrder = company.NewOrder(customer2, orderNumber, Coinage.Coin(Currencies.CODES.USD), orderNumber);
                int indexNumber1 = random.Next(includedNumbersForInputs.Length);
                int indexNumber2 = random.Next(includedNumbersForInputs.Length);
                if (indexNumber1 == indexNumber2)
                {
                    if (indexNumber2 == includedNumbersForInputs.Length - 1) indexNumber2 = 0;
                    else indexNumber2++;
                }

                var includedNumbers = $"{includedNumbersForInputs[indexNumber1]},{includedNumbersForInputs[indexNumber2]}";
                Order order = company.CreateTicketOrder(lotteries, player2, states, dates, withFireballs, numbers, selectionMode, ticketAmount, includedNumbers, excludedSubtickets, gameType, myOrder, ticketPick3StraightProd, nextDatesAccumulator, domain);
                company.AddOrders(myOrder);
                var lowBetId = company.IdentitytBetNumber;
                var highBetId = lowBetId + myOrder.CountOfItems - 1;
                company.PurchaseTickets(itIsThePresent, myOrder, now, ticketNumber + index, domain, lowBetId, 0, order.Number);
                var numbersAmount = includedNumbers.Split(',').Length;
                lotteries.CreateWagers(itIsThePresent, lowBetId, highBetId, 1, numbersAmount, ticketNumber + index, order.Number, now);
            }
            
            var drawDate = new DateTime(2021, 09, 17, 10, 20, 00);
            var drawDate1b = new DateTime(2021, 09, 17, 9, 20, 00);
            var drawDate2 = new DateTime(2021, 09, 18, 10, 20, 00);
            var drawDate3 = new DateTime(2021, 09, 19, 10, 20, 00);
            var drawDate4 = new DateTime(2021, 09, 20, 10, 20, 00);
            now = new DateTime(2021, 09, 17, 11, 00, 00);
            bool notifyEvent = false;
            lottery.DrawPicks(drawDate, "999", now, itIsThePresent, "Bart", notifyEvent);
            /*lottery2.LotteryDraw(drawDate1b, "221", now, itIsThePresent, "Bart");
            now = new DateTime(2021, 09, 18, 11, 00, 00);
            lottery.LotteryDraw(drawDate2, "000", now, itIsThePresent, "Bart");
            now = new DateTime(2021, 09, 19, 11, 00, 00);
            lottery.LotteryDraw(drawDate3, "999", now, itIsThePresent, "Bart");
            lottery.SetNoAction(itIsThePresent, drawDate3, now, "Bart");
            now = new DateTime(2021, 09, 20, 11, 00, 00);
            lottery.LotteryDraw(drawDate4, "991", now, itIsThePresent, "Bart");
            lottery.Regrade(itIsThePresent, drawDate, now, "Bart");
            now = new DateTime(2021, 09, 20, 12, 00, 00);
            lottery.LotteryDraw(drawDate, "111", now, itIsThePresent, "Bart");*/

            var queryMaker = new QueryMakerOfHistoricalPicks();
            countMessages = queue.Count(queue.TopicForCustomers);
            for (int i = 0; i < countMessages; i++)
            {
                msg = queue.Dequeue(queue.TopicForCustomers).ToString();
                DeserializeCustomerMessages(queryMaker, msg);
            }

            countMessages = queue.Count(queue.TopicForLottoGrading);
            //Assert.AreEqual(7, countMessages);
            for (int i = 0; i < countMessages; i++)
            {
                msg = queue.Dequeue(queue.TopicForLottoGrading).ToString();
                DeserializeMessages(receiver, msg);
            }

            now = new DateTime(2021, 09, 18, 12, 00, 00);
            lottery.DrawPicks(drawDate2, "111", now, itIsThePresent, "Bart", notifyEvent);
            countMessages = queue.Count(queue.TopicForLottoGrading);
            for (int i = 0; i < countMessages; i++)
            {
                msg = queue.Dequeue(queue.TopicForLottoGrading).ToString();
                DeserializeMessages(receiver, msg);
            }

        }

        static GradeTicketType GetGradeTicketType(string message)
        {
            return (GradeTicketType)int.Parse(message[0] + "");
        }

        void DeserializeMessages(ReceiverOfHistoricalPicks receiver, string msg) //it must be equal to OnMessageBeforeCommit from BI
        {
            string[] messages = KafkaMessages.Split(msg);
            DateTime day = new DateTime();
            foreach (string message in messages)
            {
                var gradeLine = GetGradeTicketType(message);
                switch (gradeLine)
                {
                    case GradeTicketType.STARTING_TICKET:
                        var startingMsg = new TicketsStreamStartingMessage(message);
                        day = startingMsg.DrawDate.Date;
                        receiver.StartStream(startingMsg);
                        break;
                    case GradeTicketType.ENDING_TICKET:
                        var endingMsg = new TicketsStreamEndingMessage(message);
                        receiver.CloseStream(endingMsg);
                        break;
                    case GradeTicketType.WINNER_TICKET:
                        var winnerInfo = new WinnerInfo(message);
                        receiver.ReceiveWinner(winnerInfo);
                        break;
                    case GradeTicketType.LOSER_TICKET:
                        var loserInfo = new LoserInfo(message);
                        receiver.ReceiveLoser(loserInfo);
                        break;
                    case GradeTicketType.NOACTION_TICKET:
                        var noActionInfo = new NoActionInfo(message);
                        receiver.ReceiveNoAction(noActionInfo);
                        break;
                    case GradeTicketType.WINNER_RESULTS:
                        WinnerResultsInfo winnersMsg = new WinnerResultsInfo(message);
                        if (winnersMsg.Draw[0] != '-')
                        {
                            receiver.IncrementTimeStamp(winnersMsg.DrawingId, winnersMsg.Year, winnersMsg.Month, winnersMsg.Day);
                            receiver.Receive(winnersMsg);
                        }
                        else
                        {
                            receiver.IncrementTimeStampForWinners(winnersMsg.DrawingId, winnersMsg.Year, winnersMsg.Month, winnersMsg.Day);
                        }
                        break;
                    case GradeTicketType.DRAWINGS:
                        DrawingMessage drawingsMsg = new DrawingMessage(message);
                        receiver.Receive(drawingsMsg);
                        break;
                    case GradeTicketType.DOMAINS:
                        var domainsMsg = new DomainBIMessage(message);
                        receiver.Receive(domainsMsg);
                        break;
                    default:
                        throw new Exception($"Message type {gradeLine} is unknown");
                }
            }

            receiver.EndReception();
            if (day != default(DateTime))
            {
                var dayToAccumulateMsg = new DayToAccumulateMsg(day);
                Integration.Kafka.Send(true, $"{KafkaMessage.ACCUMULATOR_GRADING_LOTTO_PREFIX}{Integration.Kafka.TopicForLottoGrading}", dayToAccumulateMsg);
            }
        }

        /*void DeserializeMessagesKeno(ReceiverOfHistoricalKeno receiverKeno, string msg) //it must be equal to OnMessageBeforeCommit from BI
        {
            var dayToAccumulateMsg = new DayToAccumulateMsg(msg);
            receiverKeno.AccumulateAndUpdateRecordsForReports(dayToAccumulateMsg.Day);
        }*/

        void DeserializeMessagesForResending(ReceiverOfHistorical receiver, string msg, Lottery lottery) //it must be equal to OnMessageBeforeCommit from DrawController
        {
            var message = new TicketsStreamEndingMessage(msg);
            var drawDate = message.DrawDate;
            lottery.ResendTicketsToHistorical(drawDate, true);
        }

        void DeserializeMessagesKeno(ReceiverOfHistoricalKeno receiverKeno, string msg) //it must be equal to OnMessageBeforeCommit from BI
        {
            string[] messages = KafkaMessages.Split(msg);
            DateTime day = new DateTime();
            foreach (string message in messages)
            {
                var gradeLine = GetGradeTicketType(message);
                switch (gradeLine)
                {
                    case GradeTicketType.STARTING_TICKET:
                        var startingMsg = new TicketsStreamStartingMessage(message);
                        day = startingMsg.DrawDate.Date;
                        receiverKeno.StartStream(startingMsg);
                        break;
                    case GradeTicketType.ENDING_TICKET:
                        var endingMsg = new TicketsStreamEndingMessage(message);
                        receiverKeno.CloseStream(endingMsg);
                        break;
                    case GradeTicketType.WINNER_TICKET:
                        var winnerKenoInfo = new WinnerKenoInfo(message);
                        receiverKeno.ReceiveWinner(winnerKenoInfo);
                        break;
                    case GradeTicketType.LOSER_TICKET:
                        var loserKenoInfo = new LoserKenoInfo(message);
                        receiverKeno.ReceiveLoser(loserKenoInfo);
                        break;
                    case GradeTicketType.NOACTION_TICKET:
                        var noActionKenoInfo = new NoActionKenoInfo(message);
                        receiverKeno.ReceiveNoAction(noActionKenoInfo);
                        break;
                    case GradeTicketType.WINNER_RESULTS:
                        var winnersMsg = new WinnerResultsInfo(message);
                        if (winnersMsg.Draw[0] != '-')
                        {
                            receiverKeno.IncrementTimeStamp(winnersMsg.DrawingId, winnersMsg.Year, winnersMsg.Month, winnersMsg.Day);
                            receiverKeno.Receive(winnersMsg);
                        }
                        else
                        {
                            receiverKeno.IncrementTimeStampForWinners(winnersMsg.DrawingId, winnersMsg.Year, winnersMsg.Month, winnersMsg.Day);
                        }
                        break;
                    case GradeTicketType.DRAWINGS:
                        var drawingsMsg = new DrawingMessage(message);
                        receiverKeno.Receive(drawingsMsg);
                        break;
                    case GradeTicketType.DOMAINS:
                        var domainsMsg = new DomainBIMessage(message);
                        receiverKeno.Receive(domainsMsg);
                        break;
                    default:
                        throw new Exception($"Message type {gradeLine} is unknown");
                }
            }

            receiverKeno.EndReception();
            if (day != default(DateTime))
            {
                var dayToAccumulateMsg = new DayToAccumulateMsg(day);
                Integration.Kafka.Send(true, $"{KafkaMessage.ACCUMULATOR_GRADING_LOTTO_PREFIX}{Integration.Kafka.TopicForLottoGrading}", dayToAccumulateMsg);
            }
        }

        [TestMethod]
        public void KenoGradeAndRegradeSendingLosersAndWinnerToDB()
        {
            if (string.IsNullOrEmpty(sqlStringConection)) Assert.Inconclusive("MySQL string connection its required.");

            Integration.UseKafka = true;
            QueueInMemory queue = new QueueInMemory();
            Integration.Kafka = queue;
            CurrenciesTest.AddCurrencies();

            Db bd = new Db();
            bd.MySQL = sqlStringConection;
            bd.DBSelected = DatabaseType.MySQL.ToString();
            Integration.Db = bd;

            DBDairy dbDairy = new DBDairy();
            dbDairy.DBSelected = DatabaseType.MySQL.ToString();
            Integration.DbDairy = dbDairy;

            var itIsThePresent = true;
            DateTime now = new DateTime(2019, 04, 17, 9, 0, 0);
            decimal ticketAmount = 0.25m;
            List<int> selection = new List<int> { 1, 2, 3, 4, 5, 6, 7, 8, 9, 10 };
            bool multiplier = false;
            bool bulleye = false;
            Product kenoSingleProd, kenoWithMultiplierAndBulleyeProd, kenoWithMultiplierProd, kenoWithBullEyeProd = null;

            var receiver = new ReceiverOfHistoricalKeno();
            receiver.InitHistorical(HistoricalDatabaseType.MySQL, Integration.Db.MySQL);

            Company company = new Company();
            company.Sales.CreateStore(1, "Test Store").MakeCurrent();
            company.Accounting = new MockAccounting();
            var lotto900 = company.Lotto900();

            kenoSingleProd = lotto900.GetOrCreateProductById(9);
            kenoSingleProd.Description = "Simple Keno Ticket.";
            kenoSingleProd.Price = 1;
            kenoWithMultiplierProd = lotto900.GetOrCreateProductById(10);
            kenoWithMultiplierProd.Description = "Keno Ticket with multiplier.";
            kenoWithMultiplierProd.Price = 0.25m;
            kenoWithBullEyeProd = lotto900.GetOrCreateProductById(11);
            kenoWithBullEyeProd.Description = "Keno Ticket with bulleye.";
            kenoWithBullEyeProd.Price = 0.25m;
            kenoWithMultiplierAndBulleyeProd = lotto900.GetOrCreateProductById(12);
            kenoWithMultiplierAndBulleyeProd.Description = "Keno Ticket with bulleye.";
            kenoWithMultiplierAndBulleyeProd.Price = 0.25m;

            LotteryKeno lotteryKeno = lotto900.GetKeno();
            Domain domain = company.Sales.CreateDomain(itIsThePresent, 1, "localhost", Agents.ARTEMIS);
            Domain domain2 = company.Sales.CreateDomain(itIsThePresent, 2, "ncubo.cr", Agents.ARTEMIS);
            var betRangesKeno = lotto900.BetRangesKeno;
            betRangesKeno.Add(now, domain, 0.75m, 0.25m, "Admin");
            betRangesKeno.Add(now, domain2, 0.75m, 0.25m, "Admin");

            var presetKeno = lotto900.PresetBetAmountsForKeno;
            presetKeno.Add(0.5m, "Admin", now);

            lotteryKeno.Every("*", "[0,10,20,30,40,50]", "*", now, "Admin");
            var receiverKeno = new ReceiverOfHistoricalKeno();
            receiverKeno.InitHistorical(HistoricalDatabaseType.MySQL, Integration.Db.MySQL);

            var player = company.CreateCustomer("NO*********", Agents.ARTEMIS).Player;
            var customer = company.CustomerByPlayer(player);
            customer.ReplicateInOtherNodes(itIsThePresent, true, now, "nick", "avatar", 1, "affi1");
            var ticketNumber = *********;
            var tempDate = now;
            for (int index = 0; index < 5; index ++)
            {
                var nextDraw = lotto900.NextPendingAndNoRegradedSchedulesAtForKeno(tempDate);
                int orderNumber = company.IdentityOrderNumber;
                OrderCart myOrder = company.NewOrder(customer, orderNumber, Coinage.Coin(Currencies.CODES.USD), orderNumber);
                Order order = company.CreateKenoOrder(player, selection, multiplier, bulleye, ticketAmount, nextDraw.IdPrefix, tempDate, myOrder, domain, kenoSingleProd, kenoWithMultiplierProd, kenoWithBullEyeProd, kenoWithMultiplierAndBulleyeProd);
                company.AddOrders(myOrder);
                var lowBetId = index + 1;
                var highBetId = lowBetId + myOrder.CountOfItems - 1;
                company.PurchaseTickets(itIsThePresent, myOrder, tempDate, ticketNumber + index, domain, lowBetId, 0, order.Number);
                lotto900.CreateWagers(itIsThePresent, lowBetId, highBetId, 1, 1, ticketNumber + index, order.Number, tempDate);
                tempDate = tempDate.AddMinutes(10);
            }

            multiplier = true;
            bulleye = true;
            var player2 = company.CreateCustomer("NO562430370", Agents.ARTEMIS).Player;
            var customer2 = company.CustomerByPlayer(player2);
            customer2.ReplicateInOtherNodes(itIsThePresent, true, now, "nick", "avatar", 2, "affi2");
            ticketNumber = *********;
            tempDate = now;
            for (int index = 0; index < 5; index++)
            {
                var nextDraw = lotto900.NextPendingAndNoRegradedSchedulesAtForKeno(tempDate);
                int orderNumber = company.IdentityOrderNumber;
                OrderCart myOrder = company.NewOrder(customer2, orderNumber, Coinage.Coin(Currencies.CODES.USD), orderNumber);
                Order order = company.CreateKenoOrder(player2, selection, multiplier, bulleye, ticketAmount, nextDraw.IdPrefix, tempDate, myOrder, domain, kenoSingleProd, kenoWithMultiplierProd, kenoWithBullEyeProd, kenoWithMultiplierAndBulleyeProd);
                company.AddOrders(myOrder);
                var lowBetId = index + 1;
                var highBetId = lowBetId + myOrder.CountOfItems - 1;
                company.PurchaseTickets(itIsThePresent, myOrder, tempDate, ticketNumber + index, domain, lowBetId, 0, order.Number);
                lotto900.CreateWagers(itIsThePresent, lowBetId, highBetId, 1, 1, ticketNumber + index, order.Number, tempDate);
                tempDate = tempDate.AddMinutes(10);
            }

            var drawDate = now.AddMinutes(10);
            var report = lotteryKeno.DrawKeno(KenoNextDraw.Date2PrefixId(drawDate), new int[] { 6, 16, 26, 36, 46, 56, 66, 76, 50, 15, 25, 35, 45, 55, 65, 75, 40, 14, 24, 34 }, 2, 6, now, itIsThePresent, "Bart");
            report = lotteryKeno.ConfirmDraw(drawDate, now, "Bart");

            drawDate = drawDate.AddMinutes(10);
            report = lotteryKeno.DrawKeno(KenoNextDraw.Date2PrefixId(drawDate), new int[] { 6, 1, 2, 3, 4, 5, 66, 76, 50, 15, 25, 35, 45, 55, 65, 75, 40, 14, 24, 34 }, 2, 6, now, itIsThePresent, "Bart");
            report = lotteryKeno.ConfirmDraw(drawDate, now, "Bart");

            drawDate = drawDate.AddMinutes(10);
            report = lotteryKeno.DrawKeno(KenoNextDraw.Date2PrefixId(drawDate), new int[] { 11, 16, 26, 36, 46, 56, 66, 76, 50, 15, 25, 35, 45, 55, 65, 75, 40, 14, 24, 34 }, 2, 11, now, itIsThePresent, "Bart");
            report = lotteryKeno.ConfirmDraw(drawDate, now, "Bart");

            var countMessages = queue.Count(queue.TopicForDomains);
            var msg = queue.Dequeue(queue.TopicForDomains).ToString();
            DeserializeMessagesKeno(receiverKeno, msg);
            msg = queue.Dequeue(queue.TopicForDomains).ToString();
            DeserializeMessagesKeno(receiverKeno, msg);

            var queryMaker = new QueryMakerOfHistoricalKeno();
            countMessages = queue.Count(queue.TopicForCustomers);
            for (int i = 0; i < countMessages; i++)
            {
                msg = queue.Dequeue(queue.TopicForCustomers).ToString();
                DeserializeCustomerMessages(queryMaker, msg);
            }

            countMessages = queue.Count(queue.TopicForLottoGrading + "_keno");
            //Assert.AreEqual(7, countMessages);
            for (int i = 0; i < countMessages; i++)
            {
                msg = queue.Dequeue(queue.TopicForLottoGrading + "_keno").ToString();
                DeserializeMessagesKeno(receiverKeno, msg);
            }
        }

        void DeserializeCustomerMessages(QueryMakerOfHistoricalPicks queryMaker, string msg) //it must be equal to OnMessageBeforeCommit from BI
        {
            CustomerMessage.CustomerMessageType messageType = CustomerMessage.GetType(msg);
            if (messageType == CustomerMessage.CustomerMessageType.NEW_CUSTOMER)
            {
                NewCustomerMessage customerMsg = new NewCustomerMessage(msg);
                if (customerMsg.AgentId == (int)Agents.ARTEMIS)
                {
                    var artemisCustomerMsg = new NewArtemisCustomerMessage(msg);
                    queryMaker.InsertAffiliateIfNotExist(artemisCustomerMsg.AffiliateId, artemisCustomerMsg.AffiliateName, artemisCustomerMsg.AccountNumber);
                }
            }
        }

        void DeserializeCustomerMessages(QueryMakerOfHistoricalKeno queryMaker, string msg) //it must be equal to OnMessageBeforeCommit from BI
        {
            CustomerMessage.CustomerMessageType messageType = CustomerMessage.GetType(msg);
            if (messageType == CustomerMessage.CustomerMessageType.NEW_CUSTOMER)
            {
                NewCustomerMessage customerMsg = new NewCustomerMessage(msg);
                if (customerMsg.AgentId == (int)Agents.ARTEMIS)
                {
                    var artemisCustomerMsg = new NewArtemisCustomerMessage(msg);
                    queryMaker.InsertAffiliateIfNotExist(artemisCustomerMsg.AffiliateId, artemisCustomerMsg.AffiliateName, artemisCustomerMsg.AccountNumber);
                }
            }
        }
    }
}
