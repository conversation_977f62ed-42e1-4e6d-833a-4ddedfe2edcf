﻿using GamesEngine.Settings;
using MySql.Data.MySqlClient;
using Puppeteer;
using Puppeteer.EventSourcing;
using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Linq;
using System.Reflection;
using System.Text;

namespace LottoAPI.LottoFollower
{
	public class ContadorDeNumeros
	{
		private Dictionary<string, int> counter = new Dictionary<string, int>();

		internal void IncrementarNumero(string numero, int cantidad)
		{
			if (!counter.ContainsKey(numero))
			{
				counter.Add(numero, cantidad);
			}
			else
			{
				int valor = counter[numero];
				counter[numero] = valor + cantidad;
			}
		}

		internal IEnumerable<KeyValuePair<string, int>> Conteos()
		{
			return this.counter;
		}
	}


	public class VecesCompradasUnNumeroRule : RuleWithoutActor
	{
		private ContadorDeNumeros contador = new ContadorDeNumeros();
		private int inicial;
		private int final;
		private bool sqlServer = false;

		public VecesCompradasUnNumeroRule()
		{
		}

		public override void Then(Script script)
		{
		}

		private const string CONNECTION_STRING = "persistsecurityinfo=True;port=3306;Server=localhost;Database=lotto_statistics;user id=admin;password=***456;SslMode=none";
		//private const string CONNECTION_STRING = "data source=localhost;initial catalog=lotto_statistics;user id=admin;password=***;multipleactiveresultsets=True";

		public override void Init()
		{
			CrearAlmacenamiento(CONNECTION_STRING);
			inicial = CorteDeVecesComprarNumero(CONNECTION_STRING);
		}

		private int CorteDeVecesComprarNumero(string connectionString)
		{
			if (sqlServer)
			{
				return CorteDeVecesComprarNumeroSQLServer(connectionString);
			}
			else
			{
				return CorteDeVecesComprarNumeroMySQL(connectionString);
			}
		}

		private int CorteDeVecesComprarNumeroMySQL(string connectionString)
		{
			string sql = "SELECT DairyId FROM " + TABLE_CORTES + " WHERE NombreCorte = '" + TABLE_ACUMULADOS + "'";
			int dairyId = 0;
			using (MySqlConnection connection = new MySqlConnection(connectionString))
			{
				try
				{
					connection.Open();
					using (MySqlCommand command = new MySqlCommand(sql, connection))
					using (MySqlDataReader reader = command.ExecuteReader())
					{
						while (reader.Read())
						{
							dairyId = reader.GetInt32(0);
						}
						reader.Close();
					}
				}
				finally
				{
					connection.Close();
				}
			}

			return dairyId;
		}

		private int CorteDeVecesComprarNumeroSQLServer(string connectionString)
		{
			string sql = "SELECT DairyId FROM " + TABLE_CORTES + " WHERE NombreCorte = '" + TABLE_ACUMULADOS + "'";
			int dairyId = 0;
			using (SqlConnection connection = new SqlConnection(connectionString))
			{
				try
				{
					connection.Open();
					using (SqlCommand command = new SqlCommand(sql, connection))
					using (SqlDataReader reader = command.ExecuteReader())
					{
						while (reader.Read())
						{
							dairyId = reader.GetInt32(0);
						}
						reader.Close();
					}
				}
				finally
				{
					connection.Close();
				}
			}

			return dairyId;
		}

		private const string TABLE_ACUMULADOS = "VecesCompradasUnNumero";
		private const string TABLE_CORTES = "Cortes";

		private void CrearAlmacenamiento(string connectionString)
		{
			if (sqlServer)
				CrearAlmacenamientoSQLServer(connectionString);
			else
				CrearAlmacenamientoMySQL(connectionString);
		}

		private void CrearAlmacenamientoMySQL(string connectionString)
		{
			StringBuilder statement = new StringBuilder();
			statement
				.Append("create table IF NOT EXISTS ").Append(TABLE_ACUMULADOS)
				.Append("  (")
				.Append("  Numero VARCHAR(10) NOT NULL PRIMARY KEY,")
				.Append("  Cantidad INT NOT NULL")
				.Append(" );");

			statement
				.Append("create table IF NOT EXISTS ").Append(TABLE_CORTES)
				.Append("  (")
				.Append("  NombreCorte VARCHAR(50) NOT NULL PRIMARY KEY,")
				.Append("  DairyId INT NOT NULL")
				.Append(" );")
				.Append(" INSERT INTO ").Append(TABLE_CORTES).Append("(NombreCorte, DairyId) VALUES (")
				.Append("'").Append(TABLE_ACUMULADOS).Append("'")
				.Append(", 0) ON DUPLICATE KEY UPDATE DairyId = DairyId;");

			string sql = statement.ToString();

			using (MySqlConnection connection = new MySqlConnection(connectionString))
			{
				connection.Open();
				using (MySqlCommand command = new MySqlCommand(sql, connection))
				{
					command.ExecuteNonQuery();
				}

				connection.Close();
			}
		}

		private void CrearAlmacenamientoSQLServer(string connectionString)
		{
			StringBuilder statement = new StringBuilder();
			statement
				.Append("IF NOT EXISTS(")
				.Append("SELECT 1 FROM INFORMATION_SCHEMA.TABLES ")
				.Append("WHERE TABLE_NAME = '").Append(TABLE_ACUMULADOS).Append("')")
				.Append(" BEGIN")
				.Append("  create table ").Append(TABLE_ACUMULADOS)
				.Append("  (")
				.Append("  Numero VARCHAR(10) NOT NULL PRIMARY KEY,")
				.Append("  Cantidad INT NOT NULL")
				.Append(" );")
				.Append(" END; ");

			statement
				.Append("IF NOT EXISTS(")
				.Append("SELECT 1 FROM INFORMATION_SCHEMA.TABLES ")
				.Append("WHERE TABLE_NAME = '").Append(TABLE_CORTES).Append("')")
				.Append(" BEGIN")
				.Append("  create table ").Append(TABLE_CORTES)
				.Append("  (")
				.Append("  NombreCorte VARCHAR(50) NOT NULL PRIMARY KEY,")
				.Append("  DairyId INT NOT NULL")
				.Append(" );")
				.Append(" INSERT INTO ").Append(TABLE_CORTES).Append("(NombreCorte, DairyId) VALUES (")
				.Append("'").Append(TABLE_ACUMULADOS).Append("'")
				.Append(", 0);")
				.Append(" END;");

			string sql = statement.ToString();

			using (SqlConnection connection = new SqlConnection(connectionString))
			{
				connection.Open();
				using (SqlCommand command = new SqlCommand(sql, connection))
				{
					command.ExecuteNonQuery();
				}
					
				connection.Close();
			}
		}

		public override void PartialUpdate()
		{
			foreach (var element in contador.Conteos())
			{
				var numero = element.Key;
				var conteo = element.Value;
				UpdateAcumulado(CONNECTION_STRING, numero, conteo);
			}

			UpdateCorte(CONNECTION_STRING);
		}

		private void UpdateAcumulado(string connectionString, string numero, int conteo)
		{
			if (sqlServer)
				UpdateAcumuladoSQLServer(connectionString, numero, conteo);
			else
				UpdateAcumuladoMySQL(connectionString, numero, conteo);
		}

		private void UpdateAcumuladoMySQL(string connectionString, string numero, int conteo)
		{
			string sql = $@"INSERT INTO {TABLE_ACUMULADOS}
							(Numero, cantidad)
						VALUES
							('{numero}', {conteo})
				ON DUPLICATE KEY UPDATE cantidad = cantidad + {conteo}; ";

			using (MySqlConnection connection = new MySqlConnection(connectionString))
			{
				connection.Open();
				using (MySqlCommand command = new MySqlCommand(sql, connection))
				{
					command.ExecuteNonQuery();
				}

				connection.Close();
			}
		}

		private void UpdateAcumuladoSQLServer(string connectionString, string numero, int conteo)
		{
			string sql = $@"
							If Not Exists(select * from {TABLE_ACUMULADOS} where Numero='{numero}')
							BEGIN
								INSERT INTO {TABLE_ACUMULADOS} (Numero, cantidad) VALUES ('{numero}', {conteo});
							END
							ELSE
							BEGIN
								UPDATE {TABLE_ACUMULADOS} SET cantidad = cantidad + {conteo} WHERE numero='{numero}'
							END;";

			using (SqlConnection connection = new SqlConnection(connectionString))
			{
				connection.Open();
				using (SqlCommand command = new SqlCommand(sql, connection))
				{
					command.ExecuteNonQuery();
				}

				connection.Close();
			}
		}

		private void UpdateCorte(string connectionString)
		{
			if (sqlServer)
				UpdateCorteSQLServer(connectionString);
			else
				UpdateCorteMySQL(connectionString);
		}

		private void UpdateCorteMySQL(string connectionString)
		{
			StringBuilder statement = new StringBuilder();
			statement
				.Append("UPDATE ").Append(TABLE_CORTES).Append(" SET DairyId = ").Append(final).Append(";");

			string sql = statement.ToString();

			using (MySqlConnection connection = new MySqlConnection(connectionString))
			{
				connection.Open();
				using (MySqlCommand command = new MySqlCommand(sql, connection))
				{
					command.ExecuteNonQuery();
				}

				connection.Close();
			}
		}

		private void UpdateCorteSQLServer(string connectionString)
		{
			StringBuilder statement = new StringBuilder();
			statement
				.Append("UPDATE ").Append(TABLE_CORTES).Append(" SET DairyId = ").Append(final).Append(";");

			string sql = statement.ToString();

			using (SqlConnection connection = new SqlConnection(connectionString))
			{
				connection.Open();
				using (SqlCommand command = new SqlCommand(sql, connection))
				{
					command.ExecuteNonQuery();
				}

				connection.Close();
			}
		}

		public override void When(Script script)
		{
			if(script.DairyId < inicial)
			{
				return;
			}

			bool conditionDeArbol = false;
			if (script.Text.IndexOf("nextDatesAccumulator = NextDatesAccumulator(") != -1)
			{
				var cantidad = CantidadDeSorteosXDias(script.Text);
				var numeros = Numeros(script.Text);
				foreach (var numero in numeros)
				{
					contador.IncrementarNumero(numero, cantidad);
				}

				if (script.Text.IndexOf(".SaleTickets") != -1)
				{
					conditionDeArbol = true;
				}
				else if (script.Text.IndexOf(".PurchaseTickets") != -1)
				{
					conditionDeArbol = true;
				}
			}

			final = script.DairyId;

			if (conditionDeArbol)
			{
                APMHelper.StartSpan(MethodBase.GetCurrentMethod().DeclaringType.Name, "RuleMatch");

                Then(script);

                APMHelper.EndSpan(MethodBase.GetCurrentMethod().DeclaringType.Name);
            }
		}

        public override void FinalizeRule()
        {
            APMHelper.DisposeSpan(MethodBase.GetCurrentMethod().DeclaringType.Name);
        }


        public int CantidadDeSorteosXDias(string script)
		{
			const string NEXT_DATES_ACCUMULATOR = "nextDatesAccumulator = NextDatesAccumulator(";
			int inicio = script.IndexOf(NEXT_DATES_ACCUMULATOR);
			if (inicio == -1) return -1;

			int final = script.IndexOf(");", inicio);
			string cadena = script.Substring(inicio, final - inicio);
			string[] argumentos = cadena.Split('\'');
			string fechas = argumentos[3];
			string estados = argumentos[5];

			int countFechas = fechas.Count(f => f == ',') + 1;
			int countEstados = estados.Count(f => f == ',') + 1;

			return countEstados * countFechas;
		}

		public List<string> Numeros(string script)
		{
			var tieneInputs = script.IndexOf("'MultipleInputSingleAmount'") != -1;
			var tieneBalls = script.IndexOf("'balls'") != -1;

			if(tieneInputs && tieneBalls)
			{
				return NumerosInput(script).Concat(NumerosBalls(script)).ToList();
			}
			if (tieneInputs) return NumerosInput(script);
			if (tieneBalls) return NumerosBalls(script);

			throw new System.Exception("Comando sin manejar");
		}

		//TODO: Rule #2
		Dictionary<string, int> contadorMontos = new Dictionary<string, int>();
		void foo()
		{
			decimal compra = ***.456m;
			int tickets = 0;//Sorteos X fechas X numeros... por ejemplo 35 tickets
			if(compra == 0.25m)
			{
				contadorMontos["0.25"] += tickets;
			}
			else if(compra == 0.50m)
			{
				contadorMontos["0.50"] += tickets;
			}
			else if (compra == 0.75m)
			{
				contadorMontos["0.75"] += tickets;
			}
			else if (compra == 1m)
			{
				contadorMontos["1"] += tickets;
			}
			else if(compra < 1)
			{
				contadorMontos["<1"] += tickets;
			}
			//TODO: Buscar el monto 2, 2.50, 3, 5, <5
			//TODO: Buscar el monto 10, 15, 20, 25, 30, 50, <50
			//TODO: Buscar el monto 75, 100, <100
			//TODO: Buscar el monto >100
		}

		//TODO: Rule #3
		//Contar tickets y plata por Sorteo!
		//2 diccionarios por ejemplo

		public List<string> NumerosInput(string script)
		{
			const string CREATE_TICKET_ORDER_1 = "myOrder = company.CreateTicketFullOrder(";
			const string CREATE_TICKET_ORDER_2 = "myOrder = company.CreateTicketOrder(";
			int inicio = script.IndexOf(CREATE_TICKET_ORDER_1);
			if(inicio == -1)
			{
				inicio = script.IndexOf(CREATE_TICKET_ORDER_2);
			}
			int final = script.IndexOf(");", inicio);

			string cadena = script.Substring(inicio, final - inicio);
			string[] argumentos = cadena.Split('\'');
			List<string> result = new List<string>();
			string[] numeros = argumentos[5].Split(',');
			NumeroConcatenado(numeros, "", result);
			numeros = result.ToArray();

			return numeros.ToList();
		}

		private void NumeroConcatenado(string [] numeros, string parcial, List<string> resultados)
		{
			if(numeros.Length == 0)
			{
				resultados.Add(parcial);
			}
			else
			{
				string[] restantes = new string[numeros.Length - 1];
				Array.Copy(numeros, 1, restantes, 0, restantes.Length);
				foreach (char c in numeros[0])
				{
					if(c == '*')
					{
						foreach(char c2 in "0***456789")
						{
							string nuevoParcial = parcial + c2;
							NumeroConcatenado(restantes, nuevoParcial, resultados);
						}
					}
					else
					{
						string nuevoParcial = parcial + c;
						NumeroConcatenado(restantes, nuevoParcial, resultados);
					}					
				}
			}
		}

		public List<string> NumerosBalls(string script)
		{
			const string CREATE_TICKET_ORDER_1 = "myOrder = company.CreateTicketOrder(";
			const string CREATE_TICKET_ORDER_2 = "myOrder = company.CreateTicketFullOrder(";
			int inicio = script.IndexOf(CREATE_TICKET_ORDER_1);
			if(inicio == -1)
			{
				inicio = script.IndexOf(CREATE_TICKET_ORDER_2);
			}
			int final = script.IndexOf(");", inicio);

			string cadena = script.Substring(inicio, final - inicio);
			string[] argumentos = cadena.Split('\'');
			string [] numeros = argumentos[5].Split(',');
			List<string> result = new List<string>();
			NumeroConcatenado(numeros, "", result);

			return result;
		}
	}
}
