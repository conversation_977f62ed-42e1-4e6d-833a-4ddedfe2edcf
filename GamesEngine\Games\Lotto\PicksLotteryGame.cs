﻿using Calendarizador.GamesEngine.Time.Schedulers;
using GamesEngine.Bets;
using GamesEngine.Business;
using GamesEngine.Domains;
using GamesEngine.Exchange;
using GamesEngine.Finance;
using GamesEngine.Gameboards.Lotto;
using GamesEngine.Location;
using GamesEngine.Marketing.Campaigns;
using GamesEngine.Marketing.Messaging;
using GamesEngine.Messaging;
using GamesEngine.Preferences.Lotto;
using GamesEngine.PurchaseOrders;
using GamesEngine.PurchaseOrders.Activators.Lotto;
using GamesEngine.Settings;
using GamesEngine.Time;
using Puppeteer.EventSourcing;
using Puppeteer.EventSourcing.Libraries;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Net.Sockets;
using System.Runtime.CompilerServices;
using System.Text;
using System.Threading;
using static GamesEngine.Business.Company;
using static GamesEngine.Games.Lotto.NextDatesAccumulator;
using static GamesEngine.Time.Schedule;
using TicketType = GamesEngine.Gameboards.Lotto.TicketType;

[assembly: InternalsVisibleTo("LoyaltyAPI")]

namespace GamesEngine.Games.Lotto
{
    [Puppet]
    internal class PicksLotteryGame : LotteryGame, Subscriber
    {
        private readonly Company company;
        private readonly Dictionary<State, LotteryPick<Pick2>> pick2 = new Dictionary<State, LotteryPick<Pick2>>();
        private readonly Dictionary<State, LotteryPick<Pick3>> pick3 = new Dictionary<State, LotteryPick<Pick3>>();
        private readonly Dictionary<State, LotteryPick<Pick4>> pick4 = new Dictionary<State, LotteryPick<Pick4>>();
        private readonly Dictionary<State, LotteryPick<Pick5>> pick5 = new Dictionary<State, LotteryPick<Pick5>>();
        private readonly LotteryPowerball powerball;
        private LotteryKeno keno;

        private readonly Prizes prizesKeno = new PrizesKeno();
        private readonly Prizes prizesPicks = new PrizesPicks();
        private readonly PresetTicketEntries presetTicketEntriesPowerball;

        private readonly LottoDefaultPreferences defaultPreferences;
        private readonly Reports reports;
        private readonly Store lotteryStore;

        internal const double MAX_AMOUNT_ALLOWED_PER_TICKET = 999.99; //Database field supports 999.99 value as max
        
        public const decimal DEFAULT_MIN_BET_FOR_RR = 0.25m;

        private List<AccountingPendingWager> accountingPendingWagers;
        private List<Ticket> ticketsMismatchingWithWagerNumbers;
        private DateTime lastKnownCompleteGradedDate = new DateTime(2018, 11, 01);

        private static readonly int[] pickValues = new int[] { 2, 3, 4, 5 };
        private readonly PlayersProfiles playersProfiles;
        private readonly BetRanges betRangesPick2;
        private readonly BetRanges betRangesPick3;
        private readonly BetRanges betRangesPick4;
        private readonly BetRanges betRangesPick5;
        private readonly BetRanges betRangesPowerball;
        private readonly BetRanges betRangesKeno;

        private readonly PresetBetAmounts presetBetAmountsPick2;
        private readonly PresetBetAmounts presetBetAmountsPick3;
        private readonly PresetBetAmounts presetBetAmountsPick4;
        private readonly PresetBetAmounts presetBetAmountsPick5;
        private readonly PresetBetAmounts presetBetAmountsPowerball;
        private readonly PresetBetAmounts presetBetAmountsKeno;

        private const decimal DEFAULT_MIN_BET_FOR_PICK2 = 0.25m;
        private const decimal DEFAULT_MIN_BET_FOR_PICK3 = 0.25m;
        private const decimal DEFAULT_MIN_BET_FOR_PICK4 = 0.25m;
        private const decimal DEFAULT_MIN_BET_FOR_PICK5 = 0.25m;
        private const decimal DEFAULT_MAX_BET_FOR_PICK2 = 100;
        private const decimal DEFAULT_MAX_BET_FOR_PICK3 = 100;
        private const decimal DEFAULT_MAX_BET_FOR_PICK4 = 25;
        private const decimal DEFAULT_MAX_BET_FOR_PICK5 = 25;
        private const decimal DEFAULT_MAX_BET_FOR_POWERBALL = 2;
        private const decimal DEFAULT_MAX_BET_FOR_KENO = 0.25m;

        private const decimal DEFAULT_MIN_BET_FOR_POWERBALL = 0.25m;
        private const decimal DEFAULT_MIN_BET_FOR_KENO = 0.25m;

        internal PresetBetAmounts PresetBetAmountsByPick(int pick)
        {
            if (!pickValues.Contains(pick)) throw new GameEngineException($"{nameof(pick)} {pick} is not valid");
            switch (pick)
            {
                case 2:
                    return presetBetAmountsPick2;
                case 3:
                    return presetBetAmountsPick3;
                case 4:
                    return presetBetAmountsPick4;
                case 5:
                    return presetBetAmountsPick5;
                default:
                    throw new GameEngineException($"There is no {nameof(PresetBetAmounts)} implementation for {nameof(pick)} {pick}");
            }
        }

        internal bool HasFireball(GameType gameType)
        {
            if (gameType == null) throw new ArgumentNullException(nameof(gameType));

            int pickNumber = gameType.PickNumber;
            switch (pickNumber)
            {
                case 2:
                    return pick2.Values.Any(x => x.IsFireBallTurnedOn);
                case 3:
                    return pick3.Values.Any(x => x.IsFireBallTurnedOn);
                case 4:
                    return pick4.Values.Any(x => x.IsFireBallTurnedOn);
                case 5:
                    return pick5.Values.Any(x => x.IsFireBallTurnedOn);
                default:
                    return false;
            }
        }

        internal PresetTicketEntries PresetTicketEntriesPowerball
        {
            get
            {
                return presetTicketEntriesPowerball;
            }
        }

        internal PresetBetAmounts PresetBetAmountsForPowerball => this.presetBetAmountsPowerball;

        internal PresetBetAmounts PresetBetAmountsForKeno => this.presetBetAmountsKeno;

        internal LottoDefaultPreferences DefaultPreferences
        {
            get
            {
                return defaultPreferences;
            }
        }

        internal BetRanges BetRangesByPick(int pick)
        {
            if (!pickValues.Contains(pick)) throw new GameEngineException($"{nameof(pick)} {pick} is not valid");
            switch (pick)
            {
                case 2:
                    return betRangesPick2;
                case 3:
                    return betRangesPick3;
                case 4:
                    return betRangesPick4;
                case 5:
                    return betRangesPick5;
                default:
                    throw new GameEngineException($"There is no {nameof(BetRanges)} implementation for {nameof(pick)} {pick}");
            }
        }

        internal BetRanges BetRangesForPowerBall => this.betRangesPowerball;

        internal BetRanges BetRangesKeno => this.betRangesKeno;

        internal PlayersProfiles PlayersProfiles => this.playersProfiles;

        internal static int[] PickValues => pickValues;

        internal Reports Reports
        {
            get
            {
                return reports;
            }
        }

        string Subscriber.Id => (this.lotteryStore as Subscriber).Id;

        Messages Subscriber.Messages => (this.lotteryStore as Subscriber).Messages;

        internal Messages Messages
        {
            get
            {
                return (this.lotteryStore as Subscriber).Messages;
            }
        }

        [Obsolete]
        internal GameTypes GameTypes => this.company.LotteryGamesPool.GameTypes;

        internal override RiskProfilesLotteries RiskProfiles { get; }

        internal override Risks Risks { get; }

        internal PicksLotteryGame(LotteryGamesPool lotteryGamesPool) : base(lotteryGamesPool.Company)
        {
            if (lotteryGamesPool.Company == null) throw new ArgumentNullException(nameof(lotteryGamesPool.Company));
            this.company = lotteryGamesPool.Company;
            presetTicketEntriesPowerball = new PresetTicketEntries();

            defaultPreferences = new LottoDefaultPreferences(this);
            reports = new Reports(this, pick2, pick3, pick4, pick5);
            if (company.Sales.HasCurrentStore()) lotteryStore = company.Sales.CurrentStore;

            this.playersProfiles = new PlayersProfiles();
            this.betRangesPick2 = new BetRanges();
            this.betRangesPick3 = new BetRanges();
            this.betRangesPick4 = new BetRanges();
            this.betRangesPick5 = new BetRanges();
            this.betRangesPowerball = new BetRanges();
            this.betRangesKeno = new BetRanges();

            this.presetBetAmountsPick2 = new PresetBetAmounts();
            this.presetBetAmountsPick3 = new PresetBetAmounts();
            this.presetBetAmountsPick4 = new PresetBetAmounts();
            this.presetBetAmountsPick5 = new PresetBetAmounts();
            this.presetBetAmountsPowerball = new PresetBetAmounts();
            this.presetBetAmountsKeno = new PresetBetAmounts();

            powerball = new LotteryPowerball(this);
            Risks = new Risks();
            Risks.StartupRiskForLotteryPowerball(powerball);
            RiskProfiles = new RiskProfilesLotteries(this);
        }

        internal PicksLotteryGame(PicksLotteryGame picksLotteryGameBasedOn) : base(picksLotteryGameBasedOn.Company)
        {
            if (picksLotteryGameBasedOn == null) throw new ArgumentNullException(nameof(picksLotteryGameBasedOn));

            this.company = picksLotteryGameBasedOn.Company;
            presetTicketEntriesPowerball = picksLotteryGameBasedOn.presetTicketEntriesPowerball;

            defaultPreferences = picksLotteryGameBasedOn.defaultPreferences;
            reports = new Reports(this, pick2, pick3, pick4, pick5);
            if (company.Sales.HasCurrentStore()) lotteryStore = company.Sales.CurrentStore;

            this.playersProfiles = picksLotteryGameBasedOn.playersProfiles;
            this.betRangesPick2 = picksLotteryGameBasedOn.betRangesPick2;
            this.betRangesPick3 = picksLotteryGameBasedOn.betRangesPick3;
            this.betRangesPick4 = picksLotteryGameBasedOn.betRangesPick4;
            this.betRangesPick5 = picksLotteryGameBasedOn.betRangesPick5;
            this.betRangesPowerball = picksLotteryGameBasedOn.betRangesPowerball;
            this.betRangesKeno = picksLotteryGameBasedOn.betRangesKeno;

            this.presetBetAmountsPick2 = picksLotteryGameBasedOn.presetBetAmountsPick2;
            this.presetBetAmountsPick3 = picksLotteryGameBasedOn.presetBetAmountsPick3;
            this.presetBetAmountsPick4 = picksLotteryGameBasedOn.presetBetAmountsPick4;
            this.presetBetAmountsPick5 = picksLotteryGameBasedOn.presetBetAmountsPick5;
            this.presetBetAmountsPowerball = picksLotteryGameBasedOn.presetBetAmountsPowerball;
            this.presetBetAmountsKeno = picksLotteryGameBasedOn.presetBetAmountsKeno;

            Risks = picksLotteryGameBasedOn.Risks;
            powerball = picksLotteryGameBasedOn.powerball;
            RiskProfiles = picksLotteryGameBasedOn.RiskProfiles;
        }

        internal string CreateVariablesForDairy()
        {
            string variables = State.CreateVariablesForDairy();
            return variables;
        }

        internal Prizes Prizes
        {
            get
            {
                if (keno != null) return prizesKeno;
                return prizesPicks;
            }
        }

        internal Store Store
        {
            get
            {
                return this.lotteryStore;
            }
        }

        internal override LotteryForPicks GetLottery(int pick, State state)
        {
            if (state == null) throw new ArgumentNullException(nameof(state));
            LotteryForPicks lottery = null;
            switch (pick)
            {
                case 2:
                    lottery = pick2.GetValueOrDefault(state);
                    if (lottery == null)
                    {
                        throw new GameEngineException($"There is no any {nameof(pick2)} lottery for state {state.Abbreviation}");
                    }
                    return lottery;
                case 3:
                    lottery = pick3.GetValueOrDefault(state);
                    if (lottery == null)
                    {
                        throw new GameEngineException($"There is no any {nameof(pick3)} lottery for state {state.Abbreviation}");
                    }
                    return lottery;
                case 4:
                    lottery = pick4.GetValueOrDefault(state);
                    if (lottery == null)
                    {
                        throw new GameEngineException($"There is no any {nameof(pick4)} lottery for state {state.Abbreviation}");
                    }
                    return lottery;
                case 5:
                    lottery = pick5.GetValueOrDefault(state);
                    if (lottery == null)
                    {
                        throw new GameEngineException($"There is no any {nameof(pick5)} lottery for state {state.Abbreviation}");
                    }
                    return lottery;
                default:
                    throw new GameEngineException($"There is no {nameof(lottery)} implementation for {nameof(pick)} {pick}");
            }
        }

        internal override IEnumerable<Schedule> SchedulesByTimeOf(DateTime now, Domain domain)
        {
            IEnumerable<Schedule> results = base.SchedulesByTimeOf(now, domain);
            return results.Where(x => x.Lottery is LotteryForPicks && x.Lottery is not LotteryPowerball);
        }

        internal LotteryPowerball GetPowerball()
        {
            return this.powerball;
        }

        internal LotteryKeno GetKeno()
        {
            if (keno == null)
            {
                keno = new LotteryKeno(this);
                Risks.StartupRiskForLotteryKeno(keno);
            }
            return this.keno;
        }
        internal bool HasLottery(int pick, State state)
        {
            if (state == null) throw new ArgumentNullException(nameof(state));
            Lottery lottery = null;
            switch (pick)
            {
                case 2:
                    lottery = pick2.GetValueOrDefault(state);
                    return lottery != null;
                case 3:
                    lottery = pick3.GetValueOrDefault(state);
                    return lottery != null;
                case 4:
                    lottery = pick4.GetValueOrDefault(state);
                    return lottery != null;
                case 5:
                    lottery = pick5.GetValueOrDefault(state);
                    return lottery != null;
                default:
                    throw new GameEngineException($"There is no {nameof(lottery)} implementation for {nameof(pick)} {pick}");
            }
        }

        [Obsolete]
        internal int MaxDaysFromTodayToSellATicket
        {
            get
            {
                return company.Sales.CurrentStore.MaxDaysFromTodayToSellATicket;
            }
            set
            {
                company.Sales.CurrentStore.MaxDaysFromTodayToSellATicket = value;
            }
        }

        internal LotteryForPicks GetOrCreateLottery(int pick, State state)
        {
            if (!pickValues.Contains(pick)) throw new GameEngineException($"{nameof(pick)} {pick} is not valid");
            if (state == null) throw new ArgumentNullException(nameof(state));

            LotteryForPicks lottery = null;
            switch (pick)
            {
                case 2:
                    lottery = pick2.GetValueOrDefault(state);
                    if (lottery == null)
                    {
                        lottery = new LotteryPick<Pick2>(this, state);
                        pick2.Add(state, lottery as LotteryPick<Pick2>);
                        Risks.StartupRiskForNewLottery(pick, lottery);
                    }
                    return lottery;
                case 3:
                    lottery = pick3.GetValueOrDefault(state);
                    if (lottery == null)
                    {
                        lottery = new LotteryPick<Pick3>(this, state);
                        pick3.Add(state, lottery as LotteryPick<Pick3>);
                        Risks.StartupRiskForNewLottery(pick, lottery);
                    }
                    return lottery;
                case 4:
                    lottery = pick4.GetValueOrDefault(state);
                    if (lottery == null)
                    {
                        lottery = new LotteryPick<Pick4>(this, state);
                        pick4.Add(state, lottery as LotteryPick<Pick4>);
                        Risks.StartupRiskForNewLottery(pick, lottery);
                    }
                    return lottery;
                case 5:
                    lottery = pick5.GetValueOrDefault(state);
                    if (lottery == null)
                    {
                        lottery = new LotteryPick<Pick5>(this, state);
                        pick5.Add(state, lottery as LotteryPick<Pick5>);
                        Risks.StartupRiskForNewLottery(pick, lottery);
                    }
                    return lottery;
                default:
                    throw new GameEngineException($"There is no {nameof(lottery)} implementation for {nameof(pick)} {pick}");
            }
        }

        [Obsolete]
        internal Product GetOrCreateProductById(int id) => this.company.LotteryGamesPool.GetOrCreateProductById(id);

        [Obsolete]
        internal Product GetProduct(TicketType ticketType) => this.company.LotteryGamesPool.GetProduct(ticketType);

        internal string GetSubTicketsByPick(int pick, string strNumbers)
        {
            if (String.IsNullOrWhiteSpace(strNumbers)) throw new ArgumentNullException(nameof(strNumbers));
            var numbers = strNumbers.Split(",", StringSplitOptions.RemoveEmptyEntries);
            StringBuilder result = new StringBuilder();
            switch (pick)
            {
                case 2:
                    Pick2 pick2 = new Pick2(numbers[0], numbers[1]);
                    foreach (var subTicket in pick2.SubTickets())
                    {
                        result.Append(subTicket.AsString());
                    }
                    return result.ToString();
                case 3:
                    Pick3 pick3 = new Pick3(numbers[0], numbers[1], numbers[2]);
                    foreach (var subTicket in pick3.SubTickets())
                    {
                        result.Append(subTicket.AsString());
                    }
                    return result.ToString();
                case 4:
                    Pick4 pick4 = new Pick4(numbers[0], numbers[1], numbers[2], numbers[3]);
                    foreach (var subTicket in pick4.SubTickets())
                    {
                        result.Append(subTicket.AsString());
                    }
                    return result.ToString();
                case 5:
                    Pick5 pick5 = new Pick5(numbers[0], numbers[1], numbers[2], numbers[3], numbers[4]);
                    foreach (var subTicket in pick5.SubTickets())
                    {
                        result.Append(subTicket.AsString());
                    }
                    return result.ToString();
                default:
                    throw new GameEngineException($"There is no {nameof(PresetBetAmounts)} implementation for {nameof(pick)} {pick}");
            }
        }

        

        internal IEnumerable<State> EnabledStatesWithDraws()
        {
            var result = new List<State>();
            foreach (var state in StatesWithLotteries())
            {
                if (state.Enable)
                {
                    result.Add(state);
                }
            }

            return result;
        }

        internal void DeleteState(string abbreviation, DateTime now, string employeeName)//TRIZ correr el caso de prueba asosciado al metodo.
        {
            if (String.IsNullOrWhiteSpace(abbreviation)) throw new ArgumentNullException(nameof(abbreviation));
            if (now == default(DateTime)) throw new GameEngineException("Date cannot have default value");
            if (String.IsNullOrWhiteSpace(employeeName)) throw new ArgumentNullException(nameof(employeeName));

            State foundState = null;
            foreach (State state in base.States())
            {
                if (state.Abbreviation == abbreviation)
                {
                    foundState = state;
                    if (pick2.ContainsKey(state)) pick2[state].DeleteAllCalendarEvents();
                    if (pick3.ContainsKey(state)) pick3[state].DeleteAllCalendarEvents();
                    if (pick4.ContainsKey(state)) pick4[state].DeleteAllCalendarEvents();
                    if (pick5.ContainsKey(state)) pick5[state].DeleteAllCalendarEvents();
                    var log = $"{employeeName} deletes it at {now}<br>";
                    WriteLog(state, log);
                    break;
                }
            }
            if (foundState != null) base.RemoveState(foundState);
        }

        [Obsolete]
        internal bool ContainsAll(Products otherProducts) => this.company.LotteryGamesPool.ContainsAll(otherProducts);

		internal override IEnumerable<Schedule> PendingAndNoRegradedSchedulesAt(DateTime date)
        {
            if (date == default(DateTime)) throw new GameEngineException("Date cannot have default value");

            int dayOfWeek = (int)date.DayOfWeek;
            var schedulesPick2 = pick2.Values.Where(lottery => lottery.IsScheduledAt(dayOfWeek)).
                SelectMany(x => x.PendingAndNoRegradedSchedulesAt(date));
            var schedulesPick3 = pick3.Values.Where(lottery => lottery.IsScheduledAt(dayOfWeek)).
                SelectMany(x => x.PendingAndNoRegradedSchedulesAt(date));
            var schedulesPick4 = pick4.Values.Where(lottery => lottery.IsScheduledAt(dayOfWeek)).
                SelectMany(x => x.PendingAndNoRegradedSchedulesAt(date));
            var schedulesPick5 = pick5.Values.Where(lottery => lottery.IsScheduledAt(dayOfWeek)).
                SelectMany(x => x.PendingAndNoRegradedSchedulesAt(date));
            var schedules = schedulesPick2.Concat(schedulesPick3.Concat(schedulesPick4.Concat(schedulesPick5))).
                OrderBy(schedule => schedule.Hour.Hour).
                ThenBy(schedule => schedule.Hour.Minute);
            return schedules;
        }

        internal IEnumerable<Schedule> PendingAndNoRegradedSchedulesAtForPowerBall(DateTime date)
        {
            if (date == default(DateTime)) throw new GameEngineException("Date cannot have default value");

            int dayOfWeek = (int)date.DayOfWeek;
            var schedules = powerball.IsScheduledAt(dayOfWeek) ? powerball.PendingAndNoRegradedSchedulesAt(date) : new List<Schedule>();
            schedules = schedules.OrderBy(x => x.Hour.Hour).ThenBy(x => x.Hour.Minute).ToList();
            return schedules;
        }

        //internal KenoNextDraw NextPendingAndNoRegradedSchedulesAtForKeno(DateTime purchasedate)
        //{
        //    if (purchasedate == default(DateTime)) throw new GameEngineException("Date cannot have default value");

        //    Schedule schedule;
        //    DateTime drawdate;
        //    if (keno.NextPendingAndNoRegradedSchedulesAt(purchasedate, out schedule, out drawdate)) return new KenoNextDraw(purchasedate, schedule, drawdate);

        //    throw new GameEngineException($"There is no {nameof(Schedule)} for {purchasedate}");
        //}



        internal bool IsAValidDateForKeno(DateTime date)
        {
            return date != default(DateTime);
        }

        internal DateTime CalculateNextPendingAndNoRegradedSchedulesAtForKeno(DateTime purchaseDate)
        {
            if (purchaseDate == default(DateTime)) throw new GameEngineException("Date cannot have default value");
            //Schedule schedule;
            DateTime validDrawDate = keno.NextValidDrawDate(purchaseDate);//, out schedule);

            return validDrawDate;
        }
        internal KenoNextDraw NextPendingAndNoRegradedSchedulesAtForKeno(DateTime purchasedate)
        {
            if (purchasedate == default(DateTime)) throw new GameEngineException("Date cannot have default value");

            KenoNextDraw nextDrawDate;
            if (keno.NextPendingAndNoRegradedSchedulesAt(purchasedate, out nextDrawDate))
            {
                return nextDrawDate;
            }

            throw new GameEngineException($"There is no {nameof(Schedule)} for {purchasedate}");
        }

        internal IEnumerable<Schedule> ListAllSchedulesAt(DateTime date)
        {
            if (date == default(DateTime)) throw new GameEngineException("Date cannot have default value");

            int dayOfWeek = (int)date.DayOfWeek;
            var schedulesPick2 = pick2.Values.Where(lottery => lottery.IsScheduledAt(dayOfWeek)).
                SelectMany(x => x.ListAllSchedulesAt(date));
            var schedulesPick3 = pick3.Values.Where(lottery => lottery.IsScheduledAt(dayOfWeek)).
                SelectMany(x => x.ListAllSchedulesAt(date));
            var schedulesPick4 = pick4.Values.Where(lottery => lottery.IsScheduledAt(dayOfWeek)).
                SelectMany(x => x.ListAllSchedulesAt(date));
            var schedulesPick5 = pick5.Values.Where(lottery => lottery.IsScheduledAt(dayOfWeek)).
                SelectMany(x => x.ListAllSchedulesAt(date));
            var schedules = schedulesPick2.Concat(schedulesPick3.Concat(schedulesPick4.Concat(schedulesPick5))).
                OrderBy(x => x.Hour.Hour).
                ThenBy(x => x.Hour.Minute).ToList();
            return schedules;
        }

        internal override IEnumerable<LotteryComplete> DrawsToBeConfirmed()
        {
            var result =
                pick2.Values.SelectMany(x => x.DrawsToBeConfirmed()).
                Concat(pick3.Values.SelectMany(x => x.DrawsToBeConfirmed())).
                Concat(pick4.Values.SelectMany(x => x.DrawsToBeConfirmed())).
                Concat(pick5.Values.SelectMany(x => x.DrawsToBeConfirmed())).
                OrderBy(x => x.Date).ToList();
            return result;
        }

        internal IEnumerable<LotteryDrawPowerball> DrawsToBeConfirmedForPowerBall()
        {
            var result = powerball.DrawsToBeConfirmed().Cast<LotteryDrawPowerball>().OrderBy(x => x.Date).ToList();
            return result;
        }

        internal IEnumerable<Schedule> DrawnSchedulesOf(DateTime date)
        {
            if (date == default(DateTime)) throw new GameEngineException("Date cannot have default value");

            int dayOfWeek = (int)date.DayOfWeek;
            var schedulesPick2 = pick2.Values.Where(lottery => lottery.IsScheduledAt(dayOfWeek)).
                SelectMany(x => x.DrawnSchedulesAt(date));
            var schedulesPick3 = pick3.Values.Where(lottery => lottery.IsScheduledAt(dayOfWeek)).
                SelectMany(x => x.DrawnSchedulesAt(date));
            var schedulesPick4 = pick4.Values.Where(lottery => lottery.IsScheduledAt(dayOfWeek)).
                SelectMany(x => x.DrawnSchedulesAt(date));
            var schedulesPick5 = pick5.Values.Where(lottery => lottery.IsScheduledAt(dayOfWeek)).
                SelectMany(x => x.DrawnSchedulesAt(date));
            var schedules = schedulesPick2.Concat(schedulesPick3.Concat(schedulesPick4.Concat(schedulesPick5))).
                OrderBy(x => x.Hour.Hour).
                ThenBy(x => x.Hour.Minute).ToList();
            return schedules;
        }

        internal override IEnumerable<Schedule> FinishedAndRegradedSchedulesOf(DateTime date)
        {
            if (date == default(DateTime)) throw new GameEngineException("Date cannot have default value");

            int dayOfWeek = (int)date.DayOfWeek;
            var schedulesPick2 = pick2.Values.Where(lottery => lottery.IsScheduledAt(dayOfWeek)).
                SelectMany(x => x.FinishedAndRegradedSchedulesAt(date));
            var schedulesPick3 = pick3.Values.Where(lottery => lottery.IsScheduledAt(dayOfWeek)).
                SelectMany(x => x.FinishedAndRegradedSchedulesAt(date));
            var schedulesPick4 = pick4.Values.Where(lottery => lottery.IsScheduledAt(dayOfWeek)).
                SelectMany(x => x.FinishedAndRegradedSchedulesAt(date));
            var schedulesPick5 = pick5.Values.Where(lottery => lottery.IsScheduledAt(dayOfWeek)).
                SelectMany(x => x.FinishedAndRegradedSchedulesAt(date));
            var schedules = schedulesPick2.Concat(schedulesPick3.Concat(schedulesPick4.Concat(schedulesPick5))).
                OrderBy(x => x.Hour.Hour).
                ThenBy(x => x.Hour.Minute).ToList();
            return schedules;
        }

        internal IEnumerable<Schedule> FinishedAndRegradedSchedulesOfPowerBall(DateTime date)
        {
            if (date == default(DateTime)) throw new GameEngineException("Date cannot have default value");

            int dayOfWeek = (int)date.DayOfWeek;
            var schedules = powerball.FinishedAndRegradedSchedulesAt(date).
                OrderBy(x => x.Hour.Hour).
                ThenBy(x => x.Hour.Minute).ToList();
            return schedules;
        }
        internal IEnumerable<Schedule> FinishedAndRegradedSchedulesOfKeno(DateTime date)
        {
            if (date == default(DateTime)) throw new GameEngineException("Date cannot have default value");

            int dayOfWeek = (int)date.DayOfWeek;
            var schedules = keno.FinishedAndRegradedSchedulesAt(date).
                OrderBy(x => (x as HourlySchedule).Hour.Hour).
                ThenBy(x => (x as HourlySchedule).Hour.Minute).ToList();
            return schedules;
        }

        internal void RemoveNumbersTracker(int pickNumber, Lottery lottery, DateTime drawDate)
        {
            this.Risks.ForgetNumbersTracker(pickNumber, lottery, drawDate);
        }

        const int MaxUpcomingDrawsToShow = 5;
        const int SecondsRemainingToCloseDrawPurchases = 30;
        TimeSpan IntervalToSearchDrawings = new TimeSpan(24, 0, 0);
        internal IEnumerable<Schedule> UpcomingSchedules(DateTime now, Domain domain, string number, Player player)
        {
            if (now == default(DateTime)) throw new GameEngineException("Date cannot have default value");
            if (domain == null) throw new ArgumentNullException(nameof(domain));
            if (string.IsNullOrWhiteSpace(number)) throw new ArgumentNullException(nameof(number));
            
            var result = new List<Schedule>();
            var subticket = SubTicket<IPick>.SubticketFromNumber(number);
            IReadOnlyList<Schedule> schedules = Calendar.SearchEventsAssuming(now, now.AddSeconds(SecondsRemainingToCloseDrawPurchases), IntervalToSearchDrawings);

            var lotteriesAlreadyForgotten = new List<Lottery>();
            foreach (var schedule in schedules)
            {
                var lottery = schedule.Lottery;
                if (!subticket.BelongTo(lottery.IdOfLottery)) continue;

                var drawDate = schedule.NextValidDraw(now);
                if (drawDate < now.AddSeconds(SecondsRemainingToCloseDrawPurchases)) continue;

                var riskProfile = RiskProfiles.GetRiskProfile(domain);
                var riskPerLottery = riskProfile.Risks.Risk.GetRiskPerLottery(subticket.Length, lottery);
                var ticketType = TicketTypeStraight(subticket.Length);
                var prizeCriteriaId = Disincentives.WayOfSubticket(ticketType, subticket);
                var ruleType = RiskPerLottery.RuleTypeFromTicketType(ticketType);
                var isSubticketExceedingToWin = riskPerLottery.IsSubticketExceedingToWin(schedule, prizeCriteriaId, ruleType, subticket, drawDate, domain);
                var maxAvailableBetAmount = riskPerLottery.MaxAvailableBetAmount(schedule, prizeCriteriaId, ruleType, number, drawDate, domain);
                if (lottery.IsEnabledDraw(drawDate, domain) && !isSubticketExceedingToWin && maxAvailableBetAmount >= DEFAULT_MIN_BET_FOR_RR)
                {
                    if (!lotteriesAlreadyForgotten.Contains(lottery))
                    {
                        riskPerLottery.ForgetPreviousTracking(player);
                        lotteriesAlreadyForgotten.Add(lottery);
                    }
                    riskPerLottery.Track(subticket, drawDate, player);
                    result.Add(schedule);
                }
            
                if (result.Count >= MaxUpcomingDrawsToShow) break;
            }

            return result;
        }

        public static TicketType TicketTypeStraight(int pick)
        {
            switch (pick)
            {
                case 2: 
                    return TicketType.P2S; 
                case 3: 
                    return TicketType.P3S; 
                case 4: 
                    return TicketType.P4S; 
                case 5: 
                    return TicketType.P5S; 
                default: 
                    throw new GameEngineException($"There is no {nameof(TicketType)} for {nameof(pick)} {pick}"); 
            }
        }

        internal override IEnumerable<Schedule> SchedulesByState(DateTime date, Domain domain)
        {
            if (date == default(DateTime)) throw new GameEngineException("Date cannot have default value");
            if (domain == null) throw new ArgumentNullException(nameof(domain));

            var schedules = new SortedList<ScheduleKeySortedByState, Schedule>();
            DateTime nextDate;
            foreach (var lottery in pick2.Values)
            {
                foreach (var schedule in lottery.AllEnabledSchedules(date, domain))
                {
                    nextDate = lottery.NextValidDrawDate(schedule, date);
                    var scheduleKey = new ScheduleKeySortedByState(nextDate, lottery.State.Abbreviation, lottery.GameType());
                    schedules.Add(scheduleKey, schedule);
                }
            }
            foreach (var lottery in pick3.Values)
            {
                foreach (var schedule in lottery.AllEnabledSchedules(date, domain))
                {
                    nextDate = lottery.NextValidDrawDate(schedule, date);
                    var scheduleKey = new ScheduleKeySortedByState(nextDate, lottery.State.Abbreviation, lottery.GameType());
                    schedules.Add(scheduleKey, schedule);
                }
            }
            foreach (var lottery in pick4.Values)
            {
                foreach (var schedule in lottery.AllEnabledSchedules(date, domain))
                {
                    nextDate = lottery.NextValidDrawDate(schedule, date);
                    var scheduleKey = new ScheduleKeySortedByState(nextDate, lottery.State.Abbreviation, lottery.GameType());
                    schedules.Add(scheduleKey, schedule);
                }
            }
            foreach (var lottery in pick5.Values)
            {
                foreach (var schedule in lottery.AllEnabledSchedules(date, domain))
                {
                    nextDate = lottery.NextValidDrawDate(schedule, date);
                    var scheduleKey = new ScheduleKeySortedByState(nextDate, lottery.State.Abbreviation, lottery.GameType());
                    schedules.Add(scheduleKey, schedule);
                }
            }

            var result = schedules.Values.ToList();
            return result;
        }

        internal override IEnumerable<Schedule> SchedulesBy(State state)
        {
            if (state == null) throw new ArgumentNullException(nameof(state));

            var schedulesPick2 = pick2.Where(x => x.Key == state).SelectMany(x => x.Value.Schedules);
            var schedulesPick3 = pick3.Where(x => x.Key == state).SelectMany(x => x.Value.Schedules);
            var schedulesPick4 = pick4.Where(x => x.Key == state).SelectMany(x => x.Value.Schedules);
            var schedulesPick5 = pick5.Where(x => x.Key == state).SelectMany(x => x.Value.Schedules);
            var result = schedulesPick2.Concat(schedulesPick3.Concat(schedulesPick4.Concat(schedulesPick5))).ToList();
            return result;
        }

        internal IEnumerable<Schedule> SchedulesForPowerBall()
        {
            return powerball.Schedules;
        }

        internal IEnumerable<ScheduleWithGameType> ScheduleWithGameTypesBy(State state)
        {
            if (state == null) throw new ArgumentNullException(nameof(state));

            var schedulesPick2 = pick2.Where(x => x.Key == state).SelectMany(x => x.Value.Schedules);
            var schedulesPick3 = pick3.Where(x => x.Key == state).SelectMany(x => x.Value.Schedules);
            var schedulesPick4 = pick4.Where(x => x.Key == state).SelectMany(x => x.Value.Schedules);
            var schedulesPick5 = pick5.Where(x => x.Key == state).SelectMany(x => x.Value.Schedules);
            var result = schedulesPick2.Concat(schedulesPick3.Concat(schedulesPick4.Concat(schedulesPick5))).GroupBy(x => x.AsString()).
                Select(x => new ScheduleWithGameType()
                {
                    Hour = x.Key,
                    GameTypes = x.Select(y => y.Lottery.GameType()).ToList()
                }).ToList();
            return result;
        }

        internal override IEnumerable<State> StatesWithLotteries()
        {
            var result = pick2.Keys.Concat(pick3.Keys.Concat(pick4.Keys.Concat(pick5.Keys))).Distinct().OrderBy(x => x.Abbreviation).ToList();
            return result;
        }

        internal bool IsStateAssignedToLottery(string abbreviation)
        {
            if (String.IsNullOrWhiteSpace(abbreviation)) throw new ArgumentNullException(nameof(abbreviation));
            foreach (State state in StatesWithLotteries())
            {
                if (state.Abbreviation == abbreviation) return true;
            }
            return false;
        }

        internal void DisableState(string abbreviation, DateTime now, string employeeName)
        {
            if (String.IsNullOrWhiteSpace(abbreviation)) throw new ArgumentNullException(nameof(abbreviation));
            if (now == default(DateTime)) throw new GameEngineException("Date cannot have default value");
            if (String.IsNullOrWhiteSpace(employeeName)) throw new ArgumentNullException(nameof(employeeName));

            foreach (State state in StatesWithLotteries())
            {
                if (state.Abbreviation == abbreviation)
                {
                    state.DisableState();
                    if (pick2.ContainsKey(state)) pick2[state].DeleteAllCalendarEvents();
                    if (pick3.ContainsKey(state)) pick3[state].DeleteAllCalendarEvents();
                    if (pick4.ContainsKey(state)) pick4[state].DeleteAllCalendarEvents();
                    if (pick5.ContainsKey(state)) pick5[state].DeleteAllCalendarEvents();
                    var log = $"{employeeName} disables it at {now}<br>";
                    WriteLog(state, log);
                    break;
                }
            }
        }

        internal void EnableState(string abbreviation, DateTime now, string employeeName)
        {
            if (String.IsNullOrWhiteSpace(abbreviation)) throw new ArgumentNullException(nameof(abbreviation));
            if (now == default(DateTime)) throw new GameEngineException("Date cannot have default value");
            if (String.IsNullOrWhiteSpace(employeeName)) throw new ArgumentNullException(nameof(employeeName));

            foreach (State state in States())
            {
                if (state.Abbreviation == abbreviation)
                {
                    state.EnableState();
                    if (pick2.ContainsKey(state)) pick2[state].AddAllCalendarEvents();
                    if (pick3.ContainsKey(state)) pick3[state].AddAllCalendarEvents();
                    if (pick4.ContainsKey(state)) pick4[state].AddAllCalendarEvents();
                    if (pick5.ContainsKey(state)) pick5[state].AddAllCalendarEvents();
                    var log = $"{employeeName} enables it at {now}<br>";
                    WriteLog(state, log);
                    break;
                }
            }
        }

        internal bool ContainsPick2(Lottery lottery)
        {
            if (lottery == null) throw new ArgumentNullException(nameof(lottery));

            var result = pick2.Values.Contains(lottery);
            return result;
        }

        internal bool ContainsPick3(Lottery lottery)
        {
            if (lottery == null) throw new ArgumentNullException(nameof(lottery));

            var result = pick3.Values.Contains(lottery);
            return result;
        }

        internal bool ContainsPick4(Lottery lottery)
        {
            if (lottery == null) throw new ArgumentNullException(nameof(lottery));

            var result = pick4.Values.Contains(lottery);
            return result;
        }

        internal bool ContainsPick5(Lottery lottery)
        {
            if (lottery == null) throw new ArgumentNullException(nameof(lottery));

            var result = pick5.Values.Contains(lottery);
            return result;
        }

        internal void CancelLottery(Lottery lottery)
        {
            if (lottery == null) throw new ArgumentNullException(nameof(lottery));

            if (lottery is LotteryPick<Pick2> && pick2.Values.Contains(lottery))
            {
                pick2.Remove(lottery.State);
            }
            else if (lottery is LotteryPick<Pick3> && pick3.Values.Contains(lottery))
            {
                pick3.Remove(lottery.State);
            }
            else if (lottery is LotteryPick<Pick4> && pick4.Values.Contains(lottery))
            {
                pick4.Remove(lottery.State);
            }
            else if (lottery is LotteryPick<Pick5> && pick5.Values.Contains(lottery))
            {
                pick5.Remove(lottery.State);
            }
            else
            {
                throw new GameEngineException($"Lottery does not exists in collection {nameof(pick3)}");
            }
        }

        internal IEnumerable<decimal> BetAmountsOfPresetByPick(int pick, Player player, Domain domain)
        {
            if (player == null) throw new ArgumentNullException(nameof(player));
            if (domain == null) throw new ArgumentNullException(nameof(domain));
            if (!pickValues.Contains(pick)) throw new GameEngineException($"{nameof(pick)} {pick} is not valid");

            var minAmount = MinAmountOfBetByPick(pick, player, domain);
            var maxAmount = MaxAmountOfBetByPick(pick, player, domain);
            switch (pick)
            {
                case 2:
                    var result = presetBetAmountsPick2.EnabledBetAmounts(minAmount, maxAmount);
                    return result;
                case 3:
                    result = presetBetAmountsPick3.EnabledBetAmounts(minAmount, maxAmount);
                    return result;
                case 4:
                    result = presetBetAmountsPick4.EnabledBetAmounts(minAmount, maxAmount);
                    return result;
                case 5:
                    result = presetBetAmountsPick5.EnabledBetAmounts(minAmount, maxAmount);
                    return result;
                default:
                    throw new GameEngineException($"There is no {nameof(PresetBetAmounts)} implementation for {nameof(pick)} {pick}");
            }
        }

        internal decimal MinAmountOfBetForPowerball(Player player, Domain domain)
        {
            if (player == null) throw new ArgumentNullException(nameof(player));
            if (domain == null) throw new ArgumentNullException(nameof(domain));

            var hasProfile = playersProfiles.HasBetRangePlayerProfileForPowerball(player, domain);
            var hasDomainForBetRange = betRangesPowerball.Exists(domain);
            var result = hasProfile ? playersProfiles.MinAmountForPowerball(player, domain) :
                hasDomainForBetRange ? betRangesPowerball.MinAmount(domain) : DEFAULT_MIN_BET_FOR_POWERBALL;
            return result;
        }

        internal decimal MaxAmountOfBetForPowerball(Player player, Domain domain)
        {
            if (player == null) throw new ArgumentNullException(nameof(player));
            if (domain == null) throw new ArgumentNullException(nameof(domain));

            var hasProfile = playersProfiles.HasBetRangePlayerProfileForPowerball(player, domain);
            var hasDomainForBetRange = betRangesPowerball.Exists(domain);
            var result = hasProfile ? playersProfiles.MaxAmountForPowerball(player, domain) :
                hasDomainForBetRange ? betRangesPowerball.MaxAmount(domain) : DEFAULT_MAX_BET_FOR_POWERBALL;
            return result;
        }

        internal IEnumerable<decimal> BetAmountsOfPresetForPowerball(Player player, Domain domain)
        {
            if (player == null) throw new ArgumentNullException(nameof(player));
            if (domain == null) throw new ArgumentNullException(nameof(domain));

            var minAmount = MinAmountOfBetForPowerball(player, domain);
            var maxAmount = MaxAmountOfBetForPowerball(player, domain);

            var result = presetBetAmountsPowerball.EnabledBetAmounts(minAmount, maxAmount);
            return result;
        }

        internal IEnumerable<decimal> BetAmountsOfPresetForKeno(Player player, Domain domain)
        {
            if (player == null) throw new ArgumentNullException(nameof(player));
            if (domain == null) throw new ArgumentNullException(nameof(domain));

            var minAmount = MinAmountOfBetForKeno(player, domain);
            var maxAmount = MaxAmountOfBetForKeno(player, domain);

            var result = presetBetAmountsKeno.EnabledBetAmounts(minAmount, maxAmount);
            return result;
        }

        internal decimal MinAmountOfBetForKeno(Player player, Domain domain)
        {
            if (player == null) throw new ArgumentNullException(nameof(player));
            if (domain == null) throw new ArgumentNullException(nameof(domain));

            var hasProfile = PlayersProfiles.HasBetRangePlayerProfileForKeno(player, domain);
            var hasDomainForBetRange = betRangesKeno.Exists(domain);
            var result = hasProfile ? PlayersProfiles.MinAmountForKeno(player, domain) :
                hasDomainForBetRange ? betRangesKeno.MinAmount(domain) : DEFAULT_MIN_BET_FOR_KENO;
            return result;
        }

        internal decimal MaxAmountOfBetForKeno(Player player, Domain domain)
        {
            if (player == null) throw new ArgumentNullException(nameof(player));
            if (domain == null) throw new ArgumentNullException(nameof(domain));

            var hasProfile = playersProfiles.HasBetRangePlayerProfileForKeno(player, domain);
            var hasDomainForBetRange = betRangesKeno.Exists(domain);
            var result = hasProfile ? playersProfiles.MaxAmountForKeno(player, domain) :
                hasDomainForBetRange ? betRangesKeno.MaxAmount(domain) : DEFAULT_MAX_BET_FOR_KENO;
            return result;
        }

        internal void IsBetAmountValidFor(int pick, Player player, Domain domain, decimal betAmount)
        {
            if (player == null) throw new ArgumentNullException(nameof(player));
            if (domain == null) throw new ArgumentNullException(nameof(domain));
            if (!pickValues.Contains(pick)) throw new GameEngineException($"{nameof(pick)} {pick} is not valid");

            var minAmount = MinAmountOfBetByPick(pick, player, domain);
            var maxAmount = MaxAmountOfBetByPick(pick, player, domain);
            if (betAmount > maxAmount) throw new GameEngineException($"{nameof(player)} '{player.AccountNumber}' in {nameof(domain)} {domain.Url} {nameof(betAmount)} {betAmount} exceeded maximum {maxAmount}");
            if (betAmount < minAmount) throw new GameEngineException($"{nameof(player)} '{player.AccountNumber}' in {nameof(domain)} {domain.Url} {nameof(betAmount)} {betAmount} exceeded minimum {minAmount}");
        }

        internal decimal MinAmountOfBetByPick(int pick, Player player, Domain domain)
        {
            if (player == null) throw new ArgumentNullException(nameof(player));
            if (domain == null) throw new ArgumentNullException(nameof(domain));
            if (!pickValues.Contains(pick)) throw new GameEngineException($"{nameof(pick)} {pick} is not valid");
            switch (pick)
            {
                case 2:
                    var hasProfile = playersProfiles.HasBetRangePlayerProfileForPick(pick, player, domain);
                    var hasDomainForBetRange = betRangesPick2.Exists(domain);
                    var result = hasProfile ? playersProfiles.MinAmountForPick(pick, player, domain) :
                        hasDomainForBetRange ? betRangesPick2.MinAmount(domain) : DEFAULT_MIN_BET_FOR_PICK2;
                    return result;
                case 3:
                    hasProfile = playersProfiles.HasBetRangePlayerProfileForPick(pick, player, domain);
                    hasDomainForBetRange = betRangesPick3.Exists(domain);
                    result = hasProfile ? playersProfiles.MinAmountForPick(pick, player, domain) :
                        hasDomainForBetRange ? betRangesPick3.MinAmount(domain) : DEFAULT_MIN_BET_FOR_PICK3;
                    return result;
                case 4:
                    hasProfile = playersProfiles.HasBetRangePlayerProfileForPick(pick, player, domain);
                    hasDomainForBetRange = betRangesPick4.Exists(domain);
                    result = hasProfile ? playersProfiles.MinAmountForPick(pick, player, domain) :
                        hasDomainForBetRange ? betRangesPick4.MinAmount(domain) : DEFAULT_MIN_BET_FOR_PICK4;
                    return result;
                case 5:
                    hasProfile = playersProfiles.HasBetRangePlayerProfileForPick(pick, player, domain);
                    hasDomainForBetRange = betRangesPick5.Exists(domain);
                    result = hasProfile ? playersProfiles.MinAmountForPick(pick, player, domain) :
                        hasDomainForBetRange ? betRangesPick5.MinAmount(domain) : DEFAULT_MIN_BET_FOR_PICK5;
                    return result;
                default:
                    throw new GameEngineException($"There is no {nameof(PresetBetAmounts)} implementation for {nameof(pick)} {pick}");
            }
        }

        internal decimal MaxAmountOfBetByPick(int pick, Player player, Domain domain)
        {
            if (player == null) throw new ArgumentNullException(nameof(player));
            if (domain == null) throw new ArgumentNullException(nameof(domain));
            if (!pickValues.Contains(pick)) throw new GameEngineException($"{nameof(pick)} {pick} is not valid");
            switch (pick)
            {
                case 2:
                    if (!pickValues.Contains(pick)) throw new GameEngineException($"{nameof(pick)} {pick} is not valid");
                    var hasProfile = playersProfiles.HasBetRangePlayerProfileForPick(pick, player, domain);
                    var hasDomainForBetRange = betRangesPick2.Exists(domain);
                    var result = hasProfile ? playersProfiles.MaxAmountForPick(pick, player, domain) :
                        hasDomainForBetRange ? betRangesPick2.MaxAmount(domain) : DEFAULT_MAX_BET_FOR_PICK2;
                    return result;
                case 3:
                    if (!pickValues.Contains(pick)) throw new GameEngineException($"{nameof(pick)} {pick} is not valid");
                    hasProfile = playersProfiles.HasBetRangePlayerProfileForPick(pick, player, domain);
                    hasDomainForBetRange = betRangesPick3.Exists(domain);
                    result = hasProfile ? playersProfiles.MaxAmountForPick(pick, player, domain) :
                        hasDomainForBetRange ? betRangesPick3.MaxAmount(domain) : DEFAULT_MAX_BET_FOR_PICK3;
                    return result;
                case 4:
                    hasProfile = playersProfiles.HasBetRangePlayerProfileForPick(pick, player, domain);
                    hasDomainForBetRange = betRangesPick4.Exists(domain);
                    result = hasProfile ? playersProfiles.MaxAmountForPick(pick, player, domain) :
                        hasDomainForBetRange ? betRangesPick4.MaxAmount(domain) : DEFAULT_MAX_BET_FOR_PICK4;
                    return result;
                case 5:
                    hasProfile = playersProfiles.HasBetRangePlayerProfileForPick(pick, player, domain);
                    hasDomainForBetRange = betRangesPick5.Exists(domain);
                    result = hasProfile ? playersProfiles.MaxAmountForPick(pick, player, domain) :
                        hasDomainForBetRange ? betRangesPick5.MaxAmount(domain) : DEFAULT_MAX_BET_FOR_PICK5;
                    return result;
                default:
                    throw new GameEngineException($"There is no {nameof(PresetBetAmounts)} implementation for {nameof(pick)} {pick}");
            }
        }

        internal void IsBetAmountValidForPowerball(Player player, Domain domain, decimal betAmount)
        {
            if (player == null) throw new ArgumentNullException(nameof(player));
            if (domain == null) throw new ArgumentNullException(nameof(domain));

            var minAmount = MinAmountOfBetForPowerball(player, domain);
            var maxAmount = MaxAmountOfBetForPowerball(player, domain);
            if (betAmount > maxAmount) throw new GameEngineException($"{nameof(player)} '{player.AccountNumber}' in {nameof(domain)} {domain.Url} {nameof(betAmount)} {betAmount} exceeded maximum {maxAmount}");
            if (betAmount < minAmount) throw new GameEngineException($"{nameof(player)} '{player.AccountNumber}' in {nameof(domain)} {domain.Url} {nameof(betAmount)} {betAmount} exceeded minimum {minAmount}");
        }

        internal void IsBetAmountValidForKeno(Player player, Domain domain, decimal betAmount)
        {
            if (player == null) throw new ArgumentNullException(nameof(player));
            if (domain == null) throw new ArgumentNullException(nameof(domain));

            var minAmount = MinAmountOfBetForKeno(player, domain);
            var maxAmount = MaxAmountOfBetForKeno(player, domain);
            if (betAmount > maxAmount) throw new GameEngineException($"{nameof(player)} '{player.AccountNumber}' in {nameof(domain)} {domain.Url} {nameof(betAmount)} {betAmount} exceeded maximum {maxAmount}");
            if (betAmount < minAmount) throw new GameEngineException($"{nameof(player)} '{player.AccountNumber}' in {nameof(domain)} {domain.Url} {nameof(betAmount)} {betAmount} exceeded minimum {minAmount}");
        }

        internal QuickPickGenerator GenerateQuickPick(Player player, int pick, int number)
        {
            if (player == null) throw new ArgumentNullException(nameof(player));

            var quickPickGenerator = new QuickPickGenerator(pick2, pick3, pick4, pick5, player, pick, number);
            return quickPickGenerator;
        }

        internal RandomQuickPowerballGenerator GenerateRandomQuickPowerball(List<string> currentNumbers, int quickNumber)
        {
            if (quickNumber < 0) throw new GameEngineException($"{nameof(quickNumber)} {quickNumber} must be greater or equal than 0");

            var quickGenerator = new RandomQuickPowerballGenerator(currentNumbers, quickNumber);
            return quickGenerator;
        }

        internal FavoriteQuickPowerballGenerator GenerateFavoriteQuickPowerball(Player player, List<string> currentNumbers, int quickNumber)
        {
            if (player == null) throw new ArgumentNullException(nameof(player));
            if (quickNumber < 0) throw new GameEngineException($"{nameof(quickNumber)} {quickNumber} must be greater or equal than 0");

            var favorites = player.FavoritesCatalog();
            var quickGenerator = new FavoriteQuickPowerballGenerator(favorites, currentNumbers, quickNumber);
            return quickGenerator;
        }

        internal override bool HasDrawsFor(State state)
        {
            if (pick2.ContainsKey(state)) return true;
            if (pick3.ContainsKey(state)) return true;
            if (pick4.ContainsKey(state)) return true;
            return pick5.ContainsKey(state);
        }

        internal Ticket CastGameboardToTicketFrom(Bet bet)
        {
            if (bet == null) throw new ArgumentNullException(nameof(bet));
            if (!(bet.Gameboard is Ticket)) throw new GameEngineException($"{nameof(bet.Gameboard)} is not a {nameof(Ticket)}");
            var ticket = (Ticket)bet.Gameboard;
            return ticket;
        }

		internal IEnumerable<TicketWager> TakeWagersBetweenBetIds(int theLowestBetId, int theHighestBetId, bool isMultipleAuthorization)
		{
			if (theLowestBetId <= 0) throw new GameEngineException($"{nameof(theLowestBetId)} must be greater than 0");
			if (theHighestBetId <= 0) throw new GameEngineException($"{nameof(theHighestBetId)} must be greater than 0");

            IEnumerable<Ticket> tickets;
            if (isMultipleAuthorization)
            {
                tickets = FindTicketsInRange(theLowestBetId, theHighestBetId);
            }
            else
            {
                var bets = company.FindBetsInRange(theLowestBetId, theHighestBetId);
                tickets = bets.Where(x => x.Gameboard is Ticket).
                Select(bet => bet.Gameboard).
                Cast<Ticket>();
            }

            var wagers = tickets.SelectMany(x => x.Wagers).ToList();
			return wagers;
		}

		public static Dictionary<int, int> correctos = new Dictionary<int, int>();

		internal void CreateWagers(bool itIsThePresent, int lowBetId, int theHighestBetId, int lowWagerNumber, int amountOfWagerNumbers, int ticketNumber, int orderNumber, DateTime now)
		{
			if (lowBetId <= 0) throw new GameEngineException($"{nameof(lowBetId)} must be greater than 0");
			if (theHighestBetId <= 0) throw new GameEngineException($"{nameof(theHighestBetId)} must be greater than 0");
			if (lowWagerNumber <= 0) throw new GameEngineException($"{nameof(lowWagerNumber)} must be greater than 0");
			if (amountOfWagerNumbers <= 0) throw new GameEngineException($"{nameof(amountOfWagerNumbers)} must be greater than 0");
			if (orderNumber <= 0) throw new GameEngineException($"{nameof(orderNumber)} must be greater than 0");
            
            var isMultipleAuthorization = ticketNumber == -1;
            if (isMultipleAuthorization) return;
            var wagers = TakeWagersBetweenBetIds(lowBetId, theHighestBetId, isMultipleAuthorization);
			var countOfWagerNumbers = amountOfWagerNumbers - lowWagerNumber + 1;
			var wagerNumbers = Enumerable.Range(lowWagerNumber, countOfWagerNumbers);
			//if (wagerNumbers.Count() != wagers.Count()) throw new GameEngineException($"{nameof(wagerNumbers)} has different number of elements than {nameof(wagers)}"); //TODO: uncomment
			var areThereMismatchCountingWagerNumbers = wagerNumbers.Count() != wagers.Count();
			if (areThereMismatchCountingWagerNumbers)
			{
				if (accountingPendingWagers == null) accountingPendingWagers = new List<AccountingPendingWager>();
				int initialPendingWagerNumber = wagers.Count() + 1;
				accountingPendingWagers.Add(new AccountingPendingWager(ticketNumber, initialPendingWagerNumber, countOfWagerNumbers, orderNumber, now));
			}

            var order = company.OrderByNumber(orderNumber);
            if (order != null)
            {
                HashSet<Tuple<Lottery, DateTime>> lotteriesWithDateGraded = null;
                HashSet<Tuple<Lottery, DateTime>> lotteriesWithDateNoAction = null;
                using (var e1 = wagers.GetEnumerator())
                using (var e2 = wagerNumbers.GetEnumerator())
                {
                    while (e1.MoveNext() && e2.MoveNext())
                    {
                        var wager = e1.Current;
                        var wagerNumber = e2.Current;
                        var betNumberAndWagerPosition = wager.ReferenceNumber.Split("-");
                        int betNumber = int.Parse(betNumberAndWagerPosition[0]);
                        int wagerConsecutive = int.Parse(betNumberAndWagerPosition[1]);

                        var bet = company.FindBetById(betNumber);

                        var ticket = CastGameboardToTicketFrom(bet);
                        ticket.CountAccountingWagers = countOfWagerNumbers;
                        if (areThereMismatchCountingWagerNumbers)
                        {
                            if (ticketsMismatchingWithWagerNumbers == null) ticketsMismatchingWithWagerNumbers = new List<Ticket>();
                            ticketsMismatchingWithWagerNumbers.Add(ticket);
                        }

                        var isOnlyOneGradedTicketWithoutNumber = ticket.Lottery.IsOnlyOneGradedTicketWithoutNumber(ticket.DrawDate);
                        var isOnlyOneNoActionTicketWithoutNumber = ticket.Lottery.IsOnlyOneNoActionTicketWithoutNumber(ticket.DrawDate);

                        bet.AuthorizationId = ticketNumber;
                        ticket.TicketNumber = ticketNumber;
                        ticket.UpdateWagerNumber(wagerConsecutive, wagerNumber);
                        if (isOnlyOneGradedTicketWithoutNumber)
                        {
                            if (lotteriesWithDateGraded == null)
                            {
                                lotteriesWithDateGraded = new HashSet<Tuple<Lottery, DateTime>>();
                            }
                            lotteriesWithDateGraded.Add(Tuple.Create(ticket.Lottery, ticket.DrawDate));
                        }

                        if (isOnlyOneNoActionTicketWithoutNumber)
                        {
                            if (lotteriesWithDateNoAction == null)
                            {
                                lotteriesWithDateNoAction = new HashSet<Tuple<Lottery, DateTime>>();
                            }
                            lotteriesWithDateNoAction.Add(Tuple.Create(ticket.Lottery, ticket.DrawDate));
                        }
                    }
                }

                const string employeeName = "";

                if (lotteriesWithDateGraded != null)
                {
                    SendToHistoricalLateGradedTickets(lotteriesWithDateGraded, itIsThePresent, now, employeeName);
                }
                if (lotteriesWithDateNoAction != null)
                {
                    SendToHistoricalLateNoActionTickets(lotteriesWithDateNoAction, itIsThePresent, now, employeeName);
                }
            }

			UpdateOrder(order, ticketNumber);
		}

		private void UpdateOrder(Order order, int ticketNumber)
        {
            if (order != null) order.AuthorizationId = ticketNumber;
        }

        internal bool AreAllPendingTickets(int ticketNumber)
        {
            if (ticketNumber <= 0) throw new GameEngineException($"{nameof(ticketNumber)} {ticketNumber} must be greater than 0");

            var result = pick2.Values.Any(lottery => lottery.AreAllPendingTickets(ticketNumber));
            if (result) return true;
            result = pick3.Values.Any(lottery => lottery.AreAllPendingTickets(ticketNumber));
            if (result) return true;
            result = pick4.Values.Any(lottery => lottery.AreAllPendingTickets(ticketNumber));
            if (result) return true;
            result = pick5.Values.Any(lottery => lottery.AreAllPendingTickets(ticketNumber));
            return result;
        }

        internal bool AnyTicketWithNumber(int ticketNumber)
        {
            if (ticketNumber <= 0) throw new GameEngineException($"{nameof(ticketNumber)} {ticketNumber} must be greater than 0");

            var result = pick2.Values.Any(lottery => lottery.AnyTicketWithNumber(ticketNumber));
            if (result) return true;
            result = pick3.Values.Any(lottery => lottery.AnyTicketWithNumber(ticketNumber));
            if (result) return true;
            result = pick4.Values.Any(lottery => lottery.AnyTicketWithNumber(ticketNumber));
            if (result) return true;
            result = pick5.Values.Any(lottery => lottery.AnyTicketWithNumber(ticketNumber));
            return result;
        }

        internal TicketsFinder SearchTickets(int ticketNumber)
        {
            var ticketsFound = new TicketsFinder();
            ticketsFound.FindAndLoad(ticketNumber, pick2.Values, pick3.Values, pick4.Values, pick5.Values);
            return ticketsFound;
        }

        internal string MakeDescriptionFromPurchase(int ticketNumber)
        {
            var ticketsFound = SearchTickets(ticketNumber);
            if (ticketsFound.IsEmpty()) return "Description not available";

            return ticketsFound.GenerateDescription();
        }

        internal IEnumerable<TicketByPrize> GetPendingTicketsByPrize(int ticketNumber)
        {
            var ticketsFound = SearchTickets(ticketNumber);
            var result = ticketsFound.GetPendingTicketsByPrize();
            return result.ToList();
        }

        internal bool RemoveTickets(bool itIsThePresent, int ticketNumber, DateTime now, string employeeName)
        {
            if (ticketNumber <= 0) throw new GameEngineException($"{nameof(ticketNumber)} {ticketNumber} must be greater than 0");
            if (string.IsNullOrWhiteSpace(employeeName)) throw new ArgumentNullException(nameof(employeeName));
            if (now == default(DateTime)) throw new GameEngineException("Date cannot have default value");

            var ticketsFound = SearchTickets(ticketNumber);
            if (ticketsFound.IsEmpty()) throw new GameEngineException($"No tickets with {nameof(ticketNumber)} {ticketNumber}");
            if (!ticketsFound.AreAllPending()) throw new GameEngineException($"Some tickets with {nameof(ticketNumber)} {ticketNumber} are not pending");
            
            if (ticketsFound.BelongToGameType("LotteryPick3"))
            {
                var lotteriesToRemove = pick3.Values.SelectMany(x => x.LotteriesToRemoveTicketsFor(ticketNumber));
                foreach (var lotteryToRemove in lotteriesToRemove)
                {
                    lotteryToRemove.RemoveTickets(itIsThePresent, ticketNumber, now, employeeName);
                }
                return true;
            }
            if (ticketsFound.BelongToGameType("LotteryPick4"))
            {
                var lotteriesToRemove = pick4.Values.SelectMany(x => x.LotteriesToRemoveTicketsFor(ticketNumber));
                foreach (var lotteryToRemove in lotteriesToRemove)
                {
                    lotteryToRemove.RemoveTickets(itIsThePresent, ticketNumber, now, employeeName);
                }
                return true;
            }
            if (ticketsFound.BelongToGameType("LotteryPick2"))
            {
                var lotteriesToRemove = pick2.Values.SelectMany(x => x.LotteriesToRemoveTicketsFor(ticketNumber));
                foreach (var lotteryToRemove in lotteriesToRemove)
                {
                    lotteryToRemove.RemoveTickets(itIsThePresent, ticketNumber, now, employeeName);
                }
                return true;
            }
            if (ticketsFound.BelongToGameType("LotteryPick5"))
            {
                var lotteriesToRemove = pick5.Values.SelectMany(x => x.LotteriesToRemoveTicketsFor(ticketNumber));
                foreach (var lotteryToRemove in lotteriesToRemove)
                {
                    lotteryToRemove.RemoveTickets(itIsThePresent, ticketNumber, now, employeeName);
                }
                return true;
            }
            return false;
        }

        internal bool IsTicketAsNoActionFor(string subticketNumber)
        {
            if (String.IsNullOrWhiteSpace(subticketNumber)) throw new ArgumentNullException(nameof(subticketNumber));

            var ticketNumberWithWagerNumber = subticketNumber.Split("-");
            var ticketNumber = int.Parse(ticketNumberWithWagerNumber[0]);
            var wagerNumber = int.Parse(ticketNumberWithWagerNumber[1]);
            var isTicketAsNoAction = pick2.Values.Any(x => x.IsTicketAsNoActionFor(ticketNumber, wagerNumber));
            if (isTicketAsNoAction) return true;
            isTicketAsNoAction = pick3.Values.Any(x => x.IsTicketAsNoActionFor(ticketNumber, wagerNumber));
            if (isTicketAsNoAction) return true;
            isTicketAsNoAction = pick4.Values.Any(x => x.IsTicketAsNoActionFor(ticketNumber, wagerNumber));
            if (isTicketAsNoAction) return true;
            isTicketAsNoAction = pick5.Values.Any(x => x.IsTicketAsNoActionFor(ticketNumber, wagerNumber));
            if (isTicketAsNoAction) return true;
            return false;
        }

        internal bool RemoveSubticket(bool itIsThePresent, string subticketNumber, DateTime now, string employeeName)
        {
            if (String.IsNullOrWhiteSpace(subticketNumber)) throw new ArgumentNullException(nameof(subticketNumber));
            if (String.IsNullOrWhiteSpace(employeeName)) throw new ArgumentNullException(nameof(employeeName));
            if (now == default(DateTime)) throw new GameEngineException("Date cannot have default value");

            var ticketNumberWithWagerNumber = subticketNumber.Split("-");
            var ticketNumber = int.Parse(ticketNumberWithWagerNumber[0]);
            var wagerNumber = int.Parse(ticketNumberWithWagerNumber[1]);

            var wagerToRemove = pick2.Values.Select(x => x.WagerToRemoveFor(ticketNumber, wagerNumber)).FirstOrDefault(x => x != null);
            if (wagerToRemove != null)
            {
                wagerToRemove.Ticket.Lottery.RemoveSubticket(itIsThePresent, wagerToRemove, now, employeeName);
                return true;
            }

            wagerToRemove = pick3.Values.Select(x => x.WagerToRemoveFor(ticketNumber, wagerNumber)).FirstOrDefault(x => x != null);
            if (wagerToRemove != null)
            {
                wagerToRemove.Ticket.Lottery.RemoveSubticket(itIsThePresent, wagerToRemove, now, employeeName);
                return true;
            }

            wagerToRemove = pick4.Values.Select(x => x.WagerToRemoveFor(ticketNumber, wagerNumber)).FirstOrDefault(x => x != null);
            if (wagerToRemove != null)
            {
                wagerToRemove.Ticket.Lottery.RemoveSubticket(itIsThePresent, wagerToRemove, now, employeeName);
                return true;
            }

            wagerToRemove = pick5.Values.Select(x => x.WagerToRemoveFor(ticketNumber, wagerNumber)).FirstOrDefault(x => x != null);
            if (wagerToRemove != null)
            {
                wagerToRemove.Ticket.Lottery.RemoveSubticket(itIsThePresent, wagerToRemove, now, employeeName);
                return true;
            }
            return false;
        }

        private void SendToHistoricalLateGradedTickets(HashSet<Tuple<Lottery, DateTime>> lotteriesWithDateGraded, bool itIsThePresent, DateTime now, string employeeName)
        {
            foreach (var lotteryWithDate in lotteriesWithDateGraded)
            {
                lotteryWithDate.Item1.SendToHistoricalLateGradedTickets(itIsThePresent, lotteryWithDate.Item2, now, employeeName);
            }
        }

        private void SendToHistoricalLateNoActionTickets(HashSet<Tuple<Lottery, DateTime>> lotteriesWithDateNoAction, bool itIsThePresent, DateTime now, string employeeName)
        {
            foreach (var lotteryWithDate in lotteriesWithDateNoAction)
            {
                lotteryWithDate.Item1.SendToHistoricalLateNoActionTickets(itIsThePresent, lotteryWithDate.Item2, now, employeeName);
            }
        }

        internal IEnumerable<LotteryDraw> DrawnLotteriesBetween(string strDates)
        {
            if (String.IsNullOrWhiteSpace(strDates)) throw new ArgumentNullException(nameof(strDates));

            var dates = DatesFrom(strDates);
            var winnerNumbers = pick2.Values.SelectMany(x => x.DrawnLotteriesBetween(dates)).
                Concat(pick3.Values.SelectMany(x => x.DrawnLotteriesBetween(dates)).
                Concat(pick4.Values.SelectMany(x => x.DrawnLotteriesBetween(dates)).
                Concat(pick5.Values.SelectMany(x => x.DrawnLotteriesBetween(dates)).
                Concat(powerball.DrawnLotteriesBetween(dates))
                )));
            return winnerNumbers.ToList();
        }

        internal IEnumerable<LotteryDraw> SearchWinnerNumbers(State state, DateTime date, PartsOfDay partsOfDay)
        {
            if (state == null) throw new ArgumentNullException(nameof(state));
            if (date.Hour != 0 || date.Minute != 0 || date.Second != 0) throw new GameEngineException($"{nameof(date)} {date} is not valid");

            var lotteryDraws = new List<LotteryDraw>();
            foreach (var lottery in pick2.Values)
            {
                if (lottery.State == state)
                {
                    foreach (var lotteryDraw in lottery.DrawnLotteriesBetween(new DateTime[1] { date }))
                    {
                        var schedule = lottery.FindScheduleAt(lotteryDraw.Date);
                        if (partsOfDay == schedule.NameOfPartOfDay())
                        {
                            lotteryDraws.Add(lotteryDraw);
                        }
                    }
                }
            }
            foreach (var lottery in pick3.Values)
            {
                if (lottery.State == state)
                {
                    foreach (var lotteryDraw in lottery.DrawnLotteriesBetween(new DateTime[1] { date }))
                    {
                        var schedule = lottery.FindScheduleAt(lotteryDraw.Date);
                        if (partsOfDay == schedule.NameOfPartOfDay())
                        {
                            lotteryDraws.Add(lotteryDraw);
                        }
                    }
                }
            }
            foreach (var lottery in pick4.Values)
            {
                if (lottery.State == state)
                {
                    foreach (var lotteryDraw in lottery.DrawnLotteriesBetween(new DateTime[1] { date }))
                    {
                        var schedule = lottery.FindScheduleAt(lotteryDraw.Date);
                        if (partsOfDay == schedule.NameOfPartOfDay())
                        {
                            lotteryDraws.Add(lotteryDraw);
                        }
                    }
                }
            }
            foreach (var lottery in pick5.Values)
            {
                if (lottery.State == state)
                {
                    foreach (var lotteryDraw in lottery.DrawnLotteriesBetween(new DateTime[1] { date }))
                    {
                        var schedule = lottery.FindScheduleAt(lotteryDraw.Date);
                        if (partsOfDay == schedule.NameOfPartOfDay())
                        {
                            lotteryDraws.Add(lotteryDraw);
                        }
                    }
                }
            }

            var result = lotteryDraws.OrderByDescending(lotteryDraw => lotteryDraw.Date).ToList();
            return result;
        }

        internal LotteryDrawsMatchingDesiredNumber CheckDrawsMatchingWithDesiredNumber(int pick, DateTime startedDate, DateTime endedDate, string winnerNumbers)
        {
            if (startedDate == default(DateTime)) throw new GameEngineException("Date cannot have default value");
            if (endedDate == default(DateTime)) throw new GameEngineException("Date cannot have default value");
            if (endedDate < startedDate) throw new GameEngineException($"{nameof(startedDate)} cannot be greater than {nameof(endedDate)}");
            if (String.IsNullOrWhiteSpace(winnerNumbers)) throw new ArgumentNullException(nameof(winnerNumbers));

            var dates = Enumerable.Range(0, endedDate.Subtract(startedDate).Days + 1).Select(d => startedDate.AddDays(d));
            LotteryDrawsMatchingDesiredNumber lotteryDrawsMatchingWinnerNumber;
            switch (pick)
            {
                case 2:
                    var numbers = new Pick2(winnerNumbers);
                    lotteryDrawsMatchingWinnerNumber = new LotteryDrawsMatchingDesiredNumberPick2(numbers);
                    foreach (var lottery in pick2.Values)
                    {
                        lottery.DrawnLotteriesMatchingWinnerNumbers(dates, lotteryDrawsMatchingWinnerNumber);
                    }
                    break;
                case 3:
                    var numbers3 = new Pick3(winnerNumbers);
                    lotteryDrawsMatchingWinnerNumber = new LotteryDrawsMatchingDesiredNumberPick3(numbers3);
                    foreach (var lottery in pick3.Values)
                    {
                        lottery.DrawnLotteriesMatchingWinnerNumbers(dates, lotteryDrawsMatchingWinnerNumber);
                    }
                    break;
                case 4:
                    var numbers4 = new Pick4(winnerNumbers);
                    lotteryDrawsMatchingWinnerNumber = new LotteryDrawsMatchingDesiredNumberPick4(numbers4);
                    foreach (var lottery in pick4.Values)
                    {
                        lottery.DrawnLotteriesMatchingWinnerNumbers(dates, lotteryDrawsMatchingWinnerNumber);
                    }
                    break;
                case 5:
                    var numbers5 = new Pick5(winnerNumbers);
                    lotteryDrawsMatchingWinnerNumber = new LotteryDrawsMatchingDesiredNumberPick5(numbers5);
                    foreach (var lottery in pick5.Values)
                    {
                        lottery.DrawnLotteriesMatchingWinnerNumbers(dates, lotteryDrawsMatchingWinnerNumber);
                    }
                    break;
                default:
                    throw new GameEngineException($"There is no implementation for {nameof(pick)} {pick}");
            }

            var result = lotteryDrawsMatchingWinnerNumber;
            return result;
        }

        private IEnumerable<DateTime> DatesFrom(string dates)
        {
            if (String.IsNullOrWhiteSpace(dates)) throw new ArgumentNullException(nameof(dates));

            var stringDates = dates.Split(",");
            List<DateTime> drawDates = new List<DateTime>();
            DateTime dateValue;
            foreach (string dateString in stringDates)
            {
                if (!DateTime.TryParseExact(dateString, "M/d/yyyy", Integration.CultureInfoEnUS, DateTimeStyles.None, out dateValue))
                    throw new GameEngineException($"Any date in {nameof(stringDates)} have not the correct format");

                drawDates.Add(dateValue);
            }
            return drawDates;
        }

        internal IEnumerable<LotteryDraw> DrawnLotteriesBetween(DateTime startedDate, DateTime endedDate)
        {
            if (startedDate == default(DateTime)) throw new GameEngineException("Date cannot have default value");
            if (endedDate == default(DateTime)) throw new GameEngineException("Date cannot have default value");
            if (endedDate < startedDate) throw new GameEngineException($"{nameof(startedDate)} cannot be greater than {nameof(endedDate)}");

            var dates = Enumerable.Range(0, endedDate.Subtract(startedDate).Days + 1).Select(d => startedDate.AddDays(d));
            var drawnLotteries = pick2.Values.SelectMany(x => x.DrawnLotteriesBetween(dates)).
                Concat(pick3.Values.SelectMany(x => x.DrawnLotteriesBetween(dates)).
                Concat(pick4.Values.SelectMany(x => x.DrawnLotteriesBetween(dates)).
                Concat(pick5.Values.SelectMany(x => x.DrawnLotteriesBetween(dates)).
                Concat(powerball.DrawnLotteriesBetween(dates))
                )));
            return drawnLotteries.ToList();
        }


        internal bool StateHasDisabledDomain(int pick, State state, Domain domain, DateTime date)
        {
            var lottery = GetLottery(pick, state);
            var result = lottery.HasDisabledDomain(domain, date);
            return result;
        }

        internal List<Ticket> TicketsWithTicketNumberZero()
        {
            var tickets = new List<Ticket>();
            foreach (var lotteryPair in pick2)
            {
                var lotteryTickets = lotteryPair.Value.TicketsWithTicketNumberZero();
                foreach (var ticket in lotteryTickets)
                {
                    tickets.Add(ticket);
                }
            }
            foreach (var lotteryPair in pick3)
            {
                var lotteryTickets = lotteryPair.Value.TicketsWithTicketNumberZero();
                foreach (var ticket in lotteryTickets)
                {
                    tickets.Add(ticket);
                }
            }
            foreach (var lotteryPair in pick4)
            {
                var lotteryTickets = lotteryPair.Value.TicketsWithTicketNumberZero();
                foreach (var ticket in lotteryTickets)
                {
                    tickets.Add(ticket);
                }
            }
            foreach (var lotteryPair in pick5)
            {
                var lotteryTickets = lotteryPair.Value.TicketsWithTicketNumberZero();
                foreach (var ticket in lotteryTickets)
                {
                    tickets.Add(ticket);
                }
            }
            return tickets.OrderBy(ticket => ticket.CreationDate).ToList();
        }

        internal List<Ticket> TicketsMismatchingWithWagerNumbers()
        {
            if (ticketsMismatchingWithWagerNumbers == null) return new List<Ticket>();
            var result = ticketsMismatchingWithWagerNumbers.OrderBy(ticket => ticket.DrawDate).ToList();
            return result;
        }

        internal List<AccountingPendingWager> AccountingPendingWagers()
        {
            if (accountingPendingWagers == null) return new List<AccountingPendingWager>();
            var result = accountingPendingWagers.OrderBy(wager => wager.CreationDate).ToList();
            return result;
        }

        internal IEnumerable<PendingDraw> PendingDraws()
        {
            var pendingDraws = new SortedList<PendingDrawKey, PendingDraw>();
            foreach (var lotteryPair in pick2)
            {
                var draws = lotteryPair.Value.PendingDraws();
                foreach (var draw in draws)
                {
                    pendingDraws.Add(new PendingDrawKey(draw.DrawDate, draw.State.Abbreviation, draw.GameType), draw);
                }
            }
            foreach (var lotteryPair in pick3)
            {
                var draws = lotteryPair.Value.PendingDraws();
                foreach (var draw in draws)
                {
                    pendingDraws.Add(new PendingDrawKey(draw.DrawDate, draw.State.Abbreviation, draw.GameType), draw);
                }
            }
            foreach (var lotteryPair in pick4)
            {
                var draws = lotteryPair.Value.PendingDraws();
                foreach (var draw in draws)
                {
                    pendingDraws.Add(new PendingDrawKey(draw.DrawDate, draw.State.Abbreviation, draw.GameType), draw);
                }
            }
            foreach (var lotteryPair in pick5)
            {
                var draws = lotteryPair.Value.PendingDraws();
                foreach (var draw in draws)
                {
                    pendingDraws.Add(new PendingDrawKey(draw.DrawDate, draw.State.Abbreviation, draw.GameType), draw);
                }
            }
            var powerballDraws = powerball.PendingDraws();
            foreach (var draw in powerballDraws)
            {
                pendingDraws.Add(new PendingDrawKey(draw.DrawDate, draw.State.Abbreviation, draw.GameType), draw);
            }
            return pendingDraws.Values.ToList();
        }

        internal IEnumerable<PendingDraw> PendingDrawsBy(string domainIds)
        {
            var pendingDraws = new SortedList<PendingDrawPerDomainKey, PendingDraw>();
            foreach (var lotteryPair in pick2)
            {
                var draws = lotteryPair.Value.PendingDrawsBy(domainIds);
                foreach (var draw in draws)
                {
                    pendingDraws.Add(new PendingDrawPerDomainKey(draw.DrawDate, draw.State.Abbreviation, draw.GameType, draw.UniqueDrawingId, draw.DomainId), draw);
                }
            }
            foreach (var lotteryPair in pick3)
            {
                var draws = lotteryPair.Value.PendingDrawsBy(domainIds);
                foreach (var draw in draws)
                {
                    pendingDraws.Add(new PendingDrawPerDomainKey(draw.DrawDate, draw.State.Abbreviation, draw.GameType, draw.UniqueDrawingId, draw.DomainId), draw);
                }
            }
            foreach (var lotteryPair in pick4)
            {
                var draws = lotteryPair.Value.PendingDrawsBy(domainIds);
                foreach (var draw in draws)
                {
                    pendingDraws.Add(new PendingDrawPerDomainKey(draw.DrawDate, draw.State.Abbreviation, draw.GameType, draw.UniqueDrawingId, draw.DomainId), draw);
                }
            }
            foreach (var lotteryPair in pick5)
            {
                var draws = lotteryPair.Value.PendingDrawsBy(domainIds);
                foreach (var draw in draws)
                {
                    pendingDraws.Add(new PendingDrawPerDomainKey(draw.DrawDate, draw.State.Abbreviation, draw.GameType, draw.UniqueDrawingId, draw.DomainId), draw);
                }
            }
            return pendingDraws.Values.ToList();
        }

        internal IEnumerable<LotteryComplete> CompletedDraws()
        {
            var completedDraws = new SortedList<CompletedDrawKey, LotteryComplete>();
            foreach (var lotteryPair in pick2)
            {
                var draws = lotteryPair.Value.CompletedDraws();
                foreach (var draw in draws)
                {
                    completedDraws.Add(new CompletedDrawKey(draw.Date, draw.State.Abbreviation, draw.GameType), draw);
                }
            }
            foreach (var lotteryPair in pick3)
            {
                var draws = lotteryPair.Value.CompletedDraws();
                foreach (var draw in draws)
                {
                    completedDraws.Add(new CompletedDrawKey(draw.Date, draw.State.Abbreviation, draw.GameType), draw);
                }
            }
            foreach (var lotteryPair in pick4)
            {
                var draws = lotteryPair.Value.CompletedDraws();
                foreach (var draw in draws)
                {
                    completedDraws.Add(new CompletedDrawKey(draw.Date, draw.State.Abbreviation, draw.GameType), draw);
                }
            }
            foreach (var lotteryPair in pick5)
            {
                var draws = lotteryPair.Value.CompletedDraws();
                foreach (var draw in draws)
                {
                    completedDraws.Add(new CompletedDrawKey(draw.Date, draw.State.Abbreviation, draw.GameType), draw);
                }
            }
            return completedDraws.Values.ToList();
        }

        internal override bool AnyScheduleHasChanged(IEnumerable<int> picks, IEnumerable<string> statesAbb, IEnumerable<string> hours, IEnumerable<string> withFireBalls)
        {
            if (picks == null || !picks.Any()) throw new ArgumentNullException(nameof(picks));
            if (statesAbb == null || !statesAbb.Any()) throw new ArgumentNullException(nameof(statesAbb));
            if (hours == null || !hours.Any()) throw new ArgumentNullException(nameof(hours));
            if (withFireBalls == null || !withFireBalls.Any()) throw new ArgumentNullException(nameof(withFireBalls));

            int index = 0;
            foreach (var pick in picks)
            {
                if (!pickValues.Contains(pick)) throw new GameEngineException($"{nameof(pick)} {pick} is not valid");
                var stateAbb = statesAbb.ElementAt(index);
                var hour = hours.ElementAt(index);

                bool withFireBall;
                if (!bool.TryParse(withFireBalls.ElementAt(index), out withFireBall)) throw new GameEngineException($"Invalid withFireBall value {withFireBall}");

                var anyScheduleHasChanged = HasScheduleChanged(pick, stateAbb, hour, withFireBall);
                if (!anyScheduleHasChanged) return true;
                index++;
            }
            return false;
        }

        internal bool AnyScheduleHasChanged(int pick, string statesAbb, string hours, string withFireBalls)
        {
            if (String.IsNullOrWhiteSpace(statesAbb)) throw new ArgumentNullException(nameof(statesAbb));
            if (String.IsNullOrWhiteSpace(hours)) throw new ArgumentNullException(nameof(hours));
            if (String.IsNullOrWhiteSpace(withFireBalls)) throw new ArgumentNullException(nameof(withFireBalls));
            if (!pickValues.Contains(pick)) throw new GameEngineException($"{nameof(pick)} {pick} is not valid");

            var arrayStatesAbb = statesAbb.Split(",");
            var arrayHours = hours.Split(",");
            var arrayWithFireBalls = withFireBalls.Split(",");
            for (int index = 0; index < arrayStatesAbb.Length; index++)
            {
                bool withFireBall;
                if (!bool.TryParse(arrayWithFireBalls[index], out withFireBall)) throw new GameEngineException($"Invalid withFireBall value {withFireBall}");
                var anyScheduleHasHour = HasScheduleChanged(pick, arrayStatesAbb[index], arrayHours[index], withFireBall);
                if (!anyScheduleHasHour) return true;
            }
            return false;
        }

        private bool HasScheduleChanged(int pick, string stateAbb, string hour, bool withFireBall)
        {
            var state = GetState(stateAbb);
            Lottery lottery = null;
            switch (pick)
            {
                case 2:
                    lottery = GetLottery(2, state);
                    break;
                case 3:
                    lottery = GetLottery(3, state);
                    break;
                case 4:
                    lottery = GetLottery(4, state);
                    break;
                case 5:
                    lottery = GetLottery(5, state);
                    break;
                default:
                    throw new GameEngineException($"{nameof(pick)} {pick} is not valid");
            }

            var drawHour = DateTime.ParseExact(hour, "h:m tt", Integration.CultureInfoEnUS);
            if (withFireBall)
            {
                switch (lottery)
                {
                    case LotteryPick<Pick2> pick2Lottery:
                        if (!pick2Lottery.IsFireBallTurnedOn) throw new GameEngineException("FireBall is not enabled for Pick2");
                        return pick2Lottery.FireBallLottery.AnyScheduleHasHour(drawHour.Hour, drawHour.Minute);
                    case LotteryPick<Pick3> pick3Lottery:
                        if (!pick3Lottery.IsFireBallTurnedOn) throw new GameEngineException("FireBall is not enabled for Pick3");
                        return pick3Lottery.FireBallLottery.AnyScheduleHasHour(drawHour.Hour, drawHour.Minute);
                    case LotteryPick<Pick4> pick4Lottery:
                        if (!pick4Lottery.IsFireBallTurnedOn) throw new GameEngineException("FireBall is not enabled for Pick4");
                        return pick4Lottery.FireBallLottery.AnyScheduleHasHour(drawHour.Hour, drawHour.Minute);
                    case LotteryPick<Pick5> pick5Lottery:
                        if (!pick5Lottery.IsFireBallTurnedOn) throw new GameEngineException("FireBall is not enabled for Pick5");
                        return pick5Lottery.FireBallLottery.AnyScheduleHasHour(drawHour.Hour, drawHour.Minute);
                    default:
                        throw new GameEngineException($"{nameof(lottery)} is not a LotteryPick type.");
                }
            }
            return lottery.AnyScheduleHasHour(drawHour.Hour, drawHour.Minute);
        }

        private List<Ticket> SearchAllWagersByTicketNumber(int ticketNumber)
        {
            List <Ticket> result = new List<Ticket>() ;
            foreach (var lotteryPair in pick2)
            {
                var lotteryTickets = lotteryPair.Value.SearchTicketByNumber(ticketNumber);
                foreach (var ticket in lotteryTickets)
                {
                    result.Add(ticket);
                }
            }
            foreach (var lotteryPair in pick3)
            {
                var lotteryTickets = lotteryPair.Value.SearchTicketByNumber(ticketNumber);
                foreach (var ticket in lotteryTickets)
                {
                    result.Add(ticket);
                }
            }
            foreach (var lotteryPair in pick4)
            {
                var lotteryTickets = lotteryPair.Value.SearchTicketByNumber(ticketNumber);
                foreach (var ticket in lotteryTickets)
                {
                    result.Add(ticket);
                }
            }
            foreach (var lotteryPair in pick5)
            {
                var lotteryTickets = lotteryPair.Value.SearchTicketByNumber(ticketNumber);
                foreach (var ticket in lotteryTickets)
                {
                    result.Add(ticket);
                }
            }

            return result;
        }

        internal List<Ticket> SearchTicketByNumber(int ticketNumber)
        {
            List<Ticket> result = SearchAllWagersByTicketNumber(ticketNumber);

            if (result == null) throw new GameEngineException($@"There is no order with number {ticketNumber}.");

            return result;
        }

        internal void ChangeWagersAndReferenceNumber (int ticketNumber, int oldBetNumber, int oldConsecutive, int oldWager, int newBetNumber, int newConsecutive, int newWager )
        {
            List<Ticket> tickets = SearchTicketByNumber(ticketNumber);
            List<TicketWager> wagers = new List<TicketWager>();

            bool newvaluesAreAlreadyUsed = false;

            foreach (Ticket ticket in tickets)
            {
                foreach (TicketWager wager in ticket.Wagers)
                {
                    if (wager.BetNumber == oldBetNumber
                        && wager.Consecutive == oldConsecutive
                        && wager.WagerNumber == oldWager)
                    {
                        wagers.Add(wager);
                    }

                    if (wager.BetNumber == newBetNumber
                       && wager.Consecutive == newConsecutive
                       && wager.WagerNumber == newWager)
                    {
                        newvaluesAreAlreadyUsed = true;
                        break;
                    }
                }
            }

            if(newvaluesAreAlreadyUsed) throw new GameEngineException($@"The new values for ReferenceNumber: {newBetNumber} Consecutive: {newConsecutive} Wager: {newWager} is been already used for other wager.");
            if (wagers.Count <= 0) throw new GameEngineException($@"There is no wagers with ReferenceNumber: {oldBetNumber} Consecutive: {oldConsecutive} Wager: {oldWager} for ticket {ticketNumber}");
            if (wagers.Count != 1) throw new GameEngineException($@"There is more than one wager with ReferenceNumber: {oldBetNumber} Consecutive: {oldConsecutive} Wager: {oldWager}  for ticket {ticketNumber}");

            wagers[0].WagerNumber = newWager;
            wagers[0].BetNumber = newBetNumber;
            wagers[0].Consecutive = newConsecutive;
        }

        internal void UpdateWagersWithNumberZero(int ticketNumber, List<int> betNumbers, List<int> consecutives, List<int> newWagerNumbers)
        {
            if (ticketNumber <= 0) throw new GameEngineException($"{nameof(ticketNumber)} must be greater than 0");
            if (!betNumbers.Any()) throw new GameEngineException($"{nameof(betNumbers)} cannot be empty");
            if (!consecutives.Any()) throw new GameEngineException($"{nameof(consecutives)} cannot be empty");
            if (!newWagerNumbers.Any()) throw new GameEngineException($"{nameof(newWagerNumbers)} cannot be empty");
            if (betNumbers.Count != consecutives.Count || betNumbers.Count != newWagerNumbers.Count) throw new GameEngineException($"Collection of {nameof(betNumbers)}, {nameof(consecutives)} and {nameof(newWagerNumbers)} must have the same size");

            var wagers = WagersWithNumberZero(ticketNumber);
            var wagersWithNumberZero = wagers.Wagers;
            if (wagersWithNumberZero.Count != newWagerNumbers.Count) throw new GameEngineException($"There is a mismatch between wager numbers expected and sent. {nameof(newWagerNumbers)} have {newWagerNumbers.Count} and {nameof(wagersWithNumberZero)} have {wagersWithNumberZero.Count}");
            for (int index = 0; index < betNumbers.Count; index++)
            {
                var wagerIndex = wagersWithNumberZero.FindIndex(wager => wager.BetNumber == betNumbers[index] && wager.Consecutive == consecutives[index]);
                if (wagerIndex == -1) throw new GameEngineException($"Wager with bet number {betNumbers[index]} and consecutive {consecutives[index]} is not found");
                wagersWithNumberZero[wagerIndex].WagerNumber = newWagerNumbers[index];
            }
        }

		internal void ChangeTicketNumber(int oldticketNumber, int newticketNumber)
        {

            List<Ticket> tickets = SearchTicketByNumber(newticketNumber);
            if (tickets.Count > 0)
            {
                throw new GameEngineException($@"Ticket nmber {newticketNumber} is already used.");
            }

            tickets = SearchTicketByNumber(oldticketNumber);

            foreach (Ticket ticket in tickets)
            {
                ticket.TicketNumber = newticketNumber;
            }

        }

        internal List<Ticket> TicketsWithWagersNumberZero(DateTime startDrawDay, DateTime endDrawDay)
        {
            if (startDrawDay.Hour != 0 || startDrawDay.Minute != 0 || startDrawDay.Second != 0 || startDrawDay.Millisecond != 0) throw new GameEngineException($"{nameof(startDrawDay)} '{startDrawDay}' must be an exact day");
            if (endDrawDay.Hour != 0 || endDrawDay.Minute != 0 || endDrawDay.Second != 0 || endDrawDay.Millisecond != 0) throw new GameEngineException($"{nameof(endDrawDay)} '{endDrawDay}' must be an exact day");

            var result = new List<Ticket>();
            foreach (var lottery in pick3.Values)
            {
                var tempTickets = lottery.TicketsWithWagersNumberZero(startDrawDay, endDrawDay);
                result.AddRange(tempTickets);
            }
            foreach (var lottery in pick4.Values)
            {
                var tempTickets = lottery.TicketsWithWagersNumberZero(startDrawDay, endDrawDay);
                result.AddRange(tempTickets);
            }
            foreach (var lottery in pick2.Values)
            {
                var tempTickets = lottery.TicketsWithWagersNumberZero(startDrawDay, endDrawDay);
                result.AddRange(tempTickets);
            }
            foreach (var lottery in pick5.Values)
            {
                var tempTickets = lottery.TicketsWithWagersNumberZero(startDrawDay, endDrawDay);
                result.AddRange(tempTickets);
            }
            return result;
        }

        internal List<Ticket> FindTicketsMatchingWith(IEnumerable<int> ticketNumbers)
        {
            if (!ticketNumbers.Any()) throw new GameEngineException($"{nameof(ticketNumbers)} to compare are empty.");

            var result = new List<Ticket>();
            bool found = false;
            int expectedCount = ticketNumbers.Count();
            foreach (var lottery in pick3.Values)
            {
                var tempTickets = lottery.FindTicketsMatchingWith(ticketNumbers);
                result.AddRange(tempTickets);
                if (result.Count == expectedCount) found = true;
            }
            if (!found) foreach (var lottery in pick4.Values)
                {
                    var tempTickets = lottery.FindTicketsMatchingWith(ticketNumbers);
                    result.AddRange(tempTickets);
                    if (result.Count == expectedCount) found = true;
                }
            if (!found) foreach (var lottery in pick2.Values)
                {
                    var tempTickets = lottery.FindTicketsMatchingWith(ticketNumbers);
                    result.AddRange(tempTickets);
                    if (result.Count == expectedCount) found = true;
                }
            if (!found) foreach (var lottery in pick5.Values)
                {
                    var tempTickets = lottery.FindTicketsMatchingWith(ticketNumbers);
                    result.AddRange(tempTickets);
                    if (result.Count == expectedCount) found = true;
                }
            return result;
        }

        internal Ticket FindTicketMatchingWith(int ticketNumber)
        {
            if (ticketNumber <= 0) throw new GameEngineException($"{nameof(ticketNumber)} must be greater than 0");

            foreach (var lottery in pick3.Values)
            {
                var ticket = lottery.FindTicketMatchingWith(ticketNumber);
                if (ticket != null) return ticket;
            }
            foreach (var lottery in pick4.Values)
            {
                var ticket = lottery.FindTicketMatchingWith(ticketNumber);
                if (ticket != null) return ticket;
            }
            foreach (var lottery in pick2.Values)
            {
                var ticket = lottery.FindTicketMatchingWith(ticketNumber);
                if (ticket != null) return ticket;
            }
            foreach (var lottery in pick5.Values)
            {
                var ticket = lottery.FindTicketMatchingWith(ticketNumber);
                if (ticket != null) return ticket;
            }
            var ticketKeno = keno.FindTicketMatchingWith(ticketNumber);
            if (ticketKeno != null) return ticketKeno;
            throw new GameEngineException($"{nameof(ticketNumber)} '{ticketNumber}' was not found");
        }

        internal List<Ticket> FindTicketsInRange(int theLowestBetId, int theHighestBetId)
        {
            if (theLowestBetId <= 0) throw new GameEngineException($"{nameof(theLowestBetId)} must be greater than 0");
            if (theHighestBetId <= 0) throw new GameEngineException($"{nameof(theHighestBetId)} must be greater than 0");

            var result = new List<Ticket>();
            bool found = false;
            int expectedCount = theHighestBetId - theLowestBetId + 1;
            foreach (var lottery in pick3.Values)
            {
                var tempTickets = lottery.FindTicketsInRange(theLowestBetId, theHighestBetId);
                result.AddRange(tempTickets);
                if (result.Count == expectedCount) found = true;
            }
            if (!found) foreach (var lottery in pick4.Values)
                {
                    var tempTickets = lottery.FindTicketsInRange(theLowestBetId, theHighestBetId);
                    result.AddRange(tempTickets);
                    if (result.Count == expectedCount) found = true;
                }
            if (!found) foreach (var lottery in pick2.Values)
                {
                    var tempTickets = lottery.FindTicketsInRange(theLowestBetId, theHighestBetId);
                    result.AddRange(tempTickets);
                    if (result.Count == expectedCount) found = true;
                }
            if (!found) foreach (var lottery in pick5.Values)
                {
                    var tempTickets = lottery.FindTicketsInRange(theLowestBetId, theHighestBetId);
                    result.AddRange(tempTickets);
                    if (result.Count == expectedCount) found = true;
                }
            return result;
        }

        internal WagersWithCommonTicketNumber WagersWithNumberZero(int ticketNumber)
        {
            if (ticketNumber <= 0) throw new GameEngineException($"{nameof(ticketNumber)} must be greater than 0");

            var result = new List<TicketWager>();
            foreach (var lottery in pick3.Values)
            {
                var tempTickets = lottery.WagersWithNumberZero(ticketNumber);
                result.AddRange(tempTickets);
            }
            foreach (var lottery in pick4.Values)
            {
                var tempTickets = lottery.WagersWithNumberZero(ticketNumber);
                result.AddRange(tempTickets);
            }
            foreach (var lottery in pick2.Values)
            {
                var tempTickets = lottery.WagersWithNumberZero(ticketNumber);
                result.AddRange(tempTickets);
            }
            foreach (var lottery in pick5.Values)
            {
                var tempTickets = lottery.WagersWithNumberZero(ticketNumber);
                result.AddRange(tempTickets);
            }

            if (! result.Any()) throw new GameEngineException($"{nameof(ticketNumber)} '{ticketNumber}' was not found");
            return new WagersWithCommonTicketNumber(result);
        }

        internal WagersWithCommonTicketNumber WagersOfTicket(int ticketNumber)
        {
            if (ticketNumber <= 0) throw new GameEngineException($"{nameof(ticketNumber)} must be greater than 0");

            var result = new List<TicketWager>();
            foreach (var lottery in pick3.Values)
            {
                var tempTickets = lottery.WagersOfTicket(ticketNumber);
                result.AddRange(tempTickets);
            }
            foreach (var lottery in pick4.Values)
            {
                var tempTickets = lottery.WagersOfTicket(ticketNumber);
                result.AddRange(tempTickets);
            }
            foreach (var lottery in pick2.Values)
            {
                var tempTickets = lottery.WagersOfTicket(ticketNumber);
                result.AddRange(tempTickets);
            }
            foreach (var lottery in pick5.Values)
            {
                var tempTickets = lottery.WagersOfTicket(ticketNumber);
                result.AddRange(tempTickets);
            }

            if (!result.Any()) throw new GameEngineException($"{nameof(ticketNumber)} '{ticketNumber}' was not found");
            return new WagersWithCommonTicketNumber(result);
        }

        internal class WagersWithCommonTicketNumber:Objeto
        {
            public string AccountNumber { get; }
            public List<TicketWager> Wagers { get; }

            public WagersWithCommonTicketNumber(List<TicketWager> wagers)
            {
                Wagers = wagers;
                AccountNumber = wagers.First().AccountNumber;
            }
        }

        private TicketWager SearchWagerByTicketAndWagerNumber(int ticketNumber, int wagerNumber)
        {
            foreach (var lotteryPair in pick3)
            {
                var lotteryTickets = lotteryPair.Value.SearchTicketByNumber(ticketNumber);
                foreach (var ticket in lotteryTickets)
                {
                    foreach (var wager in ticket.Wagers)
                    {
                        if (wager.WagerNumber == wagerNumber)
                        {
                            return wager;
                        }
                    }
                }
            }
            foreach (var lotteryPair in pick4)
            {
                var lotteryTickets = lotteryPair.Value.SearchTicketByNumber(ticketNumber);
                foreach (var ticket in lotteryTickets)
                {
                    foreach (var wager in ticket.Wagers)
                    {
                        if (wager.WagerNumber == wagerNumber)
                        {
                            return wager;
                        }
                    }
                }
            }
            foreach (var lotteryPair in pick2)
            {
                var lotteryTickets = lotteryPair.Value.SearchTicketByNumber(ticketNumber);
                foreach (var ticket in lotteryTickets)
                {
                    foreach (var wager in ticket.Wagers)
                    {
                        if (wager.WagerNumber == wagerNumber)
                        {
                            return wager;
                        }
                    }
                }
            }
            foreach (var lotteryPair in pick5)
            {
                var lotteryTickets = lotteryPair.Value.SearchTicketByNumber(ticketNumber);
                foreach (var ticket in lotteryTickets)
                {
                    foreach (var wager in ticket.Wagers)
                    {
                        if (wager.WagerNumber == wagerNumber)
                        {
                            return wager;
                        }
                    }
                }
            }

            throw new GameEngineException($@"There is no wager with {nameof(ticketNumber)} : {ticketNumber} {nameof(wagerNumber)} : {wagerNumber}");
        }

        internal override void AddWagerGradedWithProblemAboutIsValidTicket(int ticketNumber, int wagerNumber)
        {
            if (ticketNumber <= 0) throw new GameEngineException($"{nameof(ticketNumber)} must be greater than 0");
            if (wagerNumber <= 0) throw new GameEngineException($"{nameof(wagerNumber)} must be greater than 0");

            var wager = SearchWagerByTicketAndWagerNumber(ticketNumber, wagerNumber);
            if (wagersGradedWithProblemsAboutIsValidTicket == null) wagersGradedWithProblemsAboutIsValidTicket = new List<WagerWithError>();
            wagersGradedWithProblemsAboutIsValidTicket.Add(new WagerWithError(wager, "This wager has an invalid ticketNumber"));
        }

        internal List<WagerWithError> WagersGradedWithProblemsAboutIsValidTicket()
        {
            if (wagersGradedWithProblemsAboutIsValidTicket == null) return new List<WagerWithError>();
            var result = wagersGradedWithProblemsAboutIsValidTicket.OrderBy(wager => wager.Wager.TicketCreationDate).ToList();
            return result;
        }

        internal static IPick[] PicksOfBalls(int pickNumber, IEnumerable<string> arrayOfNumbersPattern)
        {
            IPick pick;
            switch (pickNumber)
            {
                case 2:
                    pick = new Pick2(arrayOfNumbersPattern.ElementAt(0), arrayOfNumbersPattern.ElementAt(1));
                    break;
                case 3:
                    pick = new Pick3(arrayOfNumbersPattern.ElementAt(0), arrayOfNumbersPattern.ElementAt(1), arrayOfNumbersPattern.ElementAt(2));
                    break;
                case 4:
                    pick = new Pick4(arrayOfNumbersPattern.ElementAt(0), arrayOfNumbersPattern.ElementAt(1), arrayOfNumbersPattern.ElementAt(2), arrayOfNumbersPattern.ElementAt(3));
                    break;
                case 5:
                    pick = new Pick5(arrayOfNumbersPattern.ElementAt(0), arrayOfNumbersPattern.ElementAt(1), arrayOfNumbersPattern.ElementAt(2), arrayOfNumbersPattern.ElementAt(3), arrayOfNumbersPattern.ElementAt(4));
                    break;
                default:
                    throw new GameEngineException($"There is no {nameof(IPick)} implementation for {nameof(pickNumber)} {pickNumber}");
            }
            return new IPick[] { pick };
        }

        internal static IPick[] PicksOfNumbers(int pickNumber, IEnumerable<string> includesArr, int length)
        {
            var countToInclude = length;
            var numbers = new IPick[countToInclude];

            switch (pickNumber)
            {
                case 2:
                    for (int i = 0; i < countToInclude; i++)
                    {
                        string pattern = includesArr.ElementAt(i);
                        bool isBeginningNumer = pattern.Contains(Pick2.SPLIT_BEGINNING);
                        if (isBeginningNumer) pattern = pattern.Replace(Pick2.SPLIT_BEGINNING, string.Empty);
                        if (pattern.Length != 2) throw new GameEngineException($"Number [{pattern}] is not a valid {nameof(Pick2)} ticket");
                        numbers[i] = new Pick2(pattern, !isBeginningNumer);
                        for (int p = i - 1; p >= 0; p--)
                            if (numbers[p].Equals(numbers[i]))
                                throw new GameEngineException($"Number [{pattern}] is already included on ticket {numbers}");
                    }
                    break;
                case 3:
                    for (int i = 0; i < countToInclude; i++)
                    {
                        var pattern = includesArr.ElementAt(i);
                        if (pattern.Length != 3) throw new GameEngineException($"Number [{pattern}] is not a valid {nameof(Pick3)} ticket");
                        numbers[i] = new Pick3(pattern);
                        for (int p = i - 1; p >= 0; p--)
                            if (numbers[p].Equals(numbers[i]))
                                throw new GameEngineException($"Number [{pattern}] is already included on ticket {numbers}");
                    }
                    break;
                case 4:
                    for (int i = 0; i < countToInclude; i++)
                    {
                        var pattern = includesArr.ElementAt(i);
                        if (pattern.Length != 4) throw new GameEngineException($"Number [{pattern}] is not a valid {nameof(Pick4)} ticket");
                        numbers[i] = new Pick4(pattern);
                        for (int p = i - 1; p >= 0; p--)
                            if (numbers[p].Equals(numbers[i]))
                                throw new GameEngineException($"Number [{pattern}] is already included on ticket {numbers}");
                    }
                    break;
                case 5:
                    for (int i = 0; i < countToInclude; i++)
                    {
                        var pattern = includesArr.ElementAt(i);
                        if (pattern.Length != 5) throw new GameEngineException($"Number [{pattern}] is not a valid {nameof(Pick5)} ticket");
                        numbers[i] = new Pick5(pattern);
                        for (int p = i - 1; p >= 0; p--)
                            if (numbers[p].Equals(numbers[i]))
                                throw new GameEngineException($"Number [{pattern}] is already included on ticket {numbers}");
                    }
                    break;
                default:
                    throw new GameEngineException("Unknown Pick type");
            }

            return numbers;
        }

        internal delegate TicketPickActivator TicketCreatorDelegate(Player player, State state, NextDateItem date, bool withFireBall, decimal ticketCost, string selectionMode, IEnumerable<string> numbers, string[] includedNumbersForInput, ExcludeSubtickets excludedSubtickets, Domain domain, int indexInOrder);


        internal Pick2StraightTicketActivator CreatePick2StraightTicket(Player player, State state, NextDateItem nextDateItem, bool withFireBall, decimal ticketCost, string selectionMode, IEnumerable<string> numbers, string[] includedNumbersForInput, ExcludeSubtickets excludedSubtickets, Domain domain, int indexInOrder)
        {
            if (this.StateHasDisabledDomain(2, state, domain, nextDateItem.Date)) throw new GameEngineException($"Draw {nameof(Pick2)} is disabled for {nameof(state)} {state.Abbreviation} in {nameof(domain)} {domain.Url} at {nameof(nextDateItem)} {nextDateItem.Date}");
            Pick2StraightTicketActivator activator = new Pick2StraightTicketActivator(this);
            Company.CreateTicket(activator, player, state, nextDateItem, withFireBall, ticketCost, selectionMode, numbers, includedNumbersForInput, excludedSubtickets, indexInOrder);
            return activator;
        }

        internal Pick3StraightTicketActivator CreatePick3StraightTicket(Player player, State state, NextDateItem nextDateItem, bool withFireBall, decimal ticketCost, string selectionMode, IEnumerable<string> numbers, string[] includedNumbersForInput, ExcludeSubtickets excludedSubtickets, Domain domain, int indexInOrder)
        {
            //if (lotto900.StateHasDisabledDomain(3, state, domain, nextDateItem.Date)) throw new GameEngineException($"Draw {nameof(Pick3)} is disabled for {nameof(state)} {state.Abbreviation} in {nameof(domain)} {domain.Url} at {nameof(nextDateItem)} {nextDateItem.Date}");
            if (this.StateHasDisabledDomain(3, state, domain, nextDateItem.Date)) throw new GameEngineException($"Draw {nameof(Pick3)} is disabled for {nameof(state)} {state.Abbreviation} in {nameof(domain)} {domain.Url} at {nameof(nextDateItem)} {nextDateItem.Date}");
            Pick3StraightTicketActivator activator = new Pick3StraightTicketActivator(this);
            Company.CreateTicket(activator, player, state, nextDateItem, withFireBall, ticketCost, selectionMode, numbers, includedNumbersForInput, excludedSubtickets, indexInOrder);
            return activator;
        }

        internal Pick4StraightTicketActivator CreatePick4StraightTicket(Player player, State state, NextDateItem nextDateItem, bool withFireBall, decimal ticketCost, string selectionMode, IEnumerable<string> numbers, string[] includedNumbersForInput, ExcludeSubtickets excludedSubtickets, Domain domain, int indexInOrder)
        {
            if (this.StateHasDisabledDomain(4, state, domain, nextDateItem.Date)) throw new GameEngineException($"Draw {nameof(Pick4)} is disabled for {nameof(state)} {state.Abbreviation} in {nameof(domain)} {domain.Url} at {nameof(nextDateItem)} {nextDateItem.Date}");
            Pick4StraightTicketActivator activator = new Pick4StraightTicketActivator(this);
            Company.CreateTicket(activator, player, state, nextDateItem, withFireBall, ticketCost, selectionMode, numbers, includedNumbersForInput, excludedSubtickets, indexInOrder);
            return activator;
        }

        internal Pick5StraightTicketActivator CreatePick5StraightTicket(Player player, State state, NextDateItem nextDateItem, bool withFireBall, decimal ticketCost, string selectionMode, IEnumerable<string> numbers, string[] includedNumbersForInput, ExcludeSubtickets excludedSubtickets, Domain domain, int indexInOrder)
        {
            if (this.StateHasDisabledDomain(5, state, domain, nextDateItem.Date)) throw new GameEngineException($"Draw {nameof(Pick5)} is disabled for {nameof(state)} {state.Abbreviation} in {nameof(domain)} {domain.Url} at {nameof(nextDateItem)} {nextDateItem.Date}");
            Pick5StraightTicketActivator activator = new Pick5StraightTicketActivator(this);
            Company.CreateTicket(activator, player, state, nextDateItem, withFireBall, ticketCost, selectionMode, numbers, includedNumbersForInput, excludedSubtickets, indexInOrder);
            return activator;
        }

        internal Pick2BoxedTicketActivator CreatePick2BoxedTicket(Player player, State state, NextDateItem nextDateItem, bool withFireBall, decimal ticketCost, string selectionMode, IEnumerable<string> numbers, string[] includedNumbersForInput, ExcludeSubtickets excludedSubtickets, Domain domain, int indexInOrder)
        {
            if (this.StateHasDisabledDomain(2, state, domain, nextDateItem.Date)) throw new GameEngineException($"Draw {nameof(Pick2)} is disabled for {nameof(state)} {state.Abbreviation} in {nameof(domain)} {domain.Url} at {nameof(nextDateItem)} {nextDateItem.Date}");
            Pick2BoxedTicketActivator activator = new Pick2BoxedTicketActivator(this);
            Company.CreateTicket(activator, player, state, nextDateItem, withFireBall, ticketCost, selectionMode, numbers, includedNumbersForInput, excludedSubtickets, indexInOrder);
            return activator;
        }

        private Pick3BoxedTicketActivator CreatePick3BoxedTicket(Player player, State state, NextDateItem nextDateItem, bool withFireBall, decimal ticketCost, string selectionMode, IEnumerable<string> numbers, string[] includedNumbersForInput, ExcludeSubtickets excludedSubtickets, Domain domain, int indexInOrder)
        {
            if (this.StateHasDisabledDomain(3, state, domain, nextDateItem.Date)) throw new GameEngineException($"Draw {nameof(Pick3)} is disabled for {nameof(state)} {state.Abbreviation} in {nameof(domain)} {domain.Url} at {nameof(nextDateItem)} {nextDateItem.Date}");
            Pick3BoxedTicketActivator activator = new Pick3BoxedTicketActivator(this);
            Company.CreateTicket(activator, player, state, nextDateItem, withFireBall, ticketCost, selectionMode, numbers, includedNumbersForInput, excludedSubtickets, indexInOrder);
            return activator;
        }

        internal Pick4BoxedTicketActivator CreatePick4BoxedTicket(Player player, State state, NextDateItem nextDateItem, bool withFireBall, decimal ticketCost, string selectionMode, IEnumerable<string> numbers, string[] includedNumbersForInput, ExcludeSubtickets excludedSubtickets, Domain domain, int indexInOrder)
        {
            if (this.StateHasDisabledDomain(4, state, domain, nextDateItem.Date)) throw new GameEngineException($"Draw {nameof(Pick4)} is disabled for {nameof(state)} {state.Abbreviation} in {nameof(domain)} {domain.Url} at {nameof(nextDateItem)} {nextDateItem.Date}");
            Pick4BoxedTicketActivator activator = new Pick4BoxedTicketActivator(this);
            Company.CreateTicket(activator, player, state, nextDateItem, withFireBall, ticketCost, selectionMode, numbers, includedNumbersForInput, excludedSubtickets, indexInOrder);
            return activator;
        }

        internal Pick5BoxedTicketActivator CreatePick5BoxedTicket(Player player, State state, NextDateItem nextDateItem, bool withFireBall, decimal ticketCost, string selectionMode, IEnumerable<string> numbers, string[] includedNumbersForInput, ExcludeSubtickets excludedSubtickets, Domain domain, int indexInOrder)
        {
            if (this.StateHasDisabledDomain(5, state, domain, nextDateItem.Date)) throw new GameEngineException($"Draw {nameof(Pick5)} is disabled for {nameof(state)} {state.Abbreviation} in {nameof(domain)} {domain.Url} at {nameof(nextDateItem)} {nextDateItem.Date}");
            Pick5BoxedTicketActivator activator = new Pick5BoxedTicketActivator(this);
            Company.CreateTicket(activator, player, state, nextDateItem, withFireBall, ticketCost, selectionMode, numbers, includedNumbersForInput, excludedSubtickets, indexInOrder);
            return activator;
        }

        internal TicketCreatorDelegate GetTicketCreator(TicketType ticketType)
        {
            switch (ticketType)
            {
                case TicketType.P2S:
                    return CreatePick2StraightTicket;
                case TicketType.P2B:
                    return CreatePick2BoxedTicket;
                case TicketType.P3S:
                    return CreatePick3StraightTicket;
                case TicketType.P3B:
                    return CreatePick3BoxedTicket;
                case TicketType.P4S:
                    return CreatePick4StraightTicket;
                case TicketType.P4B:
                    return CreatePick4BoxedTicket;
                case TicketType.P5S:
                    return CreatePick5StraightTicket;
                case TicketType.P5B:
                    return CreatePick5BoxedTicket;
            }
            throw new GameEngineException($"Pick type {ticketType} is not handled implement logic for {nameof(ticketType)}.");
        }

        internal override bool TryGetScheduleByUniqueId(int uniqueDrawingId, out Schedule result)
        {
            if (uniqueDrawingId <= 0) throw new GameEngineException($"{nameof(uniqueDrawingId)} {uniqueDrawingId} is not valid");

            result = null;
            foreach (var lottery in pick3.Values)
            {
                lottery.TryGetScheduleByUniqueId(uniqueDrawingId, out result);
                if (result != null) return true;
            }
            foreach (var lottery in pick4.Values)
            {
                lottery.TryGetScheduleByUniqueId(uniqueDrawingId, out result);
                if (result != null) return true;
            }
            foreach (var lottery in pick2.Values)
            {
                lottery.TryGetScheduleByUniqueId(uniqueDrawingId, out result);
                if (result != null) return true;
            }
            foreach (var lottery in pick5.Values)
            {
                lottery.TryGetScheduleByUniqueId(uniqueDrawingId, out result);
                if (result != null) return true;
            }
            return false;
        }

        internal override bool ExistsInternalSchedule(int uniqueDrawingId)
        {
            if (uniqueDrawingId <= 0) throw new GameEngineException($"{nameof(uniqueDrawingId)} {uniqueDrawingId} is not valid");

            bool exists;
            foreach (var lottery in pick3.Values)
            {
                exists = lottery.ExistsScheduleByUniqueId(uniqueDrawingId);
                if (exists) return true;
            }
            foreach (var lottery in pick4.Values)
            {
                exists = lottery.ExistsScheduleByUniqueId(uniqueDrawingId);
                if (exists) return true;
            }
            foreach (var lottery in pick2.Values)
            {
                exists = lottery.ExistsScheduleByUniqueId(uniqueDrawingId);
                if (exists) return true;
            }
            foreach (var lottery in pick5.Values)
            {
                exists = lottery.ExistsScheduleByUniqueId(uniqueDrawingId);
                if (exists) return true;
            }
            return false;
        }

        internal class ScheduleWithGameType:Objeto
        {
            internal string Hour { get; set; }
            internal IEnumerable<string> GameTypes { get; set; }
        }

        internal class TicketsFinder : Objeto
        {
            List<Ticket> ticketsFound = new List<Ticket>();
            string gameType;
            byte pickFound;
            internal byte Pick 
            {
                get 
                {
                    if (!ticketsFound.Any()) throw new GameEngineException($"Tickets collection is empty");
                    if (pickFound == 0) throw new GameEngineException($"Pick is not assigned");
                    return pickFound;
                }
            }

            internal void FindAndLoad(int ticketNumber, IEnumerable<LotteryPick<Pick2>> pick2, IEnumerable<LotteryPick<Pick3>> pick3, IEnumerable<LotteryPick<Pick4>> pick4, IEnumerable<LotteryPick<Pick5>> pick5)
            {
                if (ticketNumber <= 0) throw new GameEngineException($"{nameof(ticketNumber)} {ticketNumber} must be greater than 0");

                foreach (var lotteryPair in pick3)
                {
                    var lotteryTickets = lotteryPair.SearchTicketByNumber(ticketNumber);
                    if (lotteryTickets.Any())
                    {
                        Add(lotteryTickets);
                        pickFound = 3;
                    }
                }

                if (IsEmpty())
                {
                    foreach (var lotteryPair in pick4)
                    {
                        var lotteryTickets = lotteryPair.SearchTicketByNumber(ticketNumber);
                        if (lotteryTickets.Any())
                        {
                            Add(lotteryTickets);
                            pickFound = 4;
                        }
                    }
                }
                if (IsEmpty())
                {
                    foreach (var lotteryPair in pick2)
                    {
                        var lotteryTickets = lotteryPair.SearchTicketByNumber(ticketNumber);
                        if (lotteryTickets.Any())
                        {
                            Add(lotteryTickets);
                            pickFound = 2;
                        }
                    }
                }
                if (IsEmpty())
                {
                    foreach (var lotteryPair in pick5)
                    {
                        var lotteryTickets = lotteryPair.SearchTicketByNumber(ticketNumber);
                        if (lotteryTickets.Any())
                        {
                            Add(lotteryTickets);
                            pickFound = 5;
                        }
                    }
                }
            }

            internal bool IsEmpty()
            {
                return ticketsFound.Count == 0;
            }

            void Add(IEnumerable<Ticket> tickets)
            {
                if (!tickets.Any()) throw new GameEngineException($"Tickets collection is empty");
                if (string.IsNullOrWhiteSpace(gameType)) 
                {
                    gameType = tickets.First().Lottery.GameType();
                }

                foreach (var ticket in tickets)
                {
                    var currentGameType = ticket.Lottery.GameType();
                    if (gameType != currentGameType) throw new GameEngineException($"{nameof(gameType)} expected is {gameType} but ticket has {nameof(gameType)} {currentGameType}");
                    ticketsFound.Add(ticket);
                }
            }

            internal bool AreAllPending()
            {
                return ticketsFound.All(ticket => ticket.IsPending());
            }

            internal bool BelongToCustomer(string accountNumber)
            {
                return ticketsFound.All(ticket => ticket.Player.AccountNumber == accountNumber);
            }

            internal bool BelongToGameType(string gameType)
            {
                return this.gameType == gameType;
            }

            internal string GenerateDescription()
            {
                if (!ticketsFound.Any()) throw new GameEngineException($"Tickets collection is empty");

                string numbersAsText = null;
                string gameTypeName = null;
                HashSet<Schedule> schedules = new HashSet<Schedule>();
                HashSet<DateTime> drawDates = new HashSet<DateTime>();
                foreach (var ticket in ticketsFound)
                {
                    if (string.IsNullOrWhiteSpace(numbersAsText)) numbersAsText = ticket.AsString();
                    if (numbersAsText != ticket.AsString()) throw new GameEngineException($"Numbers between tickets for the same purchase cannot be different");
                    if (string.IsNullOrWhiteSpace(gameTypeName)) gameTypeName = GameTypeName(ticket);
                    if (gameTypeName != GameTypeName(ticket)) throw new GameEngineException($"Game type between tickets for the same purchase cannot be different");

                    schedules.Add(ticket.Schedule());
                    drawDates.Add(ticket.DrawDate);
                }

                var drawings = string.Join(',', schedules.Select(schedule=>$"{schedule.Lottery.State.Abbreviation} {schedule.Hour.AsString()}"));
                var dates = string.Join(',', drawDates.Select(date => date.ToString("MM/dd/yyyy")));
                return $"Lotto Pick {Pick} {gameTypeName} Drawings: {drawings} Dates: {dates} Numbers: {numbersAsText}";
            }

            string GameTypeName(Ticket ticket)
            {
                switch (ticket)
                {
                    case TicketPick3Straight:
                    case TicketPick4Straight:
                    case TicketPick2Straight:
                    case TicketPick5Straight:
                        return "Straight";
                    case TicketPick3Boxed:
                    case TicketPick4Boxed:
                    case TicketPick2Boxed:
                    case TicketPick5Boxed:
                        return "Boxed";
                    default:
                        throw new GameEngineException("Unknown game type");
                }
            }

            internal IEnumerable<TicketByPrize> GetPendingTicketsByPrize()
            {
                var result = Enumerable.Empty<TicketByPrize>();
                foreach (var ticket in ticketsFound)
                {
                    if (ticket.IsUnprized() && ticket.IsPending())
                    {
                        result = result.Concat(ticket.TicketsByPrize());
                    }
                }
                return result;
            }
        }
    }
}
