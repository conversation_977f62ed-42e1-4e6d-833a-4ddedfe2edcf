﻿namespace GamesEngine.RealTime
{

	public enum MessageType
	{
		CONNECTION = 0, MESSAGE = 1, SUBSCRIPTION = 2
	}

	public enum PlatformEventType
	{
		CHANGED_SPREAD_LINE = 1, 
		CHANGED_MONEY_LINE = 2,
		CHANGED_MONEYDRAW_LINE = 21,
		CHANGED_TOTAL_POINTS_LINE = 3, 
		CHANGED_OVER_UNDER_LINE = 4,
		CHANGED_YES_NO_LINE = 5,
		CHANGED_FIXED_LINE = 6,

		PLACED_DISBURSMENT = 7,

		TAGGED_SPREAD_LINE = 8,
		TAGGED_MONEY_LINE = 9,
		TAGGED_MONEYDRAW_LINE = 22,
		TAGGED_TOTAL_POINTS_LINE = 10,
		TAGGED_OVER_UNDER_LINE = 11,
		TAGGED_YES_NO_LINE = 12,
		TAGGED_FIXED_LINE = 13,

		PUBLISHED_SPREAD_LINE = 14,
		PUBLISHED_MONEY_LINE = 15,
		PUBLISHED_MONEYDRAW_LINE = 20,
		PUBLISHED_TOTAL_POINTS_LINE = 16,
		PUBLISHED_OVER_UNDER_LINE = 17,
		PUBLISHED_YES_NO_LINE = 18,
		PUBLISHED_FIXED_LINE = 19,

		SUSPENDED_LINE = 50, 
		CANCELED_LINE = 51, 
		RESUME_LINE = 52, 

		STARTED_GAME = 80,
		ENDED_GAME = 81,
		CANCELLED_GAME = 82,

		MOVED_UP_LINE = 100,
		MOVED_DOWN_LINE = 101,

		CHANGED_TO_REGRADED = 140,
		CHANGED_TO_GRADED = 141,
		CHANGED_TO_NO_ACTION = 142,

		CHANGED_SCORE = 170,
		CHANGED_PERIOD = 171,

		FIRST_PUBLISHED_LINE = 210,

		GAME_QUITTED = 899,
		CHANGED_RISK = 900,
		WINNER_TICKET = 1000,

		RECEIVING_GIFT_CARD = 1010,

        STARTUP_MESSAGE = 1100,
        NEW_STREAK_DAY = 1101,
        CONSECUTIVE_STREAK_DAYS = 1102,
        LOST_LEVEL = 1103,
        LOST_STREAK = 1104,
        NEW_STREAK = 1105,
        END_NOTIFICATION = 1106,
        END_COMING_SOON = 1107,
        REMAINING_END = 1108,
        START_COMING_SOON = 1109,
        SPIN_REWARD = 1110,
        LEVEL_UP = 1111,

        CASHBACK_REWARD = 1113,

		SPINKICK_PRIZE = 1200
    }

	public class WebSocketMessage
	{
		public MessageType Type { get; set; }
		public object Data { get; set; }
		public PlatformEventType EventType { get; set; }
	}

}
