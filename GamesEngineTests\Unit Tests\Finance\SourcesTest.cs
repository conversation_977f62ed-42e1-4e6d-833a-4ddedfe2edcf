﻿using GamesEngine.Business;
using GamesEngine.Finance;
using GamesEngine.PurchaseOrders;
using GamesEngine.Settings;
using GamesEngineMocks;
using GamesEngineTests.Unit_Tests.Games.Lotto;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Unit.Games.Tools;
using static GamesEngine.Finance.Currencies;

namespace GamesEngineTests.Unit_Tests.Finance
{
    [TestClass]
    public class SourcesTest
    {
        [TestMethod]
        public void ListSources()
        {
            Company company = new Company();
            CurrenciesTest.AddCurrencies();
            Movements.Storage = new MovementStorageMemory();
            bool itIsThePresent = true;
            DateTime now = DateTime.Now;

            AtAddress atAddress = new AtAddress($"*********");
            Source source1 = atAddress.CreateSource(itIsThePresent, now, 1, CODES.BTC);
            Source source2 = atAddress.CreateSource(itIsThePresent, now, 2, CODES.USDT);

            var sources = atAddress.ListSources();
            Assert.AreEqual(2, sources.Count());
            Assert.AreEqual(source1, sources.ElementAt(0));
            Assert.AreEqual(source2, sources.ElementAt(1));
        }

        [TestMethod]
        public void SourceName()
        {
            Company company = new Company();
            CurrenciesTest.AddCurrencies();
            company.Sales.CreateStore(1, "Test Store").MakeCurrent();
            var balancesList = company.CustomerBalancesList;
            bool itIsThePresent = true;
            DateTime now = DateTime.Now;

            AtAddress atAddress = new AtAddress($"*********");
            Assert.AreEqual("Campaign FP", balancesList.SourceName(100, "FP"));
            Source source1 = atAddress.GetOrCreateSource(itIsThePresent, now, 100, CODES.FP.ToString(), "Campaign test lotto 900");
            Assert.AreEqual("Campaign test lotto 900", balancesList.SourceName(100, "FP"));
        }

        [TestMethod]
        public void bug7754()
        {
            CurrenciesTest.AddCurrencies();
            Movements.Storage = new MovementStorageMemory();
            QueueInMemory queue = new QueueInMemory();
            Integration.Kafka = queue;
            Integration.SavePreviousSettings(tempUseKafka: false, tempUseKafkaForAuto: false);

            var msg = new DepositMessage("670582￺2￺USD￺1￺30￺akai admin￺Prueba2 - 12￺2-*************￺USD￺0￺-￺3￺Manual");
            Assert.AreEqual("USD", msg.AccountNumber);
            Assert.AreEqual("670582", msg.AtAddress);
            Assert.AreEqual("USD", msg.Currency.ToString());
            Assert.AreEqual(1, msg.StoreId);

            int authorization = 0;
            const string mock = "Mocks";
            var actor = new RestAPISpawnerActor(new CashierMocks(), string.Empty, mock, mock);
            PaymentChannels.Deposit(false, DateTime.Now, actor, msg, out authorization);
        }

        [TestMethod]
        public void ValidCurrencies()
        {
            Company company = new Company();
            company.Sales.CreateStore(1, "Test Store").MakeCurrent();
            var balancesList = company.CustomerBalancesList;

            var currencies = balancesList.ValidCurrencies();
            Assert.AreEqual(2, currencies.Count());
            Assert.AreEqual(CODES.FP.ToString(), currencies.First().CurrencyAsText);
        }

        [TestMethod]
        public void Accredit_bug8489()
        {
            Company company = new Company(); CurrenciesTest.AddCurrencies();
            Movements.Storage = new MovementStorageMemory();
            //Integration.UseKafka = true;
            QueueInMemory queue = new QueueInMemory();
            Integration.Kafka = queue;
            var store = company.Sales.CreateStore(1, "Test Store");
            store.MakeCurrent();

            AtAddress atAddress = new AtAddress("TST0");
            Assert.IsNull(atAddress.BalanceOrNull(Currencies.CODES.FP));
            Balance balanceInFP = atAddress.CreateBalanceIfNotExists(Coinage.Coin(Currencies.CODES.FP));
            bool itIsThePresent = false;
            Source source = atAddress.CreateSource(itIsThePresent, DateTime.Now, 4, Coinage.Coin(Currencies.CODES.FP));
            var documentNumber = "*********";
            source.Accredit(itIsThePresent, DateTime.Now, new FreePlay(5.4m), "N/A", documentNumber, store, "concept", "Ref", "FP");
            Assert.IsNotNull(atAddress.BalanceOrNull(Currencies.CODES.FP));
        }
    }
}
