using Elastic.Apm.AspNetCore;
using ExternalServices;
using GamesEngine;
using GamesEngine.Custodian;
using GamesEngine.Exchange;
using GamesEngine.MessageQueuing;
using GamesEngine.RealTime;
using GamesEngine.Settings;
using GamesEngineMocks;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.HttpOverrides;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.OpenApi.Models;
using NBitcoin;
using Newtonsoft.Json;
using Puppeteer.EventSourcing;
using System;
using System.Collections.Generic;
using System.Runtime.Serialization;
using System.Text;
using System.Threading;
using static GamesEngine.Exchange.BtcProcessorInstance;
using Settings = btcblockchain.Settings;

namespace GuardianAPI
{
	public class Startup
	{
        public Startup(IWebHostEnvironment env, IConfiguration configuration)
        {
            var builder = new ConfigurationBuilder()
                .SetBasePath(env.ContentRootPath)
                .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true)
                .AddJsonFile("customization/appsettings.accounting.json", optional: true)
                .AddJsonFile("customization/appsettings.security.json", optional: true)
                .AddJsonFile("customization/appsettings.kafka.json", optional: true)
                .AddJsonFile("customization/appsettings.error_emails.json", optional: true)
                .AddJsonFile("customization/appsettings.apm.json", optional: true)
                .AddJsonFile("secrets/appsettings.secrets.json", optional: true);
            Configuration = builder.Build();
            Environment = env;
        }

        public IConfiguration Configuration { get; }
        public IWebHostEnvironment Environment { get; }

        // This method gets called by the runtime. Use this method to add services to the container.
        public void ConfigureServices(IServiceCollection services)
		{
			int minWorker, minIOC;
			ThreadPool.GetMinThreads(out minWorker, out minIOC);
			int maxWorker, maxIOC;
			ThreadPool.GetMaxThreads(out maxWorker, out maxIOC);

			Console.WriteLine($@" minWorker:{minWorker} minIOC:{minIOC} maxWorker:{maxWorker} maxIOC:{maxIOC}");

			/*Security*/
			Security.Configure(services, Configuration);
			/*Security*/

			////////services.AddControllers();

			//services.AddSwaggerGen(options =>
			//{
			//	var groupName = "v1";

			//	options.SwaggerDoc(groupName, new OpenApiInfo
			//	{
			//		Title = $"Guardian {groupName}",
			//		Version = groupName,
			//		Description = "Guardian API"
			//	});
			//});

			services.AddMvc(options => options.EnableEndpointRouting = false).AddNewtonsoftJson();
			services.AddSwaggerGen(c =>
			{
				c.SwaggerDoc("v1", new OpenApiInfo { Title = "Guardian API", Version = "v1" });
			});
			services.AddSingleton<IHttpContextAccessor, HttpContextAccessor>();

			//services.AddWebSocketManager();

			var biIntegration = Configuration.GetSection("BIIntegration");

			Integration.Configure(KafkaMessage.Prefix.withExchangePrefix, biIntegration);
			Integration.ConfigureAPM(Configuration);

			var messageOriginId = Configuration.GetValue<string>("MessageOriginId");
			if (string.IsNullOrEmpty(messageOriginId) && !int.TryParse(messageOriginId, out _)) throw new Exception("MessageOriginId its requeried in the appsettings.");
			MessageCatalog.AssingMessageOriginId(Convert.ToInt32(messageOriginId));
		}

		// This method gets called by the runtime. Use this method to configure the HTTP request pipeline.
		public void Configure(IApplicationBuilder app, IWebHostEnvironment env, IServiceProvider serviceProvider)
		{
			if (Integration.UseAPM)
			{
				app.UseElasticApm(Configuration);
			}
			//if (env.IsDevelopment())
			//{
			app.UseDeveloperExceptionPage();
			app.UseSwagger();
			app.UseSwaggerUI(c =>
			{
				c.SwaggerEndpoint("/swagger/v1/swagger.json", "Guardian API V1");
				//c.RoutePrefix = string.Empty;
			});
			app.UseDeveloperExceptionPage();
			//}

			////TODO cargar las billeteras en revisi[on
			//Policy policy1 = new Policy("001", "(BTCAmountInDecimal <= 0.00540000m && Confirmations >= 2)");
			//Policy policy2 = new Policy("002", "(BTCAmountInDecimal <= 0.00011000m && Confirmations >= 1500)");
			//Policy policy3 = new Policy("003", "(BTCAmountInDecimal > 0.000110001m && Confirmations >= 6000)");

			////TODO Si es el 2do dep?sito pongale draft en exchange y ha pasado m?s 10min DRAFt ->
			//ApprovalPoliciesManager.Add(new Policy[] { policy1, policy2, policy3 });

			//////Settings.ConfigureStorage("persistsecurityinfo=True;port=3306;Server=*************;Database=bitcoinv4;user id=root;password=*********;SslMode=none");
			////Settings.ConfigureStorage("persistsecurityinfo=True;port=3307;Server=nodo.qa4.ncubo.com;Database=bitcoinv4;user id=root;password=*********;SslMode=none");
			//Settings.ConfigureStorage("persistsecurityinfo=True;port=3307;Server=nodo.qa9.ncubo.com;Database=guardianrenan;user id=root;password=*********;SslMode=none");
			Settings.ConfigureStorage(Integration.Db.MySQL);

			Network network = Network.TestNet;
			////Settings.ConfigureMiddlewares(
			////	network,
			////	"myuser",
			////	"mypassword",
			////	"http://localhost:18332/wallet/Pruebas de Integracion",
			////	"*************:18333",
			//// "Esta es un seed fake");
			//Settings.ConfigureMiddlewares(
			//	network,
			//	"myuser",
			//	"mypassword",
			//	"http://nodo.qa4.ncubo.com:18332",
			//	"nodo.qa4.ncubo.com:28333",
			//	"Esta es un seed fake");

			//BTCProcessorInstanceConfiguration configuration = new BTCProcessorInstanceConfiguration(
			//"myuser",
			//"mypassword",
			//"http://localhost:18332/wallet/Pruebas de Integracion",
			//"*************:18333",
			//"Esta es un seed fake");
			//PaymentProcessorInstance processor = new BtcProcessorInstance("4ec9fb77-31ef-4700-bc4f-2c3380211c26", "Testnet BTC wallet processor.", configuration);

			BTCProcessorInstanceConfiguration configuration = new BTCProcessorInstanceConfiguration(
			"myuser",
			"mypassword",
			"http://nodo.qa4.ncubo.com:18332",
			"nodo.qa4.ncubo.com:28333",
			"Esta es un seed fake");
			BtcProcessorInstance processor = new BtcProcessorInstance("4ec9fb77-31ef-4700-bc4f-2c3380211c26", "Testnet BTC wallet processor.", configuration);
			//Settings.ConfigureMiddlewares(network, processor);

			//FallbackRateResponse.Configure(0.00020000m);

			var sectionDairy = Configuration.GetSection("DBDairy");
			var mySQL = sectionDairy.GetSection("ConnectionStrings").GetValue<string>("MySQL");
			var sqlServer = sectionDairy.GetSection("ConnectionStrings").GetValue<string>("SQLServer");
			var dbSelected = sectionDairy.GetValue<string>("DBSelected");
			var scriptBeforeRecovering = Configuration.GetValue<string>("ActionsBeforeRecovering");

			DBDairy dbDairy = new DBDairy();
			dbDairy.DBSelected = dbSelected;
			Integration.DbDairy = dbDairy;

			//app.UseWebSockets();
			//WebSocketImplementation websocket = serviceProvider.GetService<WebSocketImplementation>();
			//app.MapSockets("/ws/notifier", websocket);

			if (dbSelected == DatabaseType.MySQL.ToString())
			{
				GuardianManagerAPI.GuardianManager.EventSourcingStorage(DatabaseType.MySQL, mySQL, scriptBeforeRecovering, AccountingSettings.NeedsUniqueIdentifierForPaymentHub);

				//Settings.ConfigureDisbursementMonitor(websocket);
			}
			else if (dbSelected == DatabaseType.SQLServer.ToString())
			{
				GuardianManagerAPI.GuardianManager.EventSourcingStorage(DatabaseType.SQLServer, sqlServer, scriptBeforeRecovering, AccountingSettings.NeedsUniqueIdentifierForPaymentHub);

				//Settings.ConfigureDisbursementMonitor(websocket);
			}
			else if (!String.IsNullOrWhiteSpace(dbSelected))
			{
				throw new Exception($"There is no connection for {dbSelected}");
			}
			else
			{
				//Settings.ConfigureDisbursementMonitor(websocket);

				//Integration.Kafka.OffSetResetToLatest();
				int numberOfTheMockConfigured = Convert.ToInt32(Configuration["MockToStart"]);

				var DOTNET_RUNNING_IN_CONTAINER = bool.Parse(System.Environment.GetEnvironmentVariable("DOTNET_RUNNING_IN_CONTAINER") ?? "false");
				if (DOTNET_RUNNING_IN_CONTAINER)
					GuardianManagerAPI.GuardianManager.EventSourcingStorage(DatabaseType.MySQL, mySQL, scriptBeforeRecovering, AccountingSettings.NeedsUniqueIdentifierForPaymentHub);

				RunMock(GuardianManagerAPI.GuardianManager.Actor, numberOfTheMockConfigured);
			}

			Settings.Storage.Create();

			//List<Policy> unStoredPolicies = ApprovalPoliciesManager.CheckPolicies();
			//Settings.Storage.Save(unStoredPolicies.ToArray());

			app.UseMvc();

			ForwardedHeadersOptions options = new ForwardedHeadersOptions();
			options.ForwardedHeaders = ForwardedHeaders.XForwardedFor;
			app.UseForwardedHeaders(options);

			////////////////////if (env.IsDevelopment())
			////////////////////{
			////////////////////	app.UseDeveloperExceptionPage();
			////////////////////}

			////////////////////app.UseHttpsRedirection();

			////////////////////app.UseRouting();

			////////////////////app.UseAuthorization();

			////////////////////app.UseEndpoints(endpoints =>
			////////////////////{
			////////////////////	endpoints.MapControllers();
			////////////////////});

			////////////////////// Enable middleware to serve generated Swagger as a JSON endpoint.
			////////////////////app.UseSwagger();

			////////////////////app.UseSwaggerUI(c =>
			////////////////////{
			////////////////////	c.SwaggerEndpoint("/swagger/v1/swagger.json", "Guardian API.");
			////////////////////});

			PaymentProcessorInstances paymentProcessors = new PaymentProcessorInstances();
			paymentProcessors.Add(processor);

			Consumers.CreateConsumerForTopics();

			var result = GuardianManagerAPI.GuardianManager.PerformQry($@"
                {{
					success = company.System.PaymentProcessor.Load(false, Now, customSettings);
                    print success success;

					accounts = guardian.Accounts();
                    print accounts.NextAccountId() accountId;
					for (processors:guardian.PaymentProcessorsWithoutAccounts())
					{{
						processor = processors;
						print processor.Entity.Name entity;
						print processor.PaymentMethodAsText paymentMethod;
						print processor.Transactions.TransactionsAsText transactionType;
						print processor.CurrencyIso4217Code currencyCode;
					}}
                }}
                ");

			if (!(result is OkObjectResult)) throw new GameEngineException($"{result}");
			OkObjectResult o = (OkObjectResult)result;
			string json = o.Value.ToString();
			var processorsWithoutAccounts = JsonConvert.DeserializeObject<ProcessorsWithoutAccounts>(json);

			var commandsToCreateAccounts = new StringBuilder();
			if (processorsWithoutAccounts != null && processorsWithoutAccounts.Processors?.Count > 0)
			{
				int accountId = processorsWithoutAccounts.AccountId;
				foreach (var processorWithoutAccounts in processorsWithoutAccounts.Processors)
				{
					commandsToCreateAccounts.Append("accounts.Create(itIsThePresent, ").
						Append(accountId).Append(", '").
						Append(processorWithoutAccounts.ProcessorKey).Append("', '").
						Append(processorWithoutAccounts.ProcessorAccount(accountId)).Append("', '").
						Append(processorWithoutAccounts.CurrencyCode).Append("');");
					accountId++;
				}
				result = GuardianManagerAPI.GuardianManager.PerformCmd($@"
				accounts = guardian.Accounts();
                {commandsToCreateAccounts}
                ");

				if (!(result is OkObjectResult)) throw new GameEngineException($"{result}");
			}
		}

		void RunMock(Puppeteer.EventSourcing.Actor actor, int index = -1)
		{
			switch (index)
			{
				case 0:
					GuardianMocks.QaBTCWallet(actor);
					break;
				default:
					throw new Exception($"The mock {index} its not implemented yet.");
			}
		}
	}
}
