﻿using GamesEngine.Bets;
using GamesEngine.Business;
using GamesEngine.Business.Marketing;
using GamesEngine.Finance;
using GamesEngine.Marketing.Campaigns.Rewarders;
using GamesEngine.PurchaseOrders;
using GamesEngine.RealTime;
using GamesEngine.RealTime.Events;
using GamesEngine.Resources;
using GamesEngine.Settings;
using Puppeteer.EventSourcing.Libraries;
using System;
using System.Collections.Generic;
using System.Linq;

namespace GamesEngine.Marketing.Campaigns
{
	internal class GiftCard : BudgetPromotion
	{
        protected DateTime startDate;
        protected DateTime endDate;

        private string descriptor;
		PreferredResources preferredResources;

		[Obsolete]
        public GiftCard(bool itIsThePresent, Company company, int promotionNumber, string name, Budget budget, DateTime creationDate) : base(company, promotionNumber, creationDate, name, budget)
        {
            if (company == null) throw new ArgumentNullException(nameof(company));
            if (budget == null) throw new ArgumentNullException(nameof(budget));
            if (promotionNumber < 0) throw new ArgumentNullException(nameof(promotionNumber));
            if (string.IsNullOrWhiteSpace(name)) throw new ArgumentNullException(nameof(name));

            if (itIsThePresent)
            {
                MarketingStorage marketingStorage = MarketingStorageCampaignInstance();
                if (marketingStorage != null)
                {
                    marketingStorage.StoreCampaign(promotionNumber, creationDate, name, null, budget.CurrencyCode, InitialBudget, IsUnlimitedBudget, creationDate, DateTime.MinValue, 0, name, DateTime.MinValue, nameof(GiftCard), nameof(GiftCard), nameof(GiftCard));
                }
            }
        }

		public GiftCard(bool itIsThePresent, Company company, int promotionNumber, string name, Budget budget, DateTime creationDate, DateTime startDate, DateTime endDate) : base(company, promotionNumber, creationDate, name, budget)
		{
			if (company == null) throw new ArgumentNullException(nameof(company));
            if (budget == null) throw new ArgumentNullException(nameof(budget));
            if (promotionNumber < 0) throw new ArgumentNullException(nameof(promotionNumber));
            if (string.IsNullOrWhiteSpace(name)) throw new ArgumentNullException(nameof(name));
			if (startDate == default(DateTime)) throw new ArgumentNullException(nameof(startDate));
			if (endDate == default(DateTime)) throw new ArgumentNullException(nameof(endDate));
			if (startDate > endDate) throw new GameEngineException($"{nameof(startDate)} must be lower than {nameof(endDate)}");

            if (itIsThePresent)
			{
                MarketingStorage marketingStorage = MarketingStorageCampaignInstance();
                if (marketingStorage != null)
				{
                    marketingStorage.StoreCampaign(promotionNumber, creationDate, name, null, budget.CurrencyCode, InitialBudget, IsUnlimitedBudget, startDate, endDate, 0, name, endDate, nameof(GiftCard), nameof(GiftCard), nameof(GiftCard));
                }
            }

			this.startDate = startDate;
			this.endDate = endDate;
		}

		internal DateTime StartDate
		{
			get
			{
                return startDate;
            }
			set
			{
				if (value == default(DateTime)) throw new ArgumentNullException(nameof(value));
				if (HasStartAndEndDate && value > endDate) throw new GameEngineException($"{nameof(value)} must be lower than {nameof(endDate)}");

                startDate = value;
			}
		}

		internal DateTime EndDate
		{
			get
			{
				return endDate;
			}
			set
			{
				if (value == default(DateTime)) throw new ArgumentNullException(nameof(value));
				if (HasStartAndEndDate && value < startDate) throw new GameEngineException($"{nameof(value)} must be greater than {nameof(startDate)}");

				endDate = value;
			}
		}
    
        internal override string CampaignType => this.GetType().Name;

		internal bool HasResources => preferredResources != null;

		internal bool HasPreferredResource
		{
			get
			{
				if (!HasResources) throw new GameEngineException($"{nameof(GiftCard)} campaign does not have resources.");

				return preferredResources.HasPreferred();
			}
		}

		internal string PreferredResourceName
		{
			get
			{
				if (!HasResources) throw new GameEngineException($"{nameof(GiftCard)} campaign does not have resources.");
				if (!HasPreferredResource) throw new GameEngineException($"{nameof(GiftCard)} campaign does not have a preferred resource.");

				return preferredResources.Preferred.Name;
			}
		}

		internal IEnumerable<LabeledResource> Resources => preferredResources.Options;


		internal string Descriptor
		{
			get
			{
				return this.descriptor;
			}
		}

		internal int CustomersCount
		{
			get
			{
				int count = beneficiaryCustomers.Count;
				return count;
			}
		}

		internal static PercentageRanges PercentageRangesDefinition { get; } = new PercentageRanges();
		internal class PercentageRanges
		{
			internal List<PercentageRange> GetAll { get; } = new List<PercentageRange>();
			public PercentageRanges()
			{
				GetAll.Add(new PercentageRange(100, 500, 0.05m));
				GetAll.Add(new PercentageRange(501, 750, 0.1m));
				GetAll.Add(new PercentageRange(751, 1000, 0.15m));
			}

			internal decimal GetPercentage(decimal amount)
			{
				foreach (var range in GetAll)
				{
					if (amount >= range.MinAmount && amount <= range.MaxAmount)
					{
						return range.Percentage;
					}
				}
				throw new GameEngineException($"{nameof(amount)} {amount} is out of range within predefined amount ranges.");
			}

			internal class PercentageRange : Objeto
			{
				internal int MinAmount { get; }
				internal int MaxAmount { get; }
				internal decimal Percentage { get; }
				internal PercentageRange(int minAmount, int maxAmount, decimal percentage)
				{
					MinAmount = minAmount;
					MaxAmount = maxAmount;
					Percentage = percentage;
				}
			}
		}

		internal decimal CalculateAmountAndDescriptorToReload(decimal amount)
		{
			if (amount < 0) throw new GameEngineException($"Deposit amount is invalid, it can not be negative.");
			if(amount < 100 || amount > 1000) throw new GameEngineException($"Deposit amount is not eligible for Reload promo. It must be between $100 to $1000.");

			decimal percentage = PercentageRangesDefinition.GetPercentage(amount);
			CalculateDescriptor(percentage);
			var resultAmount = amount * percentage;
			return Math.Round(resultAmount, 2);
		}

		internal bool IsValidAmount(decimal amount)
		{
			bool result = amount <= 5;
			return result;
		}

		internal bool HasStartAndEndDate => startDate != DateTime.MinValue && endDate != DateTime.MinValue;

		internal bool IsValidDate(DateTime date)
		{
			if (!HasStartAndEndDate) return true;

            bool result = date >= startDate && date <= endDate;
            return result;
        }

		internal override bool IsActive(DateTime now)
		{   
            return IsEnabled && IsValidDate(now);
		}

        private void CalculateDescriptor(decimal percentage)
		{
			const string DESCRIPTOR_TEXT = "FREE PLAY";
			string percentageAsText = "";
			switch (percentage)
			{
				case 0.05m:
					percentageAsText = "5";
					break;
				case 0.10m:
					percentageAsText = "10";
					break;
				case 0.15m:
					percentageAsText = "15";
					break;
			}
			this.descriptor = $"{percentageAsText}% {DESCRIPTOR_TEXT}";
		}

		internal bool HasMovementsFor(Player player)
		{
			var result = beneficiaryCustomers.Exists(player.Customer);
			return result;
		}

		internal void Give(bool itIsThePresent, Customer customer, DateTime now, decimal amount, string description, string who, Currencies.CODES currencyCode, Store store)
		{
			Give(itIsThePresent, customer, now, amount, description, who, Coinage.Coin(currencyCode), store);
		}
		internal void Give(bool itIsThePresent, Customer customer, DateTime now, decimal amount, string description, string who, string currency, Store store)
		{
			Give(itIsThePresent, customer, now, amount, description, who, Coinage.Coin(currency), store);
		}
		internal void Give(bool itIsThePresent, Customer customer, DateTime now, decimal amount, string description, string who, Coin coin, Store store)
		{
			if (customer == null) throw new ArgumentNullException(nameof(customer));
			if(now == default(DateTime)) throw new GameEngineException("Now can not be default date.");
			if(amount <= 0) throw new GameEngineException("Amount must be upper than zero.");
			if(string.IsNullOrWhiteSpace(description)) throw new ArgumentNullException(nameof(description));
			if (!IsEnabled) throw new GameEngineException($"Campaign is not enable");
			var currency = Currency.Factory(coin, amount);
			if (budget.IsExceeded(currency)) throw new GameEngineException($"{nameof(amount)} {nameof(amount)} exceeded the budget");

			if (!IsActive(now)) throw new GameEngineException($"Date {now} is not valid for this GiftCard campaign between start date {startDate} and end date {endDate}");

			budget.Disburse(currency);
			if (!beneficiaryCustomers.Exists(customer)) beneficiaryCustomers.Add(customer);
			beneficiaryCustomers.RegisterAsTheLast(customer);
			IncrementGivenTimesNumber();

            string reference = $"{base.PromotionNumber}-{now.ToString("yy")}{now.ToString("HHmmss")}{now.DayOfYear.ToString("000")}{now.ToString("yy")}";
			if (reference.Length > Movement.MAX_REFERENCE_LENGTH) reference = reference.Substring(0, Movement.MAX_REFERENCE_LENGTH);
			string accountNumber = coin.Iso4217Code;

			using (KafkaMessagesBuffer buffer = new KafkaMessagesBuffer(itIsThePresent, Integration.Kafka.TopicForDeposits))
			{
				DepositMessage deposit = new DepositMessage(
					customer.AccountNumber, 
					PromotionNumber, 
					coin, 
					store.Id, 
					amount, 
					who, 
					description,
					reference,
					accountNumber,
					WholePaymentProcessor.NoPaymentProcessor,
					string.Empty,
					customer.Player.Agent,
					Name
					);
				buffer.Send(deposit);
			}

            customer.Player.AddMessage(store, $"Congratulations! You've received {coin.Sign} {amount.ToString("N2")}");
            if (itIsThePresent)
			{
				var giftCardEvent = HasResources && HasPreferredResource ?
					new GiftCardEvent(now, amount, coin.Iso4217Code, coin.Name, customer.Player.Id, preferredResources.Preferred.Url) :
					new GiftCardEvent(now, amount, coin.Iso4217Code, coin.Name, customer.Player.Id);
				PlatformMonitor.GetInstance().WhenNewEvent(giftCardEvent);
			}
		}

		internal void TakeOut(bool itIsThePresent, Customer customer, DateTime now, decimal amount, string description, string who, Currencies.CODES currencyCode, Store store)
		{
			TakeOut(itIsThePresent, customer, now, amount, description, who, Coinage.Coin(currencyCode), store);
		}
		internal void TakeOut(bool itIsThePresent, Customer customer, DateTime now, decimal amount, string description, string who, string currency, Store store)
		{
			TakeOut(itIsThePresent, customer, now, amount, description, who, Coinage.Coin(currency), store);
		}
		internal void TakeOut(bool itIsThePresent, Customer customer, DateTime now, decimal amount, string description, string who, Coin coin, Store store)
		{
			if (customer == null) throw new ArgumentNullException(nameof(customer));
			if (now == default(DateTime)) throw new GameEngineException("Now can not be default date.");
			if (amount <= 0) throw new GameEngineException("Amount must be upper than zero.");
			if (string.IsNullOrWhiteSpace(description)) throw new ArgumentNullException(nameof(description));

			var currency = Currency.Factory(coin, amount);
			budget.Save(currency);

			string reference = $"{base.PromotionNumber}-{now.ToString("yy")}{now.ToString("HHmmss")}{now.DayOfYear.ToString("000")}{now.ToString("yy")}";
			if (reference.Length > Movement.MAX_REFERENCE_LENGTH) reference = reference.Substring(0, Movement.MAX_REFERENCE_LENGTH);
			string accountNumber = coin.Iso4217Code;

			using (KafkaMessagesBuffer buffer = new KafkaMessagesBuffer(itIsThePresent, Integration.Kafka.TopicForWithdrawals))
			{
				WithdrawMessage deposit = new WithdrawMessage(
					customer.AccountNumber,
					base.PromotionNumber,
					coin,
					store.Id,
					amount,
					who,
					description,
					reference: reference,
					accountNumber,
					WholePaymentProcessor.NoPaymentProcessor,
					string.Empty,
					customer.Player.Agent
					);
				buffer.Send(deposit);
			}
		}

		IEnumerable<LabeledResource> GenerateLabeledResources(List<string> names, List<string> urls)
		{
			var resources = new List<LabeledResource>();
			for (int index = 0; index < names.Count; index++)
			{
				if (string.IsNullOrWhiteSpace(names[index])) throw new ArgumentNullException(nameof(names));
				if (string.IsNullOrWhiteSpace(urls[index])) throw new ArgumentNullException(nameof(urls));
				resources.Add(new LabeledResource(names[index], urls[index]));
			}
			return resources;
		}

		internal void AddResources(List<string> names, List<string> urls)
		{
			if (names == null || names.Count == 0) throw new ArgumentNullException(nameof(names));
			if (urls == null || urls.Count == 0) throw new ArgumentNullException(nameof(urls));
			if (names.Count != urls.Count) throw new GameEngineException($"{nameof(names)} and {nameof(urls)} does not have the same number of elements");

			var resources = GenerateLabeledResources(names, urls);
			preferredResources = new PreferredResources(resources);
		}

		internal void UpdateResources(List<string> names, List<string> urls)
		{
			if (names == null || names.Count == 0) throw new ArgumentNullException(nameof(names));
			if (urls == null || urls.Count == 0) throw new ArgumentNullException(nameof(urls));
			if (names.Count != urls.Count) throw new GameEngineException($"{nameof(names)} and {nameof(urls)} does not have the same number of elements");

			var resources = GenerateLabeledResources(names, urls);
			if (HasResources)
			{
				preferredResources.Update(resources);
			}
			else
			{
				preferredResources = new PreferredResources(resources);
			}
		}

		internal void ChoosePreferredResource(string chosenResource)
		{
			if (!preferredResources.Contains(chosenResource)) throw new GameEngineException($"{nameof(chosenResource)} '{chosenResource}' is not an option");

			preferredResources.Choose(chosenResource);
		}

		internal void ClearResources()
		{
			preferredResources = null;
		}

		internal void ClearPreferred()
		{
			preferredResources?.ClearPreferred();
		}
	}
}
