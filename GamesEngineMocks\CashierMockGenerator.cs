﻿using GamesEngine;
using GamesEngine.Finance;
using GamesEngine.PurchaseOrders;
using GamesEngine.Settings;
using Puppeteer.EventSourcing;
using System;
using System.Collections.Generic;
using System.Text;
using town.connectors.drivers;

namespace GamesEngineMocks
{
    public class CashierMocks : MockLoader
    {
        internal override void Load(int mockToStart, CashierActor actor)
        {
			switch (mockToStart)
			{
				case 0:
					CashierMockGenerator.Exchangemock1(actor, actor.Name);
					break;
				case 1:
					CashierMockGenerator.LoadMocks(actor, actor.Name);
					break;
				case 2:
					CashierMockGenerator.LRCurrencyMock(actor, actor.Name);
					break;
				default:
					throw new Exception($"There is no mock for {Integration.MockToStart}");
			}
		}
    }

    public class CashierMockGenerator
    {

		internal static CashierActor Exchangemock1(CashierActor actor, string atAddressNumber)
		{
			if (!atAddressNumber.Equals(RestAPISpawnerActor.GENERAL)) atAddressNumber = atAddressNumber.Substring(1);
			if (atAddressNumber.Equals(RestAPISpawnerActor.GENERAL, StringComparison.OrdinalIgnoreCase))
			{
				var x = actor.PerformCmdAsync($@"
				company = Company();
				companyIDf2401f84xa94ax4db3xaec4x83f5361730ae = company;
				balancesList = company.CustomerBalancesList;

				company.Sales.CreateStore(1,'Picks Store');
				company.Sales.CreateStore(2,'Brackets Store 2019');
				company.Sales.CreateStore(3,'Brackets Store 2020');
				company.Sales.CreateStore(4,'Fiero Wallet');
				company.Sales.CreateStore(5,'Ladybet Store');
				company.Sales.CreateStore(6,'Brackets Store 2021');
				company.Sales.CreateStore(7,'Keno Store');
				if (!company.System.Coins.ExistsIsoCode('FP'))
				{{
					coin = company.System.Coins.Add(0, 'FP', 'FP', 2, 'F', 'free play', {CoinType.Digital});
					coin.Visible = true;
					coin.Enabled = true;
				}}
				if (!company.System.Coins.ExistsIsoCode('USD'))
				{{
					coin = company.System.Coins.Add(2, 'USD', '$', 2, '$', 'dollar', {CoinType.Fiat});
					coin.Visible = true;
					coin.Enabled = true;
				}}
				if (!company.System.Coins.ExistsIsoCode('BTC'))
				{{
					coin = company.System.Coins.Add(3, 'BTC', 'BTC', 8, '₿', 'bitcoin', {CoinType.Crypt});
					coin.Visible = true;
					coin.Enabled = true;
				}}
				if (!company.System.Coins.ExistsIsoCode('ETH'))
				{{
					coin = company.System.Coins.Add(7, 'ETH', 'ETH', 8, 'Ξ', 'ethereum', {CoinType.Crypt});
				}}

				marketplace = Marketplace(company, 'CR');
				cartagoAgent = marketplace.AddAgent('Cartago');
				agent1 = marketplace.AddAgent('1');
				marketplace.AddAgent('San José');
				marketplace.AddAgent('Heredia');

				customSettings = CustomSettingsCollection(company);
				{{
					company.System.Entities.Add(1, 'Apolo');
					artemisEntity = company.System.Entities.Add(2, 'Artemis');
					artemisEntity.Visible = true;
					artemisEntity.Enabled = true;
					zeusEntity = company.System.Entities.Add(3, 'Zeus');
					fieroEntity = company.System.Entities.Add(4, 'Fiero');
					fieroEntity.Visible = true;
					fieroEntity.Enabled = true;
					hadesEntity = company.System.Entities.Add(5, 'Hades');
					hadesEntity.Visible = true;
					hadesEntity.Enabled = true;
					consignmentEntity = company.System.Entities.Add(6, 'Consignment');
					consignmentEntity.Visible = true;
					consignmentEntity.Enabled = true;

					company.System.Tenants.Add(1, 'Apolo');
					artemisTenant = company.System.Tenants.Add(2, 'Artemis');
					zeusTenant = company.System.Tenants.Add(3, 'Zeus');
					insiderTenant = company.System.Tenants.Add(4, 'Insider');
					hadesTenant = company.System.Tenants.Add(5, 'Hades');

					asiPM = company.System.PaymentMethods.Add(1, 'A.S.I.');
					dgsPM = company.System.PaymentMethods.Add(2, 'D.G.S.');
					dgsV2PM = company.System.PaymentMethods.Add(3, 'D.G.S. v2');
					credirCardPM = company.System.PaymentMethods.Add(4, 'Credit card');
					blockChainPM = company.System.PaymentMethods.Add(5, 'Blockchain');
					moneyTransferPM = company.System.PaymentMethods.Add(6, 'Money transfer');
					asiSimulatorPM = company.System.PaymentMethods.Add(7, 'A.S.I. Simulator');
					dsgSimulatorPM = company.System.PaymentMethods.Add(8, 'D.G.S. Simulator');
					pm = company.System.PaymentMethods.Add(9, '{PaymentMethod.Cash.ToString()}');
					pm.Visible = true;
					pm.Enabled = true;
					pm = company.System.PaymentMethods.Add(10, '{PaymentMethod.Bank.ToString()}');
					pm.Visible = true;
					pm.Enabled = true;
					pm = company.System.PaymentMethods.Add(11, '{PaymentMethod.Creditcard.ToString()}');
					pm.Visible = true;
					pm.Enabled = true;
					pm = company.System.PaymentMethods.Add(12, '{PaymentMethod.FinancialServices.ToString()}');
					pm.Visible = true;
					pm.Enabled = true;
					pm = company.System.PaymentMethods.Add(13, '{PaymentMethod.ThirdParty.ToString()}');
					pm.Visible = true;
					pm.Enabled = true;
					pm = company.System.PaymentMethods.Add(14, '{PaymentMethod.Secrets.ToString()}');
					pm.Visible = true;
					pm.Enabled = true;
					pm = company.System.PaymentMethods.Add(15, '{PaymentMethod.P2PTransfer.ToString()}');
					pm.Visible = true;
					pm.Enabled = true;
					pm.AddProvider(1,'MoneyGram');
					pm.AddProvider(2,'Rio Money Transfer');
					pm.AddProvider(3,'Sigue');
					pm.AddProvider(4,'Boss Revolution Money Transfer');
					pm.AddProvider(5,'Delgado Travel');
					pm.AddProvider(6,'Intermex International Money Transfer');
					pm.AddProvider(7,'Maxi Money Services');
					pm.AddProvider(8,'Barri Financial Group');
					pm.AddProvider(9,'La Nacional');
					pm.AddProvider(10,'Dinex');
					pm.AddProvider(11,'Remitly');

					depositTransactionType = company.System.TransactionTypes.Add(1, '{TransactionType.Deposit.ToString()}');
					depositTransactionType.Description = 'A sum of money placed in an account';
					withDrawalTransactionType = company.System.TransactionTypes.Add(2, '{TransactionType.Withdrawal.ToString()}');
					withDrawalTransactionType.Description = 'A sum of money take out of an account';
					transferTransactionType = company.System.TransactionTypes.Add(3, '{TransactionType.Transfer.ToString()}');
					transferTransactionType.Description = 'A sum of money is sent from one account to another';
					creditNoteTransactionType = company.System.TransactionTypes.Add(4, '{TransactionType.CreditNote.ToString()}');
					creditNoteTransactionType.Description = 'It is a document used by a vendor to inform the buyer of mistake on an order';
					debitNoteTransactionType = company.System.TransactionTypes.Add(5, '{TransactionType.DebitNote.ToString()}');
					debitNoteTransactionType.Description = 'It is a document used by a vendor to inform the buyer of current debt obligations';
					saleTransactionType = company.System.TransactionTypes.Add(6, '{TransactionType.Sale.ToString()}');
					saleTransactionType.Description = 'The exchange of a commodity for money';
					depositAndLockTransactionType = company.System.TransactionTypes.Add(7, '{TransactionType.Deposit.ToString()} then Lock');
					depositAndLockTransactionType.Description = 'Add and Lock money placed in an account';

					depositTransactionType.Visible = true;
					depositTransactionType.Enabled = true;
					withDrawalTransactionType.Visible = true;
					withDrawalTransactionType.Enabled = true;
					transferTransactionType.Visible = true;
					transferTransactionType.Enabled = true;
					debitNoteTransactionType.Visible = true;
					debitNoteTransactionType.Enabled = true;
					creditNoteTransactionType.Visible = true;
					creditNoteTransactionType.Enabled = true;
					saleTransactionType.Visible = true;
					saleTransactionType.Enabled = true;
					depositAndLockTransactionType.Visible = true;
					depositAndLockTransactionType.Enabled = true;

					cs = customSettings.AddFixedParameter(Now, 'CashierDriver.url', 'http://cashierapi:5000/'); 
					cs.Description = 'Url Cashier';
					cs = customSettings.AddFixedParameter(Now, 'CompanyBaseUrlServices', 'http://cashierapi:5000/'); 
					cs.Description = 'It is the base production url';
					cs = customSettings.AddFixedParameter(Now, 'TokenSystemId', '3aab83fb'); 
					cs.Description = 'App\'s token id';
					cs = customSettings.AddSecretParameter(Now, 'TokenSystemPassword', '38f845e977af7f2cd6313c5a9d35e4de7f353405100e7748ab15b410eb53d886');
					cs.Description = 'Password for token';
					cs = customSettings.AddFixedParameter(Now, 'SendWagers', true); 
					cs.Description = 'To send wagers to third party';
					cs = customSettings.AddFixedParameter(Now, 'CompanySystemId', 'test'); 
					cs.Description = 'company id';
					cs = customSettings.AddSecretParameter(Now, 'CompanySystemPassword', 'test123'); 
					cs.Description = 'password';
					cs = customSettings.AddFixedParameter(Now, 'NeedsUniqueIdentifierForPaymentHub', 'PRODUCTION_DOES_NOT_NEED_IT'); 
					cs.Description = 'Setting';
					cs = customSettings.AddFixedParameter(Now, 'CompanyClerkId', 'Administrator'); 
					cs.Description = 'Clerk id';

					cs = customSettings.AddVariableParameter('Input$Provider'); 
					cs.Description = 'Consignment provider';
					cs = customSettings.AddVariableParameter('KYC$DOB'); 
					cs.Description = 'Birthdate from KYC';
					cs = customSettings.AddSecretParameter(Now, 'KYCUsername', 'testUser1');
					cs.Description = 'KYC Username for fields';
					cs = customSettings.AddSecretParameter(Now, 'KYCPassword', '12345');
					cs.Description = 'KYC Password for fields';
				}}

				guardian = Guardian(company);
			",
				IpAddress.DEFAULT,
#pragma warning disable VSTHRD002 // Avoid problematic synchronous waits
			UserInLog.ANONYMOUS).Result;
#pragma warning restore VSTHRD002 // Avoid problematic synchronous waits
				return actor;

			}
			else if (atAddressNumber.Equals("406827", StringComparison.OrdinalIgnoreCase))
			{
				var x = actor.PerformCmdAsync($@"
				company = Company();
				companyIDf2401f84xa94ax4db3xaec4x83f5361730ae = company;
				balancesList = company.CustomerBalancesList;

				company.Sales.CreateStore(1,'Picks Store');
				company.Sales.CreateStore(2,'Brackets Store 2019');
				company.Sales.CreateStore(3,'Brackets Store 2020');
				company.Sales.CreateStore(4,'Fiero Wallet');
				company.Sales.CreateStore(5,'Ladybet Store');
				company.Sales.CreateStore(6,'Brackets Store 2021');
				company.Sales.CreateStore(7,'Keno Store');

				atAddress = AtAddress('406827');
				balance = atAddress.CreateAccountIfNotExists('USD', 'cr00020002').Balance;
				store = company.Sales.StoreById(5);
				balance.Accredit(itIsThePresent, Now, Dollar(1000), 'ccajero', '9998', store, 'Deposit USD 1000 to cr00020002', '9998');
			",
				IpAddress.DEFAULT,
#pragma warning disable VSTHRD002 // Avoid problematic synchronous waits
			UserInLog.ANONYMOUS).Result;
#pragma warning restore VSTHRD002 // Avoid problematic synchronous waits
				return actor;

			}
			else if (atAddressNumber.Equals("no562430370", StringComparison.OrdinalIgnoreCase))
			{
				var x = actor.PerformCmdAsync($@"
				company = Company();
				companyIDf2401f84xa94ax4db3xaec4x83f5361730ae = company;
				balancesList = company.CustomerBalancesList;

				company.Sales.CreateStore(1,'Picks Store');
				company.Sales.CreateStore(2,'Brackets Store 2019');
				company.Sales.CreateStore(3,'Brackets Store 2020');
				company.Sales.CreateStore(4,'Fiero Wallet');
				company.Sales.CreateStore(5,'Ladybet Store');
				company.Sales.CreateStore(6,'Brackets Store 2021');
				company.Sales.CreateStore(7,'Keno Store');

				atAddress = AtAddress('no562430370');
				balance = atAddress.CreateAccountIfNotExists('USD', 'cr00030004').Balance;
				store = company.Sales.StoreById(5);
				balance.Accredit(itIsThePresent, Now, Dollar(1000), 'ccajero', '2', store, 'Deposit $1000 to cr00030004', '2');
				balance = atAddress.CreateAccountIfNotExists('BTC', 'cr00030005').Balance;
				balance.Accredit(itIsThePresent, Now, Btc(1000), 'ccajero', '2', store, 'Deposit BTC 1000 to cr00030005', '2');
			",
				IpAddress.DEFAULT,
#pragma warning disable VSTHRD002 // Avoid problematic synchronous waits
			UserInLog.ANONYMOUS).Result;
#pragma warning restore VSTHRD002 // Avoid problematic synchronous waits
				return actor;

			}
			else if (atAddressNumber.Equals("no562430373", StringComparison.OrdinalIgnoreCase))
			{
				var x = actor.PerformCmdAsync($@"
				company = Company();
				companyIDf2401f84xa94ax4db3xaec4x83f5361730ae = company;

				company.Sales.CreateStore(1,'Picks Store');
				company.Sales.CreateStore(2,'Brackets Store 2019');
				company.Sales.CreateStore(3,'Brackets Store 2020');
				company.Sales.CreateStore(4,'Fiero Wallet');
				company.Sales.CreateStore(5,'Ladybet Store');
				company.Sales.CreateStore(6,'Brackets Store 2021');
				company.Sales.CreateStore(7,'Keno Store');

				atAddress = AtAddress('no562430373'); 

				sourceUSD = atAddress.GetOrCreateSource(true, Now, 1, USD);
				sourceBTC = atAddress.GetOrCreateSource(true, Now, 2, BTC);
				sourceETH = atAddress.GetOrCreateSource(true, Now, 3, ETH);

				balance = atAddress.CreateAccountIfNotExists('USD', 'cr00040006').Balance;
				store = company.Sales.StoreById(5);
				balance.Accredit(itIsThePresent, Now, Dollar(1000), 'ccajero', '2', store, 'Deposit $1000 to cr00040006', '2');

				balance = atAddress.CreateAccountIfNotExists('USD', 'USD').Balance;
				balance.Accredit(itIsThePresent, Now, Currency('USD',25000), 'ccajero', '3', store, 'Deposit $25000 to USD', '3');
			",
				IpAddress.DEFAULT,
#pragma warning disable VSTHRD002 // Avoid problematic synchronous waits
			UserInLog.ANONYMOUS).Result;
#pragma warning restore VSTHRD002 // Avoid problematic synchronous waits
				return actor;

			}
			else if (atAddressNumber.Equals("lo63738186257747081", StringComparison.OrdinalIgnoreCase))
			{
				var x = actor.PerformCmdAsync($@"
				company = Company();
				companyIDf2401f84xa94ax4db3xaec4x83f5361730ae = company;
				balancesList = company.CustomerBalancesList;

				company.Sales.CreateStore(1,'Picks Store');
				company.Sales.CreateStore(2,'Brackets Store 2019');
				company.Sales.CreateStore(3,'Brackets Store 2020');
				company.Sales.CreateStore(4,'Fiero Wallet');
				company.Sales.CreateStore(5,'Ladybet Store');
				company.Sales.CreateStore(6,'Brackets Store 2021');
				company.Sales.CreateStore(7,'Keno Store');

				atAddress = AtAddress('*******************');
				balance = atAddress.CreateAccountIfNotExists('USD', 'cr00010002').Balance;
				store = company.Sales.StoreById(5);
				balance.Accredit(itIsThePresent, Now, Dollar(1000), 'ccajero', '9999', store, 'Deposit $1000 to cr00010002', '9999');
			",
				IpAddress.DEFAULT,
#pragma warning disable VSTHRD002 // Avoid problematic synchronous waits
			UserInLog.ANONYMOUS).Result;
#pragma warning restore VSTHRD002 // Avoid problematic synchronous waits
				return actor;

			}
			else
			{
				var x = actor.PerformCmdAsync($@"
				company = Company();
				companyIDf2401f84xa94ax4db3xaec4x83f5361730ae = company;
				balancesList = company.CustomerBalancesList;

				company.Sales.CreateStore(1,'Picks Store');
				company.Sales.CreateStore(2,'Brackets Store 2019');
				company.Sales.CreateStore(3,'Brackets Store 2020');
				company.Sales.CreateStore(4,'Fiero Wallet');
				company.Sales.CreateStore(5,'Ladybet Store');
				company.Sales.CreateStore(6,'Brackets Store 2021');
				company.Sales.CreateStore(7,'Keno Store');

				atAddress = AtAddress('{atAddressNumber}');
			",
				IpAddress.DEFAULT,
#pragma warning disable VSTHRD002 // Avoid problematic synchronous waits
			UserInLog.ANONYMOUS).Result;
#pragma warning restore VSTHRD002 // Avoid problematic synchronous waits

				return actor;

			}
		}

		internal static CashierActor LoadMocks(CashierActor actor, string atAddressNumber)
		{
			var x = actor.PerformCmdAsync($@"
				company = Company();
				companyIDf2401f84xa94ax4db3xaec4x83f5361730ae = company;
				company.Sales.CreateStore(1,'Picks Store');
				company.Sales.CreateStore(2,'Brackets Store 2019');
				company.Sales.CreateStore(3,'Brackets Store 2020');
				company.Sales.CreateStore(4,'Fiero Wallet');
				company.Sales.CreateStore(5,'Ladybet Store');
				company.Sales.CreateStore(6,'Brackets Store 2021');
				company.Sales.CreateStore(7,'Keno Store');

				atAddress = AtAddress('{atAddressNumber}');
				manualsource = atAddress.CreateSource(itIsThePresent, Now, 1, LR);
				firsttimesource = atAddress.CreateSource(itIsThePresent, Now, 2, LR);
				rechargesource = atAddress.CreateSource(itIsThePresent, Now, 3, LR);

				store = company.Sales.StoreById(1);
				firsttimesource.Accredit(itIsThePresent, Now, LottoReward(800), 'Mau', 'ABC2240', store, 'First deposit from the book.', 'ref');
				rechargesource.Accredit(itIsThePresent, Now, LottoReward(10), 'Dave', 'ABC2241', store, 'No descrip', 'ref');
				firsttimesource.Accredit(itIsThePresent,Now, LottoReward(15.50), 'Dave', 'ABC2242', store, 'No descrip', 'ref' );
				manualsource.Accredit(itIsThePresent,Now, Dollar(10), 'Dave', 'ABC2243', store, 'No descrip', 'ref' );
				firsttimesource.Lock(itIsThePresent,Now, LottoReward(6), 'ABC123', store, 'No descrip', 'ref' );
				firsttimesource.UnLock(itIsThePresent,Now, LottoReward(4), 'Mau', 'ABC1240', store, 'No descrip', 'ref' );
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.25), 'ABC124', store, 'Thanks for your purchase #1' , 'ref');
	
	
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(1), 'ABC1240', store, 'No descrip' , 'ref');
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.5), 'ABC1241', store, 'No descrip' , 'ref');
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(1), 'ABC1242', store, 'No descrip' , 'ref');
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(1), 'ABC1243', store, 'Thanks for your purchase #5', 'ref' );
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(1), 'ABC1244', store, 'No descrip' , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.25), 'ABC1245', store, 'No descrip', 'ref' ); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.25), 'ABC1246', store, 'No descrip' , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.25), 'ABC1247', store, 'No descrip' , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.25), 'ABC1248', store, 'No descrip' , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.25), 'ABC1249', store, 'No descrip' , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.5), 'ABC12410', store, 'Thanks for your purchase #12' , 'ref');
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(5), 'ABC12411', store, 'No descrip' , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(5), 'ABC12412', store, 'No descrip' , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.5), 'ABC12413', store, 'No descrip' , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.25), 'ABC12414', store, 'No descrip' , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(1), 'ABC12415', store, 'No descrip' , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.5), 'ABC12416', store, 'No descrip' , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(1), 'ABC12417', store, 'No descrip' , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.25), 'ABC12418', store, 'No descrip' , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.25), 'ABC12419', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.25), 'ABC12420', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(5), 'ABC12421', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(1), 'ABC12422', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.25), 'ABC12423', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.5), 'ABC12424', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.25), 'ABC12425', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.25), 'ABC12426', store, 'Thanks for your purchase #30' , 'ref');
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(5), 'ABC12427', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.25), 'ABC12428', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(1), 'ABC12429', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.5), 'ABC12430', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.5), 'ABC12431', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(1), 'ABC12432', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(1), 'ABC12433', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(5), 'ABC12434', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.5), 'ABC12435', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(1), 'ABC12436', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.5), 'ABC12437', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(5), 'ABC12438', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(1), 'ABC12439', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(1), 'ABC12440', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.25), 'ABC12441', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(5), 'ABC12442', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.25), 'ABC12443', store, 'Thanks for your purchase #53' , 'ref');
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.25), 'ABC12444', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.5), 'ABC12445', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(1), 'ABC12446', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(5), 'ABC12447', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(1), 'ABC12448', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.5), 'ABC12449', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(1), 'ABC12450', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.25), 'ABC12451', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(1), 'ABC12452', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.5), 'ABC12453', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.25), 'ABC12454', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(1), 'ABC12455', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(1), 'ABC12456', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.25), 'ABC12457', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.5), 'ABC12458', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.25), 'ABC12459', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(5), 'ABC12460', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(1), 'ABC12461', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(5), 'ABC12462', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.5), 'ABC12463', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.5), 'ABC12464', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(5), 'ABC12465', store, 'Thanks for your purchase #80' , 'ref');
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(1), 'ABC12466', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.5), 'ABC12467', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(5), 'ABC12468', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(1), 'ABC12469', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(5), 'ABC12470', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.25), 'ABC12471', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(1), 'ABC12472', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.5), 'ABC12473', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.5), 'ABC12474', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.25), 'ABC12475', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(5), 'ABC12476', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.25), 'ABC12477', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(5), 'ABC12478', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(5), 'ABC12479', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(5), 'ABC12480', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(1), 'ABC12481', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.5), 'ABC12482', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.5), 'ABC12483', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(5), 'ABC12484', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.5), 'ABC12485', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.25), 'ABC12486', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(5), 'ABC12487', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.25), 'ABC12488', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(1), 'ABC12489', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(1), 'ABC12490', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.25), 'ABC12491', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(5), 'ABC12492', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(5), 'ABC12493', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(1), 'ABC12494', store, 'Thanks for your purchase #114' , 'ref');
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.25), 'ABC12495', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.25), 'ABC12496', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.25), 'ABC12497', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.5), 'ABC12498', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.25), 'ABC12499', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(1), 'ABC124100', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.5), 'ABC124101', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(5), 'ABC124102', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.25), 'ABC124103', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.5), 'ABC124104', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(1), 'ABC124105', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.5), 'ABC124106', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.5), 'ABC124107', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(5), 'ABC124108', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.25), 'ABC124109', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.5), 'ABC124110', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.25), 'ABC124111', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.25), 'ABC124112', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.5), 'ABC124113', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.25), 'ABC124114', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.5), 'ABC124115', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(5), 'ABC124116', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(1), 'ABC124117', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(5), 'ABC124118', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(5), 'ABC124119', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.5), 'ABC124120', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.5), 'ABC124121', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(1), 'ABC124122', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(1), 'ABC124123', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(5), 'ABC124124', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(5), 'ABC124125', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.25), 'ABC124126', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.5), 'ABC124127', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(1), 'ABC124128', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.5), 'ABC124129', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(5), 'ABC124130', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(5), 'ABC124131', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(1), 'ABC124132', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(5), 'ABC124133', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.5), 'ABC124134', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.25), 'ABC124135', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(1), 'ABC124136', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(1), 'ABC124137', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.25), 'ABC124138', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(1), 'ABC124139', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(5), 'ABC124140', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.5), 'ABC124141', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(5), 'ABC124142', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(5), 'ABC124143', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(5), 'ABC124144', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(1), 'ABC124145', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.25), 'ABC124146', store, 'Thanks for your purchase #130' , 'ref');
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(1), 'ABC124147', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.5), 'ABC124148', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(5), 'ABC124149', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(1), 'ABC124150', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(5), 'ABC124151', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(1), 'ABC124152', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.5), 'ABC124153', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(1), 'ABC124154', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.5), 'ABC124155', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(1), 'ABC124156', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.5), 'ABC124157', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.25), 'ABC124158', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.5), 'ABC124159', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(1), 'ABC124160', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(5), 'ABC124161', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(1), 'ABC124162', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(1), 'ABC124163', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.25), 'ABC124164', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.5), 'ABC124165', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.5), 'ABC124166', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(1), 'ABC124167', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.25), 'ABC124168', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.25), 'ABC124169', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.25), 'ABC124170', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(1), 'ABC124171', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.25), 'ABC124172', LOTTO , 'Thanks for your purchase #150'  , 'ref');
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(1), 'ABC124173', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(5), 'ABC124174', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.25), 'ABC124175', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.5), 'ABC124176', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(1), 'ABC124177', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.5), 'ABC124178', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(1), 'ABC124179', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(5), 'ABC124180', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.5), 'ABC124181', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(5), 'ABC124182', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.5), 'ABC124183', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.25), 'ABC124184', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.25), 'ABC124185', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(1), 'ABC124186', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(5), 'ABC124187', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(1), 'ABC124188', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(5), 'ABC124189', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(5), 'ABC124190', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(5), 'ABC124191', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(1), 'ABC124192', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(1), 'ABC124193', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(5), 'ABC124194', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.25), 'ABC124195', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(1), 'ABC124196', LOTTO , 'Thanks for your purchase #167'  , 'ref');
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.5), 'ABC124197', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(5), 'ABC124198', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.25), 'ABC124199', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(5), 'ABC124200', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.5), 'ABC124201', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(1), 'ABC124202', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.5), 'ABC124203', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.5), 'ABC124204', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.25), 'ABC124205', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(5), 'ABC124206', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(5), 'ABC124207', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(5), 'ABC124208', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(1), 'ABC124209', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.25), 'ABC124210', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(5), 'ABC124211', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(5), 'ABC124212', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(5), 'ABC124213', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(5), 'ABC124214', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(5), 'ABC124215', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.25), 'ABC124216', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(5), 'ABC124217', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.25), 'ABC124218', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.25), 'ABC124219', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(1), 'ABC124220', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(5), 'ABC124221', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.25), 'ABC124222', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.25), 'ABC124223', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.25), 'ABC124224', store, 'Thanks for your purchase #168' , 'ref');
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(1), 'ABC124225', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.25), 'ABC124226', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.5), 'ABC124227', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(1), 'ABC124228', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(5), 'ABC124229', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(5), 'ABC124230', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.25), 'ABC124231', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.5), 'ABC124232', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(1), 'ABC124233', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.5), 'ABC124234', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.25), 'ABC124235', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(1), 'ABC124236', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(1), 'ABC124237', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(5), 'ABC124238', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.25), 'ABC124239', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(1), 'ABC124240', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(1), 'ABC124241', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(5), 'ABC124242', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(5), 'ABC124243', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(5), 'ABC124244', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.25), 'ABC124245', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(5), 'ABC124246', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(1), 'ABC124247', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(1), 'ABC124248', store, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.25), 'ABC124249', LOTTO , 'Thanks for your purchase #180' , 'ref');

				balanceLR = atAddress.CreateBalanceIfNotExists('LR');
				print balanceLR.Type typeLR;
				print balanceLR.Locked lockedLR;
				print balanceLR.Available availableLR;

				balanceDollars = atAddress.CreateBalanceIfNotExists('USD');	
				print balanceDollars.Type typeDollar;
				print balanceDollars.Locked lockedDollar;
				print balanceDollars.Available availableDollar;
			",
			IpAddress.DEFAULT,
#pragma warning disable VSTHRD002 // Avoid problematic synchronous waits
			UserInLog.ANONYMOUS).Result;
#pragma warning restore VSTHRD002 // Avoid problematic synchronous waits

			return actor;
		}

		internal static CashierActor LRCurrencyMock(CashierActor actor, string atAddressNumber)
		{
			var x = actor.PerformCmdAsync($@"
				company = Company();
				companyIDf2401f84xa94ax4db3xaec4x83f5361730ae = company;
				company.Sales.CreateStore(1,'Picks Store');
				company.Sales.CreateStore(2,'Brackets Store 2019');
				company.Sales.CreateStore(3,'Brackets Store 2020');
				company.Sales.CreateStore(4,'Fiero Wallet');
				company.Sales.CreateStore(5,'Ladybet Store');
				company.Sales.CreateStore(6,'Brackets Store 2021');
				company.Sales.CreateStore(7,'Keno Store');

				atAddress = AtAddress('{atAddressNumber}');
				manualsource = atAddress.CreateSource(itIsThePresent, Now, 1, LR);
				firsttimesource = atAddress.CreateSource(itIsThePresent, Now, 2, LR);
				rechargesource = atAddress.CreateSource(itIsThePresent, Now, 3, LR);

				Now = 10/12/2019 11:35:45;
				store = company.Sales.StoreById(1);
				firsttimesource.Accredit(itIsThePresent, Now, LottoReward(8000), 'Mau', 'ABC2240', store, 'First deposit from the book.', 'ref');
				atAddress.CreateAuthorization(itIsThePresent, Now, LottoReward(2.0), 3, store, 'ticketPick3StraightProd', 'ref');
				authorization = atAddress.GetAuthorization(3);
				authorization.CreateFragments(1, 1, 2.0, {{'1000002-1'}}, {{'Lotto PA 6:25PM Pick 3 10/14/2019 (3,9)-3-(3,6) $2.00 ticket for $450.00 prize'}}, 2.0, 449.5);
				ataddress.PreparePayments(itIsThePresent, 3, {{1}}, Now, store, '', Loser).ApplyChanges(itIsThePresent, 'Bart Simpson');

				Now = 10/14/2019 10:35:45;
				atAddress.CreateAuthorization(itIsThePresent, Now, LottoReward(0.75), 4, store, 'ticketPick3StraightProd', 'ref');
				authorization = atAddress.GetAuthorization(4);
				authorization.CreateFragments(1, 3, 0.25, {{'1000003-1', '1000003-2', '1000003-3'}}, {{'Lotto SC 6:30PM Pick 3 10/14/2019 8-0-1 $0.25 ticket for $225.00 prize', 'Lotto SC 6:30PM Pick 3 10/14/2019 8-8-1 $0.25 ticket for $225.00 prize', 'Lotto SC 6:30PM Pick 3 10/14/2019 8-8-8 $0.25 ticket for $225.00 prize'}}, 0.25, 224.75);
				ataddress.PreparePayments(itIsThePresent, 4, {{1, 2, 3}}, Now, store, '', Loser).ApplyChanges(itIsThePresent, 'Bart Simpson');

				Now = 10/15/2019 9:35:45;
				atAddress.CreateAuthorization(itIsThePresent, Now, LottoReward(0.75), 5, store, 'ticketPick3BoxedProd', 'ref');
				authorization = atAddress.GetAuthorization(5);
				authorization.CreateFragments(1, 3, 0.25, {{'1000004-1', '1000004-2', '1000004-3'}}, {{'Lotto NH 6:40PM Pick 3 10/14/2019 (8-0-1) BOX $0.25 ticket for $37.50 prize', 'Lotto NH 6:40PM Pick 3 10/14/2019 (8-8-1) BOX $0.25 ticket for $75.00 prize', 'Lotto NH 6:40PM Pick 3 10/14/2019 (8-8-8) BOX $0.25 ticket for $225.00 prize'}}, 0.25, 37.25);
				ataddress.PreparePayments(itIsThePresent, 5, {{1, 2, 3}}, Now, store, '', Loser).ApplyChanges(itIsThePresent, 'Bart Simpson');

				Now = 10/16/2019 8:35:45;
				atAddress.CreateAuthorization(itIsThePresent, Now, LottoReward(1.0), 6, store, 'ticketPick3BoxedProd', 'ref');
				authorization = atAddress.GetAuthorization(6);
				authorization.CreateFragments(1, 4, 0.25, {{'1000005-1', '1000005-2', '1000005-3', '1000005-4'}}, {{'Lotto VT 6:40PM Pick 3 10/14/2019 (3-3-3) BOX $0.25 ticket for $225.00 prize', 'Lotto VT 6:40PM Pick 3 10/14/2019 (3-3-6) BOX $0.25 ticket for $75.00 prize', 'Lotto VT 6:40PM Pick 3 10/14/2019 (9-3-3) BOX $0.25 ticket for $75.00 prize', 'Lotto VT 6:40PM Pick 3 10/14/2019 (9-3-6) BOX $0.25 ticket for $37.50 prize'}}, 0.25, 224.75);
				ataddress.PreparePayments(itIsThePresent, 6, {{1, 2, 3, 4}}, Now, store, '', Loser).ApplyChanges(itIsThePresent, 'Bart Simpson');

				Now = 10/14/2019 18:35:45;
				atAddress.CreateAuthorization(itIsThePresent, Now, LottoReward(1.0), 8, store, 'ticketPick3BoxedProd', 'ref');
				authorization = atAddress.GetAuthorization(8);
				authorization.CreateFragments(1, 4, 0.25, {{'1000007-1', '1000007-2', '1000007-3', '1000007-4'}}, {{'Lotto TX Evening 6:43PM Pick 3 10/14/2019 (3-3-3) BOX $0.25 ticket for $225.00 prize', 'Lotto TX Evening 6:43PM Pick 3 10/14/2019 (3-3-6) BOX $0.25 ticket for $75.00 prize', 'Lotto TX Evening 6:43PM Pick 3 10/14/2019 (9-3-3) BOX $0.25 ticket for $75.00 prize', 'Lotto TX Evening 6:43PM Pick 3 10/14/2019 (9-3-6) BOX $0.25 ticket for $37.50 prize'}}, 0.25, 224.75);
				",
			IpAddress.DEFAULT,
#pragma warning disable VSTHRD002 // Avoid problematic synchronous waits
			UserInLog.ANONYMOUS).Result;
#pragma warning restore VSTHRD002 // Avoid problematic synchronous waits
			return actor;
		}
	}
}
