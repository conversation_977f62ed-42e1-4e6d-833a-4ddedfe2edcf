﻿using Elasticsearch.Net;
using GamesEngine.Bets;
using GamesEngine.Finance;
using Microsoft.Extensions.Configuration;
using Nest;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Puppeteer.EventSourcing;
using RestSharp;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Text;
using System.Threading.Tasks;

namespace GamesEngine.Settings
{
	public class HttpELKClient
	{
		private static HttpELKClient instance = null;
		private readonly string elkServer;
		public const string SalesIndex = "sales";
		public const string LoyaltyIndex = "loyalty";
		public const string CustomersIndex = "customers";
		public const string CustomersLogIndex = "customersLog";

		private readonly ElasticClient salesClient;
		private readonly ElasticClient loyaltyClient;
		private readonly ElasticLowLevelClient queryClient;

		public HttpELKClient(string elkServer)
		{
			this.elkServer = elkServer;

			var settings = new ConnectionSettings(new Uri(elkServer)).DefaultIndex(SalesIndex);
			salesClient = new ElasticClient(settings);
			settings = new ConnectionSettings(new Uri(elkServer)).DefaultIndex(LoyaltyIndex);
			loyaltyClient = new ElasticClient(settings);

			var settingsForqueries = new ConnectionConfiguration(new Uri(elkServer)).RequestTimeout(TimeSpan.FromMinutes(2));
			queryClient = new ElasticLowLevelClient(settingsForqueries);
		}

		public static HttpELKClient GetInstance()
		{
			if (instance == null)
			{
				throw new Exception("Instance must be configured first.");
			}
			return instance;
		}

		public static void Configure(string elkServer)
		{
			if (string.IsNullOrEmpty(elkServer)) throw new ArgumentNullException(nameof(elkServer));

			instance = new HttpELKClient(elkServer);
		}

		public UpdateResponse<Adquisition> Update(string customerIdentifier, string accountNumber, AdquisitionLastPurchase adquisitionLastPurchaseDetail)
		{
			var ndexResponse = loyaltyClient.Update<Adquisition, dynamic>(
				new DocumentPath<Adquisition>(accountNumber), u => u.Index(LoyaltyIndex).Doc(adquisitionLastPurchaseDetail));
			return ndexResponse;
		}
		public IndexResponse Create(Adquisition document)
		{
			var ndexResponse = loyaltyClient.IndexDocument(document);
			return ndexResponse;
		}
		public IndexResponse Create(Sale document)
		{
			var ndexResponse = salesClient.IndexDocument(document);
			return ndexResponse;
		}
		public BulkResponse Create(Retention document)
		{
			var ndexResponse = loyaltyClient.IndexMany(document.Factory());
			return ndexResponse;
		}
		public async Task<StringResponse> SearchInSalesAsync(string query)
		{
			return await queryClient.SearchAsync<StringResponse>(SalesIndex, query);
		}
		public async Task<StringResponse> SearchInLoyaltyAsync(string query)
		{
			return await queryClient.SearchAsync<StringResponse>(LoyaltyIndex, query);
		}

		public bool ExistsDocument(string index, string id)
		{
			BytesResponse result = queryClient.DocumentExists<BytesResponse>(index, id);

			if (!result.Success)
			{
				string body = $"result: {result}";
				ErrorsSender.Send(body, $"Error checking index {id}.");
			}

			return result.Success;
		}

		public BytesResponse CreateDocument(string index, string id, PostData document)
		{
			BytesResponse result = queryClient.Index<BytesResponse>(index, id, document);

			if (!result.Success)
			{
				string body = $"body: {document} \n result: {result}";
				ErrorsSender.Send(body, $"Error creating index {id}.");
			}

			return result;
		}

		public BytesResponse CreateDocument(string index, PostData document)
		{
			BytesResponse result = queryClient.Index<BytesResponse>(index, document);

			if (!result.Success)
			{
				string body = $"body: {document} \n result: {result}";
				ErrorsSender.Send(body, $"Error creating index anonymous.");
			}

			return result;
		}

		public BytesResponse UpdateDocument(string index, string id, PostData document)
		{
			BytesResponse result = queryClient.Update<BytesResponse>(index, id, document);

			if (!result.Success)
			{
				string body = $"body: {document} \n result: {result}";
				ErrorsSender.Send(body, $"Error updating index {id}.");
			}

			return result;
		}

		public StringResponse UpdateDocumentByQuery(string index, string payload)
		{
			var result = queryClient.UpdateByQuery<StringResponse>(index, payload);

			if (!result.Success)
			{
				string body = $"body: {payload} \n result: {result}";
				ErrorsSender.Send(body, $"Error updating by query.");
			}

			return result;
		}

		public BytesResponse DeleteDocumentByQuery(string index, string document)
		{
			var param = new DeleteByQueryRequestParameters();
			param.Conflicts = Conflicts.Proceed;
			BytesResponse result = queryClient.DeleteByQuery<BytesResponse>(index, document, param);

			if (!result.Success)
			{
				string body = $"body: {document} \n result: {result.DebugInformation}";
				ErrorsSender.Send(body, $"Error updating by query.");
			}

			return result;
		}

		public BytesResponse CreateBulk(List<object> objs)
		{
			BytesResponse result = queryClient.Bulk<BytesResponse>(PostData.MultiJson(objs));

			if (!result.Success)
			{
				string body = $"result: {result}";
				ErrorsSender.Send(body, $"Error creating documents.");
			}
			else
			{
				string response2Txt = System.Text.Encoding.Default.GetString(result.Body);
				BulkReponse response = JsonConvert.DeserializeObject<BulkReponse>(response2Txt);
				if (response.Errors)
				{
					ErrorsSender.Send(response2Txt, $"Error creating documents.");
				}
			}

			return result;
		}


		public IRestResponse CreateAndIndexWithDateMappings(string index, string[] dateMappingsFileds, string[] nestedMappingsFileds, string[] suggestionMappingsFileds)
		{
			if (string.IsNullOrEmpty(elkServer)) throw new ArgumentException(nameof(elkServer));
			if (string.IsNullOrEmpty(index)) throw new ArgumentException(nameof(index));

			StringBuilder subPayload = new StringBuilder();
			if (dateMappingsFileds != null && dateMappingsFileds.Length > 0)
			{
				foreach (string dateField in dateMappingsFileds)
				{
					subPayload.Append($@"
				""{dateField}"": {{
						""type"":   ""date"",
						""format"": ""strict_date_optional_time||epoch_millis""
					}},");
				}
			}
			if (nestedMappingsFileds != null && nestedMappingsFileds.Length > 0)
			{
				foreach (string nestedField in nestedMappingsFileds)
				{
					subPayload.Append($@"
				""{nestedField}"": {{
						""type"":   ""nested""
					}},");
				}
			}
			if (suggestionMappingsFileds != null && suggestionMappingsFileds.Length > 0)
			{
				foreach (string suggestionField in suggestionMappingsFileds)
				{
					subPayload.Append($@"
				""{suggestionField}"": {{
						""type"":   ""completion""
					}},");
				}
			}
			subPayload.Length--;

			var client = new RestClient($"{elkServer}/{index}");
			client.Timeout = 5000;
			var request = new RestRequest(Method.PUT);
			request.AddHeader("Content-Type", "application/json");
			request.AddParameter("application/json", "{\r\n  \"mappings\": {\r\n    \"properties\": {\r\n      " + subPayload + "    }\r\n  }\r\n}", ParameterType.RequestBody);
			IRestResponse response = client.Execute(request);
			return response;
		}

		public IRestResponse CreateTextKeywordMapping(string index, string mappingFieldName)
		{
			if (string.IsNullOrEmpty(elkServer)) throw new ArgumentException(nameof(elkServer));
			if (string.IsNullOrEmpty(index)) throw new ArgumentException(nameof(index));

			string payload = $@"
			{{
				""mappings"": {{
					""properties"": {{
						""{mappingFieldName}"": {{
							""type"":   ""text"",
							""fielddata"": true,
							""fields"": {{
								""keyword"": {{
									""type"":   ""keyword""
								}}
							}}
						}}
					}}
				}}
			}}";

			var client = new RestClient($"{elkServer}/{index}");
			client.Timeout = 5000;

			var request = new RestRequest(Method.PUT);
			request.AddHeader("Content-Type", "application/json");
			request.AddParameter("application/json", payload, ParameterType.RequestBody);
			IRestResponse response = client.Execute(request);
			return response;
		}

		public IRestResponse CreateDateMapping(string index, string mappingFieldName)
		{
			if (string.IsNullOrWhiteSpace(elkServer)) throw new ArgumentException(nameof(elkServer));
			if (string.IsNullOrWhiteSpace(index)) throw new ArgumentException(nameof(index));
			if (string.IsNullOrWhiteSpace(mappingFieldName)) throw new ArgumentException(nameof(mappingFieldName));

			string payload = $@"
			{{
				""mappings"": {{
					""properties"": {{
						""{mappingFieldName}"": {{
							""type"": ""date"",
							""format"": ""MM/dd/yyyy HH:mm:ss""
						}}
					}}
				}}
			}}";

			var client = new RestClient($"{elkServer}/{index}");
			client.Timeout = 5000;

			var request = new RestRequest(Method.PUT);
			request.AddHeader("Content-Type", "application/json");
			request.AddParameter("application/json", payload, ParameterType.RequestBody);
			IRestResponse response = client.Execute(request);
			return response;
		}

		public void CreateMockCustomer()
		{
			var json = $@"{{
				""query"": {{
				""match_all"": {{ }}
					}}
				}} ";
			var response = GetInstance().DeleteDocumentByQuery(CustomersIndex, json);
			Loggers.GetIntance().SearchEngine.Debug($"deleting: customers response:{response}");

			json = $@"
                    {{
						""key"": ""identification_number,account_number,email"",
                        ""account_number"": ""*******************"",
                        ""approval_date"": ""03/03/2023 17:48:01"",
                        ""identification_number"": ""*********"",
                        ""who_approved"": ""N/A"",
                        ""who_created"": ""N/A"",
                        ""verifiable_first_name"": ""False"",
                        ""last_name"": ""Demo"",
                        ""verifiable_password"": ""False"",
                        ""verifiable_identification_number"": ""False"",
                        ""avatar"": ""http://www.hotavatars.com/wp-content/uploads/2019/01/I80W1Q0.png"",
                        ""creation_date"": ""03/03/2023 17:47:34"",
                        ""templateId"": ""1"",
                        ""Template"": ""1"",
                        ""password"": ""********"",
                        ""verifiable_last_name"": ""False"",
                        ""nick_name"": ""My NickName2"",
                        ""first_name"": ""Demo"",
                        ""verifiable_email"": ""False"",
                        ""email"": ""<EMAIL>"",
                        ""customer_type_id"": ""2"",
                        ""status"": ""Approved"",
                        ""DOB"": ""03/03/2000""
                    }}
                    ";
			var dict = JsonConvert.DeserializeObject<Dictionary<string, string>>(json);
			var postData = PostData.Serializable(dict);
			response = GetInstance().CreateDocument(CustomersIndex, "*********,*******************,<EMAIL>", postData);
			Loggers.GetIntance().SearchEngine.Debug($"index: customers response:{response}");

			json = $@"
                    {{
						""key"": ""account_number"",
                        ""account_number"": ""*******************"",
						""avatar"": ""http://www.hotavatars.com/wp-content/uploads/2019/01/I80W1Q0.png"",
						""nick_name"": ""My NickName1"",
                        ""approval_date"": ""03/03/2023 17:48:01"",
                        ""who_approved"": ""N/A"",
                        ""who_created"": ""N/A"",
                        ""creation_date"": ""03/03/2023 17:47:34"",
                        ""templateId"": ""2"",
                        ""Template"": ""1"",
                        ""customer_type_id"": ""2"",
                        ""status"": ""Approved""
                    }}
                    ";
			dict = JsonConvert.DeserializeObject<Dictionary<string, string>>(json);
			postData = PostData.Serializable(dict);
			response = GetInstance().CreateDocument(CustomersIndex, "*******************", postData);
			Loggers.GetIntance().SearchEngine.Debug($"index: customers response:{response}");

			json = $@"
                    {{
						""key"": ""email"",
                        ""email"": ""<EMAIL>"",
						""verifiable_email"": ""True"",
						""avatar"": ""http://www.hotavatars.com/wp-content/uploads/2019/01/I80W1Q0.png"",
						""nick_name"": ""My NickName1"",
						""approval_date"": ""03/03/2023 17:48:01"",
                        ""who_approved"": ""N/A"",
                        ""who_created"": ""N/A"",
                        ""creation_date"": ""03/03/2023 17:47:34"",
                        ""templateId"": ""5"",
                        ""Template"": ""1"",
                        ""customer_type_id"": ""2"",
                        ""status"": ""Approved""
                    }}
                    ";
			dict = JsonConvert.DeserializeObject<Dictionary<string, string>>(json);
			postData = PostData.Serializable(dict);
			response = GetInstance().CreateDocument(CustomersIndex, "<EMAIL>", postData);
			Loggers.GetIntance().SearchEngine.Debug($"index: customers response:{response}");

			json = $@"
                    {{
						""key"": ""identification_number,account_number"",
						""account_number"": ""*******************"",
                        ""identification_number"": ""*********"",
						""verifiable_identification_number"": ""True"",
                        ""who_created"": ""N/A"",
                        ""creation_date"": ""03/03/2023 17:47:34"",
                        ""templateId"": ""4"",
                        ""Template"": ""1"",
                        ""customer_type_id"": ""2"",
                        ""status"": ""Drafted""
                    }}
                    ";
			dict = JsonConvert.DeserializeObject<Dictionary<string, string>>(json);
			postData = PostData.Serializable(dict);
			response = GetInstance().CreateDocument(CustomersIndex, "*********,*******************", postData);
			Loggers.GetIntance().SearchEngine.Debug($"index: customers response:{response}");
		}

		private class BulkReponse
		{ 
			public int Took { set; get; }
			public bool Errors { set; get; }
		}
		public async Task<StringResponse> SearchAsync(string index, string query)
		{
			return await queryClient.SearchAsync<StringResponse>(index, query);
		}
		public async Task<StringResponse> SearchAsync(string index, string query, Action<bool, decimal, JObject> IfHitSomeThing)
		{
			var result = await queryClient.SearchAsync<StringResponse>(index, query);
			string jsonString = result.Body;
			if (string.IsNullOrWhiteSpace(jsonString)) return result;
			var reader = new JsonTextReader(new StringReader(jsonString));
			reader.FloatParseHandling = FloatParseHandling.Decimal;

			JObject obj = null;
			decimal amountOfHits = 0;
			bool success = result.Success;
			if (success)
			{
				obj = JObject.Load(reader);
				amountOfHits = decimal.Parse(obj["hits"]["total"]["value"].ToString());
			}

			if (IfHitSomeThing != null) IfHitSomeThing(result.Success, amountOfHits, obj);
			return result;
		}

		public StringResponse Search(string index, string query)
		{
			return queryClient.Search<StringResponse>(index, query);
		}
	}

	public abstract class Document
	{
		protected Document(string id, DateTime eventDate)
		{
			Id = id;
			EventDate = eventDate;
		}
		public const string DATE_SERIALIZATION_FORMAT = "yyyyMMddHHmmss";
		public string Id { get;}
		public DateTime EventDate { get;}
	}

	public class AdquisitionLastPurchase
	{
		protected AdquisitionLastPurchase()
		{}

		public AdquisitionLastPurchase(string customerIdentifier, string accountNumber, DateTime purchaseDate, Currency purchaseAmount, int storeId)
		{
			LastPurchaseDetail = new AdquisitionLastPurchaseDetail(customerIdentifier, accountNumber, purchaseDate, purchaseAmount, storeId);
		}

		internal AdquisitionLastPurchaseDetail LastPurchaseDetail { get; set; }

		internal class AdquisitionLastPurchaseDetail
		{
			public AdquisitionLastPurchaseDetail(string customerIdentifier, string accountNumber, DateTime purchaseDate, Currency purchaseAmount, int storeId)
			{
				CustomerIdentifier = customerIdentifier;
				AccountNumber = accountNumber;
				PurchaseDate = purchaseDate;
				CurrencyCode = purchaseAmount.CurrencyCode.ToString();
				Amount = purchaseAmount.Value;
				StoreId = storeId;
			}

			public DateTime PurchaseDate { get; internal set; }
			public decimal Amount { get; internal set; }
			public int StoreId { get; internal set; }
			public string CurrencyCode { get; internal set; }
			public string CustomerIdentifier { get; internal set; }
			public string AccountNumber { get; internal set; }
		}
	}

	internal class RetentionPerDay : RetentionBase
	{
		internal RetentionPerDay(string customerIdentifier, string accountNumber, DateTime retentionDate, int storeId)
		: base(
			$"d{retentionDate.ToString("yyyyMMdd")}_{accountNumber}",
			customerIdentifier,
			accountNumber,
			retentionDate,
			storeId)
		{
		}
	}

	internal class RetentionPerWeek : RetentionBase
	{
		internal RetentionPerWeek(string customerIdentifier, string accountNumber, DateTime retentionDate, int storeId)
		: base(
			$"{retentionDate.Year}W{GetIso8601WeekOfYear(retentionDate)}_{accountNumber}",
			customerIdentifier,
			accountNumber,
			retentionDate,
			storeId)
		{
		}

		private static int GetIso8601WeekOfYear(DateTime time)
		{
			// Seriously cheat.  If its Monday, Tuesday or Wednesday, then it'll 
			// be the same week# as whatever Thursday, Friday or Saturday are,
			// and we always get those right
			DayOfWeek day = CultureInfo.InvariantCulture.Calendar.GetDayOfWeek(time);
			if (day >= DayOfWeek.Monday && day <= DayOfWeek.Wednesday)
			{
				time = time.AddDays(3);
			}
			// Return the week of our adjusted day
			return CultureInfo.InvariantCulture.Calendar.GetWeekOfYear(time, CalendarWeekRule.FirstFourDayWeek, DayOfWeek.Monday);
		}
	}

	public class RetentionPerMonth : RetentionBase
	{
		internal RetentionPerMonth(string customerIdentifier, string accountNumber, DateTime retentionDate, int storeId)
		: base(
			$"d{retentionDate.ToString("yyyyMM")}_{accountNumber}",
			customerIdentifier,
			accountNumber,
			retentionDate,
			storeId)
		{
		}
	}

	public class RetentionPerYear : RetentionBase
	{
		internal RetentionPerYear(string customerIdentifier, string accountNumber, DateTime retentionDate, int storeId)
		: base(
			$"d{retentionDate.Year}_{accountNumber}",
			customerIdentifier,
			accountNumber,
			retentionDate,
			storeId)
		{
		}
	}

	public class Retention : RetentionBase
	{
		private readonly RetentionBase[] retentionFactory;

		public Retention(string customerIdentifier, string accountNumber, DateTime retentionDate, int storeId)
			: this(retentionDate.ToString(DATE_SERIALIZATION_FORMAT) + "_" + customerIdentifier + "_" + accountNumber, customerIdentifier, accountNumber, retentionDate, storeId)
		{ }

		private Retention(string id, string customerIdentifier, string accountNumber, DateTime retentionDate, int storeId)
			: base(id, customerIdentifier, accountNumber, retentionDate, storeId)
		{
			this.retentionFactory = new RetentionBase[]
			{
				new RetentionPerDay(customerIdentifier, accountNumber, retentionDate, storeId),
				new RetentionPerWeek(customerIdentifier, accountNumber, retentionDate, storeId),
				new RetentionPerMonth(customerIdentifier, accountNumber, retentionDate, storeId),
				new RetentionPerYear(customerIdentifier, accountNumber, retentionDate, storeId)
			};
		}

		internal RetentionBase[] Factory()
		{
			return retentionFactory;
		}
	}

	public class RetentionBase : Document
	{
		public string CustomerIdentifier { get; internal set; }
		public string AccountNumber { get; internal set; }
		public int StoreId { get; internal set; }
		public string EventType { get { return this.GetType().Name.ToLower(); } }
		
		protected RetentionBase(string id, string customerIdentifier, string accountNumber, DateTime retentionDate, int storeId)
			: base(id, retentionDate)
		{
			CustomerIdentifier = customerIdentifier;
			AccountNumber = accountNumber;
			StoreId = storeId;
		}
	}

	public sealed class Adquisition : Document
	{
		public string PlayerId { get; internal set; }
		public string Nickname { get; internal set; }
		public string Avatar { get; internal set; }
		public string AccountNumber { get; internal set; }
		public string CustomerIdentifier { get; internal set; }
		public int StoreId { get; internal set; }
		public string EventType { get { return this.GetType().Name.ToLower(); } }

		public Adquisition(string customerIdentifier, string accountNumber, DateTime adquisitonDate, int storeId)
			: base(accountNumber, adquisitonDate)
		{
			AccountNumber = accountNumber;
			CustomerIdentifier = customerIdentifier;
			StoreId = storeId;
		}
	}

	public sealed class Sale : Document
	{
		public Sale(string customerIdentifier, string accountNumber, DateTime purchaseDate, Currency purchaseAmount, int storeId)
			: base(purchaseDate.ToString(DATE_SERIALIZATION_FORMAT) + "_" + accountNumber, purchaseDate)
		{
			Day = purchaseDate.Day;
			Month = purchaseDate.Month;
			Year = purchaseDate.Year;
			CurrencyCode = purchaseAmount.CurrencyCode.ToString();
			Amount = purchaseAmount.Value;
			AccountNumber = accountNumber;
			StoreId = storeId;
		}

		public decimal Amount { get; internal set; }
		public int StoreId { get; internal set; }
		public int Day { get; internal set; }
		public int Month { get; internal set; }
		public int Year { get; internal set; }
		public string CurrencyCode { get; internal set; }
		public string AccountNumber { get; internal set; }
	}
}
