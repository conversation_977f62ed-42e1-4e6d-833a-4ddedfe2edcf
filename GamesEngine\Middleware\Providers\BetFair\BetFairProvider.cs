﻿using Betfair.ESAClient.Auth;
using GamesEngine.Games;
using GamesEngine.Settings;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Puppeteer.EventSourcing;
using Puppeteer.EventSourcing.Libraries;
using RestSharp;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using static GamesEngine.Middleware.Providers.NormalizedProviderResponse;
using static GamesEngine.Middleware.Providers.Response;

namespace GamesEngine.Middleware.Providers.BetFair
{
	public class BetFairProvider : Provider
	{
		private string[] marketTypeCodes = new string[] { "MATCH_ODDS", "MONEY_LINE", "MATCH_BET", "HALF_MATCH_ODDS", "TOTAL_MATCH_POINTS", "BOTH_TEAMS_TO_SCORE", "PENALTY_TAKEN", "SENDING_OFF", "HAT_TRICKED_SCORED", "OVER_UNDER_05", "OVER_UNDER_105_CORNR", "OVER_UNDER_135_CORNR", "OVER_UNDER_15", "OVER_UNDER_25", "OVER_UNDER_35", "OVER_UNDER_45", "OVER_UNDER_55", "OVER_UNDER_65", "OVER_UNDER_75", "OVER_UNDER_85", "OVER_UNDER_85_CORNR", "ODD_OR_EVEN", "CORRECT_SCORE", "CORRECT_SCORE2_2", "CORRECT_SCORE2_3, HALF_TIME_SCORE, HALF_TIME_FULL_TIME", "FIRST_GOAL_ODDS", "TO_SCORE", "TO_SCORE_HATTRICK", "SHOWN_A_CARD", "TO_QUALIFY", "CORNER_MATCH_BET", "BOOKING_ODDS" }; 
		private string[] marketProjections = new string[] { "COMPETITION", "EVENT", "EVENT_TYPE", "MARKET_DESCRIPTION", "RUNNER_DESCRIPTION", "RUNNER_METADATA", "MARKET_START_TIME" };
		internal BetFairProvider(int id, string name) : base(id, name)
		{

		}
		internal static BetFairAuthentication GeneraAuthentication(string appKey, string username, string password)
		{
			if (string.IsNullOrEmpty(appKey)) throw new GameEngineException(nameof(appKey));
			if (string.IsNullOrEmpty(username)) throw new GameEngineException(nameof(username));
			if (string.IsNullOrEmpty(password)) throw new GameEngineException(nameof(password));

			AppKeyAndSessionProvider sessionProvider = new AppKeyAndSessionProvider(
				AppKeyAndSessionProvider.SSO_HOST_COM,
				appKey,
				username,
				password);

			return new BetFairAuthentication(sessionProvider);
		}

		internal NormalizedProviderResponse Countries(BetFairAuthentication authentication)
		{
			if (authentication == null || !(authentication is BetFairAuthentication)) throw new GameEngineException(nameof(authentication));
			AppKeyAndSession appKeyAndSession = ((BetFairAuthentication)authentication).SessionProvider.GetOrCreateNewSession();

			var client = new RestClient("https://api.betfair.com/exchange/betting/rest/v1.0/listCountries/");
			client.Timeout = 20000;
			var request = new RestRequest(Method.POST);
			request.AddHeader("X-Application", appKeyAndSession.AppKey);
			request.AddHeader("X-Authentication", appKeyAndSession.Session);
			request.AddHeader("Content-Type", "application/json");
			string body = "{\"filter\":{\"marketBettingTypes\":[\"ODDS\",\"LINE\"]}}";
			request.AddParameter("application/json", body, ParameterType.RequestBody);
			Loggers.GetIntance().Betfair.Debug($@"Countries Product:{appKeyAndSession.AppKey} token:{appKeyAndSession.Session} body:{body}");

			IRestResponse response = client.Execute(request);

			if ((int)response.StatusCode == 200)
			{
				Loggers.GetIntance().Betfair.Debug($@"Countries Product:{appKeyAndSession.AppKey} token:{appKeyAndSession.Session} body:{body} response:{response.Content}");

				return new NormalizedProviderResponse(this, ResponseType.COUNTRY, response.Content);
			}

			ErrorsSender.Send($"No Countries were retrieved from {this.GetType().Name} provider", $@"Countries Product:{appKeyAndSession.AppKey} token:{appKeyAndSession.Session} body:{body} response:{response.Content}");
			throw new GameEngineException($"No Countries were retrieved from {this.GetType().Name} provider");
		}

		internal NormalizedProviderResponse Leagues(Authentication authentication, string eventTypeId)
		{
			if (authentication == null || !(authentication is BetFairAuthentication)) throw new GameEngineException(nameof(authentication));
			if (eventTypeId == null) throw new GameEngineException(nameof(eventTypeId));

			AppKeyAndSession appKeyAndSession = ((BetFairAuthentication)authentication).SessionProvider.GetOrCreateNewSession();

			var client = new RestClient("https://api.betfair.com/exchange/betting/rest/v1.0/listCompetitions/");
			client.Timeout = 20000;
			RestRequest request = new RestRequest(Method.POST);
			request.AddHeader("X-Application", appKeyAndSession.AppKey);
			request.AddHeader("X-Authentication", appKeyAndSession.Session);
			request.AddHeader("Content-Type", "application/json");
			string body = AddFilterConditions(request,
				new string[] { eventTypeId },
				new string[] { }, // LeaguesIds,
				marketTypeCodes,
				new string[] { "ODDS", "LINE" }
				);

			Loggers.GetIntance().Betfair.Debug($@"GeneraAuthentication Product:{appKeyAndSession.AppKey} token:{appKeyAndSession.Session} body:{body}");

			IRestResponse response = client.Execute(request);

			if ((int)response.StatusCode == 200)
			{
				Loggers.GetIntance().Betfair.Debug($@"GeneraAuthentication Product:{appKeyAndSession.AppKey} token:{appKeyAndSession.Session} body:{body} response:{response.Content}");

				return new NormalizedProviderResponse(this, ResponseType.LEAGUE, response.Content);
			}

			ErrorsSender.Send($"No Leagues were retrieved from {this.GetType().Name} provider", $@"GeneraAuthentication Product:{appKeyAndSession.AppKey} token:{appKeyAndSession.Session} body:{body} response:{response.Content}");
			throw new GameEngineException($"No Leagues were retrieved from {this.GetType().Name} provider");
		}

		private string AddFilterConditions(RestRequest request, string[] competitionIds, string[] eventIdsArgs, string[] marketTypeCodes, int maxResults, string[] marketProjection, string[] marketBettingTypes, string sort)
		{
			string body = AddFilterConditions(competitionIds, eventIdsArgs, marketTypeCodes, maxResults, marketProjection, marketBettingTypes, sort);
			request.AddParameter("application/json", body, ParameterType.RequestBody);

			return body;
		}
		private string AddFilterConditions(RestRequest request, string[] sportIds, string[] competitionIds, string[] marketTypeCodes, string[] marketBettingTypes)
		{

			string body = AddFilterConditions(sportIds, competitionIds, marketTypeCodes, marketBettingTypes);
			request.AddParameter("application/json", body, ParameterType.RequestBody);

			return body;
		}

		static internal string AddFilterConditions(string[] competitionIds, string[] eventIdsArgs, string[] marketTypeCodesArgs, int maxResults, string[] marketProjectionArgs, string[] marketBettingTypesArgs, string sort)
		{
			StringBuilder leagueIds = new StringBuilder();
			for (int i = 0; i < competitionIds.Length; i++)
			{
				string leagueId = competitionIds[i];
				leagueIds.Append("\"" + leagueId + "\",");
			}
			if (competitionIds.Length > 0) leagueIds.Length--;

			StringBuilder marketProjection = new StringBuilder();
			for (int i = 0; i < marketProjectionArgs.Length; i++)
			{
				string filterItem = marketProjectionArgs[i];
				marketProjection.Append("\"" + filterItem + "\",");
			}
			if (marketProjectionArgs.Length > 0) marketProjection.Length--;

			StringBuilder marketTypeCodes = new StringBuilder();
			for (int i = 0; i < marketTypeCodesArgs.Length; i++)
			{
				string filterItem = marketTypeCodesArgs[i];
				marketTypeCodes.Append("\"" + filterItem + "\",");
			}
			if (marketTypeCodesArgs.Length > 0) marketTypeCodes.Length--;

			StringBuilder eventIds = new StringBuilder();
			for (int i = 0; i < eventIdsArgs.Length; i++)
			{
				string filterItem = eventIdsArgs[i];
				eventIds.Append("\"" + filterItem + "\",");
			}
			if (eventIdsArgs.Length > 0) eventIds.Length--;

			StringBuilder marketBettingTypes = new StringBuilder();
			for (int i = 0; i < marketBettingTypesArgs.Length; i++)
			{
				string filterItem = marketBettingTypesArgs[i];
				marketBettingTypes.Append("\"" + filterItem + "\",");
			}
			if (marketBettingTypesArgs.Length > 0) marketBettingTypes.Length--;

			string leagueIdsFilter = (leagueIds.Length > 0) ? $@"""competitionIds"":[{leagueIds}]," : "";
			string marketTypeCodesFilter = (marketTypeCodes.Length > 0) ? $@"""marketTypeCodes"":[{marketTypeCodes}]," : "";
			string eventIdssFilter = (eventIds.Length > 0) ? $@"""eventIds"":[{eventIds}]," : "";
			string marketProjectionFilter = (marketProjection.Length > 0) ? $@"""marketProjection"":[{marketProjection}]," : "";
			string marketBettingTypesFilter = (marketBettingTypes.Length > 0) ? $@"""marketBettingTypes"":[{marketBettingTypes}]," : "";

			StringBuilder filters = new StringBuilder();
			filters.Append(leagueIdsFilter);
			filters.Append(marketTypeCodesFilter);
			filters.Append(eventIdssFilter);
			filters.Append(marketProjectionFilter);
			filters.Append(marketBettingTypesFilter);
			if (filters.Length > 0) filters.Length--;

			string body = $@"
				{{
					""filter"":{{
						{filters}
					}},
					{marketProjectionFilter}
					""maxResults"": ""{maxResults}"",
					""sort"": ""{sort}""
				}}
				";

			return body;
		}
		static internal string AddFilterConditions(string[] sportIds, string[] competitionIds, string[] marketTypeCodesArg, string[] marketBettingTypesArgs)
		{
			StringBuilder leagueIds = new StringBuilder();
			for (int i = 0; i < competitionIds.Length; i++)
			{
				string leagueId = competitionIds[i];
				leagueIds.Append("\"" + leagueId + "\",");
			}
			if (competitionIds.Length > 0) leagueIds.Length--;

			StringBuilder sportsCodes = new StringBuilder();
			for (int i = 0; i < sportIds.Length; i++)
			{
				string sportId = sportIds[i];
				sportsCodes.Append("\"" + sportId + "\",");
			}
			if (sportIds.Length > 0) sportsCodes.Length--;

			StringBuilder marketTypeCodes = new StringBuilder();
			for (int i = 0; i < marketTypeCodesArg.Length; i++)
			{
				string filterItem = marketTypeCodesArg[i];
				marketTypeCodes.Append("\"" + filterItem + "\",");
			}
			if (marketTypeCodes.Length > 0) marketTypeCodes.Length--;

			StringBuilder marketBettingTypes = new StringBuilder();
			for (int i = 0; i < marketBettingTypesArgs.Length; i++)
			{
				string filterItem = marketBettingTypesArgs[i];
				marketBettingTypes.Append("\"" + filterItem + "\",");
			}
			if (marketBettingTypesArgs.Length > 0) marketBettingTypes.Length--;

			string eventTypeIdsFilter = (sportsCodes.Length > 0) ? $@"""eventTypeIds"":[{sportsCodes}]," : "";
			string leagueIdsFilter = (leagueIds.Length > 0) ? $@"""competitionIds"":[{leagueIds}]," : "";
			string marketTypeCodesFilter = (marketTypeCodes.Length > 0) ? $@"""marketTypeCodes"":[{marketTypeCodes}]," : "";
			string marketBettingTypesFilter = (marketBettingTypes.Length > 0) ? $@"""marketBettingTypes"":[{marketBettingTypes}]," : "";

			StringBuilder filters = new StringBuilder();
			filters.Append(eventTypeIdsFilter);
			filters.Append(leagueIdsFilter);
			filters.Append(marketTypeCodesFilter);
			filters.Append(marketBettingTypesFilter);
			if (filters.Length > 0) filters.Length--;

			string body = $@"
				{{
					""filter"":{{
						{filters}
					}}
				}}
				";

			return body;
		}


		internal NormalizedProviderResponse Sports(Authentication authentication)
		{
			if (authentication == null || !(authentication is BetFairAuthentication)) throw new GameEngineException(nameof(authentication));

			AppKeyAndSession appKeyAndSession = ((BetFairAuthentication)authentication).SessionProvider.GetOrCreateNewSession();

			var client = new RestClient("https://api.betfair.com/exchange/betting/rest/v1.0/listEventTypes/");
			client.Timeout = 20000;
			var request = new RestRequest(Method.POST);
			request.AddHeader("X-Application", appKeyAndSession.AppKey);
			request.AddHeader("X-Authentication", appKeyAndSession.Session);
			request.AddHeader("Content-Type", "application/json");
			string body = "{\r\n    \"filter\" : {\"marketBettingTypes\":[\"ODDS\",\"LINE\"] }\r\n}";
			request.AddParameter("application/json", body, ParameterType.RequestBody);

			Loggers.GetIntance().Betfair.Debug($@"Sports Product:{appKeyAndSession.AppKey} token:{appKeyAndSession.Session} body:{body}");

			IRestResponse response = client.Execute(request);

			if ((int)response.StatusCode == 200)
			{
				Loggers.GetIntance().Betfair.Debug($@"Sports Product:{appKeyAndSession.AppKey} token:{appKeyAndSession.Session} body:{body} response:{response.Content}");

				return new NormalizedProviderResponse(this, ResponseType.SPORTS, response.Content);
			}

			ErrorsSender.Send($"No Sports were retrieved from {this.GetType().Name} provider", $@"Sports Product:{appKeyAndSession.AppKey} token:{appKeyAndSession.Session} body:{body} response:{response.Content}");
			throw new GameEngineException($"No Sports were retrieved from {this.GetType().Name} provider");
		}
		internal NormalizedProviderResponse Team(Authentication authentication, string competitionId, int sportId)
		{
			if (authentication == null || !(authentication is BetFairAuthentication)) throw new GameEngineException(nameof(authentication));

			AppKeyAndSession appKeyAndSession = ((BetFairAuthentication)authentication).SessionProvider.GetOrCreateNewSession();

			var client = new RestClient("https://api.betfair.com/exchange/betting/rest/v1.0/listEvents/");
			client.Timeout = 20000;
			RestRequest request = new RestRequest(Method.POST);
			request.AddHeader("X-Application", appKeyAndSession.AppKey);
			request.AddHeader("X-Authentication", appKeyAndSession.Session);
			request.AddHeader("Content-Type", "application/json");
			string body = AddFilterConditions(request, new string[] { }, new string[] { competitionId }, marketTypeCodes, new string[] { "ODDS", "LINE" });

			Loggers.GetIntance().Betfair.Debug($@"Team Product:{appKeyAndSession.AppKey} token:{appKeyAndSession.Session} body:{body}");

			IRestResponse response = client.Execute(request);

			if ((int)response.StatusCode == 200)
			{
				Loggers.GetIntance().Betfair.Debug($@"Team Product:{appKeyAndSession.AppKey} token:{appKeyAndSession.Session} body:{body} response:{response.Content}");

				return new NormalizedProviderResponse(this, ResponseType.TEAM, response.Content, new Attachments() { LeagueId = competitionId, SportId = sportId });
			}

			ErrorsSender.Send($"No Team were retrieved from {this.GetType().Name} provider", $@"Team Product:{appKeyAndSession.AppKey} token:{appKeyAndSession.Session} body:{body} response:{response.Content}");
			throw new GameEngineException($"No Teams were retrieved from {this.GetType().Name} provider");
		}
		internal IEnumerable<NormalizedProviderResponse> Lines(Authentication authentication, int tournamentId, int gameId, out IEnumerable<MarketIdsAndTimesPeer> marketIds)
		{
			if (authentication == null || !(authentication is BetFairAuthentication)) throw new GameEngineException(nameof(authentication));

			AppKeyAndSession appKeyAndSession = ((BetFairAuthentication)authentication).SessionProvider.GetOrCreateNewSession();

			var client = new RestClient("https://api.betfair.com/exchange/betting/rest/v1.0/listMarketCatalogue/");
			client.Timeout = 20000;
			var request = new RestRequest(Method.POST);
			request.AddHeader("X-Application", appKeyAndSession.AppKey);
			request.AddHeader("X-Authentication", appKeyAndSession.Session);
			request.AddHeader("Content-Type", "application/json");
			string body = AddFilterConditions(request,
				new string[] { },//LeaguesIds
				new string[] { },//EventsIds
				marketTypeCodes,
				100,
				new string[] { "COMPETITION", "EVENT", "EVENT_TYPE", "MARKET_DESCRIPTION", "RUNNER_DESCRIPTION", "RUNNER_METADATA", "MARKET_START_TIME" },
				new string[] { "ODDS", "LINE" },
				"FIRST_TO_START");

			Loggers.GetIntance().Betfair.Debug($@"Lines Product:{appKeyAndSession.AppKey} token:{appKeyAndSession.Session} body:{body}");

			IRestResponse catalogueResponse = client.Execute(request);

			if (((int)catalogueResponse.StatusCode == 200) && (catalogueResponse.Content != "[]"))
			{
				List<NormalizedProviderResponse> result = new List<NormalizedProviderResponse>();
				Loggers.GetIntance().Betfair.Debug($@"Lines Product:{appKeyAndSession.AppKey} token:{appKeyAndSession.Session} body:{body} response:{catalogueResponse.Content}");

				JArray catalogueJson = JArray.Parse(catalogueResponse.Content);
				int amountOfChunks = 5;
				Chunks chunks = MakeChunks(catalogueJson, amountOfChunks);
				foreach (Chunk chunk in chunks.List())
				{

					try
					{
						PrizesByMarketId prizesByMarketId = SearchAllThePricesAndStatus(authentication, chunk.MarketsIds);
						MergePricesToTheMarkets(catalogueJson, prizesByMarketId);
					}
					catch (Exception e)
					{
						PlaceZeroPriceToTheMarkets(catalogueJson);
					}

					result.Add(new NormalizedProviderResponse(this, ResponseType.LINES, catalogueJson.ToString()));
				}

				marketIds = chunks.ListAllMarketIds();

				return result;
			}

			ErrorsSender.Send($"No Lines were retrieved from {this.GetType().Name} provider", $@"Lines Product:{appKeyAndSession.AppKey} token:{appKeyAndSession.Session} body:{body} response:{catalogueResponse.Content}");
			throw new GameEngineException($"No Lines were retrieved from {this.GetType().Name} provider");
		}

		private IEnumerable<string> MarketIds(string response)
		{
			var marketIds = new List<string>();
			dynamic linesUpdateFromTheAPI = JsonConvert.DeserializeObject(response);
			foreach (dynamic line in linesUpdateFromTheAPI)
			{
				string id = line.marketId;
				marketIds.Add(id);
			}
			return marketIds;
		}

		private Chunks MakeChunks(JArray catalogueJson, int amountOfItems)
		{
			Chunks chunks = new Chunks();
			var marketIds = new List<string>();
			int index = 0;
			foreach (JObject mainNode in catalogueJson.Children<JObject>())
			{
				string marketId = mainNode["marketId"].ToString();
				DateTime marketTime = FechaHora.ParseToLocalTime(mainNode["description"]["marketTime"].ToString());
				marketIds.Add(marketId);

				bool full = index % amountOfItems == 0;
				if (full)
				{
					Chunk chunk = chunks.CreateNewOne();
					chunk.Append(marketId, marketTime, mainNode);
					chunks.Add(chunk);
				}
				else 
				{
					chunks.Last().Append(marketId, marketTime, mainNode);
				}
				index++;
			}

			return chunks;
		}
		
		class Chunk
		{
			private List<string> marketIds = new List<string>();
			private List<JObject> markets = new List<JObject>();
			private Chunks parent;

			internal Chunk(Chunks parent)
			{
				this.parent = parent;
			}

			internal IEnumerable<string> MarketsIds { get { return marketIds; } }

			internal void Append(string marketId, DateTime marketTime,  JObject mainNode)
			{
				marketIds.Add(marketId);
				markets.Add(mainNode);

				parent.Add(marketId, marketTime);
			}
		}
		public class MarketIdsAndTimesPeer
		{
			public MarketIdsAndTimesPeer(string marketId, DateTime marketTime)
			{
				MarketId = marketId;
				MarketTime = marketTime;
			}
			public string MarketId { get; }
			public DateTime MarketTime { get; }
		}

		class Chunks
		{
			private List<Chunk> chunks = new List<Chunk>();
			private List<MarketIdsAndTimesPeer> marketIds = new List<MarketIdsAndTimesPeer>();

			internal void Add(string marketId, DateTime marketTime)
			{
				marketIds.Add(new MarketIdsAndTimesPeer(marketId, marketTime));
			}

			internal Chunk CreateNewOne()
			{
				return new Chunk(this);
			}
			
			internal void Add(Chunk chunk)
			{
				chunks.Add(chunk);
			}

			internal Chunk Last()
			{
				return chunks[chunks.Count-1];
			}

			internal IEnumerable<Chunk> List()
			{
				return chunks;
			}
			internal IEnumerable<MarketIdsAndTimesPeer> ListAllMarketIds()
			{
				return marketIds;
			}
		}

		internal class StatusAndPrizesByRunnerId
		{
			internal string Status { get; }
			internal string MarketId { get; }
			private Dictionary<string, double> prizeByRunnerId = new Dictionary<string, double>();

			internal StatusAndPrizesByRunnerId(string marketId, string status)
			{
				if (string.IsNullOrEmpty(status)) throw new GameEngineException(nameof(status));
				if (string.IsNullOrEmpty(marketId)) throw new GameEngineException(nameof(marketId));
				Status = status;
				MarketId = marketId;
			}
			internal void Add(int selectionId, string handicap,  double price)
			{
				if (string.IsNullOrEmpty(handicap)) throw new GameEngineException(nameof(handicap));

				prizeByRunnerId.Add(selectionId+handicap, price);
			}

			internal bool TryGetValue(int selectionId, string handicap, out double price)
			{
				if (string.IsNullOrEmpty(handicap)) throw new GameEngineException(nameof(handicap));

				return prizeByRunnerId.TryGetValue(selectionId + handicap, out price);
			}
		}
		internal class PrizesByMarketId
		{
			private Dictionary<string, StatusAndPrizesByRunnerId> prizeByRunnerId = new Dictionary<string, StatusAndPrizesByRunnerId>();

			internal void Add( StatusAndPrizesByRunnerId prizesByRunnerId)
			{
				if (prizesByRunnerId==null) throw new GameEngineException(nameof(prizesByRunnerId));

				prizeByRunnerId.Add(prizesByRunnerId.MarketId, prizesByRunnerId);
			}

			internal bool TryGetValue(string marketId, out StatusAndPrizesByRunnerId prizesByRunnerId)
			{
				if (string.IsNullOrEmpty(marketId)) throw new GameEngineException(nameof(marketId));

				return prizeByRunnerId.TryGetValue(marketId, out prizesByRunnerId);
			}

			internal IEnumerable<StatusAndPrizesByRunnerId> SearchClosedOnly()
			{
				return prizeByRunnerId.Values.Where(x => x.Status == "CLOSED");
			}
		}

		internal PrizesByMarketId SearchAllThePricesAndStatus(Authentication authentication, IEnumerable<string> marketsIds)
		{
			PrizesByMarketId prizesByMarketId = new PrizesByMarketId();
			var pricesContent = RequestPrices((BetFairAuthentication)authentication, marketsIds);

			JArray pricesJson = JArray.Parse(pricesContent);
			foreach (var mainNode in pricesJson.Children<JObject>())
			{
				var marketId = mainNode["marketId"].ToString();
				var status = mainNode["status"].ToString();
				var prizeByRunnerId = new StatusAndPrizesByRunnerId(marketId, status);
				foreach (var runnerNode in mainNode["runners"].Children<JObject>())
				{
					var selectionId = runnerNode["selectionId"];
					var handicap = runnerNode["handicap"];

					double price = 0;
					if (runnerNode["lastPriceTraded"] != null) price = Convert.ToDouble(runnerNode["lastPriceTraded"].ToString());
					else if(runnerNode["ex"]!= null &&
						runnerNode["ex"]["availableToBack"] != null &&
						runnerNode["ex"]["availableToBack"].Count() > 0 &&
						runnerNode["ex"]["availableToBack"].First["price"] != null) price = Convert.ToDouble(runnerNode["ex"]["availableToBack"].First["price"].ToString());
					else if (runnerNode["ex"] != null &&
						runnerNode["ex"]["availableToLay"] != null &&
						runnerNode["ex"]["availableToLay"].Count() > 0 &&
						runnerNode["ex"]["availableToLay"].First["price"] != null) price = Convert.ToDouble(runnerNode["ex"]["availableToLay"].First["price"].ToString());

					prizeByRunnerId.Add(
						Convert.ToInt32(selectionId.ToString()),
						handicap.ToString(),
						price);
				}
				prizesByMarketId.Add(prizeByRunnerId);
			}
			return prizesByMarketId;
		}
		internal IEnumerable<NormalizedProviderResponse> Lines(Authentication authentication, string[] sportIds, string[] eventIds, out IEnumerable<MarketIdsAndTimesPeer> marketIds)
		{
			if (authentication == null || !(authentication is BetFairAuthentication)) throw new GameEngineException(nameof(authentication));

			AppKeyAndSession appKeyAndSession = ((BetFairAuthentication)authentication).SessionProvider.GetOrCreateNewSession();

			var client = new RestClient("https://api.betfair.com/exchange/betting/rest/v1.0/listMarketCatalogue/");
			client.Timeout = 20000;
			var request = new RestRequest(Method.POST);
			request.AddHeader("X-Application", appKeyAndSession.AppKey);
			request.AddHeader("X-Authentication", appKeyAndSession.Session);
			request.AddHeader("Content-Type", "application/json");
			string body = AddFilterConditions(request,
				new string[] { },//LeaguesIds
				eventIds, 
				marketTypeCodes,
				100,
				marketProjections,
				new string[] { "ODDS", "LINE" },
				"FIRST_TO_START");

			Loggers.GetIntance().Betfair.Debug($@"Lines Product:{appKeyAndSession.AppKey} token:{appKeyAndSession.Session} body:{body}");

			IRestResponse catalogueResponse = client.Execute(request);

			if (((int)catalogueResponse.StatusCode == 200) && (catalogueResponse.Content != "[]"))
			{
				List<NormalizedProviderResponse> result = new List<NormalizedProviderResponse>();
				Loggers.GetIntance().Betfair.Debug($@"Lines Product:{appKeyAndSession.AppKey} token:{appKeyAndSession.Session} body:{body} response:{catalogueResponse.Content}");

				JArray catalogueJson = JArray.Parse(catalogueResponse.Content);
				int amountOfChunks = 5;
				Chunks chunks = MakeChunks(catalogueJson, amountOfChunks);
				foreach (Chunk chunk in chunks.List())
				{
					try
					{
						PrizesByMarketId prizesByMarketId = SearchAllThePricesAndStatus(authentication, chunk.MarketsIds);
						MergePricesToTheMarkets(catalogueJson, prizesByMarketId);
					}
					catch (Exception e)
					{
						PlaceZeroPriceToTheMarkets(catalogueJson);
					}

					result.Add(new NormalizedProviderResponse(this, ResponseType.LINES, catalogueJson.ToString()));
				}

				marketIds = chunks.ListAllMarketIds();

				return result;
			}

			ErrorsSender.Send($"No Lines were retrieved from {this.GetType().Name} provider", $@"Lines Product:{appKeyAndSession.AppKey} token:{appKeyAndSession.Session} body:{body} response:{catalogueResponse.Content}");
			throw new GameEngineException($"No Lines were retrieved from {this.GetType().Name} provider");
		}

		private void PlaceZeroPriceToTheMarkets(JArray catalogueJson)
		{
			foreach (var mainNode in catalogueJson.Children<JObject>())
			{
				var marketId = mainNode["marketId"];
				if(mainNode["status"]==null)mainNode.Add("status", "INACTIVE");
				foreach (var runnerNode in mainNode["runners"].Children<JObject>())
				{
					var selectionId = Convert.ToInt32(runnerNode["selectionId"].ToString());
					var handicap = runnerNode["handicap"].ToString();
					double price=0;
					if (runnerNode["price"] == null) runnerNode.Add("price", price);
				}
			}
		}
		private void MergePricesToTheMarkets(JArray catalogueJson, PrizesByMarketId prizesByMarketId)
		{
			foreach (var mainNode in catalogueJson.Children<JObject>())
			{
				var marketId = mainNode["marketId"];

				StatusAndPrizesByRunnerId priceInfo = null;
				if (prizesByMarketId.TryGetValue(marketId.ToString(), out priceInfo))
				{
					if (mainNode["status"] == null) mainNode.Add("status", priceInfo.Status);
					foreach (var runnerNode in mainNode["runners"].Children<JObject>())
					{
						var selectionId = Convert.ToInt32(runnerNode["selectionId"].ToString());
						var handicap = runnerNode["handicap"].ToString();
						double price;
						if (priceInfo.TryGetValue(selectionId, handicap, out price))
						{
							if (runnerNode["price"] == null) runnerNode.Add("price", price);
						}
					}
				}

			}
		}

		string RequestPrices(BetFairAuthentication authentication, IEnumerable<string> marketIds)
        {
			AppKeyAndSession appKeyAndSession = ((BetFairAuthentication)authentication).SessionProvider.GetOrCreateNewSession();
			var client = new RestClient("https://api.betfair.com/exchange/betting/rest/v1.0/listMarketBook/");
			client.Timeout = 2000;
			var request = new RestRequest(Method.POST);
			request.AddHeader("X-Application", appKeyAndSession.AppKey);
			request.AddHeader("X-Authentication", appKeyAndSession.Session);
			request.AddHeader("Content-Type", "application/json");

			string body = "{\r\n        \"marketIds\": [\"" + string.Join("\",\"", marketIds) + "\"],\r\n        \"priceProjection\": {\r\n            \"priceData\": [\"SP_AVAILABLE\",\"SP_TRADED\",\"EX_BEST_OFFERS\",\"EX_ALL_OFFERS\",\"EX_TRADED\"],\r\n            \"virtualise\": \"true\"\r\n        }\r\n}\r\n\r\n";
			request.AddParameter("application/json", body, ParameterType.RequestBody);

			Loggers.GetIntance().Betfair.Debug($@"Line Product:{appKeyAndSession.AppKey} token:{appKeyAndSession.Session} body:{body}");

			IRestResponse response = client.Execute(request);
			if (((int)response.StatusCode == 200) && (response.Content != "[]"))
			{
				Loggers.GetIntance().Betfair.Debug($@"Line Product:{appKeyAndSession.AppKey} token:{appKeyAndSession.Session} body:{body} response:{response.Content}");

				return response.Content;
			}

			ErrorsSender.Send($"No Line were retrieved from {this.GetType().Name} provider", $@"Line Product:{appKeyAndSession.AppKey} token:{appKeyAndSession.Session} body:{body} response:{response.Content}");
			throw new GameEngineException($"No Line were retrieved from {this.GetType().Name} provider");
		}

		internal NormalizedProviderResponse Line(BetFairAuthentication authentication, params string[] marketIds)
		{
			if (authentication == null || !(authentication is BetFairAuthentication)) throw new GameEngineException(nameof(authentication));
			if (marketIds == null) throw new GameEngineException(nameof(marketIds));

			AppKeyAndSession appKeyAndSession = ((BetFairAuthentication)authentication).SessionProvider.GetOrCreateNewSession();

			var client = new RestClient("https://api.betfair.com/exchange/betting/rest/v1.0/listMarketBook/");
			client.Timeout = -1;
			var request = new RestRequest(Method.POST);
			request.AddHeader("X-Application", appKeyAndSession.AppKey);
			request.AddHeader("X-Authentication", appKeyAndSession.Session);
			request.AddHeader("Content-Type", "application/json");
			
			string body = "{\r\n        \"marketIds\": [\""+ string.Join("\",\"", marketIds)+"\"],\r\n        \"priceProjection\": {\r\n            \"priceData\": [\"SP_AVAILABLE\",\"SP_TRADED\",\"EX_BEST_OFFERS\",\"EX_ALL_OFFERS\",\"EX_TRADED\"],\r\n            \"virtualise\": \"true\"\r\n        }\r\n}\r\n\r\n[{\"jsonrpc\": \"2.0\", \"method\": \"SportsAPING/v1.0/listMarketBook\", \"params\": {\"marketIds\":[\"**********\"],\"priceProjection\":{\"priceData\":[\"SP_AVAILABLE\",\"SP_TRADED\",\"EX_BEST_OFFERS\",\"EX_ALL_OFFERS\",\"EX_TRADED\"]}}, \"id\": 1}]";
			request.AddParameter("application/json", body, ParameterType.RequestBody);

			Loggers.GetIntance().Betfair.Debug($@"Line Product:{appKeyAndSession.AppKey} token:{appKeyAndSession.Session} body:{body}");

			IRestResponse response = client.Execute(request);

			if ((int)response.StatusCode == 200)
			{
				Loggers.GetIntance().Betfair.Debug($@"Line Product:{appKeyAndSession.AppKey} token:{appKeyAndSession.Session} body:{body} response:{response.Content}");

				return new NormalizedProviderResponse(this, ResponseType.LINE, response.Content);
			}

			ErrorsSender.Send($"No Line were retrieved from {this.GetType().Name} provider", $@"Line Product:{appKeyAndSession.AppKey} token:{appKeyAndSession.Session} body:{body} response:{response.Content}");
			throw new GameEngineException($"No Line were retrieved from {this.GetType().Name} provider");
		}
		internal class BetFairAuthentication : Authentication
		{
			internal AppKeyAndSessionProvider SessionProvider { get; }
			public BetFairAuthentication(AppKeyAndSessionProvider sessionProvider)
			{
				SessionProvider = sessionProvider;
			}
		}
	}
}
