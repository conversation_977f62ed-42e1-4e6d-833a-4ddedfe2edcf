﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using ExternalServices;
using GamesEngine.Business;
using GamesEngine.Domains;
using GamesEngine.Exchange;
using GamesEngine.Finance;
using GamesEngine.Gameboards;
using GamesEngine.Gameboards.Lotto;
using Puppeteer.EventSourcing;
using Puppeteer.EventSourcing.Libraries;

namespace GamesEngine.PurchaseOrders
{
	internal abstract class Order : Objeto
	{
		internal enum OrderState
		{
			/// <summary>One or more products have been added to the shopping cart</summary>
			CART,
			/// <summary>Order was placed and it is ready for being processed</summary>
			PLACED,
			/// <summary>Store is ready to procces the order, Payment was confirmed</summary>
			PAYMENT,
			/// <summary>Order has successfully completed all of the previous states</summary>
			COMPLETED
		}

		private const int UNASSIGNED_AUTHORIZATION_ID = int.MinValue + 17;
		protected int authorizationId = UNASSIGNED_AUTHORIZATION_ID;
		protected bool valid = true;
		private readonly Customer customer;
		private readonly Coin coin;
		protected readonly Items items;
		private int number;
		private int nextBetNumber;

		internal Order(Customer customer, int orderNumber, Coin coin)
		{
			if (customer == null) throw new ArgumentNullException(nameof(customer));
			if (customer.Company.Sales.CurrentStore == Store.EMPTY) throw new GameEngineException("Store is still empty, it needs to be configured to sale");

			this.number = orderNumber;
			this.customer = customer;
			this.coin = coin;
			items = new Items(customer);
		}

		protected Order(Order oldOrder)
		{
			if (oldOrder == null) throw new ArgumentNullException(nameof(oldOrder));
			items = oldOrder.items;
			customer = oldOrder.customer;
			coin = oldOrder.coin;
			number = oldOrder.number;
			authorizationId = oldOrder.authorizationId;
			nextBetNumber = oldOrder.nextBetNumber;
			ToWin = oldOrder.ToWin;
			Agent = oldOrder.Agent;
			ProcessorKey = oldOrder.ProcessorKey;
			if (oldOrder.AuthorizationIds != null) AuthorizationIds = oldOrder.AuthorizationIds;
		}
		internal Coin Coin { get { return coin; } }
		internal Customer Customer
		{
			get
			{
				return customer;
			}
		}

		internal int NextBetNumber
		{
			get
			{
				return this.nextBetNumber;
			}

			set
			{
				this.nextBetNumber = value;
			}
		}

		internal int Number
		{
			set
			{
				this.number = value;

			}
			get
			{
				return number;
			}
		}

		internal bool HasItems
		{
			get
			{
				return ! items.Empty;
			}
		}

		internal int CountOfItems
		{
			get
			{
				return this.items.Count;
			}
		}

		internal IEnumerable<Item> Items
		{
			get
			{
				return this.items;
			}
		}

		internal virtual bool CanBeModified
		{
			get
			{
				return false;
			}
		}

		internal virtual OrderState State
		{
			get
			{
				throw new GameEngineException("State must be implement by classes that Inherit from Order.");
			}
		}

		internal Company Company
		{
			get
			{
				return this.customer.Company;
			}
		}

		public decimal Total()
		{
			return items.Subtotal;
			
		}

		internal Products ProductsOnOrder()
		{
			return items.ProductsOnItems();
		}

		internal void Invalidate()
		{
			valid = false;
		}

		protected bool IsValid()
		{
		   return valid;
		}

		internal string Description
		{
			get
			{
				return items.Description();
			}
		}

		internal void ConfigureWithSingleAuthorization(int lowBetId, int orderNumber, int authorizationId)
		{
			NextBetNumber = lowBetId;
			Number = orderNumber;
			this.authorizationId = authorizationId;
		}

		internal void ConfigureWithMultipleAuthorization(int lowBetId, int orderNumber, IEnumerable<int> authorizationIds, string processorKey)
        {
			NextBetNumber = lowBetId;
			Number = orderNumber;
            AuthorizationIds = authorizationIds.ToList();
			ProcessorKey = processorKey;
		}

		internal int GetAuthorizationId(int indexInOrder)
		{
			if (IsMultipleAuthorization) return AuthorizationIds.ElementAt(indexInOrder);
			return authorizationId;
		}

        internal string ProcessorKey { get; set; }

        internal int AuthorizationId
		{
			get
			{
				if (this.authorizationId == UNASSIGNED_AUTHORIZATION_ID) throw new GameEngineException("Authorization id has not been set");
				return authorizationId;
			}
			set
			{
				authorizationId = value;
			}
		}

		internal List<int> AuthorizationIds { get; private set; }

		internal bool IsMultipleAuthorization
		{
			get
			{
				return AuthorizationIds != null;
			}
		}

        internal decimal ToWin { get; set; }

		internal string CurrencyCode
		{
			get
			{
				return coin.Iso4217Code;
			}
		}

		private Domain domain;
		internal Domain Domain
		{
			get
			{
				return domain;
			}
			set
			{
				if (domain != null) throw new GameEngineException($"{nameof(domain)} only can be assigned once");
				domain = value;
			}
		}

		private Agent agent;
		internal Agent Agent
		{
			get
			{
				return agent;
			}
			set
			{
				if (agent != null) throw new GameEngineException($"{nameof(agent)} only can be assigned once");
				agent = value;
			}
		}

		internal bool HasSeller => agent != null;
	}
}
