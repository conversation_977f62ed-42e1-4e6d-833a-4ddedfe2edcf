<Project Sdk="Microsoft.NET.Sdk.Web">
  <PropertyGroup>
    <TargetFramework>net5.0</TargetFramework>
  </PropertyGroup>
  <ItemGroup>
    <Compile Include="..\ExchangeAPI\Controllers\CatalogConsumer.cs" Link="Controllers\CatalogConsumer.cs" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="Elastic.Apm.NetCoreAll" Version="1.8.1" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="3.1.7" />
    <PackageReference Include="log4net" Version="2.0.8" />
    <PackageReference Include="Microsoft.AspNetCore.Http" Version="2.2.2" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.NewtonsoftJson" Version="3.1.7" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="5.6.3" />
    <PackageReference Include="Microsoft.OpenApi" Version="1.2.3" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\GamesEngineMocks\GamesEngineMocks.csproj" />
    <ProjectReference Include="..\GamesEngine\GamesEngine.csproj" />
  </ItemGroup>
</Project>