﻿using Connectors.town.connectors.drivers.hades.tenant;
using GamesEngine;
using GamesEngine.Bets;
using GamesEngine.Business;
using GamesEngine.Business.Marketing;
using GamesEngine.Finance;
using GamesEngine.Gamification.Badges;
using GamesEngine.Messaging;
using GamesEngine.PurchaseOrders;
using GamesEngine.Settings;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using System.Text;
using System.Threading.Tasks;
using town.connectors;
using static GamesEngine.Exchange.PaymentProcessorsAndActionsByDomains;
using static GamesEngine.Finance.PaymentChannels;
using static GamesEngine.Finance.Currencies;
using static LoyaltyAPI.Controllers.LoyaltyController;
using static town.connectors.CustomSettings;
using town.connectors.drivers.artemis;
using Connectors.town.connectors.drivers.artemis;
using GamesEngine.Marketing.Campaigns;

namespace LoyaltyAPI.Controllers
{
	public class LoyaltyController : AuthorizeController
	{
		[HttpGet("api/loyalty/lines/player/notification")]
        [Authorize(Roles = "player")]
        public async Task<IActionResult> MyNotificationsAsync()
		{
			string playerId = Validator.StringEscape(Security.PlayerId(User));

			var result = await LoyaltyAPI.Loyalty.PerformQryAsync(HttpContext, $@"
				{{
					playerQry = company.CustomerByPlayerId('{playerId}').Player;
                    print playerQry.Messages.Count(now) countNotifications;
                    print Now now;
                    for (recent : playerQry.Messages.ReadAllMessages(now))
                    {{
                        print recent.Body 'body';
                        print recent.CreationDate 'creationDate';
                        print Now 'now';
						print recent.Importance.Name 'importance';
                    }}
                }}
			");

			var x = await LoyaltyAPI.Loyalty.PerformCmdAsync(HttpContext, $@"
                {{
					player = company.Players.SearchPlayer('{playerId}');
					player.Messages.MarkAllMessagesAsRead(now);
                }}
            ");

			return result;
		}

		[HttpPut("api/loyalty/player")]
        [Authorize(Roles = "player")]
        public async Task<IActionResult> UpdatePlayerAsync([FromBody] Player info)
		{
			if (info == null) return NotFound($"{nameof(info)} has not a valid value");
			if (String.IsNullOrWhiteSpace(info.Nickname)) return NotFound("Parameter Nickname is required");

			string playerId = Validator.StringEscape(Security.PlayerId(User));
			var nickNameText = info.Nickname.Length <= 128 ? info.Nickname : info.Nickname.Substring(0, 128);
			string scriptAvatar = "";
			var avatar = string.IsNullOrWhiteSpace(info.Avatar) ? info.Avatar : Validator.StringEscape(info.Avatar);

			if (!String.IsNullOrWhiteSpace(avatar))
			{
				scriptAvatar = $"player.UpdateAvatar('{avatar}', Now);";
			}
			var nickname = Validator.StringEscape(nickNameText.Trim());
			IActionResult result = await LoyaltyAPI.Loyalty.PerformCmdAsync(HttpContext, $@"
                {{
                    player = company.Players.SearchPlayer('{playerId}');
                    isNickNameAlreadyTaken = company.IsThisNickNameAlreadyTakenByAnOtherUser(player, '{nickname}');
                    print isNickNameAlreadyTaken isNickNameAlreadyTaken;
                    if(!isNickNameAlreadyTaken)
                    {{
                        player.ValidateAndUpdateNickname(itIsThePresent, '{nickname}');
                        {scriptAvatar}
                    }}
                }}
            ");
			return result;
		}

		[HttpPut("api/loyalty/player/avatar")]
        [Authorize(Roles = "player")]
        public async Task<IActionResult> UpdatePlayerAvatarAsync([FromBody] Player info)
		{
			if (info == null) return NotFound($"{nameof(info)} has not a valid value");
			if (String.IsNullOrWhiteSpace(info.Avatar)) return NotFound("Parameter Avatar is required");

			string playerId = Validator.StringEscape(Security.PlayerId(User));
			var avatar =  Validator.StringEscape(info.Avatar);

			IActionResult result = await LoyaltyAPI.Loyalty.PerformCmdAsync(HttpContext, $@"
                {{
                    player = company.Players.SearchPlayer('{playerId}');
					player.UpdateAvatar('{avatar}', Now);
					Notify Information 'Successfully updated.';
                }}
            ");
			return result;
		}

		[HttpPut("api/loyalty/player/nickname")]
        [Authorize(Roles = "player")]
        public async Task<IActionResult> UpdatePlayerNicknameAsync([FromBody] Player info)
		{
			if (info == null) return NotFound($"{nameof(info)} has not a valid value");
			if (String.IsNullOrWhiteSpace(info.Nickname)) return NotFound("Parameter Nickname is required");

			string playerId = Validator.StringEscape(Security.PlayerId(User));
			var nickNameText = info.Nickname.Length <= 128 ? info.Nickname : info.Nickname.Substring(0, 128);
			
			var nickname = Validator.StringEscape(nickNameText.Trim());
			IActionResult result = await LoyaltyAPI.Loyalty.PerformCmdAsync(HttpContext, $@"
                {{
                    player = company.Players.SearchPlayer('{playerId}');
                    isNickNameAlreadyTaken = company.IsThisNickNameAlreadyTakenByAnOtherUser(player, '{nickname}');
                    print isNickNameAlreadyTaken isNickNameAlreadyTaken;
                    if(!isNickNameAlreadyTaken)
                    {{
                        player.ValidateAndUpdateNickname(itIsThePresent, '{nickname}');
                    }}
                }}
            ");
			return result;
		}

		[HttpPost("api/loyalty/players")]
        [Authorize(Roles = "player")]
        public async Task<IActionResult> AvatarAndNicknamesAsync([FromBody] ListOfPlayersIds players)
		{
			if (players == null || players.PlayerIds == null || players.PlayerIds.Length == 0) return BadRequest("Parameter players is required");
			string playersIds = string.Join(",", players.PlayerIds);

			IActionResult result = await LoyaltyAPI.Loyalty.PerformQryAsync(HttpContext, $@"
                 {{
                     for( allPlayers : company.SearchPlayersWithThisIds('{playersIds}'))
                     {{
                         print allPlayers.Avatar.Path avatar;
                         print allPlayers.Nickname nickname;
                         print allPlayers.Id playerId;
                     }}
                 }}
             ");
			return result;
		}

		[HttpPost("api/loyalty/player/positivefeedback")]
        [Authorize(Roles = "player")]
        public async Task<IActionResult> SendPositiveFeedbackAsync([FromBody] Message message)
		{
			if (message == null) return NotFound($"{nameof(message)} has not a valid value");
			if (String.IsNullOrWhiteSpace(message.Body)) return NotFound("Parameter body is required");
			var bodyText = message.Body.Length <= 1024 ? message.Body : message.Body.Substring(0, 1024);
			bodyText = Validator.StringEscape(bodyText);
			string playerId = Validator.StringEscape(Security.PlayerId(User));

			IActionResult result = await LoyaltyAPI.Loyalty.PerformCmdAsync(HttpContext, $@"
                {{
                    player = company.Players.SearchPlayer('{playerId}');
					campaign = suggestionBoxFeedbackCampaign{message.StoreId};
                    player.SendPositiveFeedback(itIsThePresent, campaign, '{bodyText}', Now);
                }}
            ");
			return result;
		}

		[HttpPost("api/loyalty/player/negativefeedback")]
        [Authorize(Roles = "player")]
        public async Task<IActionResult> SendNegativeFeedbackAsync([FromBody] Message message)
		{
			if (message == null) return NotFound($"{nameof(message)} has not a valid value");
			if (String.IsNullOrWhiteSpace(message.Body)) return NotFound("Parameter body is required");
			if (String.IsNullOrWhiteSpace(message.Device)) return NotFound("Parameter device is required");
			if (String.IsNullOrWhiteSpace(message.Browser)) return NotFound("Parameter Browser is required");

			var bodyText = message.Body.Length <= 1024 ? message.Body : message.Body.Substring(0, 1024);
			bodyText = Validator.StringEscape(bodyText);
			var deviceName = message.Device;
			var browserName = message.Browser;
			string playerId = Validator.StringEscape(Security.PlayerId(User));

			IActionResult result = await LoyaltyAPI.Loyalty.PerformCmdAsync(HttpContext, $@"
                {{
                    player = company.Players.SearchPlayer('{playerId}');
					campaign = suggestionBoxFeedbackCampaign{message.StoreId};
                    player.SendNegativeFeedback(itIsThePresent, campaign, '{bodyText}', Now, '{deviceName}', '{browserName}');
                }}
            ");
			return result;
		}

		[HttpPost("api/loyalty/feedback/{id:int}/attach")]
        [Authorize(Roles = "player")]
        public async Task<IActionResult> AttachFeedbackToAGameboardAsync(int id, [FromBody] AttachFeedback attach)
		{
			if (id <= 0) return NotFound("Parameter feedbackId is required");
			if (String.IsNullOrWhiteSpace(attach.GameboardId)) return NotFound("Parameter gameboardId is required");

			IActionResult result = await LoyaltyAPI.Loyalty.PerformCmdAsync(HttpContext, $@"
				{{
					campaign = suggestionBoxFeedbackCampaign{attach.StoreId};
					campaign.AttachGameboardNumberToFeedback(itIsThePresent, {id}, '{attach.GameboardId}');
				}}
            ");
			return result;
		}

		[HttpGet("api/loyalty/{storeId:int}/feedback/notification/all")]
		[Authorize(Roles = "Notifications")]
		public async Task<IActionResult> GetAllFeedbackAsync(int storeId)
		{
            if (storeId <= 0) return BadRequest($"Parameter {nameof(storeId)} is not valid");

			var result = await LoyaltyAPI.Loyalty.PerformQryAsync(HttpContext, $@"
				{{
					campaign =  suggestionBoxFeedbackCampaign{storeId};
					allFeedbackMessages = campaign.AllFeedbackMessages;
					for (message : allFeedbackMessages)
					{{
						print message.Id notificationId;
						print message.isPositive() isPossitive;
						print message.Body body;
						print company.CustomerByPlayer(message.Player).AccountNumber 'accountNumber';
						print message.IsRead isRead;
						print message.HasAReply hasAReply;
						print message.HasGameboardAttached hasGameboardAttached;
						if(message.HasGameboardAttached)
						{{
							print message.GameboardNumber gameboardAttached;
						}}
						else
						{{
							print '-' gameboardAttached;
						}}
						if(message.HasAReply)
						{{
							print message.RepliedDate repliedDate;
						}}
						else
						{{
							print '-' repliedDate;
						}}
                        print message.CreationDate creationDate;
                        print Now now;
						if(! message.isPositive())
						{{
							negative = message;
							print negative.DeviceName deviceName;
							print negative.BrowserName browserName;
						}}
						else
						{{
							print '-' deviceName;
							print '-' browserName;
						}}
					}}
				}}
            ");
			return result;
		}

		private string scriptToPrintNotifications = $@"print pagedMessages.Messages.Count() count;
													print pagedMessages.RecordsTotal recordsTotal;
													for (message : pagedMessages.Messages)
													{{
														print message.Id notificationId;
														print message.isPositive() isPossitive;
														print message.Body body;
														print message.AccountNumber 'accountNumber';
														print message.IsRead isRead;
														print message.HasAReply hasAReply;
														print message.HasGameboardAttached hasGameboardAttached;
														if(message.HasGameboardAttached)
														{{
															print message.GameboardNumber gameboardAttached;
														}}
														else
														{{
															print '-' gameboardAttached;
														}}
														if(message.HasAReply)
														{{
															print message.RepliedDate repliedDate;
														}}
														else
														{{
															print '-' repliedDate;
														}}
														print message.CreationDate creationDate;
														print Now now;
														if(! message.isPositive())
														{{
															print message.DeviceName deviceName;
															print message.BrowserName browserName;
														}}
														else
														{{
															print '-' deviceName;
															print '-' browserName;
														}}
													}}";

		[HttpGet("api/loyalty/{storeId:int}/feedback/notification")]
		[Authorize(Roles = "Notifications")]
		public async Task<IActionResult> FeedbacksAsync(int storeId, string feedbackType, DateTime startDate, DateTime endDate, int initialIndex, int amountOfRows)
		{
			if (storeId <= 0) return BadRequest($"Parameter {nameof(storeId)} is not valid");
			if (string.IsNullOrWhiteSpace(feedbackType)) return BadRequest($"Parameter {nameof(feedbackType)} is required");
			if (startDate == default(DateTime)) return BadRequest($"Parameter {nameof(startDate)} is required");
			if (endDate == default(DateTime)) return BadRequest($"Parameter {nameof(endDate)} is required");
			if (startDate.Hour != 0 || startDate.Minute != 0 || startDate.Second != 0) return BadRequest($"{nameof(startDate)} {startDate} is not valid");
			if (endDate.Hour != 0 || endDate.Minute != 0 || endDate.Second != 0) return BadRequest($"{nameof(endDate)} {endDate} is not valid");
			if (initialIndex < 0) return BadRequest($"{nameof(initialIndex)} must be greater or equal than 0");
			if (amountOfRows <= 0) return BadRequest($"{nameof(amountOfRows)} must be greater than 0");

			var normalizedStartDate = $"{startDate.Month}/{startDate.Day}/{startDate.Year}";
			var normalizedEndDate = $"{endDate.Month}/{endDate.Day}/{endDate.Year}";
			var normalizedFeedbackType = feedbackType.Trim().ToLower();

			var result = await LoyaltyAPI.Loyalty.PerformQryAsync(HttpContext, $@"
				{{
					campaign = suggestionBoxFeedbackCampaign{storeId};
					pagedMessages = campaign.FeedbackMessages('{normalizedFeedbackType}', {normalizedStartDate}, {normalizedEndDate}, {initialIndex}, {amountOfRows});
					{scriptToPrintNotifications}
				}}
            ");
			return result;
		}

		[HttpGet("api/loyalty/{storeId:int}/feedback/notificationsWithoutPagination")]
		[Authorize(Roles = "Notifications")]
		public async Task<IActionResult> FeedbacksWithoutPaginationAsync(int storeId, string feedbackType, DateTime startDate, DateTime endDate)
		{
			if (storeId <= 0) return BadRequest($"Parameter {nameof(storeId)} is not valid");
			if (string.IsNullOrWhiteSpace(feedbackType)) return BadRequest($"Parameter {nameof(feedbackType)} is required");
			if (startDate == default(DateTime)) return BadRequest($"Parameter {nameof(startDate)} is required");
			if (endDate == default(DateTime)) return BadRequest($"Parameter {nameof(endDate)} is required");
			if (startDate.Hour != 0 || startDate.Minute != 0 || startDate.Second != 0) return BadRequest($"{nameof(startDate)} {startDate} is not valid");
			if (endDate.Hour != 0 || endDate.Minute != 0 || endDate.Second != 0) return BadRequest($"{nameof(endDate)} {endDate} is not valid");

			var normalizedStartDate = $"{startDate.Month}/{startDate.Day}/{startDate.Year}";
			var normalizedEndDate = $"{endDate.Month}/{endDate.Day}/{endDate.Year}";
			var normalizedFeedbackType = feedbackType.Trim().ToLower();

			var result = await LoyaltyAPI.Loyalty.PerformQryAsync(HttpContext, $@"
				{{
					campaign = suggestionBoxFeedbackCampaign{storeId};
					pagedMessages = campaign.FeedbackMessagesWithoutPagination('{normalizedFeedbackType}', {normalizedStartDate}, {normalizedEndDate});
					{scriptToPrintNotifications}
				}}
            ");
			return result;
		}

		[HttpPost("api/loyalty/feedback/{id:int}/reply")]
		[Authorize(Roles = "ly05,g5")]
		public async Task<IActionResult> ReplyFeedbackAsync(int id, [FromBody] Message message)
		{
			if (message == null) return NotFound($"{nameof(message)} has not a valid value");
			if (id <= 0) return NotFound("Parameter Id is required");
			if (String.IsNullOrWhiteSpace(message.Body)) return NotFound("Parameter body is required");

			var bodyText = message.Body.Length <= 1024 ? message.Body : message.Body.Substring(0, 1024);
			bodyText = Validator.StringEscape(bodyText);
			IActionResult result = await LoyaltyAPI.Loyalty.PerformCmdAsync(HttpContext, $@"
                {{
					campaign = suggestionBoxFeedbackCampaign{message.StoreId};
                    campaign.SendAReplyMessage(itIsThePresent, {id}, Now, '{bodyText}');
                }}
            ");
			return result;
		}

		[HttpGet("api/loyalty/{storeId:int}/replies")]
        [Authorize(Roles = "player")]
        public async Task<IActionResult> LastReplyMessagesAsync(int storeId)
		{
			var result = await LoyaltyAPI.Loyalty.PerformQryAsync(HttpContext, $@"
				{{
					campaign =  suggestionBoxFeedbackCampaign{storeId};
                    for (replies : campaign.Replies.LastReplies())
                    {{
						print replies.Body reply;
                    }}
                }}
            ");
			return result;
		}

		[HttpGet("api/loyalty/{storeId:int}/feedback/{id:int}/replies")]
		[Authorize(Roles = "ly04,g6")]
		public async Task<IActionResult> RepliesForSpecificFeedbackAsync(int storeId, int id)
		{
			if (id <= 0) return NotFound("Parameter Id is required");

			var result = await LoyaltyAPI.Loyalty.PerformQryAsync(HttpContext, $@"
				{{
					campaign =  suggestionBoxFeedbackCampaign{storeId};
                    for (replies : campaign.Replies.RepliesRelatedToFeedback({id}))
                    {{
						print replies.Body reply;
						print replies.RepliedDate repliedDate;
                    }}
                }}
            ");
			return result;
		}

		[HttpPost("api/loyalty/{storeId}/notification/all")]
		[Authorize(Roles = "g8,ly06")]
		public async Task<IActionResult> CreateNewExchangeNotificationToAllAsync(int storeId, [FromBody] Announcement message)
		{
			if (storeId <= 0) return BadRequest($"Parameter {nameof(storeId)} is not valid");
			if (message == null) return BadRequest($"{nameof(message)} has not a valid value");
			if (String.IsNullOrWhiteSpace(message.Subject)) return BadRequest("Parameter subject is required");
			if (String.IsNullOrWhiteSpace(message.Template)) return BadRequest("Parameter template is required");

			var messageText = message.Template.Length <= 1024 ? message.Template : message.Template.Substring(0, 1024);
			messageText = Validator.StringEscape(messageText);
			var subjectText = Validator.StringEscape(message.Subject);
			IActionResult result = await LoyaltyAPI.Loyalty.PerformCmdAsync(HttpContext, $@"
				{{
					announcements = announcementsCampaign{storeId};
					announcement = announcements.CreateAnnouncementCampaign(itIsThePresent, Now, '{subjectText}', '{messageText}');
					company.SendAnnouncementTo(itIsThePresent, announcement, 'all', Now, '{message.EmployeeName}');
				}}
            ");
			return result;
		}


		[HttpPost("api/loyalty/{storeId}/notification/custom")]
		[Authorize(Roles = "g8,ly06")]
		public async Task<IActionResult> CreateNewLinesNotificationToCustomAsync(int storeId, [FromBody] Announcement message)
		{
			if (storeId <= 0) return BadRequest($"Parameter {nameof(storeId)} is not valid");
			if (message == null) return BadRequest($"{nameof(message)} has not a valid value");
			if (String.IsNullOrWhiteSpace(message.Subject)) return BadRequest("Parameter subject is required");
			if (String.IsNullOrWhiteSpace(message.Template)) return BadRequest("Parameter template is required");
			if (String.IsNullOrWhiteSpace(message.Criteria)) return BadRequest("Parameter criteria is required");

			var messageText = message.Template.Length <= 1024 ? message.Template : message.Template.Substring(0, 1024);
			messageText = Validator.StringEscape(messageText);
			var subjectText = Validator.StringEscape(message.Subject);
			var criteriaText = Validator.StringEscape(message.Criteria);
			IActionResult result = await LoyaltyAPI.Loyalty.PerformCmdAsync(HttpContext, $@"
				{{
					announcements = announcementsCampaign{storeId};
					announcement = announcements.CreateAnnouncementCampaign(itIsThePresent, Now, '{subjectText}', '{messageText}');
					company.SendAnnouncementTo(itIsThePresent, announcement, '{criteriaText}', Now, '{message.EmployeeName}');
				}}
            ");
			return result;
		}

		[HttpGet("api/loyalty/{storeId}/announcements")]
		[Authorize (Roles = "g7,ly02")]
		public async Task<IActionResult> LinesAnnouncementsAsync(int storeId)
		{
			if (storeId <= 0) return BadRequest($"Parameter {nameof(storeId)} is not valid");

			var result = await LoyaltyAPI.Loyalty.PerformQryAsync(HttpContext, $@"
                {{
					announcements = announcementsCampaign{storeId};
                    for (announcement : announcements.GetAll)
                    {{
                        print announcement.WasSent wasSent;
                        print announcement.CreatedDate createdDate;
                        print announcement.SentDate sentDate;
                        print announcement.Subject subject;
                        print announcement.Template.GetTemplate message;
						print announcement.SentBy sentBy;
						print announcement.SegmentType() target;
						print announcement.Criteria criteria;
                        print Now now;
                    }}
                }}
            ");
			return result;
		}

		[HttpGet("api/loyalty/player/notification/count/{from}")]
        [Authorize(Roles = "player")]
        public async Task<IActionResult> CountNotificationAsync(string from)
		{
			if (!Validator.IsValidPlayer(from)) return NotFound($"{nameof(from)} has not a valid ID");

			string playerId = Validator.StringEscape(Security.PlayerId(User));

			var result = await LoyaltyAPI.Loyalty.PerformQryAsync(HttpContext, $@"
            {{
				player = company.Players.SearchPlayer('{playerId}');
                print player.CountMessages({from}) countNotifications;
            }}
            ");
			return result;
		}

		[HttpDelete("api/loyalty/player/notification")]
        [Authorize(Roles = "player")]
        public async Task<IActionResult> DeleteMessagesAsync()
		{
			string playerId = Validator.StringEscape(Security.PlayerId(User));
			var result = await LoyaltyAPI.Loyalty.PerformCmdAsync(HttpContext, $@"
            {{
				player = company.Players.SearchPlayer('{playerId}');
                player.RemoveMessages();
            }}
            ");
			return result;
		}

		[HttpDelete("api/loyalty/{storeId:int}/notification/{id:int}")]
		[Authorize(Roles = "administrator,assistant,support,i")]
		public async Task<IActionResult> DeleteMessageAsync(int storeId, int id)
		{
			if (storeId <= 0) return BadRequest($"{nameof(storeId)} is not a valid");
			if (id <= 0) return BadRequest($"{nameof(id)} is not a valid");

			var result = await LoyaltyAPI.Loyalty.PerformCmdAsync(HttpContext, $@"
                {{
					campaign = suggestionBoxFeedbackCampaign{storeId};
                    campaign.DeleteMessageById(itIsThePresent, {id});
                }}
            ");
			return result;
		}

		[HttpPut("api/loyalty/{storeId:int}/notification/{id:int}")]
		[Authorize(Roles = "Notifications")]
		public async Task<IActionResult> MarkMessageAsReadAsync(int storeId, int id)
		{
			if (storeId <= 0) return BadRequest($"{nameof(storeId)} is not a valid");
			if (id <= 0) return BadRequest($"{nameof(id)} is not a valid");

			var result = await LoyaltyAPI.Loyalty.PerformCmdAsync(HttpContext, $@"
                {{
					campaign = suggestionBoxFeedbackCampaign{storeId};
                    campaign.MarkFeedbackAsReadById(itIsThePresent, {id});
                }}
            ");
			return result;
		}

		[HttpGet("api/player/avatar/toBeApproved")]
		[Authorize(Roles = "b43,k,g3,ly01")]
		public async Task<IActionResult> AvatarsToBeApprovedAsync(string gameName, int initialIndex, int finalIndex)
		{
			if (string.IsNullOrWhiteSpace(gameName)) return NotFound($"{nameof(gameName)} has not a game name");

			var result = await LoyaltyAPI.Loyalty.PerformQryAsync(HttpContext, $@"
            {{
                playersWithAvatarsToBeApproved = company.PlayersWithAvatarsToBeApproved('{gameName}',{initialIndex},{finalIndex});
                print company.CountPlayersWithAvatarsToBeApproved('{gameName}') totalOfAvatarsToBeApproved;
                for( avatars : playersWithAvatarsToBeApproved )
                {{
                    print avatars.Avatar.PathToBeApproved 'pathToDownload';
                    print avatars.Avatar.UploadImageElapsedTimeInSeconds() 'uploadImageElapsedTimeInSeconds';
                    print avatars.Id 'playerId';
                    print avatars.AccountNumber accountNumber;
                }}
            }}
            ");

			return result;
		}

		[HttpPut("api/player/avatar/toBeApproved")]
		[Authorize(Roles = "b44,ApproveAvatar,DenyAvatar,g4,ly01")]
		public async Task<IActionResult> ApproveOrDenyAvatarsAsync([FromBody] Avatar[] values)
		{
			StringBuilder script = new StringBuilder();
			foreach (var value in values)
			{
				var playerId = Validator.StringEscape(value.PlayerId);
				script.AppendLine($"player = company.Players.SearchPlayer('{playerId}');");
				if (value.IsApproved)
				{
					script.AppendLine($"player.ApproveAvatar(Now);");
				}
				else
				{
					script.AppendLine($"player.DenyAvatar();");
					if (value.SendNotification)
					{
						script.AppendLine($"store = company.Sales.StoreById({value.StoreId});");
						script.AppendLine($"player.AddMessage(store,'Sorry, your avatar pic has been denied, please choose another one');");
					}
				}
			}
			var result = await LoyaltyAPI.Loyalty.PerformCmdAsync(HttpContext, $@"
            {{
                {script.ToString()}
            }}
            ");
			return result;
		}

		[HttpGet("api/loyalty/player/status")]
		[Authorize(Roles = "player")]
		public async Task<IActionResult> PlayerStatusAsync()
		{
			string playerId = Validator.StringEscape(Security.PlayerId(User));

			var result = await LoyaltyAPI.Loyalty.PerformQryAsync(HttpContext, $@"
            {{
					player = company.Players.SearchPlayer('{playerId}');
                    print player.Nickname nickname;
                    print player.Avatar.Path avatar;
                    print Now currentDateTime;
					print player.Messages.Count(Now) countNotifications;
                }}
            ");
			return result;
		}

		[HttpGet("api/loyalty/player/status/{from}")]
        [Authorize(Roles = "player")]
        public async Task<IActionResult> PlayerStatusAsync(string from)
		{
			string playerId = Validator.StringEscape(Security.PlayerId(User));

			var result = await LoyaltyAPI.Loyalty.PerformQryAsync(HttpContext, $@"
                {{
                    player = company.Players.SearchPlayer('{playerId}');
                    print player.Nickname nickname;
                    print player.Avatar.Path avatar;
                    print Now currentDateTime;
					print player.Messages.Count(now) countNotifications;
                }}
            ");
			return result;
		}

		[HttpGet("api/loyalty/tournament/{year:int:length(4)}/player/notification")]
        [Authorize(Roles = "player")]
        public async Task<IActionResult> MyNotificationsForTournamentAsync(int year)
		{
			string playerId = Validator.StringEscape(Security.PlayerId(User));

			IActionResult result = await LoyaltyAPI.Loyalty.PerformQryAsync(HttpContext, $@"
                {{
					playerQry = company.CustomerByPlayerId('{playerId}').Player;
                    print playerQry.Id playerId;
                    print playerQry.Messages.Count(now) countNotifications;
                    print Now now;
                    for (recent : playerQry.Messages.ReadAllMessages(now))
                    {{
                        print recent.Body 'body';
                        print recent.CreationDate 'creationDate';
                        print Now 'now';
						print recent.Importance.Name 'importance';
                    }}
                }}
            ");

			var x = await LoyaltyAPI.Loyalty.PerformCmdAsync(HttpContext, $@"
                {{
					player = company.Players.SearchPlayer('{playerId}');
                    player.Messages.MarkAllMessagesAsRead(now);
                }}
            ");
			return result;
		}

		[HttpGet("api/player/notifications")]
        [Authorize(Roles = "player")]
        public async Task<IActionResult> PlayerNotificationsAsync()
		{
			string playerId = Validator.StringEscape(Security.PlayerId(User));

			IActionResult result = await LoyaltyAPI.Loyalty.PerformQryAsync(HttpContext, $@"
                {{
					playerQry = company.CustomerByPlayerId('{playerId}').Player;
                    print playerQry.Id playerId;
                    print playerQry.Messages.Count(now) countNotifications;
                    print Now now;
                    for (recent : playerQry.Messages.ReadAllMessages(now))
                    {{
                        print recent.Body 'body';
                        print recent.CreationDate 'creationDate';
                        print Now 'now';
						print recent.Importance.Name 'importance';
                    }}
                }}
            ");

			var x = await LoyaltyAPI.Loyalty.PerformCmdAsync(HttpContext, $@"
                {{
					player = company.Players.SearchPlayer('{playerId}');
                    player.Messages.MarkAllMessagesAsRead(now);
                }}
            ");
			return result;
		}

		[HttpGet("api/loyalty/marketing/kpis/{fromDate}/{toDate}")]
		[Authorize(Roles = "p")]
		public async Task<IActionResult> MarketingKPIsAsync(string fromDate, string toDate)
		{
			if (string.IsNullOrEmpty(fromDate)) return BadRequest($"{nameof(fromDate)} has not a valid value");
			if (string.IsNullOrEmpty(toDate)) return BadRequest($"{nameof(toDate)} has not a valid value");

			var result = await LoyaltyAPI.Loyalty.PerformQryAsync(HttpContext, $@"
                {{
                    marketingKPIs = company.MarketingKPIs();
                    print marketingKPIs.RetentionAverage('{fromDate}','{toDate}') 'retention';
                    print marketingKPIs.LossesCount('{fromDate}','{toDate}') 'losses';
                    print marketingKPIs.VisitAverage('{fromDate}','{toDate}') 'visit';
                }}
            ");
			return result;
		}

		private enum Campaign
		{
			MANUAL = 1,
			LUCKY77 = 2,
			RELOAD = 3
		}

		private const string CLAIM_TYPE = "resource_access";

		[HttpGet("api/loyalty/campaigns")]
        [Authorize(Roles = "a15,a17,a19,campaignSettingsView,g12,g13,g14")]
        public async Task<IActionResult> CampaignsAsync()
		{
			if (Security.ItsSecuritySchemeConfigured())
			{
				foreach (var claim in HttpContext.User.Claims)
				{
					if (claim.Type == CLAIM_TYPE)
					{
						var roles = JsonConvert.DeserializeObject<RolesJwtResource>(claim.Value);
						if (roles != null && roles.lotto != null && roles.lotto.roles != null)
						{
							var userHasAllPermissions = roles.lotto.roles.Contains("a17");
							var userHasLucky77AndReload = roles.lotto.roles.Contains("a15");
							var userHasManual = roles.lotto.roles.Contains("a19");
							var scriptQuery = string.Empty;
							if (userHasAllPermissions)
							{
								scriptQuery = $"campaigns = company.Campaigns.List();";
							}
							else if (userHasLucky77AndReload && userHasManual)
							{
								scriptQuery = $"campaigns = company.Campaigns.GiftCardsWith({{{(int)Campaign.MANUAL}, {(int)Campaign.LUCKY77}, {(int)Campaign.RELOAD}}});";
							}
							else if (userHasLucky77AndReload)
							{
								scriptQuery = $"campaigns = company.Campaigns.GiftCardsWith({{{(int)Campaign.LUCKY77}, {(int)Campaign.RELOAD}}});";
							}
							else if (userHasManual)
							{
								scriptQuery = $"campaigns = company.Campaigns.GiftCardsWith({{{(int)Campaign.MANUAL}}});";
							}

							if (!string.IsNullOrWhiteSpace(scriptQuery))
							{
								return await LoyaltyAPI.Loyalty.PerformQryAsync(HttpContext, $@"
				                {{
									{scriptQuery}
					                for(campaign : campaigns)
					                {{
						                print campaign.PromotionNumber id;
						                print campaign.Name name;
						                print campaign.ReturnedCurrencyCode currency;
										print campaign.IsEnabled isEnabled;
										print campaign.IsActive(Now) isActive;
										print campaign.Description description;

										print campaign.Publishable publishable;

										print campaign.GivenTimesNumber givenTimesNumber;

										campaignType = company.Campaigns.CampaignType(campaign);
										print campaignType campaigntype;

										if(campaignType != 'GiftCard')
										{{
											rewardType = campaign.RewardType;
											print rewardType rewardType;
										}}

										if(campaignType == 'GiftCard')
										{{
											if(campaign.HasStartAndEndDate)
											{{
												print campaign.StartDate startDate;
                                                print campaign.EndDate endDate;
											}}
										}}

										isUnlimitedBudget = campaign.IsUnlimitedBudget;
										print isUnlimitedBudget isUnlimitedBudget;
										if (!isUnlimitedBudget)
										{{
											print campaign.InitialBudget initialBudget;
											print campaign.ActualBudget actualBudget;
										}}
										print campaign.GivenBudget givenBudget;

										campaignType = company.Campaigns.CampaignType(campaign);
										print campaignType campaigntype;
										
										for(lastCustomers : campaign.LastBeneficiaries)
										{{
											customer = lastCustomers;
											print customer.AccountNumber accountNumber;
										}}

										if (campaign.HasResources)
										{{
											if (campaign.HasPreferredResource)
											{{
												print campaign.PreferredResourceName chosenResource;
											}}
											for(resources : campaign.Resources)
											{{
												print resources.Name key;
												print resources.Url url;
											}}
										}}
					                }}
				                }}
			                ");
							}
						}
					}
				}
				return Unauthorized();
			}
			else
			{
				return await LoyaltyAPI.Loyalty.PerformQryAsync(HttpContext, $@"
				{{
					for(campaign : company.Campaigns.List())
					{{
						print campaign.PromotionNumber id;
						print campaign.Name name;
						print campaign.ReturnedCurrencyCode currency;
						print campaign.IsEnabled isEnabled;
						print campaign.IsActive(Now) isActive;
						print campaign.Description description;

						print campaign.Publishable publishable;
						
						print campaign.GivenTimesNumber givenTimesNumber;
						
						campaignType = company.Campaigns.CampaignType(campaign);
						print campaignType campaigntype;

						if(campaignType == 'GiftCard')
						{{
							if(campaign.HasStartAndEndDate)
							{{
								print campaign.StartDate startDate;
                                print campaign.EndDate endDate;
							}}
						}}

						isUnlimitedBudget = campaign.IsUnlimitedBudget;
						print isUnlimitedBudget isUnlimitedBudget;
						if (!isUnlimitedBudget)
						{{
							print campaign.InitialBudget initialBudget;
							print campaign.ActualBudget actualBudget;
						}}
						print campaign.GivenBudget givenBudget;

						campaignType = company.Campaigns.CampaignType(campaign);
						print campaignType campaigntype;

						for(lastCustomers : campaign.LastBeneficiaries)
						{{
							customer = lastCustomers;
							print customer.AccountNumber accountNumber;
						}}

						if (campaignType == 'GiftCard' && campaign.HasResources)
						{{
							if (campaign.HasPreferredResource)
							{{
								print campaign.PreferredResourceName chosenResource;
							}}
							
							for(resources : campaign.Resources)
							{{
								print resources.Name key;
								print resources.Url url;
							}}
						}}
					}}
				}}
			");
			}

		}

		[HttpPost("api/loyalty/campaigns/{campaignNumber}/calculate/amount")]
        [Authorize(Roles = "player")]
        public async Task<IActionResult> CalculateAmountAsync(int campaignNumber, [FromBody] CalculateAmount body)
		{
			if (campaignNumber != 3) return BadRequest($"Calculate amount for campaign number {campaignNumber} is not valid. It only applies for reload campaigns.");
			if (body.Amount <= 0) return BadRequest("Amount is required");

			return await LoyaltyAPI.Loyalty.PerformQryAsync(/*This is a query because only have prints*/HttpContext, $@"
				{{
					campaign = company.Campaigns.PromotionCampaign({campaignNumber});
					amount = campaign.CalculateAmountAndDescriptorToReload({body.Amount});
					print amount amount;
					print gCard.Descriptor descriptor;
				}}
			");
		}

		const int CampaignManualFPId = 2;
		[HttpPost("api/campaigns/manualFP/accreditation")]
		[Authorize(Roles = "mfp1,a15")]
		public async Task<IActionResult> AccreditFreePlayAsync([FromBody] FreePlayAccreditationBody body)
		{
			if (body == null) return BadRequest($"Body is required");
			if (body.Amount <= 0) return BadRequest($"{nameof(body.Amount)} is not valid");
			if (string.IsNullOrWhiteSpace(body.AccountName)) return BadRequest($"{nameof(body.AccountName)} is required");

			PlayerBalanceBody infoBalance;
			var paymentProcessor = WholePaymentProcessor.Instance().SearchBalanceProcesorBy(typeof(BalanceInfo));
			using (RecordSet recordSet = paymentProcessor.GetRecordSet())
			{
				recordSet.SetParameter("customerId", body.AccountName);

				infoBalance = await paymentProcessor.ExecuteAsync<PlayerBalanceBody>(DateTime.Now, recordSet);
			}
			var existsAccount = infoBalance.idPlayer != 0;
			if (!existsAccount) return BadRequest($"The account number {body.AccountName} does not exist.");

			string employeeName = Security.UserName(HttpContext);

			return await LoyaltyAPI.Loyalty.PerformChkThenCmdAsync(HttpContext, $@"
					campaignExist = company.Campaigns.ExistsGiftCardCampaign({CampaignManualFPId});
					Check(campaignExist) Error 'Campaign does not exists with promotionNumber {CampaignManualFPId}';
					if (campaignExist)
					{{
						campaign = company.Campaigns.PromotionCampaign({CampaignManualFPId});
						Check(campaign.IsEnabled) Error 'Campaign {CampaignManualFPId} is disabled';

						isActive = campaign.IsActive(Now);
                        Check(isActive) Error 'Campaign {CampaignManualFPId} is no longer active';
					}}
				", $@"
				{{
					customer = company.CustomerByAccountNumber('{body.AccountName}');
					store = company.Sales.StoreById({Store.STORES_SEQUENCE_LOTTO});
					campaign = company.Campaigns.PromotionCampaign({CampaignManualFPId});
					campaign.Give(itIsThePresent, customer, Now, {body.Amount}, '{body.Description}', '{employeeName}', 'FP', store);
				}}
			");
		}

		[HttpPost("api/campaigns/manualFP/withdrawal")]
		[Authorize(Roles = "mfp1,a15")]
		public async Task<IActionResult> WithdrawFreePlayAsync([FromBody] FreePlayAccreditationBody body)
		{
			if (body == null) return BadRequest($"Body is required");
			if (body.Amount <= 0) return BadRequest($"{nameof(body.Amount)} is not valid");
			if (string.IsNullOrWhiteSpace(body.AccountName)) return BadRequest($"{nameof(body.AccountName)} is required");

			PlayerBalanceBody infoBalance;
			var paymentProcessor = WholePaymentProcessor.Instance().SearchBalanceProcesorBy(typeof(BalanceInfo));
			using (RecordSet recordSet = paymentProcessor.GetRecordSet())
			{
				recordSet.SetParameter("customerId", body.AccountName);

				infoBalance = await paymentProcessor.ExecuteAsync<PlayerBalanceBody>(DateTime.Now, recordSet);
			}
			var existsAccount = infoBalance.idPlayer != 0;
			if (!existsAccount) return BadRequest($"The account number {body.AccountName} does not exist.");

			string employeeName = Security.UserName(HttpContext);
			var balanceFP = await BalanceFPAsync(HttpContext, body.AccountName.ToString());

			return await LoyaltyAPI.Loyalty.PerformChkThenCmdAsync(HttpContext, $@"
					existsCustomer = company.ExistsCustomer('{body.AccountName}');
					Check(existsCustomer) Error 'Customer {body.AccountName} does not exists.';
					campaignExist = company.Campaigns.ExistsGiftCardCampaign({CampaignManualFPId});
					Check(campaignExist) Error 'Campaign does not exists with promotionNumber {CampaignManualFPId}';
					if (campaignExist)
					{{
						campaign = company.Campaigns.PromotionCampaign({CampaignManualFPId});
						Check(campaign.IsEnabled) Error 'Campaign {CampaignManualFPId} is disabled';
						Check(campaign.IsSpentAmountGreaterOrEqualThan({body.Amount})) Error 'Campaign spent more than disbursed amount';
					}}
					Check({balanceFP}>={body.Amount}) Error 'There is no enough FP balance';
				", $@"
				{{
					customer = company.CustomerByAccountNumber('{body.AccountName}');
					store = company.Sales.StoreById({Store.STORES_SEQUENCE_LOTTO});
					campaign = company.Campaigns.PromotionCampaign({CampaignManualFPId});
					campaign.TakeOut(itIsThePresent, customer, Now, {body.Amount}, '{body.Description}', '{employeeName}', 'FP', store);
				}}
			");
		}

		const int FAKE_TICKET_NUMBER = -1;
		async Task<decimal> BalanceFPAsync(HttpContext context, string accountNumber)
		{
			BalanceFPResponse lockBalanceResponse;
			try
			{
				var url = $"{Settings.CashierUrl}api/customers/{accountNumber}/balances/FP";
				HttpRestClientConfiguration restClientToCashier = HttpRestClientConfiguration.GetInstance();
				var resultFromCashier = await restClientToCashier.GetAsync(context, url);
				if (!(resultFromCashier is OkObjectResult))
				{
					string body = string.Empty;
					if (resultFromCashier is ContentResult)
					{
						body = $@"error:{((ObjectResult)resultFromCashier).ToString()} \n url:{url} \n accountNumber:{accountNumber}";
					}
					else
					{
						body = $@"url:{url} \n accountNumber:{accountNumber}";
					}
					ErrorsSender.Send(body, $@"Request to balance FP failed. Sending accountNumber: {accountNumber}.");
					return FAKE_TICKET_NUMBER;
				}

				string resp = ((OkObjectResult)resultFromCashier).Value.ToString();
				lockBalanceResponse = JsonConvert.DeserializeObject<BalanceFPResponse>(resp);
			}
			catch (Exception e)
			{
				ErrorsSender.Send(e);
				return FAKE_TICKET_NUMBER;
			}

			return lockBalanceResponse.Balance;
		}

        const string DefaultNickName = "Player";
        const string DefaultAvatarPath = "/public/images/avatar2.png";
        [HttpPost("api/loyalty/campaigns/assign")]
		[Authorize(Roles = "a15,a19")]
		public async Task<IActionResult> AssignCustomerToCampaignAsync([FromBody] CustomerCampaign body)
		{
			if (body == null) return BadRequest($"Body is required");
			if (body.CampaignNumber <= 0) return BadRequest($"{nameof(body.CampaignNumber)} is not valid");
            if (string.IsNullOrWhiteSpace(body.AccountNumber)) return BadRequest($"{nameof(body.AccountNumber)} is required");

			if (Security.ItsSecuritySchemeConfigured())
			{
				foreach (var claim in HttpContext.User.Claims)
				{
					if (claim.Type == CLAIM_TYPE)
					{
						var roles = JsonConvert.DeserializeObject<RolesJwtResource>(claim.Value);
						if (roles != null && roles.lotto != null && roles.lotto.roles != null)
						{
							var campaignIdSentIsManual = body.CampaignNumber == (int)Campaign.MANUAL;
							var userHasAllowedLucky77AndReloadCampaign = roles.lotto.roles.Contains("a15");
							var campaignIdSentIsLucky77OrReload = body.CampaignNumber == (int)Campaign.LUCKY77 || body.CampaignNumber == (int)Campaign.RELOAD;
							var userHasAllowedManualCampaign = roles.lotto.roles.Contains("a19");
							if (userHasAllowedLucky77AndReloadCampaign && userHasAllowedManualCampaign)
							{
								break;
							}
							if (userHasAllowedLucky77AndReloadCampaign && campaignIdSentIsManual)
							{
								return BadRequest($"User does not have permission to assign campaign {Campaign.MANUAL}");
							}
							if (userHasAllowedManualCampaign && campaignIdSentIsLucky77OrReload)
							{
								return BadRequest($"User does not have permission to assign campaign {Campaign.LUCKY77} or {Campaign.RELOAD}");
							}
							break;
						}
					}
				}
			}

			var description = Validator.StringEscape(body.Description);

			var result = await LoyaltyAPI.Loyalty.PerformQryAsync(HttpContext, $@"
                {{
                    ItsNewOne = company.ExistsCustomer('{body.AccountNumber}');
                    print ! ItsNewOne thePlayerItsNew;
                }}
            ");

			if (!(result is OkObjectResult))
			{
				throw new GameEngineException($@"Error:{((ObjectResult)result).Value.ToString()}");
			}

			OkObjectResult o = (OkObjectResult)result;
			string json = o.Value.ToString();
			ExistCustomer existCustomer = JsonConvert.DeserializeObject<ExistCustomer>(json);

			bool itsNewOne = existCustomer.ThePlayerItsNew;
			if (itsNewOne)
			{
				PlayerBalanceBody infoBalance;
				var paymentProcessor = WholePaymentProcessor.Instance().SearchBalanceProcesorBy(typeof(BalanceInfo));
				using (RecordSet recordSet = paymentProcessor.GetRecordSet())
				{
					recordSet.SetParameter("customerId", body.AccountNumber);

					infoBalance = await paymentProcessor.ExecuteAsync<PlayerBalanceBody>(DateTime.Now, recordSet);
				}
				var existsAccount = infoBalance.idPlayer != 0;
				if (!existsAccount) return BadRequest($"The account number {body.AccountNumber} does not exist.");

				result = await LoyaltyAPI.Loyalty.PerformCmdAsync(HttpContext, $@"
					customer = company.GetOrCreateCustomerById('{body.AccountNumber}');
					player{body.AccountNumber} = customer.Player;
			        player{body.AccountNumber}.NickName = '{DefaultNickName}';
					player{body.AccountNumber}.UpdateAvatar('{DefaultAvatarPath}', Now);
					player{body.AccountNumber}.ApproveAvatar(Now);
				");
			}

			result = await LoyaltyAPI.Loyalty.PerformChkThenCmdAsync(HttpContext, $@"
				campaignExist = company.Campaigns.ExistsGiftCardCampaign({body.CampaignNumber});
				Check(campaignExist) Error 'Campaign does not exists with promotionNumber {body.CampaignNumber}';
				if (campaignExist)
				{{
					campaign = company.Campaigns.PromotionCampaign({body.CampaignNumber});
					Check(campaign.IsEnabled) Error 'Campaign {body.CampaignNumber} is disabled';
					Check(! campaign.IsExceededBudget({body.Amount})) Error 'Amount to give exceeded the maximum budget.';

					isActive = campaign.IsActive(Now);
                    Check(isActive) Error 'Campaign {body.CampaignNumber} is no longer active';
				}}
				", $@"
                {{
					customer = company.CustomerByAccountNumber('{body.AccountNumber}');
					store = company.Sales.StoreById({body.StoreId});
					campaign = company.Campaigns.PromotionCampaign({body.CampaignNumber});
					campaign.Give(itIsThePresent, customer, Now, {body.Amount}, '{description}', '{body.Who}', '{body.CurrencyCode}', store);
                }}
            ");

			return result;
		}

		[HttpPost("api/loyalty/campaigns/unassign")]
		[Authorize(Roles = "a15,a19")]
		public async Task<IActionResult> UnassignCustomerToCampaignAsync([FromBody] CustomerCampaign body)
		{
			if (body == null) return BadRequest($"Body is requiered");
			if (body.CampaignNumber <= 0) return BadRequest($"{nameof(body.CampaignNumber)} is not valid");
			if (string.IsNullOrWhiteSpace(body.AccountNumber)) return BadRequest($"{nameof(body.AccountNumber)} is required");

			PlayerBalanceBody infoBalance;
			var paymentProcessor = WholePaymentProcessor.Instance().SearchBalanceProcesorBy(typeof(BalanceInfo));
			using (RecordSet recordSet = paymentProcessor.GetRecordSet())
			{
				recordSet.SetParameter("customerId", body.AccountNumber);

				infoBalance = await paymentProcessor.ExecuteAsync<PlayerBalanceBody>(DateTime.Now, recordSet);
			}
			var existsAccount = infoBalance.idPlayer != 0;
			if (!existsAccount) return BadRequest($"The account number {body.AccountNumber} does not exist.");

			if (Security.ItsSecuritySchemeConfigured())
			{
				foreach (var claim in HttpContext.User.Claims)
				{
					if (claim.Type == CLAIM_TYPE)
					{
						var roles = JsonConvert.DeserializeObject<RolesJwtResource>(claim.Value);
						if (roles != null && roles.lotto != null && roles.lotto.roles != null)
						{
							var campaignIdSentIsManual = body.CampaignNumber == (int)Campaign.MANUAL;
							var userHasAllowedLucky77AndReloadCampaign = roles.lotto.roles.Contains("a15");
							var campaignIdSentIsLucky77OrReload = body.CampaignNumber == (int)Campaign.LUCKY77 || body.CampaignNumber == (int)Campaign.RELOAD;
							var userHasAllowedManualCampaign = roles.lotto.roles.Contains("a19");
							if (userHasAllowedLucky77AndReloadCampaign && userHasAllowedManualCampaign)
							{
								break;
							}
							if (userHasAllowedLucky77AndReloadCampaign && campaignIdSentIsManual)
							{
								return BadRequest($"User does not have permission to assign campaign {Campaign.MANUAL}");
							}
							if (userHasAllowedManualCampaign && campaignIdSentIsLucky77OrReload)
							{
								return BadRequest($"User does not have permission to assign campaign {Campaign.LUCKY77} or {Campaign.RELOAD}");
							}
							break;
						}
					}
				}
			}

			var description = Validator.StringEscape(body.Description);
			var balanceFP = await BalanceFPAsync(HttpContext, body.AccountNumber);
			var result = await LoyaltyAPI.Loyalty.PerformChkThenCmdAsync(HttpContext, $@"
				existsCustomer = company.ExistsCustomer('{body.AccountNumber}');
				Check(existsCustomer) Error 'Customer {body.AccountNumber} does not exists.';
				campaignExist = company.Campaigns.ExistsGiftCardCampaign({body.CampaignNumber});
				Check(campaignExist) Error 'Campaign does not exists with promotionNumber {body.CampaignNumber}';
				if (campaignExist)
				{{
					campaign = company.Campaigns.PromotionCampaign({body.CampaignNumber});
					Check(campaign.IsEnabled) Error 'Campaign {body.CampaignNumber} is disabled';
					Check(campaign.IsSpentAmountGreaterOrEqualThan({body.Amount})) Error 'Campaign spent more than disbursed amount';
				}}
				Check('{body.CurrencyCode}'=='FP' && {balanceFP}>={body.Amount}) Error 'There is no enough FP balance';
				", $@"
                {{
					customer = company.CustomerByAccountNumber('{body.AccountNumber}');
					store = company.Sales.StoreById({body.StoreId});
					campaign = company.Campaigns.PromotionCampaign({body.CampaignNumber});
                    campaign.TakeOut(itIsThePresent, customer, Now, {body.Amount}, '{description}', '{body.Who}', '{body.CurrencyCode}', store);
                }}
            ");

			return result;
		}

		public enum BudgetType
		{
			Unlimited,
			Fixed
		}

		public enum CampaignType
		{
			ManualAmount,
			PercentageRange
		}

		[HttpPost("api/loyalty/campaigns/giftCard")]
		[Authorize(Roles = "campaignCreate")]
		public async Task<IActionResult> CreateGiftCardCampaignAsync([FromBody] GiftCardCampaignCreationBody body)
		{
			if (body == null) return BadRequest($"Body is required");
			if (string.IsNullOrWhiteSpace(body.Name)) return BadRequest($"Parameter {nameof(body.Name)} is required");
			if (body.StartDate == DateTime.MinValue) return BadRequest($"Parameter {nameof(body.StartDate)} is required");
			if (body.EndDate == DateTime.MinValue) return BadRequest($"Parameter {nameof(body.EndDate)} is required");
			if (string.IsNullOrWhiteSpace(body.Description)) return BadRequest($"Parameter {nameof(body.Description)} is required");
			if (string.IsNullOrWhiteSpace(body.CurrencyCode)) return BadRequest($"Parameter {nameof(body.CurrencyCode)} is required");

			var name = Validator.StringEscape(body.Name);
			var description = Validator.StringEscape(body.Description);

			var commandToCreateAndSetUpCampaign = new StringBuilder();
			if (body.BudgetType == BudgetType.Unlimited)
			{
				if (body.Budget != 0) return BadRequest($"{nameof(body.Budget)} '{body.Budget}' is not valid for {nameof(BudgetType)} {BudgetType.Unlimited}");
                commandToCreateAndSetUpCampaign.AppendLine($"campaign = company.Campaigns.CreateGiftCardCampaign(itIsThePresent, campaignId, Now, '{name}', {body.StartDate.ToString("M/d/yyyy H:m:s")}, {body.EndDate.ToString("M/d/yyyy H:m:s")}, '{body.CurrencyCode}');");
            }
			else
			{
				if (body.Budget <= 0) return BadRequest($"Parameter {nameof(body.Budget)} is not valid");
                commandToCreateAndSetUpCampaign.AppendLine($"campaign = company.Campaigns.CreateGiftCardCampaign(itIsThePresent, campaignId, Now, '{name}', {body.StartDate.ToString("M/d/yyyy H:m:s")}, {body.EndDate.ToString("M/d/yyyy H:m:s")},'{body.CurrencyCode}', {body.Budget});");
            }

			if (body.Resources != null && body.Resources.Count > 0)
			{
				if (string.IsNullOrWhiteSpace(body.ChosenResource)) return BadRequest($"{nameof(body.ChosenResource)} is required");

				var names = new StringBuilder();
				var urls = new StringBuilder();
				var resourcesAdded = 0;
				foreach (var resource in body.Resources)
				{
					if (string.IsNullOrWhiteSpace(resource.Name)) return BadRequest($"{nameof(resource.Name)} is required");
					if (string.IsNullOrWhiteSpace(resource.Url)) return BadRequest($"{nameof(resource.Url)} is required");

					names.Append($"'{resource.Name}'");
					urls.Append($"'{resource.Url}'");

					resourcesAdded++;
					if (resourcesAdded != body.Resources.Count)
					{
						names.Append(',');
						urls.Append(',');
					}
				}

				commandToCreateAndSetUpCampaign.AppendLine($"campaign.AddResources({{{names}}}, {{{urls}}});");
			}
			if (!string.IsNullOrWhiteSpace(body.ChosenResource)) commandToCreateAndSetUpCampaign.AppendLine($"campaign.ChoosePreferredResource('{body.ChosenResource}');");
			commandToCreateAndSetUpCampaign.AppendLine($"campaign.IsEnabled = {body.IsEnabled};");

			var result = await LoyaltyAPI.Loyalty.PerformCmdAsync(HttpContext, $@"
				{{
					Eval('campaignId =' + company.Campaigns.NextCampaignConsecutive() + ';');
					{commandToCreateAndSetUpCampaign}
					campaign.Description = '{description}';
					print campaignId campaignId;
				}}
			");

			return result;
		}

        [HttpPost("api/loyalty/campaigns/spinkick")]
        [Authorize(Roles = "campaignCreate")]
        public async Task<IActionResult> CreateSpinKickCampaignAsync([FromBody] SpinKickCampaignCreationBody body)
        {
            if (body == null) return BadRequest($"Body is required");
            if (string.IsNullOrWhiteSpace(body.Name)) return BadRequest($"Parameter {nameof(body.Name)} is required");
            if (string.IsNullOrWhiteSpace(body.Description)) return BadRequest($"Parameter {nameof(body.Description)} is required");
            if (body.CampaignId <= 0) return BadRequest($"Parameter {nameof(body.CampaignId)} is not valid");
            if (body.TotalRewardUnits <= 0) return BadRequest($"Parameter {nameof(body.TotalRewardUnits)} is not valid");

            var name = Validator.StringEscape(body.Name);
            var description = Validator.StringEscape(body.Description);

            var result = await LoyaltyAPI.Loyalty.PerformChkThenCmdAsync(HttpContext, $@"
				{{
					existPromotionCampaign = company.Campaigns.ExistPromotionCampaign({body.CampaignId});
					Check(existPromotionCampaign) Error 'Campaign does not exists with promotionNumber {body.CampaignId}';
				}}
			", $@"
				{{
					Eval('campaignId =' + company.Campaigns.NextCampaignConsecutive() + ';');
					saleCampaign = company.Campaigns.SaleCampaign({body.CampaignId});
					onTheSpotCampaign = company.Campaigns.CreateSpinKickCampaign(itIsThePresent, Now, campaignId, '{name}', '{description}', {body.TotalRewardUnits}, saleCampaign);
					onTheSpotCampaign.IsEnabled = {body.IsEnabled};
                    print campaignId campaignId;
				}}
			");

            return result;
        }

        [HttpPut("api/loyalty/campaigns/spinkick/{campaignNumber}")]
        [Authorize(Roles = "campaignEdit")]
		public async Task<IActionResult> UpdateSpinKickCampaignAsync(int campaignNumber, [FromBody] SpinKickCampaignCreationBody body)
		{
            if (body == null) return BadRequest($"Body is required");
            if (campaignNumber <= 0) return BadRequest($"Parameter {nameof(campaignNumber)} is not valid");
            if (string.IsNullOrWhiteSpace(body.Name)) return BadRequest($"Parameter {nameof(body.Name)} is required");
            if (string.IsNullOrWhiteSpace(body.Description)) return BadRequest($"Parameter {nameof(body.Description)} is required");

            var name = Validator.StringEscape(body.Name);
            var description = Validator.StringEscape(body.Description);

			var result = await LoyaltyAPI.Loyalty.PerformChkThenCmdAsync(HttpContext, $@"
			{{
				existPromotionCampaign = company.Campaigns.ExistPromotionCampaign({campaignNumber});
                Check(existPromotionCampaign) Error 'Campaign does not exists with promotionNumber {campaignNumber}';
			}}
			", $@"
			{{
				campaign = company.Campaigns.PromotionCampaign({campaignNumber});
                campaign.Name = '{name}';
                campaign.Description = '{description}';
                campaign.IsEnabled = {body.IsEnabled};
			}}
			");

            return result;
        }

        [HttpGet("api/loyalty/campaigns/spinkick/{promotionNumber:int}")]
        [Authorize(Roles = "campaignSettingsView")]
        public async Task<IActionResult> InfoSpinKickCampaignAsync(int promotionNumber)
        {
            if (promotionNumber <= 0) return BadRequest($"Parameter {nameof(promotionNumber)} is not valid");

            var result = await LoyaltyAPI.Loyalty.PerformQryAsync(HttpContext, $@"
				{{
					campaign = company.Campaigns.PromotionCampaign({promotionNumber});
                    print campaign.PromotionNumber promotionNumber;
					print campaign.AccountNumber accountNumber;
                    print campaign.Name name;
                    print campaign.Description description;
                    print campaign.CampaignBase.PromotionNumber promotionNumberCampaignBase;
                    print campaign.CampaignBase.StartDate startDateCampaignBase;
                    print campaign.CampaignBase.EndDate endDateCampaignBase;
                    print campaign.CampaignBase.LastDayToChangePrizes lastDayToChangePrizesCampaignBase;
                    print campaign.TotalRewardUnits totalRewardUnits;
                    print campaign.AssignedRewardUnits assignedRewardUnits;
                    print campaign.IsEnabled isEnabled;
					print campaign.IsActive(Now) isActive;
					print campaign.ReturnedCurrencyCode currency;
				}}
			");

            return result;
        }

        [HttpPost("api/loyalty/campaigns/spinkick/{promotionNumber:int}/assignPrize")]
        [Authorize(Roles = "a15,a19")]
        public async Task<IActionResult> AssignSpinKickCampaignAsync(int promotionNumber, [FromBody] AssignSpinBody body)
        {
            if (promotionNumber <= 0) return BadRequest($"Parameter {nameof(promotionNumber)} is not valid");
            if (string.IsNullOrWhiteSpace(body.AccountNumber)) return BadRequest($"Parameter {nameof(body.AccountNumber)} is required");
            if (string.IsNullOrWhiteSpace(body.Description)) return BadRequest($"Parameter {nameof(body.Description)} is required");
            if (body.Store <= 0) return BadRequest($"Parameter {nameof(body.Store)} is not valid");

            var result = await LoyaltyAPI.Loyalty.PerformChkThenCmdAsync(HttpContext, $@"
		        {{
				    campaign = company.Campaigns.PromotionCampaign({promotionNumber});
                    Check(campaign.IsEnabled) Error 'Campaign {promotionNumber} is disabled';
                    Check(campaign.IsActive(Now)) Error 'Campaign {promotionNumber} is not active at server time';
					availableSpins = campaign.TotalRewardUnits - campaign.AssignedRewardUnits;
                    Check(availableSpins > 0) Error 'There are no more spins available';
					playerExists = company.ExistsCustomer('{body.AccountNumber}');
                    Check(playerExists) Error 'Player {body.AccountNumber} does not exist';
				    campaignBaseIsEnabled = campaign.CampaignBase.IsEnabled;
                    Check(campaignBaseIsEnabled) Error 'The linked campaign is disabled.';
					validEndDate = campaign.CampaignBase.EndDate > Now;
                    Check(validEndDate) Error 'The linked campaign has expired';
				}}
			", $@"
				{{
					player = company.CustomerByAccountNumber('{body.AccountNumber}').Player;
					spinkick = company.Campaigns.PromotionCampaign({promotionNumber});
					spinkick.AssignPrize(itIsThePresent, Now, player, '{body.Description}', {body.Store});
				}}
			");

            return result;
        }

        [HttpPost("api/loyalty/campaigns/onthespot")]
        [Authorize(Roles = "campaignCreate")]
        public async Task<IActionResult> CreateSpotRewardCampaignAsync([FromBody] OnTheSpotCampaignCreationBody body)
        {
            if (body == null) return BadRequest($"Body is required");
            if (string.IsNullOrWhiteSpace(body.Name)) return BadRequest($"Parameter {nameof(body.Name)} is required");
            if (string.IsNullOrWhiteSpace(body.Description)) return BadRequest($"Parameter {nameof(body.Description)} is required");
            if (body.StartDate == DateTime.MinValue) return BadRequest($"Parameter {nameof(body.StartDate)} is required");
            if (body.EndDate == DateTime.MinValue) return BadRequest($"Parameter {nameof(body.EndDate)} is required");
            if (body.LastDayToChangePrizes == DateTime.MinValue) return BadRequest($"Parameter {nameof(body.LastDayToChangePrizes)} is required");
			if (string.IsNullOrWhiteSpace(body.TimeZone)) return BadRequest($"Parameter {nameof(body.TimeZone)} is required");
			if (string.IsNullOrEmpty(body.ReturnedCurrencyCode)) return BadRequest($"Parameter {nameof(body.ReturnedCurrencyCode)} is required");

            Currencies.CODES returnedCurrencyCode;
            if (!Enum.TryParse(body.ReturnedCurrencyCode, out returnedCurrencyCode)) return BadRequest($"{nameof(body.ReturnedCurrencyCode)} may not be valid.");

            var name = Validator.StringEscape(body.Name);
            var description = Validator.StringEscape(body.Description);

            var result = await LoyaltyAPI.Loyalty.PerformCmdAsync(HttpContext, $@"
				{{
					Eval('campaignId =' + company.Campaigns.NextCampaignConsecutive() + ';');
					campaign = company.Campaigns.CreateSpotRewardCampaign(itIsThePresent, Now, campaignId, '{name}', '{description}', {body.StartDate.ToString("M/d/yyyy H:m:s")}, {body.EndDate.ToString("M/d/yyyy H:m:s")}, {body.LastDayToChangePrizes.ToString("M/d/yyyy H:m:s")}, {returnedCurrencyCode}, '{body.TimeZone}');
					campaign.IsEnabled = {body.IsEnabled};
					print campaignId campaignId;
				}}
			");

            return result;
        }

		[HttpPut("api/loyalty/campaigns/onthespot/{campaignNumber}")]
		[Authorize(Roles = "campaignEdit")]
		public async Task<IActionResult> UpdateOnTheSpotCampaignAsync(int campaignNumber, [FromBody] OnTheSpotCampaignCreationBody body)
		{
			if (body == null) return BadRequest($"Body is required");
            if (campaignNumber <= 0) return BadRequest($"Parameter {nameof(campaignNumber)} is not valid");
            if (string.IsNullOrWhiteSpace(body.Name)) return BadRequest($"Parameter {nameof(body.Name)} is required");
            if (string.IsNullOrWhiteSpace(body.Description)) return BadRequest($"Parameter {nameof(body.Description)} is required");
            if (body.StartDate == DateTime.MinValue) return BadRequest($"Parameter {nameof(body.StartDate)} is required");
            if (body.EndDate == DateTime.MinValue) return BadRequest($"Parameter {nameof(body.EndDate)} is required");
			if (body.LastDayToChangePrizes == DateTime.MinValue) return BadRequest($"Parameter {nameof(body.LastDayToChangePrizes)} is required");
			if (string.IsNullOrWhiteSpace(body.TimeZone)) return BadRequest($"Parameter {nameof(body.TimeZone)} is required");

            var name = Validator.StringEscape(body.Name);
            var description = Validator.StringEscape(body.Description);

            var result = await LoyaltyAPI.Loyalty.PerformChkThenCmdAsync(HttpContext, $@"
                {{
                    existPromotionCampaign = company.Campaigns.ExistPromotionCampaign({campaignNumber});
                    Check(existPromotionCampaign) Error 'Campaign does not exists with promotionNumber {campaignNumber}';
					validEndDate = {body.EndDate.ToString("M/d/yyyy H:m:s")} > {body.StartDate.ToString("M/d/yyyy H:m:s")};
                    Check(validEndDate) Error 'End date must be greater than start date';
					validLastDayToChangePrizes = {body.LastDayToChangePrizes.ToString("M/d/yyyy H:m:s")} > {body.EndDate.ToString("M/d/yyyy H:m:s")};
                    Check(validLastDayToChangePrizes) Error 'Last day to change prizes must be greater than end date';
                }}
            ", $@"
                {{
                    campaign = company.Campaigns.PromotionCampaign({campaignNumber});
                    campaign.Name = '{name}';
                    campaign.Description = '{description}';
                    campaign.IsEnabled = {body.IsEnabled};
                    campaign.StartDate = {body.StartDate.ToString("M/d/yyyy H:m:s")};
                    campaign.EndDate = {body.EndDate.ToString("M/d/yyyy H:m:s")};
					campaign.LastDayToChangePrizes = {body.LastDayToChangePrizes.ToString("M/d/yyyy H:m:s")};
                    campaign.TimeZone = '{body.TimeZone}';
                }}
            ");
		
           return result;
		}

        [HttpPut("api/loyalty/campaigns/giftCard")]
		[Authorize(Roles = "campaignEdit")]
		public async Task<IActionResult> UpdateGiftCardCampaignAsync([FromBody] GiftCardCampaignUpdateBody body)
		{
			if (body == null) return BadRequest($"Body is required");
			if (body.CampaignId <= 0) return BadRequest($"Parameter {nameof(body.CampaignId)} is not valid");
			if (body.StartDate == DateTime.MinValue) return BadRequest($"Parameter {nameof(body.StartDate)} is required");
			if (body.EndDate == DateTime.MinValue) return BadRequest($"Parameter {nameof(body.EndDate)} is required");
			if (string.IsNullOrWhiteSpace(body.Description)) return BadRequest($"Parameter {nameof(body.Description)} is required");
			var description = Validator.StringEscape(body.Description);

			var commandToCreateAndSetUpCampaign = new StringBuilder();
			commandToCreateAndSetUpCampaign.AppendLine($"campaign = company.Campaigns.PromotionCampaign({body.CampaignId});");

			if (body.Resources != null && body.Resources.Count > 0)
			{
				var names = new StringBuilder();
				var urls = new StringBuilder();
				var resourcesAdded = 0;
				foreach (var resource in body.Resources)
				{
					if (string.IsNullOrWhiteSpace(resource.Name)) return BadRequest($"{nameof(resource.Name)} is required");
					if (string.IsNullOrWhiteSpace(resource.Url)) return BadRequest($"{nameof(resource.Url)} is required");

					names.Append($"'{resource.Name}'");
					urls.Append($"'{resource.Url}'");

					resourcesAdded++;
					if (resourcesAdded != body.Resources.Count)
					{
						names.Append(',');
						urls.Append(',');
					}
				}

				commandToCreateAndSetUpCampaign.AppendLine($"campaign.UpdateResources({{{names}}}, {{{urls}}});");
			}
			else
			{
				commandToCreateAndSetUpCampaign.AppendLine("campaign.ClearResources();");
			}

			if (string.IsNullOrWhiteSpace(body.ChosenResource))
			{
				commandToCreateAndSetUpCampaign.AppendLine($"campaign.ClearPreferred();");
			}
			else
			{
				commandToCreateAndSetUpCampaign.AppendLine($"campaign.ChoosePreferredResource('{body.ChosenResource}');");
			}

            commandToCreateAndSetUpCampaign.AppendLine($"campaign.IsEnabled = {body.IsEnabled};");

            IActionResult result;
			if (body.BudgetType == BudgetType.Unlimited)
			{
				if (body.Budget != 0) return BadRequest($"{nameof(body.Budget)} '{body.Budget}' is not valid for {nameof(BudgetType)} {BudgetType.Unlimited}");

				commandToCreateAndSetUpCampaign.AppendLine($"campaign.SetUnlimitedBudget();");
				result = await LoyaltyAPI.Loyalty.PerformChkThenCmdAsync(HttpContext, $@"
				{{
					validDate = {body.StartDate.ToString("M/d/yyyy H:m:s")} < {body.EndDate.ToString("M/d/yyyy H:m:s")};
                    Check(validDate) Error 'Start date must be less than end date';
				}}
				",$@"
				{{
					{commandToCreateAndSetUpCampaign}
					campaign.Description = '{description}';
                    campaign.StartDate = {body.StartDate.ToString("M/d/yyyy H:m:s")};
                    campaign.EndDate = {body.EndDate.ToString("M/d/yyyy H:m:s")};
				}}
			");
			}
			else
			{
				if (body.Budget <= 0) return BadRequest($"Parameter {nameof(body.Budget)} is not valid");

				commandToCreateAndSetUpCampaign.AppendLine($"campaign.UpdateBudget({body.Budget});");
				result = await LoyaltyAPI.Loyalty.PerformChkThenCmdAsync(HttpContext, $@"
					campaign = company.Campaigns.PromotionCampaign({body.CampaignId});
					Check(! campaign.IsSpentAmountGreaterThan({body.Budget})) Error 'New budget {body.Budget} must be greater than current spent budget.';
					validDate = {body.StartDate.ToString("M/d/yyyy H:m:s")} < {body.EndDate.ToString("M/d/yyyy H:m:s")};
                    Check(validDate) Error 'Start date must be less than end date';
				",
					$@"
					{{
						{commandToCreateAndSetUpCampaign}
						campaign.Description = '{description}';
						campaign.StartDate = {body.StartDate.ToString("M/d/yyyy H:m:s")};
                        campaign.EndDate = {body.EndDate.ToString("M/d/yyyy H:m:s")};
					}}
				");
			}

			return result;
		}

		[HttpGet("api/loyalty/campaigns/giftCard/percentages")]
		public async Task<IActionResult> CreateGiftCardCampaignAsync()
		{
			return await LoyaltyAPI.Loyalty.PerformQryAsync(HttpContext, $@"
				{{
					for(percentageRanges : company.Campaigns.GiftCardPercentageRangesDefinition)
					{{
						percentageRange = percentageRanges;
						print percentageRange.MinAmount minAmount;
						print percentageRange.MaxAmount maxAmount;
						print percentageRange.Percentage percentage;
					}}
				}}
			");
		}

		[HttpGet("api/loyalty/campaigns/customers/count")]
		[Authorize(Roles = "a15,a19")]
		public async Task<IActionResult> CustomersLottoRewardCampaignCountAsync()
		{
			return await LoyaltyAPI.Loyalty.PerformQryAsync(HttpContext, $@"
				{{
					for(campaign : company.Campaigns.List())
					{{
						print campaign.Name name;
						print campaign.GivenTimesNumber givenTimesNumber;
					}}
				}}
			");
		}

		[HttpGet("api/loyalty/campaigns/{campaignId}/customers/count")]
		[Authorize(Roles = "a15,a19")]
		public async Task<IActionResult> CustomersLottoRewardCampaignCountAsync(int campaignId)
		{
			if (campaignId <= 0) return BadRequest($"{nameof(campaignId)} is not valid");

			return await LoyaltyAPI.Loyalty.PerformQryAsync(HttpContext, $@"
				{{
					campaign = company.Campaigns.PromotionCampaign({campaignId});
					print campaign.Name name;
					print campaign.GivenTimesNumber givenTimesNumber;
				}}
			");
		}

		[HttpGet("api/loyalty/campaigns/player/badge")]
        [Authorize(Roles = "player")]
        public async Task<IActionResult> BadgeForPlayerAsync()
		{
			string playerId = Validator.StringEscape(Security.PlayerId(User));

			var result = await LoyaltyAPI.Loyalty.PerformQryAsync(HttpContext, $@"
				{{
					badgesCampaign = company.Campaigns.BadgeCampaign(1);
					playerQry = company.CustomerByPlayerId('{playerId}').Player;
					if(playerQry.HasBadges(badgesCampaign))
					{{
						print playerQry.Highest(badgesCampaign).Name badgeName;
					}}
					else
					{{
						print '' badgeName;
					}}
				}}
			");
			return result;
		}

		[HttpPost("api/loyalty/share")]
        [Authorize(Roles = "player")]
        public async Task<IActionResult> ShareToThirdPartyAsync([FromBody] ShareToThirdParty body)
		{
			if (body == null) return NotFound($"Body is required");
			if (!Validator.IsValidPlayer(body.PlayerId)) return NotFound($"{nameof(body.PlayerId)} has not a valid ID");
			string shareItem = Validator.StringEscape(body.ShareItem);

			var playerId = Validator.StringEscape(body.PlayerId);

			var result = await LoyaltyAPI.Loyalty.PerformCmdAsync(HttpContext, $@"
                {{
					player = company.Players.SearchPlayer('{playerId}');
					player.ShareToThirdParty('{shareItem}');
                }}
            ");

			return result;
		}

		[HttpPost("api/loyalty/invite")]
        [Authorize(Roles = "player")]
        public async Task<IActionResult> InviteAPlayerAsync([FromBody] InvitePlayers body)
		{
			if (body == null) return NotFound($"Body is required");
			if (!Validator.IsValidPlayer(body.PlayerId)) return NotFound($"{nameof(body.PlayerId)} has not a valid ID");
			string emails = Validator.StringEscape(body.Emails);
			
			var playerId = Validator.StringEscape(body.PlayerId);
			
			var result = await LoyaltyAPI.Loyalty.PerformCmdAsync(HttpContext, $@"
                {{
					player = company.Players.SearchPlayer('{playerId}');
					player.InvitePlayers('{emails}');
                }}
            ");

			return result;
		}

		[HttpGet("api/loyalty/campaigns/player/achievements")]
        [Authorize(Roles = "player")]
        public async Task<IActionResult> AchievementsCampaignForPlayerAsync(string player)
		{
			if (string.IsNullOrWhiteSpace(player)) return NotFound($"Player id is required");
			if (!Validator.IsValidPlayer(player)) return NotFound($"{nameof(player)} has not a valid ID");

			var result = await LoyaltyAPI.Loyalty.PerformQryAsync(HttpContext, $@"
				{{
					badgesCampaign = company.Campaigns.BadgeCampaign(1);
					playerQry = company.CustomerByPlayerId('{player}').Player;
					for(badge : badgesCampaign.BadgesList)
					{{
						print badge.Name badgeName;
						for (achievement : badgesCampaign.AchievementsNeededFor(badge))
						{{
							print achievement.Label achievementLabel;
							print badgesCampaign.AchievementWasAccomplishedBy(playerQry, achievement) wasAccomplished;
						}}
					}}
				}}
			");
			return result;
		}

        [HttpGet("api/loyalty/player/sales")]
        [Authorize(Roles = "player")]
        public async Task<IActionResult> PlayerSalesAsync(string accountNumber, int promotionNumber, int page = 1, int pageSize = 10)
        {
			if (string.IsNullOrWhiteSpace(accountNumber)) return BadRequest($"Account number is required");
			if (promotionNumber <= 0) return BadRequest($"Promotion number is required");
			if (page <= 0) return BadRequest($"Page must be greater than 0");
			if (pageSize <= 0) return BadRequest($"Page size must be greater than 0");

			string fixedAccountNumber = Validator.StringEscape(accountNumber);

            var result = await LoyaltyAPI.Loyalty.PerformChkThenQryAsync(HttpContext, $@"
				{{
					existsSaleCampaign = company.Campaigns.ExistsSaleCampaign({promotionNumber});
                    Check(existsSaleCampaign) Error 'Campaign does exists, make sure you are using an existing one.';
				}}
				", $@"
		        {{
					campaign = company.Campaigns.SaleCampaignByNumber({promotionNumber});
					playerSales = company.Campaigns.SalesByPlayer('{fixedAccountNumber}', campaign, {page}, {pageSize});

					print campaign.Name name;
					print campaign.StartDate startDate;
					print campaign.EndDate endDate;

					print playerSales.TotalRecords totalRecords;
					print playerSales.TotalRows totalRows;
					for (sales : playerSales.ReportData)
                    {{
                        print sales.Customer customer;
	                    print sales.Account account;
                        print sales.Currency currency;
                        print sales.Amount amount;
						print sales.SaleDate saleDate;
						print sales.Store storeId;
						print sales.Domain domainId;
					}}
				}}
			");
            return result;
        }

        [HttpGet("api/loyalty/campaigns/grafana/dashboard")]
        [Authorize(Roles = "g15,g1,g10,g11,g17,g18,g19")]
        public async Task<IActionResult> GrafanaMainReportAsync(string dashboardName)
        {
            if (string.IsNullOrWhiteSpace(dashboardName)) return BadRequest("Dashboard name is required");

            // fixed grafana dashboard name
            string fixedDashboardName = Validator.StringEscape(dashboardName);

            var result = await LoyaltyAPI.Loyalty.PerformQryAsync(HttpContext, $@"
				{{
					grafanaService = company.GrafanaExternalAPI;
                    print grafanaService.DashboardURL('{fixedDashboardName}') url;
				}}
			");
            return result;
        }

        [HttpGet("api/loyalty/campaigns/grafana/listDashboard")]
        [Authorize(Roles = "g15,g1,g10,g11,g17,g18,g19")]
        public async Task<IActionResult> ListDashboardsAsync()
        {
            var result = await LoyaltyAPI.Loyalty.PerformQryAsync(HttpContext, $@"
				{{
					grafanaService = company.GrafanaExternalAPI;
					dashboardsList = grafanaService.AllDashboard();
	                for (dashboards : dashboardsList)
                    {{
                        print dashboards.title title;
                        print dashboards.uid uid;
                        print dashboards.uri uri;
                        print dashboards.url url;
                    }}
				}}
			");
            return result;
        }

        public void CreateConsumerForTopics(MarketingStorage marketingStorage)
		{
			new CustomersConsumer($"{KafkaMessage.LOYALTY_CONSUMER_PREFIX}{Integration.Kafka.Group}", Integration.Kafka.TopicForCustomers, marketingStorage).StartListening();
			new NotificationConsumer(Integration.Kafka.Group, Integration.Kafka.TopicForNotifications).StartListening();
			new ProfanityConsumer($"{KafkaMessage.LOYALTY_CONSUMER_PREFIX}{Integration.Kafka.Group}", Integration.Kafka.TopicForProfanity).StartListening();
			new ChallengesConsumer(Integration.Kafka.Group, Integration.Kafka.TopicForChallenges).StartListening();
		}

		private class NotificationConsumer : GamesEngine.Settings.Consumer
		{
			public NotificationConsumer(string group, string topic) : base(group, topic) { }

			public override void OnMessageBeforeCommit(string msg)
			{
				if (String.IsNullOrEmpty(msg)) throw new Exception(nameof(msg));

				NotificationMessage notification = new NotificationMessage(msg);
				var description = Validator.StringEscape(notification.Description);
				var playerId = Validator.StringEscape(notification.ToSubscriberId);

				switch (notification.Type)
				{
					case NotificationMessageType.ADD:
						LoyaltyAPI.Loyalty.PerformCmd(
							$@"
								player = company.Players.SearchPlayer('{playerId}');
								store = company.Campaigns.SubscriberById('{notification.From}');
							    player.AddMessage(store,'{description}');");
						break;
					case NotificationMessageType.REMOVE:
						LoyaltyAPI.Loyalty.PerformCmd(
							$@"
								player = company.Players.SearchPlayer('{playerId}');
								player.RemoveMessage('{description}');");
						break;
					case NotificationMessageType.REMOVEALL:
						LoyaltyAPI.Loyalty.PerformCmd(
							$@"
								player = company.Players.SearchPlayer('{playerId}');
								player.RemoveMessages();");
						break;
				}
			}
		}

		private class ChallengesConsumer : GamesEngine.Settings.Consumer
		{
			public ChallengesConsumer(string group, string topic) : base(group, topic)
			{ }

			public override void OnMessageBeforeCommit(string msg)
			{
				if (String.IsNullOrEmpty(msg)) throw new Exception(nameof(msg));

				ChallengeMessage challenge = new ChallengeMessage(msg);

				var playerId = Validator.StringEscape(challenge.PlayerId);

				var result = LoyaltyAPI.Loyalty.PerformCmd($@"
					player = company.Players.SearchPlayer('{playerId}');
                    company.Campaigns.BadgeCampaign({challenge.CampaignNumber}).RecordAchievement(itIsThePresent, player, '{challenge.Achievement}', Now);
                ");
			}
		}

		private class CustomersConsumer : GamesEngine.Settings.Consumer
		{
			private readonly MarketingStorage storage;

            private readonly SalesStorage salesStorage;
            private readonly TotalizerSales totalizerSales;

            public CustomersConsumer(string group, string topic, MarketingStorage storage) : base(group, topic)
			{
				this.storage = storage;

                this.salesStorage = SalesStorage.CreateStorageConnection();
                this.totalizerSales = new TotalizerSales(this.salesStorage);
            }

			public override void OnMessageBeforeCommit(string msg)
			{
				if (String.IsNullOrEmpty(msg)) throw new Exception(nameof(msg));

				CustomerMessage.CustomerMessageType messageType = CustomerMessage.GetType(msg);

				switch (messageType)
				{
					case CustomerMessage.CustomerMessageType.NEW_CUSTOMER:
						{
							NewCustomerMessage customerMsg = new NewCustomerMessage(msg);

							var result = LoyaltyAPI.Loyalty.PerformQry($@"
								{{
									ItsNewOne = company.ExistsCustomer('{customerMsg.AccountNumber}');
									print ItsNewOne thePlayerItsNew;
								}}
							");

							OkObjectResult o = (OkObjectResult)result;
							string json = o.Value.ToString();
							Customer customer = JsonConvert.DeserializeObject<Customer>(json);

							if (!customer.ThePlayerItsNew)
							{
								result = LoyaltyAPI.Loyalty.PerformCmd($@"
									customer = company.CreateCustomer('{customerMsg.AccountNumber}',{((Agents)customerMsg.AgentId).ToString()});
									player = customer.Player;
									{{
										player.NickName = '{customerMsg.DefaultNickNameIfNotExists}';
										player.UpdateAvatar('{customerMsg.DefaultAvatarIfNotExists}', Now);
										player.ApproveAvatar(Now);
									}}
								");
								if (!(result is OkObjectResult))
								{
									throw new GameEngineException($@"Error:{((ObjectResult)result).Value.ToString()}");
								}
							}

							//1 - Viene de KyC y no tiene store id, esta bien porque KyC es un actor general, no hay que registrar acquisition
							//2 - Si tiene store id, primera vez que entra a una tienda
							//3 - Ya existe, porque entro a Lotto, pero ahora esta entrando a MM

							bool isAcquisition = false;
							bool isRetention = false;
							if (customerMsg.HasStoreId)
							{
								if (customer.ThePlayerItsNew)
								{
									isAcquisition = true;
								}
								else
								{
									bool exists = storage.ExistCustomer(customerMsg.AccountNumber, customerMsg.StoreId);
									isAcquisition = !exists;
									isRetention = exists;
								}
							}
							if (isAcquisition)
							{
								storage.StoreAcquisition(customerMsg.StoreId, customerMsg.AccountNumber, customerMsg.AccountNumber, customerMsg.Date);
								var acquisitionDetail = new Adquisition
								(
									customerMsg.AccountNumber,
									customerMsg.AccountNumber,
									customerMsg.Date,
									customerMsg.StoreId
								);
								HttpELKClient.GetInstance().Create(acquisitionDetail);
							}
							else if (isRetention)
							{
								storage.StoreRetention(customerMsg.StoreId, customerMsg.AccountNumber, customerMsg.AccountNumber, customerMsg.Date);
							}
							break;
						}
					case CustomerMessage.CustomerMessageType.SALE:
						{
							CustomerSaleMessage customerMsg = new CustomerSaleMessage(msg);
							storage.StoreSale(customerMsg.StoreId, customerMsg.AccountNumber, customerMsg.AccountNumber, customerMsg.Amount, customerMsg.Date, customerMsg.Domain);
							var saleDetail = new Sale
							(
								customerMsg.AccountNumber,
								customerMsg.AccountNumber,
								customerMsg.Date,
								customerMsg.Amount,
								customerMsg.StoreId
							);
							HttpELKClient.GetInstance().Create(saleDetail);
							var adquisitionDetail = new AdquisitionLastPurchase
							(
								customerMsg.AccountNumber,
								customerMsg.AccountNumber,
								customerMsg.Date,
								customerMsg.Amount,
								customerMsg.StoreId
							);
							HttpELKClient.GetInstance().Update(customerMsg.AccountNumber, customerMsg.AccountNumber, adquisitionDetail);
							var retention = new Retention
							(
								customerMsg.AccountNumber,
								customerMsg.AccountNumber,
								customerMsg.Date,
								customerMsg.StoreId
							);
							HttpELKClient.GetInstance().Create(retention);

							Currencies.CODES currencyCode;
							if (!Enum.TryParse(customerMsg.Amount.CurrencyCode, out currencyCode))
							{
								throw new Exception("Currency code not valid");
							}

                            _ = LoyaltyAPI.Loyalty.PerformChkThenCmdAsync($@"
									{{
										player = company.CustomerByAccountNumber('{customerMsg.AccountNumber}').Player;
										appliesAtCampaign = company.Campaigns.AreApplicableTo(Now, player, {customerMsg.Domain}, {customerMsg.StoreId}, {currencyCode});
										Check(appliesAtCampaign) Information 'OK';
									}}
								", $@"
									{{
										player = company.CustomerByAccountNumber('{customerMsg.AccountNumber}').Player;
										campaigns = company.Campaigns.ActiveSaleCampaign(Now, {customerMsg.Domain}, {customerMsg.StoreId}, {currencyCode});
										for( campaign : campaigns)
										{{
											campaign.RegisterPurchase(itIsThePresent, player, {customerMsg.Date.ToString("M/d/yyyy H:m:s")}, {customerMsg.Amount.Value}, {customerMsg.StoreId}, {customerMsg.Domain}, {customerMsg.AgentId}, {currencyCode});
										}}
									}}"
                                );

                            totalizerSales.UpdateSales(customerMsg.Date);
                            break;
						}
					case CustomerMessage.CustomerMessageType.VISIT:
						{
							CustomerVisitMessage customerMsg = new CustomerVisitMessage(msg);
							storage.StoreRetention(customerMsg.StoreId, customerMsg.AccountNumber, customerMsg.AccountNumber, customerMsg.Date);
							var acquisitionDetail = new Retention
								(
									customerMsg.AccountNumber,
									customerMsg.AccountNumber,
									customerMsg.Date,
									customerMsg.StoreId
								);
							HttpELKClient.GetInstance().Create(acquisitionDetail);
							break;
						}
					case CustomerMessage.CustomerMessageType.CURRENCY:
						var coinMessage = new CoinMessage(msg);
						storage.StoreCurrency(coinMessage);
						break;
				}
			}
		}

		private class ProfanityConsumer : GamesEngine.Settings.ProfanityConsumerValidator
		{

			private readonly HttpRestClientConfiguration client;

			public ProfanityConsumer(string group, string topic) : base(group, topic)
			{
				client = HttpRestClientConfiguration.GetInstance();
			}

			public override void OnMessageBeforeCommit(string message)
			{
				if (String.IsNullOrEmpty(message)) throw new Exception(nameof(message));
				if (!Settings.IsWebProfanityConfigured) { return; }

				ProfanityMessageType messageType = new ProfanityMessage(message).MessageType;

				if (messageType == ProfanityMessageType.NICKNAME)
				{
					var profanityMessage = new NickNameMessage(message);

					String profanityResultText = profanityMessage.Value;
					try
					{
						string profanityUrl = string.Format(Settings.WebProfanityUrl, Settings.WebProfanityKey, Uri.EscapeDataString(profanityMessage.Value));
						var result = client.Get(profanityUrl);
						profanityResultText = ValidateResponseFromWebProfanity(message, profanityUrl, (result as ObjectResult), profanityMessage.Value);
					}
					catch
					{
						//TODO: Erick, Message Profanity failed
					}

					if (profanityMessage.Value == profanityResultText) { return; }
					profanityResultText = Validator.StringEscape(profanityResultText);
					LoyaltyAPI.Loyalty.PerformCmd($@"
                        {{                               
                            player = company.CustomerByAccountNumber('{profanityMessage.AccountNumber}').Player;
					        player.Nickname ='{profanityResultText}';
                        }}"
					);

				}
				else
				{
					throw new Exception("Unhandled profanity message type");
				}
			}
		}

	}


	[DataContract(Name = "Customer")]
	public class Customer
	{
		[DataMember(Name = "thePlayerItsNew")]
		public bool ThePlayerItsNew { get; set; }
	}

	[DataContract(Name = "ListOfPlayersIds")]
	public class ListOfPlayersIds
	{
		[DataMember(Name = "playerIds")]
		public string[] PlayerIds { get; set; }
	}


	[DataContract(Name = "message")]
	public class Message
	{
		[DataMember(Name = "body")]
		public string Body { get; set; }
		[DataMember(Name = "device")]
		public string Device { get; set; }
		[DataMember(Name = "browser")]
		public string Browser { get; set; }
		[DataMember(Name = "store")]
		public int StoreId { get; set; }
	}

	[DataContract(Name = "gameboard")]
	public class AttachFeedback
	{
		[DataMember(Name = "gameboardId")]
		public string GameboardId { get; set; }
		[DataMember(Name = "store")]
		public int StoreId { get; set; }
	}

	[DataContract(Name = "announcement")]
	public class Announcement
	{
		[DataMember(Name = "subject")]
		public string Subject { get; set; }
		[DataMember(Name = "template")]
		public string Template { get; set; }
		[DataMember(Name = "criteria")]
		public string Criteria { get; set; }
		[DataMember(Name = "employeeName")]
		public string EmployeeName { get; set; }
	}

	[DataContract(Name = "avatar")]
	public class Avatar
	{
		[DataMember(Name = "playerId")]
		public string PlayerId { get; set; }
		[DataMember(Name = "isApproved")]
		public bool IsApproved { get; set; }
		[DataMember(Name = "sendNotification")]
		public bool SendNotification { get; set; }
		[DataMember(Name = "store")]
		public int StoreId { get; set; }
	}

	[DataContract(Name = "player")]
	public class Player
	{
		[DataMember(Name = "nickname")]
		public string Nickname { get; set; }
		[DataMember(Name = "avatar")]
		public string Avatar { get; set; }
	}

	[DataContract(Name = "notification")]
	public class Notification
	{
		[DataMember(Name = "from")]
		public string From { get; set; }
		[DataMember(Name = "description")]
		public string Description { get; set; }
	}

	[DataContract(Name = "calculateAmount")]
	public class CalculateAmount
	{
		[DataMember(Name = "amount")]
		public decimal Amount { get; set; }
	}

	[DataContract(Name = "GiftCardCampaignCreationBody")]
	public class GiftCardCampaignCreationBody
	{
		[DataMember(Name = "name")]
		public string Name { get; set; }
		[DataMember(Name = "startDate")]
		public DateTime StartDate { get; set; }
		[DataMember(Name = "endDate")]
		public DateTime EndDate { get; set; }
		[DataMember(Name = "description")]
		public string Description { get; set; }
		[DataMember(Name = "currencyCode")]
		public string CurrencyCode { get; set; }
		[DataMember(Name = "visible")]
		public bool Visible { get; set; }
		[DataMember(Name = "isEnabled")]
		public bool IsEnabled { get; set; }
		[DataMember(Name = "budget")]
		public decimal Budget { get; set; }
		[DataMember(Name = "budgetType")]
		public BudgetType BudgetType { get; set; }
		[DataMember(Name = "type")]
		public CampaignType CampaignType { get; set; }
		[DataMember(Name = "chosenResource")]
		public string ChosenResource { get; set; }
		[DataMember(Name = "resources")]
		public List<ResourceBody> Resources { get; set; }
	}

    [DataContract(Name = "OnTheSpotCampaignCreationBody")]
    public class OnTheSpotCampaignCreationBody
    {
        [DataMember(Name = "name")]
        public string Name { get; set; }
        [DataMember(Name = "description")]
        public string Description { get; set; }
        [DataMember(Name = "startDate")]
        public DateTime StartDate { get; set; }
        [DataMember(Name = "endDate")]
        public DateTime EndDate { get; set; }
        [DataMember(Name = "lastDayToChangePrizes")]
        public DateTime LastDayToChangePrizes { get; set; }
        [DataMember(Name = "returnedCurrencyCode")]
        public string ReturnedCurrencyCode { get; set; }
        [DataMember(Name = "isEnabled")]
        public bool IsEnabled { get; set; }
        [DataMember(Name = "timeZone")]
        public string TimeZone { get; set; }
    }

    [DataContract(Name = "SpinKickCampaignCreationBody")]
    public class SpinKickCampaignCreationBody
    {
        [DataMember(Name = "name")]
        public string Name { get; set; }
        [DataMember(Name = "description")]
        public string Description { get; set; }
		[DataMember(Name = "totalRewardUnits")]
		public int TotalRewardUnits { get; set; }
        [DataMember(Name = "campaignId")]
        public int CampaignId { get; set; }
        [DataMember(Name = "isEnabled")]
        public bool IsEnabled { get; set; }
    }

    [DataContract(Name = "AssignSpinBody")]
    public class AssignSpinBody
    {
        [DataMember(Name = "accountNumber")]
        public string AccountNumber { get; set; }

        [DataMember(Name = "description")]
        public string Description { get; set; }
        [DataMember(Name = "who")]
        public string Who { get; set; }
        [DataMember(Name = "store")]
        public int Store { get; set; }
    }

    [DataContract(Name = "GiftCardCampaignUpdateBody")]
	public class GiftCardCampaignUpdateBody
	{
		[DataMember(Name = "campaignId")]
		public int CampaignId { get; set; }
        [DataMember(Name = "startDate")]
        public DateTime StartDate { get; set; }
        [DataMember(Name = "endDate")]
        public DateTime EndDate { get; set; }
        [DataMember(Name = "description")]
		public string Description { get; set; }
		[DataMember(Name = "visible")]
		public bool Visible { get; set; }
		[DataMember(Name = "isEnabled")]
		public bool IsEnabled { get; set; }
		[DataMember(Name = "budget")]
		public decimal Budget { get; set; }
		[DataMember(Name = "budgetType")]
		public BudgetType BudgetType { get; set; }
		[DataMember(Name = "chosenResource")]
		public string ChosenResource { get; set; }
		[DataMember(Name = "resources")]
		public List<ResourceBody> Resources { get; set; }
	}

	[DataContract(Name = "ResourceBody")]
	public class ResourceBody
	{
		[DataMember(Name = "name")]
		public string Name { get; set; }
		[DataMember(Name = "url")]
		public string Url { get; set; }
	}

	[DataContract(Name = "customerCampaign")]
	public class CustomerCampaign
	{
		private string accountNumber;
		[DataMember(Name = "accountNumber")]
		public string AccountNumber
		{
			get { return accountNumber; }
			set { accountNumber = value.Trim().ToLower(); }
		}


		[DataMember(Name = "amount")]
		public decimal Amount { get; set; }
		[DataMember(Name = "campaignId")]
		public int CampaignNumber { get; set; }
		[DataMember(Name = "description")]
		public string Description { get; set; }
		[DataMember(Name = "who")]
		public string Who { get; set; }
		[DataMember(Name = "currencyCode")]
		public string CurrencyCode { get; set; }
		[DataMember(Name = "store")]
		public int StoreId { get; set; }
	}

	[DataContract(Name = "share")]
	public class ShareToThirdParty
	{
		[DataMember(Name = "playerId")]
		public string PlayerId { get; set; }
		[DataMember(Name = "shareItem")]
		public string ShareItem { get; set; }
	}

	[DataContract(Name = "invite")]
	public class InvitePlayers
	{
		[DataMember(Name = "playerId")]
		public string PlayerId { get; set; }
		[DataMember(Name = "emails")]
		public string Emails { get; set; }
	}

	[DataContract(Name = "ExistCustomer")]
	public class ExistCustomer
	{
		[DataMember(Name = "thePlayerItsNew")]
		public bool ThePlayerItsNew { get; set; }
	}

	[DataContract(Name = "FreePlayAccreditationBody")]
	public class FreePlayAccreditationBody
	{
		[DataMember(Name = "amount")]
		public decimal Amount { get; set; }
		[DataMember(Name = "description")]
		public string Description { get; set; }
		[DataMember(Name = "accountName")]
		public string AccountName { get; set; }
	}
}