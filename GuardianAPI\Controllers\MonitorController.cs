﻿using GamesEngine.Custodian.Persistance;
using GamesEngine.RealTime;
using GamesEngine.RealTime.Events;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Collections.Generic;

namespace GuardianAPI.Controllers
{
	public class MonitorController : AuthorizeController
	{
		[HttpGet("api/monitor/events")]
		[Authorize(Roles = "h3")]
		public IActionResult ListPaymentCommitments(long fromEventId, string processorId)
		{
			PlatformMonitor monitor = PlatformMonitor.GetInstance();
			bool skipProcessId = string.IsNullOrEmpty(processorId);

			IEnumerable<PlatformEvent> result;
			if (skipProcessId)
			{
				result = monitor.List(x => x.Id > fromEventId);
			}
			else
			{
				if (fromEventId == 0) result = monitor.List(x => x.Id > fromEventId && (x as DisbursmentEvent).ProcesorId == processorId);
                else result = monitor.List(x => x.Id <= fromEventId && (x as DisbursmentEvent).ProcesorId == processorId);
			}
			return Ok(result);
		}
	}
}
