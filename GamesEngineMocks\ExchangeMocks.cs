﻿using GamesEngine;
using GamesEngine.Finance;
using GamesEngine.Settings;
using Puppeteer.EventSourcing;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using town.connectors.drivers;
using static GamesEngine.Finance.PaymentChannels;

namespace GamesEngineMocks
{
	public class ExchangeMocks 
	{
		static string artemisLottoScript = $@"
                company = Company();
				store = company.Sales.CreateStore(1,'Picks Store');
				store.Alias = 'lotto';
				store.MakeCurrent();

				coin = company.System.Coins.Add(2, 'USD', '$', 2, '$', 'dollar', {CoinType.Fiat});
				coin.Visible = true;
				coin.Enabled = true;

				artemisEntity = company.System.Entities.Add(2, 'Artemis');
				artemisEntity.Visible = true;
				artemisEntity.Enabled = true;
				zeusEntity = company.System.Entities.Add(3, 'Zeus');
				fieroEntity = company.System.Entities.Add(4, 'Fiero');
				fieroEntity.Visible = true;
				fieroEntity.Enabled = true;
				hadesEntity = company.System.Entities.Add(5, 'Hades');
				hadesEntity.Visible = true;
				hadesEntity.Enabled = true;
				consignmentEntity = company.System.Entities.Add(6, 'Consignment');
				consignmentEntity.Visible = true;
				consignmentEntity.Enabled = true;

				artemisTenant = company.System.Tenants.Add(2, 'Artemis');
				artemisTenant.MakeCurrent();
				artemisTenant.Visible = true;
				artemisTenant.Enabled = true;

				dgsV2PM = company.System.PaymentMethods.Add(3, 'D.G.S. v2');
				dgsV2PM.Visible = true;
				dgsV2PM.Enabled = true;
				pm = company.System.PaymentMethods.Add(9, 'ThirdParty');
				pm.Visible = true;
				pm.Enabled = true;

				depositTransactionType = company.System.TransactionTypes.Add(1, 'Deposit');
				depositTransactionType.Description = 'A sum of money placed in an account';
				depositTransactionType.Visible = true;
				depositTransactionType.Enabled = true;
				withDrawalTransactionType = company.System.TransactionTypes.Add(2, 'Withdrawal');
				withDrawalTransactionType.Description = 'A sum of money take out of an account';
				withDrawalTransactionType.Visible = true;
				withDrawalTransactionType.Enabled = true;
				saleTransactionType = company.System.TransactionTypes.Add(6, 'Sale');
				saleTransactionType.Description = 'The exchange of a commodity for money';
				saleTransactionType.Visible = true;
				saleTransactionType.Enabled = true;

				customSettings = CustomSettingsCollection(company);
				cs = customSettings.AddFixedParameter(Now, 'CashierDriver.url', 'http://cashierapi:5000/'); 
				cs.Description = 'Url Cashier';
				cs = customSettings.AddFixedParameter(Now, 'CompanyBaseUrlServices', 'http://testart.sec-xm41d.com'); 
				cs.Description = 'It is the base production url';
				cs = customSettings.AddFixedParameter(Now, 'TokenSystemId', '3aab83fb'); 
				cs.Description = 'App\'s token id';
				cs = customSettings.AddSecretParameter(Now, 'TokenSystemPassword', '38f845e977af7f2cd6313c5a9d35e4de7f353405100e7748ab15b410eb53d886');
				cs.Description = 'Password for token';
				cs = customSettings.AddFixedParameter(Now, 'SendWagers', true); 
				cs.Description = 'To send wagers to third party';
				cs = customSettings.AddFixedParameter(Now, 'CompanySystemId', 'N/A'); 
				cs.Description = 'company id';
				cs = customSettings.AddSecretParameter(Now, 'CompanySystemPassword', 'N/A'); 
				cs.Description = 'password';
				cs = customSettings.AddFixedParameter(Now, 'NeedsUniqueIdentifierForPaymentHub', 'PRODUCTION_DOES_NOT_NEED_IT'); 
				cs.Description = 'Setting';
				cs = customSettings.AddFixedParameter(Now, 'CompanyClerkId', 'Administrator'); 
				cs.Description = 'Clerk id';

				cs = customSettings.AddVariableParameter('Input$Provider'); 
				cs.Description = 'Consignment provider';
				cs = customSettings.AddVariableParameter('KYC$DOB'); 
				cs.Description = 'Birthdate from KYC';
				cs = customSettings.AddSecretParameter(Now, 'KYCUsername', 'testUser1');
				cs.Description = 'KYC Username for fields';
				cs = customSettings.AddSecretParameter(Now, 'KYCPassword', '12345');
				cs.Description = 'KYC Password for fields';
			";

		static string consignmentScript = $@"
                company = Company();
				coin = company.System.Coins.Add(0, 'FP', 'FP', 2, 'F', 'free play', {CoinType.Digital});
				coin.Visible = true;
				coin.Enabled = true;
				coin = company.System.Coins.Add(2, 'USD', '$', 2, '$', 'dollar', {CoinType.Fiat});
				coin.Visible = true;
				coin.Enabled = true;
				coin = company.System.Coins.Add(3, 'BTC', 'BTC', 8, '₿', 'bitcoin', {CoinType.Crypt});
				coin.Visible = true;
				coin.Enabled = true;
				company.System.Coins.Add(7, 'ETH', 'ETH', 8, 'Ξ', 'ethereum', {CoinType.Crypt});
				
				store = company.Sales.CreateStore(1,'Picks Store');
				store.Alias = 'lotto';
                store = company.Sales.CreateStore(2,'Brackets Store 2019');
				store.Alias = 'mm19';
                store = company.Sales.CreateStore(3,'Brackets Store 2020');
				store.Alias = 'mm20';
                store = company.Sales.CreateStore(4,'Fiero Wallet');
				store.Alias = 'fiero';
				store.MakeCurrent();
                store = company.Sales.CreateStore(5,'Ladybet Store');
				store.Alias = 'ladybet';
                store = company.Sales.CreateStore(6,'Brackets Store 2021');
				store.Alias = 'mm21';
				store = company.Sales.CreateStore(7,'Keno Store');
				store.Alias = 'keno';

				companyIDf2401f84xa94ax4db3xaec4x83f5361730ae = company;
				BTC2USDProd = company.GetOrCreateProductById(1);
				BTC2USDProd.Description = 'BTC to USD';
				BTC2USDProd.Price = 1;
				USD2BTCProd = company.GetOrCreateProductById(2);
				USD2BTCProd.Description = 'USD to BTC';
				USD2BTCProd.Price = 1;
				ETH2USDProd = company.GetOrCreateProductById(3);
				ETH2USDProd.Description = 'ETH to USD';
				ETH2USDProd.Price = 1;
				USD2ETHProd = company.GetOrCreateProductById(4);
				USD2ETHProd.Description = 'USD to ETH';
				USD2ETHProd.Price = 1;
				BTC2ETHProd = company.GetOrCreateProductById(5);
				BTC2ETHProd.Description = 'BTC to ETH';
				BTC2ETHProd.Price = 1;
				ETH2BTCProd = company.GetOrCreateProductById(6);
				ETH2BTCProd.Description = 'ETH to BTC';
				ETH2BTCProd.Price = 1;
				
				marketplace = MarketPlace(company, 'CR');
				cartagoAgent = marketplace.AddAgent('Cartago');
				agent1 = marketplace.AddAgent('1');
				marketplace.AddAgent('San José');
				marketplace.AddAgent('Heredia');

				riskRatings = RiskRatings();

				{{
					accounts = SetOfBalances();
					accounts.AddAccountIn('100-09-0987653-01', Dollar(5000000));
					accounts.AddAccountOut('100-09-0987653-01', Dollar(5000000));
					accounts.AddAccountIn('mrrPx6UBHChhtT9KwuKY4AuQPs1R8A1aXc', Btc(3000));
					accounts.AddAccountOut('mrrPx6UBHChhtT9KwuKY4AuQPs1R8A1aXc', Btc(40));
					accounts.AddAccountIn('42C6000', ETH(100));
					accounts.AddAccountOut('42C5385', ETH(100));
					marketplace.RealAccounts = accounts;
				}}

				riskRating = riskRatings.NewRiskRating('Type 2050392', 'A Description - AUTO', 1);
				
				localhostDomain = company.Sales.CreateDomain(false, 1, 'localhost', {Agents.INSIDER});
				company.Sales.CurrentStore.Add(localhostDomain);
				qaDomain = company.Sales.CreateDomain(false, 2, 'nodo.qatest.ncubo.com', {Agents.INSIDER});
				company.Sales.CurrentStore.Add(qaDomain);

				transactionOutcomes = marketplace.TransactionOutcomes();
				transactionOutcomes.CreatesOutcomesFor(localhostDomain);
				transactionOutcomes.CreatesOutcomesFor(qaDomain);

				agent1.AssignToCurrentStore(localhostDomain);
				agent1.AssignToCurrentStore(qaDomain);
				agent1.Assign(1, 'Picks Store', localhostDomain);
				agent1.Assign(1, qaDomain);

				customSettings = CustomSettingsCollection(company);
				{{
					company.System.Entities.Add(1, 'Apolo');
					artemisEntity = company.System.Entities.Add(2, 'Artemis');
					zeusEntity = company.System.Entities.Add(3, 'Zeus');
					fieroEntity = company.System.Entities.Add(4, 'Fiero');
					fieroEntity.Visible = true;
					fieroEntity.Enabled = true;
					hadesEntity = company.System.Entities.Add(5, 'Hades');
					consignmentEntity = company.System.Entities.Add(6, 'Consignment');
					consignmentEntity.Visible = true;
					consignmentEntity.Enabled = true;

					company.System.Tenants.Add(1, 'Apolo');
					artemisTenant = company.System.Tenants.Add(2, 'Artemis');
					zeusTenant = company.System.Tenants.Add(3, 'Zeus');
					insiderTenant = company.System.Tenants.Add(4, 'Insider');
					hadesTenant = company.System.Tenants.Add(5, 'Hades');
					hadesTenant.MakeCurrent();

					asiPM = company.System.PaymentMethods.Add(1, 'A.S.I.');
					dgsPM = company.System.PaymentMethods.Add(2, 'D.G.S.');
					dgsV2PM = company.System.PaymentMethods.Add(3, 'D.G.S. v2');
					credirCardPM = company.System.PaymentMethods.Add(4, 'Credit card');
					blockChainPM = company.System.PaymentMethods.Add(5, 'Blockchain');
					moneyTransferPM = company.System.PaymentMethods.Add(6, 'Money transfer');
					asiSimulatorPM = company.System.PaymentMethods.Add(7, 'A.S.I. Simulator');
					dsgSimulatorPM = company.System.PaymentMethods.Add(8, 'D.G.S. Simulator');
					pm = company.System.PaymentMethods.Add(9, '{PaymentMethod.Cash.ToString()}');
					pm.Visible = true;
					pm.Enabled = true;
					pm = company.System.PaymentMethods.Add(10, '{PaymentMethod.Bank.ToString()}');
					pm.Visible = true;
					pm.Enabled = true;
					pm = company.System.PaymentMethods.Add(11, '{PaymentMethod.Creditcard.ToString()}');
					pm.Visible = true;
					pm.Enabled = true;
					pm = company.System.PaymentMethods.Add(12, '{PaymentMethod.FinancialServices.ToString()}');
					pm.Visible = true;
					pm.Enabled = true;
					pm = company.System.PaymentMethods.Add(13, '{PaymentMethod.ThirdParty.ToString()}');
					pm.Visible = true;
					pm.Enabled = true;
					pm = company.System.PaymentMethods.Add(14, '{PaymentMethod.Secrets.ToString()}');
					pm.Visible = true;
					pm.Enabled = true;
					pm = company.System.PaymentMethods.Add(15, '{PaymentMethod.P2PTransfer.ToString()}');
					pm.Visible = true;
					pm.Enabled = true;
					pm.AddProvider(1,'MoneyGram');
					pm.AddProvider(2,'Rio Money Transfer');
					pm.AddProvider(3,'Sigue');
					pm.AddProvider(4,'Boss Revolution Money Transfer');
					pm.AddProvider(5,'Delgado Travel');
					pm.AddProvider(6,'Intermex International Money Transfer');
					pm.AddProvider(7,'Maxi Money Services');
					pm.AddProvider(8,'Barri Financial Group');
					pm.AddProvider(9,'La Nacional');
					pm.AddProvider(10,'Dinex');
					pm.AddProvider(11,'Remitly');

					depositTransactionType = company.System.TransactionTypes.Add(1, '{TransactionType.Deposit.ToString()}');
					depositTransactionType.Description = 'A sum of money placed in an account';
					withDrawalTransactionType = company.System.TransactionTypes.Add(2, '{TransactionType.Withdrawal.ToString()}');
					withDrawalTransactionType.Description = 'A sum of money take out of an account';
					transferTransactionType = company.System.TransactionTypes.Add(3, '{TransactionType.Transfer.ToString()}');
					transferTransactionType.Description = 'A sum of money is sent from one account to another';
					creditNoteTransactionType = company.System.TransactionTypes.Add(4, '{TransactionType.CreditNote.ToString()}');
					creditNoteTransactionType.Description = 'It is a document used by a vendor to inform the buyer of mistake on an order';
					debitNoteTransactionType = company.System.TransactionTypes.Add(5, '{TransactionType.DebitNote.ToString()}');
					debitNoteTransactionType.Description = 'It is a document used by a vendor to inform the buyer of current debt obligations';
					saleTransactionType = company.System.TransactionTypes.Add(6, '{TransactionType.Sale.ToString()}');
					saleTransactionType.Description = 'The exchange of a commodity for money';
					depositAndLockTransactionType = company.System.TransactionTypes.Add(7, '{TransactionType.Deposit.ToString()} then Lock');
					depositAndLockTransactionType.Description = 'Add and Lock money placed in an account';

					depositTransactionType.Visible = true;
					depositTransactionType.Enabled = true;
					withDrawalTransactionType.Visible = true;
					withDrawalTransactionType.Enabled = true;
					transferTransactionType.Visible = true;
					transferTransactionType.Enabled = true;
					debitNoteTransactionType.Visible = true;
					debitNoteTransactionType.Enabled = true;
					creditNoteTransactionType.Visible = true;
					creditNoteTransactionType.Enabled = true;
					saleTransactionType.Visible = true;
					saleTransactionType.Enabled = true;
					depositAndLockTransactionType.Visible = true;
					depositAndLockTransactionType.Enabled = true;

					cs = customSettings.AddFixedParameter(Now, 'CashierDriver.url', 'http://cashierapi:5000/'); 
					cs.Description = 'Url Cashier';
					cs = customSettings.AddFixedParameter(Now, 'CompanyBaseUrlServices', 'http://cashierapi:5000/'); 
					cs.Description = 'It is the base production url';
					cs = customSettings.AddFixedParameter(Now, 'TokenSystemId', '3aab83fb'); 
					cs.Description = 'App\'s token id';
					cs = customSettings.AddSecretParameter(Now, 'TokenSystemPassword', '38f845e977af7f2cd6313c5a9d35e4de7f353405100e7748ab15b410eb53d886');
					cs.Description = 'Password for token';
					cs = customSettings.AddFixedParameter(Now, 'SendWagers', true); 
					cs.Description = 'To send wagers to third party';
					cs = customSettings.AddFixedParameter(Now, 'CompanySystemId', 'N/A'); 
					cs.Description = 'company id';
					cs = customSettings.AddSecretParameter(Now, 'CompanySystemPassword', 'N/A'); 
					cs.Description = 'password';
					cs = customSettings.AddFixedParameter(Now, 'NeedsUniqueIdentifierForPaymentHub', 'PRODUCTION_DOES_NOT_NEED_IT'); 
					cs.Description = 'Setting';
					cs = customSettings.AddFixedParameter(Now, 'CompanyClerkId', 'Administrator'); 
					cs.Description = 'Clerk id';

					cs = customSettings.AddVariableParameter('Input$Provider'); 
					cs.Description = 'Consignment provider';
					cs = customSettings.AddVariableParameter('KYC$DOB'); 
					cs.Description = 'Birthdate from KYC';
					cs = customSettings.AddSecretParameter(Now, 'KYCUsername', 'testUser1');
					cs.Description = 'KYC Username for fields';
					cs = customSettings.AddSecretParameter(Now, 'KYCPassword', '12345');
					cs.Description = 'KYC Password for fields';
				}}
			";

		static string zeusKenoScript = $@"
                company = Company();
				store = company.Sales.CreateStore(7,'Keno Store');
				store.Alias = 'keno';
				store.MakeCurrent();

				coin = company.System.Coins.Add(2, 'USD', '$', 2, '$', 'dollar', {CoinType.Fiat});
				coin.Visible = true;
				coin.Enabled = true;

				artemisEntity = company.System.Entities.Add(3, 'Zeus');
				artemisEntity.Visible = true;
				artemisEntity.Enabled = true;
				artemisTenant = company.System.Tenants.Add(3, 'Zeus');
				artemisTenant.MakeCurrent();
				artemisTenant.Visible = true;
				artemisTenant.Enabled = true;

				dgsPM = company.System.PaymentMethods.Add(2, 'D.G.S');
				dgsPM.Visible = true;
				dgsPM.Enabled = true;
				pm = company.System.PaymentMethods.Add(9, 'ThirdParty');
				pm.Visible = true;
				pm.Enabled = true;

				depositTransactionType = company.System.TransactionTypes.Add(1, 'Deposit');
				depositTransactionType.Description = 'A sum of money placed in an account';
				depositTransactionType.Visible = true;
				depositTransactionType.Enabled = true;
				withDrawalTransactionType = company.System.TransactionTypes.Add(2, 'Withdrawal');
				withDrawalTransactionType.Description = 'A sum of money take out of an account';
				saleTransactionType = company.System.TransactionTypes.Add(6, 'Sale');
				saleTransactionType.Description = 'The exchange of a commodity for money';
				saleTransactionType.Visible = true;
				saleTransactionType.Enabled = true;

				customSettings = CustomSettingsCollection(company);
				cs = customSettings.AddFixedParameter(Now, 'CashierDriver.url', 'http://cashierapi:5000/'); 
				cs.Description = 'Url Cashier';
				cs = customSettings.AddFixedParameter(Now, 'CompanyBaseUrlServices', 'https://backend.betdsi.eu/LottoAPI/v1/'); 
				cs.Description = 'It is the base production url';
				cs = customSettings.AddFixedParameter(Now, 'TokenSystemId', '3aab83fb'); 
				cs.Description = 'App\'s token id';
				cs = customSettings.AddSecretParameter(Now, 'TokenSystemPassword', '38f845e977af7f2cd6313c5a9d35e4de7f353405100e7748ab15b410eb53d886');
				cs.Description = 'Password for token';
				cs = customSettings.AddFixedParameter(Now, 'SendWagers', true); 
				cs.Description = 'To send wagers to third party';
				cs = customSettings.AddFixedParameter(Now, 'CompanySystemId', 'N/A'); 
				cs.Description = 'company id';
				cs = customSettings.AddSecretParameter(Now, 'CompanySystemPassword', 'N/A'); 
				cs.Description = 'password';
				cs = customSettings.AddFixedParameter(Now, 'NeedsUniqueIdentifierForPaymentHub', 'PRODUCTION_DOES_NOT_NEED_IT'); 
				cs.Description = 'Setting';
				cs = customSettings.AddFixedParameter(Now, 'CompanyClerkId', 'Administrator'); 
				cs.Description = 'Clerk id';

				cs = customSettings.AddVariableParameter('Input$Provider'); 
				cs.Description = 'Consignment provider';
				cs = customSettings.AddVariableParameter('KYC$DOB'); 
				cs.Description = 'Birthdate from KYC';
				cs = customSettings.AddSecretParameter(Now, 'KYCUsername', 'testUser1');
				cs.Description = 'KYC Username for fields';
				cs = customSettings.AddSecretParameter(Now, 'KYCPassword', '12345');
				cs.Description = 'KYC Password for fields';
			";

		public static void CustomerWith1000BTC(Puppeteer.EventSourcing.Actor actor)
		{
			int entityId = 5;
			int storeId = 4;
			var mock = new ExchangeMockGenerator(actor).
				CreateBatches().
				CreateCustomer("LO63738186257747081","*********").
				CreateCustomer("LO63738186257747082","**********").
				CreateCustomer("LO63738186257747083","*********").
				CreateRate("USD", "BTC", 8501, 8500).
				SetAmountRange("CR/1", "ccajero", "*********").
				SetAmountRange("CR/1", "ccajeroqa", "*********").
				CreateADraftDeposit("*********", Currencies.CODES.BTC, 1000, "CR/1", "ccajeroqa", "localhost", PaymentMethod.ThirdParty, entityId).
				AproveTransaction("#1234", "*********", Coinage.Coin(Currencies.CODES.BTC), 1000, 1, "CR/1", "ccajeroqa", "localhost", PaymentMethod.ThirdParty, entityId, storeId);
		}

		public static void OnlyBasicStuff(Puppeteer.EventSourcing.Actor actor)
		{
			int entityId = 5;
			int storeId = 4;
			var mock = new ExchangeMockGenerator(actor).
				CreateBatches().
				CreatePlayers().
				CreateCustomer("LO63738186257747081","*********").
				CreateCustomer("LO63738186257747082","**********").
				CreateCustomer("LO63738186257747083","*********").
				CreateCustomer("LO63738186257747000", "").
				CreateCustomer("LO63738186257747001", "").
				CreateCustomer("LO63738186257747002", "223344550").
				CreateRate("USD", "BTC", 18505, 18500).
				SetAmountRange("CR/1", "ccajero", "*********").
				SetAmountRange("CR/1", "ccajeroqa", "*********");
				
            _ = Task.Run(() => ApproveAndDepositAsync(mock));
		}

        static async Task ApproveAndDepositAsync(ExchangeMockGenerator mock)
        {
			int entityId = 5;
			int storeId = 4;
			await Task.Delay(20000);
			mock.CreateRate("USD", "BTC", 18510, 18500, true);
			mock.CreateADraftDeposit("*********", Currencies.CODES.USD, 10000, "CR/1", "ccajeroqa", "localhost", PaymentMethod.ThirdParty, entityId);
			mock.AproveTransaction("cr00070002", "*********", Coinage.Coin(Currencies.CODES.USD), 10000, 1, "CR/1", "ccajeroqa", "localhost", PaymentMethod.ThirdParty, entityId, storeId);
			mock.MakeDeposit("H_T_D_USD_2", "*********", Coinage.Coin(Currencies.CODES.USD), 200000, "localhost", PaymentMethod.ThirdParty, entityId, storeId, "CR/1");
		}

		public static void ArtemisLottoMock(Puppeteer.EventSourcing.Actor actor)
		{
			var mock = new ExchangeMockGenerator(actor, artemisLottoScript);
		}

		public static void ZeusKenoMock(Puppeteer.EventSourcing.Actor actor)
		{
			var mock = new ExchangeMockGenerator(actor, zeusKenoScript);
		}

		public static void ConsignmentMock(Puppeteer.EventSourcing.Actor actor)
		{
			int entityId = 6;
			int storeId = 4;
			var mock = new ExchangeMockGenerator(actor, consignmentScript).
				CreateBatches().
				CreatePlayers().
				CreateCustomer("LO63738186257747081","*********").
				CreateCustomer("LO63738186257747082","**********").
				CreateCustomer("LO63738186257747083","*********").
				CreateRate("USD", "BTC", 8501, 8500).
				SetAmountRange("CR/1", "ccajero", "*********").
				SetAmountRange("CR/1", "ccajeroqa", "*********").
				CreateADraftDeposit("*********", Currencies.CODES.USD, 1000, "CR/1", "ccajeroqa", "localhost", PaymentMethod.ThirdParty, entityId).
				AproveTransaction("cr00070002", "*********", Coinage.Coin(Currencies.CODES.USD), 1000, 1, "CR/1", "ccajeroqa", "localhost", PaymentMethod.P2PTransfer, entityId, storeId);
		}
	}
}
