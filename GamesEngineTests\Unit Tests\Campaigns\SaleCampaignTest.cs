﻿using GamesEngine.Bets;
using GamesEngine.Business;
using GamesEngine.Finance;
using GamesEngine.Marketing.Campaigns.Rewarders;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using GamesEngine.PurchaseOrders;
using GamesEngineTests.Unit_Tests.Games.Lotto;
using GamesEngine.Marketing.Campaigns;
using GamesEngine.Marketing.Campaigns.Base;
using static GamesEngine.Marketing.Campaigns.ActionerStreak;
using GamesEngine.Time;
using static GamesEngine.Marketing.Campaigns.RewardBased;
using Microsoft.VisualBasic;
using static GamesEngine.Exchange.FloatingExchangeRate;
using GamesEngine.Games;
using Utilities;
using GamesEngine.Domains;

namespace GamesEngineTests.Unit_Tests.Campaigns
{
    [TestClass]
    public class SaleCampaignTest
    {
        private static Company CompanySetupTest(bool itIsThePresent)
        {
            Company company = new Company();
            CurrenciesTest.AddCurrencies();

            company.Sales.CreateDomain(itIsThePresent, 1, "http://Test.com", 1);
            company.Sales.CreateDomain(itIsThePresent, 2, "http://Test2.com", 1);
            var store = company.Sales.CreateStore(1, "Test Store");
            CurrenciesTest.AddCurrencies();
            store.MakeCurrent();

            return company;
        }
        private static ActionerStreak ActionStreakCampaign_Cashback_Tiered_Daily_Test(Company company, bool setupCompany = true)
        {
            bool itIsThePresent = false;            

            int promotionNumber = 1;
            decimal minPurchaseAllowed = 10M;
            int maxLevel = 11;
            DateTime creationDate = new DateTime(2024, 04, 01);
            string name = "Streak";
            Currencies.CODES currencyCode = Currencies.CODES.FP;
            DateTime startDate = new DateTime(2024, 04, 01);
            DateTime endDate = new DateTime(2024, 04, 29);
            string descripcion = "test";
            DateTime lastDayToChangePrizes = new DateTime(2024, 04, 30);
            bool isEnable = true;
            Currencies.CODES applicableCurrency = Currencies.CODES.USD;
            RewarderType rewarderType = RewarderType.TieredCashback;
            CampaignScaleType scaleType = CampaignScaleType.DailyCounter;

            ActionerStreak campaign = company.Campaigns.CreateStreakCampaign(itIsThePresent, promotionNumber, creationDate, name, currencyCode, startDate, endDate, maxLevel, descripcion, rewarderType, scaleType, isEnable, applicableCurrency);
            campaign.LastDayToChangePrizes = lastDayToChangePrizes;
            campaign.MinPurchaseAllowed = minPurchaseAllowed;
            JournalDayOfWeek applicableDays = JournalDayOfWeek.Monday | JournalDayOfWeek.Tuesday | JournalDayOfWeek.Wednesday | JournalDayOfWeek.Thursday | JournalDayOfWeek.Friday;
            campaign.SetApplicableDays(applicableDays);
            JournalDayOfWeek claimDayOfTheWeek = JournalDayOfWeek.Saturday | JournalDayOfWeek.Sunday;
            campaign.SetClaimDayOfTheWeek(claimDayOfTheWeek);
            campaign.SetClaimRangeDates(new List<RangeDate>() { new RangeDate(new DateTime(2024, 04, 1), new DateTime(2024, 04, 30)) });

            TieredCashbackRewarder campaignRewarder = company.Campaigns.Rewarder(campaign) as TieredCashbackRewarder;
            List<decimal> lowerLimits = new List<decimal>() { minPurchaseAllowed, 50, 100 };
            List<decimal> upperLimits = new List<decimal>() { 50, 100, 10000 };
            List<decimal> percentages = new List<decimal>() { 0.1M, 0.15M, 0.2M };
            campaignRewarder.SetupCashbackStage(lowerLimits, upperLimits, percentages);

            var domain = company.Sales.DomainFrom(1);
            campaign.ApplyDomains(new List<int> { domain.Id });
            var store = company.Sales.StoreById(1);
            campaign.ApplyStores(new List<int> { store.Id });

            return campaign;
        }

        private static ActionerStreak ActionStreakCampaign_Cashback_Tiered_Amount_Test(Company company, bool setupCompany = true)
        {
            bool itIsThePresent = false;

            int promotionNumber = 2;
            decimal minPurchaseAllowed = 10M;
            int maxLevel = 11;
            DateTime creationDate = new DateTime(2024, 04, 01);
            string name = "Streak";
            Currencies.CODES currencyCode = Currencies.CODES.FP;
            DateTime startDate = new DateTime(2024, 04, 01);
            DateTime endDate = new DateTime(2024, 04, 29);
            string descripcion = "test";
            DateTime lastDayToChangePrizes = new DateTime(2024, 04, 30);
            bool isEnable = true;
            Currencies.CODES applicableCurrency = Currencies.CODES.USD;
            RewarderType rewarderType = RewarderType.TieredCashback;
            CampaignScaleType scaleType = CampaignScaleType.AmountCounter;

            ActionerStreak campaign = company.Campaigns.CreateStreakCampaign(itIsThePresent, promotionNumber, creationDate, name, currencyCode, startDate, endDate, maxLevel, descripcion, rewarderType, scaleType, isEnable, applicableCurrency);
            campaign.LastDayToChangePrizes = lastDayToChangePrizes;
            campaign.MinPurchaseAllowed = minPurchaseAllowed;
            JournalDayOfWeek applicableDays = JournalDayOfWeek.Monday | JournalDayOfWeek.Tuesday | JournalDayOfWeek.Wednesday | JournalDayOfWeek.Thursday | JournalDayOfWeek.Friday;
            campaign.SetApplicableDays(applicableDays);
            JournalDayOfWeek claimDayOfTheWeek = JournalDayOfWeek.Saturday | JournalDayOfWeek.Sunday;
            campaign.SetClaimDayOfTheWeek(claimDayOfTheWeek);
            campaign.SetClaimRangeDates(new List<RangeDate>() { new RangeDate(new DateTime(2024, 04, 1), new DateTime(2024, 04, 30)) });

            campaign.PlayerAmountGoal = 25M;

            TieredCashbackRewarder campaignRewarder = company.Campaigns.Rewarder(campaign) as TieredCashbackRewarder;
            List<decimal> lowerLimits = new List<decimal>() { minPurchaseAllowed, 50, 100 };
            List<decimal> upperLimits = new List<decimal>() { 50, 100, 10000 };
            List<decimal> percentages = new List<decimal>() { 0.1M, 0.15M, 0.2M };
            campaignRewarder.SetupCashbackStage(lowerLimits, upperLimits, percentages);

            var domain = company.Sales.DomainFrom(1);
            campaign.ApplyDomains(new List<int> { domain.Id });
            var store = company.Sales.StoreById(1);
            campaign.ApplyStores(new List<int> { store.Id });

            return campaign;
        }

        private static ActionerStreak ActionStreakCampaign_Cashback_Flat_Test(Company company, bool setupCompany = true)
        {
            bool itIsThePresent = false;

            int promotionNumber = 3;
            decimal minPurchaseAllowed = 10M;
            int maxLevel = 11;
            DateTime creationDate = new DateTime(2024, 04, 01);
            string name = "Streak";
            Currencies.CODES currencyCode = Currencies.CODES.FP;
            DateTime startDate = new DateTime(2024, 04, 01);
            DateTime endDate = new DateTime(2024, 04, 29);
            string descripcion = "test";
            DateTime lastDayToChangePrizes = new DateTime(2024, 04, 30);
            bool isEnable = true;
            Currencies.CODES applicableCurrency = Currencies.CODES.USD;
            RewarderType rewarderType = RewarderType.FlatCashback;
            CampaignScaleType scaleType = CampaignScaleType.DailyCounter;

            ActionerStreak campaign = company.Campaigns.CreateStreakCampaign(itIsThePresent, promotionNumber, creationDate, name, currencyCode, startDate, endDate, maxLevel, descripcion, rewarderType, scaleType, isEnable, applicableCurrency);
            campaign.LastDayToChangePrizes = lastDayToChangePrizes;
            campaign.MinPurchaseAllowed = minPurchaseAllowed;
            JournalDayOfWeek applicableDays = JournalDayOfWeek.Monday | JournalDayOfWeek.Tuesday | JournalDayOfWeek.Wednesday | JournalDayOfWeek.Thursday | JournalDayOfWeek.Friday;
            campaign.SetApplicableDays(applicableDays);
            JournalDayOfWeek claimDayOfTheWeek = JournalDayOfWeek.Saturday | JournalDayOfWeek.Sunday;
            campaign.SetClaimDayOfTheWeek(claimDayOfTheWeek);
            campaign.SetClaimRangeDates(new List<RangeDate>() { new RangeDate(new DateTime(2024, 04, 1), new DateTime(2024, 04, 30)) });

            FlatCashbackRewarder campaignRewarder = company.Campaigns.Rewarder(campaign) as FlatCashbackRewarder;
            campaignRewarder.SetupCashbackStage(0.1M);

            var domain = company.Sales.DomainFrom(1);
            campaign.ApplyDomains(new List<int> { domain.Id });
            var store = company.Sales.StoreById(1);
            campaign.ApplyStores(new List<int> { store.Id });

            return campaign;
        }

        private static ActionerStreak ActionStreakCampaign_WheelTest(Company company, bool setupCompany = true)
        {
            bool itIsThePresent = false;

            int promotionNumber = 4;
            int maxLevel = 11;
            DateTime creationDate = new DateTime(2024, 04, 01);
            string name = "Streak";
            Currencies.CODES currencyCode = Currencies.CODES.FP;
            DateTime startDate = new DateTime(2024, 04, 01);
            DateTime endDate = new DateTime(2024, 04, 29);
            string descripcion = "test";
            DateTime lastDayToChangePrizes = new DateTime(2024, 04, 30);
            bool isEnable = true;
            Currencies.CODES applicableCurrency = Currencies.CODES.USD;
            RewarderType rewarderType = RewarderType.SpinWheel;
            CampaignScaleType scaleType = CampaignScaleType.DailyCounter;

            ActionerStreak campaign = company.Campaigns.CreateStreakCampaign(itIsThePresent, promotionNumber, creationDate, name, currencyCode, startDate, endDate, maxLevel, descripcion, rewarderType, scaleType, isEnable, applicableCurrency);
            campaign.LastDayToChangePrizes = lastDayToChangePrizes;

            SpinWheelRewarder campaignRewarder = company.Campaigns.Rewarder(campaign) as SpinWheelRewarder;
            campaignRewarder.ModifySpinWheel(new string[] { "0.25", "0.25", "0.5", "0.5", "1", "2", "3", "5" }, new decimal[] { 0.5M, 0.50M, 0.75M, 1, 1.25M, 1.50M, 1.75M, 2 }, "Levl0", 0, 1);
            campaignRewarder.ModifySpinWheel(new string[] { "0.25", "0.25", "0.5", "0.5", "1", "2", "3", "5" }, new decimal[] { 0.5M, 0.50M, 0.75M, 1, 1.25M, 1.50M, 1.75M, 2 }, "Levl1", 1, 5);
            campaignRewarder.ModifySpinWheel(new string[] { "0.25", "0.25", "0.5", "0.5", "1", "2", "3", "5" }, new decimal[] { 0.5M, 0.50M, 0.75M, 1, 1.25M, 1.50M, 1.75M, 2 }, "Levl2", 2, 5);
            campaignRewarder.ModifySpinWheel(new string[] { "0.25", "0.25", "0.5", "0.5", "1", "2", "3", "5" }, new decimal[] { 0.5M, 0.50M, 0.75M, 1, 1.25M, 1.50M, 1.75M, 2 }, "Levl3", 3, 5);
            campaignRewarder.ModifySpinWheel(new string[] { "0.25", "0.25", "0.5", "0.5", "1", "2", "3", "5" }, new decimal[] { 0.5M, 0.50M, 0.75M, 1, 1.25M, 1.50M, 1.75M, 2 }, "Levl4", 4, 5);
            campaignRewarder.ModifySpinWheel(new string[] { "0.25", "0.25", "0.5", "0.5", "1", "2", "3", "5" }, new decimal[] { 0.5M, 0.50M, 0.75M, 1, 1.25M, 1.50M, 1.75M, 2 }, "Levl5", 5, 5);
            campaignRewarder.ModifySpinWheel(new string[] { "0.25", "0.25", "0.5", "0.5", "1", "2", "3", "5" }, new decimal[] { 0.5M, 0.50M, 0.75M, 1, 1.25M, 1.50M, 1.75M, 2 }, "Levl6", 6, 5);
            campaignRewarder.ModifySpinWheel(new string[] { "0.25", "0.25", "0.5", "0.5", "1", "2", "3", "5" }, new decimal[] { 0.5M, 0.50M, 0.75M, 1, 1.25M, 1.50M, 1.75M, 2 }, "Levl7", 7, 5);
            campaignRewarder.ModifySpinWheel(new string[] { "0.25", "0.25", "0.5", "0.5", "1", "2", "3", "5" }, new decimal[] { 0.5M, 0.50M, 0.75M, 1, 1.25M, 1.50M, 1.75M, 2 }, "Levl8", 8, 5);
            campaignRewarder.ModifySpinWheel(new string[] { "0.25", "0.25", "0.5", "0.5", "1", "2", "3", "5" }, new decimal[] { 0.5M, 0.50M, 0.75M, 1, 1.25M, 1.50M, 1.75M, 2 }, "Levl9", 9, 5);
            campaignRewarder.ModifySpinWheel(new string[] { "0.25", "0.25", "0.5", "0.5", "1", "2", "3", "5" }, new decimal[] { 0.5M, 0.50M, 0.75M, 1, 1.25M, 1.50M, 1.75M, 2 }, "Levl10", 10, 5);

            var domain = company.Sales.DomainFrom(1);
            campaign.ApplyDomains(new List<int> { domain.Id });
            var store = company.Sales.StoreById(1);
            campaign.ApplyStores(new List<int> { store.Id });

            return campaign;
        }

        private static RewardBased RewardBased_CashbackTieredTest(Company company, bool setupCompany = true)
        {
            bool itIsThePresent = false;

            decimal maxAccruedPlayer = 100M;
            decimal minPurchaseAllowed = 10M;
            JournalDayOfWeek applicableDays = JournalDayOfWeek.AllWeek;
            Currencies.CODES applicableCurrencies = Currencies.CODES.USD;
            Currencies.CODES returnedCoin = Currencies.CODES.FP;
            int promotionNumber = 5;
            string name = "CashBack AllWeek";
            DateTime startDate = new DateTime(2024, 04, 01);
            DateTime endDate = new DateTime(2024, 04, 29);
            string description = "This is a test for cashback campaign.";

            JournalDayOfWeek claimDayOfTheWeek = JournalDayOfWeek.AllWeek;
            List<DateTime> claimDates = new List<DateTime>();
            List<RangeDate> claimRangeDates = new List<RangeDate>() { new RangeDate(new DateTime(2024, 04, 29), new DateTime(2024, 04, 30)) };
            bool isEnabled = true;
            RewarderType rewardType = RewarderType.TieredCashback;

            List<decimal> lowerLimits = new List<decimal>() { minPurchaseAllowed, 50, 100 };
            List<decimal> upperLimits = new List<decimal>() { 50, 100, 10000 };
            List<decimal> percentages = new List<decimal>() { 0.1M, 0.15M, 0.2M };

            var campaign = company.Campaigns.CreateRewardBasedCampaign(itIsThePresent, DateTime.Now, rewardType, minPurchaseAllowed, applicableDays, applicableCurrencies, returnedCoin, company, promotionNumber, name, startDate, endDate, description, isEnabled);
            campaign.SetClaimDayOfTheWeek(claimDayOfTheWeek);
            campaign.SetClaimDates(claimDates);
            campaign.SetClaimRangeDates(claimRangeDates);

            Domain domain1 = company.Sales.DomainFrom(1);
            Domain domain2 = company.Sales.DomainFrom(2);
            campaign.ApplyDomains(new List<int>() { domain1.Id, domain2.Id });
            var store = company.Sales.StoreById(1);
            campaign.ApplyStores(new List<int>() { store.Id });

            TieredCashbackRewarder campaignRewarder = company.Campaigns.Rewarder(campaign) as TieredCashbackRewarder;
            campaignRewarder.SetupCashbackStage(lowerLimits, upperLimits, percentages);
            campaignRewarder.MaxAccruedPlayer = maxAccruedPlayer;
            return campaign;
        }

        private static RewardBased RewardBased_CashbackFlatTest(Company company, bool setupCompany = true)
        {
            bool itIsThePresent = false;

            decimal maxAccruedPlayer = 100M;
            decimal minPurchaseAllowed = 10M;
            JournalDayOfWeek applicableDays = JournalDayOfWeek.AllWeek;
            Currencies.CODES applicableCurrencies = Currencies.CODES.USD;
            Currencies.CODES returnedCoin = Currencies.CODES.FP;
            int promotionNumber = 6;
            string name = "CashBack AllWeek";
            DateTime startDate = new DateTime(2024, 04, 01);
            DateTime endDate = new DateTime(2024, 04, 29);
            string description = "This is a test for cashback campaign.";

            JournalDayOfWeek claimDayOfTheWeek = JournalDayOfWeek.AllWeek;
            List<DateTime> claimDates = new List<DateTime>();
            List<RangeDate> claimRangeDates = new List<RangeDate>() { new RangeDate(new DateTime(2024, 04, 29), new DateTime(2024, 04, 30)) };
            bool isEnabled = true;
            RewarderType rewardType = RewarderType.FlatCashback;

            var campaign = company.Campaigns.CreateRewardBasedCampaign(itIsThePresent, DateTime.Now, rewardType, minPurchaseAllowed, applicableDays, applicableCurrencies, returnedCoin, company, promotionNumber, name, startDate, endDate, description, isEnabled);
            campaign.SetClaimDayOfTheWeek(claimDayOfTheWeek);
            campaign.SetClaimDates(claimDates);
            campaign.SetClaimRangeDates(claimRangeDates);            
            
            Domain domain1 = company.Sales.DomainFrom(1);
            Domain domain2 = company.Sales.DomainFrom(2);
            campaign.ApplyDomains(new List<int>() { domain1.Id, domain2.Id });
            var store = company.Sales.StoreById(1);
            campaign.ApplyStores(new List<int>() { store.Id });

            FlatCashbackRewarder campaignRewarder = company.Campaigns.Rewarder(campaign) as FlatCashbackRewarder;
            campaignRewarder.SetupCashbackStage(0.1M);
            campaignRewarder.MaxAccruedPlayer = maxAccruedPlayer;
            return campaign;
        }

        private static RewardBased RewardBased_WheelTest(Company company, bool setupCompany = true)
        {
            bool itIsThePresent = false;
            var domain1 = company.Sales.DomainFrom(1);
            var domain2 = company.Sales.DomainFrom(2);

            var store1 = company.Sales.StoreById(1);

            decimal minPurchaseAllowed = 10M;
            JournalDayOfWeek applicableDays = JournalDayOfWeek.AllWeek;
            Currencies.CODES applicableCurrencies = Currencies.CODES.USD;
            Currencies.CODES returnedCoin = Currencies.CODES.FP;
            int promotionNumber = 7;
            string name = "CashBack AllWeek";
            DateTime startDate = new DateTime(2024, 04, 01);
            DateTime endDate = new DateTime(2024, 04, 29);
            string description = "This is a test for cashback campaign.";
            bool isEnabled = true;
            RewarderType rewardType = RewarderType.SpinWheel;

            var campaign = company.Campaigns.CreateRewardBasedCampaign(itIsThePresent, DateTime.Now, rewardType, minPurchaseAllowed, applicableDays, applicableCurrencies, returnedCoin, company, promotionNumber, name, startDate, endDate, description, isEnabled);
            campaign.ApplyDomains(new List<int>() { domain1.Id, domain2.Id });
            campaign.ApplyStores(new List<int>() { store1.Id });

            campaign.MaxLevels = 3;
            campaign.LastDayToChangePrizes = new DateTime(2024, 04, 30);

            SpinWheelRewarder spinWheelRewarder = company.Campaigns.Rewarder(campaign) as SpinWheelRewarder;
            spinWheelRewarder.SetupSpinWheel(new string[] { "0.25", "0.25", "0.5", "0.5", "1", "2", "3", "5" }, new decimal[] { 0.5M, 0.50M, 0.75M, 1, 1.25M, 1.50M, 1.75M, 2 }, "Levl0", 0, 1);
            spinWheelRewarder.SetupSpinWheel(new string[] { "0.25", "0.25", "0.5", "0.5", "1", "2", "3", "5" }, new decimal[] { 0.5M, 0.50M, 0.75M, 1, 1.25M, 1.50M, 1.75M, 2 }, "Levl1", 1, 1);
            spinWheelRewarder.SetupSpinWheel(new string[] { "0.25", "0.25", "0.5", "0.5", "1", "2", "3", "5" }, new decimal[] { 0.5M, 0.50M, 0.75M, 1, 1.25M, 1.50M, 1.75M, 2 }, "Levl2", 2, 1);

            List<decimal> lowerLimits = new List<decimal>() { minPurchaseAllowed, 50, 100 };
            List<decimal> upperLimits = new List<decimal>() { 50, 100, 10000 };
            string errorCheck = SpinWheelRewarder.CheckStepStage(minPurchaseAllowed, lowerLimits, upperLimits);
            Assert.IsTrue(errorCheck == string.Empty);
            spinWheelRewarder.SetupStepStage(lowerLimits, upperLimits);

            return campaign;
        }

        private static RewardBased RewardBased_NoneTest(Company company, bool setupCompany = true)
        {
            bool itIsThePresent = false;
            var domain1 = company.Sales.DomainFrom(1);
            var domain2 = company.Sales.DomainFrom(2);

            var store1 = company.Sales.StoreById(1);

            decimal minPurchaseAllowed = 10M;
            JournalDayOfWeek applicableDays = JournalDayOfWeek.AllWeek;
            Currencies.CODES applicableCurrencies = Currencies.CODES.USD;
            Currencies.CODES returnedCoin = Currencies.CODES.FP;
            int number = 1;
            string name = "CashBack AllWeek";
            DateTime startDate = new DateTime(2024, 04, 01);
            DateTime endDate = new DateTime(2024, 04, 29);
            string description = "This is a test for cashback campaign.";
            bool isEnabled = true;
            RewarderType rewardType = RewarderType.None;

            var campaign = company.Campaigns.CreateRewardBasedCampaign(itIsThePresent, DateTime.Now, rewardType, minPurchaseAllowed, applicableDays, applicableCurrencies, returnedCoin, company, number, name, startDate, endDate, description, isEnabled);
            campaign.ApplyDomains(new List<int>() { domain1.Id, domain2.Id });
            campaign.ApplyStores(new List<int>() { store1.Id });
            return campaign;
        }

        [TestMethod]
        public void CreateSaleCampaign_ActionStreak_Test()
        {
            bool itIsThePresent = false;
            Company company = CompanySetupTest(itIsThePresent);

            Customer customer = company.GetOrCreateCustomerById("123456");
            Player player = company.Players.SearchPlayer(customer.Player.Id);

            ActionStreakCampaign_WheelTest(company);

            DateTime now = new DateTime(2024, 04, 15);
            int domainId = 1;
            int storeId = 1;
            IEnumerable<SaleCampaign> allCampaigns = company.Campaigns.SaleCampaigns(now, domainId, storeId, Currencies.CODES.USD);
            SaleCampaign saleCampaign = allCampaigns.First();

            Assert.IsTrue(saleCampaign.Campaign is ActionerStreak);
        }

        [TestMethod]
        public void CreateSaleCampaign_ActionStreak_Cashback_Tiered_Test()
        {
            bool itIsThePresent = false;
            Company company = CompanySetupTest(itIsThePresent);

            ActionStreakCampaign_Cashback_Tiered_Daily_Test(company);

            DateTime now = new DateTime(2024, 04, 15);
            int domainId = 1;
            int storeId = 1;
            IEnumerable<SaleCampaign> allCampaigns = company.Campaigns.SaleCampaigns(now, domainId, storeId, Currencies.CODES.USD);
            SaleCampaign saleCampaign = allCampaigns.First();

            Assert.IsTrue(saleCampaign.Campaign is ActionerStreak);
        }

        [TestMethod]
        public void CreateSaleCampaign_ActionStreak_Cashback_Flat_Test()
        {
            bool itIsThePresent = false;
            Company company = CompanySetupTest(itIsThePresent);

            ActionStreakCampaign_Cashback_Flat_Test(company);

            DateTime now = new DateTime(2024, 04, 15);
            int domainId = 1;
            int storeId = 1;
            IEnumerable<SaleCampaign> allCampaigns = company.Campaigns.SaleCampaigns(now, domainId, storeId, Currencies.CODES.USD);
            SaleCampaign saleCampaign = allCampaigns.First();

            Assert.IsTrue(saleCampaign.Campaign is ActionerStreak);
        }

        [TestMethod]
        public void CreateSaleCampaign_RewardBased_Tiered_Test()
        {
            bool itIsThePresent = false;
            Company company = CompanySetupTest(itIsThePresent);

            RewardBased_CashbackTieredTest(company);

            DateTime now = new DateTime(2024, 04, 15);
            int domainId = 1;
            int storeId = 1;
            IEnumerable<SaleCampaign> allCampaigns = company.Campaigns.SaleCampaigns(now, domainId, storeId, Currencies.CODES.USD);
            SaleCampaign saleCampaign = allCampaigns.First();

            Assert.IsTrue(saleCampaign.Campaign is RewardBased);
            Assert.IsTrue(saleCampaign.Reward is TieredCashbackRewarder);
        }

        [TestMethod]
        public void CreateSaleCampaign_RewardBased_Flat_Test()
        {
            bool itIsThePresent = false;
            Company company = CompanySetupTest(itIsThePresent);

            RewardBased_CashbackFlatTest(company);

            DateTime now = new DateTime(2024, 04, 15);
            int domainId = 1;
            int storeId = 1;
            IEnumerable<SaleCampaign> allCampaigns = company.Campaigns.SaleCampaigns(now, domainId, storeId, Currencies.CODES.USD);
            SaleCampaign saleCampaign = allCampaigns.First();

            Assert.IsTrue(saleCampaign.Campaign is RewardBased);
            Assert.IsTrue(saleCampaign.Reward is FlatCashbackRewarder);
        }

        [TestMethod]
        public void CreateSaleCampaign_RewardBased_Wheel_Test()
        {
            bool itIsThePresent = false;
            Company company = CompanySetupTest(itIsThePresent);

            RewardBased_WheelTest(company);

            DateTime now = new DateTime(2024, 04, 15);
            int domainId = 1;
            int storeId = 1;
            IEnumerable<SaleCampaign> allCampaigns = company.Campaigns.SaleCampaigns(now, domainId, storeId, Currencies.CODES.USD);
            SaleCampaign saleCampaign = allCampaigns.First();

            Assert.IsTrue(saleCampaign.Campaign is RewardBased);
            Assert.IsTrue(saleCampaign.Reward is SpinWheelRewarder);
        }

        [TestMethod]
        public void CreateSaleCampaign_RewardBased_None_Test()
        {
            bool itIsThePresent = false;
            Company company = CompanySetupTest(itIsThePresent);

            RewardBased rewardBased = RewardBased_NoneTest(company);

            DateTime now = new DateTime(2024, 04, 15);
            int domainId = 1;
            int storeId = 1;
            IEnumerable<SaleCampaign> allCampaigns = company.Campaigns.SaleCampaigns(now, domainId, storeId, Currencies.CODES.USD);
            Assert.IsTrue(allCampaigns.Count() == 1);
            SaleCampaign saleCampaign = company.Campaigns.SaleCampaign(rewardBased.PromotionNumber);

            Assert.IsTrue(saleCampaign.Campaign is RewardBased);
            Assert.IsTrue(saleCampaign.Reward is NoneRewarder);
        }

        [TestMethod]
        public void CreateSaleCampaign_ActionStreak_Purchase_Test()
        {
            bool itIsThePresent = false;
            Company company = CompanySetupTest(itIsThePresent);

            Customer customer = company.GetOrCreateCustomerById("123456");
            Player player = company.Players.SearchPlayer(customer.Player.Id);

            ActionStreakCampaign_WheelTest(company);

            DateTime now = new DateTime(2024, 04, 15);
            int domainId = 1;
            int storeId = 1;
            IEnumerable<SaleCampaign> allCampaigns = company.Campaigns.SaleCampaigns(now, domainId, storeId, Currencies.CODES.USD);
            SaleCampaign saleCampaign = allCampaigns.First();

            DateTime purchaseDate = new DateTime(2024, 04, 15);
            int agentId = 1;
            decimal purchaseAmount = 100.0M;
            Currencies.CODES currencyCode = Currencies.CODES.USD;
            saleCampaign.RegisterPurchase(itIsThePresent, player, purchaseDate, purchaseAmount, storeId, domainId, agentId, currencyCode);
        }

        [TestMethod]
        public void CreateSaleCampaign_RewardBased_Tiered_Purchase_Test()
        {
            bool itIsThePresent = false;
            Company company = CompanySetupTest(itIsThePresent);

            Customer customer = company.GetOrCreateCustomerById("123456");
            Player player = company.Players.SearchPlayer(customer.Player.Id);

            RewardBased_CashbackTieredTest(company);

            DateTime now = new DateTime(2024, 04, 15);
            int domainId = 1;
            int storeId = 1;
            IEnumerable<SaleCampaign> allCampaigns = company.Campaigns.SaleCampaigns(now, domainId, storeId, Currencies.CODES.USD);
            SaleCampaign saleCampaign = allCampaigns.First();

            DateTime purchaseDate = new DateTime(2024, 04, 15);
            int agentId = 1;
            decimal purchaseAmount = 100.0M;
            Currencies.CODES currencyCode = Currencies.CODES.USD;
            saleCampaign.RegisterPurchase(itIsThePresent, player, purchaseDate, purchaseAmount, storeId, domainId, agentId, currencyCode);
        }

        [TestMethod]
        public void CreateSaleCampaign_RewardBased_Flat_Purchase_Test()
        {
            bool itIsThePresent = false;
            Company company = CompanySetupTest(itIsThePresent);

            Customer customer = company.GetOrCreateCustomerById("123456");
            Player player = company.Players.SearchPlayer(customer.Player.Id);

            RewardBased_CashbackFlatTest(company);

            DateTime now = new DateTime(2024, 04, 15);
            int domainId = 1;
            int storeId = 1;
            IEnumerable<SaleCampaign> allCampaigns = company.Campaigns.SaleCampaigns(now, domainId, storeId, Currencies.CODES.USD);
            SaleCampaign saleCampaign = allCampaigns.First();

            DateTime purchaseDate = new DateTime(2024, 04, 15);
            int agentId = 1;
            decimal purchaseAmount = 100.0M;
            Currencies.CODES currencyCode = Currencies.CODES.USD;
            saleCampaign.RegisterPurchase(itIsThePresent, player, purchaseDate, purchaseAmount, storeId, domainId, agentId, currencyCode);
        }

        [TestMethod]
        public void CreateSaleCampaign_RewardBased_None_Purchase_Test()
        {
            bool itIsThePresent = false;
            Company company = CompanySetupTest(itIsThePresent);

            Customer customer = company.GetOrCreateCustomerById("123456");
            Player player = company.Players.SearchPlayer(customer.Player.Id);

            RewardBased rewardBased = RewardBased_NoneTest(company);

            DateTime now = new DateTime(2024, 04, 15);
            int domainId = 1;
            int storeId = 1;
            IEnumerable<SaleCampaign> allCampaigns = company.Campaigns.SaleCampaigns(now, domainId, storeId, Currencies.CODES.USD);
            Assert.IsTrue(allCampaigns.Count() == 1);
            SaleCampaign saleCampaign = company.Campaigns.SaleCampaign(rewardBased.PromotionNumber);

            DateTime purchaseDate = new DateTime(2024, 04, 15);
            int agentId = 1;
            decimal purchaseAmount = 100.0M;
            Currencies.CODES currencyCode = Currencies.CODES.USD;
            saleCampaign.RegisterPurchase(itIsThePresent, player, purchaseDate, purchaseAmount, storeId, domainId, agentId, currencyCode);
        }

        [TestMethod]
        public void CreateSaleCampaign_RewardBased_None_Purchase_Test_Sale()
        {
            bool itIsThePresent = false;
            Company company = CompanySetupTest(itIsThePresent);

            Customer customer = company.GetOrCreateCustomerById("123456");
            Player player = company.Players.SearchPlayer(customer.Player.Id);

            RewardBased rewardBased = RewardBased_NoneTest(company);

            DateTime purchaseDate = new DateTime(2024, 04, 01);
            decimal purchaseAmount = 100.0M;
            int domainId = 1;
            int storeId = 1;
            int agentId = 1;
            Currencies.CODES currencyCode = Currencies.CODES.USD;
            bool applicable = company.Campaigns.AreApplicableTo(purchaseDate, player, domainId, storeId, currencyCode);
            Assert.IsTrue(applicable);

            IEnumerable<CampaignBase> campaigns = company.Campaigns.ActiveSaleCampaign(purchaseDate, domainId, storeId, currencyCode);
            Assert.IsTrue(campaigns.Count() == 1);
            foreach(var campaign in campaigns)
            {
                campaign.RegisterPurchase(itIsThePresent, player, purchaseDate, purchaseAmount, storeId, domainId, agentId, currencyCode);
            }

            bool isParticipating = rewardBased.IsParticipating(player);
            Assert.IsTrue(isParticipating);
        }
    

        [TestMethod]
        public void CreateSaleCampaign_RewardBased_Wheel_Purchase_Test()
        {
            bool itIsThePresent = false;
            Company company = CompanySetupTest(itIsThePresent);

            Customer customer = company.GetOrCreateCustomerById("123456");
            Player player = company.Players.SearchPlayer(customer.Player.Id);

            RewardBased_WheelTest(company);

            DateTime now = new DateTime(2024, 04, 15);
            int domainId = 1;
            int storeId = 1;
            IEnumerable<SaleCampaign> allCampaigns = company.Campaigns.SaleCampaigns(now, domainId, storeId, Currencies.CODES.USD);
            SaleCampaign saleCampaign = allCampaigns.First();

            DateTime purchaseDate = new DateTime(2024, 04, 15);
            int agentId = 1;
            decimal purchaseAmount = 100.0M;
            Currencies.CODES currencyCode = Currencies.CODES.USD;
            saleCampaign.RegisterPurchase(itIsThePresent, player, purchaseDate, purchaseAmount, storeId, domainId, agentId, currencyCode);
        }

        [TestMethod]
        public void CreateSaleCampaign_RewardBased_Wheel_Purchase_Spins_Test()
        {
            bool itIsThePresent = false;
            Company company = CompanySetupTest(itIsThePresent);

            Customer customer = company.GetOrCreateCustomerById("123456");
            Player player = company.Players.SearchPlayer(customer.Player.Id);

            RewardBased_WheelTest(company);

            DateTime now = new DateTime(2024, 04, 15);
            int domainId = 1;
            int storeId = 1;
            IEnumerable<SaleCampaign> allCampaigns = company.Campaigns.SaleCampaigns(now, domainId, storeId, Currencies.CODES.USD);
            SaleCampaign saleCampaign = allCampaigns.First();
            CampaignBase campaign = saleCampaign.Campaign;
            SpinWheelRewarder spinWheelRewarder = saleCampaign.Reward as SpinWheelRewarder;

            DateTime purchaseDate = new DateTime(2024, 04, 15);
            int agentId = 1;
            decimal purchaseAmount = 10.0M;
            Currencies.CODES currencyCode = Currencies.CODES.USD;
            saleCampaign.RegisterPurchase(itIsThePresent, player, purchaseDate, purchaseAmount, storeId, domainId, agentId, currencyCode);
            IEnumerable<AvailableSpinByLevel> spins = spinWheelRewarder.AvailableSpins(player);
            Assert.IsTrue(spins.Count() == 1);
            Assert.IsTrue(spins.ElementAt(0).Level == 0);
            Assert.IsTrue(spins.ElementAt(0).Turns == 1);

            purchaseAmount = 50.0M;
            saleCampaign.RegisterPurchase(itIsThePresent, player, purchaseDate, purchaseAmount, storeId, domainId, agentId, currencyCode);
            spins = spinWheelRewarder.AvailableSpins(player);
            Assert.IsTrue(spins.Count() == 2);
            Assert.IsTrue(spins.ElementAt(0).Level == 1);
            Assert.IsTrue(spins.ElementAt(0).Turns == 1);
            Assert.IsTrue(spins.ElementAt(1).Level == 0);
            Assert.IsTrue(spins.ElementAt(1).Turns == 1);

            purchaseAmount = 100.0M;
            saleCampaign.RegisterPurchase(itIsThePresent, player, purchaseDate, purchaseAmount, storeId, domainId, agentId, currencyCode);
            spins = spinWheelRewarder.AvailableSpins(player);
            Assert.IsTrue(spins.Count() == 3);
            Assert.IsTrue(spins.ElementAt(0).Level == 2);
            Assert.IsTrue(spins.ElementAt(0).Turns == 1);
            Assert.IsTrue(spins.ElementAt(1).Level == 1);
            Assert.IsTrue(spins.ElementAt(1).Turns == 1);
            Assert.IsTrue(spins.ElementAt(2).Level == 0);
            Assert.IsTrue(spins.ElementAt(2).Turns == 1);
        }

        [TestMethod]
        public void CreateSaleCampaign_RewardBased_Wheel_Purchase_Particing_Test()
        {
            bool itIsThePresent = false;
            Company company = CompanySetupTest(itIsThePresent);

            Customer customer = company.GetOrCreateCustomerById("123456");
            Player player = company.Players.SearchPlayer(customer.Player.Id);

            RewardBased_WheelTest(company);

            DateTime now = new DateTime(2024, 04, 15);
            int domainId = 1;
            int storeId = 1;
            IEnumerable<SaleCampaign> allCampaigns = company.Campaigns.SaleCampaigns(now, domainId, storeId, Currencies.CODES.USD);
            SaleCampaign saleCampaign = allCampaigns.First();
            CampaignBase campaign = saleCampaign.Campaign;

            DateTime purchaseDate = new DateTime(2024, 04, 15);
            int agentId = 1;
            decimal purchaseAmount = 10.0M;
            Currencies.CODES currencyCode = Currencies.CODES.USD;
            saleCampaign.RegisterPurchase(itIsThePresent, player, purchaseDate, purchaseAmount, storeId, domainId, agentId, currencyCode);

            bool isParticing = campaign.IsParticipating(player);
            Assert.IsTrue(isParticing);
        }

        [TestMethod]
        public void CreateSaleCampaign_RewardBased_Wheel_Purchase_ActiveSaleCampaign_Particing_Test()
        {
            bool itIsThePresent = false;
            Company company = CompanySetupTest(itIsThePresent);

            Customer customer = company.GetOrCreateCustomerById("123456");
            Player player = company.Players.SearchPlayer(customer.Player.Id);

            RewardBased_WheelTest(company);

            DateTime now = new DateTime(2024, 04, 15);
            int domainId = 1;
            int storeId = 1;
            DateTime purchaseDate = new DateTime(2024, 04, 15);
            int agentId = 1;
            decimal purchaseAmount = 10.0M;
            Currencies.CODES currencyCode = Currencies.CODES.USD;

            foreach (var campaign in company.Campaigns.ActiveSaleCampaign(purchaseDate, domainId, storeId, currencyCode))
            {
                campaign.RegisterPurchase(itIsThePresent, player, purchaseDate, purchaseAmount, storeId, domainId, agentId, currencyCode);
            }

            foreach (var campaign in company.Campaigns.ActiveSaleCampaign(now, domainId, storeId, currencyCode))
            {
                bool isParticing = campaign.IsParticipating(player);
                Assert.IsTrue(isParticing);
            }
        }

        [TestMethod]
        public void CreateSaleCampaign_ActionStreak_Cashback_Tiered_Purchase_Test()
        {
            bool itIsThePresent = false;
            Company company = CompanySetupTest(itIsThePresent);

            Customer customer = company.GetOrCreateCustomerById("123456");
            Player player = company.Players.SearchPlayer(customer.Player.Id);

            ActionerStreak actionerStreak = ActionStreakCampaign_Cashback_Tiered_Daily_Test(company);
            JournalDayOfWeek applicableDays = JournalDayOfWeek.Monday | JournalDayOfWeek.Tuesday;
            actionerStreak.SetApplicableDays(applicableDays);
            actionerStreak.SetClaimDayOfTheWeek(JournalDayOfWeek.Monday);

            DateTime now = new DateTime(2024, 04, 15);
            int domainId = 1;
            int storeId = 1;
            int agentId = 1;
            decimal purchaseAmount = 10.0M;
            DateTime purchaseDate = new DateTime(2024, 04, 15);
            Currencies.CODES currencyCode = Currencies.CODES.USD;
            foreach (var campaign in company.Campaigns.ActiveSaleCampaign(purchaseDate, domainId, storeId, currencyCode))
            {
                campaign.RegisterPurchase(itIsThePresent, player, purchaseDate, purchaseAmount, storeId, domainId, agentId, currencyCode);
            }

            foreach (var campaign in company.Campaigns.ActiveSaleCampaign(now, domainId, storeId, currencyCode))
            {
                bool isParticing = campaign.IsParticipating(player);
                Assert.IsTrue(isParticing);
            }

            TieredCashbackRewarder rewarder = company.Campaigns.Rewarder(actionerStreak) as TieredCashbackRewarder;
            rewarder.MaxAccruedPlayer = 100.0M;
            bool canClaim = rewarder.CanPlayerClaim(now, player);
            Assert.IsFalse(canClaim);

            purchaseDate = new DateTime(2024, 04, 17);
            foreach (var campaign in company.Campaigns.ActiveSaleCampaign(purchaseDate, domainId, storeId, currencyCode))
            {
                campaign.RegisterPurchase(itIsThePresent, player, purchaseDate, purchaseAmount, storeId, domainId, agentId, currencyCode);
            }
            canClaim = rewarder.CanPlayerClaim(now, player);
            Assert.IsFalse(canClaim);

            purchaseDate = new DateTime(2024, 04, 18);
            foreach (var campaign in company.Campaigns.ActiveSaleCampaign(purchaseDate, domainId, storeId, currencyCode))
            {
                campaign.RegisterPurchase(itIsThePresent, player, purchaseDate, purchaseAmount, storeId, domainId, agentId, currencyCode);
            }
            canClaim = rewarder.CanPlayerClaim(now, player);
            Assert.IsFalse(canClaim);

            purchaseDate = new DateTime(2024, 04, 21);
            foreach (var campaign in company.Campaigns.ActiveSaleCampaign(purchaseDate, domainId, storeId, currencyCode))
            {
                campaign.RegisterPurchase(itIsThePresent, player, purchaseDate, purchaseAmount, storeId, domainId, agentId, currencyCode);
            }
            canClaim = rewarder.CanPlayerClaim(now, player);
            Assert.IsFalse(canClaim);

            purchaseDate = new DateTime(2024, 04, 22);
            foreach (var campaign in company.Campaigns.ActiveSaleCampaign(purchaseDate, domainId, storeId, currencyCode))
            {
                campaign.RegisterPurchase(itIsThePresent, player, purchaseDate, purchaseAmount, storeId, domainId, agentId, currencyCode);
            }
            canClaim = rewarder.CanPlayerClaim(now, player);
            Assert.IsFalse(canClaim);

            purchaseDate = new DateTime(2024, 04, 23);
            foreach (var campaign in company.Campaigns.ActiveSaleCampaign(purchaseDate, domainId, storeId, currencyCode))
            {
                campaign.RegisterPurchase(itIsThePresent, player, purchaseDate, purchaseAmount, storeId, domainId, agentId, currencyCode);
            }
            canClaim = rewarder.CanPlayerClaim(now, player);
            Assert.IsTrue(canClaim);

            purchaseDate = new DateTime(2024, 04, 24);
            foreach (var campaign in company.Campaigns.ActiveSaleCampaign(purchaseDate, domainId, storeId, currencyCode))
            {
                campaign.RegisterPurchase(itIsThePresent, player, purchaseDate, purchaseAmount, storeId, domainId, agentId, currencyCode);
            }
            canClaim = rewarder.CanPlayerClaim(now, player);
            Assert.IsTrue(canClaim);

            purchaseDate = new DateTime(2024, 04, 23);
            decimal claimedCashback = rewarder.ClaimCashback(itIsThePresent, storeId, purchaseDate, player, "N/A", domainId);
            Assert.IsTrue(claimedCashback == 4.0M);
        }

        [TestMethod]
        public void CreateSaleCampaign_ActionStreak_Cashback_Flat_Purchase_Test()
        {
            bool itIsThePresent = false;
            Company company = CompanySetupTest(itIsThePresent);

            Customer customer = company.GetOrCreateCustomerById("123456");
            Player player = company.Players.SearchPlayer(customer.Player.Id);

            ActionerStreak actionerStreak = ActionStreakCampaign_Cashback_Flat_Test(company);
            actionerStreak.SetClaimDayOfTheWeek(JournalDayOfWeek.Monday);

            DateTime now = new DateTime(2024, 04, 15);
            int domainId = 1;
            int storeId = 1;
            DateTime purchaseDate = new DateTime(2024, 04, 15);
            int agentId = 1;
            decimal purchaseAmount = 10.0M;
            Currencies.CODES currencyCode = Currencies.CODES.USD;
            foreach (var campaign in company.Campaigns.ActiveSaleCampaign(purchaseDate, domainId, storeId, currencyCode))
            {
                campaign.RegisterPurchase(itIsThePresent, player, purchaseDate, purchaseAmount, storeId, domainId, agentId, currencyCode);
            }

            foreach (var campaign in company.Campaigns.ActiveSaleCampaign(now, domainId, storeId, currencyCode))
            {
                bool isParticing = campaign.IsParticipating(player);
                Assert.IsTrue(isParticing);
            }

            FlatCashbackRewarder rewarder = company.Campaigns.Rewarder(actionerStreak) as FlatCashbackRewarder;
            rewarder.MaxAccruedPlayer = 100.0M;
            bool canClaim = rewarder.CanPlayerClaim(now, player);
            Assert.IsFalse(canClaim);

            purchaseDate = new DateTime(2024, 04, 16);
            foreach (var campaign in company.Campaigns.ActiveSaleCampaign(purchaseDate, domainId, storeId, currencyCode))
            {
                campaign.RegisterPurchase(itIsThePresent, player, purchaseDate, purchaseAmount, storeId, domainId, agentId, currencyCode);
            }
            canClaim = rewarder.CanPlayerClaim(now, player);
            Assert.IsFalse(canClaim);

            purchaseDate = new DateTime(2024, 04, 17);
            foreach (var campaign in company.Campaigns.ActiveSaleCampaign(purchaseDate, domainId, storeId, currencyCode))
            {
                campaign.RegisterPurchase(itIsThePresent, player, purchaseDate, purchaseAmount, storeId, domainId, agentId, currencyCode);
            }
            canClaim = rewarder.CanPlayerClaim(now, player);
            Assert.IsFalse(canClaim);

            purchaseDate = new DateTime(2024, 04, 18);
            foreach (var campaign in company.Campaigns.ActiveSaleCampaign(purchaseDate, domainId, storeId, currencyCode))
            {
                campaign.RegisterPurchase(itIsThePresent, player, purchaseDate, purchaseAmount, storeId, domainId, agentId, currencyCode);
            }
            canClaim = rewarder.CanPlayerClaim(now, player);
            Assert.IsFalse(canClaim);

            purchaseDate = new DateTime(2024, 04, 19);
            foreach (var campaign in company.Campaigns.ActiveSaleCampaign(purchaseDate, domainId, storeId, currencyCode))
            {
                campaign.RegisterPurchase(itIsThePresent, player, purchaseDate, purchaseAmount, storeId, domainId, agentId, currencyCode);
            }
            canClaim = rewarder.CanPlayerClaim(now, player);
            Assert.IsTrue(canClaim);

            purchaseDate = new DateTime(2024, 04, 20);
            foreach (var campaign in company.Campaigns.ActiveSaleCampaign(purchaseDate, domainId, storeId, currencyCode))
            {
                campaign.RegisterPurchase(itIsThePresent, player, purchaseDate, purchaseAmount, storeId, domainId, agentId, currencyCode);
            }
            canClaim = rewarder.CanPlayerClaim(now, player);
            Assert.IsTrue(canClaim);

            purchaseDate = new DateTime(2024, 04, 21);
            decimal claimedCashback = rewarder.ClaimCashback(itIsThePresent, storeId, purchaseDate, player, "N/A", domainId);
            Assert.IsTrue(claimedCashback == 6.0M);
        }

        [TestMethod]
        public void CreateSaleCampaign_ActionStreak_Cashback_Tiered_Purchase_Amount_Test()
        {
            bool itIsThePresent = false;
            Company company = CompanySetupTest(itIsThePresent);

            Customer customer = company.GetOrCreateCustomerById("123456");
            Player player = company.Players.SearchPlayer(customer.Player.Id);

            ActionerStreak actionerStreak = ActionStreakCampaign_Cashback_Tiered_Amount_Test(company);
            actionerStreak.SetClaimDayOfTheWeek(JournalDayOfWeek.Monday);

            DateTime now = new DateTime(2024, 04, 15);
            int domainId = 1;
            int storeId = 1;
            DateTime purchaseDate = new DateTime(2024, 04, 14);
            int agentId = 1;
            decimal purchaseAmount = 10.0M;
            Currencies.CODES currencyCode = Currencies.CODES.USD;
            foreach (var campaign in company.Campaigns.ActiveSaleCampaign(purchaseDate, domainId, storeId, currencyCode))
            {
                campaign.RegisterPurchase(itIsThePresent, player, purchaseDate, purchaseAmount, storeId, domainId, agentId, currencyCode);
            }

            foreach (var campaign in company.Campaigns.ActiveSaleCampaign(now, domainId, storeId, currencyCode))
            {
                bool isParticing = campaign.IsParticipating(player);
                Assert.IsFalse(isParticing);
            }

            TieredCashbackRewarder rewarder = company.Campaigns.Rewarder(actionerStreak) as TieredCashbackRewarder;
            rewarder.MaxAccruedPlayer = 30;
            bool canClaim = rewarder.CanPlayerClaim(now, player);
            Assert.IsFalse(canClaim);
            decimal currentStreakPlayerAmount = actionerStreak.CurrentStreakPlayerAmount(player);
            Assert.IsTrue(currentStreakPlayerAmount == 0);

            purchaseDate = new DateTime(2024, 04, 15);
            foreach (var campaign in company.Campaigns.ActiveSaleCampaign(purchaseDate, domainId, storeId, currencyCode))
            {
                campaign.RegisterPurchase(itIsThePresent, player, purchaseDate, purchaseAmount, storeId, domainId, agentId, currencyCode);
            }
            canClaim = rewarder.CanPlayerClaim(now, player);
            Assert.IsFalse(canClaim);
            currentStreakPlayerAmount = actionerStreak.CurrentStreakPlayerAmount(player);
            Assert.IsTrue(currentStreakPlayerAmount == 10);

            purchaseDate = new DateTime(2024, 04, 16);
            foreach (var campaign in company.Campaigns.ActiveSaleCampaign(purchaseDate, domainId, storeId, currencyCode))
            {
                campaign.RegisterPurchase(itIsThePresent, player, purchaseDate, purchaseAmount, storeId, domainId, agentId, currencyCode);
            }
            canClaim = rewarder.CanPlayerClaim(now, player);
            Assert.IsFalse(canClaim);
            currentStreakPlayerAmount = actionerStreak.CurrentStreakPlayerAmount(player);
            Assert.IsTrue(currentStreakPlayerAmount == 20);

            purchaseDate = new DateTime(2024, 04, 17);
            foreach (var campaign in company.Campaigns.ActiveSaleCampaign(purchaseDate, domainId, storeId, currencyCode))
            {
                campaign.RegisterPurchase(itIsThePresent, player, purchaseDate, purchaseAmount, storeId, domainId, agentId, currencyCode);
            }
            canClaim = rewarder.CanPlayerClaim(now, player);
            Assert.IsTrue(canClaim);
            currentStreakPlayerAmount = actionerStreak.CurrentStreakPlayerAmount(player);
            Assert.IsTrue(currentStreakPlayerAmount == 30);
        }

        [TestMethod]
        public void CreateSaleCampaign_ActionStreak_And_RewardBased_Test()
        {
            bool itIsThePresent = false;
            Company company = CompanySetupTest(itIsThePresent);

            Customer customer = company.GetOrCreateCustomerById("123456");
            Player player = company.Players.SearchPlayer(customer.Player.Id);

            ActionerStreak actionerStreakCashback = ActionStreakCampaign_Cashback_Tiered_Daily_Test(company);
            JournalDayOfWeek allowedDaysOfWeekValue = actionerStreakCashback.AllowedDaysOfWeekValue;
            allowedDaysOfWeekValue |= JournalDayOfWeek.Sunday;
            actionerStreakCashback.SetApplicableDays(allowedDaysOfWeekValue);
            actionerStreakCashback.MinPurchaseAllowed = 10M;

            ActionerStreak actionerStreakWheel = ActionStreakCampaign_WheelTest(company);
            actionerStreakWheel.MinPurchaseAllowed = 10M;

            int domainId = 1;
            int storeId = 1;
            DateTime purchaseDate = new DateTime(2024, 04, 14);
            int agentId = 1;
            decimal purchaseAmount = 1.0M;
            Currencies.CODES currencyCode = Currencies.CODES.USD;

            IEnumerable<CampaignBase> campaigns = company.Campaigns.ActiveSaleCampaign(purchaseDate, domainId, storeId, currencyCode);
            Assert.IsTrue(campaigns.Count() == 2);
            foreach (var campaign in campaigns)
            {
                campaign.RegisterPurchase(itIsThePresent, player, purchaseDate, purchaseAmount, storeId, domainId, agentId, currencyCode);
            }
            Assert.IsFalse(actionerStreakCashback.IsParticipating(player));
            Assert.IsFalse(actionerStreakWheel.IsParticipating(player));

            purchaseAmount = 10.0M;
            campaigns = company.Campaigns.ActiveSaleCampaign(purchaseDate, domainId, storeId, currencyCode);
            Assert.IsTrue(campaigns.Count() == 2);
            foreach (var campaign in campaigns)
            {
                campaign.RegisterPurchase(itIsThePresent, player, purchaseDate, purchaseAmount, storeId, domainId, agentId, currencyCode);
            }
            Assert.IsTrue(actionerStreakCashback.IsParticipating(player));
            Assert.IsTrue(actionerStreakWheel.IsParticipating(player));
        }

        [TestMethod]
        public void CreateSaleCampaign_TimeZone_Test()
        {
            TimeZoneInfo easternZone = TimeZoneInfo.FindSystemTimeZoneById("Eastern Standard Time");
            TimeZoneInfo pacificZone = TimeZoneInfo.FindSystemTimeZoneById("Pacific Standard Time");

            DateTime testDateLocal = new DateTime(2024, 10, 06, 21, 00, 00, DateTimeKind.Local);
            DateTime escTime = TimeZoneInfo.ConvertTime(testDateLocal, easternZone);
            Assert.IsTrue(escTime == new DateTime(2024, 10, 06, 23, 00, 00, DateTimeKind.Local));
            DateTime pstTime = TimeZoneInfo.ConvertTime(testDateLocal, pacificZone);
            Assert.IsTrue(pstTime == new DateTime(2024, 10, 06, 20, 00, 00, DateTimeKind.Local));

            DateTime testDateUtc = new DateTime(2024, 10, 06, 21, 00, 00, DateTimeKind.Utc);
            DateTime escTimeUtc = TimeZoneInfo.ConvertTime(testDateUtc, easternZone);
            Assert.IsTrue(escTimeUtc == new DateTime(2024, 10, 06, 17, 00, 00, DateTimeKind.Utc));
            DateTime pstTimeUtc = TimeZoneInfo.ConvertTime(testDateUtc, pacificZone);
            Assert.IsTrue(pstTimeUtc == new DateTime(2024, 10, 06, 14, 00, 00, DateTimeKind.Utc));

            DateTime testDateUnspecified = new DateTime(2024, 10, 06, 21, 00, 00, DateTimeKind.Unspecified);
            DateTime escTimeUnspecified = TimeZoneInfo.ConvertTime(testDateUnspecified, easternZone);
            Assert.IsTrue(escTimeUnspecified == new DateTime(2024, 10, 06, 23, 00, 00, DateTimeKind.Unspecified));
            DateTime pstTimeUnspecified = TimeZoneInfo.ConvertTime(testDateUnspecified, pacificZone);
            Assert.IsTrue(pstTimeUnspecified == new DateTime(2024, 10, 06, 20, 00, 00, DateTimeKind.Unspecified));
        }

        [TestMethod]
        public void CreateSaleCampaign_OneTimeZone_To_OtherTimeZone()
        {
            TimeZoneInfo easternZone = TimeZoneInfo.FindSystemTimeZoneById("Eastern Standard Time");
            TimeZoneInfo pacificZone = TimeZoneInfo.FindSystemTimeZoneById("Pacific Standard Time");

            DateTime testDateLocal = new DateTime(2024, 10, 06, 21, 00, 00, DateTimeKind.Local);

            DateTime escTime = TimeZoneInfo.ConvertTime(testDateLocal, easternZone);
            Assert.IsTrue(escTime == new DateTime(2024, 10, 06, 23, 00, 00, DateTimeKind.Local));

            DateTime pstTime = TimeZoneInfo.ConvertTime(testDateLocal, pacificZone);
            Assert.IsTrue(pstTime == new DateTime(2024, 10, 06, 20, 00, 00, DateTimeKind.Local));

            // convert the escTime Eastern Standard Time to Pacific Standard Time
            DateTime escTimePst = TimeZoneInfo.ConvertTime(escTime, easternZone, pacificZone);
            Assert.IsTrue(escTimePst == new DateTime(2024, 10, 06, 20, 00, 00, DateTimeKind.Local));
        }

        [TestMethod]
        public void CreateSaleCampaign_SameTimeZone()
        {
            CountryTimeZone tzEST = new CountryTimeZone { CountryCode = "ZZ", Windows = "Eastern Standard Time", Linux = "EST5EDT" };
            TimeZoneConverter.Instance.Configure(new List<CountryTimeZone> { tzEST });

            bool itIsThePresent = false;

            Company company = CompanySetupTest(itIsThePresent);
            company.TimeZone = tzEST.CountryCode;
            var domain = company.Sales.DomainFrom(1);

            var store = company.Sales.StoreById(1);

            const byte maxLevel = 11;

            DateTime startDate = new DateTime(2024, 04, 02, 00, 00, 00);
            DateTime endDate = new DateTime(2024, 04, 04, 00, 00, 00);
            DateTime lastDatePrizes = new DateTime(2024, 04, 05, 00, 00, 00);

            ActionerStreak streakCampaign = company.Campaigns.CreateStreakCampaign(itIsThePresent, 1, DateTime.Now, "Streak", Currencies.CODES.FP, startDate, endDate, maxLevel, "test", RewarderType.SpinWheel, CampaignScaleType.DailyCounter, true, Currencies.CODES.USD, tzEST.CountryCode);
            streakCampaign.LastDayToChangePrizes = lastDatePrizes;
            SpinWheelRewarder rewarder = streakCampaign.Rewarder as SpinWheelRewarder;
            rewarder.ModifySpinWheel(new string[] { "0.25", "0.25", "0.5", "0.5", "1", "2", "3", "5" }, new decimal[] { 0.5M, 0.50M, 0.75M, 1, 1.25M, 1.50M, 1.75M, 2 }, "Levl0", 0, 1);
            rewarder.ModifySpinWheel(new string[] { "0.25", "0.25", "0.5", "0.5", "1", "2", "3", "5" }, new decimal[] { 0.5M, 0.50M, 0.75M, 1, 1.25M, 1.50M, 1.75M, 2 }, "Levl1", 1, 5);
            rewarder.ModifySpinWheel(new string[] { "0.25", "0.25", "0.5", "0.5", "1", "2", "3", "5" }, new decimal[] { 0.5M, 0.50M, 0.75M, 1, 1.25M, 1.50M, 1.75M, 2 }, "Levl2", 2, 5);
            rewarder.ModifySpinWheel(new string[] { "0.25", "0.25", "0.5", "0.5", "1", "2", "3", "5" }, new decimal[] { 0.5M, 0.50M, 0.75M, 1, 1.25M, 1.50M, 1.75M, 2 }, "Levl3", 3, 5);
            rewarder.ModifySpinWheel(new string[] { "0.25", "0.25", "0.5", "0.5", "1", "2", "3", "5" }, new decimal[] { 0.5M, 0.50M, 0.75M, 1, 1.25M, 1.50M, 1.75M, 2 }, "Levl4", 4, 5);
            rewarder.ModifySpinWheel(new string[] { "0.25", "0.25", "0.5", "0.5", "1", "2", "3", "5" }, new decimal[] { 0.5M, 0.50M, 0.75M, 1, 1.25M, 1.50M, 1.75M, 2 }, "Levl5", 5, 5);
            rewarder.ModifySpinWheel(new string[] { "0.25", "0.25", "0.5", "0.5", "1", "2", "3", "5" }, new decimal[] { 0.5M, 0.50M, 0.75M, 1, 1.25M, 1.50M, 1.75M, 2 }, "Levl6", 6, 5);
            rewarder.ModifySpinWheel(new string[] { "0.25", "0.25", "0.5", "0.5", "1", "2", "3", "5" }, new decimal[] { 0.5M, 0.50M, 0.75M, 1, 1.25M, 1.50M, 1.75M, 2 }, "Levl7", 7, 5);
            rewarder.ModifySpinWheel(new string[] { "0.25", "0.25", "0.5", "0.5", "1", "2", "3", "5" }, new decimal[] { 0.5M, 0.50M, 0.75M, 1, 1.25M, 1.50M, 1.75M, 2 }, "Levl8", 8, 5);
            rewarder.ModifySpinWheel(new string[] { "0.25", "0.25", "0.5", "0.5", "1", "2", "3", "5" }, new decimal[] { 0.5M, 0.50M, 0.75M, 1, 1.25M, 1.50M, 1.75M, 2 }, "Levl9", 9, 5);
            rewarder.ModifySpinWheel(new string[] { "0.25", "0.25", "0.5", "0.5", "1", "2", "3", "5" }, new decimal[] { 0.5M, 0.50M, 0.75M, 1, 1.25M, 1.50M, 1.75M, 2 }, "Levl10", 10, 5);

            Player player = company.GetOrCreateCustomerById("1234567").Player;

            streakCampaign.ApplyDomains(new List<int> { domain.Id });
            streakCampaign.ApplyStores(new List<int>() { store.Id });

            DateTime purchaseDate = new DateTime(2024, 04, 01, 00, 00, 00);
            int agentId = 1;
            decimal purchaseAmount = 1.0M;

            IEnumerable<CampaignBase> campaigns = company.Campaigns.ActiveSaleCampaign(purchaseDate, domain.Id, store.Id, Currencies.CODES.USD);
            Assert.IsTrue(campaigns.Count() == 0);

            purchaseDate = new DateTime(2024, 04, 02, 00, 00, 00);
            campaigns = company.Campaigns.ActiveSaleCampaign(purchaseDate, domain.Id, store.Id, Currencies.CODES.USD);
            Assert.IsTrue(campaigns.Count() == 1);

            purchaseAmount = 10.0M;
            campaigns = company.Campaigns.ActiveSaleCampaign(purchaseDate, domain.Id, store.Id, Currencies.CODES.USD);
            Assert.IsTrue(campaigns.Count() == 1);
            campaigns.First().RegisterPurchase(itIsThePresent, player, purchaseDate, purchaseAmount, store.Id, domain.Id, agentId, Currencies.CODES.USD);
            Assert.IsTrue(campaigns.First().IsParticipating(player));

            Assert.IsTrue(campaigns.First().TimeZone == tzEST.CountryCode);

            Assert.IsTrue(campaigns.First().StartDate == new DateTime(2024, 04, 02, 00, 00, 00));
            Assert.IsTrue(campaigns.First().EndDate == new DateTime(2024, 04, 04, 00, 00, 00));

            Assert.IsTrue(campaigns.First().ServerStartDate == new DateTime(2024, 04, 02, 00, 00, 00));
            Assert.IsTrue(campaigns.First().ServerEndDate == new DateTime(2024, 04, 04, 00, 00, 00));
        }

        [TestMethod]
        public void CreateSaleCampaign_DiffTimeZone()
        {
            CountryTimeZone tzEST = new CountryTimeZone { CountryCode = "ZZ", Windows = "Eastern Standard Time", Linux = "EST5EDT" };
            CountryTimeZone tzKR = new CountryTimeZone { CountryCode = "KR", Windows = "Korea Standard Time", Linux = "Asia/Seoul" };
            TimeZoneConverter.Instance.Configure(new List<CountryTimeZone> { tzEST, tzKR });

            bool itIsThePresent = false;

            Company company = CompanySetupTest(itIsThePresent);
            company.TimeZone = tzEST.CountryCode;
            var domain = company.Sales.DomainFrom(1);

            var store = company.Sales.StoreById(1);

            const byte maxLevel = 11;

            DateTime startDate = new DateTime(2024, 04, 02, 00, 00, 00);
            DateTime startDateInKorea = TimeZoneConverter.Instance.ConvertTime(startDate, tzEST.CountryCode, tzKR.CountryCode);
            DateTime endDate = new DateTime(2024, 04, 04, 00, 00, 00);
            DateTime endDateInKorea = TimeZoneConverter.Instance.ConvertTime(endDate, tzEST.CountryCode, tzKR.CountryCode);
            DateTime lastDatePrizes = new DateTime(2024, 04, 05, 00, 00, 00);
            DateTime lastDatePrizesInKorea = TimeZoneConverter.Instance.ConvertTime(lastDatePrizes, tzEST.CountryCode, tzKR.CountryCode);

            ActionerStreak streakCampaign = company.Campaigns.CreateStreakCampaign(itIsThePresent, 1, DateTime.Now, "Streak", Currencies.CODES.FP, startDateInKorea, endDateInKorea, maxLevel, "test", RewarderType.SpinWheel, CampaignScaleType.DailyCounter, true, Currencies.CODES.USD, tzKR.CountryCode);
            streakCampaign.LastDayToChangePrizes = lastDatePrizesInKorea;
            SpinWheelRewarder rewarder = streakCampaign.Rewarder as SpinWheelRewarder;
            rewarder.ModifySpinWheel(new string[] { "0.25", "0.25", "0.5", "0.5", "1", "2", "3", "5" }, new decimal[] { 0.5M, 0.50M, 0.75M, 1, 1.25M, 1.50M, 1.75M, 2 }, "Levl0", 0, 1);
            rewarder.ModifySpinWheel(new string[] { "0.25", "0.25", "0.5", "0.5", "1", "2", "3", "5" }, new decimal[] { 0.5M, 0.50M, 0.75M, 1, 1.25M, 1.50M, 1.75M, 2 }, "Levl1", 1, 5);
            rewarder.ModifySpinWheel(new string[] { "0.25", "0.25", "0.5", "0.5", "1", "2", "3", "5" }, new decimal[] { 0.5M, 0.50M, 0.75M, 1, 1.25M, 1.50M, 1.75M, 2 }, "Levl2", 2, 5);
            rewarder.ModifySpinWheel(new string[] { "0.25", "0.25", "0.5", "0.5", "1", "2", "3", "5" }, new decimal[] { 0.5M, 0.50M, 0.75M, 1, 1.25M, 1.50M, 1.75M, 2 }, "Levl3", 3, 5);
            rewarder.ModifySpinWheel(new string[] { "0.25", "0.25", "0.5", "0.5", "1", "2", "3", "5" }, new decimal[] { 0.5M, 0.50M, 0.75M, 1, 1.25M, 1.50M, 1.75M, 2 }, "Levl4", 4, 5);
            rewarder.ModifySpinWheel(new string[] { "0.25", "0.25", "0.5", "0.5", "1", "2", "3", "5" }, new decimal[] { 0.5M, 0.50M, 0.75M, 1, 1.25M, 1.50M, 1.75M, 2 }, "Levl5", 5, 5);
            rewarder.ModifySpinWheel(new string[] { "0.25", "0.25", "0.5", "0.5", "1", "2", "3", "5" }, new decimal[] { 0.5M, 0.50M, 0.75M, 1, 1.25M, 1.50M, 1.75M, 2 }, "Levl6", 6, 5);
            rewarder.ModifySpinWheel(new string[] { "0.25", "0.25", "0.5", "0.5", "1", "2", "3", "5" }, new decimal[] { 0.5M, 0.50M, 0.75M, 1, 1.25M, 1.50M, 1.75M, 2 }, "Levl7", 7, 5);
            rewarder.ModifySpinWheel(new string[] { "0.25", "0.25", "0.5", "0.5", "1", "2", "3", "5" }, new decimal[] { 0.5M, 0.50M, 0.75M, 1, 1.25M, 1.50M, 1.75M, 2 }, "Levl8", 8, 5);
            rewarder.ModifySpinWheel(new string[] { "0.25", "0.25", "0.5", "0.5", "1", "2", "3", "5" }, new decimal[] { 0.5M, 0.50M, 0.75M, 1, 1.25M, 1.50M, 1.75M, 2 }, "Levl9", 9, 5);
            rewarder.ModifySpinWheel(new string[] { "0.25", "0.25", "0.5", "0.5", "1", "2", "3", "5" }, new decimal[] { 0.5M, 0.50M, 0.75M, 1, 1.25M, 1.50M, 1.75M, 2 }, "Levl10", 10, 5);

            Player player = company.GetOrCreateCustomerById("1234567").Player;

            streakCampaign.ApplyDomains(new List<int> { domain.Id });
            streakCampaign.ApplyStores(new List<int>() { store.Id });

            DateTime purchaseDate = new DateTime(2024, 04, 01, 00, 00, 00);
            int agentId = 1;
            decimal purchaseAmount = 1.0M;

            IEnumerable<CampaignBase> campaigns = company.Campaigns.ActiveSaleCampaign(purchaseDate, domain.Id, store.Id, Currencies.CODES.USD);
            Assert.IsTrue(campaigns.Count() == 0);

            purchaseDate = new DateTime(2024, 04, 02, 00, 00, 00);
            campaigns = company.Campaigns.ActiveSaleCampaign(purchaseDate, domain.Id, store.Id, Currencies.CODES.USD);
            Assert.IsTrue(campaigns.Count() == 1);

            purchaseAmount = 10.0M;
            campaigns = company.Campaigns.ActiveSaleCampaign(purchaseDate, domain.Id, store.Id, Currencies.CODES.USD);
            Assert.IsTrue(campaigns.Count() == 1);
            campaigns.First().RegisterPurchase(itIsThePresent, player, purchaseDate, purchaseAmount, store.Id, domain.Id, agentId, Currencies.CODES.USD);
            Assert.IsTrue(campaigns.First().IsParticipating(player));

            Assert.IsTrue(campaigns.First().TimeZone == tzKR.CountryCode);

            Assert.IsTrue(campaigns.First().StartDate == startDateInKorea);
            Assert.IsTrue(campaigns.First().EndDate == endDateInKorea);

            Assert.IsTrue(campaigns.First().ServerStartDate == startDate);
            Assert.IsTrue(campaigns.First().ServerEndDate == endDate);
        }

        [TestMethod]
        public void ThreeCampaigns_AreApplicableTo_DifferentTimeZone()
        {
            CountryTimeZone tzCR = new CountryTimeZone { CountryCode = "CR", Windows = "Central Standard Time", Linux = "America/Costa_Rica" };
            CountryTimeZone tzKR = new CountryTimeZone { CountryCode = "KR", Windows = "Korea Standard Time", Linux = "Asia/Seoul" };

            TimeZoneConverter.Instance.Configure(new List<CountryTimeZone> { tzCR, tzKR });

            bool itIsThePresent = false;
            Company company = CompanySetupTest(itIsThePresent);
            company.TimeZone = tzCR.CountryCode;

            var domain = company.Sales.DomainFrom(1);
            var store = company.Sales.StoreById(1);

            const byte maxLevel = 11;
            DateTime todayCentral = new DateTime(2024, 07, 01, 00, 00, 00, DateTimeKind.Unspecified);
            DateTime todayKorea = TimeZoneConverter.Instance.ConvertTime(todayCentral, tzCR.CountryCode, tzKR.CountryCode);

            ActionerStreak crStreakCampaign = company.Campaigns.CreateStreakCampaign(itIsThePresent, 1, todayCentral, "Streak CR", Currencies.CODES.FP, todayCentral, todayCentral.AddDays(15), maxLevel, "test", RewarderType.SpinWheel, CampaignScaleType.DailyCounter, true, Currencies.CODES.USD, tzCR.CountryCode);
            crStreakCampaign.LastDayToChangePrizes = todayCentral.AddDays(30);
            SpinWheelRewarder crRewarder = crStreakCampaign.Rewarder as SpinWheelRewarder;
            crRewarder.ModifySpinWheel(new string[] { "0.25", "0.25", "0.5", "0.5", "1", "2", "3", "5" }, new decimal[] { 0.5M, 0.50M, 0.75M, 1, 1.25M, 1.50M, 1.75M, 2 }, "Levl0", 0, 1);
            crRewarder.ModifySpinWheel(new string[] { "0.25", "0.25", "0.5", "0.5", "1", "2", "3", "5" }, new decimal[] { 0.5M, 0.50M, 0.75M, 1, 1.25M, 1.50M, 1.75M, 2 }, "Levl1", 1, 5);
            crRewarder.ModifySpinWheel(new string[] { "0.25", "0.25", "0.5", "0.5", "1", "2", "3", "5" }, new decimal[] { 0.5M, 0.50M, 0.75M, 1, 1.25M, 1.50M, 1.75M, 2 }, "Levl2", 2, 5);
            crRewarder.ModifySpinWheel(new string[] { "0.25", "0.25", "0.5", "0.5", "1", "2", "3", "5" }, new decimal[] { 0.5M, 0.50M, 0.75M, 1, 1.25M, 1.50M, 1.75M, 2 }, "Levl3", 3, 5);
            crRewarder.ModifySpinWheel(new string[] { "0.25", "0.25", "0.5", "0.5", "1", "2", "3", "5" }, new decimal[] { 0.5M, 0.50M, 0.75M, 1, 1.25M, 1.50M, 1.75M, 2 }, "Levl4", 4, 5);
            crRewarder.ModifySpinWheel(new string[] { "0.25", "0.25", "0.5", "0.5", "1", "2", "3", "5" }, new decimal[] { 0.5M, 0.50M, 0.75M, 1, 1.25M, 1.50M, 1.75M, 2 }, "Levl5", 5, 5);
            crRewarder.ModifySpinWheel(new string[] { "0.25", "0.25", "0.5", "0.5", "1", "2", "3", "5" }, new decimal[] { 0.5M, 0.50M, 0.75M, 1, 1.25M, 1.50M, 1.75M, 2 }, "Levl6", 6, 5);
            crRewarder.ModifySpinWheel(new string[] { "0.25", "0.25", "0.5", "0.5", "1", "2", "3", "5" }, new decimal[] { 0.5M, 0.50M, 0.75M, 1, 1.25M, 1.50M, 1.75M, 2 }, "Levl7", 7, 5);
            crRewarder.ModifySpinWheel(new string[] { "0.25", "0.25", "0.5", "0.5", "1", "2", "3", "5" }, new decimal[] { 0.5M, 0.50M, 0.75M, 1, 1.25M, 1.50M, 1.75M, 2 }, "Levl8", 8, 5);
            crRewarder.ModifySpinWheel(new string[] { "0.25", "0.25", "0.5", "0.5", "1", "2", "3", "5" }, new decimal[] { 0.5M, 0.50M, 0.75M, 1, 1.25M, 1.50M, 1.75M, 2 }, "Levl9", 9, 5);
            crRewarder.ModifySpinWheel(new string[] { "0.25", "0.25", "0.5", "0.5", "1", "2", "3", "5" }, new decimal[] { 0.5M, 0.50M, 0.75M, 1, 1.25M, 1.50M, 1.75M, 2 }, "Levl10", 10, 5);
            crStreakCampaign.ApplyDomains(new List<int> { domain.Id });
            crStreakCampaign.ApplyStores(new List<int> { store.Id });
            Assert.IsTrue(crStreakCampaign.Publishable);

            ActionerStreak korStreakCampaign = company.Campaigns.CreateStreakCampaign(itIsThePresent, 2, todayKorea, "Streak KOR", Currencies.CODES.FP, todayKorea, todayKorea.AddDays(15), maxLevel, "test", RewarderType.SpinWheel, CampaignScaleType.DailyCounter, true, Currencies.CODES.USD, tzKR.CountryCode);
            korStreakCampaign.LastDayToChangePrizes = todayKorea.AddDays(30);
            SpinWheelRewarder korRewarder = korStreakCampaign.Rewarder as SpinWheelRewarder;
            korRewarder.ModifySpinWheel(new string[] { "0.25", "0.25", "0.5", "0.5", "1", "2", "3", "5" }, new decimal[] { 0.5M, 0.50M, 0.75M, 1, 1.25M, 1.50M, 1.75M, 2 }, "Levl0", 0, 1);
            korRewarder.ModifySpinWheel(new string[] { "0.25", "0.25", "0.5", "0.5", "1", "2", "3", "5" }, new decimal[] { 0.5M, 0.50M, 0.75M, 1, 1.25M, 1.50M, 1.75M, 2 }, "Levl1", 1, 5);
            korRewarder.ModifySpinWheel(new string[] { "0.25", "0.25", "0.5", "0.5", "1", "2", "3", "5" }, new decimal[] { 0.5M, 0.50M, 0.75M, 1, 1.25M, 1.50M, 1.75M, 2 }, "Levl2", 2, 5);
            korRewarder.ModifySpinWheel(new string[] { "0.25", "0.25", "0.5", "0.5", "1", "2", "3", "5" }, new decimal[] { 0.5M, 0.50M, 0.75M, 1, 1.25M, 1.50M, 1.75M, 2 }, "Levl3", 3, 5);
            korRewarder.ModifySpinWheel(new string[] { "0.25", "0.25", "0.5", "0.5", "1", "2", "3", "5" }, new decimal[] { 0.5M, 0.50M, 0.75M, 1, 1.25M, 1.50M, 1.75M, 2 }, "Levl4", 4, 5);
            korRewarder.ModifySpinWheel(new string[] { "0.25", "0.25", "0.5", "0.5", "1", "2", "3", "5" }, new decimal[] { 0.5M, 0.50M, 0.75M, 1, 1.25M, 1.50M, 1.75M, 2 }, "Levl5", 5, 5);
            korRewarder.ModifySpinWheel(new string[] { "0.25", "0.25", "0.5", "0.5", "1", "2", "3", "5" }, new decimal[] { 0.5M, 0.50M, 0.75M, 1, 1.25M, 1.50M, 1.75M, 2 }, "Levl6", 6, 5);
            korRewarder.ModifySpinWheel(new string[] { "0.25", "0.25", "0.5", "0.5", "1", "2", "3", "5" }, new decimal[] { 0.5M, 0.50M, 0.75M, 1, 1.25M, 1.50M, 1.75M, 2 }, "Levl7", 7, 5);
            korRewarder.ModifySpinWheel(new string[] { "0.25", "0.25", "0.5", "0.5", "1", "2", "3", "5" }, new decimal[] { 0.5M, 0.50M, 0.75M, 1, 1.25M, 1.50M, 1.75M, 2 }, "Levl8", 8, 5);
            korRewarder.ModifySpinWheel(new string[] { "0.25", "0.25", "0.5", "0.5", "1", "2", "3", "5" }, new decimal[] { 0.5M, 0.50M, 0.75M, 1, 1.25M, 1.50M, 1.75M, 2 }, "Levl9", 9, 5);
            korRewarder.ModifySpinWheel(new string[] { "0.25", "0.25", "0.5", "0.5", "1", "2", "3", "5" }, new decimal[] { 0.5M, 0.50M, 0.75M, 1, 1.25M, 1.50M, 1.75M, 2 }, "Levl10", 10, 5);
            korStreakCampaign.ApplyDomains(new List<int> { domain.Id });
            korStreakCampaign.ApplyStores(new List<int> { store.Id });
            Assert.IsTrue(korStreakCampaign.Publishable);

            var customer = company.GetOrCreateCustomerById("123456");
            Player player = company.Players.SearchPlayer(customer.Player.Id);

            bool playerIsAllowToBuy;
            IEnumerable<CampaignBase> campaigns;

            DateTime purchaseDate = new DateTime(2024, 06, 30, 00, 00, 00);
            playerIsAllowToBuy = company.Campaigns.AreApplicableTo(purchaseDate, player, domain.Id, store.Id, Currencies.CODES.USD);
            Assert.IsFalse(playerIsAllowToBuy);
            //Check campaigns
            campaigns = company.Campaigns.ActiveSaleCampaign(purchaseDate, domain.Id, store.Id, Currencies.CODES.USD);
            Assert.IsTrue(campaigns.Count() == 0);

            purchaseDate = new DateTime(2024, 07, 01, 00, 00, 00);
            playerIsAllowToBuy = company.Campaigns.AreApplicableTo(purchaseDate, player, domain.Id, store.Id, Currencies.CODES.USD);
            Assert.IsTrue(playerIsAllowToBuy);
            //PURCHASE ONCE
            campaigns = company.Campaigns.ActiveSaleCampaign(purchaseDate, domain.Id, store.Id, Currencies.CODES.USD);
            Assert.IsTrue(campaigns.Count() == 2);
            foreach (var campaign in campaigns)
            {
                campaign.RegisterPurchase(itIsThePresent, player, purchaseDate, 1, store.Id, domain.Id, 1, Currencies.CODES.USD);
            }
            //CHECK IF THE PLAYER CAN BUY AGAIN
            playerIsAllowToBuy = company.Campaigns.AreApplicableTo(purchaseDate, player, domain.Id, store.Id, Currencies.CODES.USD);
            Assert.IsFalse(playerIsAllowToBuy);
            //DO SPIN in CrStreakCampaign
            SpinResult spinResult = crRewarder.Spin(itIsThePresent, purchaseDate, 0, player, 0, "Test", store.Id, domain.Id);
            Assert.IsTrue(spinResult != null);
            //Create a cashback campaign
            decimal maxAccruedPlayer = 100M;
            RewardBased korCashbackCampaign = company.Campaigns.CreateRewardBasedCampaign(itIsThePresent, todayKorea, RewarderType.TieredCashback, 0, (int)JournalDayOfWeek.AllWeek, Currencies.CODES.USD, Currencies.CODES.FP, company, 3, "Cashback Kor", todayKorea, todayKorea.AddDays(15), "Cashback Desc", true, tzKR.CountryCode);
            TieredCashbackRewarder korCashbackCampaignRewarder = korCashbackCampaign.Rewarder as TieredCashbackRewarder;
            korCashbackCampaignRewarder.MaxAccruedPlayer = maxAccruedPlayer;
            korCashbackCampaign.SetClaimDayOfTheWeek(JournalDayOfWeek.AllWeek);
            korCashbackCampaign.SetClaimRangeDates(new List<DateTime> { todayKorea.AddDays(15) }, new List<DateTime> { todayKorea.AddDays(30) });
            korCashbackCampaign.ApplyDomains(new List<int> { domain.Id });
            korCashbackCampaign.ApplyStores(new List<int> { store.Id });
            List<decimal> lowerLimits = new List<decimal>() { 0, 50, 100 };
            List<decimal> upperLimits = new List<decimal>() { 50, 100, 10000 };
            List<decimal> percentages = new List<decimal>() { 0.1M, 0.15M, 0.2M };
            korCashbackCampaignRewarder.SetupCashbackStage(lowerLimits, upperLimits, percentages);
            Assert.IsTrue(korCashbackCampaign.Publishable);
            campaigns = company.Campaigns.ActiveSaleCampaign(purchaseDate, domain.Id, store.Id, Currencies.CODES.USD);
            Assert.IsTrue(campaigns.Count() == 3);
            //CHECK IF THE PLAYER CAN BUY AGAIN
            playerIsAllowToBuy = company.Campaigns.AreApplicableTo(purchaseDate, player, domain.Id, store.Id, Currencies.CODES.USD);
            Assert.IsTrue(playerIsAllowToBuy);
            //REGISTER PURCHASE
            campaigns = company.Campaigns.ActiveSaleCampaign(purchaseDate, domain.Id, store.Id, Currencies.CODES.USD);
            foreach (var campaign in campaigns)
            {
                campaign.RegisterPurchase(itIsThePresent, player, purchaseDate, 100, store.Id, domain.Id, 1, Currencies.CODES.USD);
            }
            //CHECK PLAYER CASHBACK
            decimal cashback = korCashbackCampaignRewarder.AccruedCashback(player);
            Assert.IsTrue(cashback == 20M);

            purchaseDate = new DateTime(2024, 07, 15, 00, 00, 00);
            playerIsAllowToBuy = company.Campaigns.AreApplicableTo(purchaseDate, player, domain.Id, store.Id, Currencies.CODES.USD);
            Assert.IsTrue(playerIsAllowToBuy);

            purchaseDate = new DateTime(2024, 07, 31, 00, 00, 00);
            playerIsAllowToBuy = company.Campaigns.AreApplicableTo(purchaseDate, player, domain.Id, store.Id, Currencies.CODES.USD);
            Assert.IsFalse(playerIsAllowToBuy);
            //USE THE CASHBACK CAMPAIGN
            korCashbackCampaignRewarder.ClaimCashback(itIsThePresent, store.Id, purchaseDate, player, "TEST", domain.Id);

            purchaseDate = new DateTime(2024, 08, 01, 00, 00, 00);
            playerIsAllowToBuy = company.Campaigns.AreApplicableTo(purchaseDate, player, domain.Id, store.Id, Currencies.CODES.USD);
            Assert.IsFalse(playerIsAllowToBuy);
        }

        [TestMethod]
        public void TimeZoneConverterTest()
        {
            CountryTimeZone tzEST = new CountryTimeZone { CountryCode = "ZZ", Windows = "Eastern Standard Time", Linux = "EST5EDT" };
            CountryTimeZone tzKR = new CountryTimeZone { CountryCode = "KR", Windows = "Korea Standard Time", Linux = "Asia/Seoul" };
            TimeZoneConverter.Instance.Configure(new List<CountryTimeZone> { tzEST, tzKR });

            DateTime localDateZZ = new DateTime(2024, 07, 16, 00, 00, 00, DateTimeKind.Local);
            DateTime localDateKR = TimeZoneConverter.Instance.ConvertTime(localDateZZ, tzEST.CountryCode, tzKR.CountryCode);
            Assert.IsTrue(localDateKR == new DateTime(2024, 07, 16, 13, 00, 00, DateTimeKind.Local));

            localDateZZ = new DateTime(2024, 07, 16, 00, 00, 00, DateTimeKind.Unspecified);
            localDateKR = TimeZoneConverter.Instance.ConvertTime(localDateZZ, tzEST.CountryCode, tzKR.CountryCode);
            Assert.IsTrue(localDateKR == new DateTime(2024, 07, 16, 13, 00, 00, DateTimeKind.Local));

            localDateZZ = new DateTime(2024, 07, 16, 00, 00, 00, DateTimeKind.Utc);
            localDateKR = TimeZoneConverter.Instance.ConvertTime(localDateZZ, tzEST.CountryCode, tzKR.CountryCode);
            Assert.IsTrue(localDateKR == new DateTime(2024, 07, 16, 13, 00, 00, DateTimeKind.Local));
        }
    }
}
