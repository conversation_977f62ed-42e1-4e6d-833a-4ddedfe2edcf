﻿using GamesEngine;
using GamesEngine.Bets;
using GamesEngine.Business;
using GamesEngine.Domains;
using GamesEngine.Finance;
using GamesEngine.Gamification.Badges;
using GamesEngine.Marketing;
using GamesEngine.Marketing.Campaigns;
using GamesEngine.Marketing.Campaigns.Base;
using GamesEngine.Marketing.Campaigns.Rewarders;
using GamesEngine.PurchaseOrders;
using GamesEngine.Settings;
using GamesEngine.Time;
using GamesEngineTests.Unit_Tests.Games.Lotto;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Unit.Games.Tools;

namespace GamesEngineTests.Unit_Tests.Campaigns
{
    [TestClass]
    public class CampaignsTest
    {
        private DateTime Now = DateTime.Now;

        [TestMethod]
        public void CreateCampaigns()
        {
            Company company = new Company();
            company.Sales.CreateStore(1, "Test Store").MakeCurrent();
            CurrenciesTest.AddCurrencies();
            var manualLottoReward = company.Campaigns.CreateGiftCardCampaign(1, Now, "Manual", Currencies.CODES.LR);
            var firsttimeLottoReward = company.Campaigns.CreateGiftCardCampaign(2, Now, "Lucky 77", Currencies.CODES.LR);
            var rechargeLottoReward = company.Campaigns.CreateGiftCardCampaign(3, Now, "Reload", Currencies.CODES.LR);

            Assert.AreEqual(1, manualLottoReward.PromotionNumber);
            Assert.AreEqual(2, firsttimeLottoReward.PromotionNumber);
            Assert.AreEqual(3, rechargeLottoReward.PromotionNumber);

            Assert.IsFalse(manualLottoReward.HasStartAndEndDate);
            manualLottoReward.StartDate = Now;
            manualLottoReward.EndDate = Now.AddDays(30);
            Assert.IsTrue(manualLottoReward.HasStartAndEndDate);
        }

        [TestMethod]
        public void ExistsGiftCardCampaign()
        {
            Company company = new Company();
            company.Sales.CreateStore(1, "Test Store").MakeCurrent();
            CurrenciesTest.AddCurrencies();

            Assert.IsFalse(company.Campaigns.ExistsGiftCardCampaign(1));
            var manualLottoReward = company.Campaigns.CreateGiftCardCampaign(1, Now, "Manual", Currencies.CODES.FP);

            Assert.IsTrue(company.Campaigns.ExistsGiftCardCampaign(1));
            Assert.IsFalse(company.Campaigns.ExistsGiftCardCampaign(2));
        }

        [TestMethod]
        public void AssignPeopleToACampaign()
        {
            //Integration.UseKafka = true;
            QueueInMemory queue = new QueueInMemory();
            Integration.Kafka = queue;

            Company company = new Company();
            CurrenciesTest.AddCurrencies();
            var store = company.Sales.CreateStore(1, "Test Store");
            store.MakeCurrent();
            var manualLottoReward = company.Campaigns.CreateGiftCardCampaign(1, Now, "Manual", Currencies.CODES.LR);
            var firsttimeLottoReward = company.Campaigns.CreateGiftCardCampaign(2, Now, "Lucky 77", Currencies.CODES.LR);
            var rechargeLottoReward = company.Campaigns.CreateGiftCardCampaign(3, Now, "Reload", Currencies.CODES.LR);

            Assert.AreEqual(1, manualLottoReward.PromotionNumber);
            Assert.AreEqual(2, firsttimeLottoReward.PromotionNumber);
            Assert.AreEqual(3, rechargeLottoReward.PromotionNumber);

            Customer customer = company.GetOrCreateCustomerById("123456");
            manualLottoReward.Give(true, customer, Now, 10.0m, "Por promo", "Mau", Currencies.CODES.LR, store);

            Player player = customer.Player;
            foreach (var message in player.Messages.ReadAllMessages(Now))
            {
                Assert.Inconclusive("THIS METHOD IS NEEDS TO BE CHECK LATER, IT HAS NO PRIORIZE");
                Assert.AreEqual("Congratulations! You've received LottoReward 10.00", message.Body);
            }
        }

        [TestMethod]
        public void KPIsForLottoCampaigns()
        {
            //Integration.UseKafka = true;
            QueueInMemory queue = new QueueInMemory();
            Integration.Kafka = queue;

            Company company = new Company();

            var store = company.Sales.CreateStore(1, "Test Store");
            CurrenciesTest.AddCurrencies();
            store.MakeCurrent();
            var manualLottoReward = company.Campaigns.CreateGiftCardCampaign(1, Now, "Manual", Currencies.CODES.LR);
            var firsttimeLottoReward = company.Campaigns.CreateGiftCardCampaign(2, Now, "Lucky 77", Currencies.CODES.LR);
            var rechargeLottoReward = company.Campaigns.CreateGiftCardCampaign(3, Now, "Reload", Currencies.CODES.LR);

            Assert.AreEqual(1, manualLottoReward.PromotionNumber);
            Assert.AreEqual(2, firsttimeLottoReward.PromotionNumber);
            Assert.AreEqual(3, rechargeLottoReward.PromotionNumber);

            Customer customer = company.GetOrCreateCustomerById("123456");
            manualLottoReward.Give(true, customer, Now, 10.0m, "Por promo", "Mau", Currencies.CODES.LR.ToString(), store);
            manualLottoReward.Give(true, customer, Now, 10.0m, "Por promo", "Mau", Currencies.CODES.LR.ToString(), store);
            Assert.AreEqual(manualLottoReward.GivenTimesNumber, 2);

            Customer customer2 = company.GetOrCreateCustomerById("789101");
            firsttimeLottoReward.Give(true, customer2, Now, 10.0m, "Por promo", "Mau", Currencies.CODES.LR, store);
            firsttimeLottoReward.Give(true, customer2, Now, 10.0m, "Por promo", "Mau", Currencies.CODES.LR, store);
            Assert.AreEqual(firsttimeLottoReward.GivenTimesNumber, 2);

            Customer customer3 = company.GetOrCreateCustomerById("887654");
            rechargeLottoReward.Give(true, customer3, Now, 10.0m, "Por promo", "Mau", Currencies.CODES.LR, store);
            rechargeLottoReward.Give(true, customer3, Now, 10.0m, "Por promo", "Mau", Currencies.CODES.LR, store);
            Assert.AreEqual(rechargeLottoReward.GivenTimesNumber, 2);

            firsttimeLottoReward.Give(true, customer, Now, 10.0m, "Por promo", "Mau", Currencies.CODES.LR, store);
            Assert.AreEqual(firsttimeLottoReward.GivenTimesNumber, 3);
            Assert.AreEqual(manualLottoReward.GivenTimesNumber, 2);
            Assert.AreEqual(rechargeLottoReward.GivenTimesNumber, 2);

            firsttimeLottoReward.TakeOut(true, customer, Now, 10.0m, "Por promo", "Mau", Currencies.CODES.LR, store);
            Assert.AreEqual(firsttimeLottoReward.GivenTimesNumber, 3);
            Assert.AreEqual(manualLottoReward.GivenTimesNumber, 2);
            Assert.AreEqual(rechargeLottoReward.GivenTimesNumber, 2);
        }

        [TestMethod]
        public void CreateBadgesCampaign()
        {
            Company company = new Company();
            company.Sales.CreateStore(1, "Test Store").MakeCurrent();
            var mm2020Badges = company.Campaigns.CreateBadgesCampaign(1, Now, "MarchMadness2020", "Brackets 2020");
            Assert.AreEqual(1, mm2020Badges.Number);
            Assert.AreEqual("MarchMadness2020", mm2020Badges.Name);

            Badges badges = company.Campaigns.BadgeCampaign(mm2020Badges.Number);
            Assert.IsNotNull(badges);
        }

        [TestMethod]
        public void PercentageToGiveOnReloadCampaign()
        {
            Company company = new Company();
            company.Sales.CreateStore(1, "Test Store").MakeCurrent();
            CurrenciesTest.AddCurrencies();
            var manualLottoReward = company.Campaigns.CreateGiftCardCampaign(1, Now, "Manual", Currencies.CODES.LR);
            var firsttimeLottoReward = company.Campaigns.CreateGiftCardCampaign(2, Now, "Lucky 77", Currencies.CODES.LR);
            var rechargeLottoReward = company.Campaigns.CreateGiftCardCampaign(3, Now, "Reload", Currencies.CODES.LR);

            Assert.AreEqual(1, manualLottoReward.PromotionNumber);
            Assert.AreEqual(2, firsttimeLottoReward.PromotionNumber);
            Assert.AreEqual(3, rechargeLottoReward.PromotionNumber);

            decimal amountToGive = 0;
            //5%
            try
            {
                amountToGive = rechargeLottoReward.CalculateAmountAndDescriptorToReload(0);
            }
            catch (GameEngineException e)
            {
                Assert.AreEqual(e.Message, "Deposit amount is not eligible for Reload promo. It must be between $100 to $1000.");
            }

            amountToGive = rechargeLottoReward.CalculateAmountAndDescriptorToReload(100);
            Assert.AreEqual(amountToGive, 5);
            amountToGive = rechargeLottoReward.CalculateAmountAndDescriptorToReload(333);
            Assert.AreEqual(amountToGive, 16.65m);
            amountToGive = rechargeLottoReward.CalculateAmountAndDescriptorToReload(425.5m);
            Assert.AreEqual(amountToGive, 21.28m);
            amountToGive = rechargeLottoReward.CalculateAmountAndDescriptorToReload(500);
            Assert.AreEqual(amountToGive, 25);

            //10%
            amountToGive = rechargeLottoReward.CalculateAmountAndDescriptorToReload(501);
            Assert.AreEqual(amountToGive, 50.10m);
            amountToGive = rechargeLottoReward.CalculateAmountAndDescriptorToReload(687.5m);
            Assert.AreEqual(amountToGive, 68.75m);
            amountToGive = rechargeLottoReward.CalculateAmountAndDescriptorToReload(750);
            Assert.AreEqual(amountToGive, 75);

            //15%
            amountToGive = rechargeLottoReward.CalculateAmountAndDescriptorToReload(751);
            Assert.AreEqual(amountToGive, 112.65m);
            amountToGive = rechargeLottoReward.CalculateAmountAndDescriptorToReload(1000);
            Assert.AreEqual(amountToGive, 150);

            try
            {
                amountToGive = rechargeLottoReward.CalculateAmountAndDescriptorToReload(2000);
            }
            catch (GameEngineException e)
            {
                Assert.AreEqual(e.Message, "Deposit amount is not eligible for Reload promo. It must be between $100 to $1000.");
            }

            try
            {
                amountToGive = rechargeLottoReward.CalculateAmountAndDescriptorToReload(-1);
            }
            catch (GameEngineException e)
            {
                Assert.AreEqual(e.Message, "Deposit amount is invalid, it can not be negative.");
            }
        }

        [TestMethod]
        public void CreateAllCampaigns_IncludingFeedbackCampaign()
        {
            //Integration.UseKafka = true;
            QueueInMemory queue = new QueueInMemory();
            Integration.Kafka = queue;
            decimal amountToGive = 5.0m;

            Company company = new Company();

            var store = company.Sales.CreateStore(1, "Test Store");
            CurrenciesTest.AddCurrencies();
            store.MakeCurrent();
            var manualLottoReward = company.Campaigns.CreateGiftCardCampaign(1, Now, "Manual", Currencies.CODES.LR);
            var firsttimeLottoReward = company.Campaigns.CreateGiftCardCampaign(2, Now, "Lucky 77", Currencies.CODES.LR);
            var rechargeLottoReward = company.Campaigns.CreateGiftCardCampaign(3, Now, "Reload", Currencies.CODES.LR);
            var feedbackCampaign = company.Campaigns.CreateGiftCardCampaign(4, Now, "Loyalty", Currencies.CODES.LR);

            Assert.AreEqual(1, manualLottoReward.PromotionNumber);
            Assert.AreEqual(2, firsttimeLottoReward.PromotionNumber);
            Assert.AreEqual(3, rechargeLottoReward.PromotionNumber);
            Assert.AreEqual(4, feedbackCampaign.PromotionNumber);

            Customer customer = company.GetOrCreateCustomerById("123456");

            Assert.IsTrue(feedbackCampaign.IsValidAmount(amountToGive), "Valid Amount");
            feedbackCampaign.Give(true, customer, Now, amountToGive, "Por enviar feedback", "Mau", Currencies.CODES.LR, store);
            Assert.AreEqual(1, feedbackCampaign.CustomersCount);

            amountToGive = 20.0m;
            Assert.IsFalse(feedbackCampaign.IsValidAmount(amountToGive), "Invalid amount");

            try
            {
                amountToGive = 20.0m;
                feedbackCampaign.Give(true, customer, Now, amountToGive, "Por enviar feedback", "Mau", Currencies.CODES.LR, store);
            }
            catch (Exception e)
            {
                Assert.AreEqual(e.Message, "You can not assign more than $5 in Loyalty campaigns");
            }

            foreach (var campaign in company.Campaigns.List())
            {
                if (campaign.PromotionNumber == 4 && campaign.Name == "Loyalty")
                    Assert.IsNotNull(campaign);
            }
        }

        [TestMethod]
        public void PlayerHasMovementsOnCampaign_Task5558()
        {
            //Integration.UseKafka = true;
            QueueInMemory queue = new QueueInMemory();
            Integration.Kafka = queue;
            decimal amountToGive = 5.0m;

            Company company = new Company();

            var store = company.Sales.CreateStore(1, "Test Store");
            CurrenciesTest.AddCurrencies();
            store.MakeCurrent();
            var manualLottoReward = company.Campaigns.CreateGiftCardCampaign(1, Now, "Manual", Currencies.CODES.LR);
            var firsttimeLottoReward = company.Campaigns.CreateGiftCardCampaign(2, Now, "Lucky 77", Currencies.CODES.LR);
            var rechargeLottoReward = company.Campaigns.CreateGiftCardCampaign(3, Now, "Reload", Currencies.CODES.LR);
            var feedbackCampaign = company.Campaigns.CreateGiftCardCampaign(4, Now, "Loyalty", Currencies.CODES.LR);

            Assert.AreEqual(1, manualLottoReward.PromotionNumber);
            Assert.AreEqual(2, firsttimeLottoReward.PromotionNumber);
            Assert.AreEqual(3, rechargeLottoReward.PromotionNumber);
            Assert.AreEqual(4, feedbackCampaign.PromotionNumber);

            Customer customer = company.GetOrCreateCustomerById("123456");
            Assert.IsTrue(feedbackCampaign.IsValidAmount(amountToGive), "Valid Amount");
            feedbackCampaign.Give(true, customer, Now, amountToGive, "Por enviar feedback", "Mau", Currencies.CODES.LR, store);
            Assert.AreEqual(1, feedbackCampaign.CustomersCount);

            Player player = customer.Player;
            GiftCard campaign = (GiftCard)company.Campaigns.PromotionCampaign(4);
            Assert.IsTrue(campaign.HasMovementsFor(player));

            customer = company.GetOrCreateCustomerById("4455654");
            player = customer.Player;
            campaign = (GiftCard)company.Campaigns.PromotionCampaign(4);
            Assert.IsFalse(campaign.HasMovementsFor(player));
        }

        [TestMethod]
        public void Budget()
        {
            //Integration.UseKafka = true;
            QueueInMemory queue = new QueueInMemory();
            Integration.Kafka = queue;

            Company company = new Company();
            var store = company.Sales.CreateStore(1, "Test Store");
            CurrenciesTest.AddCurrencies();
            store.MakeCurrent();

            var manualLottoReward = company.Campaigns.CreateGiftCardCampaign(1, Now, "Manual", Currencies.CODES.FP.ToString(), 5000m);
            Customer customer = company.GetOrCreateCustomerById("123456");
            decimal amountToGive = 5.0m;
            manualLottoReward.Give(true, customer, Now, amountToGive, "Award 1", "N/A", Currencies.CODES.FP.ToString(), store);

            Assert.AreEqual(1, manualLottoReward.PromotionNumber);
            Assert.IsFalse(manualLottoReward.IsUnlimitedBudget);
            Assert.IsTrue(manualLottoReward.IsEnabled);
            Assert.AreEqual(5000m, manualLottoReward.InitialBudget);
            Assert.AreEqual(4995m, manualLottoReward.ActualBudget);
            Assert.AreEqual(5m, manualLottoReward.GivenBudget);

            var firsttimeLottoReward = company.Campaigns.CreateGiftCardCampaign(2, Now, "Lucky 77", Currencies.CODES.FP);
            firsttimeLottoReward.IsEnabled = false;

            Assert.AreEqual(2, firsttimeLottoReward.PromotionNumber);
            Assert.IsTrue(firsttimeLottoReward.IsUnlimitedBudget);
            Assert.IsFalse(firsttimeLottoReward.IsEnabled);
            Assert.AreEqual(0m, firsttimeLottoReward.GivenBudget);
        }

        [TestMethod]
        public void UpdateBudget()
        {
            //Integration.UseKafka = true;
            QueueInMemory queue = new QueueInMemory();
            Integration.Kafka = queue;

            Company company = new Company();
            var store = company.Sales.CreateStore(1, "Test Store");
            CurrenciesTest.AddCurrencies();
            store.MakeCurrent();

            var manualLottoReward = company.Campaigns.CreateGiftCardCampaign(1, Now, "Manual", Currencies.CODES.FP.ToString(), 5000m);
            Customer customer = company.GetOrCreateCustomerById("123456");
            decimal amountToGive = 5.0m;
            manualLottoReward.Give(true, customer, Now, amountToGive, "Award 1", "N/A", Currencies.CODES.FP.ToString(), store);

            Assert.AreEqual(1, manualLottoReward.PromotionNumber);
            Assert.IsFalse(manualLottoReward.IsUnlimitedBudget);
            Assert.IsTrue(manualLottoReward.IsEnabled);
            Assert.AreEqual(5000m, manualLottoReward.InitialBudget);
            Assert.AreEqual(4995m, manualLottoReward.ActualBudget);
            Assert.AreEqual(5m, manualLottoReward.GivenBudget);

            manualLottoReward.SetUnlimitedBudget();
            Assert.AreEqual(1, manualLottoReward.PromotionNumber);
            Assert.IsTrue(manualLottoReward.IsUnlimitedBudget);
            Assert.AreEqual(5m, manualLottoReward.GivenBudget);

            amountToGive = 1500.0m;
            manualLottoReward.Give(true, customer, Now, amountToGive, "Award 1", "N/A", Currencies.CODES.FP.ToString(), store);
            manualLottoReward.UpdateBudget(3000m);

            Assert.IsFalse(manualLottoReward.IsUnlimitedBudget);
            Assert.IsTrue(manualLottoReward.IsEnabled);
            Assert.AreEqual(3000m, manualLottoReward.InitialBudget);
            Assert.AreEqual(1495m, manualLottoReward.ActualBudget);
            Assert.AreEqual(1505m, manualLottoReward.GivenBudget);
        }

        [TestMethod]
        public void IsSpentAmountGreaterThan()
        {
            //Integration.UseKafka = true;
            QueueInMemory queue = new QueueInMemory();
            Integration.Kafka = queue;

            Company company = new Company();
            var store = company.Sales.CreateStore(1, "Test Store");
            CurrenciesTest.AddCurrencies();
            store.MakeCurrent();

            var manualLottoReward = company.Campaigns.CreateGiftCardCampaign(1, Now, "Manual", Currencies.CODES.FP.ToString(), 5000m);
            Customer customer = company.GetOrCreateCustomerById("123456");
            var amountToGive = 1500.0m;
            manualLottoReward.Give(true, customer, Now, amountToGive, "Award 1", "N/A", Currencies.CODES.FP.ToString(), store);

            Assert.IsFalse(manualLottoReward.IsSpentAmountGreaterThan(2000m));
            Assert.IsTrue(manualLottoReward.IsSpentAmountGreaterOrEqualThan(1500m));
            Assert.IsTrue(manualLottoReward.IsSpentAmountGreaterThan(1000m));
        }

        [TestMethod]
        public void IsExceededBudget()
        {
            //Integration.UseKafka = true;
            QueueInMemory queue = new QueueInMemory();
            Integration.Kafka = queue;

            Company company = new Company();
            var store = company.Sales.CreateStore(1, "Test Store");
            CurrenciesTest.AddCurrencies();
            store.MakeCurrent();

            var manualLottoReward = company.Campaigns.CreateGiftCardCampaign(1, Now, "Manual", Currencies.CODES.FP.ToString(), 1500m);
            Customer customer = company.GetOrCreateCustomerById("123456");
            var amountToGive = 1000.0m;
            manualLottoReward.Give(true, customer, Now, amountToGive, "Award 1", "N/A", Currencies.CODES.FP.ToString(), store);

            Assert.IsFalse(manualLottoReward.IsExceededBudget(500m));
            Assert.IsTrue(manualLottoReward.IsExceededBudget(1000m));
        }

        [TestMethod]
        public void UpdateResources()
        {
            //Integration.UseKafka = true;
            QueueInMemory queue = new QueueInMemory();
            Integration.Kafka = queue;

            Company company = new Company();
            var store = company.Sales.CreateStore(1, "Test Store");
            CurrenciesTest.AddCurrencies();
            store.MakeCurrent();

            var manualLottoReward = company.Campaigns.CreateGiftCardCampaign(1, Now, "Manual", Currencies.CODES.FP.ToString(), 5000m);
            Customer customer = company.GetOrCreateCustomerById("123456");

            Assert.IsFalse(manualLottoReward.HasResources);

            var names = new List<string> { "giftcard1", "giftcard2" };
            var urls = new List<string> { "http://resources.cr/giftcard1.jpg", "http://resources.cr/giftcard2.jpg" };
            manualLottoReward.AddResources(names, urls);
            manualLottoReward.ChoosePreferredResource(names[1]);

            Assert.IsTrue(manualLottoReward.HasResources);
            Assert.AreEqual(names[1], manualLottoReward.PreferredResourceName);
            Assert.AreEqual(2, manualLottoReward.Resources.Count());

            var names2 = new List<string> { "giftcard10", "giftcard20", "giftcard30" };
            var urls2 = new List<string> { "http://resources.cr/giftcard10.jpg", "http://resources.cr/giftcard20.jpg", "http://resources.cr/giftcard30.jpg" };
            manualLottoReward.UpdateResources(names2, urls2);
            manualLottoReward.ChoosePreferredResource(names2[0]);

            Assert.AreEqual(names2[0], manualLottoReward.PreferredResourceName);
            Assert.AreEqual(3, manualLottoReward.Resources.Count());
        }

        [TestMethod]
        public void ClearResources()
        {
            //Integration.UseKafka = true;
            QueueInMemory queue = new QueueInMemory();
            Integration.Kafka = queue;

            Company company = new Company();
            var store = company.Sales.CreateStore(1, "Test Store");
            CurrenciesTest.AddCurrencies();
            store.MakeCurrent();

            var manualLottoReward = company.Campaigns.CreateGiftCardCampaign(1, Now, "Manual", Currencies.CODES.FP.ToString(), 5000m);
            Customer customer = company.GetOrCreateCustomerById("123456");

            Assert.IsFalse(manualLottoReward.HasResources);

            var names = new List<string> { "giftcard1", "giftcard2" };
            var urls = new List<string> { "http://resources.cr/giftcard1.jpg", "http://resources.cr/giftcard2.jpg" };
            manualLottoReward.UpdateResources(names, urls);
            manualLottoReward.ChoosePreferredResource(names[1]);

            Assert.IsTrue(manualLottoReward.HasResources);
            Assert.AreEqual(names[1], manualLottoReward.PreferredResourceName);
            Assert.AreEqual(2, manualLottoReward.Resources.Count());

            manualLottoReward.ClearResources();

            Assert.IsFalse(manualLottoReward.HasResources);
        }

        [TestMethod]
        public void ClearPreferred()
        {
            //Integration.UseKafka = true;
            QueueInMemory queue = new QueueInMemory();
            Integration.Kafka = queue;

            Company company = new Company();
            var store = company.Sales.CreateStore(1, "Test Store");
            CurrenciesTest.AddCurrencies();
            store.MakeCurrent();

            var manualLottoReward = company.Campaigns.CreateGiftCardCampaign(1, Now, "Manual", Currencies.CODES.FP.ToString(), 5000m);
            manualLottoReward.ClearPreferred();

            var names = new List<string> { "giftcard1", "giftcard2" };
            var urls = new List<string> { "http://resources.cr/giftcard1.jpg", "http://resources.cr/giftcard2.jpg" };
            manualLottoReward.UpdateResources(names, urls);
            manualLottoReward.ChoosePreferredResource(names[1]);

            Assert.IsTrue(manualLottoReward.HasPreferredResource);
            Assert.AreEqual(names[1], manualLottoReward.PreferredResourceName);
            Assert.AreEqual(2, manualLottoReward.Resources.Count());

            manualLottoReward.ClearPreferred();

            Assert.IsFalse(manualLottoReward.HasPreferredResource);
        }

        [TestMethod]
        public void LastCustomers()
        {
            //Integration.UseKafka = true;
            QueueInMemory queue = new QueueInMemory();
            Integration.Kafka = queue;

            Company company = new Company();
            var store = company.Sales.CreateStore(1, "Test Store");
            CurrenciesTest.AddCurrencies();
            store.MakeCurrent();

            var manualLottoReward = company.Campaigns.CreateGiftCardCampaign(1, Now, "Manual", Currencies.CODES.FP.ToString(), 5000m);
            var customers = new List<Customer>();
            for (int i = 0; i < 12; i++)
            {
                var customer = company.GetOrCreateCustomerById($"{10000 + i}");
                customers.Add(customer);
            }

            decimal amountToGive = 5.0m;
            foreach (var customer in customers)
            {
                manualLottoReward.Give(true, customer, Now, amountToGive, "Award 1", "N/A", Currencies.CODES.FP.ToString(), store);
            }

            Assert.AreEqual(10, manualLottoReward.LastBeneficiaries.Count());
            Assert.AreEqual(12, manualLottoReward.GivenTimesNumber);

            var firstCustomer = manualLottoReward.LastBeneficiaries.First();
            var lastCustomer = manualLottoReward.LastBeneficiaries.Last();
            Assert.AreEqual("10011", firstCustomer.AccountNumber);
            Assert.AreEqual("10002", lastCustomer.AccountNumber);
        }

        [TestMethod]
        public void GiftCardPercentages()
        {
            Company company = new Company();
            var store = company.Sales.CreateStore(1, "Test Store");
            CurrenciesTest.AddCurrencies();
            store.MakeCurrent();

            var percentages = company.Campaigns.GiftCardPercentageRangesDefinition;
            Assert.AreEqual(3, percentages.Count());

            var range1 = percentages.ElementAt(0);
            Assert.AreEqual(100, range1.MinAmount);
            Assert.AreEqual(500, range1.MaxAmount);
            Assert.AreEqual(0.05m, range1.Percentage);

            var range2 = percentages.ElementAt(1);
            Assert.AreEqual(501, range2.MinAmount);
            Assert.AreEqual(750, range2.MaxAmount);
            Assert.AreEqual(0.1m, range2.Percentage);

            var range3 = percentages.ElementAt(2);
            Assert.AreEqual(751, range3.MinAmount);
            Assert.AreEqual(1000, range3.MaxAmount);
            Assert.AreEqual(0.15m, range3.Percentage);
        }

        [TestMethod]
        public void StreakCampaign_AreApplicableTo_DifferentDay()
        {
            bool itIsThePresent = false;
            Company company = new Company();
            company.Sales.CreateDomain(false, 1, "http://Test.com", 1);
            var store = company.Sales.CreateStore(1, "Test Store");
            CurrenciesTest.AddCurrencies();
            store.MakeCurrent();

            const byte maxLevel = 11;
            DateTime ActualDate = DateTime.Now;
            DateTime startDate = ActualDate.AddDays(1);
            DateTime endDate = ActualDate.AddDays(150);
            ActionerStreak streakCampaign = company.Campaigns.CreateStreakCampaign(itIsThePresent, 1, ActualDate, "Streak", Currencies.CODES.FP, startDate, endDate, maxLevel, "test", RewarderType.SpinWheel, CampaignScaleType.DailyCounter, true, Currencies.CODES.USD);
            streakCampaign.LastDayToChangePrizes = ActualDate.AddDays(153);

            SpinWheelRewarder campaignRewarder = company.Campaigns.Rewarder(streakCampaign) as SpinWheelRewarder;
            campaignRewarder.ModifySpinWheel(new string[] { "0.25", "0.25", "0.5", "0.5", "1", "2", "3", "5" }, new decimal[] { 0.5M, 0.50M, 0.75M, 1, 1.25M, 1.50M, 1.75M, 2 }, "Levl0", 0, 1);
            campaignRewarder.ModifySpinWheel(new string[] { "0.25", "0.25", "0.5", "0.5", "1", "2", "3", "5" }, new decimal[] { 0.5M, 0.50M, 0.75M, 1, 1.25M, 1.50M, 1.75M, 2 }, "Levl1", 1, 5);
            campaignRewarder.ModifySpinWheel(new string[] { "0.25", "0.25", "0.5", "0.5", "1", "2", "3", "5" }, new decimal[] { 0.5M, 0.50M, 0.75M, 1, 1.25M, 1.50M, 1.75M, 2 }, "Levl2", 2, 5);
            campaignRewarder.ModifySpinWheel(new string[] { "0.25", "0.25", "0.5", "0.5", "1", "2", "3", "5" }, new decimal[] { 0.5M, 0.50M, 0.75M, 1, 1.25M, 1.50M, 1.75M, 2 }, "Levl3", 3, 5);
            campaignRewarder.ModifySpinWheel(new string[] { "0.25", "0.25", "0.5", "0.5", "1", "2", "3", "5" }, new decimal[] { 0.5M, 0.50M, 0.75M, 1, 1.25M, 1.50M, 1.75M, 2 }, "Levl4", 4, 5);
            campaignRewarder.ModifySpinWheel(new string[] { "0.25", "0.25", "0.5", "0.5", "1", "2", "3", "5" }, new decimal[] { 0.5M, 0.50M, 0.75M, 1, 1.25M, 1.50M, 1.75M, 2 }, "Levl5", 5, 5);
            campaignRewarder.ModifySpinWheel(new string[] { "0.25", "0.25", "0.5", "0.5", "1", "2", "3", "5" }, new decimal[] { 0.5M, 0.50M, 0.75M, 1, 1.25M, 1.50M, 1.75M, 2 }, "Levl6", 6, 5);
            campaignRewarder.ModifySpinWheel(new string[] { "0.25", "0.25", "0.5", "0.5", "1", "2", "3", "5" }, new decimal[] { 0.5M, 0.50M, 0.75M, 1, 1.25M, 1.50M, 1.75M, 2 }, "Levl7", 7, 5);
            campaignRewarder.ModifySpinWheel(new string[] { "0.25", "0.25", "0.5", "0.5", "1", "2", "3", "5" }, new decimal[] { 0.5M, 0.50M, 0.75M, 1, 1.25M, 1.50M, 1.75M, 2 }, "Levl8", 8, 5);
            campaignRewarder.ModifySpinWheel(new string[] { "0.25", "0.25", "0.5", "0.5", "1", "2", "3", "5" }, new decimal[] { 0.5M, 0.50M, 0.75M, 1, 1.25M, 1.50M, 1.75M, 2 }, "Levl9", 9, 5);
            campaignRewarder.ModifySpinWheel(new string[] { "0.25", "0.25", "0.5", "0.5", "1", "2", "3", "5" }, new decimal[] { 0.5M, 0.50M, 0.75M, 1, 1.25M, 1.50M, 1.75M, 2 }, "Levl10", 10, 5);

            Customer customer = company.GetOrCreateCustomerById("123456");
            Player player = company.Players.SearchPlayer(customer.Player.Id);

            var domain = company.Sales.DomainFrom("Test.com");
            streakCampaign.ApplyDomains(new List<int> { domain.Id });
            streakCampaign.ApplyStores(new List<int> { store.Id });

            streakCampaign.RegisterPurchase(false, player, startDate, 0, 1, 1, 1, Currencies.CODES.USD);

            bool playerIsAllowToBuy;
            playerIsAllowToBuy = company.Campaigns.AreApplicableTo(startDate, player, 1, 1, Currencies.CODES.USD);
            Assert.IsFalse(playerIsAllowToBuy);

            playerIsAllowToBuy = company.Campaigns.AreApplicableTo(startDate.AddDays(1), player, 1, 1, Currencies.CODES.USD);
            Assert.IsTrue(playerIsAllowToBuy);

            playerIsAllowToBuy = company.Campaigns.AreApplicableTo(endDate.AddDays(1), player, 1, 1, Currencies.CODES.USD);
            Assert.IsFalse(playerIsAllowToBuy);
        }

        [TestMethod]
        public void StreakCampaign_AreApplicable_ToSameDay()
        {
            bool itIsThePresent = false;
            Company company = new Company();
            company.Sales.CreateDomain(false, 1, "http://Test.com", 1);
            var store = company.Sales.CreateStore(1, "Test Store");
            CurrenciesTest.AddCurrencies();
            store.MakeCurrent();

            const byte maxLevel = 11;
            DateTime ActualDate = DateTime.Now;
            DateTime startDate = ActualDate.AddDays(1);
            ActionerStreak streakCampaign = company.Campaigns.CreateStreakCampaign(itIsThePresent, 1, ActualDate, "Streak", Currencies.CODES.FP, startDate, ActualDate.AddDays(150), maxLevel, "test", RewarderType.SpinWheel, CampaignScaleType.DailyCounter, true, Currencies.CODES.USD);
            streakCampaign.LastDayToChangePrizes = ActualDate.AddDays(153);

            SpinWheelRewarder campaignRewarder = company.Campaigns.Rewarder(streakCampaign) as SpinWheelRewarder;
            campaignRewarder.ModifySpinWheel(new string[] { "0.25", "0.25", "0.5", "0.5", "1", "2", "3", "5" }, new decimal[] { 0.5M, 0.50M, 0.75M, 1, 1.25M, 1.50M, 1.75M, 2 }, "Levl0", 0, 1);
            campaignRewarder.ModifySpinWheel(new string[] { "0.25", "0.25", "0.5", "0.5", "1", "2", "3", "5" }, new decimal[] { 0.5M, 0.50M, 0.75M, 1, 1.25M, 1.50M, 1.75M, 2 }, "Levl1", 1, 5);
            campaignRewarder.ModifySpinWheel(new string[] { "0.25", "0.25", "0.5", "0.5", "1", "2", "3", "5" }, new decimal[] { 0.5M, 0.50M, 0.75M, 1, 1.25M, 1.50M, 1.75M, 2 }, "Levl2", 2, 5);
            campaignRewarder.ModifySpinWheel(new string[] { "0.25", "0.25", "0.5", "0.5", "1", "2", "3", "5" }, new decimal[] { 0.5M, 0.50M, 0.75M, 1, 1.25M, 1.50M, 1.75M, 2 }, "Levl3", 3, 5);
            campaignRewarder.ModifySpinWheel(new string[] { "0.25", "0.25", "0.5", "0.5", "1", "2", "3", "5" }, new decimal[] { 0.5M, 0.50M, 0.75M, 1, 1.25M, 1.50M, 1.75M, 2 }, "Levl4", 4, 5);
            campaignRewarder.ModifySpinWheel(new string[] { "0.25", "0.25", "0.5", "0.5", "1", "2", "3", "5" }, new decimal[] { 0.5M, 0.50M, 0.75M, 1, 1.25M, 1.50M, 1.75M, 2 }, "Levl5", 5, 5);
            campaignRewarder.ModifySpinWheel(new string[] { "0.25", "0.25", "0.5", "0.5", "1", "2", "3", "5" }, new decimal[] { 0.5M, 0.50M, 0.75M, 1, 1.25M, 1.50M, 1.75M, 2 }, "Levl6", 6, 5);
            campaignRewarder.ModifySpinWheel(new string[] { "0.25", "0.25", "0.5", "0.5", "1", "2", "3", "5" }, new decimal[] { 0.5M, 0.50M, 0.75M, 1, 1.25M, 1.50M, 1.75M, 2 }, "Levl7", 7, 5);
            campaignRewarder.ModifySpinWheel(new string[] { "0.25", "0.25", "0.5", "0.5", "1", "2", "3", "5" }, new decimal[] { 0.5M, 0.50M, 0.75M, 1, 1.25M, 1.50M, 1.75M, 2 }, "Levl8", 8, 5);
            campaignRewarder.ModifySpinWheel(new string[] { "0.25", "0.25", "0.5", "0.5", "1", "2", "3", "5" }, new decimal[] { 0.5M, 0.50M, 0.75M, 1, 1.25M, 1.50M, 1.75M, 2 }, "Levl9", 9, 5);
            campaignRewarder.ModifySpinWheel(new string[] { "0.25", "0.25", "0.5", "0.5", "1", "2", "3", "5" }, new decimal[] { 0.5M, 0.50M, 0.75M, 1, 1.25M, 1.50M, 1.75M, 2 }, "Levl10", 10, 5);

            Customer customer = company.GetOrCreateCustomerById("123456");
            Player player = company.Players.SearchPlayer(customer.Player.Id);

            var domain = company.Sales.DomainFrom("Test.com");
            streakCampaign.ApplyDomains(new List<int> { domain.Id });
            streakCampaign.ApplyStores(new List<int> { store.Id });

            bool playerIsAllowToBuy;
            playerIsAllowToBuy = company.Campaigns.AreApplicableTo(startDate, player, 1, 1, Currencies.CODES.USD);
            Assert.IsTrue(playerIsAllowToBuy);

            streakCampaign.RegisterPurchase(false, player, startDate, 0, 1, 1, 1, Currencies.CODES.USD);

            playerIsAllowToBuy = company.Campaigns.AreApplicableTo(startDate, player, 1, 1, Currencies.CODES.USD);
            Assert.IsFalse(playerIsAllowToBuy);
        }

        [TestMethod]
        public void StreakCampaign_AreApplicable_OutOfDay()
        {
            bool itIsThePresent = false;
            Company company = new Company();
            company.Sales.CreateDomain(false, 1, "http://Test.com", 1);
            var store = company.Sales.CreateStore(1, "Test Store");
            CurrenciesTest.AddCurrencies();
            store.MakeCurrent();

            const byte maxLevel = 11;
            DateTime ActualDate = DateTime.Now;
            DateTime startDate = ActualDate.AddDays(1);
            ActionerStreak streakCampaign = company.Campaigns.CreateStreakCampaign(itIsThePresent, 1, ActualDate, "Streak", Currencies.CODES.FP, startDate, ActualDate.AddDays(150), maxLevel, "test", RewarderType.SpinWheel, CampaignScaleType.DailyCounter, true, Currencies.CODES.USD);
            streakCampaign.LastDayToChangePrizes = ActualDate.AddDays(153);

            SpinWheelRewarder campaignRewarder = company.Campaigns.Rewarder(streakCampaign) as SpinWheelRewarder;
            campaignRewarder.ModifySpinWheel(new string[] { "0.25", "0.25", "0.5", "0.5", "1", "2", "3", "5" }, new decimal[] { 0.5M, 0.50M, 0.75M, 1, 1.25M, 1.50M, 1.75M, 2 }, "Levl0", 0, 1);
            campaignRewarder.ModifySpinWheel(new string[] { "0.25", "0.25", "0.5", "0.5", "1", "2", "3", "5" }, new decimal[] { 0.5M, 0.50M, 0.75M, 1, 1.25M, 1.50M, 1.75M, 2 }, "Levl1", 1, 5);
            campaignRewarder.ModifySpinWheel(new string[] { "0.25", "0.25", "0.5", "0.5", "1", "2", "3", "5" }, new decimal[] { 0.5M, 0.50M, 0.75M, 1, 1.25M, 1.50M, 1.75M, 2 }, "Levl2", 2, 5);
            campaignRewarder.ModifySpinWheel(new string[] { "0.25", "0.25", "0.5", "0.5", "1", "2", "3", "5" }, new decimal[] { 0.5M, 0.50M, 0.75M, 1, 1.25M, 1.50M, 1.75M, 2 }, "Levl3", 3, 5);
            campaignRewarder.ModifySpinWheel(new string[] { "0.25", "0.25", "0.5", "0.5", "1", "2", "3", "5" }, new decimal[] { 0.5M, 0.50M, 0.75M, 1, 1.25M, 1.50M, 1.75M, 2 }, "Levl4", 4, 5);
            campaignRewarder.ModifySpinWheel(new string[] { "0.25", "0.25", "0.5", "0.5", "1", "2", "3", "5" }, new decimal[] { 0.5M, 0.50M, 0.75M, 1, 1.25M, 1.50M, 1.75M, 2 }, "Levl5", 5, 5);
            campaignRewarder.ModifySpinWheel(new string[] { "0.25", "0.25", "0.5", "0.5", "1", "2", "3", "5" }, new decimal[] { 0.5M, 0.50M, 0.75M, 1, 1.25M, 1.50M, 1.75M, 2 }, "Levl6", 6, 5);
            campaignRewarder.ModifySpinWheel(new string[] { "0.25", "0.25", "0.5", "0.5", "1", "2", "3", "5" }, new decimal[] { 0.5M, 0.50M, 0.75M, 1, 1.25M, 1.50M, 1.75M, 2 }, "Levl7", 7, 5);
            campaignRewarder.ModifySpinWheel(new string[] { "0.25", "0.25", "0.5", "0.5", "1", "2", "3", "5" }, new decimal[] { 0.5M, 0.50M, 0.75M, 1, 1.25M, 1.50M, 1.75M, 2 }, "Levl8", 8, 5);
            campaignRewarder.ModifySpinWheel(new string[] { "0.25", "0.25", "0.5", "0.5", "1", "2", "3", "5" }, new decimal[] { 0.5M, 0.50M, 0.75M, 1, 1.25M, 1.50M, 1.75M, 2 }, "Levl9", 9, 5);
            campaignRewarder.ModifySpinWheel(new string[] { "0.25", "0.25", "0.5", "0.5", "1", "2", "3", "5" }, new decimal[] { 0.5M, 0.50M, 0.75M, 1, 1.25M, 1.50M, 1.75M, 2 }, "Levl10", 10, 5);

            Customer customer = company.GetOrCreateCustomerById("123456");
            Player player = company.Players.SearchPlayer(customer.Player.Id);

            var domain = company.Sales.DomainFrom("Test.com");
            streakCampaign.ApplyDomains(new List<int> { domain.Id });
            streakCampaign.ApplyStores(new List<int> { store.Id });

            bool playerIsAllowToBuy;
            playerIsAllowToBuy = company.Campaigns.AreApplicableTo(startDate, player, 1, 1, Currencies.CODES.USD);
            Assert.IsTrue(playerIsAllowToBuy);

            Assert.ThrowsException<GameEngineException>(() => {
                streakCampaign.RegisterPurchase(false, player, startDate.AddYears(-1), 0, 1, 1, 1, Currencies.CODES.USD);
            });

            playerIsAllowToBuy = company.Campaigns.AreApplicableTo(startDate.AddDays(1), player, 1, 1, Currencies.CODES.USD);
            Assert.IsTrue(playerIsAllowToBuy);

            playerIsAllowToBuy = company.Campaigns.AreApplicableTo(startDate.AddYears(1), player, 1, 1, Currencies.CODES.USD);
            Assert.IsFalse(playerIsAllowToBuy);
        }

        [TestMethod]
        public void StreakCampaign_AreApplicable_DiferentPlayer()
        {
            bool itIsThePresent = false;
            Company company = new Company();
            company.Sales.CreateDomain(false, 1, "http://Test.com", 1);
            var store = company.Sales.CreateStore(1, "Test Store");
            CurrenciesTest.AddCurrencies();
            store.MakeCurrent();

            const byte maxLevel = 11;
            DateTime ActualDate = DateTime.Now;
            DateTime startDate = ActualDate.AddDays(1);
            ActionerStreak streakCampaign = company.Campaigns.CreateStreakCampaign(itIsThePresent, 1, ActualDate, "Streak", Currencies.CODES.FP, startDate, ActualDate.AddDays(150), maxLevel, "test", RewarderType.SpinWheel, CampaignScaleType.DailyCounter, true, Currencies.CODES.USD);
            streakCampaign.LastDayToChangePrizes = ActualDate.AddDays(153);

            SpinWheelRewarder campaignRewarder = company.Campaigns.Rewarder(streakCampaign) as SpinWheelRewarder;
            campaignRewarder.ModifySpinWheel(new string[] { "0.25", "0.25", "0.5", "0.5", "1", "2", "3", "5" }, new decimal[] { 0.5M, 0.50M, 0.75M, 1, 1.25M, 1.50M, 1.75M, 2 }, "Levl0", 0, 1);
            campaignRewarder.ModifySpinWheel(new string[] { "0.25", "0.25", "0.5", "0.5", "1", "2", "3", "5" }, new decimal[] { 0.5M, 0.50M, 0.75M, 1, 1.25M, 1.50M, 1.75M, 2 }, "Levl1", 1, 5);
            campaignRewarder.ModifySpinWheel(new string[] { "0.25", "0.25", "0.5", "0.5", "1", "2", "3", "5" }, new decimal[] { 0.5M, 0.50M, 0.75M, 1, 1.25M, 1.50M, 1.75M, 2 }, "Levl2", 2, 5);
            campaignRewarder.ModifySpinWheel(new string[] { "0.25", "0.25", "0.5", "0.5", "1", "2", "3", "5" }, new decimal[] { 0.5M, 0.50M, 0.75M, 1, 1.25M, 1.50M, 1.75M, 2 }, "Levl3", 3, 5);
            campaignRewarder.ModifySpinWheel(new string[] { "0.25", "0.25", "0.5", "0.5", "1", "2", "3", "5" }, new decimal[] { 0.5M, 0.50M, 0.75M, 1, 1.25M, 1.50M, 1.75M, 2 }, "Levl4", 4, 5);
            campaignRewarder.ModifySpinWheel(new string[] { "0.25", "0.25", "0.5", "0.5", "1", "2", "3", "5" }, new decimal[] { 0.5M, 0.50M, 0.75M, 1, 1.25M, 1.50M, 1.75M, 2 }, "Levl5", 5, 5);
            campaignRewarder.ModifySpinWheel(new string[] { "0.25", "0.25", "0.5", "0.5", "1", "2", "3", "5" }, new decimal[] { 0.5M, 0.50M, 0.75M, 1, 1.25M, 1.50M, 1.75M, 2 }, "Levl6", 6, 5);
            campaignRewarder.ModifySpinWheel(new string[] { "0.25", "0.25", "0.5", "0.5", "1", "2", "3", "5" }, new decimal[] { 0.5M, 0.50M, 0.75M, 1, 1.25M, 1.50M, 1.75M, 2 }, "Levl7", 7, 5);
            campaignRewarder.ModifySpinWheel(new string[] { "0.25", "0.25", "0.5", "0.5", "1", "2", "3", "5" }, new decimal[] { 0.5M, 0.50M, 0.75M, 1, 1.25M, 1.50M, 1.75M, 2 }, "Levl8", 8, 5);
            campaignRewarder.ModifySpinWheel(new string[] { "0.25", "0.25", "0.5", "0.5", "1", "2", "3", "5" }, new decimal[] { 0.5M, 0.50M, 0.75M, 1, 1.25M, 1.50M, 1.75M, 2 }, "Levl9", 9, 5);
            campaignRewarder.ModifySpinWheel(new string[] { "0.25", "0.25", "0.5", "0.5", "1", "2", "3", "5" }, new decimal[] { 0.5M, 0.50M, 0.75M, 1, 1.25M, 1.50M, 1.75M, 2 }, "Levl10", 10, 5);

            Customer customer1 = company.GetOrCreateCustomerById("123456");
            Customer customer2 = company.GetOrCreateCustomerById("123457");
            Player player1 = company.Players.SearchPlayer(customer1.Player.Id);
            Player player2 = company.Players.SearchPlayer(customer2.Player.Id);

            var domain = company.Sales.DomainFrom("Test.com");
            streakCampaign.ApplyDomains(new List<int> { domain.Id });
            streakCampaign.ApplyStores(new List<int> { store.Id });

            bool playerIsAllowToBuy;
            playerIsAllowToBuy = company.Campaigns.AreApplicableTo(startDate, player1, 1, 1, Currencies.CODES.USD);
            Assert.IsTrue(playerIsAllowToBuy);

            playerIsAllowToBuy = company.Campaigns.AreApplicableTo(startDate, player2, 1, 1, Currencies.CODES.USD);
            Assert.IsTrue(playerIsAllowToBuy);

            streakCampaign.RegisterPurchase(false, player1, startDate, 0, 1, 1, 1, Currencies.CODES.USD);

            playerIsAllowToBuy = company.Campaigns.AreApplicableTo(startDate, player1, 1, 1, Currencies.CODES.USD);
            Assert.IsFalse(playerIsAllowToBuy);

            playerIsAllowToBuy = company.Campaigns.AreApplicableTo(startDate, player2, 1, 1, Currencies.CODES.USD);
            Assert.IsTrue(playerIsAllowToBuy);

            playerIsAllowToBuy = company.Campaigns.AreApplicableTo(startDate.AddDays(1), player2, 1, 1, Currencies.CODES.USD);
            Assert.IsTrue(playerIsAllowToBuy);

            playerIsAllowToBuy = company.Campaigns.AreApplicableTo(startDate.AddDays(1), player1, 1, 1, Currencies.CODES.USD);
            Assert.IsTrue(playerIsAllowToBuy);
        }

        [TestMethod]
        public void StreakCampaign_AreApplicable_DiferentDomain()
        {
            bool itIsThePresent = false;
            Company company = new Company();
            company.Sales.CreateDomain(false, 1, "http://Test.com", 1);
            company.Sales.CreateDomain(false, 2, "http://Test2.com", 1);
            var store = company.Sales.CreateStore(1, "Test Store");
            CurrenciesTest.AddCurrencies();
            store.MakeCurrent();

            const byte maxLevel = 11;
            DateTime ActualDate = DateTime.Now;
            DateTime startDate = ActualDate.AddDays(1);
            ActionerStreak streakCampaign = company.Campaigns.CreateStreakCampaign(itIsThePresent, 1, ActualDate, "Streak", Currencies.CODES.FP, startDate, ActualDate.AddDays(150), maxLevel, "test", RewarderType.SpinWheel, CampaignScaleType.DailyCounter, true, Currencies.CODES.USD);
            streakCampaign.LastDayToChangePrizes = ActualDate.AddDays(153);

            SpinWheelRewarder campaignRewarder = company.Campaigns.Rewarder(streakCampaign) as SpinWheelRewarder;
            campaignRewarder.ModifySpinWheel(new string[] { "0.25", "0.25", "0.5", "0.5", "1", "2", "3", "5" }, new decimal[] { 0.5M, 0.50M, 0.75M, 1, 1.25M, 1.50M, 1.75M, 2 }, "Levl0", 0, 1);
            campaignRewarder.ModifySpinWheel(new string[] { "0.25", "0.25", "0.5", "0.5", "1", "2", "3", "5" }, new decimal[] { 0.5M, 0.50M, 0.75M, 1, 1.25M, 1.50M, 1.75M, 2 }, "Levl1", 1, 5);
            campaignRewarder.ModifySpinWheel(new string[] { "0.25", "0.25", "0.5", "0.5", "1", "2", "3", "5" }, new decimal[] { 0.5M, 0.50M, 0.75M, 1, 1.25M, 1.50M, 1.75M, 2 }, "Levl2", 2, 5);
            campaignRewarder.ModifySpinWheel(new string[] { "0.25", "0.25", "0.5", "0.5", "1", "2", "3", "5" }, new decimal[] { 0.5M, 0.50M, 0.75M, 1, 1.25M, 1.50M, 1.75M, 2 }, "Levl3", 3, 5);
            campaignRewarder.ModifySpinWheel(new string[] { "0.25", "0.25", "0.5", "0.5", "1", "2", "3", "5" }, new decimal[] { 0.5M, 0.50M, 0.75M, 1, 1.25M, 1.50M, 1.75M, 2 }, "Levl4", 4, 5);
            campaignRewarder.ModifySpinWheel(new string[] { "0.25", "0.25", "0.5", "0.5", "1", "2", "3", "5" }, new decimal[] { 0.5M, 0.50M, 0.75M, 1, 1.25M, 1.50M, 1.75M, 2 }, "Levl5", 5, 5);
            campaignRewarder.ModifySpinWheel(new string[] { "0.25", "0.25", "0.5", "0.5", "1", "2", "3", "5" }, new decimal[] { 0.5M, 0.50M, 0.75M, 1, 1.25M, 1.50M, 1.75M, 2 }, "Levl6", 6, 5);
            campaignRewarder.ModifySpinWheel(new string[] { "0.25", "0.25", "0.5", "0.5", "1", "2", "3", "5" }, new decimal[] { 0.5M, 0.50M, 0.75M, 1, 1.25M, 1.50M, 1.75M, 2 }, "Levl7", 7, 5);
            campaignRewarder.ModifySpinWheel(new string[] { "0.25", "0.25", "0.5", "0.5", "1", "2", "3", "5" }, new decimal[] { 0.5M, 0.50M, 0.75M, 1, 1.25M, 1.50M, 1.75M, 2 }, "Levl8", 8, 5);
            campaignRewarder.ModifySpinWheel(new string[] { "0.25", "0.25", "0.5", "0.5", "1", "2", "3", "5" }, new decimal[] { 0.5M, 0.50M, 0.75M, 1, 1.25M, 1.50M, 1.75M, 2 }, "Levl9", 9, 5);
            campaignRewarder.ModifySpinWheel(new string[] { "0.25", "0.25", "0.5", "0.5", "1", "2", "3", "5" }, new decimal[] { 0.5M, 0.50M, 0.75M, 1, 1.25M, 1.50M, 1.75M, 2 }, "Levl10", 10, 5);

            Customer customer = company.GetOrCreateCustomerById("123456");
            Player player = company.Players.SearchPlayer(customer.Player.Id);
            var domain = company.Sales.DomainFrom("Test.com");
            streakCampaign.ApplyDomains(new List<int> { domain.Id });
            streakCampaign.ApplyStores(new List<int> { store.Id });

            bool playerIsAllowToBuy;
            playerIsAllowToBuy = company.Campaigns.AreApplicableTo(startDate, player, domain.Id, store.Id, Currencies.CODES.USD);
            Assert.IsTrue(playerIsAllowToBuy);

            streakCampaign.RegisterPurchase(false, player, startDate, 0, 1, 1, 1, Currencies.CODES.USD);

            playerIsAllowToBuy = company.Campaigns.AreApplicableTo(startDate, player, 2, store.Id, Currencies.CODES.USD);
            Assert.IsFalse(playerIsAllowToBuy);
        }

        [TestMethod]
        public void StreakCampaign_AreApplicable_TZ()
        {
            CountryTimeZone tzEST = new CountryTimeZone { CountryCode = "ZZ", Windows = "Eastern Standard Time", Linux = "EST5EDT" };
            CountryTimeZone tzCR = new CountryTimeZone { CountryCode = "CR", Windows = "Central Standard Time", Linux = "America/Costa_Rica" };
            TimeZoneConverter.Instance.Configure(new List<CountryTimeZone> { tzEST, tzCR });

            bool itIsThePresent = false;
            Company company = new Company();
            company.TimeZone = tzCR.CountryCode;
            company.Sales.CreateDomain(false, 1, "http://Test.com", 1);
            company.Sales.CreateDomain(false, 2, "http://Test2.com", 1);
            var store = company.Sales.CreateStore(1, "Test Store");
            CurrenciesTest.AddCurrencies();
            store.MakeCurrent();

            const byte maxLevel = 11;
            DateTime creationDate = new DateTime(2024, 08, 21, 00, 00, 00);
            DateTime startDateCampaign = new DateTime(2024, 08, 21, 00, 00, 00);
            DateTime endDateCampaign = new DateTime(2024, 08, 25, 00, 0, 0);
            ActionerStreak streakCampaign = company.Campaigns.CreateStreakCampaign(itIsThePresent, 1, creationDate, "Streak", Currencies.CODES.FP, startDateCampaign, endDateCampaign, maxLevel, "test", RewarderType.SpinWheel, CampaignScaleType.DailyCounter, true, Currencies.CODES.USD, tzEST.CountryCode);
            streakCampaign.LastDayToChangePrizes = new DateTime(2024, 08, 26, 23, 59, 59);

            SpinWheelRewarder campaignRewarder = company.Campaigns.Rewarder(streakCampaign) as SpinWheelRewarder;
            campaignRewarder.ModifySpinWheel(new string[] { "0.25", "0.25", "0.5", "0.5", "1", "2", "3", "5" }, new decimal[] { 0.5M, 0.50M, 0.75M, 1, 1.25M, 1.50M, 1.75M, 2 }, "Levl0", 0, 1);
            campaignRewarder.ModifySpinWheel(new string[] { "0.25", "0.25", "0.5", "0.5", "1", "2", "3", "5" }, new decimal[] { 0.5M, 0.50M, 0.75M, 1, 1.25M, 1.50M, 1.75M, 2 }, "Levl1", 1, 5);
            campaignRewarder.ModifySpinWheel(new string[] { "0.25", "0.25", "0.5", "0.5", "1", "2", "3", "5" }, new decimal[] { 0.5M, 0.50M, 0.75M, 1, 1.25M, 1.50M, 1.75M, 2 }, "Levl2", 2, 5);
            campaignRewarder.ModifySpinWheel(new string[] { "0.25", "0.25", "0.5", "0.5", "1", "2", "3", "5" }, new decimal[] { 0.5M, 0.50M, 0.75M, 1, 1.25M, 1.50M, 1.75M, 2 }, "Levl3", 3, 5);
            campaignRewarder.ModifySpinWheel(new string[] { "0.25", "0.25", "0.5", "0.5", "1", "2", "3", "5" }, new decimal[] { 0.5M, 0.50M, 0.75M, 1, 1.25M, 1.50M, 1.75M, 2 }, "Levl4", 4, 5);
            campaignRewarder.ModifySpinWheel(new string[] { "0.25", "0.25", "0.5", "0.5", "1", "2", "3", "5" }, new decimal[] { 0.5M, 0.50M, 0.75M, 1, 1.25M, 1.50M, 1.75M, 2 }, "Levl5", 5, 5);
            campaignRewarder.ModifySpinWheel(new string[] { "0.25", "0.25", "0.5", "0.5", "1", "2", "3", "5" }, new decimal[] { 0.5M, 0.50M, 0.75M, 1, 1.25M, 1.50M, 1.75M, 2 }, "Levl6", 6, 5);
            campaignRewarder.ModifySpinWheel(new string[] { "0.25", "0.25", "0.5", "0.5", "1", "2", "3", "5" }, new decimal[] { 0.5M, 0.50M, 0.75M, 1, 1.25M, 1.50M, 1.75M, 2 }, "Levl7", 7, 5);
            campaignRewarder.ModifySpinWheel(new string[] { "0.25", "0.25", "0.5", "0.5", "1", "2", "3", "5" }, new decimal[] { 0.5M, 0.50M, 0.75M, 1, 1.25M, 1.50M, 1.75M, 2 }, "Levl8", 8, 5);
            campaignRewarder.ModifySpinWheel(new string[] { "0.25", "0.25", "0.5", "0.5", "1", "2", "3", "5" }, new decimal[] { 0.5M, 0.50M, 0.75M, 1, 1.25M, 1.50M, 1.75M, 2 }, "Levl9", 9, 5);
            campaignRewarder.ModifySpinWheel(new string[] { "0.25", "0.25", "0.5", "0.5", "1", "2", "3", "5" }, new decimal[] { 0.5M, 0.50M, 0.75M, 1, 1.25M, 1.50M, 1.75M, 2 }, "Levl10", 10, 5);

            Customer customer = company.GetOrCreateCustomerById("123456");
            Player player = company.Players.SearchPlayer(customer.Player.Id);
            var domain = company.Sales.DomainFrom("Test.com");
            streakCampaign.ApplyDomains(new List<int> { domain.Id });
            streakCampaign.ApplyStores(new List<int> { store.Id });

            DateTime serverTime = new DateTime(2024, 08, 20, 22, 00, 00);
            IEnumerable<CampaignBase> playerCampaigns = company.Campaigns.ActivePlayerSaleCampaigns(serverTime, player, domain.Id, store.Id);
            Assert.IsTrue(playerCampaigns.Count() == 0);

            serverTime = new DateTime(2024, 08, 20, 22, 00, 00);
            Assert.ThrowsException<GameEngineException>(() =>
            {
                streakCampaign.RegisterPurchase(false, player, serverTime, 10, store.Id, domain.Id, 1, Currencies.CODES.USD);
            });

            serverTime = new DateTime(2024, 08, 20, 23, 00, 00);
            streakCampaign.RegisterPurchase(false, player, serverTime, 10, store.Id, domain.Id, 1, Currencies.CODES.USD);
            playerCampaigns = company.Campaigns.ActivePlayerSaleCampaigns(serverTime, player, domain.Id, store.Id);
            Assert.IsTrue(playerCampaigns.Count() == 1);
        }

        [TestMethod]
        public void GiftCardCampaign_StartDate_EndDate()
        {
            //Integration.UseKafka = true;
            QueueInMemory queue = new QueueInMemory();
            Integration.Kafka = queue;

            Company company = new Company();
            var store = company.Sales.CreateStore(1, "Test Store");
            CurrenciesTest.AddCurrencies();
            store.MakeCurrent();

            DateTime now = new DateTime(2024, 11, 21, 00, 00, 00);
            DateTime startDate = new DateTime(2024, 12, 01, 00, 00, 00);
            DateTime dateBetweenStartAndEndDate = new DateTime(2024, 12, 14, 00, 00, 00);
            DateTime endDate = new DateTime(2024, 12, 31, 23, 59, 59);
            DateTime dateAfterEndDate = new DateTime(2025, 01, 01, 00, 00, 00);

            var manualLottoReward = company.Campaigns.CreateGiftCardCampaign(itIsThePresent: true, promotionNumber: 1, creationDate: now, name: "Manual", startDate: startDate, endDate: endDate, Currencies.CODES.FP.ToString(), 1000m);
            Customer customer = company.GetOrCreateCustomerById("123456");
            decimal amountToGive = 100m;

            Assert.AreEqual(1, manualLottoReward.PromotionNumber);
            Assert.IsFalse(manualLottoReward.IsUnlimitedBudget);
            Assert.IsTrue(manualLottoReward.IsEnabled);
            Assert.AreEqual(1000m, manualLottoReward.InitialBudget);

            Assert.IsFalse(manualLottoReward.IsActive(now));
            Assert.ThrowsException<GameEngineException>(() =>
            {
                manualLottoReward.Give(true, customer, now, amountToGive, "Award 1", "N/A", Currencies.CODES.FP.ToString(), store);
            });

            Assert.IsTrue(manualLottoReward.IsActive(startDate));
            manualLottoReward.Give(true, customer, startDate, amountToGive, "Award 1", "N/A", Currencies.CODES.FP.ToString(), store);
            Assert.AreEqual(900m, manualLottoReward.ActualBudget);
            Assert.AreEqual(100m, manualLottoReward.GivenBudget);

            Assert.IsTrue(manualLottoReward.IsActive(dateBetweenStartAndEndDate));
            manualLottoReward.Give(true, customer, dateBetweenStartAndEndDate, amountToGive, "Award 1", "N/A", Currencies.CODES.FP.ToString(), store);
            Assert.AreEqual(800m, manualLottoReward.ActualBudget);
            Assert.AreEqual(200m, manualLottoReward.GivenBudget);

            manualLottoReward.IsEnabled = false;
            Assert.IsFalse(manualLottoReward.IsActive(dateBetweenStartAndEndDate));

            Assert.ThrowsException<GameEngineException>(() =>
            {
                manualLottoReward.Give(true, customer, dateBetweenStartAndEndDate, amountToGive, "Award 1", "N/A", Currencies.CODES.FP.ToString(), store);
            });

            manualLottoReward.IsEnabled = true;
            Assert.IsTrue(manualLottoReward.IsActive(dateBetweenStartAndEndDate));

            Assert.IsTrue(manualLottoReward.IsActive(endDate));
            manualLottoReward.Give(true, customer, endDate, amountToGive, "Award 1", "N/A", Currencies.CODES.FP.ToString(), store);
            Assert.AreEqual(700m, manualLottoReward.ActualBudget);
            Assert.AreEqual(300m, manualLottoReward.GivenBudget);

            Assert.IsFalse(manualLottoReward.IsActive(dateAfterEndDate));
            Assert.ThrowsException<GameEngineException>(() =>
            {
                manualLottoReward.Give(true, customer, dateAfterEndDate, amountToGive, "Award 1", "N/A", Currencies.CODES.FP.ToString(), store);
            });

            DateTime newEndDate = new DateTime(2025, 01, 31, 00, 00, 00);
            manualLottoReward.EndDate = newEndDate;
            Assert.IsTrue(manualLottoReward.IsActive(dateAfterEndDate));
        }

        [TestMethod]
        public void SpinKinkCampaign_Test()
        {
            QueueInMemory queue = new QueueInMemory();
            Integration.Kafka = queue;

            Company company = new Company();
            var store = company.Sales.CreateStore(1, "Test Store");
            store.MakeCurrent();
            Domain domain1 = company.Sales.CreateDomain(false, 1, "http://Test.com", 1);
            Domain domain2 = company.Sales.CreateDomain(false, 2, "http://Test2.com", 1);
            CurrenciesTest.AddCurrencies();
            if (!company.System.Coins.ExistsIsoCode(SpinKick.CURRENCY_CODE)) company.System.Coins.Add(8, SpinKick.CURRENCY_CODE, SpinKick.CURRENCY_CODE, 0, "s", "On The Spot Reward", CoinType.Digital);

            DateTime now = new DateTime(2024, 11, 30, 00, 00, 00);
            DateTime startDate = new DateTime(2024, 12, 01, 00, 00, 00);
            DateTime endDate = new DateTime(2024, 12, 25, 23, 59, 59);
            DateTime lastDateToChangePrize = new DateTime(2024, 12, 31, 23, 59, 59);
            Currencies.CODES returnedCurrency = Currencies.CODES.FP;
            OnTheSpot campaignOnTheSpot = company.Campaigns.CreateSpotRewardCampaign(itIsThePresent: true, creationDate: now, promotionNumber: 1, name: "SpinKick", descripcion: "description", startDate: startDate, endDate: endDate, lastDateToChangePrize: lastDateToChangePrize, returnedCurrency: returnedCurrency, timeZone: CountryTimeZone.DEFAULT.CountryCode);

            campaignOnTheSpot.ApplyDomains(new List<int> { 1, 2 });
            campaignOnTheSpot.ApplyStores(new List<int> { 1 });

            SpinWheelRewarder campaignRewarder = campaignOnTheSpot.Rewarder as SpinWheelRewarder;
            campaignRewarder.ModifySpinWheel(new string[] { "0.25", "0.25", "0.5", "0.5", "1", "2", "3", "5" }, new decimal[] { 0.5M, 0.50M, 0.75M, 1, 1.25M, 1.50M, 1.75M, 2 }, "Levl0", 0, 1);

            Assert.IsTrue(campaignOnTheSpot.IsEnabled);
            Assert.IsFalse(campaignOnTheSpot.IsActive(now));

            IEnumerable <Promotion> campaigns = company.Campaigns.List();
            Assert.AreEqual(1, campaigns.Count());

            now = new DateTime(2024, 12, 01, 00, 00, 00);
            Assert.IsTrue(campaignOnTheSpot.IsActive(now));

            SaleCampaign saleCampaign = company.Campaigns.SaleCampaign(campaignOnTheSpot.Id);
            SpinKick onTheSpotCampaign = company.Campaigns.CreateSpinKickCampaign(itIsThePresent: true, createdDate: now, promotionNumber: 2, name: "On The Spot", descripcion: "desc", totalRewardUnits: 50, saleCampaign);

            campaigns = company.Campaigns.List();
            Assert.AreEqual(2, campaigns.Count());

            Customer customer = company.GetOrCreateCustomerById("123456");
            Player player = customer.Player;

            var spins = campaignRewarder.AvailableSpins(player);
            Assert.IsTrue(spins.Count() == 0);

            var activeCampaigns = company.Campaigns.ActivePlayerSaleCampaigns(now, player, domainID: 1, storeID: 1);
            Assert.IsTrue(activeCampaigns.Count() == 0);

            onTheSpotCampaign.AssignPrize(itIsThePresent: true, now: now, player, description: "text description", storeId: 1);

            spins = campaignRewarder.AvailableSpins(player);
            Assert.IsTrue(spins.Count() == 1);

            activeCampaigns = company.Campaigns.ActivePlayerSaleCampaigns(now, player, domainID: 1, storeID: 1);
            Assert.IsTrue(activeCampaigns.Count() == 1);

            int playerLevel = campaignOnTheSpot.PlayerLevel(player);
            int randomSelection = campaignRewarder.GenerateRamdomSection(playerLevel);
            var spinResult = campaignRewarder.Spin(itIsThePresent: true, now: now, playerLevel: playerLevel, player, index: randomSelection, who: "who", storeId: 1, domainId: 1);
            Assert.IsTrue(spinResult.PrizeIndex == randomSelection);
            Assert.IsTrue(spinResult.PrizeValue > 0);

            spins = campaignRewarder.AvailableSpins(player);
            Assert.IsTrue(spins.Count() == 0);

            activeCampaigns = company.Campaigns.ActivePlayerSaleCampaigns(now, player, domainID: 1, storeID: 1);
            Assert.IsTrue(activeCampaigns.Count() == 0);
        }
    }
}
