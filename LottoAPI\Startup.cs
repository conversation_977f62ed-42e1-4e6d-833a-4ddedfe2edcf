﻿using ExternalServices;
using GamesEngine.Loyalty;
using GamesEngine.Settings;
using GamesEngineMocks;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.HttpOverrides;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Puppeteer.EventSourcing;
using System;
using System.Threading;
using Microsoft.Extensions.Hosting;
using LottoAPI.Controllers;
using Elastic.Apm.AspNetCore;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;

using Microsoft.OpenApi.Models;
using GamesEngine;
using GamesEngine.Business;
using GamesEngine.MessageQueuing;
using GamesEngine.RealTime;
using GamesEngine.RealTime.Events;
using System.Text.Json;
using GamesEngine.Time;
using System.Reflection.Metadata.Ecma335;

namespace LottoAPI
{
    public class Startup
    {
        public Startup(IWebHostEnvironment env, IConfiguration configuration)
        {
            var builder = new ConfigurationBuilder()
                .SetBasePath(env.ContentRootPath)
                .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true)
                .AddJsonFile("customization/appsettings.accounting.json", optional: true)
                .AddJsonFile("customization/appsettings.security.json", optional: true)
                .AddJsonFile("customization/appsettings.kafka.json", optional: true)
                .AddJsonFile("customization/appsettings.error_emails.json", optional: true)
                .AddJsonFile("customization/appsettings.apm.json", optional: true)
                .AddJsonFile("secrets/appsettings.secrets.json", optional: true);
            Configuration = builder.Build();
            Environment = env;
        }

        public IConfiguration Configuration { get; }
        public IWebHostEnvironment Environment { get; }

        // This method gets called by the runtime. Use this method to add services to the container.
        public void ConfigureServices(IServiceCollection services)
        {
            int minWorker, minIOC;
            ThreadPool.GetMinThreads(out minWorker, out minIOC);
            int maxWorker, maxIOC;
            ThreadPool.GetMaxThreads(out maxWorker, out maxIOC);

            Console.WriteLine($@" minWorker:{minWorker} minIOC:{minIOC} maxWorker:{maxWorker} maxIOC:{maxIOC}");

            services.AddSwaggerGen(c =>
            {
                c.SwaggerDoc("v1", new OpenApiInfo { Title = "Lotto API", Version = "v1" });
            });

            /*Security*/
            Security.Configure(services, Configuration);
            /*Security*/

            services.AddMvc(options => options.EnableEndpointRouting = false).AddNewtonsoftJson();

            Settings.WebProfanityKey = Configuration.GetValue<string>("WebProfanityKey");
            Settings.WebProfanityUrl = Configuration.GetValue<string>("WebProfanityUrl");

            //if (string.IsNullOrEmpty(Configuration.GetValue<string>("CashierUrl"))) throw new Exception("CashierUrl its requeried in the appsettings.");
            //string cashierUrl = Configuration.GetValue<string>("CashierUrl");
            //CashierDriver.ConfigureUrl(cashierUrl);

            ErrorsSender.Configure(Configuration.GetSection("ErrorsSender"), Environment.IsProduction());

            var biIntegration = Configuration.GetSection("BIIntegration");

            Console.WriteLine($"biIntegration {biIntegration}");

            Integration.Configure(KafkaMessage.Prefix.withLottoPrefix, biIntegration);

            Console.WriteLine($"Integration is ready.");

            if (Integration.UseDb && Integration.UseKafka)
            {
                throw new Exception("This project can use kafka or database but not both.");
            }
            Integration.ConfigureAPM(Configuration);

            var messageOriginId = Configuration.GetValue<string>("MessageOriginId");
            if (string.IsNullOrEmpty(messageOriginId) && !int.TryParse(messageOriginId, out _)) throw new Exception("MessageOriginId its requeried in the appsettings.");
            MessageCatalog.AssingMessageOriginId(Convert.ToInt32(messageOriginId));
        }

        // This method gets called by the runtime. Use this method to configure the HTTP request pipeline.
        public void Configure(IApplicationBuilder app, IWebHostEnvironment env)
        {
            if (Integration.UseAPM)
            {
                app.UseElasticApm(Configuration);
            }
            //if (env.IsDevelopment())
            //{
                app.UseDeveloperExceptionPage();
                app.UseSwagger();
                app.UseSwaggerUI(c =>
                {
                    if (LottoAPI.Actor.IsKenoActor())
                    {
                        c.SwaggerEndpoint("/swagger/v1/swagger.json", "Keno API V1");
                    }
                    else                  
                    {
                        c.SwaggerEndpoint("/swagger/v1/swagger.json", "Lotto API V1");
                    }

                    
                    c.RoutePrefix = string.Empty;
                });
            //}

            TimeZoneConverter.Instance.Configure(Configuration);

            var sectionDairy = Configuration.GetSection("DBDairy");
            Integration.MySQL  = sectionDairy.GetSection("ConnectionStrings").GetValue<string>("MySQL");
            Integration.SqlServer = sectionDairy.GetSection("ConnectionStrings").GetValue<string>("SQLServer");
            Integration.DbSelected = sectionDairy.GetValue<string>("DBSelected");
            var scriptBeforeRecovering = Configuration.GetValue<string>("ActionsBeforeRecovering");
            var sectionAccounting = Configuration.GetSection("Accounting");
            var isFollower = Configuration.GetValue<bool>("IsFollower");
            AccountingSettings.NeedsUniqueIdentifierForPaymentHub = sectionAccounting.GetValue<string>("NeedsUniqueIdentifierForPaymentHub");

            if (!isFollower)
            {
                if (Integration.DbSelected == DatabaseType.MySQL.ToString())
                {
                    LottoAPI.Actor.EventSourcingStorage(DatabaseType.MySQL, Integration.MySQL, scriptBeforeRecovering, AccountingSettings.NeedsUniqueIdentifierForPaymentHub);
                }
                else if (Integration.DbSelected == DatabaseType.SQLServer.ToString())
                {
                    LottoAPI.Actor.EventSourcingStorage(DatabaseType.SQLServer, Integration.SqlServer, scriptBeforeRecovering, AccountingSettings.NeedsUniqueIdentifierForPaymentHub);
                }
                else if (!String.IsNullOrWhiteSpace(Integration.DbSelected))
                {
                    throw new Exception($"There is no connection for {Integration.DbSelected}");
                }
                else
                {
                    Integration.Kafka.OffSetResetToLatest();
                    int numberOfTheMockConfigured = Convert.ToInt32(Configuration["MockToStart"]);

                    var DOTNET_RUNNING_IN_CONTAINER = bool.Parse(System.Environment.GetEnvironmentVariable("DOTNET_RUNNING_IN_CONTAINER") ?? "false");
                    if (DOTNET_RUNNING_IN_CONTAINER)
                        LottoAPI.Actor.EventSourcingStorage(DatabaseType.MySQL, Integration.MySQL, scriptBeforeRecovering, AccountingSettings.NeedsUniqueIdentifierForPaymentHub);
                    RunMock(LottoAPI.Actor, numberOfTheMockConfigured);
                }
            }

            /*Security*/
            app.UseAuthentication();
            /*Security*/
            app.UseMvc();

            ForwardedHeadersOptions options = new ForwardedHeadersOptions();
            options.ForwardedHeaders = ForwardedHeaders.XForwardedFor;
            app.UseForwardedHeaders(options);

            /*Consumer must start ones the actor already recover the state to avoid race conditions.*/
            if (!isFollower)
            {
                var tenantAndStore = Integration.GetCurrentTenantAndStore(LottoAPI.Actor);
                if (Integration.UseKafka)
                {
                    var controller = new DrawController();
                    controller.CreateConsumerForTopics();
                    if (tenantAndStore.isAlreadyRegisteredTenantAndStore)
                    {
                        controller.CreateConsumerForTopics(Integration.CurrentTenantName, tenantAndStore.storeId);
                    }
                }
                PlatformMonitor.GetInstance().CloudMessage = CloudMessage;
                var sectionSolana = Configuration.GetSection("SmartContracts").GetSection("Solana");
                SolanaSmartContractsIntegration solanaSmartContracts = new SolanaSmartContractsIntegration(sectionSolana);
                Integration.SmartContractsIntegration = solanaSmartContracts;

            
                if (LottoAPI.Actor.IsKenoActor())
                {
                    var now = DateTime.Now;
                    var stringDate = $"{now.Month}/{now.Day}/{now.Year} {now.Hour}:{now.Minute}:{now.Second}";

                    var result1 = LottoAPI.Actor.PerformQry($@"
                    {{
                        print Now currentDateTime;
                        schedules=keno.PendingAndNoRegradedSchedulesAtForKeno({stringDate});
                        for (scheduledLotteries:schedules)
                        {{
                            lottery=scheduledLotteries.Lottery;
                            print scheduledLotteries.IdPrefix drawId;
                            print scheduledLotteries.GetDescription() description;
                            drawDate = scheduledLotteries.DrawDate;
                            print drawDate.HHmmAMPM() scheduledHour;
                            print drawDate 'drawDate';
                            print lottery.GameType() gameType;
                            print lottery.IsEnabledDraw(drawDate, domain) isEnabled;
                        }}
                    }}
                    ");

                    if (!(result1 is OkObjectResult)) throw new GameEngineException("Pending draws can not be retrieved.");
                    OkObjectResult o = (OkObjectResult)result1;
                    string json = o.Value.ToString();
                    DrawsStatus drawsStatus = JsonConvert.DeserializeObject<DrawsStatus>(json);

                    _ = solanaSmartContracts.ExecutePendingDrawForAsync(now, drawsStatus);
                    _ = solanaSmartContracts.SetupJobForDrawGradingAsync(now, drawsStatus);
                }

                var result = LottoAPI.Actor.PerformQry($@"
                    {{
                        success = company.System.PaymentProcessor.Load(false, Now, customSettings);
                        print success success;
                    }}
                    ");
                if (!(result is OkObjectResult)) throw new GameEngineException("Processor were not loaded;");

                if (!tenantAndStore.isAlreadyRegisteredTenantAndStore)
                {
                    result = LottoAPI.Actor.PerformCmd($@"
                    {{
                        company.RegisterTenantAndStore(itIsThePresent, '{Integration.CurrentTenantName}', customSettings);
                    }}
                    ");
                    if (!(result is OkObjectResult)) throw new GameEngineException("Tenant and store cannot be registered");
                }
            }
            
        }

		public void CloudMessage(WebSocketMessage message)
        {
            switch (message.EventType)
            {
                case PlatformEventType.GAME_QUITTED:
                    Console.WriteLine($"Data: {message.Data}");
                    var data = JsonConvert.DeserializeObject<GameQuittedEvent>(((JsonElement)message.Data).GetRawText());
                    if (!Integration.Localization.ToString().Equals(data.Localization, StringComparison.OrdinalIgnoreCase)) break;
                    Console.WriteLine($"Parsed: {data}");
                    var result = LottoAPI.Actor.PerformQry($@"
                    {{
                        domain = company.Sales.DomainFrom({data.DomainId});
                        player = company.Players.SearchPlayer('{data.PlayerId}');
                        risk = lotto900.Risks.Risk;
                        risk.ForgetPreviousTracking({data.Number.Length}, player);
                    }}
                    ");
                    if (!(result is OkObjectResult)) throw new Exception(((ObjectResult)result).ToString());
                    break;
            }
        }

        void RunMock(RestAPIActorAsync actor, int index = -1)
        {
            if (actor.IsKenoActor())
            {
                switch (index)
                {
                    case 0:
                        KenoMocks.BasicKeno(actor.Actor);
                        break;
                    case 1:
                        KenoMocks.PendingKenoPurchases(actor.Actor);
                        break;
                    default:
                        KenoMocks.BasicKeno(actor.Actor);
                        break;
                }
            }
            else 
            {
                switch (index)
                {
                    case 0:
                        LottoMocks.NoPurchases(actor.Actor);
                        break;

                    case 1:
                        LottoMocks.FewDrawsCreated(actor.Actor);
                        break;

                    case 2:
                        LottoMocks.ManyCustomersBuying(actor.Actor);
                        break;
                    case 3:
                        LottoMocks.ManyDrawsOff(actor.Actor);
                        break;

                    case 4:
                        LottoMocks.Ticketsbuyed(actor.Actor,75000, 2, DateTime.Now);
                        break;

                    case 5:
                        LottoMocks.PendingPurchases(actor.Actor);
                        break;
                    case 6:
                        LottoMocks.BuySomeLottosAndDraw(actor.Actor);
                        break;
                    case 7:
                        LottoMocks.FavoritesCases(actor.Actor);
                        break;
                    case 8:
                        LottoMocks.ScheduleDrawsOfPick2andPick5(actor.Actor);
                        break;
                    case 9:
                        LottoMocks.ciclo(actor.Actor);
                        break;
                    case 10:
                        LottoMocks.PBDrawConfirmation(actor.Actor);
                        break;
                    case 11:
                        LottoMocks.Pick5Purchases(actor.Actor);
                        break;
                    default:
                        LottoMocks.BuyManyLottosAndDraw(actor.Actor);
                        break;
                }
            }

        }

        
    }
}
