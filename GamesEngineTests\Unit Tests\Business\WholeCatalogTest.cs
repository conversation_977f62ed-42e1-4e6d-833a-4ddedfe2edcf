﻿using GamesEngine.Business;
using GamesEngine.Exchange;
using GamesEngine.Finance;
using GamesEngine.MessageQueuing;
using GamesEngine.Settings;
using GamesEngineTests.Custodian;
using GamesEngineTests.Unit_Tests.Games.Lotto;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;
using town.connectors.drivers;
using Unit.Games.Tools;
using static GamesEngine.Business.WholePaymentMethods;
using static GamesEngine.Business.WholePaymentProcessor;
using static GamesEngine.Finance.PaymentChannels;

namespace GamesEngineTests.Unit_Tests.Business
{
	[TestClass]
	public class WholeCatalogTest
	{
		[TestMethod]
		public void PaymentProcessorsCount()
		{
			CurrenciesTest.AddCurrencies();
			var company = new Company();
			GuardianTest.LoadProcessors(company);

			Assert.AreEqual(10, company.System.PaymentProcessor.Count());
			Assert.AreEqual(10, company.System.PaymentProcessor.VisibleMembers.Count());
			Assert.AreEqual(9, company.System.PaymentProcessor.EnabledMembers.Count());
		}

		[TestMethod]
		public void SearchProcessor()
		{
			CurrenciesTest.AddCurrencies();
			var company = new Company();
			company.Sales.CreateStore(1, "Test Store").MakeCurrent();
			var domain = company.Sales.CreateDomain(false, 1, "localhost", PaymentChannels.Agents.INSIDER);
			company.Sales.CurrentStore.Add(domain);
			GuardianTest.LoadProcessors(company);

			var entityId = 5;
			var paymentMethod = company.System.PaymentProcessor.SearchPaymentMethodByName(PaymentMethod.Cash.ToString());
			var transactionDeposit = company.System.PaymentProcessor.SearchTransactionByName(TransactionType.Deposit.ToString());
			var coin = (ProcessorCoin)company.System.Coins.Find("dollar");
			company.System.PaymentProcessor.SearchProcessor(entityId, paymentMethod.Id, transactionDeposit.Id, coin.Iso4217Code, out PaymentProcessor processorDeposit);
			Assert.IsNotNull(processorDeposit);

			var transactionWithdrawal = company.System.PaymentProcessor.SearchTransactionByName(TransactionType.Withdrawal.ToString());
			company.System.PaymentProcessor.SearchProcessor(entityId, paymentMethod.Id, transactionWithdrawal.Id, coin.Iso4217Code, out PaymentProcessor processorWithdrawal);
			Assert.IsNotNull(processorWithdrawal);
		}

		[TestMethod]
		public void SearchProcessors()
		{
			CurrenciesTest.AddCurrencies();
			var company = new Company();
			company.Sales.CreateStore(1, "Test Store").MakeCurrent();
			var domain = company.Sales.CreateDomain(false, 1, "localhost", PaymentChannels.Agents.INSIDER);
			company.Sales.CurrentStore.Add(domain);
			GuardianTest.LoadProcessors(company);

			var entityId = 5;
			var paymentMethod = company.System.PaymentProcessor.SearchPaymentMethodByName(PaymentMethod.Cash.ToString());
			var transactionDeposit = company.System.PaymentProcessor.SearchTransactionByName(TransactionType.Deposit.ToString());
			var coin = (ProcessorCoin)company.System.Coins.Find("dollar");
			var processors = company.System.PaymentProcessor.SearchProcessors(entityId, paymentMethod.Id, transactionDeposit.Id, coin.Id);
			Assert.AreEqual(1, processors.Count());
		}

		[TestMethod]
		public void SearchProcessorsWithDistinctKey()
		{
			CurrenciesTest.AddCurrencies();
			var company = new Company();
			company.Sales.CreateStore(1, "Test Store").MakeCurrent();
			var domain = company.Sales.CreateDomain(false, 1, "localhost", PaymentChannels.Agents.INSIDER);
			company.Sales.CurrentStore.Add(domain);
			GuardianTest.LoadProcessors(company);

			var processors = company.System.PaymentProcessor.SearchProcessorsWithDistinctKey();
			Assert.AreEqual(9, processors.Count());
		}

		[TestMethod]
		public void ReorderEntities()
		{
			CurrenciesTest.AddCurrencies();
			var company = new Company();
			company.Sales.CreateStore(1, "Test Store").MakeCurrent();
			var domain = company.Sales.CreateDomain(false, 1, "localhost", PaymentChannels.Agents.INSIDER);
			company.Sales.CurrentStore.Add(domain);
			GuardianTest.LoadProcessors(company);

			var coin = (ProcessorCoin)company.System.Coins.Find("dollar");
			var processorsForDeposit = company.System.PaymentProcessor.ProcessorsForDeposit(coin.Coin);

			var transactionDeposit = company.System.TransactionTypes.SearchByName<ProcessorTransaction>(TransactionType.Deposit.ToString());
			var transactionWithdrawal = company.System.TransactionTypes.SearchByName<ProcessorTransaction>(TransactionType.Withdrawal.ToString());

			var processors = company.System.PaymentProcessor.PaymentProcessorsAndActionsByDomains.SearchFor(coin.Coin, transactionDeposit);
			Assert.AreEqual(4, processors.Count());
			Assert.AreEqual(5, processors.First().Entity.Id);

			var entities = company.System.PaymentProcessor.Entities(transactionDeposit, coin);
			Assert.AreEqual(5, entities.ElementAt(0).Id);
			Assert.AreEqual(6, entities.ElementAt(1).Id);
			Assert.AreEqual(7, entities.ElementAt(2).Id);

			company.System.PaymentProcessor.ReorderEntities(transactionDeposit, coin, new List<int>() { 7, 6, 5 });
			entities = company.System.PaymentProcessor.Entities(transactionDeposit, coin);
			Assert.AreEqual(7, entities.ElementAt(0).Id);
			Assert.AreEqual(6, entities.ElementAt(1).Id);
			Assert.AreEqual(5, entities.ElementAt(2).Id);

			entities = company.System.PaymentProcessor.EntitiesPerformingDeposit(coin);
			Assert.AreEqual(7, entities.ElementAt(0).Id);
			Assert.AreEqual(6, entities.ElementAt(1).Id);
			Assert.AreEqual(5, entities.ElementAt(2).Id);

			entities = company.System.PaymentProcessor.Entities(transactionWithdrawal, coin);
			Assert.AreEqual(5, entities.ElementAt(0).Id);
			Assert.AreEqual(6, entities.ElementAt(1).Id);
			Assert.AreEqual(7, entities.ElementAt(2).Id);

			entities = company.System.PaymentProcessor.EntitiesPerformingWithdrawal(coin);
			Assert.AreEqual(5, entities.ElementAt(0).Id);
			Assert.AreEqual(6, entities.ElementAt(1).Id);
			Assert.AreEqual(7, entities.ElementAt(2).Id);

			processors = company.System.PaymentProcessor.PaymentProcessorsAndActionsByDomains.SearchFor(coin.Coin, transactionDeposit);
			Assert.AreEqual(4, processors.Count());
			Assert.AreEqual(7, processors.First().Entity.Id);
		}

		[TestMethod]
		public void EntitiesForCashAndCreditCard()
		{
			CurrenciesTest.AddCurrencies();
			var company = new Company();
			company.Sales.CreateStore(1, "Test Store").MakeCurrent();
			var domain = company.Sales.CreateDomain(false, 1, "localhost", PaymentChannels.Agents.INSIDER);
			company.Sales.CurrentStore.Add(domain);
			GuardianTest.LoadProcessors(company);

			var transactionDeposit = company.System.TransactionTypes.SearchByName<ProcessorTransaction>(TransactionType.Deposit.ToString());
			var transactionWithdrawal = company.System.TransactionTypes.SearchByName<ProcessorTransaction>(TransactionType.Withdrawal.ToString());

			var entities = company.System.PaymentProcessor.EntitiesForCashAndCreditCard(transactionDeposit);
			Assert.AreEqual(3, entities.Count());
			Assert.AreEqual(5, entities.ElementAt(0).Id);
			Assert.AreEqual(6, entities.ElementAt(1).Id);
			Assert.AreEqual(7, entities.ElementAt(2).Id);

			entities = company.System.PaymentProcessor.EntitiesForCashAndCreditCard(transactionWithdrawal);
			Assert.AreEqual(3, entities.Count());
			Assert.AreEqual(5, entities.ElementAt(0).Id);
			Assert.AreEqual(6, entities.ElementAt(1).Id);
			Assert.AreEqual(7, entities.ElementAt(2).Id);
		}

		[TestMethod]
		public void CountProcessors()
		{
			CurrenciesTest.AddCurrencies();
			var company = new Company();
			company.Sales.CreateStore(1, "Test Store").MakeCurrent();
			var marketplace = new Marketplace(company, "CR");
			var domain = company.Sales.CreateDomain(false, 1, "localhost", PaymentChannels.Agents.INSIDER);
			company.Sales.CurrentStore.Add(domain);
			GuardianTest.LoadProcessors(company);

			var coin = company.System.Coins.SearchByIsoCode("USD");
			var processorsForDeposit = company.System.PaymentProcessor.ProcessorsForDeposit(coin.Coin);

			var transactionDeposit = company.System.TransactionTypes.SearchByName<ProcessorTransaction>(TransactionType.Deposit.ToString());
			var processors = marketplace.PaymentProcessors().SearchFor(coin.Coin, transactionDeposit);
			Assert.AreEqual(company.System.PaymentProcessor.PaymentProcessorsAndActionsByDomains, marketplace.PaymentProcessors());
			Assert.AreEqual(4, processors.Count());

			var firstProcessor = (PaymentProcessor)company.System.PaymentProcessor.First();
			Assert.IsFalse(firstProcessor.Enabled);

			processors = marketplace.PaymentProcessors().SearchFor(coin.Coin, transactionDeposit);
			Assert.AreEqual(4, processors.Count());
		}

		[TestMethod]
		public void EnableProcessor_DisableProcessor()
		{
			CurrenciesTest.AddCurrencies();
			var company = new Company();
			company.Sales.CreateStore(1, "Test Store").MakeCurrent();
			var domain = company.Sales.CreateDomain(false, 1, "localhost", PaymentChannels.Agents.INSIDER);
			company.Sales.CurrentStore.Add(domain);
			GuardianTest.LoadProcessors(company);

			var firstProcessor = (PaymentProcessor)company.System.PaymentProcessor.Last();
			Assert.IsTrue(firstProcessor.Enabled);

			company.System.PaymentProcessor.DisableProcessor(firstProcessor.Entity.Id, firstProcessor.Group.Id, firstProcessor.Transactions.First().Id, firstProcessor.ProcessorCoin.Id);
			Assert.IsFalse(firstProcessor.Enabled);

			company.System.PaymentProcessor.EnableProcessor(firstProcessor.Entity.Id, firstProcessor.Group.Id, firstProcessor.Transactions.First().Id, firstProcessor.ProcessorCoin.Id);
			Assert.IsTrue(firstProcessor.Enabled);
		}

		[TestMethod]
		public void UpdateAlias()
		{
			CurrenciesTest.AddCurrencies();
			var company = new Company();
			company.Sales.CreateStore(1, "Test Store").MakeCurrent();
			var domain = company.Sales.CreateDomain(false, 1, "localhost", PaymentChannels.Agents.INSIDER);
			company.Sales.CurrentStore.Add(domain);
			GuardianTest.LoadProcessors(company);

			var lastProcessor = (PaymentProcessor)company.System.PaymentProcessor.Last();
			Assert.AreEqual("TEST1_Creditcard_Withdrawal_USD", lastProcessor.Alias);

			company.System.PaymentProcessor.UpdateAlias(lastProcessor.Entity.Id, lastProcessor.Group.Id, lastProcessor.Transactions.First().Id, lastProcessor.ProcessorCoin.Id, "alias changed");
			Assert.AreEqual("alias changed", lastProcessor.Alias);
		}

		[TestMethod]
		public void SearchProcessorWithHigherVersion()
		{
			CurrenciesTest.AddCurrencies();
			var company = new Company();
			company.Sales.CreateStore(1, "Test Store").MakeCurrent();
			var domain = company.Sales.CreateDomain(false, 1, "localhost", PaymentChannels.Agents.INSIDER);
			company.Sales.CurrentStore.Add(domain);
			GuardianTest.LoadProcessors(company);

			var firstProcessor = (PaymentProcessor)company.System.PaymentProcessor.First();

			var result = company.System.PaymentProcessor.SearchProcessorWithHigherVersion(firstProcessor.Entity.Id, firstProcessor.Group.Id, firstProcessor.Transactions.First().Id, firstProcessor.ProcessorCoin.Id);
			Assert.AreNotEqual(firstProcessor, result);
		}

		[TestMethod]
		public void AllProcessorsWithTheSameVersion()
		{
			CurrenciesTest.AddCurrencies();
			var company = new Company();
			company.Sales.CreateStore(1, "Test Store").MakeCurrent();
			var domain = company.Sales.CreateDomain(false, 1, "localhost", PaymentChannels.Agents.INSIDER);
			company.Sales.CurrentStore.Add(domain);
			GuardianTest.LoadProcessors(company);

			var firstProcessor = (PaymentProcessor)company.System.PaymentProcessor.First();

			var result = company.System.PaymentProcessor.AllProcessorsWithTheSameVersion(firstProcessor.Entity.Id, firstProcessor.Group.Id, firstProcessor.Transactions.First().Id, firstProcessor.ProcessorCoin.Id);
			Assert.IsFalse(result);
		}

		[TestMethod]
		public void PaymentMethodsPerDomainWithProviders()
		{
			CurrenciesTest.AddCurrencies();
			var company = new Company();
			company.Sales.CreateStore(1, "Test Store").MakeCurrent();
			var domain = company.Sales.CreateDomain(false, 1, "localhost", PaymentChannels.Agents.INSIDER);
			company.Sales.CurrentStore.Add(domain);
			GuardianTest.LoadProcessors(company);

			var coin = (ProcessorCoin)company.System.Coins.Find("dollar");
			var processorsForDeposit = company.System.PaymentProcessor.ProcessorsForDeposit(coin.Coin);

			var transactionDeposit = company.System.TransactionTypes.SearchByName<ProcessorTransaction>(TransactionType.Deposit.ToString());
			var firstEntity = (Entity)company.System.Entities.First();

			var paymentMethods = company.System.PaymentProcessor.PaymentMethodsWithProviders(firstEntity, transactionDeposit);
			Assert.AreEqual(2, paymentMethods.Count());
			Assert.AreEqual(PaymentMethod.Cash.ToString(), paymentMethods.ElementAt(0).Name);
			Assert.AreEqual(PaymentMethod.Creditcard.ToString(), paymentMethods.ElementAt(1).Name);
		}

		[TestMethod]
		public void EntitiesPerDomainWithProviders()
		{
			CurrenciesTest.AddCurrencies();
			var company = new Company();
			company.Sales.CreateStore(1, "Test Store").MakeCurrent();
			var domain = company.Sales.CreateDomain(false, 1, "localhost", PaymentChannels.Agents.INSIDER);
			company.Sales.CurrentStore.Add(domain);
			GuardianTest.LoadProcessors(company);

			var coin = (ProcessorCoin)company.System.Coins.Find("dollar");
			var processorsForDeposit = company.System.PaymentProcessor.ProcessorsForDeposit(coin.Coin);

			var transactionDeposit = company.System.TransactionTypes.SearchByName<ProcessorTransaction>(TransactionType.Deposit.ToString());

			var entities = company.System.PaymentProcessor.EntitiesWithProviders(transactionDeposit);
			Assert.AreEqual(3, entities.Count());
			Assert.AreEqual("TEST", entities.ElementAt(0).Name);
			Assert.AreEqual("TEST0", entities.ElementAt(1).Name);
			Assert.AreEqual("TEST1", entities.ElementAt(2).Name);
		}

		[TestMethod]
		public void Entities()
		{
			Company company = new Company();
			Assert.AreEqual(0, company.System.Entities.Count());

			company.System.Entities.Add(1, "entity1");
			company.System.Entities.Add(2, "entity2");
			company.System.Entities.Add(3, "entity3");
			company.System.Entities.Add(4, "entity4");

			Assert.AreEqual(4, company.System.Entities.Count());
			Assert.AreEqual(0, company.System.Entities.VisibleMembers.Count());
			Assert.AreEqual(0, company.System.Entities.EnabledMembers.Count());

			company.System.Entities.MakeVisible(1);
			company.System.Entities.MakeVisible(2);
			company.System.Entities.Enable(2);

			Assert.AreEqual(2, company.System.Entities.VisibleMembers.Count());
			Assert.AreEqual(1, company.System.Entities.EnabledMembers.Count());

			company.System.Entities.Hide(1);
			company.System.Entities.Disable(2);

			Assert.AreEqual(1, company.System.Entities.VisibleMembers.Count());
			Assert.AreEqual(0, company.System.Entities.EnabledMembers.Count());
		}

		[TestMethod]
		public void Entities2()
		{
			CurrenciesTest.AddCurrencies();
			var company = new Company();
			GuardianTest.LoadProcessors(company);

			var entities = company.System.PaymentProcessor.Entities();
			Assert.AreEqual(3, entities.Count());

			Assert.AreEqual("TEST", entities.ElementAt(0).Name);
			Assert.AreEqual("TEST0", entities.ElementAt(1).Name);
			Assert.AreEqual("TEST1", entities.ElementAt(2).Name);
		}

		[TestMethod]
		public void TransactionTypes()
		{
			Company company = new Company();
			Assert.AreEqual(0, company.System.TransactionTypes.Count());

			company.System.TransactionTypes.Add(1, "transaction1");
			company.System.TransactionTypes.Add(2, "transaction2");
			company.System.TransactionTypes.Add(3, "transaction3");
			company.System.TransactionTypes.Add(4, "transaction4");

			Assert.AreEqual(4, company.System.TransactionTypes.Count());
			Assert.AreEqual(0, company.System.TransactionTypes.VisibleMembers.Count());
			Assert.AreEqual(0, company.System.TransactionTypes.EnabledMembers.Count());

			company.System.TransactionTypes.MakeVisible(1);
			company.System.TransactionTypes.MakeVisible(2);
			company.System.TransactionTypes.Enable(2);

			Assert.AreEqual(2, company.System.TransactionTypes.VisibleMembers.Count());
			Assert.AreEqual(1, company.System.TransactionTypes.EnabledMembers.Count());

			company.System.TransactionTypes.Hide(1);
			company.System.TransactionTypes.Disable(2);

			Assert.AreEqual(1, company.System.TransactionTypes.VisibleMembers.Count());
			Assert.AreEqual(0, company.System.TransactionTypes.EnabledMembers.Count());
		}

		[TestMethod]
		public void TransactionTypes2()
		{
			CurrenciesTest.AddCurrencies();
			var company = new Company();
			GuardianTest.LoadProcessors(company);

			var firstProcessor = (PaymentProcessor)company.System.PaymentProcessor.First();
			var paymentMethod = company.System.PaymentProcessor.SearchPaymentMethodByName(PaymentMethod.Secrets.ToString());
			var types = company.System.PaymentProcessor.TransactionTypes(firstProcessor.Entity, paymentMethod);
			Assert.AreEqual(2, types.Count());

			Assert.AreEqual(TransactionType.Deposit.ToString(), types.ElementAt(0).Name);
			Assert.AreEqual(TransactionType.Withdrawal.ToString(), types.ElementAt(1).Name);
		}

		[TestMethod]
		public void PaymentMethods()
		{
			Company company = new Company();
			Assert.AreEqual(0, company.System.PaymentMethods.Count());

			company.System.PaymentMethods.Add(1, "method1");
			company.System.PaymentMethods.Add(2, "method2");
			company.System.PaymentMethods.Add(3, "method3");
			company.System.PaymentMethods.Add(4, "method4");

			Assert.AreEqual(4, company.System.PaymentMethods.Count());
			Assert.AreEqual(0, company.System.PaymentMethods.VisibleMembers.Count());
			Assert.AreEqual(0, company.System.PaymentMethods.EnabledMembers.Count());

			company.System.PaymentMethods.MakeVisible(1);
			company.System.PaymentMethods.MakeVisible(2);
			company.System.PaymentMethods.Enable(2);

			Assert.AreEqual(2, company.System.PaymentMethods.VisibleMembers.Count());
			Assert.AreEqual(1, company.System.PaymentMethods.EnabledMembers.Count());

			company.System.PaymentMethods.Hide(1);
			company.System.PaymentMethods.Disable(2);

			Assert.AreEqual(1, company.System.PaymentMethods.VisibleMembers.Count());
			Assert.AreEqual(0, company.System.PaymentMethods.EnabledMembers.Count());
		}

		[TestMethod]
		public void PaymentMethods2()
		{
			CurrenciesTest.AddCurrencies();
			var company = new Company();
			GuardianTest.LoadProcessors(company);

			var firstProcessor = (PaymentProcessor)company.System.PaymentProcessor.First();
			var methods = company.System.PaymentProcessor.PaymentMethods(firstProcessor.Entity);
			Assert.AreEqual(3, methods.Count());

			Assert.AreEqual(PaymentMethod.Secrets.ToString(), methods.ElementAt(0).Name);
			Assert.AreEqual(PaymentMethod.Cash.ToString(), methods.ElementAt(1).Name);
			Assert.AreEqual(PaymentMethod.Creditcard.ToString(), methods.ElementAt(2).Name);
		}

		[TestMethod]
		public void PaymentMethodsForCashAndCreditCard()
		{
			CurrenciesTest.AddCurrencies();
			var company = new Company();
			GuardianTest.LoadProcessors(company);

			var transactionDeposit = company.System.TransactionTypes.SearchByName<ProcessorTransaction>(TransactionType.Deposit.ToString());
			var transactionWithdrawal = company.System.TransactionTypes.SearchByName<ProcessorTransaction>(TransactionType.Withdrawal.ToString());

			var firstProcessor = (PaymentProcessor)company.System.PaymentProcessor.First();
			var methods = company.System.PaymentProcessor.PaymentMethodsForCashAndCreditCard(firstProcessor.Entity, transactionDeposit);
			Assert.AreEqual(2, methods.Count());

			Assert.AreEqual(PaymentMethod.Cash.ToString(), methods.ElementAt(0).Name);
			Assert.AreEqual(PaymentMethod.Creditcard.ToString(), methods.ElementAt(1).Name);

			methods = company.System.PaymentProcessor.PaymentMethodsForCashAndCreditCard(firstProcessor.Entity, transactionWithdrawal);
			Assert.AreEqual(1, methods.Count());

			Assert.AreEqual(PaymentMethod.Cash.ToString(), methods.ElementAt(0).Name);
		}

		[TestMethod]
		public void Tenants()
		{
			Company company = new Company();
			Assert.AreEqual(0, company.System.Tenants.Count());

			company.System.Tenants.Add(1, "tenant1");
			company.System.Tenants.Add(2, "tenant2");
			company.System.Tenants.Add(3, "tenant3");
			company.System.Tenants.Add(4, "tenant4");

			Assert.AreEqual(4, company.System.Tenants.Count());
			Assert.AreEqual(0, company.System.Tenants.VisibleMembers.Count());
			Assert.AreEqual(0, company.System.Tenants.EnabledMembers.Count());

			company.System.Tenants.MakeVisible(1);
			company.System.Tenants.MakeVisible(2);
			company.System.Tenants.Enable(2);

			Assert.AreEqual(2, company.System.Tenants.VisibleMembers.Count());
			Assert.AreEqual(1, company.System.Tenants.EnabledMembers.Count());

			company.System.Tenants.Hide(1);
			company.System.Tenants.Disable(2);

			Assert.AreEqual(1, company.System.Tenants.VisibleMembers.Count());
			Assert.AreEqual(0, company.System.Tenants.EnabledMembers.Count());
		}

		[TestMethod]
		public void Coins()
		{
			var company = new Company();
			if (!company.System.Coins.ExistsIsoCode("FP")) company.System.Coins.Add(0, "FP", "FP", 2, "f", "free play", CoinType.Digital);
			if (!company.System.Coins.ExistsIsoCode("LR"))
			{
				var coin = company.System.Coins.Add(1, "LR", "LR", 2, "l", "lotto reward", CoinType.Digital);
				coin.Visible = true;
				coin.Enabled = true;
			}
			if (!company.System.Coins.ExistsIsoCode("USD"))
			{
				var coin = company.System.Coins.Add(2, "USD", "$", 2, "\u0024", "dollar", CoinType.Fiat);
				coin.Visible = true;
				coin.Enabled = true;
			}
			if (!company.System.Coins.ExistsIsoCode("BTC"))
			{
				var coin = company.System.Coins.Add(3, "BTC", "BTC", 8, "\u20BF", "bitcoin", CoinType.Crypt);
				coin.Visible = true;
				coin.Enabled = true;
			}
			if (!company.System.Coins.ExistsIsoCode("USDT")) company.System.Coins.Add(4, "USDT", "USDT", 8, "-", "tether", CoinType.Crypt);
			if (!company.System.Coins.ExistsIsoCode("ETH")) company.System.Coins.Add(7, "ETH", "ETH", 8, "\u039E", "ethereum", CoinType.Crypt);
			Assert.AreEqual(6, company.System.Coins.Count());

			company.System.Coins.MakeVisible(1);
			company.System.Coins.MakeVisible(2);
			company.System.Coins.MakeVisible(3);
			company.System.Coins.Enable(1);
			company.System.Coins.Enable(2);
			company.System.Coins.Enable(3);
			Assert.AreEqual(3, company.System.Coins.VisibleMembers.Count());
			Assert.AreEqual(3, company.System.Coins.EnabledMembers.Count());

			company.System.Coins.Hide(1);
			company.System.Coins.Disable(2);

			Assert.AreEqual(2, company.System.Coins.VisibleMembers.Count());
			Assert.AreEqual(1, company.System.Coins.EnabledMembers.Count());

			company.System.Coins.MakeVisible(1);
			company.System.Coins.Enable(2);

			Assert.AreEqual(3, company.System.Coins.VisibleMembers.Count());
			Assert.AreEqual(2, company.System.Coins.EnabledMembers.Count());
		}

		[TestMethod]
		public void Coins2()
		{
			CurrenciesTest.AddCurrencies();
			var company = new Company();
			GuardianTest.LoadProcessors(company);

			var coins = company.System.PaymentProcessor.Coins();
			Assert.AreEqual(2, coins.Count());

			Assert.AreEqual("bitcoin", coins.ElementAt(0).Name);
			Assert.AreEqual("dollar", coins.ElementAt(1).Name);
		}

		[TestMethod]
		public void Coins3()
		{
			CurrenciesTest.AddCurrencies();
			var company = new Company();
			GuardianTest.LoadProcessors(company);

			var entity = (Entity)company.System.Entities.First();
			var paymentMethod = company.System.PaymentProcessor.SearchPaymentMethodByName(PaymentMethod.Secrets.ToString());
			var transactionType = company.System.PaymentProcessor.SearchTransactionByName(TransactionType.Deposit.ToString());
			var coins = company.System.PaymentProcessor.Coins(entity, paymentMethod, transactionType);
			Assert.AreEqual(1, coins.Count());

			Assert.AreEqual("bitcoin", coins.ElementAt(0).Name);
		}

		[TestMethod]
		public void Coins4()
		{
			CurrenciesTest.AddCurrencies();
			var company = new Company();
			GuardianTest.LoadProcessors(company);

			var transactionType = company.System.PaymentProcessor.SearchTransactionByName(TransactionType.Deposit.ToString());
			var coins = company.System.PaymentProcessor.Coins(transactionType);
			Assert.AreEqual(2, coins.Count());

			Assert.AreEqual("bitcoin", coins.ElementAt(0).Name);
			Assert.AreEqual("dollar", coins.ElementAt(1).Name);
		}

		[TestMethod]
		public void CopyFrom()
		{
			var company = new Company();
			var now = DateTime.Now;
			var customSettings = new CustomSettingsCollection(company);
			var oldCS1 = customSettings.AddFixedParameter(now, "CashierDriver.url", "http://cashierapi:5000/");
			oldCS1.Description = "Cashier Driver url";
			oldCS1.Enabled = true;
			var oldCS2 = customSettings.AddFixedParameter(now, "CompanyBaseUrlServices", "http://cashierapi:5000/");
			oldCS2.Enabled = false;
			var oldCS3 = customSettings.AddFixedParameter(now, "CompanySystemId", "N/A");
			var oldCS4 = customSettings.AddSecretParameter(now, "CompanySystemPassword", "N/A");
			var oldCS5 = customSettings.AddVariableParameter("DOB");
			var oldCSs = new CustomSetting[] { oldCS1, oldCS2, oldCS3, oldCS4, oldCS5 };
			Assert.AreEqual(5, customSettings.Count());

			var newSettings = new CustomSettingsCollection(company);
			Assert.AreEqual(0, newSettings.Count());

			newSettings.CopyFrom(customSettings);
			Assert.AreEqual(customSettings.Count(), newSettings.Count());

			var newCS1 = newSettings.Get(now, "CashierDriver.url");
			var newCS2 = newSettings.Get(now, "CompanyBaseUrlServices");
			var newCS3 = newSettings.Get(now, "CompanySystemId");
			var newCS4 = newSettings.Get(now, "CompanySystemPassword");
			//var newCS5 = newSettings.Get("DOB");
			var newCSs = new CustomSetting[] { newCS1, newCS2, newCS3, newCS4 };
			for (int index = 0; index < 4; index++)
			{
				var oldCS = oldCSs[index];
				var newCS = newCSs[index];
				Assert.AreEqual(oldCS.AsString, newCS.AsString);
				Assert.AreEqual(oldCS.Description, newCS.Description);
				Assert.AreEqual(oldCS.Enabled, newCS.Enabled);
				Assert.AreEqual(oldCS.HasScheduledChange, newCS.HasScheduledChange);
				Assert.AreEqual(oldCS.IsVariable, newCS.IsVariable);
				Assert.AreEqual(oldCS.Key, newCS.Key);
				Assert.AreEqual(oldCS.StartDate.Date, newCS.StartDate.Date);
				Assert.AreEqual(oldCS.Type, newCS.Type);
			}

		}

		[TestMethod]
		public void ChangeValueStartingOn()
		{
			var company = new Company();
			var now = DateTime.Now;
			var customSettings = new CustomSettingsCollection(company);
			var cs = customSettings.AddFixedParameter(now, "CashierDriver.url", "http://cashierapi:5000/");
			DateTime tomorrow = now.AddDays(1);
			customSettings.ChangeValueStartingOn(tomorrow, "CashierDriver.url", "http://cashierapi:5001/", "N/A");

			cs = customSettings.Get(now, "CashierDriver.url");
			Assert.AreEqual("http://cashierapi:5000/", cs.AsString);
			cs = customSettings.Get(tomorrow, "CashierDriver.url");
			Assert.AreEqual("http://cashierapi:5001/", cs.AsString);
		}

		[TestMethod]
		public void ChangeValueStartingOn2()
		{
			QueueInMemory queue = new QueueInMemory();
			Integration.Kafka = queue;
			Integration.SavePreviousSettings(tempUseKafka: true, tempUseKafkaForAuto: false);

			var company = new Company();
			var store = company.Sales.CreateStore(1, "Test Store");
			store.Alias = "testStore";
			store.MakeCurrent();
			var tenant = (Tenant)company.System.Tenants.Add(1, "fiero");
			tenant.MakeCurrent();
			var now = DateTime.Now;

			var customSettings = new CustomSettingsCollection(company);
			var cs = customSettings.AddFixedParameter(now, "CashierDriver.url", "http://cashierapi:5000/");
			DateTime tomorrow = now.AddDays(1);
			customSettings.ChangeValueStartingOn(true, tomorrow, "CashierDriver.url", "http://cashierapi:5001/", "N/A");

			var count = queue.Count(Integration.Kafka.TopicForCustomSettings);
			Assert.AreEqual(1, count);
			string msg = queue.Dequeue(Integration.Kafka.TopicForCustomSettings).ToString();
			var customSettingMessage = new CustomSettingMessage(msg);
			Assert.AreEqual(cs.Key, customSettingMessage.Key);
			Assert.AreEqual(cs.NextValue, customSettingMessage.Value);
			Assert.AreEqual(cs.Description, customSettingMessage.Description);
			Assert.AreEqual("N/A", customSettingMessage.EmployeeName);
			Assert.AreEqual(cs.NextDateToChange.ToString("MM/dd/yyyy HH:mm:ss"), customSettingMessage.DateToApplyTheChange);
		}

		[TestMethod]
		public void ChangeSecretStartingOn()
		{
			var company = new Company();
			var now = DateTime.Now;
			var customSettings = new CustomSettingsCollection(company);
			var cs = customSettings.AddSecretParameter(now, "CompanySystemPassword", "12345");
			DateTime tomorrow = now.AddDays(1);
			customSettings.ChangeSecretStartingOn(tomorrow, "CompanySystemPassword", "54321", "N/A");

			cs = customSettings.Get(now, "CompanySystemPassword");
			Assert.AreEqual("12345", cs.AsString);
			cs = customSettings.Get(tomorrow, "CompanySystemPassword");
			Assert.AreEqual("54321", cs.AsString);
		}

		[TestMethod]
		public void ChangeSecretStartingOn2()
		{
			QueueInMemory queue = new QueueInMemory();
			Integration.Kafka = queue;
			Integration.SavePreviousSettings(tempUseKafka: true, tempUseKafkaForAuto: false);

			var company = new Company();
			var store = company.Sales.CreateStore(1, "Test Store");
			store.Alias = "testStore";
			store.MakeCurrent();
			var tenant = (Tenant)company.System.Tenants.Add(1, "fiero");
			tenant.MakeCurrent();
			var now = DateTime.Now;

			var customSettings = new CustomSettingsCollection(company);
			var cs = customSettings.AddSecretParameter(now, "CompanySystemPassword", "12345");
			DateTime tomorrow = now.AddDays(1);
			customSettings.ChangeSecretStartingOn(true, tomorrow, "CompanySystemPassword", "54321", "N/A");

			var count = queue.Count(Integration.Kafka.TopicForCustomSettings);
			Assert.AreEqual(1, count);
			string msg = queue.Dequeue(Integration.Kafka.TopicForCustomSettings).ToString();
			var customSettingMessage = new CustomSettingMessage(msg);
			Assert.AreEqual(cs.Key, customSettingMessage.Key);
			Assert.AreEqual(cs.NextValue, customSettingMessage.Value);
			Assert.AreEqual(cs.Description, customSettingMessage.Description);
			Assert.AreEqual("N/A", customSettingMessage.EmployeeName);
			Assert.AreEqual(cs.NextDateToChange.ToString("MM/dd/yyyy HH:mm:ss"), customSettingMessage.DateToApplyTheChange);
		}

		[TestMethod]
		public void UpdateDescription()
		{
			var company = new Company();
			var now = DateTime.Now;
			var customSettings = new CustomSettingsCollection(company);
			var cs = customSettings.AddSecretParameter(now, "CompanySystemPassword", "12345");
			cs.Description = "a password";
			customSettings.UpdateDescription(false, "CompanySystemPassword", "system pass");

			cs = customSettings.Get(now, "CompanySystemPassword");
			Assert.AreEqual("system pass", cs.Description);
		}

		[TestMethod]
		public void Enable_Disable()
		{
			var company = new Company();
			var now = DateTime.Now;
			var customSettings = new CustomSettingsCollection(company);
			var cs = customSettings.AddSecretParameter(now, "CompanySystemPassword", "12345");
			Assert.IsTrue(cs.Enabled);

			customSettings.Disable(false, "CompanySystemPassword");
			cs = customSettings.Get(now, "CompanySystemPassword");
			Assert.IsFalse(cs.Enabled);

			customSettings.Enable(false, "CompanySystemPassword");
			cs = customSettings.Get(now, "CompanySystemPassword");
			Assert.IsTrue(cs.Enabled);
		}
	}
}
