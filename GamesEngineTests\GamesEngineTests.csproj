﻿
<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net5.0</TargetFramework>
	<SignAssembly>true</SignAssembly>
    <AssemblyOriginatorKeyFile>GamesEngineTests.snk</AssemblyOriginatorKeyFile>
    <IsPackable>false</IsPackable>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="Properties\AssemblyInfo.cs" />
  </ItemGroup>

  <ItemGroup>
    <None Remove="appsettings.json" />
    <None Remove="Jsons\league.json" />
    <None Remove="packages.config" />
    <None Remove="Settings\lottohistoricalsettings.json" />
  </ItemGroup>

  <ItemGroup>
    <Content Include="appsettings.json">
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="Jsons\league.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
    </Content>
    <Content Include="Settings\lottohistoricalsettings.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
    </Content>
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="log4net" Version="2.0.14" />
    <PackageReference Include="Microsoft.NET.Test.Sdk" Version="15.7.0" />
    <PackageReference Include="Microsoft.VisualStudio.Threading.Analyzers" Version="16.4.43" />
    <PackageReference Include="MSTest.TestAdapter" Version="1.2.1" />
    <PackageReference Include="MSTest.TestFramework" Version="1.2.1" />
	<PackageReference Include="Moq" Version="4.8.2" />
	<PackageReference Include="System.Collections.Concurrent" Version="4.3.0" />
	<PackageReference Include="System.ValueTuple" Version="4.4.0" />
  </ItemGroup>
  
  <ItemGroup>
	<ProjectReference Include="..\CashierAPI\CashierAPI.csproj" />
	<ProjectReference Include="..\GamesEngineMocks\GamesEngineMocks.csproj" />
	<ProjectReference Include="..\LinesAPI\LinesAPI.csproj" />
	<ProjectReference Include="..\LinesBIAPI\LinesBIAPI.csproj" />
	<ProjectReference Include="..\LottoAPI\LottoAPI.csproj" />
	<ProjectReference Include="..\LottoBIAPI\LottoBIAPI.csproj" />
	<ProjectReference Include="..\LoyaltyAPI\LoyaltyAPI.csproj" />
    <ProjectReference Include="..\Puppeteer\Puppeteer.csproj" />
  </ItemGroup>

</Project>