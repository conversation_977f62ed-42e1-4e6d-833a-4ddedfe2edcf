﻿using Connectors.town.connectors.driver.transactions;
using Connectors.town.connectors.drivers.hades;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Threading.Tasks;
using town.connectors;
using town.connectors.commons;
using town.connectors.drivers;
using static town.connectors.CustomSettings;
using static town.connectors.CustomSettings.CustomSetting;

namespace GamesEngineTests.Unit_Tests.Connectors
{

	[TestClass]
	public class ASIConnectorTest
	{
		[TestMethod]
		public void Fragment_an_Authorization()
		{
			//TODO disparar el docker-compose  cashier asi stub
			Variables variables = new Variables();
			var obj = new { Name = "tester" };
			DateTime now = DateTime.Now;
			CustomSettings cs = new CustomSettings(variables);
			cs.AddFixedParameter(now, "CompanyBaseUrlServices", "http://testart.sec-xm41d.com");
			cs.AddFixedParameter(now, "CompanySystemId", "company1");
			cs.AddFixedParameter(now, "CompanySystemPassword", new Secret("12314$565"));
			cs.AddFixedParameter(now, "CompanyClerkId", "123");
			cs.AddFixedParameter(now, "TokenSystemId", "3aab83fb"); 

			Fragment_an_Authorization aaa = new Fragment_an_Authorization();
			aaa.ConfigureThenPrepare(now,cs);
			using (RecordSet recordSet = aaa.CustomSettings.GetRecordSet())
			{
				recordSet.SetParameter("ticketNumber", 100);
				recordSet.SetParameter("customerId", "Cris5");
				recordSet.SetParameter("wagers", new PostFreeFormWager[] { new PostFreeFormWager() });

				PostFreeFormWagerCollectionSuccessResponse result = aaa.Execute<PostFreeFormWagerCollectionSuccessResponse>(now, recordSet);
			}
		}

        [TestMethod]
        public async Task DeclarationAsync()
        {

            //TODO disparar el docker-compose  cashier asi stub
            Variables variables = new Variables();
            var obj = new { Name = "tester" };
            DateTime now = DateTime.Now;
            CustomSettings cs = new CustomSettings(variables);
            cs.AddFixedParameter(now, "CompanyBaseUrlServices", "http://testart.sec-xm41d.com");
            cs.AddFixedParameter(now, "CompanySystemId", "company1");
            cs.AddFixedParameter(now, "CompanySystemPassword", new Secret("12314$565"));
            cs.AddFixedParameter(now, "CompanyClerkId", "123");
            cs.AddFixedParameter(now, "TokenSystemId", "3aab83fb");

			Authorization authorization = new Authorization();
			authorization.ConfigureThenPrepare(now, cs);
			authorization.RegisterANotifyErrorsBehavior((string message, Exception e, string[] details) =>
            {

            });
			authorization.RegisterANotifyWarningsBehavior((string message, Exception e, string[] details) =>
            {

            });

			AuthorizationTransaction result;

			authorization.ConfigureThenPrepare(now,cs);
			using (RecordSet recordSet = authorization.CustomSettings.GetRecordSet())
			{
				recordSet.SetParameter("purchaseTotal.Value", 100);
				recordSet.SetParameter("purchaseBody", "Cris5");
				recordSet.SetParameter("atAddress", "ABC5");

				result = (AuthorizationTransaction)await authorization.ExecuteAsync<AuthorizationTransaction>(now, recordSet);
			}
        }

        [TestMethod]
		public void List_All_ddls()
		{
			string path = Path.GetDirectoryName(Assembly.GetExecutingAssembly().Location);
			string logs = "";
			var result = Driver.LoadAllConnectorsFrom(path, out logs);

			Assert.AreEqual(true, result.Count() > 0);


		}

	}

}
