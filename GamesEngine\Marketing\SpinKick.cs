﻿using GamesEngine.Bets;
using GamesEngine.Business;
using GamesEngine.Finance;
using GamesEngine.Marketing.Campaigns;
using GamesEngine.Marketing.Campaigns.Base;
using GamesEngine.Marketing.Campaigns.Rewarders;
using GamesEngine.RealTime.Events;
using GamesEngine.RealTime;
using GamesEngine.Settings;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using GamesEngine.Business.Marketing;
using GamesEngine.Custodian;
using static GamesEngine.Finance.PaymentChannels;
using GamesEngine.Resources;
using GamesEngine.Messaging;
using GamesEngine.PurchaseOrders;

namespace GamesEngine.Marketing
{
    internal class SpinKick : BudgetPromotion
    {
        internal static string CURRENCY_CODE = "SPR";

        private int totalRewardUnits;
        private int assignedRewardUnits;

        private OnTheSpot onTheSpot;

        internal string AccountNumber => $"{this.GetType().Name}_{PromotionNumber}_{CURRENCY_CODE}";

        internal SpinKick(bool itIsThePresent, Company company, int promotionNumber, DateTime createdDate, string name, string description, int totalRewardUnits, SaleCampaign saleCampaign) : base(company, promotionNumber, createdDate, name, saleCampaign.Campaign.Budget)
        {
            if (company == null) throw new GameEngineException(nameof(company));
            if (promotionNumber <= 0) throw new GameEngineException(nameof(promotionNumber));
            if (createdDate == DateTime.MinValue) throw new GameEngineException(nameof(createdDate));
            if (string.IsNullOrWhiteSpace(name)) throw new GameEngineException(nameof(name));
            if (string.IsNullOrWhiteSpace(description)) throw new GameEngineException(nameof(description));
            if (totalRewardUnits <= 0) throw new GameEngineException("The total available spins must be greater than 0.");
            if (saleCampaign == null) throw new GameEngineException(nameof(saleCampaign));

            Description = description;
            this.totalRewardUnits = totalRewardUnits;
            this.assignedRewardUnits = 0;

            if (saleCampaign.Campaign is not OnTheSpot) throw new GameEngineException("The linked campaign must be a SpinKick campaign.");
            if (saleCampaign.Reward is not SpinWheelRewarder) throw new GameEngineException("The linked campaign must have a SpinWheel rewarder.");
            this.onTheSpot = saleCampaign.Campaign as OnTheSpot;
            if (this.onTheSpot == null) throw new GameEngineException("The linked campaign must be a SpinKick campaign.");

            if (itIsThePresent)
            {
                string reference = $"{PromotionNumber}-{createdDate.ToString("yy")}{createdDate.ToString("HHmmss")}{createdDate.DayOfYear.ToString("000")}{createdDate.ToString("yy")}";
                if (reference.Length > Movement.MAX_REFERENCE_LENGTH) reference = reference.Substring(0, Movement.MAX_REFERENCE_LENGTH);
                string accountNumber = CURRENCY_CODE;
                using (KafkaMessagesBuffer buffer = new KafkaMessagesBuffer(itIsThePresent, Integration.Kafka.TopicForDeposits))
                {
                    DepositMessage deposit = new DepositMessage(
                        AccountNumber,
                        PromotionNumber,
                        Coinage.Coin(CURRENCY_CODE),
                        storeId: 1,
                        totalRewardUnits,
                        Name,
                        Description,
                        reference,
                        accountNumber,
                        WholePaymentProcessor.NoPaymentProcessor,
                        string.Empty,
                        Agents.INSIDER,
                        Name
                    );
                    buffer.Send(deposit);
                }

                MarketingStorage marketingStorage = MarketingStorageCampaignInstance();
                if (marketingStorage != null)
                {
                    string currencyCode = SpinKick.CURRENCY_CODE;
                    DateTime startDate = saleCampaign.Campaign.StartDate;
                    DateTime endDate = saleCampaign.Campaign.DeadLine;
                    marketingStorage.StoreCampaign(promotionNumber, createdDate, name, currencyCode, currencyCode, InitialBudget, IsUnlimitedBudget, startDate, endDate, 1, description, endDate, CampaignType, CampaignType, CampaignType);
                }
            }
        }

        internal override string ReturnedCurrencyCode => CURRENCY_CODE;

        internal string RewardType => CampaignBase.RewardType;

        internal bool HasResources => false;

        internal override string CampaignType => this.GetType().Name;

        internal int TotalRewardUnits => this.totalRewardUnits;

        internal int AssignedRewardUnits => this.assignedRewardUnits;

        internal OnTheSpot CampaignBase => onTheSpot;

        internal override bool IsActive(DateTime now)
        {
            if (now == DateTime.MinValue) throw new ArgumentNullException(nameof(now));

            bool isBetweenDates = now >= CampaignBase.StartDate && now <= CampaignBase.DeadLine;

            var result = IsEnabled && isBetweenDates;
            return result;
        }

        internal void AssignPrize(bool itIsThePresent, DateTime now, Player player, string description, int storeId)
        {
            if (player == null) throw new ArgumentNullException(nameof(player));
            if (now == DateTime.MinValue) throw new ArgumentNullException(nameof(now));
            if (now < CampaignBase.StartDate) throw new GameEngineException("The promotion has not started yet.");
            if (now > CampaignBase.EndDate) throw new GameEngineException("The promotion has ended.");
            if (string.IsNullOrWhiteSpace(description)) throw new ArgumentNullException(nameof(description));
            if (storeId <= 0) throw new GameEngineException(nameof(storeId));

            if (this.assignedRewardUnits >= this.totalRewardUnits) throw new GameEngineException("There are no more spins available for this promotion.");

            if (!this.onTheSpot.IsActive(now)) throw new GameEngineException("The linked campaign is not active.");

            if (!this.onTheSpot.HasStarted) this.onTheSpot.HasStarted = true;

            if (this.onTheSpot.Rewarder is SpinWheelRewarder spinWheelRewarder)
            {
                SpinWheel spinWheel = spinWheelRewarder.SpinWheels.FirstOrDefault();
                if (spinWheel == null) throw new GameEngineException("The spin wheel rewarder must have at least one spin wheel.");
                spinWheel.PrizePlayer(player);

                if (itIsThePresent)
                {
                    string reference = $"{PromotionNumber}-{now.ToString("yy")}{now.ToString("HHmmss")}{now.DayOfYear.ToString("000")}{now.ToString("yy")}-{player.AccountNumber}";
                    if (reference.Length > Movement.MAX_REFERENCE_LENGTH) reference = reference.Substring(0, Movement.MAX_REFERENCE_LENGTH);
                    string accountNumber = CURRENCY_CODE;
                    using (KafkaMessagesBuffer buffer = new KafkaMessagesBuffer(itIsThePresent, Integration.Kafka.TopicForWithdrawals))
                    {
                        WithdrawMessage movement = new WithdrawMessage(
                            AccountNumber,
                            PromotionNumber,
                            Coinage.Coin(CURRENCY_CODE),
                            storeId,
                            1.00M,
                            Name,
                            Description,
                            reference,
                            accountNumber,
                            WholePaymentProcessor.NoPaymentProcessor,
                            string.Empty,
                            Agents.INSIDER
                        );
                        buffer.Send(movement);
                    }

                    Subscriber from = Company.Sales.StoreById(Store.STORES_SEQUENCE_LOTTO);
                    player.AddMessage(from, $"{Name}: You acquired a spin");
                    SpinKickPrizeEvent campaignEvent = new SpinKickPrizeEvent(now, PromotionNumber, CampaignType, RewardType, CampaignType, player.Id);
                    PlatformMonitor.GetInstance().WhenNewEvent(campaignEvent);
                }

            }
            else
            {
                throw new GameEngineException("The linked sale campaign must have a spin wheel rewarder type.");
            }
            this.assignedRewardUnits++;
        }
    }
}
