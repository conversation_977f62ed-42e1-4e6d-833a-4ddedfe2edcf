﻿using ExternalServices;
using GamesEngine.Games.Lotto;
using GamesEngine.Settings;
using GamesEngineMocks;
using LottoBIAPI.Controllers;
using LottoBIAPI;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.HttpOverrides;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Puppeteer.EventSourcing;
using Swashbuckle.AspNetCore.Swagger;
using System;
using System.Threading;
using Elastic.Apm.NetCoreAll;
using Elastic.Apm.AspNetCore;
using Microsoft.OpenApi.Models;
using GamesEngine.Finance;
using GamesEngine.MessageQueuing;

namespace LottoBIAPI
{
    public class Startup
    {
        public Startup(IWebHostEnvironment env, IConfiguration configuration)
        {
            var builder = new ConfigurationBuilder()
                .SetBasePath(env.ContentRootPath)
                .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true)
                .AddJsonFile("customization/appsettings.accounting.json", optional: true)
                .AddJsonFile("customization/appsettings.security.json", optional: true)
                .AddJsonFile("customization/appsettings.kafka.json", optional: true)
                .AddJsonFile("customization/appsettings.error_emails.json", optional: true)
                .AddJsonFile("customization/appsettings.apm.json", optional: true)
                .AddJsonFile("secrets/appsettings.secrets.json", optional: true);
            Configuration = builder.Build();
            Environment = env;
        }

        public IConfiguration Configuration { get; }
        public IWebHostEnvironment Environment { get; }

        // This method gets called by the runtime. Use this method to add services to the container.
        public void ConfigureServices(IServiceCollection services)
        {
            int minWorker, minIOC;
            ThreadPool.GetMinThreads(out minWorker, out minIOC);
            int maxWorker, maxIOC;
            ThreadPool.GetMaxThreads(out maxWorker, out maxIOC);

            Console.WriteLine($@" minWorker:{minWorker} minIOC:{minIOC} maxWorker:{maxWorker} maxIOC:{maxIOC}");

            /*Security*/
            Security.Configure(services, Configuration);
            /*Security*/

            services.AddMvc(options => options.EnableEndpointRouting = false).AddNewtonsoftJson();

            services.AddSwaggerGen(c =>
            {
                c.SwaggerDoc("v1", new OpenApiInfo { Title = "Lotto BI API", Version = "v1" });
            });

            var biIntegration = Configuration.GetSection("BIIntegration");

            Integration.Configure(KafkaMessage.Prefix.NoTransacction, biIntegration);

            ErrorsSender.Configure(Configuration.GetSection("ErrorsSender"), Environment.IsProduction());
            Integration.ConfigureAPM(Configuration);

            var messageOriginId = Configuration.GetValue<string>("MessageOriginId");
            if (string.IsNullOrEmpty(messageOriginId) && !int.TryParse(messageOriginId, out _)) throw new Exception("MessageOriginId its requeried in the appsettings.");
            MessageCatalog.AssingMessageOriginId(Convert.ToInt32(messageOriginId));
        }

        // This method gets called by the runtime. Use this method to configure the HTTP request pipeline.
        public void Configure(IApplicationBuilder app, IWebHostEnvironment env)
        {
            if (Integration.UseAPM)
            {
                app.UseElasticApm(Configuration);
            }
            if (env.IsDevelopment())
            {
                app.UseDeveloperExceptionPage();
                app.UseSwagger();
                app.UseSwaggerUI(c =>
                {
                    c.SwaggerEndpoint("/swagger/v1/swagger.json", "Lotto BI API V1");
                    c.RoutePrefix = string.Empty;
                });
            }
            else
            {
                if (String.IsNullOrEmpty(Integration.Db.DBSelected))
                {
                    throw new Exception("Db configuration its required.");
                }

            }
            var sectionDairy = Configuration.GetSection("DBDairy");
            var mySQL = sectionDairy.GetSection("ConnectionStrings").GetValue<string>("MySQL");
            var sqlServer = sectionDairy.GetSection("ConnectionStrings").GetValue<string>("SQLServer");
            var dbSelected = sectionDairy.GetValue<string>("DBSelected");
            var scriptBeforeRecovering = Configuration.GetValue<string>("ActionsBeforeRecovering");

            if (dbSelected == DatabaseType.MySQL.ToString())
            {
                LottoBIAPI.LottoBI.EventSourcingStorage(DatabaseType.MySQL, mySQL, scriptBeforeRecovering, "");
            }
            else if (dbSelected == DatabaseType.SQLServer.ToString())
            {
                LottoBIAPI.LottoBI.EventSourcingStorage(DatabaseType.SQLServer, sqlServer, scriptBeforeRecovering, "");
            }
            else if (!String.IsNullOrWhiteSpace(dbSelected))
            {
                throw new Exception($"There is no connection for {dbSelected}");
            }
            else
            {
                var DOTNET_RUNNING_IN_CONTAINER = bool.Parse(System.Environment.GetEnvironmentVariable("DOTNET_RUNNING_IN_CONTAINER") ?? "false");
                if (DOTNET_RUNNING_IN_CONTAINER)
                    LottoBIAPI.LottoBI.EventSourcingStorage(DatabaseType.MySQL, mySQL, scriptBeforeRecovering, "");

                LottoMocks.BI(LottoBIAPI.LottoBI.Actor);
            }

            /*Security*/
            app.UseAuthentication();
            /*Security*/

            app.UseMvc();

            if ( !Integration.UseDb)
            {
                throw new Exception("This project must use bd integration to show data.");
            }

            ReceiverOfHistoricalPicks receiverForLottoGrading = new ReceiverOfHistoricalPicks();
            var receiverForKenoGrading = new ReceiverOfHistoricalKeno();
            ReceiverOfHistoricalPicks receiverForDrawings = new ReceiverOfHistoricalPicks();
            var receiverForDomains = new ReceiverOfHistoricalPicks();
            ReceiverOfHistoricalPicks receiverForPrizes = new ReceiverOfHistoricalPicks();

            if (Integration.Db.DBSelected == HistoricalDatabaseType.MySQL.ToString())
            {
                receiverForLottoGrading.InitHistorical(HistoricalDatabaseType.MySQL, Integration.Db.MySQL);
                receiverForKenoGrading.InitHistorical(HistoricalDatabaseType.MySQL, Integration.Db.MySQL);
                receiverForDrawings.InitHistorical(HistoricalDatabaseType.MySQL, Integration.Db.MySQL);
                receiverForDomains.InitHistorical(HistoricalDatabaseType.MySQL, Integration.Db.MySQL);
                receiverForPrizes.InitHistorical(HistoricalDatabaseType.MySQL, Integration.Db.MySQL);
            }
            else if (Integration.Db.DBSelected == HistoricalDatabaseType.SQLServer.ToString())
            {
                receiverForLottoGrading.InitHistorical(HistoricalDatabaseType.SQLServer, Integration.Db.SQLServer);
                receiverForKenoGrading.InitHistorical(HistoricalDatabaseType.SQLServer, Integration.Db.SQLServer);
                receiverForDrawings.InitHistorical(HistoricalDatabaseType.SQLServer, Integration.Db.SQLServer);
                receiverForDomains.InitHistorical(HistoricalDatabaseType.SQLServer, Integration.Db.SQLServer);
                receiverForPrizes.InitHistorical(HistoricalDatabaseType.SQLServer, Integration.Db.SQLServer);
            }
            else if (!String.IsNullOrWhiteSpace(Integration.Db.DBSelected))
            {
                throw new Exception($"There is no connection for {Integration.Db.DBSelected}");
            }

            if (Integration.UseKafka)
            {
                if (String.IsNullOrEmpty(Integration.Db.DBSelected))
                {
                    throw new Exception("Db configuration its required.");
                }

                new ReportController().CreateConsumerForTopics(receiverForLottoGrading, receiverForKenoGrading, receiverForDrawings, receiverForDomains, receiverForPrizes);
            }

            QueryMakerOfHistoricalPicks queryMaker = new QueryMakerOfHistoricalPicks();
            var winnersCurrentMonth = queryMaker.GenerateWinnersOfTheMonthReport(DateTime.Now);
            foreach (var winner in winnersCurrentMonth)
            {
                Reports.ClassifyWinner(winner.GameType, winner.WinnerNumber, winner.Prize, winner.DrawingName, winner.Date);
            }

            ForwardedHeadersOptions options = new ForwardedHeadersOptions();
            options.ForwardedHeaders = ForwardedHeaders.XForwardedFor;
            app.UseForwardedHeaders(options);
        }

    }
}
