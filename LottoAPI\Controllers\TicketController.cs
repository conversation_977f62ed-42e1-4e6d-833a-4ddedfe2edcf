﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using Microsoft.AspNetCore.Mvc;
using GamesEngine.Games.Lotto;
using System.Text;
using System.IO;
using System.Runtime.Serialization.Json;
using GamesEngine.Settings;
using Microsoft.AspNetCore.Authorization;
using Newtonsoft.Json;
using GamesEngine;
using System.Globalization;
using Microsoft.AspNetCore.Http;
using System.Net.Http.Headers;
using GamesEngine.Finance;
using System.Net.Http;
using static LottoAPI.Controllers.CustomerController;
using System.Threading.Tasks;
using Puppeteer.EventSourcing;
using LottoAPI;
using GamesEngine.PurchaseOrders;
using GamesEngine.Domains;
using GamesEngine.Exchange;
using static GamesEngine.Exchange.PaymentProcessorsAndActionsByDomains;
using System.IdentityModel.Tokens.Jwt;
using static GamesEngine.Finance.PaymentChannels;
using ExternalServices;
using town.connectors.commons;
using town.connectors.drivers;
using Connectors.town.connectors.drivers.hades;
using static town.connectors.CustomSettings;
using town.connectors;
using Connectors.town.connectors.driver.transactions;
using GamesEngine.Business;
using static Connectors.town.connectors.drivers.artemis.Fragment_an_Authorization;
using town.connectors.drivers.artemis;
using ToWinByDrawAndNumber = GamesEngine.Finance.ToWinByDrawAndNumber;
using static GamesEngine.Business.ArtemisBehavior;
using static LottoAPI.Controllers.DrawController;
using static LottoAPI.Controllers.TicketController;
using Microsoft.AspNetCore.Components.Forms;
using GamesEngine.Custodian;
using static LottoAPI.Controllers.TicketController.NumbersAndPrizesPerAmount;
using Elastic.Apm.Api.Kubernetes;
using GamesEngine.Gameboards.Lotto;
using Connectors.town.connectors.commons;

namespace LottoAPI.Controllers
{
    public class TicketController : AuthorizeController
    {
        private int CalculateSubticketsNumber(string[] numbersPattern)
        {
            var subticketsNumber = 0;
            if (numbersPattern.Length == 2)
            {
                var pick2 = new Pick2(numbersPattern[0], numbersPattern[1]);
                subticketsNumber = pick2.Count;
            }
            else if (numbersPattern.Length == 3)
            {
                var pick3 = new Pick3(numbersPattern[0], numbersPattern[1], numbersPattern[2]);
                subticketsNumber = pick3.Count;
            }
            else if (numbersPattern.Length == 4)
            {
                var pick4 = new Pick4(numbersPattern[0], numbersPattern[1], numbersPattern[2], numbersPattern[3]);
                subticketsNumber = pick4.Count;
            }
            else if (numbersPattern.Length == 5)
            {
                var pick5 = new Pick5(numbersPattern[0], numbersPattern[1], numbersPattern[2], numbersPattern[3], numbersPattern[4]);
                subticketsNumber = pick5.Count;
            }
            return subticketsNumber;
        }

        private void FixAccountsNumbersForDemoPurposesOnly(TicketsBody body)
        {
            if (string.IsNullOrWhiteSpace(body.AccountNumber))
            {
                body.AccountNumber = "USD";
            }
        }

        const int FAKE_TICKET_NUMBER = -1;
        private static void AppendSequence(ref StringBuilder commandForAuthorizations, ref bool isFirstSequence, ref bool isFirstAdd, int currentAuthorizationId, int consecutive)
        {
            if (!isFirstSequence)
            {
                if (isFirstAdd)
                {
                    isFirstAdd = false;
                    commandForAuthorizations.Append("); auths.Add(");
                }
                else
                    commandForAuthorizations.Append(").Add(");
            }
            if (consecutive == 1)
            {
                commandForAuthorizations.Append(currentAuthorizationId);
            }
            else
            {
                commandForAuthorizations.Append(currentAuthorizationId - consecutive + 1)
                    .Append(", ")
                    .Append(consecutive);
            }
            isFirstSequence = false;
        }

        [HttpPost("api/lotto/ticket/pick")]
        [Authorize(Roles = "player")]
        public async Task<IActionResult> CreateTicketAsync([FromBody] TicketsBody body, [FromHeader(Name = "domain-url")] string domain)
        {
            if (body == null) return BadRequest("Body is required");
            if (body.Tickets == null || body.Tickets.Length==0) return BadRequest($"{nameof(body.Tickets)} is required");
            if (string.IsNullOrWhiteSpace(HttpContext.Request.Headers["domain-url"])) return BadRequest("Header 'domain-url' is not sent in request");
            FixAccountsNumbersForDemoPurposesOnly(body);
            if (string.IsNullOrWhiteSpace(body.AccountNumber)) return BadRequest($"{nameof(body.AccountNumber)} is required.");

            int pickNumber = int.MinValue;
            string strAmount = null;
            IEnumerable<string> datesToPurchase;
            Domain domainObj;
            IDepositResponse response;
            OrderResponse orderResponse;
            byte[] bytes;
            string lockDescription = string.Empty;
            string strProduct = null;
            string selectionMode = string.Empty;
            string dates = string.Empty;
            string strRuleType = string.Empty;
            string includedSubticketsForInput = string.Empty;
            string scriptForExclusions = string.Empty;
            string playerId = Validator.StringEscape(Security.PlayerId(User));
            IActionResult resultCreatingOrder;
            var result = await LottoAPI.Actor.PerformQryAsync(HttpContext, $@"
				{{
                    customer = company.CustomerByPlayerId('{playerId}');
                    player = customer.Player;
                    print player.AgentNumber agentNumber;
				}}
            ");
            if (!(result is OkObjectResult))
            {
                return BadRequest($@"Error:{((ObjectResult)result).Value.ToString()}");
            }

            OkObjectResult o = (OkObjectResult)result;
            string json = o.Value.ToString();
            var playerResponse = JsonConvert.DeserializeObject<PlayerResponse>(json);
            StringBuilder activatorScriptsForQry = new StringBuilder();
            StringBuilder activatorScriptsForCmd = new StringBuilder();
            List<ChosenDrawBody> drawSchedules = null;
            if (playerResponse.Agent == Agents.ARTEMIS)
            {
                OrderResponseWithExternalTickets orderResponseWithExternalTickets = null;
                OrderErrorResponseAccumulated orderErrorResponseAccumulated = new OrderErrorResponseAccumulated();
                StringBuilder states = new StringBuilder();
                StringBuilder withFireBalls = new StringBuilder();
                StringBuilder hours = new StringBuilder();
                StringBuilder strIsPresentPlayerBeforeToCloseStore = new StringBuilder();
                StringBuilder strUseNextDate = new StringBuilder();
                int ticketIndex = 0;
                var lastIndexInGroupOfTickets = body.Tickets.Length - 1;
                bool anyExclusionFromBody = false;
                foreach (var ticket in body.Tickets)
                {
                    if (!string.IsNullOrWhiteSpace(selectionMode) && !selectionMode.Equals(ticket.SelectionMode, StringComparison.OrdinalIgnoreCase)) return BadRequest($"{nameof(ticket.SelectionMode)} must be the same for all tickets");
                    selectionMode = ticket.SelectionMode;
                    drawSchedules = new List<ChosenDrawBody>();

                    if (String.IsNullOrWhiteSpace(ticket.GameType)) return BadRequest($"{nameof(ticket.GameType)} cannot be null");
                    if (ticket.GameType.StartsWith("pick") && ticket.GameType.Length == 5 && ticket.GameType[4] >= '2' && ticket.GameType[4] <= '5')
                    {
                        if (!int.TryParse(ticket.GameType[4].ToString(), out pickNumber))
                        {
                            return BadRequest($"Invalid {nameof(ticket.GameType)} {ticket.GameType}");
                        }
                    }
                    else
                    {
                        return BadRequest($"There is no {nameof(ticket.GameType)} or {nameof(ticket.RuleType)} matching");
                    }

                    strAmount = ticket.Amount.ToString();

                    dates = string.Join(',', ticket.Dates);
                    string statesOfTicket = string.Empty;
                    string withFireBallOfTicket = string.Empty;
                    string hoursOfTicket = string.Empty;
                    string strIsPresentPlayerBeforeToCloseStoreOfTicket = string.Empty;
                    string strUseNextDateOfTicket = string.Empty;
                    var lastDrawIndexInTicket = ticket.DrawSchedules.Length - 1;
                    for (int index = 0; index < ticket.DrawSchedules.Length; index++)
                    {
                        var state = ticket.DrawSchedules[index].State;
                        var hour = ticket.DrawSchedules[index].Hour;
                        var withFireBall = ticket.DrawSchedules[index].WithFireBall;
                        statesOfTicket += state;
                        withFireBallOfTicket += withFireBall;
                        hoursOfTicket += hour;
                        strIsPresentPlayerBeforeToCloseStoreOfTicket += ticket.DrawSchedules[index].IsPresentPlayerBeforeToCloseStore;
                        strUseNextDateOfTicket += ticket.DrawSchedules[index].UseNextDate;
                        drawSchedules.Add(new ChosenDrawBody(state, hour));

                        if (index != lastDrawIndexInTicket)
                        {
                            statesOfTicket += ',';
                            withFireBallOfTicket += ',';
                            hoursOfTicket += ',';
                            strIsPresentPlayerBeforeToCloseStoreOfTicket += ',';
                            strUseNextDateOfTicket += ',';
                        }
                    }

                    states.Append(statesOfTicket);
                    withFireBalls.Append(withFireBallOfTicket);
                    hours.Append(hoursOfTicket);
                    strIsPresentPlayerBeforeToCloseStore.Append(strIsPresentPlayerBeforeToCloseStoreOfTicket);
                    strUseNextDate.Append(strUseNextDateOfTicket);
                    if (ticketIndex != lastIndexInGroupOfTickets)
                    {
                        states.Append(',');
                        withFireBalls.Append(',');
                        hours.Append(',');
                        strIsPresentPlayerBeforeToCloseStore.Append(',');
                        strUseNextDate.Append(',');
                    }

                    if (ticket.Subtickets == null || ticket.Subtickets.Length == 0) return BadRequest($"When you use {nameof(ticket.SelectionMode)} {ticket.SelectionMode} {nameof(ticket.Subtickets)} is required");
                    lastDrawIndexInTicket = ticket.Subtickets.Length - 1;
                    var includedSubtickets = new StringBuilder();
                    for (int index = 0; index < ticket.Subtickets.Length; index++)
                    {
                        if (string.IsNullOrWhiteSpace(ticket.Subtickets[index])) return BadRequest($"Sorry, your purchase cannot be completed at this moment. Please, navigate to the input numbers section and double check that the chosen numbers are written in the correct way, and then try again.");
                        if (includedSubtickets.Length != 0) includedSubtickets.Append("','");
                        includedSubtickets.Append(ticket.Subtickets[index]);
                    }

                    if (ticket.RuleType == "straight")
                    {
                        strRuleType = "Straight";
                    }
                    else if (ticket.RuleType == "boxed")
                    {
                        strRuleType = "Boxed";
                    }
                    else
                    {
                        return BadRequest($"There is no {nameof(ticket.GameType)} or {nameof(ticket.RuleType)} matching");
                    }
                    lockDescription = $"Lotto Ticket Pick {pickNumber} {strRuleType}";

                    includedSubticketsForInput = includedSubtickets.ToString();
                    bool withExcludes = ticket.ExcludeSubtickets.Length > 0;
                    if (withExcludes)
                    {
                        anyExclusionFromBody = true;
                        activatorScriptsForCmd.AppendLine($"excludedSubtickets = ExcludeSubtickets();");
                        var dictExcludeSubticket = new Dictionary<ExcludedSubticketKey, GroupedExcludedSubticket>();
                        foreach (var excludedSubticket in ticket.ExcludeSubtickets)
                        {
                            var key = new ExcludedSubticketKey()
                            {
                                Hour = excludedSubticket.Hour,
                                State = excludedSubticket.State,
                                StrNumbers = string.Join(",", ticket.ExcludeSubtickets.Where(e => excludedSubticket.State == e.State && excludedSubticket.Hour == e.Hour && excludedSubticket.Date == e.Date).Select(e => e.Subticket))
                            };
                            var value = dictExcludeSubticket.GetValueOrDefault(key);
                            if (value == null)
                            {
                                value = new GroupedExcludedSubticket();
                                value.DatesSet.Add(excludedSubticket.Date);
                                value.NumbersSet.Add(excludedSubticket.Subticket);
                                dictExcludeSubticket.Add(key, value);
                            }
                            else
                            {
                                value.DatesSet.Add(excludedSubticket.Date);
                                value.NumbersSet.Add(excludedSubticket.Subticket);
                            }
                        }

                        foreach (var groupedExcludedSubticket in dictExcludeSubticket)
                        {
                            var excludedNumbers = groupedExcludedSubticket.Value.Subtickets;
                            activatorScriptsForQry.AppendLine($"excludedSubtickets.Add(state{groupedExcludedSubticket.Key.State},'{groupedExcludedSubticket.Key.Hour}','{groupedExcludedSubticket.Value.Dates}','{excludedNumbers}');");
                            activatorScriptsForCmd.AppendLine($"excludedSubtickets.Add(state{groupedExcludedSubticket.Key.State},'{groupedExcludedSubticket.Key.Hour}','{groupedExcludedSubticket.Value.Dates}','{excludedNumbers}');");
                        }

                        APMHelper.SetTransactionLabel("Purchase retry", playerId);
                        APMHelper.SetTransactionLabel(playerId, "Purchase retry");
                        APMHelper.EndTransaction();
                    }
                    
                    var script = ArtemisBehavior.CommandsForPurchaseValidation(IdOfLotteryGame.Picks, pickNumber.ToString(), pickNumber, strAmount, dates, statesOfTicket, hoursOfTicket, withFireBallOfTicket, strIsPresentPlayerBeforeToCloseStoreOfTicket, strUseNextDateOfTicket, ticket.SelectionMode, includedSubticketsForInput, strRuleType, body.CurrencyCode, string.Empty, applyToleranceFactor:withExcludes, body.Issued);
                    activatorScriptsForQry.AppendLine(script);

                    scriptForExclusions = $@"
                        print company.Settings.HoursKeepingAliveTickets hoursKeepingAliveTickets;
                        for (datesToPurchase : totalOrderAndExclusionsByToWin.ValidDatesToPurchase)
                        {{
                            print datesToPurchase dateToPurchase;
                        }}
                        for (excludeSubtickets : totalOrderAndExclusionsByToWin.ExclusionsForSingleSelection)
                        {{
                            exclude = excludeSubtickets;
                            print exclude.StateAbb state;
                            print exclude.HourFormattedAsText hour;
                            print exclude.DateFormattedAsText date;
                            print exclude.Subticket subticket;
                            print exclude.PickNumber pick;
                        }}
                    ";

                    var scriptToPrintInternalTicketData =  ArtemisBehavior.CommandsToPrintExternalTicketData();
                    resultCreatingOrder = await LottoAPI.Actor.PerformQryAsync(HttpContext, $@"
				        {{
                            customer = company.CustomerByPlayerId('{playerId}');
                            player = customer.Player;
                            domain = company.Sales.DomainFrom('{domain}');
                            excludedSubtickets = ExcludeSubtickets();
					        {activatorScriptsForQry.ToString()}
                            print nextDatesAccumulator.AnyTicketIsExpired anyTicketIsExpired;
                            print totalOrderAndExclusionsByToWin.TotalOrder total;
                            print totalOrderAndExclusionsByToWin.ErrorMessage errorMessage;
                            print totalOrderAndExclusionsByToWin.IsValidToRetryPurchase isValidToRetryPurchase;
                            print customer.AccountNumber atAddress;
                            print player.AgentNumber agentNumber;
                            {scriptForExclusions}
                            print company.Sales.CurrentStore.Id storeId;
					        print domain.Id domainId;
                            print domain.Url domainUrl;
				            existsDomainInMarketplace = company.Sales.CurrentStore.ExistsDomain(domain.Id);
					        print existsDomainInMarketplace existsDomainInMarketplace;
                            print Now purchaseDate;
                            {scriptToPrintInternalTicketData}
				        }}
                    ");

                    if (!(resultCreatingOrder is OkObjectResult)) return BadRequest("Error validating the purchase data");
                    o = (OkObjectResult)resultCreatingOrder;
                    json = o.Value.ToString();
                    orderResponse = JsonConvert.DeserializeObject<OrderResponseWithExternalTickets>(json);

                    if (!string.IsNullOrWhiteSpace(orderResponse.ErrorMessage))
                    {
                        orderErrorResponseAccumulated.Append(orderResponse);
                    }
                    else if (orderResponse.AnyTicketIsExpired)
                        {
                        result = await LottoAPI.Actor.PerformQryAsync(HttpContext, $@"
					        {{
						        print true anyTicketIsExpired;
					        }}
				        ");
                        return result;
                        }
                    else
                    {
                        orderErrorResponseAccumulated.IsValidToRetryPurchase = true;
                    }

                    if (orderResponseWithExternalTickets == null) orderResponseWithExternalTickets = (OrderResponseWithExternalTickets)orderResponse;
                    else
                    {
                        orderResponseWithExternalTickets.Append((OrderResponseWithExternalTickets)orderResponse);
                    }

                    activatorScriptsForQry.Clear();
                    ticketIndex++;
                }

                if (orderErrorResponseAccumulated.ExcludeSubtickets != null || orderErrorResponseAccumulated.ExclusionsForInputs != null || !string.IsNullOrWhiteSpace(orderErrorResponseAccumulated.ErrorMessage))
                {
                    if (orderErrorResponseAccumulated.ExclusionsForInputs == null) 
                    {
                        if (!anyExclusionFromBody)
                        {
                            APMHelper.SetTransactionLabel("Purchase risk limit", playerId);
                            APMHelper.SetTransactionLabel(playerId, "Purchase risk limit");
                            APMHelper.EndTransaction();
                        }
                        
                        return BadRequest(new OrderErrorResponseForSingleSelection(orderErrorResponseAccumulated)); 
                    }
                    else if (orderErrorResponseAccumulated.ExcludeSubtickets == null) return BadRequest(new OrderErrorResponseForMultiSelection(orderErrorResponseAccumulated));
                    else return BadRequest(orderErrorResponseAccumulated.ErrorMessage);
                }

                if (orderResponseWithExternalTickets.Total <= 0) return BadRequest("Ticket cost must be greater than 0");
                if (orderResponseWithExternalTickets.Tickets.Count > 10000) return BadRequest("The maximum number of tickets that can be purchased is 10000");
                domainObj = new Domain(false, orderResponseWithExternalTickets.DomainId, orderResponseWithExternalTickets.DomainUrl, playerResponse.Agent);
                datesToPurchase = orderResponseWithExternalTickets.DatesToPurchase.Select(date => date.DateToPurchase);
                response = await PaymentChannels.LockWithExternalResponseAsync(
                    HttpContext,
                    orderResponseWithExternalTickets.Agent,
                    orderResponseWithExternalTickets.AtAddress,
                    body.CurrencyCode.ToString(),
                    lockDescription,
                    orderResponseWithExternalTickets.PurchaseDate,
                    string.Empty,
                    Coinage.Coin(body.CurrencyCode),
                    orderResponseWithExternalTickets.Total,
                    orderResponseWithExternalTickets.StoreId,
                    domainObj,
                    orderResponseWithExternalTickets.GetMaxDateToPurchase().AddHours(orderResponseWithExternalTickets.HoursKeepingAliveTickets),
                    orderResponseWithExternalTickets.Tickets
                    );
                FragmentResponse fragmentResponse = (FragmentResponse)response;

                var hasResponseExternalTicketData = fragmentResponse.Response != null && fragmentResponse.Response.tickets != null && fragmentResponse.Response.tickets.Count > 0;
                if (hasResponseExternalTicketData && response.AuthorizationId == 0 && fragmentResponse.Code == AuthorizationResponseCode.OK)
                {
                    StringBuilder betAmounts = new StringBuilder();
                    StringBuilder commandForAuthorizations = new StringBuilder();
                    commandForAuthorizations.Append("auths = AuthorizationsNumbers(");
                    bool isFirstSequence = true;
                    bool isFirstAdd = true;
                    int currentAuthorizationId = 0;
                    int consecutive = 1;
                    foreach (var ticketInfo in fragmentResponse.Response.tickets)
                    {
                        if (betAmounts.Length != 0) betAmounts.Append("','");
                        betAmounts.Append(ticketInfo.risk);

                        var authorizationId = int.Parse(ticketInfo.ticketId);
                        if (currentAuthorizationId == 0)
                        {
                            currentAuthorizationId = authorizationId;
                        }
                        else if (currentAuthorizationId + 1 == authorizationId)
                        {
                            consecutive++;
                        }
                        else
                        {
                            AppendSequence(ref commandForAuthorizations, ref isFirstSequence, ref isFirstAdd, currentAuthorizationId, consecutive);
                            consecutive = 1;
                        }
                        currentAuthorizationId = authorizationId;
                    }
                    AppendSequence(ref commandForAuthorizations, ref isFirstSequence, ref isFirstAdd, currentAuthorizationId, consecutive); 
                    commandForAuthorizations.Append(");");

                    string commandForCreateTicketOrder;
                    if (selectionMode == "SingleInputMultipleAmount")
                    {
                        commandForCreateTicketOrder = $@"myOrder = company.CreateTicketOrderWithMultipleAuthorization(player, '{states}', '{dates}', '{withFireBalls}', '{selectionMode}', {{'{betAmounts}'}}, {{'{includedSubticketsForInput}'}}, '{strRuleType[0]}', myOrder, ticketPick{pickNumber}{strRuleType}Prod, lotto900.NextDatesAccumulator, domain);";
                    }
                    else
                    {
                        var hasExclusions = activatorScriptsForCmd.Length > 0;
                        if (hasExclusions) commandForCreateTicketOrder = $"myOrder = company.CreateTicketOrderWithMultipleAuthorization(player, '{states}', '{dates}', '{withFireBalls}', '{selectionMode}', {strAmount}, {{'{includedSubticketsForInput}'}}, excludedSubtickets, '{strRuleType[0]}', myOrder, ticketPick{pickNumber}{strRuleType}Prod, lotto900.NextDatesAccumulator, domain);";
                        else commandForCreateTicketOrder = $"myOrder = company.CreateTicketOrderWithMultipleAuthorization(player, '{states}', '{dates}', '{withFireBalls}', '{selectionMode}', {strAmount}, {{'{includedSubticketsForInput}'}}, '{strRuleType[0]}', myOrder, ticketPick{pickNumber}{strRuleType}Prod, lotto900.NextDatesAccumulator, domain);";
                    }

                    var purchaseDateAsText = orderResponseWithExternalTickets.PurchaseDate.ToString("MM/dd/yyyy HH:mm:ss");
                    result = await LottoAPI.Actor.PerformCmdAsync(HttpContext, $@"
                    {{
                        print false anyTicketIsExpired;
					    domain = company.Sales.DomainFrom('{domain}');
					    player = company.Players.SearchPlayer('{playerId}');
					    customer = company.CustomerByPlayer(player);
					    {{
                            Eval('orderNumber = ' + company.IdentityOrderNumber + ';');
						    myOrder = company.NewOrder(customer, orderNumber, {body.CurrencyCode});
                            lotto900.NextDatesAccumulator.Calculate(domain, {purchaseDateAsText}, '{pickNumber}', '{dates}','{states}','{hours}', '{withFireBalls}');
                            {activatorScriptsForCmd.ToString()}
						    {commandForCreateTicketOrder}
						    company.AddOrders(myOrder);
						    Eval('lowReference = ' + company.IdentitytBetNumber + ';');
						    Eval('highReference = ' + (lowReference + myOrder.CountOfItems - 1) + ';');
                            {commandForAuthorizations}
						    company.PurchaseTickets(itIsThePresent, myOrder, {purchaseDateAsText}, auths, domain, lowReference, orderNumber, '{fragmentResponse.ProcessorKey}');
                        }}
                    }}
			        ");

                    return result;
                }
                else
                {
                    result = await LottoAPI.Actor.PerformQryAsync(HttpContext, $@"
					    {{
						    Notify Information '{fragmentResponse.Message}';
                            print '{fragmentResponse.Code}' code;
					    }}
				    ");
                    return result;
                }
            }
            else
            {
                activatorScriptsForQry.AppendLine($"totalOrderAndExclusionsByToWin = TotalOrderAndExclusionsByToWin({body.Tickets.Length});");
                activatorScriptsForQry.AppendLine("riskAccumulator = ToWinAccumulator();");

                foreach (var ticket in body.Tickets)
                {
                    var excludedSubtickets = new List<string>();
                    int numberOfSubtickets = 0;

                    string[] numbersPattern = null;
                    switch (ticket.SelectionMode)
                    {
                        case "SingleInputMultipleAmount":
                        case "MultipleInputSingleAmount":
                            if (ticket.Subtickets == null || ticket.Subtickets.Length == 0) return BadRequest($"When you use {nameof(ticket.SelectionMode)} {ticket.SelectionMode} {nameof(ticket.Subtickets)} is required");
                            foreach (string subticket in ticket.Subtickets)
                            {
                                if (string.IsNullOrWhiteSpace(subticket)) return BadRequest($"Sorry, your purchase cannot be completed at this moment. Please, navigate to the input numbers section and double check that the chosen numbers are written in the correct way, and then try again.");
                            }
                            includedSubticketsForInput = string.Join(',', ticket.Subtickets);
                            numberOfSubtickets = ticket.Subtickets.Length;
                            break;
                        case "balls":
                            numbersPattern = ticket.NumbersPattern;
                            if (numbersPattern == null || numbersPattern.Length == 0) return BadRequest($"When you use {nameof(ticket.SelectionMode)} {ticket.SelectionMode} {nameof(ticket.NumbersPattern)} is required");
                            foreach (string pattern in numbersPattern)
                            {
                                if (string.IsNullOrWhiteSpace(pattern)) return BadRequest($"Sorry, your purchase cannot be completed at this moment. Please, navigate to the input numbers section and double check that the chosen numbers are written in the correct way, and then try again.");
                            }
                            numberOfSubtickets = CalculateSubticketsNumber(numbersPattern);
                            break;
                        default:
                            return BadRequest($"{nameof(ticket.SelectionMode)} {ticket.SelectionMode} is not valid");
                    }
                    if (ticket.Dates == null || ticket.Dates.Length == 0) return BadRequest($"Dates must be provided");
                    dates = string.Join(',', ticket.Dates);

                    if (ticket.DrawSchedules == null || ticket.DrawSchedules.Length == 0) return BadRequest($"DrawSchedules must be provided");
                    var states = string.Join(',', ticket.DrawSchedules.Select(x => x.State));
                    var withFireBalls = string.Join(',', ticket.DrawSchedules.Select(x => x.WithFireBall));
                    var hours = string.Join(',', ticket.DrawSchedules.Select(x => x.Hour));
                    var drawDescription = string.Join(", ", ticket.DrawSchedules.Select(x => $"{x.State}:{x.Hour}"));
                    var collectionOfIsPresentPlayerBeforeToCloseStore = ticket.DrawSchedules.Select(drawSchedule => drawSchedule.IsPresentPlayerBeforeToCloseStore);
                    var strIsPresentPlayerBeforeToCloseStore = string.Join(',', collectionOfIsPresentPlayerBeforeToCloseStore);
                    var collectionOfUseNextDate = ticket.DrawSchedules.Select(drawSchedule => drawSchedule.UseNextDate);
                    var strUseNextDate = string.Join(',', collectionOfUseNextDate);
                    strAmount = ticket.Amount.ToString();

                    if (String.IsNullOrWhiteSpace(ticket.GameType)) return BadRequest($"{nameof(ticket.GameType)} cannot be null");
                    if (ticket.GameType.StartsWith("pick") && ticket.GameType.Length == 5 && ticket.GameType[4] >= '2' && ticket.GameType[4] <= '5')
                    {
                        pickNumber = ticket.GameType[4];
                    }
                    else
                    {
                        return BadRequest($"There is no {nameof(ticket.GameType)} or {nameof(ticket.RuleType)} matching");
                    }

                    if (numbersPattern != null && numbersPattern.Length != pickNumber) return BadRequest($"Numbers pattern does not match with pick {pickNumber}");
                    string numbersWithComma = numbersPattern == null ? string.Empty : string.Join(",", numbersPattern);
                    var numbersDescription = string.IsNullOrWhiteSpace(numbersWithComma) ? includedSubticketsForInput : numbersWithComma;

                    if (ticket.RuleType == "straight")
                    {
                        strRuleType = "Straight";
                    }
                    else if (ticket.RuleType == "boxed")
                    {
                        strRuleType = "Boxed";
                    }
                    else
                    {
                        return BadRequest($"There is no {nameof(ticket.GameType)} or {nameof(ticket.RuleType)} matching");
                    }

                    strProduct = $"ticketPick{pickNumber}{strRuleType}Prod";
                    lockDescription = $"Lotto - Pick {pickNumber} {strRuleType} {dates}";
                    activatorScriptsForQry.AppendLine($"lotto900.IsBetAmountValidFor({pickNumber}, player, domain, {strAmount});");
                    activatorScriptsForQry.AppendLine($"nextDatesAccumulator = NextDatesAccumulator(lotto900, domain, Now, '{pickNumber}', '{dates}','{states}','{hours}', '{strIsPresentPlayerBeforeToCloseStore}', '{strUseNextDate}');");
                    activatorScriptsForQry.AppendLine($"excludedSubtickets = ExcludeSubtickets();");

                    activatorScriptsForCmd.AppendLine($"lotto900.IsBetAmountValidFor({pickNumber}, player, domain, {strAmount});");
                    activatorScriptsForCmd.AppendLine($"nextDatesAccumulator = NextDatesAccumulator(lotto900, domain, Now, '{pickNumber}', '{dates}','{states}','{hours}', '{strIsPresentPlayerBeforeToCloseStore}', '{strUseNextDate}');");

                    bool withExcludes = ticket.ExcludeSubtickets.Count() > 0;

                    if (withExcludes)
                    {
                        activatorScriptsForCmd.AppendLine($"excludedSubtickets = ExcludeSubtickets();");

                        var dictExcludeSubticket = new Dictionary<ExcludedSubticketKey, GroupedExcludedSubticket>();
                        foreach (var excludedSubticket in ticket.ExcludeSubtickets)
                        {
                            var key = new ExcludedSubticketKey()
                            {
                                Hour = excludedSubticket.Hour,
                                State = excludedSubticket.State,
                                StrNumbers = string.Join(",", ticket.ExcludeSubtickets.Where(e => excludedSubticket.State == e.State && excludedSubticket.Hour == e.Hour && excludedSubticket.Date == e.Date).Select(e => e.Subticket))
                            };
                            var value = dictExcludeSubticket.GetValueOrDefault(key);
                            if (value == null)
                            {
                                value = new GroupedExcludedSubticket();
                                value.DatesSet.Add(excludedSubticket.Date);
                                value.NumbersSet.Add(excludedSubticket.Subticket);
                                dictExcludeSubticket.Add(key, value);
                            }
                            else
                            {
                                value.DatesSet.Add(excludedSubticket.Date);
                                value.NumbersSet.Add(excludedSubticket.Subticket);
                            }
                        }
                        foreach (var groupedExcludedSubticket in dictExcludeSubticket)
                        {
                            var excludedNumbers = groupedExcludedSubticket.Value.Subtickets;
                            activatorScriptsForQry.AppendLine($"excludedSubtickets.Add(state{groupedExcludedSubticket.Key.State},'{groupedExcludedSubticket.Key.Hour}','{groupedExcludedSubticket.Value.Dates}','{excludedNumbers}');");
                            activatorScriptsForCmd.AppendLine($"excludedSubtickets.Add(state{groupedExcludedSubticket.Key.State},'{groupedExcludedSubticket.Key.Hour}','{groupedExcludedSubticket.Value.Dates}','{excludedNumbers}');");
                        }

                        activatorScriptsForQry.Append($@"totalOrderAndExclusionsByToWin = company.TotalTicketOrderAndExclusionsByToWin(domain, '{states}', '{dates}', '{withFireBalls}', '{numbersWithComma}', '{ticket.SelectionMode}', {strAmount}, '{includedSubticketsForInput}', '{strRuleType[0]}', excludedSubtickets, {strProduct}, nextDatesAccumulator, riskAccumulator, totalOrderAndExclusionsByToWin, '{body.CurrencyCode}', '');");
                        activatorScriptsForCmd.Append($@"myOrder = company.CreateTicketOrder(lotto900, player, '{states}', '{dates}', '{withFireBalls}', '{numbersWithComma}', '{ticket.SelectionMode}', {strAmount}, '{includedSubticketsForInput}', excludedSubtickets, '{strRuleType[0]}', myOrder, {strProduct}, nextDatesAccumulator, domain);");
                    }
                    else
                    {
                        activatorScriptsForQry.Append($@"totalOrderAndExclusionsByToWin = company.TotalTicketOrderAndExclusionsByToWin(domain, '{states}', '{dates}', '{withFireBalls}', '{numbersWithComma}', '{ticket.SelectionMode}', {strAmount}, '{includedSubticketsForInput}', '{strRuleType[0]}', {strProduct}, nextDatesAccumulator, riskAccumulator, totalOrderAndExclusionsByToWin, '{body.CurrencyCode}', '');");
                        activatorScriptsForCmd.Append($@"myOrder = company.CreateTicketFullOrder(player, '{states}', '{dates}', '{withFireBalls}', '{numbersWithComma}', '{ticket.SelectionMode}', {strAmount}, '{includedSubticketsForInput}', '{strRuleType[0]}', myOrder, {strProduct}, nextDatesAccumulator, domain);");
                    }
                }

                scriptForExclusions = $@"
                    print company.Settings.HoursKeepingAliveTickets hoursKeepingAliveTickets;
                    print totalOrderAndExclusionsByToWin.MaxValidDateToPurchase maxValidDateToPurchase;
                    for (datesToPurchase : totalOrderAndExclusionsByToWin.ValidDatesToPurchase)
                    {{
                        print datesToPurchase dateToPurchase;
                    }}
                ";
                if (body.Tickets.Length == 1)
                {
                    scriptForExclusions += $@"
                    for (excludeSubtickets : totalOrderAndExclusionsByToWin.ExclusionsForSingleSelection)
                    {{
                        exclude = excludeSubtickets;
                        print exclude.StateAbb state;
                        print exclude.HourFormattedAsText hour;
                        print exclude.DateFormattedAsText date;
                        print exclude.Subticket subticket;
                        print exclude.PickNumber pick;
                    }}
                ";
                }
                else if (body.Tickets.Length == 2)
                {
                    scriptForExclusions += $@"
                    for (exclusionsForBalls : totalOrderAndExclusionsByToWin.ExclusionsForMultiSelectionBalls)
                    {{
                        exclude = exclusionsForBalls;
                        print exclude.StateAbb state;
                        print exclude.HourFormattedAsText hour;
                        print exclude.DateFormattedAsText date;
                        print exclude.Subticket subticket;
                    }}
                    for (exclusionsForInputs : totalOrderAndExclusionsByToWin.ExclusionsForMultiSelectionInputs)
                    {{
                        exclude = exclusionsForInputs;
                        print exclude.StateAbb state;
                        print exclude.HourFormattedAsText hour;
                        print exclude.DateFormattedAsText date;
                        print exclude.Subticket subticket;
                    }}
                ";
                }
                else
                {
                    return BadRequest($"Only 1 or 2 group of tickets can be bought");
                }

                resultCreatingOrder = await LottoAPI.Actor.PerformQryAsync(HttpContext, $@"
				{{
                    customer = company.CustomerByPlayerId('{playerId}');
                    player = customer.Player;
                    domain = company.Sales.DomainFrom('{domain}');
					{activatorScriptsForQry.ToString()}
                    print nextDatesAccumulator.AnyTicketIsExpired anyTicketIsExpired;
                    print totalOrderAndExclusionsByToWin.TotalOrder total;
                    print totalOrderAndExclusionsByToWin.ErrorMessage errorMessage;
                    print totalOrderAndExclusionsByToWin.IsValidToRetryPurchase isValidToRetryPurchase;
                    print customer.AccountNumber atAddress;
                    print player.AgentNumber agentNumber;
                    {scriptForExclusions}
                    print company.Sales.CurrentStore.Id storeId;
					print domain.Id domainId;
                    print domain.Url domainUrl;
				    existsDomainInMarketplace = company.Sales.CurrentStore.ExistsDomain(domain.Id);
					print existsDomainInMarketplace existsDomainInMarketplace;
                    print Now purchaseDate;
				}}
            ");

                if (!(resultCreatingOrder is OkObjectResult)) return BadRequest("Error validating the purchase data");
                bytes = Encoding.Unicode.GetBytes(((OkObjectResult)resultCreatingOrder).Value.ToString());
                using (MemoryStream mst = new MemoryStream(bytes))
                {
                    var serializer = new DataContractJsonSerializer(typeof(OrderResponse));
                    orderResponse = (OrderResponse)serializer.ReadObject(mst);
                }

            if (! string.IsNullOrWhiteSpace(orderResponse.ErrorMessage))
            {
                if (orderResponse.ExclusionsForBalls == null && orderResponse.ExclusionsForInputs == null) return BadRequest(new OrderErrorResponseForSingleSelection(orderResponse));
                else if (orderResponse.ExcludeSubtickets == null) return BadRequest(new OrderErrorResponseForMultiSelection(orderResponse));
                else return BadRequest(orderResponse.ErrorMessage);
            }
            else if (orderResponse.AnyTicketIsExpired)
            {
                result = await LottoAPI.Actor.PerformQryAsync(HttpContext, $@"
					{{
						print true anyTicketIsExpired;
					}}
				");
                    return result;
                }

                if (orderResponse.Total <= 0) return BadRequest("Ticket cost must be greater than 0");

                domainObj = new Domain(false, orderResponse.DomainId, orderResponse.DomainUrl, playerResponse.Agent);
                datesToPurchase = orderResponse.DatesToPurchase.Select(date => date.DateToPurchase);

                response = await PaymentChannels.LockAsync(
                HttpContext,
                playerResponse.Agent,
                orderResponse.AtAddress,
                body.CurrencyCode.ToString(),
                lockDescription,
                orderResponse.PurchaseDate,
                string.Empty,
                Coinage.Coin(body.CurrencyCode),
                orderResponse.Total,
                orderResponse.StoreId,
                domainObj,
                orderResponse.MaxDateToPurchase.AddHours(orderResponse.HoursKeepingAliveTickets)
                );

                if (response.AuthorizationId != 0 && response.AuthorizationId != FAKE_TICKET_NUMBER)
                {
                    var purchaseDateAsText = orderResponse.PurchaseDate.ToString("MM/dd/yyyy HH:mm:ss");
                    result = await LottoAPI.Actor.PerformCmdAsync(HttpContext, $@"
					{{
                        print false anyTicketIsExpired;
						domain = company.Sales.DomainFrom('{domain}');
						player = company.Players.SearchPlayer('{playerId}');
						customer = company.CustomerByPlayer(player);
						{{
							Eval('orderNumber = ' + company.IdentityOrderNumber + ';');
							myOrder = company.NewOrder(customer, orderNumber, '{body.CurrencyCode}');
                            agent = marketplace.SearchAgentInFirstLevel(customer.AffiliateIdAsText);
                            myOrder.Agent = agent;
							{activatorScriptsForCmd.ToString().Replace("Now", purchaseDateAsText)}
							company.AddOrders(myOrder);
							Eval('lowReference = ' + company.IdentitytBetNumber + ';');
							Eval('highReference = ' + (lowReference + myOrder.CountOfItems - 1) + ';');
							company.PurchaseTickets(itIsThePresent, myOrder, {purchaseDateAsText}, {response.AuthorizationId}, domain, lowReference, highReference, orderNumber);
						}}
					}}
				");
                    return result;
                }
                else
                {
                    result = await LottoAPI.Actor.PerformQryAsync(HttpContext, $@"
					{{
						Notify Information 'Sorry, your purchase cannot be completed at this moment. Please try again. If the problem persists, please contact customer service.';
					}}
				");
                    return result;
                }
            }

        }

        private bool IsValidSelectionMode(IdOfLotteryGame lotteryGameType, Ticket[] Tickets)
        {
            string selectionMode = null;
            foreach (var ticket in Tickets)
            {
                if (!string.IsNullOrWhiteSpace(selectionMode) && !selectionMode.Equals(ticket.SelectionMode, StringComparison.OrdinalIgnoreCase)) return false;
                selectionMode = ticket.SelectionMode;
            }
            return true;
        }

        private bool IsValidGameType(IdOfLotteryGame lotteryGameType, Ticket[] Tickets)
        {
            string gameType = null;
            foreach (var ticket in Tickets)
            {
                switch (lotteryGameType)
                {
                    case IdOfLotteryGame.Triz:
                        if (ticket.GameType != "triz") return false;
                        gameType = "triz";
                        break;
                    case IdOfLotteryGame.Picks:
                        if (ticket.GameType.StartsWith("pick") && ticket.GameType.Length == 5 && ticket.GameType[4] >= '2' && ticket.GameType[4] <= '5')
                        {
                            gameType = ticket.GameType[4].ToString();
                        }
                        else
                        {
                            return false;
                        }
                        break;
                }

            }
            return true;
        }

        private bool IsValidFireBall(IdOfLotteryGame lotteryGameType, Ticket[] Tickets)
        {
            foreach (var ticket in Tickets)
            {
                for (int index = 0; index < ticket.Subtickets.Length; index++)
                {
                    for (int indexDrawSchedules = 0; indexDrawSchedules < ticket.DrawSchedules.Length; indexDrawSchedules++)
                    {
                        bool withFireBall = ticket.DrawSchedules[indexDrawSchedules].WithFireBall;
                        switch (lotteryGameType)
                        {
                            case IdOfLotteryGame.Triz:
                                if (withFireBall) return false;
                                break;
                        }
                    }
                }

            }
            return true;
        }

        [HttpPost("api/triz/ticket/MultipleInputMultipleAmount")]
        [Authorize(Roles = "player")]
        public async Task<IActionResult> TrizCreateTicketMultipleInputMultipleAmountAsync([FromBody] TicketsBody body, [FromHeader(Name = "domain-url")] string domain)
        {
            if (body == null) return BadRequest("Body is required");
            if (body.Tickets == null) return BadRequest($"{nameof(body.Tickets)} is required");
            if (String.IsNullOrWhiteSpace(HttpContext.Request.Headers["domain-url"])) return NotFound("Header 'domain-url' is not sent in request");
            FixAccountsNumbersForDemoPurposesOnly(body);
            if (string.IsNullOrWhiteSpace(body.AccountNumber)) return BadRequest($"{nameof(body.AccountNumber)} is required.");

            var checkResult = IsValidSelectionMode(IdOfLotteryGame.Triz, body.Tickets);
            if (!checkResult) return BadRequest("Invalid selection mode");
            checkResult = IsValidGameType(IdOfLotteryGame.Triz, body.Tickets);
            if (!checkResult) return BadRequest(body.Tickets[0].GameType + " is not valid");
            checkResult = IsValidFireBall(IdOfLotteryGame.Triz, body.Tickets);
            if (!checkResult) return BadRequest(body.Tickets[0].GameType + " is not valid");

            return await CreateTicketMultipleInputMultipleAmountAsync(IdOfLotteryGame.Triz, body, domain);
        }

        [HttpPost("api/picks/ticket/MultipleInputMultipleAmount")]
        [Authorize(Roles = "player")]
        public async Task<IActionResult> PicksCreateTicketMultipleInputMultipleAmountAsync([FromBody] TicketsBody body, [FromHeader(Name = "domain-url")] string domain)
        {
            if (body == null) return BadRequest("Body is required");
            if (body.Tickets == null) return BadRequest($"{nameof(body.Tickets)} is required");
            if (String.IsNullOrWhiteSpace(HttpContext.Request.Headers["domain-url"])) return NotFound("Header 'domain-url' is not sent in request");
            FixAccountsNumbersForDemoPurposesOnly(body);
            if (string.IsNullOrWhiteSpace(body.AccountNumber)) return BadRequest($"{nameof(body.AccountNumber)} is required.");

            var checkResult = IsValidSelectionMode(IdOfLotteryGame.Picks, body.Tickets);
            if (!checkResult) return BadRequest("Invalid selection mode");
            checkResult = IsValidGameType(IdOfLotteryGame.Picks, body.Tickets);
            if (!checkResult) return BadRequest(body.Tickets[0].GameType + " is not valid");
            checkResult = IsValidFireBall(IdOfLotteryGame.Picks, body.Tickets);
            if (!checkResult) return BadRequest(body.Tickets[0].GameType + " is not valid");

            return await CreateTicketMultipleInputMultipleAmountAsync(IdOfLotteryGame.Picks, body, domain);
        }

        private async Task<IActionResult> CreateTicketMultipleInputMultipleAmountAsync(IdOfLotteryGame lotteryGameType, [FromBody] TicketsBody body, [FromHeader(Name = "domain-url")] string domain)
        {
            if (body == null) return BadRequest("Body is required");
            if (body.Tickets == null) return BadRequest($"{nameof(body.Tickets)} is required");
            if (String.IsNullOrWhiteSpace(HttpContext.Request.Headers["domain-url"])) return NotFound("Header 'domain-url' is not sent in request");
            FixAccountsNumbersForDemoPurposesOnly(body);
            if (string.IsNullOrWhiteSpace(body.AccountNumber)) return BadRequest($"{nameof(body.AccountNumber)} is required.");

            string gameType = string.Empty;
            string strAmount = null;
            IEnumerable<string> datesToPurchase;
            Domain domainObj;
            IDepositResponse response;
            OrderResponse orderResponse;
            byte[] bytes;
            string lockDescription = string.Empty;
            string strProduct = null;
            string selectionMode = string.Empty;
            string dates = string.Empty;
            string strRuleType = string.Empty;
            string includedSubticketsForInput = string.Empty;
            string scriptForExclusions = string.Empty;
            string playerId = Validator.StringEscape(Security.PlayerId(User));
            IActionResult resultCreatingOrder;
            var result = await LottoAPI.Actor.PerformQryAsync(HttpContext, $@"
		        {{
                    customer = company.CustomerByPlayerId('{playerId}');
                    player = customer.Player;
                    print player.AgentNumber agentNumber;
		        }}
            ");
            if (!(result is OkObjectResult))
            {
                throw new GameEngineException($@"Error:{((ObjectResult)result).Value.ToString()}");
            }

            OkObjectResult o = (OkObjectResult)result;
            string json = o.Value.ToString();
            var playerResponse = JsonConvert.DeserializeObject<PlayerResponse>(json);
            StringBuilder activatorScriptsForQry = new StringBuilder();
            StringBuilder activatorScriptsForCmd = new StringBuilder();
            FragmentResponse fragmentResponse = default(FragmentResponse);
            List<ChosenDrawBody> drawSchedules = null;
            if (playerResponse.Agent == Agents.ARTEMIS)
            {
                OrderResponseWithExternalTickets orderResponseWithExternalTickets = null;
                OrderErrorResponseAccumulated orderErrorResponseAccumulated = new OrderErrorResponseAccumulated();
                StringBuilder states = new StringBuilder();
                StringBuilder hours = new StringBuilder();
                StringBuilder withFireBalls = new StringBuilder();
                List<bool> withFireBallsList = new List<bool>();
                StringBuilder strIsPresentPlayerBeforeToCloseStore = new StringBuilder();
                StringBuilder strUseNextDate = new StringBuilder();
                Queue<string> queueSubtickets = new Queue<string>();
                int ticketIndex = 0;
                var lastIndexInGroupOfTickets = body.Tickets.Length - 1;
                foreach (var ticket in body.Tickets)
                {
                    if (!string.IsNullOrWhiteSpace(selectionMode) && !selectionMode.Equals(ticket.SelectionMode, StringComparison.OrdinalIgnoreCase)) return BadRequest($"{nameof(ticket.SelectionMode)} must be the same for all tickets");
                    selectionMode = ticket.SelectionMode;
                    drawSchedules = new List<ChosenDrawBody>();

                    switch (lotteryGameType)
                    {
                        case IdOfLotteryGame.Triz:
                            gameType = "triz";
                            break;
                        case IdOfLotteryGame.Picks:
                            gameType = ticket.GameType;
                            break;
                        default:
                            return BadRequest($"There is no {nameof(lotteryGameType)} matching");
                    }

                    strAmount = ticket.Amount.ToString();

                    dates = string.Join(',', ticket.Dates);
                    string statesOfTicket = string.Empty;
                    string hoursOfTicket = string.Empty;
                    string withFireBallsOfTicket = string.Empty;
                    string strIsPresentPlayerBeforeToCloseStoreOfTicket = string.Empty;
                    string strUseNextDateOfTicket = string.Empty;
                    var lastDrawIndexInTicket = ticket.DrawSchedules.Length - 1;
                    for (int index = 0; index < ticket.DrawSchedules.Length; index++)
                    {
                        var state = ticket.DrawSchedules[index].State;
                        var hour = ticket.DrawSchedules[index].Hour;
                        var withFireBall = ticket.DrawSchedules[index].WithFireBall;
                        statesOfTicket += state;
                        hoursOfTicket += hour;
                        withFireBallsOfTicket += withFireBall;
                        strIsPresentPlayerBeforeToCloseStoreOfTicket += ticket.DrawSchedules[index].IsPresentPlayerBeforeToCloseStore;
                        strUseNextDateOfTicket += ticket.DrawSchedules[index].UseNextDate;
                        drawSchedules.Add(new ChosenDrawBody(state, hour));

                        if (index != lastDrawIndexInTicket)
                        {
                            statesOfTicket += ',';
                            hoursOfTicket += ',';
                            withFireBallsOfTicket += ',';
                            strIsPresentPlayerBeforeToCloseStoreOfTicket += ',';
                            strUseNextDateOfTicket += ',';
                        }
                    }

                    states.Append(statesOfTicket);
                    hours.Append(hoursOfTicket);
                    withFireBalls.Append(withFireBallsOfTicket);
                    strIsPresentPlayerBeforeToCloseStore.Append(strIsPresentPlayerBeforeToCloseStoreOfTicket);
                    strUseNextDate.Append(strUseNextDateOfTicket);
                    if (ticketIndex != lastIndexInGroupOfTickets)
                    {
                        states.Append(',');
                        hours.Append(',');
                        withFireBalls.Append(',');
                        strIsPresentPlayerBeforeToCloseStore.Append(',');
                        strUseNextDate.Append(',');
                    }

                    if (ticket.Subtickets == null || ticket.Subtickets.Length == 0) return BadRequest($"When you use {nameof(ticket.SelectionMode)} {ticket.SelectionMode} {nameof(ticket.Subtickets)} is required");
                    lastDrawIndexInTicket = ticket.Subtickets.Length - 1;
                    var includedSubtickets = new StringBuilder();
                    for (int index = 0; index < ticket.Subtickets.Length; index++)
                    {
                        string subTicket = ticket.Subtickets[index];
                        if (string.IsNullOrWhiteSpace(subTicket)) return BadRequest($"Sorry, your purchase cannot be completed at this moment. Please, navigate to the input numbers section and double check that the chosen numbers are written in the correct way, and then try again.");
                        if (includedSubtickets.Length != 0) includedSubtickets.Append("','");

                        includedSubtickets.Append(subTicket);

                        for (int indexDrawSchedules = 0; indexDrawSchedules < ticket.DrawSchedules.Length; indexDrawSchedules++)
                        {
                            bool withFireBall = ticket.DrawSchedules[indexDrawSchedules].WithFireBall;
                            withFireBallsList.Add(withFireBall);
                            queueSubtickets.Enqueue(subTicket);
                        }
                    }

                    if (ticket.RuleType == "straight")
                    {
                        strRuleType = "Straight";
                    }
                    else if (ticket.RuleType == "boxed")
                    {
                        strRuleType = "Boxed";
                    }
                    else
                    {
                        return BadRequest($"There is no {nameof(ticket.GameType)} or {nameof(ticket.RuleType)} matching");
                    }
                    lockDescription = $"Lotto Ticket {gameType} {strRuleType}";

                    includedSubticketsForInput = includedSubtickets.ToString();
                    string firstSubTicket = ticket.Subtickets.First();
                    int pickNumberProduct = firstSubTicket.Replace(Pick2.SPLIT_BEGINNING, string.Empty).Length;
                    var script = ArtemisBehavior.CommandsForPurchaseValidation(lotteryGameType, gameType, pickNumberProduct, strAmount, dates, statesOfTicket, hoursOfTicket, withFireBallsOfTicket, strIsPresentPlayerBeforeToCloseStoreOfTicket, strUseNextDateOfTicket, ticket.SelectionMode, includedSubticketsForInput, strRuleType, body.CurrencyCode, string.Empty, applyToleranceFactor:false, issued: string.Empty);
                    activatorScriptsForQry.AppendLine(script);

                    scriptForExclusions = $@"
                        print company.Settings.HoursKeepingAliveTickets hoursKeepingAliveTickets;
                        for (datesToPurchase : totalOrderAndExclusionsByToWin.ValidDatesToPurchase)
                        {{
                            print datesToPurchase dateToPurchase;
                        }}
                        for (excludeSubtickets : totalOrderAndExclusionsByToWin.ExclusionsForSingleSelection)
                        {{
                            exclude = excludeSubtickets;
                            print exclude.StateAbb state;
                            print exclude.HourFormattedAsText hour;
                            print exclude.DateFormattedAsText date;
                            print exclude.Subticket subticket;
                            print exclude.PickNumber pick;
                        }}
                    ";

                    var scriptToPrintInternalTicketData = ArtemisBehavior.CommandsToPrintExternalTicketData();
                    resultCreatingOrder = await LottoAPI.Actor.PerformQryAsync(HttpContext, $@"
		                {{
                            customer = company.CustomerByPlayerId('{playerId}');
                            player = customer.Player;
                            domain = company.Sales.DomainFrom('{domain}');
                            excludedSubtickets = ExcludeSubtickets();
			                {activatorScriptsForQry.ToString()}
                            print nextDatesAccumulator.AnyTicketIsExpired anyTicketIsExpired;
                            print totalOrderAndExclusionsByToWin.TotalOrder total;
                            print totalOrderAndExclusionsByToWin.ErrorMessage errorMessage;
                            print totalOrderAndExclusionsByToWin.IsValidToRetryPurchase isValidToRetryPurchase;
                            print customer.AccountNumber atAddress;
                            print player.AgentNumber agentNumber;
                            {scriptForExclusions}
                            print company.Sales.CurrentStore.Id storeId;
			                print domain.Id domainId;
                            print domain.Url domainUrl;
		                    existsDomainInMarketplace = company.Sales.CurrentStore.ExistsDomain(domain.Id);
			                print existsDomainInMarketplace existsDomainInMarketplace;
                            print Now purchaseDate;
                            {scriptToPrintInternalTicketData}
		                }}
                    ");

                    if (!(resultCreatingOrder is OkObjectResult)) return BadRequest("Error validating the purchase data");
                    o = (OkObjectResult)resultCreatingOrder;
                    json = o.Value.ToString();
                    orderResponse = JsonConvert.DeserializeObject<OrderResponseWithExternalTickets>(json);

                    if (!string.IsNullOrWhiteSpace(orderResponse.ErrorMessage))
                    {
                        orderErrorResponseAccumulated.Append(orderResponse);
                    }
                    else if (orderResponse.AnyTicketIsExpired)
                    {
                        result = await LottoAPI.Actor.PerformQryAsync(HttpContext, $@"
			                {{
				                print true anyTicketIsExpired;
			                }}
		                ");
                        return result;
                    }
                    else
                    {
                        orderErrorResponseAccumulated.IsValidToRetryPurchase = true;
                    }

                    if (orderResponseWithExternalTickets == null) orderResponseWithExternalTickets = (OrderResponseWithExternalTickets)orderResponse;
                    else
                    {
                        orderResponseWithExternalTickets.Append((OrderResponseWithExternalTickets)orderResponse);
                    }

                    activatorScriptsForQry.Clear();
                    ticketIndex++;
                }

                if (orderErrorResponseAccumulated.ExcludeSubtickets != null || orderErrorResponseAccumulated.ExclusionsForInputs != null || !string.IsNullOrWhiteSpace(orderErrorResponseAccumulated.ErrorMessage))
                {
                    if (orderErrorResponseAccumulated.ExclusionsForInputs == null) return BadRequest(new OrderErrorResponseForSingleSelection(orderErrorResponseAccumulated));
                    else if (orderErrorResponseAccumulated.ExcludeSubtickets == null) return BadRequest(new OrderErrorResponseForMultiSelection(orderErrorResponseAccumulated));
                    else return BadRequest(orderErrorResponseAccumulated.ErrorMessage);
                }

                if (orderResponseWithExternalTickets.Total <= 0) return BadRequest("Ticket cost must be greater than 0");
                if (orderResponseWithExternalTickets.Tickets.Count > 10000) return BadRequest("The maximum number of tickets that can be purchased is 10000");
                domainObj = new Domain(false, orderResponseWithExternalTickets.DomainId, orderResponseWithExternalTickets.DomainUrl, playerResponse.Agent);
                datesToPurchase = orderResponseWithExternalTickets.DatesToPurchase.Select(date => date.DateToPurchase);
                response = await PaymentChannels.LockWithExternalResponseAsync(
                    HttpContext,
                    orderResponseWithExternalTickets.Agent,
                    orderResponseWithExternalTickets.AtAddress,
                    body.CurrencyCode.ToString(),
                    lockDescription,
                    orderResponseWithExternalTickets.PurchaseDate,
                    string.Empty,
                    Coinage.Coin(body.CurrencyCode),
                    orderResponseWithExternalTickets.Total,
                    orderResponseWithExternalTickets.StoreId,
                    domainObj,
                    orderResponseWithExternalTickets.GetMaxDateToPurchase().AddHours(orderResponseWithExternalTickets.HoursKeepingAliveTickets),
                    orderResponseWithExternalTickets.Tickets
                    );
                fragmentResponse = (FragmentResponse)response;

                var hasResponseExternalTicketData = fragmentResponse.Response != null && fragmentResponse.Response.tickets != null && fragmentResponse.Response.tickets.Count > 0;
                if (hasResponseExternalTicketData && response.AuthorizationId == 0 && fragmentResponse.Code == AuthorizationResponseCode.OK)
                {
                    StringBuilder commandForAuthorizations = new StringBuilder();
                    commandForAuthorizations.Append("auths = AuthorizationsNumbers(");
                    bool isFirstSequence = true;
                    bool isFirstAdd = true;
                    int currentAuthorizationId = 0;
                    int consecutive = 1;
                    var numbersAndPrizesPerAmount = new NumbersAndPrizesPerAmount();
                    if (withFireBallsList.Count() != fragmentResponse.Response.tickets.Count) return BadRequest("The total tickets withFireBalls must be the same as the number of tickets in the payment channel.");
                    if (queueSubtickets.Count != fragmentResponse.Response.tickets.Count) return BadRequest("The total tickets must be the same as the number of tickets in the response of payment channel.");
                    for (var ticketInfoIndex = 0; ticketInfoIndex < fragmentResponse.Response.tickets.Count; ticketInfoIndex++)
                    {
                        var ticketInfo = fragmentResponse.Response.tickets[ticketInfoIndex];
                        bool withFireBall = withFireBallsList[ticketInfoIndex];

                        var authorizationId = int.Parse(ticketInfo.ticketId);
                        if (currentAuthorizationId == 0)
                        {
                            currentAuthorizationId = authorizationId;
                        }
                        else if (currentAuthorizationId + 1 == authorizationId)
                        {
                            consecutive++;
                        }
                        else
                        {
                            AppendSequence(ref commandForAuthorizations, ref isFirstSequence, ref isFirstAdd, currentAuthorizationId, consecutive);
                            consecutive = 1;
                        }
                        currentAuthorizationId = authorizationId;
                        string ticketInfoNumber = queueSubtickets.Dequeue();
                        if (ticketInfoNumber.Replace(Pick2.SPLIT_BEGINNING, string.Empty) != ticketInfo.number) return BadRequest("The ticket number must be the same as the number of tickets in the response of payment channel.");

                        numbersAndPrizesPerAmount.Add(ticketInfo.risk, ticketInfoNumber, ticketInfo.draw, withFireBall, ticketInfo.drawHour);
                    }
                    AppendSequence(ref commandForAuthorizations, ref isFirstSequence, ref isFirstAdd, currentAuthorizationId, consecutive);
                    commandForAuthorizations.Append(");");
                    var commandsForAddingNumbers = numbersAndPrizesPerAmount.GenerateCommand();
                    var purchaseDateAsText = orderResponseWithExternalTickets.PurchaseDate.ToString("MM/dd/yyyy HH:mm:ss");
                    
                    StringBuilder lotteryGameCmd = new StringBuilder();
                    if (lotteryGameType == IdOfLotteryGame.Picks)
                    {
                        lotteryGameCmd.AppendLine($"lotteryGame = company.LotteryGamesPool.PicksLotteryGame;");
                    }
                    else if (lotteryGameType == IdOfLotteryGame.Triz)
                    {
                        lotteryGameCmd.AppendLine($"lotteryGame = company.LotteryGamesPool.TrizLotteryGame;");
                    }
                    else
                    {
                        return BadRequest($"There is no {nameof(lotteryGameType)} matching");
                    }

                    result = await LottoAPI.Actor.PerformCmdAsync(HttpContext, $@"
                    {{
                        print false anyTicketIsExpired;
			            domain = company.Sales.DomainFrom('{domain}');
			            player = company.Players.SearchPlayer('{playerId}');
			            customer = company.CustomerByPlayer(player);
			            {{
                            Eval('orderNumber = ' + company.IdentityOrderNumber + ';');
				            myOrder = company.NewOrder(customer, orderNumber, {body.CurrencyCode});
                            
                            {lotteryGameCmd}
                            lotteryGame.NextDatesAccumulator.Calculate(domain, {purchaseDateAsText}, '{gameType}', '{dates}','{states}','{hours}','{withFireBalls}');
                            myOrder = company.PrepareTicketOrderWithMultipleAuthorization(player, '{selectionMode}', '{strRuleType[0]}', myOrder, lotteryGame.NextDatesAccumulator, domain){commandsForAddingNumbers}.Commit();
                            company.AddOrders(myOrder);

				            Eval('lowReference = ' + company.IdentitytBetNumber + ';');
				            Eval('highReference = ' + (lowReference + myOrder.CountOfItems - 1) + ';');
                            {commandForAuthorizations}
				            company.PurchaseTickets(itIsThePresent, myOrder, {purchaseDateAsText}, auths, domain, lowReference, orderNumber, '{fragmentResponse.ProcessorKey}');
                        }}
                    }}
	                ");

                    return result;
                }
            }
            var message = fragmentResponse.Equals(default(FragmentResponse)) || string.IsNullOrWhiteSpace(fragmentResponse.Message) ? "Sorry, your purchase cannot be completed at this moment. Please try again. If the problem persists, please contact customer service." : fragmentResponse.Message;
            result = await LottoAPI.Actor.PerformQryAsync(HttpContext, $@"
			        {{
				        Notify Information '{message}';
                        print '{fragmentResponse.Code}' code;
			        }}
		        ");
            return result;
        }

        internal class NumbersAndPrizesPerAmount
        {
            internal Dictionary<string, NumbersAndPrizes> numbersAndPrizesPerAmounts = new Dictionary<string, NumbersAndPrizes>();
            internal void Add(decimal amount, string number, string state, bool withFireBall, string hour)
            {
                bool isEndingNumber = !number.Contains('-');
                string fixedNumber = number.Replace("-", "");

                string withFireBallKey = withFireBall ? "_FB" : string.Empty;
                string firtsDigits = isEndingNumber ? string.Empty : "_TRIZ-FIRST-2DIGITS";
                var key = $"{amount}_{state}{withFireBallKey}{firtsDigits}_{hour}";
                if (numbersAndPrizesPerAmounts.ContainsKey(key))
                {
                    numbersAndPrizesPerAmounts[key].Numbers.Add(fixedNumber);
                }
                else
                {
                    numbersAndPrizesPerAmounts.Add(key, new NumbersAndPrizes(amount, fixedNumber, state, withFireBall, isEndingNumber));
                }
            }

            internal string GenerateCommand()
            {
                string resultCommand = string.Empty;
                IEnumerable<NumbersAndPrizes> enumerableNotEnding = numbersAndPrizesPerAmounts.Values.Where(x => !x.IsEndingNumber);
                if (enumerableNotEnding.Any()) resultCommand += GenerateCommandFrom(enumerableNotEnding, false);

                IEnumerable<NumbersAndPrizes> enumerable = numbersAndPrizesPerAmounts.Values.Where(x => x.IsEndingNumber);
                if (enumerable.Any()) resultCommand += GenerateCommandFrom(enumerable, true);

                return resultCommand;
            }

            private string GenerateCommandFrom(IEnumerable<NumbersAndPrizes> listNumbersAndPrizes, bool isEndingListNumbers)
            {
                string addCommand = isEndingListNumbers ? ".Add({'" : ".AddLeft({'";
                var commands = new StringBuilder();
                var sbNumbers = new StringBuilder();
                var sbStates = new StringBuilder();
                var sbWithFireBall = new StringBuilder();
                List<string> lastNumbers = null;
                decimal lastAmount = 0;
                int countStates = 0;
                NumbersAndPrizes lastNumbersAndPrizes = null;
                foreach (NumbersAndPrizes numbersAndPrizes in listNumbersAndPrizes)
                {
                    if (lastNumbers == null || !lastNumbers.SequenceEqual(numbersAndPrizes.Numbers) || lastAmount != numbersAndPrizes.Amount)
                    {
                        var isFirstTime = lastNumbers == null && lastAmount == 0;
                        if (!isFirstTime)
                        {
                            if (countStates > 1)
                                commands.Append(addCommand).Append(sbNumbers).Append("'}, {'").Append(sbStates).Append("'}, {").Append(sbWithFireBall).Append("}, ").Append(lastAmount).Append(')');
                            else
                                commands.Append(addCommand).Append(sbNumbers).Append("'}, '").Append(lastNumbersAndPrizes.States[0]).Append("', ").Append(lastNumbersAndPrizes.WithFireBalls[0]).Append(", ").Append(lastAmount).Append(')');

                            sbNumbers.Clear();
                            sbStates.Clear();
                            sbWithFireBall.Clear();
                            countStates = 0;
                        }

                        foreach (var state in numbersAndPrizes.States)
                        {
                            if (sbStates.Length != 0) sbStates.Append("','");
                            sbStates.Append(state);
                            countStates++;
                        }
                        foreach (var withFireBall in numbersAndPrizes.WithFireBalls)
                        {
                            if (sbWithFireBall.Length != 0) sbWithFireBall.Append(',');
                            sbWithFireBall.Append(withFireBall ? "true" : "false");
                        }
                        foreach (var number in numbersAndPrizes.Numbers)
                        {
                            if (sbNumbers.Length != 0) sbNumbers.Append("','");
                            sbNumbers.Append(number);
                        }

                        lastNumbersAndPrizes = numbersAndPrizes;
                        lastNumbers = numbersAndPrizes.Numbers;
                        lastAmount = numbersAndPrizes.Amount;
                    }
                    else
                    {
                        foreach (var state in numbersAndPrizes.States)
                        {
                            if (sbStates.Length != 0) sbStates.Append("','");
                            sbStates.Append(state);
                            countStates++;
                        }
                        foreach (var withFireBall in numbersAndPrizes.WithFireBalls)
                        {
                            if (sbWithFireBall.Length != 0) sbWithFireBall.Append(',');
                            sbWithFireBall.Append(withFireBall ? "true" : "false");
                        }
                    }
                }

                if (countStates > 1)
                    commands.Append(addCommand).Append(sbNumbers).Append("'}, {'").Append(sbStates).Append("'}, {").Append(sbWithFireBall).Append("}, ").Append(lastAmount).Append(')');
                else
                    commands.Append(addCommand).Append(sbNumbers).Append("'}, '").Append(lastNumbersAndPrizes.States[0]).Append("', ").Append(lastNumbersAndPrizes.WithFireBalls[0]).Append(", ").Append(lastAmount).Append(')');

                return commands.ToString();
            }

            internal class NumbersAndPrizes
            {
                internal decimal Amount { get; }
                internal List<string> Numbers { get; } = new List<string>();
                internal List<string> States { get; } = new List<string>();
                internal List<bool> WithFireBalls { get; } = new List<bool>();
                internal bool IsEndingNumber { get; set; } = true;

                public NumbersAndPrizes(decimal amount, string number, string state, bool withFireBall, bool isEndingNumber)
                {
                    Amount = amount;
                    Numbers.Add(number);
                    States.Add(state);
                    WithFireBalls.Add(withFireBall);
                    IsEndingNumber = isEndingNumber;
                }
            }
        }

        [HttpPost("api/lotto/tickets/multiTypesAndPicks")]
        [Authorize(Roles = "player")]
        public async Task<IActionResult> CreateTicketMultiTypesAndPicksAsync([FromBody] TicketsBody body, [FromHeader(Name = "domain-url")] string domain)
        {
            if (body == null) return BadRequest("Body is required");
            if (body.Tickets == null || body.Tickets.Length == 0) return BadRequest($"{nameof(body.Tickets)} is required");
            if (string.IsNullOrWhiteSpace(HttpContext.Request.Headers["domain-url"])) return BadRequest("Header 'domain-url' is not sent in request");

            char pickNumber = char.MinValue;
            string strAmount = null;
            string lockDescription = string.Empty;
            string selectionMode = string.Empty;
            string dates = string.Empty;
            string strRuleType = string.Empty;
            string includedSubticketsForInput = string.Empty;
            string playerId = Validator.StringEscape(Security.PlayerId(User));

            IActionResult result = null;
            List<ChosenDrawBody> drawSchedules = null;
            OrderResponseWithExternalTickets orderResponseWithExternalTickets = null;
            OrderErrorResponseAccumulated orderErrorResponseAccumulated = new OrderErrorResponseAccumulated();
            StringBuilder activatorScriptsForQry = new StringBuilder();
            StringBuilder states = new StringBuilder();
            StringBuilder hours = new StringBuilder();
            StringBuilder withFireBalls = new StringBuilder();
            StringBuilder strIsPresentPlayerBeforeToCloseStore = new StringBuilder();
            StringBuilder strUseNextDate = new StringBuilder();

            int ticketIndex = 0;
            var lastIndexInGroupOfTickets = body.Tickets.Length - 1;
            foreach (var ticket in body.Tickets)
            {
                if (!string.IsNullOrWhiteSpace(selectionMode) && !selectionMode.Equals(ticket.SelectionMode, StringComparison.OrdinalIgnoreCase)) return BadRequest($"{nameof(ticket.SelectionMode)} must be the same for all tickets");
                selectionMode = ticket.SelectionMode;
                drawSchedules = new List<ChosenDrawBody>();

                if (string.IsNullOrWhiteSpace(ticket.GameType)) return BadRequest($"{nameof(ticket.GameType)} cannot be null");
                if (ticket.GameType.StartsWith("pick") && ticket.GameType.Length == 5 && ticket.GameType[4] >= '2' && ticket.GameType[4] <= '5')
                {
                    pickNumber = ticket.GameType[4];
                }
                else
                {
                    return BadRequest($"There is no {nameof(ticket.GameType)} or {nameof(ticket.RuleType)} matching");
                }

                strAmount = ticket.Amount.ToString();
                if (ticket.Dates.Length > 1) return BadRequest("Only one date is allowed");
                dates = ticket.Dates.First();

                string statesOfTicket = string.Empty;
                string hoursOfTicket = string.Empty;
                string withFireBallsOfTicket = string.Empty;
                string strIsPresentPlayerBeforeToCloseStoreOfTicket = string.Empty;
                string strUseNextDateOfTicket = string.Empty;
                var lastDrawIndexInTicket = ticket.DrawSchedules.Length - 1;
                for (int index = 0; index < ticket.DrawSchedules.Length; index++)
                {
                    var state = ticket.DrawSchedules[index].State;
                    var hour = ticket.DrawSchedules[index].Hour;
                    var withFireBall = ticket.DrawSchedules[index].WithFireBall;
                    statesOfTicket += state;
                    hoursOfTicket += hour;
                    withFireBallsOfTicket += withFireBall;
                    strIsPresentPlayerBeforeToCloseStoreOfTicket += ticket.DrawSchedules[index].IsPresentPlayerBeforeToCloseStore;
                    strUseNextDateOfTicket += ticket.DrawSchedules[index].UseNextDate;
                    drawSchedules.Add(new ChosenDrawBody(state, hour));

                    if (index != lastDrawIndexInTicket)
                    {
                        statesOfTicket += ',';
                        hoursOfTicket += ',';
                        withFireBallsOfTicket += ',';
                        strIsPresentPlayerBeforeToCloseStoreOfTicket += ',';
                        strUseNextDateOfTicket += ',';
                    }
                }

                states.Append(statesOfTicket);
                hours.Append(hoursOfTicket);
                withFireBalls.Append(withFireBallsOfTicket);
                strIsPresentPlayerBeforeToCloseStore.Append(strIsPresentPlayerBeforeToCloseStoreOfTicket);
                strUseNextDate.Append(strUseNextDateOfTicket);
                if (ticketIndex != lastIndexInGroupOfTickets)
                {
                    states.Append(',');
                    hours.Append(',');
                    withFireBalls.Append(',');
                    strIsPresentPlayerBeforeToCloseStore.Append(',');
                    strUseNextDate.Append(',');
                }

                if (ticket.Subtickets == null || ticket.Subtickets.Length == 0) return BadRequest($"When you use {nameof(ticket.SelectionMode)} {ticket.SelectionMode} {nameof(ticket.Subtickets)} is required");
                lastDrawIndexInTicket = ticket.Subtickets.Length - 1;
                var includedSubtickets = new StringBuilder();
                for (int index = 0; index < ticket.Subtickets.Length; index++)
                {
                    if (string.IsNullOrWhiteSpace(ticket.Subtickets[index])) return BadRequest($"Sorry, your purchase cannot be completed at this moment. Please, navigate to the input numbers section and double check that the chosen numbers are written in the correct way, and then try again.");
                    if (includedSubtickets.Length != 0) includedSubtickets.Append("','");
                    includedSubtickets.Append(ticket.Subtickets[index]);
                }

                if (ticket.RuleType == "straight")
                {
                    strRuleType = "Straight";
                }
                else if (ticket.RuleType == "boxed")
                {
                    strRuleType = "Boxed";
                }
                else
                {
                    return BadRequest($"There is no {nameof(ticket.GameType)} or {nameof(ticket.RuleType)} matching");
                }
                lockDescription = $"Lotto Ticket Pick {pickNumber} {strRuleType}";

                includedSubticketsForInput = includedSubtickets.ToString();
                var script = ArtemisBehavior.CommandsForPurchaseValidation(IdOfLotteryGame.Picks, pickNumber.ToString(), pickNumber, strAmount, dates, statesOfTicket, hoursOfTicket, withFireBallsOfTicket, strIsPresentPlayerBeforeToCloseStoreOfTicket, strUseNextDateOfTicket, ticket.SelectionMode, includedSubticketsForInput, strRuleType, body.CurrencyCode, "pickify", applyToleranceFactor: false, issued: string.Empty);
                activatorScriptsForQry.AppendLine(script);

                var scriptForExclusions = $@"
                        print company.Settings.HoursKeepingAliveTickets hoursKeepingAliveTickets;
                        for (datesToPurchase : totalOrderAndExclusionsByToWin.ValidDatesToPurchase)
                        {{
                            print datesToPurchase dateToPurchase;
                        }}
                        for (excludeSubtickets : totalOrderAndExclusionsByToWin.ExclusionsForSingleSelection)
                        {{
                            exclude = excludeSubtickets;
                            print exclude.StateAbb state;
                            print exclude.HourFormattedAsText hour;
                            print exclude.DateFormattedAsText date;
                            print exclude.Subticket subticket;
                            print exclude.PickNumber pick;
                        }}
                    ";

                var scriptToPrintInternalTicketData = ArtemisBehavior.CommandsToPrintExternalTicketData();
                var resultCreatingOrder = await LottoAPI.Actor.PerformQryAsync(HttpContext, $@"
				        {{
                            customer = company.CustomerByPlayerId('{playerId}');
                            player = customer.Player;
                            domain = company.Sales.DomainFrom('{domain}');
					        {activatorScriptsForQry.ToString()}
                            print nextDatesAccumulator.AnyTicketIsExpired anyTicketIsExpired;
                            print totalOrderAndExclusionsByToWin.TotalOrder total;
                            print totalOrderAndExclusionsByToWin.ErrorMessage errorMessage;
                            print totalOrderAndExclusionsByToWin.IsValidToRetryPurchase isValidToRetryPurchase;
                            print customer.AccountNumber atAddress;
                            print player.AgentNumber agentNumber;
                            {scriptForExclusions}
                            print company.Sales.CurrentStore.Id storeId;
					        print domain.Id domainId;
                            print domain.Url domainUrl;
				            existsDomainInMarketplace = company.Sales.CurrentStore.ExistsDomain(domain.Id);
					        print existsDomainInMarketplace existsDomainInMarketplace;
                            print Now purchaseDate;
                            {scriptToPrintInternalTicketData}
				        }}
                    ");

                if (!(resultCreatingOrder is OkObjectResult)) return BadRequest("Error validating the purchase data");
                var o = (OkObjectResult)resultCreatingOrder;
                var json = o.Value.ToString();
                var orderResponse = JsonConvert.DeserializeObject<OrderResponseWithExternalTickets>(json);

                if (!string.IsNullOrWhiteSpace(orderResponse.ErrorMessage))
                {
                    orderErrorResponseAccumulated.Append(orderResponse);
                }
                else if (orderResponse.AnyTicketIsExpired)
                {
                    result = await LottoAPI.Actor.PerformQryAsync(HttpContext, $@"
					        {{
						        print true anyTicketIsExpired;
					        }}
				        ");
                    return result;
                }
                else
                {
                    orderErrorResponseAccumulated.IsValidToRetryPurchase = true;
                }

                if (orderResponseWithExternalTickets == null) orderResponseWithExternalTickets = (OrderResponseWithExternalTickets)orderResponse;
                else
                {
                    orderResponseWithExternalTickets.Append((OrderResponseWithExternalTickets)orderResponse);
                }

                activatorScriptsForQry.Clear();
                ticketIndex++;
            }

            if (orderErrorResponseAccumulated.ExcludeSubtickets != null || orderErrorResponseAccumulated.ExclusionsForInputs != null || !string.IsNullOrWhiteSpace(orderErrorResponseAccumulated.ErrorMessage))
            {
                if (orderErrorResponseAccumulated.ExclusionsForInputs == null) return BadRequest(new OrderErrorResponseForSingleSelection(orderErrorResponseAccumulated));
                else if (orderErrorResponseAccumulated.ExcludeSubtickets == null) return BadRequest(new OrderErrorResponseForMultiSelection(orderErrorResponseAccumulated));
                else return BadRequest(orderErrorResponseAccumulated.ErrorMessage);
            }

            if (orderResponseWithExternalTickets.Total <= 0) return BadRequest("Ticket cost must be greater than 0");
            if (orderResponseWithExternalTickets.Tickets.Count > 10000) return BadRequest("The maximum number of tickets that can be purchased is 10000");
            var domainObj = new Domain(false, orderResponseWithExternalTickets.DomainId, orderResponseWithExternalTickets.DomainUrl, Agents.ARTEMIS);
            var datesToPurchase = orderResponseWithExternalTickets.DatesToPurchase.Select(date => date.DateToPurchase);
            var fragmentResponse = await PaymentChannels.LockWithExternalResponseAsync(
                HttpContext,
                orderResponseWithExternalTickets.Agent,
                orderResponseWithExternalTickets.AtAddress,
                body.CurrencyCode.ToString(),
                lockDescription,
                orderResponseWithExternalTickets.PurchaseDate,
                string.Empty,
                Coinage.Coin(body.CurrencyCode),
                orderResponseWithExternalTickets.Total,
                orderResponseWithExternalTickets.StoreId,
                domainObj,
                orderResponseWithExternalTickets.GetMaxDateToPurchase().AddHours(orderResponseWithExternalTickets.HoursKeepingAliveTickets),
                orderResponseWithExternalTickets.Tickets
                );

            var hasResponseExternalTicketData = fragmentResponse.Response != null && fragmentResponse.Response.tickets != null && fragmentResponse.Response.tickets.Count > 0;
            if (hasResponseExternalTicketData && fragmentResponse.AuthorizationId == 0 && fragmentResponse.Code == AuthorizationResponseCode.OK)
            {
                StringBuilder commandsForAddingProducts = new StringBuilder();
                StringBuilder toWins = new StringBuilder();
                StringBuilder authorizationIds = new StringBuilder();
                StringBuilder inputNumbers = new StringBuilder();
                StringBuilder sbStates = new StringBuilder();
                StringBuilder sbHours = new StringBuilder();
                StringBuilder sbIsPresentPlayer = new StringBuilder();
                StringBuilder sbUseNextDate = new StringBuilder();
                StringBuilder sbPicks = new StringBuilder();

                HashSet<string> uniqueStatesAndHours = new HashSet<string>();
                HashSet<string> uniqueDates = new HashSet<string>();
                HashSet<string> uniqueNumbers = new HashSet<string>();
                int previousPick = 0;
                string previousType = string.Empty;
                string isPresentPlayer = string.Empty;
                string useNextDate = string.Empty;
                foreach (var ticketInfo in fragmentResponse.Response.tickets)
                {
                    var isStraight = ticketInfo.type.Equals("straight", StringComparison.OrdinalIgnoreCase);
                    var type = isStraight ? "Straight" : "Boxed";
                    var isRequiredToCreateNewTicketOrder = (previousPick != 0 && !string.IsNullOrEmpty(previousType)) && (previousPick != ticketInfo.pick || previousType != type);
                    if (isRequiredToCreateNewTicketOrder)
                    {
                        commandsForAddingProducts.Append(".Add").Append(previousType).Append("({'").Append(inputNumbers).Append("'}, ");
                        if (previousType.Equals("Straight", StringComparison.OrdinalIgnoreCase))
                            commandsForAddingProducts.Append('\'').Append(toWins).Append("')");
                        else
                            commandsForAddingProducts.Append("{'").Append(toWins).Append("'})");

                        toWins.Clear();
                        inputNumbers.Clear();
                    }
                    
                    if (authorizationIds.Length != 0) authorizationIds.Append(',');
                    authorizationIds.Append(ticketInfo.ticketId);
                    if (!uniqueNumbers.Contains(ticketInfo.number))
                    {
                        if (inputNumbers.Length != 0) inputNumbers.Append("','");
                        inputNumbers.Append(ticketInfo.number);
                        uniqueNumbers.Add(ticketInfo.number);

                        if (isStraight)
                        {
                            if (toWins.Length == 0) toWins.Append(ticketInfo.toWin);
                        }
                        else
                        {
                            if (toWins.Length != 0) toWins.Append("','");
                            toWins.Append(ticketInfo.toWin);
                        }
                    }

                    var hour = DateTime.ParseExact(ticketInfo.drawHour, "h:mm:ss tt", Integration.CultureInfoEnUS).ToString("h:m tt");
                    var stateAndHour = $"{ticketInfo.draw}_{hour}_{ticketInfo.pick}";
                    if (!uniqueStatesAndHours.Contains(stateAndHour))
                    {
                        if (sbStates.Length != 0) sbStates.Append("','");
                        sbStates.Append(ticketInfo.draw);
                        if (sbHours.Length != 0) sbHours.Append("','");
                        sbHours.Append(hour);
                        uniqueStatesAndHours.Add(stateAndHour);

                        if (sbIsPresentPlayer.Length != 0) sbIsPresentPlayer.Append("','");
                        sbIsPresentPlayer.Append("True");
                        if (sbUseNextDate.Length != 0) sbUseNextDate.Append("','");
                        sbUseNextDate.Append("True");
                        if (sbPicks.Length != 0) sbPicks.Append(',');
                        sbPicks.Append(ticketInfo.pick);
                    }
                    if (!uniqueDates.Contains(ticketInfo.drawDate))
                    {
                        uniqueDates.Add(ticketInfo.drawDate);
                    }

                    previousPick = ticketInfo.pick;
                    previousType = type;
                }
                commandsForAddingProducts.Append(".Add").Append(previousType).Append("({'").Append(inputNumbers).Append("'}, ");
                if (previousType.Equals("Straight", StringComparison.OrdinalIgnoreCase))
                    commandsForAddingProducts.Append('\'').Append(toWins).Append("')");
                else
                    commandsForAddingProducts.Append("{'").Append(toWins).Append("'})");

                var purchaseDateAsText = orderResponseWithExternalTickets.PurchaseDate.ToString("MM/dd/yyyy HH:mm:ss");
                var today = DateTime.Now.ToString("M/d/yyyy");
                result = await LottoAPI.Actor.PerformCmdAsync(HttpContext, $@"
                    {{
                        print false anyTicketIsExpired;
					    domain = company.Sales.DomainFrom('{domain}');
					    player = company.Players.SearchPlayer('{playerId}');
					    customer = company.CustomerByPlayer(player);
					    {{
                            Eval('orderNumber = ' + company.IdentityOrderNumber + ';');
						    myOrder = company.NewOrder(customer, orderNumber, {body.CurrencyCode});
						    lotto900.NextDatesAccumulator.Calculate(domain, {purchaseDateAsText}, {{{sbPicks}}}, '{today}', {{'{sbStates}'}}, {{'{sbHours}'}}, {{'{sbIsPresentPlayer}'}}, {{'{sbUseNextDate}'}});
                            myOrder = company.PrepareTicketOrderWithMultipleAuthorization(player, '{selectionMode}', {strAmount}, myOrder, lotto900.NextDatesAccumulator, domain){commandsForAddingProducts}.Commit();
						    company.AddOrders(myOrder);
						    Eval('lowReference = ' + company.IdentitytBetNumber + ';');
						    Eval('highReference = ' + (lowReference + myOrder.CountOfItems - 1) + ';');
						    company.PurchaseTickets(itIsThePresent, myOrder, {purchaseDateAsText}, {{{authorizationIds}}}, domain, lowReference, orderNumber, '{fragmentResponse.ProcessorKey}');
                        }}
                    }}
			        ");

                return result;
            }
            else
            {
                var message = fragmentResponse.Equals(default(FragmentResponse)) || string.IsNullOrWhiteSpace(fragmentResponse.Message) ? "Sorry, your purchase cannot be completed at this moment. Please try again. If the problem persists, please contact customer service." : fragmentResponse.Message;
                result = await LottoAPI.Actor.PerformQryAsync(HttpContext, $@"
					{{
						Notify Information '{message}';
                        print '{fragmentResponse.Code}' code;
					}}
				");
                return result;
            }
        }
        
        [HttpPost("api/lotto/ticket/powerball")]
        [Authorize(Roles = "player")]
        public async Task<IActionResult> CreateTicketPowerballAsync([FromBody]TicketsPowerballBody body, [FromHeader(Name = "domain-url")]string domain)
		{
			if (body == null) return BadRequest("Body is required");
			if (body.Tickets == null || body.Tickets.Length == 0) return BadRequest($"{nameof(body.Tickets)} is required");
            if (string.IsNullOrWhiteSpace(HttpContext.Request.Headers["domain-url"])) return BadRequest("Header 'domain-url' is not sent in request");

            var maxDateToPurchase = body.Tickets.Select(x => DateTime.ParseExact(x.Date, "M/d/yyyy HH:mm:ss", Integration.CultureInfoEnUS)).Max();
            var drawDates = body.Tickets.Select(x => x.Date);
            var amounts = body.Tickets.Select(x => x.Amount);
            var dozenOfNumbers = body.Tickets.Select(x => x.DozenOfNumber);  
            var powerplays = body.Tickets.Select(x => x.Powerplay);

			if (drawDates.Any(date => String.IsNullOrWhiteSpace(date))) return BadRequest("Powerball dates must be provided");
			if (amounts.Any(amount => amount <= 0)) return BadRequest("Powerball amounts must be provided");
			if (dozenOfNumbers.Any(dozenOfNumber => dozenOfNumber.Length != 12)) return BadRequest($"Some of Powerball Numbers are invalid");

			StringBuilder activatorScriptsForQry = new StringBuilder();
			StringBuilder activatorScriptsForCmd = new StringBuilder();

			foreach (decimal amount in amounts)
			{
				activatorScriptsForQry.AppendLine($"lotto900.IsBetAmountValidForPowerball(player, domain, {amount});");//TODO: check all array of amounts in one line
                activatorScriptsForCmd.AppendLine($"lotto900.IsBetAmountValidForPowerball(player, domain, {amount});");//TODO: check all array of amounts in one line
            }

			var strDates = string.Join(",", drawDates);
			var strAmounts = string.Join(",", amounts);
			var strDozenOfNumbers = string.Join("','", dozenOfNumbers);
			var strPowerplays = string.Join(",", powerplays);
			//TODO: Erick, arreglos para recibir productos

			activatorScriptsForQry.AppendLine($@"total = company.TotalTicketOrderForPowerball({{{strDates}}}, {{'{strDozenOfNumbers}'}}, {{{strPowerplays}}}, {{{strAmounts}}}, ticketPowerBallSingleProd, ticketPowerBallPowerPlayProd);");
			activatorScriptsForCmd.Append($"myOrder = company.CreatePowerballOrder(player, {{{strDates}}}, {{'{strDozenOfNumbers}'}}, {{{strPowerplays}}}, {{{strAmounts}}}, myOrder, domain, ticketPowerBallSingleProd, ticketPowerBallPowerPlayProd);");

			activatorScriptsForQry.AppendLine($"print total total;");
			activatorScriptsForQry.Append($"print customer.AccountNumber accountNumber;");

            string playerId = Validator.StringEscape(Security.PlayerId(User));
            var resultCreatingOrder =await LottoAPI.Actor.PerformQryAsync(HttpContext, $@"
				{{
					player = company.Players.SearchPlayer('{playerId}');
					customer = company.CustomerByPlayer(player);
                    domain = company.Sales.DomainFrom('{domain}');
					{activatorScriptsForQry.ToString()}
                    print company.Sales.CurrentStore.Id storeId;
					print domain.Id domainId;
                    print domain.Url domainUrl;
				    existsDomainInMarketplace = company.Sales.CurrentStore.ExistsDomain(domain.Id);
					print existsDomainInMarketplace existsDomainInMarketplace;
                    print Now purchaseDate;
				}}
            ");

			if (!(resultCreatingOrder is OkObjectResult)) return BadRequest("Error validating the purchase data");
            var orderResponse = new OrderResponse();
			var bytes = Encoding.Unicode.GetBytes(((OkObjectResult)resultCreatingOrder).Value.ToString());
			using (MemoryStream mst = new MemoryStream(bytes))
			{
				var serializer = new DataContractJsonSerializer(typeof(OrderResponse));
				orderResponse = (OrderResponse)serializer.ReadObject(mst);
			}

            if (orderResponse.Total <= 0) return BadRequest("Ticket cost must be greater than 0");
            string decription = $"{body.Currency.ToString()}{orderResponse.Total} powerball purchase";
            string addicionalInfo = "";

            Domain domainObj = new Domain(false, orderResponse.DomainId, orderResponse.DomainUrl, orderResponse.Agent);

            var response = await PaymentChannels.LockAsync(
                HttpContext,
                orderResponse.Agent,
                orderResponse.AtAddress,
                body.Currency.ToString(),
                decription,
                orderResponse.PurchaseDate,
                addicionalInfo,
                Coinage.Coin(body.Currency),
                orderResponse.Total,
                orderResponse.StoreId,
                domainObj,
                maxDateToPurchase
                );

            if (response.AuthorizationId != FAKE_TICKET_NUMBER)
			{
                var purchaseDateAsText = orderResponse.PurchaseDate.ToString("MM/dd/yyyy HH:mm:ss");
				var result = await LottoAPI.Actor.PerformCmdAsync(HttpContext, $@"
					{{
						domain=company.Sales.DomainFrom('{domain}');
						player = company.Players.SearchPlayer('{playerId}');
						customer = company.CustomerByPlayer(player);
						{{
							myOrder = company.GetNewOrder(customer);
                            agent = marketplace.SearchAgentInFirstLevel(customer.AffiliateIdAsText);
                            myOrder.Agent = agent;
							{activatorScriptsForCmd.ToString()}
							company.AddOrders(myOrder);
							company.SaleTickets(itIsThePresent, myOrder, {purchaseDateAsText}, {response.AuthorizationId}, domain);
						}}
					}}
				");
	
                return (IActionResult)Ok();
			}
			else
			{
				var result =await LottoAPI.Actor.PerformQryAsync(HttpContext, $@"
					{{
						Notify Information 'Sorry, your purchase cannot be completed at this moment. Please try again. If the problem persists, please contact customer service.';
					}}
				");
				return result;
			}
		}

        [HttpPost("api/lotto/ticket/keno")]
        [Authorize(Roles = "player")]
        public async Task<IActionResult> CreateTicketKenolAsync([FromBody] TicketsKenolBody body, [FromHeader(Name = "domain-url")] string domain)
        {
            if (body == null) return BadRequest("Body is required");
            if (body.Numbers == null || body.Numbers.Length == 0) return BadRequest($"{nameof(body.Numbers)} is required");
            if (string.IsNullOrWhiteSpace(HttpContext.Request.Headers["domain-url"])) return BadRequest("Header 'domain-url' is not sent in request");

            var amount = body.Amount;
           
            StringBuilder activatorScriptsForQry = new StringBuilder();
            StringBuilder activatorScriptsForCmd = new StringBuilder();

            activatorScriptsForQry.AppendLine($"lotteries.IsBetAmountValidForKeno(player, domain, {amount});");//TODO: check all array of amounts in one line
            activatorScriptsForCmd.AppendLine($"lotteries.IsBetAmountValidForKeno(player, domain, {amount});");//TODO: check all array of amounts in one line

            var selection = string.Join(",", body.Numbers);

            activatorScriptsForQry.AppendLine($@"total = company.TotalTicketOrderForKeno({{{selection}}}, {body.HasMultiplier}, {body.HasBullsEye}, {amount}, '{body.Draw}', now, kenoSingleProd, kenoWithMultiplierProd, kenoWithBullEyeProd, kenoWithMultiplierAndBulleyeProd);");
            activatorScriptsForCmd.Append($"myOrder = company.CreateKenoOrder(player, {{{selection}}}, {body.HasMultiplier}, {body.HasBullsEye}, {amount}, '{body.Draw}', now, myOrder, domain, kenoSingleProd, kenoWithMultiplierProd, kenoWithBullEyeProd, kenoWithMultiplierAndBulleyeProd);");

            activatorScriptsForQry.AppendLine($"print total total;");
            activatorScriptsForQry.Append($"print customer.AccountNumber accountNumber;");

            string playerId = Validator.StringEscape(Security.PlayerId(User));
            var resultCreatingOrder = await LottoAPI.Actor.PerformQryAsync(HttpContext, $@"
				{{
					player = company.Players.SearchPlayer('{playerId}');
					customer = company.CustomerByPlayer(player);
                    domain = company.Sales.DomainFrom('{domain}');
					{activatorScriptsForQry.ToString()}
				    existsDomainInMarketplace = company.Sales.CurrentStore.ExistsDomain(domain.Id);

                    currentDateTime= Now;
                    nextDrawDate = lotteries.CalculateNextPendingAndNoRegradedSchedulesAtForKeno(currentDateTime);
                    isAValidDateForKeno = lotteries.IsAValidDateForKeno(nextDrawDate);

                    if(isAValidDateForKeno)
                    {{
                        scheduledLotteries=lotteries.NextPendingAndNoRegradedSchedulesAtForKeno(currentDateTime);
                        print scheduledLotteries.Id fixedDraw;

                        sameDraw = scheduledLotteries.IdPrefix != '{body.Draw}';
                        print sameDraw anyTicketIsExpired;
                    }}
                    else
                    {{
                        print true anyTicketIsExpired;
                    }}
                    print company.Settings.HoursKeepingAliveTickets hoursKeepingAliveTickets;
                    print '{body.Draw}' currentDraw;
                    print nextDrawDate nextDrawDate;
					print existsDomainInMarketplace existsDomainInMarketplace;
                    print customer.AccountNumber atAddress;
                    print player.AgentNumber agentNumber;
                    print company.Sales.CurrentStore.Id storeId;
					print domain.Id domainId;
                    print '' errorMessage;
                    print currentDateTime currentDateTime;
				}}
            ");
            
            if (!(resultCreatingOrder is OkObjectResult)) return BadRequest(((ObjectResult)resultCreatingOrder).ToString());
            var orderResponse = new OrderKenoResponse();
            var bytes = Encoding.Unicode.GetBytes(((OkObjectResult)resultCreatingOrder).Value.ToString());
            using (MemoryStream mst = new MemoryStream(bytes))
            {
                var serializer = new DataContractJsonSerializer(typeof(OrderKenoResponse));
                orderResponse = (OrderKenoResponse)serializer.ReadObject(mst);
            }
            
            if (orderResponse.AnyTicketIsExpired )
            {
                return Ok(orderResponse);
            }

            if (orderResponse.Total <= 0) return BadRequest("Ticket cost must be greater than 0");
            string decription = $"{body.CurrencyCode.ToString()}{orderResponse.Total} Keno purchase";
            string addicionalInfo = "";

            Domain domainObj = new Domain(false, orderResponse.DomainId, domain, orderResponse.Agent);

            var response = await PaymentChannels.LockAsync(
                HttpContext,
                orderResponse.Agent,
                orderResponse.AtAddress,
                body.CurrencyCode.ToString(),
                decription,
                DateTime.Now,
                addicionalInfo,
                Coinage.Coin(body.CurrencyCode),
                orderResponse.Total,
                orderResponse.StoreId,
                domainObj,
                DateTime.Now.AddHours(orderResponse.HoursKeepingAliveTickets)
                );
            
            if (response.AuthorizationId != FAKE_TICKET_NUMBER)
            {
                var result = await LottoAPI.Actor.PerformCmdAsync(HttpContext, $@"
					{{
                        currentDateTime= Now;
                        drawDate = lotteries.CalculateNextPendingAndNoRegradedSchedulesAtForKeno(currentDateTime);
                        nextDraw = lotteries.NextPendingAndNoRegradedSchedulesAtForKeno(currentDateTime);                       
                        print drawDate drawDate;
                        print nextDraw.Id drawId;
                        print currentDateTime now;

						domain=company.Sales.DomainFrom('{domain}');
						player = company.Players.SearchPlayer('{playerId}');
						customer = company.CustomerByPlayer(player);
						{{
                            Eval('orderNumber = ' + company.IdentityOrderNumber + ';');
							myOrder = company.GetNewOrder(customer);
                            agent = marketplace.SearchAgentInFirstLevel(customer.AffiliateIdAsText);
                            myOrder.Agent = agent;
							{activatorScriptsForCmd.ToString()}
							company.AddOrders(myOrder);
                            company.PurchaseTickets(itIsThePresent, myOrder, currentDateTime, {response.AuthorizationId}, domain, orderNumber);
						}}
					}}
				");

                return (IActionResult)Ok(result);
            }
            else
            {
                var result = await LottoAPI.Actor.PerformQryAsync(HttpContext, $@"
					{{
						Notify Information 'Sorry, your purchase cannot be completed at this moment. Please try again. If the problem persists, please contact customer service.';
					}}
				");
                return result;
            }
        }
        private string CreateAListOfStringBaseOnTextArray(string[] text)
		{
			StringBuilder result = new StringBuilder();
			for (int i = 0; i < text.Length; i++)
			{
				bool lastElement = i == (text.Length - 1);
				result.Append($"\"{text[i]}\"");
				if (!lastElement)
					result.Append(',');
			}
			return result.ToString();
		}

		[HttpGet("api/lotto/ticket/pick/{pickNumber:int:min(2):max(5)}/subtickets")]
        [Authorize(Roles = "player")]
        public async Task<IActionResult> SubTicketsAsync(int pickNumber, string number1, string number2, string number3, string number4, string number5)
        {
            if (String.IsNullOrWhiteSpace(number1)) return BadRequest($"Parameter {nameof(number1)} is required");
            if (String.IsNullOrWhiteSpace(number2)) return BadRequest($"Parameter {nameof(number2)} is required");

            var strNumbers = $"{number1},{number2},{number3},{number4},{number5}";
            var result =await LottoAPI.Actor.PerformQryAsync(HttpContext, $@"
            {{
                subTickets = lotto900.GetSubTicketsByPick({pickNumber}, '{strNumbers}');
                print subTickets subTickets; 
            }}
            ");
            return result;
        }

        [HttpGet("api/lotto/tickets/{ticketNumber}/description")]
        [AllowAnonymous]
        public async Task<IActionResult> GetTicketDescriptionAsync(string ticketNumber)
        {
            if (string.IsNullOrWhiteSpace(ticketNumber)) return BadRequest($"Parameter {nameof(ticketNumber)} is required");

            var result = await LottoAPI.Actor.PerformQryAsync(HttpContext, $@"
            {{
                description = lotto900.MakeDescriptionFromPurchase({ticketNumber});
                print description description;
            }}
            ");
            return result;
        }

        [HttpGet("api/lotto/tickets/{ticketNumber}/pending")]
        [AllowAnonymous]
        public async Task<IActionResult> GetPendingTicketsAsync(string ticketNumber)
        {
            if (string.IsNullOrWhiteSpace(ticketNumber)) return BadRequest($"Parameter {nameof(ticketNumber)} is required");

            var result = await LottoAPI.Actor.PerformQryAsync(HttpContext, $@"
            {{
                pendingTickets = lotto900.GetPendingTicketsByPrize({ticketNumber});
                ticketsFound = pendingTickets.Count() != 0;
                if (ticketsFound)
                {{
                    for (tickets:pendingTickets)
                    {{
                        ticketByPrize = tickets;
                        ticket = tickets.Ticket;
                        print ticket.GameType() gameType;
                        drawDate = ticket.DrawDate;
                        print drawDate.MMddyyyyOrTodayOrTomorrow(Now) drawDateFormatted;
                        print drawDate.HHmmAMPM() hour;
                        print ticket.LotteryState().Abbreviation state;
                        print ticket.DrawingDescription description;
                        print ticket.TicketNumber ticketNumber;
                        print ticket.BetAmount() betAmount;
                        print ticketByPrize.TicketAmount() ticketAmount;
                        print ticketByPrize.AmountToWin() prize;
                        print ticket.GradingAsString gradingStatus;
                        print false isWinner;
                        print ticket.CreationDate.MMddyyyy_hhmmss() creationDate;
                        for (numbers:ticketByPrize.Wagers())
                        {{
                            wager = numbers;
                            print wager.SubticketAsString subticket;
                            print wager.FullWagerNumber() fullWagerNumber;
                        }}
                    }}
                }}
                else 
                {{
                    print 'Ticket number {ticketNumber} was not found' errorMessage;
                }}
            }}
            ");
            return result;
        }

        [HttpDelete("api/lotto/ticket")]
        [Authorize(Roles = "RemoveTransaction")]
		public async Task<IActionResult> RemoveTicketAsync(int ticketNumber)
        {
            if (ticketNumber <= 0) return BadRequest($"Parameter {nameof(ticketNumber)} is required");

            string employeeName = Security.UserName(HttpContext);
            var result = await LottoAPI.Actor.PerformCmdAsync(HttpContext, $@"
            {{
                areAllPendingTickets = lotto900.AreAllPendingTickets({ticketNumber});
                print areAllPendingTickets areAllPendingTickets;
                if(areAllPendingTickets)
                {{
                    isSuccessful = lotto900.RemoveTickets(itIsThePresent, {ticketNumber}, Now, '{employeeName}');
                    print isSuccessful isSuccessful;
                }}
                else
                {{
                    print false isSuccessful;
                }}
            }}
            ");
            return result;
        }

        [HttpDelete("api/lotto/ticket/subticket")]
        [Authorize(Roles = "g")]
		public async Task<IActionResult> RemoveSubticketAsync(string subticketNumber, string employeeName)
        {
            if (String.IsNullOrWhiteSpace(subticketNumber)) return BadRequest($"Parameter {nameof(subticketNumber)} is required");
            var result = await LottoAPI.Actor.PerformCmdAsync(HttpContext, $@"
            {{
                isTicketAsNoAction = lotto900.IsTicketAsNoActionFor('{subticketNumber}');
                print isTicketAsNoAction isTicketAsNoAction;
                if(! isTicketAsNoAction)
                {{
                    isSuccessful = lotto900.RemoveSubticket(itIsThePresent, '{subticketNumber}', Now, '{employeeName}');
                    print isSuccessful isSuccessful;
                }}
                else
                {{
                    print false isSuccessful;
                }}
            }}
            ");
            return result;
        }

		[HttpPost("api/lotto/ticket/powerball/quick/{number}")]
        [Authorize(Roles = "player")]
        public async Task<IActionResult> QuickPowerballAsync(int number, [FromBody]TicketNumbersBody body)
		{
			if (number < 0) return BadRequest($"Parameter {nameof(number)} is required");
			var concatenatedNumbers = string.Join("','", body.TicketNumbers);
			var strTicketNumbers = $"'{concatenatedNumbers}'";
			var result =await LottoAPI.Actor.PerformQryAsync(/*This is a query because only have prints*/HttpContext, $@"
            {{
                quickGenerator = lotto900.GenerateRandomQuickPowerball({{{strTicketNumbers}}}, {number});
                for (ticketNumbers:quickGenerator.PowerBallNumbers())
                {{
                    print ticketNumbers ticketNumber;
                }}
            }}
            ");
			return result;
		}

        [HttpGet("api/lotto/ticket/documentNumber/availability")]
        [Authorize(Roles = "o")]
        public async Task<IActionResult> DocumentNumberAvailabilityAsync(string documentNumber)
        {
            if (String.IsNullOrWhiteSpace(documentNumber)) return NotFound($"Parameter {nameof(documentNumber)} is required");
            documentNumber = Validator.StringEscape(documentNumber);
            var result =await LottoAPI.Actor.PerformQryAsync(HttpContext, $@"
            {{
                exists = company.HasDocumentNumberAssigned({documentNumber});
                print exists exists;
            }}
            ");
            return result;
        }

        [HttpGet("api/lotto/ticket/ticketNumberZero")]
        public async Task<IActionResult> TicketsWithTicketNumberZeroAsync()
        {
            var result =await LottoAPI.Actor.PerformQryAsync(HttpContext, $@"
            {{
                ticketsWithTicketNumberZero = lotto900.TicketsWithTicketNumberZero();
                print ticketsWithTicketNumberZero.Count() countTickets;
                for (tickets : ticketsWithTicketNumberZero)
                {{
                    print tickets.TicketNumber ticketNumber;
                    print tickets.Player.AccountNumber accountNumber;
                    print tickets.AsString() numbers;
                    print tickets.Count totalSubtickets;
                    print tickets.GameType() gameType;
                    print tickets.GradingAsString gradingStatus;
                    drawDate = tickets.DrawDate;
                    print drawDate drawDate;
                    print drawDate.HHmmAMPM() hour;
                    print tickets.LotteryState().Abbreviation state;
                    print tickets.CreationDate() creationDate;
                }}
            }}
            ");
            return result;
        }

        [HttpGet("api/lotto/ticket/mismatchingWithWagerNumber")]
        [Authorize(Roles = "devops")]
        public async Task<IActionResult> TicketsMismatchingWagerNumbersAsync()
        {
            var result =await LottoAPI.Actor.PerformQryAsync(HttpContext, $@"
            {{
                ticketsMismatchingWithWagerNumbers = lotto900.TicketsMismatchingWithWagerNumbers();
                print ticketsMismatchingWithWagerNumbers.Count() countTickets;
                for (tickets : ticketsMismatchingWithWagerNumbers)
                {{
                    print tickets.TicketNumber ticketNumber;
                    print tickets.Player.AccountNumber accountNumber;
                    print tickets.AsString() numbers;
                    print tickets.Count totalSubtickets;
                    print tickets.GameType() gameType;
                    print tickets.GradingAsString gradingStatus;
                    drawDate = tickets.DrawDate;
                    print drawDate drawDate;
                    print drawDate.HHmmAMPM() hour;
                    print tickets.LotteryState().Abbreviation state;
                    print tickets.CountWagers countWagers;
                    print tickets.CountAccountingWagers countAccountingWagers;
                    if (tickets.IsPending())
                    {{
                        print tickets.CreationDate() creationDate;
                    }}
                }}
            }}
            ");
            return result;
        }

        [HttpGet("api/lotto/ticket/failedRegistration")]
        [Authorize(Roles = "devops")]
        public async Task<IActionResult> TicketsWithWagersNumberZeroAsync(DateTime startDate, DateTime endDate)
        {
            if (startDate == default(DateTime)) return BadRequest($"{nameof(startDate)} is required");
            if (endDate == default(DateTime)) return BadRequest($"{nameof(endDate)} is required");
            if (startDate.Hour != 0 || startDate.Minute != 0 || startDate.Second != 0) return BadRequest($"{nameof(startDate)} {startDate} is not valid");
            if (endDate.Hour != 0 || endDate.Minute != 0 || endDate.Second != 0) return BadRequest($"{nameof(endDate)} {endDate} is not valid");

            var startDateAsText = $"{startDate.Month}/{startDate.Day}/{startDate.Year}";
            var endDateAsText = $"{endDate.Month}/{endDate.Day}/{endDate.Year}";
            var result = await LottoAPI.Actor.PerformQryAsync(HttpContext, $@"
            {{
                ticketsWithWagersNumberZero = lotto900.TicketsWithWagersNumberZero({startDateAsText}, {endDateAsText});
                print ticketsWithWagersNumberZero.Count() countTickets;
                for (tickets : ticketsWithWagersNumberZero)
                {{
                    ticket = tickets;
                    print ticket.TicketNumber ticketNumber;
                    print ticket.Player.AccountNumber accountNumber;
                    print ticket.AsString() numbers;
                    print ticket.Count totalSubtickets;
                    print ticket.GameType() gameType;
                    print ticket.GradingAsString gradingStatus;
                    drawDate = ticket.DrawDate;
                    print drawDate drawDate;
                    print drawDate.HHmmAMPM() hour;
                    print ticket.LotteryState().Abbreviation state;
                    print ticket.CountWagers countWagers;
                    print ticket.CreationDate creationDate;
                }}
            }}
            ");
            return result;
        }

        [HttpGet("api/lotto/ticket/accountingPendingWager")]
        [Authorize(Roles = "devops")]
        public async Task<IActionResult> AccountingPendingWagersAsync()
        {
            var result =await LottoAPI.Actor.PerformQryAsync(HttpContext, $@"
            {{
                pendingWagers = lotto900.AccountingPendingWagers();
                print pendingWagers.Count() countOrders;
                for (wagers : pendingWagers)
                {{
                    print wagers.TicketNumber ticketNumber;
                    print wagers.OrderNumber orderNumber;
                    print wagers.InitialWagerNumber initialWagerNumber;
                    print wagers.FinalWagerNumber finalWagerNumber;
                    print wagers.CreationDate creationDate;
                }}
            }}
            ");
            return result;
        }

		[HttpGet("api/lotto/ticket/{ticketNumber}/wagers")]
        [Authorize(Roles = "devops")]
        public async Task<IActionResult> WagersPerTicketAsync(int ticketNumber)
        {
            var result =await LottoAPI.Actor.PerformQryAsync(HttpContext, $@"
			   {{
					tickets = lotto900.SearchTicketByNumber({ticketNumber});
	
					for(ticket : tickets)
					{{
						print ticket.CreationDate() date;
						print ticket.TicketNumber TicketNumber;
	
						for( wager : ticket.Wagers)
						{{
							print wager.betNumber referenceNumber ;
							print wager.Consecutive consecutive ;
							print wager.ReferenceNumber completeReferenceNumber;
							print wager.SubticketAsString subticketAsString;
							print wager.wagerNumber wagerNumber ;
							print wager.BetDescription description;
							print wager.AdjustedWinAmount adjustedWinAmount;
							print wager.AdjustedLossAmount adjustedLossAmount;
							print wager.Risk risk;
                            print wager.StatusAsLetter() status;
						}}
					}}
				}}
            ");
            return result;
        }

        [HttpGet("api/lotto/ticket")]
        [Authorize(Roles = "devops")]
        public async Task<IActionResult> TicketAsync(int ticketNumber, int pick, string state, DateTime drawDate)
        {
            if (ticketNumber <= 0) return BadRequest($"Parameter {nameof(ticketNumber)} is required");
            if (pick < 2 || pick > 5) return BadRequest($"Parameter {nameof(pick)} is not valid");
            if (string.IsNullOrWhiteSpace(state)) return BadRequest($"Parameter {state} is required");

            var stringDate = $"{drawDate.Month}/{drawDate.Day}/{drawDate.Year} {drawDate.Hour}:{drawDate.Minute}:{drawDate.Second}";
            var result = await LottoAPI.Actor.PerformQryAsync(HttpContext, $@"
			   {{
                    lottery = lotto900.GetLottery({pick}, state{state});
					ticket = lottery.SearchTicketBy({stringDate}, {ticketNumber});
                    print ticket.GameType() gameType;
                    schedule = lottery.FindScheduleAt({stringDate});
                    print schedule.GetDescription() description;
					for( numbers : ticket.Wagers)
					{{
						print numbers.SubticketAsString number;
					}}
				}}
            ");
            return result;
        }

        [HttpGet("api/lotto/ticket/{ticketNumber}/wagersWithNumberZero")]
        [Authorize(Roles = "devops")]
        public async Task<IActionResult> WagersWithNumberZeroAsync(int ticketNumber)
        {
            if (ticketNumber <= 0) return BadRequest($"Parameter {nameof(ticketNumber)} is required");

            var resultQry = await LottoAPI.Actor.PerformQryAsync(HttpContext, $@"
			   {{
					wagersWithNumberZero = lotto900.WagersWithNumberZero({ticketNumber});
                    print 'XXXX' systemID;
                    print 'YYYY' systemPassword;
                    print 'ZZZZ' clerkID;
                    print wagersWithNumberZero.AccountNumber customerID;
                    print {ticketNumber} ticketNumber;

					for( wagers : wagersWithNumberZero.Wagers)
					{{
                        wager = wagers;
						print wager.BetNumber referenceNumber;
						print wager.WagerNumber wagerNumber;
						print wager.BetDescription description;
						print wager.Risk risk;
                        print wager.ToWin toWin;
                        print wager.Consecutive consecutive;
					}}
				}}
            ");

            if (!(resultQry is OkObjectResult))
            {
                throw new Exception($@"Error:{((ObjectResult)resultQry).Value.ToString()}");
            }
            OkObjectResult o = (OkObjectResult)resultQry;
            string json = o.Value.ToString();
            dynamic body = JsonConvert.DeserializeObject(json);
            var postWagers = new List<PostFreeFormWagerV2>();
            foreach (var bodyWager in body.wagers)
            {
                var w = new PostFreeFormWagerV2() { 
                    BetDescription = bodyWager.description,
                    ReferenceNumber = bodyWager.referenceNumber,
                    Risk = bodyWager.risk,
                    ToWin = bodyWager.toWin,
                    WagerNumber = bodyWager.wagerNumber,
                    Consecutive = bodyWager.consecutive
                };
                postWagers.Add(w);
            }

            var result = new WagersRegistrationBodyV2()
            {
                AccountNumber = body.customerID,
                TicketNumber = ticketNumber,
                Wagers = postWagers.ToArray()
            };
            return Ok(result);
        }

        [HttpGet("api/lotto/ticket/{ticketNumber}/wagersForExternalGrading")]
        [Authorize(Roles = "devops")]
        public async Task<IActionResult> WagersForExternalGradingAsync(int ticketNumber)
        {
            if (ticketNumber <= 0) return BadRequest($"Parameter {nameof(ticketNumber)} is required");
            
            var resultQry = await LottoAPI.Actor.PerformQryAsync(HttpContext, $@"
			   {{
					wagersOfTicket = lotto900.WagersOfTicket({ticketNumber});
                    print 'XXXX' systemID;
                    print 'YYYY' systemPassword;
                    print 'ZZZZ' clerkID;

					for( wagers : wagersOfTicket.Wagers)
					{{
                        wager = wagers;
                        print '{ticketNumber}' ticketNumber;
						print wager.WagerNumber wagerNumber;
                        print wager.StatusAsLetter() outcome;
						print wager.DrawDate.Fecha().Ano.ToString()+''+wager.DrawDate.Fecha().Mes.ToString()+''+wager.DrawDate.Fecha().Dia.ToString() dailyFigureDate_YYYYMMDD;
						print wager.AdjustedWinAmount adjustedWinAmount;
                        print wager.AdjustedLossAmount adjustedLossAmount;
                        print true isValidTicketNumber;
					}}
				}}
            ");
            return resultQry;
        }

        [HttpGet("api/lotto/ticket/wagersGradedWithProblems")]
        [Authorize(Roles = "devops")]
        public async Task<IActionResult> WagersGradedWithProblemsAsync()
        {
            var result =await LottoAPI.Actor.PerformQryAsync(HttpContext, $@"
            {{
                wagersGradedWithProblems = lotto900.WagersGradedWithProblemsAboutIsValidTicket();
	            for (wagersWithProblems : wagersGradedWithProblems)
	            {{
                    wager = wagersWithProblems.Wager;
                    print wager.DrawDate drawDate;
                    print wager.TicketNumber ticketNumber;
                    print wager.AccountNumber accountNumber;
		            print wager.BetNumber referenceNumber;
		            print wager.Consecutive consecutive;
		            print wager.ReferenceNumber completeReferenceNumber;
		            print wager.SubticketAsString subticketAsString;
		            print wager.WagerNumber wagerNumber;
		            print wager.BetDescription description;
		            print wager.AdjustedWinAmount adjustedWinAmount;
		            print wager.AdjustedLossAmount adjustedLossAmount;
		            print wager.Risk risk;
                    print wager.IsWinner() isWinner;
                    print wagersWithProblems.ErrorMessage errorMessage;
	            }}
            }}

            ");
            return result;
        }

        [HttpPut("api/lotto/ticket/{ticketNumber}/wagersWithNumberZero")]
        [Authorize(Roles = "devops")]
        public async Task<IActionResult> UpdateWagersWithNumberZeroAsync(int ticketNumber, [FromBody] WagersRegistrationBodyV2 body)
        {
            if (body == null) return BadRequest("Body is required");
            if (ticketNumber <= 0 && body.TicketNumber <= 0) return BadRequest($"Parameter {nameof(ticketNumber)} is not valid");
            if (!body.Wagers.Any()) return BadRequest($"Parameter {nameof(body.Wagers)} is required");

            var consecutives = new List<string>();
            var betNumbers = new List<string>();
            var wagerNumbers = new List<string>();
            foreach (var wager in body.Wagers)
            {
                if (string.IsNullOrWhiteSpace(wager.ReferenceNumber)) return BadRequest($"{nameof(wager.ReferenceNumber)} is required");
                if (string.IsNullOrWhiteSpace(wager.Consecutive)) return BadRequest($"{nameof(wager.Consecutive)} is required");
                if (string.IsNullOrWhiteSpace(wager.WagerNumber)) return BadRequest($"{nameof(wager.WagerNumber)} is required");

                betNumbers.Add(wager.ReferenceNumber);
                consecutives.Add(wager.Consecutive);
                wagerNumbers.Add(wager.WagerNumber);
            }
            
            var betNumbersAsText = string.Join(",", betNumbers);
            var consecutivesAsText = string.Join(",", consecutives);
            var wagerNumbersAsText = string.Join(",", wagerNumbers);
            var result = await LottoAPI.Actor.PerformCmdAsync(HttpContext, $@"
                {{
                    lotto900.UpdateWagersWithNumberZero({ticketNumber}, {{{betNumbersAsText}}}, {{{consecutivesAsText}}}, {{{wagerNumbersAsText}}});
                }}
            ");

            var paymentProcessor = WholePaymentProcessor.Instance().SearchFragmentProcessor();
            PostFreeFormWagerCollectionSuccessResponse response;
            using (RecordSet recordSet = paymentProcessor.GetRecordSet())
            {
                recordSet.SetParameter("ticketNumber", body.TicketNumber);
                recordSet.SetParameter("customerId", body.AccountNumber);
                recordSet.SetParameter("wagers", body.ChangeWagersObjectForRequest());

                response = paymentProcessor.Execute<PostFreeFormWagerCollectionSuccessResponse>(DateTime.Now, recordSet);
            }
            return Ok(response);
        }

        [HttpPut("api/lotto/ticket/{ticketNumber}/wagers")]
        [Authorize(Roles = "devops")]
        public async Task<IActionResult> UpdateWagerAsync([FromBody]WagerBody body)
        {
            if (body == null) return BadRequest("Body is required");
          
            IActionResult result = null;
            result = await LottoAPI.Actor.PerformCmdAsync(HttpContext, $@"
                {{
                    lotto900.ChangeWagersAndReferenceNumber ({body.TicketNumber}, {body.OldBetNumber}, {body.OldConsecutive}, {body.OldWager},  {body.NewBetNumber}, {body.NewConsecutive}, {body.NewWager} );
                }}"
            );

            if (!(result is OkObjectResult)) throw new Exception(((ObjectResult)result).ToString());
            return Ok(result);
        }

		[HttpPut("api/lotto/ticket/{ticketNumber}/number")]
		[Authorize(Roles = "devops")]
		public async Task<IActionResult> UpdateTicketNumberAsync(string ticketNumber, [FromBody]TicketNumberBody body)
		{
			if (body == null) return BadRequest("Body is required");

			IActionResult result = null;
			result = await LottoAPI.Actor.PerformCmdAsync(HttpContext, $@"
                {{
                    lotto900.ChangeTicketNumber ({ticketNumber}, {body.NewTicketNumber});
                }}"
			);

			if (!(result is OkObjectResult)) throw new Exception(((ObjectResult)result).ToString());
			return Ok(result);
		}

		[HttpGet("api/lotto/ticket/wagers")]
		[Authorize(Roles = "devops")]
		public async Task<IActionResult> TicketWagersAsync(int ticketNumber)
		{
            var paymentProcessor = WholePaymentProcessor.Instance().SearchOtherProcessorBy(typeof(Tickets_And_Wagers));
            PostFreeFormWagerCollectionWagers response;
            using (RecordSet recordSet = paymentProcessor.GetRecordSet())
            {
                recordSet.SetParameter("ticketNumber", ticketNumber);

                response = await paymentProcessor.ExecuteAsync<PostFreeFormWagerCollectionWagers>(DateTime.Now, recordSet);
            }
			return Ok(response);
		}

		[HttpPut("api/lotto/ticket/updateWager")]
        [Authorize(Roles = "devops")]
        public async Task<IActionResult> UpdateWagerAsync([FromBody]UpdateWagerBody body)
        {
            if (body == null) return BadRequest("Body is required");
            if (body.TheLowestBetId > body.TheHighestBetId) return BadRequest("TheLowestBetId value can't be higher than TheHighestBetId value");
            if (body.TheLowestWagerNumber > body.TheHighestWagerNumber) return BadRequest("TheLowestWagerNumber value can't be higher than TheHighestWagerNumber value");

            IActionResult result = null;
            result = await LottoAPI.Actor.PerformCmdAsync(HttpContext, $@"
                {{
                    lotto900.CreateWagers(ItIsThePresent,{body.TheLowestBetId}, {body.TheHighestBetId}, {body.TheLowestWagerNumber}, {body.TheHighestWagerNumber}, {body.TicketNumber}, {body.OrderNumber}, Now);
                }}"
            );

            if (!(result is OkObjectResult)) throw new Exception(((ObjectResult)result).ToString());
            return Ok(result);
        }

        [HttpPost("api/lotto/ticket/accountingLock")]
        [Authorize(Roles = "devops")]
        public async Task<IActionResult> AccountingLockAsync([FromBody]AccountingLockBody body)
        {
            if (body == null) return BadRequest("Body is required");

            var paymentProcessor = WholePaymentProcessor.Instance().SearchBy(TransactionType.Sale, "USD");
            AuthorizationTransaction result;
            using (RecordSet recordSet = paymentProcessor.GetRecordSet())
            {
                recordSet.SetParameter("amount", body.Total);
                recordSet.SetParameter("purchaseBody", string.Empty);
                recordSet.SetParameter("customerId", body.AccountNumber);

                result = await paymentProcessor.ExecuteAsync<AuthorizationTransaction>(DateTime.Now, recordSet);
            }
            return Ok(result.AuthorizationId);
        }

        [HttpPost("api/lotto/ticket/wagersRegistration")]//No async coz is InternalAdmin only
        [Authorize(Roles = "devops")]
        public IActionResult WagersRegistration([FromBody]WagersRegistrationBody body)
        {
            if (body == null) return BadRequest("Body is required");

            var paymentProcessor = WholePaymentProcessor.Instance().SearchFragmentProcessor();
            PostFreeFormWagerCollectionSuccessResponse response;
            using (RecordSet recordSet = paymentProcessor.GetRecordSet())
            {
                recordSet.SetParameter("ticketNumber", body.TicketNumber);
                recordSet.SetParameter("customerId", body.AccountNumber);
                recordSet.SetParameter("wagers", body.Wagers);

                response = paymentProcessor.Execute<PostFreeFormWagerCollectionSuccessResponse>(DateTime.Now, recordSet);
            }
            return Ok(response);
        }

        [HttpPost("api/lotto/ticket/wagersGrading")]//No async coz is InternalAdmin only
        [Authorize(Roles = "devops")]
        public IActionResult WagersGrading([FromBody]WagersGradingBody body)
        {
            if (body == null) return BadRequest("Body is required");

            var paymentProcessor = WholePaymentProcessor.Instance().SearchGradeProcessorBy();
            GradeFreeFormWagersResponse response;
            using (RecordSet recordSet = paymentProcessor.GetRecordSet())
            {
                recordSet.SetParameter("wagers", body.Wagers);

                response = paymentProcessor.Execute<GradeFreeFormWagersResponse>(DateTime.Now, recordSet);
            }
            return Ok(response);
        }

		[HttpDelete("api/lotto/ticket/transaction")]
		public async Task<IActionResult> RemoveTransactionAsync(string ticketNumber)
		{
			if (String.IsNullOrWhiteSpace(ticketNumber)) return NotFound($"Parameter {nameof(ticketNumber)} is required");

            var paymentProcessor = WholePaymentProcessor.Instance().SearchOtherProcessorBy(typeof(RemoveTransaction));
            bool response;
            using (RecordSet recordSet = paymentProcessor.GetRecordSet())
            {
                recordSet.SetParameter("ticketNumber", ticketNumber);

                response = await paymentProcessor.ExecuteAsync<bool>(DateTime.Now, recordSet);
            }
			return Ok(response);
		}

        [HttpPost("api/lotto/ticket/wager/payment")]
        [Authorize(Roles = "devops")]
        public async Task<IActionResult> PayWagerAsync([FromBody]WagerPaymentBody body)
        {
            if (body == null) return BadRequest("Body is required");

            var paymentProcessor = WholePaymentProcessor.Instance().SearchOtherProcessorBy(typeof(Tickets_And_Wagers));
            PostFreeFormWagerCollectionWagers response;
            using (RecordSet recordSet = paymentProcessor.GetRecordSet())
            {
                recordSet.SetParameter("ticketNumber", body.TicketNumber);

                response = await paymentProcessor.ExecuteAsync<PostFreeFormWagerCollectionWagers>(DateTime.Now, recordSet);
            }
			var existWagerNumberInAccounting = false;
            foreach (var wager in response.Wagers)
            {
                if (wager.WagerNumber== body.WagerNumber.ToString())
                {
                    existWagerNumberInAccounting = true;
                    break;
                }
            }
            if (existWagerNumberInAccounting)
            {
                var result = await LottoAPI.Actor.PerformCmdAsync(HttpContext, $@"
                    {{
                        lotto900.PayWager(ItIsThePresent, {body.TicketNumber}, {body.WagerNumber}, Now);
                    }}"
                );
                return result;
            }

			return BadRequest("Wager must be fixed before to pay");
		}

        [DataContract(Name = "ticketNumbersBody")]
        public class TicketNumbersBody
        {
            [DataMember(Name = "ticketNumbers")]
            public string[] TicketNumbers { get; set; }
        }

        [DataContract(Name = "tickets")]
        public class TicketsBody
        {
            public TicketsBody()
            {
                CurrencyCode = Coinage.Coin(Currencies.CODES.USD).Iso4217Code;
            }
            [DataMember(Name = "currencyCode")]
            public string CurrencyCode { get; set; }
            [DataMember(Name = "tickets")]
            public Ticket[] Tickets { get; set; }
            [DataMember(Name = "accountNumber")]
            public string AccountNumber { get; set; }
            [DataMember(Name = "issued")]
            public string Issued { get; set; }
        }

        [DataContract(Name = "ticketsPowerball")]
        public class TicketsPowerballBody
        {
            public TicketsPowerballBody()
            {
                Currency = Coinage.Coin(Currencies.CODES.USD).Iso4217Code;
            }
            [DataMember(Name = "currency")]
            public string Currency { get; set; }
            [DataMember(Name = "tickets")]
            public TicketPowerball[] Tickets { get; set; }
        }

        [DataContract(Name = "ticketsKeno")]
        public class TicketsKenolBody
        {
            [DataMember(Name = "currencyCode")]
            public string CurrencyCode { get; set; }
            [DataMember(Name = "amount")]
            public decimal Amount { get; set; }
            [DataMember(Name = "numbers")]
            public int[] Numbers { get; set; }
            [DataMember(Name = "draw")]
            public string Draw { get; set; }
            [DataMember(Name = "hasMultiplier")]
            public bool HasMultiplier { get; set; }
            [DataMember(Name = "hasBullsEye")]
            public bool HasBullsEye { get; set; }
        }

        [DataContract(Name = "ticketToBuy")]
        public class Ticket
        {
            [DataMember(Name = "gameType")]
            public string GameType { get; set; }
            [DataMember(Name = "selectionMode")]
            public string SelectionMode { get; set; }
            [DataMember(Name = "ruleType")]
            public string RuleType { get; set; }
            [DataMember(Name = "numbersPattern")]
            public string[] NumbersPattern { get; set; }
            [DataMember(Name = "subtickets")]
            public string[] Subtickets { get; set; }
            [DataMember(Name = "excludeTickets")]
            public ExcludeTicket[] ExcludeTickets { get; set; }
            [DataMember(Name = "excludeSubtickets")]
            public ExcludeSubticket[] ExcludeSubtickets { get; set; }
            [DataMember(Name = "amount")]
            public decimal Amount { get; set; }
            [DataMember(Name = "dates")]
            public string[] Dates { get; set; }
            [DataMember(Name = "drawSchedules")]
            public DrawScheduleTicket[] DrawSchedules { get; set; }
        }

		[DataContract(Name = "ticketPowerballToBuy")]
		public class TicketPowerball
		{
			[DataMember(Name = "amount")]
			public decimal Amount { get; set; }
			[DataMember(Name = "dozenOfNumber")]
			public string DozenOfNumber { get; set; }
			[DataMember(Name = "powerplay")]
			public bool Powerplay { get; set; }
			[DataMember(Name = "date")]
			public string Date { get; set; }
		}

        [DataContract(Name = "OrderResponseWithExternalTickets")]
        public class OrderResponseWithExternalTickets : OrderResponse
        {
            [DataMember(Name = "tickets")]
            public List<ToWinByDrawAndNumber> Tickets { get; set; } = new List<ToWinByDrawAndNumber>();
            [DataMember(Name = "purchaseDate")]
            public DateTime PurchaseDate { get; set; }
            internal void Append(OrderResponseWithExternalTickets order)
            {
                Total += order.Total;
                Tickets.AddRange(order.Tickets);
            }

            internal DateTime GetMaxDateToPurchase()
            {
                return Tickets.Select(x => DateTime.ParseExact(x.drawDate, "M/d/yyyy", Integration.CultureInfoEnUS)).Max();
            }
        }

        [DataContract(Name = "PlayerResponse")]
        public class PlayerResponse
        {
            [DataMember(Name = "agentNumber")]
            public int AgentNumber { get; set; }
            public Agents Agent { get { return (Agents)AgentNumber; } }
        }

        [DataContract(Name = "OrderResponse")]
        public class OrderResponse
        {      
            [DataMember(Name = "draw")]
            public int Draw { get; set; }

            [DataMember(Name = "total")]
            public decimal Total { get; set; }
            [DataMember(Name = "atAddress")]
            public string AtAddress { get; set; }
            [DataMember(Name = "anyTicketIsExpired")]
            public bool AnyTicketIsExpired { get; set; }
            [DataMember(Name = "errorMessage")]
            public string ErrorMessage { get; set; }
            [DataMember(Name = "isValidToRetryPurchase")]
            public bool IsValidToRetryPurchase { get; set; }
            [DataMember(Name = "excludeSubtickets")]
            public List<ExcludeSubticket> ExcludeSubtickets { get; set; }
            [DataMember(Name = "exclusionsForBalls")]
            public List<ExcludeSubticket> ExclusionsForBalls { get; set; }
            [DataMember(Name = "exclusionsForInputs")]
            public List<ExcludeSubticket> ExclusionsForInputs { get; set; }
            [DataMember(Name = "datesToPurchase")]
            public DateToPurchaseResponse[] DatesToPurchase { get; set; }
            [DataMember(Name = "maxDateToPurchase")]
            public DateTime MaxDateToPurchase { get; set; }
            [DataMember(Name = "hoursKeepingAliveTickets")]
            public int HoursKeepingAliveTickets { get; set; }
            [DataMember(Name = "storeId")]
            public int StoreId { get; set; }
            [DataMember(Name = "agentNumber")]
            public int AgentNumber { get; set; }
            public Agents Agent { get { return (Agents)AgentNumber; } }
            [DataMember(Name = "domainId")]
            public int DomainId { get; set; }
            [DataMember(Name = "domainUrl")]
            public string DomainUrl { get; set; }
            [DataMember(Name = "existsDomainInMarketplace")]
            public bool ExistsDomainInMarketplace { get; set; }
            [DataMember(Name = "purchaseDate")]
            public DateTime PurchaseDate { get; set; }
        }

        [DataContract(Name = "OrderKenoResponse")]
        public class OrderKenoResponse
        {
            [DataMember(Name = "currentDraw")]
            public string CurrentDraw { get; set; }
            [DataMember(Name = "fixedDraw")]     
            public string FixedDraw { get; set; }
            [DataMember(Name = "total")]
            public decimal Total { get; set; }
            [DataMember(Name = "atAddress")]
            public string AtAddress { get; set; }
            [DataMember(Name = "nextDrawDate")]
            public string NextDrawDate { get; set; }
            [DataMember(Name = "anyTicketIsExpired")]
            public bool AnyTicketIsExpired { get; set; }
            [DataMember(Name = "errorMessage")]
            public string ErrorMessage { get; set; }
            [DataMember(Name = "hoursKeepingAliveTickets")]
            public int HoursKeepingAliveTickets { get; set; }
            [DataMember(Name = "storeId")]
            public int StoreId { get; set; }
            [DataMember(Name = "agentNumber")]
            public int AgentNumber { get; set; }
            public Agents Agent { get { return (Agents)AgentNumber; } }
            [DataMember(Name = "domainId")]
            public int DomainId { get; set; }
            [DataMember(Name = "existsDomainInMarketplace")]
            public bool ExistsDomainInMarketplace { get; set; }
        }
        [DataContract(Name = "DateToPurchase")]
        public class DateToPurchaseResponse
        {
            [DataMember(Name = "dateToPurchase")]
            public string DateToPurchase { get; set; }
        }

        [DataContract(Name = "OrderErrorResponseForSingleSelection")]
        public class OrderErrorResponseForSingleSelection
        {
            [DataMember(Name = "errorMessage")]
            public string ErrorMessage { get; set; }
            [DataMember(Name = "isValidToRetryPurchase")]
            public bool IsValidToRetryPurchase { get; set; }
            [DataMember(Name = "excludeSubtickets")]
            public List<ExcludeSubticket> ExcludeSubtickets { get; set; }

            public OrderErrorResponseForSingleSelection()
            {

            }

            public OrderErrorResponseForSingleSelection(OrderResponse orderResponse)
            {
                if (orderResponse.ExcludeSubtickets == null) throw new ArgumentNullException(nameof(orderResponse.ExcludeSubtickets));

                ErrorMessage = orderResponse.ErrorMessage;
                IsValidToRetryPurchase = orderResponse.IsValidToRetryPurchase;
                ExcludeSubtickets = orderResponse.ExcludeSubtickets;
            }

            public OrderErrorResponseForSingleSelection(OrderErrorResponseAccumulated orderResponse)
            {
                if (orderResponse.ExcludeSubtickets == null) throw new ArgumentNullException(nameof(orderResponse.ExcludeSubtickets));

                ErrorMessage = orderResponse.ErrorMessage;
                IsValidToRetryPurchase = orderResponse.IsValidToRetryPurchase;
                ExcludeSubtickets = orderResponse.ExcludeSubtickets;
        }
        }

        [DataContract(Name = "OrderErrorResponseForMultiSelection")]
        public class OrderErrorResponseForMultiSelection
        {
            [DataMember(Name = "errorMessage")]
            public string ErrorMessage { get; set; }
            [DataMember(Name = "isValidToRetryPurchase")]
            public bool IsValidToRetryPurchase { get; set; }
            [DataMember(Name = "exclusionsForBalls")]
            public List<ExcludeSubticket> ExclusionsForBalls { get; set; }
            [DataMember(Name = "exclusionsForInputs")]
            public List<ExcludeSubticket> ExclusionsForInputs { get; set; }

            public OrderErrorResponseForMultiSelection()
            {

            }

            public OrderErrorResponseForMultiSelection(OrderResponse orderResponse)
            {
                if (orderResponse.ExclusionsForBalls == null && orderResponse.ExclusionsForInputs == null) throw new ArgumentNullException(nameof(orderResponse));

                ErrorMessage = orderResponse.ErrorMessage;
                IsValidToRetryPurchase = orderResponse.IsValidToRetryPurchase;
                ExclusionsForBalls = orderResponse.ExclusionsForBalls;
                ExclusionsForInputs = orderResponse.ExclusionsForInputs;
            }

            public OrderErrorResponseForMultiSelection(OrderErrorResponseAccumulated orderResponse)
            {
                if (orderResponse.ExclusionsForInputs == null) throw new ArgumentNullException(nameof(orderResponse));

                ErrorMessage = orderResponse.ErrorMessage;
                IsValidToRetryPurchase = orderResponse.IsValidToRetryPurchase;
                ExclusionsForInputs = orderResponse.ExclusionsForInputs;
        }
        }

        [DataContract(Name = "OrderErrorResponseAccumulated")]
        public class OrderErrorResponseAccumulated
        {
            [DataMember(Name = "errorMessage")]
            public string ErrorMessage { get; set; }
            [DataMember(Name = "isValidToRetryPurchase")]
            public bool IsValidToRetryPurchase { get; set; }
            [DataMember(Name = "excludeSubtickets")]
            public List<ExcludeSubticket> ExcludeSubtickets { get; set; }
            [DataMember(Name = "exclusionsForInputs")]
            public List<ExcludeSubticket> ExclusionsForInputs { get; set; }

            internal void Append(OrderResponse order)
            {
                if (string.IsNullOrWhiteSpace(ErrorMessage)) ErrorMessage = order.ErrorMessage;
                else if (ErrorMessage != order.ErrorMessage) throw new GameEngineException($"Error messages cannot be mixed in response. Errors: {ErrorMessage} and {order.ErrorMessage}");

                IsValidToRetryPurchase = IsValidToRetryPurchase || order.IsValidToRetryPurchase;
                if (order.ExcludeSubtickets != null)
                {
                    if (ExcludeSubtickets == null) ExcludeSubtickets = order.ExcludeSubtickets;
                    else ExcludeSubtickets.AddRange(order.ExcludeSubtickets);
                }
                else if (order.ExclusionsForInputs != null)
                {
                    if (ExclusionsForInputs == null) ExclusionsForInputs = order.ExclusionsForInputs;
                    ExclusionsForInputs.AddRange(order.ExclusionsForInputs);
                }
            }
        }

        [DataContract(Name = "accountingLockBody")]
        public class AccountingLockBody
        {
            [DataMember(Name = "total")]
            public decimal Total { get; set; }
            [DataMember(Name = "accountNumber")]
            public string AccountNumber { get; set; }
        }

        [DataContract(Name = "wagerRegistrationBody")]
        public class WagersRegistrationBody
        {
            [DataMember(Name = "ticketNumber")]
            public int TicketNumber { get; set; }
            [DataMember(Name = "accountNumber")]
            public string AccountNumber { get; set; }
            [DataMember(Name = "wagers")]
            public PostFreeFormWager[] Wagers { get; set; }
        }

        [DataContract(Name = "kenoTicket")]
        public class KenoTicketBody
        {
            [DataMember(Name = "currencyCode")]
            public Currencies.CODES CurrencyCode { get; set; }
            [DataMember(Name = "amount")]
            public decimal Amount { get; set; }
            [DataMember(Name = "numbers")]
            public int[] Numbers { get; set; }
            [DataMember(Name = "draw")]
            public int Draw { get; set; }
            [DataMember(Name = "hasMultiplier")]
            public bool HasMultiplier { get; set; }
            [DataMember(Name = "hasBullseye")]
            public bool HasBullsEye { get; set; }
        }

        [DataContract(Name = "wagerRegistrationBodyV2")]
        public class WagersRegistrationBodyV2
        {
            [DataMember(Name = "ticketNumber")]
            public int TicketNumber { get; set; }
            [DataMember(Name = "accountNumber")]
            public string AccountNumber { get; set; }
            [DataMember(Name = "wagers")]
            public PostFreeFormWagerV2[] Wagers { get; set; }

            public PostFreeFormWager[] ChangeWagersObjectForRequest()
            {
                var result = new PostFreeFormWager[Wagers.Length];
                for (int index = 0; index < Wagers.Length; index++)
                {
                    var w = Wagers[index];
                    result[index] = new PostFreeFormWager()
                    {
                        BetDescription = w.BetDescription,
                        ReferenceNumber = w.ReferenceNumber,
                        Risk = w.Risk,
                        Status = w.Status,
                        ToWin = w.ToWin,
                        WagerNumber = w.WagerNumber
                    };
                }
                return result;
            }
        }

        [DataContract(Name = "postFreeFormWager2")]
        public class PostFreeFormWagerV2
        {
            [DataMember(Name = "risk")]
            public string Risk { get; set; }
            [DataMember(Name = "toWin")]
            public string ToWin { get; set; }
            [DataMember(Name = "betDescription")]
            public string BetDescription { get; set; }
            [DataMember(Name = "wagerNumber")]
            public string WagerNumber { get; set; }
            [DataMember(Name = "referenceNumber")]
            public string ReferenceNumber { get; set; }
            [DataMember(Name = "status")]
            public string Status { get; set; }
            [DataMember(Name = "consecutive")]
            public string Consecutive { get; set; }
        }

        [DataContract(Name = "gradingBody")]
        public class WagersGradingBody
        {
            [DataMember(Name = "wagers")]
            public List<PayFragmentsMessage> Wagers { get; set; }
        }

        [DataContract(Name = "drawScheduleTicket")]
        public class DrawScheduleTicket
        {
            [DataMember(Name = "state")]
            public string State { get; set; }
            [DataMember(Name = "hour")]
            public string Hour { get; set; }
            [DataMember(Name = "isPresentPlayerBeforeToCloseStore")]
            public bool IsPresentPlayerBeforeToCloseStore { get; set; }
            [DataMember(Name = "useNextDate")]
            public bool UseNextDate { get; set; }
            [DataMember(Name = "withFireBall")]
            public bool WithFireBall { get; set; }
        }

		[DataContract(Name = "drawScheduleTicketPowerball")]
		public class DrawScheduleTicketPowerball
		{
			[DataMember(Name = "state")]
			public string State { get; set; }
			[DataMember(Name = "hour")]
			public string[] Hour { get; set; }
			[DataMember(Name = "isPresentPlayerBeforeToCloseStore")]
			public bool IsPresentPlayerBeforeToCloseStore { get; set; }
		}

		[DataContract(Name = "excludeTicket")]
        public class ExcludeTicket
        {
            [DataMember(Name = "state")]
            public string State { get; set; }
            [DataMember(Name = "hour")]
            public string Hour { get; set; }
            [DataMember(Name = "date")]
            public string Date { get; set; }
        }

        [DataContract(Name = "excludeSubticket")]
        public class ExcludeSubticket
        {
            [DataMember(Name = "state")]
            public string State { get; set; }
            [DataMember(Name = "hour")]
            public string Hour { get; set; }
            [DataMember(Name = "date")]
            public string Date { get; set; }
            [DataMember(Name = "subticket")]
            public string Subticket { get; set; }
            [DataMember(Name = "pick")]
            public int PickNumber { get; set; }

            public ExcludeSubticket()
            {

            }
            public ExcludeSubticket(DrawScheduleTicket drawSchedule, string date)
            {
                State = drawSchedule.State;
                Hour = drawSchedule.Hour;
                Date = date;
                Subticket = ExcludeSubtickets.INDICATOR_FOR_ALL_SUBTICKETS_EXCLUDED;
            }
        }

        [DataContract(Name = "ticketNumberBody")]
        public class TicketNumberBody
        {
            [DataMember(Name = "newticketNumber")]
            public int NewTicketNumber { get; set; }
        }
        [DataContract(Name = "updateWagerBody")]
        public class UpdateWagerBody
        {
            [DataMember(Name = "theLowestWagerNumber")]
            public int TheLowestWagerNumber { get; set; }
            [DataMember(Name = "theHighestWagerNumber")]
            public int TheHighestWagerNumber { get; set; }
            [DataMember(Name = "theLowestBetId")]
            public int TheLowestBetId { get; set; }
            [DataMember(Name = "theHighestBetId")]
            public int TheHighestBetId { get; set; }
            [DataMember(Name = "ticketNumber")]
            public int TicketNumber { get; set; }
            [DataMember(Name = "orderNumber")]
            public int OrderNumber { get; set; }
        }

		[DataContract(Name = "wagerBody")]
		public class WagerBody
		{
			[DataMember(Name = "ticketNumber")]
			public int TicketNumber { get; set; }
			[DataMember(Name = "oldBetNumber")]
			public int OldBetNumber { get; set; }
			[DataMember(Name = "oldConsecutive")]
			public int OldConsecutive { get; set; }
			[DataMember(Name = "oldWager")]
			public int OldWager { get; set; }
			[DataMember(Name = "newBetNumber")]
			public int NewBetNumber { get; set; }
			[DataMember(Name = "newConsecutive")]
			public int NewConsecutive { get; set; }
			[DataMember(Name = "newWager")]
			public int NewWager { get; set; }
        }

        [DataContract(Name = "wagerPaymentBody")]
        public class WagerPaymentBody
        {
            [DataMember(Name = "ticketNumber")]
            public int TicketNumber { get; set; }
            [DataMember(Name = "wagerNumber")]
            public int WagerNumber { get; set; }
        }

    }
}
