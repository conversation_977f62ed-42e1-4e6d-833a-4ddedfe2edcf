﻿using GamesEngine;
using GamesEngine.Finance;
using GamesEngine.Games.Lines;
using GamesEngine.Settings;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Globalization;
using System.Runtime.Serialization;
using System.Text;
using System.Threading.Tasks;
using GamesEngine.Tools;
using static LinesAPI.Security;
using GamesEngine.RealTime;
using System.Collections.Generic;
using Newtonsoft.Json.Linq;
using GamesEngine.PurchaseOrders;
using GamesEngine.Gameboards.Lines;
using Newtonsoft.Json;
using Microsoft.AspNetCore.Http;
using GamesEngine.Games.Tournaments;
using Microsoft.AspNetCore.Authorization;
using town.connectors.commons;
using GamesEngine.Business;
using town.connectors.drivers.fiero;
using static GamesEngine.Business.WholePaymentProcessor;
using static town.connectors.CustomSettings;
using Connectors.town.connectors.driver.transactions;
using Authorization = town.connectors.drivers.fiero.Authorization;

namespace LinesAPI.Controllers
{
	//   public class WagerPurchase
	//{
	//       private WagerBody body;
	//       private DateTime date;
	//       private Domain domain
	//}

	public class WagerController : AuthorizeController
	{
        //Hacer nuevo servicio que la pantalla de los Delay dijo cancelar este body con Id temp 123
        //cancelado = true;
        public string GetCommonScriptForPurchase(WagerBody body, string domain, int authorizationId)
        {
            var playerId = Validator.StringEscape(body.PlayerId);

            var script = $@"tournament = company.Tournaments.FindById({body.TournamentId});
                    betBoard = company.Betboard(tournament);
                    domain = company.Sales.DomainFrom('{domain}');
                    accountNumber = company.EncryptionHelper.Decrypt('{body.PlayerId}');
                    customer = company.CustomerByAccountNumber(accountNumber);
                    Eval('orderNumber = ' + company.IdentityOrderNumber + ';');
                    order = company.NewOrder(customer, orderNumber, { body.CurrencyCode}, { authorizationId});
                    Eval('betNumber = ' + company.IdentitytBetNumber + ';');
                    line = betBoard.GetLine({ body.LineId});
                    game = tournament.GetGameNumber({ body.GameNumber});
                    print game.ScheduledDate scheduledDate; ";
            return script;
        }

        private void FixAccountsNumbersForDemoPurposesOnly(WagerBody body )
        {
            if (string.IsNullOrWhiteSpace(body.AccountNumber) && body.PlayerId == "ID012345678901234567890123456789012346")
            {
                body.AccountNumber = "cr00030004";
            }
            else if (string.IsNullOrWhiteSpace(body.AccountNumber) && body.PlayerId == "iCDlytS7kCxVPfgc0uSVvfJDtkgoUtRUtOSb09HFuQk=")
            { 
                body.AccountNumber = "cr00040006";
            }
        }
        private async Task<string> GetAtAddressAsync(HttpContext context, string playerId)
        {
            playerId = Validator.StringEscape(playerId);

            var playerResult = await LinesAPI.Lines.PerformQryAsync(context, $@"
            {{
                  player = company.GetOrCreateCustomerById('{playerId}').Player;
                  print player.AccountNumber atAddress;
            }}
            ");

            if (!(playerResult is OkObjectResult))
            {
                throw new GameEngineException($@"Error:{((ObjectResult)playerResult).Value.ToString()}");
            }

            OkObjectResult o = (OkObjectResult)playerResult;
            string json = o.Value.ToString();
            var playerInfo = JsonConvert.DeserializeObject<PlayerInfo>(json);
            return playerInfo.AtAddress;
        }

        const int DaysToBecomeUseless = 7;
        [HttpPost("api/lines/wager/spreadline")]
        [Authorize(Roles = "devops")]//VERIFY_ROLES
        public async Task<IActionResult> CreateSpreadLineWagerAsync([FromBody] WagerBody body)
		{
			if (body == null) return BadRequest("Body is required");
            if (body.TournamentId <= 0) return BadRequest($"{nameof(body.TournamentId)} must be greater than 0");
            if (body.GameNumber <= 0) return BadRequest($"{nameof(body.GameNumber)} must be greater than 0");
            if (body.LineId <= 0) return BadRequest($"{nameof(body.LineId)} must be greater than 0");
            if (body.Amount <= 0) return BadRequest($"{nameof(body.Amount)} must be greater than 0");
            if (string.IsNullOrWhiteSpace(body.ChosenOption)) return BadRequest($"{nameof(body.ChosenOption)} must be greater than 0");
            FixAccountsNumbersForDemoPurposesOnly(body);
            if (string.IsNullOrWhiteSpace(body.AccountNumber)) return BadRequest($"{nameof(body.AccountNumber)} is required.");
            if(body.ChosenOption != "A" && body.ChosenOption != "B") return BadRequest("Sorry, your purchase cannot be completed at this time. Please try again or contact the support team.");

            string atAddress = await GetAtAddressAsync(HttpContext, body.PlayerId);
            string domain = HttpContext.Request.Host.Host;

            string team = body.ChosenOption == "A" ? "game.TeamA" : "game.TeamB";
            var result = await LinesAPI.Lines.PerformChkThenQryAsync(HttpContext, $@"
                    existsTournament = company.Tournaments.Exists({body.TournamentId});
                    if (existsTournament)
                    {{
                        tournament = company.Tournaments.FindById({body.TournamentId});
                        existsGame = tournament.ExistsGame({body.GameNumber});
                        betBoard = company.Betboard(tournament);
                        existsLine = betBoard.ExistLine({body.LineId});
                        existsDomain = company.Sales.ExistsDomain('{domain}');
                        if (existsGame && existsDomain && existsLine)
                        {{
                            domain = company.Sales.DomainFrom('{domain}');
                            store = company.Sales.CurrentStore;
                            isEnabled = store.IsEnabledOn(domain);
                            game = tournament.GetGameNumber({ body.GameNumber});
                            isEnabledSport = store.IsEnabled(game.Sport, domain);
                            line = betBoard.GetLine({ body.LineId});
                            isTheSameGame = line.Game == game;
                            isEnabledLine = line.IsEnabled(domain);
                            scheduledDay = game.ScheduledDate.Fecha();
                            matchDay = betBoard.Matchday(scheduledDay);
                            showcase = matchDay.GetShowcase(game);
                            isEnabledMatch = showcase.IsEnabled(domain);
                            Check(isEnabled && isEnabledSport && isEnabledLine && isEnabledMatch && line.IsPending() && line.IsPublished && line.IsLastVersion() && line.RemainingAvailabilityTime(Now) != 0 && isTheSameGame) Error 'Sorry, your purchase cannot be completed at this time. Please try again or contact the support team.';
                        }}
                        
                        Check(existsGame && existsDomain && existsLine) Error 'Sorry, your purchase cannot be completed at this time. Please try again or contact the support team.';
                    }}
                    Check(existsTournament) Error 'Sorry, your purchase cannot be completed at this time. Please try again or contact the support team.';
                ",
				$@"
				{{
                    tournament = company.Tournaments.FindById({body.TournamentId});
                    betBoard = company.Betboard(tournament);
                    game = tournament.GetGameNumber({ body.GameNumber});
                    scheduledDay = game.ScheduledDate.Fecha();
                    line = betBoard.GetLine({ body.LineId});
                    answer = line.GetTeamAnswer({team});

                    print company.Sales.CurrentStore.Id storeId;
                    print game.TeamA.Name TeamAName;
                    print game.TeamB.Name TeamBName;
                    print scheduledDay scheduledDay;
                    print company.IdentitytBetNumber referenceNumber;
                    print line.ToWinIfPlayerWould({body.Amount}, answer) toWin;
                    print answer.Text + ' ' + line.RewardAsString(answer) answerText;
				}}
			");

			if (!(result is OkObjectResult))
			{
                return result;
            }

			OkObjectResult o = (OkObjectResult)result;
			string json = o.Value.ToString();
            ContextInformation context = JsonConvert.DeserializeObject<ContextInformation>(json);

            string concept = $"Ladybet Game {context.TeamAName} vs {context.TeamBName} {context.ScheduledDay} Line {body.LineId} {context.AnswerText} Total {body.CurrencyCode}{body.Amount} for {body.CurrencyCode}{context.ToWin} prize";
            string referenceNumber = context.ReferenceNumber;
            var useless = DateTime.Now.AddDays(DaysToBecomeUseless);
            AuthorizationTransaction resultTransaction;
            PaymentProcessor paymentProcessor = WholePaymentProcessor.Instance().SearchSaleProcessorBy(typeof(Authorization));
            using (RecordSet recordSet = paymentProcessor.GetRecordSet())
            {
                recordSet.SetParameter("context", HttpContext);
                recordSet.SetParameter("atAddress", atAddress);
                recordSet.SetParameter("purchaseTotal.Value", body.Amount);
                recordSet.SetParameter("purchaseTotal.CurrencyCode", body.CurrencyCode);
                recordSet.SetParameter("storeId", context.StoreId);
                recordSet.SetParameter("concept", concept);
                recordSet.SetParameter("referenceNumber", referenceNumber);
                recordSet.SetParameter("accountNumber", body.AccountNumber);
                recordSet.SetParameter("fragmentInformation", new FragmentInformation()
                {
                    ItsConfigured = true,
                    Towin = context.ToWin
                });
                resultTransaction = await paymentProcessor.ExecuteAsync<AuthorizationTransaction>(DateTime.Now, recordSet);
            }
            /*int authorization = await Settings.AuthorizatorsAsync.PurchaseAsync(
                    HttpContext,
                    atAddress,
                    CryptCurrency.Factory(body.CurrencyCode, body.Amount),
                    context.StoreId,
                    concept,
                    referenceNumber,
                    body.AccountNumber,
                    useless,
                    new FragmentInformation() {
                        ItsConfigured = true,
                        Towin = context.ToWin
                    }); */

            var commonScriptForPurchase = GetCommonScriptForPurchase(body, domain, resultTransaction.AuthorizationId);
            var scriptOrderAddingLine = $"order.AddSpreadLine(betNumber, line, {team}, product, {body.Amount});";

            result = await LinesAPI.Lines.PerformCmdAsync(HttpContext, 
                $@"
				{{
                    {commonScriptForPurchase}
                    {scriptOrderAddingLine}
                    company.PurchaseOrder(itIsThePresent, order, domain, now);
				}}
			");
			return result;
		}

        [HttpPost("api/lines/wager/moneyline")]
        [Authorize(Roles = "devops")]//VERIFY_ROLES
        public async Task<IActionResult> CreateMoneyLineWagerAsync([FromBody] WagerBody body)
        {
            if (body == null) return BadRequest("Body is required");
            if (body.TournamentId <= 0) return BadRequest($"{nameof(body.TournamentId)} must be greater than 0");
            if (body.GameNumber <= 0) return BadRequest($"{nameof(body.GameNumber)} must be greater than 0");
            if (body.LineId <= 0) return BadRequest($"{nameof(body.LineId)} must be greater than 0");
            if (body.Amount <= 0) return BadRequest($"{nameof(body.Amount)} must be greater than 0");
            if (string.IsNullOrWhiteSpace(body.ChosenOption)) return BadRequest($"{nameof(body.ChosenOption)} must be greater than 0");
            if (string.IsNullOrWhiteSpace(body.ChosenOption)) return BadRequest($"{nameof(body.ChosenOption)} must be greater than 0");
            FixAccountsNumbersForDemoPurposesOnly(body);
            if (string.IsNullOrWhiteSpace(body.AccountNumber)) return BadRequest($"{nameof(body.AccountNumber)} is required.");
            if (body.ChosenOption != "A" && body.ChosenOption != "B") return BadRequest("Sorry, your purchase cannot be completed at this time. Please try again or contact the support team.");

            string atAddress = await GetAtAddressAsync(HttpContext, body.PlayerId);
            string domain = HttpContext.Request.Host.Host;
            string team = body.ChosenOption == "A" ? "game.TeamA" : "game.TeamB";

            var result = await LinesAPI.Lines.PerformChkThenQryAsync(HttpContext, $@"
                    existsTournament = company.Tournaments.Exists({body.TournamentId});
                    if (existsTournament)
                    {{
                        tournament = company.Tournaments.FindById({body.TournamentId});
                        existsGame = tournament.ExistsGame({body.GameNumber});
                        betBoard = company.Betboard(tournament);
                        existsLine = betBoard.ExistLine({body.LineId});
                        existsDomain = company.Sales.ExistsDomain('{domain}');
                        if (existsGame && existsDomain && existsLine)
                        {{
                            domain = company.Sales.DomainFrom('{domain}');
                            store = company.Sales.CurrentStore;
                            isEnabled = store.IsEnabledOn(domain);
                            game = tournament.GetGameNumber({ body.GameNumber});
                            isEnabledSport = store.IsEnabled(game.Sport, domain);
                            line = betBoard.GetLine({ body.LineId});
                            isTheSameGame = line.Game == game;
                            isEnabledLine = line.IsEnabled(domain);
                            scheduledDay = game.ScheduledDate.Fecha();
                            matchDay = betBoard.Matchday(scheduledDay);
                            showcase = matchDay.GetShowcase(game);
                            isEnabledMatch = showcase.IsEnabled(domain);
                            Check(isEnabled && isEnabledSport && isEnabledLine && isEnabledMatch && line.IsPending() && line.IsPublished && line.IsLastVersion() && line.RemainingAvailabilityTime(now) != 0 && isTheSameGame) Error 'Sorry, your purchase cannot be completed at this time. Please try again or contact the support team.';
                        }}
                        Check(existsGame && existsDomain && existsLine) Error 'Sorry, your purchase cannot be completed at this time. Please try again or contact the support team.';
                    }}
                    Check(existsTournament) Error 'Sorry, your purchase cannot be completed at this time. Please try again or contact the support team.';
                ",
                $@"
				{{
                    tournament = company.Tournaments.FindById({body.TournamentId});
                    betBoard = company.Betboard(tournament);
                    game = tournament.GetGameNumber({ body.GameNumber});
                    scheduledDay = game.ScheduledDate.Fecha();
                    line = betBoard.GetLine({ body.LineId});
                    answer = line.GetTeamAnswer({team});

                    print company.Sales.CurrentStore.Id storeId;
                    print game.TeamA.Name TeamAName;
                    print game.TeamB.Name TeamBName;
                    print scheduledDay scheduledDay;
                    print company.IdentitytBetNumber referenceNumber;
                    print line.ToWinIfPlayerWould({body.Amount}, answer) toWin;
                    print answer.Text + ' ' + line.RewardAsString(answer) answerText;
				}}
			");
            if (!(result is OkObjectResult))
            {
                throw new GameEngineException($@"Error:{((ObjectResult)result).Value.ToString()}");
            }
            OkObjectResult o = (OkObjectResult)result;
            string json = o.Value.ToString();
            ContextInformation context = JsonConvert.DeserializeObject<ContextInformation>(json);

            string concept = $"Ladybet Game {context.TeamAName} vs {context.TeamBName} {context.ScheduledDay} Line {body.LineId} {context.AnswerText} Total {body.CurrencyCode}{body.Amount} for {body.CurrencyCode}{context.ToWin} prize";
            string referenceNumber = context.ReferenceNumber;
            var useless = DateTime.Now.AddDays(DaysToBecomeUseless);
            AuthorizationTransaction resultTransaction;
            PaymentProcessor paymentProcessor = WholePaymentProcessor.Instance().SearchSaleProcessorBy(typeof(Authorization));
            using (RecordSet recordSet = paymentProcessor.GetRecordSet())
            {
                recordSet.SetParameter("context", HttpContext);
                recordSet.SetParameter("atAddress", atAddress);
                recordSet.SetParameter("purchaseTotal.Value", body.Amount);
                recordSet.SetParameter("purchaseTotal.CurrencyCode", body.CurrencyCode);
                recordSet.SetParameter("storeId", context.StoreId);
                recordSet.SetParameter("concept", concept);
                recordSet.SetParameter("referenceNumber", referenceNumber);
                recordSet.SetParameter("accountNumber", body.AccountNumber);
                recordSet.SetParameter("fragmentInformation", new FragmentInformation()
                {
                    ItsConfigured = true,
                    Towin = context.ToWin
                });
                resultTransaction = await paymentProcessor.ExecuteAsync<AuthorizationTransaction>(DateTime.Now, recordSet);
            }
            /*int authorization = await Settings.AuthorizatorsAsync.PurchaseAsync(
                    HttpContext,
                    atAddress,
                    CryptCurrency.Factory(body.CurrencyCode, body.Amount),
                    context.StoreId,
                    concept,
                    referenceNumber,
                    body.AccountNumber,
                    useless,
                    new FragmentInformation()
                    {
                        ItsConfigured = true,
                        Towin = context.ToWin
                    });*/

            var commonScriptForPurchase = GetCommonScriptForPurchase(body, domain, resultTransaction.AuthorizationId);
            var scriptOrderAddingLine = $"order.AddMoneyLine(betNumber, line, {team}, product, {body.Amount});";

            result = await LinesAPI.Lines.PerformCmdAsync(HttpContext, 
                $@"
				{{
                    {commonScriptForPurchase}
                    {scriptOrderAddingLine}
                    company.PurchaseOrder(itIsThePresent, order, domain, now);
				}}
			");
            return result;
        }

        [HttpPost("api/lines/wager/moneydrawline")]
        [Authorize(Roles = "devops")]//VERIFY_ROLES
        public async Task<IActionResult> CreateMoneyDrawLineWagerAsync([FromBody] WagerBody body)
        {
            if (body == null) return BadRequest("Body is required");
            if (body.TournamentId <= 0) return BadRequest($"{nameof(body.TournamentId)} must be greater than 0");
            if (body.GameNumber <= 0) return BadRequest($"{nameof(body.GameNumber)} must be greater than 0");
            if (body.LineId <= 0) return BadRequest($"{nameof(body.LineId)} must be greater than 0");
            if (body.Amount <= 0) return BadRequest($"{nameof(body.Amount)} must be greater than 0");
            if (string.IsNullOrWhiteSpace(body.ChosenOption)) return BadRequest($"{nameof(body.ChosenOption)} must be greater than 0");
            if (string.IsNullOrWhiteSpace(body.ChosenOption)) return BadRequest($"{nameof(body.ChosenOption)} must be greater than 0");
            FixAccountsNumbersForDemoPurposesOnly(body);
            if (string.IsNullOrWhiteSpace(body.AccountNumber)) return BadRequest($"{nameof(body.AccountNumber)} is required.");
            if (body.ChosenOption != "A" && body.ChosenOption != "B" && body.ChosenOption != "DRAW" && !string.IsNullOrWhiteSpace(body.ChosenOption)) return BadRequest("Sorry, your purchase cannot be completed at this time. Please try again or contact the support team.");

            string atAddress = await GetAtAddressAsync(HttpContext, body.PlayerId);
            string domain = HttpContext.Request.Host.Host;
            string team = body.ChosenOption == "A" ? "game.TeamA" : "game.TeamB";
            string lineGetAnswer = body.ChosenOption == "DRAW" ? "line.GetTieAnswer(game.TeamA, game.TeamB)" : $"line.GetTeamAnswer({team})";

            var result = await LinesAPI.Lines.PerformChkThenQryAsync(HttpContext, $@"
                    existsTournament = company.Tournaments.Exists({body.TournamentId});
                    if (existsTournament)
                    {{
                        tournament = company.Tournaments.FindById({body.TournamentId});
                        existsGame = tournament.ExistsGame({body.GameNumber});
                        betBoard = company.Betboard(tournament);
                        existsLine = betBoard.ExistLine({body.LineId});
                        existsDomain = company.Sales.ExistsDomain('{domain}');
                        if (existsGame && existsDomain && existsLine)
                        {{
                            domain = company.Sales.DomainFrom('{domain}');
                            store = company.Sales.CurrentStore;
                            isEnabled = store.IsEnabledOn(domain);
                            game = tournament.GetGameNumber({ body.GameNumber});
                            isEnabledSport = store.IsEnabled(game.Sport, domain);
                            line = betBoard.GetLine({ body.LineId});
                            isTheSameGame = line.Game == game;
                            isEnabledLine = line.IsEnabled(domain);
                            scheduledDay = game.ScheduledDate.Fecha();
                            matchDay = betBoard.Matchday(scheduledDay);
                            showcase = matchDay.GetShowcase(game);
                            isEnabledMatch = showcase.IsEnabled(domain);
                            Check(isEnabled && isEnabledSport && isEnabledLine && isEnabledMatch && line.IsPending() && line.IsPublished && line.IsLastVersion() && line.RemainingAvailabilityTime(now) != 0 && isTheSameGame) Error 'Sorry, your purchase cannot be completed at this time. Please try again or contact the support team.';
                        }}
                        Check(existsGame && existsDomain && existsLine) Error 'Sorry, your purchase cannot be completed at this time. Please try again or contact the support team.';
                    }}
                    Check(existsTournament) Error 'Sorry, your purchase cannot be completed at this time. Please try again or contact the support team.';
                ",
    $@"
				{{
                    tournament = company.Tournaments.FindById({body.TournamentId});
                    betBoard = company.Betboard(tournament);
                    game = tournament.GetGameNumber({ body.GameNumber});
                    scheduledDay = game.ScheduledDate.Fecha();
                    line = betBoard.GetLine({ body.LineId});
                    answer = {lineGetAnswer};

                    print company.Sales.CurrentStore.Id storeId;
                    print game.TeamA.Name TeamAName;
                    print game.TeamB.Name TeamBName;
                    print scheduledDay scheduledDay;
                    print company.IdentitytBetNumber referenceNumber;
                    print line.ToWinIfPlayerWould({body.Amount}, answer) toWin;
                    print answer.Text + ' ' + line.RewardAsString(answer) answerText;
				}}
			");
            OkObjectResult o = (OkObjectResult)result;
            string json = o.Value.ToString();
            ContextInformation context = JsonConvert.DeserializeObject<ContextInformation>(json);

            string concept = $"Ladybet Game {context.TeamAName} vs {context.TeamBName} {context.ScheduledDay} Line {body.LineId} {context.AnswerText} Total {body.CurrencyCode}{body.Amount} for {body.CurrencyCode}{context.ToWin} prize";
            string referenceNumber = context.ReferenceNumber;
            var useless = DateTime.Now.AddDays(DaysToBecomeUseless);
            AuthorizationTransaction resultTransaction;
            PaymentProcessor paymentProcessor = WholePaymentProcessor.Instance().SearchSaleProcessorBy(typeof(Authorization));
            using (RecordSet recordSet = paymentProcessor.GetRecordSet())
            {
                recordSet.SetParameter("context", HttpContext);
                recordSet.SetParameter("atAddress", atAddress);
                recordSet.SetParameter("purchaseTotal.Value", body.Amount);
                recordSet.SetParameter("purchaseTotal.CurrencyCode", body.CurrencyCode);
                recordSet.SetParameter("storeId", context.StoreId);
                recordSet.SetParameter("concept", concept);
                recordSet.SetParameter("referenceNumber", referenceNumber);
                recordSet.SetParameter("accountNumber", body.AccountNumber);
                recordSet.SetParameter("fragmentInformation", new FragmentInformation()
                {
                    ItsConfigured = true,
                    Towin = context.ToWin
                });
                resultTransaction = await paymentProcessor.ExecuteAsync<AuthorizationTransaction>(DateTime.Now, recordSet);
            }
            /*int authorization = await Settings.AuthorizatorsAsync.PurchaseAsync(
                    HttpContext,
                    atAddress,
                    CryptCurrency.Factory(body.CurrencyCode, body.Amount),
                    context.StoreId,
                    concept,
                    referenceNumber,
                    body.AccountNumber,
                    useless,
                    new FragmentInformation()
                    {
                        ItsConfigured = true,
                        Towin = context.ToWin
                    });*/

            var commonScriptForPurchase = GetCommonScriptForPurchase(body, domain, resultTransaction.AuthorizationId);
            string scriptOrderAddingLine;
            if (body.ChosenOption == "A") 
            {
                scriptOrderAddingLine = $"order.AddMoneyDrawLine(betNumber, line, game.TeamA, product, {body.Amount});";
            }
            else if(body.ChosenOption == "B")
            {
                scriptOrderAddingLine = $"order.AddMoneyDrawLine(betNumber, line, game.TeamB, product, {body.Amount});";
            }
            else
            {
                scriptOrderAddingLine = $"order.AddMoneyDrawLine(betNumber, line, game, product, {body.Amount});";
            }

            result = await LinesAPI.Lines.PerformCmdAsync(HttpContext, 
                $@"
				{{
                    {commonScriptForPurchase}
                    {scriptOrderAddingLine}
                    company.PurchaseOrder(itIsThePresent, order, domain, now);
				}}
			");
            return result;
        }

        [HttpPost("api/lines/wager/yesnoline")]
        [Authorize(Roles = "devops")]//VERIFY_ROLES
        public async Task<IActionResult> CreateYesNoLineWagerAsync([FromBody] WagerBody body)
        {
            if (body == null) return BadRequest("Body is required");
            if (body.TournamentId <= 0) return BadRequest($"{nameof(body.TournamentId)} must be greater than 0");
            if (body.GameNumber <= 0) return BadRequest($"{nameof(body.GameNumber)} must be greater than 0");
            if (body.LineId <= 0) return BadRequest($"{nameof(body.LineId)} must be greater than 0");
            if (body.Amount <= 0) return BadRequest($"{nameof(body.Amount)} must be greater than 0");
            if (string.IsNullOrWhiteSpace(body.ChosenOption)) return BadRequest($"{nameof(body.ChosenOption)} must be greater than 0");
            if (string.IsNullOrWhiteSpace(body.ChosenOption)) return BadRequest($"{nameof(body.ChosenOption)} must be greater than 0");
            FixAccountsNumbersForDemoPurposesOnly(body);
            if (string.IsNullOrWhiteSpace(body.AccountNumber)) return BadRequest($"{nameof(body.AccountNumber)} is required.");
            if (body.ChosenOption != "YES" && body.ChosenOption != "NO") return BadRequest("Sorry, your purchase cannot be completed at this time. Please try again or contact the support team.");

            string domain = HttpContext.Request.Host.Host;
            string atAddress = await GetAtAddressAsync(HttpContext, body.PlayerId);
            string isYes = body.ChosenOption == "YES" ? "true" : "false";

            var result = await LinesAPI.Lines.PerformChkThenQryAsync(HttpContext, $@"
                    existsTournament = company.Tournaments.Exists({body.TournamentId});
                    if (existsTournament)
                    {{
                        tournament = company.Tournaments.FindById({body.TournamentId});
                        existsGame = tournament.ExistsGame({body.GameNumber});
                        betBoard = company.Betboard(tournament);
                        existsLine = betBoard.ExistLine({body.LineId});
                        existsDomain = company.Sales.ExistsDomain('{domain}');
                        if (existsGame && existsDomain && existsLine)
                        {{
                            domain = company.Sales.DomainFrom('{domain}');
                            store = company.Sales.CurrentStore;
                            isEnabled = store.IsEnabledOn(domain);
                            game = tournament.GetGameNumber({ body.GameNumber});
                            isEnabledSport = store.IsEnabled(game.Sport, domain);
                            line = betBoard.GetLine({ body.LineId});
                            isTheSameGame = line.Game == game;
                            isEnabledLine = line.IsEnabled(domain);
                            scheduledDay = game.ScheduledDate.Fecha();
                            matchDay = betBoard.Matchday(scheduledDay);
                            showcase = matchDay.GetShowcase(game);
                            isEnabledMatch = showcase.IsEnabled(domain);
                            Check(isEnabled && isEnabledSport && isEnabledLine && isEnabledMatch && line.IsPending() && line.IsPublished && line.IsLastVersion() && line.RemainingAvailabilityTime(now) != 0 && isTheSameGame) Error 'Sorry, your purchase cannot be completed at this time. Please try again or contact the support team.';
                        }}
                        Check(existsGame && existsDomain && existsLine) Error 'Sorry, your purchase cannot be completed at this time. Please try again or contact the support team.';
                    }}
                    Check(existsTournament) Error 'Sorry, your purchase cannot be completed at this time. Please try again or contact the support team.';
                ",
               $@"
				{{
                    tournament = company.Tournaments.FindById({body.TournamentId});
                    betBoard = company.Betboard(tournament);
                    game = tournament.GetGameNumber({ body.GameNumber});
                    scheduledDay = game.ScheduledDate.Fecha();
                    line = betBoard.GetLine({ body.LineId});
                    answer = line.GetAnswer({isYes});

                    print company.Sales.CurrentStore.Id storeId;
                    print game.TeamA.Name TeamAName;
                    print game.TeamB.Name TeamBName;
                    print scheduledDay scheduledDay;
                    print company.IdentitytBetNumber referenceNumber;
                    print line.ToWinIfPlayerWould({body.Amount}, answer) toWin;
                    print answer.Text + ' ' + line.RewardAsString(answer) answerText;
				}}
			");
            OkObjectResult o = (OkObjectResult)result;
            string json = o.Value.ToString();
            ContextInformation context = JsonConvert.DeserializeObject<ContextInformation>(json);

            string concept = $"Ladybet Game {context.TeamAName} vs {context.TeamBName} {context.ScheduledDay} Line {body.LineId} {context.AnswerText} Total {body.CurrencyCode}{body.Amount} for {body.CurrencyCode}{context.ToWin} prize";
            string referenceNumber = context.ReferenceNumber;
            var useless = DateTime.Now.AddDays(DaysToBecomeUseless);
            AuthorizationTransaction resultTransaction;
            PaymentProcessor paymentProcessor = WholePaymentProcessor.Instance().SearchSaleProcessorBy(typeof(Authorization));
            using (RecordSet recordSet = paymentProcessor.GetRecordSet())
            {
                recordSet.SetParameter("context", HttpContext);
                recordSet.SetParameter("atAddress", atAddress);
                recordSet.SetParameter("purchaseTotal.Value", body.Amount);
                recordSet.SetParameter("purchaseTotal.CurrencyCode", body.CurrencyCode);
                recordSet.SetParameter("storeId", context.StoreId);
                recordSet.SetParameter("concept", concept);
                recordSet.SetParameter("referenceNumber", referenceNumber);
                recordSet.SetParameter("accountNumber", body.AccountNumber);
                recordSet.SetParameter("fragmentInformation", new FragmentInformation()
                {
                    ItsConfigured = true,
                    Towin = context.ToWin
                });
                resultTransaction = await paymentProcessor.ExecuteAsync<AuthorizationTransaction>(DateTime.Now, recordSet);
            }
            /*int authorization = await Settings.AuthorizatorsAsync.PurchaseAsync(
                    HttpContext,
                    atAddress,
                    CryptCurrency.Factory(body.CurrencyCode, body.Amount),
                    context.StoreId,
                    concept,
                    referenceNumber,
                    body.AccountNumber,
                    useless,
                    new FragmentInformation()
                    {
                        ItsConfigured = true,
                        Towin = context.ToWin
                    });*/

            var commonScriptForPurchase = GetCommonScriptForPurchase(body, domain, resultTransaction.AuthorizationId);
            var scriptOrderAddingLine = $"order.AddYesNoLine(betNumber, line, {isYes}, product, {body.Amount});";

            result = await LinesAPI.Lines.PerformCmdAsync(HttpContext, 
                $@"
				{{
                    {commonScriptForPurchase}
                    {scriptOrderAddingLine}
                    company.PurchaseOrder(itIsThePresent, order, domain, now);
				}}
			");
            return result;
        }

        [HttpPost("api/lines/wager/overunderline")]
        [Authorize(Roles = "devops")]//VERIFY_ROLES
        public async Task<IActionResult> CreateOverUnderLineWagerAsync([FromBody] WagerBody body)
        {
            if (body == null) return BadRequest("Body is required");
            if (body.TournamentId <= 0) return BadRequest($"{nameof(body.TournamentId)} must be greater than 0");
            if (body.GameNumber <= 0) return BadRequest($"{nameof(body.GameNumber)} must be greater than 0");
            if (body.LineId <= 0) return BadRequest($"{nameof(body.LineId)} must be greater than 0");
            if (body.Amount <= 0) return BadRequest($"{nameof(body.Amount)} must be greater than 0");
            if (string.IsNullOrWhiteSpace(body.ChosenOption)) return BadRequest($"{nameof(body.ChosenOption)} must be greater than 0");
            if (string.IsNullOrWhiteSpace(body.ChosenOption)) return BadRequest($"{nameof(body.ChosenOption)} must be greater than 0");
            FixAccountsNumbersForDemoPurposesOnly(body);
            if (string.IsNullOrWhiteSpace(body.AccountNumber)) return BadRequest($"{nameof(body.AccountNumber)} is required.");
            if (body.ChosenOption != "OVER" && body.ChosenOption != "UNDER") return BadRequest("Sorry, your purchase cannot be completed at this time. Please try again or contact the support team.");

            string atAddress = await GetAtAddressAsync(HttpContext, body.PlayerId);
            string domain = HttpContext.Request.Host.Host;
            string isOver = body.ChosenOption == "OVER" ? "true" : "false";

            var result = await LinesAPI.Lines.PerformChkThenQryAsync(HttpContext, $@"
                    existsTournament = company.Tournaments.Exists({body.TournamentId});
                    if (existsTournament)
                    {{
                        tournament = company.Tournaments.FindById({body.TournamentId});
                        existsGame = tournament.ExistsGame({body.GameNumber});
                        betBoard = company.Betboard(tournament);
                        existsLine = betBoard.ExistLine({body.LineId});
                        existsDomain = company.Sales.ExistsDomain('{domain}');
                        if (existsGame && existsDomain && existsLine)
                        {{
                            domain = company.Sales.DomainFrom('{domain}');
                            store = company.Sales.CurrentStore;
                            isEnabled = store.IsEnabledOn(domain);
                            game = tournament.GetGameNumber({ body.GameNumber});
                            isEnabledSport = store.IsEnabled(game.Sport, domain);
                            line = betBoard.GetLine({ body.LineId});
                            isTheSameGame = line.Game == game;
                            isEnabledLine = line.IsEnabled(domain);
                            scheduledDay = game.ScheduledDate.Fecha();
                            matchDay = betBoard.Matchday(scheduledDay);
                            showcase = matchDay.GetShowcase(game);
                            isEnabledMatch = showcase.IsEnabled(domain);
                            Check(isEnabled && isEnabledSport && isEnabledLine && isEnabledMatch && line.IsPending() && line.IsPublished && line.IsLastVersion() && line.RemainingAvailabilityTime(now) != 0 && isTheSameGame) Error 'Sorry, your purchase cannot be completed at this time. Please try again or contact the support team.';
                        }}
                        Check(existsGame && existsDomain && existsLine) Error 'Sorry, your purchase cannot be completed at this time. Please try again or contact the support team.';
                    }}
                    Check(existsTournament) Error 'Sorry, your purchase cannot be completed at this time. Please try again or contact the support team.';
                ",
                $@"
				{{
                    tournament = company.Tournaments.FindById({body.TournamentId});
                    betBoard = company.Betboard(tournament);
                    game = tournament.GetGameNumber({ body.GameNumber});
                    scheduledDay = game.ScheduledDate.Fecha();
                    line = betBoard.GetLine({ body.LineId});
                    answer = line.GetAnswer({isOver});

                    print company.Sales.CurrentStore.Id storeId;
                    print game.TeamA.Name TeamAName;
                    print game.TeamB.Name TeamBName;
                    print scheduledDay scheduledDay;
                    print company.IdentitytBetNumber referenceNumber;
                    print line.ToWinIfPlayerWould({body.Amount}, answer) toWin;
                    print answer.Text + ' ' + line.RewardAsString(answer) answerText;
				}}
			");
            OkObjectResult o = (OkObjectResult)result;
            string json = o.Value.ToString();
            ContextInformation context = JsonConvert.DeserializeObject<ContextInformation>(json);

            string concept = $"Ladybet Game {context.TeamAName} vs {context.TeamBName} {context.ScheduledDay} Line {body.LineId} {context.AnswerText} Total {body.CurrencyCode}{body.Amount} for {body.CurrencyCode}{context.ToWin} prize";
            string referenceNumber = context.ReferenceNumber;
            var useless = DateTime.Now.AddDays(DaysToBecomeUseless);
            AuthorizationTransaction resultTransaction;
            PaymentProcessor paymentProcessor = WholePaymentProcessor.Instance().SearchSaleProcessorBy(typeof(Authorization));
            using (RecordSet recordSet = paymentProcessor.GetRecordSet())
            {
                recordSet.SetParameter("context", HttpContext);
                recordSet.SetParameter("atAddress", atAddress);
                recordSet.SetParameter("purchaseTotal.Value", body.Amount);
                recordSet.SetParameter("purchaseTotal.CurrencyCode", body.CurrencyCode);
                recordSet.SetParameter("storeId", context.StoreId);
                recordSet.SetParameter("concept", concept);
                recordSet.SetParameter("referenceNumber", referenceNumber);
                recordSet.SetParameter("accountNumber", body.AccountNumber);
                recordSet.SetParameter("fragmentInformation", new FragmentInformation()
                {
                    ItsConfigured = true,
                    Towin = context.ToWin
                });
                resultTransaction = await paymentProcessor.ExecuteAsync<AuthorizationTransaction>(DateTime.Now, recordSet);
            }
            /*int authorization = await Settings.AuthorizatorsAsync.PurchaseAsync(
                    HttpContext,
                    atAddress,
                    CryptCurrency.Factory(body.CurrencyCode, body.Amount),
                    context.StoreId,
                    concept,
                    referenceNumber,
                    body.AccountNumber,
                    useless,
                    new FragmentInformation()
                    {
                        ItsConfigured = true,
                        Towin = context.ToWin
                    });*/

            var commonScriptForPurchase = GetCommonScriptForPurchase(body, domain, resultTransaction.AuthorizationId);
            var scriptOrderAddingLine = $"order.AddOverUnderLine(betNumber, line, {isOver}, product, {body.Amount});";

            result = await LinesAPI.Lines.PerformCmdAsync(HttpContext,
                $@"
				{{
                    {commonScriptForPurchase}
                    {scriptOrderAddingLine}
                    company.PurchaseOrder(itIsThePresent, order, domain, now);
				}}
			");
            return result;
        }

        [HttpPost("api/lines/wager/totalpointsline")]
        [Authorize(Roles = "devops")]//VERIFY_ROLES
        public async Task<IActionResult> CreateTotalPointsLineWagerAsync([FromBody] WagerBody body)
        {
            if (body == null) return BadRequest("Body is required");
            if (body.TournamentId <= 0) return BadRequest($"{nameof(body.TournamentId)} must be greater than 0");
            if (body.GameNumber <= 0) return BadRequest($"{nameof(body.GameNumber)} must be greater than 0");
            if (body.LineId <= 0) return BadRequest($"{nameof(body.LineId)} must be greater than 0");
            if (body.Amount <= 0) return BadRequest($"{nameof(body.Amount)} must be greater than 0");
            if (string.IsNullOrWhiteSpace(body.ChosenOption)) return BadRequest($"{nameof(body.ChosenOption)} must be greater than 0");
            if (string.IsNullOrWhiteSpace(body.ChosenOption)) return BadRequest($"{nameof(body.ChosenOption)} must be greater than 0");
            FixAccountsNumbersForDemoPurposesOnly(body);
            if (string.IsNullOrWhiteSpace(body.AccountNumber)) return BadRequest($"{nameof(body.AccountNumber)} is required.");
            if (body.ChosenOption != "OVER" && body.ChosenOption != "UNDER") return BadRequest("Sorry, your purchase cannot be completed at this time. Please try again or contact the support team.");

            string atAddress = await GetAtAddressAsync(HttpContext, body.PlayerId);
            string domain = HttpContext.Request.Host.Host;
            string isOver = body.ChosenOption == "OVER" ? "true" : "false";

            var result = await LinesAPI.Lines.PerformChkThenQryAsync(HttpContext, $@"
                    existsTournament = company.Tournaments.Exists({body.TournamentId});
                    if (existsTournament)
                    {{
                        tournament = company.Tournaments.FindById({body.TournamentId});
                        existsGame = tournament.ExistsGame({body.GameNumber});
                        betBoard = company.Betboard(tournament);
                        existsLine = betBoard.ExistLine({body.LineId});
                        existsDomain = company.Sales.ExistsDomain('{domain}');
                        if (existsGame && existsDomain && existsLine)
                        {{
                            domain = company.Sales.DomainFrom('{domain}');
                            store = company.Sales.CurrentStore;
                            isEnabled = store.IsEnabledOn(domain);
                            game = tournament.GetGameNumber({ body.GameNumber});
                            isEnabledSport = store.IsEnabled(game.Sport, domain);
                            line = betBoard.GetLine({ body.LineId});
                            isTheSameGame = line.Game == game;
                            isEnabledLine = line.IsEnabled(domain);
                            scheduledDay = game.ScheduledDate.Fecha();
                            matchDay = betBoard.Matchday(scheduledDay);
                            showcase = matchDay.GetShowcase(game);
                            isEnabledMatch = showcase.IsEnabled(domain);
                            Check(isEnabled && isEnabledSport && isEnabledLine && isEnabledMatch && line.IsPending() && line.IsPublished && line.IsLastVersion() && line.RemainingAvailabilityTime(now) != 0 && isTheSameGame) Error 'Sorry, your purchase cannot be completed at this time. Please try again or contact the support team.';
                        }}
                        Check(existsGame && existsDomain && existsLine) Error 'Sorry, your purchase cannot be completed at this time. Please try again or contact the support team.';
                    }}
                    Check(existsTournament) Error 'Sorry, your purchase cannot be completed at this time. Please try again or contact the support team.';
                ",
                $@"
				{{
                    tournament = company.Tournaments.FindById({body.TournamentId});
                    betBoard = company.Betboard(tournament);
                    game = tournament.GetGameNumber({ body.GameNumber});
                    scheduledDay = game.ScheduledDate.Fecha();
                    line = betBoard.GetLine({ body.LineId});
                    answer = line.GetAnswer({isOver});

                    print company.Sales.CurrentStore.Id storeId;
                    print game.TeamA.Name TeamAName;
                    print game.TeamB.Name TeamBName;
                    print scheduledDay scheduledDay;
                    print company.IdentitytBetNumber referenceNumber;
                    print line.ToWinIfPlayerWould({body.Amount}, answer) toWin;
                    print answer.Text + ' ' + line.RewardAsString(answer) answerText;
				}}
			");
            OkObjectResult o = (OkObjectResult)result;
            string json = o.Value.ToString();
            ContextInformation context = JsonConvert.DeserializeObject<ContextInformation>(json);

            string concept = $"Ladybet Game {context.TeamAName} vs {context.TeamBName} {context.ScheduledDay} Line {body.LineId} {context.AnswerText} Total {body.CurrencyCode}{body.Amount} for {body.CurrencyCode}{context.ToWin} prize";
            string referenceNumber = context.ReferenceNumber;

            var useless = DateTime.Now.AddDays(DaysToBecomeUseless);
            AuthorizationTransaction resultTransaction;
            PaymentProcessor paymentProcessor = WholePaymentProcessor.Instance().SearchSaleProcessorBy(typeof(Authorization));
            using (RecordSet recordSet = paymentProcessor.GetRecordSet())
            {
                recordSet.SetParameter("context", HttpContext);
                recordSet.SetParameter("atAddress", atAddress);
                recordSet.SetParameter("purchaseTotal.Value", body.Amount);
                recordSet.SetParameter("purchaseTotal.CurrencyCode", body.CurrencyCode);
                recordSet.SetParameter("storeId", context.StoreId);
                recordSet.SetParameter("concept", concept);
                recordSet.SetParameter("referenceNumber", referenceNumber);
                recordSet.SetParameter("accountNumber", body.AccountNumber);
                recordSet.SetParameter("fragmentInformation", new FragmentInformation()
                {
                    ItsConfigured = true,
                    Towin = context.ToWin
                });
                resultTransaction = await paymentProcessor.ExecuteAsync<AuthorizationTransaction>(DateTime.Now, recordSet);
            }
            /*int authorization = await Settings.AuthorizatorsAsync.PurchaseAsync(
                    HttpContext,
                    atAddress,
                    CryptCurrency.Factory(body.CurrencyCode, body.Amount),
                    context.StoreId,
                    concept,
                    referenceNumber,
                    body.AccountNumber,
                    useless,
                    new FragmentInformation()
                    {
                        ItsConfigured = true,
                        Towin = context.ToWin
                    });*/

            var commonScriptForPurchase = GetCommonScriptForPurchase(body, domain, resultTransaction.AuthorizationId);
            var scriptOrderAddingLine = $"order.AddTotalPointsLine(betNumber, line, {isOver}, product, {body.Amount});";

            result = await LinesAPI.Lines.PerformCmdAsync(HttpContext,
                $@"
				{{
                    {commonScriptForPurchase}
                    {scriptOrderAddingLine}
                    company.PurchaseOrder(itIsThePresent, order, domain, now);
				}}
			");
            return result;
        }

        [HttpPost("api/lines/wager/fixedline")]
        [Authorize(Roles = "devops")]//VERIFY_ROLES
        public async Task<IActionResult> CreateFixedLineWagerAsync([FromBody] WagerBody body)
        {
            if (body == null) return BadRequest("Body is required");
            if (body.TournamentId <= 0) return BadRequest($"{nameof(body.TournamentId)} must be greater than 0");
            if (body.GameNumber <= 0) return BadRequest($"{nameof(body.GameNumber)} must be greater than 0");
            if (body.LineId <= 0) return BadRequest($"{nameof(body.LineId)} must be greater than 0");
            if (body.Amount <= 0) return BadRequest($"{nameof(body.Amount)} must be greater than 0");
            if (string.IsNullOrWhiteSpace(body.ChosenOption)) return BadRequest($"{nameof(body.ChosenOption)} must be greater than 0");
            if (string.IsNullOrWhiteSpace(body.ChosenOption)) return BadRequest($"{nameof(body.ChosenOption)} must be greater than 0");
            FixAccountsNumbersForDemoPurposesOnly(body);
            if (string.IsNullOrWhiteSpace(body.AccountNumber)) return BadRequest($"{nameof(body.AccountNumber)} is required.");

            string atAddress = await GetAtAddressAsync(HttpContext, body.PlayerId);
            string domain = HttpContext.Request.Host.Host;

            var result = await LinesAPI.Lines.PerformChkThenQryAsync(HttpContext, $@"
                    existsTournament = company.Tournaments.Exists({body.TournamentId});
                    if (existsTournament)
                    {{
                        tournament = company.Tournaments.FindById({body.TournamentId});
                        existsGame = tournament.ExistsGame({body.GameNumber});
                        betBoard = company.Betboard(tournament);
                        existsLine = betBoard.ExistLine({body.LineId});
                        existsDomain = company.Sales.ExistsDomain('{domain}');
                        if (existsGame && existsDomain && existsLine)
                        {{
                            domain = company.Sales.DomainFrom('{domain}');
                            store = company.Sales.CurrentStore;
                            isEnabled = store.IsEnabledOn(domain);
                            game = tournament.GetGameNumber({ body.GameNumber});
                            isEnabledSport = store.IsEnabled(game.Sport, domain);
                            line = betBoard.GetLine({ body.LineId});
                            isTheSameGame = line.Game == game;
                            isEnabledLine = line.IsEnabled(domain);
                            scheduledDay = game.ScheduledDate.Fecha();
                            matchDay = betBoard.Matchday(scheduledDay);
                            showcase = matchDay.GetShowcase(game);
                            isEnabledMatch = showcase.IsEnabled(domain);
                            Check(isEnabled && isEnabledSport && isEnabledLine && isEnabledMatch && line.IsPending() && line.IsPublished && line.IsLastVersion() && line.RemainingAvailabilityTime(now) != 0 && isTheSameGame) Error 'Sorry, your purchase cannot be completed at this time. Please try again or contact the support team.';
                        }}
                        if (existsLine)
                        {{
                            line = betBoard.GetLine({body.LineId});
                            existsOption = line.ExistsOption('{body.ChosenOption}');
                            Check(existsOption) Error 'Sorry, your purchase cannot be completed at this time. Please try again or contact the support team.';
                        }}
                        Check(existsGame && existsDomain && existsLine) Error 'Sorry, your purchase cannot be completed at this time. Please try again or contact the support team.';
                    }}
                    Check(existsTournament) Error 'Sorry, your purchase cannot be completed at this time. Please try again or contact the support team.';
                ",
                $@"
				{{
                    tournament = company.Tournaments.FindById({body.TournamentId});
                    betBoard = company.Betboard(tournament);
                    game = tournament.GetGameNumber({ body.GameNumber});
                    scheduledDay = game.ScheduledDate.Fecha();
                    line = betBoard.GetLine({ body.LineId});
                    answer = line.GetAnAnswer('{body.ChosenOption}');

                    print company.Sales.CurrentStore.Id storeId;
                    print game.TeamA.Name TeamAName;
                    print game.TeamB.Name TeamBName;
                    print scheduledDay scheduledDay;
                    print company.IdentitytBetNumber referenceNumber;
                    print line.ToWinIfPlayerWould({body.Amount}, answer) toWin;
                    print answer.Text + ' ' + line.RewardAsString(answer) answerText;
				}}
			");
            OkObjectResult o = (OkObjectResult)result;
            string json = o.Value.ToString();
            ContextInformation context = JsonConvert.DeserializeObject<ContextInformation>(json);

            string concept = $"Ladybet Game {context.TeamAName} vs {context.TeamBName} {context.ScheduledDay} Line {body.LineId} {context.AnswerText} Total {body.CurrencyCode}{body.Amount} for {body.CurrencyCode}{context.ToWin} prize";
            string referenceNumber = context.ReferenceNumber;
            var useless = DateTime.Now.AddDays(DaysToBecomeUseless);
            AuthorizationTransaction resultTransaction;
            PaymentProcessor paymentProcessor = WholePaymentProcessor.Instance().SearchSaleProcessorBy(typeof(Authorization));
            using (RecordSet recordSet = paymentProcessor.GetRecordSet())
            {
                recordSet.SetParameter("context", HttpContext);
                recordSet.SetParameter("atAddress", atAddress);
                recordSet.SetParameter("purchaseTotal.Value", body.Amount);
                recordSet.SetParameter("purchaseTotal.CurrencyCode", body.CurrencyCode);
                recordSet.SetParameter("storeId", context.StoreId);
                recordSet.SetParameter("concept", concept);
                recordSet.SetParameter("referenceNumber", referenceNumber);
                recordSet.SetParameter("accountNumber", body.AccountNumber);
                recordSet.SetParameter("fragmentInformation", new FragmentInformation()
                {
                    ItsConfigured = true,
                    Towin = context.ToWin
                });
                resultTransaction = await paymentProcessor.ExecuteAsync<AuthorizationTransaction>(DateTime.Now, recordSet);
            }
            /*int authorization = await Settings.AuthorizatorsAsync.PurchaseAsync(
                    HttpContext,
                    atAddress,
                    CryptCurrency.Factory(body.CurrencyCode, body.Amount),
                    context.StoreId,
                    concept,
                    referenceNumber,
                    body.AccountNumber,
                    useless,
                    new FragmentInformation()
                    {
                        ItsConfigured = true,
                        Towin = context.ToWin
                    });*/

            var commonScriptForPurchase = GetCommonScriptForPurchase(body, domain, resultTransaction.AuthorizationId);

            result = await LinesAPI.Lines.PerformCmdAsync(HttpContext, 
                $@"
				{{
                    {commonScriptForPurchase}
                    order.AddFixedLine(betNumber, line, '{body.ChosenOption}', product, {body.Amount});
                    company.PurchaseOrder(itIsThePresent, order, domain, now);
				}}
			");
            return result;
        }

        
        [HttpPost("api/lines/wager/grade")]
        [Authorize(Roles = "devops")]//VERIFY_ROLES
        public async Task<IActionResult> GradeLineAsync([FromBody] GradedABLinesBody body)
        {
            if (body == null) return BadRequest("Body is required");
            if(body.Lines.Count == 0) return BadRequest("At least one line is required ");

            string employeeName = Security.UserName(HttpContext);
            List<GradeLineBody> linesGraded = new List<GradeLineBody>();

            foreach( var line in body.Lines)
			{
                if (line.TournamentId <= 0) return BadRequest($"{nameof(line.TournamentId)} must be greater than 0");
                if (line.LineId <= 0) return BadRequest($"{nameof(line.LineId)} must be greater than 0");
                if (line.GameNumber <= 0) return BadRequest($"{nameof(line.GameNumber)} must be greater than 0");

                var resultLine = await LinesAPI.Lines.PerformCmdAsync(HttpContext, $@"
				    {{
                        tournament = company.Tournaments.FindById({line.TournamentId});
                        betBoard = company.Betboard(tournament);
                        line = betBoard.GetLine({line.LineId});
                        showcase = line.Showcase;
                        game = line.Game;
                        showcase.Grade(ItIsThePresent, line, Now, game.TeamA, {line.ScoreA}, game.TeamB, {line.ScoreB}, '{employeeName}');
				    }}
			    ");
                var okResult = resultLine as OkObjectResult;

                line.IsSuccess = okResult == null ? false : true;
                linesGraded.Add(line);
			}
            JObject json = new JObject();

            json["result"] = JToken.FromObject(linesGraded);
            return Ok(json);

        }
        

        [HttpPost("api/lines/wager/abline/grade")]
        [Authorize(Roles = "l29")]
        public async Task<IActionResult> GradeABLineAsync([FromBody] GradedABLineBody body)
        {
            if (body == null) return BadRequest("Body is required");
            if (body.TournamentId <= 0) return BadRequest($"{nameof(body.TournamentId)} must be greater than 0");

            string employeeName = Security.UserName(HttpContext);

            var result = await LinesAPI.Lines.PerformCmdAsync(HttpContext, $@"
				{{
                    tournament = company.Tournaments.FindById({body.TournamentId});
                    betBoard = company.Betboard(tournament);
                    line = betBoard.GetLine({body.LineId});
                    showcase = line.Showcase;
                    game = line.Game;
                    showcase.Grade(ItIsThePresent, line, Now, game.TeamA, {body.ScoreA}, game.TeamB, {body.ScoreB}, '{employeeName}');
				}}
			");
            return result;
        }

        [HttpPost("api/lines/wager/yesnoline/grade")]
        [Authorize(Roles = "l29")]
        public async Task<IActionResult> GradeYesNoLineAsync([FromBody] GradedYesNoLineBody body)
        {
            if (body == null) return BadRequest("Body is required");
            if (body.TournamentId <= 0) return BadRequest($"{nameof(body.TournamentId)} must be greater than 0");

            string employeeName = Security.UserName(HttpContext);

            var result = await LinesAPI.Lines.PerformCmdAsync(HttpContext, $@"
				{{
                    tournament = company.Tournaments.FindById({body.TournamentId});
                    betBoard = company.Betboard(tournament);
                    line = betBoard.GetLine({body.LineId});
                    showcase = line.Showcase;
                    showcase.Grade(ItIsThePresent, line, {body.IsYesAnswer}, Now, '{employeeName}');
				}}
			");
            return result;
        }

        [HttpPost("api/lines/wager/overunderline/grade")]
        [Authorize(Roles = "l29")]
        public async Task<IActionResult> GradeOverUnderLineAsync([FromBody] GradedOverUnderLineBody body)
        {
            if (body == null) return BadRequest("Body is required");
            if (body.TournamentId <= 0) return BadRequest($"{nameof(body.TournamentId)} must be greater than 0");

            string employeeName = Security.UserName(HttpContext);

            var result = await LinesAPI.Lines.PerformCmdAsync(HttpContext, $@"
				{{
                    tournament = company.Tournaments.FindById({body.TournamentId});
                    betBoard = company.Betboard(tournament);
                    line = betBoard.GetLine({body.LineId});
                    showcase = line.Showcase;
                    showcase.Grade(ItIsThePresent, line, {body.ScoreAnswer}, Now, '{employeeName}');
				}}
			");
            return result;
        }

        [HttpPost("api/lines/wager/fixedline/grade")]
        [Authorize(Roles = "l29")]
        public async Task<IActionResult> GradeFixedLineAsync([FromBody] GradedFixedLineBody body)
        {
            if (body == null) return BadRequest("Body is required");
            if (body.TournamentId <= 0) return BadRequest($"{nameof(body.TournamentId)} must be greater than 0");

            string employeeName = Security.UserName(HttpContext);

            var result = await LinesAPI.Lines.PerformCmdAsync(HttpContext, $@"
				{{
                    tournament = company.Tournaments.FindById({body.TournamentId});
                    betBoard = company.Betboard(tournament);
                    line = betBoard.GetLine({body.LineId});
                    showcase = line.Showcase;
                    showcase.Grade(ItIsThePresent, line, '{body.OptionAnswer}', Now, '{employeeName}');
				}}
			");
            return result;
        }

        [HttpPost("api/lines/wager/noaction/line")]
        [Authorize(Roles = "l24")]
        public async Task<IActionResult> NoActionAsync([FromBody] LineNoActionBody body)
        {
            if (body == null) return BadRequest("Body is required");
            if (body.GameNumber <= 0) return BadRequest($"{nameof(body.GameNumber)} must be greater than 0");
            if (body.LineId <= 0) return BadRequest($"{nameof(body.LineId)} must be greater than 0");
            if (body.TournamentId <= 0) return BadRequest($"{nameof(body.TournamentId)} must be greater than 0");

            string employeeName = Security.UserName(HttpContext);

            var result = await LinesAPI.Lines.PerformCmdAsync(HttpContext, $@"
				{{
                    tournament = company.Tournaments.FindById({body.TournamentId});
                    betBoard = company.Betboard(tournament);
                    line = betBoard.GetLine({body.LineId});
                    showcase = line.Showcase;
                    showcase.SetNoAction(ItIsThePresent, line, Now, '{employeeName}');
				}}
			");
            return result;
        }

        [HttpPost("api/lines/wager/regrade/line")]
        [Authorize(Roles = "l33")]
        public async Task<IActionResult> RegradeAsync([FromBody] LineRegradeBody body)
        {
            if (body == null) return BadRequest("Body is required");
            if (body.GameNumber <= 0) return BadRequest($"{nameof(body.GameNumber)} must be greater than 0");
            if (body.LineId <= 0) return BadRequest($"{nameof(body.LineId)} must be greater than 0");
            if (body.TournamentId <= 0) return BadRequest($"{nameof(body.TournamentId)} must be greater than 0");

            string employeeName = Security.UserName(HttpContext);

            var result = await LinesAPI.Lines.PerformCmdAsync(HttpContext, $@"
				{{
                    tournament = company.Tournaments.FindById({body.TournamentId});
                    betBoard = company.Betboard(tournament);
                    line = betBoard.GetLine({body.LineId});
                    game = tournament.GetGameNumber({body.GameNumber});
                    scheduledDate = game.ScheduledDate.Fecha();
                    matchDay = betBoard.Matchday(scheduledDate);
                    showcase = matchDay.GetShowcase(game);
                    showcase.Regrade(ItIsThePresent, line, Now, '{employeeName}');
				}}
			");
            return result;
        }

        [HttpGet("api/lines/inprogress")]
        [Authorize(Roles = "devops")]//VERIFY_ROLES
        public async Task<IActionResult> LinesInProgressAsync(string sportName, string status, string shortcut)
        {
            if (string.IsNullOrWhiteSpace(sportName)) return BadRequest($"Parameter {nameof(sportName)} cannot be empty or null.");
            if (string.IsNullOrWhiteSpace(status)) return BadRequest($"Parameter {nameof(status)} cannot be empty or null.");
            if (string.IsNullOrWhiteSpace(shortcut)) return BadRequest($"Parameter {nameof(shortcut)} cannot be empty or null.");

            Visibility visibility = Visibility.DRAFT;
            var normalizedStatus = status.ToLower();
            if (status != "all")
            {
                if (! Enum.TryParse(status, out visibility)) return BadRequest($"Parameter {nameof(status)} is not valid.");
            }
            var normalizedSportName = sportName.ToLower();
            var shortcutEscaped = Validator.StringEscape(shortcut);

            var result = await LinesAPI.Lines.PerformQryAsync(HttpContext, $@"
                {{
                    sportTournaments = tournaments.List();
                    if ({normalizedSportName} != 'all')
                    {{
                        sport = tournaments.Sports.FindBy({normalizedSportName});
                        sportTournaments = tournaments.FindTournamentsBy(sport);
                    }}
                    
                    for(tournamentsList : sportTournaments)
                    {{
                        betBoard = company.Betboard(tournamentsList);
                        matchDay = betBoard.Matchday(Now.Date);
                        catalog = betBoard.Catalog;
                        
                        filteredLines = matchDay.AllLines();
                        if ({normalizedStatus} != 'all')
                        {{
                            filteredLines = matchDay.LinesBasedOn({visibility});
                        }}
                        else if ({shortcutEscaped} != 'all')
                        {{
                            questionsList = catalog.QuestionsMatchingWith({shortcutEscaped});
                            filteredLines = matchDay.LinesBasedOn(questionsList);
                        }}

                        for(lines : filteredLines)
                        {{
                            print lines.LineId id;
                            print lines.LineTypeAsString type;
                            print lines.Text text;
                            print lines.Visibility.ToString() visibility;
                            print lines.Tier.Name tier;
                            print lines.BasedOn.Shortcut shortcut;

                            print tournament.Id tournamentId;
                            print catalog.Sport.Id sportId;
                            print catalog.Sport.Name sportName;

                            showcase = lines.Showcase;
                            linesCounter = showcase.CountLines();
				            print linesCounter.CountLines countLines;
				            print linesCounter.CountGradedLines countGradedLines;
				            print linesCounter.CountPublishedLines countPublishedLines;
				            print linesCounter.CountDraftedLines countDraftedLines;
				            print linesCounter.CountSuspendedLines countSuspendedLines;
				            print showcase.CountBackgrounds countBackgrounds;
				            print showcase.CountSideBySide countSideBySide;
                            riskAssestment = showcase.RiskAssestment;

                            game = lines.Game;
                            print game.Number gameNumber;
                            print game.TeamA.Name teamAName;
                            print game.TeamB.Name teamBName;
                            print game.ScheduledDate scheduledDate;
                            if(lines.LineTypeAsString == 'SPREAD_LINE')
                            {{
                                spreadLine = lines;
                                print spreadLine.TeamAReward teamAReward;
                                print spreadLine.TeamBReward teamBReward;
                                print spreadLine.Spread spread;
                                print riskAssestment.PlacedFor(lines, spreadLine.GetTeamAnswer(game.TeamA)) placeForTeamA;
                                print riskAssestment.PlacedFor(lines, spreadLine.GetTeamAnswer(game.TeamB)) placeForTeamB;
                                print riskAssestment.ToPayFor(lines, spreadLine.GetTeamAnswer(game.TeamA)) toPayForTeamA;
                                print riskAssestment.ToPayFor(lines, spreadLine.GetTeamAnswer(game.TeamB)) toPayForTeamB;
                                print riskAssestment.CountForTeamA(lines) countTeamA;
                                print riskAssestment.CountForTeamB(lines) countTeamB;
                            }}
                            else if(line.LineTypeAsString == 'MONEY_LINE')
                            {{
                                moneyLine = lines;
                                print moneyLine.TeamAReward teamAReward;
                                print moneyLine.TeamBReward teamBReward;
                                print riskAssestment.PlacedFor(lines, moneyLine.GetTeamAnswer(game.TeamA)) placeForTeamA;
                                print riskAssestment.PlacedFor(lines, moneyLine.GetTeamAnswer(game.TeamB)) placeForTeamB;
                                print riskAssestment.ToPayFor(lines, moneyLine.GetTeamAnswer(game.TeamA)) toPayForTeamA;
                                print riskAssestment.ToPayFor(lines, moneyLine.GetTeamAnswer(game.TeamB)) toPayForTeamB;
                                print riskAssestment.CountForTeamA(lines) countTeamA;
                                print riskAssestment.CountForTeamB(lines) countTeamB;
                            }}
                            else if(line.LineTypeAsString == 'MONEYDRAW_LINE')
                            {{
                                moneyDrawLine = lines;
                                print moneyDrawLine.TeamAReward teamAReward;
                                print moneyDrawLine.TieReward tieReward;
                                print moneyDrawLine.TeamBReward teamBReward;
                                print riskAssestment.PlacedFor(lines, moneyDrawLine.GetTeamAnswer(game.TeamA)) placeForTeamA;
                                print riskAssestment.PlacedFor(lines, moneyDrawLine.GetTeamAnswer(game.TeamB)) placeForTeamB;
                                print riskAssestment.PlacedFor(lines, moneyDrawLine.GetTieAnswer(game.TeamA, game.TeamB)) placeForDraw;
                                print riskAssestment.ToPayFor(lines, moneyDrawLine.GetTeamAnswer(game.TeamA)) toPayForTeamA;
                                print riskAssestment.ToPayFor(lines, moneyDrawLine.GetTeamAnswer(game.TeamB)) toPayForTeamB;
                                print riskAssestment.ToPayFor(lines, moneyDrawLine.GetTieAnswer(game.TeamA, game.TeamB)) toPayForDraw;
                                print riskAssestment.CountForTeamA(lines) countTeamA;
                                print riskAssestment.CountForTeamB(lines) countTeamB;
                                print riskAssestment.CountForDraw(lines) countDraw;
                            }}
                            else if(line.LineTypeAsString == 'YES_NO_LINE')
                            {{
                                yesNoLine = lines;
                                print yesNoLine.YesReward yesReward;
                                print yesNoLine.NoReward noReward;
                            
                                yesAnswer = {YesNoAnswer.YES};
                                noAnswer = {YesNoAnswer.NO};
                                print riskAssestment.PlacedFor(lines, yesAnswer) placeForYes;
                                print riskAssestment.PlacedFor(lines, noAnswer) placeForNo;
                                print riskAssestment.ToPayFor(lines, yesAnswer) toPayForYes;
                                print riskAssestment.ToPayFor(lines, noAnswer) toPayForNo;
                                print riskAssestment.CountForYes(lines) countYes;
                                print riskAssestment.CountForNo(lines) countNo;
                            
                            }}
                            else if(line.LineTypeAsString == 'OVER_UNDER_LINE')
                            {{
                                overUnderLine = lines;
                                print overUnderLine.OverReward overReward;
                                print overUnderLine.UnderReward underReward;
                                print overUnderLine.Score score;

                                overAnswer = {OverUnderAnswer.OVER};
                                underAnswer = {OverUnderAnswer.UNDER};
                                print riskAssestment.PlacedFor(lines, overAnswer) placeForOver;
                                print riskAssestment.PlacedFor(lines, underAnswer) placeForUnder;
                                print riskAssestment.ToPayFor(lines, overAnswer) toPayForOver;
                                print riskAssestment.ToPayFor(lines, underAnswer) toPayForUnder;
                                print riskAssestment.CountForOver(lines) countOver;
                                print riskAssestment.CountForUnder(lines) countUnder;
                            
                            }}
                            else if(line.LineTypeAsString == 'FIXED_LINE')
                            {{
                                fixedLine = lines;
                                options = fixedLine.Options;
                                for(option : options)
                                {{
                                    print option option;
                                    print fixedLine.GetReward(option) reward;
                                
                                    answer = fixedLine.GetAnAnswer(option);
                                    print riskAssestment.PlacedFor(lines, answer) placeForOption;
                                    print riskAssestment.ToPayFor(lines, answer) toPayForOption;
                                    print riskAssestment.CountForFixed(lines, option) countForOption;
                                }}

                            }}
                            else if(line.LineTypeAsString == 'TOTAL_POINTS_LINE')
                            {{
                                totalPointsLine = lines;
                                print totalPointsLine.OverReward overReward;
                                print totalPointsLine.UnderReward underReward;
                                print totalPointsLine.Score score;

                                overTotalAnswer = {OverUnderAnswer.OVER};
                                underTotalAnswer = {OverUnderAnswer.UNDER};
                                print riskAssestment.PlacedFor(lines, overTotalAnswer) placeForOver;
                                print riskAssestment.PlacedFor(lines, underTotalAnswer) placeForUnder;
                                print riskAssestment.ToPayFor(lines, overTotalAnswer) toPayForOver;
                                print riskAssestment.ToPayFor(lines, underTotalAnswer) toPayForUnder;
                                print riskAssestment.CountForOver(lines) countOver;
                                print riskAssestment.CountForUnder(lines) countUnder;
                            }}
                        }}
                    }}
                }}
			");
            return result;
        }

        [HttpGet("api/tournament/{tournamentId}/linesOffer/game/{gameNumber}")]
        [Authorize(Roles = "l04")]
        public async Task<IActionResult> GetLinesOfferAsync(int tournamentId, int gameNumber, string status)
        {
            if (tournamentId <= 0) return BadRequest($"Parameter {nameof(tournamentId)} must be greater than 0");
            if (gameNumber <= 0) return BadRequest($"Parameter {nameof(gameNumber)} must be greater than 0");
            if (string.IsNullOrWhiteSpace(status)) return BadRequest($"Parameter {nameof(status)} cannot be empty or null.");

            var result = await LinesAPI.Lines.PerformQryAsync(HttpContext, $@"
                {{
                    filterBy = '{status}';
                    tournament = company.Tournaments.FindById({tournamentId});
                    
                    game = tournament.GetGameNumber({gameNumber});
                    betBoard = company.Betboard(tournament);
                    scheduledDay = game.ScheduledDate.Fecha();
                    matchDay = betBoard.Matchday(scheduledDay);
                    showcase = matchDay.GetShowcase(game);
                    riskAssestment = showcase.RiskAssestment;
                    print showcase.LastPosition() maxPosition;
                    print game.TeamA.Logo teamALogo;
                    print game.TeamB.Logo teamBLogo;

                    catalog = betBoard.Catalog;
                    print catalog.Sport.Id sportId;

                    for ( upcomingPeriod : game.UpcomingPeriod())
                    {{
                        print upcomingPeriod.Name name;
                    }}

                    filteredLines = showcase.AllLinesOffered();
                    if(filterBy == 'DRAFT')
                    {{
                        filteredLines = showcase.DraftLinesOffered();
                    }}
                    else if(filterBy == 'SUSPENDED')
                    {{
                        filteredLines = showcase.SuspendedLinesOffered();
                    }}
                    else if(filterBy == 'PUBLISHED')
                    {{
                        filteredLines = showcase.PublishedLinesOffered();
                    }}
                    else if(filterBy == 'NONPENDING')
                    {{
                        filteredLines = showcase.AllNonPendingLines();
                    }}

                    for(liness : filteredLines)
                    {{
                        print lines.LineId id;
                        print lines.LineTypeAsString type;
                        print lines.Text text;
                        print lines.Visibility.ToString() visibility;
                        print lines.Index index;
                        print lines.ShelveIndex shelveIndex;
                        print lines.RemainingAvailabilityTime(Now) timeKillerInSeconds;
                        print lines.Tier.Name tier;
                        print lines.BasedOn.Shortcut shortcut;

                        for (validPeriods : liness.ValidPeriodsToBePublished())
                        {{
                            print validPeriods.Name name;
                        }}

                        if (lines.DueDate.MilestoneAsString() == 'Limits')
                        {{
                            print lines.DueDate.IsUnlimited isUnlimited;
                            print 'Unlimited' timeKillerMomentAsString;
                        }}
                        else if (lines.DueDate.MilestoneAsString() == 'BeforeGameStarts' || lines.DueDate.MilestoneAsString() == 'AfterLinePublishing' || lines.DueDate.MilestoneAsString() == 'AfterGameStarts')
                        {{
                            print lines.DueDate.TimeInSeconds timeKillerSetUpInSeconds;
                            print lines.DueDate.MilestoneAsString() timeKillerMomentAsString;
                        }}
                        else if (lines.DueDate.MilestoneAsString() == 'AfterPeriodStarts' )
                        {{
                            print lines.DueDate.TimeInSeconds timeKillerSetUpInSeconds;
                            print lines.DueDate.MilestoneAsString() timeKillerMomentAsString;
                            print lines.DueDate.BasePeriod timeKillerPeriodName;
                        }}
                        else if(lines.DueDate.MilestoneAsString() == 'SpecificDateTime')
                        {{
                            print lines.DueDate.SpecificDateTime specificDateTime;
                            print lines.DueDate.MilestoneAsString() timeKillerMomentAsString;
                        }}

                        logEntries = lines.Log.LastEntries(5);
						for (log:logEntries)
						{{
							print log.DateFormattedAsText date;
							print log.Who who;
							print log.Message message;
						}}

                        if(lines.LineTypeAsString == 'SPREAD_LINE')
                        {{
                            spreadLine = lines;
                            print spreadLine.TeamAReward teamAReward;
                            print spreadLine.TeamBReward teamBReward;
                            print spreadLine.Spread spread;
                            print showcase.RiskAssestment.PlacedFor(lines, spreadLine.GetTeamAnswer(game.TeamA)) placeForTeamA;
                            print showcase.RiskAssestment.PlacedFor(lines, spreadLine.GetTeamAnswer(game.TeamB)) placeForTeamB;
                            print showcase.RiskAssestment.ToPayFor(lines, spreadLine.GetTeamAnswer(game.TeamA)) toPayForTeamA;
                            print showcase.RiskAssestment.ToPayFor(lines, spreadLine.GetTeamAnswer(game.TeamB)) toPayForTeamB;
                            print showcase.RiskAssestment.CountForTeamA(lines) countTeamA;
                            print showcase.RiskAssestment.CountForTeamB(lines) countTeamB;
                        }}
                        else if(lines.LineTypeAsString == 'MONEY_LINE')
                        {{
                            moneyLine = lines;
                            print moneyLine.TeamAReward teamAReward;
                            print moneyLine.TeamBReward teamBReward;
                            print showcase.RiskAssestment.PlacedFor(lines, moneyLine.GetTeamAnswer(game.TeamA)) placeForTeamA;
                            print showcase.RiskAssestment.PlacedFor(lines, moneyLine.GetTeamAnswer(game.TeamB)) placeForTeamB;
                            print showcase.RiskAssestment.ToPayFor(lines, moneyLine.GetTeamAnswer(game.TeamA)) toPayForTeamA;
                            print showcase.RiskAssestment.ToPayFor(lines, moneyLine.GetTeamAnswer(game.TeamB)) toPayForTeamB;
                            print showcase.RiskAssestment.CountForTeamA(lines) countTeamA;
                            print showcase.RiskAssestment.CountForTeamB(lines) countTeamB;
                        }}
                        else if(lines.LineTypeAsString == 'MONEYDRAW_LINE')
                        {{
                            moneyDrawLine = lines;
                            print moneyDrawLine.TeamAReward teamAReward;
                            print moneyDrawLine.TieReward tieReward;
                            print moneyDrawLine.TeamBReward teamBReward;
                            print showcase.RiskAssestment.PlacedFor(lines, moneyDrawLine.GetTeamAnswer(game.TeamA)) placeForTeamA;
                            print showcase.RiskAssestment.PlacedFor(lines, moneyDrawLine.GetTeamAnswer(game.TeamB)) placeForTeamB;
                            print riskAssestment.PlacedFor(lines, moneyDrawLine.GetTieAnswer(game.TeamA, game.TeamB)) placeForDraw;
                            print showcase.RiskAssestment.ToPayFor(lines, moneyDrawLine.GetTeamAnswer(game.TeamA)) toPayForTeamA;
                            print showcase.RiskAssestment.ToPayFor(lines, moneyDrawLine.GetTeamAnswer(game.TeamB)) toPayForTeamB;
                            print riskAssestment.ToPayFor(lines, moneyDrawLine.GetTieAnswer(game.TeamA, game.TeamB)) toPayForDraw;
                            print showcase.RiskAssestment.CountForTeamA(lines) countTeamA;
                            print showcase.RiskAssestment.CountForTeamB(lines) countTeamB;
                            print riskAssestment.CountForDraw(lines) countDraw;
                        }}
                        else if(lines.LineTypeAsString == 'YES_NO_LINE')
                        {{
                            yesNoLine = lines;
                            print yesNoLine.YesReward yesReward;
                            print yesNoLine.NoReward noReward;
                            
                            yesAnswer = {YesNoAnswer.YES};
                            noAnswer = {YesNoAnswer.NO};
                            print showcase.RiskAssestment.PlacedFor(lines, yesAnswer) placeForYes;
                            print showcase.RiskAssestment.PlacedFor(lines, noAnswer) placeForNo;
                            print showcase.RiskAssestment.ToPayFor(lines, yesAnswer) toPayForYes;
                            print showcase.RiskAssestment.ToPayFor(lines, noAnswer) toPayForNo;
                            print showcase.RiskAssestment.CountForYes(lines) countYes;
                            print showcase.RiskAssestment.CountForNo(lines) countNo;
                            
                        }}
                        else if(lines.LineTypeAsString == 'OVER_UNDER_LINE')
                        {{
                            overUnderLine = lines;
                            print overUnderLine.OverReward overReward;
                            print overUnderLine.UnderReward underReward;
                            print overUnderLine.Score score;

                            overAnswer = {OverUnderAnswer.OVER};
                            underAnswer = {OverUnderAnswer.UNDER};
                            print showcase.RiskAssestment.PlacedFor(lines, overAnswer) placeForOver;
                            print showcase.RiskAssestment.PlacedFor(lines, underAnswer) placeForUnder;
                            print showcase.RiskAssestment.ToPayFor(lines, overAnswer) toPayForOver;
                            print showcase.RiskAssestment.ToPayFor(lines, underAnswer) toPayForUnder;
                            print showcase.RiskAssestment.CountForOver(lines) countOver;
                            print showcase.RiskAssestment.CountForUnder(lines) countUnder;
                            
                        }}
                        else if(lines.LineTypeAsString == 'FIXED_LINE')
                        {{
                            fixedLine = lines;
                            options = fixedLine.Options;
                            for(option : options)
                            {{
                                print option option;
                                print fixedLine.GetReward(option) reward;
                                
                                answer = fixedLine.GetAnAnswer(option);
                                print showcase.RiskAssestment.PlacedFor(lines, answer) placeForOption;
                                print showcase.RiskAssestment.ToPayFor(lines, answer) toPayForOption;
                                print showcase.RiskAssestment.CountForFixed(lines, option) countForOption;
                            }}

                        }}
                        else if(lines.LineTypeAsString == 'TOTAL_POINTS_LINE')
                        {{
                            totalPointsLine = lines;
                            print totalPointsLine.OverReward overReward;
                            print totalPointsLine.UnderReward underReward;
                            print totalPointsLine.Score score;

                            overTotalAnswer = {OverUnderAnswer.OVER};
                            underTotalAnswer = {OverUnderAnswer.UNDER};
                            print showcase.RiskAssestment.PlacedFor(lines, overTotalAnswer) placeForOver;
                            print showcase.RiskAssestment.PlacedFor(lines, underTotalAnswer) placeForUnder;
                            print showcase.RiskAssestment.ToPayFor(lines, overTotalAnswer) toPayForOver;
                            print showcase.RiskAssestment.ToPayFor(lines, underTotalAnswer) toPayForUnder;
                            print showcase.RiskAssestment.CountForOver(lines) countOver;
                            print showcase.RiskAssestment.CountForUnder(lines) countUnder;
                        }}
                    }}
                }}
			");
			return result;
		}

        [HttpGet("api/tournament/{tournamentId}/availableLines/game/{gameNumber}")]
        [Authorize(Roles = "devops")]//VERIFY_ROLES
        public async Task<IActionResult> AvailableLinesAsync(int tournamentId, int hours, int gameNumber)
        {
            if (hours < 1) return BadRequest("Parameter hours is required");
            if (tournamentId <= 0) return BadRequest($"Parameter {nameof(tournamentId)} must be greater than 0");
            if (gameNumber <= 0) return BadRequest($"Parameter {nameof(gameNumber)} must be greater than 0");

            string domain = HttpContext.Request.Host.Host;

            var result = await LinesAPI.Lines.PerformQryAsync(HttpContext, $@"
                {{
                    tournament = company.Tournaments.FindById({tournamentId});
                    mydomain= '{domain}';
                    currDomain = company.Sales.DomainFrom(mydomain);
                   
                    game = tournament.GetGameNumber({gameNumber});
                    betBoard = company.Betboard(tournament);
                    scheduledDay = game.ScheduledDate.Fecha();
                    matchDay = betBoard.Matchday(scheduledDay);
                    showcase = matchDay.GetShowcase(game);
                    
                    catalog = betBoard.Catalog;
                    print catalog.Sport.Id sportId;
                    print game.TeamA.Logo teamALogo;
                    print game.TeamB.Logo teamBLogo;

                    availableLines = showcase.AvailableLines(currDomain, Now);
                    for(lines : availableLines)
                    {{
                        print lines.LineId linesId;
                        print lines.LineTypeAsString type;
                        print lines.Text question;
                        print lines.Index index;
                        print lines.ShelveIndex shelveIndex;
                        print lines.Game.Number gameNumber;
                        print lines.Game.TeamA.Name teamAName;
                        print lines.Game.TeamB.Name teamBName;
                        print lines.Game.ScheduledDate scheduledDate;
                        print lines.Game.Sport.Id sportId;
                        if (lines.HasBackground())
                        {{
                            print lines.Background.Url background;
                        }}
                        print lines.DueDate.RemainingTimeInSeconds(Now) timeKiller;

                        if(lines.LineTypeAsString == 'SPREAD_LINE')
                        {{
                            spreadLine = lines;
                            print spreadLine.TeamAReward teamAReward;
                            print spreadLine.TeamBReward teamBReward;
                            print spreadLine.Spread spread;
                        }}
                        else if(lines.LineTypeAsString == 'MONEY_LINE')
                        {{
                            moneyLine = lines;
                            print moneyLine.TeamAReward teamAReward;
                            print moneyLine.TeamBReward teamBReward;
                        }}
                        else if(lines.LineTypeAsString == 'MONEYDRAW_LINE')
                        {{
                            moneyDrawLine = lines;
                            print moneyDrawLine.TeamAReward teamAReward;
                            print moneyDrawLine.TeamBReward teamBReward;
                            print moneyDrawLine.TieReward tieReward;
                        }}
                        else if(lines.LineTypeAsString == 'YES_NO_LINE')
                        {{
                            yesNoLine = lines;
                            print yesNoLine.YesReward yesReward;
                            print yesNoLine.NoReward noReward;
                        }}
                        else if(lines.LineTypeAsString == 'OVER_UNDER_LINE')
                        {{
                            overUnderLine = lines;
                            print overUnderLine.OverReward overReward;
                            print overUnderLine.UnderReward underReward;
                            print overUnderLine.Score score;
                        }}
                        else if(lines.LineTypeAsString == 'FIXED_LINE')
                        {{
                            fixedLine = lines;
                            options = fixedLine.Options;
                            for(option : options)
                            {{
                                print option option;
                                print fixedLine.GetReward(option) reward;
                            }}
                        }}
                        else if(lines.LineTypeAsString == 'TOTAL_POINTS_LINE')
                        {{
                            totalPointsLine = lines;
                            print totalPointsLine.OverReward overReward;
                            print totalPointsLine.UnderReward underReward;
                            print totalPointsLine.Score score;
                        }}
                    }}
                }}
			");
			return result;
		}
        
        [HttpGet("api/tournament/{tournamentId}/lines/betboard/game/{gameNumber}/lines")]
        [Authorize(Roles = "l23")]
        public async Task<IActionResult> LinesByGameAsync(int tournamentId, int gameNumber)
        {
            if (gameNumber <= 0) return BadRequest($"{nameof(gameNumber)} must be greater than 0");
            if (tournamentId <= 0) return BadRequest($"{nameof(tournamentId)} must be greater than 0");

            var result = await LinesAPI.Lines.PerformQryAsync(HttpContext, $@"
				{{
                    tournament = company.Tournaments.FindById({tournamentId});
                    game = tournament.GetGameNumber({gameNumber});
                    betBoard = company.Betboard(tournament);
                    print game.Sport.Name sportName;
                    print game.TeamA.Name teamA;
                    print game.TeamB.Name teamB;
                    print game.ScheduledDate scheduledDate;
                    print game.ScheduledDate.ToString('HH:mm') hour;
                    print game.CurrentPeriod.Name currentPeriod;
                    isGameEnded = game.IsGameOver();
                    print isGameEnded isGameEnded;
                    if (isGameEnded)
                    {{
                        print game.ScoreTeamA scoreTeamA;
                        print game.ScoreTeamB scoreTeamB;
                    }}
                    matchDay = betBoard.Matchday(game.ScheduledDate.Date);
                    showcase = matchDay.GetShowcase(game);
                    pendingOrRegradedLinesList = showcase.PendingOrRegradedLines();
                    for(pendingOrRegradedLines : pendingOrRegradedLinesList)
                    {{
                        print pendingOrRegradedLines.LineId id;
                        print pendingOrRegradedLines.Index index;
                        print pendingOrRegradedLines.LineTypeAsString type;
                        print pendingOrRegradedLines.Tier.Name tier;
                        print pendingOrRegradedLines.Text text;
                        print pendingOrRegradedLines.PublishedOn.Name publishedOn;
                        print showcase.RiskAssestment.CountFor(pendingOrRegradedLines) purchaseTickets;
                        logEntries = pendingOrRegradedLines.Log.LastEntries(5);
						for (log:logEntries)
						{{
							print log.DateFormattedAsText date;
							print log.Who who;
							print log.Message message;
						}}

                        if(pendingOrRegradedLines.IsFixedLine())
                        {{
                            fixedLine = pendingOrRegradedLines;
                            options = fixedLine.Options;
                            for(option : options)
                            {{
                                print option option;
                            }}
                        }}
                    }}
                    gradedLinesList = showcase.GradedLines();
                    for(gradedLines : gradedLinesList)
                    {{
                        gradedLine = gradedLines;
                        print gradedLine.LineId id;
                        print gradedLine.Index index;
                        print gradedLine.LineTypeAsString type;
                        print gradedLine.Tier.Name tier;
                        print gradedLine.Text text;
                        print gradedLine.PublishedOn.Name publishedOn;
                        print gradedLine.GradedOn.Name gradedOn;
                        print gradedLine.GradingWasConfirmed gradingWasConfirmed;
                        print showcase.RiskAssestment.CountFor(gradedLine) purchaseTickets;
                        logGradedEntries = gradedLine.Log.LastEntries(5);
						for (log:logGradedEntries)
						{{
							print log.DateFormattedAsText date;
							print log.Who who;
							print log.Message message;
						}}

                        if(gradedLine.IsSpreadLine() && gradedLines.IsGraded())
                        {{
                            spreadLine = gradedLine;
                            print spreadLine.ScoreTeamA() scoreTeamA;
                            print spreadLine.ScoreTeamB() scoreTeamB;
                        }}
                        else if(gradedLine.IsMoneyLine() && gradedLines.IsGraded())
                        {{
                            moneyLine = gradedLine;
                            print moneyLine.ScoreTeamA() scoreTeamA;
                            print moneyLine.ScoreTeamB() scoreTeamB;
                        }}
                        else if(gradedLine.IsMoneyDrawLine() && gradedLines.IsGraded())
                        {{
                            moneyDrawLine = gradedLine;
                            print moneyDrawLine.ScoreTeamA() scoreTeamA;
                            print moneyDrawLine.ScoreTeamB() scoreTeamB;
                        }}
                        else if(gradedLine.IsTotalPointsLine())
                        {{
                            totalPointsLine = gradedLine;
                            answerIsOverTotalPoints = totalPointsLine.AnswerIsOver();
                            print answerIsOverTotalPoints answerIsOver;
                            if(answerIsOverTotalPoints)
                            {{
                                print 'Over' answerTotalPoints;
                            }}
                            else
                            {{
                                print 'Under' answerTotalPoints;
                            }}
                        }}
                        else if(gradedLine.IsYesNoLine())
                        {{
                            yesNoLine = gradedLine;
                            answerIsYes = yesNoLine.AnswerIsYes();
                            print answerIsYes answerIsYes;
                            if(answerIsYes)
                            {{
                                print 'Yes' answerYesNo;
                            }}
                            else
                            {{
                                print 'No' answerYesNo;
                            }}
                        }}
                        else if(gradedLine.IsOverUnderLine())
                        {{
                            overUnderLine = gradedLine;
                            answerIsOver = overUnderLine.AnswerIsOver();
                            print answerIsOver answerIsOver;
                            if(answerIsOver)
                            {{
                                print 'Over' answerOverUnder;
                            }}
                            else
                            {{
                                print 'Under' answerOverUnder;
                            }}
                        }}
                        else if(gradedLine.IsFixedLine())
                        {{
                            fixedGradedLine = gradedLine;
                            print fixedGradedLine.ChosenOption() chosenOption;
                        }}
                    }}
                }}
			");
            return result;
        }

        [HttpPost("api/lines/lines/suspension")]
        [Authorize(Roles = "l10")]
        public async Task<IActionResult> SuspendLineAsync([FromBody] SuspendedLineBody body)
        {
            if (body == null) return BadRequest("Body is required");
            if (body.TournamentId <= 0) return BadRequest($"{nameof(body.TournamentId)} must be greater than 0");

            string employeeName = Security.UserName(HttpContext);

            var msg = "Line was suspended";
            var result = await LinesAPI.Lines.PerformCmdAsync(HttpContext, $@"
				{{
                    tournament = company.Tournaments.FindById({body.TournamentId});
                    betBoard = company.Betboard(tournament);
                    game = tournament.GetGameNumber({body.GameNumber});
                    scheduledDate = game.ScheduledDate.Fecha();
                    matchDay = betBoard.Matchday(scheduledDate);
                    showcase = matchDay.GetShowcase(game);
                    line = showcase.SuspendLine(itIsThePresent, {body.LineId}, Now);
                    line.AddAnnotation('{msg}', '{employeeName}', Now);
				}}
			");
            return result;
        }

        [HttpPost("api/game/confirm/grade/line")]
        [Authorize(Roles = "l34")]
        public async Task<IActionResult> LineConfirmGradeAsync([FromBody] ConfirmLineGradeGameBody body)
        {
            if (body == null) return BadRequest("Body is required");
            if (body.TournamentId <= 0) return BadRequest($"{nameof(body.TournamentId)} must be greater than 0");
            if (body.GameNumber <= 0) return BadRequest($"{nameof(body.GameNumber)} must be greater than 0");
            if (body.LineId <= 0) return BadRequest($"{nameof(body.LineId)} must be greater than 0");

            string employeeName = Security.UserName(HttpContext);

            var result = await LinesAPI.Lines.PerformCmdAsync(HttpContext, $@"
				{{
                    tournament = company.Tournaments.FindById({body.TournamentId});
                    betBoard = company.Betboard(tournament);
                    line = betBoard.GetLine({body.LineId});
                    game = tournament.GetGameNumber({body.GameNumber});
                    scheduledDate = game.ScheduledDate.Fecha();
                    matchDay = betBoard.Matchday(scheduledDate);
                    showcase = matchDay.GetShowcase(game);
                    showcase.Confirm(ItIsThePresent, line, Now, '{employeeName}');
				}}
			");
            return result;
        }

        [HttpPost("api/lines/lines/resumption")]
        [Authorize(Roles = "l11")]
        public async Task<IActionResult> ResumeLineAsync([FromBody] SuspendedLineBody body)
        {
            if (body == null) return BadRequest("Body is required");
            if (body.TournamentId <= 0) return BadRequest($"{nameof(body.TournamentId)} must be greater than 0");

            string employeeName = Security.UserName(HttpContext);

            var msg = "Line was resumed";
            var result = await LinesAPI.Lines.PerformCmdAsync(HttpContext, $@"
				{{
                    tournament = company.Tournaments.FindById({body.TournamentId});
                    betBoard = company.Betboard(tournament);
                    game = tournament.GetGameNumber({body.GameNumber});
                    scheduledDate = game.ScheduledDate.Fecha();
                    matchDay = betBoard.Matchday(scheduledDate);
                    showcase = matchDay.GetShowcase(game);
                    line = showcase.ResumeLine(itIsThePresent, {body.LineId}, Now);
                    line.AddAnnotation('{msg}', '{employeeName}', Now);
				}}
			");
            return result;
        }


        [HttpPost("api/lines/lines/cancel")]
        [Authorize(Roles = "l15")]
        public async Task<IActionResult> CancelLineAsync([FromBody] CancelLineBody body)
        {
            if (body == null) return BadRequest("Body is required");
            if (body.TournamentId <= 0) return BadRequest($"{nameof(body.TournamentId)} must be greater than 0");
            if (body.LineId <= 0) return BadRequest($"{nameof(body.LineId)} must be greater than 0");
            if (body.GameNumber <= 0) return BadRequest($"{nameof(body.GameNumber)} must be greater than 0");

            string employeeName = Security.UserName(HttpContext);
            var msg = "Line was cancelled";

            var result = await LinesAPI.Lines.PerformChkThenCmdAsync(HttpContext, $@"
                    tournament = company.Tournaments.FindById({body.TournamentId});
                    betBoard = company.Betboard(tournament);
                    game = tournament.GetGameNumber({body.GameNumber});
                    scheduledDate = game.ScheduledDate.Fecha();
                    matchDay = betBoard.Matchday(scheduledDate);
                    showcase = matchDay.GetShowcase(game);
                    line = betBoard.GetLine({body.LineId});
                
                    Check( ! showcase.HasPurchases(line) ) Error 'Line can not be canceled because it already has purchases. Grade as NoAction instead.';",
                $@"
				{{
                    tournament = company.Tournaments.FindById({body.TournamentId});
                    betBoard = company.Betboard(tournament);
                    game = tournament.GetGameNumber({body.GameNumber});
                    scheduledDate = game.ScheduledDate.Fecha();
                    matchDay = betBoard.Matchday(scheduledDate);
                    showcase = matchDay.GetShowcase(game);
                    line = showcase.CancelLine(itIsThePresent, {body.LineId}, Now);
                    line.AddAnnotation('{msg}', '{employeeName}', Now);

				}}
			");
            return result;
        }

        //        [HttpGet("api/tournament/{tournamentId}/lines/confirm")]

        [DataContract(Name = "wager")]
		public class WagerBody
		{
			public WagerBody()
			{
				CurrencyCode = Currencies.CODES.USD.ToString();
			}
			[DataMember(Name = "currencyCode")]
			public string CurrencyCode { get; set; }
			[DataMember(Name = "playerId")]
			public string PlayerId { get; set; }
			[DataMember(Name = "lineId")]
			public int LineId { get; set; }
			[DataMember(Name = "amount")]
			public decimal Amount { get; set; }
			[DataMember(Name = "gameNumber")]
			public int GameNumber { get; set; }
			[DataMember(Name = "chosenOption")]
			public string ChosenOption { get; set; }
            [DataMember(Name = "tournamentId")]
            public int TournamentId { get; set; }
            [DataMember(Name = "accountNumber")]
            public string AccountNumber { get; set; }
        }
        [DataContract(Name = "playerInfo")]
        public class PlayerInfo
        {
            public PlayerInfo()
            {
            }
            [DataMember(Name = "atAddress")]
            public string AtAddress { get; set; }
        }

        [DataContract(Name = "GradedABLinesBody")]
        public class GradedABLinesBody
        {
            [DataMember(Name = "lines")]
            public List<GradeLineBody> Lines { get; set; }
        }

        [DataContract(Name = "GradeLineBody")]
        public class GradeLineBody
        {
            [DataMember(Name = "lineId")]
            public int LineId { get; set; }
            [DataMember(Name = "gameNumber")]
            public int GameNumber { get; set; }
            [DataMember(Name = "tournamentId")]
            public int TournamentId { get; set; }
            [DataMember(Name = "isSuccess")]
            public bool IsSuccess { get; set; }

            [DataMember(Name = "scoreA")]
            public int ScoreA { get; set; }
            [DataMember(Name = "scoreB")]
            public int ScoreB { get; set; }
        }

        [DataContract(Name = "GradedABLineBody")]
        public class GradedABLineBody
        {
            [DataMember(Name = "lineId")]
            public int LineId { get; set; }
            [DataMember(Name = "gameNumber")]
            public int GameNumber { get; set; }
            [DataMember(Name = "scoreA")]
            public int ScoreA { get; set; }
            [DataMember(Name = "scoreB")]
            public int ScoreB { get; set; }
            [DataMember(Name = "tournamentId")]
            public int TournamentId { get; set; }
        }

        [DataContract(Name = "GradedYesNoLineBody")]
        public class GradedYesNoLineBody
        {
            [DataMember(Name = "lineId")]
            public int LineId { get; set; }
            [DataMember(Name = "gameNumber")]
            public int GameNumber { get; set; }
            [DataMember(Name = "isYesAnswer")]
            public bool IsYesAnswer { get; set; }
            [DataMember(Name = "tournamentId")]
            public int TournamentId { get; set; }
        }

        [DataContract(Name = "GradedOverUnderLineBody")]
        public class GradedOverUnderLineBody
        {
            [DataMember(Name = "lineId")]
            public int LineId { get; set; }
            [DataMember(Name = "gameNumber")]
            public int GameNumber { get; set; }
            [DataMember(Name = "scoreAnswer")]
            public int ScoreAnswer { get; set; }
            [DataMember(Name = "tournamentId")]
            public int TournamentId { get; set; }
        }

        [DataContract(Name = "GradedFixedLineBody")]
        public class GradedFixedLineBody
        {
            [DataMember(Name = "lineId")]
            public int LineId { get; set; }
            [DataMember(Name = "gameNumber")]
            public int GameNumber { get; set; }
            [DataMember(Name = "optionAnswer")]
            public string OptionAnswer { get; set; }
            [DataMember(Name = "tournamentId")]
            public int TournamentId { get; set; }
        }

        [DataContract(Name = "Tier1RegradeBody")]
        public class Tier1RegradeBody
        {
            [DataMember(Name = "gameNumber")]
            public int GameNumber { get; set; }
            [DataMember(Name = "tournamentId")]
            public int TournamentId { get; set; }
        }

        [DataContract(Name = "LineRegradeBody")]
        public class LineRegradeBody
        {
            [DataMember(Name = "lineId")]
            public int LineId { get; set; }
            [DataMember(Name = "gameNumber")]
            public int GameNumber { get; set; }
            [DataMember(Name = "tournamentId")]
            public int TournamentId { get; set; }
        }

        [DataContract(Name = "Tier1NoActionBody")]
        public class Tier1NoActionBody
        {
            [DataMember(Name = "gameNumber")]
            public int GameNumber { get; set; }
            [DataMember(Name = "tournamentId")]
            public int TournamentId { get; set; }
        }

        [DataContract(Name = "LineNoActionBody")]
        public class LineNoActionBody
        {
            [DataMember(Name = "lineId")]
            public int LineId { get; set; }
            [DataMember(Name = "gameNumber")]
            public int GameNumber { get; set; }
            [DataMember(Name = "tournamentId")]
            public int TournamentId { get; set; }
        }

        [DataContract(Name = "ConfirmLineGradeGameBody")]
        public class ConfirmLineGradeGameBody
        {
            [DataMember(Name = "lineId")]
            public int LineId { get; set; }
            [DataMember(Name = "gameNumber")]
            public int GameNumber { get; set; }
            [DataMember(Name = "tournamentId")]
            public int TournamentId { get; set; }
        }

        [DataContract(Name = "ConfirmGameGradeBody")]
        public class ConfirmGameGradeBody
        {
            [DataMember(Name = "gameNumber")]
            public int GameNumber { get; set; }
            [DataMember(Name = "tournamentId")]
            public int TournamentId { get; set; }
        }

        [DataContract(Name = "SuspendedLineBody")]
        public class SuspendedLineBody
        {
            [DataMember(Name = "lineId")]
            public int LineId { get; set; }
            [DataMember(Name = "gameNumber")]
            public int GameNumber { get; set; }
            [DataMember(Name = "tournamentId")]
            public int TournamentId { get; set; }
        }

        [DataContract(Name = "CancelLineBody")]
        public class CancelLineBody
        {
            [DataMember(Name = "lineId")]
            public int LineId { get; set; }
            [DataMember(Name = "gameNumber")]
            public int GameNumber { get; set; }
            [DataMember(Name = "tournamentId")]
            public int TournamentId { get; set; }
        }

        [DataContract(Name = "StoreInformation")]
        public class ContextInformation
        {
            [DataMember(Name = "storeId")]
            public int StoreId { get; set; }
            [DataMember(Name = "TeamAName")]
            public string TeamAName { get; set; }
            [DataMember(Name = "TeamBName")]
            public string TeamBName { get; set; }
            [DataMember(Name = "referenceNumber")]
            public string ReferenceNumber { get; set; }
            [DataMember(Name = "scheduledDay")]
            public string ScheduledDay { get; set; }
            [DataMember(Name = "answerText")]
            public string AnswerText { get; set; }
            [DataMember(Name = "toWin")]
            public decimal ToWin { get; set; }
        }
        
    }
}
