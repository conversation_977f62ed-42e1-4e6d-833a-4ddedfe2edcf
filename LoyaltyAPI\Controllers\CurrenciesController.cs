﻿using GamesEngine.Business;
using GamesEngine.Finance;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace LoyaltyAPI.Controllers
{
    public class CurrenciesController : AuthorizeController
    {
        [HttpGet("api/currencies")]
        [Authorize(Roles = "player")]
        public async Task<IActionResult> CurrenciesAsync()
        {
            var result = await LoyaltyAPI.Loyalty.PerformQryAsync(HttpContext, $@"
            {{
                for (currencies:company.System.EnabledCoins)
                {{
                    currency = currencies;
					print currency.Id id;
                    print currency.Iso4217Code currencyCode;
                    print currency.UnicodeAsText unicode;
                    print currency.Name name;
					print currency.TypeAsText type;
                }}
            }}
            ");
            return result;
        }
    }
}
