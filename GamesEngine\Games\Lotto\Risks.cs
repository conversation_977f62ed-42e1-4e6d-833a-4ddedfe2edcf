﻿using GamesEngine.Bets;
using GamesEngine.Domains;
using GamesEngine.Gameboards.Lotto;
using GamesEngine.PurchaseOrders;
using GamesEngine.Time;
using Puppeteer.EventSourcing.Libraries;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using static GamesEngine.Games.Lotto.Disincentives;

namespace GamesEngine.Games.Lotto
{
	class Risks : Objeto
	{
		internal Disincentives Disincentives { get; } = new Disincentives();
		internal RiskPerLotteries Risk { get; }

		public Risks()
		{
			Risk = new RiskPerLotteries(this);
		}

		internal void StartupRiskForNewLottery(int pickNumber, Lottery lottery)
		{
			Risk.StartupRiskForLottery(pickNumber, lottery);
		}

		internal void StartupRiskForLotteryPowerball(LotteryPowerball lottery)
		{
			Risk.StartupRiskForLotteryPowerball(lottery);
		}

		internal void StartupRiskForLotteryKeno(LotteryKeno lottery)
		{
			Risk.StartupRiskForLotteryKeno(lottery);
		}

		internal void ForgetNumbersTracker(int pickNumber, Lottery lottery, DateTime drawDate)
        {
			if (lottery is LotteryTriz)
			{
				Risk.ForgetNumbersTracker(2, lottery, drawDate);
				Risk.ForgetNumbersTracker(3, lottery, drawDate);
				Risk.ForgetNumbersTracker(4, lottery, drawDate);
				Risk.ForgetNumbersTracker(5, lottery, drawDate);
			}
			else
			{
				Risk.ForgetNumbersTracker(pickNumber, lottery, drawDate);
			}
        }

		internal void CloneFrom(Risks risks, Domain domain)
		{
            if (risks == null) throw new ArgumentNullException(nameof(risks));
			if (domain == null) throw new ArgumentNullException(nameof(domain));

            Disincentives.CloneFrom(risks.Disincentives);
            Risk.CloneFrom(risks.Risk, domain);
        }

	}

	class RiskPerLotteries : Objeto
	{
		internal Risks Risks { get; }
		Dictionary<Lottery, RiskPerLottery> riskPerLotteriesPick2 = new Dictionary<Lottery, RiskPerLottery>();
		Dictionary<Lottery, RiskPerLottery> riskPerLotteriesPick3 = new Dictionary<Lottery, RiskPerLottery>();
		Dictionary<Lottery, RiskPerLottery> riskPerLotteriesPick4 = new Dictionary<Lottery, RiskPerLottery>();
		Dictionary<Lottery, RiskPerLottery> riskPerLotteriesPick5 = new Dictionary<Lottery, RiskPerLottery>();
		RiskPerLottery riskPerLotteryPowerball;
		RiskPerLottery riskPerLotteryKeno;
		const decimal DEFAULT_MAX_RISK_FOR_PICK2 = 540000;
		const decimal DEFAULT_MAX_RISK_FOR_PICK3 = 540000;
		const decimal DEFAULT_MAX_RISK_FOR_PICK4 = 900000;
		const decimal DEFAULT_MAX_RISK_FOR_PICK5 = 900000;
		decimal maxToWinPick2 = DEFAULT_MAX_RISK_FOR_PICK2;
		decimal maxToWinPick3 = DEFAULT_MAX_RISK_FOR_PICK3;
		decimal maxToWinPick4 = DEFAULT_MAX_RISK_FOR_PICK4;
		decimal maxToWinPick5 = DEFAULT_MAX_RISK_FOR_PICK5;
		Dictionary<Domain, MaximumToWinPerGameTypes> maxToWinPerGameTypes = new Dictionary<Domain, MaximumToWinPerGameTypes>();

		private decimal riskToleranceFactor = 0.8m;
        internal decimal RiskToleranceFactor
		{
			get
			{
				return riskToleranceFactor;
			}
			set
			{
                if (value <= 0 || value > 1) throw new GameEngineException("Risk tolerance factor must be between 0 and 1");
                riskToleranceFactor = value;
			}
		}

		internal int RiskQuotationExpirationTime { get; set; } = 60;

        public RiskPerLotteries(Risks risks)
        {
			Risks = risks;
		}

		internal void StartupRiskForLottery(int pickNumber, Lottery lottery)
		{
			if (lottery is LotteryTriz lotteryTriz)
			{
                LotteryForPicks internalLotteryPick2 = lotteryTriz.GetLotteryPick(2);
                var riskPerLottery2 = new RiskPerLottery(this, internalLotteryPick2);
                riskPerLotteriesPick2.Add(internalLotteryPick2, riskPerLottery2);

				LotteryForPicks internalLotteryPick3 = lotteryTriz.GetLotteryPick(3);
				var riskPerLottery3 = new RiskPerLottery(this, internalLotteryPick3);
				riskPerLotteriesPick3.Add(internalLotteryPick3, riskPerLottery3);

				LotteryForPicks internalLotteryPick4 = lotteryTriz.GetLotteryPick(4);
				var riskPerLottery4 = new RiskPerLottery(this, internalLotteryPick4);
				riskPerLotteriesPick4.Add(internalLotteryPick4, riskPerLottery4);

				LotteryForPicks internalLotteryPick5 = lotteryTriz.GetLotteryPick(5);
				var riskPerLottery5 = new RiskPerLottery(this, internalLotteryPick5);
				riskPerLotteriesPick5.Add(internalLotteryPick5, riskPerLottery5);
            }
			else
			{
                var riskPerLottery = new RiskPerLottery(this, lottery);

                switch (pickNumber)
                {
                    case 2:
                        riskPerLotteriesPick2.Add(lottery, riskPerLottery);
                        break;
                    case 3:
                        riskPerLotteriesPick3.Add(lottery, riskPerLottery);
                        break;
                    case 4:
                        riskPerLotteriesPick4.Add(lottery, riskPerLottery);
                        break;
                    case 5:
                        riskPerLotteriesPick5.Add(lottery, riskPerLottery);
                        break;
                    default:
                        throw new GameEngineException($"There is no {nameof(IPick)} implementation for {nameof(pickNumber)} {pickNumber}");
                }
            }
		}

		internal void StartupRiskForLotteryPowerball(LotteryPowerball lottery)
        {
			riskPerLotteryPowerball = new RiskPerLottery(this, lottery);
		}

		internal void StartupRiskForLotteryKeno(LotteryKeno lottery)
		{
			riskPerLotteryKeno = new RiskPerLottery(this, lottery);
		}

		internal RiskPerLottery GetRiskPerLottery(int pickNumber, Lottery lottery)
		{
			Lottery lotteryRisk = lottery;
            if (lottery is LotteryForPicks lotteryPicksBase && lotteryPicksBase.IsLotteryPickWithFireBall)
            {
                switch (lotteryPicksBase)
                {
                    case LotteryPickWithFireBall<Pick2> lotteryPickWithFireBall2:
                        lotteryRisk = lotteryPickWithFireBall2.BaseLottery;
                        break;
                    case LotteryPickWithFireBall<Pick3> lotteryPickWithFireBall3:
                        lotteryRisk = lotteryPickWithFireBall3.BaseLottery;
                        break;
                    case LotteryPickWithFireBall<Pick4> lotteryPickWithFireBall4:
                        lotteryRisk = lotteryPickWithFireBall4.BaseLottery;
                        break;
                    case LotteryPickWithFireBall<Pick5> lotteryPickWithFireBall5:
                        lotteryRisk = lotteryPickWithFireBall5.BaseLottery;
                        break;
                    default:
                        throw new GameEngineException($"There is no {nameof(IPick)} implementation for {nameof(lotteryPicksBase)} {lotteryPicksBase}");
                }
            }

			if (lotteryRisk is LotteryTriz lotteryTriz)
			{
                lotteryRisk = lotteryTriz.GetLotteryPick(pickNumber);
            }

            switch (pickNumber)
            {
                case 2:
                    var risk = riskPerLotteriesPick2.GetValueOrDefault(lotteryRisk);
                    if (risk == null) throw new GameEngineException($"No risk for {nameof(lotteryRisk)} '{lotteryRisk.State.Abbreviation}' {nameof(pickNumber)} {pickNumber}");
                    return risk;
                case 3:
                    risk = riskPerLotteriesPick3.GetValueOrDefault(lotteryRisk);
                    if (risk == null) throw new GameEngineException($"No risk for {nameof(lotteryRisk)} '{lotteryRisk.State.Abbreviation}' {nameof(pickNumber)} {pickNumber}");
                    return risk;
                case 4:
                    risk = riskPerLotteriesPick4.GetValueOrDefault(lotteryRisk);
                    if (risk == null) throw new GameEngineException($"No risk for {nameof(lotteryRisk)} '{lotteryRisk.State.Abbreviation}' {nameof(pickNumber)} {pickNumber}");
                    return risk;
                case 5:
                    risk = riskPerLotteriesPick5.GetValueOrDefault(lotteryRisk);
                    if (risk == null) throw new GameEngineException($"No risk for {nameof(lotteryRisk)} '{lotteryRisk.State.Abbreviation}' {nameof(pickNumber)} {pickNumber}");
                    return risk;
                case 12:
                    if (!(lottery is LotteryPowerball)) throw new GameEngineException($"{nameof(lotteryRisk)} '{lotteryRisk.State.Abbreviation}' must be of type {nameof(LotteryPowerball)} for {nameof(pickNumber)} {pickNumber}");
                    return riskPerLotteryPowerball;
                default:
                    throw new GameEngineException($"There is no {nameof(IPick)} implementation for {nameof(pickNumber)} {pickNumber}");
            }
        }

		internal RiskPerLottery GetRiskPerLottery(Lottery lottery)
		{
            if (lottery is LotteryForPicks lotteryPicksBase && lotteryPicksBase.IsLotteryPickWithFireBall)
			{
				switch (lotteryPicksBase)
				{
					case LotteryPickWithFireBall<Pick2> lotteryPickWithFireBall2:
                        lottery = lotteryPickWithFireBall2.BaseLottery;
                        break;
                    case LotteryPickWithFireBall<Pick3> lotteryPickWithFireBall3:
						lottery = lotteryPickWithFireBall3.BaseLottery;
                        break;
                    case LotteryPickWithFireBall<Pick4> lotteryPickWithFireBall4:
						lottery = lotteryPickWithFireBall4.BaseLottery;
                        break;
                    case LotteryPickWithFireBall<Pick5> lotteryPickWithFireBall5:
						lottery = lotteryPickWithFireBall5.BaseLottery;
                        break;
					default:
						throw new GameEngineException($"There is no {nameof(IPick)} implementation for {nameof(lotteryPicksBase)} {lotteryPicksBase}");
                }
            }

			var risk = riskPerLotteriesPick3.GetValueOrDefault(lottery);
			if (risk != null) return risk;
			risk = riskPerLotteriesPick4.GetValueOrDefault(lottery);
			if (risk != null) return risk;
			risk = riskPerLotteriesPick2.GetValueOrDefault(lottery);
			if (risk != null) return risk;
			risk = riskPerLotteriesPick5.GetValueOrDefault(lottery);
			if (risk != null) return risk;
			throw new GameEngineException($"No risk for {nameof(lottery)} '{lottery.State.Abbreviation}'");
		}

		internal RiskPerLottery GetRiskPerLotteryPowerball()
		{
			return riskPerLotteryPowerball;
		}

		internal RiskPerLottery GetRiskPerLotteryKeno()
		{
			return riskPerLotteryKeno;
		}

        internal decimal GetMaximumToWin(int pickNumber)
        {
            switch (pickNumber)
            {
                case 2:
                    if (maxToWinPerGameTypes.Count == 0) return maxToWinPick2;
					return maxToWinPerGameTypes.Values.Select(toWin => toWin.GetMaximumToWin(pickNumber)).Min();
                case 3:
                    if (maxToWinPerGameTypes.Count == 0) return maxToWinPick3;
                    return maxToWinPerGameTypes.Values.Select(toWin => toWin.GetMaximumToWin(pickNumber)).Min();
                case 4:
                    if (maxToWinPerGameTypes.Count == 0) return maxToWinPick4;
                    return maxToWinPerGameTypes.Values.Select(toWin => toWin.GetMaximumToWin(pickNumber)).Min();
                case 5:
                    if (maxToWinPerGameTypes.Count == 0) return maxToWinPick5;
                    return maxToWinPerGameTypes.Values.Select(toWin => toWin.GetMaximumToWin(pickNumber)).Min();
                default:
                    throw new GameEngineException($"There is no {nameof(IPick)} implementation for {nameof(pickNumber)} {pickNumber}");
            }
        }

        internal decimal GetMaximumToWin(Domain domain, int pickNumber)
		{
			if (domain == null) throw new ArgumentNullException(nameof(domain));

			switch (pickNumber)
			{
				case 2:
					if (!maxToWinPerGameTypes.ContainsKey(domain)) return maxToWinPick2;
					return maxToWinPerGameTypes[domain].GetMaximumToWin(pickNumber);
				case 3:
                    if (!maxToWinPerGameTypes.ContainsKey(domain)) return maxToWinPick3;
					return maxToWinPerGameTypes[domain].GetMaximumToWin(pickNumber);
				case 4:
                    if (!maxToWinPerGameTypes.ContainsKey(domain)) return maxToWinPick4;
					return maxToWinPerGameTypes[domain].GetMaximumToWin(pickNumber);
				case 5:
                    if (!maxToWinPerGameTypes.ContainsKey(domain)) return maxToWinPick5;
					return maxToWinPerGameTypes[domain].GetMaximumToWin(pickNumber);
				default:
					throw new GameEngineException($"There is no {nameof(IPick)} implementation for {nameof(pickNumber)} {pickNumber}");
			}
		}

		[Obsolete("Use UpdateToWin(Domain domain, int pickNumber, decimal newToWin) instead")]
		internal void UpdateToWin(int pickNumber, decimal newToWin)
		{
			switch (pickNumber)
			{
				case 2:
					maxToWinPick2 = newToWin;
					break;
				case 3:
					maxToWinPick3 = newToWin;
					break;
				case 4:
					maxToWinPick4 = newToWin;
					break;
				case 5:
					maxToWinPick5 = newToWin;
					break;
				default:
					throw new GameEngineException($"There is no {nameof(IPick)} implementation for {nameof(pickNumber)} {pickNumber}");
			}
		}

		internal void UpdateToWin(Domain domain, int pickNumber, decimal newToWin)
		{
            if (!maxToWinPerGameTypes.ContainsKey(domain)) maxToWinPerGameTypes.Add(domain, new MaximumToWinPerGameTypes(domain, maxToWinPick2, maxToWinPick3, maxToWinPick4, maxToWinPick5));
			maxToWinPerGameTypes[domain].UpdateToWin(pickNumber, newToWin);
        }

		internal void ForgetNumbersTracker(int pickNumber, Lottery lottery, DateTime drawDate)
		{
			var riskPerLottery = GetRiskPerLottery(pickNumber, lottery);
			riskPerLottery.ForgetNumbersTracker(drawDate);
		}

		internal void ForgetPreviousTracking(int pickNumber, Player player)
        {
			if (player == null) throw new ArgumentNullException(nameof(player));

			IEnumerable<RiskPerLottery> riskPerLotteries;
			switch (pickNumber)
			{
				case 2:
					riskPerLotteries = riskPerLotteriesPick2.Values;
					break;
				case 3:
					riskPerLotteries = riskPerLotteriesPick3.Values;
					break;
				case 4:
					riskPerLotteries = riskPerLotteriesPick4.Values;
					break;
				case 5:
					riskPerLotteries = riskPerLotteriesPick5.Values;
					break;
				default:
					throw new GameEngineException($"There is no {nameof(IPick)} implementation for {nameof(pickNumber)} {pickNumber}");
			}

            foreach (var riskPerLottery in riskPerLotteries)
            {
				riskPerLottery.ForgetPreviousTracking(player);
            }
		}

        internal void CloneFrom(RiskPerLotteries riskPerLotteries, Domain domain)
		{
            if (riskPerLotteries == null) throw new ArgumentNullException(nameof(riskPerLotteries));
			if (domain == null) throw new ArgumentNullException(nameof(domain));

            maxToWinPick2 = riskPerLotteries.maxToWinPick2;
            maxToWinPick3 = riskPerLotteries.maxToWinPick3;
            maxToWinPick4 = riskPerLotteries.maxToWinPick4;
            maxToWinPick5 = riskPerLotteries.maxToWinPick5;

			foreach (var riskPerLotteryPair in riskPerLotteries.riskPerLotteriesPick2)
			{
				var lottery = riskPerLotteryPair.Key;
                var riskPerLottery = riskPerLotteryPair.Value;
                var newRiskPerLottery = new RiskPerLottery(this, lottery);
				newRiskPerLottery.CloneFrom(riskPerLottery);
                riskPerLotteriesPick2.Add(lottery, newRiskPerLottery);
            }
			foreach (var riskPerLotteryPair in riskPerLotteries.riskPerLotteriesPick3)
			{
				var lottery = riskPerLotteryPair.Key;
                var riskPerLottery = riskPerLotteryPair.Value;
                var newRiskPerLottery = new RiskPerLottery(this, lottery);
				newRiskPerLottery.CloneFrom(riskPerLottery);
				riskPerLotteriesPick3.Add(lottery, newRiskPerLottery);
			}
            foreach (var riskPerLotteryPair in riskPerLotteries.riskPerLotteriesPick4)
            {
                var lottery = riskPerLotteryPair.Key;
                var riskPerLottery = riskPerLotteryPair.Value;
                var newRiskPerLottery = new RiskPerLottery(this, lottery);
                newRiskPerLottery.CloneFrom(riskPerLottery);
                riskPerLotteriesPick4.Add(lottery, newRiskPerLottery);
            }
            foreach (var riskPerLotteryPair in riskPerLotteries.riskPerLotteriesPick5)
            {
                var lottery = riskPerLotteryPair.Key;
                var riskPerLottery = riskPerLotteryPair.Value;
                var newRiskPerLottery = new RiskPerLottery(this, lottery);
                newRiskPerLottery.CloneFrom(riskPerLottery);
                riskPerLotteriesPick5.Add(lottery, newRiskPerLottery);
            }

			if (riskPerLotteries.maxToWinPerGameTypes.ContainsKey(domain))
			{
				var toWinPick2 = riskPerLotteries.maxToWinPerGameTypes[domain].MaxToWinPerGameTypes[0];
				var toWinPick3 = riskPerLotteries.maxToWinPerGameTypes[domain].MaxToWinPerGameTypes[1];
				var toWinPick4 = riskPerLotteries.maxToWinPerGameTypes[domain].MaxToWinPerGameTypes[2];
				var toWinPick5 = riskPerLotteries.maxToWinPerGameTypes[domain].MaxToWinPerGameTypes[3];
                maxToWinPerGameTypes.Add(domain, new MaximumToWinPerGameTypes(domain, toWinPick2, toWinPick3, toWinPick4, toWinPick5));
            }
        }

		class MaximumToWinPerGameTypes
		{
			internal List<decimal> MaxToWinPerGameTypes { get; } = new List<decimal>();
			Domain domain;
			internal MaximumToWinPerGameTypes(Domain domain, decimal maxToWinPick2, decimal maxToWinPick3, decimal maxToWinPick4, decimal maxToWinPick5)
			{
				this.domain = domain;
				MaxToWinPerGameTypes.Add(maxToWinPick2);
				MaxToWinPerGameTypes.Add(maxToWinPick3);
				MaxToWinPerGameTypes.Add(maxToWinPick4);
				MaxToWinPerGameTypes.Add(maxToWinPick5);
			}

            internal decimal GetMaximumToWin(int pickNumber)
            {
                switch (pickNumber)
                {
                    case 2:
                        return MaxToWinPerGameTypes[0];
                    case 3:
                        return MaxToWinPerGameTypes[1];
                    case 4:
                        return MaxToWinPerGameTypes[2];
                    case 5:
                        return MaxToWinPerGameTypes[3];
                    default:
                        throw new GameEngineException($"There is no {nameof(IPick)} implementation for {nameof(pickNumber)} {pickNumber}");
                }
            }

            internal void UpdateToWin(int pickNumber, decimal newToWin)
            {
                switch (pickNumber)
                {
                    case 2:
                        MaxToWinPerGameTypes[0] = newToWin;
                        break;
                    case 3:
                        MaxToWinPerGameTypes[1] = newToWin;
                        break;
                    case 4:
                        MaxToWinPerGameTypes[2] = newToWin;
                        break;
                    case 5:
                        MaxToWinPerGameTypes[3] = newToWin;
                        break;
                    default:
                        throw new GameEngineException($"There is no {nameof(IPick)} implementation for {nameof(pickNumber)} {pickNumber}");
                }
            }
        }
    }

    internal class RiskPerLottery:Objeto
	{
		readonly Lottery lottery;
		readonly RiskPerLotteries riskPerLotteries;
		readonly Dictionary<DateTime, ToWinPerSubtickets> accumulatedToWinPerSubticketsPerDrawDate = new Dictionary<DateTime, ToWinPerSubtickets>();

		public RiskPerLottery(RiskPerLotteries riskPerLotteries, Lottery lottery)
		{
			if (lottery == null) throw new ArgumentNullException(nameof(lottery));

			this.riskPerLotteries = riskPerLotteries;
			this.lottery = lottery;
		}

        internal Lottery Lottery 
		{ 
			get { return lottery; }
        }

        ToWinPerSubtickets GetAccumulatedToWinPerSubtickets(DateTime drawDate)
        {
			var result = accumulatedToWinPerSubticketsPerDrawDate.GetValueOrDefault(drawDate);
			if (result == null) throw new GameEngineException($"No {nameof(ToWinPerSubtickets)} registered for date '{drawDate}'");
			return result;
		}

		bool ExistsToWinPerSubtickets(DateTime drawDate)
        {
			var result = accumulatedToWinPerSubticketsPerDrawDate.ContainsKey(drawDate);
			return result;
		}

		internal void AddToWin(DateTime drawDate, Ticket ticket, DateTime now)
		{
			if (drawDate == default(DateTime)) throw new GameEngineException("Date cannot have default value");
			if (drawDate.Second != 0 || drawDate.Millisecond != 0) throw new GameEngineException("Date of Lottery drawings can not have seconds");

			ToWinPerSubtickets toWinPerSubtickets;
			if (ExistsToWinPerSubtickets(drawDate))
            {
				toWinPerSubtickets = GetAccumulatedToWinPerSubtickets(drawDate);
			}
			else
            {
				toWinPerSubtickets = new ToWinPerSubtickets(lottery, drawDate);
				accumulatedToWinPerSubticketsPerDrawDate.Add(drawDate, toWinPerSubtickets);
			}
			toWinPerSubtickets.Add(ticket, now, NotifyRiskChange);
			ForgetPreviousTracking(ticket.Player);
		}

		internal IEnumerable<ToWin> ToWinPerSubticketsAt(DateTime drawDate)
		{
			if (drawDate == default(DateTime)) throw new GameEngineException("Date cannot have default value");
			if (drawDate.Second != 0 || drawDate.Millisecond != 0) throw new GameEngineException("Date of Lottery drawings can not have seconds");

			if (ExistsToWinPerSubtickets(drawDate))
			{
				var toWinPerSubtickets = GetAccumulatedToWinPerSubtickets(drawDate);
				return toWinPerSubtickets.ToWins;
			}
			return Enumerable.Empty<ToWin>().ToList();
		}

		internal decimal AccumulatedToWinAt(DateTime drawDate)
		{
			if (drawDate == default(DateTime)) throw new GameEngineException("Date cannot have default value");
			if (drawDate.Second != 0 || drawDate.Millisecond != 0) throw new GameEngineException("Date of Lottery drawings can not have seconds");

			if (ExistsToWinPerSubtickets(drawDate))
			{
				var toWinPerSubtickets = GetAccumulatedToWinPerSubtickets(drawDate);
				return toWinPerSubtickets.AccumulatedToWin;
			}
			return 0m;
		}

        internal decimal AccumulatedToWinForNumber(SubTicket<IPick> subticket, DateTime drawDate)
        {
            if (subticket == null) throw new ArgumentNullException(nameof(subticket));
            if (drawDate == default(DateTime)) throw new GameEngineException("Date cannot have default value");
            if (drawDate.Second != 0 || drawDate.Millisecond != 0) throw new GameEngineException("Date of Lottery drawings can not have seconds");

            if (ExistsToWinPerSubtickets(drawDate))
            {
                var toWinPerSubtickets = GetAccumulatedToWinPerSubtickets(drawDate);
                return toWinPerSubtickets.AccumulatedToWinForNumber(subticket);
            }
            return 0m;
        }

        internal decimal MaxAvailableBetAmount(Schedule schedule, PrizeCriteriaIds prizeCriteriaId, RuleType ruleType, string number, DateTime drawDate, Domain domain)
		{
			if (string.IsNullOrWhiteSpace(number)) throw new ArgumentNullException(nameof(number));
			if (drawDate == default(DateTime)) throw new GameEngineException("Date cannot have default value");
			if (drawDate.Second != 0 || drawDate.Millisecond != 0) throw new GameEngineException("Date of Lottery drawings can not have seconds");
            if (domain == null) throw new ArgumentNullException(nameof(domain));

            var subticket = SubTicket<IPick>.SubticketFromNumber(number);
			var remainingToWin = RemainingToWinToAccumulate(schedule, prizeCriteriaId, ruleType, subticket, domain, drawDate);
			var result = remainingToWin / BaseStraightPrize(subticket, domain);
			return result;
		}

		const int BaseBetAmount = 1;
		decimal BaseStraightPrize(SubTicket<IPick> subticket, Domain domain)
        {
			switch (subticket.Length)
			{
				case 2:
					var result = CalculatePrizeToWin(subticket, BaseBetAmount, TicketType.P2S, domain);
					return result;
				case 3:
					result = CalculatePrizeToWin(subticket, BaseBetAmount, TicketType.P3S, domain);
					return result;
				case 4:
					result = CalculatePrizeToWin(subticket, BaseBetAmount, TicketType.P4S, domain);
					return result;
				case 5:
					result = CalculatePrizeToWin(subticket, BaseBetAmount, TicketType.P5S, domain);
					return result;
				default:
					throw new GameEngineException($"There is no implementation for pick {subticket.Length}");
			}
		}

		internal decimal RemainingToWinToAccumulate(Schedule schedule, PrizeCriteriaIds prizeCriteriaId, RuleType ruleType, SubTicket<IPick> subticket, Domain domain, DateTime drawDate)
		{
			if (drawDate == default(DateTime)) throw new GameEngineException("Date cannot have default value");
			if (drawDate.Second != 0 || drawDate.Millisecond != 0) throw new GameEngineException("Date of Lottery drawings can not have seconds");
			if (subticket == null) throw new ArgumentNullException(nameof(subticket));

			var maxToWin = riskPerLotteries.GetMaximumToWin(domain, subticket.Length);
			var disincentivatedRisk = riskPerLotteries.Risks.Disincentives.ApplyTo(schedule, prizeCriteriaId, ruleType, subticket.Length, maxToWin);
			if (! ExistsToWinPerSubtickets(drawDate)) return disincentivatedRisk;

			var toWinPerSubtickets = GetAccumulatedToWinPerSubtickets(drawDate);
			return disincentivatedRisk - toWinPerSubtickets.AccumulatedToWinForNumber(subticket);
        }

		internal IEnumerable<SubticketWithAvailable> SubticketsExceedingToWin(PrizesPicks prizes, Schedule schedule, List<string> numbersAsText, DateTime drawDate, Domain domain, decimal betAmount, TicketType ticketType)
		{
			if (numbersAsText == null || numbersAsText.Count == 0) throw new ArgumentNullException(nameof(numbersAsText));
            if (drawDate == default(DateTime)) throw new GameEngineException("Date cannot have default value");
            if (drawDate.Second != 0 || drawDate.Millisecond != 0) throw new GameEngineException("Date of Lottery drawings can not have seconds");
            if (betAmount <= 0) throw new GameEngineException($"{nameof(betAmount)} {betAmount} must be greater than 0");
            if (domain == null) throw new ArgumentNullException(nameof(domain));

			var result = new List<SubticketWithAvailable>();
			foreach (var numberAsText in numbersAsText)
			{
                if (string.IsNullOrWhiteSpace(numberAsText)) throw new ArgumentNullException(nameof(numberAsText));
                var subticket = SubTicket<IPick>.SubticketFromNumber(numberAsText);
				var prizeForCurrentSubticket = CalculatePrizeToWin(subticket, betAmount, ticketType, domain);
                var prizeCriteriaId = Disincentives.WayOfSubticket(ticketType, subticket);
                var ruleType = RuleTypeFromTicketType(ticketType);
                var remainingToWin = RemainingToWinToAccumulate(schedule, prizeCriteriaId, ruleType, subticket, domain, drawDate);
				if (prizeForCurrentSubticket > remainingToWin)
				{
					var maxRiskForSubticket = Math.Floor((betAmount * remainingToWin / prizeForCurrentSubticket)*100)/100;
					result.Add(new SubticketWithAvailable(numberAsText, maxRiskForSubticket));
				}
			}
			return result;
		}

        internal NumbersQuotation QuoteNumbers(Lottery lottery, List<string> numbersAsText, List<string> drawDatesAsText, Domain domain, decimal betAmount, TicketType ticketType, DateTime now)
        {
            if (lottery == null) throw new ArgumentNullException(nameof(lottery));
            if (numbersAsText == null || numbersAsText.Count == 0) throw new ArgumentNullException(nameof(numbersAsText));
            if (drawDatesAsText == null || drawDatesAsText.Count == 0) throw new ArgumentNullException(nameof(drawDatesAsText));
            if (betAmount <= 0) throw new GameEngineException($"{nameof(betAmount)} {betAmount} must be greater than 0");
            if (domain == null) throw new ArgumentNullException(nameof(domain));
            if (now == default(DateTime)) throw new GameEngineException("Date cannot have default value");

            var result = new NumbersQuotation();
            foreach (var numberAsText in numbersAsText)
            {
                if (string.IsNullOrWhiteSpace(numberAsText)) throw new ArgumentNullException(nameof(numberAsText));
                var subticket = SubTicket<IPick>.SubticketFromNumber(numberAsText);
                var prizeForCurrentSubticket = CalculatePrizeToWin(subticket, betAmount, ticketType, domain);
                var prizeCriteriaId = Disincentives.WayOfSubticket(ticketType, subticket);
                var ruleType = RuleTypeFromTicketType(ticketType);

                bool availableInAllDrawDates = true;
                foreach (var drawDateAsText in drawDatesAsText)
                {
                    if (string.IsNullOrWhiteSpace(drawDateAsText)) throw new ArgumentNullException(nameof(drawDateAsText));
                    if (!DateTime.TryParse(drawDateAsText, out DateTime drawDate)) throw new GameEngineException($"Invalid date '{drawDateAsText}'");
                    if (drawDate.Second != 0 || drawDate.Millisecond != 0) throw new GameEngineException("Date of Lottery drawings can not have seconds");

                    var schedule = lottery.FindScheduleAt(drawDate);
                    var remainingToWin = RemainingToWinToAccumulate(schedule, prizeCriteriaId, ruleType, subticket, domain, drawDate);

                if (prizeForCurrentSubticket > remainingToWin)
                {
                        availableInAllDrawDates = false;
                        break;
                }
				}

                if (availableInAllDrawDates)
                {
                    result.AddAvailable(numberAsText);
            }
                else
			{
                    result.AddUnavailable(numberAsText);
                }
            }

			if (result.ExistsAvailable())
			{
				var company = lottery.Company;
                result.Issued = company.EncryptionHelper.Encrypt(now.ToString("MM/dd/yyyy HH:mm:ss"));
			}
            return result;
        }

		internal class NumbersQuotation : Objeto
		{
            internal List<string> AvailableNumbers { get; } = new List<string>();
            internal List<string> UnavailableNumbers { get; } = new List<string>();
            internal string Issued { get; set; }

            internal void AddAvailable(string number)
			{
                if (string.IsNullOrWhiteSpace(number)) throw new ArgumentNullException(nameof(number));

                AvailableNumbers.Add(number);
            }

            internal void AddUnavailable(string number)
            {
                if (string.IsNullOrWhiteSpace(number)) throw new ArgumentNullException(nameof(number));

                UnavailableNumbers.Add(number);
            }

			internal bool ExistsAvailable() => AvailableNumbers.Count > 0;
        }

		internal class SubticketWithAvailable:Objeto
		{
			internal string Number { get; }
			internal decimal Available { get; }
            internal SubticketWithAvailable(string number, decimal available)
            {
                if (string.IsNullOrWhiteSpace(number)) throw new ArgumentNullException(nameof(number));

                Number = number;
                Available = available;
            }
		}

		internal bool IsSubticketExceedingToWin(Schedule schedule, PrizeCriteriaIds prizeCriteriaId, RuleType ruleType, SubTicket<IPick> subticket, DateTime drawDate, Domain domain)
        {
			if (drawDate == default(DateTime)) throw new GameEngineException("Date cannot have default value");
			if (drawDate.Second != 0 || drawDate.Millisecond != 0) throw new GameEngineException("Date of Lottery drawings can not have seconds");
			if (subticket == null) throw new ArgumentNullException(nameof(subticket));
			if (domain == null) throw new ArgumentNullException(nameof(domain));
			if (!ExistsToWinPerSubtickets(drawDate)) return false;

			var toWinPerSubtickets = GetAccumulatedToWinPerSubtickets(drawDate);
			var result = toWinPerSubtickets.IsSubticketExceedingToWin(schedule, prizeCriteriaId, ruleType, subticket, domain);
			return result;
		}

		internal IEnumerable<SubTicket<IPick>> SubticketsExceedingToWin(PrizesPicks prizes, Schedule schedule, DateTime drawDate, IEnumerable<SubTicket<IPick>> subtickets, decimal betAmount, TicketType ticketType, 
			Domain domain, ToWinAccumulator toWinAccumulator, bool withFireball, bool applyToleranceFactor)
		{
			if (drawDate == default(DateTime)) throw new GameEngineException("Date cannot have default value");
			if (drawDate.Second != 0 || drawDate.Millisecond != 0) throw new GameEngineException("Date of Lottery drawings can not have seconds");
			if (subtickets == null) throw new ArgumentNullException(nameof(subtickets));
			if (betAmount <= 0) throw new GameEngineException($"{nameof(betAmount)} {betAmount} must be greater than 0");
			if (toWinAccumulator == null) throw new ArgumentNullException(nameof(toWinAccumulator));
			if (domain == null) throw new ArgumentNullException(nameof(domain));

			IEnumerable<SubTicket<IPick>> subticketsToAccumulate = Enumerable.Empty<SubTicket<IPick>>();
			if (ticketType == TicketType.P2S || ticketType == TicketType.P3S || ticketType == TicketType.P4S || ticketType == TicketType.P5S) subticketsToAccumulate = subtickets;
			else if (ticketType == TicketType.P2B || ticketType == TicketType.P3B || ticketType == TicketType.P4B || ticketType == TicketType.P5B)
			{
				foreach (var subticket in subtickets)
				{
					var picks = subticket.Permute();

					foreach (var pick in picks)
					{
						if (pick.Count != 1) throw new GameEngineException("Pick to accumulate risk only can have one number");

						subticketsToAccumulate = subticketsToAccumulate.Append(pick.SubTickets().First());
					}
				}
			}
			else throw new GameEngineException($"{nameof(ticketType)} {ticketType} is not valid to accumulate risk");

			if (!ExistsToWinPerSubtickets(drawDate))
			{
				var maxToWin = riskPerLotteries.GetMaximumToWin(domain, schedule.Lottery.PickNumber);
                var ruleType = RuleTypeFromTicketType(ticketType);
				var accumulatedToWinInSubticketsByEnding = 0m;
                foreach (var subticket in subticketsToAccumulate)
				{
                    var prizeForCurrentSubticket = CalculatePrizeToWin(subticket, betAmount, ticketType, domain);
					if (schedule.Lottery.RootLottery is LotteryTriz lotteryTriz) accumulatedToWinInSubticketsByEnding = CalculateAccumulatedToWinInSubticketsByEnding(subticket, drawDate, lotteryTriz);
                    if (withFireball) prizeForCurrentSubticket /= 2;
					if (applyToleranceFactor) prizeForCurrentSubticket *= riskPerLotteries.Risks.Risk.RiskToleranceFactor;

                    var number = subticket.ToInt64();
					var accumulatedToWinInPurchase = toWinAccumulator.GetOrCreateCurrentToWinFor(lottery, drawDate, number, subticket.Length);
                    var prizeCriteriaId = Disincentives.WayOfSubticket(ticketType, subticket);
                    var disincentivatedRisk = riskPerLotteries.Risks.Disincentives.ApplyTo(schedule, prizeCriteriaId, ruleType, subticket.Length, maxToWin);
					if (prizeForCurrentSubticket + accumulatedToWinInSubticketsByEnding + accumulatedToWinInPurchase.Amount > disincentivatedRisk)
						return subtickets;
					accumulatedToWinInPurchase.AddRisk(prizeForCurrentSubticket);
				}
				return Enumerable.Empty<SubTicket<IPick>>();
			}

			var toWinPerSubtickets = GetAccumulatedToWinPerSubtickets(drawDate);
			var result = toWinPerSubtickets.SubticketsExceedingToWin(schedule, schedule.Lottery.PickNumber, drawDate, subtickets, betAmount, ticketType, domain, toWinAccumulator, withFireball, applyToleranceFactor);
			return result;
		}

		private decimal CalculateAccumulatedToWinInSubticketsByEnding(SubTicket<IPick> subticket, DateTime drawDate, LotteryTriz lotteryTriz)
		{
            var subticketsByEnding = SubTicket<IPick>.GenerateSubticketsByEnding(subticket.Number);
            var accumulatedToWin = 0m;
            foreach (var subticketByEnding in subticketsByEnding)
            {
				var internalLottery = lotteryTriz.GetLotteryPick(subticketByEnding.Length);
                var riskPerLottery = riskPerLotteries.GetRiskPerLottery(subticketByEnding.Length, internalLottery);
                accumulatedToWin += riskPerLottery.AccumulatedToWinForNumber(subticketByEnding, drawDate);
            }
            return accumulatedToWin;
        }

		public static RuleType RuleTypeFromTicketType(TicketType ticketType)
		{
			switch (ticketType)
			{
				case TicketType.P2S:
				case TicketType.P3S:
				case TicketType.P4S:
				case TicketType.P5S:
					return RuleType.Straight;
				case TicketType.P2B:
				case TicketType.P3B:
				case TicketType.P4B:
				case TicketType.P5B:
					return RuleType.Boxed;
				default:
					throw new GameEngineException($"There is no implementation for {nameof(ticketType)} {ticketType}");
			}
		}

        internal decimal CalculatePrizeToWin(SubTicket<IPick> subticket, decimal betAmount, TicketType ticketType, Domain domain)
		{
			var prizes = lottery.PicksLotteryGame.RiskProfiles.GetRiskProfile(domain).Prizes;
			var prizeCriteria = prizes.WayOfSubticket(ticketType, subticket);
			decimal prize = prizes.Prize(ticketType, prizeCriteria);
			prize *= betAmount;
			return prize;
		}

		internal void RemoveAccumulatedToWinAt(DateTime drawDate)
        {
			if (drawDate == default(DateTime)) throw new GameEngineException("Date cannot have default value");
			if (drawDate.Second != 0 || drawDate.Millisecond != 0) throw new GameEngineException("Date of Lottery drawings can not have seconds");

			accumulatedToWinPerSubticketsPerDrawDate.Remove(drawDate);
		}

		Dictionary<DateTime, NumbersTracker> numbersTrackers;
		Dictionary<Player, List<DateTime>> playersTrackingDrawDates;
		SemaphoreSlim _semaphoreForTracking = new SemaphoreSlim(1, 1);
		internal bool ExistsNumbersTracker(DateTime drawDate)
		{
			if (numbersTrackers == null) return false;
			var result = numbersTrackers.ContainsKey(drawDate);
			return result;
		}
		internal bool ExistsPlayerTrackingNumbers(Player player)
		{
			if (playersTrackingDrawDates == null) return false;
			var result = playersTrackingDrawDates.ContainsKey(player);
			return result;
		}

		NumbersTracker NumbersTrackerPerDrawDate(DateTime drawDate)
		{
			var result = numbersTrackers.GetValueOrDefault(drawDate);
			if (result == null) throw new GameEngineException($"No {nameof(NumbersTracker)} registered for date '{drawDate}'");
			return result;
		}
		IEnumerable<DateTime> DrawDatesTrackedPerPlayer(Player player)
		{
			var result = playersTrackingDrawDates.GetValueOrDefault(player);
			if (result == null) throw new GameEngineException($"No {nameof(player)} '{nameof(player.AccountNumber)}' found tracking numbers");
			return result;
		}

		internal void Track(SubTicket<IPick> subticket, DateTime drawDate, Player player)
		{
			if (drawDate == default(DateTime)) throw new GameEngineException("Date cannot have default value");
			if (drawDate.Second != 0 || drawDate.Millisecond != 0) throw new GameEngineException("Date of Lottery drawings can not have seconds");
			if (subticket == null) throw new ArgumentNullException(nameof(subticket));
			if (player == null) throw new ArgumentNullException(nameof(player));
			if (!lottery.AnyScheduleHasHour(drawDate.Hour, drawDate.Minute)) throw new GameEngineException($"{nameof(drawDate)} {drawDate} does not belong to lottery {lottery.State.Abbreviation}");

			_semaphoreForTracking.Wait();
			try
			{
				if (numbersTrackers == null) numbersTrackers = new Dictionary<DateTime, NumbersTracker>();
				if (playersTrackingDrawDates == null) playersTrackingDrawDates = new Dictionary<Player, List<DateTime>>();

				NumbersTracker numbersTracker;
				if (ExistsNumbersTracker(drawDate))
				{
					numbersTracker = NumbersTrackerPerDrawDate(drawDate);
				}
				else
				{
					numbersTracker = new NumbersTracker(lottery, drawDate);
					numbersTrackers.Add(drawDate, numbersTracker);
				}
				numbersTracker.Track(subticket, player);

				if (ExistsPlayerTrackingNumbers(player))
				{
					playersTrackingDrawDates[player].Add(drawDate);
				}
				else
				{
					playersTrackingDrawDates.Add(player, new List<DateTime>() { drawDate });
				}
			}
			finally
			{
				_semaphoreForTracking.Release();
			}
		}

		internal void ForgetPreviousTracking(Player player)
        {
			if (player == null) throw new ArgumentNullException(nameof(player));
			_semaphoreForTracking.Wait();
			try
			{
				if (ExistsPlayerTrackingNumbers(player))
				{
					var drawDates = DrawDatesTrackedPerPlayer(player);
					foreach (var drawDate in drawDates)
					{
						if (ExistsNumbersTracker(drawDate))
						{
							var numbersTracker = NumbersTrackerPerDrawDate(drawDate);
							if (numbersTracker.IsLastPlayer(player)) numbersTrackers.Remove(drawDate);
							else numbersTracker.Forget(player);
						}
					}
					playersTrackingDrawDates.Remove(player);
				}
			}
			finally
			{
				_semaphoreForTracking.Release();
			}
		}
		internal void ForgetNumbersTracker(DateTime drawDate)
        {
			_semaphoreForTracking.Wait();
			try
			{
				if (ExistsNumbersTracker(drawDate))
				{
					var numbersTracker = NumbersTrackerPerDrawDate(drawDate);
					var players = numbersTracker.TrackingPlayers();
					foreach (var player in players)
					{
						if (ExistsPlayerTrackingNumbers(player))
						{
							var isLastDrawDate = playersTrackingDrawDates[player].Count == 1 && playersTrackingDrawDates[player].First() == drawDate;
							if (isLastDrawDate) playersTrackingDrawDates.Remove(player);
							else playersTrackingDrawDates[player].Remove(drawDate);
						}
					}
					numbersTrackers.Remove(drawDate);
				}
			}
			finally
			{
				_semaphoreForTracking.Release();
			}
		}

		internal void NotifyRiskChange(DateTime now, SubTicket<IPick> subticket, decimal risk, DateTime drawDate)
        {
			if (ExistsNumbersTracker(drawDate))
			{
				var numbersTracker = NumbersTrackerPerDrawDate(drawDate);
				if (numbersTracker.IsNumberTracked(subticket)) numbersTracker.NotifyRiskChange(now, subticket, risk);
			}
		}

		internal void CloneFrom(RiskPerLottery riskPerLottery)
		{
            if (riskPerLottery == null) throw new ArgumentNullException(nameof(riskPerLottery));

            foreach (var accumulatedToWinPerSubticketsPerDrawDatePair in riskPerLottery.accumulatedToWinPerSubticketsPerDrawDate)
			{
                var drawDate = accumulatedToWinPerSubticketsPerDrawDatePair.Key;
                var accumulatedToWinPerSubtickets = accumulatedToWinPerSubticketsPerDrawDatePair.Value;
                var newAccumulatedToWinPerSubtickets = new ToWinPerSubtickets(lottery, drawDate);
                newAccumulatedToWinPerSubtickets.CloneFrom(accumulatedToWinPerSubtickets);
                accumulatedToWinPerSubticketsPerDrawDate.Add(drawDate, newAccumulatedToWinPerSubtickets);
            }
        }
	}

}
