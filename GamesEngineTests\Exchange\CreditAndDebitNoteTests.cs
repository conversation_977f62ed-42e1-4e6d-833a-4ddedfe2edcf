﻿using GamesEngine.Business;
using GamesEngine.Domains;
using GamesEngine.Exchange;
using GamesEngine.Exchange.Batch;
using GamesEngine.Finance;
using GamesEngine.PurchaseOrders;
using GamesEngineTests.Custodian;
using GamesEngineTests.Unit_Tests.Games.Lotto;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using System;
using System.Collections.Generic;
using System.Linq;
using static GamesEngine.Business.WholePaymentProcessor;
using static GamesEngine.Exchange.TransactionCompleted;

namespace GamesEngineTests.Exchange
{
	[TestClass]
	public class CreditAndDebitNoteTests
    {
		[TestMethod]
		public void CreditNoteApproved()
		{
			bool itIsThePresent = false;
			DateTime today = DateTime.Now;
			Company company = new Company();
			CurrenciesTest.AddCurrencies();
			company.Sales.CreateStore(1, "Test Store").MakeCurrent();
			string ced="abc123";string accountNumber = "B15461";Customer customer=company.GetOrCreateCustomerById(accountNumber);customer.Identifier=ced;
			CustomerAccount dollarAccount = customer.CreateNewAccountFor(Coinage.Coin(Currencies.CODES.USD));
			Marketplace marketplace = new Marketplace(company, "CR");
			var a = (GamesEngine.Exchange.Agent)marketplace.AddAgent("A");
			var b = a.AddCashier("Juan");
			string employeeName = "Juan";
			string concept = "concept";
			int referenceId = 1;
			int batchNumber = 1;
			Domain domain = company.Sales.CreateDomain(false, 1, "localhost", PaymentChannels.Agents.INSIDER);
			company.Sales.CurrentStore.Add(domain);
			GuardianTest.LoadProcessors(company);

			#region rate #1
			RegularConversionSpread rate1 = marketplace.CreateConversionSpread(1, today, itIsThePresent, Coinage.Coin(Currencies.CODES.USD), Coinage.Coin(Currencies.CODES.BTC), 9600.01m, 9600.00m, employeeName);
			Assert.AreEqual(rate1, marketplace.ConversionSpreadAt(today, Coinage.Coin(Currencies.CODES.USD), Coinage.Coin(Currencies.CODES.BTC)));
			#endregion

			BatchTransactions juanTransactions = BatchTest.SetInitialSettings(marketplace, today, new Domain[] { domain });
			BatchSet agentBatch = juanTransactions.Batch();
			string agentPath = a.FullName;

			int id = marketplace.NewTransationNumber();
			var expectedResult = new Dollar(10m);
			var transaction1 = marketplace.From(id, dollarAccount, agentPath, "Juan", domain).CreditNote(today, itIsThePresent, expectedResult, employeeName, concept, referenceId, batchNumber, (PaymentProcessor)marketplace.Company.System.PaymentProcessor.First(), 1);
			List<BatchMovement> movements = juanTransactions.Movements().ToList();
			Assert.AreEqual(1, movements.Count, "Lock and credit movements were expected.");
			Assert.AreEqual(expectedResult, movements[0].Amount);
			Assert.AreEqual(new Dollar(4999990m), juanTransactions.Available(Coinage.Coin(Currencies.CODES.USD)), "No available modification are expected.");
			Assert.AreEqual(expectedResult, juanTransactions.Locked(Coinage.Coin(Currencies.CODES.USD)), $"{expectedResult} to customer is not the same;");

			var journalEntryNumber = marketplace.NewJournalEntryNumber();
			TransactionCompleted result1 = transaction1.Approve(today, itIsThePresent, employeeName, journalEntryNumber);
			Assert.IsFalse(marketplace.ExistsDraftTransaction(juanTransactions, id));

			TransactionResult resultStats1 = (TransactionResult)result1.Result;
			Assert.AreEqual(new Dollar(0m).Value, resultStats1.Comission.Value);
			Assert.AreEqual(expectedResult.Value, resultStats1.Gross.Value);
			Assert.AreEqual(expectedResult.Value, resultStats1.Net.Value);
			Assert.AreEqual(expectedResult.Value, resultStats1.Amount.Value);

			movements = juanTransactions.Movements().ToList();
			Assert.AreEqual(2, movements.Count, "Unlock and credit movements were expected.");
			Assert.AreEqual(expectedResult, movements[1].Amount);
			Assert.AreEqual(new Dollar(5000000m - expectedResult.Value), juanTransactions.Available(Coinage.Coin(Currencies.CODES.USD)), "4999990m + amount to customer is not the same;");
			Assert.AreEqual(new Dollar(0m), juanTransactions.Locked(Coinage.Coin(Currencies.CODES.USD)), "No available modification are expected.");
		}

		[TestMethod]
		public void DebitNoteApproved()
		{
			bool itIsThePresent = false;
			DateTime today = DateTime.Now;
			Company company = new Company();
			CurrenciesTest.AddCurrencies();
			company.Sales.CreateStore(1, "Test Store").MakeCurrent();
			string ced="abc123";string accountNumber = "B15461";Customer customer=company.GetOrCreateCustomerById(accountNumber);customer.Identifier=ced;
			CustomerAccount dollarAccount = customer.CreateNewAccountFor(Coinage.Coin(Currencies.CODES.USD));
			Marketplace marketplace = new Marketplace(company, "CR");
			var a = (GamesEngine.Exchange.Agent)marketplace.AddAgent("A");
			var b = a.AddCashier("Juan");
			string employeeName = "Juan";
			string concept = "concept";
			int referenceId = 1;
			int batchNumber = 1;
			Domain domain = company.Sales.CreateDomain(false, 1, "localhost", PaymentChannels.Agents.INSIDER);
			company.Sales.CurrentStore.Add(domain);
			GuardianTest.LoadProcessors(company);

			#region rate #1
			RegularConversionSpread rate1 = marketplace.CreateConversionSpread(1, today, itIsThePresent, Coinage.Coin(Currencies.CODES.USD), Coinage.Coin(Currencies.CODES.BTC), 9600.01m, 9600.00m, employeeName);
			Assert.AreEqual(rate1, marketplace.ConversionSpreadAt(today, Coinage.Coin(Currencies.CODES.USD), Coinage.Coin(Currencies.CODES.BTC)));
			#endregion

			BatchTransactions juanTransactions = BatchTest.SetInitialSettings(marketplace, today, new Domain[] { domain });
			BatchSet agentBatch = juanTransactions.Batch();
			string agentPath = a.FullName;

			int id = marketplace.NewTransationNumber();
			var expectedResult = new Dollar(10m);
			var transaction1 = marketplace.From(id, dollarAccount, agentPath, "Juan", domain).DebitNote(today, itIsThePresent, new Dollar(10), employeeName, concept, referenceId, batchNumber, (PaymentProcessor)marketplace.Company.System.PaymentProcessor.First(), 1);

			List<BatchMovement> movements = juanTransactions.Movements().ToList();
			Assert.AreEqual(2, movements.Count, "Lock movement was expected.");
			Assert.AreEqual(expectedResult, movements[0].Amount);
			Assert.AreEqual(new Dollar(5000000m), juanTransactions.Available(Coinage.Coin(Currencies.CODES.USD)), "No available modification are expected.");
			Assert.AreEqual(new Dollar(0m), juanTransactions.Locked(Coinage.Coin(Currencies.CODES.USD)), $"{expectedResult} to customer is not the same;");

			var journalEntryNumber = marketplace.NewJournalEntryNumber();
			TransactionCompleted result1 = transaction1.Approve(today, itIsThePresent, employeeName, journalEntryNumber);
			Assert.IsFalse(marketplace.ExistsDraftTransaction(juanTransactions, id));

			TransactionResult resultStats1 = (TransactionResult)result1.Result;
			Assert.AreEqual(new Dollar(0m).Value, resultStats1.Comission.Value);
			Assert.AreEqual(expectedResult.Value, resultStats1.Gross.Value);
			Assert.AreEqual(expectedResult.Value, resultStats1.Net.Value);
			Assert.AreEqual(expectedResult.Value, resultStats1.Amount.Value);

			movements = juanTransactions.Movements().ToList();
			Assert.AreEqual(4, movements.Count, "Unlock movement was expected.");
			Assert.AreEqual(expectedResult, movements[1].Amount);
			Assert.AreEqual(new Dollar(5000000m), juanTransactions.Available(Coinage.Coin(Currencies.CODES.USD)), "5000000m - amount to customer is not the same;");
			Assert.AreEqual(new Dollar(0m), juanTransactions.Locked(Coinage.Coin(Currencies.CODES.USD)), "No available modification are expected.");
		}
	}
}
