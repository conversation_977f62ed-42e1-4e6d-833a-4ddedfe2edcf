﻿using GamesEngine.Business;
using GamesEngine.Gameboards.Lotto;
using GamesEngine.Games.Lotto;
using GamesEngine.Location;
using GamesEngine.Settings;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Unit.Games.Tools;

namespace GamesEngineTests.Unit_Tests.Games.Lotto
{
    [TestClass]
    public class TicketTests
    {
        [TestMethod]
        public void Permute_Pick2()
        {
            Company company = new Company();
            CurrenciesTest.AddCurrencies();
            company.Sales.CreateStore(1, "Test Store").MakeCurrent();
            company.Accounting = new MockAccounting();
            var lotteries = company.Lotto900();
            DateTime now = new DateTime(2021, 9, 17, 10, 0, 0);

            DateTime date = new DateTime(2021, 9, 17, 10, 20, 0);
            State state = lotteries.GetOrCreateState("VA", "Virginia", now, "Bart");
            var lottery = (LotteryPick<Pick2>)lotteries.GetOrCreateLottery(2, state);
            for (int i = 0; i <= 6; i++) lottery.Every(10, 20, i, now, "Bart");
            
            var player = company.GetOrCreateCustomerById("*********").Player;
            var ticket = (TicketPick2Boxed)lottery.BoxedTicket(player, date, false, new string[] { "01" }, Ticket.Selection.MultipleInputSingleAmount, now, 1m, 1, lotteries.Prizes);

            var subtickets = ticket.SubTickets();
            Assert.AreEqual(1, subtickets.Count());
            subtickets = ticket.Permute();
            Assert.AreEqual(2, subtickets.Count());

            ticket = (TicketPick2Boxed)lottery.BoxedTicket(player, date, false, new string[] { "01", "11" }, Ticket.Selection.MultipleInputSingleAmount, now, 1m, 2, lotteries.Prizes);
            subtickets = ticket.SubTickets();
            Assert.AreEqual(2, subtickets.Count());
            subtickets = ticket.Permute();
            Assert.AreEqual(3, subtickets.Count());

            ticket = (TicketPick2Boxed)lottery.BoxedTicket(player, date, false, new string[] { "01", "11", "10" }, Ticket.Selection.MultipleInputSingleAmount, now, 1m, 3, lotteries.Prizes);
            subtickets = ticket.SubTickets();
            Assert.AreEqual(3, subtickets.Count());
            subtickets = ticket.Permute();
            Assert.AreEqual(5, subtickets.Count());
        }

        [TestMethod]
        public void Permute_Pick3()
        {
            Company company = new Company();
            CurrenciesTest.AddCurrencies();
            company.Sales.CreateStore(1, "Test Store").MakeCurrent();
            company.Accounting = new MockAccounting();
            var lotteries = company.Lotto900();
            DateTime now = new DateTime(2021, 9, 17, 10, 0, 0);

            DateTime date = new DateTime(2021, 9, 17, 10, 20, 0);
            State state = lotteries.GetOrCreateState("VA", "Virginia", now, "Bart");
            LotteryPick<Pick3> lottery = (LotteryPick<Pick3>)lotteries.GetOrCreateLottery(3, state);
            for (int i = 0; i <= 6; i++) lottery.Every(10, 20, i, now, "Bart");

            var player = company.GetOrCreateCustomerById("*********").Player;
            var ticket = (TicketPick3Boxed)lottery.BoxedTicket(player, date, false, new string[] { "012" }, Ticket.Selection.MultipleInputSingleAmount, now, 1m, 1, lotteries.Prizes);

            var subtickets = ticket.SubTickets();
            Assert.AreEqual(1, subtickets.Count());
            subtickets = ticket.Permute();
            Assert.AreEqual(6, subtickets.Count());

            ticket = (TicketPick3Boxed)lottery.BoxedTicket(player, date, false, new string[] { "012", "011" }, Ticket.Selection.MultipleInputSingleAmount, now, 1m, 2, lotteries.Prizes);
            subtickets = ticket.SubTickets();
            Assert.AreEqual(2, subtickets.Count());
            subtickets = ticket.Permute();
            Assert.AreEqual(9, subtickets.Count());

            ticket = (TicketPick3Boxed)lottery.BoxedTicket(player, date, false, new string[] { "012", "011", "101" }, Ticket.Selection.MultipleInputSingleAmount, now, 1m, 3, lotteries.Prizes);
            subtickets = ticket.SubTickets();
            Assert.AreEqual(3, subtickets.Count());
            subtickets = ticket.Permute();
            Assert.AreEqual(12, subtickets.Count());
        }

        [TestMethod]
        public void Permute_Pick4()
        {
            Company company = new Company();
            CurrenciesTest.AddCurrencies();
            company.Sales.CreateStore(1, "Test Store").MakeCurrent();
            company.Accounting = new MockAccounting();
            var lotteries = company.Lotto900();
            DateTime now = new DateTime(2021, 9, 17, 10, 0, 0);

            DateTime date = new DateTime(2021, 9, 17, 10, 20, 0);
            State state = lotteries.GetOrCreateState("VA", "Virginia", now, "Bart");
            var lottery = (LotteryPick<Pick4>)lotteries.GetOrCreateLottery(4, state);
            for (int i = 0; i <= 6; i++) lottery.Every(10, 20, i, now, "Bart");

            var player = company.GetOrCreateCustomerById("*********").Player;
            var ticket = (TicketPick4Boxed)lottery.BoxedTicket(player, date, false, new string[] { "0123" }, Ticket.Selection.MultipleInputSingleAmount, now, 1m, 1, lotteries.Prizes);

            var subtickets = ticket.SubTickets();
            Assert.AreEqual(1, subtickets.Count());
            subtickets = ticket.Permute();
            Assert.AreEqual(24, subtickets.Count());

            ticket = (TicketPick4Boxed)lottery.BoxedTicket(player, date, false, new string[] { "0123", "0111" }, Ticket.Selection.MultipleInputSingleAmount, now, 1m, 2, lotteries.Prizes);
            subtickets = ticket.SubTickets();
            Assert.AreEqual(2, subtickets.Count());
            subtickets = ticket.Permute();
            Assert.AreEqual(28, subtickets.Count());

            ticket = (TicketPick4Boxed)lottery.BoxedTicket(player, date, false, new string[] { "0123", "0111", "1011" }, Ticket.Selection.MultipleInputSingleAmount, now, 1m, 3, lotteries.Prizes);
            subtickets = ticket.SubTickets();
            Assert.AreEqual(3, subtickets.Count());
            subtickets = ticket.Permute();
            Assert.AreEqual(32, subtickets.Count());
        }

        [TestMethod]
        public void Permute_Pick5()
        {
            Company company = new Company();
            CurrenciesTest.AddCurrencies();
            company.Sales.CreateStore(1, "Test Store").MakeCurrent();
            company.Accounting = new MockAccounting();
            var lotteries = company.Lotto900();
            DateTime now = new DateTime(2021, 9, 17, 10, 0, 0);

            DateTime date = new DateTime(2021, 9, 17, 10, 20, 0);
            State state = lotteries.GetOrCreateState("VA", "Virginia", now, "Bart");
            var lottery = (LotteryPick<Pick5>)lotteries.GetOrCreateLottery(5, state);
            for (int i = 0; i <= 6; i++) lottery.Every(10, 20, i, now, "Bart");

            var player = company.GetOrCreateCustomerById("*********").Player;
            var ticket = (TicketPick5Boxed)lottery.BoxedTicket(player, date, false, new string[] { "01234" }, Ticket.Selection.MultipleInputSingleAmount, now, 1m, 1, lotteries.Prizes);

            var subtickets = ticket.SubTickets();
            Assert.AreEqual(1, subtickets.Count());
            subtickets = ticket.Permute();
            Assert.AreEqual(120, subtickets.Count());

            ticket = (TicketPick5Boxed)lottery.BoxedTicket(player, date, false, new string[] { "01234", "01111" }, Ticket.Selection.MultipleInputSingleAmount, now, 1m, 2, lotteries.Prizes);
            subtickets = ticket.SubTickets();
            Assert.AreEqual(2, subtickets.Count());
            subtickets = ticket.Permute();
            Assert.AreEqual(125, subtickets.Count());

            ticket = (TicketPick5Boxed)lottery.BoxedTicket(player, date, false, new string[] { "01234", "01111", "10111" }, Ticket.Selection.MultipleInputSingleAmount, now, 1m, 3, lotteries.Prizes);
            subtickets = ticket.SubTickets();
            Assert.AreEqual(3, subtickets.Count());
            subtickets = ticket.Permute();
            Assert.AreEqual(130, subtickets.Count());
        }
    }
}
