﻿using GamesEngine.Business;
using GamesEngine.Domains;
using GamesEngine.Exchange;
using GamesEngine.Gameboards.Lotto;
using GamesEngine.Location;
using GamesEngine.Settings;
using GamesEngine.Time;
using Puppeteer.EventSourcing;
using Puppeteer.EventSourcing.Libraries;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using static GamesEngine.Games.Lotto.PicksLotteryGame;

namespace GamesEngine.Games.Lotto
{
    [Puppet]
    internal class TrizLotteryGame : LotteryGame
    {
        private readonly LotteryTriz triz;
        private readonly LotteryGamesPool lotteryGamesPool;
        internal TrizLotteryGame(LotteryGamesPool lotteryGamesPool) : base(lotteryGamesPool.Company)
        {
            if (lotteryGamesPool == null) throw new ArgumentNullException(nameof(lotteryGamesPool));

            this.lotteryGamesPool = lotteryGamesPool;
            this.triz = new LotteryTriz(lotteryGamesPool, this);
            this.reports = new ReportsTriz(this);
        }

        private readonly ReportsTriz reports;
        internal ReportsTriz Reports
        {
            get
            {
                return reports;
            }
        }

        internal override RiskProfilesLotteries RiskProfiles
        {
            get
            {
                return lotteryGamesPool.RiskProfiles;
            }
        }

        internal override Risks Risks 
        {
            get
            {
                return lotteryGamesPool.Risks;
            }
        }

        internal LotteryTriz GetLottery()
        {
            return triz;
        }

        internal override bool HasDrawsFor(State state)
        {
            if (state == null) throw new ArgumentNullException(nameof(state));

            return this.triz.State == state;
        }

        internal override IEnumerable<Schedule> SchedulesByTimeOf(DateTime now, Domain domain)
        {
            if (now == default(DateTime)) throw new GameEngineException("Date cannot have default value");
            if (domain == null) throw new ArgumentNullException(nameof(domain));

            IEnumerable<Schedule> results = base.SchedulesByTimeOf(now, domain);
            return results.Where(x => x.Lottery is LotteryTriz);
        }

        internal override IEnumerable<Schedule> FinishedAndRegradedSchedulesOf(DateTime date)
        {
            if (date == default(DateTime)) throw new GameEngineException("Date cannot have default value");

            return triz.FinishedAndRegradedSchedulesAt(date);
        }

        internal override IEnumerable<Schedule> PendingAndNoRegradedSchedulesAt(DateTime date)
        {
            if (date == default(DateTime)) throw new GameEngineException("Date cannot have default value");

            IEnumerable<Schedule> schedules = triz.PendingAndNoRegradedSchedulesAt(date);
            return schedules;
        }

        internal override LotteryForPicks GetLottery(int pick, State state)
        {
            if (state == null) throw new ArgumentNullException(nameof(state));
            if (state != triz.State) throw new GameEngineException($"State {state.Abbreviation} is not a valid state for Triz Lottery");

            LotteryForPicks lottery = this.triz.GetLotteryPick(pick);
            return lottery;
        }

        internal bool StateHasDisabledDomain(Domain domain, DateTime date)
        {
            var result = triz.HasDisabledDomain(domain, date);
            return result;
        }

        internal TicketCreatorDelegate GetTicketCreator(TicketType ticketType, bool ending)
        {
            return triz.GetTicketCreator(ticketType, ending);
        }

        internal override IEnumerable<Schedule> SchedulesBy(State state)
        {
            if (state == null) throw new ArgumentNullException(nameof(state));

            if (state != triz.State) throw new GameEngineException($"State {state.Abbreviation} is not the same as Triz Lottery");
            return triz.Schedules;
        }

        internal override IEnumerable<Schedule> SchedulesByState(DateTime date, Domain domain)
        {
            if (date == default(DateTime)) throw new GameEngineException("Date cannot have default value");
            if (domain == null) throw new ArgumentNullException(nameof(domain));

            var schedules = new SortedList<ScheduleKeySortedByState, Schedule>();
            DateTime nextDate;

            foreach (var schedule in triz.AllEnabledSchedules(date, domain))
            {
                nextDate = triz.NextValidDrawDate(schedule, date);
                var scheduleKey = new ScheduleKeySortedByState(nextDate, triz.State.Abbreviation, schedule.Lottery.GameType()+"_"+triz.GetTypeNumberSequence(schedule.Lottery));
                schedules.Add(scheduleKey, schedule);
            }

            var result = schedules.Values.ToList();
            return result;
        }

        internal override IEnumerable<State> StatesWithLotteries()
        {
            var result = new List<State>() { triz.State };
            return result;
        }

        internal override void AddWagerGradedWithProblemAboutIsValidTicket(int ticketNumber, int wagerNumber)
        {
            if (ticketNumber <= 0) throw new GameEngineException($"{nameof(ticketNumber)} must be greater than 0");
            if (wagerNumber <= 0) throw new GameEngineException($"{nameof(wagerNumber)} must be greater than 0");

            var wager = SearchWagerByTicketAndWagerNumber(ticketNumber, wagerNumber);
            if (wagersGradedWithProblemsAboutIsValidTicket == null) wagersGradedWithProblemsAboutIsValidTicket = new List<WagerWithError>();
            wagersGradedWithProblemsAboutIsValidTicket.Add(new WagerWithError(wager, "This wager has an invalid ticketNumber"));
        }

        private TicketWager SearchWagerByTicketAndWagerNumber(int ticketNumber, int wagerNumber)
        {
            var lotteryTickets = this.triz.SearchTicketByNumber(ticketNumber);
            foreach (var ticket in lotteryTickets)
            {
                foreach (var wager in ticket.Wagers)
                {
                    if (wager.WagerNumber == wagerNumber)
                    {
                        return wager;
                    }
                }
            }

            throw new GameEngineException($@"There is no wager with {nameof(ticketNumber)} : {ticketNumber} {nameof(wagerNumber)} : {wagerNumber}");
        }

        internal override bool AnyScheduleHasChanged(IEnumerable<int> picks, IEnumerable<string> statesAbb, IEnumerable<string> hours, IEnumerable<string> withFireBalls)
        {
            if (picks == null || !picks.Any()) throw new ArgumentNullException(nameof(picks));
            if (statesAbb == null || !statesAbb.Any()) throw new ArgumentNullException(nameof(statesAbb));
            if (hours == null || !hours.Any()) throw new ArgumentNullException(nameof(hours));
            if (withFireBalls == null || !withFireBalls.Any()) throw new ArgumentNullException(nameof(withFireBalls));

            string stateAbbreviation = this.triz.State.Abbreviation;
            if (statesAbb.Any(x => x != stateAbbreviation)) throw new GameEngineException($"State {stateAbbreviation} is not valid");

            int index = 0;
            foreach (var pick in picks)
            {
                LotteryForPicks trizLotteryPickItem = this.triz.GetLotteryPick(pick);

                var stateAbb = statesAbb.ElementAt(index);
                var hour = hours.ElementAt(index);

                var drawHour = DateTime.ParseExact(hour, "h:m tt", Integration.CultureInfoEnUS);
                bool anyScheduleHasChanged = trizLotteryPickItem.AnyScheduleHasHour(drawHour.Hour, drawHour.Minute);
                if (!anyScheduleHasChanged) return true;

                index++;
            }
            return false;
        }

        internal override IEnumerable<LotteryComplete> DrawsToBeConfirmed()
        {
            var result = this.triz.DrawsToBeConfirmed();
            return result;
        }

        internal override bool TryGetScheduleByUniqueId(int uniqueDrawingId, out Schedule result)
        {
            if (uniqueDrawingId <= 0) throw new GameEngineException($"{nameof(uniqueDrawingId)} {uniqueDrawingId} is not valid");

            var schedule = this.triz.TryGetScheduleByUniqueId(uniqueDrawingId, out result);
            return schedule;
        }

        internal override bool ExistsInternalSchedule(int uniqueDrawingId)
        {
            if (uniqueDrawingId <= 0) throw new GameEngineException($"{nameof(uniqueDrawingId)} {uniqueDrawingId} is not valid");

            var result = triz.ExistsScheduleByUniqueId(uniqueDrawingId);
            return result;
        }
    }
}
