﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using System.Text;
using System.Threading.Tasks;
using GamesEngine.Games;
using GamesEngine.Middleware;
using GamesEngine.Middleware.Providers;
using GamesEngine.Middleware.Providers.Bet365;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace LinesETLAPI.Controllers
{
	public class LeagueController : AuthorizeController
	{
		[HttpGet("api/mappings/bets365/leagues")]
        [Authorize(Roles = "devops")]//VERIFY_ROLES
        public async Task<IActionResult> ListLeaguesMappingsAsync()
		{
			Sport sport = Sport.SOCCER;
			Country country = LocalEnvironmentCountries.Instance().FindByCode("us");
			Provider provider = ProvidersCollection.Bet365Mock;

			var resultQry =  await LinesETLAPI.LinesETL.PerformQryAsync(HttpContext, $@"
				{{
					hasCheckpoint = mapper.HasLeagueCheckPointFor({provider},{sport},{country});
					print hasCheckpoint hasCheckpoint;
					if( hasCheckpoint )
					{{
						checkpoint = mapper.LastLeagueCheckPoint({provider},{sport},{country});
						print checkpoint.Id id;
						print checkpoint.Page page;
					}}
				}}
			");
			return Ok(resultQry);
		}

		[HttpGet("api/mappings/betfair/lines")]
        [Authorize(Roles = "devops")]//VERIFY_ROLES
        public async Task<IActionResult> ListLinesAsync()
		{
			Provider provider = ProvidersCollection.BetFair;

			var resultQry = await LinesETLAPI.LinesETL.PerformQryAsync(HttpContext, $@"
				{{
					for(mappings : mapper.LineMappingsForProvider({provider}))
					{{
						print mappings.IdInTheProvider idInTheProvider;
					}}
				}}
				");
			return Ok(resultQry);
		}
	}
}
