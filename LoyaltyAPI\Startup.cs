﻿using ExternalServices;
using GamesEngine.Loyalty;
using GamesEngine.Settings;
using GamesEngineMocks;
using LoyaltyAPI.Controllers;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.HttpOverrides;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Puppeteer.EventSourcing;
using System;
using System.Globalization;
using System.Threading;
using Microsoft.Extensions.Hosting;
using Elastic.Apm.NetCoreAll;
using Elastic.Apm.AspNetCore;
using GamesEngine.Business.Marketing;
using GamesEngine.Business;
using RestSharp;
using System.Diagnostics;
using Microsoft.OpenApi.Models;
using Microsoft.AspNetCore.Mvc;
using GamesEngine;
using GamesEngine.MessageQueuing;
using GamesEngine.Time;

namespace LoyaltyAPI
{
    public class Startup
    {
        public Startup(IWebHostEnvironment env, IConfiguration configuration)
        {
            var builder = new ConfigurationBuilder()
                .SetBasePath(env.ContentRootPath)
                .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true)
                .AddJsonFile("customization/appsettings.accounting.json", optional: true)
                .AddJsonFile("customization/appsettings.security.json", optional: true)
                .AddJsonFile("customization/appsettings.kafka.json", optional: true)
                .AddJsonFile("customization/appsettings.error_emails.json", optional: true)
                .AddJsonFile("customization/appsettings.apm.json", optional: true)
                .AddJsonFile("secrets/appsettings.secrets.json", optional: true);
            Configuration = builder.Build();
            Environment = env;
        }

        public IConfiguration Configuration { get; }
        public IWebHostEnvironment Environment { get; }

        // This method gets called by the runtime. Use this method to add services to the container.
        public void ConfigureServices(IServiceCollection services)
        {
            int minWorker, minIOC;
            ThreadPool.GetMinThreads(out minWorker, out minIOC);
            int maxWorker, maxIOC;
            ThreadPool.GetMaxThreads(out maxWorker, out maxIOC);

            Console.WriteLine($@" minWorker:{minWorker} minIOC:{minIOC} maxWorker:{maxWorker} maxIOC:{maxIOC}");

            services.AddSwaggerGen(c =>
            {
                c.SwaggerDoc("v1", new OpenApiInfo { Title = "Loyalty API", Version = "v1" });
            });

            /*Security*/
            Security.Configure(services, Configuration);
            /*Security*/

            services.AddMvc(options => options.EnableEndpointRouting = false).AddNewtonsoftJson();

            if (string.IsNullOrEmpty(Configuration.GetValue<string>("CashierUrl"))) throw new Exception("CashierUrl its requeried in the appsettings.");
            Settings.CashierUrl = Configuration.GetValue<string>("CashierUrl");

            services.AddSingleton<IHttpContextAccessor, HttpContextAccessor>();
            var biIntegration = Configuration.GetSection("BIIntegration");

            Integration.Configure(KafkaMessage.Prefix.NoTransacction, biIntegration);

            string elkServer = Configuration.GetSection("ELKIntegration").GetValue<string>("elkserver");
            HttpELKClient.Configure(elkServer);

            IRestResponse response = HttpELKClient.GetInstance().CreateTextKeywordMapping(HttpELKClient.SalesIndex, "accountNumber");
            if (200 != (int)response.StatusCode)
            {
                Debug.WriteLine($"Mappings for '{HttpELKClient.SalesIndex}' index already exist.");
            }
            response = HttpELKClient.GetInstance().CreateTextKeywordMapping(HttpELKClient.LoyaltyIndex, "accountNumber");
            if (200 != (int)response.StatusCode)
            {
                Debug.WriteLine($"Mappings for '{HttpELKClient.LoyaltyIndex}' index already exist.");
            }

            ErrorsSender.Configure(Configuration.GetSection("ErrorsSender"), Environment.IsProduction());

            Integration.ConfigureAPM(Configuration);

            var messageOriginId = Configuration.GetValue<string>("MessageOriginId");
            if (string.IsNullOrEmpty(messageOriginId) && !int.TryParse(messageOriginId, out _)) throw new Exception("MessageOriginId its requeried in the appsettings.");
            MessageCatalog.AssingMessageOriginId(Convert.ToInt32(messageOriginId));
        }

        // This method gets called by the runtime. Use this method to configure the HTTP request pipeline.
        public void Configure(IApplicationBuilder app, IWebHostEnvironment env)
        {
            if (Integration.UseAPM)
            {
                app.UseElasticApm(Configuration);
            }
            if (env.IsDevelopment())
            {
                app.UseDeveloperExceptionPage();
                app.UseSwagger();
                app.UseSwaggerUI(c =>
                {
                    c.SwaggerEndpoint("/swagger/v1/swagger.json", "Loyalty API V1");
                    c.RoutePrefix = string.Empty;
                });
            }
            else
            {
                if (String.IsNullOrEmpty(Integration.Db.DBSelected))
                {
                    throw new Exception("Db configuration its required.");
                }
                if (!Integration.UseKafka)
                {
                    throw new Exception("This project must use kafka integration to show data.");
                }
            }

            TimeZoneConverter.Instance.Configure(Configuration);

            var sectionDairy = Configuration.GetSection("DBDairy");
            Integration.MySQL = sectionDairy.GetSection("ConnectionStrings").GetValue<string>("MySQL");
            Integration.SqlServer = sectionDairy.GetSection("ConnectionStrings").GetValue<string>("SQLServer");
            Integration.DbSelected = sectionDairy.GetValue<string>("DBSelected");
            var scriptBeforeRecovering = Configuration.GetValue<string>("ActionsBeforeRecovering");
            Settings.WebProfanityKey = Configuration.GetValue<string>("WebProfanityKey");
            Settings.WebProfanityUrl = Configuration.GetValue<string>("WebProfanityUrl");
            var isFollower = Configuration.GetValue<bool>("IsFollower"); ;

            DBDairy dbDairy = new DBDairy();
            dbDairy.DBSelected = Integration.DbSelected;
            Integration.DbDairy = dbDairy;

            if (!isFollower)
            {
                if (Integration.DbSelected == DatabaseType.MySQL.ToString())
                {
                    LoyaltyAPI.Loyalty.EventSourcingStorage(DatabaseType.MySQL, Integration.MySQL, scriptBeforeRecovering, AccountingSettings.NeedsUniqueIdentifierForPaymentHub);
                }
                else if (Integration.DbSelected == DatabaseType.SQLServer.ToString())
                {
                    LoyaltyAPI.Loyalty.EventSourcingStorage(DatabaseType.SQLServer, Integration.SqlServer, scriptBeforeRecovering, AccountingSettings.NeedsUniqueIdentifierForPaymentHub);
                }
                else if (!String.IsNullOrWhiteSpace(Integration.DbSelected))
                {
                    throw new Exception($"There is no connection for {Integration.DbSelected}");
                }
                else
                {

                    Integration.Kafka.OffSetResetToLatest();
                    //Loyalty.LoadFrom(LottoMocks.Loyalty());
                    //Loyalty.LoadFrom(MarchMadnessMocks.Loyalty());

                    var DOTNET_RUNNING_IN_CONTAINER = bool.Parse(System.Environment.GetEnvironmentVariable("DOTNET_RUNNING_IN_CONTAINER") ?? "false");
                    if (DOTNET_RUNNING_IN_CONTAINER)
                        LoyaltyAPI.Loyalty.EventSourcingStorage(DatabaseType.MySQL, Integration.MySQL, scriptBeforeRecovering, AccountingSettings.NeedsUniqueIdentifierForPaymentHub);

                    LoyaltyMocks.Loyalty(LoyaltyAPI.Loyalty.Actor);
                }

            }
            /*Security*/
            app.UseAuthentication();
            /*Security*/

            app.UseMvc();

            if (!isFollower)
            {
                NotificationStorage storage = null;
                if (Integration.Db.DBSelected == HistoricalDatabaseType.MySQL.ToString())
                {
                    storage = new NotificationStorage(HistoricalDatabaseType.MySQL, Integration.Db.MySQL);
                }
                else if (Integration.Db.DBSelected == HistoricalDatabaseType.SQLServer.ToString())
                {
                    storage = new NotificationStorage(HistoricalDatabaseType.SQLServer, Integration.Db.SQLServer);
                }
                else if (!String.IsNullOrWhiteSpace(Integration.Db.DBSelected))
                {
                    throw new Exception($"There is no connection for {Integration.Db.DBSelected}");
                }

                if (Integration.UseKafka)
                {
                    if (String.IsNullOrEmpty(Integration.Db.DBSelected))
                    {
                        throw new Exception("Db configuration its required.");
                    }

                    var marketingStorage = Settings.GetAMarketingStorageInstance();
                    new LoyaltyController().CreateConsumerForTopics(marketingStorage);
                }

                ForwardedHeadersOptions options = new ForwardedHeadersOptions();
                options.ForwardedHeaders = ForwardedHeaders.XForwardedFor;
                app.UseForwardedHeaders(options);

                var result = LoyaltyAPI.Loyalty.PerformQry($@"
                    {{
                        success = company.System.PaymentProcessor.Load(false, Now, customSettings);
                        print success success;
                    }}
                    ");
                if (!(result is OkObjectResult)) throw new GameEngineException("Processor were not loaded;");

                // Grafrana settings, port, host and token using Configuration.GetValue
                string port = Configuration.GetValue<string>("GrafanaConfiguration:PORT");
                string hostname = Configuration.GetValue<string>("GrafanaConfiguration:HOST");
                string token = Configuration.GetValue<string>("GrafanaConfiguration:TOKEN");
                LoyaltyAPI.Loyalty.PerformQry($@"
                {{
                    grafanaService = company.GrafanaExternalAPI;
                    grafanaService.Configure('{hostname}', '{port}', '{token}');
                }}
                ");
            }
        }
    }
}
