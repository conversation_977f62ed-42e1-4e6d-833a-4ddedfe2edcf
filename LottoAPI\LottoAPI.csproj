﻿<Project Sdk="Microsoft.NET.Sdk.Web">
  <PropertyGroup>
    <TargetFramework>net5.0</TargetFramework>
    <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
    <DockerComposeProjectPath>..\docker-compose.dcproj</DockerComposeProjectPath>
  </PropertyGroup>
  <ItemGroup>
    <Compile Include="..\ExchangeAPI\Controllers\CatalogConsumer.cs" Link="Controllers\CatalogConsumer.cs" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="Elastic.Apm.NetCoreAll" Version="1.8.1" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="3.1.7" />
    <PackageReference Include="log4net" Version="2.0.8" />
    <PackageReference Include="Microsoft.AspNetCore.Http" Version="2.2.2" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.NewtonsoftJson" Version="3.1.7" />
    <PackageReference Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" Version="1.14.0" />
    <PackageReference Include="Solnet.KeyStore" Version="0.6.0" />
    <PackageReference Include="Solnet.Rpc" Version="0.6.0" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="5.6.3" />
    <PackageReference Include="Microsoft.OpenApi" Version="1.2.3" />
    <PackageReference Include="Swashbuckle.AspNetCore.Filters" Version="6.1.0" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\GamesEngineMocks\GamesEngineMocks.csproj" />
    <ProjectReference Include="..\GamesEngine\GamesEngine.csproj" />
  </ItemGroup>
  <ItemGroup>
    <Reference Include="Guardian">
      <HintPath>Guardian.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Content Update="appsettings.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Update="solanaKeyPair.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
  </ItemGroup>
</Project>