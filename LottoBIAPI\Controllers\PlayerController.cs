﻿using Microsoft.AspNetCore.Mvc;
using System;
using System.Globalization;
using System.Runtime.Serialization;
using System.Text;
using System.Collections.Generic;
using GamesEngine;
using Microsoft.AspNetCore.Authorization;
using System.Threading.Tasks;

namespace LottoBIAPI.Controllers
{
    public class PlayerController : AuthorizeController
    {
        private string ScriptForMyPlayedTickets()
        {
            var script = $@"
                print myWinnerTickets.Count()+myLoserTickets.Count()+myNoActionTickets.Count() totalTickets;
                print myWinnerTicketsByPrizeWithTotalSubtickets.TotalSubtickets() totalWinnerSubtickets;
                print myLoserTicketsByPrizeWithTotalSubtickets.TotalSubtickets() totalLoserSubtickets;
                print myNoActionTicketsByPrizeWithTotalSubtickets.TotalSubtickets() totalNoActionSubtickets;
                print myWinnerTicketsByPrizeWithTotalSubtickets.TotalSubtickets()+myLoserTicketsByPrizeWithTotalSubtickets.TotalSubtickets()+myNoActionTicketsByPrizeWithTotalSubtickets.TotalSubtickets() totalSubtickets;
                for (winnerTickets:myWinnerTickets)
                {{
                    ticketByPrize = winnerTickets;
                    ticket = winnerTickets.WinnerInfo;
                    print ticketByPrize.AsString() numbers;
                    print ticketByPrize.Count totalSubtickets;
                    print ticket.GameType() gameType;
                    drawDate = ticket.DrawDate();
                    print drawDate.MMddyyyy_hhmmss() drawDate;
                    print drawDate.MMddyyyyOrTodayOrTomorrow(Now) drawDateFormatted;
                    hour = drawDate.HHmmAMPM();
                    print hour hour;
                    state = ticket.StateAbb;
                    print state state;
                    print ticket.DrawingName description;
                    print ticket.Amount betAmount;
                    print ticketByPrize.TicketAmount() ticketAmount;
                    print ticketByPrize.PrizeToWinForOneDollar prizeToWinForOneDollar;
                    print ticketByPrize.WonAmount() prize;
                    gameTypeLottery = ticket.GameTypeLottery();
                    print ticket.Draw winnerNumber;
                    print ticket.SelectionModeAsString() selectionMode;
                    print ticket.GradingAsString() gradingStatus;
                    print ticketByPrize.IsWinner() isWinner;
                    numbers = ticketByPrize.AsStringWithoutID();
                    isFavorite = favorites.IsFavorite(numbers, hour, state);
                    print isFavorite isFavorite;
                    if (isFavorite)
                    {{
                        print favorites.Id(numbers, hour, state) favoriteId;
                    }}
                    print ticket.TicketNumber ticketNumber;
                    print ticket.Creation creationDate;
                    print ticket.WasCreatedByPattern() wasCreatedByPattern;
                    isPowerball = ticket.IsPowerball();
                    if (isPowerball)
                    {{
                        print ticket.PowerplayCost() powerplayCost;
                    }}
                    print ticket.WasPurchasedForFree wasPurchasedWithLR;
                }}
                for (loserTickets:myLoserTickets)
                {{
                    ticketByPrize = loserTickets;
                    ticket = loserTickets.LoserInfo;
                    print ticketByPrize.AsString() numbers;
                    print ticketByPrize.Count totalSubtickets;
                    print ticket.GameType() gameType;
                    drawDate = ticket.DrawDate();
                    print drawDate.MMddyyyy_hhmmss() drawDate;
                    print drawDate.MMddyyyyOrTodayOrTomorrow(Now) drawDateFormatted;
                    hour = drawDate.HHmmAMPM();
                    print hour hour;
                    state = ticket.StateAbb;
                    print state state;
                    print ticket.DrawingName description;
                    print ticket.Amount betAmount;
                    print ticketByPrize.TicketAmount() ticketAmount;
                    print ticketByPrize.Prize prizeToWinForOneDollar;
                    print 0 prize;
                    gameTypeLottery = ticket.GameTypeLottery();
                    print ticket.Draw winnerNumber;
                    print ticket.SelectionModeAsString() selectionMode;
                    print ticket.GradingAsString() gradingStatus;
                    print false isWinner;
                    numbers = ticketByPrize.AsStringWithoutID();
                    isFavorite = favorites.IsFavorite(numbers, hour, state);
                    print isFavorite isFavorite;
                    if (isFavorite)
                    {{
                        print favorites.Id(numbers, hour, state) favoriteId;
                    }}
                    print ticket.TicketNumber ticketNumber;
					print ticket.Creation creationDate;
                    print ticket.WasCreatedByPattern() wasCreatedByPattern;
                    isPowerball = ticket.IsPowerball();
                    if (isPowerball)
                    {{
                        print ticket.PowerplayCost() powerplayCost;
                    }}
                    print ticket.WasPurchasedForFree wasPurchasedWithLR;
                }}
                for (noActionTickets:myNoActionTickets)
                {{
                    ticketByPrize = noActionTickets;
                    ticket = noActionTickets.NoActionInfo;
                    print ticketByPrize.AsString() numbers;
                    print ticketByPrize.Count totalSubtickets;
                    print ticket.GameType() gameType;
                    drawDate = ticket.DrawDate();
                    print drawDate.MMddyyyy_hhmmss() drawDate;
                    print drawDate.MMddyyyyOrTodayOrTomorrow(Now) drawDateFormatted;
                    hour = drawDate.HHmmAMPM();
                    print hour hour;
                    state = ticket.StateAbb;
                    print state state;
                    print ticket.DrawingName description;
                    print ticket.Amount betAmount;
                    print ticketByPrize.TicketAmount() ticketAmount;
                    print ticketByPrize.Prize prizeToWinForOneDollar;
                    print 0 prize;
                    print ticket.SelectionModeAsString() selectionMode;
                    print ticket.GradingAsString() gradingStatus;
                    print false isWinner;
                    numbers = ticketByPrize.AsStringWithoutID();
                    isFavorite = favorites.IsFavorite(numbers, hour, state);
                    print isFavorite isFavorite;
                    if (isFavorite)
                    {{
                        print favorites.Id(numbers, hour, state) favoriteId;
                    }}
                    print ticket.TicketNumber ticketNumber;
                    print ticket.Creation creationDate;
                    print ticket.WasCreatedByPattern() wasCreatedByPattern;
                    isPowerball = ticket.IsPowerball();
                    if (isPowerball)
                    {{
                        print ticket.PowerplayCost() powerplayCost;
                    }}
                    print false wasPurchasedWithLR;
                }}
                ";
            return script;
        }

        private string ScriptToStoreFavorites(Favorite[] favorites)
        {
            StringBuilder script = new StringBuilder();
            script.Append("favorites=FavoriteChecker();");
            if (favorites != null) foreach (var favorite in favorites)
            {
                if (favorite.Draws != null)
                {
					script.Append("favorites");
					foreach (var draw in favorite.Draws)
                    {
                        script.Append($".AddSchedule('{favorite.Id}','{draw.Hour}','{draw.State}')");
                    }
					script.Append(';');
                }
                if (favorite.Numbers == null)
                {
                    throw new Exception("Favorites logic must has a least one number but it is null.");
                }
				
				bool isTheFirstTime = true;

				foreach (var number in favorite.Numbers)
                {
					if (isTheFirstTime)
					{
						script.Append($"favorites.AddNumber('{favorite.Id}', '");
					}

					if (!isTheFirstTime) script.Append('-');
					script.Append(number.Numbers);
					isTheFirstTime = false;
                }

				if(!isTheFirstTime)	script.Append($"');");
            }
            return script.ToString();
        }

        [HttpGet("api/player/tickets/recent")]
        [Authorize(Roles = "player")]
        public async Task<IActionResult> MyRecentTicketsAsync(DateTime startDate)
        {
            if (startDate == default(DateTime)) return BadRequest($"{nameof(startDate)} is required");

            var stringStartDate = $"{startDate.Month}/{startDate.Day}/{startDate.Year} {startDate.Hour}:{startDate.Minute}:{startDate.Second}";
            string playerId = Validator.StringEscape(Security.PlayerId(User));
            var result = await LottoBIAPI.LottoBI.PerformQryAsync(HttpContext, $@"
            {{
                player = company.Players.SearchPlayer('{playerId}');
                myWinnerTicketsByPrize = player.PlayerLottoReports.MyWinnerTicketsBetween({stringStartDate}, Now);
                for (winnerTickets:myWinnerTicketsByPrize)
                {{
                    ticketByPrize = winnerTickets;
                    ticket = winnerTickets.FirstTicket;
                    print ticket.GameType() gameType;
                    drawDate = ticket.DrawDate();
                    print drawDate.MMddyyyy_hhmmss() drawDate;
                    print drawDate.MMddyyyyOrTodayOrTomorrow(Now) drawDateFormatted;
                    print drawDate.HHmmAMPM() hour;
                    print ticket.StateAbb state;
                    print ticket.DrawingName description;
                    print ticket.BelongsToFireBallDraw withFireBall;
                    if (ticket.BelongsToFireBallDraw)
                    {{
                        print ticket.Fireball fireBall;
                    }}
                    print ticket.TypeNumberSequenceAsText() position;
                    print ticket.Amount betAmount;
                    print ticket.AmountFormatted betAmountFormatted;
                    print ticketByPrize.TicketAmount() ticketAmount;
                    print ticketByPrize.TicketAmountFormatted() ticketAmountFormatted;
                    print ticketByPrize.PrizeToWinForOneDollar prizeToWinForOneDollar;
                    print ticketByPrize.WonAmount() prize;
                    print ticketByPrize.WonAmountFormatted() prizeFormatted;
                    print ticket.Draw winnerNumber;
                    print ticket.WinnerDigits() winnerDigits;
                    print ticket.SelectionModeAsString() selectionMode;
                    print ticket.GradingAsString() gradingStatus;
                    print ticketByPrize.IsWinner() isWinner;
                    print ticket.Creation creationDate;
                    print ticket.WasPurchasedForFree wasPurchasedWithFP;
                    for (numbers:ticketByPrize.Wagers())
                    {{
                        wager = numbers;
                        print wager.SubticketAsString subticket;
                        print wager.FullWagerNumber() fullWagerNumber;
                    }}
                }}

                myLoserTicketsByPrize = player.PlayerLottoReports.MyLoserTicketsBetween({stringStartDate}, Now);
                for (loserTickets:myLoserTicketsByPrize)
                {{
                    ticketByPrize = loserTickets;
                    ticket = loserTickets.FirstTicket;
                    print ticket.GameType() gameType;
                    drawDate = ticket.DrawDate();
                    print drawDate.MMddyyyy_hhmmss() drawDate;
                    print drawDate.MMddyyyyOrTodayOrTomorrow(Now) drawDateFormatted;
                    print drawDate.HHmmAMPM() hour;
                    print ticket.StateAbb state;
                    print ticket.DrawingName description;
                    print ticket.BelongsToFireBallDraw withFireBall;
                    if (ticket.BelongsToFireBallDraw)
                    {{
                        print ticket.Fireball fireBall;
                    }}  
                    print ticket.TypeNumberSequenceAsText() position;
                    print ticket.Amount betAmount;
                    print ticket.AmountFormatted betAmountFormatted;
                    print ticketByPrize.TicketAmount() ticketAmount;
                    print ticketByPrize.TicketAmountFormatted() ticketAmountFormatted;
                    print ticketByPrize.PrizeToWinForOneDollar prizeToWinForOneDollar;
                    print 0 prize;
                    print ticketByPrize.WonAmountFormatted() prizeFormatted;
                    print ticket.Draw winnerNumber;
                    print ticket.WinnerDigits() winnerDigits;
                    print ticket.SelectionModeAsString() selectionMode;
                    print ticket.GradingAsString() gradingStatus;
                    print false isWinner;
                    print ticket.Creation creationDate;
                    print ticket.WasPurchasedForFree wasPurchasedWithFP;
                    for (numbers:ticketByPrize.Wagers())
                    {{
                        wager = numbers;
                        print wager.SubticketAsString subticket;
                        print wager.FullWagerNumber() fullWagerNumber;
                    }}
                }}

                myNoActionTicketsByPrize = player.PlayerLottoReports.MyNoActionTicketsBetween({stringStartDate}, Now);
                for (noActionTickets:myNoActionTicketsByPrize)
                {{
                    ticketByPrize = noActionTickets;
                    ticket = noActionTickets.FirstTicket;
                    print ticket.GameType() gameType;
                    drawDate = ticket.DrawDate();
                    print drawDate.MMddyyyy_hhmmss() drawDate;
                    print drawDate.MMddyyyyOrTodayOrTomorrow(Now) drawDateFormatted;
                    print drawDate.HHmmAMPM() hour;
                    print ticket.StateAbb state;
                    print ticket.DrawingName description;
                    print false withFireBall;
                    print ticket.TypeNumberSequenceAsText() position;
                    print ticket.Amount betAmount;
                    print ticket.AmountFormatted betAmountFormatted;
                    print ticketByPrize.TicketAmount() ticketAmount;
                    print ticketByPrize.TicketAmountFormatted() ticketAmountFormatted;
                    print ticketByPrize.PrizeToWinForOneDollar prizeToWinForOneDollar;
                    print 0 prize;
                    print ticketByPrize.WonAmountFormatted() prizeFormatted;
                    print ticket.SelectionModeAsString() selectionMode;
                    print ticket.GradingAsString() gradingStatus;
                    print false isWinner;
                    print ticket.Creation creationDate;
                    print false wasPurchasedWithFP;
                    for (numbers:ticketByPrize.Wagers())
                    {{
                        wager = numbers;
                        print wager.SubticketAsString subticket;
                        print wager.FullWagerNumber() fullWagerNumber;
                    }}
                }}
            }}
            ");
            return result;
        }

        [HttpGet("api/player/tickets/keno/recent")]
        [Authorize(Roles = "player")]
        public async Task<IActionResult> MyRecentKenoTicketsAsync(DateTime startDate)
        {
            if (startDate == default(DateTime)) return BadRequest($"{nameof(startDate)} is required");

            var stringStartDate = $"{startDate.Month}/{startDate.Day}/{startDate.Year} {startDate.Hour}:{startDate.Minute}:{startDate.Second}";
            string playerId = Validator.StringEscape(Security.PlayerId(User));
            var result = await LottoBIAPI.LottoBI.PerformQryAsync(HttpContext, $@"
            {{
                player = company.Players.SearchPlayer('{playerId}');
                myWinnerTickets = player.PlayerLottoReports.MyWinnerKenoTicketsBetween({stringStartDate}, Now);
                myLoserTickets = player.PlayerLottoReports.MyLoserKenoTicketsBetween({stringStartDate}, Now);
                myNoActionTickets = player.PlayerLottoReports.MyNoActionKenoTicketsBetween({stringStartDate}, Now);
                for (winnerTickets:myWinnerTickets)
                {{
                    ticket = winnerTickets;
                    print ticket.DrawDate.MMddyyyy_hhmmss() drawDate;
                    print ticket.DrawId drawId;
                    print ticket.BetAmount betAmount;
                    print ticket.TicketAmount ticketAmount;
                    print ticket.Prize prize;
                    print ticket.BulleyePrize bulleyePrize;
                    print ticket.NumbersPrize numbersPrize;
                    print ticket.HasMultiplier hasMultiplier;
                    print ticket.HasBulleye hasBulleye;
                    print ticket.Multiplier multiplier;
                    print ticket.Bulleye bulleye;
                    print ticket.Numbers numbers;
                    print ticket.WinnerNumber winnerNumber;
                    print ticket.GradingAsString() gradingStatus;
                    print true isWinner;
                    print ticket.TicketNumber ticketNumber;
                    print ticket.Creation.MMddyyyy_hhmmss() creationDate;
                }}
                for (loserTickets:myLoserTickets)
                {{
                    ticket = loserTickets;
                    print ticket.DrawDate.MMddyyyy_hhmmss() drawDate;
                    print ticket.DrawId drawId;
                    print ticket.BetAmount betAmount;
                    print ticket.TicketAmount ticketAmount;
                    print ticket.Prize prize;
                    print ticket.BulleyePrize bulleyePrize;
                    print ticket.NumbersPrize numbersPrize;
                    print ticket.HasMultiplier hasMultiplier;
                    print ticket.HasBulleye hasBulleye;
                    print ticket.Multiplier multiplier;
                    print ticket.Bulleye bulleye;
                    print ticket.Numbers numbers;
                    print ticket.WinnerNumber winnerNumber;
                    print ticket.GradingAsString() gradingStatus;
                    print false isWinner;
                    print ticket.TicketNumber ticketNumber;
                    print ticket.Creation.MMddyyyy_hhmmss() creationDate;
                }}
                for (noActionTickets:myNoActionTickets)
                {{
                    ticket = noActionTickets;
                    print ticket.DrawDate.MMddyyyy_hhmmss() drawDate;
                    print ticket.DrawId drawId;
                    print ticket.BetAmount betAmount;
                    print ticket.TicketAmount ticketAmount;
                    print ticket.Prize prize;
                    print ticket.BulleyePrize bulleyePrize;
                    print ticket.NumbersPrize numbersPrize;
                    print ticket.HasMultiplier hasMultiplier;
                    print ticket.HasBulleye hasBulleye;
                    print ticket.Multiplier multiplier;
                    print ticket.Bulleye bulleye;
                    print ticket.Numbers numbers;
                    print ticket.WinnerNumber winnerNumber;
                    print ticket.GradingAsString() gradingStatus;
                    print false isWinner;
                    print ticket.TicketNumber ticketNumber;
                    print ticket.Creation.MMddyyyy_hhmmss() creationDate;
                }}
            }}
            ");
            return result;
        }

        [HttpGet("api/player/tickets/keno")]
        [Authorize(Roles = "player")]
        public async Task<IActionResult> MyPlayedKenoTicketsAsync(DateTime startDate, DateTime endDate)
        {
            if (startDate == default(DateTime)) return BadRequest($"{nameof(startDate)} is required");
            if (endDate == default(DateTime)) return BadRequest($"{nameof(endDate)} is required");

            var startDateAsText = $"{startDate.Month}/{startDate.Day}/{startDate.Year}";
            var endDateAsText = $"{endDate.Month}/{endDate.Day}/{endDate.Year}";
            string playerId = Validator.StringEscape(Security.PlayerId(User));
            var result = await LottoBIAPI.LottoBI.PerformQryAsync(HttpContext, $@"
            {{
                player = company.Players.SearchPlayer('{playerId}');
                myWinnerTickets = player.PlayerLottoReports.MyWinnerKenoTicketsBetween({startDateAsText}, {endDateAsText});
                myLoserTickets = player.PlayerLottoReports.MyLoserKenoTicketsBetween({startDateAsText}, {endDateAsText});
                myNoActionTickets = player.PlayerLottoReports.MyNoActionKenoTicketsBetween({startDateAsText}, {endDateAsText});
                for (winnerTickets:myWinnerTickets)
                {{
                    ticket = winnerTickets;
                    print ticket.DrawDate.MMddyyyy_hhmmss() drawDate;
                    print ticket.DrawId drawId;
                    print ticket.BetAmount betAmount;
                    print ticket.TicketAmount ticketAmount;
                    print ticket.Prize prize;
                    print ticket.BulleyePrize bulleyePrize;
                    print ticket.NumbersPrize numbersPrize;
                    print ticket.HasMultiplier hasMultiplier;
                    print ticket.HasBulleye hasBulleye;
                    print ticket.Multiplier multiplier;
                    print ticket.Bulleye bulleye;
                    print ticket.Numbers numbers;
                    print ticket.WinnerNumber winnerNumber;
                    print ticket.GradingAsString() gradingStatus;
                    print true isWinner;
                    print ticket.TicketNumber ticketNumber;
                    print ticket.Creation.MMddyyyy_hhmmss() creationDate;
                }}
                for (loserTickets:myLoserTickets)
                {{
                    ticket = loserTickets;
                    print ticket.DrawDate.MMddyyyy_hhmmss() drawDate;
                    print ticket.DrawId drawId;
                    print ticket.BetAmount betAmount;
                    print ticket.TicketAmount ticketAmount;
                    print ticket.Prize prize;
                    print ticket.BulleyePrize bulleyePrize;
                    print ticket.NumbersPrize numbersPrize;
                    print ticket.HasMultiplier hasMultiplier;
                    print ticket.HasBulleye hasBulleye;
                    print ticket.Multiplier multiplier;
                    print ticket.Bulleye bulleye;
                    print ticket.Numbers numbers;
                    print ticket.WinnerNumber winnerNumber;
                    print ticket.GradingAsString() gradingStatus;
                    print false isWinner;
                    print ticket.TicketNumber ticketNumber;
                    print ticket.Creation.MMddyyyy_hhmmss() creationDate;
                }}
                for (noActionTickets:myNoActionTickets)
                {{
                    ticket = noActionTickets;
                    print ticket.DrawDate.MMddyyyy_hhmmss() drawDate;
                    print ticket.DrawId drawId;
                    print ticket.BetAmount betAmount;
                    print ticket.TicketAmount ticketAmount;
                    print ticket.Prize prize;
                    print ticket.BulleyePrize bulleyePrize;
                    print ticket.NumbersPrize numbersPrize;
                    print ticket.HasMultiplier hasMultiplier;
                    print ticket.HasBulleye hasBulleye;
                    print ticket.Multiplier multiplier;
                    print ticket.Bulleye bulleye;
                    print ticket.Numbers numbers;
                    print ticket.WinnerNumber winnerNumber;
                    print ticket.GradingAsString() gradingStatus;
                    print false isWinner;
                    print ticket.TicketNumber ticketNumber;
                    print ticket.Creation.MMddyyyy_hhmmss() creationDate;
                }}
            }}
            ");
            return result;
        }

        [HttpGet("api/player/tickets")]
        [Authorize(Roles = "player")]
        public async Task<IActionResult> MyPlayedTicketsAsync(DateTime startDate, DateTime endDate)
        {
            if (startDate == default(DateTime)) return BadRequest($"{nameof(startDate)} is required");
            if (endDate == default(DateTime)) return BadRequest($"{nameof(endDate)} is required");

            var startDateAsText = $"{startDate.Month}/{startDate.Day}/{startDate.Year}";
            var endDateAsText = $"{endDate.Month}/{endDate.Day}/{endDate.Year}";
            string playerId = Validator.StringEscape(Security.PlayerId(User));
            var result = await LottoBIAPI.LottoBI.PerformQryAsync(HttpContext, $@"
            {{
                player = company.Players.SearchPlayer('{playerId}');
                myWinnerTicketsByPrize = player.PlayerLottoReports.MyWinnerTicketsBetween({startDateAsText}, {endDateAsText});
                for (winnerTickets:myWinnerTicketsByPrize)
                {{
                    ticketByPrize = winnerTickets;
                    ticket = winnerTickets.FirstTicket;
                    print ticket.GameType() gameType;
                    drawDate = ticket.DrawDate();
                    print drawDate.MMddyyyy_hhmmss() drawDate;
                    print drawDate.MMddyyyyOrTodayOrTomorrow(Now) drawDateFormatted;
                    print drawDate.HHmmAMPM() hour;
                    print ticket.StateAbb state;
                    print ticket.DrawingName description;
                    print ticket.BelongsToFireBallDraw withFireBall;
                    if (ticket.BelongsToFireBallDraw)
                    {{
                        print ticket.Fireball fireBall;
                    }}
                    print ticket.TypeNumberSequenceAsText() position;
                    print ticket.Amount betAmount;
                    print ticket.AmountFormatted betAmountFormatted;
                    print ticketByPrize.TicketAmount() ticketAmount;
                    print ticketByPrize.TicketAmountFormatted() ticketAmountFormatted;
                    print ticketByPrize.PrizeToWinForOneDollar prizeToWinForOneDollar;
                    print ticketByPrize.WonAmount() prize;
                    print ticketByPrize.WonAmountFormatted() prizeFormatted;
                    print ticket.Draw winnerNumber;
                    print ticket.WinnerDigits() winnerDigits;
                    print ticket.SelectionModeAsString() selectionMode;
                    print ticket.GradingAsString() gradingStatus;
                    print ticketByPrize.IsWinner() isWinner;
                    print ticket.Creation creationDate;
                    print ticket.WasPurchasedForFree wasPurchasedWithFP;
                    for (numbers:ticketByPrize.Wagers())
                    {{
                        wager = numbers;
                        print wager.SubticketAsString subticket;
                        print wager.FullWagerNumber() fullWagerNumber;
                    }}
                }}

                myLoserTicketsByPrize = player.PlayerLottoReports.MyLoserTicketsBetween({startDateAsText}, {endDateAsText});
                for (loserTickets:myLoserTicketsByPrize)
                {{
                    ticketByPrize = loserTickets;
                    ticket = loserTickets.FirstTicket;
                    print ticket.GameType() gameType;
                    drawDate = ticket.DrawDate();
                    print drawDate.MMddyyyy_hhmmss() drawDate;
                    print drawDate.MMddyyyyOrTodayOrTomorrow(Now) drawDateFormatted;
                    print drawDate.HHmmAMPM() hour;
                    print ticket.StateAbb state;
                    print ticket.DrawingName description;
                    print ticket.BelongsToFireBallDraw withFireBall;
                    if (ticket.BelongsToFireBallDraw)
                    {{
                        print ticket.Fireball fireBall;
                    }}
                    print ticket.TypeNumberSequenceAsText() position;
                    print ticket.Amount betAmount;
                    print ticket.AmountFormatted betAmountFormatted;
                    print ticketByPrize.TicketAmount() ticketAmount;
                    print ticketByPrize.TicketAmountFormatted() ticketAmountFormatted;
                    print ticketByPrize.PrizeToWinForOneDollar prizeToWinForOneDollar;
                    print 0 prize;
                    print ticketByPrize.WonAmountFormatted() prizeFormatted;
                    print ticket.Draw winnerNumber;
                    print ticket.WinnerDigits() winnerDigits;
                    print ticket.SelectionModeAsString() selectionMode;
                    print ticket.GradingAsString() gradingStatus;
                    print false isWinner;
                    print ticket.Creation creationDate;
                    print ticket.WasPurchasedForFree wasPurchasedWithFP;
                    for (numbers:ticketByPrize.Wagers())
                    {{
                        wager = numbers;
                        print wager.SubticketAsString subticket;
                        print wager.FullWagerNumber() fullWagerNumber;
                    }}
                }}

                myNoActionTicketsByPrize = player.PlayerLottoReports.MyNoActionTicketsBetween({startDateAsText}, {endDateAsText});
                for (noActionTickets:myNoActionTicketsByPrize)
                {{
                    ticketByPrize = noActionTickets;
                    ticket = noActionTickets.FirstTicket;
                    print ticket.GameType() gameType;
                    drawDate = ticket.DrawDate();
                    print drawDate.MMddyyyy_hhmmss() drawDate;
                    print drawDate.MMddyyyyOrTodayOrTomorrow(Now) drawDateFormatted;
                    print drawDate.HHmmAMPM() hour;
                    print ticket.StateAbb state;
                    print ticket.DrawingName description;
                    print false withFireBall;
                    print ticket.TypeNumberSequenceAsText() position;
                    print ticket.Amount betAmount;
                    print ticket.AmountFormatted betAmountFormatted;
                    print ticketByPrize.TicketAmount() ticketAmount;
                    print ticketByPrize.TicketAmountFormatted() ticketAmountFormatted;
                    print ticketByPrize.PrizeToWinForOneDollar prizeToWinForOneDollar;
                    print 0 prize;
                    print ticketByPrize.WonAmountFormatted() prizeFormatted;
                    print ticket.SelectionModeAsString() selectionMode;
                    print ticket.GradingAsString() gradingStatus;
                    print false isWinner;
                    print ticket.Creation creationDate;
                    print false wasPurchasedWithFP;
                    for (numbers:ticketByPrize.Wagers())
                    {{
                        wager = numbers;
                        print wager.SubticketAsString subticket;
                        print wager.FullWagerNumber() fullWagerNumber;
                    }}
                }}
            }}
            ");
            return result;
        }

        [HttpGet("api/player/drawings")]
        [Authorize(Roles = "player")]
        public async Task<IActionResult> LastPlayedDrawsAsync()
        {
            string playerId = Validator.StringEscape(Security.PlayerId(User));

            var result = await LottoBIAPI.LottoBI.PerformQryAsync(HttpContext, $@"
            {{
				player = company.Players.SearchPlayer('{playerId}');
                completedDrawings = player.PlayerLottoReports.LastPlayedDrawings();
                for (drawings:completedDrawings.GetAllOrderedByDrawDate)
                {{
                    draw = drawings;
                    drawDate = draw.DrawDate;
                    print drawDate.MMddyyyy_hhmmss() drawDate;
                    print drawDate.HHmmAMPM() hour;
                    print draw.State state;
                }}
            }}
            ");
            return result;
        }

        [HttpGet("api/player/drawings/tickets")]
        [Authorize(Roles = "player")]
        public async Task<IActionResult> MyPlayedTicketsOfDrawingAsync(DateTime drawDate, string state)
        {
            if (drawDate == default(DateTime)) return BadRequest($"{nameof(drawDate)} is required");

            var stringDrawDate = $"{drawDate.Month}/{drawDate.Day}/{drawDate.Year} {drawDate.Hour}:{drawDate.Minute}:{drawDate.Second}";
            string playerId = Validator.StringEscape(Security.PlayerId(User));
            var result = await LottoBIAPI.LottoBI.PerformQryAsync(HttpContext, $@"
            {{
                player = company.Players.SearchPlayer('{playerId}');
                myWinnerTicketsByPrize = player.PlayerLottoReports.MyWinnerTicketsOfDrawing({stringDrawDate}, '{state}');
                myLoserTicketsByPrize = player.PlayerLottoReports.MyLoserTicketsOfDrawing({stringDrawDate}, '{state}');
                myNoActionTicketsByPrize = player.PlayerLottoReports.MyNoActionTicketsOfDrawing({stringDrawDate}, '{state}');
                for (winnerTickets:myWinnerTicketsByPrize)
                {{
                    ticketByPrize = winnerTickets;
                    ticket = winnerTickets.FirstTicket;
                    print ticket.GameType() gameType;
                    drawDate = ticket.DrawDate();
                    print drawDate.MMddyyyy_hhmmss() drawDate;
                    print drawDate.MMddyyyyOrTodayOrTomorrow(Now) drawDateFormatted;
                    print drawDate.HHmmAMPM() hour;
                    print ticket.StateAbb state;
                    print ticket.DrawingName description;
                    print ticket.BelongsToFireBallDraw withFireBall;
                    if (ticket.BelongsToFireBallDraw)
                    {{
                        print ticket.Fireball fireBall;
                    }}
                    print ticket.TypeNumberSequenceAsText() position;
                    print ticket.Amount betAmount;
                    print ticket.AmountFormatted betAmountFormatted;
                    print ticketByPrize.TicketAmount() ticketAmount;
                    print ticketByPrize.TicketAmountFormatted() ticketAmountFormatted;
                    print ticketByPrize.PrizeToWinForOneDollar prizeToWinForOneDollar;
                    print ticketByPrize.WonAmount() prize;
                    print ticketByPrize.WonAmountFormatted() prizeFormatted;
                    print ticket.Draw winnerNumber;
                    print ticket.WinnerDigits() winnerDigits;
                    print ticket.SelectionModeAsString() selectionMode;
                    print ticket.GradingAsString() gradingStatus;
                    print ticketByPrize.IsWinner() isWinner;
                    print ticket.Creation creationDate;
                    print ticket.WasPurchasedForFree wasPurchasedWithFP;
                    for (numbers:ticketByPrize.Wagers())
                    {{
                        wager = numbers;
                        print wager.SubticketAsString subticket;
                        print wager.FullWagerNumber() fullWagerNumber;
                    }}
                }}
                for (loserTickets:myLoserTicketsByPrize)
                {{
                    ticketByPrize = loserTickets;
                    ticket = loserTickets.FirstTicket;
                    print ticket.GameType() gameType;
                    drawDate = ticket.DrawDate();
                    print drawDate.MMddyyyy_hhmmss() drawDate;
                    print drawDate.MMddyyyyOrTodayOrTomorrow(Now) drawDateFormatted;
                    print drawDate.HHmmAMPM() hour;
                    print ticket.StateAbb state;
                    print ticket.DrawingName description;
                    print ticket.BelongsToFireBallDraw withFireBall;
                    if (ticket.BelongsToFireBallDraw)
                    {{
                        print ticket.Fireball fireBall;
                    }}
                    print ticket.TypeNumberSequenceAsText() position;
                    print ticket.Amount betAmount;
                    print ticket.AmountFormatted betAmountFormatted;
                    print ticketByPrize.TicketAmount() ticketAmount;
                    print ticketByPrize.TicketAmountFormatted() ticketAmountFormatted;
                    print ticketByPrize.PrizeToWinForOneDollar prizeToWinForOneDollar;
                    print 0 prize;
                    print ticketByPrize.WonAmountFormatted() prizeFormatted;
                    print ticket.Draw winnerNumber;
                    print ticket.WinnerDigits() winnerDigits;
                    print ticket.SelectionModeAsString() selectionMode;
                    print ticket.GradingAsString() gradingStatus;
                    print false isWinner;
                    print ticket.Creation creationDate;
                    print ticket.WasPurchasedForFree wasPurchasedWithFP;
                    for (numbers:ticketByPrize.Wagers())
                    {{
                        wager = numbers;
                        print wager.SubticketAsString subticket;
                        print wager.FullWagerNumber() fullWagerNumber;
                    }}
                }}
                for (noActionTickets:myNoActionTicketsByPrize)
                {{
                    ticketByPrize = noActionTickets;
                    ticket = noActionTickets.FirstTicket;
                    print ticket.GameType() gameType;
                    drawDate = ticket.DrawDate();
                    print drawDate.MMddyyyy_hhmmss() drawDate;
                    print drawDate.MMddyyyyOrTodayOrTomorrow(Now) drawDateFormatted;
                    print drawDate.HHmmAMPM() hour;
                    print ticket.StateAbb state;
                    print ticket.DrawingName description;
                    print false withFireBall;
                    print ticket.TypeNumberSequenceAsText() position;
                    print ticket.Amount betAmount;
                    print ticket.AmountFormatted betAmountFormatted;
                    print ticketByPrize.TicketAmount() ticketAmount;
                    print ticketByPrize.TicketAmountFormatted() ticketAmountFormatted;
                    print ticketByPrize.PrizeToWinForOneDollar prizeToWinForOneDollar;
                    print 0 prize;
                    print ticketByPrize.WonAmountFormatted() prizeFormatted;
                    print ticket.SelectionModeAsString() selectionMode;
                    print ticket.GradingAsString() gradingStatus;
                    print false isWinner;
                    print ticket.Creation creationDate;
                    print false wasPurchasedWithFP;
                    for (numbers:ticketByPrize.Wagers())
                    {{
                        wager = numbers;
                        print wager.SubticketAsString subticket;
                        print wager.FullWagerNumber() fullWagerNumber;
                    }}
                }}
            }}
            ");
            return result;
        }

        [HttpGet("api/player/drawings/keno")]
        [Authorize(Roles = "player")]
        public async Task<IActionResult> LastPlayedKenoDrawsAsync()
        {
            string playerId = Validator.StringEscape(Security.PlayerId(User));

            var result = await LottoBIAPI.LottoBI.PerformQryAsync(HttpContext, $@"
            {{
				player = company.Players.SearchPlayer('{playerId}');
                completedDrawings = player.PlayerLottoReports.LastPlayedKenoDrawings();
                for (drawings:completedDrawings.GetAllOrderedByDrawDate)
                {{
                    draw = drawings;
                    print draw.DrawDate drawDate;
                    print draw.DrawingId drawId;
                }}
            }}
            ");
            return result;
        }

        [HttpGet("api/player/drawings/keno/tickets")]
        [Authorize(Roles = "player")]
        public async Task<IActionResult> MyPlayedKenoTicketsOfDrawingAsync(DateTime drawDate)
        {
            if (drawDate == default(DateTime)) return BadRequest($"{nameof(drawDate)} is required");

            var stringDrawDate = $"{drawDate.Month}/{drawDate.Day}/{drawDate.Year} {drawDate.Hour}:{drawDate.Minute}:{drawDate.Second}";
            string playerId = Validator.StringEscape(Security.PlayerId(User));
            var result = await LottoBIAPI.LottoBI.PerformQryAsync(HttpContext, $@"
            {{
                player = company.Players.SearchPlayer('{playerId}');
                myWinnerTickets = player.PlayerLottoReports.MyWinnerKenoTicketsOfDrawing({stringDrawDate});
                myLoserTickets = player.PlayerLottoReports.MyLoserKenoTicketsOfDrawing({stringDrawDate});
                myNoActionTickets = player.PlayerLottoReports.MyNoActionKenoTicketsOfDrawing({stringDrawDate});
                for (winnerTickets:myWinnerTickets)
                {{
                    ticket = winnerTickets;
                    print ticket.DrawDate.MMddyyyy_hhmmss() drawDate;
                    print ticket.DrawId drawId;
                    print ticket.BetAmount betAmount;
                    print ticket.TicketAmount ticketAmount;
                    print ticket.Prize prize;
                    print ticket.BulleyePrize bulleyePrize;
                    print ticket.NumbersPrize numbersPrize;
                    print ticket.HasMultiplier hasMultiplier;
                    print ticket.HasBulleye hasBulleye;
                    print ticket.Multiplier multiplier;
                    print ticket.Bulleye bulleye;
                    print ticket.Numbers numbers;
                    print ticket.WinnerNumber winnerNumber;
                    print ticket.GradingAsString() gradingStatus;
                    print true isWinner;
                    print ticket.TicketNumber ticketNumber;
                    print ticket.Creation.MMddyyyy_hhmmss() creationDate;
                }}
                for (loserTickets:myLoserTickets)
                {{
                    ticket = loserTickets;
                    print ticket.DrawDate.MMddyyyy_hhmmss() drawDate;
                    print ticket.DrawId drawId;
                    print ticket.BetAmount betAmount;
                    print ticket.TicketAmount ticketAmount;
                    print ticket.Prize prize;
                    print ticket.BulleyePrize bulleyePrize;
                    print ticket.NumbersPrize numbersPrize;
                    print ticket.HasMultiplier hasMultiplier;
                    print ticket.HasBulleye hasBulleye;
                    print ticket.Multiplier multiplier;
                    print ticket.Bulleye bulleye;
                    print ticket.Numbers numbers;
                    print ticket.WinnerNumber winnerNumber;
                    print ticket.GradingAsString() gradingStatus;
                    print false isWinner;
                    print ticket.TicketNumber ticketNumber;
                    print ticket.Creation.MMddyyyy_hhmmss() creationDate;
                }}
                for (noActionTickets:myNoActionTickets)
                {{
                    ticket = noActionTickets;
                    print ticket.DrawDate.MMddyyyy_hhmmss() drawDate;
                    print ticket.DrawId drawId;
                    print ticket.BetAmount betAmount;
                    print ticket.TicketAmount ticketAmount;
                    print ticket.Prize prize;
                    print ticket.BulleyePrize bulleyePrize;
                    print ticket.NumbersPrize numbersPrize;
                    print ticket.HasMultiplier hasMultiplier;
                    print ticket.HasBulleye hasBulleye;
                    print ticket.Multiplier multiplier;
                    print ticket.Bulleye bulleye;
                    print ticket.Numbers numbers;
                    print ticket.WinnerNumber winnerNumber;
                    print ticket.GradingAsString() gradingStatus;
                    print false isWinner;
                    print ticket.TicketNumber ticketNumber;
                    print ticket.Creation.MMddyyyy_hhmmss() creationDate;
                }}
            }}
            ");
            return result;
        }

        [HttpPost("api/lotto/player/ticket/played/byTime")]
        [Authorize(Roles = "player")]
        public async Task<IActionResult> MyPlayedTicketsByTimeAsync(string date, string dates, string gameType, bool withLR, [FromBody]MyTicketsBody body)
        {
            if (String.IsNullOrWhiteSpace(date)) return BadRequest($"Parameter {nameof(date)} is required");
            if (body == null) return BadRequest("Body is required");

            if (String.IsNullOrWhiteSpace(gameType))
            {
                gameType = "All";
            }
            if (gameType != "All" && gameType != "Powerball" && gameType != "Pick") return BadRequest($"{nameof(gameType)} '{gameType}' is not a valid value");

            var scriptToStoreFavorites = string.Empty;
            var scriptForMyPlayedTickets = string.Empty;
            try
            {
                scriptToStoreFavorites = ScriptToStoreFavorites(body.Favorites);
                scriptForMyPlayedTickets = ScriptForMyPlayedTickets();
            }
            catch (Exception e)
            {
                return BadRequest(e.Message);
            }
            
            var linesToGetTickets = new StringBuilder();
            switch (date)
            {
                case "this_week":
                    {
                        linesToGetTickets.Append("startedDate = Now.InicioDeActualSemana();");
                        linesToGetTickets.Append("endedDate = startedDate.EnUnaSemana();");
                        linesToGetTickets.Append($"myWinnerTicketsByPrizeWithTotalSubtickets = player.PlayerLottoReports.MyWinnerTicketsByTimeBetween(startedDate, endedDate, '{gameType}', {withLR});");
                        linesToGetTickets.Append($"myLoserTicketsByPrizeWithTotalSubtickets = player.PlayerLottoReports.MyLoserTicketsByTimeBetween(startedDate, endedDate, '{gameType}', {withLR});");
                        linesToGetTickets.Append($"myNoActionTicketsByPrizeWithTotalSubtickets = player.PlayerLottoReports.MyNoActionTicketsByTimeBetween(startedDate, endedDate, '{gameType}');");
                        break;
                    }
                case "yesterday":
                    {
                        linesToGetTickets.Append("date=Now.Ayer();");
                        linesToGetTickets.Append($"myWinnerTicketsByPrizeWithTotalSubtickets = player.PlayerLottoReports.MyWinnerTicketsByTimeAt(date, '{gameType}', {withLR});");
                        linesToGetTickets.Append($"myLoserTicketsByPrizeWithTotalSubtickets = player.PlayerLottoReports.MyLoserTicketsByTimeAt(date, '{gameType}', {withLR});");
                        linesToGetTickets.Append($"myNoActionTicketsByPrizeWithTotalSubtickets = player.PlayerLottoReports.MyNoActionTicketsByTimeAt(date, '{gameType}');");
                        break;
                    }
                case "last_week":
                    {
                        linesToGetTickets.Append("date=Now.AnteriorSemana();");
                        linesToGetTickets.Append($"myWinnerTicketsByPrizeWithTotalSubtickets = player.PlayerLottoReports.MyWinnerTicketsByTimeFrom(date, '{gameType}', {withLR});");
                        linesToGetTickets.Append($"myLoserTicketsByPrizeWithTotalSubtickets = player.PlayerLottoReports.MyLoserTicketsByTimeFrom(date, '{gameType}', {withLR});");
                        linesToGetTickets.Append($"myNoActionTicketsByPrizeWithTotalSubtickets = player.PlayerLottoReports.MyNoActionTicketsByTimeFrom(date, '{gameType}');");
                        break;
                    }
                case "last_month":
                    {
                        linesToGetTickets.Append("date=Now.AnteriorMes();");
                        linesToGetTickets.Append($"myWinnerTicketsByPrizeWithTotalSubtickets = player.PlayerLottoReports.MyWinnerTicketsByTimeFrom(date, '{gameType}', {withLR});");
                        linesToGetTickets.Append($"myLoserTicketsByPrizeWithTotalSubtickets = player.PlayerLottoReports.MyLoserTicketsByTimeFrom(date, '{gameType}', {withLR});");
                        linesToGetTickets.Append($"myNoActionTicketsByPrizeWithTotalSubtickets = player.PlayerLottoReports.MyNoActionTicketsByTimeFrom(date, '{gameType}');");
                        break;
                    }
                case "specific_day":
                    {
                        if (String.IsNullOrWhiteSpace(dates)) return BadRequest($"Parameter {nameof(dates)} is required");
                        linesToGetTickets.Append($"myWinnerTicketsByPrizeWithTotalSubtickets = player.PlayerLottoReports.MyWinnerTicketsByTimeIn('{dates}', '{gameType}', {withLR});");
                        linesToGetTickets.Append($"myLoserTicketsByPrizeWithTotalSubtickets = player.PlayerLottoReports.MyLoserTicketsByTimeIn('{dates}', '{gameType}', {withLR});");
                        linesToGetTickets.Append($"myNoActionTicketsByPrizeWithTotalSubtickets = player.PlayerLottoReports.MyNoActionTicketsByTimeIn('{dates}', '{gameType}');");
                        break;
                    }
                default:
                    {
                        return BadRequest($"There is no date for label {date}");
                    }
            }
            string playerId = Validator.StringEscape(Security.PlayerId(User));
            var result = await LottoBIAPI.LottoBI.PerformQryAsync(HttpContext, $@"
            {{
                player = company.Players.SearchPlayer('{playerId}');
                {linesToGetTickets.ToString()}
                myWinnerTickets = myWinnerTicketsByPrizeWithTotalSubtickets.TicketsByPrize;
                myLoserTickets = myLoserTicketsByPrizeWithTotalSubtickets.TicketsByPrize;
                myNoActionTickets = myNoActionTicketsByPrizeWithTotalSubtickets.TicketsByPrize;
                {scriptToStoreFavorites}
                {scriptForMyPlayedTickets}
            }}
            ");
            return result;
        }

        [HttpPost("api/lotto/player/ticket/played/byState")]
        [Authorize(Roles = "player")]
        public async Task<IActionResult> MyPlayedTicketsByStateAsync(string date, string dates, string gameType, bool withLR, [FromBody]MyTicketsBody body)
        {
            if (String.IsNullOrWhiteSpace(date)) return BadRequest($"Parameter {nameof(date)} is required");
            if (body == null) return BadRequest("Body is required");

            if (String.IsNullOrWhiteSpace(gameType))
            {
                gameType = "All";
            }
            if (gameType != "All" && gameType != "Powerball" && gameType != "Pick") return BadRequest($"{nameof(gameType)} '{gameType}' is not a valid value");

            var scriptToStoreFavorites = string.Empty;
            var scriptForMyPlayedTickets = string.Empty;
            try
            {
                scriptToStoreFavorites = ScriptToStoreFavorites(body.Favorites);
                scriptForMyPlayedTickets = ScriptForMyPlayedTickets();
            }
            catch (Exception e)
            {
                return BadRequest(e.Message);
            }

            var linesToGetTickets = new StringBuilder();
            switch (date)
            {
                case "this_week":
                    {
                        linesToGetTickets.Append("startedDate = Now.InicioDeActualSemana();");
                        linesToGetTickets.Append("endedDate = startedDate.EnUnaSemana();");
                        linesToGetTickets.Append($"myWinnerTicketsByPrizeWithTotalSubtickets = player.PlayerLottoReports.MyWinnerTicketsByStateBetween(startedDate, endedDate, '{gameType}', {withLR});");
                        linesToGetTickets.Append($"myLoserTicketsByPrizeWithTotalSubtickets = player.PlayerLottoReports.MyLoserTicketsByStateBetween(startedDate, endedDate, '{gameType}', {withLR});");
                        linesToGetTickets.Append($"myNoActionTicketsByPrizeWithTotalSubtickets = player.PlayerLottoReports.MyNoActionTicketsByStateBetween(startedDate, endedDate, '{gameType}');");
                        break;
                    }
                case "yesterday":
                    {
                        linesToGetTickets.Append("date=Now.Ayer();");
                        linesToGetTickets.Append($"myWinnerTicketsByPrizeWithTotalSubtickets = player.PlayerLottoReports.MyWinnerTicketsByStateAt(date, '{gameType}', {withLR});");
                        linesToGetTickets.Append($"myLoserTicketsByPrizeWithTotalSubtickets = player.PlayerLottoReports.MyLoserTicketsByStateAt(date, '{gameType}', {withLR});");
                        linesToGetTickets.Append($"myNoActionTicketsByPrizeWithTotalSubtickets = player.PlayerLottoReports.MyNoActionTicketsByStateAt(date, '{gameType}');");
                        break;
                    }
                case "last_week":
                    {
                        linesToGetTickets.Append("date=Now.AnteriorSemana();");
                        linesToGetTickets.Append($"myWinnerTicketsByPrizeWithTotalSubtickets = player.PlayerLottoReports.MyWinnerTicketsByStateFrom(date, '{gameType}', {withLR});");
                        linesToGetTickets.Append($"myLoserTicketsByPrizeWithTotalSubtickets = player.PlayerLottoReports.MyLoserTicketsByStateFrom(date, '{gameType}', {withLR});");
                        linesToGetTickets.Append($"myNoActionTicketsByPrizeWithTotalSubtickets = player.PlayerLottoReports.MyNoActionTicketsByStateFrom(date, '{gameType}');");
                        break;
                    }
                case "last_month":
                    {
                        linesToGetTickets.Append("date=Now.AnteriorMes();");
                        linesToGetTickets.Append($"myWinnerTicketsByPrizeWithTotalSubtickets = player.PlayerLottoReports.MyWinnerTicketsByStateFrom(date, '{gameType}', {withLR});");
                        linesToGetTickets.Append($"myLoserTicketsByPrizeWithTotalSubtickets = player.PlayerLottoReports.MyLoserTicketsByStateFrom(date, '{gameType}', {withLR});");
                        linesToGetTickets.Append($"myNoActionTicketsByPrizeWithTotalSubtickets = player.PlayerLottoReports.MyNoActionTicketsByStateFrom(date, '{gameType}');");
                        break;
                    }
                case "specific_day":
                    {
                        if (String.IsNullOrWhiteSpace(dates)) return BadRequest($"Parameter {nameof(dates)} is required");
                        linesToGetTickets.Append($"myWinnerTicketsByPrizeWithTotalSubtickets = player.PlayerLottoReports.MyWinnerTicketsByStateIn('{dates}', '{gameType}', {withLR});");
                        linesToGetTickets.Append($"myLoserTicketsByPrizeWithTotalSubtickets = player.PlayerLottoReports.MyLoserTicketsByStateIn('{dates}', '{gameType}', {withLR});");
                        linesToGetTickets.Append($"myNoActionTicketsByPrizeWithTotalSubtickets = player.PlayerLottoReports.MyNoActionTicketsByStateIn('{dates}', '{gameType}');");
                        break;
                    }
                default:
                    {
                        return BadRequest($"There is no date for label {date}");
                    }
            }
            string playerId = Validator.StringEscape(Security.PlayerId(User));
            var result = await LottoBIAPI.LottoBI.PerformQryAsync(HttpContext,$@"
            {{
                player = company.Players.SearchPlayer('{playerId}');
                {linesToGetTickets.ToString()}
                myWinnerTickets = myWinnerTicketsByPrizeWithTotalSubtickets.TicketsByPrize;
                myLoserTickets = myLoserTicketsByPrizeWithTotalSubtickets.TicketsByPrize;
                myNoActionTickets = myNoActionTicketsByPrizeWithTotalSubtickets.TicketsByPrize;
                {scriptToStoreFavorites}
                {scriptForMyPlayedTickets}
            }}
            ");
            return result;
        }

        [AllowAnonymous]
        [HttpGet("api/lotto/player/winnerTicket")]
        public async Task<IActionResult> WinnerTicketAsync(string creationDate, string drawDate, string state, string prize, bool wasCreatedByPattern)
        {
            if (String.IsNullOrWhiteSpace(creationDate)) return BadRequest($"Parameter {nameof(creationDate)} is required");
            if (String.IsNullOrWhiteSpace(drawDate)) return BadRequest($"Parameter {nameof(drawDate)} is required");
            if (String.IsNullOrWhiteSpace(state)) return BadRequest($"Parameter {nameof(state)} is required");
            if (String.IsNullOrWhiteSpace(prize)) return BadRequest($"Parameter {nameof(prize)} is required");

            string playerId = Validator.StringEscape(Security.PlayerId(User));
            var newDrawDate = DateTime.ParseExact(drawDate, "M/d/yyyy HH:mm:ss", new CultureInfo("en-US"));
            var strDrawDate = $"{newDrawDate.Month}/{newDrawDate.Day}/{newDrawDate.Year} {newDrawDate.Hour}:{newDrawDate.Minute}:{newDrawDate.Second}";
            var newCreationDate = DateTime.ParseExact(creationDate, "M/d/yyyy HH:mm:ss", new CultureInfo("en-US"));
            var strCreationDate = $"{newCreationDate.Month}/{newCreationDate.Day}/{newCreationDate.Year} {newCreationDate.Hour}:{newCreationDate.Minute}:{newCreationDate.Second}";
            var result = await LottoBIAPI.LottoBI.PerformQryAsync(HttpContext, $@"
                {{
                    player = company.Players.SearchPlayer('{playerId}');
                    winnerTicket = player.PlayerLottoReports.GetWinnerTicketBy('{state}',{strCreationDate},{strDrawDate},{prize},{wasCreatedByPattern});
                    hasSubtickets = winnerTicket.HasSubtickets();
                    print hasSubtickets 'hasSubtickets';
                    if (hasSubtickets)
                    {{
                        print winnerTicket.AsString() numbers;
                        print winnerTicket.Count totalSubtickets;
                        print winnerTicket.WinnerInfo.GameType() gameType;
                        drawDate = winnerTicket.WinnerInfo.DrawDate();
                        print drawDate.MMddyyyy_hhmmss() drawDate;
                        print drawDate.MMddyyyyOrTodayOrTomorrow(Now) drawDateFormatted;
                        hour = drawDate.HHmmAMPM();
                        print hour hour;
                        state = winnerTicket.WinnerInfo.StateAbb;
                        print state state;
                        print winnerTicket.WinnerInfo.Amount betAmount;
                        print winnerTicket.TicketAmount() ticketAmount;
                        print winnerTicket.PrizeToWinForOneDollar prizeToWinForOneDollar;
                        print winnerTicket.WonAmount() prize;
                        print winnerTicket.WinnerInfo.Draw winnerNumber;
                        print winnerTicket.WinnerInfo.SelectionModeAsString() selectionMode;
                        print winnerTicket.IsWinner() isWinner;
                        print winnerTicket.WinnerInfo.TicketNumber ticketNumber;
                        print winnerTicket.WinnerInfo.Creation creationDate;
                        print winnerTicket.WinnerInfo.WasCreatedByPattern() wasCreatedByPattern;
                    }}
                }}
            ");
            return result;
        }

        [AllowAnonymous]
        [HttpGet("api/lotto/player/loserTicket")]
        public async Task<IActionResult> LoserTicketAsync(string creationDate, string drawDate, string state, string prize, bool wasCreatedByPattern)
        {
            if (String.IsNullOrWhiteSpace(creationDate)) return BadRequest($"Parameter {nameof(creationDate)} is required");
            if (String.IsNullOrWhiteSpace(drawDate)) return BadRequest($"Parameter {nameof(drawDate)} is required");
            if (String.IsNullOrWhiteSpace(state)) return BadRequest($"Parameter {nameof(state)} is required");
            if (String.IsNullOrWhiteSpace(prize)) return BadRequest($"Parameter {nameof(prize)} is required");

            string playerId = Validator.StringEscape(Security.PlayerId(User));
            var newDrawDate = DateTime.ParseExact(drawDate, "M/d/yyyy HH:mm:ss", new CultureInfo("en-US"));
            var strDrawDate = $"{newDrawDate.Month}/{newDrawDate.Day}/{newDrawDate.Year} {newDrawDate.Hour}:{newDrawDate.Minute}:{newDrawDate.Second}";
            var newCreationDate = DateTime.ParseExact(creationDate, "M/d/yyyy HH:mm:ss", new CultureInfo("en-US"));
            var strCreationDate = $"{newCreationDate.Month}/{newCreationDate.Day}/{newCreationDate.Year} {newCreationDate.Hour}:{newCreationDate.Minute}:{newCreationDate.Second}";
            var result = await LottoBIAPI.LottoBI.PerformQryAsync(HttpContext, $@"
                {{
                    player = company.Players.SearchPlayer('{playerId}');
                    loserTicket = player.PlayerLottoReports.GetLoserTicketBy('{state}',{strCreationDate},{strDrawDate},{prize},{wasCreatedByPattern});
                    hasSubtickets = loserTicket.HasSubtickets();
                    print hasSubtickets 'hasSubtickets';
                    if (hasSubtickets)
                    {{
                        print loserTicket.AsString() numbers;
                        print loserTicket.Count totalSubtickets;
                        print loserTicket.LoserInfo.GameType() gameType;
                        drawDate = loserTicket.LoserInfo.DrawDate();
                        print drawDate.MMddyyyy_hhmmss() drawDate;
                        print drawDate.MMddyyyyOrTodayOrTomorrow(Now) drawDateFormatted;
                        hour = drawDate.HHmmAMPM();
                        print hour hour;
                        state = loserTicket.LoserInfo.StateAbb;
                        print state state;
                        print loserTicket.LoserInfo.Amount betAmount;
                        print loserTicket.TicketAmount() ticketAmount;
                        print loserTicket.Prize prizeToWinForOneDollar;
                        print 0 prize;
                        print loserTicket.LoserInfo.Draw winnerNumber;
                        print loserTicket.LoserInfo.SelectionModeAsString() selectionMode;
                        print false isWinner;
                        print loserTicket.LoserInfo.TicketNumber ticketNumber;
                        print loserTicket.LoserInfo.Creation creationDate;
                        print loserTicket.LoserInfo.WasCreatedByPattern() wasCreatedByPattern;
                    }}
                }}
                ");
            return result;
        }

        [AllowAnonymous]
        [HttpGet("api/lotto/player/noActionTicket")]
        public async Task<IActionResult> NoActionTicketAsync(string creationDate, string drawDate, string state, string prize, bool wasCreatedByPattern)
        {
            if (String.IsNullOrWhiteSpace(creationDate)) return BadRequest($"Parameter {nameof(creationDate)} is required");
            if (String.IsNullOrWhiteSpace(drawDate)) return BadRequest($"Parameter {nameof(drawDate)} is required");
            if (String.IsNullOrWhiteSpace(state)) return BadRequest($"Parameter {nameof(state)} is required");
            if (String.IsNullOrWhiteSpace(prize)) return BadRequest($"Parameter {nameof(prize)} is required");

            string playerId = Validator.StringEscape(Security.PlayerId(User));
            var newDrawDate = DateTime.ParseExact(drawDate, "M/d/yyyy HH:mm:ss", new CultureInfo("en-US"));
            var strDrawDate = $"{newDrawDate.Month}/{newDrawDate.Day}/{newDrawDate.Year} {newDrawDate.Hour}:{newDrawDate.Minute}:{newDrawDate.Second}";
            var newCreationDate = DateTime.ParseExact(creationDate, "M/d/yyyy HH:mm:ss", new CultureInfo("en-US"));
            var strCreationDate = $"{newCreationDate.Month}/{newCreationDate.Day}/{newCreationDate.Year} {newCreationDate.Hour}:{newCreationDate.Minute}:{newCreationDate.Second}";
            var result = await LottoBIAPI.LottoBI.PerformQryAsync(HttpContext, $@"
                {{
                    player = company.Players.SearchPlayer('{playerId}');
                    noActionTicket = player.PlayerLottoReports.GetNoActionTicketBy('{state}',{strCreationDate},{strDrawDate},{prize},{wasCreatedByPattern});
                    hasSubtickets = noActionTicket.HasSubtickets();
                    print hasSubtickets 'hasSubtickets';
                    if (hasSubtickets)
                    {{
                        print noActionTicket.AsString() numbers;
                        print noActionTicket.Count totalSubtickets;
                        print noActionTicket.NoActionInfo.GameType() gameType;
                        drawDate = noActionTicket.NoActionInfo.DrawDate();
                        print drawDate.MMddyyyy_hhmmss() drawDate;
                        print drawDate.MMddyyyyOrTodayOrTomorrow(Now) drawDateFormatted;
                        hour = drawDate.HHmmAMPM();
                        print hour hour;
                        state = noActionTicket.NoActionInfo.StateAbb;
                        print state state;
                        print noActionTicket.NoActionInfo.Amount betAmount;
                        print noActionTicket.TicketAmount() ticketAmount;
                        print noActionTicket.Prize prizeToWinForOneDollar;
                        print 0 prize;
                        print noActionTicket.NoActionInfo.SelectionModeAsString() selectionMode;
                        print false isWinner;
                        print noActionTicket.NoActionInfo.TicketNumber ticketNumber;
                        print noActionTicket.NoActionInfo.Creation creationDate;
                        print noActionTicket.NoActionInfo.WasCreatedByPattern() wasCreatedByPattern;
                    }}
                }}
                ");
            return result;
        }

        [HttpGet("api/lotto/player/winnerTicket/wager")]
        [Authorize(Roles = "player")]
        public async Task<IActionResult> WagersOfWinnerTicketAsync(string creationDate, string drawDate, string state, string prize, bool wasCreatedByPattern)
        {
            if (String.IsNullOrWhiteSpace(creationDate)) return BadRequest($"Parameter {nameof(creationDate)} is required");
            if (String.IsNullOrWhiteSpace(drawDate)) return BadRequest($"Parameter {nameof(drawDate)} is required");
            if (String.IsNullOrWhiteSpace(state)) return BadRequest($"Parameter {nameof(state)} is required");
            if (String.IsNullOrWhiteSpace(prize)) return BadRequest($"Parameter {nameof(prize)} is required");

            string playerId = Validator.StringEscape(Security.PlayerId(User));
            var newDrawDate = DateTime.ParseExact(drawDate, "M/d/yyyy HH:mm:ss", new CultureInfo("en-US"));
            var strDrawDate = $"{newDrawDate.Month}/{newDrawDate.Day}/{newDrawDate.Year} {newDrawDate.Hour}:{newDrawDate.Minute}:{newDrawDate.Second}";
            var newCreationDate = DateTime.ParseExact(creationDate, "M/d/yyyy HH:mm:ss", new CultureInfo("en-US"));
            var strCreationDate = $"{newCreationDate.Month}/{newCreationDate.Day}/{newCreationDate.Year} {newCreationDate.Hour}:{newCreationDate.Minute}:{newCreationDate.Second}";
            var result = await LottoBIAPI.LottoBI.PerformQryAsync(HttpContext, $@"
                {{
                    player = company.Players.SearchPlayer('{playerId}');
                    winnerTicketByPrize = player.PlayerLottoReports.GetWinnerTicketBy('{state}',{strCreationDate},{strDrawDate},{prize},{wasCreatedByPattern});
                    hasSubtickets = winnerTicketByPrize.HasSubtickets();
                    print hasSubtickets 'hasSubtickets';
                    for (wager:winnerTicketByPrize.Wagers())
                    {{
                        print wager.SubticketAsString subticket;
                        print wager.FullWagerNumber(winnerTicketByPrize.WinnerInfo.TicketNumber) fullWagerNumber;
                    }}
                }}
            ");
            return result;
        }

        [HttpGet("api/lotto/player/loserTicket/wager")]
        [Authorize(Roles = "player")]
        public async Task<IActionResult> WagersOfLoserTicketAsync(string creationDate, string drawDate, string state, string prize, bool wasCreatedByPattern)
        {
            if (String.IsNullOrWhiteSpace(creationDate)) return BadRequest($"Parameter {nameof(creationDate)} is required");
            if (String.IsNullOrWhiteSpace(drawDate)) return BadRequest($"Parameter {nameof(drawDate)} is required");
            if (String.IsNullOrWhiteSpace(state)) return BadRequest($"Parameter {nameof(state)} is required");
            if (String.IsNullOrWhiteSpace(prize)) return BadRequest($"Parameter {nameof(prize)} is required");

            string playerId = Validator.StringEscape(Security.PlayerId(User));
            var newDrawDate = DateTime.ParseExact(drawDate, "M/d/yyyy HH:mm:ss", new CultureInfo("en-US"));
            var strDrawDate = $"{newDrawDate.Month}/{newDrawDate.Day}/{newDrawDate.Year} {newDrawDate.Hour}:{newDrawDate.Minute}:{newDrawDate.Second}";
            var newCreationDate = DateTime.ParseExact(creationDate, "M/d/yyyy HH:mm:ss", new CultureInfo("en-US"));
            var strCreationDate = $"{newCreationDate.Month}/{newCreationDate.Day}/{newCreationDate.Year} {newCreationDate.Hour}:{newCreationDate.Minute}:{newCreationDate.Second}";
            var result = await LottoBIAPI.LottoBI.PerformQryAsync(HttpContext, $@"
                {{
                    player = company.Players.SearchPlayer('{playerId}');
                    loserTicketByPrize = player.PlayerLottoReports.GetLoserTicketBy('{state}',{strCreationDate},{strDrawDate},{prize},{wasCreatedByPattern});
                    hasSubtickets = loserTicketByPrize.HasSubtickets();
                    print hasSubtickets 'hasSubtickets';
                    for (wager:loserTicketByPrize.Wagers())
                    {{
                        print wager.SubticketAsString subticket;
                        print wager.FullWagerNumber(loserTicketByPrize.LoserInfo.TicketNumber) fullWagerNumber;
                    }}
                }}
            ");
            return result;
        }

        [HttpGet("api/lotto/player/noActionTicket/wager")]
        [Authorize(Roles = "player")]
        public async Task<IActionResult> WagersOfNoActionTicketAsync(string creationDate, string drawDate, string state, string prize, bool wasCreatedByPattern)
        {
            if (String.IsNullOrWhiteSpace(creationDate)) return BadRequest($"Parameter {nameof(creationDate)} is required");
            if (String.IsNullOrWhiteSpace(drawDate)) return BadRequest($"Parameter {nameof(drawDate)} is required");
            if (String.IsNullOrWhiteSpace(state)) return BadRequest($"Parameter {nameof(state)} is required");
            if (String.IsNullOrWhiteSpace(prize)) return BadRequest($"Parameter {nameof(prize)} is required");

            string playerId = Validator.StringEscape(Security.PlayerId(User));
            var newDrawDate = DateTime.ParseExact(drawDate, "M/d/yyyy HH:mm:ss", new CultureInfo("en-US"));
            var strDrawDate = $"{newDrawDate.Month}/{newDrawDate.Day}/{newDrawDate.Year} {newDrawDate.Hour}:{newDrawDate.Minute}:{newDrawDate.Second}";
            var newCreationDate = DateTime.ParseExact(creationDate, "M/d/yyyy HH:mm:ss", new CultureInfo("en-US"));
            var strCreationDate = $"{newCreationDate.Month}/{newCreationDate.Day}/{newCreationDate.Year} {newCreationDate.Hour}:{newCreationDate.Minute}:{newCreationDate.Second}";
            var result = await LottoBIAPI.LottoBI.PerformQryAsync(HttpContext, $@"
                {{
                    player = company.Players.SearchPlayer('{playerId}');
                    noActionTicketByPrize = player.PlayerLottoReports.GetNoActionTicketBy('{state}',{strCreationDate},{strDrawDate},{prize},{wasCreatedByPattern});
                    hasSubtickets = noActionTicketByPrize.HasSubtickets();
                    print hasSubtickets 'hasSubtickets';
                    for (wager:noActionTicketByPrize.Wagers())
                    {{
                        print wager.SubticketAsString subticket;
                        print wager.FullWagerNumber(noActionTicketByPrize.NoActionInfo.TicketNumber) fullWagerNumber;
                    }}
                }}
            ");
            return result;
        }

        [HttpGet("api/player/winnerTickets/recents")]
        [Authorize(Roles = "player")]
        public async Task<IActionResult> WinnerTicketsAsync(DateTime startDate)
        {
            if (startDate == default(DateTime)) return BadRequest($"{nameof(startDate)} is required");

            var startDateAsText = $"{startDate.Month}/{startDate.Day}/{startDate.Year} {startDate.Hour}:{startDate.Minute}:{startDate.Second}";
            string playerId = Security.PlayerId(HttpContext);
            if (String.IsNullOrWhiteSpace(playerId)) return BadRequest(nameof(playerId));
            string validPlayerId = Validator.StringEscape(playerId);
            var result = await LottoBIAPI.LottoBI.PerformQryAsync(HttpContext, $@"
			   {{
                    player = company.Players.SearchPlayer('{validPlayerId}');
                    myWinnerTicketsByPrize = player.PlayerLottoReports.MyWinnerTicketsBetween({startDateAsText}, Now);
                    for (winnerTickets:myWinnerTicketsByPrize)
                    {{
                        ticketByPrize = winnerTickets;
                        ticket = winnerTickets.WinnerInfo;
                        print ticket.GameType() gameType;
                        drawDate = ticket.DrawDate();
                        print drawDate.MMddyyyy_hhmmss() drawDate;
                        print drawDate.HHmmAMPM() hour;
                        print ticket.StateAbb state;
                        print ticket.Draw winnerNumber;
                    }}
				}}
            ");
            return result;
        }

        [DataContract(Name = "favorite")]
        public class Favorite
        {
            [DataMember(Name = "id")]
            public string Id { get; set; }
            [DataMember(Name = "draws")]
            public List<FavoriteDraw> Draws { get; set; }
            [DataMember(Name = "numbers")]
            public List<NumbersBody> Numbers { get; set; }
        }

        [DataContract(Name = "numbersBody")]
        public class NumbersBody
        {
            [DataMember(Name = "number")]
            public string Numbers { get; set; }
        }

        [DataContract(Name = "favoriteDraw")]
        public class FavoriteDraw
        {
            [DataMember(Name = "state")]
            public string State { get; set; }
            [DataMember(Name = "hour")]
            public string Hour { get; set; }
        }

        [DataContract(Name = "myTicketsBody")]
        public class MyTicketsBody
        {
            [DataMember(Name = "favorites")]
            public Favorite[] Favorites { get; set; }
        }
    }
}
