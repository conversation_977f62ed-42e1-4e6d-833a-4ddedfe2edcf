﻿using GamesEngine;
using GamesEngine.Business;
using GamesEngine.Finance;
using GamesEngine.PurchaseOrders;
using GamesEngine.Settings;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Unit.Games.Tools;

namespace GamesEngineTests.Unit_Tests.Games.Lotto
{
	[TestClass]
	public class AuthorizationsTest
	{
		private string account = "*********";
		private string mysqlStringConection = "";//;"persistsecurityinfo=True;port=3306;Server=localhost;Database=lottocris;user id = *********; password=*********;SslMode=none";
		private string sqlStringConection = "";//"data source=localhost; initial catalog=example; user id=admin; password=***;multipleactiveresultsets=False";
		private readonly bool itIsThePresent = true;
		private DateTime Now = DateTime.Now;

		[TestMethod]
		public void IntegrationTest()
		{
			Company company = new Company(); CurrenciesTest.AddCurrencies();
			Movements.Storage = new MovementStorageMemory();
			Integration.ConfigurationForTest();
			Movements.Storage.CreateAuthorizationSequenceIfNotExists();

			int a = Movements.Storage.NextAuthorizationNumber();
			int b = Movements.Storage.NextAuthorizationNumber();

			Assert.AreEqual(1, a, "La 1er a authorizacion siempre debe ser 1. ");
			Assert.AreEqual(2, b, "La 2da b authorizacion siempre debe ser 2. ");
		}

		[TestMethod]
		public void IntegrationMysql()
		{
			if (string.IsNullOrEmpty(sqlStringConection)) Assert.Inconclusive("mysql string connection its required.");

			Movements.Storage = new MovementStorageMySQL(mysqlStringConection);
			Movements.Storage.CreateAuthorizationSequenceIfNotExists();

			int a = Movements.Storage.NextAuthorizationNumber();
			int b = Movements.Storage.NextAuthorizationNumber();

			Assert.AreNotEqual(0, a, "El valor a generado debe ser distinto a 0.");
			Assert.AreNotEqual(-1, a, "El valor b generado debe ser distinto a -1.");
			Assert.AreNotEqual(0, b, "El valor a generado debe ser distinto a 0.");
			Assert.AreNotEqual(-1, b, "El valor b generado debe ser distinto a -1.");
		}

		[TestMethod]
		public void IntegrationSql()
		{
			if (string.IsNullOrEmpty(sqlStringConection)) Assert.Inconclusive("sql string connection its required.");

			Movements.Storage = new MovementStorageSQLServer(sqlStringConection);
			Movements.Storage.CreateAuthorizationSequenceIfNotExists();

			int a = Movements.Storage.NextAuthorizationNumber();
			int b = Movements.Storage.NextAuthorizationNumber();

			Assert.AreNotEqual(0, a, "El valor generado debe ser distinto a 0.");
			Assert.AreNotEqual(-1, a, "El valor generado debe ser distinto a -1.");
			Assert.AreNotEqual(0, b, "El valor generado debe ser distinto a 0.");
			Assert.AreNotEqual(-1, b, "El valor generado debe ser distinto a -1.");
		}

		[TestMethod]
		public void Grade_Regrade_Grade_Mysql()
		{
			if (string.IsNullOrEmpty(mysqlStringConection)) Assert.Inconclusive("sql string connection its required.");

			Movements.Storage = new MovementStorageMySQL(mysqlStringConection);
			Movements.Storage.CreateAuthorizationSequenceIfNotExists();

			//Integration.UseKafka = true; //Ver porque con FALSE no funciona
			QueueInMemory queue = new QueueInMemory();
			Integration.Kafka = queue;

			Company company = new Company();CurrenciesTest.AddCurrencies();
			var store = company.Sales.CreateStore(1, "Test Store");
			store.MakeCurrent();
			AtAddress atAddress = new AtAddress($"{account}");
			Source manualSource = atAddress.CreateSource(itIsThePresent, Now, 1, Currencies.CODES.LR);
			Source lucky77Source = atAddress.CreateSource(itIsThePresent, Now, 2, Currencies.CODES.LR);
			Source reloadSource = atAddress.CreateSource(itIsThePresent, Now, 3, Currencies.CODES.LR);

			Source source = atAddress.GetSource(2);
			source.Accredit(itIsThePresent, Now, new LottoReward(800), "Mau", "ABC2240", store, "First deposit from the book.", "Ref");

			source = atAddress.GetSource(2);
			source.Accredit(itIsThePresent, Now, new LottoReward(100), "Otto Murillo", "2", store, "Lets  play", "Ref");

			source = atAddress.GetSource(2);
			source.Accredit(itIsThePresent, Now, new LottoReward(100), "Otto Murillo1", "2", store, "Lets  play", "Ref");

			atAddress.CreateAuthorization(itIsThePresent, Now, new Dollar(3.5m), *********, store, "ticketPick2StraightProd", "Ref");

			Authorization authorization = atAddress.GetAuthorizationForUpdate(*********);
			authorization.CreateFragments(1, 14,
				new List<string>() {
					"1000001-1", "1000001-2", "1000001-3", "1000001-4", "1000001-5", "1000001-6", "1000001-7",
					"1000001-8", "1000001-9", "1000001-10", "1000001-11", "1000001-12", "1000001-13", "1000001-14"},
				new List<string>(){
					"Lotto AK 12:04 PM Pick 2 10/10/2019 0-0 $0.25 ticket for $22.50 prize",
					"Lotto AK 12:04 PM Pick 2 10/10/2019 9-9 $0.25 ticket for $22.50 prize",
					"Lotto AK 12:04 PM Pick 2 10/10/2019 8-8 $0.25 ticket for $22.50 prize",
					"Lotto AK 12:04 PM Pick 2 10/10/2019 7-8 $0.25 ticket for $22.50 prize",
					"Lotto AK 12:04 PM Pick 2 10/10/2019 7-7 $0.25 ticket for $22.50 prize",
					"Lotto AK 12:04 PM Pick 2 10/10/2019 6-6 $0.25 ticket for $22.50 prize",
					"Lotto AK 12:04 PM Pick 2 10/10/2019 5-6 $0.25 ticket for $22.50 prize",
					"Lotto AK 12:04 PM Pick 2 10/10/2019 5-5 $0.25 ticket for $22.50 prize",
					"Lotto AK 12:04 PM Pick 2 10/10/2019 4-4 $0.25 ticket for $22.50 prize",
					"Lotto AK 12:04 PM Pick 2 10/10/2019 3-4 $0.25 ticket for $22.50 prize",
					"Lotto AK 12:04 PM Pick 2 10/10/2019 3-3 $0.25 ticket for $22.50 prize",
					"Lotto AK 12:04 PM Pick 2 10/10/2019 2-2 $0.25 ticket for $22.50 prize",
					"Lotto AK 12:04 PM Pick 2 10/10/2019 1-2 $0.25 ticket for $22.50 prize",
					"Lotto AK 12:04 PM Pick 2 10/10/2019 1-1 $0.25 ticket for $22.50 prize" }, 0.25m, 22.25m);

			atAddress.CreateAuthorization(itIsThePresent, Now, new Dollar(1.5m), 490263241, store, "ticketPick2StraightProd", "Ref");

			authorization = atAddress.GetAuthorizationForUpdate(490263241);
			authorization.CreateFragments(1, 6,
				new List<string>() { "1000002-1", "1000002-2", "1000002-3", "1000002-4", "1000002-5", "1000002-6" },
				new List<string>(){ "Lotto AK 12:04 PM Pick 2 10/10/2019 6-5 $0.25 ticket for $22.50 prize",
					"Lotto AK 12:04 PM Pick 2 10/10/2019 9-9 $0.25 ticket for $22.50 prize",
					"Lotto AK 12:04 PM Pick 2 10/10/2019 7-8 $0.25 ticket for $22.50 prize",
					"Lotto AK 12:04 PM Pick 2 10/10/2019 5-6 $0.25 ticket for $22.50 prize",
					"Lotto AK 12:04 PM Pick 2 10/10/2019 3-4 $0.25 ticket for $22.50 prize",
					"Lotto AK 12:04 PM Pick 2 10/10/2019 1-2 $0.25 ticket for $22.50 prize" }, 0.25m, 22.25m);

			authorization = atAddress.GetAuthorization(*********);
			atAddress
				.PreparePayments(itIsThePresent, authorization, new List<int>() { 2 }, Now, store, "", FragmentReason.Winner)
				.PreparePayments(itIsThePresent, authorization, new List<int>() { 1, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14 }, Now, store, "", FragmentReason.Loser)
				.ApplyChanges(itIsThePresent, "Game Admin");

			authorization = atAddress.GetAuthorization(490263241);
			atAddress
				.PreparePayments(itIsThePresent, authorization, new List<int>() { 2 }, Now, store, "", FragmentReason.Winner)
				.PreparePayments(itIsThePresent, authorization, new List<int>() { 1, 3, 4, 5, 6 }, Now, store, "", FragmentReason.Loser)
				.ApplyChanges(itIsThePresent, "Game Admin");

			//Regrade
			authorization = atAddress.GetAuthorization(*********);
			atAddress
				.PreparePayments(itIsThePresent, authorization, new List<int> { 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14 }, Now, store, "", FragmentReason.Loser)
				.ApplyChanges(itIsThePresent, "Game Admin");

			authorization = atAddress.GetAuthorization(490263241);
			atAddress
				.PreparePayments(itIsThePresent, authorization, new List<int> { 1, 2, 3, 4, 5, 6 }, Now, store, "", FragmentReason.Loser)
				.ApplyChanges(itIsThePresent, "Game Admin");

			int a = Movements.Storage.NextAuthorizationNumber();
			int b = Movements.Storage.NextAuthorizationNumber();

			Assert.AreNotEqual(0, a, "El valor a generado debe ser distinto a 0.");
			Assert.AreNotEqual(-1, a, "El valor b generado debe ser distinto a -1.");
			Assert.AreNotEqual(0, b, "El valor a generado debe ser distinto a 0.");
			Assert.AreNotEqual(-1, b, "El valor b generado debe ser distinto a -1.");
		}

		[TestMethod]
		public async Task Grade_Regrade_Grade_SqlAsync()
		{
			if (string.IsNullOrEmpty(sqlStringConection)) Assert.Inconclusive("sql string connection its required.");

			Movements.Storage = new MovementStorageSQLServer(sqlStringConection);
			Movements.Storage.CreateAuthorizationSequenceIfNotExists();

			//Integration.UseKafka = true; //Ver porque con FALSE no funciona
			QueueInMemory queue = new QueueInMemory();
			Integration.Kafka = queue;
			CashierActors c = new CashierActors("SQLServer", sqlStringConection, "");
			await c.LoadActorAsync(account);

			Company company = new Company();CurrenciesTest.AddCurrencies();
			var store = company.Sales.CreateStore(1, "Test Store");
			store.MakeCurrent();
			AtAddress atAddress = new AtAddress($"{account}");
			Source manualSource = atAddress.CreateSource(itIsThePresent, Now, 1, Currencies.CODES.LR);
			Source lucky77Source = atAddress.CreateSource(itIsThePresent, Now, 2, Currencies.CODES.LR);
			Source reloadSource = atAddress.CreateSource(itIsThePresent, Now, 3, Currencies.CODES.LR);

			Source source = atAddress.GetSource(2);
			source.Accredit(itIsThePresent, Now, new LottoReward(800), "Mau", "ABC2240", store, "First deposit from the book.", "Ref");
			
			source = atAddress.GetSource(2);
			source.Accredit(itIsThePresent, Now, new LottoReward(100), "Otto Murillo", "2", store, "Lets  play", "Ref");
			
			source = atAddress.GetSource(2);
			source.Accredit(itIsThePresent, Now, new LottoReward(100), "Otto Murillo1", "2", store, "Lets  play", "Ref");
			
			atAddress.CreateAuthorization(itIsThePresent, Now, new Dollar(3.5m), *********, store, "ticketPick2StraightProd", "Ref");

			Authorization authorization = atAddress.GetAuthorizationForUpdate(*********);
			authorization.CreateFragments(1, 14,
				new List<string>() {
					"1000001-1", "1000001-2", "1000001-3", "1000001-4", "1000001-5", "1000001-6", "1000001-7",
					"1000001-8", "1000001-9", "1000001-10", "1000001-11", "1000001-12", "1000001-13", "1000001-14"}, 
				new List<string>(){
					"Lotto AK 12:04 PM Pick 2 10/10/2019 0-0 $0.25 ticket for $22.50 prize",
					"Lotto AK 12:04 PM Pick 2 10/10/2019 9-9 $0.25 ticket for $22.50 prize",
					"Lotto AK 12:04 PM Pick 2 10/10/2019 8-8 $0.25 ticket for $22.50 prize",
					"Lotto AK 12:04 PM Pick 2 10/10/2019 7-8 $0.25 ticket for $22.50 prize",
					"Lotto AK 12:04 PM Pick 2 10/10/2019 7-7 $0.25 ticket for $22.50 prize",
					"Lotto AK 12:04 PM Pick 2 10/10/2019 6-6 $0.25 ticket for $22.50 prize",
					"Lotto AK 12:04 PM Pick 2 10/10/2019 5-6 $0.25 ticket for $22.50 prize",
					"Lotto AK 12:04 PM Pick 2 10/10/2019 5-5 $0.25 ticket for $22.50 prize",
					"Lotto AK 12:04 PM Pick 2 10/10/2019 4-4 $0.25 ticket for $22.50 prize",
					"Lotto AK 12:04 PM Pick 2 10/10/2019 3-4 $0.25 ticket for $22.50 prize",
					"Lotto AK 12:04 PM Pick 2 10/10/2019 3-3 $0.25 ticket for $22.50 prize",
					"Lotto AK 12:04 PM Pick 2 10/10/2019 2-2 $0.25 ticket for $22.50 prize",
					"Lotto AK 12:04 PM Pick 2 10/10/2019 1-2 $0.25 ticket for $22.50 prize",
					"Lotto AK 12:04 PM Pick 2 10/10/2019 1-1 $0.25 ticket for $22.50 prize" }, 0.25m, 22.25m);

			atAddress.CreateAuthorization(itIsThePresent, Now, new Dollar(1.5m), 490263241, store, "ticketPick2StraightProd", "Ref");

			authorization = atAddress.GetAuthorizationForUpdate(490263241);
			authorization.CreateFragments(1, 6,
				new List<string>(){ "1000002-1", "1000002-2", "1000002-3", "1000002-4", "1000002-5", "1000002-6"}, 
				new List<string>(){ "Lotto AK 12:04 PM Pick 2 10/10/2019 6-5 $0.25 ticket for $22.50 prize",
					"Lotto AK 12:04 PM Pick 2 10/10/2019 9-9 $0.25 ticket for $22.50 prize",
					"Lotto AK 12:04 PM Pick 2 10/10/2019 7-8 $0.25 ticket for $22.50 prize",
					"Lotto AK 12:04 PM Pick 2 10/10/2019 5-6 $0.25 ticket for $22.50 prize",
					"Lotto AK 12:04 PM Pick 2 10/10/2019 3-4 $0.25 ticket for $22.50 prize",
					"Lotto AK 12:04 PM Pick 2 10/10/2019 1-2 $0.25 ticket for $22.50 prize" }, 0.25m, 22.25m);

			authorization = atAddress.GetAuthorization(*********);
			atAddress
				.PreparePayments(itIsThePresent, authorization, new List<int>(){2}, Now, store, "", FragmentReason.Winner)
				.PreparePayments(itIsThePresent, authorization, new List<int>(){ 1, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14}, Now, store, "", FragmentReason.Loser)
				.ApplyChanges(itIsThePresent, "Game Admin");

			authorization = atAddress.GetAuthorization(490263241);
			atAddress
				.PreparePayments(itIsThePresent, authorization, new List<int>(){2}, Now, store, "", FragmentReason.Winner)
				.PreparePayments(itIsThePresent, authorization, new List<int>() { 1, 3, 4, 5, 6}, Now, store, "", FragmentReason.Loser)
				.ApplyChanges(itIsThePresent, "Game Admin");

			//Regrade
			authorization = atAddress.GetAuthorization(*********);
			atAddress.PreparePayments(itIsThePresent, authorization, new List<int> { 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14 }, Now, store, "", FragmentReason.Loser);
			//List<Authorization> discarted = atAddress.DiscartedAuthorizations().ToList();
			//foreach(var auth in discarted)
			//{
			//	Assert.IsFalse(auth.HasPendingsFragments());
			//}
			atAddress.ApplyChanges(itIsThePresent, "Game Admin");

			authorization = atAddress.GetAuthorization(490263241);
			atAddress.PreparePayments(itIsThePresent, authorization, new List<int> { 1, 2, 3, 4, 5, 6 }, Now, store, "", FragmentReason.Loser);
			//discarted = atAddress.DiscartedAuthorizations().ToList();
			//foreach (var auth in discarted)
			//{
			//	Assert.IsFalse(auth.HasPendingsFragments());
			//}
			atAddress.ApplyChanges(itIsThePresent, "Game Admin");

			int a = Movements.Storage.NextAuthorizationNumber();
			int b = Movements.Storage.NextAuthorizationNumber();

			Assert.AreNotEqual(0, a, "El valor a generado debe ser distinto a 0.");
			Assert.AreNotEqual(-1, a, "El valor b generado debe ser distinto a -1.");
			Assert.AreNotEqual(0, b, "El valor a generado debe ser distinto a 0.");
			Assert.AreNotEqual(-1, b, "El valor b generado debe ser distinto a -1.");
		}

        [TestMethod]
        public void CreateAuthorization_bug8871()
        {
            Integration.UseKafka = true;
            QueueInMemory queue = new QueueInMemory();
            Integration.Kafka = queue;

            Company company = new Company(); CurrenciesTest.AddCurrencies();
            Movements.Storage = new MovementStorageMemory();
            AtAddress atAddress = new AtAddress($"{account}");
            
            var store = company.Sales.CreateStore(1, "Test Store");
            store.MakeCurrent();
			var documentNumber = "*********";
            Balance balanceInFP = atAddress.CreateBalanceIfNotExists(Coinage.Coin(Currencies.CODES.FP));
            balanceInFP.Accredit(itIsThePresent, DateTime.Now, new FreePlay(50), "N/A", documentNumber, store, "concept", "Ref");

            var authorizations = new AuthorizationsNumbers(*********, 1);
			var useless = new DateTime(2019, 10, 10, 12, 4, 0);
            atAddress.CreateAuthorization(itIsThePresent, Now, new FreePlay(10.0m), authorizations.Numbers.ToList(), store, "concept", "Ref", "FP", useless);
			Assert.AreEqual(10m, balanceInFP.Locked);
        }

        [TestMethod]
		public void DiferrentToWinAmountsTest_Bug4195()
		{
			Company company = new Company(); CurrenciesTest.AddCurrencies();
			Movements.Storage = new MovementStorageMemory();
			AtAddress atAddress = new AtAddress($"{account}");

			Integration.ConfigurationForTest();
			var store = company.Sales.CreateStore(1, "Test Store");
			store.MakeCurrent();
			Source asisource = atAddress.CreateSource(itIsThePresent, Now, 1, Coinage.Coin(Currencies.CODES.USD));
			asisource.Accredit(false, Now, new Dollar(10.0m), "cris", "1", store, "a", "a");
			Authorization authorization = atAddress.CreateAuthorization(itIsThePresent, Now, new Dollar(10.0m), *********, store, "ticketPick3StraightProd", "Ref");
			List<string> references = new List<string> { "1-1", "2-2", "3-3", "4-4", "5-5", "6-6", "7-7", "8-8", "9-9", "10-10" };
			List<string> descriptions = new List<string> { "Description 1", "Description 2", "Description 3", "Description 4", "Description 5", "Description 6", "Description 7", "Description 8", "Description 9", "Description 10" };
			List<decimal> toWins = new List<decimal> { 100.01m, 100.02m, 100.03m, 100.04m, 100.05m, 100.06m, 100.07m, 100.08m, 100.08m, 100.10m };
			authorization.CreateFragments(1, 10, references, descriptions, 1.0m, toWins);

			List<AuthorizationFragment> fragments= authorization.PendingFragments().ToList();
			Assert.AreEqual(toWins[0], fragments[0].ToWin);
			Assert.AreEqual(toWins[1], fragments[1].ToWin);
			Assert.AreEqual(toWins[2], fragments[2].ToWin);
			Assert.AreEqual(toWins[3], fragments[3].ToWin);
			Assert.AreEqual(toWins[4], fragments[4].ToWin);
			Assert.AreEqual(toWins[5], fragments[5].ToWin);
			Assert.AreEqual(toWins[6], fragments[6].ToWin);
			Assert.AreEqual(toWins[7], fragments[7].ToWin);
			Assert.AreEqual(toWins[8], fragments[8].ToWin);
			Assert.AreEqual(toWins[9], fragments[9].ToWin);
		}

		[TestMethod]
		public void DiferrentToRiskAmountsTest_Bug4214()
		{
			//Integration.SavePreviousSettings(tempUseKafka: false, tempUseKafkaForAuto: false);
			Company company = new Company(); CurrenciesTest.AddCurrencies();
			Movements.Storage = new MovementStorageMemory();
			AtAddress atAddress = new AtAddress($"{account}");

			Integration.ConfigurationForTest();

			var store = company.Sales.CreateStore(1, "Test Store");
			store.MakeCurrent();
			Source asisource = atAddress.CreateSource(itIsThePresent, Now, 1, Coinage.Coin(Currencies.CODES.USD));
			asisource.Accredit(false, Now, new Dollar(10.0m), "cris", "1", store, "a", "a");
			Authorization authorization = atAddress.CreateAuthorization(itIsThePresent, Now, new Dollar(10.0m), *********, store, "ticketPick3StraightProd", "Ref");
			List<string> references = new List<string> { "1-1", "2-2", "3-3", "4-4", "5-5", "6-6", "7-7", "8-8", "9-9", "10-10" };
			List<string> descriptions = new List<string> { "Description 1", "Description 2", "Description 3", "Description 4", "Description 5", "Description 6", "Description 7", "Description 8", "Description 9", "Description 10" };
			List<decimal> toWins = new List<decimal> { 100.01m, 100.02m, 100.03m, 100.04m, 100.05m, 100.06m, 100.07m, 100.08m, 100.08m, 100.10m };
			List<decimal> risks = new List<decimal> { 0.1m, 0.2m, 0.3m, 0.4m, 0.5m, 0.6m, 0.7m, 0.8m, 0.9m, 5.5m };
			authorization.CreateFragments(1, 10, references, descriptions, risks, toWins);

			List<AuthorizationFragment> fragments = authorization.PendingFragments().ToList();
			Assert.AreEqual(risks[0], fragments[0].Risk);
			Assert.AreEqual(risks[1], fragments[1].Risk);
			Assert.AreEqual(risks[2], fragments[2].Risk);
			Assert.AreEqual(risks[3], fragments[3].Risk);
			Assert.AreEqual(risks[4], fragments[4].Risk);
			Assert.AreEqual(risks[5], fragments[5].Risk);
			Assert.AreEqual(risks[6], fragments[6].Risk);
			Assert.AreEqual(risks[7], fragments[7].Risk);
			Assert.AreEqual(risks[8], fragments[8].Risk);
			Assert.AreEqual(risks[9], fragments[9].Risk);

			//Integration.RestoreToDefaultSettings();
		}
		public void DiferrentToRiskAmountsTestSameToWin_Bug4214()
		{
			Movements.Storage = new MovementStorageMemory();
			AtAddress atAddress = new AtAddress($"{account}");

			Integration.ConfigurationForTest();

			Company company = new Company();CurrenciesTest.AddCurrencies();
			var store = company.Sales.CreateStore(1, "Test Store");
			store.MakeCurrent();
			Authorization authorization = atAddress.CreateAuthorization(itIsThePresent, Now, new Dollar(10.0m), *********, store, "ticketPick3StraightProd", "Ref");
			List<string> references = new List<string> { "1-1", "2-2", "3-3", "4-4", "5-5", "6-6", "7-7", "8-8", "9-9", "10-10" };
			List<string> descriptions = new List<string> { "Description 1", "Description 2", "Description 3", "Description 4", "Description 5", "Description 6", "Description 7", "Description 8", "Description 9", "Description 10" };
			decimal toWin = 104.5m;
			List<decimal> risks = new List<decimal> { 0.1m, 0.2m, 0.3m, 0.4m, 0.5m, 0.6m, 0.7m, 0.8m, 0.9m, 5.5m };
			authorization.CreateFragments(1, 10, references, descriptions, risks, toWin);

			List<AuthorizationFragment> fragments = authorization.PendingFragments().ToList();
			Assert.AreEqual(risks[0], fragments[0].Risk);
			Assert.AreEqual(risks[1], fragments[1].Risk);
			Assert.AreEqual(risks[2], fragments[2].Risk);
			Assert.AreEqual(risks[3], fragments[3].Risk);
			Assert.AreEqual(risks[4], fragments[4].Risk);
			Assert.AreEqual(risks[5], fragments[5].Risk);
			Assert.AreEqual(risks[6], fragments[6].Risk);
			Assert.AreEqual(risks[7], fragments[7].Risk);
			Assert.AreEqual(risks[8], fragments[8].Risk);
			Assert.AreEqual(risks[9], fragments[9].Risk);
		}


		[TestMethod]
		public void ToWinInZeroValidationTest_Bug4195()
		{
			Company company = new Company(); CurrenciesTest.AddCurrencies();
			Movements.Storage = new MovementStorageMemory();
			AtAddress atAddress = new AtAddress($"{account}");

			Integration.ConfigurationForTest();
			var store = company.Sales.CreateStore(1, "Test Store");
			store.MakeCurrent();
			Source asisource = atAddress.CreateSource(itIsThePresent, Now, 1, Coinage.Coin(Currencies.CODES.USD));
			asisource.Accredit(false, Now, new Dollar(10.0m), "cris", "1", store, "a", "a");
			Authorization authorization = atAddress.CreateAuthorization(itIsThePresent, Now, new Dollar(10.0m), *********, store, "ticketPick3StraightProd", "Ref");
			List<string> references = new List<string> { "1-1", "2-2", "3-3", "4-4", "5-5", "6-6", "7-7", "8-8", "9-9", "10-10" };
			List<string> descriptions = new List<string> { "Description 1", "Description 2", "Description 3", "Description 4", "Description 5", "Description 6", "Description 7", "Description 8", "Description 9", "Description 10" };
			List<decimal> toWins = new List<decimal> { 100.01m, 100.02m, 100.03m, 100.04m, 100.05m, 100.06m, 100.07m, 100.08m, 100.08m, 000.00m };

			try
			{
				authorization.CreateFragments(1, 10, references, descriptions, 1.0m, toWins);
				Assert.Fail($"Towin amount in zero are not allowed.");
			}
			catch (Exception e)
			{
				Assert.AreEqual("To win must be upper than zero.", e.Message);
			}
		}

		[TestMethod]
		public void TowinsLenghtValidationTest_Bug4195()
		{
			Company company = new Company(); CurrenciesTest.AddCurrencies();
			Movements.Storage = new MovementStorageMemory();
			AtAddress atAddress = new AtAddress($"{account}");

			Integration.ConfigurationForTest();
			var store = company.Sales.CreateStore(1, "Test Store");
			store.MakeCurrent();
			Source asisource = atAddress.CreateSource(itIsThePresent, Now, 1, Coinage.Coin(Currencies.CODES.USD));
			asisource.Accredit(false, Now, new Dollar(10.0m), "cris", "1", store, "a", "a");
			Authorization authorization = atAddress.CreateAuthorization(itIsThePresent, Now, new Dollar(10.0m), *********, store, "ticketPick3StraightProd", "Ref");
			List<string> references = new List<string> { "1-1", "2-2", "3-3", "4-4", "5-5", "6-6", "7-7", "8-8", "9-9", "10-10" };
			List<string> descriptions = new List<string> { "Description 1", "Description 2", "Description 3", "Description 4", "Description 5", "Description 6", "Description 7", "Description 8", "Description 9", "Description 10" };
			List<decimal> toWins = new List<decimal> { 100.01m, 100.02m, 100.03m, 100.04m, 100.05m, 100.06m, 100.07m, 100.08m, 100.08m, 100.09m, 100.10m };

			try
			{
				authorization.CreateFragments(1, 10, references, descriptions, 1.0m, toWins);
				Assert.Fail($"Towin lenght must be the same of references and descriptions.");
			}
			catch (Exception e)
			{
				Assert.AreEqual($"Towins length {toWins.Count()} can not be upper or lower than low consecutive and high consecutive", e.Message);
			}
		}

		[TestMethod]
		public void CreateFakeAuthorizationWithoutLock()
		{
			Company company = new Company(); CurrenciesTest.AddCurrencies();
			Movements.Storage = new MovementStorageMemory();
			AtAddress atAddress = new AtAddress($"{account}");

			Integration.ConfigurationForTest();
			var store = company.Sales.CreateStore(1, "Test Store");
			store.MakeCurrent();

			string accountNumber1 = "Cr-09***";
			atAddress.CreateAccountIfNotExists(Coinage.Coin(Currencies.CODES.USD), accountNumber1);
			Authorization authorization = atAddress.CreateFakeAuthorizationWithoutLock("USD", *********, accountNumber1, DateTime.Now);
			authorization.CreateFakeFragment("ref-1", "description", 1.0m);

			List<AuthorizationFragment> fragments = authorization.PendingFragments().ToList();
			Assert.AreEqual(1, fragments.Count);
			Assert.AreEqual(0m, fragments[0].Risk);

			atAddress.PrepareFakePayment(authorization, Now, store, FragmentReason.Winner).ApplyChanges(itIsThePresent, "N/A");
			fragments = authorization.PendingFragments().ToList();
			Assert.AreEqual(0, fragments.Count);
			fragments = authorization.PayedFragments().ToList();
			Assert.AreEqual(1, fragments.Count);
		}
	}
}
