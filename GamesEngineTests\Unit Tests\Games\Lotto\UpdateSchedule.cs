﻿using GamesEngine.Bets;
using GamesEngine.Business;
using GamesEngine.Domains;
using GamesEngine.Exchange;
using GamesEngine.Games.Lotto;
using GamesEngine.Location;
using GamesEngine.PurchaseOrders;
using GamesEngine.Settings;
using GamesEngine.Time;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using System;
using System.Globalization;
using Unit.Games.Tools;
using static GamesEngine.Finance.PaymentChannels;
using static GamesEngine.Gameboards.Lotto.Ticket;

namespace GamesEngineTests.Unit_Tests.Games.Lotto
{

	[TestClass]
    public class UpdateSchedule
    {

        [TestMethod]
        public void ScheduleVersionsTest_1()
        {
            Company c = new Company();
            c.Sales.CreateStore(1, "Test Store").MakeCurrent();
            DateTime now = new DateTime(2019, 03, 29, 11, 12, 13);
            var lotteries = c.Lotto900();
            State s = new State("TX", "Texas");
            var lottery = lotteries.GetOrCreateLottery(3, s);
            for (int i = 0; i <= 6; i++) lottery.Every(9, 30, i, now, "Game Admin");
            var schedule = lottery.GetScheduleId(1);

            schedule.Update(true, "V1", "0123456", 9, 32, now, "Game Admin");
            var last = lottery.LastKnownGradeOrDefault(schedule);
            Assert.AreEqual(last, DateTime.MinValue);

            lottery.SetNoAction(true, new DateTime(2019, 03, 29, 9, 32, 0), now, "Game Admin");
            last = lottery.LastKnownGradeOrDefault(schedule);
            Assert.AreEqual(last, new DateTime(2019, 03, 29, 9, 32, 0));

            schedule.Update(true, "V1", "0123456", 9, 34, now, "Game Admin");
            last = lottery.LastKnownGradeOrDefault(schedule);
            Assert.AreEqual(last, new DateTime(2019, 03, 29, 9, 32, 0));
            var x = schedule.ToDateTime(new DateTime(2019, 03, 29, 9, 32, 0));
            Assert.AreEqual(x, new DateTime(2019, 03, 29, 9, 32, 0));
        }

        [TestMethod]
        public void ScheduleVersionsTest_2()
        {
            Company c = new Company();
            c.Sales.CreateStore(1, "Test Store").MakeCurrent();
            DateTime now = new DateTime(2019, 03, 29, 11, 12, 13);
            var lotteries = c.Lotto900();
            State s = new State("TX", "Texas");
            var lottery = lotteries.GetOrCreateLottery(3, s);
            for (int i = 0; i <= 6; i++) lottery.Every(9, 30, i, now, "Game Admin");
            var schedule = lottery.GetScheduleId(1);

            schedule.Update(true, "V1", "0123456", 9, 32, now, "Game Admin");
            var last = lottery.LastKnownGradeOrDefault(schedule);
            Assert.AreEqual(last, DateTime.MinValue);

            lottery.SetNoAction(true, new DateTime(2019, 03, 29, 9, 32, 0), now, "Game Admin");
            last = lottery.LastKnownGradeOrDefault(schedule);
            Assert.AreEqual(last, new DateTime(2019, 03, 29, 9, 32, 0));

            now = new DateTime(2019, 03, 30, 11, 12, 13);
            lottery.SetNoAction(true, new DateTime(2019, 03, 30, 9, 32, 0), now, "Game Admin");
            last = lottery.LastKnownGradeOrDefault(schedule);
            Assert.AreEqual(last, new DateTime(2019, 03, 30, 9, 32, 0));
            var x = schedule.ToDateTime(new DateTime(2019, 06, 01, 17, 06, 0));
            Assert.AreEqual(x, new DateTime(2019, 06, 01, 9, 32, 0));
        }

        [TestMethod]
        public void ScheduleVersionsTest_3()
        {
            Company c = new Company();
            c.Sales.CreateStore(1, "Test Store").MakeCurrent();
            DateTime now = new DateTime(2019, 03, 29, 11, 12, 13);
            var lotteries = c.Lotto900();
            State s = new State("TX", "Texas");
            var lottery = lotteries.GetOrCreateLottery(3, s);
            for (int i = 0; i <= 6; i++) lottery.Every(9, 32, i, now, "Game Admin");
            var schedule = lottery.GetScheduleId(1);

            lottery.SetNoAction(true, new DateTime(2019, 03, 29, 9, 32, 0), now, "Game Admin");
            var last = lottery.LastKnownGradeOrDefault(schedule);
            Assert.AreEqual(last, new DateTime(2019, 03, 29, 9, 32, 0));

            now = new DateTime(2019, 03, 30, 11, 12, 13);
            lottery.SetNoAction(true, new DateTime(2019, 03, 30, 9, 32, 0), now, "Game Admin");
            last = lottery.LastKnownGradeOrDefault(schedule);
            Assert.AreEqual(last, new DateTime(2019, 03, 30, 9, 32, 0));

            var x = schedule.ToDateTime(new DateTime(2019, 06, 01, 17, 06, 0));
            Assert.AreEqual(x, new DateTime(2019, 06, 01, 9, 32, 0));
        }

        [TestMethod]
        public void ScheduleVersionsTest_4()
        {
            var culture = new CultureInfo("en-US");
            CultureInfo.DefaultThreadCurrentCulture = culture;
            CultureInfo.DefaultThreadCurrentUICulture = culture;

            Company c = new Company();
            c.Sales.CreateStore(1, "Test Store").MakeCurrent();
            DateTime now = new DateTime(2019, 03, 25, 11, 12, 13);
            var lotteries = c.Lotto900();
            State s = new State("TX", "Texas");
            var lottery = lotteries.GetOrCreateLottery(3, s);
            for (int i = 0; i <= 6; i++) lottery.Every(9, 30, i, now, "Game Admin");
            var schedule = lottery.GetScheduleId(1);

            lottery.SetNoAction(true, new DateTime(2019, 03, 25, 9, 30, 0), now, "Game Admin");
            var last = lottery.LastKnownGradeOrDefault(schedule);
            Assert.AreEqual(last, new DateTime(2019, 03, 25, 9, 30, 0));

            now = new DateTime(2019, 03, 27, 11, 12, 13);
            schedule.Update(true, "V1", "0123456", 9, 32, now, "Game Admin");
            last = lottery.LastKnownGradeOrDefault(schedule);
            Assert.AreEqual(last, new DateTime(2019, 03, 25, 9, 30, 0));

            lottery.SetNoAction(true, new DateTime(2019, 03, 27, 9, 32, 0), now, "Game Admin");
            last = lottery.LastKnownGradeOrDefault(schedule);
            Assert.AreEqual(last, new DateTime(2019, 03, 27, 9, 32, 0));

            var x = schedule.ToDateTime(new DateTime(2019, 06, 01, 17, 06, 0));
            Assert.AreEqual(x, new DateTime(2019, 06, 01, 9, 32, 0));

            var y = schedule.ToDateTime(new DateTime(2019, 03, 25, 17, 06, 0));
            Assert.AreEqual(y, new DateTime(2019, 03, 25, 9, 30, 0));

            var z = schedule.ToDateTime(new DateTime(2019, 03, 27, 17, 06, 0));
            Assert.AreEqual(z, new DateTime(2019, 03, 27, 9, 32, 0));
        }

        [TestMethod]
        public void ScheduleVersionsTest_5()
        {
            Company c = new Company();
            c.Sales.CreateStore(1, "Test Store").MakeCurrent();
            DateTime now = new DateTime(2019, 03, 25, 11, 12, 13);
            var lotteries = c.Lotto900();
            State s = new State("TX", "Texas");
            var lottery = lotteries.GetOrCreateLottery(3, s);
            for (int i = 0; i <= 6; i++) lottery.Every(9, 30, i, now, "Game Admin");
            var schedule = lottery.GetScheduleId(1);

            lottery.SetNoAction(true, new DateTime(2019, 03, 25, 9, 30, 0), now, "Game Admin");

            var schedules = lotteries.FinishedAndRegradedSchedulesOf(new DateTime(2019, 03, 25, 9, 30, 0));
            foreach (var finishedLotteries in schedules)
            {
                var drawDate = finishedLotteries.ToDateTime(new DateTime(2019, 03, 25, 9, 30, 0));
                var isNoAction1 = lottery.IsScheduleMarkedAsNoActionAt(new DateTime(2019, 03, 25, 9, 30, 0));
                var isNoAction2 = lottery.IsScheduleMarkedAsNoActionAt(drawDate);
                Assert.AreEqual(isNoAction1, isNoAction2);
            }
        }

        [TestMethod]
        public void ScheduleVersionsTest_6()
        {
            Company c = new Company();
            c.Sales.CreateStore(1, "Test Store").MakeCurrent();
            DateTime now = new DateTime(2019, 04, 01, 11, 12, 13);
            var lotteries = c.Lotto900();
            State s = new State("TX", "Texas");
            var lottery = lotteries.GetOrCreateLottery(3, s);
            for (int i = 0; i <= 6; i++) lottery.Every(12, 10, i, now, "Game Admin");
            var schedule = lottery.GetScheduleId(1);

            schedule.Update(true, "V1", "0123456", 12, 0, now, "Game Admin");
            var last = lottery.LastKnownGradeOrDefault(schedule);
            Assert.AreEqual(last, DateTime.MinValue);

            var schedules = lotteries.PendingAndNoRegradedSchedulesAt(new DateTime(2019, 04, 01, 23, 59, 0));
            foreach (var pendingLotteries in schedules)
            {
                var drawDate = pendingLotteries.ToDateTime(new DateTime(2019, 04, 01, 23, 59, 59));
                var isNoAction1 = lottery.IsScheduleMarkedAsNoActionAt(new DateTime(2019, 04, 01, 23, 59, 0));
                var isNoAction2 = lottery.IsScheduleMarkedAsNoActionAt(drawDate);
                Assert.AreEqual(isNoAction1, isNoAction2);
            }
        }

        [TestMethod]
        public void ScheduleVersionsTest_7()
        {
            var culture = new CultureInfo("en-US");
            CultureInfo.DefaultThreadCurrentCulture = culture;
            CultureInfo.DefaultThreadCurrentUICulture = culture;

            Company c = new Company();
            c.Sales.CreateStore(1, "Test Store").MakeCurrent();
            DateTime now = new DateTime(2019, 04, 01, 17, 08, 00);
            var lotteries = c.Lotto900();
            State s = new State("GA", "Georgia");
            var lottery = lotteries.GetOrCreateLottery(4, s);
            for (int i = 0; i <= 6; i++) lottery.Every(12, 14, i, now, "Game Admin");
            var schedule = lottery.GetScheduleId(1);

            bool notifyEvent = false;
            var report = (lottery as LotteryPick<Pick4>).DrawPicks(new DateTime(2019, 04, 01, 12, 14, 0), "9999", now, true, "Erick", notifyEvent);
            var completed = lottery.IsCompletedFor(2019, 04, 01, 12,14);
            Assert.AreEqual(completed, true);

            lottery.SetNoAction(true, new DateTime(2019, 04, 01, 12, 14, 0), now, "Game Admin");
            var isNoAction1 = lottery.IsScheduleMarkedAsNoActionAt(new DateTime(2019, 04, 01, 12, 14, 0));
            Assert.AreEqual(isNoAction1, true);

            completed = lottery.IsCompletedFor(2019, 04, 01, 12, 14);
            Assert.AreEqual(completed, true);

        }

        [TestMethod]
        public void ScheduleVersionsTest_8()
        {
            Company c = new Company();
            c.Sales.CreateStore(1, "Test Store").MakeCurrent();
            DateTime now = new DateTime(2019, 04, 03, 17, 30, 00);
            var lotteries = c.Lotto900();
            State s = new State("GA", "Georgia");
            LotteryPick<Pick3> lottery = (LotteryPick<Pick3>)lotteries.GetOrCreateLottery(3, s);
            for (int i = 0; i <= 6; i++) lottery.Every(18, 25, i, now, "Game Admin");
            var schedule = lottery.GetScheduleId(1);
            CurrenciesTest.AddCurrencies();
            Player player = c.GetOrCreateCustomerById("*********").Player;

            //Comprar ticket ayer
            lottery.StraightTicket(player, new DateTime(2019, 04, 03, 18, 25, 0), false, "1", "2", "3", Selection.BALLS, now, 0.5m, 1, lotteries.Prizes);

            now = new DateTime(2019, 04, 04, 12, 30, 00);
            schedule.Update(true, "V1", "0123456", 14, 49, now, "Game Admin");
            var last = lottery.LastKnownGradeOrDefault(schedule);
            Assert.AreEqual(last, DateTime.MinValue);

            now = new DateTime(2019, 04, 04, 15, 30, 00);
            bool notifyEvent = false;
            var report = lottery.DrawPicks(new DateTime(2019, 04, 04, 14, 49, 00), "765", now, true, "Erick", notifyEvent);
            var completed = lottery.IsCompletedFor(2019, 04, 04, 14, 49);
            Assert.AreEqual(completed, true);

            now = new DateTime(2019, 04, 04, 15, 35, 00);
            schedule.Update(true, "V2", "0123456", 14, 45, now, "Game Admin");
            last = lottery.LastKnownGradeOrDefault(schedule);
            Assert.AreEqual(last, new DateTime(2019, 04, 04, 14, 49, 00));
        }

        [TestMethod]
        public void ScheduleVersionsTest_Bug_2435()
        {
            Company c = new Company();
            c.Sales.CreateStore(1, "Test Store").MakeCurrent();
            DateTime now = new DateTime(2019, 04, 19, 10, 00, 00);
            var lotteries = c.Lotto900();
            State s = new State("GA", "Georgia");
            LotteryPick<Pick3> lottery = (LotteryPick<Pick3>)lotteries.GetOrCreateLottery(3, s);
            for (int i = 0; i <= 6; i++) lottery.Every(12, 10, i, now, "Game Admin");
            var schedule = lottery.GetScheduleId(1);

            schedule.Update(true, "V1", "0123456", 13, 00, now, "Game Admin");
            var last = lottery.LastKnownGradeOrDefault(schedule);

            var schedules = lotteries.PendingAndNoRegradedSchedulesAt(new DateTime(2019, 02, 01, 23, 59, 0));
            foreach (var pendingLotteries in schedules)
            {
                var drawDate = pendingLotteries.ToDateTime(new DateTime(2019, 02, 01, 23, 59, 0));
                Assert.AreEqual(new DateTime(2019, 02, 01, 12, 10, 00), drawDate);
            }

            schedules = lotteries.PendingAndNoRegradedSchedulesAt(new DateTime(2019, 06, 01, 23, 59, 0));
            foreach (var pendingLotteries in schedules)
            {
                var drawDate = pendingLotteries.ToDateTime(new DateTime(2019, 06, 01, 23, 59, 0));
                Assert.AreEqual(new DateTime(2019, 06, 01, 13, 00, 00), drawDate);
            }
        }

        [TestMethod]
        public void ScheduleVersionsTest_Bug_2435_2()
        {
            Company c = new Company();
            c.Sales.CreateStore(1, "Test Store").MakeCurrent();
            DateTime now = new DateTime(2019, 04, 19, 10, 00, 00);
            var lotteries = c.Lotto900();
            State s = new State("GA", "Georgia");
            LotteryPick<Pick3> lottery = (LotteryPick<Pick3>)lotteries.GetOrCreateLottery(3, s);
            foreach (int i in new int[] { 1, 2, 3, 4 }) lottery.Every(12, 10, i, now, "Game Admin");
            var schedule = lottery.GetScheduleId(1);

            schedule.Update(true, "V1", "12356", 13, 00, now, "Game Admin");
            var last = lottery.LastKnownGradeOrDefault(schedule);

            var schedules = lotteries.PendingAndNoRegradedSchedulesAt(new DateTime(2019, 02, 01, 23, 59, 0));//Saturday no hay sorteo
            Assert.AreEqual(schedules.GetEnumerator().MoveNext(), false);

            schedules = lotteries.PendingAndNoRegradedSchedulesAt(new DateTime(2019, 06, 01, 23, 59, 0));//Saturday si hay sorteo
            foreach (var pendingLotteries in schedules)
            {
                var drawDate = pendingLotteries.ToDateTime(new DateTime(2019, 06, 01, 23, 59, 0));
                Assert.AreEqual(new DateTime(2019, 06, 01, 13, 00, 00), drawDate);
            }
        }

        //-----------------------------
        //Pruebas del grade
        [TestMethod]
        public void ScheduleVersionsTest_10()
        {
            //Integration.UseKafka = true;
            QueueInMemory queue = new QueueInMemory();
            Integration.Kafka = queue;

            Company company = new Company();
            company.Sales.CreateStore(1, "Test Store").MakeCurrent();
            DateTime now = new DateTime(2019, 02, 02, 9, 00, 00);
            company.Accounting = new MockAccounting();
            CurrenciesTest.AddCurrencies();
            var lotteries = company.Lotto900();
            State s = lotteries.GetOrCreateState("GA", "Georgia", now, "Game Admin");

            LotteryPick<Pick3> lottery = (LotteryPick<Pick3>)lotteries.GetOrCreateLottery(3, s);
            for (int i = 0; i <= 6; i++) lottery.Every(10, 00, i, now, "Game Admin");

            Player player = company.GetOrCreateCustomerById("*********").Player;
            string states = "GA"; string hours = "10:00 AM"; string strIsPresentPlayerBeforeToCloseStore = "true";
            string dates = "02/02/2019"; string numbers = "1,2,3"; string selectionMode = "balls";
            string withFireballs = "false";
            string pickNumber = "3"; decimal ticketAmount = 10; string includedNumbersForInput = "";
            string gameType = "Straight"; decimal founds = 80000;
            Product ticketPick3StraightProd = lotteries.GetOrCreateProductById(1);
            ticketPick3StraightProd.Description = "Ticket pick 3 straight";
            ticketPick3StraightProd.Price = 1;
            Domain domain = company.Sales.CreateDomain(false, 1, "localhost", Agents.INSIDER);

            //Comprar un ticket #1
            NextDatesAccumulator nextDatesAccumulator = new NextDatesAccumulator(lotteries, domain, now, pickNumber, dates, states, hours, strIsPresentPlayerBeforeToCloseStore);
            ExcludeSubtickets excludedSubtickets = new ExcludeSubtickets();
            Customer customer = company.CustomerByPlayer(player);
            OrderCart myOrder = (OrderCart)company.GetNewOrder(customer);

            Order order = company.CreateTicketOrder(lotteries, player, states, dates, withFireballs, numbers, selectionMode, ticketAmount, includedNumbersForInput, excludedSubtickets, gameType, myOrder, ticketPick3StraightProd, nextDatesAccumulator, domain);
            company.AddOrders(myOrder, founds);
            int orderNumber = order.Number;
            order = company.OrderByNumber(orderNumber);
            order.AuthorizationId = 123;
            var marketplace = new Marketplace(company, "CR");
            var agent = marketplace.AddAgent("1");
            myOrder.Agent = (GamesEngine.Exchange.Agent)agent;
            company.SaleTickets(true, myOrder, now, founds, 1, domain);
            Assert.AreEqual(1, orderNumber);
            //Fin compra #1

            //Grade compra #1
            bool notifyEvent = false;
            now = new DateTime(2019, 02, 02, 10, 00, 00);
            var report = lottery.DrawPicks(new DateTime(2019, 02, 02, 10, 00, 00), "765", now, true, "Erick", notifyEvent);
            var completed = lottery.IsCompletedFor(2019, 02, 02, 10, 00);
            Assert.AreEqual(completed, true);
            Assert.AreEqual(1, report.TotalTickets);

            //Comprar un ticket #2
            now = new DateTime(2019, 04, 11, 10, 00, 00);
            string hours2 = "10:00 AM"; string dates2 = "04/11/2019"; string numbers2 = "4,5,6";
            string withFireBall2 = "false";
            NextDatesAccumulator nextDatesAccumulator2 = new NextDatesAccumulator(lotteries, domain, now, pickNumber, dates2, states, hours2, strIsPresentPlayerBeforeToCloseStore);
            ExcludeSubtickets excludedSubtickets2 = new ExcludeSubtickets();
            OrderCart myOrder2 = (OrderCart)company.GetNewOrder(customer);

            Order order2 = company.CreateTicketOrder(lotteries, player, states, dates2, withFireBall2, numbers2, selectionMode, ticketAmount, includedNumbersForInput, excludedSubtickets, gameType, myOrder2, ticketPick3StraightProd, nextDatesAccumulator2, domain);
            company.AddOrders(myOrder2, founds);
            int orderNumber2 = order2.Number;
            order2 = company.OrderByNumber(orderNumber2);
            order2.AuthorizationId = 124;
            myOrder2.Agent = (GamesEngine.Exchange.Agent)agent;
            company.SaleTickets(true, myOrder2, now, founds, 1, domain);
            Assert.AreEqual(2, orderNumber2);
            //Fin compra #2

            //Grade compra #2
            now = new DateTime(2019, 04, 11, 12, 00, 00);
            report = lottery.DrawPicks(new DateTime(2019, 04, 11, 10, 00, 00), "891", now, true, "Erick", notifyEvent);

            //Despues de gradear el segundo, la idea es que el primero NO debe existir
            TicketsOfDraw ticketsInMemory = lottery.TicketInMemory(new DateTime(2019, 02, 02, 10, 00, 00));
            Assert.AreEqual(0, ticketsInMemory.Count);
        }

        //Pruebas del grade / regrade
        [TestMethod]
        public void ScheduleVersionsTest_11()
        {
            //Integration.UseKafka = true;
            QueueInMemory queue = new QueueInMemory();
            Integration.Kafka = queue;

            Company company = new Company();
            company.Sales.CreateStore(1, "Test Store").MakeCurrent();
            DateTime now = new DateTime(2019, 02, 02, 9, 00, 00);
            company.Accounting = new MockAccounting();
            CurrenciesTest.AddCurrencies();
            var lotteries = company.Lotto900();
            State s = lotteries.GetOrCreateState("GA", "Georgia", now, "Game Admin");

            LotteryPick<Pick3> lottery = (LotteryPick<Pick3>)lotteries.GetOrCreateLottery(3, s);
            for (int i = 0; i <= 6; i++) lottery.Every(10, 00, i, now, "Game Admin");

            Player player = company.GetOrCreateCustomerById("*********").Player;
            string states = "GA"; string hours = "10:00 AM"; string strIsPresentPlayerBeforeToCloseStore = "true";
            string dates = "02/02/2019"; string numbers = "1,2,3"; string selectionMode = "balls";
            string withFireballs = "false";
            string pickNumber = "3"; decimal ticketAmount = 10; string includedNumbersForInput = "";
            string gameType = "Straight"; decimal founds = 80000;
            Product ticketPick3StraightProd = lotteries.GetOrCreateProductById(1);
            ticketPick3StraightProd.Description = "Ticket pick 3 straight";
            ticketPick3StraightProd.Price = 1;
            Domain domain = company.Sales.CreateDomain(false, 1, "localhost", Agents.INSIDER);

            //Comprar un ticket #1
            NextDatesAccumulator nextDatesAccumulator = new NextDatesAccumulator(lotteries, domain, now, pickNumber, dates, states, hours, strIsPresentPlayerBeforeToCloseStore);
            ExcludeSubtickets excludedSubtickets = new ExcludeSubtickets();
            Customer customer = company.CustomerByPlayer(player);
            OrderCart myOrder = (OrderCart)company.GetNewOrder(customer);

            Order order = company.CreateTicketOrder(lotteries, player, states, dates, withFireballs, numbers, selectionMode, ticketAmount, includedNumbersForInput, excludedSubtickets, gameType, myOrder, ticketPick3StraightProd, nextDatesAccumulator, domain);
            company.AddOrders(myOrder, founds);
            int orderNumber = order.Number;
            order = company.OrderByNumber(orderNumber);
            order.AuthorizationId = 123;
            var marketplace = new Marketplace(company, "CR");
            var agent = marketplace.AddAgent("1");
            myOrder.Agent = (GamesEngine.Exchange.Agent)agent;
            company.SaleTickets(true, myOrder, now, founds, 1, domain);
            Assert.AreEqual(1, orderNumber);
            //Fin compra #1

            //Grade compra #1
            now = new DateTime(2019, 02, 02, 10, 00, 00);
            bool notifyEvent = false;
            var report = lottery.DrawPicks(new DateTime(2019, 02, 02, 10, 00, 00), "765", now, true, "Erick", notifyEvent);
            var completed = lottery.IsCompletedFor(2019, 02, 02, 10, 00);
            Assert.AreEqual(completed, true);
            Assert.AreEqual(1, report.TotalTickets);

            //Regrade de compra #1
            now = new DateTime(2019, 02, 02, 10, 00, 00);
            report = lottery.Regrade(true, new DateTime(2019, 02, 02, 10, 00, 00), now, "Erick");
            completed = lottery.IsCompletedFor(2019, 02, 02, 10, 00);
            Assert.AreEqual(completed, false);
            Assert.AreEqual(1, report.TotalTickets);

            //Comprar un ticket #2
            now = new DateTime(2019, 04, 11, 10, 00, 00);
            string hours2 = "10:00 AM"; string dates2 = "04/11/2019"; string numbers2 = "4,5,6";
            string withFireBall2 = "false";
            NextDatesAccumulator nextDatesAccumulator2 = new NextDatesAccumulator(lotteries, domain, now, pickNumber, dates2, states, hours2, strIsPresentPlayerBeforeToCloseStore);
            ExcludeSubtickets excludedSubtickets2 = new ExcludeSubtickets();
            OrderCart myOrder2 = (OrderCart)company.GetNewOrder(customer);

            Order order2 = company.CreateTicketOrder(lotteries, player, states, dates2, withFireBall2, numbers2, selectionMode, ticketAmount, includedNumbersForInput, excludedSubtickets, gameType, myOrder2, ticketPick3StraightProd, nextDatesAccumulator2, domain);
            company.AddOrders(myOrder2, founds);
            int orderNumber2 = order2.Number;
            order2 = company.OrderByNumber(orderNumber2);
            order2.AuthorizationId = 124;
            myOrder2.Agent = (GamesEngine.Exchange.Agent)agent;
            company.SaleTickets(true, myOrder2, now, founds, 1, domain);
            Assert.AreEqual(2, orderNumber2);
            //Fin compra #2

            //Grade compra #2
            now = new DateTime(2019, 04, 11, 12, 00, 00);
            report = lottery.DrawPicks(new DateTime(2019, 04, 11, 10, 00, 00), "891", now, true, "Erick", notifyEvent);

            //Despues de gradear el segundo, la idea es que el primero al ser REGRADED DEBE existir
            TicketsOfDraw ticketsInMemory = lottery.TicketInMemory(new DateTime(2019, 02, 02, 10, 00, 00));
            Assert.AreEqual(ticketsInMemory.DateOfSet, new DateTime(2019, 02, 02, 10, 00, 00));
        }

        //Pruebas del no action
        [TestMethod]
        public void ScheduleVersionsTest_12()
        {
            //Integration.UseKafka = true;
            QueueInMemory queue = new QueueInMemory();
            Integration.Kafka = queue;

            Company company = new Company();
            company.Sales.CreateStore(1, "Test Store").MakeCurrent();
            DateTime now = new DateTime(2019, 02, 02, 9, 00, 00);
            company.Accounting = new MockAccounting();
            CurrenciesTest.AddCurrencies();
            var lotteries = company.Lotto900();
            State s = lotteries.GetOrCreateState("GA", "Georgia", now, "Game Admin");

            LotteryPick<Pick3> lottery = (LotteryPick<Pick3>)lotteries.GetOrCreateLottery(3, s);
            for (int i = 0; i <= 6; i++) lottery.Every(10, 00, i, now, "Game Admin");

            Player player = company.GetOrCreateCustomerById("*********").Player;
            string states = "GA"; string hours = "10:00 AM"; string strIsPresentPlayerBeforeToCloseStore = "true";
            string dates = "02/02/2019"; string numbers = "1,2,3"; string selectionMode = "balls";
            string withFireballs = "false";
            string pickNumber = "3"; decimal ticketAmount = 10; string includedNumbersForInput = "";
            string gameType = "Straight"; decimal founds = 80000;
            Product ticketPick3StraightProd = lotteries.GetOrCreateProductById(1);
            ticketPick3StraightProd.Description = "Ticket pick 3 straight";
            ticketPick3StraightProd.Price = 1;
            Domain domain = company.Sales.CreateDomain(false, 1, "localhost", Agents.INSIDER);

            //Comprar un ticket #1
            NextDatesAccumulator nextDatesAccumulator = new NextDatesAccumulator(lotteries, domain, now, pickNumber, dates, states, hours, strIsPresentPlayerBeforeToCloseStore);
            ExcludeSubtickets excludedSubtickets = new ExcludeSubtickets();
            Customer customer = company.CustomerByPlayer(player);
            OrderCart myOrder = (OrderCart)company.GetNewOrder(customer);

            Order order = company.CreateTicketOrder(lotteries, player, states, dates, withFireballs, numbers, selectionMode, ticketAmount, includedNumbersForInput, excludedSubtickets, gameType, myOrder, ticketPick3StraightProd, nextDatesAccumulator, domain);
            company.AddOrders(myOrder, founds);
            int orderNumber = order.Number;
            order = company.OrderByNumber(orderNumber);
            order.AuthorizationId = 123;
            var marketplace = new Marketplace(company, "CR");
            var agent = marketplace.AddAgent("1");
            myOrder.Agent = (GamesEngine.Exchange.Agent)agent;
            company.SaleTickets(true, myOrder, now, founds, 1, domain);
            Assert.AreEqual(1, orderNumber);
            //Fin compra #1

            //Grade compra #1
            now = new DateTime(2019, 02, 02, 10, 00, 00);
            bool notifyEvent = false;
            var report = lottery.DrawPicks(new DateTime(2019, 02, 02, 10, 00, 00), "765", now, true, "Erick", notifyEvent);
            var completed = lottery.IsCompletedFor(2019, 02, 02, 10, 00);
            Assert.AreEqual(completed, true);
            Assert.AreEqual(1, report.TotalTickets);

            //NoAction de compra #1
            now = new DateTime(2019, 02, 02, 10, 00, 00);
            NoActionReport noActionReport = lottery.SetNoAction(true, new DateTime(2019, 02, 02, 10, 00, 00), now, "Erick");
            completed = lottery.IsCompletedFor(2019, 02, 02, 10, 00);
            Assert.AreEqual(completed, true);
            Assert.AreEqual(1, noActionReport.TotalTickets);

            //Comprar un ticket #2
            now = new DateTime(2019, 04, 11, 10, 00, 00);
            string hours2 = "10:00 AM"; string dates2 = "04/11/2019"; string numbers2 = "4,5,6";
            string withFireBall2 = "false";
            NextDatesAccumulator nextDatesAccumulator2 = new NextDatesAccumulator(lotteries, domain, now, pickNumber, dates2, states, hours2, strIsPresentPlayerBeforeToCloseStore);
            ExcludeSubtickets excludedSubtickets2 = new ExcludeSubtickets();
            OrderCart myOrder2 = (OrderCart)company.GetNewOrder(customer);

            Order order2 = company.CreateTicketOrder(lotteries, player, states, dates2, withFireBall2, numbers2, selectionMode, ticketAmount, includedNumbersForInput, excludedSubtickets, gameType, myOrder2, ticketPick3StraightProd, nextDatesAccumulator2, domain);
            company.AddOrders(myOrder2, founds);
            int orderNumber2 = order2.Number;
            order2 = company.OrderByNumber(orderNumber2);
            order2.AuthorizationId = 124;
            myOrder2.Agent = (GamesEngine.Exchange.Agent)agent;
            company.SaleTickets(true, myOrder2, now, founds, 1, domain);
            Assert.AreEqual(2, orderNumber2);
            //Fin compra #2

            //Grade compra #2
            now = new DateTime(2019, 04, 11, 12, 00, 00);
            report = lottery.DrawPicks(new DateTime(2019, 04, 11, 10, 00, 00), "891", now, true, "Erick", notifyEvent);

            //Despues de gradear el segundo, la idea es que el primero al ser NOACTION NO debe existir
            TicketsOfDraw ticketsInMemory = lottery.TicketInMemory(new DateTime(2019, 02, 02, 10, 00, 00));
            Assert.AreEqual(0, ticketsInMemory.Count);
        }

        //Prueba de tickets pending
        [TestMethod]
        public void ScheduleVersionsTest_13()
        {
            Company company = new Company();
            company.Sales.CreateStore(1, "Test Store").MakeCurrent();
            DateTime now = new DateTime(2019, 02, 02, 9, 00, 00);
            company.Accounting = new MockAccounting();
            CurrenciesTest.AddCurrencies();
            var lotteries = company.Lotto900();
            State s = lotteries.GetOrCreateState("GA", "Georgia", now, "Game Admin");

            LotteryPick<Pick3> lottery = (LotteryPick<Pick3>)lotteries.GetOrCreateLottery(3, s);
            for (int i = 0; i <= 6; i++) lottery.Every(10, 00, i, now, "Game Admin");

            Player player = company.GetOrCreateCustomerById("*********").Player;
            string states = "GA"; string hours = "10:00 AM"; string strIsPresentPlayerBeforeToCloseStore = "true";
            string dates = "02/02/2019"; string numbers = "1,2,3"; string selectionMode = "balls";
            string withFireballs = "false";
            string pickNumber = "3"; decimal ticketAmount = 10; string includedNumbersForInput = "";
            string gameType = "Straight"; decimal founds = 80000;
            Product ticketPick3StraightProd = lotteries.GetOrCreateProductById(1);
            ticketPick3StraightProd.Description = "Ticket pick 3 straight";
            ticketPick3StraightProd.Price = 1;
            Domain domain = company.Sales.CreateDomain(false, 1, "localhost", Agents.INSIDER);

            //Comprar un ticket #1
            NextDatesAccumulator nextDatesAccumulator = new NextDatesAccumulator(lotteries, domain, now, pickNumber, dates, states, hours, strIsPresentPlayerBeforeToCloseStore);
            ExcludeSubtickets excludedSubtickets = new ExcludeSubtickets();
            Customer customer = company.CustomerByPlayer(player);
            OrderCart myOrder = (OrderCart)company.GetNewOrder(customer);

            Order order = company.CreateTicketOrder(lotteries, player, states, dates, withFireballs, numbers, selectionMode, ticketAmount, includedNumbersForInput, excludedSubtickets, gameType, myOrder, ticketPick3StraightProd, nextDatesAccumulator, domain);
            company.AddOrders(myOrder, founds);
            int orderNumber = order.Number;
            order = company.OrderByNumber(orderNumber);
            order.AuthorizationId = 123;
            var marketplace = new Marketplace(company, "CR");
            var agent = marketplace.AddAgent("1");
            myOrder.Agent = (GamesEngine.Exchange.Agent)agent;
            company.SaleTickets(true, myOrder, now, founds, 1, domain);
            Assert.AreEqual(1, orderNumber);
            //Fin compra #1

            //Comprar un ticket #2
            now = new DateTime(2019, 04, 11, 10, 00, 00);
            string hours2 = "10:00 AM"; string dates2 = "04/11/2019"; string numbers2 = "4,5,6";
            string withFireBall2 = "false";
            NextDatesAccumulator nextDatesAccumulator2 = new NextDatesAccumulator(lotteries, domain, now, pickNumber, dates2, states, hours2, strIsPresentPlayerBeforeToCloseStore);
            ExcludeSubtickets excludedSubtickets2 = new ExcludeSubtickets();
            OrderCart myOrder2 = (OrderCart)company.GetNewOrder(customer);

            Order order2 = company.CreateTicketOrder(lotteries, player, states, dates2, withFireBall2, numbers2, selectionMode, ticketAmount, includedNumbersForInput, excludedSubtickets, gameType, myOrder2, ticketPick3StraightProd, nextDatesAccumulator2, domain);
            company.AddOrders(myOrder2, founds);
            int orderNumber2 = order2.Number;
            order2 = company.OrderByNumber(orderNumber2);
            order2.AuthorizationId = 124;
            myOrder2.Agent = (GamesEngine.Exchange.Agent)agent;
            company.SaleTickets(true, myOrder2, now, founds, 1, domain);
            Assert.AreEqual(2, orderNumber2);
            //Fin compra #2

            //Deben existir ambos tickets pues aun no se han gradeado
            TicketsOfDraw ticketsInMemory = lottery.TicketInMemory(new DateTime(2019, 02, 02, 10, 00, 00));
            Assert.AreEqual(ticketsInMemory.DateOfSet, new DateTime(2019, 02, 02, 10, 00, 00));
            ticketsInMemory = lottery.TicketInMemory(new DateTime(2019, 04, 11, 10, 00, 00));
            Assert.AreEqual(ticketsInMemory.DateOfSet, new DateTime(2019, 04, 11, 10, 00, 00));
        }

        //Pruebas del grade con un horario adicional
        [TestMethod]
        public void ScheduleVersionsTest_14()
        {
            //Integration.UseKafka = true;
            QueueInMemory queue = new QueueInMemory();
            Integration.Kafka = queue;

            Company company = new Company();
            company.Sales.CreateStore(1, "Test Store").MakeCurrent();
            DateTime now = new DateTime(2019, 02, 02, 9, 00, 00);
            company.Accounting = new MockAccounting();
            CurrenciesTest.AddCurrencies();
            var lotteries = company.Lotto900();
            State s = lotteries.GetOrCreateState("GA", "Georgia", now, "Game Admin");

            LotteryPick<Pick3> lottery = (LotteryPick<Pick3>)lotteries.GetOrCreateLottery(3, s);
            for (int i = 0; i <= 6; i++) lottery.Every(10, 00, i, now, "Game Admin");
            for (int i = 0; i <= 6; i++) lottery.Every(11, 00, i, now, "Game Admin");

            Player player = company.GetOrCreateCustomerById("*********").Player;
            string states = "GA"; string hours = "10:00 AM"; string strIsPresentPlayerBeforeToCloseStore = "true";
            string dates = "02/02/2019"; string numbers = "1,2,3"; string selectionMode = "balls";
            string withFireballs = "false";
            string pickNumber = "3"; decimal ticketAmount = 10; string includedNumbersForInput = "";
            string gameType = "Straight"; decimal founds = 80000;
            Product ticketPick3StraightProd = lotteries.GetOrCreateProductById(1);
            ticketPick3StraightProd.Description = "Ticket pick 3 straight";
            ticketPick3StraightProd.Price = 1;
            Domain domain = company.Sales.CreateDomain(false, 1, "localhost", Agents.INSIDER);

            //Comprar un ticket #1
            NextDatesAccumulator nextDatesAccumulator = new NextDatesAccumulator(lotteries, domain, now, pickNumber, dates, states, hours, strIsPresentPlayerBeforeToCloseStore);
            ExcludeSubtickets excludedSubtickets = new ExcludeSubtickets();
            Customer customer = company.CustomerByPlayer(player);
            OrderCart myOrder = (OrderCart)company.GetNewOrder(customer);

            Order order = company.CreateTicketOrder(lotteries, player, states, dates, withFireballs, numbers, selectionMode, ticketAmount, includedNumbersForInput, excludedSubtickets, gameType, myOrder, ticketPick3StraightProd, nextDatesAccumulator, domain);
            company.AddOrders(myOrder, founds);
            int orderNumber = order.Number;
            order = company.OrderByNumber(orderNumber);
            order.AuthorizationId = 123;
            var marketplace = new Marketplace(company, "CR");
            var agent = marketplace.AddAgent("1");
            myOrder.Agent = (GamesEngine.Exchange.Agent)agent;
            company.SaleTickets(true, myOrder, now, founds, 1, domain);
            Assert.AreEqual(1, orderNumber);
            //Fin compra #1

            //Grade compra #1
            now = new DateTime(2019, 02, 02, 10, 00, 00);
            bool notifyEvent = false;
            var report = lottery.DrawPicks(new DateTime(2019, 02, 02, 10, 00, 00), "765", now, true, "Erick", notifyEvent);
            var completed = lottery.IsCompletedFor(2019, 02, 02, 10, 00);
            Assert.AreEqual(completed, true);
            Assert.AreEqual(1, report.TotalTickets);

            //Comprar un ticket #2
            now = new DateTime(2019, 04, 11, 10, 00, 00);
            string hours2 = "11:00 AM"; string dates2 = "04/11/2019"; string numbers2 = "4,5,6";
            string withFireBall2 = "false";
            NextDatesAccumulator nextDatesAccumulator2 = new NextDatesAccumulator(lotteries, domain, now, pickNumber, dates2, states, hours2, strIsPresentPlayerBeforeToCloseStore);
            ExcludeSubtickets excludedSubtickets2 = new ExcludeSubtickets();
            OrderCart myOrder2 = (OrderCart)company.GetNewOrder(customer);

            Order order2 = company.CreateTicketOrder(lotteries, player, states, dates2, withFireBall2, numbers2, selectionMode, ticketAmount, includedNumbersForInput, excludedSubtickets, gameType, myOrder2, ticketPick3StraightProd, nextDatesAccumulator2, domain);
            company.AddOrders(myOrder2, founds);
            int orderNumber2 = order2.Number;
            order2 = company.OrderByNumber(orderNumber2);
            order2.AuthorizationId = 124;
            myOrder2.Agent = (GamesEngine.Exchange.Agent)agent;
            company.SaleTickets(true, myOrder2, now, founds, 1, domain);
            Assert.AreEqual(2, orderNumber2);
            //Fin compra #2

            //Grade compra #2
            now = new DateTime(2019, 04, 11, 12, 00, 00);
            report = lottery.DrawPicks(new DateTime(2019, 04, 11, 11, 00, 00), "891", now, true, "Erick", notifyEvent);

            //El primero deberia NO existir
            TicketsOfDraw ticketsInMemory = lottery.TicketInMemory(new DateTime(2019, 02, 02, 10, 00, 00));
            Assert.AreEqual(0, ticketsInMemory.Count);
        }

        [TestMethod]
        public void ScheduleVersionsTest_RemoveDisabledSchedules()
        {
            //Integration.UseKafka = true;
            QueueInMemory queue = new QueueInMemory();
            Integration.Kafka = queue;

            Company company = new Company();
            company.Sales.CreateStore(1, "Test Store").MakeCurrent();
            DateTime now = new DateTime(2019, 02, 02, 9, 00, 00);
            company.Accounting = new MockAccounting();
            CurrenciesTest.AddCurrencies();
            var lotteries = company.Lotto900();
            State s = lotteries.GetOrCreateState("GA", "Georgia");

            LotteryPick<Pick3> lottery = (LotteryPick<Pick3>)lotteries.GetOrCreateLottery(3, s);
            for (int i = 0; i <= 6; i++) lottery.Every(10, 00, i, now, "Game Admin");

            Player player = company.GetOrCreateCustomerById("*********").Player;
            string states = "GA"; string hours = "10:00 AM"; string strIsPresentPlayerBeforeToCloseStore = "true";
            string dates = "02/02/2019"; string numbers = "1,2,3"; string selectionMode = "balls";
            string withFireballs = "false";
            string pickNumber = "3"; decimal ticketAmount = 10; string includedNumbersForInput = "";
            string gameType = "Straight"; decimal founds = 80000;
            Product ticketPick3StraightProd = lotteries.GetOrCreateProductById(1);
            ticketPick3StraightProd.Description = "Ticket pick 3 straight";
            ticketPick3StraightProd.Price = 1;
            Domain domain = company.Sales.CreateDomain(false, 1, "localhost", Agents.INSIDER);

            //Comprar un ticket #1
            NextDatesAccumulator nextDatesAccumulator = new NextDatesAccumulator(lotteries, domain, now, pickNumber, dates, states, hours, strIsPresentPlayerBeforeToCloseStore);
            ExcludeSubtickets excludedSubtickets = new ExcludeSubtickets();
            Customer customer = company.CustomerByPlayer(player);
            OrderCart myOrder = (OrderCart)company.GetNewOrder(customer);

            Order order = company.CreateTicketOrder(lotteries, player, states, dates, withFireballs, numbers, selectionMode, ticketAmount, includedNumbersForInput, excludedSubtickets, gameType, myOrder, ticketPick3StraightProd, nextDatesAccumulator, domain);
            company.AddOrders(myOrder, founds);
            int orderNumber = order.Number;
            order = company.OrderByNumber(orderNumber);
            order.AuthorizationId = 123;
            var marketplace = new Marketplace(company, "CR");
            var agent = marketplace.AddAgent("1");
            myOrder.Agent = (GamesEngine.Exchange.Agent)agent;
            company.SaleTickets(true, myOrder, now, founds, 1, domain);
            Assert.AreEqual(1, orderNumber);
            //Fin compra #1

            //Grade compra #1
            now = new DateTime(2019, 02, 02, 10, 00, 00);
            bool notifyEvent = false;
            var report = lottery.DrawPicks(new DateTime(2019, 02, 02, 10, 00, 00), "765", now, true, "Erick", notifyEvent);
            var completed = lottery.IsCompletedFor(2019, 02, 02, 10, 00);
            Assert.AreEqual(completed, true);
            Assert.AreEqual(1, report.TotalTickets);

            //Cancelar el schedule
            Lottery lotteryToCancel = lotteries.GetLottery(3, s);
            Schedule scheduleToCancel = lotteryToCancel.GetScheduleId(1);
            lottery.Cancel(scheduleToCancel, now, "Erick");

            //Comprar un ticket #2
            now = new DateTime(2019, 04, 11, 10, 00, 00);
            string hours2 = "10:00 AM"; string dates2 = "04/11/2019"; string numbers2 = "4,5,6";
            string withFireBall2 = "false";
            NextDatesAccumulator nextDatesAccumulator2 = new NextDatesAccumulator(lotteries, domain, now, pickNumber, dates2, states, hours2, strIsPresentPlayerBeforeToCloseStore);
            ExcludeSubtickets excludedSubtickets2 = new ExcludeSubtickets();
            OrderCart myOrder2 = (OrderCart)company.GetNewOrder(customer);

            Order order2 = company.CreateTicketOrder(lotteries, player, states, dates2, withFireBall2, numbers2, selectionMode, ticketAmount, includedNumbersForInput, excludedSubtickets, gameType, myOrder2, ticketPick3StraightProd, nextDatesAccumulator2, domain);
            company.AddOrders(myOrder2, founds);
            int orderNumber2 = order2.Number;
            order2 = company.OrderByNumber(orderNumber2);
            order2.AuthorizationId = 124;
            myOrder2.Agent = (GamesEngine.Exchange.Agent)agent;
            company.SaleTickets(true, myOrder2, now, founds, 1, domain);
            Assert.AreEqual(2, orderNumber2);
            //Fin compra #2

            //Grade compra #2
            now = new DateTime(2019, 04, 11, 12, 00, 00);
            report = lottery.DrawPicks(new DateTime(2019, 04, 11, 10, 00, 00), "891", now, true, "Erick", notifyEvent);

            //Despues de gradear el segundo, la idea es que el primero NO debe existir
            TicketsOfDraw ticketsInMemory = lottery.TicketInMemory(new DateTime(2019, 02, 02, 10, 00, 00));
            Assert.AreEqual(0, ticketsInMemory.Count);
        }

        [TestMethod]
        public void ScheduleVersionsTest_Bug2928()
        {
            Company company = new Company();
            company.Sales.CreateStore(1, "Test Store").MakeCurrent();
            company.Accounting = new MockAccounting();
            CurrenciesTest.AddCurrencies();
            var lotteries = company.Lotto900();
            State s = lotteries.GetOrCreateState("GA", "Georgia");

            LotteryPick<Pick3> lottery = (LotteryPick<Pick3>)lotteries.GetOrCreateLottery(3, s);
            for (int i = 0; i <= 6; i++) lottery.Every(10, 00, i);

            DateTime now = new DateTime(2019, 05, 02, 9, 00, 00);
            Player player = company.GetOrCreateCustomerById("*********").Player;
            string states = "GA"; string hours = "10:00 AM"; string strIsPresentPlayerBeforeToCloseStore = "true";
            string dates = "05/02/2019"; string numbers = "1,2,3"; string selectionMode = "balls";
            string withFireballs = "false";
            string pickNumber = "3"; decimal ticketAmount = 10; string includedNumbersForInput = "";
            string gameType = "Straight"; decimal founds = 80000;
            Product ticketPick3StraightProd = lotteries.GetOrCreateProductById(1);
            ticketPick3StraightProd.Description = "Ticket pick 3 straight";
            ticketPick3StraightProd.Price = 1;
            Domain domain = company.Sales.CreateDomain(false, 1, "localhost", Agents.INSIDER);

            //Comprar un ticket #1
            NextDatesAccumulator nextDatesAccumulator = new NextDatesAccumulator(lotteries, domain, now, pickNumber, dates, states, hours, strIsPresentPlayerBeforeToCloseStore);
            ExcludeSubtickets excludedSubtickets = new ExcludeSubtickets();
            Customer customer = company.CustomerByPlayer(player);
            OrderCart myOrder = (OrderCart)company.GetNewOrder(customer);

            Order order = company.CreateTicketOrder(lotteries, player, states, dates, withFireballs, numbers, selectionMode, ticketAmount, includedNumbersForInput, excludedSubtickets, gameType, myOrder, ticketPick3StraightProd, nextDatesAccumulator, domain);
            company.AddOrders(myOrder, founds);
            int orderNumber = order.Number;
            order = company.OrderByNumber(orderNumber);
            order.AuthorizationId = 123;
            var marketplace = new Marketplace(company, "CR");
            var agent = marketplace.AddAgent("1");
            myOrder.Agent = (GamesEngine.Exchange.Agent)agent;
            company.SaleTickets(true, myOrder, now, founds, 1, domain);
            Assert.AreEqual(1, orderNumber);
            //Fin compra #1

            now = new DateTime(2019, 05, 03, 10, 05, 00);

            var schedule = lottery.GetScheduleId(1);
            schedule.Update(true, "V1", "0123456", 10, 30, now, "Game Admin");

            var schedules = lotteries.PendingAndNoRegradedSchedulesAt(new DateTime(2019, 05, 02, 23, 59, 0));
            foreach (var pendingLotteries in schedules)
            {
                var drawDate = pendingLotteries.ToDateTime(new DateTime(2019, 05, 02, 23, 59, 59));
                Assert.AreEqual(drawDate, new DateTime(2019, 05, 02, 10, 30, 0));
            }
        }

        [TestMethod]
        public void ScheduleVersionsTest_Bug2928_2()
        {
            Company c = new Company();
            c.Sales.CreateStore(1, "Test Store").MakeCurrent();
            c.Accounting = new MockAccounting();

            var lotteries = c.Lotto900();
            State s = lotteries.GetOrCreateState("GA", "Georgia");

            LotteryPick<Pick3> lottery = (LotteryPick<Pick3>)lotteries.GetOrCreateLottery(3, s);
            for (int i = 0; i <= 6; i++) lottery.Every(10, 00, i);

            DateTime now = new DateTime(2019, 05, 03, 10, 05, 00);
            bool notifyEvent = false;
            var report = lottery.DrawPicks(new DateTime(2019, 05, 01, 10, 00, 00), "891", now, true, "Erick", notifyEvent);

            now = new DateTime(2019, 05, 03, 10, 35, 00);
            var schedule = lottery.GetScheduleId(1);
            schedule.Update(true, "V1", "0123456", 10, 30, now, "Game Admin");

            var schedules = lotteries.PendingAndNoRegradedSchedulesAt(new DateTime(2019, 05, 02, 23, 59, 0));
            Assert.IsTrue(schedules.GetEnumerator().MoveNext());
            foreach (var pendingLotteries in schedules)
            {
                var drawDate = pendingLotteries.ToDateTime(new DateTime(2019, 05, 02, 23, 59, 59));
                Assert.AreEqual(drawDate, new DateTime(2019, 05, 02, 10, 30, 0));
            }

            var schedules2 = lotteries.PendingAndNoRegradedSchedulesAt(new DateTime(2019, 05, 05, 23, 59, 0));
            Assert.IsTrue(schedules2.GetEnumerator().MoveNext());
            foreach (var pendingLotteries in schedules2)
            {
                var drawDate = pendingLotteries.ToDateTime(new DateTime(2019, 05, 05, 23, 59, 59));
                Assert.AreEqual(drawDate, new DateTime(2019, 05, 05, 10, 30, 0));
            }

            var schedules3 = lotteries.PendingAndNoRegradedSchedulesAt(new DateTime(2019, 04, 05, 23, 59, 0));
            Assert.IsTrue(schedules3.GetEnumerator().MoveNext());
            foreach (var pendingLotteries in schedules3)
            {
                var drawDate = pendingLotteries.ToDateTime(new DateTime(2019, 04, 05, 23, 59, 59));
                Assert.AreEqual(drawDate, new DateTime(2019, 04, 05, 10, 00, 0));
            }
        }

        [TestMethod]
        public void ScheduleVersionsTest_Bug3037()
        {
            Company c = new Company();
            c.Sales.CreateStore(1, "Test Store").MakeCurrent();
            c.Accounting = new MockAccounting();

            var lotteries = c.Lotto900();
            State s = lotteries.GetOrCreateState("GA", "Georgia");

            LotteryPick<Pick3> lottery = (LotteryPick<Pick3>)lotteries.GetOrCreateLottery(3, s);
            for (int i = 0; i <= 6; i++) lottery.Every(10, 00, i);

            DateTime now = new DateTime(2019, 05, 03, 10, 05, 00);
            var schedule = lottery.GetScheduleId(1);
            schedule.Update(true, "V1", "0123456", 9, 50, now, "Game Admin");

            now = new DateTime(2019, 05, 03, 9, 55, 00);
            bool notifyEvent = false;
            var report = lottery.DrawPicks(new DateTime(2019, 05, 03, 9, 50, 00), "891", now, true, "Erick", notifyEvent);

            var schedules = lotteries.FinishedAndRegradedSchedulesOf(new DateTime(2019, 05, 03, 00, 00, 0).AddDays(1).AddMilliseconds(-1));
            Assert.IsTrue(schedules.GetEnumerator().MoveNext());
            foreach (var finishedLotteries in schedules)
            {
                var drawDate = finishedLotteries.ToDateTime(new DateTime(2019, 05, 03, 00, 00, 00).AddDays(1).AddMilliseconds(-1));
                Assert.AreEqual(drawDate, new DateTime(2019, 05, 03, 9, 50, 0));
                var drawDate2 = finishedLotteries.ToDateTime(new DateTime(2019, 05, 03, 8, 00, 00));
                Assert.AreEqual(drawDate2, new DateTime(2019, 05, 03, 10, 00, 0));
            }
        }

        [TestMethod]
        public void ScheduleVersionsTest_Bug3118_Escenario_SinProblema_Update_Update_NoAction()
        {
            Company company = new Company();
            company.Sales.CreateStore(1, "Test Store").MakeCurrent();
            company.Accounting = new MockAccounting();
            CurrenciesTest.AddCurrencies();
            var lotteries = company.Lotto900();
            State s = lotteries.GetOrCreateState("GA", "Georgia");

            LotteryPick<Pick3> lottery = (LotteryPick<Pick3>)lotteries.GetOrCreateLottery(3, s);
            for (int i = 0; i <= 6; i++) lottery.Every(10, 00, i);

            DateTime now = new DateTime(2019, 05, 24, 9, 00, 00);
            Player player = company.GetOrCreateCustomerById("*********").Player;
            string states = "GA"; string hours = "10:00 AM"; string strIsPresentPlayerBeforeToCloseStore = "true";
            string dates = "05/27/2019"; string numbers = "1,2,3"; string selectionMode = "balls";
            string withFireballs = "false";
            string pickNumber = "3"; decimal ticketAmount = 10; string includedNumbersForInput = "";
            string gameType = "Straight"; decimal founds = 80000;
            Product ticketPick3StraightProd = lotteries.GetOrCreateProductById(1);
            ticketPick3StraightProd.Description = "Ticket pick 3 straight";
            ticketPick3StraightProd.Price = 1;
            Domain domain = company.Sales.CreateDomain(false, 1, "localhost", Agents.INSIDER);

            //Comprar un ticket #1
            NextDatesAccumulator nextDatesAccumulator = new NextDatesAccumulator(lotteries, domain, now, pickNumber, dates, states, hours, strIsPresentPlayerBeforeToCloseStore);
            ExcludeSubtickets excludedSubtickets = new ExcludeSubtickets();
            Customer customer = company.CustomerByPlayer(player);
            OrderCart myOrder = (OrderCart)company.GetNewOrder(customer);

            Order order = company.CreateTicketOrder(lotteries, player, states, dates, withFireballs, numbers, selectionMode, ticketAmount, includedNumbersForInput, excludedSubtickets, gameType, myOrder, ticketPick3StraightProd, nextDatesAccumulator, domain);
            company.AddOrders(myOrder, founds);
            int orderNumber = order.Number;
            order = company.OrderByNumber(orderNumber);
            order.AuthorizationId = 123;
            var marketplace = new Marketplace(company, "CR");
            var agent = marketplace.AddAgent("1");
            myOrder.Agent = (GamesEngine.Exchange.Agent)agent;
            company.SaleTickets(true, myOrder, now, founds, 1, domain);
            Assert.AreEqual(1, orderNumber);
            //Fin compra #1

            now = new DateTime(2019, 05, 24, 10, 05, 00);

            var schedule = lottery.GetScheduleId(1);
            schedule.Update(true, "V1", "0123456", 10, 30, now, "Game Admin");

            var schedules = lotteries.PendingAndNoRegradedSchedulesAt(new DateTime(2019, 05, 24, 23, 59, 59));
            Assert.IsTrue(schedules.GetEnumerator().MoveNext());
            foreach (var pendingLotteries in schedules)
            {
                var drawDate = pendingLotteries.ToDateTime(new DateTime(2019, 05, 24, 23, 59, 59));
                Assert.AreEqual(drawDate, new DateTime(2019, 05, 24, 10, 30, 0));
            }

            now = new DateTime(2019, 05, 24, 10, 40, 00);
            int scheduleIdUpdated = schedule.Id;
            schedule = lottery.GetScheduleId(scheduleIdUpdated);
            schedule.Update(true, "V2", "0123456", 10, 00, now, "Game Admin");

            schedules = lotteries.PendingAndNoRegradedSchedulesAt(new DateTime(2019, 05, 24, 23, 59, 59));
            Assert.IsTrue(schedules.GetEnumerator().MoveNext());
            foreach (var pendingLotteries in schedules)
            {
                var drawDate = pendingLotteries.ToDateTime(new DateTime(2019, 05, 24, 23, 59, 59));
                Assert.AreEqual(drawDate, new DateTime(2019, 05, 24, 10, 00, 0));
            }

            now = new DateTime(2019, 05, 24, 10, 45, 00);
            var report = lottery.SetNoAction(true, new DateTime(2019, 05, 24, 10, 00, 0), now, "Game Admin");

            schedules = lotteries.FinishedAndRegradedSchedulesOf(new DateTime(2019, 05, 24, 00, 00, 00).AddDays(1).AddMilliseconds(-1));
            Assert.IsTrue(schedules.GetEnumerator().MoveNext());
            foreach (var finishedLotteries in schedules)
            {
                var drawDate = finishedLotteries.ToDateTime(new DateTime(2019, 05, 24, 00, 00, 00).AddDays(1).AddMilliseconds(-1));
                Assert.AreEqual(drawDate, new DateTime(2019, 05, 24, 10, 00, 0));
            }
        }

        [TestMethod]
        public void ScheduleVersionsTest_Bug3118_Escenario_SinProblema_Update_Grade()
        {
            Company company = new Company();
            company.Sales.CreateStore(1, "Test Store").MakeCurrent();
            company.Accounting = new MockAccounting();
            CurrenciesTest.AddCurrencies();
            var lotteries = company.Lotto900();
            State s = lotteries.GetOrCreateState("GA", "Georgia");

            LotteryPick<Pick3> lottery = (LotteryPick<Pick3>)lotteries.GetOrCreateLottery(3, s);
            for (int i = 0; i <= 6; i++) lottery.Every(10, 00, i);

            DateTime now = new DateTime(2019, 05, 24, 9, 00, 00);
            Player player = company.GetOrCreateCustomerById("*********").Player;
            string states = "GA"; string hours = "10:00 AM"; string strIsPresentPlayerBeforeToCloseStore = "true";
            string dates = "05/27/2019"; string numbers = "1,2,3"; string selectionMode = "balls";
            string withFireballs = "false";
            string pickNumber = "3"; decimal ticketAmount = 10; string includedNumbersForInput = "";
            string gameType = "Straight"; decimal founds = 80000;
            Product ticketPick3StraightProd = lotteries.GetOrCreateProductById(1);
            ticketPick3StraightProd.Description = "Ticket pick 3 straight";
            ticketPick3StraightProd.Price = 1;
            Domain domain = company.Sales.CreateDomain(false, 1, "localhost", Agents.INSIDER);

            //Comprar un ticket #1
            NextDatesAccumulator nextDatesAccumulator = new NextDatesAccumulator(lotteries, domain, now, pickNumber, dates, states, hours, strIsPresentPlayerBeforeToCloseStore);
            ExcludeSubtickets excludedSubtickets = new ExcludeSubtickets();
            Customer customer = company.CustomerByPlayer(player);
            OrderCart myOrder = (OrderCart)company.GetNewOrder(customer);

            Order order = company.CreateTicketOrder(lotteries, player, states, dates, withFireballs, numbers, selectionMode, ticketAmount, includedNumbersForInput, excludedSubtickets, gameType, myOrder, ticketPick3StraightProd, nextDatesAccumulator, domain);
            company.AddOrders(myOrder, founds);
            int orderNumber = order.Number;
            order = company.OrderByNumber(orderNumber);
            order.AuthorizationId = 123;
            var marketplace = new Marketplace(company, "CR");
            var agent = marketplace.AddAgent("1");
            myOrder.Agent = (GamesEngine.Exchange.Agent)agent;
            company.SaleTickets(true, myOrder, now, founds, 1, domain);
            Assert.AreEqual(1, orderNumber);
            //Fin compra #1

            now = new DateTime(2019, 05, 24, 10, 05, 00);

            var schedule = lottery.GetScheduleId(1);
            schedule.Update(true, "V1", "0123456", 10, 30, now, "Game Admin");

            now = new DateTime(2019, 05, 24, 10, 35, 00);
            var report = lottery.SetNoAction(true, new DateTime(2019, 05, 24, 10, 30, 0), now, "Game Admin");
            //var report = lottery.LotteryDraw(new DateTime(2019, 05, 24, 10, 30, 00), "891", now, true, "Erick");

            var schedules = lotteries.FinishedAndRegradedSchedulesOf(new DateTime(2019, 05, 24, 00, 00, 00).AddDays(1).AddMilliseconds(-1));
            Assert.IsTrue(schedules.GetEnumerator().MoveNext());
            foreach (var finishedLotteries in schedules)
            {
                var drawDate = finishedLotteries.ToDateTime(new DateTime(2019, 05, 24, 00, 00, 00).AddDays(1).AddMilliseconds(-1));
                Assert.AreEqual(drawDate, new DateTime(2019, 05, 24, 10, 30, 0));
            }
        }

        [TestMethod]
        public void ScheduleVersionsTest_Bug3118_Escenario_SinProblema_Grade_Update_Grade()
        {
            Company company = new Company();
            company.Sales.CreateStore(1, "Test Store").MakeCurrent();
            company.Accounting = new MockAccounting();
            CurrenciesTest.AddCurrencies();
            var lotteries = company.Lotto900();
            State s = lotteries.GetOrCreateState("GA", "Georgia");

            LotteryPick<Pick3> lottery = (LotteryPick<Pick3>)lotteries.GetOrCreateLottery(3, s);
            for (int i = 0; i <= 6; i++) lottery.Every(10, 00, i);

            DateTime now = new DateTime(2019, 05, 24, 9, 00, 00);
            Player player = company.GetOrCreateCustomerById("*********").Player;
            string states = "GA"; string hours = "10:00 AM"; string strIsPresentPlayerBeforeToCloseStore = "true";
            string dates = "05/27/2019"; string numbers = "1,2,3"; string selectionMode = "balls";
            string withFireballs = "false";
            string pickNumber = "3"; decimal ticketAmount = 10; string includedNumbersForInput = "";
            string gameType = "Straight"; decimal founds = 80000;
            Product ticketPick3StraightProd = lotteries.GetOrCreateProductById(1);
            ticketPick3StraightProd.Description = "Ticket pick 3 straight";
            ticketPick3StraightProd.Price = 1;
            Domain domain = company.Sales.CreateDomain(false, 1, "localhost", Agents.INSIDER);

            //Comprar un ticket #1
            NextDatesAccumulator nextDatesAccumulator = new NextDatesAccumulator(lotteries, domain, now, pickNumber, dates, states, hours, strIsPresentPlayerBeforeToCloseStore);
            ExcludeSubtickets excludedSubtickets = new ExcludeSubtickets();
            Customer customer = company.CustomerByPlayer(player);
            OrderCart myOrder = (OrderCart)company.GetNewOrder(customer);

            Order order = company.CreateTicketOrder(lotteries, player, states, dates, withFireballs, numbers, selectionMode, ticketAmount, includedNumbersForInput, excludedSubtickets, gameType, myOrder, ticketPick3StraightProd, nextDatesAccumulator, domain);
            company.AddOrders(myOrder, founds);
            int orderNumber = order.Number;
            order = company.OrderByNumber(orderNumber);
            order.AuthorizationId = 123;
            var marketplace = new Marketplace(company, "CR");
            var agent = marketplace.AddAgent("1");
            myOrder.Agent = (GamesEngine.Exchange.Agent)agent;
            company.SaleTickets(true, myOrder, now, founds, 1, domain);
            Assert.AreEqual(1, orderNumber);
            //Fin compra #1

            now = new DateTime(2019, 05, 24, 10, 05, 00);
            bool notifyEvent = false;
            var report = lottery.DrawPicks(new DateTime(2019, 05, 24, 10, 00, 00), "891", now, true, "Erick", notifyEvent);

            now = new DateTime(2019, 05, 24, 10, 25, 00);
            var schedule = lottery.GetScheduleId(1);
            schedule.Update(true, "V1", "0123456", 10, 30, now, "Game Admin");

            var schedules = lotteries.FinishedAndRegradedSchedulesOf(new DateTime(2019, 05, 24, 00, 00, 00).AddDays(1).AddMilliseconds(-1));
            Assert.IsTrue(schedules.GetEnumerator().MoveNext());
            foreach (var finishedLotteries in schedules)
            {
                var drawDate = finishedLotteries.ToDateTime(new DateTime(2019, 05, 24, 00, 00, 00).AddDays(1).AddMilliseconds(-1));
                Assert.AreEqual(drawDate, new DateTime(2019, 05, 24, 10, 00, 0));
            }

            schedules = lotteries.PendingAndNoRegradedSchedulesAt(new DateTime(2019, 05, 25, 23, 59, 59));
            Assert.IsTrue(schedules.GetEnumerator().MoveNext());
            foreach (var pendingLotteries in schedules)
            {
                var drawDate = pendingLotteries.ToDateTime(new DateTime(2019, 05, 25, 23, 59, 59));
                Assert.AreEqual(drawDate, new DateTime(2019, 05, 25, 10, 30, 0));
            }

            now = new DateTime(2019, 05, 24, 10, 35, 00);
            report = lottery.Regrade(true, new DateTime(2019, 05, 24, 10, 00, 00), now, "Erick");

            schedules = lotteries.FinishedAndRegradedSchedulesOf(new DateTime(2019, 05, 24, 00, 00, 00).AddDays(1).AddMilliseconds(-1));
            Assert.IsTrue(schedules.GetEnumerator().MoveNext());
            foreach (var finishedLotteries in schedules)
            {
                var drawDate = finishedLotteries.ToDateTime(new DateTime(2019, 05, 24, 00, 00, 00).AddDays(1).AddMilliseconds(-1));
                Assert.AreEqual(drawDate, new DateTime(2019, 05, 24, 10, 00, 0));
            }
        }

        [TestMethod]
        public void ScheduleVersionsTest_Bug3118_Escenario2_ConProblema()
        {
            Company company = new Company();
            company.Sales.CreateStore(1, "Test Store").MakeCurrent();
            company.Accounting = new MockAccounting();
            CurrenciesTest.AddCurrencies();
            var lotteries = company.Lotto900();
            State s = lotteries.GetOrCreateState("GA", "Georgia");

            LotteryPick<Pick3> lottery = (LotteryPick<Pick3>)lotteries.GetOrCreateLottery(3, s);
            for (int i = 0; i <= 6; i++) lottery.Every(10, 00, i);

            DateTime now = new DateTime(2019, 05, 24, 9, 00, 00);
            Player player = company.GetOrCreateCustomerById("*********").Player;
            string states = "GA"; string hours = "10:00 AM"; string strIsPresentPlayerBeforeToCloseStore = "true";
            string dates = "05/27/2019"; string numbers = "1,2,3"; string selectionMode = "balls";
            string withFireballs = "false";
            string pickNumber = "3"; decimal ticketAmount = 10; string includedNumbersForInput = "";
            string gameType = "Straight"; decimal founds = 80000;
            Product ticketPick3StraightProd = lotteries.GetOrCreateProductById(1);
            ticketPick3StraightProd.Description = "Ticket pick 3 straight";
            ticketPick3StraightProd.Price = 1;
            Domain domain = company.Sales.CreateDomain(false, 1, "localhost", Agents.INSIDER);

            //Comprar un ticket #1
            NextDatesAccumulator nextDatesAccumulator = new NextDatesAccumulator(lotteries, domain, now, pickNumber, dates, states, hours, strIsPresentPlayerBeforeToCloseStore);
            ExcludeSubtickets excludedSubtickets = new ExcludeSubtickets();
            Customer customer = company.CustomerByPlayer(player);
            OrderCart myOrder = (OrderCart)company.GetNewOrder(customer);

            Order order = company.CreateTicketOrder(lotteries, player, states, dates, withFireballs, numbers, selectionMode, ticketAmount, includedNumbersForInput, excludedSubtickets, gameType, myOrder, ticketPick3StraightProd, nextDatesAccumulator, domain);
            company.AddOrders(myOrder, founds);
            int orderNumber = order.Number;
            order = company.OrderByNumber(orderNumber);
            order.AuthorizationId = 123;
            var marketplace = new Marketplace(company, "CR");
            var agent = marketplace.AddAgent("1");
            myOrder.Agent = (GamesEngine.Exchange.Agent)agent;
            company.SaleTickets(true, myOrder, now, founds, 1, domain);
            Assert.AreEqual(1, orderNumber);
            //Fin compra #1

            now = new DateTime(2019, 05, 24, 10, 05, 00);

            var schedule = lottery.GetScheduleId(1);
            schedule.Update(true, "V1", "0123456", 10, 30, now, "Game Admin");

            now = new DateTime(2019, 05, 24, 10, 35, 00);
            var report = lottery.SetNoAction(true, new DateTime(2019, 05, 24, 10, 30, 0), now, "Game Admin");

            var schedules = lotteries.FinishedAndRegradedSchedulesOf(new DateTime(2019, 05, 24, 00, 00, 00).AddDays(1).AddMilliseconds(-1));
            Assert.IsTrue(schedules.GetEnumerator().MoveNext());
            foreach (var finishedLotteries in schedules)
            {
                var drawDate = finishedLotteries.ToDateTime(new DateTime(2019, 05, 24, 00, 00, 00).AddDays(1).AddMilliseconds(-1));
                Assert.AreEqual(drawDate, new DateTime(2019, 05, 24, 10, 30, 0));
            }

            //Este escenario tiene problemas, el draw no esta ni en pending ni en finish para cuando se hace todo el mismo dia y se pregunta por ese dia
            now = new DateTime(2019, 05, 24, 10, 40, 00);
            int scheduleIdUpdated = schedule.Id;
            schedule = lottery.GetScheduleId(scheduleIdUpdated);
            schedule.Update(true, "V2", "0123456", 10, 00, now, "Game Admin");

            schedules = lotteries.FinishedAndRegradedSchedulesOf(new DateTime(2019, 05, 24, 00, 00, 00).AddDays(1).AddMilliseconds(-1));
            Assert.IsTrue(schedules.GetEnumerator().MoveNext());
            foreach (var finishedLotteries in schedules)
            {
                var drawDate = finishedLotteries.ToDateTime(new DateTime(2019, 05, 24, 00, 00, 00).AddDays(1).AddMilliseconds(-1));
                Assert.AreEqual(drawDate, new DateTime(2019, 05, 24, 10, 30, 0));
            }

            schedules = lotteries.PendingAndNoRegradedSchedulesAt(new DateTime(2019, 05, 24, 23, 59, 59));
            Assert.IsFalse(schedules.GetEnumerator().MoveNext());
        }

        [TestMethod]
        public void ScheduleVersionsTest_Bug3118_Escenario2_2()
        {
            Company company = new Company();
            company.Sales.CreateStore(1, "Test Store").MakeCurrent();
            company.Accounting = new MockAccounting();
            CurrenciesTest.AddCurrencies();
            var lotteries = company.Lotto900();
            State s = lotteries.GetOrCreateState("GA", "Georgia");

            LotteryPick<Pick3> lottery = (LotteryPick<Pick3>)lotteries.GetOrCreateLottery(3, s);
            for (int i = 0; i <= 6; i++) lottery.Every(10, 00, i);

            DateTime now = new DateTime(2019, 05, 24, 9, 00, 00);
            Player player = company.GetOrCreateCustomerById("*********").Player;
            string states = "GA"; string hours = "10:00 AM"; string strIsPresentPlayerBeforeToCloseStore = "true";
            string dates = "05/27/2019"; string numbers = "1,2,3"; string selectionMode = "balls";
            string withFireballs = "false";
            string pickNumber = "3"; decimal ticketAmount = 10; string includedNumbersForInput = "";
            string gameType = "Straight"; decimal founds = 80000;
            Product ticketPick3StraightProd = lotteries.GetOrCreateProductById(1);
            ticketPick3StraightProd.Description = "Ticket pick 3 straight";
            ticketPick3StraightProd.Price = 1;
            Domain domain = company.Sales.CreateDomain(false, 1, "localhost", Agents.INSIDER);

            //Comprar un ticket #1
            NextDatesAccumulator nextDatesAccumulator = new NextDatesAccumulator(lotteries, domain, now, pickNumber, dates, states, hours, strIsPresentPlayerBeforeToCloseStore);
            ExcludeSubtickets excludedSubtickets = new ExcludeSubtickets();
            Customer customer = company.CustomerByPlayer(player);
            OrderCart myOrder = (OrderCart)company.GetNewOrder(customer);

            Order order = company.CreateTicketOrder(lotteries, player, states, dates, withFireballs, numbers, selectionMode, ticketAmount, includedNumbersForInput, excludedSubtickets, gameType, myOrder, ticketPick3StraightProd, nextDatesAccumulator, domain);
            company.AddOrders(myOrder, founds);
            int orderNumber = order.Number;
            order = company.OrderByNumber(orderNumber);
            order.AuthorizationId = 123;
            var marketplace = new Marketplace(company, "CR");
            var agent = marketplace.AddAgent("1");
            myOrder.Agent = (GamesEngine.Exchange.Agent)agent;
            company.SaleTickets(true, myOrder, now, founds, 1, domain);
            Assert.AreEqual(1, orderNumber);
            //Fin compra #1

            now = new DateTime(2019, 05, 24, 10, 05, 00);
            var schedule = lottery.GetScheduleId(1);
            schedule.Update(true, "V1", "0123456", 10, 30, now, "Game Admin");

            now = new DateTime(2019, 05, 24, 10, 35, 00);
            bool notifyEvent = false;
            var report = lottery.DrawPicks(new DateTime(2019, 05, 24, 10, 30, 00), "891", now, true, "Erick", notifyEvent);

            var schedules = lotteries.FinishedAndRegradedSchedulesOf(new DateTime(2019, 05, 24, 00, 00, 0).AddDays(1).AddMilliseconds(-1));
            Assert.IsTrue(schedules.GetEnumerator().MoveNext());
            foreach (var finishedLotteries in schedules)
            {
                var drawDate = finishedLotteries.ToDateTime(new DateTime(2019, 05, 24, 00, 00, 00).AddDays(1).AddMilliseconds(-1));
                Assert.AreEqual(drawDate, new DateTime(2019, 05, 24, 10, 30, 0));
                var drawDate2 = finishedLotteries.ToDateTime(new DateTime(2019, 05, 24, 00, 00, 00));
                Assert.AreEqual(drawDate2, new DateTime(2019, 05, 24, 10, 00, 0));
            }

            now = new DateTime(2019, 05, 25, 10, 36, 00);
            schedule.Update(true, "V2", "0123456", 10, 35, now, "Game Admin");

            now = new DateTime(2019, 05, 25, 10, 37, 00);
            report = lottery.DrawPicks(new DateTime(2019, 05, 25, 10, 35, 00), "891", now, true, "Erick", notifyEvent);

            var schedules2 = lotteries.FinishedAndRegradedSchedulesOf(new DateTime(2019, 05, 25, 00, 00, 0).AddDays(1).AddMilliseconds(-1));
            Assert.IsTrue(schedules2.GetEnumerator().MoveNext());
            foreach (var finishedLotteries in schedules2)
            {
                var drawDate = finishedLotteries.ToDateTime(new DateTime(2019, 05, 25, 00, 00, 00).AddDays(1).AddMilliseconds(-1));
                Assert.AreEqual(drawDate, new DateTime(2019, 05, 25, 10, 35, 0));
                var drawDate2 = finishedLotteries.ToDateTime(new DateTime(2019, 05, 25, 00, 00, 00));
                Assert.AreEqual(drawDate2, new DateTime(2019, 05, 25, 10, 35, 0));
            }

            now = new DateTime(2019, 05, 26, 10, 37, 00);
            var schedules3 = lotteries.PendingAndNoRegradedSchedulesAt(new DateTime(2019, 05, 26, 23, 59, 59));
            Assert.IsTrue(schedules3.GetEnumerator().MoveNext());
            foreach (var pendingLotteries in schedules3)
            {
                var drawDate = pendingLotteries.ToDateTime(new DateTime(2019, 05, 26, 23, 59, 59));
                Assert.AreEqual(drawDate, new DateTime(2019, 05, 26, 10, 35, 0));
            }
        }
    }
}
