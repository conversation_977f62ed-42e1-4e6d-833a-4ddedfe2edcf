﻿using Elastic.Apm.AspNetCore;
using ExternalServices;
using GamesEngine.Settings;
using GamesEngineMocks;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.HttpOverrides;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Newtonsoft.Json;
using Microsoft.OpenApi.Models;
using Puppeteer.EventSourcing;
using RestSharp;
using System;
using System.Threading;
using GamesEngine.Middleware.Providers;
using GamesEngine.Games;
using GamesEngine;
using GamesEngine.MessageQueuing;

namespace LinesAPI
{
	public class Startup
    {
        public Startup(IWebHostEnvironment env, IConfiguration configuration)
        {
            var builder = new ConfigurationBuilder()
                .SetBasePath(env.ContentRootPath)
                .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true)
                .AddJsonFile("customization/appsettings.accounting.json", optional: true)
                .AddJsonFile("customization/appsettings.security.json", optional: true)
                .AddJsonFile("customization/appsettings.kafka.json", optional: true)
                .AddJsonFile("customization/appsettings.error_emails.json", optional: true)
                .AddJsonFile("customization/appsettings.apm.json", optional: true)
                .AddJsonFile("secrets/appsettings.secrets.json", optional: true);
            Configuration = builder.Build();
            Environment = env;
        }

        public IConfiguration Configuration { get; }
        public IWebHostEnvironment Environment { get; }

        // This method gets called by the runtime. Use this method to add services to the container.
        public void ConfigureServices(IServiceCollection services)
        {
            int minWorker, minIOC;
            ThreadPool.GetMinThreads(out minWorker, out minIOC);
            int maxWorker, maxIOC;
            ThreadPool.GetMaxThreads(out maxWorker, out maxIOC);

            Console.WriteLine($@" minWorker:{minWorker} minIOC:{minIOC} maxWorker:{maxWorker} maxIOC:{maxIOC}");

            services.AddSwaggerGen(c =>
            {
                c.SwaggerDoc("v1", new OpenApiInfo { Title = "Lines API", Version = "v1" });
            });

            /*Security*/
            Security.Configure(services, Configuration);
            /*Security*/

            services.AddMvc(options => options.EnableEndpointRouting = false).AddNewtonsoftJson();

            if (string.IsNullOrEmpty(Configuration.GetValue<string>("CashierUrl"))) throw new Exception($"{nameof(Settings.CashierUrl)} its requeried in the appsettings.");
            Settings.CashierUrl = Configuration.GetValue<string>("CashierUrl");
            if (string.IsNullOrEmpty(Configuration.GetValue<string>("LinesETLUrl"))) throw new Exception($"{nameof(Settings.LinesETLUrl)} its requeried in the appsettings.");
            Settings.LinesETLUrl = Configuration.GetValue<string>("LinesETLUrl");

            services.AddSingleton<IHttpContextAccessor, HttpContextAccessor>();
            //services.AddWebSocketManager();
            services.Configure<IISServerOptions>(options =>
            {
                options.AllowSynchronousIO = true;
            });
            var biIntegration = Configuration.GetSection("BIIntegration");

            AccountingSettings.NeedsUniqueIdentifierForPaymentHub = "STAGE_NEED_IT";

            Integration.Configure(KafkaMessage.Prefix.NoTransacction, biIntegration);

            string elkServer = Configuration.GetSection("ELKIntegration").GetValue<string>("elkserver");
            HttpELKClient.Configure(elkServer);

            ErrorsSender.Configure(Configuration.GetSection("ErrorsSender"), Environment.IsProduction());
            Integration.ConfigureAPM(Configuration);

            var messageOriginId = Configuration.GetValue<string>("MessageOriginId");
            if (string.IsNullOrEmpty(messageOriginId) && !int.TryParse(messageOriginId, out _)) throw new Exception("MessageOriginId its requeried in the appsettings.");
            MessageCatalog.AssingMessageOriginId(Convert.ToInt32(messageOriginId));
        }

        // This method gets called by the runtime. Use this method to configure the HTTP request pipeline.
        public void Configure(IApplicationBuilder app, IWebHostEnvironment env, IServiceProvider serviceProvider)
        {
            if (Integration.UseAPM)
            {
                app.UseElasticApm(Configuration);
            }
            if (env.IsDevelopment())
            {
                app.UseDeveloperExceptionPage();
            }
            else
            {
                if (String.IsNullOrEmpty(Integration.Db.DBSelected))
                {
                    throw new Exception("Db configuration its required.");
                }
                if (!Integration.UseKafka)
                {
                    throw new Exception("This project must use kafka integration to show data.");
                }
            }

            IRestResponse response = HttpELKClient.GetInstance().CreateAndIndexWithDateMappings(
                "officials",
                new []{ "tournamentOpenDate", "gameOpenDate"},
                new[] { "alias" },
                new[] { "suggest"});

			if (200 != (int)response.StatusCode)
			{
				Console.WriteLine("mappings for officials index already exist.");
			}

			app.UseSwagger();
            app.UseSwaggerUI(c =>
            {
                c.SwaggerEndpoint("/swagger/v1/swagger.json", "Lines API V1");
                c.RoutePrefix = string.Empty;
            });

            var sectionDairy = Configuration.GetSection("DBDairy");
            var mySQL = sectionDairy.GetSection("ConnectionStrings").GetValue<string>("MySQL");
            var sqlServer = sectionDairy.GetSection("ConnectionStrings").GetValue<string>("SQLServer");
            var dbSelected = sectionDairy.GetValue<string>("DBSelected");
            var scriptBeforeRecovering = Configuration.GetValue<string>("ActionsBeforeRecovering");
            bool useEtlInMocks = Configuration.GetValue<bool>("useETLinMocks");

            DBDairy dbDairy = new DBDairy();
            dbDairy.DBSelected = dbSelected;
            Integration.DbDairy = dbDairy;

            //app.UseWebSockets();
            //WebSocketImplementation websocket = serviceProvider.GetService<WebSocketImplementation>();
            //app.MapSockets("/ws/notifier", websocket);
            if (dbSelected == DatabaseType.MySQL.ToString())
            {
                 LinesAPI.Lines.EventSourcingStorage(DatabaseType.MySQL, mySQL, scriptBeforeRecovering, AccountingSettings.NeedsUniqueIdentifierForPaymentHub);
                //Settings.ConfigureRiskTagsMonitor(websocket);
            }
            else if (dbSelected == DatabaseType.SQLServer.ToString())
            {
                 LinesAPI.Lines.EventSourcingStorage(DatabaseType.SQLServer, sqlServer, scriptBeforeRecovering, AccountingSettings.NeedsUniqueIdentifierForPaymentHub);
                //Settings.ConfigureRiskTagsMonitor(websocket);
            }
            else if (!String.IsNullOrWhiteSpace(dbSelected))
            {
                throw new Exception($"There is no connection for {dbSelected}");
            }
            else
            {
                //Settings.ConfigureRiskTagsMonitor(websocket);
                //Integration.Kafka.OffSetResetToLatest();
                int numberOfTheMockConfigured = Convert.ToInt32(Configuration["MockToStart"]);

                var DOTNET_RUNNING_IN_CONTAINER = bool.Parse(System.Environment.GetEnvironmentVariable("DOTNET_RUNNING_IN_CONTAINER") ?? "false");
                if (DOTNET_RUNNING_IN_CONTAINER)
                    LinesAPI.Lines.EventSourcingStorage(DatabaseType.MySQL, mySQL, scriptBeforeRecovering, AccountingSettings.NeedsUniqueIdentifierForPaymentHub);

                RunMock(LinesAPI.Lines.Actor, numberOfTheMockConfigured, useEtlInMocks);
            }

            /*Security*/
            app.UseAuthentication();
            /*Security*/

            app.UseMvc();

            if (Integration.UseKafka)
            {
                new Consumers().CreateConsumerForTopics();
            }

            ForwardedHeadersOptions options = new ForwardedHeadersOptions();
            options.ForwardedHeaders = ForwardedHeaders.XForwardedFor;
            app.UseForwardedHeaders(options);

            SyncSportActivations();

            var tenantAndStore = Integration.GetCurrentTenantAndStore(LinesAPI.Lines);
            var result = LinesAPI.Lines.PerformQry($@"
                    {{
                        success = company.System.PaymentProcessor.Load(false, Now, customSettings);
                        print success success;
                    }}
                    ");
            if (!(result is OkObjectResult)) throw new GameEngineException("Processor were not loaded;");

            if (!tenantAndStore.isAlreadyRegisteredTenantAndStore)
            {
                result = LinesAPI.Lines.PerformCmd($@"
                    {{
                        company.RegisterTenantAndStore(itIsThePresent, '{Integration.CurrentTenantName}', customSettings);
                    }}
                    ");
                if (!(result is OkObjectResult)) throw new GameEngineException("Tenant and store cannot be registered");
            }
        }

        void RunMock(Puppeteer.EventSourcing.Actor actor, int index, bool useEtlInMocks)
        {
            switch (index)
            {
                case 0:
                    LinesMocks.Init(actor);
                    break;
                case 1:
                   LinesMocks.LinesBasketballIn2doQuarter(actor);
                    break;
                case 2:
                    LinesMocks.LinesInOpenRegistration(actor);
                    break;
                case 3:
                    LinesMocks.LinesSoccerFirtsHalf(actor);
                    break;
                case 4:
                   LinesMocks.LinesBasketballPregame(actor);
                    break;
                case 5:
                    LinesMocks.LinesBasketballPregameFewLinesWithUpdateEveryXSeconds(actor);
                    break;
                case 6:
                   LinesMocks.LinesBasketballWithGrade(actor);
                    break;
                case 7:
                    LinesMocks.LinesBasketballPregameAndInPlayFewLines(actor);
                    break;
                default:
                    throw new Exception($"The mock {index} its not implemented yet.");
            }
        }

        private int StoreId()
        {
            var result = LinesAPI.Lines.PerformQry(@"
                {{
                    print company.Sales.CurrentStore.Id storeId;    
                }}"
            );

            OkObjectResult o = (OkObjectResult)result;
            string json = o.Value.ToString();
            var storeInfo = JsonConvert.DeserializeObject<StoreInformation>(json);
            return storeInfo.storeId;
        }

        private struct StoreInformation
        {
            public int storeId { get; set; }
        }

        void SyncSportActivations()
        {
            var resultQry = LinesAPI.Lines.PerformQry($@"
            {{
                enabledSports = company.Sales.CurrentStore.EnabledSportsWithForeingAlias();
                for (sports:enabledSports)
                {{
                    print sports.Id id;
                    print sports.Name name;
                }}
            }}
            ");

            if (!(resultQry is OkObjectResult))
            {
                throw new Exception($@"Error:{((ObjectResult)resultQry).Value.ToString()}");
            }
            OkObjectResult o = (OkObjectResult)resultQry;
            string json = o.Value.ToString();
            dynamic enabledSportsInfo = JsonConvert.DeserializeObject(json);
            var providers = ProvidersCollection.Instance();
            
            foreach (dynamic enabledSport in enabledSportsInfo.sports)
            {
                int sportId = enabledSport.id;
                GamesEngine.Games.Sport sport = new Sports().FindById(sportId);

                enabledSport.wasThereAnyEnabledDomainForSport = false;
                enabledSport.anyEnabledDomainForSport = true;
                json = JsonConvert.SerializeObject(enabledSport);
                foreach (var provider in providers)
                {
                    var response = new SportResponseFromKafka(sport, provider, json, new NormalizedProviderResponse.Attachments() { SportId = sportId });
                    Integration.Kafka.Send(true, Integration.Kafka.TopicForLinesETL, NormalizedProviderResponse.Serialize(response));
                }
            }
        }
    }

}
