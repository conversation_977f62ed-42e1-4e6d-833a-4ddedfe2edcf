﻿using GamesEngine.Business;
using GamesEngine.Domains;
using GamesEngine.Exchange;
using GamesEngine.Exchange.Batch;
using GamesEngine.Finance;
using GamesEngine.PurchaseOrders;
using GamesEngineTests.Custodian;
using GamesEngineTests.Unit_Tests.Games.Lotto;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Text;
using town.connectors.drivers;
using static GamesEngine.Business.WholePaymentProcessor;
using static GamesEngine.Finance.Currencies;

namespace GamesEngineTests.Exchange
{
	[TestClass]
    public class DeniedTransactionsTest
    {
        [TestInitialize]
        public void Initialize()
        {
            typeof(WholePaymentProcessor).GetField("_wholePaymentProcessor", System.Reflection.BindingFlags.NonPublic | BindingFlags.Static).SetValue(null, null);
        }

        [TestMethod]
        public void DenyDepositTransaction()
        {
            bool itIsThePresent = false;
            DateTime today = DateTime.Now;
            Company company = new Company();
            CurrenciesTest.AddCurrencies();
            company.Sales.CreateStore(1, "Test Store").MakeCurrent();
            Domain domain = company.Sales.CreateDomain(false, 1, "localhost", PaymentChannels.Agents.INSIDER);
            company.Sales.CurrentStore.Add(domain);
            GuardianTest.LoadProcessors(company);

            string ced="abc123";string accountNumber = "B15461";Customer customer=company.GetOrCreateCustomerById(accountNumber);customer.Identifier=ced;
            CustomerAccount dollarAccount = customer.CreateNewAccountFor(Coinage.Coin(CODES.USD));
            Marketplace marketplace = new Marketplace(company, "CR");
            var a = (GamesEngine.Exchange.Agent)marketplace.AddAgent("A");
            var b = a.AddCashier("Juan");
            marketplace.RegisterNewAccount(1, dollarAccount);

            string employeeName = "Juan";

            BatchTransactions juanTransactions = BatchTest.SetInitialSettings(marketplace, today, new Domain[] { domain });
			BatchSet agentBatch = juanTransactions.Batch();
			string agentPath = a.FullName;

            var transactionType = marketplace.Company.System.TransactionTypes.SearchByName<ProcessorTransaction>(TransactionType.Deposit.ToString());
            int authorizationNumber = PaymentChannels.Deposit(
                itIsThePresent,
                Coinage.Coin(Currencies.CODES.USD),
                domain,
                transactionType,
                new DespositBody(
                    accountNumber,
                    ced,
                    100,
                    "deposit test",
                    today,
                    "",
                    "cris",
                    domain)).AuthorizationId;

            int id = marketplace.NewTransationNumber();
            var transaction = marketplace.From(id, dollarAccount, agentPath, employeeName, domain).Deposit(today, itIsThePresent, new Dollar(100), employeeName, (PaymentProcessor)marketplace.Company.System.PaymentProcessor.First(), 1);
            transaction.Deny(today, itIsThePresent, employeeName, "incomplete data");
            Assert.IsFalse(marketplace.ExistsDraftTransaction(juanTransactions, id));
        }

        [TestMethod]
        public void DenyWithdrawalTransaction()
        {
            bool itIsThePresent = false;
            DateTime today = DateTime.Now;
            Company company = new Company();
            CurrenciesTest.AddCurrencies();
            company.Sales.CreateStore(1, "Test Store").MakeCurrent();
            Domain domain = company.Sales.CreateDomain(false, 1, "localhost", PaymentChannels.Agents.INSIDER);
            company.Sales.CurrentStore.Add(domain);
            GuardianTest.LoadProcessors(company);

            string ced="abc123";string accountNumber = "B15461";Customer customer=company.GetOrCreateCustomerById(accountNumber);customer.Identifier=ced;
            CustomerAccount dollarAccount = customer.CreateNewAccountFor(Coinage.Coin(CODES.USD));
            Marketplace marketplace = new Marketplace(company, "CR");
            var a = (GamesEngine.Exchange.Agent)marketplace.AddAgent("A");
            var b = a.AddCashier("Juan");
            marketplace.RegisterNewAccount(1, dollarAccount);

            BatchTransactions juanTransactions = BatchTest.SetInitialSettings(marketplace, today, new Domain[] { domain });
			BatchSet agentBatch = juanTransactions.Batch();
			string agentPath = a.FullName;

			string employeeName = "Juan";

            int id = marketplace.NewTransationNumber();
            var processor = marketplace.Company.System.PaymentProcessor.SearchWithdrawalProcessorBy("USD");
            var transaction = marketplace.From(id, dollarAccount, agentPath, employeeName, domain).Withdraw(today, itIsThePresent, new Dollar(100), 1, employeeName, "1-11", "", new NoFeeUSD(), processor, 1);
            transaction.Deny(today, itIsThePresent, employeeName, "incomplete data");
            Assert.IsFalse(marketplace.ExistsDraftTransaction(juanTransactions, id));
        }

        [TestMethod]
        public void DenyTransferTransaction()
        {
            bool itIsThePresent = false;
            DateTime today = DateTime.Now;
            Company company = new Company();
            CurrenciesTest.AddCurrencies();
            company.Sales.CreateStore(1, "Test Store").MakeCurrent();
            Domain domain = company.Sales.CreateDomain(false, 1, "localhost", PaymentChannels.Agents.INSIDER);
            company.Sales.CurrentStore.Add(domain);
            GuardianTest.LoadProcessors(company);

            string ced="abc123";string accountNumber = "B15461";Customer customer=company.GetOrCreateCustomerById(accountNumber);customer.Identifier=ced;
            string ced2="abc1234";string accountNumber2 = "B15462";Customer toCustomer=company.GetOrCreateCustomerById(accountNumber2);toCustomer.Identifier=ced2;
            CustomerAccount dollarAccount = customer.CreateNewAccountFor(Coinage.Coin(CODES.USD));
            CustomerAccount bitcoinAccount = toCustomer.CreateNewAccountFor(Coinage.Coin(CODES.BTC));
            Marketplace marketplace = new Marketplace(company, "CR");
            var a = (GamesEngine.Exchange.Agent)marketplace.AddAgent("A");
            var b = a.AddCashier("Juan");
            marketplace.RegisterNewAccount(1, dollarAccount);
            marketplace.RegisterNewAccount(2, bitcoinAccount);

            BatchTransactions juanTransactions = BatchTest.SetInitialSettings(marketplace, today, new Domain[] { domain });
			BatchSet agentBatch = juanTransactions.Batch();
			string agentPath = a.FullName;

			string employeeName = "Juan";

            int id = marketplace.NewTransationNumber();
            var transaction = marketplace.From(id, dollarAccount, agentPath, employeeName, domain).TransferTo(today, itIsThePresent, new Dollar(100), 1, bitcoinAccount, employeeName, (PaymentProcessor)marketplace.Company.System.PaymentProcessor.First(), 1);
            transaction.Deny(today, itIsThePresent, employeeName, "incomplete data");
            Assert.IsFalse(marketplace.ExistsDraftTransaction(juanTransactions, id));
        }

        [TestMethod]
        public void DenyCreditNoteTransaction()
        {
            bool itIsThePresent = false;
            DateTime today = DateTime.Now;
            Company company = new Company();
            CurrenciesTest.AddCurrencies();
            company.Sales.CreateStore(1, "Test Store").MakeCurrent();
            Domain domain = company.Sales.CreateDomain(false, 1, "localhost", PaymentChannels.Agents.INSIDER);
            company.Sales.CurrentStore.Add(domain);
            GuardianTest.LoadProcessors(company);

            string ced="abc123";string accountNumber = "B15461";Customer customer=company.GetOrCreateCustomerById(accountNumber);customer.Identifier=ced;
            CustomerAccount dollarAccount = customer.CreateNewAccountFor(Coinage.Coin(CODES.USD));
            Marketplace marketplace = new Marketplace(company, "CR");
            var a = (GamesEngine.Exchange.Agent)marketplace.AddAgent("A");
            var b = a.AddCashier("Juan");
            marketplace.RegisterNewAccount(1, dollarAccount);

            string employeeName = "Juan";
            string concept = "concept 1";
            int batchNumber = 1;

            BatchTransactions juanTransactions = BatchTest.SetInitialSettings(marketplace, today, new Domain[] { domain });
			BatchSet agentBatch = juanTransactions.Batch();
			string agentPath = a.FullName;

			int id = marketplace.NewTransationNumber();
            var expectedResult = new Dollar(10m);
            var transaction = marketplace.From(id, dollarAccount, agentPath, employeeName, domain).CreditNote(today, itIsThePresent, expectedResult, employeeName, concept, 1, batchNumber, (PaymentProcessor)marketplace.Company.System.PaymentProcessor.First(), 1);

            List<BatchMovement> movements = juanTransactions.Movements().ToList();
            Assert.AreEqual(1, movements.Count, "Lock and credit movements were expected.");
            Assert.AreEqual(expectedResult, movements[0].Amount);
            Assert.AreEqual(new Dollar(5000000m - expectedResult.Value ), juanTransactions.Available(Coinage.Coin(Currencies.CODES.USD)), "No available modification are expected.");
            Assert.AreEqual(expectedResult, juanTransactions.Locked(Coinage.Coin(Currencies.CODES.USD)), $"{expectedResult} to customer is not the same;");

            transaction.Deny(today, itIsThePresent, employeeName, "incomplete data");
            Assert.IsFalse(marketplace.ExistsDraftTransaction(juanTransactions, id));

            movements = juanTransactions.Movements().ToList();
            Assert.AreEqual(3, movements.Count, "Unlock movement was expected.");
            Assert.AreEqual(expectedResult, movements[2].Amount);
            Assert.AreEqual(new Dollar(5000000m), juanTransactions.Available(Coinage.Coin(Currencies.CODES.USD)), "No available modification are expected.");
            Assert.AreEqual(new Dollar(0m), juanTransactions.Locked(Coinage.Coin(Currencies.CODES.USD)), "No available modification are expected.");
        }

        [TestMethod]
        public void DenyDebitNoteTransaction()
        {
            bool itIsThePresent = false;
            DateTime today = DateTime.Now;
            Company company = new Company();
            CurrenciesTest.AddCurrencies();
            company.Sales.CreateStore(1, "Test Store").MakeCurrent();
            Domain domain = company.Sales.CreateDomain(false, 1, "localhost", PaymentChannels.Agents.INSIDER);
            company.Sales.CurrentStore.Add(domain);
            GuardianTest.LoadProcessors(company);

            string ced="abc123";string accountNumber = "B15461";Customer customer=company.GetOrCreateCustomerById(accountNumber);customer.Identifier=ced;
            CustomerAccount dollarAccount = customer.CreateNewAccountFor(Coinage.Coin(CODES.USD));
            Marketplace marketplace = new Marketplace(company, "CR");
            var a = (GamesEngine.Exchange.Agent)marketplace.AddAgent("A");
            var b = a.AddCashier("Juan");
            marketplace.RegisterNewAccount(1, dollarAccount);

            string employeeName = "Juan";
            string concept = "concept 1";
            int batchNumber = 1;

            BatchTransactions juanTransactions = BatchTest.SetInitialSettings(marketplace, today, new Domain[] { domain });
			BatchSet agentBatch = juanTransactions.Batch();
			string agentPath = a.FullName;

			int id = marketplace.NewTransationNumber();
            var expectedResult = new Dollar(10m);
            var transaction = marketplace.From(id, dollarAccount, agentPath, employeeName, domain).DebitNote(today, itIsThePresent, expectedResult, employeeName, concept, 1, batchNumber, (PaymentProcessor)marketplace.Company.System.PaymentProcessor.First(), 1);

            List<BatchMovement> movements = juanTransactions.Movements().ToList();
            Assert.AreEqual(2, movements.Count, "Lock movement was expected.");
            Assert.AreEqual(expectedResult, movements[0].Amount);
            Assert.AreEqual(new Dollar(5000000m ), juanTransactions.Available(Coinage.Coin(Currencies.CODES.USD)), "No available modification are expected.");
            Assert.AreEqual(new Dollar(0m), juanTransactions.Locked(Coinage.Coin(Currencies.CODES.USD)), $"{expectedResult} to customer is not the same;");

            transaction.Deny(today, itIsThePresent, employeeName, "incomplete data");
            Assert.IsFalse(marketplace.ExistsDraftTransaction(juanTransactions, id));

            movements = juanTransactions.Movements().ToList();
            Assert.AreEqual(3, movements.Count, "Unlock and credit movement was expected.");
            Assert.AreEqual(expectedResult, movements[1].Amount);
            Assert.AreEqual(expectedResult, movements[2].Amount);
            Assert.AreEqual(new Dollar(5000000m), juanTransactions.Available(Coinage.Coin(Currencies.CODES.USD)), "No available modification are expected.");
            Assert.AreEqual(new Dollar(0m), juanTransactions.Locked(Coinage.Coin(Currencies.CODES.USD)), "No available modification are expected.");
        }
    }
}
