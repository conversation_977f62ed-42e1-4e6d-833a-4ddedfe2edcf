﻿using GamesEngine;
using GamesEngine.Bets;
using GamesEngine.Business;
using GamesEngine.Domains;
using GamesEngine.Exchange;
using GamesEngine.Finance;
using GamesEngine.Games.Lotto;
using GamesEngine.Location;
using GamesEngine.PurchaseOrders;
using GamesEngine.Settings;
using GamesEngine.Time;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using Puppeteer.EventSourcing.Libraries;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Unit.Games.Tools;
using Utilities;
using static LottoAPI.Controllers.DrawController;

namespace GamesEngineTests.Unit_Tests.Games.Lotto
{
    [TestClass]
    public class LottoPicksFireBall
    {
        [TestMethod]
        public void DrawPicks_1FireBallTicketsInMemory()
        {
            Integration.UseKafka = true;
            QueueInMemory queue = new QueueInMemory();
            Integration.Kafka = queue;

            DateTime now = new DateTime(2024, 08, 29, 00, 00, 00);
            Company company = new Company();
            company.Accounting = new RealAccounting();
            CurrenciesTest.AddCurrencies();
            company.Sales.CreateStore(1, "Test Store").MakeCurrent();

            var itIsThePresent = true;
            Domain domain = company.Sales.CreateDomain(itIsThePresent, 1, "localhost", PaymentChannels.Agents.INSIDER);
            Player player = company.GetOrCreateCustomerById("*********").Player;
            Customer customer = company.CustomerByPlayer(player);
            customer.Identifier = "*********";

            var lotteries = company.Lotto900();
            State s = lotteries.GetOrCreateState("GA", "Georgia", now, "Bart");
            LotteryPick<Pick2> lottery = (LotteryPick<Pick2>)lotteries.GetOrCreateLottery(2, s);
            lottery.ForEach(12, 00, "123456", now, "N/A");
            lottery.TurnOnFireBall(itIsThePresent, now, "N/A");
            var schedule = lottery.Schedules.First();

            Product ticketPick3StraightProd = lotteries.GetOrCreateProductById(1);
            ticketPick3StraightProd.Description = "Ticket pick 2 straight";
            ticketPick3StraightProd.Price = 1;
            string dates = "08/29/2024";
            string states = "GA";
            string hours = "12:00 PM";
            string withFireballs = "true";
            string selectionMode = "MultipleInputSingleAmount";
            string includedNumbersForInput = "12";
            decimal ticketAmount = 1;
            var orderNumber = 30001;
            OrderCart myOrder = (OrderCart)company.NewOrder(customer, orderNumber, Currencies.CODES.USD);
            lotteries.NextDatesAccumulator.Calculate(domain, now, "2", dates, states, hours, withFireballs);
            ExcludeSubtickets excludedSubtickets = new ExcludeSubtickets();
            var order = (OrderCart)company.CreateTicketOrderWithMultipleAuthorization(player, states, dates, withFireballs, selectionMode, ticketAmount, new List<string> { includedNumbersForInput }, "B", myOrder, ticketPick3StraightProd, lotteries.NextDatesAccumulator, domain);
            company.AddOrders(myOrder);

            var authorizations = new AuthorizationsNumbers(*********, 1);
            var lowReference = 100001;
            company.PurchaseTickets(itIsThePresent, myOrder, now, authorizations, domain, lowReference, orderNumber, "Artemis_ThirdParty_Deposit_USD");

            var drawDate = new DateTime(2024, 08, 29, 12, 00, 00);
            TicketsOfDraw ticketsInMemory = lottery.TicketInMemory(drawDate);
            Assert.IsNotNull(ticketsInMemory);
            Assert.AreEqual(1, ticketsInMemory.Tickets.Count());

            int fireBallNumber = 9;
            string winnerNumber = "12";
            var report = lottery.DrawPicks(drawDate, winnerNumber, fireBallNumber, now, itIsThePresent, "N/A", false);
            var count = queue.Count(Integration.Kafka.TopicForLottoGrading);
            Assert.AreEqual(1, count);
            var msg = queue.Dequeue(Integration.Kafka.TopicForLottoGrading).ToString();
            string[] messages = KafkaMessages.Split(msg);
            Assert.AreEqual(6, messages.Length);

            var winnerResultsInfo = new WinnerResultsInfo(messages[0]);
            Assert.AreEqual(winnerNumber, winnerResultsInfo.Draw);
            Assert.AreEqual(schedule.FireballId, winnerResultsInfo.DrawingId);
            Assert.AreEqual(fireBallNumber, winnerResultsInfo.Fireball);

            var drawingMessage = new DrawingMessage(messages[1]);
            Assert.AreEqual(((WeeklySchedule)schedule).FireballDescription(), drawingMessage.Description);
            Assert.AreEqual(schedule.FireballId, drawingMessage.Id);

            var domainBIMessage = new DomainBIMessage(messages[2]);
            Assert.AreEqual(domain.Url, domainBIMessage.Url);

            var ticketsStreamStartingMessage = new TicketsStreamStartingMessage(messages[3]);
            Assert.AreEqual(schedule.FireballId, ticketsStreamStartingMessage.DrawingId);
            Assert.AreEqual(fireBallNumber, ticketsStreamStartingMessage.Fireball);

            var winnerInfo = new WinnerInfo(messages[4]);
            Assert.AreEqual(0, winnerInfo.DrawingId);
            Assert.AreEqual(GradeTicketType.WINNER_TICKET, winnerInfo.GradeType);
            Assert.AreEqual(0, winnerInfo.Fireball);

            var ticketsStreamEndingMessage = new TicketsStreamEndingMessage(messages[5]);
            Assert.AreEqual(states, ticketsStreamEndingMessage.StateAbb);
            Assert.AreEqual(drawDate, ticketsStreamEndingMessage.DrawDate);
        }

        [TestMethod]
        public void DrawPicks_1FireBallTicketsInMemory_1TicketsInMemory()
        {
            Integration.UseKafka = true;
            QueueInMemory queue = new QueueInMemory();
            Integration.Kafka = queue;

            DateTime now = new DateTime(2024, 08, 29, 00, 00, 00);
            Company company = new Company();
            company.Accounting = new RealAccounting();
            CurrenciesTest.AddCurrencies();
            company.Sales.CreateStore(1, "Test Store").MakeCurrent();

            var itIsThePresent = true;
            Domain domain = company.Sales.CreateDomain(itIsThePresent, 1, "localhost", PaymentChannels.Agents.INSIDER);
            Player player = company.GetOrCreateCustomerById("*********").Player;
            Customer customer = company.CustomerByPlayer(player);
            customer.Identifier = "*********";

            var lotteries = company.Lotto900();
            State s = lotteries.GetOrCreateState("GA", "Georgia", now, "Bart");
            LotteryPick<Pick2> lottery = (LotteryPick<Pick2>)lotteries.GetOrCreateLottery(2, s);
            lottery.ForEach(12, 00, "123456", now, "N/A");
            lottery.TurnOnFireBall(itIsThePresent, now, "N/A");
            var schedule = lottery.Schedules.First();

            Product ticketPick3StraightProd = lotteries.GetOrCreateProductById(1);
            ticketPick3StraightProd.Description = "Ticket pick 2 straight";
            ticketPick3StraightProd.Price = 1;
            string dates = "08/29/2024";
            string states = "GA";
            string hours = "12:00 PM";
            string withFireballs = "true";
            string selectionMode = "MultipleInputSingleAmount";
            string includedNumbersForInput = "12";
            decimal ticketAmount = 1;
            var orderNumber = 30001;

            //1st ticket
            OrderCart myOrder = (OrderCart)company.NewOrder(customer, orderNumber, Currencies.CODES.USD);
            lotteries.NextDatesAccumulator.Calculate(domain, now, "2", dates, states, hours, withFireballs);
            ExcludeSubtickets excludedSubtickets = new ExcludeSubtickets();
            var order = (OrderCart)company.CreateTicketOrderWithMultipleAuthorization(player, states, dates, withFireballs, selectionMode, ticketAmount, new List<string> { includedNumbersForInput }, "B", myOrder, ticketPick3StraightProd, lotteries.NextDatesAccumulator, domain);
            company.AddOrders(myOrder);

            var authorizations = new AuthorizationsNumbers(*********, 1);
            var lowReference = 100001;
            company.PurchaseTickets(itIsThePresent, myOrder, now, authorizations, domain, lowReference, orderNumber, "Artemis_ThirdParty_Deposit_USD");

            var drawDate = new DateTime(2024, 08, 29, 12, 00, 00);
            TicketsOfDraw ticketsInMemory = lottery.TicketInMemory(drawDate);
            Assert.IsNotNull(ticketsInMemory);
            Assert.AreEqual(1, ticketsInMemory.Tickets.Count());

            //2nd ticket
            withFireballs = "false";
            includedNumbersForInput = "11";
            orderNumber = 30002;
            myOrder = (OrderCart)company.NewOrder(customer, orderNumber, Currencies.CODES.USD);
            lotteries.NextDatesAccumulator.Calculate(domain, now, "2", dates, states, hours, withFireballs);
            excludedSubtickets = new ExcludeSubtickets();
            order = (OrderCart)company.CreateTicketOrderWithMultipleAuthorization(player, states, dates, withFireballs, selectionMode, ticketAmount, new List<string> { includedNumbersForInput }, "B", myOrder, ticketPick3StraightProd, lotteries.NextDatesAccumulator, domain);
            company.AddOrders(myOrder);

            authorizations = new AuthorizationsNumbers(*********, 1);
            company.PurchaseTickets(itIsThePresent, myOrder, now, authorizations, domain, lowReference, orderNumber, "Artemis_ThirdParty_Deposit_USD");

            ticketsInMemory = lottery.TicketInMemory(drawDate);
            Assert.IsNotNull(ticketsInMemory);
            Assert.AreEqual(2, ticketsInMemory.Tickets.Count());

            int fireBallNumber = 9;
            string winnerNumber = "12";
            var report = lottery.DrawPicks(drawDate, winnerNumber, fireBallNumber, now, itIsThePresent, "N/A", false);
            var count = queue.Count(Integration.Kafka.TopicForLottoGrading);
            Assert.AreEqual(2, count);

            //1st message
            var msg = queue.Dequeue(Integration.Kafka.TopicForLottoGrading).ToString();
            string[] messages = KafkaMessages.Split(msg);
            Assert.AreEqual(6, messages.Length);

            var winnerResultsInfo = new WinnerResultsInfo(messages[0]);
            Assert.AreEqual(winnerNumber, winnerResultsInfo.Draw);
            Assert.AreEqual(schedule.Id, winnerResultsInfo.DrawingId);
            Assert.AreEqual(LotteryDraw.WITHOUT_FIREBALL, winnerResultsInfo.Fireball);

            var drawingMessage = new DrawingMessage(messages[1]);
            Assert.AreEqual(schedule.GetDescription(), drawingMessage.Description);
            Assert.AreEqual(schedule.Id, drawingMessage.Id);

            var domainBIMessage = new DomainBIMessage(messages[2]);
            Assert.AreEqual(domain.Url, domainBIMessage.Url);

            var ticketsStreamStartingMessage = new TicketsStreamStartingMessage(messages[3]);
            Assert.AreEqual(schedule.Id, ticketsStreamStartingMessage.DrawingId);
            Assert.AreEqual(LotteryDraw.WITHOUT_FIREBALL, ticketsStreamStartingMessage.Fireball);

            var loserInfo = new LoserInfo(messages[4]);
            Assert.AreEqual(0, loserInfo.DrawingId);
            Assert.AreEqual(GradeTicketType.LOSER_TICKET, loserInfo.GradeType);
            Assert.AreEqual(0, loserInfo.Fireball);

            var ticketsStreamEndingMessage = new TicketsStreamEndingMessage(messages[5]);
            Assert.AreEqual(states, ticketsStreamEndingMessage.StateAbb);
            Assert.AreEqual(drawDate, ticketsStreamEndingMessage.DrawDate);

            //2nd message
            msg = queue.Dequeue(Integration.Kafka.TopicForLottoGrading).ToString();
            messages = KafkaMessages.Split(msg);
            Assert.AreEqual(6, messages.Length);

            winnerResultsInfo = new WinnerResultsInfo(messages[0]);
            Assert.AreEqual(winnerNumber, winnerResultsInfo.Draw);
            Assert.AreEqual(schedule.FireballId, winnerResultsInfo.DrawingId);
            Assert.AreEqual(fireBallNumber, winnerResultsInfo.Fireball);

            drawingMessage = new DrawingMessage(messages[1]);
            Assert.AreEqual(((WeeklySchedule)schedule).FireballDescription(), drawingMessage.Description);
            Assert.AreEqual(schedule.FireballId, drawingMessage.Id);

            domainBIMessage = new DomainBIMessage(messages[2]);
            Assert.AreEqual(domain.Url, domainBIMessage.Url);

            ticketsStreamStartingMessage = new TicketsStreamStartingMessage(messages[3]);
            Assert.AreEqual(schedule.FireballId, ticketsStreamStartingMessage.DrawingId);
            Assert.AreEqual(fireBallNumber, ticketsStreamStartingMessage.Fireball);

            var winnerInfo = new WinnerInfo(messages[4]);
            Assert.AreEqual(0, winnerInfo.DrawingId);
            Assert.AreEqual(GradeTicketType.WINNER_TICKET, winnerInfo.GradeType);
            Assert.AreEqual(0, winnerInfo.Fireball);

            ticketsStreamEndingMessage = new TicketsStreamEndingMessage(messages[5]);
            Assert.AreEqual(states, ticketsStreamEndingMessage.StateAbb);
            Assert.AreEqual(drawDate, ticketsStreamEndingMessage.DrawDate);
        }

        [TestMethod]
        public void BuyTicketWinner_DrawPicks2_Test()
        {
            QueueInMemory queue = new QueueInMemory();
            Integration.Kafka = queue;

            DateTime now = new DateTime(2024, 08, 29, 00, 00, 00);
            decimal founds = 80000;
            Company company = new Company();
            company.Accounting = new RealAccounting();
            CurrenciesTest.AddCurrencies();
            company.Sales.CreateStore(1, "Test Store").MakeCurrent();

            Domain domain = company.Sales.CreateDomain(false, 1, "localhost", PaymentChannels.Agents.INSIDER);
            Player player = company.GetOrCreateCustomerById("*********").Player;
            Customer customer = company.CustomerByPlayer(player);
            customer.Identifier = "*********";

            var lotteries = company.Lotto900();
            State s = lotteries.GetOrCreateState("GA", "Georgia");
            LotteryPick<Pick2> lottery = (LotteryPick<Pick2>)lotteries.GetOrCreateLottery(2, s);
            lottery.ForEach(12, 00, "123456", now, "N/A");

            // Create a product and buy the ticket
            Product ticketPick3StraightProd = lotteries.GetOrCreateProductById(1);
            ticketPick3StraightProd.Description = "Ticket pick 2 straight";
            ticketPick3StraightProd.Price = 1;
            string dates = "08/29/2024";
            string states = "GA";
            string hours = "12:00 PM";
            string withFireballs = "false";
            string strIsPresentPlayerBeforeToCloseStore = "true";
            NextDatesAccumulator nextDatesAccumulator = new NextDatesAccumulator(lotteries, domain, now, "2", dates, states, hours, withFireballs, strIsPresentPlayerBeforeToCloseStore);
            OrderCart myOrder = (OrderCart)company.GetNewOrder(customer);

            ExcludeSubtickets excludedSubtickets = new ExcludeSubtickets();
            string numbers = "";
            string selectionMode = "MultipleInputSingleAmount";
            decimal ticketAmount = 1;
            string includedNumbersForInput = "12";
            string gameType = "Straight";
            Order order = company.CreateTicketOrder(lotteries, player, states, dates, withFireballs, numbers, selectionMode, ticketAmount, includedNumbersForInput, excludedSubtickets, gameType, myOrder, ticketPick3StraightProd, nextDatesAccumulator, domain);
            company.AddOrders(myOrder, founds);
            int orderNumber = order.Number;
            order = company.OrderByNumber(orderNumber);
            order.AuthorizationId = 123;
            var marketplace = new Marketplace(company, "CR");
            var agent = marketplace.AddAgent("1");
            myOrder.Agent = (GamesEngine.Exchange.Agent)agent;
            company.SaleTickets(true, myOrder, now, founds, 1, domain);
            Assert.AreEqual(1, orderNumber);

            string winnerNumber = "12";
            var report = lottery.DrawPicks(new DateTime(2024, 08, 29, 12, 00, 00), winnerNumber, now, false, "Erick", false);
            Assert.AreEqual(1, report.TotalTickets);
            Assert.AreEqual(1, report.TotalWinners);
            Assert.AreEqual(90M, report.TotalPrize);
        }

        [TestMethod]
        public void BuyTicketLoser_DrawPicks2_Test()
        {
            QueueInMemory queue = new QueueInMemory();
            Integration.Kafka = queue;

            DateTime now = new DateTime(2024, 08, 29, 00, 00, 00);
            decimal founds = 80000;
            Company company = new Company();
            company.Accounting = new RealAccounting();
            CurrenciesTest.AddCurrencies();
            company.Sales.CreateStore(1, "Test Store").MakeCurrent();

            Domain domain = company.Sales.CreateDomain(false, 1, "localhost", PaymentChannels.Agents.INSIDER);
            Player player = company.GetOrCreateCustomerById("*********").Player;
            Customer customer = company.CustomerByPlayer(player);
            customer.Identifier = "*********";

            var lotteries = company.Lotto900();
            State s = lotteries.GetOrCreateState("GA", "Georgia");
            LotteryPick<Pick2> lottery = (LotteryPick<Pick2>)lotteries.GetOrCreateLottery(2, s);
            lottery.ForEach(12, 00, "123456", now, "N/A");

            // Create a product and buy the ticket
            Product ticketPick3StraightProd = lotteries.GetOrCreateProductById(1);
            ticketPick3StraightProd.Description = "Ticket pick 2 straight";
            ticketPick3StraightProd.Price = 1;
            string dates = "08/29/2024";
            string states = "GA";
            string hours = "12:00 PM";
            string withFireballs = "false";
            string strIsPresentPlayerBeforeToCloseStore = "true";
            NextDatesAccumulator nextDatesAccumulator = new NextDatesAccumulator(lotteries, domain, now, "2", dates, states, hours, withFireballs, strIsPresentPlayerBeforeToCloseStore);
            OrderCart myOrder = (OrderCart)company.GetNewOrder(customer);

            ExcludeSubtickets excludedSubtickets = new ExcludeSubtickets();
            string numbers = "";
            string selectionMode = "MultipleInputSingleAmount";
            decimal ticketAmount = 1;
            string includedNumbersForInput = "11";
            string gameType = "Straight";
            Order order = company.CreateTicketOrder(lotteries, player, states, dates, withFireballs, numbers, selectionMode, ticketAmount, includedNumbersForInput, excludedSubtickets, gameType, myOrder, ticketPick3StraightProd, nextDatesAccumulator, domain);
            company.AddOrders(myOrder, founds);
            int orderNumber = order.Number;
            order = company.OrderByNumber(orderNumber);
            order.AuthorizationId = 123;
            var marketplace = new Marketplace(company, "CR");
            var agent = marketplace.AddAgent("1");
            myOrder.Agent = (GamesEngine.Exchange.Agent)agent;
            company.SaleTickets(true, myOrder, now, founds, 1, domain);
            Assert.AreEqual(1, orderNumber);

            string winnerNumber = "12";
            var report = lottery.DrawPicks(new DateTime(2024, 08, 29, 12, 00, 00), winnerNumber, now, false, "Erick", false);
            Assert.AreEqual(1, report.TotalTickets);
            Assert.AreEqual(0, report.TotalWinners);
            Assert.AreEqual(0, report.TotalPrize);
        }

        [TestMethod]
        public void BuyTicketWinner_DrawPicks2_FireBall_Test()
        {
            QueueInMemory queue = new QueueInMemory();
            Integration.Kafka = queue;

            DateTime now = new DateTime(2024, 08, 29, 00, 00, 00);
            decimal founds = 80000;
            Company company = new Company();
            company.Accounting = new RealAccounting();
            CurrenciesTest.AddCurrencies();
            company.Sales.CreateStore(1, "Test Store").MakeCurrent();

            Domain domain = company.Sales.CreateDomain(false, 1, "localhost", PaymentChannels.Agents.INSIDER);
            Player player = company.GetOrCreateCustomerById("*********").Player;
            Customer customer = company.CustomerByPlayer(player);
            customer.Identifier = "*********";

            var lotteries = company.Lotto900();
            State s = lotteries.GetOrCreateState("GA", "Georgia");
            LotteryPick<Pick2> lottery = (LotteryPick<Pick2>)lotteries.GetOrCreateLottery(2, s);
            lottery.TurnOnFireBall(false, now, "N/A");
            lottery.ForEach(12, 00, "123456", now, "N/A");

            // Create a product and buy the ticket
            Product ticketPick3StraightProd = lotteries.GetOrCreateProductById(1);
            ticketPick3StraightProd.Description = "Ticket pick 2 straight";
            ticketPick3StraightProd.Price = 1;
            string dates = "08/29/2024";
            string states = "GA";
            string hours = "12:00 PM";
            string withFireballs = "true";
            string strIsPresentPlayerBeforeToCloseStore = "true";
            string strUseNextDate = "false";
            NextDatesAccumulator nextDatesAccumulator = new NextDatesAccumulator(lotteries, domain, now, "2", dates, states, hours, withFireballs, strIsPresentPlayerBeforeToCloseStore, strUseNextDate);
            OrderCart myOrder = (OrderCart)company.GetNewOrder(customer);

            ExcludeSubtickets excludedSubtickets = new ExcludeSubtickets();
            string numbers = "";
            string selectionMode = "MultipleInputSingleAmount";
            decimal ticketAmount = 1;
            string includedNumbersForInput = "19";
            string gameType = "Straight";
            Order order = company.CreateTicketOrder(lotteries, player, states, dates, withFireballs, numbers, selectionMode, ticketAmount, includedNumbersForInput, excludedSubtickets, gameType, myOrder, ticketPick3StraightProd, nextDatesAccumulator, domain);
            company.AddOrders(myOrder, founds);
            int orderNumber = order.Number;
            order = company.OrderByNumber(orderNumber);
            order.AuthorizationId = 123;
            var marketplace = new Marketplace(company, "CR");
            var agent = marketplace.AddAgent("1");
            myOrder.Agent = (GamesEngine.Exchange.Agent)agent;
            company.SaleTickets(true, myOrder, now, founds, 1, domain);
            Assert.AreEqual(1, orderNumber);

            int fireBallNumber = 9;
            string winnerNumber = "12";
            var report = lottery.DrawPicks(new DateTime(2024, 08, 29, 12, 00, 00), winnerNumber, fireBallNumber, now, false, "Erick", false);
            Assert.AreEqual(1, report.TotalTickets);
            Assert.AreEqual(1, report.TotalWinners);
            Assert.AreEqual(22.50M, report.TotalPrize);
        }

        [TestMethod]
        public void BuyTicketWinner_DrawPicks3_FireBall_Test()
        {
            QueueInMemory queue = new QueueInMemory();
            Integration.Kafka = queue;

            DateTime now = new DateTime(2024, 08, 29, 00, 00, 00);
            decimal founds = 80000;
            Company company = new Company();
            company.Accounting = new RealAccounting();
            CurrenciesTest.AddCurrencies();
            company.Sales.CreateStore(1, "Test Store").MakeCurrent();

            Domain domain = company.Sales.CreateDomain(false, 1, "localhost", PaymentChannels.Agents.INSIDER);
            Player player = company.GetOrCreateCustomerById("*********").Player;
            Customer customer = company.CustomerByPlayer(player);
            customer.Identifier = "*********";

            var lotteries = company.Lotto900();
            State s = lotteries.GetOrCreateState("GA", "Georgia");
            LotteryPick<Pick3> lottery = (LotteryPick<Pick3>)lotteries.GetOrCreateLottery(3, s);
            lottery.TurnOnFireBall(false, now, "N/A");
            lottery.ForEach(12, 00, "123456", now, "N/A");

            // Create a product and buy the ticket
            Product ticketPick3StraightProd = lotteries.GetOrCreateProductById(1);
            ticketPick3StraightProd.Description = "Ticket pick 3 straight";
            ticketPick3StraightProd.Price = 1;
            string dates = "08/29/2024";
            string states = "GA";
            string hours = "12:00 PM";
            string withFireballs = "true";
            string strIsPresentPlayerBeforeToCloseStore = "true";
            string strUseNextDate = "false";
            NextDatesAccumulator nextDatesAccumulator = new NextDatesAccumulator(lotteries, domain, now, "3", dates, states, hours, withFireballs, strIsPresentPlayerBeforeToCloseStore, strUseNextDate);
            OrderCart myOrder = (OrderCart)company.GetNewOrder(customer);

            ExcludeSubtickets excludedSubtickets = new ExcludeSubtickets();
            string numbers = "";
            string selectionMode = "MultipleInputSingleAmount";
            decimal ticketAmount = 1;
            string includedNumbersForInput = "119";
            string gameType = "Straight";
            Order order = company.CreateTicketOrder(lotteries, player, states, dates, withFireballs, numbers, selectionMode, ticketAmount, includedNumbersForInput, excludedSubtickets, gameType, myOrder, ticketPick3StraightProd, nextDatesAccumulator, domain);
            company.AddOrders(myOrder, founds);
            int orderNumber = order.Number;
            order = company.OrderByNumber(orderNumber);
            order.AuthorizationId = 123;
            var marketplace = new Marketplace(company, "CR");
            var agent = marketplace.AddAgent("1");
            myOrder.Agent = (GamesEngine.Exchange.Agent)agent;
            company.SaleTickets(true, myOrder, now, founds, 1, domain);
            Assert.AreEqual(1, orderNumber);

            int fireBallNumber = 9;
            string winnerNumber = "112";
            var report = lottery.DrawPicks(new DateTime(2024, 08, 29, 12, 00, 00), winnerNumber, fireBallNumber, now, false, "Erick", false);
            Assert.AreEqual(1, report.TotalTickets);
            Assert.AreEqual(1, report.TotalWinners);
            Assert.AreEqual(150M, report.TotalPrize);
        }

        [TestMethod]
        public void BuyTicketWinner_DrawPicks4_FireBall_Test()
        {
            QueueInMemory queue = new QueueInMemory();
            Integration.Kafka = queue;

            DateTime now = new DateTime(2024, 08, 29, 00, 00, 00);
            decimal founds = 80000;
            Company company = new Company();
            company.Accounting = new RealAccounting();
            CurrenciesTest.AddCurrencies();
            company.Sales.CreateStore(1, "Test Store").MakeCurrent();

            Domain domain = company.Sales.CreateDomain(false, 1, "localhost", PaymentChannels.Agents.INSIDER);
            Player player = company.GetOrCreateCustomerById("*********").Player;
            Customer customer = company.CustomerByPlayer(player);
            customer.Identifier = "*********";

            var lotteries = company.Lotto900();
            State s = lotteries.GetOrCreateState("GA", "Georgia");
            LotteryPick<Pick4> lottery = (LotteryPick<Pick4>)lotteries.GetOrCreateLottery(4, s);
            lottery.TurnOnFireBall(false, now, "N/A");
            lottery.ForEach(12, 00, "123456", now, "N/A");

            // Create a product and buy the ticket
            Product ticketPick3StraightProd = lotteries.GetOrCreateProductById(1);
            ticketPick3StraightProd.Description = "Ticket pick 4 straight";
            ticketPick3StraightProd.Price = 1;
            string dates = "08/29/2024";
            string states = "GA";
            string hours = "12:00 PM";
            string withFireballs = "true";
            string strIsPresentPlayerBeforeToCloseStore = "true";
            string strUseNextDate = "false";
            NextDatesAccumulator nextDatesAccumulator = new NextDatesAccumulator(lotteries, domain, now, "4", dates, states, hours, withFireballs, strIsPresentPlayerBeforeToCloseStore, strUseNextDate);
            OrderCart myOrder = (OrderCart)company.GetNewOrder(customer);

            ExcludeSubtickets excludedSubtickets = new ExcludeSubtickets();
            string numbers = "";
            string selectionMode = "MultipleInputSingleAmount";
            decimal ticketAmount = 1;
            string includedNumbersForInput = "1119";
            string gameType = "Straight";
            Order order = company.CreateTicketOrder(lotteries, player, states, dates, withFireballs, numbers, selectionMode, ticketAmount, includedNumbersForInput, excludedSubtickets, gameType, myOrder, ticketPick3StraightProd, nextDatesAccumulator, domain);
            company.AddOrders(myOrder, founds);
            int orderNumber = order.Number;
            order = company.OrderByNumber(orderNumber);
            order.AuthorizationId = 123;
            var marketplace = new Marketplace(company, "CR");
            var agent = marketplace.AddAgent("1");
            myOrder.Agent = (GamesEngine.Exchange.Agent)agent;
            company.SaleTickets(true, myOrder, now, founds, 1, domain);
            Assert.AreEqual(1, orderNumber);

            int fireBallNumber = 9;
            string winnerNumber = "1112";
            var report = lottery.DrawPicks(new DateTime(2024, 08, 29, 12, 00, 00), winnerNumber, fireBallNumber, now, false, "Erick", false);
            Assert.AreEqual(1, report.TotalTickets);
            Assert.AreEqual(1, report.TotalWinners);
            Assert.AreEqual(1125.000M, report.TotalPrize);
        }

        [TestMethod]
        public void BuyTicketWinner_DrawPicks5_FireBall_Test()
        {
            QueueInMemory queue = new QueueInMemory();
            Integration.Kafka = queue;

            DateTime now = new DateTime(2024, 08, 29, 00, 00, 00);
            decimal founds = 80000;
            Company company = new Company();
            company.Accounting = new RealAccounting();
            CurrenciesTest.AddCurrencies();
            company.Sales.CreateStore(1, "Test Store").MakeCurrent();

            Domain domain = company.Sales.CreateDomain(false, 1, "localhost", PaymentChannels.Agents.INSIDER);
            Player player = company.GetOrCreateCustomerById("*********").Player;
            Customer customer = company.CustomerByPlayer(player);
            customer.Identifier = "*********";

            var lotteries = company.Lotto900();
            State s = lotteries.GetOrCreateState("GA", "Georgia");
            LotteryPick<Pick5> lottery = (LotteryPick<Pick5>)lotteries.GetOrCreateLottery(5, s);
            lottery.TurnOnFireBall(false, now, "N/A");
            lottery.ForEach(12, 00, "123456", now, "N/A");

            // Create a product and buy the ticket
            Product ticketPick3StraightProd = lotteries.GetOrCreateProductById(1);
            ticketPick3StraightProd.Description = "Ticket pick 5 straight";
            ticketPick3StraightProd.Price = 1;
            string dates = "08/29/2024";
            string states = "GA";
            string hours = "12:00 PM";
            string withFireballs = "true";
            string strIsPresentPlayerBeforeToCloseStore = "true";
            string strUseNextDate = "false";
            NextDatesAccumulator nextDatesAccumulator = new NextDatesAccumulator(lotteries, domain, now, "5", dates, states, hours, withFireballs, strIsPresentPlayerBeforeToCloseStore, strUseNextDate);
            OrderCart myOrder = (OrderCart)company.GetNewOrder(customer);

            ExcludeSubtickets excludedSubtickets = new ExcludeSubtickets();
            string numbers = "";
            string selectionMode = "MultipleInputSingleAmount";
            decimal ticketAmount = 1;
            string includedNumbersForInput = "11119";
            string gameType = "Straight";
            Order order = company.CreateTicketOrder(lotteries, player, states, dates, withFireballs, numbers, selectionMode, ticketAmount, includedNumbersForInput, excludedSubtickets, gameType, myOrder, ticketPick3StraightProd, nextDatesAccumulator, domain);
            company.AddOrders(myOrder, founds);
            int orderNumber = order.Number;
            order = company.OrderByNumber(orderNumber);
            order.AuthorizationId = 123;
            var marketplace = new Marketplace(company, "CR");
            var agent = marketplace.AddAgent("1");
            myOrder.Agent = (GamesEngine.Exchange.Agent)agent;
            company.SaleTickets(true, myOrder, now, founds, 1, domain);
            Assert.AreEqual(1, orderNumber);

            int fireBallNumber = 9;
            string winnerNumber = "11112";
            var report = lottery.DrawPicks(new DateTime(2024, 08, 29, 12, 00, 00), winnerNumber, fireBallNumber, now, false, "Erick", false);
            Assert.AreEqual(1, report.TotalTickets);
            Assert.AreEqual(1, report.TotalWinners);
            Assert.AreEqual(9000.0M, report.TotalPrize);
        }

        [TestMethod]
        public void BuyTicketWinner_DrawPicks2_Boxed_Test()
        {
            QueueInMemory queue = new QueueInMemory();
            Integration.Kafka = queue;

            DateTime now = new DateTime(2024, 08, 29, 00, 00, 00);
            decimal founds = 80000;
            Company company = new Company();
            company.Accounting = new RealAccounting();
            CurrenciesTest.AddCurrencies();
            company.Sales.CreateStore(1, "Test Store").MakeCurrent();

            Domain domain = company.Sales.CreateDomain(false, 1, "localhost", PaymentChannels.Agents.INSIDER);
            Player player = company.GetOrCreateCustomerById("*********").Player;
            Customer customer = company.CustomerByPlayer(player);
            customer.Identifier = "*********";

            var lotteries = company.Lotto900();
            State s = lotteries.GetOrCreateState("GA", "Georgia");
            LotteryPick<Pick2> lottery = (LotteryPick<Pick2>)lotteries.GetOrCreateLottery(2, s);
            lottery.TurnOnFireBall(false, now, "N/A");
            lottery.ForEach(12, 00, "123456", now, "N/A");

            // Create a product and buy the ticket
            Product ticketPick3StraightProd = lotteries.GetOrCreateProductById(1);
            ticketPick3StraightProd.Description = "Ticket pick 2 boxed";
            ticketPick3StraightProd.Price = 1;
            string dates = "08/29/2024";
            string states = "GA";
            string hours = "12:00 PM";
            string withFireballs = "true";
            string strIsPresentPlayerBeforeToCloseStore = "true";
            string strUseNextDate = "false";
            NextDatesAccumulator nextDatesAccumulator = new NextDatesAccumulator(lotteries, domain, now, "2", dates, states, hours, withFireballs, strIsPresentPlayerBeforeToCloseStore, strUseNextDate);
            OrderCart myOrder = (OrderCart)company.GetNewOrder(customer);

            ExcludeSubtickets excludedSubtickets = new ExcludeSubtickets();
            string numbers = "";
            string selectionMode = "MultipleInputSingleAmount";
            decimal ticketAmount = 1;
            string includedNumbersForInput = "19";
            string gameType = "Boxed";
            Order order = company.CreateTicketOrder(lotteries, player, states, dates, withFireballs, numbers, selectionMode, ticketAmount, includedNumbersForInput, excludedSubtickets, gameType, myOrder, ticketPick3StraightProd, nextDatesAccumulator, domain);
            company.AddOrders(myOrder, founds);
            int orderNumber = order.Number;
            order = company.OrderByNumber(orderNumber);
            order.AuthorizationId = 123;
            var marketplace = new Marketplace(company, "CR");
            var agent = marketplace.AddAgent("1");
            myOrder.Agent = (GamesEngine.Exchange.Agent)agent;
            company.SaleTickets(true, myOrder, now, founds, 1, domain);
            Assert.AreEqual(1, orderNumber);

            int fireBallNumber = 9;
            string winnerNumber = "12";
            var report = lottery.DrawPicks(new DateTime(2024, 08, 29, 12, 00, 00), winnerNumber, fireBallNumber, now, false, "Erick", false);
            Assert.AreEqual(1, report.TotalTickets);
            Assert.AreEqual(1, report.TotalWinners);
            Assert.AreEqual(11.25M, report.TotalPrize);
        }

        [TestMethod]
        public void BuyTicketWinner_DrawPicks2_FireBall_Boxed_Test()
        {
            QueueInMemory queue = new QueueInMemory();
            Integration.Kafka = queue;

            DateTime now = new DateTime(2024, 08, 29, 00, 00, 00);
            decimal founds = 80000;
            Company company = new Company();
            company.Accounting = new RealAccounting();
            CurrenciesTest.AddCurrencies();
            company.Sales.CreateStore(1, "Test Store").MakeCurrent();

            Domain domain = company.Sales.CreateDomain(false, 1, "localhost", PaymentChannels.Agents.INSIDER);
            Player player = company.GetOrCreateCustomerById("*********").Player;
            Customer customer = company.CustomerByPlayer(player);
            customer.Identifier = "*********";

            var lotteries = company.Lotto900();
            State s = lotteries.GetOrCreateState("GA", "Georgia");
            LotteryPick<Pick2> lottery = (LotteryPick<Pick2>)lotteries.GetOrCreateLottery(2, s);
            lottery.TurnOnFireBall(false, now, "N/A");
            lottery.ForEach(12, 00, "123456", now, "N/A");

            // Create a product and buy the ticket
            Product ticketPick3StraightProd = lotteries.GetOrCreateProductById(1);
            ticketPick3StraightProd.Description = "Ticket pick 2 boxed";
            ticketPick3StraightProd.Price = 1;
            string dates = "08/29/2024";
            string states = "GA";
            string hours = "12:00 PM";
            string withFireballs = "true";
            string strIsPresentPlayerBeforeToCloseStore = "true";
            string strUseNextDate = "false";
            NextDatesAccumulator nextDatesAccumulator = new NextDatesAccumulator(lotteries, domain, now, "2", dates, states, hours, withFireballs, strIsPresentPlayerBeforeToCloseStore, strUseNextDate);
            OrderCart myOrder = (OrderCart)company.GetNewOrder(customer);

            ExcludeSubtickets excludedSubtickets = new ExcludeSubtickets();
            string numbers = "";
            string selectionMode = "MultipleInputSingleAmount";
            decimal ticketAmount = 1;
            string includedNumbersForInput = "19";
            string gameType = "Boxed";
            Order order = company.CreateTicketOrder(lotteries, player, states, dates, withFireballs, numbers, selectionMode, ticketAmount, includedNumbersForInput, excludedSubtickets, gameType, myOrder, ticketPick3StraightProd, nextDatesAccumulator, domain);
            company.AddOrders(myOrder, founds);
            int orderNumber = order.Number;
            order = company.OrderByNumber(orderNumber);
            order.AuthorizationId = 123;
            var marketplace = new Marketplace(company, "CR");
            var agent = marketplace.AddAgent("1");
            myOrder.Agent = (GamesEngine.Exchange.Agent)agent;
            company.SaleTickets(true, myOrder, now, founds, 1, domain);
            Assert.AreEqual(1, orderNumber);

            int fireBallNumber = 9;
            string winnerNumber = "12";
            var report = lottery.DrawPicks(new DateTime(2024, 08, 29, 12, 00, 00), winnerNumber, fireBallNumber, now, false, "Erick", false);
            Assert.AreEqual(1, report.TotalTickets);
            Assert.AreEqual(1, report.TotalWinners);
            Assert.AreEqual(11.25M, report.TotalPrize);
        }

        [TestMethod]
        public void BuyTicketWinner_DrawPicks3_FireBall_Boxed_Test()
        {
            QueueInMemory queue = new QueueInMemory();
            Integration.Kafka = queue;

            DateTime now = new DateTime(2024, 08, 29, 00, 00, 00);
            decimal founds = 80000;
            Company company = new Company();
            company.Accounting = new RealAccounting();
            CurrenciesTest.AddCurrencies();
            company.Sales.CreateStore(1, "Test Store").MakeCurrent();

            Domain domain = company.Sales.CreateDomain(false, 1, "localhost", PaymentChannels.Agents.INSIDER);
            Player player = company.GetOrCreateCustomerById("*********").Player;
            Customer customer = company.CustomerByPlayer(player);
            customer.Identifier = "*********";

            var lotteries = company.Lotto900();
            State s = lotteries.GetOrCreateState("GA", "Georgia");
            LotteryPick<Pick3> lottery = (LotteryPick<Pick3>)lotteries.GetOrCreateLottery(3, s);
            lottery.TurnOnFireBall(false, now, "N/A");
            lottery.ForEach(12, 00, "123456", now, "N/A");

            // Create a product and buy the ticket
            Product ticketPick3StraightProd = lotteries.GetOrCreateProductById(1);
            ticketPick3StraightProd.Description = "Ticket pick 2 boxed";
            ticketPick3StraightProd.Price = 1;
            string dates = "08/29/2024";
            string states = "GA";
            string hours = "12:00 PM";
            string withFireballs = "true";
            string strIsPresentPlayerBeforeToCloseStore = "true";
            string strUseNextDate = "false";
            NextDatesAccumulator nextDatesAccumulator = new NextDatesAccumulator(lotteries, domain, now, "3", dates, states, hours, withFireballs, strIsPresentPlayerBeforeToCloseStore, strUseNextDate);
            OrderCart myOrder = (OrderCart)company.GetNewOrder(customer);

            ExcludeSubtickets excludedSubtickets = new ExcludeSubtickets();
            string numbers = "";
            string selectionMode = "MultipleInputSingleAmount";
            decimal ticketAmount = 1;
            string includedNumbersForInput = "119";
            string gameType = "Boxed";
            Order order = company.CreateTicketOrder(lotteries, player, states, dates, withFireballs, numbers, selectionMode, ticketAmount, includedNumbersForInput, excludedSubtickets, gameType, myOrder, ticketPick3StraightProd, nextDatesAccumulator, domain);
            company.AddOrders(myOrder, founds);
            int orderNumber = order.Number;
            order = company.OrderByNumber(orderNumber);
            order.AuthorizationId = 123;
            var marketplace = new Marketplace(company, "CR");
            var agent = marketplace.AddAgent("1");
            myOrder.Agent = (GamesEngine.Exchange.Agent)agent;
            company.SaleTickets(true, myOrder, now, founds, 1, domain);
            Assert.AreEqual(1, orderNumber);

            int fireBallNumber = 9;
            string winnerNumber = "112";
            var report = lottery.DrawPicks(new DateTime(2024, 08, 29, 12, 00, 00), winnerNumber, fireBallNumber, now, false, "Erick", false);
            Assert.AreEqual(1, report.TotalTickets);
            Assert.AreEqual(1, report.TotalWinners);
            Assert.AreEqual(50M, report.TotalPrize);
        }

        [TestMethod]
        public void BuyTicketWinner_DrawPicks4_FireBall_Boxed_Test()
        {
            QueueInMemory queue = new QueueInMemory();
            Integration.Kafka = queue;

            DateTime now = new DateTime(2024, 08, 29, 00, 00, 00);
            decimal founds = 80000;
            Company company = new Company();
            company.Accounting = new RealAccounting();
            CurrenciesTest.AddCurrencies();
            company.Sales.CreateStore(1, "Test Store").MakeCurrent();

            Domain domain = company.Sales.CreateDomain(false, 1, "localhost", PaymentChannels.Agents.INSIDER);
            Player player = company.GetOrCreateCustomerById("*********").Player;
            Customer customer = company.CustomerByPlayer(player);
            customer.Identifier = "*********";

            var lotteries = company.Lotto900();
            State s = lotteries.GetOrCreateState("GA", "Georgia");
            LotteryPick<Pick4> lottery = (LotteryPick<Pick4>)lotteries.GetOrCreateLottery(4, s);
            lottery.TurnOnFireBall(false, now, "N/A");
            lottery.ForEach(12, 00, "123456", now, "N/A");

            // Create a product and buy the ticket
            Product ticketPick3StraightProd = lotteries.GetOrCreateProductById(1);
            ticketPick3StraightProd.Description = "Ticket pick 2 boxed";
            ticketPick3StraightProd.Price = 1;
            string dates = "08/29/2024";
            string states = "GA";
            string hours = "12:00 PM";
            string withFireballs = "true";
            string strIsPresentPlayerBeforeToCloseStore = "true";
            string strUseNextDate = "false";
            NextDatesAccumulator nextDatesAccumulator = new NextDatesAccumulator(lotteries, domain, now, "4", dates, states, hours, withFireballs, strIsPresentPlayerBeforeToCloseStore, strUseNextDate);
            OrderCart myOrder = (OrderCart)company.GetNewOrder(customer);

            ExcludeSubtickets excludedSubtickets = new ExcludeSubtickets();
            string numbers = "";
            string selectionMode = "MultipleInputSingleAmount";
            decimal ticketAmount = 1;
            string includedNumbersForInput = "1119";
            string gameType = "Boxed";
            Order order = company.CreateTicketOrder(lotteries, player, states, dates, withFireballs, numbers, selectionMode, ticketAmount, includedNumbersForInput, excludedSubtickets, gameType, myOrder, ticketPick3StraightProd, nextDatesAccumulator, domain);
            company.AddOrders(myOrder, founds);
            int orderNumber = order.Number;
            order = company.OrderByNumber(orderNumber);
            order.AuthorizationId = 123;
            var marketplace = new Marketplace(company, "CR");
            var agent = marketplace.AddAgent("1");
            myOrder.Agent = (GamesEngine.Exchange.Agent)agent;
            company.SaleTickets(true, myOrder, now, founds, 1, domain);
            Assert.AreEqual(1, orderNumber);

            int fireBallNumber = 9;
            string winnerNumber = "1112";
            var report = lottery.DrawPicks(new DateTime(2024, 08, 29, 12, 00, 00), winnerNumber, fireBallNumber, now, false, "Erick", false);
            Assert.AreEqual(1, report.TotalTickets);
            Assert.AreEqual(1, report.TotalWinners);
            Assert.AreEqual(281.250M, report.TotalPrize);
        }

        [TestMethod]
        public void BuyTicketWinner_DrawPicks5_FireBall_Boxed_Test()
        {
            QueueInMemory queue = new QueueInMemory();
            Integration.Kafka = queue;

            DateTime now = new DateTime(2024, 08, 29, 00, 00, 00);
            decimal founds = 80000;
            Company company = new Company();
            company.Accounting = new RealAccounting();
            CurrenciesTest.AddCurrencies();
            company.Sales.CreateStore(1, "Test Store").MakeCurrent();

            Domain domain = company.Sales.CreateDomain(false, 1, "localhost", PaymentChannels.Agents.INSIDER);
            Player player = company.GetOrCreateCustomerById("*********").Player;
            Customer customer = company.CustomerByPlayer(player);
            customer.Identifier = "*********";

            var lotteries = company.Lotto900();
            State s = lotteries.GetOrCreateState("GA", "Georgia");
            LotteryPick<Pick5> lottery = (LotteryPick<Pick5>)lotteries.GetOrCreateLottery(5, s);
            lottery.TurnOnFireBall(false, now, "N/A");
            lottery.ForEach(12, 00, "123456", now, "N/A");

            // Create a product and buy the ticket
            Product ticketPick3StraightProd = lotteries.GetOrCreateProductById(1);
            ticketPick3StraightProd.Description = "Ticket pick 2 boxed";
            ticketPick3StraightProd.Price = 1;
            string dates = "08/29/2024";
            string states = "GA";
            string hours = "12:00 PM";
            string withFireballs = "true";
            string strIsPresentPlayerBeforeToCloseStore = "true";
            string strUseNextDate = "false";
            NextDatesAccumulator nextDatesAccumulator = new NextDatesAccumulator(lotteries, domain, now, "5", dates, states, hours, withFireballs, strIsPresentPlayerBeforeToCloseStore, strUseNextDate);
            OrderCart myOrder = (OrderCart)company.GetNewOrder(customer);

            ExcludeSubtickets excludedSubtickets = new ExcludeSubtickets();
            string numbers = "";
            string selectionMode = "MultipleInputSingleAmount";
            decimal ticketAmount = 1;
            string includedNumbersForInput = "11119";
            string gameType = "Boxed";
            Order order = company.CreateTicketOrder(lotteries, player, states, dates, withFireballs, numbers, selectionMode, ticketAmount, includedNumbersForInput, excludedSubtickets, gameType, myOrder, ticketPick3StraightProd, nextDatesAccumulator, domain);
            company.AddOrders(myOrder, founds);
            int orderNumber = order.Number;
            order = company.OrderByNumber(orderNumber);
            order.AuthorizationId = 123;
            var marketplace = new Marketplace(company, "CR");
            var agent = marketplace.AddAgent("1");
            myOrder.Agent = (GamesEngine.Exchange.Agent)agent;
            company.SaleTickets(true, myOrder, now, founds, 1, domain);
            Assert.AreEqual(1, orderNumber);

            int fireBallNumber = 9;
            string winnerNumber = "11112";
            var report = lottery.DrawPicks(new DateTime(2024, 08, 29, 12, 00, 00), winnerNumber, fireBallNumber, now, false, "Erick", false);
            Assert.AreEqual(1, report.TotalTickets);
            Assert.AreEqual(1, report.TotalWinners);
            Assert.AreEqual(1800.0M, report.TotalPrize);
        }

        [TestMethod]
        public void BuyTicketLosser_DrawPicks_FireBall_Test()
        {
            QueueInMemory queue = new QueueInMemory();
            Integration.Kafka = queue;

            DateTime now = new DateTime(2024, 08, 29, 00, 00, 00);
            decimal founds = 80000;
            Company company = new Company();
            company.Accounting = new RealAccounting();
            CurrenciesTest.AddCurrencies();
            company.Sales.CreateStore(1, "Test Store").MakeCurrent();

            Domain domain = company.Sales.CreateDomain(false, 1, "localhost", PaymentChannels.Agents.INSIDER);
            Player player = company.GetOrCreateCustomerById("*********").Player;
            Customer customer = company.CustomerByPlayer(player);
            customer.Identifier = "*********";

            var lotteries = company.Lotto900();
            State s = lotteries.GetOrCreateState("GA", "Georgia");
            LotteryPick<Pick2> lottery = (LotteryPick<Pick2>)lotteries.GetOrCreateLottery(2, s);
            lottery.TurnOnFireBall(false, now, "N/A");
            lottery.ForEach(12, 00, "123456", now, "N/A");

            // Create a product and buy the ticket
            Product ticketPick3StraightProd = lotteries.GetOrCreateProductById(1);
            ticketPick3StraightProd.Description = "Ticket pick 2 straight";
            ticketPick3StraightProd.Price = 1;
            string dates = "08/29/2024";
            string states = "GA";
            string hours = "12:00 PM";
            string withFireballs = "true";
            string strIsPresentPlayerBeforeToCloseStore = "true";
            string strUseNextDate = "false";
            NextDatesAccumulator nextDatesAccumulator = new NextDatesAccumulator(lotteries, domain, now, "2", dates, states, hours, withFireballs, strIsPresentPlayerBeforeToCloseStore, strUseNextDate);
            OrderCart myOrder = (OrderCart)company.GetNewOrder(customer);

            ExcludeSubtickets excludedSubtickets = new ExcludeSubtickets();
            string numbers = "";
            string selectionMode = "MultipleInputSingleAmount";
            decimal ticketAmount = 10;
            string includedNumbersForInput = "59";
            string gameType = "Straight";
            Order order = company.CreateTicketOrder(lotteries, player, states, dates, withFireballs, numbers, selectionMode, ticketAmount, includedNumbersForInput, excludedSubtickets, gameType, myOrder, ticketPick3StraightProd, nextDatesAccumulator, domain);
            company.AddOrders(myOrder, founds);
            int orderNumber = order.Number;
            order = company.OrderByNumber(orderNumber);
            order.AuthorizationId = 123;
            var marketplace = new Marketplace(company, "CR");
            var agent = marketplace.AddAgent("1");
            myOrder.Agent = (GamesEngine.Exchange.Agent)agent;
            company.SaleTickets(true, myOrder, now, founds, 1, domain);
            Assert.AreEqual(1, orderNumber);

            int fireBallNumber = 9;
            string winnerNumber = "12";
            var report = lottery.DrawPicks(new DateTime(2024, 08, 29, 12, 00, 00), winnerNumber, fireBallNumber, now, false, "Erick", false);
            Assert.AreEqual(1, report.TotalTickets);
            Assert.AreEqual(0, report.TotalWinners);
            Assert.AreEqual(0, report.TotalPrize);
        }

        [TestMethod]
        public void SameTicket_OneWinnerNormal_OneWinnerWithFireBall_DrawPicks2_FireBall_Test()
        {
            QueueInMemory queue = new QueueInMemory();
            Integration.Kafka = queue;

            DateTime now = new DateTime(2024, 08, 29, 00, 00, 00);
            decimal founds = 80000;
            Company company = new Company();
            company.Accounting = new RealAccounting();
            CurrenciesTest.AddCurrencies();
            company.Sales.CreateStore(1, "Test Store").MakeCurrent();

            Domain domain = company.Sales.CreateDomain(false, 1, "localhost", PaymentChannels.Agents.INSIDER);
            Player player = company.GetOrCreateCustomerById("*********").Player;
            Customer customer = company.CustomerByPlayer(player);
            customer.Identifier = "*********";

            var lotteries = company.Lotto900();
            State s = lotteries.GetOrCreateState("GA", "Georgia");
            LotteryPick<Pick2> lottery = (LotteryPick<Pick2>)lotteries.GetOrCreateLottery(2, s);
            lottery.TurnOnFireBall(false, now, "N/A");
            lottery.ForEach(12, 00, "123456", now, "N/A");
            ExcludeSubtickets excludedSubtickets = new ExcludeSubtickets();

            // Create a product and buy the ticket
            Product ticketPick3StraightProd = lotteries.GetOrCreateProductById(1);
            ticketPick3StraightProd.Description = "Ticket pick 3 straight";
            ticketPick3StraightProd.Price = 1;
            string dates = "08/29/2024";
            string states = "GA,GA";
            string hours = "12:00 PM,12:00 PM";
            string withFireballs = "false,true";
            string strIsPresentPlayerBeforeToCloseStore = "true,true";
            string strUseNextDate = "false,false";
            NextDatesAccumulator nextDatesAccumulator = new NextDatesAccumulator(lotteries, domain, now, "2", dates, states, hours, withFireballs, strIsPresentPlayerBeforeToCloseStore, strUseNextDate);
            OrderCart myOrder = (OrderCart)company.GetNewOrder(customer);
            string numbers = "";
            string selectionMode = "MultipleInputSingleAmount";
            decimal ticketAmount = 1;
            string includedNumbersForInput = "12";
            string gameType = "Straight";
            Order order = company.CreateTicketOrder(lotteries, player, states, dates, withFireballs, numbers, selectionMode, ticketAmount, includedNumbersForInput, excludedSubtickets, gameType, myOrder, ticketPick3StraightProd, nextDatesAccumulator, domain);
            company.AddOrders(myOrder, founds);
            int orderNumber = order.Number;
            order = company.OrderByNumber(orderNumber);
            order.AuthorizationId = 123;
            var marketplace = new Marketplace(company, "CR");
            var agent = marketplace.AddAgent("1");
            myOrder.Agent = (GamesEngine.Exchange.Agent)agent;
            company.SaleTickets(true, myOrder, now, founds, 1, domain);
            Assert.AreEqual(1, orderNumber);

            int fireBallNumber = 9;
            string winnerNumber = "12";
            var report = lottery.DrawPicks(new DateTime(2024, 08, 29, 12, 00, 00), winnerNumber, fireBallNumber, now, false, "Erick", false);
            Assert.AreEqual(2, report.TotalTickets);
            Assert.AreEqual(2, report.TotalWinners);
            Assert.AreEqual(90.0M + 45.0M, report.TotalPrize);
        }

        [TestMethod]
        public void SameTicket_4Tickets_2WinnerNormal_2WinnerWithFireBall_DrawPicks2_FireBall_Test()
        {
            QueueInMemory queue = new QueueInMemory();
            Integration.Kafka = queue;

            DateTime now = new DateTime(2024, 08, 29, 00, 00, 00);
            decimal founds = 80000;
            Company company = new Company();
            company.Accounting = new RealAccounting();
            CurrenciesTest.AddCurrencies();
            company.Sales.CreateStore(1, "Test Store").MakeCurrent();

            Domain domain = company.Sales.CreateDomain(false, 1, "localhost", PaymentChannels.Agents.INSIDER);
            Player player = company.GetOrCreateCustomerById("*********").Player;
            Customer customer = company.CustomerByPlayer(player);
            customer.Identifier = "*********";

            var lotteries = company.Lotto900();
            State s = lotteries.GetOrCreateState("GA", "Georgia");
            LotteryPick<Pick2> lottery = (LotteryPick<Pick2>)lotteries.GetOrCreateLottery(2, s);
            lottery.TurnOnFireBall(false, now, "N/A");
            lottery.ForEach(12, 00, "123456", now, "N/A");
            ExcludeSubtickets excludedSubtickets = new ExcludeSubtickets();

            // Create a product and buy the ticket
            Product ticketPick3StraightProd = lotteries.GetOrCreateProductById(1);
            ticketPick3StraightProd.Description = "Ticket pick 3 straight";
            ticketPick3StraightProd.Price = 1;
            string dates = "08/29/2024";
            string states = "GA,GA";
            string hours = "12:00 PM,12:00 PM";
            string withFireballs = "false,true";
            string strIsPresentPlayerBeforeToCloseStore = "true,true";
            string strUseNextDate = "false,false";
            NextDatesAccumulator nextDatesAccumulator = new NextDatesAccumulator(lotteries, domain, now, "2", dates, states, hours, withFireballs, strIsPresentPlayerBeforeToCloseStore, strUseNextDate);
            OrderCart myOrder = (OrderCart)company.GetNewOrder(customer);
            string numbers = "";
            string selectionMode = "MultipleInputSingleAmount";
            decimal ticketAmount = 1;
            string includedNumbersForInput = "12";
            string gameType = "Straight";
            Order order = company.CreateTicketOrder(lotteries, player, states, dates, withFireballs, numbers, selectionMode, ticketAmount, includedNumbersForInput, excludedSubtickets, gameType, myOrder, ticketPick3StraightProd, nextDatesAccumulator, domain);
            company.AddOrders(myOrder, founds);
            int orderNumber = order.Number;
            order = company.OrderByNumber(orderNumber);
            order.AuthorizationId = 123;
            var marketplace = new Marketplace(company, "CR");
            var agent = marketplace.AddAgent("1");
            myOrder.Agent = (GamesEngine.Exchange.Agent)agent;
            company.SaleTickets(true, myOrder, now, founds, 1, domain);
            Assert.AreEqual(1, orderNumber);

            int fireBallNumber = 9;
            string winnerNumber = "12";
            var report = lottery.DrawPicks(new DateTime(2024, 08, 29, 12, 00, 00), winnerNumber, fireBallNumber, now, false, "Erick", false);
            Assert.AreEqual(2, report.TotalTickets);
            Assert.AreEqual(2, report.TotalWinners);
            Assert.AreEqual(90.0M + 45.0M, report.TotalPrize);
        }

        [TestMethod]
        public void TwoTickets_OneWinnerNormal_OneWinnerWithFireBall_DrawPicks2_FireBall_Test()
        {
            QueueInMemory queue = new QueueInMemory();
            Integration.Kafka = queue;

            DateTime now = new DateTime(2024, 08, 29, 00, 00, 00);
            decimal founds = 80000;
            Company company = new Company();
            company.Accounting = new RealAccounting();
            CurrenciesTest.AddCurrencies();
            company.Sales.CreateStore(1, "Test Store").MakeCurrent();

            Domain domain = company.Sales.CreateDomain(false, 1, "localhost", PaymentChannels.Agents.INSIDER);
            Player player = company.GetOrCreateCustomerById("*********").Player;
            Customer customer = company.CustomerByPlayer(player);
            customer.Identifier = "*********";

            var lotteries = company.Lotto900();
            State s = lotteries.GetOrCreateState("GA", "Georgia");
            LotteryPick<Pick2> lottery = (LotteryPick<Pick2>)lotteries.GetOrCreateLottery(2, s);
            lottery.TurnOnFireBall(false, now, "N/A");
            lottery.ForEach(12, 00, "123456", now, "N/A");
            ExcludeSubtickets excludedSubtickets = new ExcludeSubtickets();

            // Create a product and buy the ticket
            Product ticketPick3StraightProd = lotteries.GetOrCreateProductById(1);
            ticketPick3StraightProd.Description = "Ticket pick 2 straight";
            ticketPick3StraightProd.Price = 1;
            string dates = "08/29/2024";
            string states = "GA";
            string hours = "12:00 PM";
            string withFireballs = "false";
            string strIsPresentPlayerBeforeToCloseStore = "true";
            string strUseNextDate = "false";
            NextDatesAccumulator nextDatesAccumulator = new NextDatesAccumulator(lotteries, domain, now, "2", dates, states, hours, withFireballs, strIsPresentPlayerBeforeToCloseStore, strUseNextDate);
            OrderCart myOrder = (OrderCart)company.GetNewOrder(customer);
            string numbers = "";
            string selectionMode = "MultipleInputSingleAmount";
            decimal ticketAmount = 1;
            string includedNumbersForInput = "12";
            string gameType = "Straight";
            Order order = company.CreateTicketOrder(lotteries, player, states, dates, withFireballs, numbers, selectionMode, ticketAmount, includedNumbersForInput, excludedSubtickets, gameType, myOrder, ticketPick3StraightProd, nextDatesAccumulator, domain);
            company.AddOrders(myOrder, founds);
            int orderNumber = order.Number;
            order = company.OrderByNumber(orderNumber);
            order.AuthorizationId = 123;
            var marketplace = new Marketplace(company, "CR");
            var agent = marketplace.AddAgent("1");
            myOrder.Agent = (GamesEngine.Exchange.Agent)agent;
            company.SaleTickets(true, myOrder, now, founds, 1, domain);
            Assert.AreEqual(1, orderNumber);

            // Create a product and buy the ticket
            ticketPick3StraightProd = lotteries.GetOrCreateProductById(2);
            ticketPick3StraightProd.Description = "Ticket pick 2 straight";
            ticketPick3StraightProd.Price = 1;
            dates = "08/29/2024";
            states = "GA";
            hours = "12:00 PM";
            withFireballs = "true";
            strIsPresentPlayerBeforeToCloseStore = "true";
            strUseNextDate = "false";
            nextDatesAccumulator = new NextDatesAccumulator(lotteries, domain, now, "2", dates, states, hours, withFireballs, strIsPresentPlayerBeforeToCloseStore, strUseNextDate);
            myOrder = (OrderCart)company.GetNewOrder(customer);
            numbers = "";
            selectionMode = "MultipleInputSingleAmount";
            ticketAmount = 1;
            includedNumbersForInput = "12";
            gameType = "Straight";
            order = company.CreateTicketOrder(lotteries, player, states, dates, withFireballs, numbers, selectionMode, ticketAmount, includedNumbersForInput, excludedSubtickets, gameType, myOrder, ticketPick3StraightProd, nextDatesAccumulator, domain);
            company.AddOrders(myOrder, founds);
            orderNumber = order.Number;
            order = company.OrderByNumber(orderNumber);
            order.AuthorizationId = 123;
            marketplace = new Marketplace(company, "CR");
            agent = marketplace.AddAgent("1");
            myOrder.Agent = (GamesEngine.Exchange.Agent)agent;
            company.SaleTickets(true, myOrder, now, founds, 2, domain);
            Assert.AreEqual(2, orderNumber);

            int fireBallNumber = 9;
            string winnerNumber = "12";
            var report = lottery.DrawPicks(new DateTime(2024, 08, 29, 12, 00, 00), winnerNumber, fireBallNumber, now, false, "Erick", false);
            Assert.AreEqual(2, report.TotalTickets);
            Assert.AreEqual(2, report.TotalWinners);
            Assert.AreEqual(90.0M + 45.0M, report.TotalPrize);
        }

        [TestMethod]
        public void DrawPicks2WithFB_TicketWithoutFB_Test()
        {
            QueueInMemory queue = new QueueInMemory();
            Integration.Kafka = queue;

            DateTime now = new DateTime(2024, 08, 29, 00, 00, 00);
            decimal founds = 80000;
            Company company = new Company();
            company.Accounting = new RealAccounting();
            CurrenciesTest.AddCurrencies();
            company.Sales.CreateStore(1, "Test Store").MakeCurrent();

            Domain domain = company.Sales.CreateDomain(false, 1, "localhost", PaymentChannels.Agents.INSIDER);
            Player player = company.GetOrCreateCustomerById("*********").Player;
            Customer customer = company.CustomerByPlayer(player);
            customer.Identifier = "*********";

            var lotteries = company.Lotto900();
            State s = lotteries.GetOrCreateState("GA", "Georgia");
            LotteryPick<Pick2> lottery = (LotteryPick<Pick2>)lotteries.GetOrCreateLottery(2, s);
            lottery.TurnOnFireBall(false, now, "N/A");
            lottery.ForEach(12, 00, "123456", now, "N/A");

            // Create a product and buy the ticket
            Product ticketPick3StraightProd = lotteries.GetOrCreateProductById(1);
            ticketPick3StraightProd.Description = "Ticket pick 3 straight";
            ticketPick3StraightProd.Price = 1;
            string dates = "08/29/2024";
            string states = "GA";
            string hours = "12:00 PM";
            string withFireballs = "false";
            string strIsPresentPlayerBeforeToCloseStore = "true";
            string strUseNextDate = "false";
            NextDatesAccumulator nextDatesAccumulator = new NextDatesAccumulator(lotteries, domain, now, "2", dates, states, hours, withFireballs, strIsPresentPlayerBeforeToCloseStore, strUseNextDate);
            OrderCart myOrder = (OrderCart)company.GetNewOrder(customer);

            ExcludeSubtickets excludedSubtickets = new ExcludeSubtickets();
            string numbers = "";
            string selectionMode = "MultipleInputSingleAmount";
            decimal ticketAmount = 1;
            string includedNumbersForInput = "12";
            string gameType = "Straight";
            Order order = company.CreateTicketOrder(lotteries, player, states, dates, withFireballs, numbers, selectionMode, ticketAmount, includedNumbersForInput, excludedSubtickets, gameType, myOrder, ticketPick3StraightProd, nextDatesAccumulator, domain);
            company.AddOrders(myOrder, founds);
            int orderNumber = order.Number;
            order = company.OrderByNumber(orderNumber);
            order.AuthorizationId = 123;
            var marketplace = new Marketplace(company, "CR");
            var agent = marketplace.AddAgent("1");
            myOrder.Agent = (GamesEngine.Exchange.Agent)agent;
            company.SaleTickets(true, myOrder, now, founds, 1, domain);
            Assert.AreEqual(1, orderNumber);

            int fireBallNumber = 9;
            string winnerNumber = "12";
            var report = lottery.DrawPicks(new DateTime(2024, 08, 29, 12, 00, 00), winnerNumber, fireBallNumber, now, false, "Erick", false);
            Assert.AreEqual(1, report.TotalTickets);
            Assert.AreEqual(1, report.TotalWinners);
            Assert.AreEqual(90M, report.TotalPrize);
        }

        [TestMethod]
        public void TurnOnFireBall_TurnOffFireBall_Test()
        {
            bool itIsThePresent = false;
            QueueInMemory queue = new QueueInMemory();
            Integration.Kafka = queue;

            DateTime now = new DateTime(2024, 08, 29, 00, 00, 00);
            Company company = new Company();
            company.Accounting = new RealAccounting();
            CurrenciesTest.AddCurrencies();
            company.Sales.CreateStore(1, "Test Store").MakeCurrent();

            Domain domain = company.Sales.CreateDomain(false, 1, "localhost", PaymentChannels.Agents.INSIDER);
            Player player = company.GetOrCreateCustomerById("*********").Player;
            Customer customer = company.CustomerByPlayer(player);
            customer.Identifier = "*********";

            var lotteries = company.Lotto900();

            State s = lotteries.GetOrCreateState("GA", "Georgia");
            LotteryPick<Pick2> lottery = (LotteryPick<Pick2>)lotteries.GetOrCreateLottery(2, s);
            lottery.TurnOnFireBall(itIsThePresent, now, "N/A");
            lottery.ForEach(12, 00, "123456", now, "N/A");

            Assert.IsTrue(lottery.IsFireBallTurnedOn);
            Assert.ThrowsException<GameEngineException>(() => lottery.TurnOnFireBall(itIsThePresent, now, "N/A"));
            lottery.TurnOffFireBall(itIsThePresent, now, "N/A");
            Assert.IsFalse(lottery.IsFireBallTurnedOn);
            Assert.ThrowsException<GameEngineException>(() => lottery.TurnOffFireBall(itIsThePresent, now, "N/A"));
            lottery.TurnOnFireBall(itIsThePresent, now, "N/A");
            Assert.IsTrue(lottery.IsFireBallTurnedOn);
        }

        [TestMethod]
        public void PurchaseTickets_Winner_DrawPicks2_ConfirmDraw_Test()
        {
            bool itIsThePresent = false;
            QueueInMemory queue = new QueueInMemory();
            Integration.Kafka = queue;

            DateTime now = new DateTime(2024, 08, 29, 00, 00, 00);
            Company company = new Company();
            company.Accounting = new RealAccounting();
            CurrenciesTest.AddCurrencies();
            company.Sales.CreateStore(1, "Test Store").MakeCurrent();

            Domain domain = company.Sales.CreateDomain(false, 1, "localhost", PaymentChannels.Agents.INSIDER);
            Player player = company.GetOrCreateCustomerById("*********").Player;
            Customer customer = company.CustomerByPlayer(player);
            customer.Identifier = "*********";

            var lotteries = company.Lotto900();

            Product ticketPick2StraightProd = lotteries.GetOrCreateProductById(5);
            ticketPick2StraightProd.Description = "Ticket pick 2 straight";
            ticketPick2StraightProd.Price = 1;

            State s = lotteries.GetOrCreateState("GA", "Georgia");
            LotteryPick<Pick2> lottery = (LotteryPick<Pick2>)lotteries.GetOrCreateLottery(2, s);
            lottery.ForEach(12, 00, "123456", now, "N/A");

            // Create a product and buy the ticket
            Product ticketPick3StraightProd = lotteries.GetOrCreateProductById(1);
            ticketPick3StraightProd.Description = "Ticket pick 2 straight";
            ticketPick3StraightProd.Price = 1;
            string dates = "08/29/2024";
            string states = "GA";
            string hours = "12:00 PM";
            string withFireballs = "false";
            ExcludeSubtickets excludedSubtickets = new ExcludeSubtickets();

            int orderNumber = company.IdentityOrderNumber;
            OrderCart myOrder = (OrderCart)company.NewOrder(customer, orderNumber, Currencies.CODES.USD);
            lotteries.NextDatesAccumulator.Calculate(domain, now, "2", dates, states, hours, withFireballs);

            myOrder = (OrderCart)company.CreateTicketOrderWithMultipleAuthorization(player, states, dates, withFireballs, "MultipleInputSingleAmount", 1, new List<string> { "12" }, "S", myOrder, ticketPick2StraightProd, lotteries.NextDatesAccumulator, domain);

            company.AddOrders(myOrder);
            int lowReference = company.IdentitytBetNumber;
            int highReference = (lowReference + myOrder.CountOfItems - 1);
            AuthorizationsNumbers auths = new AuthorizationsNumbers(*********, 2);
            company.PurchaseTickets(itIsThePresent, myOrder, now, auths, domain, lowReference, orderNumber, "Artemis_ThirdParty_Deposit_USD");

            string winnerNumber = "12";
            var report = lottery.DrawPicks(new DateTime(2024, 08, 29, 12, 00, 00), winnerNumber, now, false, "Erick", false);
            Assert.AreEqual(1, report.TotalTickets);
            Assert.AreEqual(1, report.TotalWinners);
            Assert.AreEqual(90M, report.TotalPrize);

            DateTime drawDate = new DateTime(2024, 08, 29, 12, 00, 00);
            bool canBeConfirmed = lottery.CanBeConfirmed(drawDate);
            Assert.IsTrue(canBeConfirmed);

            PicksDrawingsSummaryReport drawingsSummaryReport = (PicksDrawingsSummaryReport)lottery.ConfirmDraw(drawDate, now, "N/a", itIsThePresent, true);
            Assert.AreEqual(1, drawingsSummaryReport.TotalPlayers);
            Assert.AreEqual(1, drawingsSummaryReport.TotalWinners);
            Assert.AreEqual(1, drawingsSummaryReport.TotalTickets);
            Assert.AreEqual(1, drawingsSummaryReport.TotalTicketAmount);
            Assert.AreEqual(90M, drawingsSummaryReport.TotalPrize);
            //Assert.AreEqual(0M, drawingsSummaryReport.Profit);
            //Assert.IsFalse(drawingsSummaryReport.IsAlreadyGradedWithTheSameNumbers);
            Assert.IsFalse(drawingsSummaryReport.WithFireBall);
        }

        [TestMethod]
        public void ConfirmDraw_WithAndWithout_FireBall_Test()
        {
            bool itIsThePresent = false;
            QueueInMemory queue = new QueueInMemory();
            Integration.Kafka = queue;

            DateTime now = new DateTime(2024, 08, 29, 00, 00, 00);
            Company company = new Company();
            company.Accounting = new RealAccounting();
            CurrenciesTest.AddCurrencies();
            company.Sales.CreateStore(1, "Test Store").MakeCurrent();

            Domain domain = company.Sales.CreateDomain(false, 1, "localhost", PaymentChannels.Agents.INSIDER);
            Player player = company.GetOrCreateCustomerById("*********").Player;
            Customer customer = company.CustomerByPlayer(player);
            customer.Identifier = "*********";

            var lotteries = company.Lotto900();

            Product ticketPick2StraightProd = lotteries.GetOrCreateProductById(5);
            ticketPick2StraightProd.Description = "Ticket pick 2 straight";
            ticketPick2StraightProd.Price = 1;

            State s = lotteries.GetOrCreateState("GA", "Georgia");
            LotteryPick<Pick2> lottery = (LotteryPick<Pick2>)lotteries.GetOrCreateLottery(2, s);
            lottery.TurnOnFireBall(itIsThePresent, now, "N/A");
            lottery.ForEach(12, 00, "123456", now, "N/A");

            // Create a product and buy the ticket
            Product ticketPick3StraightProd = lotteries.GetOrCreateProductById(1);
            ticketPick3StraightProd.Description = "Ticket pick 2 straight";
            ticketPick3StraightProd.Price = 1;
            string dates = "08/29/2024";
            string states = "GA,GA";
            string hours = "12:00 PM,12:00 PM";
            string withFireballs = "false,true";
            ExcludeSubtickets excludedSubtickets = new ExcludeSubtickets();

            int orderNumber = company.IdentityOrderNumber;
            OrderCart myOrder = (OrderCart)company.NewOrder(customer, orderNumber, Currencies.CODES.USD);
            lotteries.NextDatesAccumulator.Calculate(domain, now, "2", dates, states, hours, withFireballs);

            myOrder = (OrderCart)company.CreateTicketOrderWithMultipleAuthorization(player, states, dates, withFireballs, "MultipleInputSingleAmount", 1, new List<string> { "12" }, "S", myOrder, ticketPick2StraightProd, lotteries.NextDatesAccumulator, domain);

            company.AddOrders(myOrder);
            int lowReference = company.IdentitytBetNumber;
            int highReference = (lowReference + myOrder.CountOfItems - 1);
            AuthorizationsNumbers auths = new AuthorizationsNumbers(*********, 2);
            company.PurchaseTickets(itIsThePresent, myOrder, now, auths, domain, lowReference, orderNumber, "Artemis_ThirdParty_Deposit_USD");

            int fireBallNumber = 9;
            string winnerNumber = "12";
            var report = lottery.DrawPicks(new DateTime(2024, 08, 29, 12, 00, 00), winnerNumber, fireBallNumber, now, false, "Erick", false);
            Assert.AreEqual(2, report.TotalTickets);
            Assert.AreEqual(2, report.TotalWinners);
            Assert.AreEqual(90M + 45M, report.TotalPrize);

            DateTime drawDate = new DateTime(2024, 08, 29, 12, 00, 00);
            bool canBeConfirmed = lottery.CanBeConfirmed(drawDate);
            Assert.IsTrue(canBeConfirmed);

            PicksDrawingsSummaryReport drawingsSummaryReport = (PicksDrawingsSummaryReport)lottery.ConfirmDraw(drawDate, now, "N/a", itIsThePresent, true);
            Assert.AreEqual(2, drawingsSummaryReport.TotalPlayers);
            Assert.AreEqual(2, drawingsSummaryReport.TotalWinners);
            Assert.AreEqual(2, drawingsSummaryReport.TotalTickets);
            Assert.AreEqual(2, drawingsSummaryReport.TotalTicketAmount);
            Assert.AreEqual(90M + 45M, drawingsSummaryReport.TotalPrize);
            Assert.IsFalse(drawingsSummaryReport.WithFireBall);
        }

        [TestMethod]
        public void PurchaseTickets_Winner_WinnerFB_TurnOff_DrawPicks2_ConfirmDraw_Test()
        {
            bool itIsThePresent = false;
            QueueInMemory queue = new QueueInMemory();
            Integration.Kafka = queue;

            DateTime now = new DateTime(2024, 08, 29, 00, 00, 00);
            Company company = new Company();
            company.Accounting = new RealAccounting();
            CurrenciesTest.AddCurrencies();
            company.Sales.CreateStore(1, "Test Store").MakeCurrent();

            Domain domain = company.Sales.CreateDomain(false, 1, "localhost", PaymentChannels.Agents.INSIDER);
            Player player = company.GetOrCreateCustomerById("*********").Player;
            Customer customer = company.CustomerByPlayer(player);
            customer.Identifier = "*********";

            var lotteries = company.Lotto900();

            Product ticketPick2StraightProd = lotteries.GetOrCreateProductById(5);
            ticketPick2StraightProd.Description = "Ticket pick 2 straight";
            ticketPick2StraightProd.Price = 1;

            State s = lotteries.GetOrCreateState("GA", "Georgia");
            LotteryPick<Pick2> lottery = (LotteryPick<Pick2>)lotteries.GetOrCreateLottery(2, s);
            lottery.TurnOnFireBall(itIsThePresent, now, "N/A");
            lottery.ForEach(12, 00, "123456", now, "N/A");

            State s2 = lotteries.GetOrCreateState("FL", "Florida");
            LotteryPick<Pick2> lottery2 = (LotteryPick<Pick2>)lotteries.GetOrCreateLottery(2, s2);
            lottery2.TurnOnFireBall(itIsThePresent, now, "N/A");
            lottery2.ForEach(12, 00, "123456", now, "N/A");

            string dates = "08/29/2024";
            string states = "GA,FL,GA,FL";
            string hours = "12:00 PM,12:00 PM,12:00 PM,12:00 PM";
            string withFireballs = "true,false,false,true";
            ExcludeSubtickets excludedSubtickets = new ExcludeSubtickets();

            int orderNumber = company.IdentityOrderNumber;
            OrderCart myOrder = (OrderCart)company.NewOrder(customer, orderNumber, Currencies.CODES.USD);
            lotteries.NextDatesAccumulator.Calculate(domain, now, "2", dates, states, hours, withFireballs);

            myOrder = (OrderCart)company.CreateTicketOrderWithMultipleAuthorization(player, states, dates, withFireballs, "MultipleInputSingleAmount", 1, new List<string> { "12" }, "S", myOrder, ticketPick2StraightProd, lotteries.NextDatesAccumulator, domain);

            company.AddOrders(myOrder);
            int lowReference = company.IdentitytBetNumber;
            int highReference = (lowReference + myOrder.CountOfItems - 1);
            AuthorizationsNumbers auths = new AuthorizationsNumbers(*********, 4);
            company.PurchaseTickets(itIsThePresent, myOrder, now, auths, domain, lowReference, orderNumber, "Artemis_ThirdParty_Deposit_USD");

            int fireBallNumber = 9;
            string winnerNumber = "12";
            var report = lottery.DrawPicks(new DateTime(2024, 08, 29, 12, 00, 00), winnerNumber, fireBallNumber, now, false, "Erick", false);
            Assert.AreEqual(2, report.TotalTickets);
            Assert.AreEqual(2, report.TotalWinners);
            Assert.AreEqual(135M, report.TotalPrize);

            fireBallNumber = 9;
            winnerNumber = "12";
            report = lottery2.DrawPicks(new DateTime(2024, 08, 29, 12, 00, 00), winnerNumber, fireBallNumber, now, false, "Erick", false);
            Assert.AreEqual(2, report.TotalTickets);
            Assert.AreEqual(2, report.TotalWinners);
            Assert.AreEqual(135M, report.TotalPrize);

            lottery.TurnOffFireBall(itIsThePresent, now, "N/A");
            DateTime drawDate = new DateTime(2024, 08, 29, 12, 00, 00);
            bool canBeConfirmed = lottery.CanBeConfirmed(drawDate);
            Assert.IsTrue(canBeConfirmed);

            drawDate = new DateTime(2024, 08, 29, 12, 00, 00);
            canBeConfirmed = lottery2.CanBeConfirmed(drawDate);
            Assert.IsTrue(canBeConfirmed);

            PicksDrawingsSummaryReport drawingsSummaryReport = (PicksDrawingsSummaryReport)lottery.ConfirmDraw(drawDate, now, "N/a", itIsThePresent, true);
            Assert.AreEqual(2, drawingsSummaryReport.TotalPlayers);
            Assert.AreEqual(2, drawingsSummaryReport.TotalWinners);
            Assert.AreEqual(2, drawingsSummaryReport.TotalTickets);
            Assert.AreEqual(2, drawingsSummaryReport.TotalTicketAmount);
            Assert.AreEqual(90M + 45M, drawingsSummaryReport.TotalPrize);
            Assert.IsFalse(drawingsSummaryReport.WithFireBall);

            PicksDrawingsSummaryReport drawingsSummaryReport2 = (PicksDrawingsSummaryReport)lottery2.ConfirmDraw(drawDate, now, "N/a", itIsThePresent, true);
            Assert.AreEqual(2, drawingsSummaryReport2.TotalPlayers);
            Assert.AreEqual(2, drawingsSummaryReport2.TotalWinners);
            Assert.AreEqual(2, drawingsSummaryReport2.TotalTickets);
            Assert.AreEqual(2, drawingsSummaryReport2.TotalTicketAmount);
            Assert.AreEqual(90M + 45M, drawingsSummaryReport2.TotalPrize);
            Assert.IsFalse(drawingsSummaryReport2.WithFireBall);
        }

        [TestMethod]
        public void T1_TurnOff_Test()
        {
            bool itIsThePresent = false;
            QueueInMemory queue = new QueueInMemory();
            Integration.Kafka = queue;

            DateTime now = new DateTime(2024, 08, 29, 00, 00, 00);
            Company company = new Company();
            company.Accounting = new RealAccounting();
            CurrenciesTest.AddCurrencies();
            company.Sales.CreateStore(1, "Test Store").MakeCurrent();

            Domain domain = company.Sales.CreateDomain(false, 1, "localhost", PaymentChannels.Agents.INSIDER);
            Player player = company.GetOrCreateCustomerById("*********").Player;
            Customer customer = company.CustomerByPlayer(player);
            customer.Identifier = "*********";

            var lotteries = company.Lotto900();
            ExcludeSubtickets excludedSubtickets = new ExcludeSubtickets();

            Product ticketPick2StraightProd = lotteries.GetOrCreateProductById(5);
            ticketPick2StraightProd.Description = "Ticket pick 2 straight";
            ticketPick2StraightProd.Price = 1;

            State s = lotteries.GetOrCreateState("GA", "Georgia");
            LotteryPick<Pick2> lottery = (LotteryPick<Pick2>)lotteries.GetOrCreateLottery(2, s);
            lottery.TurnOnFireBall(itIsThePresent, now, "N/A");
            lottery.ForEach(12, 00, "123456", now, "N/A");

            // Buy ticket Today
            string dates = "08/29/2024";
            string states = "GA";
            string hours = "12:00 PM";
            string withFireballs = "false";
            int orderNumber = company.IdentityOrderNumber;
            OrderCart myOrder = (OrderCart)company.NewOrder(customer, orderNumber, Currencies.CODES.USD);
            lotteries.NextDatesAccumulator.Calculate(domain, now, "2", dates, states, hours, withFireballs);
            myOrder = (OrderCart)company.CreateTicketOrderWithMultipleAuthorization(player, states, dates, withFireballs, "MultipleInputSingleAmount", 1, new List<string> { "12" }, "S", myOrder, ticketPick2StraightProd, lotteries.NextDatesAccumulator, domain);
            company.AddOrders(myOrder);
            int lowReference = company.IdentitytBetNumber;
            int highReference = (lowReference + myOrder.CountOfItems - 1);
            AuthorizationsNumbers auths = new AuthorizationsNumbers(*********, 2);
            company.PurchaseTickets(itIsThePresent, myOrder, now, auths, domain, lowReference, orderNumber, "Artemis_ThirdParty_Deposit_USD");

            // Buy ticket Tomorrow
            dates = "08/30/2024";
            states = "GA";
            hours = "12:00 PM";
            withFireballs = "true";
            orderNumber = company.IdentityOrderNumber;
            myOrder = (OrderCart)company.NewOrder(customer, orderNumber, Currencies.CODES.USD);
            lotteries.NextDatesAccumulator.Calculate(domain, now, "2", dates, states, hours, withFireballs);
            myOrder = (OrderCart)company.CreateTicketOrderWithMultipleAuthorization(player, states, dates, withFireballs, "MultipleInputSingleAmount", 1, new List<string> { "12" }, "S", myOrder, ticketPick2StraightProd, lotteries.NextDatesAccumulator, domain);
            company.AddOrders(myOrder);
            lowReference = company.IdentitytBetNumber;
            highReference = (lowReference + myOrder.CountOfItems - 1);
            auths = new AuthorizationsNumbers(*********, 2);
            company.PurchaseTickets(itIsThePresent, myOrder, now, auths, domain, lowReference, orderNumber, "Artemis_ThirdParty_Deposit_USD");

            // Turn off fireball
            lottery.TurnOffFireBall(itIsThePresent, now, "N/A");

            int fireBallNumber = 9;
            string winnerNumber = "12";
            var report = lottery.DrawPicks(new DateTime(2024, 08, 29, 12, 00, 00), winnerNumber, fireBallNumber, now, false, "Erick", false);
            Assert.AreEqual(1, report.TotalTickets);
            Assert.AreEqual(1, report.TotalWinners);
            Assert.AreEqual(90M, report.TotalPrize);

            winnerNumber = "12";
            report = lottery.DrawPicks(new DateTime(2024, 08, 30, 12, 00, 00), winnerNumber, fireBallNumber, now, false, "Erick", false);
            Assert.AreEqual(1, report.TotalTickets);
            Assert.AreEqual(1, report.TotalWinners);
            Assert.AreEqual(45.0M, report.TotalPrize);

            var schedule = lottery.Schedules.ElementAt(0);
            Assert.AreEqual(lottery.SequenceOfNumbersOfDrawAt(new DateTime(2024, 08, 29, 12, 00, 00), schedule), "12");
            var lotterywithFireball = lottery.FireBallLottery;
            Assert.AreEqual(lotterywithFireball.SequenceOfNumbersOfDrawAt(new DateTime(2024, 08, 29, 12, 00, 00), schedule), "12");
            Assert.AreEqual(lotterywithFireball.FireBallNumber(new DateTime(2024, 08, 29, 12, 00, 00), schedule), 9);
            Assert.IsTrue(lotterywithFireball.HasFireBallNumber(new DateTime(2024, 08, 29, 12, 00, 00), schedule));
        }

        [TestMethod]
        public void T2_TurnOff_Test()
        {
            bool itIsThePresent = false;
            QueueInMemory queue = new QueueInMemory();
            Integration.Kafka = queue;

            DateTime now = new DateTime(2024, 08, 29, 00, 00, 00);
            Company company = new Company();
            company.Accounting = new RealAccounting();
            CurrenciesTest.AddCurrencies();
            company.Sales.CreateStore(1, "Test Store").MakeCurrent();

            Domain domain = company.Sales.CreateDomain(false, 1, "localhost", PaymentChannels.Agents.INSIDER);
            Player player = company.GetOrCreateCustomerById("*********").Player;
            Customer customer = company.CustomerByPlayer(player);
            customer.Identifier = "*********";

            var lotteries = company.Lotto900();
            ExcludeSubtickets excludedSubtickets = new ExcludeSubtickets();

            Product ticketPick2StraightProd = lotteries.GetOrCreateProductById(5);
            ticketPick2StraightProd.Description = "Ticket pick 2 straight";
            ticketPick2StraightProd.Price = 1;

            State s = lotteries.GetOrCreateState("GA", "Georgia");
            LotteryPick<Pick2> lottery = (LotteryPick<Pick2>)lotteries.GetOrCreateLottery(2, s);
            lottery.TurnOnFireBall(itIsThePresent, now, "N/A");
            lottery.ForEach(12, 00, "123456", now, "N/A");

            // Buy ticket Today
            string dates = "08/29/2024";
            string states = "GA";
            string hours = "12:00 PM";
            string withFireballs = "false";
            int orderNumber = company.IdentityOrderNumber;
            OrderCart myOrder = (OrderCart)company.NewOrder(customer, orderNumber, Currencies.CODES.USD);
            lotteries.NextDatesAccumulator.Calculate(domain, now, "2", dates, states, hours, withFireballs);
            myOrder = (OrderCart)company.CreateTicketOrderWithMultipleAuthorization(player, states, dates, withFireballs, "MultipleInputSingleAmount", 1, new List<string> { "12" }, "S", myOrder, ticketPick2StraightProd, lotteries.NextDatesAccumulator, domain);
            company.AddOrders(myOrder);
            int lowReference = company.IdentitytBetNumber;
            int highReference = (lowReference + myOrder.CountOfItems - 1);
            AuthorizationsNumbers auths = new AuthorizationsNumbers(*********, 2);
            company.PurchaseTickets(itIsThePresent, myOrder, now, auths, domain, lowReference, orderNumber, "Artemis_ThirdParty_Deposit_USD");

            // Buy ticket Tomorrow
            dates = "08/30/2024";
            states = "GA";
            hours = "12:00 PM";
            withFireballs = "true";
            orderNumber = company.IdentityOrderNumber;
            myOrder = (OrderCart)company.NewOrder(customer, orderNumber, Currencies.CODES.USD);
            lotteries.NextDatesAccumulator.Calculate(domain, now, "2", dates, states, hours, withFireballs);
            myOrder = (OrderCart)company.CreateTicketOrderWithMultipleAuthorization(player, states, dates, withFireballs, "MultipleInputSingleAmount", 1, new List<string> { "12" }, "S", myOrder, ticketPick2StraightProd, lotteries.NextDatesAccumulator, domain);
            company.AddOrders(myOrder);
            lowReference = company.IdentitytBetNumber;
            highReference = (lowReference + myOrder.CountOfItems - 1);
            auths = new AuthorizationsNumbers(*********, 2);
            company.PurchaseTickets(itIsThePresent, myOrder, now, auths, domain, lowReference, orderNumber, "Artemis_ThirdParty_Deposit_USD");

            // Turn off fireball
            lottery.TurnOffFireBall(itIsThePresent, now, "N/A");

            int fireBallNumber = 9;
            string winnerNumber = "12";
            var report = lottery.DrawPicks(new DateTime(2024, 08, 29, 12, 00, 00), winnerNumber, fireBallNumber, now, false, "Erick", false);
            Assert.AreEqual(1, report.TotalTickets);
            Assert.AreEqual(1, report.TotalWinners);
            Assert.AreEqual(90M, report.TotalPrize);

            // Turn on fireball
            lottery.TurnOnFireBall(itIsThePresent, now, "N/A");

            // Buy ticket Tomorrow
            dates = "08/30/2024";
            states = "GA";
            hours = "12:00 PM";
            withFireballs = "true";
            orderNumber = company.IdentityOrderNumber;
            myOrder = (OrderCart)company.NewOrder(customer, orderNumber, Currencies.CODES.USD);
            lotteries.NextDatesAccumulator.Calculate(domain, now, "2", dates, states, hours, withFireballs);
            myOrder = (OrderCart)company.CreateTicketOrderWithMultipleAuthorization(player, states, dates, withFireballs, "MultipleInputSingleAmount", 1, new List<string> { "12" }, "S", myOrder, ticketPick2StraightProd, lotteries.NextDatesAccumulator, domain);
            company.AddOrders(myOrder);
            lowReference = company.IdentitytBetNumber;
            highReference = (lowReference + myOrder.CountOfItems - 1);
            auths = new AuthorizationsNumbers(*********, 2);
            company.PurchaseTickets(itIsThePresent, myOrder, now, auths, domain, lowReference, orderNumber, "Artemis_ThirdParty_Deposit_USD");

            winnerNumber = "12";
            report = lottery.DrawPicks(new DateTime(2024, 08, 30, 12, 00, 00), winnerNumber, fireBallNumber, now, false, "Erick", false);
            Assert.AreEqual(2, report.TotalTickets);
            Assert.AreEqual(2, report.TotalWinners);//? es el mismo winner pero se sumaron los dos tickets
            Assert.AreEqual(90M, report.TotalPrize);
        }

        [TestMethod]
        public void T3_TurnOff_Test()
        {
            bool itIsThePresent = false;
            QueueInMemory queue = new QueueInMemory();
            Integration.Kafka = queue;

            DateTime now = new DateTime(2024, 08, 29, 00, 00, 00);
            Company company = new Company();
            company.Accounting = new RealAccounting();
            CurrenciesTest.AddCurrencies();
            company.Sales.CreateStore(1, "Test Store").MakeCurrent();

            Domain domain = company.Sales.CreateDomain(false, 1, "localhost", PaymentChannels.Agents.INSIDER);
            Player player = company.GetOrCreateCustomerById("*********").Player;
            Customer customer = company.CustomerByPlayer(player);
            customer.Identifier = "*********";

            var lotteries = company.Lotto900();
            ExcludeSubtickets excludedSubtickets = new ExcludeSubtickets();

            Product ticketPick2StraightProd = lotteries.GetOrCreateProductById(5);
            ticketPick2StraightProd.Description = "Ticket pick 2 straight";
            ticketPick2StraightProd.Price = 1;

            State s = lotteries.GetOrCreateState("GA", "Georgia");
            LotteryPick<Pick2> lottery = (LotteryPick<Pick2>)lotteries.GetOrCreateLottery(2, s);
            lottery.TurnOnFireBall(itIsThePresent, now, "N/A");
            lottery.ForEach(12, 00, "123456", now, "N/A");

            // Buy ticket Today
            string dates = "08/29/2024";
            string states = "GA";
            string hours = "12:00 PM";
            string withFireballs = "false";
            int orderNumber = company.IdentityOrderNumber;
            OrderCart myOrder = (OrderCart)company.NewOrder(customer, orderNumber, Currencies.CODES.USD);
            lotteries.NextDatesAccumulator.Calculate(domain, now, "2", dates, states, hours, withFireballs);
            myOrder = (OrderCart)company.CreateTicketOrderWithMultipleAuthorization(player, states, dates, withFireballs, "MultipleInputSingleAmount", 1, new List<string> { "12" }, "S", myOrder, ticketPick2StraightProd, lotteries.NextDatesAccumulator, domain);
            company.AddOrders(myOrder);
            int lowReference = company.IdentitytBetNumber;
            int highReference = (lowReference + myOrder.CountOfItems - 1);
            AuthorizationsNumbers auths = new AuthorizationsNumbers(*********, 2);
            company.PurchaseTickets(itIsThePresent, myOrder, now, auths, domain, lowReference, orderNumber, "Artemis_ThirdParty_Deposit_USD");

            // Turn off fireball
            lottery.TurnOffFireBall(itIsThePresent, now, "N/A");

            // check list of draws to grade


            int fireBallNumber = 9;
            string winnerNumber = "12";
            var report = lottery.DrawPicks(new DateTime(2024, 08, 29, 12, 00, 00), winnerNumber, fireBallNumber, now, false, "Erick", false);
            Assert.AreEqual(1, report.TotalTickets);
            Assert.AreEqual(1, report.TotalWinners);
            Assert.AreEqual(90M, report.TotalPrize);

            // check list of draws to confirm
        }

        [TestMethod]
        public void NoAction_FB_Test()
        {
            bool itIsThePresent = false;
            QueueInMemory queue = new QueueInMemory();
            Integration.Kafka = queue;

            DateTime now = new DateTime(2024, 08, 29, 00, 00, 00);
            Company company = new Company();
            company.Accounting = new RealAccounting();
            CurrenciesTest.AddCurrencies();
            company.Sales.CreateStore(1, "Test Store").MakeCurrent();

            Domain domain = company.Sales.CreateDomain(false, 1, "localhost", PaymentChannels.Agents.INSIDER);
            Player player = company.GetOrCreateCustomerById("*********").Player;
            Customer customer = company.CustomerByPlayer(player);
            customer.Identifier = "*********";

            var lotteries = company.Lotto900();
            ExcludeSubtickets excludedSubtickets = new ExcludeSubtickets();

            Product ticketPick2StraightProd = lotteries.GetOrCreateProductById(5);
            ticketPick2StraightProd.Description = "Ticket pick 2 straight";
            ticketPick2StraightProd.Price = 1;

            State s = lotteries.GetOrCreateState("GA", "Georgia");
            LotteryPick<Pick2> lottery = (LotteryPick<Pick2>)lotteries.GetOrCreateLottery(2, s);
            lottery.TurnOnFireBall(itIsThePresent, now, "N/A");
            lottery.ForEach(12, 00, "123456", now, "N/A");

            // Buy ticket Today
            string dates = "08/29/2024";
            string states = "GA";
            string hours = "12:00 PM";
            string withFireballs = "false";
            int orderNumber = company.IdentityOrderNumber;
            OrderCart myOrder = (OrderCart)company.NewOrder(customer, orderNumber, Currencies.CODES.USD);
            lotteries.NextDatesAccumulator.Calculate(domain, now, "2", dates, states, hours, withFireballs);
            myOrder = (OrderCart)company.CreateTicketOrderWithMultipleAuthorization(player, states, dates, withFireballs, "MultipleInputSingleAmount", 1, new List<string> { "12" }, "S", myOrder, ticketPick2StraightProd, lotteries.NextDatesAccumulator, domain);
            company.AddOrders(myOrder);
            int lowReference = company.IdentitytBetNumber;
            int highReference = (lowReference + myOrder.CountOfItems - 1);
            AuthorizationsNumbers auths = new AuthorizationsNumbers(*********, 2);
            company.PurchaseTickets(itIsThePresent, myOrder, now, auths, domain, lowReference, orderNumber, "Artemis_ThirdParty_Deposit_USD");

            // Buy ticket Tomorrow
            dates = "08/29/2024";
            states = "GA";
            hours = "12:00 PM";
            withFireballs = "true";
            orderNumber = company.IdentityOrderNumber;
            myOrder = (OrderCart)company.NewOrder(customer, orderNumber, Currencies.CODES.USD);
            lotteries.NextDatesAccumulator.Calculate(domain, now, "2", dates, states, hours, withFireballs);
            myOrder = (OrderCart)company.CreateTicketOrderWithMultipleAuthorization(player, states, dates, withFireballs, "MultipleInputSingleAmount", 1, new List<string> { "12" }, "S", myOrder, ticketPick2StraightProd, lotteries.NextDatesAccumulator, domain);
            company.AddOrders(myOrder);
            lowReference = company.IdentitytBetNumber;
            highReference = (lowReference + myOrder.CountOfItems - 1);
            auths = new AuthorizationsNumbers(*********, 2);
            company.PurchaseTickets(itIsThePresent, myOrder, now, auths, domain, lowReference, orderNumber, "Artemis_ThirdParty_Deposit_USD");

            var gradeDate = new DateTime(2024, 08, 29, 12, 00, 00);
            var report = lottery.SetNoAction(itIsThePresent, gradeDate, now, "Game Admin");
            Assert.IsTrue(report.TotalTickets == 2);

            lottery.ConfirmDraw(gradeDate, now, "N/A", itIsThePresent);
        }

        [TestMethod]
        public void Regrade_T1_FB_Test()
        {
            bool itIsThePresent = false;
            QueueInMemory queue = new QueueInMemory();
            Integration.Kafka = queue;

            DateTime now = new DateTime(2024, 08, 29, 00, 00, 00);
            Company company = new Company();
            company.Accounting = new RealAccounting();
            CurrenciesTest.AddCurrencies();
            company.Sales.CreateStore(1, "Test Store").MakeCurrent();

            Domain domain = company.Sales.CreateDomain(false, 1, "localhost", PaymentChannels.Agents.INSIDER);
            Player player = company.GetOrCreateCustomerById("*********").Player;
            Customer customer = company.CustomerByPlayer(player);
            customer.Identifier = "*********";

            var lotteries = company.Lotto900();
            ExcludeSubtickets excludedSubtickets = new ExcludeSubtickets();

            Product ticketPick2StraightProd = lotteries.GetOrCreateProductById(5);
            ticketPick2StraightProd.Description = "Ticket pick 2 straight";
            ticketPick2StraightProd.Price = 1;

            State s = lotteries.GetOrCreateState("GA", "Georgia");
            LotteryPick<Pick2> lottery = (LotteryPick<Pick2>)lotteries.GetOrCreateLottery(2, s);
            lottery.TurnOnFireBall(itIsThePresent, now, "N/A");
            lottery.ForEach(12, 00, "123456", now, "N/A");

            // Buy ticket 1 winner 1 loser
            decimal ticketAmount = 1;
            string number = "12";
            string dates = "08/29/2024";
            string states = "GA";
            string hours = "12:00 PM";
            string withFireballs = "false";
            int orderNumber = company.IdentityOrderNumber;
            OrderCart myOrder = (OrderCart)company.NewOrder(customer, orderNumber, Currencies.CODES.USD);
            lotteries.NextDatesAccumulator.Calculate(domain, now, "2", dates, states, hours, withFireballs);
            myOrder = (OrderCart)company.CreateTicketOrderWithMultipleAuthorization(player, states, dates, withFireballs, "MultipleInputSingleAmount", ticketAmount, new List<string> { number }, "S", myOrder, ticketPick2StraightProd, lotteries.NextDatesAccumulator, domain);
            company.AddOrders(myOrder);
            int lowReference = company.IdentitytBetNumber;
            int highReference = (lowReference + myOrder.CountOfItems - 1);
            AuthorizationsNumbers auths = new AuthorizationsNumbers(*********, 2);
            company.PurchaseTickets(itIsThePresent, myOrder, now, auths, domain, lowReference, orderNumber, "Artemis_ThirdParty_Deposit_USD");
            ticketAmount = 1;
            number = "68";
            dates = "08/29/2024";
            states = "GA";
            hours = "12:00 PM";
            withFireballs = "false";
            orderNumber = company.IdentityOrderNumber;
            myOrder = (OrderCart)company.NewOrder(customer, orderNumber, Currencies.CODES.USD);
            lotteries.NextDatesAccumulator.Calculate(domain, now, "2", dates, states, hours, withFireballs);
            myOrder = (OrderCart)company.CreateTicketOrderWithMultipleAuthorization(player, states, dates, withFireballs, "MultipleInputSingleAmount", ticketAmount, new List<string> { number }, "S", myOrder, ticketPick2StraightProd, lotteries.NextDatesAccumulator, domain);
            company.AddOrders(myOrder);
            lowReference = company.IdentitytBetNumber;
            highReference = (lowReference + myOrder.CountOfItems - 1);
            auths = new AuthorizationsNumbers(*********, 2);
            company.PurchaseTickets(itIsThePresent, myOrder, now, auths, domain, lowReference, orderNumber, "Artemis_ThirdParty_Deposit_USD");

            // Buy ticket 1 winner FB 1 loser FB
            ticketAmount = 1;
            number = "12";
            dates = "08/29/2024";
            states = "GA";
            hours = "12:00 PM";
            withFireballs = "true";
            orderNumber = company.IdentityOrderNumber;
            myOrder = (OrderCart)company.NewOrder(customer, orderNumber, Currencies.CODES.USD);
            lotteries.NextDatesAccumulator.Calculate(domain, now, "2", dates, states, hours, withFireballs);
            myOrder = (OrderCart)company.CreateTicketOrderWithMultipleAuthorization(player, states, dates, withFireballs, "MultipleInputSingleAmount", ticketAmount, new List<string> { number }, "S", myOrder, ticketPick2StraightProd, lotteries.NextDatesAccumulator, domain);
            company.AddOrders(myOrder);
            lowReference = company.IdentitytBetNumber;
            highReference = (lowReference + myOrder.CountOfItems - 1);
            auths = new AuthorizationsNumbers(*********, 2);
            company.PurchaseTickets(itIsThePresent, myOrder, now, auths, domain, lowReference, orderNumber, "Artemis_ThirdParty_Deposit_USD");
            ticketAmount = 1;
            number = "98";
            dates = "08/29/2024";
            states = "GA";
            hours = "12:00 PM";
            withFireballs = "true";
            orderNumber = company.IdentityOrderNumber;
            myOrder = (OrderCart)company.NewOrder(customer, orderNumber, Currencies.CODES.USD);
            lotteries.NextDatesAccumulator.Calculate(domain, now, "2", dates, states, hours, withFireballs);
            myOrder = (OrderCart)company.CreateTicketOrderWithMultipleAuthorization(player, states, dates, withFireballs, "MultipleInputSingleAmount", ticketAmount, new List<string> { number }, "S", myOrder, ticketPick2StraightProd, lotteries.NextDatesAccumulator, domain);
            company.AddOrders(myOrder);
            lowReference = company.IdentitytBetNumber;
            highReference = (lowReference + myOrder.CountOfItems - 1);
            auths = new AuthorizationsNumbers(*********, 2);
            company.PurchaseTickets(itIsThePresent, myOrder, now, auths, domain, lowReference, orderNumber, "Artemis_ThirdParty_Deposit_USD");

            // Gradear
            DateTime drawDate = new DateTime(2024, 08, 29, 12, 00, 00);
            int fireBallNumber = 0;
            string winnerNumber = "12";
            var report = lottery.DrawPicks(drawDate, winnerNumber, fireBallNumber, now, false, "Erick", false);
            Assert.AreEqual(4, report.TotalTickets);
            Assert.AreEqual(2, report.TotalWinners);
            Assert.AreEqual(135M, report.TotalPrize);

            // Confirmar
            DateTime gradeDate = new DateTime(2024, 08, 29, 12, 00, 00);
            lottery.ConfirmDraw(gradeDate, now, "N/A", itIsThePresent);

            // Regradear
            gradeDate = new DateTime(2024, 08, 29, 12, 00, 00);
            report = lottery.Regrade(itIsThePresent, gradeDate, now, "N/A");
            Assert.AreEqual(4, report.TotalTickets);
            Assert.AreEqual(0, report.TotalWinners);
            Assert.AreEqual(0, report.TotalPrize);

            // Confirmar
            gradeDate = new DateTime(2024, 08, 29, 12, 00, 00);
            lottery.ConfirmDraw(gradeDate, now, "N/A", itIsThePresent);

            // Gradear
            drawDate = new DateTime(2024, 08, 29, 12, 00, 00);
            fireBallNumber = 9;
            winnerNumber = "68";
            report = lottery.DrawPicks(drawDate, winnerNumber, fireBallNumber, now, false, "Erick", false);
            Assert.AreEqual(4, report.TotalTickets);
            Assert.AreEqual(2, report.TotalWinners);
            Assert.AreEqual(112.5M, report.TotalPrize);

            // Confirmar
            gradeDate = new DateTime(2024, 08, 29, 12, 00, 00);
            lottery.ConfirmDraw(gradeDate, now, "N/A", itIsThePresent);
        }

        [TestMethod]
        public void Regrade_T2_FB_Test()
        {
            bool itIsThePresent = false;
            QueueInMemory queue = new QueueInMemory();
            Integration.Kafka = queue;

            DateTime now = new DateTime(2024, 08, 29, 00, 00, 00);
            Company company = new Company();
            company.Accounting = new RealAccounting();
            CurrenciesTest.AddCurrencies();
            company.Sales.CreateStore(1, "Test Store").MakeCurrent();

            Domain domain = company.Sales.CreateDomain(false, 1, "localhost", PaymentChannels.Agents.INSIDER);
            Player player = company.GetOrCreateCustomerById("*********").Player;
            Customer customer = company.CustomerByPlayer(player);
            customer.Identifier = "*********";

            var lotteries = company.Lotto900();
            ExcludeSubtickets excludedSubtickets = new ExcludeSubtickets();

            Product ticketPick2StraightProd = lotteries.GetOrCreateProductById(5);
            ticketPick2StraightProd.Description = "Ticket pick 2 straight";
            ticketPick2StraightProd.Price = 1;

            State s = lotteries.GetOrCreateState("GA", "Georgia");
            LotteryPick<Pick2> lottery = (LotteryPick<Pick2>)lotteries.GetOrCreateLottery(2, s);
            lottery.TurnOnFireBall(itIsThePresent, now, "N/A");
            lottery.ForEach(12, 00, "123456", now, "N/A");

            decimal ticketAmount = 1;
            string number = "12";
            string dates = "08/29/2024";
            string states = "GA";
            string hours = "12:00 PM";
            string withFireballs = "false";
            int orderNumber = company.IdentityOrderNumber;
            OrderCart myOrder = (OrderCart)company.NewOrder(customer, orderNumber, Currencies.CODES.USD);
            lotteries.NextDatesAccumulator.Calculate(domain, now, "2", dates, states, hours, withFireballs);
            myOrder = (OrderCart)company.CreateTicketOrderWithMultipleAuthorization(player, states, dates, withFireballs, "MultipleInputSingleAmount", ticketAmount, new List<string> { number }, "S", myOrder, ticketPick2StraightProd, lotteries.NextDatesAccumulator, domain);
            company.AddOrders(myOrder);
            int lowReference = company.IdentitytBetNumber;
            int highReference = (lowReference + myOrder.CountOfItems - 1);
            AuthorizationsNumbers auths = new AuthorizationsNumbers(*********, 2);
            company.PurchaseTickets(itIsThePresent, myOrder, now, auths, domain, lowReference, orderNumber, "Artemis_ThirdParty_Deposit_USD");
            ticketAmount = 1;
            number = "98";
            dates = "08/29/2024";
            states = "GA";
            hours = "12:00 PM";
            withFireballs = "false";
            orderNumber = company.IdentityOrderNumber;
            myOrder = (OrderCart)company.NewOrder(customer, orderNumber, Currencies.CODES.USD);
            lotteries.NextDatesAccumulator.Calculate(domain, now, "2", dates, states, hours, withFireballs);
            myOrder = (OrderCart)company.CreateTicketOrderWithMultipleAuthorization(player, states, dates, withFireballs, "MultipleInputSingleAmount", ticketAmount, new List<string> { number }, "S", myOrder, ticketPick2StraightProd, lotteries.NextDatesAccumulator, domain);
            company.AddOrders(myOrder);
            lowReference = company.IdentitytBetNumber;
            highReference = (lowReference + myOrder.CountOfItems - 1);
            auths = new AuthorizationsNumbers(*********, 2);
            company.PurchaseTickets(itIsThePresent, myOrder, now, auths, domain, lowReference, orderNumber, "Artemis_ThirdParty_Deposit_USD");

            ticketAmount = 1;
            number = "18";
            dates = "08/29/2024";
            states = "GA";
            hours = "12:00 PM";
            withFireballs = "true";
            orderNumber = company.IdentityOrderNumber;
            myOrder = (OrderCart)company.NewOrder(customer, orderNumber, Currencies.CODES.USD);
            lotteries.NextDatesAccumulator.Calculate(domain, now, "2", dates, states, hours, withFireballs);
            myOrder = (OrderCart)company.CreateTicketOrderWithMultipleAuthorization(player, states, dates, withFireballs, "MultipleInputSingleAmount", ticketAmount, new List<string> { number }, "S", myOrder, ticketPick2StraightProd, lotteries.NextDatesAccumulator, domain);
            company.AddOrders(myOrder);
            lowReference = company.IdentitytBetNumber;
            highReference = (lowReference + myOrder.CountOfItems - 1);
            auths = new AuthorizationsNumbers(*********, 2);
            company.PurchaseTickets(itIsThePresent, myOrder, now, auths, domain, lowReference, orderNumber, "Artemis_ThirdParty_Deposit_USD");
            ticketAmount = 1;
            number = "98";
            dates = "08/29/2024";
            states = "GA";
            hours = "12:00 PM";
            withFireballs = "true";
            orderNumber = company.IdentityOrderNumber;
            myOrder = (OrderCart)company.NewOrder(customer, orderNumber, Currencies.CODES.USD);
            lotteries.NextDatesAccumulator.Calculate(domain, now, "2", dates, states, hours, withFireballs);
            myOrder = (OrderCart)company.CreateTicketOrderWithMultipleAuthorization(player, states, dates, withFireballs, "MultipleInputSingleAmount", ticketAmount, new List<string> { number }, "S", myOrder, ticketPick2StraightProd, lotteries.NextDatesAccumulator, domain);
            company.AddOrders(myOrder);
            lowReference = company.IdentitytBetNumber;
            highReference = (lowReference + myOrder.CountOfItems - 1);
            auths = new AuthorizationsNumbers(*********, 2);
            company.PurchaseTickets(itIsThePresent, myOrder, now, auths, domain, lowReference, orderNumber, "Artemis_ThirdParty_Deposit_USD");

            // Gradear
            DateTime drawDate = new DateTime(2024, 08, 29, 12, 00, 00);
            int fireBallNumber = 0;
            string winnerNumber = "12";
            var report = lottery.DrawPicks(drawDate, winnerNumber, fireBallNumber, now, false, "Erick", false);
            Assert.AreEqual(4, report.TotalTickets);
            Assert.AreEqual(1, report.TotalWinners);
            Assert.AreEqual(90M, report.TotalPrize);

            // Regradear
            DateTime gradeDate = new DateTime(2024, 08, 29, 12, 00, 00);
            report = lottery.Regrade(itIsThePresent, gradeDate, now, "N/A");
            Assert.AreEqual(4, report.TotalTickets);
            Assert.AreEqual(0, report.TotalWinners);
            Assert.AreEqual(0, report.TotalPrize);

            // Gradear
            drawDate = new DateTime(2024, 08, 29, 12, 00, 00);
            fireBallNumber = 9;
            winnerNumber = "18";
            report = lottery.DrawPicks(drawDate, winnerNumber, fireBallNumber, now, false, "Erick", false);
            Assert.AreEqual(4, report.TotalTickets);
            Assert.AreEqual(2, report.TotalWinners);
            Assert.AreEqual(67.5M, report.TotalPrize);

            // Confirmar
            gradeDate = new DateTime(2024, 08, 29, 12, 00, 00);
            lottery.ConfirmDraw(gradeDate, now, "N/A", itIsThePresent);
        }

        [TestMethod]
        public void Regrade_T3_FB_Test()
        {
            bool itIsThePresent = false;
            QueueInMemory queue = new QueueInMemory();
            Integration.Kafka = queue;

            DateTime now = new DateTime(2024, 08, 29, 00, 00, 00);
            Company company = new Company();
            company.Accounting = new RealAccounting();
            CurrenciesTest.AddCurrencies();
            company.Sales.CreateStore(1, "Test Store").MakeCurrent();

            Domain domain = company.Sales.CreateDomain(false, 1, "localhost", PaymentChannels.Agents.INSIDER);
            Player player = company.GetOrCreateCustomerById("*********").Player;
            Customer customer = company.CustomerByPlayer(player);
            customer.Identifier = "*********";

            var lotteries = company.Lotto900();
            ExcludeSubtickets excludedSubtickets = new ExcludeSubtickets();

            Product ticketPick2StraightProd = lotteries.GetOrCreateProductById(5);
            ticketPick2StraightProd.Description = "Ticket pick 2 straight";
            ticketPick2StraightProd.Price = 1;

            State s = lotteries.GetOrCreateState("GA", "Georgia");
            LotteryPick<Pick2> lottery = (LotteryPick<Pick2>)lotteries.GetOrCreateLottery(2, s);
            lottery.TurnOnFireBall(itIsThePresent, now, "N/A");
            lottery.ForEach(12, 00, "123456", now, "N/A");

            decimal ticketAmount = 1;
            string number = "12";
            string dates = "08/29/2024";
            string states = "GA";
            string hours = "12:00 PM";
            string withFireballs = "false";
            int orderNumber = company.IdentityOrderNumber;
            OrderCart myOrder = (OrderCart)company.NewOrder(customer, orderNumber, Currencies.CODES.USD);
            lotteries.NextDatesAccumulator.Calculate(domain, now, "2", dates, states, hours, withFireballs);
            myOrder = (OrderCart)company.CreateTicketOrderWithMultipleAuthorization(player, states, dates, withFireballs, "MultipleInputSingleAmount", ticketAmount, new List<string> { number }, "S", myOrder, ticketPick2StraightProd, lotteries.NextDatesAccumulator, domain);
            company.AddOrders(myOrder);
            int lowReference = company.IdentitytBetNumber;
            int highReference = (lowReference + myOrder.CountOfItems - 1);
            AuthorizationsNumbers auths = new AuthorizationsNumbers(*********, 2);
            company.PurchaseTickets(itIsThePresent, myOrder, now, auths, domain, lowReference, orderNumber, "Artemis_ThirdParty_Deposit_USD");
            ticketAmount = 1;
            number = "98";
            dates = "08/29/2024";
            states = "GA";
            hours = "12:00 PM";
            withFireballs = "false";
            orderNumber = company.IdentityOrderNumber;
            myOrder = (OrderCart)company.NewOrder(customer, orderNumber, Currencies.CODES.USD);
            lotteries.NextDatesAccumulator.Calculate(domain, now, "2", dates, states, hours, withFireballs);
            myOrder = (OrderCart)company.CreateTicketOrderWithMultipleAuthorization(player, states, dates, withFireballs, "MultipleInputSingleAmount", ticketAmount, new List<string> { number }, "S", myOrder, ticketPick2StraightProd, lotteries.NextDatesAccumulator, domain);
            company.AddOrders(myOrder);
            lowReference = company.IdentitytBetNumber;
            highReference = (lowReference + myOrder.CountOfItems - 1);
            auths = new AuthorizationsNumbers(*********, 2);
            company.PurchaseTickets(itIsThePresent, myOrder, now, auths, domain, lowReference, orderNumber, "Artemis_ThirdParty_Deposit_USD");

            ticketAmount = 1;
            number = "18";
            dates = "08/29/2024";
            states = "GA";
            hours = "12:00 PM";
            withFireballs = "true";
            orderNumber = company.IdentityOrderNumber;
            myOrder = (OrderCart)company.NewOrder(customer, orderNumber, Currencies.CODES.USD);
            lotteries.NextDatesAccumulator.Calculate(domain, now, "2", dates, states, hours, withFireballs);
            myOrder = (OrderCart)company.CreateTicketOrderWithMultipleAuthorization(player, states, dates, withFireballs, "MultipleInputSingleAmount", ticketAmount, new List<string> { number }, "S", myOrder, ticketPick2StraightProd, lotteries.NextDatesAccumulator, domain);
            company.AddOrders(myOrder);
            lowReference = company.IdentitytBetNumber;
            highReference = (lowReference + myOrder.CountOfItems - 1);
            auths = new AuthorizationsNumbers(*********, 2);
            company.PurchaseTickets(itIsThePresent, myOrder, now, auths, domain, lowReference, orderNumber, "Artemis_ThirdParty_Deposit_USD");
            ticketAmount = 1;
            number = "98";
            dates = "08/29/2024";
            states = "GA";
            hours = "12:00 PM";
            withFireballs = "true";
            orderNumber = company.IdentityOrderNumber;
            myOrder = (OrderCart)company.NewOrder(customer, orderNumber, Currencies.CODES.USD);
            lotteries.NextDatesAccumulator.Calculate(domain, now, "2", dates, states, hours, withFireballs);
            myOrder = (OrderCart)company.CreateTicketOrderWithMultipleAuthorization(player, states, dates, withFireballs, "MultipleInputSingleAmount", ticketAmount, new List<string> { number }, "S", myOrder, ticketPick2StraightProd, lotteries.NextDatesAccumulator, domain);
            company.AddOrders(myOrder);
            lowReference = company.IdentitytBetNumber;
            highReference = (lowReference + myOrder.CountOfItems - 1);
            auths = new AuthorizationsNumbers(*********, 2);
            company.PurchaseTickets(itIsThePresent, myOrder, now, auths, domain, lowReference, orderNumber, "Artemis_ThirdParty_Deposit_USD");

            // Gradear
            DateTime drawDate = new DateTime(2024, 08, 29, 12, 00, 00);
            int fireBallNumber = 0;
            string winnerNumber = "12";
            var report = lottery.DrawPicks(drawDate, winnerNumber, fireBallNumber, now, false, "Erick", false);
            Assert.AreEqual(4, report.TotalTickets);
            Assert.AreEqual(1, report.TotalWinners);
            Assert.AreEqual(90M, report.TotalPrize);

            DateTime gradeDate = new DateTime(2024, 08, 29, 12, 00, 00);
            lottery.ConfirmDraw(gradeDate, now, "N/A", itIsThePresent);

            // Regradear
            gradeDate = new DateTime(2024, 08, 29, 12, 00, 00);
            report = lottery.Regrade(itIsThePresent, gradeDate, now, "N/A");
            Assert.AreEqual(4, report.TotalTickets);
            Assert.AreEqual(0, report.TotalWinners);
            Assert.AreEqual(0, report.TotalPrize);

            // Gradear
            drawDate = new DateTime(2024, 08, 29, 12, 00, 00);
            fireBallNumber = 5;
            winnerNumber = "18";
            report = lottery.DrawPicks(drawDate, winnerNumber, fireBallNumber, now, false, "Erick", false);
            Assert.AreEqual(4, report.TotalTickets);
            Assert.AreEqual(1, report.TotalWinners);
            Assert.AreEqual(45M, report.TotalPrize);
        }

        [TestMethod]
        public void T4_Expire_FireBallNumber_Sequence_Test()
        {
            bool itIsThePresent = false;
            QueueInMemory queue = new QueueInMemory();
            Integration.Kafka = queue;

            DateTime now = new DateTime(2024, 08, 29, 00, 00, 00);
            Company company = new Company();
            company.Accounting = new RealAccounting();
            CurrenciesTest.AddCurrencies();
            company.Sales.CreateStore(1, "Test Store").MakeCurrent();

            Domain domain = company.Sales.CreateDomain(false, 1, "localhost", PaymentChannels.Agents.INSIDER);
            Player player = company.GetOrCreateCustomerById("*********").Player;
            Customer customer = company.CustomerByPlayer(player);
            customer.Identifier = "*********";

            var lotteries = company.Lotto900();
            ExcludeSubtickets excludedSubtickets = new ExcludeSubtickets();

            Product ticketPick2StraightProd = lotteries.GetOrCreateProductById(5);
            ticketPick2StraightProd.Description = "Ticket pick 2 straight";
            ticketPick2StraightProd.Price = 1;

            State s = lotteries.GetOrCreateState("GA", "Georgia");
            LotteryPick<Pick2> lottery = (LotteryPick<Pick2>)lotteries.GetOrCreateLottery(2, s);
            lottery.TurnOnFireBall(itIsThePresent, now, "N/A");
            lottery.ForEach(12, 00, "123456", now, "N/A");

            // Buy ticket 1 winner 1 loser
            decimal ticketAmount = 1;
            string number = "12";
            string dates = "08/29/2024";
            string states = "GA";
            string hours = "12:00 PM";
            string withFireballs = "false";
            int orderNumber = company.IdentityOrderNumber;
            OrderCart myOrder = (OrderCart)company.NewOrder(customer, orderNumber, Currencies.CODES.USD);
            lotteries.NextDatesAccumulator.Calculate(domain, now, "2", dates, states, hours, withFireballs);
            myOrder = (OrderCart)company.CreateTicketOrderWithMultipleAuthorization(player, states, dates, withFireballs, "MultipleInputSingleAmount", ticketAmount, new List<string> { number }, "S", myOrder, ticketPick2StraightProd, lotteries.NextDatesAccumulator, domain);
            company.AddOrders(myOrder);
            int lowReference = company.IdentitytBetNumber;
            int highReference = (lowReference + myOrder.CountOfItems - 1);
            AuthorizationsNumbers auths = new AuthorizationsNumbers(*********, 2);
            company.PurchaseTickets(itIsThePresent, myOrder, now, auths, domain, lowReference, orderNumber, "Artemis_ThirdParty_Deposit_USD");
            ticketAmount = 1;
            number = "98";
            dates = "08/29/2024";
            states = "GA";
            hours = "12:00 PM";
            withFireballs = "false";
            orderNumber = company.IdentityOrderNumber;
            myOrder = (OrderCart)company.NewOrder(customer, orderNumber, Currencies.CODES.USD);
            lotteries.NextDatesAccumulator.Calculate(domain, now, "2", dates, states, hours, withFireballs);
            myOrder = (OrderCart)company.CreateTicketOrderWithMultipleAuthorization(player, states, dates, withFireballs, "MultipleInputSingleAmount", ticketAmount, new List<string> { number }, "S", myOrder, ticketPick2StraightProd, lotteries.NextDatesAccumulator, domain);
            company.AddOrders(myOrder);
            lowReference = company.IdentitytBetNumber;
            highReference = (lowReference + myOrder.CountOfItems - 1);
            auths = new AuthorizationsNumbers(*********, 2);
            company.PurchaseTickets(itIsThePresent, myOrder, now, auths, domain, lowReference, orderNumber, "Artemis_ThirdParty_Deposit_USD");

            // Buy ticket 1 winner FB 1 loser FB
            ticketAmount = 1;
            number = "18";
            dates = "08/29/2024";
            states = "GA";
            hours = "12:00 PM";
            withFireballs = "true";
            orderNumber = company.IdentityOrderNumber;
            myOrder = (OrderCart)company.NewOrder(customer, orderNumber, Currencies.CODES.USD);
            lotteries.NextDatesAccumulator.Calculate(domain, now, "2", dates, states, hours, withFireballs);
            myOrder = (OrderCart)company.CreateTicketOrderWithMultipleAuthorization(player, states, dates, withFireballs, "MultipleInputSingleAmount", ticketAmount, new List<string> { number }, "S", myOrder, ticketPick2StraightProd, lotteries.NextDatesAccumulator, domain);
            company.AddOrders(myOrder);
            lowReference = company.IdentitytBetNumber;
            highReference = (lowReference + myOrder.CountOfItems - 1);
            auths = new AuthorizationsNumbers(*********, 2);
            company.PurchaseTickets(itIsThePresent, myOrder, now, auths, domain, lowReference, orderNumber, "Artemis_ThirdParty_Deposit_USD");
            ticketAmount = 1;
            number = "98";
            dates = "08/29/2024";
            states = "GA";
            hours = "12:00 PM";
            withFireballs = "true";
            orderNumber = company.IdentityOrderNumber;
            myOrder = (OrderCart)company.NewOrder(customer, orderNumber, Currencies.CODES.USD);
            lotteries.NextDatesAccumulator.Calculate(domain, now, "2", dates, states, hours, withFireballs);
            myOrder = (OrderCart)company.CreateTicketOrderWithMultipleAuthorization(player, states, dates, withFireballs, "MultipleInputSingleAmount", ticketAmount, new List<string> { number }, "S", myOrder, ticketPick2StraightProd, lotteries.NextDatesAccumulator, domain);
            company.AddOrders(myOrder);
            lowReference = company.IdentitytBetNumber;
            highReference = (lowReference + myOrder.CountOfItems - 1);
            auths = new AuthorizationsNumbers(*********, 2);
            company.PurchaseTickets(itIsThePresent, myOrder, now, auths, domain, lowReference, orderNumber, "Artemis_ThirdParty_Deposit_USD");

            // Gradear
            DateTime drawDate = new DateTime(2024, 08, 29, 12, 00, 00);
            int fireBallNumber = 8;
            string winnerNumber = "12";
            var report = lottery.DrawPicks(drawDate, winnerNumber, fireBallNumber, now, false, "Erick", false);
            Assert.AreEqual(4, report.TotalTickets);
            Assert.AreEqual(2, report.TotalWinners);
            Assert.AreEqual(112.5M, report.TotalPrize);

            // Confirmar
            DateTime gradeDate = new DateTime(2024, 08, 29, 12, 00, 00);
            lottery.ConfirmDraw(gradeDate, now, "N/A", itIsThePresent);

            // Regradear
            gradeDate = new DateTime(2024, 08, 29, 12, 00, 00);
            report = lottery.Regrade(itIsThePresent, gradeDate, now, "N/A");
            Assert.AreEqual(4, report.TotalTickets);
            Assert.AreEqual(0, report.TotalWinners);
            Assert.AreEqual(0, report.TotalPrize);

            // Gradear
            drawDate = new DateTime(2024, 08, 29, 12, 00, 00);
            fireBallNumber = 3;
            winnerNumber = "98";
            report = lottery.DrawPicks(drawDate, winnerNumber, fireBallNumber, now, false, "Erick", false);
            Assert.AreEqual(4, report.TotalTickets);
            Assert.AreEqual(2, report.TotalWinners);
            Assert.AreEqual(135M, report.TotalPrize);
        }

        [TestMethod]
        public void T4_DeleteExpiredTickets_FB_Test()
        {
            bool itIsThePresent = false;
            QueueInMemory queue = new QueueInMemory();
            Integration.Kafka = queue;

            DateTime now = new DateTime(2023, 08, 29, 00, 00, 00);
            Company company = new Company();
            company.Accounting = new RealAccounting();
            CurrenciesTest.AddCurrencies();
            company.Sales.CreateStore(1, "Test Store").MakeCurrent();

            Domain domain = company.Sales.CreateDomain(false, 1, "localhost", PaymentChannels.Agents.INSIDER);
            Player player = company.GetOrCreateCustomerById("*********").Player;
            Customer customer = company.CustomerByPlayer(player);
            customer.Identifier = "*********";

            var lotteries = company.Lotto900();
            ExcludeSubtickets excludedSubtickets = new ExcludeSubtickets();

            Product ticketPick2StraightProd = lotteries.GetOrCreateProductById(5);
            ticketPick2StraightProd.Description = "Ticket pick 2 straight";
            ticketPick2StraightProd.Price = 1;

            State s = lotteries.GetOrCreateState("GA", "Georgia");
            LotteryPick<Pick2> lottery = (LotteryPick<Pick2>)lotteries.GetOrCreateLottery(2, s);
            lottery.TurnOnFireBall(itIsThePresent, now, "N/A");
            lottery.ForEach(12, 00, "123456", now, "N/A");

            decimal ticketAmount = 1;
            string number = "12";
            string dates = "08/29/2023";
            string states = "GA";
            string hours = "12:00 PM";
            string withFireballs = "true";
            int orderNumber = company.IdentityOrderNumber;
            OrderCart myOrder = (OrderCart)company.NewOrder(customer, orderNumber, Currencies.CODES.USD);
            lotteries.NextDatesAccumulator.Calculate(domain, now, "2", dates, states, hours, withFireballs);
            myOrder = (OrderCart)company.CreateTicketOrderWithMultipleAuthorization(player, states, dates, withFireballs, "MultipleInputSingleAmount", ticketAmount, new List<string> { number }, "S", myOrder, ticketPick2StraightProd, lotteries.NextDatesAccumulator, domain);
            company.AddOrders(myOrder);
            int lowReference = company.IdentitytBetNumber;
            int highReference = (lowReference + myOrder.CountOfItems - 1);
            AuthorizationsNumbers auths = new AuthorizationsNumbers(*********, 2);
            company.PurchaseTickets(itIsThePresent, myOrder, now, auths, domain, lowReference, orderNumber, "Artemis_ThirdParty_Deposit_USD");
            ticketAmount = 1;
            number = "98";
            dates = "08/29/2023";
            states = "GA";
            hours = "12:00 PM";
            withFireballs = "true";
            orderNumber = company.IdentityOrderNumber;
            myOrder = (OrderCart)company.NewOrder(customer, orderNumber, Currencies.CODES.USD);
            lotteries.NextDatesAccumulator.Calculate(domain, now, "2", dates, states, hours, withFireballs);
            myOrder = (OrderCart)company.CreateTicketOrderWithMultipleAuthorization(player, states, dates, withFireballs, "MultipleInputSingleAmount", ticketAmount, new List<string> { number }, "S", myOrder, ticketPick2StraightProd, lotteries.NextDatesAccumulator, domain);
            company.AddOrders(myOrder);
            lowReference = company.IdentitytBetNumber;
            highReference = (lowReference + myOrder.CountOfItems - 1);
            auths = new AuthorizationsNumbers(*********, 2);
            company.PurchaseTickets(itIsThePresent, myOrder, now, auths, domain, lowReference, orderNumber, "Artemis_ThirdParty_Deposit_USD");
            
            ticketAmount = 1;
            number = "12";
            dates = "08/29/2023";
            states = "GA";
            hours = "12:00 PM";
            withFireballs = "false";
            orderNumber = company.IdentityOrderNumber;
            myOrder = (OrderCart)company.NewOrder(customer, orderNumber, Currencies.CODES.USD);
            lotteries.NextDatesAccumulator.Calculate(domain, now, "2", dates, states, hours, withFireballs);
            myOrder = (OrderCart)company.CreateTicketOrderWithMultipleAuthorization(player, states, dates, withFireballs, "MultipleInputSingleAmount", ticketAmount, new List<string> { number }, "S", myOrder, ticketPick2StraightProd, lotteries.NextDatesAccumulator, domain);
            company.AddOrders(myOrder);
            lowReference = company.IdentitytBetNumber;
            highReference = (lowReference + myOrder.CountOfItems - 1);
            auths = new AuthorizationsNumbers(*********, 2);
            company.PurchaseTickets(itIsThePresent, myOrder, now, auths, domain, lowReference, orderNumber, "Artemis_ThirdParty_Deposit_USD");

            now = new DateTime(2024, 08, 29, 00, 00, 00);

            ticketAmount = 1;
            number = "12";
            dates = "08/29/2024";
            states = "GA";
            hours = "12:00 PM";
            withFireballs = "true";
            orderNumber = company.IdentityOrderNumber;
            myOrder = (OrderCart)company.NewOrder(customer, orderNumber, Currencies.CODES.USD);
            lotteries.NextDatesAccumulator.Calculate(domain, now, "2", dates, states, hours, withFireballs);
            myOrder = (OrderCart)company.CreateTicketOrderWithMultipleAuthorization(player, states, dates, withFireballs, "MultipleInputSingleAmount", ticketAmount, new List<string> { number }, "S", myOrder, ticketPick2StraightProd, lotteries.NextDatesAccumulator, domain);
            company.AddOrders(myOrder);
            lowReference = company.IdentitytBetNumber;
            highReference = (lowReference + myOrder.CountOfItems - 1);
            auths = new AuthorizationsNumbers(*********, 2);
            company.PurchaseTickets(itIsThePresent, myOrder, now, auths, domain, lowReference, orderNumber, "Artemis_ThirdParty_Deposit_USD");
            ticketAmount = 1;
            number = "98";
            dates = "08/29/2024";
            states = "GA";
            hours = "12:00 PM";
            withFireballs = "true";
            orderNumber = company.IdentityOrderNumber;
            myOrder = (OrderCart)company.NewOrder(customer, orderNumber, Currencies.CODES.USD);
            lotteries.NextDatesAccumulator.Calculate(domain, now, "2", dates, states, hours, withFireballs);
            myOrder = (OrderCart)company.CreateTicketOrderWithMultipleAuthorization(player, states, dates, withFireballs, "MultipleInputSingleAmount", ticketAmount, new List<string> { number }, "S", myOrder, ticketPick2StraightProd, lotteries.NextDatesAccumulator, domain);
            company.AddOrders(myOrder);
            lowReference = company.IdentitytBetNumber;
            highReference = (lowReference + myOrder.CountOfItems - 1);
            auths = new AuthorizationsNumbers(*********, 2);
            company.PurchaseTickets(itIsThePresent, myOrder, now, auths, domain, lowReference, orderNumber, "Artemis_ThirdParty_Deposit_USD");
            ticketAmount = 1;
            number = "77";
            dates = "08/29/2024";
            states = "GA";
            hours = "12:00 PM";
            withFireballs = "false";
            orderNumber = company.IdentityOrderNumber;
            myOrder = (OrderCart)company.NewOrder(customer, orderNumber, Currencies.CODES.USD);
            lotteries.NextDatesAccumulator.Calculate(domain, now, "2", dates, states, hours, withFireballs);
            myOrder = (OrderCart)company.CreateTicketOrderWithMultipleAuthorization(player, states, dates, withFireballs, "MultipleInputSingleAmount", ticketAmount, new List<string> { number }, "S", myOrder, ticketPick2StraightProd, lotteries.NextDatesAccumulator, domain);
            company.AddOrders(myOrder);
            lowReference = company.IdentitytBetNumber;
            highReference = (lowReference + myOrder.CountOfItems - 1);
            auths = new AuthorizationsNumbers(*********, 2);
            company.PurchaseTickets(itIsThePresent, myOrder, now, auths, domain, lowReference, orderNumber, "Artemis_ThirdParty_Deposit_USD");

            // Gradear
            DateTime drawDate = new DateTime(2023, 08, 29, 12, 00, 00);
            int fireBallNumber = 0;
            string winnerNumber = "12";
            var report = lottery.DrawPicks(drawDate, winnerNumber, fireBallNumber, now, false, "Erick", false);
            Assert.AreEqual(3, report.TotalTickets);
            Assert.AreEqual(2, report.TotalWinners);
            Assert.AreEqual(45M + 90M, report.TotalPrize);

            DateTime exactDate = new DateTime(2023, 08, 29, 12, 00, 00);
            int totalTickets = lottery.TicketsOfDrawAt(exactDate).Count();
            Assert.AreEqual(3, totalTickets);

            // Gradear
            drawDate = new DateTime(2024, 08, 29, 12, 00, 00);
            fireBallNumber = 0;
            winnerNumber = "98";
            report = lottery.DrawPicks(drawDate, winnerNumber, fireBallNumber, now, false, "Erick", false);
            Assert.AreEqual(3, report.TotalTickets);
            Assert.AreEqual(1, report.TotalWinners);
            Assert.AreEqual(45M, report.TotalPrize);

            totalTickets = lottery.TicketsOfDrawAt(exactDate).Count();
            Assert.AreEqual(0, totalTickets);
        }

        [TestMethod]
        public void T5_DeleteExpiredTickets_FB_Test()
        {
            bool itIsThePresent = false;
            QueueInMemory queue = new QueueInMemory();
            Integration.Kafka = queue;

            DateTime now = new DateTime(2023, 08, 29, 00, 00, 00);
            Company company = new Company();
            company.Accounting = new RealAccounting();
            CurrenciesTest.AddCurrencies();
            company.Sales.CreateStore(1, "Test Store").MakeCurrent();

            Domain domain = company.Sales.CreateDomain(false, 1, "localhost", PaymentChannels.Agents.INSIDER);
            Player player = company.GetOrCreateCustomerById("*********").Player;
            Customer customer = company.CustomerByPlayer(player);
            customer.Identifier = "*********";

            var lotteries = company.Lotto900();
            ExcludeSubtickets excludedSubtickets = new ExcludeSubtickets();

            Product ticketPick2StraightProd = lotteries.GetOrCreateProductById(5);
            ticketPick2StraightProd.Description = "Ticket pick 2 straight";
            ticketPick2StraightProd.Price = 1;

            State s = lotteries.GetOrCreateState("GA", "Georgia");
            LotteryPick<Pick2> lottery = (LotteryPick<Pick2>)lotteries.GetOrCreateLottery(2, s);
            lottery.TurnOnFireBall(itIsThePresent, now, "N/A");
            lottery.ForEach(12, 00, "123456", now, "N/A");

            decimal ticketAmount = 1;
            string number = "12";
            string dates = "08/29/2023";
            string states = "GA";
            string hours = "12:00 PM";
            string withFireballs = "true";
            int orderNumber = company.IdentityOrderNumber;
            OrderCart myOrder = (OrderCart)company.NewOrder(customer, orderNumber, Currencies.CODES.USD);
            lotteries.NextDatesAccumulator.Calculate(domain, now, "2", dates, states, hours, withFireballs);
            myOrder = (OrderCart)company.CreateTicketOrderWithMultipleAuthorization(player, states, dates, withFireballs, "MultipleInputSingleAmount", ticketAmount, new List<string> { number }, "S", myOrder, ticketPick2StraightProd, lotteries.NextDatesAccumulator, domain);
            company.AddOrders(myOrder);
            int lowReference = company.IdentitytBetNumber;
            int highReference = (lowReference + myOrder.CountOfItems - 1);
            AuthorizationsNumbers auths = new AuthorizationsNumbers(*********, 2);
            company.PurchaseTickets(itIsThePresent, myOrder, now, auths, domain, lowReference, orderNumber, "Artemis_ThirdParty_Deposit_USD");

            now = new DateTime(2024, 08, 29, 00, 00, 00);

            ticketAmount = 1;
            number = "12";
            dates = "08/29/2024";
            states = "GA";
            hours = "12:00 PM";
            withFireballs = "false";
            orderNumber = company.IdentityOrderNumber;
            myOrder = (OrderCart)company.NewOrder(customer, orderNumber, Currencies.CODES.USD);
            lotteries.NextDatesAccumulator.Calculate(domain, now, "2", dates, states, hours, withFireballs);
            myOrder = (OrderCart)company.CreateTicketOrderWithMultipleAuthorization(player, states, dates, withFireballs, "MultipleInputSingleAmount", ticketAmount, new List<string> { number }, "S", myOrder, ticketPick2StraightProd, lotteries.NextDatesAccumulator, domain);
            company.AddOrders(myOrder);
            lowReference = company.IdentitytBetNumber;
            highReference = (lowReference + myOrder.CountOfItems - 1);
            auths = new AuthorizationsNumbers(*********, 2);
            company.PurchaseTickets(itIsThePresent, myOrder, now, auths, domain, lowReference, orderNumber, "Artemis_ThirdParty_Deposit_USD");
            
            // Gradear
            DateTime drawDate = new DateTime(2023, 08, 29, 12, 00, 00);
            int fireBallNumber = 0;
            string winnerNumber = "12";
            var report = lottery.DrawPicks(drawDate, winnerNumber, fireBallNumber, now, false, "Erick", false);
            Assert.AreEqual(1, report.TotalTickets);
            Assert.AreEqual(1, report.TotalWinners);
            Assert.AreEqual(45M, report.TotalPrize);

            DateTime exactDate = new DateTime(2023, 08, 29, 12, 00, 00);
            int totalTickets = lottery.TicketsOfDrawAt(exactDate).Count();
            Assert.AreEqual(1, totalTickets);

            // Gradear
            drawDate = new DateTime(2024, 08, 29, 12, 00, 00);
            fireBallNumber = 0;
            winnerNumber = "98";
            report = lottery.DrawPicks(drawDate, winnerNumber, fireBallNumber, now, false, "Erick", false);
            Assert.AreEqual(1, report.TotalTickets);
            Assert.AreEqual(0, report.TotalWinners);
            Assert.AreEqual(0, report.TotalPrize);

            totalTickets = lottery.TicketsOfDrawAt(exactDate).Count();
            Assert.AreEqual(1, totalTickets);
        }

        [TestMethod]
        public void DrawsToBeConfirmed_Bug()
        {
            bool itIsThePresent = false;
            QueueInMemory queue = new QueueInMemory();
            Integration.Kafka = queue;

            DateTime now = new DateTime(2023, 08, 29, 00, 00, 00);
            Company company = new Company();
            company.Accounting = new RealAccounting();
            CurrenciesTest.AddCurrencies();
            company.Sales.CreateStore(1, "Test Store").MakeCurrent();

            Domain domain = company.Sales.CreateDomain(false, 1, "localhost", PaymentChannels.Agents.INSIDER);
            Player player = company.GetOrCreateCustomerById("*********").Player;
            Customer customer = company.CustomerByPlayer(player);
            customer.Identifier = "*********";

            var lotteries = company.Lotto900();
            ExcludeSubtickets excludedSubtickets = new ExcludeSubtickets();

            Product ticketPick2StraightProd = lotteries.GetOrCreateProductById(5);
            ticketPick2StraightProd.Description = "Ticket pick 2 straight";
            ticketPick2StraightProd.Price = 1;

            State s = lotteries.GetOrCreateState("GA", "Georgia");
            LotteryPick<Pick2> lottery = (LotteryPick<Pick2>)lotteries.GetOrCreateLottery(2, s);
            lottery.TurnOnFireBall(itIsThePresent, now, "N/A");
            lottery.ForEach(12, 00, "123456", now, "N/A");

            // Buy ticket 1 winner 1 loser
            decimal ticketAmount = 1;
            string number = "12";
            string dates = "08/29/2023";
            string states = "GA";
            string hours = "12:00 PM";
            string withFireballs = "true";
            int orderNumber = company.IdentityOrderNumber;
            OrderCart myOrder = (OrderCart)company.NewOrder(customer, orderNumber, Currencies.CODES.USD);
            lotteries.NextDatesAccumulator.Calculate(domain, now, "2", dates, states, hours, withFireballs);
            myOrder = (OrderCart)company.CreateTicketOrderWithMultipleAuthorization(player, states, dates, withFireballs, "MultipleInputSingleAmount", ticketAmount, new List<string> { number }, "S", myOrder, ticketPick2StraightProd, lotteries.NextDatesAccumulator, domain);
            company.AddOrders(myOrder);
            int lowReference = company.IdentitytBetNumber;
            int highReference = (lowReference + myOrder.CountOfItems - 1);
            AuthorizationsNumbers auths = new AuthorizationsNumbers(*********, 2);
            company.PurchaseTickets(itIsThePresent, myOrder, now, auths, domain, lowReference, orderNumber, "Artemis_ThirdParty_Deposit_USD");
            ticketAmount = 2;
            number = "98";
            dates = "08/29/2023";
            states = "GA";
            hours = "12:00 PM";
            withFireballs = "true";
            orderNumber = company.IdentityOrderNumber;
            myOrder = (OrderCart)company.NewOrder(customer, orderNumber, Currencies.CODES.USD);
            lotteries.NextDatesAccumulator.Calculate(domain, now, "2", dates, states, hours, withFireballs);
            myOrder = (OrderCart)company.CreateTicketOrderWithMultipleAuthorization(player, states, dates, withFireballs, "MultipleInputSingleAmount", ticketAmount, new List<string> { number }, "S", myOrder, ticketPick2StraightProd, lotteries.NextDatesAccumulator, domain);
            company.AddOrders(myOrder);
            lowReference = company.IdentitytBetNumber;
            highReference = (lowReference + myOrder.CountOfItems - 1);
            auths = new AuthorizationsNumbers(*********, 2);
            company.PurchaseTickets(itIsThePresent, myOrder, now, auths, domain, lowReference, orderNumber, "Artemis_ThirdParty_Deposit_USD");

            now = new DateTime(2024, 08, 29, 00, 00, 00);
            // Buy ticket 1 winner FB 1 loser FB
            ticketAmount = 1;
            number = "12";
            dates = "08/29/2024";
            states = "GA";
            hours = "12:00 PM";
            withFireballs = "true";
            orderNumber = company.IdentityOrderNumber;
            myOrder = (OrderCart)company.NewOrder(customer, orderNumber, Currencies.CODES.USD);
            lotteries.NextDatesAccumulator.Calculate(domain, now, "2", dates, states, hours, withFireballs);
            myOrder = (OrderCart)company.CreateTicketOrderWithMultipleAuthorization(player, states, dates, withFireballs, "MultipleInputSingleAmount", ticketAmount, new List<string> { number }, "S", myOrder, ticketPick2StraightProd, lotteries.NextDatesAccumulator, domain);
            company.AddOrders(myOrder);
            lowReference = company.IdentitytBetNumber;
            highReference = (lowReference + myOrder.CountOfItems - 1);
            auths = new AuthorizationsNumbers(*********, 2);
            company.PurchaseTickets(itIsThePresent, myOrder, now, auths, domain, lowReference, orderNumber, "Artemis_ThirdParty_Deposit_USD");
            ticketAmount = 2;
            number = "98";
            dates = "08/29/2024";
            states = "GA";
            hours = "12:00 PM";
            withFireballs = "true";
            orderNumber = company.IdentityOrderNumber;
            myOrder = (OrderCart)company.NewOrder(customer, orderNumber, Currencies.CODES.USD);
            lotteries.NextDatesAccumulator.Calculate(domain, now, "2", dates, states, hours, withFireballs);
            myOrder = (OrderCart)company.CreateTicketOrderWithMultipleAuthorization(player, states, dates, withFireballs, "MultipleInputSingleAmount", ticketAmount, new List<string> { number }, "S", myOrder, ticketPick2StraightProd, lotteries.NextDatesAccumulator, domain);
            company.AddOrders(myOrder);
            lowReference = company.IdentitytBetNumber;
            highReference = (lowReference + myOrder.CountOfItems - 1);
            auths = new AuthorizationsNumbers(*********, 2);
            company.PurchaseTickets(itIsThePresent, myOrder, now, auths, domain, lowReference, orderNumber, "Artemis_ThirdParty_Deposit_USD");


            // Gradear
            DateTime drawDate = new DateTime(2023, 08, 29, 12, 00, 00);

            int fireBallNumber = 7;
            string winnerNumber = "12";
            var report = lottery.DrawPicks(drawDate, winnerNumber, fireBallNumber, now, false, "Erick", false);

            foreach (var drawsToBeConfirmed2 in lotteries.DrawsToBeConfirmed())
            {
                LotteryDraw drawsToBeConfirmed = drawsToBeConfirmed2 as LotteryDraw;
                var grading = drawsToBeConfirmed.GradingAsString;
                bool hasFireBall = (drawsToBeConfirmed.Lottery as LotteryForPicks).HasFireBall;
                if(grading == "GRADED")
                {
                    if (hasFireBall)
                    {
                        var myFireball = (drawsToBeConfirmed.Lottery as LotteryPick<Pick2>).FireBallLottery;
                        var myFireballDraw = myFireball.LotteryDraws.GetLotteryDraw(drawDate);
                        var fbNumber = myFireballDraw.FireBallNumber;
                        Assert.IsTrue(fbNumber == 7);
                        bool hasFireBallCheck = lottery.FireBallLottery.HasFireBallNumber(drawDate, lottery.Schedules.First());
                        Assert.IsTrue(hasFireBallCheck);
                        var fireBallNumberTest = lottery.FireBallLottery.FireBallNumber(drawDate, lottery.Schedules.First());
                        Assert.IsTrue(fireBallNumberTest == 7);
                    }
                }
            }
        }

        [TestMethod]
        public void Controller_Draws_Bug_ChecksDrawFireBallNumber()
        {
            bool itIsThePresent = false;
            QueueInMemory queue = new QueueInMemory();
            Integration.Kafka = queue;

            DateTime now = new DateTime(2024, 08, 29, 00, 00, 00);
            Company company = new Company();
            company.Accounting = new RealAccounting();
            CurrenciesTest.AddCurrencies();
            company.Sales.CreateStore(1, "Test Store").MakeCurrent();

            Domain domain = company.Sales.CreateDomain(false, 1, "localhost", PaymentChannels.Agents.INSIDER);
            Player player = company.GetOrCreateCustomerById("*********").Player;
            Customer customer = company.CustomerByPlayer(player);
            customer.Identifier = "*********";

            var lotteries = company.Lotto900();
            ExcludeSubtickets excludedSubtickets = new ExcludeSubtickets();

            Product ticketPick2StraightProd = lotteries.GetOrCreateProductById(5);
            ticketPick2StraightProd.Description = "Ticket pick 2 straight";
            ticketPick2StraightProd.Price = 1;

            State s = lotteries.GetOrCreateState("GA", "Georgia");
            LotteryPick<Pick2> lottery = (LotteryPick<Pick2>)lotteries.GetOrCreateLottery(2, s);
            lottery.ForEach(12, 00, "123456", now, "N/A");

            decimal ticketAmount = 1;
            string number = "12";
            string dates = "08/29/2024";
            string states = "GA";
            string hours = "12:00 PM";
            string withFireballs = "false";
            int orderNumber = company.IdentityOrderNumber;
            OrderCart myOrder = (OrderCart)company.NewOrder(customer, orderNumber, Currencies.CODES.USD);
            lotteries.NextDatesAccumulator.Calculate(domain, now, "2", dates, states, hours, withFireballs);
            myOrder = (OrderCart)company.CreateTicketOrderWithMultipleAuthorization(player, states, dates, withFireballs, "MultipleInputSingleAmount", ticketAmount, new List<string> { number }, "S", myOrder, ticketPick2StraightProd, lotteries.NextDatesAccumulator, domain);
            company.AddOrders(myOrder);
            int lowReference = company.IdentitytBetNumber;
            int highReference = (lowReference + myOrder.CountOfItems - 1);
            AuthorizationsNumbers auths = new AuthorizationsNumbers(*********, 2);
            company.PurchaseTickets(itIsThePresent, myOrder, now, auths, domain, lowReference, orderNumber, "Artemis_ThirdParty_Deposit_USD");

            DateTime drawDate = new DateTime(2024, 08, 29, 12, 00, 00);
            string winnerNumber = "12";
            var report = lottery.DrawPicks(drawDate, winnerNumber, now, false, "Erick", false);
            Assert.AreEqual(1, report.TotalTickets);
            Assert.AreEqual(1, report.TotalWinners);
            Assert.AreEqual(90M, report.TotalPrize);

            DateTime lastVersionOfDate = now.Date.AddHours(23).AddMinutes(59).AddSeconds(59);
            List<GamesEngine.Time.Schedule> schedules = (List<GamesEngine.Time.Schedule>)lotteries.FinishedAndRegradedSchedulesOf(lastVersionOfDate);
            GamesEngine.Time.Schedule schedule = schedules.First();
            Assert.IsTrue(schedules.Count == 1);

            bool isNoAction = lottery.IsScheduleMarkedAsNoActionAt(drawDate);
            bool isRegraded = lottery.IsRegraded(drawDate);
            Assert.IsTrue(!isNoAction && !isRegraded);
            Assert.IsFalse(lottery.HasFireBall);

            DateTime scheduleDrawDate = schedule.ToDateTime(lastVersionOfDate);
            Assert.ThrowsException<GameEngineException>(() => {
                lottery.FireBallLottery.HasFireBallNumber(scheduleDrawDate, schedule);
            });
            Assert.ThrowsException<GameEngineException>(() => {
                lottery.FireBallLottery.FireBallNumber(scheduleDrawDate, schedule);
            });

            // Turn on FireBall
            lottery.TurnOnFireBall(itIsThePresent, now, "N/A");
            // Regradear
            DateTime gradeDate = new DateTime(2024, 08, 29, 12, 00, 00);
            report = lottery.Regrade(itIsThePresent, gradeDate, now, "N/A");
            Assert.AreEqual(1, report.TotalTickets);
            Assert.AreEqual(0, report.TotalWinners);
            Assert.AreEqual(0, report.TotalPrize);

            int fireBallNumber = 7;
            drawDate = new DateTime(2024, 08, 29, 12, 00, 00);
            winnerNumber = "21";
            report = lottery.DrawPicks(drawDate, winnerNumber, fireBallNumber, now, false, "Erick", false);
            Assert.AreEqual(1, report.TotalTickets);
            Assert.AreEqual(0, report.TotalWinners);
            Assert.AreEqual(0, report.TotalPrize);

            scheduleDrawDate = schedule.ToDateTime(lastVersionOfDate);
            isNoAction = lottery.IsScheduleMarkedAsNoActionAt(drawDate);
            isRegraded = lottery.IsRegraded(drawDate);
            Assert.IsTrue(!isNoAction && !isRegraded);
            Assert.IsTrue(lottery.HasFireBall);

            scheduleDrawDate = schedule.ToDateTime(lastVersionOfDate);
            bool hasFireBallNumber = lottery.FireBallLottery.HasFireBallNumber(scheduleDrawDate, schedule);
            Assert.IsTrue(hasFireBallNumber);
            int fireBallNumberCheck = lottery.FireBallLottery.FireBallNumber(scheduleDrawDate, schedule);
            Assert.IsTrue(fireBallNumberCheck == 7);
        }

        [TestMethod]
        public void Controller_ConfirmDraws_Bug_ChecksDrawFireBallNumber()
        {
            bool itIsThePresent = false;
            QueueInMemory queue = new QueueInMemory();
            Integration.Kafka = queue;

            DateTime now = new DateTime(2024, 08, 29, 00, 00, 00);
            Company company = new Company();
            company.Accounting = new RealAccounting();
            CurrenciesTest.AddCurrencies();
            company.Sales.CreateStore(1, "Test Store").MakeCurrent();

            Domain domain = company.Sales.CreateDomain(false, 1, "localhost", PaymentChannels.Agents.INSIDER);
            Player player = company.GetOrCreateCustomerById("*********").Player;
            Customer customer = company.CustomerByPlayer(player);
            customer.Identifier = "*********";

            var lotteries = company.Lotto900();
            ExcludeSubtickets excludedSubtickets = new ExcludeSubtickets();

            Product ticketPick2StraightProd = lotteries.GetOrCreateProductById(5);
            ticketPick2StraightProd.Description = "Ticket pick 2 straight";
            ticketPick2StraightProd.Price = 1;

            State s = lotteries.GetOrCreateState("GA", "Georgia");
            LotteryPick<Pick2> lottery = (LotteryPick<Pick2>)lotteries.GetOrCreateLottery(2, s);
            lottery.ForEach(12, 00, "123456", now, "N/A");

            decimal ticketAmount = 1;
            string number = "12";
            string dates = "08/29/2024";
            string states = "GA";
            string hours = "12:00 PM";
            string withFireballs = "false";
            int orderNumber = company.IdentityOrderNumber;
            OrderCart myOrder = (OrderCart)company.NewOrder(customer, orderNumber, Currencies.CODES.USD);
            lotteries.NextDatesAccumulator.Calculate(domain, now, "2", dates, states, hours, withFireballs);
            myOrder = (OrderCart)company.CreateTicketOrderWithMultipleAuthorization(player, states, dates, withFireballs, "MultipleInputSingleAmount", ticketAmount, new List<string> { number }, "S", myOrder, ticketPick2StraightProd, lotteries.NextDatesAccumulator, domain);
            company.AddOrders(myOrder);
            int lowReference = company.IdentitytBetNumber;
            int highReference = (lowReference + myOrder.CountOfItems - 1);
            AuthorizationsNumbers auths = new AuthorizationsNumbers(*********, 2);
            company.PurchaseTickets(itIsThePresent, myOrder, now, auths, domain, lowReference, orderNumber, "Artemis_ThirdParty_Deposit_USD");

            DateTime drawDate = new DateTime(2024, 08, 29, 12, 00, 00);
            string winnerNumber = "12";
            var report = lottery.DrawPicks(drawDate, winnerNumber, now, false, "Erick", false);
            Assert.AreEqual(1, report.TotalTickets);
            Assert.AreEqual(1, report.TotalWinners);
            Assert.AreEqual(90M, report.TotalPrize);

            List<LotteryComplete> tobeconfirmed = (List<LotteryComplete>)lotteries.DrawsToBeConfirmed();
            LotteryComplete drawToBeConfirm = tobeconfirmed.First();
            Assert.IsTrue(tobeconfirmed.Count == 1);

            string grading = drawToBeConfirm.GradingAsString;
            Assert.IsTrue(grading == "GRADED");

            bool hasFireBall = lottery.HasFireBall && lottery.FireBallLottery.LotteryDraws.HasSequenceOfNumbers(drawToBeConfirm.Date);
            Assert.IsFalse(hasFireBall);
            Assert.ThrowsException<GameEngineException>(() => {
                lottery.FireBallLottery.HasFireBallNumber(drawToBeConfirm.Date, drawToBeConfirm.Schedule);
            });
            Assert.ThrowsException<GameEngineException>(() =>{
                lottery.FireBallLottery.FireBallNumber(drawToBeConfirm.Date, drawToBeConfirm.Schedule);
            });

            // Turn on FireBall
            lottery.TurnOnFireBall(itIsThePresent, now, "N/A");
            // Regradear
            DateTime gradeDate = new DateTime(2024, 08, 29, 12, 00, 00);
            report = lottery.Regrade(itIsThePresent, gradeDate, now, "N/A");
            Assert.AreEqual(1, report.TotalTickets);
            Assert.AreEqual(0, report.TotalWinners);
            Assert.AreEqual(0, report.TotalPrize);

            int fireBallNumber = 7;
            drawDate = new DateTime(2024, 08, 29, 12, 00, 00);
            winnerNumber = "21";
            report = lottery.DrawPicks(drawDate, winnerNumber, fireBallNumber, now, false, "Erick", false);
            Assert.AreEqual(1, report.TotalTickets);
            Assert.AreEqual(0, report.TotalWinners);
            Assert.AreEqual(0, report.TotalPrize);

            tobeconfirmed = (List<LotteryComplete>)lotteries.DrawsToBeConfirmed();
            drawToBeConfirm = tobeconfirmed.First();
            grading = drawToBeConfirm.GradingAsString;
            Assert.IsTrue(grading == "GRADED");

            Assert.IsTrue(lottery.HasFireBall);
            Assert.IsTrue(lottery.FireBallLottery.LotteryDraws.HasSequenceOfNumbers(drawToBeConfirm.Date));
            hasFireBall = lottery.HasFireBall && lottery.FireBallLottery.LotteryDraws.HasSequenceOfNumbers(drawToBeConfirm.Date);
            Assert.IsTrue(hasFireBall);
            bool hasFireBallNumber = lottery.FireBallLottery.HasFireBallNumber(drawToBeConfirm.Date, drawToBeConfirm.Schedule);
            Assert.IsTrue(hasFireBallNumber);
            int fireBallNumberCheck = lottery.FireBallLottery.FireBallNumber(drawToBeConfirm.Date, drawToBeConfirm.Schedule);
            Assert.IsTrue(fireBallNumberCheck == 7);
        }

        [TestMethod]
        public void DecimalPrize_DrawPicks3_FireBall_Test()
        {
            bool itIsThePresent = false;
            QueueInMemory queue = new QueueInMemory();
            Integration.Kafka = queue;

            DateTime now = new DateTime(2024, 08, 29, 00, 00, 00);
            Company company = new Company();
            company.Accounting = new RealAccounting();
            CurrenciesTest.AddCurrencies();
            company.Sales.CreateStore(1, "Test Store").MakeCurrent();

            Domain domain = company.Sales.CreateDomain(false, 1, "localhost", PaymentChannels.Agents.INSIDER);
            Player player = company.GetOrCreateCustomerById("*********").Player;
            Customer customer = company.CustomerByPlayer(player);
            customer.Identifier = "*********";

            var lotteries = company.Lotto900();
            ExcludeSubtickets excludedSubtickets = new ExcludeSubtickets();

            Product ticketPick2StraightProd = lotteries.GetOrCreateProductById(5);
            ticketPick2StraightProd.Description = "Ticket pick 3 straight";
            ticketPick2StraightProd.Price = 1;

            State s = lotteries.GetOrCreateState("GA", "Georgia");
            LotteryPick<Pick3> lottery = (LotteryPick<Pick3>)lotteries.GetOrCreateLottery(3, s);
            lottery.TurnOnFireBall(itIsThePresent, now, "N/A");
            lottery.ForEach(12, 00, "123456", now, "N/A");

            decimal ticketAmount = 0.5M;
            string number = "187";
            string dates = "08/29/2024";
            string states = "GA";
            string hours = "12:00 PM";
            string withFireballs = "true";
            int orderNumber = company.IdentityOrderNumber;
            OrderCart myOrder = (OrderCart)company.NewOrder(customer, orderNumber, Currencies.CODES.USD);
            lotteries.NextDatesAccumulator.Calculate(domain, now, "3", dates, states, hours, withFireballs);
            myOrder = (OrderCart)company.CreateTicketOrderWithMultipleAuthorization(player, states, dates, withFireballs, "MultipleInputSingleAmount", ticketAmount, new List<string> { number }, "S", myOrder, ticketPick2StraightProd, lotteries.NextDatesAccumulator, domain);
            company.AddOrders(myOrder);
            int lowReference = company.IdentitytBetNumber;
            int highReference = (lowReference + myOrder.CountOfItems - 1);
            AuthorizationsNumbers auths = new AuthorizationsNumbers(*********, 2);
            company.PurchaseTickets(itIsThePresent, myOrder, now, auths, domain, lowReference, orderNumber, "Artemis_ThirdParty_Deposit_USD");

            DateTime drawDate = new DateTime(2024, 08, 29, 12, 00, 00);
            int fireBallNumber = 1;
            string winnerNumber = "987";
            var report = lottery.DrawPicks(drawDate, winnerNumber, fireBallNumber, now, itIsThePresent, "Erick", false);
            Assert.AreEqual(1, report.TotalTickets);
            Assert.AreEqual(1, report.TotalWinners);
            Assert.AreEqual(75M, report.TotalPrize);
        }

        [TestMethod]
        public void ConfirmDraw_FromOldDraw_WithNoFireBall_Bug9028()
        {
            bool itIsThePresent = false;
            QueueInMemory queue = new QueueInMemory();
            Integration.Kafka = queue;

            DateTime now = new DateTime(2023, 08, 29, 00, 00, 00);
            Company company = new Company();
            company.Accounting = new RealAccounting();
            CurrenciesTest.AddCurrencies();
            company.Sales.CreateStore(1, "Test Store").MakeCurrent();

            Domain domain = company.Sales.CreateDomain(false, 1, "localhost", PaymentChannels.Agents.INSIDER);
            Player player = company.GetOrCreateCustomerById("*********").Player;
            Customer customer = company.CustomerByPlayer(player);
            customer.Identifier = "*********";

            var lotteries = company.Lotto900();
            ExcludeSubtickets excludedSubtickets = new ExcludeSubtickets();

            Product ticketPick2StraightProd = lotteries.GetOrCreateProductById(5);
            ticketPick2StraightProd.Description = "Ticket pick 2 straight";
            ticketPick2StraightProd.Price = 1;

            State s2 = lotteries.GetOrCreateState("GA", "Georgia");
            LotteryPick<Pick2> lottery2 = (LotteryPick<Pick2>)lotteries.GetOrCreateLottery(2, s2);
            lottery2.ForEach(12, 00, "123456", now, "N/A");

            State s3 = lotteries.GetOrCreateState("FL", "Florida");
            LotteryPick<Pick3> lottery3 = (LotteryPick<Pick3>)lotteries.GetOrCreateLottery(3, s3);
            lottery3.TurnOnFireBall(itIsThePresent, now, "N/A");
            lottery3.ForEach(12, 00, "123456", now, "N/A");

            decimal ticketAmount = 1;
            string number = "12";
            string dates = "08/29/2023";
            string states = "GA";
            string hours = "12:00 PM";
            string withFireballs = "false";
            int orderNumber = company.IdentityOrderNumber;
            OrderCart myOrder = (OrderCart)company.NewOrder(customer, orderNumber, Currencies.CODES.USD);
            lotteries.NextDatesAccumulator.Calculate(domain, now, "2", dates, states, hours, withFireballs);
            myOrder = (OrderCart)company.CreateTicketOrderWithMultipleAuthorization(player, states, dates, withFireballs, "MultipleInputSingleAmount", ticketAmount, new List<string> { number }, "S", myOrder, ticketPick2StraightProd, lotteries.NextDatesAccumulator, domain);
            company.AddOrders(myOrder);
            int lowReference = company.IdentitytBetNumber;
            int highReference = (lowReference + myOrder.CountOfItems - 1);
            AuthorizationsNumbers auths = new AuthorizationsNumbers(*********, 2);
            company.PurchaseTickets(itIsThePresent, myOrder, now, auths, domain, lowReference, orderNumber, "Artemis_ThirdParty_Deposit_USD");
            
            ticketAmount = 1;
            number = "123";
            dates = "08/29/2023";
            states = "FL";
            hours = "12:00 PM";
            withFireballs = "false";
            orderNumber = company.IdentityOrderNumber;
            myOrder = (OrderCart)company.NewOrder(customer, orderNumber, Currencies.CODES.USD);
            lotteries.NextDatesAccumulator.Calculate(domain, now, "3", dates, states, hours, withFireballs);
            myOrder = (OrderCart)company.CreateTicketOrderWithMultipleAuthorization(player, states, dates, withFireballs, "MultipleInputSingleAmount", ticketAmount, new List<string> { number }, "S", myOrder, ticketPick2StraightProd, lotteries.NextDatesAccumulator, domain);
            company.AddOrders(myOrder);
            lowReference = company.IdentitytBetNumber;
            highReference = (lowReference + myOrder.CountOfItems - 1);
            auths = new AuthorizationsNumbers(*********, 2);
            company.PurchaseTickets(itIsThePresent, myOrder, now, auths, domain, lowReference, orderNumber, "Artemis_ThirdParty_Deposit_USD");

            DateTime drawDate = new DateTime(2023, 08, 29, 12, 00, 00);

            // Gradear Pick 2
            string winnerNumber = "12";
            var report = lottery2.DrawPicks(drawDate, winnerNumber, now, false, "Erick", false);
            Assert.AreEqual(1, report.TotalTickets);
            Assert.AreEqual(1, report.TotalWinners);
            Assert.AreEqual(90M, report.TotalPrize);

            // Gradear Pick 3
            winnerNumber = "123";
            int fireBallNumber = 9;
            report = lottery3.DrawPicks(drawDate, winnerNumber, fireBallNumber, now, false, "Erick", false);
            Assert.AreEqual(1, report.TotalTickets);
            Assert.AreEqual(1, report.TotalWinners);
            Assert.AreEqual(900M, report.TotalPrize);

            now = new DateTime(2024, 09, 25, 00, 00, 00);

            // turn on FireBall pick 2
            lottery2.TurnOnFireBall(itIsThePresent, now, "N/A");

            // Can be confirmed pick 2
            bool canBeConfirmed = lottery2.CanBeConfirmed(drawDate);
            Assert.IsTrue(canBeConfirmed);

            // Can be confirmed pick 3
            canBeConfirmed = lottery3.CanBeConfirmed(drawDate);
            Assert.IsTrue(canBeConfirmed);

            // Confirm pick 2
            PicksDrawingsSummaryReport drawingsSummaryReport = (PicksDrawingsSummaryReport)lottery2.ConfirmDraw(drawDate, now, "N/a", itIsThePresent, true);
            Assert.AreEqual(1, drawingsSummaryReport.TotalTickets);
            Assert.AreEqual(1, drawingsSummaryReport.TotalWinners);
            Assert.AreEqual(90M, drawingsSummaryReport.TotalPrize);

            // Confirm pick 3
            drawingsSummaryReport = (PicksDrawingsSummaryReport)lottery3.ConfirmDraw(drawDate, now, "N/a", itIsThePresent, true);
            Assert.AreEqual(1, drawingsSummaryReport.TotalTickets);
            Assert.AreEqual(1, drawingsSummaryReport.TotalWinners);
            Assert.AreEqual(900M, drawingsSummaryReport.TotalPrize);
        }

        [TestMethod]
        public void GradeAndRegrade_NoFireBallDraw_Test()
        {
            bool itIsThePresent = false;
            QueueInMemory queue = new QueueInMemory();
            Integration.Kafka = queue;

            DateTime now = new DateTime(2024, 09, 26, 00, 00, 00);
            Company company = new Company();
            company.Accounting = new RealAccounting();
            CurrenciesTest.AddCurrencies();
            company.Sales.CreateStore(1, "Test Store").MakeCurrent();

            Domain domain = company.Sales.CreateDomain(false, 1, "localhost", PaymentChannels.Agents.INSIDER);
            Player player = company.GetOrCreateCustomerById("*********").Player;
            Customer customer = company.CustomerByPlayer(player);
            customer.Identifier = "*********";

            var lotteries = company.Lotto900();
            ExcludeSubtickets excludedSubtickets = new ExcludeSubtickets();

            Product ticketPick2StraightProd = lotteries.GetOrCreateProductById(5);
            ticketPick2StraightProd.Description = "Ticket pick 2 straight";
            ticketPick2StraightProd.Price = 1;

            State s = lotteries.GetOrCreateState("GA", "Georgia");
            LotteryPick<Pick2> lottery = (LotteryPick<Pick2>)lotteries.GetOrCreateLottery(2, s);
            lottery.ForEach(12, 00, "123456", now, "N/A");

            decimal ticketAmount = 1;
            string number = "12";
            string dates = "09/06/2024";
            string states = "GA";
            string hours = "12:00 PM";
            string withFireballs = "false";
            int orderNumber = company.IdentityOrderNumber;
            OrderCart myOrder = (OrderCart)company.NewOrder(customer, orderNumber, Currencies.CODES.USD);
            lotteries.NextDatesAccumulator.Calculate(domain, now, "2", dates, states, hours, withFireballs);
            myOrder = (OrderCart)company.CreateTicketOrderWithMultipleAuthorization(player, states, dates, withFireballs, "MultipleInputSingleAmount", ticketAmount, new List<string> { number }, "S", myOrder, ticketPick2StraightProd, lotteries.NextDatesAccumulator, domain);
            company.AddOrders(myOrder);
            int lowReference = company.IdentitytBetNumber;
            int highReference = (lowReference + myOrder.CountOfItems - 1);
            AuthorizationsNumbers auths = new AuthorizationsNumbers(*********, 2);
            company.PurchaseTickets(itIsThePresent, myOrder, now, auths, domain, lowReference, orderNumber, "Artemis_ThirdParty_Deposit_USD");

            lottery.TurnOnFireBall(itIsThePresent, now, "N/A");

            DateTime drawDate = new DateTime(2024, 09, 26, 12, 00, 00);

            // Gradear
            string winnerNumber = "12";
            int fireBallNumber = 9;
            var report = lottery.DrawPicks(drawDate, winnerNumber, fireBallNumber, now, false, "Erick", false);
            Assert.AreEqual(1, report.TotalTickets);
            Assert.AreEqual(1, report.TotalWinners);
            Assert.AreEqual(90M, report.TotalPrize);

            // Regradear
            DateTime reGradeDate = new DateTime(2024, 09, 26, 12, 00, 00);
            report = lottery.Regrade(itIsThePresent, reGradeDate, now, "N/A");
            Assert.AreEqual(1, report.TotalTickets);
            Assert.AreEqual(0, report.TotalWinners);
            Assert.AreEqual(0, report.TotalPrize);
        }

        [TestMethod]
        public void NoActionAndRegrade_NoFireBallDraw_Test()
        {
            bool itIsThePresent = false;
            QueueInMemory queue = new QueueInMemory();
            Integration.Kafka = queue;

            DateTime now = new DateTime(2024, 09, 26, 00, 00, 00);
            Company company = new Company();
            company.Accounting = new RealAccounting();
            CurrenciesTest.AddCurrencies();
            company.Sales.CreateStore(1, "Test Store").MakeCurrent();

            Domain domain = company.Sales.CreateDomain(false, 1, "localhost", PaymentChannels.Agents.INSIDER);
            Player player = company.GetOrCreateCustomerById("*********").Player;
            Customer customer = company.CustomerByPlayer(player);
            customer.Identifier = "*********";

            var lotteries = company.Lotto900();
            ExcludeSubtickets excludedSubtickets = new ExcludeSubtickets();

            Product ticketPick2StraightProd = lotteries.GetOrCreateProductById(5);
            ticketPick2StraightProd.Description = "Ticket pick 2 straight";
            ticketPick2StraightProd.Price = 1;

            State s = lotteries.GetOrCreateState("GA", "Georgia");
            LotteryPick<Pick2> lottery = (LotteryPick<Pick2>)lotteries.GetOrCreateLottery(2, s);
            lottery.ForEach(12, 00, "123456", now, "N/A");

            decimal ticketAmount = 1;
            string number = "12";
            string dates = "09/06/2024";
            string states = "GA";
            string hours = "12:00 PM";
            string withFireballs = "false";
            int orderNumber = company.IdentityOrderNumber;
            OrderCart myOrder = (OrderCart)company.NewOrder(customer, orderNumber, Currencies.CODES.USD);
            lotteries.NextDatesAccumulator.Calculate(domain, now, "2", dates, states, hours, withFireballs);
            myOrder = (OrderCart)company.CreateTicketOrderWithMultipleAuthorization(player, states, dates, withFireballs, "MultipleInputSingleAmount", ticketAmount, new List<string> { number }, "S", myOrder, ticketPick2StraightProd, lotteries.NextDatesAccumulator, domain);
            company.AddOrders(myOrder);
            int lowReference = company.IdentitytBetNumber;
            int highReference = (lowReference + myOrder.CountOfItems - 1);
            AuthorizationsNumbers auths = new AuthorizationsNumbers(*********, 2);
            company.PurchaseTickets(itIsThePresent, myOrder, now, auths, domain, lowReference, orderNumber, "Artemis_ThirdParty_Deposit_USD");

            lottery.TurnOnFireBall(itIsThePresent, now, "N/A");

            DateTime drawDate = new DateTime(2024, 09, 26, 12, 00, 00);

            NoActionReport report = lottery.SetNoAction(itIsThePresent, drawDate, now, "Game Admin");
            Assert.AreEqual(1, report.TotalTickets);

            // Regradear
            DateTime reGradeDate = new DateTime(2024, 09, 26, 12, 00, 00);
            Assert.ThrowsException<GameEngineException>(() =>
            {
                lottery.Regrade(itIsThePresent, reGradeDate, now, "N/A");
            });
        }

        [TestMethod]
        public void Grade_Regrade_Grade_WithFireBall_Test()
        {
            bool itIsThePresent = false;
            QueueInMemory queue = new QueueInMemory();
            Integration.Kafka = queue;

            DateTime now = new DateTime(2024, 09, 26, 00, 00, 00);
            Company company = new Company();
            company.Accounting = new RealAccounting();
            CurrenciesTest.AddCurrencies();
            company.Sales.CreateStore(1, "Test Store").MakeCurrent();

            Domain domain = company.Sales.CreateDomain(false, 1, "localhost", PaymentChannels.Agents.INSIDER);
            Player player = company.GetOrCreateCustomerById("*********").Player;
            Customer customer = company.CustomerByPlayer(player);
            customer.Identifier = "*********";

            var lotteries = company.Lotto900();
            ExcludeSubtickets excludedSubtickets = new ExcludeSubtickets();

            Product ticketPick2StraightProd = lotteries.GetOrCreateProductById(5);
            ticketPick2StraightProd.Description = "Ticket pick 2 straight";
            ticketPick2StraightProd.Price = 1;

            State s = lotteries.GetOrCreateState("GA", "Georgia");
            LotteryPick<Pick2> lottery = (LotteryPick<Pick2>)lotteries.GetOrCreateLottery(2, s);
            lottery.ForEach(12, 00, "123456", now, "N/A");

            decimal ticketAmount = 1;
            string number = "12";
            string dates = "09/06/2024";
            string states = "GA";
            string hours = "12:00 PM";
            string withFireballs = "false";
            int orderNumber = company.IdentityOrderNumber;
            OrderCart myOrder = (OrderCart)company.NewOrder(customer, orderNumber, Currencies.CODES.USD);
            lotteries.NextDatesAccumulator.Calculate(domain, now, "2", dates, states, hours, withFireballs);
            myOrder = (OrderCart)company.CreateTicketOrderWithMultipleAuthorization(player, states, dates, withFireballs, "MultipleInputSingleAmount", ticketAmount, new List<string> { number }, "S", myOrder, ticketPick2StraightProd, lotteries.NextDatesAccumulator, domain);
            company.AddOrders(myOrder);
            int lowReference = company.IdentitytBetNumber;
            int highReference = (lowReference + myOrder.CountOfItems - 1);
            AuthorizationsNumbers auths = new AuthorizationsNumbers(*********, 2);
            company.PurchaseTickets(itIsThePresent, myOrder, now, auths, domain, lowReference, orderNumber, "Artemis_ThirdParty_Deposit_USD");

            lottery.TurnOnFireBall(itIsThePresent, now, "N/A");

            DateTime drawDate = new DateTime(2024, 09, 26, 12, 00, 00);

            // Gradear
            string winnerNumber = "12";
            int fireBallNumber = 9;
            var report = lottery.DrawPicks(drawDate, winnerNumber, fireBallNumber, now, false, "Erick", false);
            Assert.AreEqual(1, report.TotalTickets);
            Assert.AreEqual(1, report.TotalWinners);
            Assert.AreEqual(90M, report.TotalPrize);

            // Regradear
            DateTime reGradeDate = new DateTime(2024, 09, 26, 12, 00, 00);
            report = lottery.Regrade(itIsThePresent, reGradeDate, now, "N/A");
            Assert.AreEqual(1, report.TotalTickets);
            Assert.AreEqual(0, report.TotalWinners);
            Assert.AreEqual(0, report.TotalPrize);

            // Gradear
            winnerNumber = "98";
            fireBallNumber = 9;
            report = lottery.DrawPicks(drawDate, winnerNumber, fireBallNumber, now, false, "Erick", false);
            Assert.AreEqual(1, report.TotalTickets);
            Assert.AreEqual(0, report.TotalWinners);
            Assert.AreEqual(0, report.TotalPrize);
        }

        [TestMethod]
        public void Grade_Regrade_NoAction_WithFireBall_Test()
        {
            bool itIsThePresent = false;
            QueueInMemory queue = new QueueInMemory();
            Integration.Kafka = queue;

            DateTime now = new DateTime(2024, 09, 26, 00, 00, 00);
            Company company = new Company();
            company.Accounting = new RealAccounting();
            CurrenciesTest.AddCurrencies();
            company.Sales.CreateStore(1, "Test Store").MakeCurrent();

            Domain domain = company.Sales.CreateDomain(false, 1, "localhost", PaymentChannels.Agents.INSIDER);
            Player player = company.GetOrCreateCustomerById("*********").Player;
            Customer customer = company.CustomerByPlayer(player);
            customer.Identifier = "*********";

            var lotteries = company.Lotto900();
            ExcludeSubtickets excludedSubtickets = new ExcludeSubtickets();

            Product ticketPick2StraightProd = lotteries.GetOrCreateProductById(5);
            ticketPick2StraightProd.Description = "Ticket pick 2 straight";
            ticketPick2StraightProd.Price = 1;

            State s = lotteries.GetOrCreateState("GA", "Georgia");
            LotteryPick<Pick2> lottery = (LotteryPick<Pick2>)lotteries.GetOrCreateLottery(2, s);
            lottery.ForEach(12, 00, "123456", now, "N/A");

            decimal ticketAmount = 1;
            string number = "12";
            string dates = "09/06/2024";
            string states = "GA";
            string hours = "12:00 PM";
            string withFireballs = "false";
            int orderNumber = company.IdentityOrderNumber;
            OrderCart myOrder = (OrderCart)company.NewOrder(customer, orderNumber, Currencies.CODES.USD);
            lotteries.NextDatesAccumulator.Calculate(domain, now, "2", dates, states, hours, withFireballs);
            myOrder = (OrderCart)company.CreateTicketOrderWithMultipleAuthorization(player, states, dates, withFireballs, "MultipleInputSingleAmount", ticketAmount, new List<string> { number }, "S", myOrder, ticketPick2StraightProd, lotteries.NextDatesAccumulator, domain);
            company.AddOrders(myOrder);
            int lowReference = company.IdentitytBetNumber;
            int highReference = (lowReference + myOrder.CountOfItems - 1);
            AuthorizationsNumbers auths = new AuthorizationsNumbers(*********, 2);
            company.PurchaseTickets(itIsThePresent, myOrder, now, auths, domain, lowReference, orderNumber, "Artemis_ThirdParty_Deposit_USD");

            lottery.TurnOnFireBall(itIsThePresent, now, "N/A");

            DateTime drawDate = new DateTime(2024, 09, 26, 12, 00, 00);

            // Gradear
            string winnerNumber = "12";
            int fireBallNumber = 9;
            var report = lottery.DrawPicks(drawDate, winnerNumber, fireBallNumber, now, false, "Erick", false);
            Assert.AreEqual(1, report.TotalTickets);
            Assert.AreEqual(1, report.TotalWinners);
            Assert.AreEqual(90M, report.TotalPrize);

            // Regradear
            DateTime reGradeDate = new DateTime(2024, 09, 26, 12, 00, 00);
            report = lottery.Regrade(itIsThePresent, reGradeDate, now, "N/A");
            Assert.AreEqual(1, report.TotalTickets);
            Assert.AreEqual(0, report.TotalWinners);
            Assert.AreEqual(0, report.TotalPrize);

            // NoAction
            DateTime noActionDate = new DateTime(2024, 09, 26, 12, 00, 00);
            NoActionReport noActionReport = lottery.SetNoAction(itIsThePresent, noActionDate, now, "Game Admin");
            Assert.AreEqual(1, noActionReport.TotalTickets);
        }

        [TestMethod]
        public void Bug9048_TurnOff_Test()
        {
            bool itIsThePresent = false;
            QueueInMemory queue = new QueueInMemory();
            Integration.Kafka = queue;

            DateTime now = new DateTime(2024, 08, 29, 00, 00, 00);
            Company company = new Company();
            company.Accounting = new RealAccounting();
            CurrenciesTest.AddCurrencies();
            company.Sales.CreateStore(1, "Test Store").MakeCurrent();

            Domain domain = company.Sales.CreateDomain(false, 1, "localhost", PaymentChannels.Agents.INSIDER);
            Player player = company.GetOrCreateCustomerById("*********").Player;
            Customer customer = company.CustomerByPlayer(player);
            customer.Identifier = "*********";

            var lotteries = company.Lotto900();
            ExcludeSubtickets excludedSubtickets = new ExcludeSubtickets();

            Product ticketPick2StraightProd = lotteries.GetOrCreateProductById(5);
            ticketPick2StraightProd.Description = "Ticket pick 2 straight";
            ticketPick2StraightProd.Price = 1;

            State s = lotteries.GetOrCreateState("GA", "Georgia");
            LotteryPick<Pick2> lottery = (LotteryPick<Pick2>)lotteries.GetOrCreateLottery(2, s);
            lottery.TurnOnFireBall(itIsThePresent, now, "N/A");
            lottery.ForEach(12, 00, "123456", now, "N/A");

            // Buy ticket Today
            string dates = "08/29/2024";
            string states = "GA";
            string hours = "12:00 PM";
            string withFireballs = "false";
            int orderNumber = company.IdentityOrderNumber;
            OrderCart myOrder = (OrderCart)company.NewOrder(customer, orderNumber, Currencies.CODES.USD);
            lotteries.NextDatesAccumulator.Calculate(domain, now, "2", dates, states, hours, withFireballs);
            myOrder = (OrderCart)company.CreateTicketOrderWithMultipleAuthorization(player, states, dates, withFireballs, "MultipleInputSingleAmount", 1, new List<string> { "12" }, "S", myOrder, ticketPick2StraightProd, lotteries.NextDatesAccumulator, domain);
            company.AddOrders(myOrder);
            int lowReference = company.IdentitytBetNumber;
            int highReference = (lowReference + myOrder.CountOfItems - 1);
            AuthorizationsNumbers auths = new AuthorizationsNumbers(*********, 2);
            company.PurchaseTickets(itIsThePresent, myOrder, now, auths, domain, lowReference, orderNumber, "Artemis_ThirdParty_Deposit_USD");

            // Buy ticket Tomorrow
            dates = "08/30/2024";
            states = "GA";
            hours = "12:00 PM";
            withFireballs = "true";
            orderNumber = company.IdentityOrderNumber;
            myOrder = (OrderCart)company.NewOrder(customer, orderNumber, Currencies.CODES.USD);
            lotteries.NextDatesAccumulator.Calculate(domain, now, "2", dates, states, hours, withFireballs);
            myOrder = (OrderCart)company.CreateTicketOrderWithMultipleAuthorization(player, states, dates, withFireballs, "MultipleInputSingleAmount", 1, new List<string> { "12" }, "S", myOrder, ticketPick2StraightProd, lotteries.NextDatesAccumulator, domain);
            company.AddOrders(myOrder);
            lowReference = company.IdentitytBetNumber;
            highReference = (lowReference + myOrder.CountOfItems - 1);
            auths = new AuthorizationsNumbers(*********, 2);
            company.PurchaseTickets(itIsThePresent, myOrder, now, auths, domain, lowReference, orderNumber, "Artemis_ThirdParty_Deposit_USD");

            // Turn off fireball
            lottery.TurnOffFireBall(itIsThePresent, now, "N/A");

            DateTime lastVersionOfDate = new DateTime(2024, 08, 29, 23, 59, 59);
            var schedules = lotteries.PendingAndNoRegradedSchedulesAt(lastVersionOfDate);
            foreach (var scheduledLotteries in schedules)
            {
                var lott = scheduledLotteries.Lottery as LotteryForPicks;
                bool hasFireBall = lott.IsFireBallTurnedOn;
                Assert.IsFalse(hasFireBall);
            }

            // gradear
            string winnerNumber = "12";
            DateTime drawDate = new DateTime(2024, 08, 29, 12, 00, 00);
            var report = lottery.DrawPicks(drawDate, winnerNumber, now, false, "Erick", false);
            Assert.AreEqual(1, report.TotalTickets);
            Assert.AreEqual(1, report.TotalWinners);
            Assert.AreEqual(90M, report.TotalPrize);
        }

        [TestMethod]
        public void AnyScheduleHasChanged_And_NextDatesAccumulator_Bug8996_FireBall_Test()
        {
            QueueInMemory queue = new QueueInMemory();
            Integration.Kafka = queue;

            DateTime now = new DateTime(2024, 08, 29, 00, 00, 00);
            decimal founds = 80000;
            Company company = new Company();
            company.Accounting = new RealAccounting();
            CurrenciesTest.AddCurrencies();
            company.Sales.CreateStore(1, "Test Store").MakeCurrent();

            Domain domain = company.Sales.CreateDomain(false, 1, "localhost", PaymentChannels.Agents.INSIDER);
            Player player = company.GetOrCreateCustomerById("*********").Player;
            Customer customer = company.CustomerByPlayer(player);
            customer.Identifier = "*********";

            var lotteries = company.Lotto900();
            State s = lotteries.GetOrCreateState("GA", "Georgia");
            LotteryPick<Pick2> lottery = (LotteryPick<Pick2>)lotteries.GetOrCreateLottery(2, s);
            lottery.TurnOnFireBall(false, now, "N/A");
            lottery.ForEach(12, 00, "123456", now, "N/A");
            ExcludeSubtickets excludedSubtickets = new ExcludeSubtickets();

            // Create a product and buy the ticket
            Product ticketPick3StraightProd = lotteries.GetOrCreateProductById(1);
            ticketPick3StraightProd.Description = "Ticket pick 2 straight";
            ticketPick3StraightProd.Price = 1;
            string dates = "08/29/2024";
            string states = "GA,GA";
            string hours = "12:00 PM,12:00 PM";
            string withFireballs = "false,true";
            string strIsPresentPlayerBeforeToCloseStore = "true,true";
            string strUseNextDate = "false,false";


            //BEFORE PURCHASE
            bool anyScheduleHasChangedBeforePurchase = lotteries.AnyScheduleHasChanged(2, states, hours, withFireballs);
            Assert.IsFalse(anyScheduleHasChangedBeforePurchase);
            var nextDatesAccumulatorBeforePurchase = new NextDatesAccumulator(lotteries, domain, now, "2", dates, states, hours, withFireballs, strIsPresentPlayerBeforeToCloseStore, strUseNextDate);
            var getAll = nextDatesAccumulatorBeforePurchase.GetAll;
            Assert.AreEqual(2, getAll.Count());
            Assert.IsFalse(getAll.ElementAt(0).WithFireBall);
            Assert.IsTrue(getAll.ElementAt(1).WithFireBall);

            NextDatesAccumulator nextDatesAccumulator = new NextDatesAccumulator(lotteries, domain, now, "2", dates, states, hours, withFireballs, strIsPresentPlayerBeforeToCloseStore, strUseNextDate);
            OrderCart myOrder = (OrderCart)company.GetNewOrder(customer);
            string numbers = "";
            string selectionMode = "MultipleInputSingleAmount";
            decimal ticketAmount = 1;
            string includedNumbersForInput = "12";
            string gameType = "Straight";
            Order order = company.CreateTicketOrder(lotteries, player, states, dates, withFireballs, numbers, selectionMode, ticketAmount, includedNumbersForInput, excludedSubtickets, gameType, myOrder, ticketPick3StraightProd, nextDatesAccumulator, domain);
            company.AddOrders(myOrder, founds);
            int orderNumber = order.Number;
            order = company.OrderByNumber(orderNumber);
            order.AuthorizationId = 123;
            var marketplace = new Marketplace(company, "CR");
            var agent = marketplace.AddAgent("1");
            myOrder.Agent = (GamesEngine.Exchange.Agent)agent;
            company.SaleTickets(true, myOrder, now, founds, 1, domain);
            Assert.AreEqual(1, orderNumber);

            int fireBallNumber = 9;
            string winnerNumber = "12";
            var report = lottery.DrawPicks(new DateTime(2024, 08, 29, 12, 00, 00), winnerNumber, fireBallNumber, now, false, "Erick", false);
            Assert.AreEqual(2, report.TotalTickets);
            Assert.AreEqual(2, report.TotalWinners);
            Assert.AreEqual(90.0M + 45.0M, report.TotalPrize);
        }

        [TestMethod]
        public void Bug9055_Pending_FireballTicket_Description_Test()
        {
            QueueInMemory queue = new QueueInMemory();
            Integration.Kafka = queue;

            DateTime now = new DateTime(2024, 08, 29, 00, 00, 00);
            decimal founds = 80000;
            Company company = new Company();
            company.Accounting = new RealAccounting();
            CurrenciesTest.AddCurrencies();
            company.Sales.CreateStore(1, "Test Store").MakeCurrent();

            Domain domain = company.Sales.CreateDomain(false, 1, "localhost", PaymentChannels.Agents.INSIDER);
            Player player = company.GetOrCreateCustomerById("*********").Player;
            Customer customer = company.CustomerByPlayer(player);
            customer.Identifier = "*********";

            var lotteries = company.Lotto900();
            State s = lotteries.GetOrCreateState("GA", "Georgia");
            LotteryPick<Pick5> lottery = (LotteryPick<Pick5>)lotteries.GetOrCreateLottery(5, s);
            lottery.TurnOnFireBall(false, now, "N/A");
            lottery.ForEach(12, 00, "123456", now, "N/A");

            // Create a product and buy the ticket
            Product ticketPick3StraightProd = lotteries.GetOrCreateProductById(1);
            ticketPick3StraightProd.Description = "Ticket pick 5 straight";
            ticketPick3StraightProd.Price = 1;
            string dates = "08/29/2024";
            string states = "GA,GA";
            string hours = "12:00 PM,12:00 PM";
            string withFireballs = "true,false";
            string strIsPresentPlayerBeforeToCloseStore = "true,true";
            string strUseNextDate = "false,false";
            NextDatesAccumulator nextDatesAccumulator = new NextDatesAccumulator(lotteries, domain, now, "5", dates, states, hours, withFireballs, strIsPresentPlayerBeforeToCloseStore, strUseNextDate);
            OrderCart myOrder = (OrderCart)company.GetNewOrder(customer);

            ExcludeSubtickets excludedSubtickets = new ExcludeSubtickets();
            string numbers = "";
            string selectionMode = "MultipleInputSingleAmount";
            decimal ticketAmount = 1;
            string includedNumbersForInput = "11119";
            string gameType = "Straight";
            Order order = company.CreateTicketOrder(lotteries, player, states, dates, withFireballs, numbers, selectionMode, ticketAmount, includedNumbersForInput, excludedSubtickets, gameType, myOrder, ticketPick3StraightProd, nextDatesAccumulator, domain);
            company.AddOrders(myOrder, founds);
            int orderNumber = order.Number;
            order = company.OrderByNumber(orderNumber);
            order.AuthorizationId = 123;
            var marketplace = new Marketplace(company, "CR");
            var agent = marketplace.AddAgent("1");
            myOrder.Agent = (GamesEngine.Exchange.Agent)agent;
            company.SaleTickets(true, myOrder, now, founds, 1, domain);
            Assert.AreEqual(1, orderNumber);

            DateTime startDate = new DateTime(2024, 08, 29, 00, 00, 00);
            DateTime endDate = new DateTime(2024, 08, 29, 00, 00, 00);
            var myAlivePendingTickets = player.PlayerLottoReports.MyAlivePendingTickets(startDate, endDate);
            Assert.IsTrue(myAlivePendingTickets.Count() == 2);

            Assert.AreEqual("GA 12:00 PM Fireball", myAlivePendingTickets.ElementAt(0).FirstTicket.DrawingDescription);
            Assert.AreEqual("GA 12:00 PM", myAlivePendingTickets.ElementAt(1).FirstTicket.DrawingDescription);
        }

        [TestMethod]
        public void Controller_Draws_Bug9057()
        {
            bool itIsThePresent = false;
            QueueInMemory queue = new QueueInMemory();
            Integration.Kafka = queue;

            DateTime now = new DateTime(2024, 08, 29, 00, 00, 00);
            Company company = new Company();
            company.Accounting = new RealAccounting();
            CurrenciesTest.AddCurrencies();
            company.Sales.CreateStore(1, "Test Store").MakeCurrent();

            Domain domain = company.Sales.CreateDomain(false, 1, "localhost", PaymentChannels.Agents.INSIDER);
            Player player = company.GetOrCreateCustomerById("*********").Player;
            Customer customer = company.CustomerByPlayer(player);
            customer.Identifier = "*********";

            var lotteries = company.Lotto900();
            ExcludeSubtickets excludedSubtickets = new ExcludeSubtickets();

            Product ticketPick2StraightProd = lotteries.GetOrCreateProductById(5);
            ticketPick2StraightProd.Description = "Ticket pick 2 straight";
            ticketPick2StraightProd.Price = 1;

            State s = lotteries.GetOrCreateState("GA", "Georgia");
            LotteryPick<Pick2> lottery = (LotteryPick<Pick2>)lotteries.GetOrCreateLottery(2, s);
            lottery.TurnOnFireBall(itIsThePresent, now, "N/A");
            lottery.ForEach(12, 00, "123456", now, "N/A");

            decimal ticketAmount = 1;
            string number = "12";
            string dates = "08/29/2024";
            string states = "GA";
            string hours = "12:00 PM";
            string withFireballs = "false";
            int orderNumber = company.IdentityOrderNumber;
            OrderCart myOrder = (OrderCart)company.NewOrder(customer, orderNumber, Currencies.CODES.USD);
            lotteries.NextDatesAccumulator.Calculate(domain, now, "2", dates, states, hours, withFireballs);
            myOrder = (OrderCart)company.CreateTicketOrderWithMultipleAuthorization(player, states, dates, withFireballs, "MultipleInputSingleAmount", ticketAmount, new List<string> { number }, "S", myOrder, ticketPick2StraightProd, lotteries.NextDatesAccumulator, domain);
            company.AddOrders(myOrder);
            int lowReference = company.IdentitytBetNumber;
            int highReference = (lowReference + myOrder.CountOfItems - 1);
            AuthorizationsNumbers auths = new AuthorizationsNumbers(*********, 2);
            company.PurchaseTickets(itIsThePresent, myOrder, now, auths, domain, lowReference, orderNumber, "Artemis_ThirdParty_Deposit_USD");

            DateTime drawDate = new DateTime(2024, 08, 29, 12, 00, 00);
            string winnerNumber = "12";
            int fireBallNumber = 9;
            var report = lottery.DrawPicks(drawDate, winnerNumber, fireBallNumber, now, false, "Erick", false);
            Assert.AreEqual(1, report.TotalTickets);
            Assert.AreEqual(1, report.TotalWinners);
            Assert.AreEqual(90M, report.TotalPrize);

            //NoAction
            DateTime noActionDate = new DateTime(2024, 08, 29, 12, 00, 00);
            NoActionReport noActionReport = lottery.SetNoAction(itIsThePresent, noActionDate, now, "Game Admin");
            Assert.AreEqual(1, noActionReport.TotalTickets);

            DateTime lastVersionOfDate = now.Date.AddHours(23).AddMinutes(59).AddSeconds(59);
            List<GamesEngine.Time.Schedule> schedules = (List<GamesEngine.Time.Schedule>)lotteries.FinishedAndRegradedSchedulesOf(lastVersionOfDate);

            foreach (var finishedLotteries in schedules)
            {
                LotteryForPicks lott = finishedLotteries.Lottery as LotteryForPicks;

                bool isFireBallTurnedOn = lott.IsFireBallTurnedOn;
                Assert.IsTrue(isFireBallTurnedOn);
                
                bool isNoAction = lottery.IsScheduleMarkedAsNoActionAt(drawDate);
                bool isRegraded = lottery.IsRegraded(drawDate);
                Assert.IsTrue(isNoAction);
                Assert.IsFalse(isRegraded);
            }

            //Gradear
            winnerNumber = "12";
            fireBallNumber = 9;
            report = lottery.DrawPicks(drawDate, winnerNumber, fireBallNumber, now, false, "Erick", false);
            Assert.AreEqual(1, report.TotalTickets);
            Assert.AreEqual(1, report.TotalWinners);
            Assert.AreEqual(90M, report.TotalPrize);

            foreach (var finishedLotteries in schedules)
            {
                LotteryForPicks lott = finishedLotteries.Lottery as LotteryForPicks;

                bool isFireBallTurnedOn = lott.IsFireBallTurnedOn;
                Assert.IsTrue(isFireBallTurnedOn);

                bool hasFireBallNumber = lottery.FireBallLottery.HasFireBallNumber(drawDate, finishedLotteries);
                Assert.IsTrue(hasFireBallNumber);

                Assert.IsTrue(lottery.HasFireBall);
                int fbNumber = lottery.FireBallLottery.FireBallNumber(drawDate, finishedLotteries);
                Assert.AreEqual(9, fbNumber);

                bool isNoAction = lottery.IsScheduleMarkedAsNoActionAt(drawDate);
                bool isRegraded = lottery.IsRegraded(drawDate);
                Assert.IsFalse(isNoAction);
                Assert.IsFalse(isRegraded);
                string winnerNumbers = lottery.SequenceOfNumbersOfDrawAt(drawDate, finishedLotteries);
                Assert.AreEqual("12", winnerNumbers);
            }

            //Regrade
            DateTime reGradeDate = new DateTime(2024, 08, 29, 12, 00, 00);
            report = lottery.Regrade(itIsThePresent, reGradeDate, now, "N/A");
            Assert.AreEqual(1, report.TotalTickets);
            Assert.AreEqual(0, report.TotalWinners);
            Assert.AreEqual(0, report.TotalPrize);

            foreach (var finishedLotteries in schedules)
            {
                LotteryForPicks lott = finishedLotteries.Lottery as LotteryForPicks;

                bool isFireBallTurnedOn = lott.IsFireBallTurnedOn;
                Assert.IsTrue(isFireBallTurnedOn);

                Assert.IsTrue(lottery.HasFireBall);
                bool hasFireBallNumber = lottery.FireBallLottery.HasFireBallNumber(drawDate, finishedLotteries);
                Assert.IsFalse(hasFireBallNumber);

                bool isNoAction = lottery.IsScheduleMarkedAsNoActionAt(drawDate);
                bool isRegraded = lottery.IsRegraded(drawDate);
                Assert.IsFalse(isNoAction);
                Assert.IsTrue(isRegraded);
            }
        }

        [TestMethod]
        public void Fireball_Ticket_Resend_Test()
        {
            QueueInMemory queue = new QueueInMemory();
            Integration.Kafka = queue;

            DateTime now = new DateTime(2024, 08, 29, 00, 00, 00);
            decimal founds = 80000;
            Company company = new Company();
            company.Accounting = new RealAccounting();
            CurrenciesTest.AddCurrencies();
            company.Sales.CreateStore(1, "Test Store").MakeCurrent();

            Domain domain = company.Sales.CreateDomain(false, 1, "localhost", PaymentChannels.Agents.INSIDER);
            Player player = company.GetOrCreateCustomerById("*********").Player;
            Customer customer = company.CustomerByPlayer(player);
            customer.Identifier = "*********";

            var lotteries = company.LotteryGamesPool.PicksLotteryGame;
            State s = lotteries.GetOrCreateState("GA", "Georgia");
            LotteryPick<Pick5> lottery = (LotteryPick<Pick5>)lotteries.GetOrCreateLottery(5, s);
            lottery.TurnOnFireBall(false, now, "N/A");
            lottery.ForEach(12, 00, "123456", now, "N/A");

            // Create a product and buy the ticket
            Product ticketPick3StraightProd = lotteries.GetOrCreateProductById(1);
            ticketPick3StraightProd.Description = "Ticket pick 5 straight";
            ticketPick3StraightProd.Price = 1;
            string dates = "08/29/2024";
            string states = "GA,GA";
            string hours = "12:00 PM,12:00 PM";
            string withFireballs = "true,false";
            string strIsPresentPlayerBeforeToCloseStore = "true,true";
            string strUseNextDate = "false,false";
            NextDatesAccumulator nextDatesAccumulator = new NextDatesAccumulator(lotteries, domain, now, "5", dates, states, hours, withFireballs, strIsPresentPlayerBeforeToCloseStore, strUseNextDate);
            OrderCart myOrder = (OrderCart)company.GetNewOrder(customer);

            ExcludeSubtickets excludedSubtickets = new ExcludeSubtickets();
            string numbers = "";
            string selectionMode = "MultipleInputSingleAmount";
            decimal ticketAmount = 1;
            string includedNumbersForInput = "11119";
            string gameType = "Straight";
            Order order = company.CreateTicketOrder(lotteries, player, states, dates, withFireballs, numbers, selectionMode, ticketAmount, includedNumbersForInput, excludedSubtickets, gameType, myOrder, ticketPick3StraightProd, nextDatesAccumulator, domain);
            company.AddOrders(myOrder, founds);
            int orderNumber = order.Number;
            order = company.OrderByNumber(orderNumber);
            order.AuthorizationId = 123;
            var marketplace = new Marketplace(company, "CR");
            var agent = marketplace.AddAgent("1");
            myOrder.Agent = (GamesEngine.Exchange.Agent)agent;
            company.SaleTickets(true, myOrder, now, founds, 1, domain);
            Assert.AreEqual(1, orderNumber);

            // gradear
            string winnerNumber = "11119";
            int fireBallNumber = 9;
            DateTime drawDate = new DateTime(2024, 08, 29, 12, 00, 00);
            var report = lottery.DrawPicks(drawDate, winnerNumber, fireBallNumber, now, false, "Erick", false);
            Assert.AreEqual(2, report.TotalTickets);
            Assert.AreEqual(2, report.TotalWinners);
            Assert.AreEqual(90000.0M + 45000.0M + 9000.0M, report.TotalPrize);

            // ExistsPostedDrawInMemory
            bool existsDraw = lottery.ExistsPostedDrawInMemory(drawDate);
            Assert.IsTrue(existsDraw);

            // WasAlreadyConfirmed
            bool wasAlreadyConfirmed = lottery.WasAlreadyConfirmed(drawDate);
            Assert.IsFalse(wasAlreadyConfirmed);

            // Resend
            var gradeFreeFormWagers = lottery.CreateMessagesToResendDraw(drawDate, now);
            Assert.AreEqual(2, gradeFreeFormWagers.Count());
        }

        [TestMethod]
        public void Fireball_Ticket_Resend_ARTEMIS_Test()
        {
            QueueInMemory queue = new QueueInMemory();
            Integration.Kafka = queue;

            DateTime now = new DateTime(2024, 08, 29, 00, 00, 00);
            decimal founds = 80000;
            Company company = new Company();
            company.Accounting = new RealAccounting();
            CurrenciesTest.AddCurrencies();
            company.Sales.CreateStore(1, "Test Store").MakeCurrent();

            Domain domain = company.Sales.CreateDomain(false, 1, "localhost", PaymentChannels.Agents.INSIDER);
            Player player = company.GetOrCreateCustomerById("*********").Player;
            Customer customer = company.CustomerByPlayer(player);
            customer.Identifier = "*********";

            var lotteries = company.LotteryGamesPool.PicksLotteryGame;
            State s = lotteries.GetOrCreateState("GA", "Georgia");
            LotteryPick<Pick5> lottery = (LotteryPick<Pick5>)lotteries.GetOrCreateLottery(5, s);
            lottery.TurnOnFireBall(false, now, "N/A");
            lottery.ForEach(12, 00, "123456", now, "N/A");

            // Create a product and buy the ticket
            Product ticketPick3StraightProd = lotteries.GetOrCreateProductById(1);
            ticketPick3StraightProd.Description = "Ticket pick 5 straight";
            ticketPick3StraightProd.Price = 1;
            string dates = "08/29/2024";
            string states = "GA,GA";
            string hours = "12:00 PM,12:00 PM";
            string withFireballs = "true,false";
            string strIsPresentPlayerBeforeToCloseStore = "true,true";
            string strUseNextDate = "false,false";
            NextDatesAccumulator nextDatesAccumulator = new NextDatesAccumulator(lotteries, domain, now, "5", dates, states, hours, withFireballs, strIsPresentPlayerBeforeToCloseStore, strUseNextDate);
            OrderCart myOrder = (OrderCart)company.GetNewOrder(customer);

            ExcludeSubtickets excludedSubtickets = new ExcludeSubtickets();
            string numbers = "";
            string selectionMode = "MultipleInputSingleAmount";
            decimal ticketAmount = 1;
            string includedNumbersForInput = "11119";
            string gameType = "Straight";
            Order order = company.CreateTicketOrder(lotteries, player, states, dates, withFireballs, numbers, selectionMode, ticketAmount, includedNumbersForInput, excludedSubtickets, gameType, myOrder, ticketPick3StraightProd, nextDatesAccumulator, domain);
            company.AddOrders(myOrder, founds);
            int orderNumber = order.Number;
            order = company.OrderByNumber(orderNumber);
            order.AuthorizationId = 123;
            var marketplace = new Marketplace(company, "CR");
            var agent = marketplace.AddAgent("1");
            myOrder.Agent = (GamesEngine.Exchange.Agent)agent;
            company.SaleTickets(true, myOrder, now, founds, 1, domain);
            Assert.AreEqual(1, orderNumber);

            // gradear
            string winnerNumber = "11119";
            int fireBallNumber = 9;
            DateTime drawDate = new DateTime(2024, 08, 29, 12, 00, 00);
            var report = lottery.DrawPicks(drawDate, winnerNumber, fireBallNumber, now, false, "Erick", false);
            Assert.AreEqual(2, report.TotalTickets);
            Assert.AreEqual(2, report.TotalWinners);
            Assert.AreEqual(90000.0M + 45000.0M + 9000.0M, report.TotalPrize);

            // ExistsPostedDrawInMemory
            bool existsDraw = lottery.ExistsPostedDrawInMemory(drawDate);
            Assert.IsTrue(existsDraw);

            // WasAlreadyConfirmed
            bool wasAlreadyConfirmed = lottery.WasAlreadyConfirmed(drawDate);
            Assert.IsFalse(wasAlreadyConfirmed);

            // Resend
            var gradeFreeFormWagers = lottery.ResendToExternalAccounting(drawDate, now, itIsThePresent: false, "N/A");
            Assert.AreEqual(2, gradeFreeFormWagers.Count());
        }
    }
}
