using Elastic.Apm.AspNetCore;
using Elastic.Apm.NetCoreAll;
using Elasticsearch.Net;
using ExternalServices;
using GamesEngine.MessageQueuing;
using GamesEngine.Settings;
using GamesEngineMocks;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.HttpOverrides;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Newtonsoft.Json;
using Puppeteer.EventSourcing;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Globalization;
using System.Threading;

namespace KnowYourCustomerAPI
{
    public class Startup
    {
        public Startup(IWebHostEnvironment env, IConfiguration configuration)
        {
            var builder = new ConfigurationBuilder()
                .SetBasePath(env.ContentRootPath)
                .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true)
                .AddJsonFile("customization/appsettings.accounting.json", optional: true)
                .AddJsonFile("customization/appsettings.security.json", optional: true)
                .AddJsonFile("customization/appsettings.kafka.json", optional: true)
                .AddJsonFile("customization/appsettings.error_emails.json", optional: true)
                .AddJsonFile("customization/appsettings.apm.json", optional: true)
                .AddJsonFile("secrets/appsettings.secrets.json", optional: true);
            Configuration = builder.Build();
            Environment = env;
        }

        public IConfiguration Configuration { get; }
        public IWebHostEnvironment Environment { get; }

        // This method gets called by the runtime. Use this method to add services to the container.
        public void ConfigureServices(IServiceCollection services)
        {
            int minWorker, minIOC;
            ThreadPool.GetMinThreads(out minWorker, out minIOC);
            int maxWorker, maxIOC;
            ThreadPool.GetMaxThreads(out maxWorker, out maxIOC);

            Console.WriteLine($@" minWorker:{minWorker} minIOC:{minIOC} maxWorker:{maxWorker} maxIOC:{maxIOC}");

            /*Security*/
            Security.Configure(services, Configuration);
            /*Security*/

            services.AddMvc(options => options.EnableEndpointRouting = false).AddNewtonsoftJson();

            services.AddSingleton<IHttpContextAccessor, HttpContextAccessor>();
            var biIntegration = Configuration.GetSection("BIIntegration");

            Integration.Configure(KafkaMessage.Prefix.NoTransacction, biIntegration);

            ErrorsSender.Configure(Configuration.GetSection("ErrorsSender"), Environment.IsProduction());
            Integration.ConfigureAPM(Configuration);

            var messageOriginId = Configuration.GetValue<string>("MessageOriginId");
            if (string.IsNullOrEmpty(messageOriginId) && !int.TryParse(messageOriginId, out _)) throw new Exception("MessageOriginId its requeried in the appsettings.");
            MessageCatalog.AssingMessageOriginId(Convert.ToInt32(messageOriginId));
        }

        // This method gets called by the runtime. Use this method to configure the HTTP request pipeline.
        public void Configure(IApplicationBuilder app, IWebHostEnvironment env)
        {
            if (Integration.UseAPM)
            {
                app.UseElasticApm(Configuration);
            }
            if (env.IsDevelopment())
            {
                app.UseDeveloperExceptionPage();
            }
            else
            {
                if (String.IsNullOrEmpty(Integration.Db.DBSelected))
                {
                    throw new Exception("Db configuration its required.");
                }
                if (!Integration.UseKafka)
                {
                    throw new Exception("This project must use kafka integration to show data.");
                }
            }
            var sectionDairy = Configuration.GetSection("DBDairy");
            var mySQL = sectionDairy.GetSection("ConnectionStrings").GetValue<string>("MySQL");
            var sqlServer = sectionDairy.GetSection("ConnectionStrings").GetValue<string>("SQLServer");
            var dbSelected = sectionDairy.GetValue<string>("DBSelected");
            var scriptBeforeRecovering = Configuration.GetValue<string>("ActionsBeforeRecovering");

            string elkServer = Configuration.GetSection("ELKIntegration").GetValue<string>("elkserver");
            HttpELKClient.Configure(elkServer);

            DBDairy dbDairy = new DBDairy();
            dbDairy.DBSelected = dbSelected;
            Integration.DbDairy = dbDairy;

            var responseMapping = HttpELKClient.GetInstance().CreateDateMapping(HttpELKClient.CustomersIndex, "creation_date");
            if (200 != (int)responseMapping.StatusCode)
            {
                Debug.WriteLine($"Mappings for '{HttpELKClient.CustomersIndex}' index already exist.");
            }
            responseMapping = HttpELKClient.GetInstance().CreateDateMapping(HttpELKClient.CustomersLogIndex, "date");
            if (200 != (int)responseMapping.StatusCode)
            {
                Debug.WriteLine($"Mappings for '{HttpELKClient.CustomersLogIndex}' index already exist.");
            }

            if (dbSelected == DatabaseType.MySQL.ToString())
            {
                KnowYourCustomerAPI.KnowYourCustomer.EventSourcingStorage(DatabaseType.MySQL, mySQL, scriptBeforeRecovering, AccountingSettings.NeedsUniqueIdentifierForPaymentHub);
            }
            else if (dbSelected == DatabaseType.SQLServer.ToString())
            {
                KnowYourCustomerAPI.KnowYourCustomer.EventSourcingStorage(DatabaseType.SQLServer, sqlServer, scriptBeforeRecovering, AccountingSettings.NeedsUniqueIdentifierForPaymentHub);
            }
            else
            {
                Integration.Kafka.OffSetResetToLatest();
                int numberOfTheMockConfigured = Convert.ToInt32(Configuration["MockToStart"]);

                var DOTNET_RUNNING_IN_CONTAINER = bool.Parse(System.Environment.GetEnvironmentVariable("DOTNET_RUNNING_IN_CONTAINER") ?? "false");
                if (DOTNET_RUNNING_IN_CONTAINER)
                {
                    KnowYourCustomerAPI.KnowYourCustomer.EventSourcingStorage(DatabaseType.MySQL, mySQL, scriptBeforeRecovering, AccountingSettings.NeedsUniqueIdentifierForPaymentHub);
                    HttpELKClient.GetInstance().CreateMockCustomer();
                }
                RunMock(numberOfTheMockConfigured);
            }

            /*Security*/
            app.UseAuthentication();
            /*Security*/

            app.UseMvc();

            if (Integration.UseKafka)
            {
                new Consumers().CreateConsumerForTopics();
            }

            ForwardedHeadersOptions options = new ForwardedHeadersOptions();
            options.ForwardedHeaders = ForwardedHeaders.XForwardedFor;
            app.UseForwardedHeaders(options);
        }

        void RunMock(int index = -1)
        {
            switch (index)
            {
                case 0:
                    KnowYourCustomerMock.Init(KnowYourCustomerAPI.KnowYourCustomer.Actor);
                    break;
                case 1:
                    KnowYourCustomerMock.PlayerOfLines(KnowYourCustomerAPI.KnowYourCustomer.Actor);
                    break;
                default:
                    throw new Exception($"The mock {index} its not implemented yet.");
            }
        }
    }
}
