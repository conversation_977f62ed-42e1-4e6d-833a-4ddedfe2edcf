﻿{
  "version": "",
  "DBDairy": {
    "ConnectionStrings": {
      "MySQL": "persistsecurityinfo=True;port=3306;Server=localhost;Database=exchange;user id=root;password=*********;SslMode=none",
      "SQLServer": "data source=localhost;initial catalog=lottodairy;user id=sa;password=******;multipleactiveresultsets=True"
    },
    "DBSelected": ""
  },
  "ActionsBeforeRecovering": "",
  "Security": {
    "ForceAuthority": true,
    "exchange": {
      "Api": "http://security.qa9.ncubo.com:8080/auth/admin/realms/exchange",
      "Authority": "http://security.qa9.ncubo.com:8080/auth/realms/exchange",
      "Audience": "servers",
      "Certificate": "MIICnzCCAYcCBgFyujBa9jANBgkqhkiG9w0BAQsFADATMREwDwYDVQQDDAhleGNoYW5nZTAeFw0yMDA2MTUyMjUyMTBaFw0zMDA2MTUyMjUzNTBaMBMxETAPBgNVBAMMCGV4Y2hhbmdlMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAol797HBHzcISwUe+iALK2Ecmd/H/0hQu6+ZlriwupIsXi8bhPhd+dvfBaR2/GQ+zkjy7mCg39dpZPS4A70wYZdgD0oFcwcP9oGTUYqWhEHjWegkjSERpv0ayCiSqDtyey0sqnferfal6lacgb84KXawNPwqJrY8OURlRfwVNXrPI7LEMYEv5li/z1dQT7FqOY/IokwwtzPMI+EKshpVcrA/eT4Q221jNKtmKudlqLflTsRJiNDACLioed9jocV3sbGP8pZOOnHJlAHXwp/L4s+1xbpI9tU0UKOtAIV0kAueRCSkZ/bAl/+ycwXUiX/8TEghzb6EnD19b/sD0/r/K0QIDAQABMA0GCSqGSIb3DQEBCwUAA4IBAQBXd+i5toytISa2jw4b7Es7tYLfx0zmkQRIJcyv04whMJT4tDoxmTISMrKvF6Tj0AMCz/PdML3CA51k4ts0nPYSqDVBfowm1BRh3A9Rt/yX47VKXr3z/RQW7BIOqDDDBqwN3F9erLq1tglCpwtTRbv1njxeue1XG8G1yTPhkQVeaLh1Ji+6t3TGOUNsXqMyWx8xZIwmWOJsBOR9raz1rI+Pl4KO5RvCpkcTUuddeRNyE1wac+NecjkFI5JI/RV5RMtzr4wtCkrp34CWEPyflCTmPTIqGy214OfN7YuWc7wznwPtfx79LWJLKz5v+B5aKwI9W8xlt+HodlckqoksFDVf",
      "PublicKey": "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAol797HBHzcISwUe+iALK2Ecmd/H/0hQu6+ZlriwupIsXi8bhPhd+dvfBaR2/GQ+zkjy7mCg39dpZPS4A70wYZdgD0oFcwcP9oGTUYqWhEHjWegkjSERpv0ayCiSqDtyey0sqnferfal6lacgb84KXawNPwqJrY8OURlRfwVNXrPI7LEMYEv5li/z1dQT7FqOY/IokwwtzPMI+EKshpVcrA/eT4Q221jNKtmKudlqLflTsRJiNDACLioed9jocV3sbGP8pZOOnHJlAHXwp/L4s+1xbpI9tU0UKOtAIV0kAueRCSkZ/bAl/+ycwXUiX/8TEghzb6EnD19b/sD0/r/K0QIDAQAB",
      "DefaultUser": "exchangeadmin",
      "DefaultPassword": "A6m1n2020"
    }
  },
  "Logging": {
    "IncludeScopes": false,
    "Debug": {
      "LogLevel": {
        "Default": "Warning"
      }
    },
    "Console": {
      "LogLevel": {
        "Default": "Warning"
      }
    }
  },
  "BIIntegration": {
    "useKafka": false,
    "useDb": true,
    "kafka": {
      "server": "localhost:9092",
      "topicForLottoGrading": "LottoGrading",
      "topicForTransacctions": "Transactions",
      "topicForGrades": "LottoGrades",
      "topicForDrawings": "LottoDrawings",
      "topicForRecents": "Recents",
      "topicForDomains": "Domains",
      "topicForStoreRegistration": "StoreRegistration",
      "topicForCustomSettings": "CustomSettings",
      "topicForCatalog": "Catalog",
      "topicForCustomers": "Customers",
      "topicForNotifications": "Notifications",
      "topicForProfanity": "Profanity",
      "topicForScheduler": "Scheduler",
      "topicForPrizes": "LottoPrizes",
      "topicForFragments": "Fragments",
      "topicForPayFragments": "PayFragments",
      "topicForMovements": "Movements",
      "topicForDeposits": "Deposits",
      "topicForWithdrawals": "Withdrawals",
      "topicForChallenges": "Challenges",
      "topicForBalances": "Balances",
      "topicForIncomingOperations": "IncomingOperations",
      "topicForOutgoingOperations": "OutgoingOperations",
      "topicForGuardian": "guardian",
      "topicForLinesTags": "LinesTags",
      "topicForLinesScores": "LinesScores",
      "topicForLinesGrading": "LinesGrading",
      "topicForLinesETL": "EtlInfo",
      "group": "games"
    },
    "DBHistorical": {
      "ConnectionStrings": {
        "MySQL": "persistsecurityinfo=True;port=3306;Server=localhost;Database=exchange;user id=root;password=*********;SslMode=none",
        "SQLServer": "data source=localhost;initial catalog=lottohistorical;user id=sa;password=******;multipleactiveresultsets=True"
      },
      "DBSelected": "MySQL"
    }
  },
  "ErrorsSender": {
    "SmtpServer": "smtp.gmail.com",
    "SmtpPort": 587,
    "SmtpUsername": "<EMAIL>",
    "SmtpPassword": "Rdiaz123",
    "UseSender": true,
    "SendTo": ""
  },
  "ELKIntegration": {
    "elkserver": "http://localhost:9200"
  }
}
