﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using System.Text;
using System.Threading.Tasks;
using GamesEngine.Games;
using GamesEngine.Middleware;
using GamesEngine.Middleware.Providers;
using LinesETLAPI;
using LinesETLAPI.Controllers;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json.Linq;

namespace LinesETLAPI.Controllers
{
	public class JobsController : AuthorizeController
	{
		[HttpPut("api/jobs")]
        [Authorize(Roles = "devops")]//VERIFY_ROLES
        public IActionResult ResgiterJobs()
		{

			Jobs jobs = Jobs.Instance()
			.Resgiter(
				new Bet365Updater(),
				new BetFairUpdater());

			return Ok();
		}

		[HttpGet("api/jobs/betfair/leagues")]
        [Authorize(Roles = "devops")]//VERIFY_ROLES
        public IActionResult TriggerLeagues(int sportId=0)
		{
			const string name = "BetFair";
			Jobs jobs = Jobs.Instance();
			BetFairUpdater updater = (BetFairUpdater)jobs.SearchByName(name);

			JArray response = updater.UpdateLeague(true, null);

			return Ok(response.ToString());
		}
		[HttpPost("api/jobs/all/leagues/matches")]
        [Authorize(Roles = "devops")]//VERIFY_ROLES
        public IActionResult TriggerTeams([FromBody] TriggerTeamsPayload body)
		{
			if (body == null) return BadRequest("Body is required");
			if (body.LeagueIdTheProvider == null || body.LeagueIdTheProvider.Length == 0) return BadRequest($"{nameof(body.LeagueIdTheProvider)} is required");
			if (body.ProviderIds == null || body.ProviderIds.Length == 0) return BadRequest($"{nameof(body.ProviderIds)} is required");
			if (body.SportIds == null || body.SportIds.Length == 0) return BadRequest($"{nameof(body.SportIds)} is required");
			if (body.LeagueIdTheProvider.Length != body.ProviderIds.Length || body.LeagueIdTheProvider.Length != body.SportIds.Length) return BadRequest($"{nameof(body.LeagueIdTheProvider)}, {nameof(body.ProviderIds)}, {nameof(body.SportIds)} must have the same number of elements.");

			Jobs jobs = Jobs.Instance();
			JArray result = new JArray();
			for (int i=0;i< body.ProviderIds.Length;i++)
			{
				int providerId = body.ProviderIds[i];
				int sportId = body.SportIds[i];
				string leagueIdTheProvider = body.LeagueIdTheProvider[i];

				Provider provider = ProvidersCollection.Instance().ById(providerId);
				BetFairUpdater updater = (BetFairUpdater)jobs.SearchByProvider(provider);

				JArray response = updater.UpdateTeam(true, leagueIdTheProvider, sportId);
				result.Add(response);
			}

			return Ok(result.ToString());
		}
		[HttpPost("api/jobs/all/leagues/matches/lines")]
        [Authorize(Roles = "devops")]//VERIFY_ROLES
        public IActionResult TriggerMatchLines([FromBody] TriggerLinesPayload body)
		{
			Jobs jobs = Jobs.Instance();
			for (int i = 0; i < body.ProviderIds.Length; i++)
			{
				int providerId = body.ProviderIds[i];

				Provider provider = ProvidersCollection.Instance().ById(providerId);
				BetFairUpdater updater = (BetFairUpdater)jobs.SearchByProvider(provider);

				updater.UpdateLines(true, body.MatchIdsInTheProvider, body.TournamentId, body.GameId);
			}

			return Ok();
		}

		[HttpGet("api/jobs/betfair/lines")]
        [Authorize(Roles = "devops")]//VERIFY_ROLES
        public IActionResult ListLines(int tournamentId, int gameId)
		{
			const string name = "BetFair";
			Jobs jobs = Jobs.Instance();
			BetFairUpdater updater = (BetFairUpdater)jobs.SearchByName(name);

			NormalizedProviderResponse result=null; 
			updater.UpdateLines(true, tournamentId, gameId, (NormalizedProviderResponse normalResponse) =>{ result = normalResponse; });

			return Ok(result);
		}

		[HttpPut("api/jobs/betfair/lines")]
        [Authorize(Roles = "devops")]//VERIFY_ROLES
        public IActionResult UpdateExistingLines()
		{
			const string name = "BetFair";
			Jobs jobs = Jobs.Instance();
			BetFairUpdater updater = (BetFairUpdater)jobs.SearchByName(name);

			NormalizedProviderResponse result = null;
			updater.SyncLines(true, (NormalizedProviderResponse normalResponse) => { result = normalResponse; });

			return Ok(result);
		}
	}

	[DataContract(Name = "TriggerTeamsPayload")]
	public class TriggerTeamsPayload
	{
		[DataMember(Name = "providerIds")]
		public int[] ProviderIds { get; set; }
		[DataMember(Name = "leagueIdTheProvider")]
		public string[] LeagueIdTheProvider { get; set; }
		[DataMember(Name = "sportIds")]
		public int[] SportIds { get; set; }
	}

	[DataContract(Name = "TriggerLinesPayload")]
	public class TriggerLinesPayload
	{
		[DataMember(Name = "providerIds")]
		public int[] ProviderIds { get; set; }
		[DataMember(Name = "matchIdsInTheProvider")]
		public string[] MatchIdsInTheProvider { get; set; }
		[DataMember(Name = "tournamentId")]
		public int TournamentId { get; set; }
		[DataMember(Name = "gameId")]
		public int GameId { get; set; }
	}
}
