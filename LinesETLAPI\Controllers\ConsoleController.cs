using GamesEngine.Settings;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace LinesETLAPI.Controllers
{
	public class ConsoleController : AuthorizeController
	{
		public static bool liveness = false;
		public static bool readyness = false;
		[HttpPost("console/liveness")]
		[AllowAnonymous]
		public IActionResult Liveness()
		{
			return Ok(readyness);
		}

		[HttpPost("console/readyness")]
		[AllowAnonymous]
		public IActionResult Readyness()
		{
			return Ok(readyness);
		}

		[HttpPost("console/command")]
		[Authorize(Roles = "devops")]
		public async Task<IActionResult> processCommandAsync()
		{
			string body = "";

			using (StreamReader reader = new StreamReader(Request.Body, Encoding.UTF8))
			{
				body = await reader.ReadToEndAsync();
			}

			if (body == null) return NotFound("Body is required");
			if (String.IsNullOrWhiteSpace(body)) return NotFound($"Parameter {nameof(body)} is required");

			var result = await LinesETLAPI.LinesETL.PerformCmdAsync(HttpContext, body);
			return result;
		}

		[HttpPost("console/query")]
		[Authorize(Roles = "devops")]
		public async Task<IActionResult> processQueryAsync()
		{
			string body = "";

			using (StreamReader reader = new StreamReader(Request.Body, Encoding.UTF8))
			{
				body = await reader.ReadToEndAsync();
			}

			if (body == null) return NotFound("Body is required");
			if (String.IsNullOrWhiteSpace(body)) return NotFound($"Parameter {nameof(body)} is required");

			var result = await LinesETLAPI.LinesETL.PerformQryAsync(HttpContext, body);
			return result;
		}


		[HttpGet("console/jobs")]
		[Authorize(Roles = "devops")]
		public IActionResult GetJobs()
		{
			return Ok(Integration.TimersLoaded.Jobs());
		}

		[HttpGet("console/ping")]
		[AllowAnonymous]
		public IActionResult Ping()
		{
			return Ok("pong");
		}
	}
}

