using Calendarizador.GamesEngine.Time.Schedulers;
using GamesEngine.Bets;
using GamesEngine.Business;
using GamesEngine.Domains;
using GamesEngine.Finance;
using GamesEngine.Gameboards;
using GamesEngine.Gameboards.Lotto;
using GamesEngine.Location;
using GamesEngine.MessageQueuing;
using GamesEngine.PurchaseOrders;
using GamesEngine.RealTime;
using GamesEngine.RealTime.Events;
using GamesEngine.Settings;
using GamesEngine.Time;
using Puppeteer.EventSourcing;
using Puppeteer.EventSourcing.Libraries;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using static GamesEngine.Games.Lotto.TicketsOfDraw;

namespace GamesEngine.Games.Lotto
{
    public enum IdOfLottery
    {
        KN = 0,
        P2 = 1,
        P3 = 2,
        P4 = 3,
        P5 = 4,
        PB = 5,
        TZ = 6
    }

    public enum IdOfLotteryGame
    {
        Triz,
        Picks
    }

    public static class IdOfLotteryExtensions
    {
        public static bool EqualsTo(this IdOfLottery idOfPick, string idOfPickName)
        {
            switch (idOfPick)
            {
                case IdOfLottery.P3:
                    return idOfPickName == "P3";
                case IdOfLottery.P4:
                    return idOfPickName == "P4";
                case IdOfLottery.P2:
                    return idOfPickName == "P2";
                case IdOfLottery.P5:
                    return idOfPickName == "P5";
                case IdOfLottery.PB:
                    return idOfPickName == "PB";
                case IdOfLottery.KN:
                    return idOfPickName == "KN";
                default:
                    return false;
            }
        }

        public static IdOfLottery ParseFromGameType(string gameType)
        {
            switch (gameType)
            {
                case "3":
                case "pick3":
					return IdOfLottery.P3;
				case "4":
                case "pick4":
					return IdOfLottery.P4;
				case "2":
                case "pick2":
					return IdOfLottery.P2;
				case "5":
                case "pick5":
					return IdOfLottery.P5;
				case "PB":
                case "powerball":
					return IdOfLottery.PB;
				case "KN":
                case "keno":
					return IdOfLottery.KN;
				case "TZ":
				case "triz":
                    return IdOfLottery.TZ;
				default:
					throw new GameEngineException($"Invalid game type {gameType}");
            }
        }

		public static string TextValue(this IdOfLottery idOfLottery)
		{
			switch (idOfLottery)
			{
                case IdOfLottery.P3:
                    return "3";
                case IdOfLottery.P4:
                    return "4";
                case IdOfLottery.P2:
                    return "2";
                case IdOfLottery.P5:
                    return "5";
                case IdOfLottery.PB:
                    return "PB";
                case IdOfLottery.KN:
                    return "KN";
                case IdOfLottery.TZ:
                    return "TZ";
                default:
                    throw new GameEngineException($"Invalid game type {idOfLottery}");
            }
		}
    }

    public enum TypeNumberSequence
    {
        ENDING,
        BEGINNING
    }

    [Puppet]
	internal abstract class Lottery : Objeto
    {
		protected readonly State state;
		protected readonly LotteryDraws lotteryDraws;

        private readonly EventsCalendar<Schedule> calendar;

        protected List<Schedule> schedules = new List<Schedule>();//TODO: tratar de hacer readonly
        private readonly Dictionary<Schedule, StringBuilder> schedulesLog = new Dictionary<Schedule, StringBuilder>();

        internal virtual LotteryDraws LotteryDraws
		{
			get
			{
				return lotteryDraws;
			}
		}

		private readonly Tickets tickets;
		private readonly Pool pool;
		private readonly LotteryGame lotteryGame;

		internal Lottery(LotteryGame lotteryGame, State state)
		{
			if (state == null) throw new ArgumentNullException(nameof(state));
			if (!state.Enable) throw new GameEngineException("You can not create a Lottery with a disable state.");
			this.state = state;
			this.lotteryDraws = new LotteryDraws(this);
			this.tickets = new Tickets(this);
			this.lotteryGame = lotteryGame;
            this.calendar = lotteryGame.Calendar;

            this.pool = Company.Book.CreateNewPool($"Ticket's pool for {state.Abbreviation}");

			if (!this.lotteryGame.OfficialLinks.Exists(state))
			{
				this.lotteryGame.OfficialLinks.Add(state, "");
			}
		}

        internal State State
		{
			get { return state; }
		}

		internal Pool Pool
		{
			get { return pool; }
		}

		internal IEnumerable<Schedule> Schedules
		{
			get { return schedules; }
		}

		internal string DefaultValueForEmptyWinnerNumber()
		{
			return new string(GamesEngine.Games.Lotto.LotteryDraw.DEFAULT_VALUE_FOR_EMPTY_WINNER_NUMBER[0], this.NumberOfDigits());
		}

        internal int NumberOfDigits()
        {
            int result;
            if (this is LotteryPick<Pick3>)
                result = 3;
            else if (this is LotteryPick<Pick4>)
                result = 4;
            else if (this is LotteryPick<Pick2>)
                result = 2;
            else if (this is LotteryPick<Pick5>)
                result = 5;
            else if (this is LotteryPowerball)
                result = 12;
            else if (this is LotteryKeno)
                result = 20;
			else if (this is LotteryTriz)
				result = 5;
            else
                throw new GameEngineException("Invalid pick for this Lottery");
            return result;
        }

        internal GenericGameType GetGameType()
		{
            GenericGameType result;
			if (this is LotteryPick<Pick3>)
				result = GenericGameType.P3;
			else if (this is LotteryPick<Pick4>)
				result = GenericGameType.P4;
			else if (this is LotteryPick<Pick2>)
				result = GenericGameType.P2;
			else if (this is LotteryPick<Pick5>)
				result = GenericGameType.P5;
			else if (this is LotteryPowerball)
				result = GenericGameType.PB;
            else if (this is LotteryTriz)
                result = GenericGameType.TZ;
			else
				throw new GameEngineException("Invalid game type for this Lottery");
			return result;
		}

		internal IdOfLottery IdOfLottery
		{
			get
			{
				IdOfLottery result;
				if (this is LotteryPick<Pick3>)
					result = IdOfLottery.P3;
				else if (this is LotteryPick<Pick4>)
					result = IdOfLottery.P4;
				else if (this is LotteryPick<Pick2>)
					result = IdOfLottery.P2;
				else if (this is LotteryPick<Pick5>)
					result = IdOfLottery.P5;
				else if (this is LotteryTriz)
					result = IdOfLottery.TZ;
				else if (this is LotteryPowerball)
					result = IdOfLottery.PB;
				else if (this is LotteryKeno)
					result = IdOfLottery.KN;
				else
					throw new GameEngineException("Invalid pick for this Lottery");

				return result;
			}
		}

		internal Lottery RootLottery => Company.LotteryGamesPool.RootLottery(this);

        internal TypeNumberSequence GetTypeNumberSequence()
		{
            TypeNumberSequence result = TypeNumberSequence.ENDING;
			if (this is LotteryTriz) return result;

            Lottery rootLottery = RootLottery;
			if (rootLottery is LotteryTriz lotteryTriz)
			{
                result = lotteryTriz.GetTypeNumberSequence(this);
            }

			return result;
        }

        internal string TypeNumberSequenceAsText() => GetTypeNumberSequence().ToString();

        internal virtual IEnumerable<Schedule> AllEnabledSchedules(DateTime date, Domain domain)
		{
			var enabledSchedules = new List<Schedule>();
			foreach (var schedule in schedules)
			{
				if (state.Enable && schedule.IsEnabled() && IsEnabledDraw(schedule.ToDateTime(date), domain))
				{
					enabledSchedules.Add(schedule);
				}
			}
			return enabledSchedules;
		}

		internal Tickets Tickets
		{
			get
			{
				return tickets;
			}
		}

        internal virtual IEnumerable<PendingDraw> PendingDraws(HourMinute hour)
        {
            var result = this.Tickets.PendingDraws().Where(x => x.Schedule.Hour.Equals(hour));
            return result;
        }

        internal virtual bool ExistPendingTicketsAt(DateTime date)
		{
			var result = tickets.ExistPendingTicketsAt(date);
			return result;
		}

        internal virtual IEnumerable<Ticket> SearchTicketByNumber(int ticketNumber)
        {
            var result = Tickets.SearchTicketByNumber(ticketNumber);
            return result;
        }

        internal PicksLotteryGame PicksLotteryGame//TRIZ revisar, deberia borrarse y solo debe existir LotteryGame
        {
			get
			{
				return lotteryGame as PicksLotteryGame;
			}
		}

        internal LotteryGame LotteryGame
        {
            get
            {
                return lotteryGame;
            }
        }

        internal Company Company
		{
			get
			{
				return this.lotteryGame.Company;
			}
		}

		protected internal void AddTicket(DateTime date, Ticket ticket)
		{
			if (ticket == null) throw new ArgumentNullException(nameof(ticket));
			if (!ExistsScheduleAt(date)) throw new GameEngineException($"There is not any Lottery scheduled for {date.ToString("MMMM dd, yyyy 'at' H:mm")}");
			if (date.Second != 0 || date.Millisecond != 0) throw new GameEngineException("Date of Lottery drawings can not have seconds");
			if (IsAlreadyGraded(date)) throw new GameEngineException($"Lottery scheduled for {date.ToString("MMMM dd, yyyy 'at' H:mm")} has already finish");
			if (lotteryDraws.IsMarkedAsNoAction(date)) throw new GameEngineException($"Lottery scheduled for {date.ToString("MMMM dd, yyyy 'at' H:mm")} was marked as no action");
			if (!state.Enable) throw new GameEngineException($"Lotto can not sell tickets because state {state.Abbreviation} is temporary disabled");

			tickets.Add(date, ticket);
		}

		internal virtual TicketsOfDraw TicketInMemory(DateTime date)
		{
			if (date == default(DateTime)) throw new GameEngineException("Date cannot have default value");

			var ticket = tickets.TicketsInMemory(date);
			return ticket;
		}

		internal virtual bool ExistsPostedDrawInMemory(DateTime drawDate)
        {
			var ticket = tickets.AnyTicketsInMemory(drawDate) && CanBeConfirmed(drawDate);
			return ticket;
		}

        internal virtual List<PayFragmentsMessage> ResendToExternalAccounting(DateTime drawDate, DateTime now, bool itIsThePresent, string employeeName)
		{
			if (drawDate == default(DateTime)) throw new GameEngineException("Date cannot have default value");
			if (now == default(DateTime)) throw new GameEngineException("Date cannot have default value");
			if (string.IsNullOrWhiteSpace(employeeName)) throw new ArgumentNullException(nameof(employeeName));

			var messages = tickets.CreateMessagesToResendTickets(drawDate, now);
            var payFragmentsMessages = messages.Select(m => new town.connectors.commons.PayFragmentsMessage
            {
                AdjustedLossAmount = m.AdjustedLossAmount,
                AdjustedWinAmount = m.AdjustedWinAmount,
				AgentId = m.AgentId,
				DailyFigureDate_YYYYMMDD = m.DailyFigureDate_YYYYMMDD,
				IsValidTicketNumber = m.IsValidTicketNumber,
				Outcome = m.Outcome,
				TicketNumber = m.TicketNumber,
				WagerNumber = m.WagerNumber
            }).ToList();
            var fragmentsMessages = new FragmentPaymentSendingToAgentMessages(payFragmentsMessages);
            Integration.Kafka.Send(itIsThePresent, $"{Integration.Kafka.TopicForFragmentPaymentsForAll}{KafkaMessage.SENDING_FRAGMENTS_CONSUMER_SUFFIX}", fragmentsMessages);
			return messages;
		}

        [Obsolete("This method is obsolete, use respective method for draw. for example DrawPicks or DrawPowerBall")]
		internal DrawingsSummaryReport LotteryDraw(DateTime date, string sequenceOfNumbers, DateTime now, bool itIsThePresent, string employeeName)
		{
			return LotteryDraw(date, sequenceOfNumbers, now, itIsThePresent, employeeName, false);
		}

        [Obsolete("This method is obsolete, use respective method for draw. for example DrawPicks or DrawPowerBall")]
        internal DrawingsSummaryReport LotteryDraw(DateTime date, string sequenceOfNumbers, DateTime now, bool itIsThePresent, string employeeName, bool notifyEvent)
		{
			//TODO: Verify if IsAssignableFrom is in correct form
			if (!(this.GetType().GetGenericTypeDefinition() == typeof(LotteryPick<>))) throw new GameEngineException($"This method can only be used for Picks lotteries");
			var result = LotteryDrawForPickOrPowerball(date, sequenceOfNumbers, 0, now, itIsThePresent, employeeName, notifyEvent);
			return result;
		}

		[Obsolete("This method is obsolete, use respective method for draw. for example DrawPicks or DrawPowerBall")]
		internal DrawingsSummaryReport LotteryDraw(DateTime date, string sequenceOfNumbers, int multiplierNumber, DateTime now, bool itIsThePresent, string employeeName)
		{
			if (!(multiplierNumber == 1 || multiplierNumber == 2 || multiplierNumber == 3 || multiplierNumber == 4 || multiplierNumber == 5 || multiplierNumber == 10)) throw new GameEngineException("Multiplier numbers is not valid.");
			//TODO: Verify if IsAssignableFrom is in correct form
			if (!this.GetType().IsAssignableFrom(typeof(LotteryPowerball))) throw new GameEngineException($"This method can only be used for Powerball lottery");
			var result = LotteryDrawForPickOrPowerball(date, sequenceOfNumbers, multiplierNumber, now, itIsThePresent, employeeName, false);
			return result;
		}
		
		internal bool IsAlreadyGraded(string drawIdPrefix, int[] sequenceOfNumbers, int multiplierNumber, int bulleye, DateTime now)
		{
			DateTime date = KenoNextDraw.IdPrefix2Date(drawIdPrefix);
			if (!(multiplierNumber == 1 || multiplierNumber == 2)) throw new GameEngineException("Multiplier numbers is not valid.");
			if (!sequenceOfNumbers.Any(x => x == bulleye)) throw new GameEngineException("Bulleye number is not valid.");
			if (date == default(DateTime)) throw new GameEngineException("Date cannot have default value");
			if (sequenceOfNumbers == null) throw new ArgumentNullException(nameof(sequenceOfNumbers));
			if (sequenceOfNumbers.Count() != 20) throw new GameEngineException($"{nameof(sequenceOfNumbers)} must have 20 items.");
			if (date.Second != 0 || date.Millisecond != 0) throw new GameEngineException("Date of Lottery drawings can not have seconds");
			if (sequenceOfNumbers.Length != sequenceOfNumbers.Distinct().Count()) throw new GameEngineException("There is repeated numbers.");

			LotteryKeno.CheckNumbersRepeated(sequenceOfNumbers);
			var numbers = LotteryKeno.SortNumbers(sequenceOfNumbers);
			var elapsedTimeToDraw = now.Subtract(date);
			this.lotteryGame.RecordElapsedTimeToDraw(elapsedTimeToDraw);
			string previousSequenceOfNumbers = string.Empty;
			bool isAlreadyGradedWithTheSameNumbers = false;

			string sequence = string.Join("", numbers.Select(n => $"{n.ToString("00")}"));

			var drawIsAlreadyGraded = lotteryDraws.IsRegraded(date) || lotteryDraws.IsGraded(date);

			return drawIsAlreadyGraded;
		}

		[Obsolete("This method is obsolete, use respective method for draw. for example DrawPicks or DrawPowerBall")]
		private DrawingsSummaryReport LotteryDrawForPickOrPowerball(DateTime date, string sequenceOfNumbers, int multiplierNumber, DateTime now, bool itIsThePresent, string employeeName, bool notifyEvent)
		{
			bool isPowerball = this.IdOfLottery == IdOfLottery.PB;
			if (date == default(DateTime)) throw new GameEngineException("Date cannot have default value");
			if (now == default(DateTime)) throw new GameEngineException("Date cannot have default value");
			if (String.IsNullOrWhiteSpace(sequenceOfNumbers)) throw new ArgumentNullException(nameof(sequenceOfNumbers));
			if (String.IsNullOrWhiteSpace(employeeName)) throw new ArgumentNullException(nameof(employeeName));
			if (date.Second != 0 || date.Millisecond != 0) throw new GameEngineException("Date of Lottery drawings can not have seconds");
			if (isPowerball && multiplierNumber == 0) throw new GameEngineException("Invalid Powerball multiplier number invocation");
			if (!isPowerball && multiplierNumber != 0) throw new GameEngineException("Invalid Pick invocation");

			var elapsedTimeToDraw = now.Subtract(date);
			this.lotteryGame.RecordElapsedTimeToDraw(elapsedTimeToDraw);
			string previousSequenceOfNumbers = string.Empty;
			LotteryDraw currentLotteryDraw = null;
			DrawingsSummaryReport report = null;
			bool isAlreadyGradedWithTheSameNumbers = false;
			try
			{
				previousSequenceOfNumbers = lotteryDraws.IsRegraded(date) || lotteryDraws.IsGraded(date) ? lotteryDraws.GetLotteryDraw(date).SequenceOfNumbers : string.Empty;
                isAlreadyGradedWithTheSameNumbers = IsAlreadyGradedWithTheSameNumbers(date, sequenceOfNumbers);

				if (lotteryDraws.IsDrawCurrentlyNoActionAt(date))
				{
					var log = $"{employeeName} reverts no action at {now}<br>";
					lotteryDraws.SetNoActionAsDisabled(date, now, employeeName, log);
				}

				if (!isAlreadyGradedWithTheSameNumbers)
				{
					if (isPowerball)
					{
						currentLotteryDraw = lotteryDraws.Draw(date, sequenceOfNumbers, multiplierNumber, now, employeeName);
					}
					else
					{
						currentLotteryDraw = lotteryDraws.Draw(date, sequenceOfNumbers, Lotto.LotteryDraw.WITHOUT_FIREBALL, now, employeeName);
					}

					if (tickets.ExistTicketsAt(date))
					{
						report = tickets.LotteryDraw(date, currentLotteryDraw, itIsThePresent, now, previousSequenceOfNumbers, employeeName);
					}

					lotteryGame.RemoveNumbersTracker(sequenceOfNumbers.Length, this, date);

                    bool canBeSendToKafka = true;
                    if (this.RootLottery is LotteryTriz && this is not LotteryTriz) canBeSendToKafka = false;

                    if (itIsThePresent && Integration.UseKafka && notifyEvent && canBeSendToKafka)
					{
                        var schedule = FindScheduleAt(date);
                        Integration.Kafka.Send(itIsThePresent, $"{KafkaMessage.LOTTERY_DRAW_CONSUMER_PREFIX}{Integration.Kafka.TopicForAllGrades}",
							new LotteryDrawGradeMessage(GetGameType(), state.Abbreviation, date, employeeName, schedule.UniqueId, sequenceOfNumbers));
					}
				}
			}
			catch (Exception e)
			{
				tickets.RollbackFromGradeToPending(date, currentLotteryDraw, now, previousSequenceOfNumbers);

				if (!string.IsNullOrWhiteSpace(previousSequenceOfNumbers))
				{
					var wasRegraded = previousSequenceOfNumbers[0] == '-';
					if (wasRegraded)
					{
						currentLotteryDraw?.UpdateToRegrade(currentLotteryDraw.WhoRegraded, now);
					}
					else
					{
						currentLotteryDraw?.Update(previousSequenceOfNumbers, employeeName, now);
					}
				}
				else
				{
					currentLotteryDraw?.RollbackGrade(employeeName, now);
				}
				throw e;
			}

			if (report == null) report = new PicksDrawingsSummaryReport(isAlreadyGradedWithTheSameNumbers);
			return report;
		}

        protected DrawingsSummaryReport ReleaseDraw(Func<LotteryDraw> gradeAction, DateTime date, string sequenceOfNumbers, DateTime now, bool itIsThePresent, string employeeName, bool notifyEvent)
		{
            if (date == default(DateTime)) throw new GameEngineException("Date cannot have default value");
            if (now == default(DateTime)) throw new GameEngineException("Date cannot have default value");
            if (String.IsNullOrWhiteSpace(sequenceOfNumbers)) throw new ArgumentNullException(nameof(sequenceOfNumbers));
            if (String.IsNullOrWhiteSpace(employeeName)) throw new ArgumentNullException(nameof(employeeName));
            if (date.Second != 0 || date.Millisecond != 0) throw new GameEngineException("Date of Lottery drawings can not have seconds");

            var elapsedTimeToDraw = now.Subtract(date);
            this.lotteryGame.RecordElapsedTimeToDraw(elapsedTimeToDraw);
            string previousSequenceOfNumbers = string.Empty;
            LotteryDraw currentLotteryDraw = null;
            DrawingsSummaryReport report = null;
            bool isAlreadyGradedWithTheSameNumbers = false;

			try
			{
				var drawIsAlreadyGraded = lotteryDraws.IsRegraded(date) || lotteryDraws.IsGraded(date);
				previousSequenceOfNumbers = drawIsAlreadyGraded ? lotteryDraws.GetLotteryDraw(date).SequenceOfNumbers : string.Empty;

				isAlreadyGradedWithTheSameNumbers = IsAlreadyGradedWithTheSameNumbers(date, sequenceOfNumbers);

				if (lotteryDraws.IsDrawCurrentlyNoActionAt(date))
				{
					var log = $"{employeeName} reverts no action at {now}<br>";
					lotteryDraws.SetNoActionAsDisabled(date, now, employeeName, log);
				}

				if (!isAlreadyGradedWithTheSameNumbers)
				{
					currentLotteryDraw = gradeAction();

                    report = tickets.LotteryDraw(date, currentLotteryDraw, itIsThePresent, now, previousSequenceOfNumbers, employeeName);

                    switch (this.IdOfLottery)
					{
						case IdOfLottery.P2:
						case IdOfLottery.P3:
						case IdOfLottery.P4:
						case IdOfLottery.P5:
                        case IdOfLottery.PB:
                            lotteryGame.RemoveNumbersTracker(sequenceOfNumbers.Length, this, date);
                            break;
                    }

					bool canBeSendToKafka = true;
                    if (this.RootLottery is LotteryTriz && this is not LotteryTriz) canBeSendToKafka = false;

                    if (itIsThePresent && Integration.UseKafka && notifyEvent && canBeSendToKafka)
                    {
                        switch (this.IdOfLottery)
						{
							case IdOfLottery.KN:
								KenoGradeStatusEvent gradeEvent = new KenoGradeStatusEvent(now, currentLotteryDraw as LotteryDrawKeno);
								PlatformMonitor.GetInstance().WhenNewEvent(gradeEvent);
								break;
							case IdOfLottery.P2:
							case IdOfLottery.P3:
							case IdOfLottery.P4:
							case IdOfLottery.P5:
							case IdOfLottery.PB:
							case IdOfLottery.TZ:
                                var schedule = FindScheduleAt(date);
                                    Integration.Kafka.Send(itIsThePresent, $"{KafkaMessage.LOTTERY_DRAW_CONSUMER_PREFIX}{Integration.Kafka.TopicForAllGrades}",
                                    new LotteryDrawGradeMessage(GetGameType(), state.Abbreviation, date, employeeName, schedule.UniqueId, sequenceOfNumbers));
                                break;
                        }
                    }
                }
            }
			catch (Exception e)
			{
                tickets.RollbackFromGradeToPending(date, currentLotteryDraw, now, previousSequenceOfNumbers);

				if (!string.IsNullOrWhiteSpace(previousSequenceOfNumbers))
				{
                    if (!string.IsNullOrWhiteSpace(previousSequenceOfNumbers))
                    {
                        var wasRegraded = previousSequenceOfNumbers[0] == '-';
                        if (wasRegraded)
                        {
                            currentLotteryDraw?.UpdateToRegrade(currentLotteryDraw.WhoRegraded, now);
                        }
                        else
                        {
                            currentLotteryDraw?.Update(previousSequenceOfNumbers, employeeName, now);
                        }
                    }
                    else
                    {
                        currentLotteryDraw?.RollbackGrade(employeeName, now);
                    }
                    throw;
                }
            }

            switch (this.IdOfLottery)
            {
                case IdOfLottery.KN:
                case IdOfLottery.TZ:
                    if (report == null) report = new EmptyDrawingsSummaryReport();
                    break;
                case IdOfLottery.P2:
                case IdOfLottery.P3:
                case IdOfLottery.P4:
                case IdOfLottery.P5:
                case IdOfLottery.PB:
                    if (report == null) report = new PicksDrawingsSummaryReport(isAlreadyGradedWithTheSameNumbers);
                    break;
            }

            return report;
        }

        internal virtual List<PayFragmentsMessage> CreateMessagesToResendDraw(DateTime date, DateTime now)
		{
			if (date == default(DateTime)) throw new GameEngineException("Date cannot have default value");
			if (now == default(DateTime)) throw new GameEngineException("Date cannot have default value");
			if (date.Second != 0 || date.Millisecond != 0) throw new GameEngineException("Date of Lottery drawings can not have seconds");
			if (!tickets.ExistTicketsAt(date)) throw new GameEngineException($"There are no tickets for drawing at {date}");

			var result = tickets.CreateMessagesToResendTickets(date, now);
			return result;
		}

		internal void ResendTicketsToHistorical(DateTime date, bool itIsThePresent)
		{
			if (date == default(DateTime)) throw new GameEngineException("Date cannot have default value");
			if (date.Second != 0 || date.Millisecond != 0) throw new GameEngineException("Date of Lottery drawings can not have seconds");
			if (!tickets.ExistTicketsAt(date)) throw new GameEngineException($"There are no tickets for drawing at {date}");
			if (tickets.AnyFakeTicketNumberAt(date)) throw new GameEngineException($"Some ticket has a fake ticket number for drawing at {date}");

			tickets.ResendTicketsToHistorical(date, itIsThePresent);
		}

		protected bool IsAlreadyGradedWithTheSameNumbers(DateTime date, string newSequenceOfNumbers)
		{
			bool hasSequenceOfNumbers = lotteryDraws.HasSequenceOfNumbers(date);
			bool existLotteryDraw = lotteryDraws.Contains(date);
			bool isMarkedAsNoAction = lotteryDraws.IsDrawCurrentlyNoActionAt(date);
			var currentSequenceOfNumbers = (hasSequenceOfNumbers && existLotteryDraw && !isMarkedAsNoAction) ? lotteryDraws.GetLotteryDraw(date).SequenceOfNumbers : string.Empty;
			var result = newSequenceOfNumbers == currentSequenceOfNumbers;
			return result;
		}

		internal DateTime LastKnownGradeOrDefault(Schedule schedule)
		{
			if (schedule == null) throw new ArgumentNullException(nameof(schedule));
			return this.lotteryDraws.LastKnownGradeOrDefault(schedule);
		}

		internal void ChangePendingTicketsHour(HourMinute oldHour, HourMinute newHour)
		{
			tickets.ChangePendingTicketsHour(oldHour, newHour);
		}
		internal void ChangePendingTicketsHour(int oldMinutes, int newMinutes)
		{
			tickets.ChangePendingTicketsHour(oldMinutes, newMinutes);
		}

		internal bool HasPendingTicketsFor(Schedule oldSchedule, DayOfWeek day)
		{
			return tickets.HasPendingTicketsFor(oldSchedule, day);
		}

		internal virtual bool HasPendingTickets(Schedule oldSchedule)
		{
			foreach (DayOfWeek day in oldSchedule.Days)
			{
				if (this.HasPendingTicketsFor(oldSchedule, day)) return true;
			}
			return false;
		}

		internal bool HasPendingTicketsFor(Schedule oldSchedule, string newDaysOfWeek)
		{
			foreach (DayOfWeek day in MissingDaysIn(oldSchedule, newDaysOfWeek))
			{
				if (this.HasPendingTicketsFor(oldSchedule, day)) return true;
			}
			return false;
		}

		private IEnumerable<DayOfWeek> MissingDaysIn(Schedule oldSchedule, string newDaysOfWeek)
		{
			List<DayOfWeek> result = new List<DayOfWeek>();
			foreach (DayOfWeek day in oldSchedule.Days)
			{
				if (newDaysOfWeek.IndexOf((char)((int)day + '0')) == -1) result.Add(day);
			}
			return result;
		}

		internal DrawingsSummaryReport Regrade(bool itIsThePresent, DateTime date, DateTime now, string employeeName)
		{
			return Regrade(itIsThePresent, date, now, employeeName, false);
		}

		internal virtual DrawingsSummaryReport Regrade(bool itIsThePresent, DateTime date, DateTime now, string employeeName, bool notifyEvent)
		{
			if (date == default(DateTime)) throw new GameEngineException("Date cannot have default value");
			if (now == default(DateTime)) throw new GameEngineException("Date cannot have default value");
			if (String.IsNullOrWhiteSpace(employeeName)) throw new ArgumentNullException(nameof(employeeName));
			if (date.Second != 0 || date.Millisecond != 0) throw new GameEngineException("Date of Lottery drawings can not have seconds");
			if (!lotteryDraws.IsGraded(date)) throw new GameEngineException($"Lottery scheduled for {date.ToString("MMMM dd, yyyy 'at' H:mm")} was not graded yet");

			const bool THIS_IS_POWER_BALL = false;
			var result = RegradeForPickOrPowerball(THIS_IS_POWER_BALL, date, 0, now, itIsThePresent, employeeName, notifyEvent);
			return result;
		}
		internal DrawingsSummaryReport RegradeKeno(bool itIsThePresent, DateTime date, DateTime now, string employeeName)
		{
			if (date == default(DateTime)) throw new GameEngineException("Date cannot have default value");
			if (now == default(DateTime)) throw new GameEngineException("Date cannot have default value");
			if (String.IsNullOrWhiteSpace(employeeName)) throw new ArgumentNullException(nameof(employeeName));
			if (date.Second != 0 || date.Millisecond != 0) throw new GameEngineException("Date of Lottery drawings can not have seconds");
			if (!lotteryDraws.IsGraded(date)) throw new GameEngineException($"Lottery scheduled for {date.ToString("MMMM dd, yyyy 'at' H:mm")} was not graded yet");

			var result = RegradeForKeno(date, now, itIsThePresent, employeeName);

			if (itIsThePresent)
			{
				KenoRegradeStatusEvent regradeEvent = new KenoRegradeStatusEvent(now, date);
				PlatformMonitor.GetInstance().WhenNewEvent(regradeEvent);
			}

			return result;
		}
		internal DrawingsSummaryReport Regrade(bool itIsThePresent, DateTime date, int multiplier, DateTime now, string employeeName)
		{
			if (date == default(DateTime)) throw new GameEngineException("Date cannot have default value");
			if (now == default(DateTime)) throw new GameEngineException("Date cannot have default value");
			if (String.IsNullOrWhiteSpace(employeeName)) throw new ArgumentNullException(nameof(employeeName));
			if (!ExistsScheduleAt(date)) throw new GameEngineException($"There is not any Lottery scheduled for {date.ToString("MMMM dd, yyyy 'at' H:mm")}");
			if (date.Second != 0 || date.Millisecond != 0) throw new GameEngineException("Date of Lottery drawings can not have seconds");
			if (lotteryDraws.IsMarkedAsNoAction(date)) throw new GameEngineException($"Lottery scheduled for {date.ToString("MMMM dd, yyyy 'at' H:mm")} was marked as no action");
			if (!(multiplier == 1 || multiplier == 2 || multiplier == 3 || multiplier == 4 || multiplier == 5 || multiplier == 10)) throw new GameEngineException("Multiplier numbers is not valid.");

			const bool THIS_IS_POWER_BALL = true;
			var result = RegradeForPickOrPowerball(THIS_IS_POWER_BALL, date, multiplier, now, itIsThePresent, employeeName, false);
			return result;
		}

		private DrawingsSummaryReport RegradeForPickOrPowerball(bool isPowerball, DateTime date, int multiplier, DateTime now, bool itIsThePresent, string employeeName, bool notifyEvent)
		{
			if (isPowerball && multiplier == 0) throw new GameEngineException("Invalid Powerball multiplier number invocation");
			if (!isPowerball && multiplier != 0) throw new GameEngineException("Invalid Pick invocation");

			var elapsedTimeToDraw = now.Subtract(date);
			this.lotteryGame.RecordElapsedTimeToDraw(elapsedTimeToDraw);
			string previousSequenceOfNumbers = string.Empty;
			LotteryDraw currentLotteryDraw = null;
			DrawingsSummaryReport report = null;

			try
			{
				previousSequenceOfNumbers = lotteryDraws.GetLotteryDraw(date).SequenceOfNumbers;
				if (isPowerball)
				{
					currentLotteryDraw = lotteryDraws.Regrade(date, multiplier, now, employeeName);
				}
				else
				{
					currentLotteryDraw = lotteryDraws.Regrade(date, now, employeeName);
				}

				if (tickets.ExistTicketsAt(date))
				{
					report = tickets.Regrade(date, currentLotteryDraw, itIsThePresent, now, previousSequenceOfNumbers, employeeName);

					tickets.CommitRegradeAndSendToHistorical(date, itIsThePresent);
				}

                bool canBeSendToKafka = true;
                if (this.RootLottery is LotteryTriz && this is not LotteryTriz) canBeSendToKafka = false;

                if (itIsThePresent && Integration.UseKafka && notifyEvent && canBeSendToKafka)
				{
                    var schedule = FindScheduleAt(date);
                    Integration.Kafka.Send(itIsThePresent, $"{KafkaMessage.LOTTERY_DRAW_CONSUMER_PREFIX}{Integration.Kafka.TopicForAllGrades}",
						new LotteryDrawRegradeMessage(GetGameType(), state.Abbreviation, date, employeeName, schedule.UniqueId));
				}
			}
			catch (Exception e)
			{
				tickets.RollbackFromRegradeToPending(date, currentLotteryDraw, now, previousSequenceOfNumbers);
				if (!string.IsNullOrWhiteSpace(previousSequenceOfNumbers))
				{
					currentLotteryDraw?.Update(previousSequenceOfNumbers, employeeName, now);
				}

				throw e;
			}
			if (report == null) report = new EmptyDrawingsSummaryReport();
			return report;
		}
		private DrawingsSummaryReport RegradeForKeno(DateTime date, DateTime now, bool itIsThePresent, string employeeName)
		{
			var elapsedTimeToDraw = now.Subtract(date);
			this.lotteryGame.RecordElapsedTimeToDraw(elapsedTimeToDraw);
			string previousSequenceOfNumbers = string.Empty;
			LotteryDrawKeno currentLotteryDraw = null;
			DrawingsSummaryReport report = null;

			const int MULTIPLIER = 1;
			const int BULL_EYE = 0;

			try
			{
				previousSequenceOfNumbers = lotteryDraws.GetLotteryDraw(date).SequenceOfNumbers;
				currentLotteryDraw = (LotteryDrawKeno)lotteryDraws.Regrade(date, MULTIPLIER, BULL_EYE, now, employeeName);

				if (tickets.ExistTicketsAt(date))
				{
					report = tickets.RegradeKeno(date, currentLotteryDraw, itIsThePresent, now, previousSequenceOfNumbers, employeeName);

					tickets.CommitRegradeAndSendToHistorical(date, itIsThePresent);
				}
			}
			catch (Exception e)
			{
				tickets.RollbackFromRegradeToPending(date, currentLotteryDraw, now, previousSequenceOfNumbers);
				if (!string.IsNullOrWhiteSpace(previousSequenceOfNumbers))
				{
					currentLotteryDraw?.Update(previousSequenceOfNumbers, employeeName, now);
				}

				throw e;
			}
			if (report == null) report = new EmptyDrawingsSummaryReport();
			return report;
		}
		internal DrawingsSummaryReport ConfirmDraw(DateTime drawDate, DateTime now, string employeeName)
		{
			return ConfirmDraw(drawDate, now, employeeName, false);
		}
		internal DrawingsSummaryReport ConfirmDraw(DateTime drawDate, DateTime now, string employeeName, bool itIsThePresent)
		{
			return ConfirmDraw(drawDate, now, employeeName, itIsThePresent, false);
		}
        internal virtual DrawingsSummaryReport ConfirmDraw(DateTime drawDate, DateTime now, string employeeName, bool itIsThePresent, bool notifyEvent)
        {
            if (drawDate == default(DateTime)) throw new GameEngineException("Date cannot have default value");
            if (now == default(DateTime)) throw new GameEngineException("Date cannot have default value");
            if (String.IsNullOrWhiteSpace(employeeName)) throw new ArgumentNullException(nameof(employeeName));
            if (drawDate.Second != 0 || drawDate.Millisecond != 0) throw new GameEngineException("Date of Lottery drawings can not have seconds");
            if (!ExistsScheduleAt(drawDate)) throw new GameEngineException($"There is not any Lottery scheduled for {drawDate.ToString("MMMM dd, yyyy at H:mm")}");
            if (!CanBeConfirmed(drawDate)) throw new GameEngineException("Drawing cannot be confirmed because it is not graded or noaction");

            var winner = lotteryDraws.DrawInExactTimeAt(drawDate);
            var sometimeWereTicketsSentToAccounting = winner.SometimeWereTicketsSentToAccounting;
            winner.Confirm(employeeName);
            lotteryDraws.WriteLog(drawDate, $"{employeeName} confirmed winner numbers at {now}<br>");

            var ticketsOfDraw = tickets.TicketsOfDrawAt(drawDate);
            var ticketsToChangeInAccounting = ticketsOfDraw.Where(ticket => ticket.NeedsAccountingSettle);
            if (ticketsToChangeInAccounting.Any()) winner.MarkAsSentToAccounting();
            this.Company.Cashier.PayGameboards(itIsThePresent, ticketsToChangeInAccounting, now, employeeName, sometimeWereTicketsSentToAccounting);
            foreach (var ticket in ticketsToChangeInAccounting) ticket.MarkAccountingSettleAsDone();

            TicketsOfDraw set = tickets.TicketsInMemory(drawDate);
            if (set == null) return new EmptyDrawingsSummaryReport();

            bool canBeSendToKafka = true;
            if (this.RootLottery is LotteryTriz && this is not LotteryTriz) canBeSendToKafka = false;

            if (itIsThePresent && Integration.UseKafka && notifyEvent && canBeSendToKafka)
            {
                var schedule = FindScheduleAt(drawDate);
                Integration.Kafka.Send(itIsThePresent, $"{KafkaMessage.LOTTERY_DRAW_CONSUMER_PREFIX}{Integration.Kafka.TopicForAllGrades}",
                    new LotteryDrawConfirmationMessage(GetGameType(), state.Abbreviation, drawDate, employeeName, schedule.UniqueId));
            }
            return new PicksDrawingsSummaryReport(set);
        }

        internal bool CanBeConfirmed(DateTime drawDate)
		{
			if (drawDate == default(DateTime)) throw new GameEngineException("Date cannot have default value");
			var result = IsGradedAt(drawDate) || IsNoActionAt(drawDate) || IsRegraded(drawDate);
			return result;
		}

		internal virtual bool WasAlreadyConfirmed(DateTime drawDate)
		{
			if (drawDate == default(DateTime)) throw new GameEngineException("Date cannot have default value");
			var lotteryDraw = lotteryDraws.DrawInExactTimeAt(drawDate);
			var result = !lotteryDraw.IsToBeConfirmed;

			return result;
		}

        internal NoActionReport SetNoAction(bool itIsThePresent, DateTime date, DateTime now, string employeeName)
		{
			return SetNoAction(itIsThePresent, date, now, employeeName, false);
		}
		internal virtual NoActionReport SetNoAction(bool itIsThePresent, DateTime date, DateTime now, string employeeName, bool notifyEvent)
		{
			if (date == default(DateTime)) throw new GameEngineException("Date cannot have default value");
			if (now == default(DateTime)) throw new GameEngineException("Date cannot have default value");
			if (String.IsNullOrWhiteSpace(employeeName)) throw new ArgumentNullException(nameof(employeeName));
			if (date.Second != 0 || date.Millisecond != 0) throw new GameEngineException("Date of Lottery drawings can not have seconds");
			if (!ExistsScheduleAt(date)) throw new GameEngineException($"There is not any Lottery scheduled for {date.ToString("MMMM dd, yyyy 'at' H:mm")}");
			//  //if (now < date) throw new GameEngineException($"Draw date: {date} cannot be greater than now: {now}");

			var log = $"{employeeName} sets lottery as no action at {now}<br>";
			var lotteryNoAction = lotteryDraws.SetNoAction(date, now, employeeName, log);

			if (itIsThePresent)
			{
				KenoNoActionStatusEvent noActionEvent = new KenoNoActionStatusEvent(now, date);
				PlatformMonitor.GetInstance().WhenNewEvent(noActionEvent);
			}

			NoActionReport report;
			if (tickets.ExistTicketsAt(date))
			{
				report = tickets.SetNoAction(date, itIsThePresent, now, employeeName, lotteryNoAction);
			}
			else
			{
				report = new NoActionReport(new List<Ticket>());
			}

            var pickNumber = NumberOfDigits();

			if (pickNumber >= 2 && pickNumber <= 5) lotteryGame.RemoveNumbersTracker(pickNumber, this, date);

            bool canBeSendToKafka = true;
            if (this.RootLottery is LotteryTriz && this is not LotteryTriz) canBeSendToKafka = false;

            if (itIsThePresent && Integration.UseKafka && notifyEvent && canBeSendToKafka)
			{
				var schedule = FindScheduleAt(date);
				Integration.Kafka.Send(itIsThePresent, $"{KafkaMessage.LOTTERY_DRAW_CONSUMER_PREFIX}{Integration.Kafka.TopicForAllGrades}", 
					new LotteryDrawNoActionMessage(GetGameType(), state.Abbreviation, date, employeeName, schedule.UniqueId));
			}
			return report;
		}

		internal virtual NoActionReport PreviewNoAction(DateTime date)
		{
			if (date == default(DateTime)) throw new GameEngineException("Date cannot have default value");
			if (date.Second != 0 || date.Millisecond != 0) throw new GameEngineException("Date of Lottery drawings can not have seconds");
			if (!ExistsScheduleAt(date)) throw new GameEngineException($"There is not any Lottery scheduled for {date.ToString("MMMM dd, yyyy 'at' H:mm")}");
			if (lotteryDraws.IsMarkedAsNoAction(date)) throw new GameEngineException($"Lottery scheduled for {date.ToString("MMMM dd, yyyy 'at' H:mm")} is already marked as no action");

			IEnumerable<Ticket> ticketsOfDraw = new List<Ticket>();
			if (tickets.ExistTicketsAt(date))
			{
				ticketsOfDraw = tickets.TicketsOfDrawWithoutNoActionAt(date);
			}

			return new NoActionReport(ticketsOfDraw);
		}

		internal NoActionReport PreviewNoAction(DateTime date, int multiplier)
		{
			if (date == default(DateTime)) throw new GameEngineException("Date cannot have default value");
			if (date.Second != 0 || date.Millisecond != 0) throw new GameEngineException("Date of Lottery drawings can not have seconds");
			if (!ExistsScheduleAt(date)) throw new GameEngineException($"There is not any Lottery scheduled for {date.ToString("MMMM dd, yyyy 'at' H:mm")}");
			if (lotteryDraws.IsMarkedAsNoAction(date)) throw new GameEngineException($"Lottery scheduled for {date.ToString("MMMM dd, yyyy 'at' H:mm")} is already marked as no action");
			if (!(multiplier == 1 || multiplier == 2 || multiplier == 3 || multiplier == 4 || multiplier == 5 || multiplier == 10)) throw new GameEngineException("Multiplier numbers is not valid.");

			IEnumerable<Ticket> ticketsOfDraw = new List<Ticket>();
			if (tickets.ExistTicketsAt(date))
			{
				ticketsOfDraw = tickets.TicketsOfDrawAt(date);
			}

			return new NoActionReport(ticketsOfDraw);
		}

		internal void SetAction(bool itIsThePresent, DateTime date, DateTime now, string employeeName)
		{
			if (date == default(DateTime)) throw new GameEngineException("Date cannot have default value");
			if (String.IsNullOrWhiteSpace(employeeName)) throw new ArgumentNullException(nameof(employeeName));
			if (date.Second != 0 || date.Millisecond != 0) throw new GameEngineException("Date of Lottery drawings can not have seconds");
			if (!ExistsScheduleAt(date)) throw new GameEngineException($"There is not any Lottery scheduled for {date.ToString("MMMM dd, yyyy 'at' H:mm")}");

			if (lotteryDraws.IsDrawCurrentlyNoActionAt(date))
			{
				var log = $"{employeeName} reverts no action at {now}<br>";
				lotteryDraws.SetNoActionAsDisabled(date, now, employeeName, log);
				if (tickets.ExistTicketsAt(date))
				{
					var ticketsOfDraw = tickets.TicketsOfDrawAt(date);
					foreach (var ticket in ticketsOfDraw.Where(x => !x.IsRefunded()))
					{
						ticket.ChangeToRegraded();
					}
				}
			}
		}

		internal LotteryNoAction GetLotteryNoAction(DateTime date)
		{
			if (date == default(DateTime)) throw new GameEngineException("Date cannot have default value");
			if (date.Second != 0 || date.Millisecond != 0) throw new GameEngineException("Date of Lottery drawings can not have seconds");
			if (!ExistsScheduleAt(date)) throw new GameEngineException($"There is not any Lottery scheduled for {date.ToString("MMMM dd, yyyy 'at' H:mm")}");
			if (!lotteryDraws.ExistsRecordForNoActionAt(date)) throw new GameEngineException($"Lottery scheduled for {date.ToString("MMMM dd, yyyy 'at' H:mm")} is not marked as no action");

			var noAction = lotteryDraws.GetLotteryNoAction(date);
			return noAction;
		}

		internal void SendToHistoricalLateGradedTickets(bool itIsThePresent, DateTime date, DateTime now, string employeeName)
		{
			if (date == default(DateTime)) throw new GameEngineException("Date cannot have default value");
			if (now == default(DateTime)) throw new GameEngineException("Date cannot have default value");
			if (date.Second != 0 || date.Millisecond != 0) throw new GameEngineException("Date of Lottery drawings can not have seconds");
			if (!ExistsScheduleAt(date)) throw new GameEngineException($"There is not any Lottery scheduled for {date.ToString("MMMM dd, yyyy 'at' H:mm")}");

			tickets.SendToHistoricalLateGradedTickets(itIsThePresent, date, employeeName);
			if (WasAlreadyConfirmed(date))
			{
				IEnumerable<Gameboard> ticketsWithoutNoAction = tickets.TicketsOfDrawWithoutNoActionAt(date).Where(x => x.NeedsAccountingSettle);
				var winner = lotteryDraws.DrawInExactTimeAt(date);
				var sometimeWereTicketsSentToAccounting = winner.SometimeWereTicketsSentToAccounting;
				this.Company.Cashier.PayGameboards(itIsThePresent, ticketsWithoutNoAction, now, employeeName, sometimeWereTicketsSentToAccounting);
				foreach (var ticket in ticketsWithoutNoAction) ticket.MarkAccountingSettleAsDone();
			}
		}

		internal void SendToHistoricalLateNoActionTickets(bool itIsThePresent, DateTime date, DateTime now, string employeeName)
		{
			if (date == default(DateTime)) throw new GameEngineException("Date cannot have default value");
			if (now == default(DateTime)) throw new GameEngineException("Date cannot have default value");
			if (date.Second != 0 || date.Millisecond != 0) throw new GameEngineException("Date of Lottery drawings can not have seconds");
			if (!ExistsScheduleAt(date)) throw new GameEngineException($"There is not any Lottery scheduled for {date.ToString("MMMM dd, yyyy 'at' H:mm")}");

			tickets.SendToHistoricalLateNoActionTickets(itIsThePresent, date, employeeName);
			if (WasAlreadyConfirmed(date))
			{
				var ticketsOfDraw = tickets.TicketsOfDrawAt(date).Where(x => x.NeedsAccountingSettle);
				var winner = lotteryDraws.DrawInExactTimeAt(date);
				var sometimeWereTicketsSentToAccounting = winner.SometimeWereTicketsSentToAccounting;
				this.Company.Cashier.PayGameboards(itIsThePresent, ticketsOfDraw, now, employeeName, sometimeWereTicketsSentToAccounting);
				foreach (var ticket in ticketsOfDraw) ticket.MarkAccountingSettleAsDone();
			}
		}

		internal bool IsOnlyOneGradedTicketWithoutNumber(DateTime date)
		{
			if (date == default(DateTime)) throw new GameEngineException("Date cannot have default value");
			if (date.Second != 0 || date.Millisecond != 0) throw new GameEngineException("Date of Lottery drawings can not have seconds");
			if (!ExistsScheduleAt(date)) throw new GameEngineException($"There is not any Lottery scheduled for {date.ToString("MMMM dd, yyyy 'at' H:mm")}");

			var result = tickets.IsOnlyOneGradedTicketWithoutNumber(date);
			return result;
		}

		internal bool IsOnlyOneNoActionTicketWithoutNumber(DateTime date)
		{
			if (date == default(DateTime)) throw new GameEngineException("Date cannot have default value");
			if (date.Second != 0 || date.Millisecond != 0) throw new GameEngineException("Date of Lottery drawings can not have seconds");
			if (!ExistsScheduleAt(date)) throw new GameEngineException($"There is not any Lottery scheduled for {date.ToString("MMMM dd, yyyy 'at' H:mm")}");

			var result = tickets.IsOnlyOneNoActionTicketWithoutNumber(date);
			return result;
		}

		internal void PayPrize(Ticket ticket)
		{
			if (!ticket.IsWinner()) throw new GameEngineException($"Ticket should be in {GameboardStatus.WINNER} status but it is at {ticket.Prizing}");
			var prize = ticket.CalculatedPrize();
			var owner = ticket.Player;

			Company.NewBetPayed(owner, ticket, pool, prize);
		}

		internal void ReturnPrize(Ticket ticket)
		{
			if (!ticket.PreviouslyWasWinner()) throw new GameEngineException($"Ticket should not be in {GameboardStatus.WINNER} status");
			this.Company.NewBetLockedFromBetPayed(ticket.Player, ticket, pool);
		}

		internal void Refunds(Ticket ticket)
		{
			if (!ticket.IsUnprized() && !ticket.IsRefunded()) throw new GameEngineException($"Ticket should be in {GameboardStatus.UNPRIZED} status but it is at {ticket.Prizing}");
			this.Company.CancelBet(ticket.Player, ticket, pool);
		}

		internal void Punch(Ticket ticket)
		{
			if (!ticket.IsLoser()) throw new GameEngineException($"Ticket should be in {GameboardStatus.LOSER} status but it is at {ticket.Prizing}");
			this.Company.PunchBet(ticket.Player, ticket, pool);
		}

		internal void LockBetPunched(Ticket ticket)
		{
			if (!ticket.IsUnprized()) throw new GameEngineException($"Ticket should not be in {GameboardStatus.LOSER} status");
			this.Company.LockBetPunched(ticket.Player, ticket, pool);
		}

		internal void LockBetCanceled(Ticket ticket)
		{
			if (!ticket.IsNoAction() && ticket.PreviouslyWasNoAction()) throw new GameEngineException($"Ticket should be in {GameboardStatus.NOACTION} status");
			this.Company.LockBetCanceled(ticket.Player, ticket, pool);
		}

		internal void RemoveDisposedTickets()
		{
			this.lotteryGame.RemoveDisposedTickets();
		}

		internal virtual void DisableDraw(DateTime date, Domain domain, DateTime now, string employeeName)
		{
			if (date == default(DateTime)) throw new GameEngineException("Date cannot have default value");
			if (domain == null) throw new ArgumentNullException(nameof(domain));
			if (now == default(DateTime)) throw new GameEngineException("Date cannot have default value");
			if (String.IsNullOrWhiteSpace(employeeName)) throw new ArgumentNullException(nameof(employeeName));
			if (!ExistsScheduleAt(date)) throw new GameEngineException($"There is not any Lottery scheduled for {date.ToString("MMMM dd, yyyy 'at' H:mm")}");
			if (date.Second != 0 || date.Millisecond != 0) throw new GameEngineException("Date of Lottery drawings can not have seconds");
			if (lotteryDraws.IsMarkedAsNoAction(date)) throw new GameEngineException($"Lottery scheduled for {date.ToString("MMMM dd, yyyy 'at' H:mm")} was marked as no action");

			lotteryDraws.DisableDraw(date, domain, now, employeeName);
		}

		internal virtual void EnableDraw(DateTime date, Domain domain, DateTime now, string employeeName)
		{
			if (date == default(DateTime)) throw new GameEngineException("Date cannot have default value");
			if (domain == null) throw new ArgumentNullException(nameof(domain));
			if (now == default(DateTime)) throw new GameEngineException("Date cannot have default value");
			if (String.IsNullOrWhiteSpace(employeeName)) throw new ArgumentNullException(nameof(employeeName));
			if (!ExistsScheduleAt(date)) throw new GameEngineException($"There is not any Lottery scheduled for {date.ToString("MMMM dd, yyyy 'at' H:mm")}");
			if (date.Second != 0 || date.Millisecond != 0) throw new GameEngineException("Date of Lottery drawings can not have seconds");

			lotteryDraws.EnableDraw(date, domain, now, employeeName);
		}

        internal virtual void AttachToDate(DateTime drawDate, DateTime newDrawDate, DateTime now, string employeeName)
		{
            if (drawDate == DateTime.MinValue) throw new GameEngineException("Draw date cannot have default value");
			if (newDrawDate == DateTime.MinValue) throw new GameEngineException("New draw date cannot have default value");
			if (now == DateTime.MinValue) throw new GameEngineException("Date cannot have default value");
			if (String.IsNullOrWhiteSpace(employeeName)) throw new ArgumentNullException(nameof(employeeName));
			if (drawDate == newDrawDate) throw new GameEngineException("Draw date and new draw date cannot be the same");

            if (!ExistsScheduleAt(drawDate)) throw new GameEngineException($"There is not any Lottery scheduled for {drawDate.ToString("MMMM dd, yyyy 'at' H:mm")}");
			if (tickets.ExistPendingTicketsAt(drawDate)) throw new GameEngineException($"There are pending tickets for drawing at {drawDate}");

            bool hasDrawInDate = lotteryDraws.Contains(drawDate);
			if (hasDrawInDate) throw new GameEngineException($"There Lottery has draw for {drawDate}");

            var schedule = FindScheduleAt(drawDate);
			if (schedule is WeeklySchedule weeklySchedule)
			{
                bool isContainedDay = weeklySchedule.ContainsDayOfWeek(newDrawDate.DayOfWeek);
				if (!isContainedDay) throw new GameEngineException($"There is not any Lottery scheduled for {drawDate.ToString("MMMM dd, yyyy 'at' H:mm")}");
            }
            schedule.AttachDate(newDrawDate);

			WriteLog(schedule, $"Date {drawDate} was attached to {newDrawDate} by {employeeName} at {now}");
        }

        internal virtual void EditAttachToDate(DateTime previusDrawDate, DateTime newDrawDate, DateTime now, string employeeName)
        {
            if (previusDrawDate == DateTime.MinValue) throw new GameEngineException("Draw date cannot have default value");
            if (newDrawDate == DateTime.MinValue) throw new GameEngineException("New draw date cannot have default value");
            if (now == DateTime.MinValue) throw new GameEngineException("Date cannot have default value");
            if (String.IsNullOrWhiteSpace(employeeName)) throw new ArgumentNullException(nameof(employeeName));
            if (previusDrawDate == newDrawDate) throw new GameEngineException("Draw date and new draw date cannot be the same");

            if (!ExistsScheduleAt(previusDrawDate)) throw new GameEngineException($"There is not any Lottery scheduled for {previusDrawDate.ToString("MMMM dd, yyyy 'at' H:mm")}");
            if (tickets.ExistPendingTicketsAt(previusDrawDate)) throw new GameEngineException($"There are pending tickets for drawing at {previusDrawDate}");

            bool hasDrawInDate = lotteryDraws.Contains(previusDrawDate);
            if (hasDrawInDate) throw new GameEngineException($"There Lottery has draw for {previusDrawDate}");

            var schedule = FindScheduleAt(previusDrawDate);
            if (schedule is WeeklySchedule weeklySchedule)
            {
                bool isContainedDay = weeklySchedule.ContainsDayOfWeek(newDrawDate.DayOfWeek);
                if (!isContainedDay) throw new GameEngineException($"There is not any Lottery scheduled for {previusDrawDate.ToString("MMMM dd, yyyy 'at' H:mm")}");
            }
            schedule.EditAttachDate(previusDrawDate, newDrawDate);

            WriteLog(schedule, $"Attached date {previusDrawDate} was change to {newDrawDate} by {employeeName} at {now}");
        }

        private bool ExistsScheduleAt(DateTime date)
		{
            foreach (Schedule s in schedules)
			{
				if (s.ToDateTime(date) == date)
				{
					return true;
				}
			}
			return false;
		}

		internal bool IsEnabledScheduleAt(DateTime date)
		{
			bool exists = schedules.Exists(schedule => schedule.IsScheduledAt(date) && schedule.IsEnabled());
			return exists;
		}

		private DateTime ToDateTime(DateTime date)
		{
			var schedule = GetScheduleOrNullFor(date);
			if (schedule == null) throw new GameEngineException($"There is a known schedule for this date {date.ToString("MM/dd/yyyy")} at {date.Hour}:{date.Minute}");
			return schedule.ToDateTime(date);
		}

		private IEnumerable<DateTime> SchedulesToDateTimeAt(DateTime date)
		{
			List<DateTime> result = new List<DateTime>();
			foreach (Schedule s in schedules)
			{
				var d = s.ToDateTime(date);
				result.Add(d);
			}
			return result;
		}

		internal bool IsScheduleMarkedAsNoActionAt(Schedule schedule, DateTime date)
		{
			if (date == default(DateTime)) throw new GameEngineException("Date cannot have default value");
			if (schedule == null) throw new ArgumentNullException(nameof(schedule));

			var exactDateTime = schedule.ToDateTime(date);
			return IsScheduleMarkedAsNoActionAt(exactDateTime);
		}

		internal bool IsScheduleMarkedAsNoActionAt(DateTime date)
		{
			var exists = ExistsScheduleAt(date);
			if (exists)
			{
				exists = lotteryDraws.IsMarkedAsNoAction(ToDateTime(date));
			}
			return exists;
		}

		internal Schedule GetScheduleId(int idSchedule)
		{
			var s1 = schedules.SingleOrDefault(x => x.Id == idSchedule);
			if (s1 == null) throw new GameEngineException($"Schedule {idSchedule} does not exist.");
			return s1;
		}

        internal virtual Schedule GetScheduleByUniqueId(int uniqueId)
        {
            var result = schedules.SingleOrDefault(x => x.UniqueId == uniqueId);
            if (result == null) throw new GameEngineException($"Schedule {nameof(uniqueId)} {uniqueId} does not exist.");
            return result;
        }

		internal virtual bool TryGetScheduleByUniqueId(int uniqueDrawingId, out Schedule schedule)
		{
            schedule = schedules.SingleOrDefault(x => x.UniqueId == uniqueDrawingId);
            return schedule != null;
        }

        internal virtual bool ExistsScheduleByUniqueId(int uniqueId)
        {
            var result = schedules.Any(x => x.UniqueId == uniqueId);
            return result;
        }

        protected abstract void Cancel(Schedule aSchedule, int dayOfWeek, DateTime now, string employeeName, Tickets tickets);

		internal abstract void Cancel(Schedule aSchedule, DateTime now, string employeeName);

		internal void RemoveDisabledSchedules(DateTime date)
		{
			if (date == default(DateTime)) throw new GameEngineException("Date cannot have default value");
			var scheduleFound = schedules.Find(x => x.IsScheduledAt(date) || x.WasScheduledAt(date));
			if (scheduleFound == null) throw new GameEngineException($"There is not any Lottery scheduled {date.Date} at {date.Hour}:{date.Minute}");
			if (scheduleFound.WasDeleted())
			{
				TryToCancelLottery();
			}
		}

		protected abstract void TryToCancelLottery();

		protected bool AreAllSchedulesDisabled()
		{
			var anyEnabled = schedules.Exists(x => x.IsEnabled());
			return !anyEnabled;
		}

		internal bool IsScheduledAt(int dayOfWeek)
		{
			if (!(0 >= dayOfWeek || dayOfWeek <= 6)) throw new GameEngineException($"Day of week {dayOfWeek} is not valid. It should be between 0 and 6");
			DayOfWeek day = (DayOfWeek)dayOfWeek;
			var isScheduled = schedules.Exists(x => x.Days.Contains(day));
			return isScheduled;
		}

		//internal bool IsScheduledAfter(int dayOfWeek, int hour, int minute)
		//{
		//	if (!(0 >= dayOfWeek || dayOfWeek <= 6)) throw new GameEngineException($"Day of week {dayOfWeek} is not valid. It should be between 0 and 6");
		//	if (!(0 >= hour || hour <= 23)) throw new GameEngineException($"Hour {hour} is not valid. It should be between 0 and 23");
		//	if (!(0 >= minute || minute <= 59)) throw new GameEngineException($"Minute {minute} is not valid. It should be between 0 and 59");
		//	DayOfWeek day = (DayOfWeek)dayOfWeek;
		//	var isScheduled = schedules.Exists(x => x.Days.Contains(day)
		//										&& x.Hour.GreaterThan(new HourMinute(hour, minute)));
		//	return isScheduled;
		//}

		protected LotteryDraw FindLotteryDraw(DateTime date, Schedule schedule)
        {
            if (date == default(DateTime)) throw new GameEngineException("Date cannot have default value");
            if (schedule == null) throw new ArgumentNullException(nameof(schedule));

            var gradedDate = schedule.ToDateTime(date);
            if (!lotteryDraws.Contains(gradedDate))
            {
                gradedDate = RegisteredGradedDateAt(date);
            }

            LotteryDraw draw = (LotteryDraw)lotteryDraws.DrawInExactTimeAt(gradedDate);
			return draw;
        }

        internal string SequenceOfNumbersOfDrawAt(DateTime date, Schedule schedule)
		{
			if (date == default(DateTime)) throw new GameEngineException("Date cannot have default value");
			if (schedule == null) throw new ArgumentNullException(nameof(schedule));
			if (!IsAlreadyGraded(date)) throw new GameEngineException("Drawing was not graded yet");

            LotteryDraw draw = FindLotteryDraw(date, schedule);
            return draw.SequenceOfNumbers;
		}

		private DateTime SetExactDateTime(DateTime date, HourMinute elapsedTime)
		{
			var exactDateTime = date.Date.AddHours(elapsedTime.Hour).AddMinutes(elapsedTime.Minute);
			return exactDateTime;
		}

		internal abstract int PickNumber { get; }

		internal bool Contains(Schedule otherSchedule)
		{
			if (schedules == null) throw new ArgumentNullException(nameof(schedules));
			var result = schedules.Contains(otherSchedule);
			return result;
		}

		internal IEnumerable<Schedule> PendingAndNoRegradedSchedulesAt(DateTime date)
		{
			if (date == default(DateTime)) throw new GameEngineException("Date cannot have default value");
			var result = schedules.Where(schedule =>
			{
				var exactDate = schedule.ToDateTime(date);
				var isAlreadyGraded = IsAlreadyGraded(exactDate);
				var isRegraded = IsRegraded(exactDate);
				var isMarkedAsNoAction = lotteryDraws.IsMarkedAsNoAction(exactDate);
				var isDeleted = schedule.WasDeletedAt(date);
				return !isAlreadyGraded && !isRegraded && !isMarkedAsNoAction && (schedule.IsScheduledAt(exactDate) || schedule.WasScheduledAt(exactDate)) && !isDeleted;
			});
			return result;
		}

        internal IEnumerable<Schedule> ListAllSchedulesAt(DateTime date)
		{
			if (date == default(DateTime)) throw new GameEngineException("Date cannot have default value");
			var result = schedules.Where(x =>
			{
				var exactDate = x.ToDateTime(date);
				var isDeleted = x.WasDeletedAt(date);
				return (x.IsScheduledAt(exactDate) || x.WasScheduledAt(exactDate)) && !isDeleted;
			});
			return result;
		}

		internal IEnumerable<LotteryComplete> DrawsToBeConfirmed()
		{
			var result = lotteryDraws.DrawsToBeConfirmed();
			return result;
		}

		internal IEnumerable<Schedule> PlayableSchedulesAt(DateTime date)
		{
			if (date == default(DateTime)) throw new GameEngineException("Date cannot have default value");
			var datePart = date.Date;
			var result = schedules.Where(x => datePart == NextValidDrawDate(x, date).Date);
			return result;
		}

		internal int CountPlayableSchedulesAt(DateTime date)
		{
			if (date == default(DateTime)) throw new GameEngineException("Date cannot have default value");
			var count = PlayableSchedulesAt(date).Count();
			return count;
		}

		internal IEnumerable<Schedule> DrawnSchedulesAt(DateTime date)
		{
			if (date == default(DateTime)) throw new GameEngineException("Date cannot have default value");
			var result = schedules.Where(x => lotteryDraws.Contains(x.ToDateTime(date)));
			return result;
		}

		internal virtual IEnumerable<Schedule> FinishedAndRegradedSchedulesAt(DateTime date)
		{
			if (date == default(DateTime)) throw new GameEngineException("Date cannot have default value");

			var result = schedules.Where(x =>
			{
				var exactDate = x.ToDateTime(date);
				var drawsFinalized = IsAlreadyGraded(exactDate) || lotteryDraws.IsMarkedAsNoAction(exactDate);
				var drawsNotFinalizedWithLog = IsRegraded(exactDate);
				return drawsFinalized || drawsNotFinalizedWithLog;
			});
			return result.ToList();
		}

		internal string Changelog(DateTime date, Schedule schedule)
		{
			if (date == default(DateTime)) throw new GameEngineException("Date cannot have default value");
			if (schedule == null) throw new ArgumentNullException(nameof(schedule));

			var gradedDate = schedule.ToDateTime(date);
			if (!lotteryDraws.Contains(gradedDate))
			{
				gradedDate = RegisteredGradedDateOrNoActionAt(date);
			}
			var log = lotteryDraws.LogInExactTimeAt(gradedDate);
			return log;
		}

		internal Schedule FindScheduleAt(DateTime date)
		{
			if (date == default(DateTime)) throw new GameEngineException("Date cannot have default value");
			var scheduleFound = GetScheduleOrNullFor(date);
			if (scheduleFound == null) throw new GameEngineException($"There is not any schedule at {date.Hour}:{date.Minute}");
			return scheduleFound;
		}
		internal Schedule FindScheduleAt(string date)
		{
            if (string.IsNullOrWhiteSpace(date)) throw new ArgumentNullException(nameof(date));
            if (!DateTime.TryParse(date, out DateTime drawDate)) throw new GameEngineException($"Date {date} is not valid");
			if (drawDate.Second != 0 || drawDate.Millisecond != 0) throw new GameEngineException("Date of Lottery drawings can not have seconds");

            return FindScheduleAt(drawDate);
        }

        internal Schedule RandomSchedule()
		{
			var random = new Random();
			var randomIndex = random.Next(schedules.Count);
			var result = schedules[randomIndex];
			return result;
		}

		internal IEnumerable<DateTime> AllNextValidDrawDateForSchedules(DateTime now)
		{
			if (now == default(DateTime)) throw new GameEngineException("Date cannot have default value");

			var drawDates = schedules.Select(schedule => schedule.NextValidDraw(now)).ToList();
			return drawDates;
		}

		internal DateTime NextValidDrawDate(Schedule schedule, DateTime now, string toCountryTimeZone)
		{
            if (schedule == null) throw new ArgumentNullException(nameof(schedule));
            if (now == default(DateTime)) throw new GameEngineException("Date cannot have default value");
			if (string.IsNullOrWhiteSpace(toCountryTimeZone)) throw new ArgumentNullException(nameof(toCountryTimeZone));

            DateTime resultDate = NextValidDrawDate(schedule, now);

            string serverCountryCode = Company.TimeZone;
            DateTime fixedResultDate = TimeZoneConverter.Instance.ConvertTime(resultDate, serverCountryCode, toCountryTimeZone);
            return fixedResultDate;
        }

		internal DateTime NextValidDrawDate(Schedule schedule, DateTime now)
		{
			if (schedule == null) throw new ArgumentNullException(nameof(schedule));
			if (now == default(DateTime)) throw new GameEngineException("Date cannot have default value");
			var nextDate = schedule.ToDateTime(now);

			if (IsAlreadyDrawnAt(schedule, now) || IsScheduleMarkedAsNoActionAt(nextDate) || !schedule.IsScheduled(now.DayOfWeek))
			{
				nextDate = schedule.NextValidDraw(nextDate);
			}

            nextDate = schedule.AdjustedEventTime(nextDate);
			return nextDate;
		}

		private DateTime RegisteredGradedDateAt(DateTime date)
		{
			var scheduleDates = SchedulesToDateTimeAt(date);
			foreach (var scheduleDate in scheduleDates)
			{
				var hasDraw = lotteryDraws.Contains(scheduleDate);
				var result = hasDraw && lotteryDraws.HasSequenceOfNumbers(scheduleDate);
				if (result) return scheduleDate;
			}

			throw new GameEngineException("Drawing was not graded yet");
		}

		private DateTime RegisteredGradedDateOrNoActionAt(DateTime date)
		{
			var scheduleDates = SchedulesToDateTimeAt(date);
			foreach (var scheduleDate in scheduleDates)
			{
				var hasDraw = lotteryDraws.Contains(scheduleDate) || lotteryDraws.ContainsNoAction(scheduleDate);
				if (hasDraw) return scheduleDate;
			}

			throw new GameEngineException("Drawing was not graded or no action yet");
		}

		internal bool IsAlreadyGraded(DateTime date)
		{
            foreach (Schedule s in schedules)
			{
				var exactDate = s.ToDateTime(date);
				if (exactDate == date)
				{
					var result = lotteryDraws.HasSequenceOfNumbers(exactDate);
					return result;
				}
			}
			return false;
		}

		internal bool IsGradedAt(DateTime date)
		{
			var hasDraw = lotteryDraws.Contains(date);
			if (!hasDraw) return false;
			var result = hasDraw && lotteryDraws.IsGraded(date);
			return result;
		}

        internal bool IsGradedAt(DateTime day, Schedule schedule)
        {
            var drawDate = day.AddHours(schedule.Hour.Hour).AddMinutes(schedule.Hour.Minute);
            return IsGradedAt(drawDate);
        }

        internal bool IsNoActionAt(DateTime date)
		{
			var result = lotteryDraws.IsMarkedAsNoAction(date);
			return result;
		}

		internal bool IsNoActionAt(DateTime day, Schedule schedule)
		{
            var drawDate = day.AddHours(schedule.Hour.Hour).AddMinutes(schedule.Hour.Minute);
            return IsNoActionAt(drawDate);
        }
            
		internal bool IsCompletedFor(int year, int month, int day, int hour, int minute)
		{
			DateTime date;
			if (!DateTime.TryParse($"{month}/{day}/{year} {hour}:{minute}:00", out date)) throw new GameEngineException("This is not a valid date");

			var result = lotteryDraws.IsCompletedFor(year, month, day, hour, minute);
			return result;
		}

		internal bool IsPendingFor(DateTime date)
		{
			if (ExistsScheduleAt(date))
			{
				var exactDate = ToDateTime(date);
				var result = !lotteryDraws.HasSequenceOfNumbers(exactDate) && ! lotteryDraws.IsMarkedAsNoAction(date);
				return result;
			}
			return false;
		}

		internal bool IsRegraded(DateTime date)
		{
			if (ExistsScheduleAt(date))
			{
				var exactDate = ToDateTime(date);
				var result = lotteryDraws.IsRegraded(exactDate);
				return result;
			}
			return false;
		}

		internal bool IsRegraded(DateTime day, Schedule schedule)
		{
            var drawDate = day.AddHours(schedule.Hour.Hour).AddMinutes(schedule.Hour.Minute);
            return IsRegraded(drawDate);
        }

        internal bool IsAlreadyDrawnAt(Schedule schedule, DateTime date)
		{
			if (schedule == null) throw new ArgumentNullException(nameof(schedule));
			if (date == default(DateTime)) throw new GameEngineException("Date cannot have default value");
			if (!schedule.IsScheduled(date.DayOfWeek))
			{
				return false;
			}
			else
			{
				var exactDrawDate = schedule.ToDateTime(date);
				var isOverTime = IsOverTimeToCloseStore(schedule, date);
				var isAlreadyGraded = IsAlreadyGraded(exactDrawDate);
				var hasNoAction = IsScheduleMarkedAsNoActionAt(exactDrawDate);
				return (isOverTime || isAlreadyGraded) && !hasNoAction;
			}
		}

		internal bool IsOverTimeToCloseStore(Schedule schedule, DateTime date)
		{
			if (schedule is WeeklySchedule)
			{
				HourMinute moment = new HourMinute(date.Hour, date.Minute);
				HourMinute hm = moment.AddMinutes(Store.DEFAULT_TIME_IN_MINUTES_TO_CLOSE_STORE);
				bool result = schedule.Hour.LowerThan(hm);
				return result;
			}
			else if (schedule is HourlySchedule)
			{
				var exactDrawDate = (schedule as HourlySchedule).ToDateTime(date);
				return exactDrawDate<date.AddMinutes(Store.DEFAULT_TIME_IN_MINUTES_TO_CLOSE_STORE);
			}
			else
			{
				throw new GameEngineException($"{schedule.GetType().FullName} is not valid yet.");
			}
		}

		internal string HourToCloseTheStore(Schedule schedule)
		{
			if (!Schedules.Contains(schedule))
			{
				throw new GameEngineException("There is no draw for this schedule.");
			}
			HourMinute hourMinute = schedule.Hour.RemoveMinutes(Store.DEFAULT_TIME_IN_MINUTES_TO_CLOSE_STORE);
			var hour = hourMinute.Hour == 0 ? 12 : hourMinute.Hour;
			var result = hour <= 12 ? "" + hour : "" + (hour - 12);
			result = result + ":" + (hourMinute.Minute < 10 ? "0" + hourMinute.Minute : "" + hourMinute.Minute);
			result = hourMinute.Hour >= 12 ? result + " PM" : result + " AM";
			return result;
		}

		internal IEnumerable<IEnumerable<Ticket>> TicketsAt(List<Schedule> schedules)
		{
			foreach (var schedule in schedules)
			{
				if (this.schedules.Contains(schedule))
				{
					yield return tickets.TicketsAt(schedule);
				}
			}
		}

		internal virtual IEnumerable<Ticket> TicketsOfDrawAt(DateTime drawDate)
		{
			var result = tickets.TicketsOfDrawAt(drawDate);
			return result;
		}

		internal double RemainingTimeInSecondsToNextDraw(DateTime now)
		{
			if (now == default(DateTime)) throw new GameEngineException("Date cannot have default value");
			double result;
			var anyEnabled = schedules.Exists(x => x.IsEnabled());
			if (!anyEnabled)
			{
				const double IT_WONT_BE_A_NEXT_DRAW = Int32.MaxValue;
				result = IT_WONT_BE_A_NEXT_DRAW;
			}
			else
			{
				var nextDraw = NextValidDrawDate(now);
				if (nextDraw < now)
				{
					const double IN_ANY_MOMENT_FROM_NOW = 0;
					result = IN_ANY_MOMENT_FROM_NOW;
				}
				else
				{
					result = nextDraw.Subtract(now).TotalSeconds;
				}
			}
			return result;
		}

		internal double RemainingTimeInSecondsToNextDraw(Schedule schedule, DateTime now)
		{
			if (now == default(DateTime)) throw new GameEngineException("Date cannot have default value");
			if (schedule == null) throw new ArgumentNullException(nameof(schedule));
			double result;
			var nextDraw = schedule.NextValidDraw(now);
			if (nextDraw < now)
			{
				const double IN_ANY_MOMENT_FROM_NOW = 0;
				result = IN_ANY_MOMENT_FROM_NOW;
			}
			else
			{
				result = nextDraw.Subtract(now).TotalSeconds;
			}

			return result;
		}

		internal DateTime NextValidDrawDate(DateTime now)
		{
			if (now == default(DateTime)) throw new GameEngineException("Date cannot have default value");
			DateTime result = this.schedules.Min(x => x.NextValidDraw(now));
			return result;
		}

		//internal DateTime NextValidDrawDate(DateTime now, out  Schedule schedule)
		//{
		//	if (now == default(DateTime)) throw new GameEngineException("Date cannot have default value");
		//	if (schedules.Count() < 0) throw new GameEngineException($"At least one draw configured is required.");

		//	DateTime result= DateTime.MaxValue;
		//	schedule = null;

		//	foreach (Schedule item in this.schedules)
		//	{
		//		DateTime  draw = item.NextValidDraw(now);
		//		if (schedule == null || result > draw)
		//		{
		//			schedule = item;
		//			result = draw;
		//		}
		//	}
		//	return result;
		//}
		internal bool IsEnabledDraw(DateTime date, Domain domain)
		{
			if (date == default(DateTime)) throw new GameEngineException("Date cannot have default value");
			if (domain == null) throw new ArgumentNullException(nameof(domain));

			var isEnabled = lotteryDraws.IsEnabledDraw(date, domain);
			return isEnabled;
		}

		internal IEnumerable<LotteryDraw> DisabledDraws()
		{
			var result = lotteryDraws.DisabledDraws().
				OrderBy(x => x.Date).ToList();
			return result;
		}

		internal IEnumerable<Schedule> EnabledSchedules()
		{
			var result = schedules.Where(x => x.IsEnabled());
			return result;
		}

		internal void DrawnLotteriesMatchingWinnerNumbers(IEnumerable<DateTime> dates, LotteryDrawsMatchingDesiredNumber lotteryDrawsMatchingDesiredNumber)
		{
			if (lotteryDrawsMatchingDesiredNumber == null) throw new ArgumentNullException(nameof(lotteryDrawsMatchingDesiredNumber));

			foreach (var date in dates.Select(d => d.Date))
			{
				if (date.Hour != 0 || date.Minute != 0 || date.Second != 0) throw new GameEngineException($"{nameof(date)} {date} is not valid");

				var lotteryDraws = this.lotteryDraws.DrawsPerDayAt(date);
				var noActions = this.lotteryDraws.DrawsNoActionPerDayAt(date);
				foreach (var lotteryDraw in lotteryDraws)
				{
					var isNoAction = false;
					if (lotteryDraw.HasSequenceOfNumbers)
					{
						foreach (var noAction in noActions)
						{
							var drawStayedAsNoAction = noAction.NoActionDate > lotteryDraw.LastGradedDate;
							if ((noAction.Date == lotteryDraw.Date) && drawStayedAsNoAction)
							{
								isNoAction = true;
								break;
							}
						}

						if ((IsAlreadyGraded(lotteryDraw.Date) || IsRegraded(lotteryDraw.Date)) && (!isNoAction))
						{
							lotteryDrawsMatchingDesiredNumber.AddIfItMatchesWinner(lotteryDraw);
						}
					}
				}
			}
		}

		internal virtual IEnumerable<LotteryDraw> DrawnLotteriesBetween(IEnumerable<DateTime> dates)
		{
			var drawnLotteries = new List<LotteryDraw>();
			foreach (var date in dates)
			{
				var lotteryDraws = this.lotteryDraws.DrawsPerDayAt(date);
				var noActions = this.lotteryDraws.DrawsNoActionPerDayAt(date);
				foreach (var lotteryDraw in lotteryDraws)
				{
					var isNoAction = false;
					if (lotteryDraw.HasSequenceOfNumbers)
					{
						foreach (var noAction in noActions)
						{
							var drawStayedAsNoAction = noAction.NoActionDate > lotteryDraw.LastGradedDate;
							if ((noAction.Date == lotteryDraw.Date) && drawStayedAsNoAction)
							{
								isNoAction = true;
								break;
							}
						}

						if ((IsAlreadyGraded(lotteryDraw.Date) || IsRegraded(lotteryDraw.Date)) && (!isNoAction))
						{
							drawnLotteries.Add(lotteryDraw);
						}
					}
				}
			}
			return drawnLotteries;
		}

		internal virtual IEnumerable<LotteryDraw> DrawnLotteriesBetween(IEnumerable<DateTime> dates, int hour, int minute)
		{
			var drawnLotteries = new List<LotteryDraw>();
			foreach (var date in dates)
			{
				var exactDateTime = date.AddHours(hour).AddMinutes(minute);
				if (lotteryDraws.Contains(exactDateTime))
				{
					LotteryDraw lotteryDraw = (LotteryDraw)this.lotteryDraws.DrawInExactTimeAt(exactDateTime);
					if (lotteryDraw.HasSequenceOfNumbers)
					{
						var drawStayedAsNoAction = false;
						if (lotteryDraws.ExistsRecordForNoActionAt(exactDateTime))
						{
							var noAction = this.lotteryDraws.GetLotteryNoAction(exactDateTime);
							drawStayedAsNoAction = noAction != null && noAction.NoActionDate > lotteryDraw.LastGradedDate;
						}
						if (!drawStayedAsNoAction)
						{
							drawnLotteries.Add(lotteryDraw);
						}
					}
                }
			}
			return drawnLotteries;
		}

		internal IEnumerable<LotteryNoAction> DrawnLotteriesNoActionBetween(IEnumerable<DateTime> dates)
		{
			var drawnLotteries = new List<LotteryNoAction>();
			foreach (var date in dates)
			{
				var noActions = this.lotteryDraws.DrawsNoActionPerDayAt(date);
				if (noActions.Count() > 0)
				{
					foreach (var noAction in noActions)
					{
						if (noAction.IsEnabled)
						{
							drawnLotteries.Add(noAction);
						}
					}
				}
			}
			return drawnLotteries;
		}

		internal IEnumerable<LotteryNoAction> DrawnLotteriesNoActionBetween(IEnumerable<DateTime> dates, int hour, int minute)
		{
			var drawnLotteries = new List<LotteryNoAction>();
			foreach (var date in dates)
			{
				var exactDateTime = date.AddHours(hour).AddMinutes(minute);
				if (lotteryDraws.ExistsRecordForNoActionAt(exactDateTime))
				{
					var noAction = this.lotteryDraws.GetLotteryNoAction(exactDateTime);
					if ((noAction != null) && (noAction.IsEnabled))
					{
						drawnLotteries.Add(noAction);
					}
				}
			}
			return drawnLotteries;
		}

		internal bool AnyTicketWithNumber(int ticketNumber)
		{
			if (ticketNumber <= 0) throw new GameEngineException($"{nameof(ticketNumber)} {ticketNumber} must be greater than 0");
			var result = tickets.SearchTicketByNumber(ticketNumber);
			return result.Any();
		}

		internal IEnumerable<Lottery> LotteriesToRemoveTicketsFor(int ticketNumber)
		{
			if (ticketNumber <= 0) throw new GameEngineException($"{nameof(ticketNumber)} {ticketNumber} must be greater than 0");
			var ticketsToRemove = tickets.SearchTicketByNumber(ticketNumber).Select(x => x.Lottery).Distinct();
			return ticketsToRemove;
		}

		internal void RemoveTickets(bool itIsThePresent, int ticketNumber, DateTime now, string employeeName)
		{
			if (ticketNumber <= 0) throw new GameEngineException($"{nameof(ticketNumber)} {ticketNumber} must be greater than 0");
			if (now == default(DateTime)) throw new GameEngineException("Date cannot have default value");
			if (string.IsNullOrWhiteSpace(employeeName)) throw new ArgumentNullException(nameof(employeeName));

			var ticketsToRemove = tickets.SearchTicketByNumber(ticketNumber);
			foreach (var ticketToRemove in ticketsToRemove)
			{
				var log = $"{employeeName} sets ticket {ticketToRemove.AsString()} as no action at {now}<br>";
				lotteryDraws.SetNoAction(ticketToRemove.DrawDate, now, employeeName, log);
				lotteryDraws.SetNoActionAsDisabled(ticketToRemove.DrawDate, now, employeeName, string.Empty);
			}

			SetNoActionForPendingTickets(itIsThePresent, ticketsToRemove, employeeName, now);
			this.Company.DeleteTickets(itIsThePresent, ticketsToRemove, now, employeeName);
		}

		//TODO: Erick, ver como diseniar este caso cuando es ticket por ticket
		//Ver como se manejo, sirve o no? Ver el reporte actual.
		private void SetNoActionForPendingTickets(bool itIsThePresent, IEnumerable<Ticket> tickets, string employeeName, DateTime now)
		{
			using (SenderOfHistoricalPicks historical = new SenderOfHistoricalPicks(this, itIsThePresent))
			{
				foreach (Ticket ticket in tickets)
				{
					if (!ticket.IsPending()) throw new GameEngineException($"Ticket must be in {GameboardStatus.PENDING} status");
					ticket.ChangeToRefunded();
					historical.WriteNoActionData(ticket.DrawDate, ticket);
				}
			}
		}

		internal TicketWager WagerToRemoveFor(int ticketNumber, int wagerNumber)
		{
			if (ticketNumber <= 0) throw new GameEngineException($"{nameof(ticketNumber)} {ticketNumber} must be greater than 0");
			if (wagerNumber <= 0) throw new GameEngineException($"{nameof(wagerNumber)} {wagerNumber} must be greater than 0");

			var wagerToRemove = tickets.WagerToRemoveFor(ticketNumber, wagerNumber);
			return wagerToRemove;
		}

		internal void RemoveSubticket(bool itIsThePresent, TicketWager wagerToRemove, DateTime now, string employeeName)
		{
			if (wagerToRemove == null) throw new ArgumentNullException(nameof(wagerToRemove));
			if (now == default(DateTime)) throw new GameEngineException("Date cannot have default value");
			if (String.IsNullOrWhiteSpace(employeeName)) throw new ArgumentNullException(nameof(employeeName));

			var isTheLastWager = wagerToRemove.Ticket.CountWagers == 1;
			wagerToRemove.RemoveItselfFromTicket();
			this.Company.DeleteWager(itIsThePresent, wagerToRemove, now, employeeName);
			Company.Cashier.PayRefund(itIsThePresent, wagerToRemove, now, employeeName);
			if (isTheLastWager)
			{
				wagerToRemove.Ticket.MarkAccountingSettleAsDone();
			}
		}

		internal bool IsTicketAsNoActionFor(int ticketNumber, int wagerNumber)
		{
			if (ticketNumber <= 0) throw new GameEngineException($"{nameof(ticketNumber)} {ticketNumber} must be greater than 0");
			if (wagerNumber <= 0) throw new GameEngineException($"{nameof(wagerNumber)} {wagerNumber} must be greater than 0");

			var wagerToRemove = tickets.WagerToRemoveFor(ticketNumber, wagerNumber);
			var isWagerFound = wagerToRemove != null;
			if (!isWagerFound) return false;
			var isTheLastWager = wagerToRemove.Ticket.CountWagers == 1;
			if (!isTheLastWager) return false;

			var isNoAction = wagerToRemove.Ticket.IsNoAction();
			return isNoAction;
		}

		internal bool HasDisabledDomain(Domain domain, DateTime date)
		{
			var result = lotteryDraws.HasDisabledDomain(domain, date);
			return result;
		}

		internal bool AnyScheduleHasHour(int hour, int minute)
		{
			foreach (var schedule in schedules)
			{
				if (schedule.HasHour(hour, minute)) return true;
			}
			return false;
		}

		internal IEnumerable<PendingDraw> PendingDraws()
		{
			var result = tickets.PendingDraws();
			return result;
		}

		internal IEnumerable<PendingKenoDraw> PendingKenoDrawsBy(string domainIds)
		{
			var result = tickets.PendingKenoDrawsBy(domainIds);
			return result;
		}

		internal IEnumerable<LotteryComplete> CompletedDraws()
		{
			var completedDraws = new Dictionary<DateTime, LotteryComplete>();
			var gradedDraws = lotteryDraws.GradedDraws();
			foreach (var gradedDraw in gradedDraws)
			{
				if (!completedDraws.ContainsKey(gradedDraw.Date))
				{
					completedDraws.Add(gradedDraw.Date, gradedDraw);
				}
			}
			var noActionDraws = lotteryDraws.NoActionDraws();
			foreach (var noActionDraw in noActionDraws)
			{
				if (!completedDraws.ContainsKey(noActionDraw.Date))
				{
					completedDraws.Add(noActionDraw.Date, noActionDraw);
				}
			}
			return completedDraws.Values;
		}

		internal List<Ticket> TicketsWithTicketNumberZero()
		{
			var tickets = new List<Ticket>();
			foreach (var ticket in this.tickets.AllTickets())
			{
				if (ticket.TicketNumber == 0)
				{
					tickets.Add(ticket);
				}
			}
			return tickets;
		}

		internal Ticket SearchTicketBy(DateTime drawDate, int ticketNumber)
		{
			if (ticketNumber <= 0) throw new GameEngineException($"{nameof(ticketNumber)} {ticketNumber} must be greater than 0");
			if (drawDate == default(DateTime)) throw new GameEngineException("Date cannot have default value");
			if (drawDate.Second != 0 || drawDate.Millisecond != 0) throw new GameEngineException("Date of Lottery drawings can not have seconds");

			var ticketsOfDraw = tickets.TicketsOfDrawAt(drawDate);
			foreach (var ticket in ticketsOfDraw)
			{
				if (ticket.TicketNumber == ticketNumber) return ticket;
			}
			throw new GameEngineException($"No ticket in draw date {drawDate} with ticket number {ticketNumber}");
		}

		internal IEnumerable<TicketWager> WagersWithNumberZero(int ticketNumber)
		{
			if (ticketNumber <= 0) throw new GameEngineException($"{nameof(ticketNumber)} {ticketNumber} must be greater than 0");

			var result = Enumerable.Empty<TicketWager>();
			foreach (var ticket in this.tickets.SearchTicketByNumber(ticketNumber))
			{
				result = result.Concat(ticket.WagersWithNumberZero());
			}
			return result;
		}

		internal IEnumerable<TicketWager> WagersOfTicket(int ticketNumber)
		{
			if (ticketNumber <= 0) throw new GameEngineException($"{nameof(ticketNumber)} {ticketNumber} must be greater than 0");

			var result = Enumerable.Empty<TicketWager>();
			foreach (var ticket in this.tickets.SearchTicketByNumber(ticketNumber))
			{
				result = result.Concat(ticket.Wagers);
			}
			return result;
		}

		internal List<Ticket> TicketsWithFakeTicketNumber()
		{
			var tickets = new List<Ticket>();
			foreach (var ticket in this.tickets.AllTickets())
			{
				if (ticket.TicketNumber == Ticket.FAKE_TICKET_NUMBER)
				{
					tickets.Add(ticket);
				}
			}
			return tickets;
		}

		internal IEnumerable<Ticket> TicketsWithWagersNumberZero(DateTime startDrawDay, DateTime endDrawDay)
		{
			if (startDrawDay.Hour != 0 || startDrawDay.Minute != 0 || startDrawDay.Second != 0 || startDrawDay.Millisecond != 0) throw new GameEngineException($"{nameof(startDrawDay)} '{startDrawDay}' must be an exact day");
			if (endDrawDay.Hour != 0 || endDrawDay.Minute != 0 || endDrawDay.Second != 0 || endDrawDay.Millisecond != 0) throw new GameEngineException($"{nameof(endDrawDay)} '{endDrawDay}' must be an exact day");

			var result = Enumerable.Empty<Ticket>();
			foreach (var ticket in this.tickets.TicketsWithDrawDateBetween(startDrawDay, endDrawDay))
			{
				if (ticket.AnyWagerNumberIsZero())
				{
					result = result.Append(ticket);
				}
			}
			return result;
		}
		
		internal virtual IEnumerable<Ticket> FindTicketsMatchingWith(IEnumerable<int> ticketNumbers)
		{
			var result = tickets.FindTicketsMatchingWith(ticketNumbers);
			return result;
		}

        internal Ticket FindTicketMatchingWith(int ticketNumber)
        {
            var result = tickets.FindTicketMatchingWith(ticketNumber);
            return result;
        }

        internal IEnumerable<Ticket> FindTicketsInRange(int theLowestBetId, int theHighestBetId)
		{
			var result = tickets.FindTicketsInRange(theLowestBetId, theHighestBetId);
			return result;
		}

        internal DateTime NextValidDrawDateFrom(NextDatesAccumulator nextDatesAccumulator, bool withFireBall, DateTime date, DateTime now, Domain domain, bool isPresentPlayerBeforeToCloseStore)
        {
            if (nextDatesAccumulator == null) throw new ArgumentNullException(nameof(nextDatesAccumulator));
            if (date == default(DateTime)) throw new GameEngineException("Date cannot have default value");
            if (now == default(DateTime)) throw new GameEngineException("Date cannot have default value");
            if (domain == null) throw new ArgumentNullException(nameof(domain));
            var scheduleToValidate = GetScheduleOrNullFor(date);
            if (scheduleToValidate == null) throw new GameEngineException($"There is not any schedule at {date.Hour}:{date.Minute}");

            DateTime currentDate = date;
            DateTime currentTime = isPresentPlayerBeforeToCloseStore ? now : now.AddMinutes(Store.DEFAULT_TIME_IN_MINUTES_TO_CLOSE_STORE);
            Schedule scheduleFound;

            do
            {
                scheduleFound = this.schedules.
                    FirstOrDefault(schedule =>
                    {
                        var dateTimeForThisSchedule = schedule.ToDateTime(currentDate);
                        var isGreaterThanCurrentDate = currentTime <= dateTimeForThisSchedule;
                        if (!isGreaterThanCurrentDate) return false;
                        var isTheSameHour = schedule.Hour.Hour == currentDate.Hour && schedule.Hour.Minute == currentDate.Minute;
						if (!isTheSameHour)
						{
							bool isAttachedDated = schedule.IsAttachedDate(currentDate);
							if (!isAttachedDated) return false;
						}
                        var isScheduled = schedule.IsScheduled(currentDate.DayOfWeek);
                        if (!isScheduled) return false;
                        var isGradedPending = !IsAlreadyGraded(dateTimeForThisSchedule);
                        if (!isGradedPending) return false;
                        var isAction = !IsScheduleMarkedAsNoActionAt(dateTimeForThisSchedule);
                        if (!isAction) return false;
                        var isEnabledDraw = IsEnabledDraw(dateTimeForThisSchedule, domain);
                        if (!isEnabledDraw) return false;
                        var isNewInAccumulator = !nextDatesAccumulator.Contains(state, withFireBall, dateTimeForThisSchedule, schedule.Lottery.IdOfLottery);
                        if (!isNewInAccumulator) return false;
                        return true;
                    }
                    );
                if (scheduleFound == null)
                {
                    currentDate = currentDate.AddDays(1);
                }
            } while (scheduleFound == null);

            var result = scheduleFound.ToDateTime(currentDate);
            return result;
        }

        [Obsolete]
        internal void Every(int hour, int minute, int dayOfWeek)
        {
            if (!(0 >= hour || hour <= 23)) throw new GameEngineException($"Hour {hour} is not valid. It should be at 24-hour time format");
            if (!(0 >= minute || minute <= 59)) throw new GameEngineException($"Minutes {minute} is not valid. Minutes parameter should be between 0 and 59");
            if (!(0 >= dayOfWeek || dayOfWeek <= 6)) throw new GameEngineException($"Day of week {dayOfWeek} is not valid. It should be between 0 and 6");

            const string DEFAULT_EMPLOYEE_NAME = "Admin";
            var DATE_OF_FRESH_EXECUTION_ON_PRODUCTION = new DateTime(2019, 04, 23);
            Every(hour, minute, dayOfWeek, DATE_OF_FRESH_EXECUTION_ON_PRODUCTION, DEFAULT_EMPLOYEE_NAME);
        }

        internal virtual void Every(int hour, int minute, int dayOfWeek, DateTime now, string employeeName)
        {
            if (!(0 >= hour || hour <= 23)) throw new GameEngineException($"Hour {hour} is not valid. It should be at 24-honew r should be between 0 and 59");
            if (!(0 >= dayOfWeek || dayOfWeek <= 6)) throw new GameEngineException($"Day of week {dayOfWeek} is not valid. It should be between 0 and 6");
            if (String.IsNullOrWhiteSpace(employeeName)) throw new ArgumentNullException(nameof(employeeName));

            DayOfWeek d = (DayOfWeek)dayOfWeek;
            var schedule = ScheduleAt(hour, minute, d, now, employeeName);
            var eventId = calendar.AddDailyEvent(d, new TimeSpan(hour, minute, 0), schedule);
            (schedule as WeeklySchedule).RegisterEvent(d, eventId);
        }

        private Schedule ScheduleAt(int hour, int minute, DayOfWeek day, DateTime now, string employeeName)
        {
            if (schedules.Exists(x => x.Hour.Hour == hour && x.Hour.Minute == minute))
            {
                var previous = schedules.Find(x => x.Hour.Hour == hour && x.Hour.Minute == minute);
                previous.Mark(day);
                var log = $"{employeeName} adds day {day} in schedule {hour}:{minute} at {now}<br>";
                WriteLog(previous, log);
                return previous;
            }
            else
            {
				var id = LotteryGame.Company.LotteryGamesPool.NextScheduleId();
                WeeklySchedule s = new WeeklySchedule(this as Lottery, id, (this as Lottery).Tickets, hour, minute, day);
                schedules.Add(s);
                var log = $"{employeeName} creates schedule {hour}:{minute} with day {day} at {now}<br>";
                WriteLog(s, log);
                return s;
            }
        }

		internal virtual void UpdateSchedule(bool itIsThePresent, string description, string newDaysOfWeek, int hour, int minute, DateTime now, string employeeName)
        {
            if (!(0 >= hour || hour <= 23)) throw new GameEngineException($"Hour {hour} is not valid. It should be at 24-hour time format");
            if (!(0 >= minute || minute <= 59)) throw new GameEngineException($"Minutes {minute} is not valid. Minutes parameter should be between 0 and 59");
            if (string.IsNullOrWhiteSpace(newDaysOfWeek)) throw new ArgumentNullException(nameof(newDaysOfWeek));
            if (now == default(DateTime)) throw new GameEngineException("Date cannot have default value");
            if (string.IsNullOrWhiteSpace(employeeName)) throw new ArgumentNullException(nameof(employeeName));
            foreach (char c in newDaysOfWeek) if (!char.IsDigit(c)) throw new GameEngineException("Days of week should be a number from 0..6");
            if (string.IsNullOrWhiteSpace(description)) throw new ArgumentNullException(nameof(description));

			var schedule = GetSchedule(hour, minute);
            if (HasPendingTicketsFor(schedule, newDaysOfWeek)) throw new GameEngineException($"Lottery still has pending tickets on {newDaysOfWeek}. Refound pending tickets before try to delete this day.");
            schedule.Update(itIsThePresent, description, newDaysOfWeek, hour, minute, now, employeeName);
        }

		internal virtual void UpdateDescription(bool itIsThePresent, string description, int hour, int minute)
		{
            if (!(0 >= hour || hour <= 23)) throw new GameEngineException($"Hour {hour} is not valid. It should be at 24-hour time format");
            if (!(0 >= minute || minute <= 59)) throw new GameEngineException($"Minutes {minute} is not valid. Minutes parameter should be between 0 and 59");
            if (string.IsNullOrWhiteSpace(description)) throw new ArgumentNullException(nameof(description));

            var schedule = GetSchedule(hour, minute);
            schedule.UpdateDescription(itIsThePresent, description);
        }
            
		internal void WriteLog(Schedule schedule, string message)
        {
            if (schedulesLog.ContainsKey(schedule))
            {
                schedulesLog[schedule].Insert(0, message);
            }
            else
            {
                var messageBuilder = new StringBuilder(message);
                schedulesLog.Add(schedule, messageBuilder);
            }
        }

        internal string LogFor(Schedule schedule)
        {
            if (schedule == null) throw new ArgumentNullException(nameof(schedule));
            var log = schedulesLog.ContainsKey(schedule) ? schedulesLog[schedule].ToString() : string.Empty;
            return log;
        }


        internal Schedule GetSchedule(int hour, int minute)
        {
            if (!(0 >= hour || hour <= 23)) throw new GameEngineException($"Hour {hour} is not valid. It should be at 24-hour time format");
            if (!(0 >= minute || minute <= 59)) throw new GameEngineException($"Minutes {minute} is not valid. Minutes parameter should be between 0 and 59");

            var result = GetScheduleOrNullFor(new DateTime(3000, 01, 01, hour, minute, 0));
            if (result == null) throw new GameEngineException($"Currently there is not any Schedule at this hour {hour}:{minute}");
            return result;
        }

        internal Schedule GetScheduleOrNullFor(DateTime date)
        {
            Schedule result = null;
            foreach (Schedule s in schedules)
            {
                if (s.ToDateTime(date) == date)
                {
                    result = s;
                    break;
                }
            }
            return result;
        }

        internal bool AnyScheduleAt(DateTime date)
        {
            if (date == default(DateTime)) throw new ArgumentNullException(nameof(date));

            var scheduleFound = GetScheduleOrNullFor(date);
            return scheduleFound != null;
        }

        internal abstract string GameType();

        internal void AddAllCalendarEvents()
        {
            foreach (var schedule in schedules)
            {
                foreach (var day in schedule.Days)
                {
                    var eventId = calendar.AddDailyEvent(day, new TimeSpan(schedule.Hour.Hour, schedule.Hour.Minute, 0), schedule);
                    (schedule as WeeklySchedule).RegisterEvent(day, eventId);
                }
            }
        }

        internal virtual void ModifyCalendarEvent(string eventId, DayOfWeek day, int hour, int minute)
        {
            if (string.IsNullOrWhiteSpace(eventId)) throw new ArgumentNullException(nameof(eventId));
            if (!(0 >= hour || hour <= 23)) throw new GameEngineException($"Hour {hour} is not valid. It should be at 24-hour time format");
            if (!(0 >= minute || minute <= 59)) throw new GameEngineException($"Minutes {minute} is not valid. Minutes parameter should be between 0 and 59");

            calendar.ModifyDailyEvent(eventId, new TimeSpan(hour, minute, 0), day);
        }

        internal string AddCalendarEvent(Schedule schedule, DayOfWeek day, int hour, int minute)
        {
            if (schedule == null) throw new ArgumentNullException(nameof(schedule));
            if (!(0 >= hour || hour <= 23)) throw new GameEngineException($"Hour {hour} is not valid. It should be at 24-hour time format");
            if (!(0 >= minute || minute <= 59)) throw new GameEngineException($"Minutes {minute} is not valid. Minutes parameter should be between 0 and 59");

            var eventId = calendar.AddDailyEvent(day, new TimeSpan(hour, minute, 0), schedule);
            return eventId;
        }

        internal void DeleteCalendarEvent(string eventId)
        {
            if (string.IsNullOrWhiteSpace(eventId)) throw new ArgumentNullException(nameof(eventId));

            calendar.DeleteEvent(eventId);
        }

        internal bool IsAttachedDate(DateTime date)
        {
            if (date == default(DateTime)) throw new GameEngineException("Date cannot have default value");

            var scheduleFound = GetScheduleOrNullFor(date);
			if (scheduleFound == null) return false;

			bool result = scheduleFound.IsAttachedDate(date);
            return result;
        }
    }

    internal abstract class LotteryForPicks : Lottery
    {
        internal LotteryForPicks(LotteryGame lotteries, State state) : base(lotteries, state)
        {
		}

        // Propiedades para FireBall que no se pudieron implemetar en class LotteryPick<TPick>
        internal abstract bool IsLotteryPickWithFireBall { get; }
        internal abstract bool IsFireBallTurnedOn { get; }
        internal abstract bool HasFireBall { get; }

        protected void CloneSchedules(LotteryForPicks primaryInstance)
        {
            this.schedules = primaryInstance.schedules;
        }

        internal void ForEach(int hour, int minute, string daysOfWeek, DateTime now, string employeeName)
		{
			if (!(0 >= hour || hour <= 23)) throw new GameEngineException($"Hour {hour} is not valid. It should be at 24-hour time format");
			if (!(0 >= minute || minute <= 59)) throw new GameEngineException($"Minutes {minute} is not valid. Minutes parameter should be between 0 and 59");
			if (String.IsNullOrWhiteSpace(daysOfWeek)) throw new ArgumentNullException(nameof(daysOfWeek));
			if (now == default(DateTime)) throw new GameEngineException("Date cannot have default value");
			if (String.IsNullOrWhiteSpace(employeeName)) throw new ArgumentNullException(nameof(employeeName));

			for (int i = 0; i < daysOfWeek.Length; i++)
			{
				int dayOfWeek = Int32.Parse(daysOfWeek[i] + "");
				if (!(0 >= dayOfWeek || dayOfWeek <= 6)) throw new GameEngineException($"Day of week {dayOfWeek} is not valid. It should be between 0 and 6");
				DayOfWeek d = (DayOfWeek)dayOfWeek;
				if (schedules.Exists(x => x.Hour.Hour == hour && x.Hour.Minute == minute)) throw new GameEngineException("Lottery already was scheduled at this time");
			}
			for (int i = 0; i < daysOfWeek.Length; i++)
			{
				int dayOfWeek = Int32.Parse(daysOfWeek[i] + "");
				Every(hour, minute, dayOfWeek, now, employeeName);
			}
		}

		internal void DeleteAllCalendarEvents()
		{
			foreach (var schedule in schedules)
			{
				foreach (var day in schedule.Days)
				{
					(schedule as WeeklySchedule).DeleteEvent(day);
				}
			}
		}

		protected override void Cancel(Schedule aSchedule, int dayOfWeek, DateTime now, string employeeName, Tickets tickets)
		{
			if (aSchedule.AreAllDaysDisabled()) throw new GameEngineException($"This lottery is already disabled on schedule of {aSchedule.Hour.Hour}:{aSchedule.Hour.Minute} cannot be disabled again");
			if (now == default(DateTime)) throw new GameEngineException("Date cannot have default value");
			if (!(0 >= dayOfWeek || dayOfWeek <= 6)) throw new GameEngineException($"Day of week {dayOfWeek} is not valid. It should be between 0 and 6");
			DayOfWeek day = (DayOfWeek)dayOfWeek;
			if (this.HasPendingTicketsFor(aSchedule, day)) throw new GameEngineException("Lottery still has pending tickets. Refound pending tickets before try to cancel.");

			int hour = aSchedule.Hour.Hour;
			int minute = aSchedule.Hour.Minute;
			if (!aSchedule.IsScheduled(day)) throw new GameEngineException($"There is not any Lottery scheduled {day} at {hour}:{minute}");
			var log = string.Empty;
			if (tickets.ExistTicketsAt(new HourMinute(hour, minute)))
			{
				aSchedule.Disable(day);
				log = $"{employeeName} disables day {day} in schedule {hour}:{minute} at {now}<br>";
				if (aSchedule.AreAllDaysDisabled())
				{
					aSchedule.Disable(now);
					log = $"{employeeName} disables schedule {hour}:{minute} at {now}<br>";
				}
			}
			else
			{
				var isTheLastDay = aSchedule.CountDays == 1;
				if (isTheLastDay)
				{
					schedules.Remove(aSchedule);
					log = $"{employeeName} removes schedule {hour}:{minute} at {now}<br>";
					TryToCancelLottery();
				}
				else
				{
					log = $"{employeeName} cancels day {day} in schedule {hour}:{minute} at {now}<br>";
					aSchedule.Cancel(day, now);
				}
			}
			(aSchedule as WeeklySchedule).DeleteEvent(day);
			WriteLog(aSchedule, log);
		}

		internal override void Cancel(Schedule aSchedule, DateTime now, string employeeName)
		{
			if (aSchedule.AreAllDaysDisabled()) throw new GameEngineException($"This lottery is already disabled on schedule of {aSchedule.Hour.Hour}:{aSchedule.Hour.Minute} cannot be disabled again");
			if (now == default(DateTime)) throw new GameEngineException("Date cannot have default value");
			if (String.IsNullOrWhiteSpace(employeeName)) throw new ArgumentNullException(nameof(employeeName));
			for (int dayOfWeek = 0; dayOfWeek <= 6; dayOfWeek++)
			{
				if (this.HasPendingTicketsFor(aSchedule, (DayOfWeek)dayOfWeek)) throw new GameEngineException("Lottery still has pending tickets. Refound pending tickets before try to cancel.");
			}
			for (int dayOfWeek = 0; dayOfWeek <= 6; dayOfWeek++)
			{
				if (aSchedule.IsScheduled((DayOfWeek)dayOfWeek))
				{
					Cancel(aSchedule, dayOfWeek, now, employeeName, this.Tickets);
				}
			}
		}
	}

	public enum LotteryDrawType { GRADE, REGRADE, NOACTION, CONFIRMATION };
    public enum GenericGameType { P2, P3, P4, P5, PB, TZ };

	public abstract class LotteryDrawMessage : TypedMessage
	{
        public GenericGameType GameType { get; private set; }
        public Localization Localization { get; private set; }
        public string State { get; private set; }
		public DateTime DrawDate { get; private set; }
		public string EmployeeName { get; private set; }
        public int ScheduleId { get; private set; }

        internal LotteryDrawMessage(LotteryDrawType type, GenericGameType gameType, string state, DateTime drawDate, string employeeName, int scheduleId) : base((char)(int)type)
		{
			if (string.IsNullOrWhiteSpace(state)) throw new ArgumentNullException(nameof(state));
			if (string.IsNullOrWhiteSpace(employeeName)) throw new ArgumentNullException(nameof(employeeName));
			if (drawDate.Second != 0 || drawDate.Millisecond != 0) throw new GameEngineException("Date of Lottery drawings can not have seconds");

            GameType = gameType;
			State = state;
			DrawDate = drawDate;
			EmployeeName = employeeName;
			ScheduleId = scheduleId;
            Localization = Integration.Localization;
		}

		public LotteryDrawMessage(string message) : base(message)
		{
		}

		public LotteryDrawType Type
		{
			get
			{
				return (LotteryDrawType)(int)base.MessageTypeSelector;
			}
		}

		protected override void InternalSerialize()
		{
			base.InternalSerialize();
			AddProperty((int)GameType).
			AddProperty((int)Localization).
			AddProperty(State).
			AddProperty(DrawDate).
			AddProperty(EmployeeName).
			AddProperty(ScheduleId);
		}

		protected override void Deserialize(string[] message, out int fieldOrder)
		{
			base.Deserialize(message, out fieldOrder);
			GameType = (GenericGameType)int.Parse(message[fieldOrder++]);
			Localization = (Localization)int.Parse(message[fieldOrder++]);
			State = message[fieldOrder++];
			int year = int.Parse(message[fieldOrder++]);
			int month = int.Parse(message[fieldOrder++]);
			int day = int.Parse(message[fieldOrder++]);
			int hour = int.Parse(message[fieldOrder++]);
			int minute = int.Parse(message[fieldOrder++]);
			int second = int.Parse(message[fieldOrder++]);
			DrawDate = new DateTime(year, month, day, hour, minute, second);
			EmployeeName = message[fieldOrder++];
			ScheduleId = int.Parse(message[fieldOrder++]);
		}
	}

	public class LotteryDrawGradeMessage : LotteryDrawMessage
	{
		public int FireBallNumber { get; private set; }
		public string WinnerNumbers { get; private set; }

		public LotteryDrawGradeMessage(GenericGameType gameType, string state, DateTime drawDate, string employeeName, int scheduleId, string winnerNumbers) :
			base(LotteryDrawType.GRADE, gameType, state, drawDate, employeeName, scheduleId)
		{
			if (string.IsNullOrWhiteSpace(winnerNumbers)) throw new ArgumentNullException(nameof(winnerNumbers));

			WinnerNumbers = winnerNumbers;
            FireBallNumber = LotteryDraw.WITHOUT_FIREBALL;
        }

        public LotteryDrawGradeMessage(GenericGameType gameType, string state, DateTime drawDate, string employeeName, int scheduleId, int fireBallNumber, string winnerNumbers) :
            base(LotteryDrawType.GRADE, gameType, state, drawDate, employeeName, scheduleId)
        {
            if (string.IsNullOrWhiteSpace(winnerNumbers)) throw new ArgumentNullException(nameof(winnerNumbers));

            WinnerNumbers = winnerNumbers;
			FireBallNumber = fireBallNumber;
        }

        public LotteryDrawGradeMessage(string message) : base(message)
		{
		}

		protected override void InternalSerialize()
		{
			base.InternalSerialize();
			AddProperty(WinnerNumbers);
			AddProperty(FireBallNumber);
		}

		protected override void Deserialize(string[] message, out int fieldOrder)
		{
			base.Deserialize(message, out fieldOrder);
			WinnerNumbers = message[fieldOrder++];
			FireBallNumber = int.Parse(message[fieldOrder++]);
		}
	}

	public class LotteryDrawRegradeMessage : LotteryDrawMessage
	{
		public LotteryDrawRegradeMessage(GenericGameType gameType, string state, DateTime drawDate, string employeeName, int scheduleId) :
			base(LotteryDrawType.REGRADE, gameType, state, drawDate, employeeName, scheduleId)
		{
		}

		public LotteryDrawRegradeMessage(string message) : base(message)
		{
		}

    }

	public class LotteryDrawNoActionMessage : LotteryDrawMessage
	{
		public LotteryDrawNoActionMessage(GenericGameType gameType, string state, DateTime drawDate, string employeeName, int scheduleId) :
			base(LotteryDrawType.NOACTION, gameType, state, drawDate, employeeName, scheduleId)
		{
		}

		public LotteryDrawNoActionMessage(string message) : base(message)
		{
		}
    }

	public class LotteryDrawConfirmationMessage : LotteryDrawMessage
	{
		public LotteryDrawConfirmationMessage(GenericGameType gameType, string state, DateTime drawDate, string employeeName, int scheduleId) :
			base(LotteryDrawType.CONFIRMATION, gameType, state, drawDate, employeeName, scheduleId)
		{
		}

		public LotteryDrawConfirmationMessage(string message) : base(message)
		{
		}
    }

	
}