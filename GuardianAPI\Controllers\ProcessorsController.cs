﻿using GamesEngine.Custodian.Persistance;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Threading.Tasks;
using System;

namespace GuardianAPI.Controllers
{
	public class ProcessorsController : AuthorizeController
	{
		private Transactions transactions = Transactions.GetInstance();

		[HttpGet("api/processors")]
		[Authorize(Roles = "h1,h2")]
		public async Task<IActionResult> ListAuthorizersAsync()
		{
			IActionResult result = await GuardianManagerAPI.GuardianManager.PerformQryAsync(HttpContext, $@"
				{{
					for(processor : company.System.PaymentProcessor.SearchProcessorsWithDistinctKey())
					{{
						print processor.Id id;
						print processor.DriverId driverId;
						print processor.Alias alias;
						print processor.Description description;
						print processor.Name name;
						print processor.CurrencyIso4217Code currency;
						print processor.PaymentMethodAsText group;
						for( transactionTypes : processor.Transactions.Types )
						{{
							print transactionTypes.Name transactionType;
						}}
					}}
				}}
			");
			return result;
		}

		[HttpGet("api/currencies")]
        [Authorize(Roles = "devops")]//VERIFY_ROLES
        public async Task<IActionResult> CurrenciesAsync()
		{
			var result = await GuardianManagerAPI.GuardianManager.PerformQryAsync(HttpContext, $@"
            {{
                for (currencies:company.System.EnabledCoins)
                {{
                    currency = currencies;
					print currency.Id id;
                    print currency.Iso4217Code currency;
                    print currency.UnicodeAsText unicode;
                    print currency.Name name;
					print currency.TypeAsText type;
                }}
            }}
            ");
			return result;
		}
	}
}
