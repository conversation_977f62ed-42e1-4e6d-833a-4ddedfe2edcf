﻿using log4net;
using System;
using System.Threading;

namespace Puppeteer.EventSourcing
{
	public sealed class Loggers
	{
		public static Loggers loggers = new Loggers();

		private Loggers()
		{
			Webhook = new Logger();
			Db = new Logger();
			Smart = new Logger();
			AccountingServicesASI = new Logger();
			AccountingServicesASIRemoveTransaction = new Logger();
			AccountingServicesASIPostTransaction = new Logger();
			AccountingServicesASIPostTransactionWRef = new Logger();
			AccountingServicesASIPostFreeFormTicket = new Logger();
			AccountingServicesASIPostFreeFormWagerCollection = new Logger();
			AccountingServicesASIPostFreeFormTicketAndWagers = new Logger();
			AccountingServicesASIGradeFreeFormWagerCollection = new Logger();
			AccountingServicesASISvcValidateCustomer = new Logger();
			AccountingServicesASIGetLottoCustomer = new Logger();
			AccountingServicesASIGetTicketWagers = new Logger();

			AccountingServicesDGS = new Logger();
			AccountingServicesDGSGetToken = new Logger();
			AccountingServicesDGSDeposit = new Logger();
			AccountingServicesDGSWithdraw = new Logger();
			AccountingServicesDGSValidateCustomer = new Logger();
			AccountingServicesDGSCreateWagers = new Logger();
			AccountingServicesDGSUpdateWagers = new Logger();

			AccountingConsignmentDeposit = new Logger();
			AccountingConsignmentWithdraw = new Logger();
			AccountingConsignmentGetCommittedNames = new Logger();
			AccountingConsignmentGetDepositInfo = new Logger();
			AccountingConsignmentGetDepositUpdate = new Logger();
			AccountingConsignmentGetName = new Logger();
			AccountingConsignmentGetPayoutDetail = new Logger();
			AccountingConsignmentGetPayoutInfo = new Logger();
			AccountingConsignmentGetPayoutUpdate = new Logger();
			AccountingConsignmentGetSenders = new Logger();
			AccountingConsignmentGetDeposits = new Logger();
			AccountingConsignmentGetPayouts = new Logger();

			Emails = new Logger();
			Jobs = new Logger();
			SearchEngine = new Logger();
			Scripts = new Logger();
			Betfair = new Logger();
		}
        public Logger Webhook { get; set; }
		public Logger Smart { get; set; }
        public Logger Db { get; set; }
		public Logger AccountingServicesASI { get; set; }
		public Logger AccountingServicesASIRemoveTransaction { get; set; }
		public Logger AccountingServicesASIPostTransaction { get; set; }
		public Logger AccountingServicesASIPostTransactionWRef { get; set; }
		public Logger AccountingServicesASIPostFreeFormTicket { get; set; }
		public Logger AccountingServicesASIPostFreeFormWagerCollection { get; set; }
		public Logger AccountingServicesASIPostFreeFormTicketAndWagers { get; set; }
		public Logger AccountingServicesASIGradeFreeFormWagerCollection { get; set; }
		public Logger AccountingServicesASISvcValidateCustomer { get; set; }
		public Logger AccountingServicesASIGetLottoCustomer { get; set; }
		public Logger AccountingServicesASIGetTicketWagers { get; set; }

		public Logger AccountingServicesDGS { get; set; }
		public Logger AccountingServicesDGSGetToken { get; set; }
		public Logger AccountingServicesDGSDeposit { get; set; }
		public Logger AccountingServicesDGSWithdraw { get; set; }
		public Logger AccountingServicesDGSValidateCustomer { get; set; }
		public Logger AccountingServicesDGSCreateWagers { get; set; }
		public Logger AccountingServicesDGSUpdateWagers { get; set; }

        public Logger AccountingConsignmentDeposit { get; set; }
		public Logger AccountingConsignmentWithdraw { get; set; }
		public Logger AccountingConsignmentGetCommittedNames { get; set; }
		public Logger AccountingConsignmentGetDepositInfo { get; set; }
		public Logger AccountingConsignmentGetDepositUpdate { get; set; }
		public Logger AccountingConsignmentGetName { get; set; }
		public Logger AccountingConsignmentGetPayoutDetail { get; set; }
		public Logger AccountingConsignmentGetPayoutInfo { get; set; }
		public Logger AccountingConsignmentGetPayoutUpdate { get; set; }
		public Logger AccountingConsignmentGetSenders { get; set; }
		public Logger AccountingConsignmentGetDeposits { get; set; }
		public Logger AccountingConsignmentGetPayouts { get; set; }

        public Logger Emails { get; set; }
		public Logger Jobs { get; set; }
		public Logger Betfair { get; set; }
		public Logger SearchEngine { get; set; }
		public Logger Scripts { get; set; }

		public static Loggers GetIntance()
		{
			return loggers;
		}
	}

	public sealed class Logger
	{
		private ILog logger = log4net.LogManager.GetLogger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);

		internal Logger()
		{
		}

		public void Error(string message, Exception e)
		{
			ThreadPool.QueueUserWorkItem(task => logger.Error(message, e));
		}

		public void Debug(string message)
		{
			ThreadPool.QueueUserWorkItem(task => logger.Debug(message));
		}

		public void SetLogger(ILog log)
		{
			this.logger = log;
		}
	}
}
