﻿using GamesEngine;
using Newtonsoft.Json.Linq;
using Puppeteer.EventSourcing;
using System;
using System.Collections.Generic;
using System.Linq;
using static GamesEngine.Finance.PaymentChannels;
using static GamesEngineMocks.LottoMockGenerator;
using static GamesEngineMocks.LottoMocks;
using static GamesEngineMocks.LottoMocks.GameTypes;

namespace GamesEngineMocks
{
    public class LottoMocks
    {
        private static int[] powerballDays = new int[] { (int)DayOfWeek.Wednesday, (int)DayOfWeek.Saturday };
        private static int[] noSundays = new int[] { (int)DayOfWeek.Monday, (int)DayOfWeek.Tuesday, (int)DayOfWeek.Wednesday, (int)DayOfWeek.Thursday, (int)DayOfWeek.Friday, (int)DayOfWeek.Saturday };
        private static int[] everyday = new int[] { (int)DayOfWeek.Monday, (int)DayOfWeek.Tuesday, (int)DayOfWeek.Wednesday, (int)DayOfWeek.Thursday, (int)DayOfWeek.Friday, (int)DayOfWeek.Saturday, (int)DayOfWeek.Sunday };

        public class DateAndState
        {
            private string state;
            private string date;
            public DateAndState(string date, string state)
            {
                if (string.IsNullOrEmpty(date)) throw new ArgumentException(nameof(date));
                if (string.IsNullOrEmpty(state)) throw new ArgumentException(nameof(state));
                this.date = date;
                this.state = state;
            }

            public object State { get { return state; } }
            public object Date { get { return date; } }
        }
        public static class GameTypes
        {
            public static readonly GameType Pick3 = new GameType("Pick3", "LotteryPick3");
            public static readonly GameType Pick4 = new GameType("Pick4", "LotteryPick4");
            private static Random random = new Random();
            public class GameType
            {
                private string name;
                private string className;

                public GameType(string name, string className)
                {
                    this.name = name;
                    this.className = className;
                }

                public string Name { get { return this.name; } }
                public string ClassName { get { return this.className; } }
            }

            public static GameType WithThisClassname(string gameType)
            {
                if (Pick3.ClassName.Equals(gameType))
                {
                    return Pick3;
                }
                else if (Pick4.ClassName.Equals(gameType))
                {
                    return Pick4;
                }
                else
                {
                    throw new Exception($@"There is no classname {gameType} registered in the GameTypes");
                }
            }

            public static string GetRandomSelectionMode()
            {
                int randomNumber = random.Next(1, 2);

                if (randomNumber == 1)
                {
                    return "Boxed";
                }
                else if (randomNumber == 2)
                {
                    return "Straight";
                }
                else
                {
                    throw new Exception("No valid option for Selection mode.");
                }
            }

            public static GameType GetRandomPickMode()
            {
                int randomNumber = random.Next(1, 3);

                if (randomNumber == 1)
                {
                    return Pick3;
                }
                else if (randomNumber == 2)
                {
                    return Pick4;
                }
                else
                {
                    throw new Exception("No valid option for Pick mode.");
                }
            }
        }

        public static void NoPurchases(Puppeteer.EventSourcing.Actor actor)
        {
            LottoMockGenerator mock = new LottoMockGenerator(actor).
                ScheduleDefaultLotteries(noSundays, everyday, powerballDays).
                CreatePlayer("406827", Agents.ARTEMIS).
				CreateCustomer("*********");
        }

        public static void PBDrawConfirmation(Puppeteer.EventSourcing.Actor actor)
        {
            string playerId = LottoMockGenerator.ACCOUNT_NUMBER_2;
            var tickets = new List<TicketPowerball>()
            {
                new TicketPowerball()
                {
                    Amount=0.3m, Date = "05/08/2019", DozenOfNumber="************", Powerplay=true                }
        };
            LottoMockGenerator mock = new LottoMockGenerator(actor).
                ScheduleDefaultLotteries(noSundays, everyday, powerballDays).
                BuyTicketPowerball(playerId, tickets).
                DrawPB(playerId, "05/08/2019 22:59:00", "************", 1);
        }

        public static void FewDrawsCreated(Puppeteer.EventSourcing.Actor actor)
        {
            int[] sundays = new int[] { (int)DayOfWeek.Sunday };

            LottoMockGenerator mock = new LottoMockGenerator(actor).
                 ScheduleLottery("TX", "pick3", 10, 15, sundays, "description test").
                 ScheduleLottery("TX", "pick4", 11, 15, sundays, "description test");
        }

        public static void BI(Actor actor)
        {
            LottoMockGenerator mock = new LottoMockGenerator(actor, "BI");
        }

        public static void BuySomeLottosAndDraw(Puppeteer.EventSourcing.Actor actor)
        {
            string playerId = LottoMockGenerator.ACCOUNT_NUMBER_2;
            string state = "TN";
            string[] numbersPick4 = new string[] { "1", "2", "3", "45" };
            string[] numbers1 = new string[] { "1", "2", "3" };
            string[] numbers2 = new string[] { "01", "12", "23" };
            string[] numbers3 = new string[] { "1", "1", "*" };
            string[] numbers4 = new string[] { "012", "4", "4" };
            string[] numbers5 = new string[] { "4", "5", "6" };
            string[] hours1 = new string[] { "8/24/2018 10:15:00", "8/24/2018 13:15:00" };
            string[] hours2 = new string[] { "8/24/2018 10:44:00", "8/24/2018 13:10:00" };
            string[] hours3 = new string[] { "8/24/2018 10:44:00", "8/24/2018 13:10:00" };
            string[] excludedNumbers3 = new string[] { "110", "111" };
            string drawDate = "8/24/2018 10:15:00", betAmount = "0.25";
            string now1 = "9/24/2018 10:20:00", now2 = "9/24/2018 11:20:00", now3 = "9/25/2018 10:25:00";
            LottoMockGenerator mock = new LottoMockGenerator(actor).
                ScheduleDefaultLotteries(noSundays, everyday).
                BuyTicketPick3Straight(betAmount, playerId, "TX", "9/11/2018 10:44:00", "balls", numbers1).
                BuyTicketPick3Straight(betAmount, playerId, "TX", "9/24/2018 10:44:00", "balls", numbers1).
                BuyTicketPick4Straight("0.5", playerId, "TX", "9/24/2018 10:44:00", "balls", numbersPick4).
                BuyTicketPick4Straight("0.5", playerId, "TX", "9/25/2018 10:44:00", "balls", numbersPick4).
                BuyTicketPick3Straight(betAmount, playerId, "TX", "9/25/2018 10:44:00", "balls", numbers1).
                BuyTicketPick4Straight("0.5", playerId, "TX", "9/26/2018 10:44:00", "balls", numbersPick4).
                BuyTicketPick3Straight(betAmount, playerId, "TX", "9/26/2018 10:44:00", "balls", numbers5).
                BuyTicketPick4Straight("0.5", playerId, state, drawDate, "balls", numbersPick4).
                BuyTicketPick3Straight(betAmount, playerId, state, drawDate, "balls", numbers1).
                BuyTicketPick3Straight("2.0", playerId, state, drawDate, "balls", numbers2).
                BuyTicketPick3Straight("2.0", playerId, state, drawDate, "balls", numbers3, 222222, excludedNumbers3).
                BuyTicketPick3Boxed("2.0", playerId, "NY", "9/12/2018 12:10:00", "balls", new string[] { "01", "23", "45" }).
                BuyTicketPick4Boxed("4.0", playerId, "NY", "9/12/2018 12:10:00", "balls", new string[] { "01", "23", "45", "67" }).
                BuyTicketPick3Boxed("1.5", playerId, "NY", "8/30/2018 12:10:00", "balls", new string[] { "012", "0", "01" }).
                BuyTicketPick4Boxed("1.5", playerId, "NY", "8/30/2018 12:10:00", "balls", new string[] { "012", "0", "01", "0" }).
                BuyTicketPick3Straight("0.75", playerId, "NY", "8/30/2018 12:10:00", "balls", numbers4).
                BuyTicketPick4Straight("0.5", playerId, "NY", "8/30/2018 12:10:00", "balls", numbersPick4).
                BuyTicketPick3Straight("0.75", playerId, "MD", "8/30/2018 12:13:00", "balls", numbers4).
                BuyTicketPick3Boxed("1.5", playerId, "MD", "8/30/2018 12:13:00", "balls", new string[] { "012", "0", "01" }).
                BuyTicketPick4Boxed("1.5", playerId, "MD", "8/30/2018 12:13:00", "balls", new string[] { "012", "0", "01", "0" }).
                DrawLottery("pick3", state, drawDate, "112").
                ConfirmDraw("pick3", state, drawDate).
                DisableDraw("pick3", "TN", "9/11/2018 19:15:00", "localhost").
                SetNoAction("pick3", "TX", "9/11/2018 10:44:00").
                SetNoAction("pick3", "NY", "9/12/2018 12:10:00").
                DrawLottery("pick3", "TX", "9/24/2018 10:44:00", "111", now2).
                SaveFavoriteNumbers("pick3", "straight", "TX", playerId, 0.75m, "favorite test 1", new string[] { "8/24/2018 10:44:00" }, new string[][] { new string[] { "1", "2", "3" } }).
                SaveFavoriteNumbers("pick3", "straight", "TX", playerId, 2.5m, "favorite test 2", hours2, new string[][] { new string[] { "5", "2", "36" } }).
                SaveFavoriteNumbers("pick4", "straight", "TX", playerId, 8m, "favorite test 3", hours2, new string[][] { new string[] { "1", "2", "3", "*" } }).
                AddDrawPreferenceForPlayer("pick3", "TX", playerId, hours3);
        }

		public static void FavoritesCases(Puppeteer.EventSourcing.Actor actor)
        {
            string playerId = LottoMockGenerator.ACCOUNT_NUMBER_2;
            LottoMockGenerator mock = new LottoMockGenerator(actor).
                ScheduleDefaultLotteries(noSundays, everyday).
                BuyTicketPick3Straight("0.25", playerId, "MD", "11/30/2018 12:13:00", "balls", new string[] { "1", "2", "4" }). //1
                BuyTicketPick3Boxed("0.25", playerId, "MD", "11/30/2018 12:13:00", "balls", new string[] { "2", "2", "2" }). //fake
                BuyTicketPick3Boxed("0.26", playerId, "MD", "11/30/2018 12:13:00", "MultipleInputSingleAmount", new string[] { "222" }). //2
                BuyTicketPick3Straight("0.5", playerId, "NY", "11/30/2018 12:10:00", "balls", new string[] { "2", "2", "23" }). //3
                BuyTicketPick3Boxed("0.5", playerId, "NY", "11/30/2018 12:10:00", "balls", new string[] { "2", "2", "23" }). //fake y 4
                BuyTicketPick3Boxed("0.5", playerId, "NY", "11/30/2018 12:10:00", "MultipleInputSingleAmount", new string[] { "222", "223" }). //fake y 4
                BuyTicketPick3Straight("0.5", playerId, "NY", "11/30/2018 12:10:00", "MultipleInputSingleAmount", new string[] { "222", "223" }). //fake
                BuyTicketPick3Straight("0.75", playerId, "GA", "11/30/2018 12:14:00", "MultipleInputSingleAmount", new string[] { "223", "232", "322" }). //5
                BuyTicketPick3Boxed("0.75", playerId, "GA", "11/30/2018 12:14:00", "balls", new string[] { "23", "23", "23" }, 222222, new string[] { "222", "333", "233", "323", "332" }). //fake
                BuyTicketPick3Straight("0.75", playerId, "GA", "11/30/2018 12:14:00", "balls", new string[] { "23", "23", "23" }, 222222, new string[] { "222", "333", "233", "323", "332" }). //fake
                BuyTicketPick3Boxed("1", playerId, "OH", "11/30/2018 12:14:00", "MultipleInputSingleAmount", new string[] { "222", "232", "322", "234" }). //fake, fake y 6
                SaveFavoriteNumbers("pick3", "straight", "MD", playerId, 0.25m, "favorite test1", new string[] { "11/30/2018 12:13:00" }, new string[][] { new string[] { "1", "2", "4" } }).
                SaveFavoriteNumbers("pick3", "boxed", "MD", playerId, 0.26m, "favorite test2", new string[] { "11/30/2018 12:13:00" }, new string[][] { new string[] { "2", "2", "2" } }).
                SaveFavoriteNumbers("pick3", "straight", "NY", playerId, 0.25m, "favorite test3", new string[] { "11/30/2018 12:10:00" }, new string[][] { new string[] { "2", "2", "23" } }).
                SaveFavoriteNumbers("pick3", "boxed", "NY", playerId, 0.25m, "favorite test4", new string[] { "11/30/2018 12:10:00" }, new string[][] { new string[] { "2", "2", "3" } }).
                SaveFavoriteNumbers("pick3", "straight", "GA", playerId, 0.25m, "favorite test5", new string[] { "11/30/2018 12:14:00" }, new string[][] { new string[] { "2", "2", "3" }, new string[] { "2", "3", "2" }, new string[] { "3", "2", "2" } }).
                SaveFavoriteNumbers("pick3", "boxed", "OH", playerId, 0.25m, "favorite test5", new string[] { "11/30/2018 12:14:00" }, new string[][] { new string[] { "2", "3", "2" }, new string[] { "3", "2", "2" } })
                ;
        }

        public static void Pick5Purchases(Puppeteer.EventSourcing.Actor actor)
        {
            string playerId = LottoMockGenerator.ACCOUNT_NUMBER_2;
            string[] numbers1 = new string[] { "12345" };
            string[] numbers2 = new string[] { "05678", "15678", "25678", "35678", "45678", "55678", "65678", "75678" };
            string betAmount = "2.0";
            LottoMockGenerator mock = new LottoMockGenerator(actor).
                ScheduleDefaultLotteries(everyday, everyday).
            // compras para hoy
                BuyTicketPick5Straight(betAmount, playerId, "DC", DateTime.Now.AddDays(1).ToString("MM/dd/yyyy ") + " 14:20:00", "MultipleInputSingleAmount", numbers1).
                BuyTicketPick5Straight(betAmount, playerId, "FL", DateTime.Now.AddDays(1).ToString("MM/dd/yyyy ") + " 13:30:00", "MultipleInputSingleAmount", numbers1).
                BuyTicketPick5Straight(betAmount, playerId, "OH", DateTime.Now.AddDays(1).ToString("MM/dd/yyyy ") + " 19:29:00", "MultipleInputSingleAmount", numbers2);
        }

        public static void PendingPurchases(Puppeteer.EventSourcing.Actor actor)
        {
            string playerId = LottoMockGenerator.ACCOUNT_NUMBER_2;
            string playerId2 = LottoMockGenerator.ACCOUNT_NUMBER_1;
            string state = "TN";
            string[] numbersPick4 = new string[] { "1234", "1235" };
            string[] numbers1 = new string[] { "123" };
            string[] numbers2 = new string[] { "012", "013", "112", "113", "022", "023", "123", "122"};
            string[] numbers3 = new string[] { "111", "112", "113", "114", "115", "116", "117", "118", "119", "110" };
            string[] numbers4 = new string[] { "044", "144", "244" };
            string[] numbers5 = new string[] { "456" };
            string[] hours1 = new string[] { "8/24/2018 10:15:00", "8/24/2018 13:15:00" };
            string[] hours2 = new string[] { "8/24/2018 10:44:00", "8/24/2018 13:10:00" };
            string[] hours3 = new string[] { "8/24/2018 10:44:00", "8/24/2018 13:10:00" };
            string[] excludedNumbers3 = new string[] { "110", "111" };
            string drawDateToNextWeek = DateTime.Now.AddDays(7).ToString("MM/dd/yyyy 10:15:00"), betAmount = "0.25";
            string drawDateToThisMonth = DateTime.Now.AddDays(14).ToString("MM/dd/yyyy 12:10:00");

            LottoMockGenerator mock = new LottoMockGenerator(actor).
                ScheduleDefaultLotteries(everyday, everyday).
            // compras para hoy
                BuyTicketPick3Straight(betAmount, playerId2, "TX", DateTime.Now.AddDays(1).ToString("MM/dd/yyyy ") + " 10:44:00", "MultipleInputSingleAmount", numbers1).
                BuyTicketPick4Straight("0.5", playerId2, "TX", DateTime.Now.AddDays(1).ToString("MM/dd/yyyy ") + " 10:44:00", "MultipleInputSingleAmount", numbersPick4).
                BuyTicketPick4Straight("0.5", playerId2, "TX", DateTime.Now.AddDays(1).ToString("MM/dd/yyyy ") + " 10:44:00", "MultipleInputSingleAmount", numbersPick4).
                BuyTicketPick3Straight(betAmount, playerId, "TX", DateTime.Now.AddDays(1).ToString("MM/dd/yyyy ") + " 10:44:00", "MultipleInputSingleAmount", numbers1).
                BuyTicketPick4Straight("0.5", playerId, "TX", DateTime.Now.AddDays(1).ToString("MM/dd/yyyy ") + " 10:44:00", "MultipleInputSingleAmount", numbersPick4).
                BuyTicketPick3Straight(betAmount, playerId, "TX", DateTime.Now.AddDays(1).ToString("MM/dd/yyyy ") + " 10:44:00", "MultipleInputSingleAmount", numbers5).
            // compras para la siguiente semana
                BuyTicketPick4Straight("0.5", playerId, state, drawDateToNextWeek, "MultipleInputSingleAmount", numbersPick4).
                BuyTicketPick3Straight(betAmount, playerId, state, drawDateToNextWeek, "MultipleInputSingleAmount", numbers1).
                BuyTicketPick3Straight("2.0", playerId, state, drawDateToNextWeek, "MultipleInputSingleAmount", numbers2).
                BuyTicketPick3Straight("2.0", playerId, state, drawDateToNextWeek, "MultipleInputSingleAmount", numbers3, 222222, excludedNumbers3).

            // compras para la 2da semana de mas arriba 
                BuyTicketPick3Boxed("2.0", playerId, "NY", drawDateToThisMonth, "MultipleInputSingleAmount", new string[] { "024", "034", "025", "035", "124", "134", "125", "135" }).
                BuyTicketPick4Boxed("4.0", playerId, "NY", drawDateToThisMonth, "MultipleInputSingleAmount", new string[] { "0246", "0256", "0247", "0257", "0346", "0356", "0347", "0357", "1246", "1256", "1247", "1257", "1346", "1356", "1347", "1357" }).
                BuyTicketPick3Boxed("1.5", playerId, "NY", drawDateToThisMonth, "MultipleInputSingleAmount", new string[] { "000", "001", "100", "101", "200", "201" }).
                BuyTicketPick4Boxed("1.5", playerId, "NY", drawDateToThisMonth, "MultipleInputSingleAmount", new string[] { "0000", "0010", "1000", "1010", "2000", "2010" }).
                BuyTicketPick3Straight("0.75", playerId, "NY", drawDateToThisMonth, "MultipleInputSingleAmount", numbers4).
                BuyTicketPick4Straight("0.5", playerId, "NY", drawDateToThisMonth, "MultipleInputSingleAmount", numbersPick4).

                BuyTicketPick3Straight("0.75", playerId, "MD", DateTime.Now.AddDays(32).ToString("MM/dd/yyyy ") + " 12:13:00", "MultipleInputSingleAmount", numbers4).
                BuyTicketPick3Boxed("1.5", playerId, "MD", DateTime.Now.AddDays(32).ToString("MM/dd/yyyy ") + " 12:13:00", "MultipleInputSingleAmount", new string[] { "000", "001", "100", "101", "200", "201" }).

                BuyTicketPick4Boxed("1.5", playerId, "MD", "8/30/2018 12:13:00", "MultipleInputSingleAmount", new string[] { "0000", "0010", "1000", "1010", "2000", "2010" });
        }


        public static void BuyManyLottosAndDraw(Puppeteer.EventSourcing.Actor actor)
        {
            var accountsById = new Dictionary<string, string>(){
                {"*********","iCDlytS7kCxVPfgc0uSVvfJDtkgoUtRUtOSb09HFuQk=" },
                {"*********","ID012345678901234567890123456789000001" },
                {"*********","ID012345678901234567890123456789000002" },
                {"*********","ID012345678901234567890123456789000003" },
                {"*********","ID012345678901234567890123456789000004" },
                {"*********","ID012345678901234567890123456789000005" }
            };
            var drawnDatesByState = new Dictionary<string, string>
            {
                {"7/23/2018 10:15:00","TN"},
                {"7/23/2018 10:44:00","TX"},
                {"7/23/2018 12:10:00","NY"},
                {"7/23/2018 12:13:00","MD"},
                {"7/23/2018 12:14:00","GA"},
                {"7/24/2018 10:15:00","TN"},
                {"7/24/2018 10:44:00","TX"},
                {"7/24/2018 12:10:00","NY"},
                {"7/24/2018 12:13:00","MD"},
                {"7/24/2018 12:14:00","GA"},
                {"7/25/2018 10:15:00","TN"},
                {"7/25/2018 10:44:00","TX"},
                {"7/25/2018 12:10:00","NY"},
                {"7/25/2018 12:13:00","MD"},
                {"7/25/2018 12:14:00","GA"},
                {"7/26/2018 10:15:00","TN"},
                {"7/26/2018 10:44:00","TX"},
                {"7/26/2018 12:10:00","NY"},
                {"7/26/2018 12:13:00","MD"},
                {"7/26/2018 12:14:00","GA"},
                {"7/27/2018 10:15:00","TN"},
                {"7/27/2018 10:44:00","TX"},
                {"7/27/2018 12:10:00","NY"},
                {"7/27/2018 12:13:00","MD"},
                {"7/27/2018 12:14:00","GA"},
                {"7/30/2018 10:15:00","TN"},
                {"7/30/2018 10:44:00","TX"},
                {"7/30/2018 12:10:00","NY"},
                {"7/30/2018 12:13:00","MD"},
                {"7/30/2018 12:14:00","GA"},
                {"7/31/2018 10:15:00","TN"},
                {"7/31/2018 10:44:00","TX"},
                {"7/31/2018 12:10:00","NY"},
                {"7/31/2018 12:13:00","MD"},
                {"7/31/2018 12:14:00","GA"},
                {"1/8/2018 10:15:00","TN"},
                {"1/8/2018 10:44:00","TX"},
                {"1/8/2018 12:10:00","NY"},
                {"1/8/2018 12:13:00","MD"},
                {"1/8/2018 12:14:00","GA"},
                {"2/8/2018 10:15:00","TN"},
                {"2/8/2018 10:44:00","TX"},
                {"2/8/2018 12:10:00","NY"},
                {"2/8/2018 12:13:00","MD"},
                {"2/8/2018 12:14:00","GA"},
                {"3/8/2018 10:15:00","TN"},
                {"3/8/2018 10:44:00","TX"},
                {"3/8/2018 12:10:00","NY"},
                {"3/8/2018 12:13:00","MD"},
                {"3/8/2018 12:14:00","GA"},
                {"6/8/2018 10:15:00","TN"},
                {"6/8/2018 10:44:00","TX"},
                {"6/8/2018 12:10:00","NY"},
                {"6/8/2018 12:13:00","MD"},
                {"6/8/2018 12:14:00","GA"},
                {"7/8/2018 10:15:00","TN"},
                {"7/8/2018 10:44:00","TX"},
                {"7/8/2018 12:10:00","NY"},
                {"7/8/2018 12:13:00","MD"},
                {"7/8/2018 12:14:00","GA"},
                {"8/8/2018 10:15:00","TN"},
                {"8/8/2018 10:44:00","TX"},
                {"8/8/2018 12:10:00","NY"},
                {"8/8/2018 12:13:00","MD"},
                {"8/8/2018 12:14:00","GA"},
                {"9/8/2018 10:15:00","TN"},
                {"9/8/2018 10:44:00","TX"},
                {"9/8/2018 12:10:00","NY"},
                {"9/8/2018 12:13:00","MD"},
                {"9/8/2018 12:14:00","GA"},
                {"8/10/2018 10:15:00","TN"},
                {"8/10/2018 10:44:00","TX"},
                {"8/10/2018 12:10:00","NY"},
                {"8/10/2018 12:13:00","MD"},
                {"8/10/2018 12:14:00","GA"},
                {"8/13/2018 10:15:00","TN"},
                {"8/13/2018 10:44:00","TX"},
                {"8/13/2018 12:10:00","NY"},
                {"8/13/2018 12:13:00","MD"},
                {"8/13/2018 12:14:00","GA"},
                {"8/14/2018 10:15:00","TN"},
                {"8/14/2018 10:44:00","TX"},
                {"8/14/2018 12:10:00","NY"},
                {"8/14/2018 12:13:00","MD"},
                {"8/14/2018 12:14:00","GA"},
                {"8/15/2018 10:15:00","TN"},
                {"8/15/2018 10:44:00","TX"},
                {"8/15/2018 12:10:00","NY"},
                {"8/15/2018 12:13:00","MD"},
                {"8/15/2018 12:14:00","GA"},
                {"8/16/2018 10:15:00","TN"},
                {"8/16/2018 10:44:00","TX"},
                {"8/16/2018 12:10:00","NY"},
                {"8/16/2018 12:13:00","MD"},
                {"8/16/2018 12:14:00","GA"},
                {"8/17/2018 10:15:00","TN"},
                {"8/17/2018 10:44:00","TX"},
                {"8/17/2018 12:10:00","NY"},
                {"8/17/2018 12:13:00","MD"},
                {"8/17/2018 12:14:00","GA"}
            };

            var pendingDatesByState = new Dictionary<string, string>
            {
                {"9/17/2018 10:15:00","TN"},
                {"9/17/2018 10:44:00","TX"},
                {"9/17/2018 12:10:00","NY"},
                {"9/17/2018 12:13:00","MD"},
                {"9/17/2018 12:14:00","GA"},
                {"9/18/2018 10:15:00","TN"},
                {"9/18/2018 10:44:00","TX"},
                {"9/18/2018 12:10:00","NY"},
                {"9/18/2018 12:13:00","MD"},
                {"9/18/2018 12:14:00","GA"},
                {"9/19/2018 10:15:00","TN"},
                {"9/19/2018 10:44:00","TX"},
                {"9/19/2018 12:10:00","NY"},
                {"9/19/2018 12:13:00","MD"},
                {"9/19/2018 12:14:00","GA"},
                {"9/20/2018 10:15:00","TN"},
                {"9/20/2018 10:44:00","TX"},
                {"9/20/2018 12:10:00","NY"},
                {"9/20/2018 12:13:00","MD"},
                {"9/20/2018 12:14:00","GA"},
                {"9/21/2018 10:15:00","TN"},
                {"9/21/2018 10:44:00","TX"},
                {"9/21/2018 12:10:00","NY"},
                {"9/21/2018 12:13:00","MD"},
                {"9/21/2018 12:14:00","GA"},
                {"9/24/2018 10:15:00","TN"},
                {"9/24/2018 10:44:00","TX"},
                {"9/24/2018 12:10:00","NY"},
                {"9/24/2018 12:13:00","MD"},
                {"9/24/2018 12:14:00","GA"}
            };

            var drawnDatesByState2 = new Dictionary<string, string>
            {
                {"8/20/2018 10:15:00","TN"},
                {"8/20/2018 10:44:00","TX"},
                {"8/20/2018 12:10:00","NY"},
                {"8/20/2018 12:13:00","MD"},
                {"8/20/2018 12:14:00","GA"},
                {"8/21/2018 10:15:00","TN"},
                {"8/21/2018 10:44:00","TX"},
                {"8/21/2018 12:10:00","NY"},
                {"8/21/2018 12:13:00","MD"},
                {"8/21/2018 12:14:00","GA"},
                {"8/22/2018 10:15:00","TN"},
                {"8/22/2018 10:44:00","TX"},
                {"8/22/2018 12:10:00","NY"},
                {"8/22/2018 12:13:00","MD"},
                {"8/22/2018 12:14:00","GA"},
                {"8/23/2018 10:15:00","TN"},
                {"8/23/2018 10:44:00","TX"},
                {"8/23/2018 12:10:00","NY"},
                {"8/23/2018 12:13:00","MD"},
                {"8/23/2018 12:14:00","GA"},
                {"8/24/2018 10:15:00","TN"},
                {"8/24/2018 10:44:00","TX"},
                {"8/24/2018 12:10:00","NY"},
                {"8/24/2018 12:13:00","MD"},
                {"8/24/2018 12:14:00","GA"} };

            LottoMockGenerator mock = new LottoMockGenerator(actor).ScheduleDefaultLotteries(noSundays, everyday).
                CreatePlayers(accountsById).
                BuyManyTicketFor(GameTypes.Pick3, "Straight", drawnDatesByState2, accountsById.Keys.ToArray()).
                BuyManyTicketFor(GameTypes.Pick4, "Straight", drawnDatesByState2, accountsById.Keys.ToArray()).
                BuyManyTicketFor(GameTypes.Pick3, "Boxed", drawnDatesByState2, accountsById.Keys.ToArray()).
                BuyManyTicketFor(GameTypes.Pick4, "Boxed", drawnDatesByState2, accountsById.Keys.ToArray()).
                BuyManyTicketFor(GameTypes.Pick3, "Straight", pendingDatesByState, accountsById.Keys.ToArray()).
                BuyManyTicketFor(GameTypes.Pick4, "Straight", pendingDatesByState, accountsById.Keys.ToArray()).
                BuyManyTicketFor(GameTypes.Pick3, "Boxed", pendingDatesByState, accountsById.Keys.ToArray()).
                BuyManyTicketFor(GameTypes.Pick4, "Boxed", pendingDatesByState, accountsById.Keys.ToArray()).
                DrawLottery("pick3", drawnDatesByState2).
                DrawLottery("pick4", drawnDatesByState2)
                ;
        }
        public static void ManyCustomersBuying(Puppeteer.EventSourcing.Actor actor)
        {
            DateTime start = new DateTime(2018, 9, 18);
            int countOfCustomers = 6000;
            Random r = new Random();
            var betAmount = r.Next(1, 25);
            var custommersById = new Dictionary<string, string>();

            for (int i = 0; i <= countOfCustomers; i++)
            {
                if (i == 0)
                {
                    custommersById.Add("*********", "iCDlytS7kCxVPfgc0uSVvfJDtkgoUtRUtOSb09HFuQk=");
                }
                else
                {
                    custommersById.Add("EL4" + i.ToString("D5"), "ID" + i.ToString("D20"));
                }
            }
            
            string[] numbersPick4 = new string[] { "1", "2", "3", "4" };
            string[] excludedNumbers3 = new string[] { "110", "111" };

            string today = DateTime.Now.ToString("MM/dd/yyyy"), date = "8/1/2018";


            LottoMockGenerator mock = new LottoMockGenerator(actor).
                CreatePlayers().
                ScheduleDefaultLotteries(noSundays, everyday);

            var result = mock.Perform($@"
            {{
                print Now now;
                schedules=lotto900.SchedulesByState(Now);
                for (scheduledLotteries:schedules)
                {{
                    lottery=scheduledLotteries.Lottery;
                    print lottery.State.Abbreviation state;
                    drawDate = lottery.NextValidDrawDate(scheduledLotteries,Now);
                    print drawDate.HHmmAMPM() scheduledHour;
                    print drawDate 'drawDate';
                    print scheduledLotteries.GetDescription() description;
                    print lottery.GameType() gameType;
                    print lottery.IsAlreadyDrawnAt(scheduledLotteries,Now) isAlreadyDrawnToday;
                    print scheduledLotteries.IsScheduledAt(Now) isForToday;
                    print scheduledLotteries.IsScheduledSaturday() isScheduledSaturday;
                    print scheduledLotteries.IsScheduledSunday() isScheduledSunday;
					print lottery.RemainingTimeInSecondsToNextDraw(scheduledLotteries, Now) timeInSecondsToNextDraw;
                }}
            }}
            ");
            JObject json = JObject.Parse(result);
            foreach (var sorteo in json["scheduledLotteries"])
            {
                var drawnDatesByState = new Dictionary<string, string>();
                string state = (string)sorteo["state"];
                string drawDate = (string)sorteo["drawDate"];
                string gameType = (string)sorteo["gameType"];
                bool isScheduledSaturday = (bool)sorteo["isScheduledSaturday"];
                bool isScheduledSunday = (bool)sorteo["isScheduledSunday"];
                String[] horaDelSorteo = drawDate.Split(" ");
                for (DateTime x = start; x < DateTime.Now; x = x.AddDays(1))
                {
                    DayOfWeek day = x.DayOfWeek;
                    if (day == DayOfWeek.Sunday)
                    {
                        if (isScheduledSunday == false)
                        {
                            continue;
                        }
                        if (isScheduledSaturday == false)
                        {
                            continue;
                        }
                    }
                    var fecha = DateTime.Now.ToString(x.ToString("dd/MM/yyyy") + " " + horaDelSorteo[1].ToString());
                    drawnDatesByState.Add(fecha, state);
                }
                if (gameType.Contains(GameTypes.Pick3.Name) == true)
                {
                    mock.BuyManyTicketFor(GameTypes.Pick3, "Straight", drawnDatesByState, custommersById.Values.ToArray());
                    foreach (var dateByState in drawnDatesByState)
                    {
                        string[] winnerNumbers = new string[] { "0", "0", "0" };
                        winnerNumbers[0] = r.Next(0, 2).ToString();
                        winnerNumbers[1] = r.Next(3, 5).ToString();
                        winnerNumbers[2] = r.Next(0, 9).ToString();
                        mock.DrawLottery("pick3", dateByState.Value, dateByState.Key, string.Join("", winnerNumbers));
                    }
                }
                else
                {
                    mock.BuyManyTicketFor(GameTypes.Pick4, "Straight", drawnDatesByState, custommersById.Values.ToArray());
                    foreach (var dateByState in drawnDatesByState)
                    {
                        string[] winnerNumbers = new string[] { "0", "0", "0", "0" };
                        winnerNumbers[0] = r.Next(0, 2).ToString();
                        winnerNumbers[1] = r.Next(0, 5).ToString();
                        winnerNumbers[2] = r.Next(6, 8).ToString();
                        winnerNumbers[3] = r.Next(0, 9).ToString();
                        mock.DrawLottery("pick4", dateByState.Value, dateByState.Key, string.Join("", winnerNumbers));
                    }
                }

            }
        }
        public static void ManyDrawsOff(Puppeteer.EventSourcing.Actor actor)
        {
            LottoMockGenerator mock = new LottoMockGenerator(actor).
                LotteriesInTheMorning(noSundays, everyday);
        }

        static string diaDeCompra = "";
        static int cantidadAComprar = 10000;
        static int gradeApartirDe = 600000;
        static int totalComprados = 0;
        static Queue<string> sorteosParaGrade = new Queue<string>();
        public static void ciclo(Puppeteer.EventSourcing.Actor actor)
        {
            int[] everyday = new int[] { (int)DayOfWeek.Monday, (int)DayOfWeek.Tuesday, (int)DayOfWeek.Wednesday, (int)DayOfWeek.Thursday, (int)DayOfWeek.Friday, (int)DayOfWeek.Saturday, (int)DayOfWeek.Sunday };

            LottoMockGenerator mock = new LottoMockGenerator(actor);
            var diaInicial = new DateTime(2018, 02, 01, 13, 44, 00);

            mock.ScheduleDefaultLotteries(everyday, everyday);
            for (int i = 0; i < 365; i++)
            {
                var nuevoDia = diaInicial.AddDays(i);
                diaDeCompra = nuevoDia.ToString("MM/dd/yyyy 13:44:00");
                sorteosParaGrade.Enqueue(diaDeCompra);
                Memoria(mock);
            }
        }

        public static void Memoria(LottoMockGenerator mock)
        {
            for (int i = 0; i < cantidadAComprar; i++)
            {
                string playerID = "ID012345678901234567890123456789" + i % 100;
                var custommersById = new Dictionary<string, string>();
                custommersById.Add("562430" + i % 100, playerID);
                mock.CreatePlayers();
                mock.BuyTicketPick3Straight("0.33", playerID, "AR", diaDeCompra, "balls", new string[] { "4", "5", "6" }, 222222, null);
            }
            totalComprados += cantidadAComprar;
            Console.WriteLine("total comprados: {0}", totalComprados);
            if (totalComprados > gradeApartirDe)
            {
                mock.DrawLottery("pick3", "AR", sorteosParaGrade.Dequeue(), "123");
                totalComprados -= cantidadAComprar;
            }
        }
        public static void Ticketsbuyed(Puppeteer.EventSourcing.Actor actor, int amountodUsers, int amounOfOrdersPerUser, DateTime endDayforAvailableDrawings)
        {
            if (amountodUsers < 3) throw new Exception("At least 3 users.");

            var drawnDatesByState = new Drawings();
            var custommersById = new Dictionary<string, string>();

            /*Create drawings*/
            Random r = new Random();
            string today = DateTime.Now.ToString("MM/dd/yyyy");
            LottoMockGenerator mock = new LottoMockGenerator(actor).
                ScheduleDefaultLotteries(noSundays, everyday);

            var result = mock.Perform($@"
            {{
                Now = 01/30/2019 02:12:15;
                print Now now;
                domain=company.Sales.DomainFrom('localhost.com');
                schedules=lotto900.SchedulesByState(Now,domain);
                for (scheduledLotteries:schedules)
                {{
                    lottery=scheduledLotteries.Lottery;
                    print lottery.State.Abbreviation state;
                    drawDate = lottery.NextValidDrawDate(scheduledLotteries,Now);
                    print drawDate.HHmmAMPM() scheduledHour;
                    print drawDate 'drawDate';
                    print scheduledLotteries.GetDescription() description;
                    print lottery.GameType() gameType;
                    print lottery.IsAlreadyDrawnAt(scheduledLotteries,Now) isAlreadyDrawnToday;
                    print scheduledLotteries.IsScheduledAt(Now) isForToday;
                    print scheduledLotteries.IsScheduledSaturday() isScheduledSaturday;
                    print scheduledLotteries.IsScheduledSunday() isScheduledSunday;
					print lottery.RemainingTimeInSecondsToNextDraw(scheduledLotteries, Now) timeInSecondsToNextDraw;
                }}
            }}
            ");
            JObject json = JObject.Parse(result);
            foreach (var sorteo in json["scheduledLotteries"])
            {
                string state = (string)sorteo["state"];
                string drawDate = (string)sorteo["drawDate"];
                string gameType = (string)sorteo["gameType"];
                bool isScheduledSaturday = (bool)sorteo["isScheduledSaturday"];
                bool isScheduledSunday = (bool)sorteo["isScheduledSunday"];
                String[] horaDelSorteo = drawDate.Split(" ");
                for (DateTime x = endDayforAvailableDrawings; x <= DateTime.Now; x = x.AddDays(1))
                {
                    DayOfWeek day = x.DayOfWeek;
                    if (day == DayOfWeek.Sunday)
                    {
                        if (isScheduledSunday == false)
                        {
                            continue;
                        }
                        if (isScheduledSaturday == false)
                        {
                            continue;
                        }
                    }

                    var fecha = x.ToString("MM/dd/yyyy") + " " + horaDelSorteo[1].ToString();
                    drawnDatesByState.Add(new DateAndState(fecha, state), GameTypes.WithThisClassname(gameType));
                }
            }
            /*End of create drawings*/
            /*Create customers*/
            amountodUsers = amountodUsers - 3;
            for (int i = 0; i <= amountodUsers; i++)
            {
                custommersById.Add("SB" + i.ToString("D8"), "ID" + i.ToString("D20"));
            }
            //Console.WriteLine("se van a crear todos los players");
            mock.CreatePlayers();
            //Console.WriteLine("Listo!");
            /*End Create customers*/
            custommersById.Add("*********", "iCDlytS7kCxVPfgc0uSVvfJDtkgoUtRUtOSb09HFuQk=");
            custommersById.Add("562430370", "ID012345678901234567890123456789012345");

            /*Console.WriteLine($@"Amount of avaible customer: {custommersById.Values.Count}");
            Console.WriteLine($@"Amount of pick 3 avaible drawings: { drawnDatesByState.DatesAndStateForgameType(GameTypes.Pick3).Count}");
            Console.WriteLine($@"Amount of pick 4 avaible drawings: { drawnDatesByState.DatesAndStateForgameType(GameTypes.Pick4).Count}");*/

            Random random = new Random();
            int amountOfOrders = 0;
            var temporalCustommers = new Dictionary<string, string>();
            var temporalDrawingandDateByState = new List<DateAndState>();
            foreach (var customer in custommersById)
            {
                for (int i = 0; i < amounOfOrdersPerUser; i++)
                {
                    temporalCustommers.Add(customer.Key, customer.Value);
                    GameType pickMode = GameTypes.GetRandomPickMode();
                    string selectionMode = GameTypes.GetRandomSelectionMode();

                    List<DateAndState> temporalDrawingandDatesByState = drawnDatesByState.DatesAndStateForgameType(pickMode);
                    int randomNumber = random.Next(0, temporalDrawingandDatesByState.Count - 1);
                    temporalDrawingandDateByState.Add(temporalDrawingandDatesByState[randomNumber]);

                    mock.BuyManyTicketFor(pickMode, selectionMode, temporalDrawingandDateByState, temporalCustommers.Values.ToArray());
                    amountOfOrders++;
                    temporalCustommers.Clear();
                    temporalDrawingandDateByState.Clear();
                    //Console.WriteLine($@"Order number {amountOfOrders} created for user {customer.Key} in pick {pickMode} with selection mode {selectionMode}.");
                }
            }

            //mock.BuyManyTicketFor(GameTypes.Pick3, "Boxed", drawnDatesByState.DatesAndStateForgameType(GameTypes.Pick3), custommersById.Values.ToArray());
            //mock.BuyManyTicketFor(GameTypes.Pick4, "Straight", drawnDatesByState.DatesAndStateForgameType(GameTypes.Pick4), custommersById.Values.ToArray());
            foreach (var drawnDateByState in drawnDatesByState.DatesAndStateByGameTypes)
            {
                mock.DrawLottery(drawnDateByState.Key.Name.ToString().ToLower(), drawnDateByState.Value);
            }
        }
        public static void ScheduleDrawsOfPick2andPick5(Puppeteer.EventSourcing.Actor actor)
        {
            string playerId = LottoMockGenerator.ACCOUNT_NUMBER_2;
            string state = "TN";
            string[] numbersPick4 = new string[] { "1", "2", "3", "45" };
            string[] numbers1 = new string[] { "1", "2", "3" };
            string[] numbers2 = new string[] { "01", "12", "23" };
            string[] numbers3 = new string[] { "1", "1", "*" };
            string[] numbers4 = new string[] { "012", "4", "4" };
            string[] numbers5 = new string[] { "4", "5", "6" };
            string[] hours1 = new string[] { "8/24/2018 10:15:00", "8/24/2018 13:15:00" };
            string[] hours2 = new string[] { "8/24/2018 10:44:00", "8/24/2018 13:10:00" };
            string[] hours3 = new string[] { "8/24/2018 10:44:00", "8/24/2018 13:10:00" };
            string[] excludedNumbers3 = new string[] { "110", "111" };
            string drawDate = "8/24/2018 10:15:00", betAmount = "0.25";
            LottoMockGenerator mock = new LottoMockGenerator(actor).
                ScheduleDefaultLotteries(noSundays, everyday).
                BuyTicketPick3Straight(betAmount, playerId, "TX", "9/11/2018 10:44:00", "balls", numbers1).
                BuyTicketPick3Straight(betAmount, playerId, "TX", "9/24/2018 10:44:00", "balls", numbers1).
                BuyTicketPick4Straight("0.5", playerId, "TX", "9/24/2018 10:44:00", "balls", numbersPick4).
                BuyTicketPick4Straight("0.5", playerId, "TX", "9/25/2018 10:44:00", "balls", numbersPick4).
                BuyTicketPick3Straight(betAmount, playerId, "TX", "9/25/2018 10:44:00", "balls", numbers1).
                BuyTicketPick4Straight("0.5", playerId, "TX", "9/26/2018 10:44:00", "balls", numbersPick4).
                BuyTicketPick3Straight(betAmount, playerId, "TX", "9/26/2018 10:44:00", "balls", numbers5).
                BuyTicketPick4Straight("0.5", playerId, state, drawDate, "balls", numbersPick4).
                BuyTicketPick3Straight(betAmount, playerId, state, drawDate, "balls", numbers1).
                BuyTicketPick3Straight("2.0", playerId, state, drawDate, "balls", numbers2).
                BuyTicketPick3Straight("2.0", playerId, state, drawDate, "balls", numbers3, 222222, excludedNumbers3).
                BuyTicketPick3Boxed("2.0", playerId, "NY", "9/12/2018 12:10:00", "balls", new string[] { "01", "23", "45" }).
                BuyTicketPick4Boxed("4.0", playerId, "NY", "9/12/2018 12:10:00", "balls", new string[] { "01", "23", "45", "67" }).
                BuyTicketPick3Boxed("1.5", playerId, "NY", "8/30/2018 12:10:00", "balls", new string[] { "012", "0", "01" }).
                BuyTicketPick4Boxed("1.5", playerId, "NY", "8/30/2018 12:10:00", "balls", new string[] { "012", "0", "01", "0" }).
                BuyTicketPick3Straight("0.75", playerId, "NY", "8/30/2018 12:10:00", "balls", numbers4).
                BuyTicketPick4Straight("0.5", playerId, "NY", "8/30/2018 12:10:00", "balls", numbersPick4).
                BuyTicketPick3Straight("0.75", playerId, "MD", "8/30/2018 12:13:00", "balls", numbers4).
                BuyTicketPick3Boxed("1.5", playerId, "MD", "8/30/2018 12:13:00", "balls", new string[] { "012", "0", "01" }).
                BuyTicketPick4Boxed("1.5", playerId, "MD", "8/30/2018 12:13:00", "balls", new string[] { "012", "0", "01", "0" }).
                DrawLottery("pick3", state, drawDate, "112").
                DrawLottery("pick3", state, drawDate, "999").
                DrawLottery("pick3", state, drawDate, "123").
                ConfirmDraw("pick3", state, drawDate).
                DisableDraw("pick3", "TN", "9/11/2018 19:15:00", "localhost").
                SetNoAction("pick3", "TX", "9/11/2018 10:44:00").
                SetAction("pick3", "TX", "9/11/2018 10:44:00").
                DrawLottery("pick3", "TX", "9/11/2018 10:44:00", "111").
                SetNoAction("pick3", "NY", "9/12/2018 12:10:00").
                DrawLottery("pick3", "TX", "9/24/2018 10:44:00", "111").
                DrawLottery("pick3", "TX", "9/25/2018 10:44:00", "123").
                SaveFavoriteNumbers("pick3", "straight", "TX", playerId, 0.75m, "favorite test 1", new string[] { "8/24/2018 10:44:00" }, new string[][] { new string[] { "1", "2", "3" } }).
                SaveFavoriteNumbers("pick3", "straight", "TX", playerId, 2.5m, "favorite test 2", hours2, new string[][] { new string[] { "5", "2", "36" } }).
                SaveFavoriteNumbers("pick4", "straight", "TX", playerId, 8m, "favorite test 3", hours2, new string[][] { new string[] { "1", "2", "3", "*" } }).
                AddDrawPreferenceForPlayer("pick3", "TX", playerId, hours3);
        }

    }

    public class Drawings
    {
        private Dictionary<GameType, List<DateAndState>> datesAndStateByGameTypes = new Dictionary<GameType, List<DateAndState>>();

        public Dictionary<GameType, List<DateAndState>> DatesAndStateByGameTypes
        {
            get { return datesAndStateByGameTypes; }
            set { datesAndStateByGameTypes = value; }
        }

        public Drawings()
        {
            datesAndStateByGameTypes.Add(GameTypes.Pick3, new List<DateAndState>());
            datesAndStateByGameTypes.Add(GameTypes.Pick4, new List<DateAndState>());
        }

        public object Fecha { get; }
        public object State { get; }

        public void Add(DateAndState dateAndState, GameType gameType)
        {
            datesAndStateByGameTypes[gameType].Add(dateAndState);
        }

        public List<DateAndState> DatesAndStateForgameType(GameType gameType)
        {
            return datesAndStateByGameTypes[gameType];
        }
    }
}
