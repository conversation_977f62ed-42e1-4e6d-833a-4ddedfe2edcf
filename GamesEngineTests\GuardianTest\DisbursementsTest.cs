﻿using GamesEngine.Business;
using GamesEngine.Exchange;
using GamesEngine.Finance;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using System;
using System.Collections.Generic;
using System.Linq;
using GamesEngine;
using Unit.Games.Tools;
using GamesEngine.Settings;
using GamesEngine.Custodian;
using System.Threading.Tasks;
using GamesEngine.Custodian.Operations;
using static GamesEngine.Custodian.Operations.Operation;
using GamesEngine.Custodian.Persistance;
using GamesEngineTests.Unit_Tests.Games.Lotto;
using town.connectors.drivers;
using static town.connectors.drivers.Result;
using static GamesEngine.Business.WholePaymentProcessor;
using System.Reflection;
using GamesEngine.Domains;

namespace GamesEngineTests.Custodian
{

	[TestClass]
	public class GuardianDisbursementsTest
	{
		[TestInitialize]
		public void Initialize()
		{
			typeof(WholePaymentProcessor).GetField("_wholePaymentProcessor", System.Reflection.BindingFlags.NonPublic | BindingFlags.Static).SetValue(null, null);
		}

		[TestMethod]
		public void Controller_Add_if_New_Disbursements_The_First_Time()
		{
			CurrenciesTest.AddCurrencies();

			DateTime today = DateTime.Now;
			Dollar total = new Dollar(300);

			DisbursementsModel stored = new DisbursementsModel();
			stored.Disbursements = new List<DisbursementModel>();

			List<DisbursementRequest> disbursementRequests = new List<DisbursementRequest>();
			disbursementRequests.Add(new DisbursementRequest() { Id=1, AccountNumber="1232", Amount=200, ScheduledDate= today.ToString("MM/dd/yyyy HH:mm:ss") });

			List<DisbursementRequest> disbursementstoBeUpdated = stored.ToBeAddedOrChanged(disbursementRequests);
			List<int> disbursementIdstoBeRemoved = stored.ToBeRemoved(disbursementRequests);

			Assert.AreEqual(1, disbursementstoBeUpdated.Count);
			Assert.AreEqual(0, disbursementIdstoBeRemoved.Count);
		}

		[TestMethod]
		public void Controller_Remove_Existing_Disbursements_The_First_Time()
		{
			CurrenciesTest.AddCurrencies();

			DateTime today = DateTime.Now;
			Dollar total = new Dollar(300);

			DisbursementsModel stored = new DisbursementsModel();
			stored.Disbursements = new List<DisbursementModel>();
			stored.Disbursements.Add(new DisbursementModel() { AccountNumber = "234324", Id = 1, ScheduledDate = today.AddDays(1).ToString(), Amount=20 });
			stored.Disbursements.Add(new DisbursementModel() { AccountNumber = "234324", Id = 2, ScheduledDate = today.AddDays(1).ToString(), Amount = 20 });
			stored.Disbursements.Add(new DisbursementModel() { AccountNumber = "234324", Id = 3, ScheduledDate = today.AddDays(1).ToString(), Amount = 20 });
			stored.Amount = 3;

			List<DisbursementRequest> disbursementRequests = new List<DisbursementRequest>();
			disbursementRequests.Add(new DisbursementRequest() { Id = 3, AccountNumber = "1232", Amount = 200, ScheduledDate = today.ToString("MM/dd/yyyy HH:mm:ss") });

			List<DisbursementRequest> disbursementstoBeUpdated = stored.ToBeAddedOrChanged(disbursementRequests);
			List<int> disbursementIdstoBeRemoved = stored.ToBeRemoved(disbursementRequests);

			Assert.AreEqual(1, disbursementstoBeUpdated.Count);
			Assert.AreEqual(2, disbursementIdstoBeRemoved.Count);
		}


		[TestMethod]
		public void DisbursementsChanges()
		{
			CurrenciesTest.AddCurrencies();
			Company cia;
			Guardian guardian;
			QueueInMemory queue;
			DateTime today = DateTime.Now;
			GuardianTest.Init(today, out cia, out guardian, out queue);
			Dollar total = new Dollar(300);
			AccountNumbers accounts = guardian.Accounts();
			bool itsThePresent = true;
			AccountNumber account = accounts.Search("3770-4512-5467-6352");

			Disbursement disbursement1 = new DisbursementChange(1, account, today, 100);

			Disbursements disbursements = new Disbursements();
			disbursements.Add(disbursement1);
			disbursements.Add(new DisbursementChange(2, account, today, 100));
			disbursements.Add(new DisbursementChange(3, account, today, 100));
			Disbursements disbursementsShadow = disbursements.MakeACopyUnExecuted();

			Assert.AreEqual(3, disbursements.Count());
			Assert.AreEqual(3, disbursementsShadow.Count());
			Assert.AreNotEqual(disbursements, disbursementsShadow);

			DisbursementsChanges changes1 = new DisbursementsChanges(today, guardian);
			changes1.Add(itsThePresent, 1, account.Number, today, 150);
			changes1.Add(itsThePresent, 2, account.Number, today, 50);
			Disbursements.ValidateChanges(disbursements, today, changes1, total);
			disbursements.Update(changes1);

			Assert.AreEqual(150, disbursement1.Amount);

			DisbursementsChanges changes2 = new DisbursementsChanges(today, guardian);
			changes2.Add(itsThePresent, 1, account.Number, today, 150);
			changes2.Add(itsThePresent, 2, account.Number, today, 50);
			Disbursements.ValidateChanges(disbursements, today, changes2, total);
			disbursements.Update(changes2);

			Assert.AreEqual(150, disbursement1.Amount);
			Assert.AreEqual(account.Number, disbursement1.Account.Number);

			DateTime nextDay = today.AddDays(1);
			DisbursementsChanges changes3 = new DisbursementsChanges(today, guardian);
			changes3.Add(itsThePresent, 1, account.Number, nextDay, 150);
			changes3.Add(itsThePresent, 2, account.Number, today, 50);
			Disbursements.ValidateChanges(disbursements, today, changes3, total);
			disbursements.Update(changes3);

			Assert.AreEqual(150, disbursement1.Amount);
			Assert.AreEqual(account.Number, disbursement1.Account.Number);
			Assert.AreEqual(nextDay, disbursement1.ScheduledDate);

			Integration.RestoreToDefaultSettings();
		}

		[TestMethod]
		public void DisbursementsChangesValidations()
		{
			CurrenciesTest.AddCurrencies();

			Company cia;
			Guardian guardian;
			QueueInMemory queue;
			DateTime today = DateTime.Now;
			GuardianTest.Init(today, out cia, out guardian, out queue);
			Dollar total = new Dollar(300);
			AccountNumbers accounts = guardian.Accounts();
			bool itIsThePresent = true;

			AccountNumber account = accounts.Search("3770-4512-5467-6352");

			Disbursement disbursement1 = new DisbursementChange(1, account, today, 100);

			Disbursements disbursements = new Disbursements();
			disbursements.Add(disbursement1);
			disbursements.Add(new DisbursementChange(2, account, today, 100));
			disbursements.Add(new DisbursementChange(3, account, today, 100));
			Disbursements disbursementsShadow = disbursements.MakeACopyUnExecuted();

			Assert.AreEqual(3, disbursements.Count());
			Assert.AreEqual(3, disbursementsShadow.Count());
			Assert.AreNotEqual(disbursements, disbursementsShadow);

			DisbursementsChanges changes = new DisbursementsChanges(today, guardian);
			changes.Remove(3);
			changes.Add(itIsThePresent, 2, account.Number, today, 200);
			Disbursements.ValidateChanges(disbursements, today, changes, total);
			disbursements.Update(changes);
			
			Assert.AreEqual(2, disbursements.Count());
			Assert.AreEqual(3, disbursementsShadow.Count());

			try
			{
				DisbursementsChanges changes1 = new DisbursementsChanges(today, guardian);
				changes1.Remove(3);
				Disbursements.ValidateChanges(disbursements, today, changes1, total);
				disbursements.Update(changes1);

				Assert.Fail($"Id 3 does not exists.");
			}
			catch (GameEngineException e)
			{ }

			DisbursementsChanges changes2 = new DisbursementsChanges(today, guardian);
			changes2.Remove(2);
			changes2.Add(itIsThePresent, 1, account.Number, today, 300);
			Disbursements.ValidateChanges(disbursements, today, changes2, total);
			disbursements.Update(changes2);

			Assert.AreEqual(1, disbursements.Count());
			Assert.AreEqual(3, disbursementsShadow.Count());

			Integration.RestoreToDefaultSettings();
		}

		[TestMethod]
		public async Task DisbursementsAsync()
		{
			CurrenciesTest.AddCurrencies();
			Company cia;
			Guardian guardian;
			QueueInMemory queue;
			DateTime today = DateTime.Now;
			GuardianTest.Init(today, out cia, out guardian, out queue);
			AccountNumbers accounts = guardian.Accounts();
			bool itIsThePresent = true;
			Transactions transactions = Transactions.GetInstance();

			AccountNumber account = accounts.Search("3770-4512-5467-6352");

			PaymentProcessorsAndActionsByDomains processsors = guardian.PaymentProcessors();

			PaymentProcessor processsor  = processsors.SearchById("TEST-Cash-TestDepositUSD-USD-1");

			string domainUrl = "localhost";
			int domainId = 1;

			Dollar withdrawalAmount = new Dollar(4);

			Operations operations = guardian.Operations();
            PendingWithDrawal withdrawal = operations.CreateWithdrawal(itIsThePresent, DateTime.Now, 1, 1, "5D2428442", withdrawalAmount, processsor.Group.Id, processsor, domainId, domainUrl, "Bart");

			DateTime scheculeddate1 = today.AddDays(1);
			DateTime scheculeddate2 = today.AddDays(3);
			DateTime scheculeddate3 = today.AddDays(5);
			DisbursementsChanges changes1 = new DisbursementsChanges(today, guardian);
			changes1.Add(itIsThePresent, 1, account.Number, scheculeddate1, 1);
			changes1.Add(itIsThePresent, 2, account.Number, scheculeddate2, 1);
			changes1.Add(itIsThePresent, 3, account.Number, scheculeddate3, 2);
			withdrawal.Apply(itIsThePresent, today, changes1);

			Accumulate accumulate;
			bool result = account.GetAccumulate(scheculeddate1, withdrawal.Coin, out accumulate);
			Assert.AreEqual(false, result);

			Profiles profielsAuthorizedByTheOwner = new Profiles();
			profielsAuthorizedByTheOwner = new Profiles();
			profielsAuthorizedByTheOwner.Add(guardian.Profiles().SearchByName("Financial Manager"));
			profielsAuthorizedByTheOwner.Add(guardian.Profiles().SearchByName("General Manager"));
			operations.Approve(itIsThePresent, today, withdrawal.TransactionId, "<EMAIL>", profielsAuthorizedByTheOwner);

			InprocessWithDrawal inprocessWithDrawal = (InprocessWithDrawal)guardian.Operations().SearchFor(1);

			account.GetAccumulate(scheculeddate1, inprocessWithDrawal.Coin, out accumulate);
			Assert.AreEqual(1, accumulate.Value);

			account.GetAccumulate(scheculeddate2, inprocessWithDrawal.Coin, out accumulate);
			Assert.AreEqual(1, accumulate.Value);

			account.GetAccumulate(scheculeddate3, inprocessWithDrawal.Coin, out accumulate);
			Assert.AreEqual(2, accumulate.Value);

			GuardianTest.ValidateAccountsCreations(queue);

			string msn = queue.Dequeue(Integration.Kafka.TopicForGuardianInternalOperations);
			InternalOperationUpdateMessage withdrawalFromKafka = (InternalOperationUpdateMessage)InternalOperationMessage.Factory(msn);
			Assert.AreEqual(inprocessWithDrawal.Description, withdrawalFromKafka.Description);
			Assert.AreEqual(inprocessWithDrawal.DisbursementAmount.Value, withdrawalFromKafka.DisbursementAmount.Value);
			Assert.AreEqual(inprocessWithDrawal.DisbursementAmount.CurrencyCode, withdrawalFromKafka.DisbursementAmount.CurrencyCode);
			Assert.AreEqual(inprocessWithDrawal.Group, withdrawalFromKafka.Group);
			Assert.AreEqual(inprocessWithDrawal.Processor.Driver.Id, withdrawalFromKafka.ProcessorId);
			Assert.AreEqual(inprocessWithDrawal.TransactionId, withdrawalFromKafka.TransactionId);

			transactions.Save(withdrawalFromKafka);

			IEnumerable<StoredOperation> storedPendingOperation = await transactions.ListPendingOperationsAsync(1, OperationSchedule.Both, today.AddDays(-10), today.AddDays(10), 100, 0);
			Assert.AreEqual(true, storedPendingOperation.Count() > 0);

			Integration.RestoreToDefaultSettings();
            //GamesEngine.Custodian.PaymentMethodsActions.ResetInstance();
		}

		[TestMethod]
		public void ListAccumulates_Test()
		{
			CurrenciesTest.AddCurrencies();

			Company cia;
			CurrenciesTest.AddCurrencies();
			Guardian guardian;
			QueueInMemory queue;
			DateTime today = DateTime.Now;
			GuardianTest.Init(today, out cia, out guardian, out queue);
			AccountNumbers accounts = guardian.Accounts();
			bool itIsThePresent = true;
			Transactions transactions = Transactions.GetInstance();

			AccountNumber accountInDollars = accounts.Search("3770-4512-5467-6352");
			AccountNumber accountInBTC = accounts.Search("3d1b1b52e955530b935cbe260d1f63c26edf9f8748b4a2550ff23005710c803a");

			PaymentProcessorsAndActionsByDomains processsors = guardian.PaymentProcessors();

			PaymentProcessor processsor  = processsors.SearchById("TEST-Cash-TestDepositUSD-USD-1");

			string domainUrl = "localhost";
			int domainId = 1;

			Dollar withdrawalAmount = new Dollar(4);

			Operations operations = guardian.Operations();
            PendingWithDrawal withdrawal = operations.CreateWithdrawal(itIsThePresent, DateTime.Now, 1, 1, "5D2428442", withdrawalAmount, processsor.Group.Id, processsor, domainId, domainUrl, "Bart");

			DateTime scheculeddate1 = today.AddDays(1);
			DateTime scheculeddate2 = today.AddDays(3);
			DateTime scheculeddate3 = today.AddDays(5);
			DisbursementsChanges changes1 = new DisbursementsChanges(today, guardian);
			changes1.Add(itIsThePresent, 1, accountInDollars.Number, scheculeddate1, 1);
			changes1.Add(itIsThePresent, 2, accountInDollars.Number, scheculeddate2, 1);
			changes1.Add(itIsThePresent, 3, accountInDollars.Number, scheculeddate3, 2);
			withdrawal.Apply(itIsThePresent, today, changes1);

			Accumulate accumulate;
			accountInDollars.GetAccumulate(scheculeddate1, withdrawal.Coin, out accumulate);
			accountInDollars.GetAccumulate(scheculeddate2, withdrawal.Coin, out accumulate);
			accountInDollars.GetAccumulate(scheculeddate3, withdrawal.Coin, out accumulate);

			Profiles profielsAuthorizedByTheOwner = new Profiles();
			profielsAuthorizedByTheOwner = new Profiles();
			profielsAuthorizedByTheOwner.Add(guardian.Profiles().SearchByName("Financial Manager"));
			profielsAuthorizedByTheOwner.Add(guardian.Profiles().SearchByName("General Manager"));
			operations.Approve(itIsThePresent, today, withdrawal.TransactionId, "<EMAIL>", profielsAuthorizedByTheOwner);

			AccountNumbers accountsStored = (AccountNumbers)guardian.Accounts();
			AccountNumberAndAccumulates accountsFiltered = (AccountNumberAndAccumulates)accountsStored.ListAccumulates(today.AddDays(-1), today.AddDays(6), 0, 100, "");
			Assert.AreEqual(3, accountsFiltered.List().Count());

			accountsFiltered = (AccountNumberAndAccumulates)accountsStored.ListAccumulates(today.AddDays(-1), today.AddDays(6), 0, 1, "");
			Assert.AreEqual(1, accountsFiltered.List().Count());
			Assert.AreEqual(scheculeddate1, accountsFiltered.List().First().ScheduledDate);

			accountsFiltered = (AccountNumberAndAccumulates)accountsStored.ListAccumulates(today.AddDays(-1), today.AddDays(6), 1, 1, "");
			Assert.AreEqual(1, accountsFiltered.List().Count());
			Assert.AreEqual(scheculeddate2, accountsFiltered.List().First().ScheduledDate);

			accountsFiltered = (AccountNumberAndAccumulates)accountsStored.ListAccumulates(today.AddDays(-1), today.AddDays(6), 2, 1, "");
			Assert.AreEqual(1, accountsFiltered.List().Count());
			Assert.AreEqual(scheculeddate3, accountsFiltered.List().First().ScheduledDate);

			accountsFiltered = (AccountNumberAndAccumulates)accountsStored.ListAccumulates(today.AddDays(-1), today.AddDays(1), 0, 100, "");
			Assert.AreEqual(1, accountsFiltered.List().Count());
			Assert.AreEqual(scheculeddate1, accountsFiltered.List().First().ScheduledDate);

			accountsFiltered = (AccountNumberAndAccumulates)accountsStored.ListAccumulates(today.AddDays(2), today.AddDays(4), 0, 100, "");
			Assert.AreEqual(1, accountsFiltered.List().Count());
			Assert.AreEqual(scheculeddate2, accountsFiltered.List().First().ScheduledDate);

			accountsFiltered = (AccountNumberAndAccumulates)accountsStored.ListAccumulates(today.AddDays(4), today.AddDays(6), 0, 100, "");
			Assert.AreEqual(1, accountsFiltered.List().Count());
			Assert.AreEqual(scheculeddate3, accountsFiltered.List().First().ScheduledDate);

			accountsFiltered = (AccountNumberAndAccumulates)accountsStored.ListAccumulates(today.AddDays(-1), today.AddDays(6), 0, 100, "No processor id.");
			Assert.AreEqual(0, accountsFiltered.List().Count());

			accountsFiltered = (AccountNumberAndAccumulates)accountsStored.ListAccumulates(today.AddDays(4), today.AddDays(6), 0, 100, "TEST-Cash-TestDepositUSD-USD-1");
			Assert.AreEqual(1, accountsFiltered.List().Count());
			Assert.AreEqual(scheculeddate3, accountsFiltered.List().First().ScheduledDate);

			accountsFiltered = (AccountNumberAndAccumulates)accountsStored.ListAccumulates(today.AddDays(-1), today.AddDays(6), 0, 100, "", accountInBTC.Number);
			Assert.AreEqual(0, accountsFiltered.List().Count());

			accountsFiltered = (AccountNumberAndAccumulates)accountsStored.ListAccumulates(today.AddDays(-1), today.AddDays(6), 0, 100, "", accountInDollars.Number);
			Assert.AreEqual(3, accountsFiltered.List().Count());

			accountsFiltered = (AccountNumberAndAccumulates)accountsStored.ListAccumulates(today.AddDays(-1), today.AddDays(6), 0, 100, "", "", Currencies.CODES.BTC.ToString());
			Assert.AreEqual(0, accountsFiltered.List().Count());

			accountsFiltered = (AccountNumberAndAccumulates)accountsStored.ListAccumulates(today.AddDays(-1), today.AddDays(6), 0, 100, "", "", Currencies.CODES.USD.ToString());
			Assert.AreEqual(3, accountsFiltered.List().Count());

			accountsFiltered = (AccountNumberAndAccumulates)accountsStored.ListAccumulates(today.AddDays(-1), today.AddDays(6), 0, 100, "", "", Currencies.CODES.USD.ToString(), 1, 1.9m);
			Assert.AreEqual(2, accountsFiltered.List().Count());

			accountsFiltered = (AccountNumberAndAccumulates)accountsStored.ListAccumulates(today.AddDays(-1), today.AddDays(6), 0, 100, "", "", Currencies.CODES.USD.ToString(), 2, 3);
			Assert.AreEqual(1, accountsFiltered.List().Count());

			accountsFiltered = (AccountNumberAndAccumulates)accountsStored.ListAccumulates(today.AddDays(-1), today.AddDays(6), 0, 100, "", "", Currencies.CODES.USD.ToString(), 0, 5, TransactionType.Deposit.ToString());
			Assert.AreEqual(0, accountsFiltered.List().Count());

			accountsFiltered = (AccountNumberAndAccumulates)accountsStored.ListAccumulates(today.AddDays(-1), today.AddDays(6), 0, 100, "", "", Currencies.CODES.USD.ToString(), 0, 5, TransactionType.Withdrawal.ToString());
			Assert.AreEqual(3, accountsFiltered.List().Count());

			Integration.RestoreToDefaultSettings();
			//GamesEngine.Custodian.PaymentMethodsActions.ResetInstance();
		}

		[TestMethod]
		public void DisbursementsAccumulates()
		{
			CurrenciesTest.AddCurrencies();
			Company cia;
			CurrenciesTest.AddCurrencies();
			Guardian guardian;
			QueueInMemory queue;
			DateTime today = DateTime.Now;
			GuardianTest.Init(today, out cia, out guardian, out queue);
			AccountNumbers accounts = guardian.Accounts();
			bool itIsThePresent = true;
			Transactions transactions = Transactions.GetInstance();

			AccountNumber accountInDollars = accounts.Search("3770-4512-5467-6352");
			AccountNumber accountInBTC = accounts.Search("3d1b1b52e955530b935cbe260d1f63c26edf9f8748b4a2550ff23005710c803a");

			PaymentProcessorsAndActionsByDomains processsors = guardian.PaymentProcessors();

			PaymentProcessor processsor  = processsors.SearchById("TEST-Cash-TestDepositUSD-USD-1");

			string domainUrl = "localhost";
			int domainId = 1;

			Dollar withdrawalAmount = new Dollar(4);

			Operations operations = guardian.Operations();
            PendingWithDrawal withdrawal = operations.CreateWithdrawal(itIsThePresent, DateTime.Now, 1, 1, "5D2428442", withdrawalAmount, processsor.Group.Id, processsor, domainId, domainUrl, "Bart");

			DateTime scheculeddate1 = today.AddDays(1);
			DateTime scheculeddate2 = today.AddDays(3);
			DateTime scheculeddate3 = today.AddDays(5);
			DisbursementsChanges changes1 = new DisbursementsChanges(today, guardian);
			changes1.Add(itIsThePresent, 1, accountInDollars.Number, scheculeddate1, 1);
			changes1.Add(itIsThePresent, 2, accountInDollars.Number, scheculeddate2, 1);
			changes1.Add(itIsThePresent, 3, accountInDollars.Number, scheculeddate3, 2);
			withdrawal.Apply(itIsThePresent, today, changes1);

			Profiles profielsAuthorizedByTheOwner = new Profiles();
			profielsAuthorizedByTheOwner = new Profiles();
			profielsAuthorizedByTheOwner.Add(guardian.Profiles().SearchByName("Financial Manager"));
			profielsAuthorizedByTheOwner.Add(guardian.Profiles().SearchByName("General Manager"));
			operations.Approve(itIsThePresent, today, withdrawal.TransactionId, "<EMAIL>", profielsAuthorizedByTheOwner);

			InprocessWithDrawal inprocessWithDrawal = (InprocessWithDrawal)guardian.Operations().SearchFor(1);

			Accumulate accumulate;
			accountInDollars.GetAccumulate(scheculeddate1, inprocessWithDrawal.Coin, out accumulate);
			Assert.AreEqual(1, accumulate.Value);

			accountInDollars.GetAccumulate(scheculeddate2, inprocessWithDrawal.Coin, out accumulate);
			Assert.AreEqual(1, accumulate.Value);

			accountInDollars.GetAccumulate(scheculeddate3, inprocessWithDrawal.Coin, out accumulate);
			Assert.AreEqual(2, accumulate.Value);

			//Change the CurrencyCode of the destiny account

			DisbursementsChanges changes2 = new DisbursementsChanges(today, guardian);
			changes2.Add(itIsThePresent, 1, accountInDollars.Number, scheculeddate1, 1);
			changes2.Add(itIsThePresent, 2, accountInDollars.Number, scheculeddate2, 1);
			changes2.Add(itIsThePresent, 3, accountInBTC.Number, scheculeddate3, 2);
			inprocessWithDrawal.Apply(itIsThePresent, today, changes2);

			accountInDollars.GetAccumulate(scheculeddate1, inprocessWithDrawal.Coin, out accumulate);
			Assert.AreEqual(1, accumulate.Value);

			accountInDollars.GetAccumulate(scheculeddate2, inprocessWithDrawal.Coin, out accumulate);
			Assert.AreEqual(1, accumulate.Value);

			bool result = accountInDollars.GetAccumulate(scheculeddate3, inprocessWithDrawal.Coin, out accumulate);
			Assert.AreEqual(false, result);

			accountInBTC.GetAccumulate(scheculeddate3, inprocessWithDrawal.Coin, out accumulate);
			Assert.AreEqual(2, accumulate.Value);

			accountInBTC.GetAccumulate(scheculeddate3, inprocessWithDrawal.Coin, out accumulate);
			Assert.AreEqual(Currencies.CODES.USD.ToString(), accumulate.CurrencyCode);

			//Add new Disbursement

			DisbursementsChanges changes3 = new DisbursementsChanges(today, guardian);
			changes3.Add(itIsThePresent, 1, accountInDollars.Number, scheculeddate1, 1);
			changes3.Add(itIsThePresent, 2, accountInDollars.Number, scheculeddate2, 1);
			changes3.Add(itIsThePresent, 3, accountInBTC.Number, scheculeddate3, 1.5m);
			changes3.Add(itIsThePresent, 4, accountInBTC.Number, scheculeddate1, 0.5m);
			inprocessWithDrawal.Apply(itIsThePresent, today, changes3);

			accountInDollars.GetAccumulate(scheculeddate1, inprocessWithDrawal.Coin, out accumulate);
			Assert.AreEqual(1, accumulate.Value);

			accountInDollars.GetAccumulate(scheculeddate2, inprocessWithDrawal.Coin, out accumulate);
			Assert.AreEqual(1, accumulate.Value);

			result = accountInDollars.GetAccumulate(scheculeddate3, inprocessWithDrawal.Coin, out accumulate);
			Assert.AreEqual(false, result);

			accountInBTC.GetAccumulate(scheculeddate3, inprocessWithDrawal.Coin, out accumulate);
			Assert.AreEqual(1.5m, accumulate.Value);

			accountInBTC.GetAccumulate(scheculeddate3, inprocessWithDrawal.Coin, out accumulate);
			Assert.AreEqual(Currencies.CODES.USD.ToString(), accumulate.CurrencyCode);

			accountInBTC.GetAccumulate(scheculeddate1, inprocessWithDrawal.Coin, out accumulate);
			Assert.AreEqual(0.5m, accumulate.Value);

			accountInBTC.GetAccumulate(scheculeddate1, inprocessWithDrawal.Coin, out accumulate);
			Assert.AreEqual(Currencies.CODES.USD.ToString(), accumulate.CurrencyCode);

			//Reduce the Disbursement

			DisbursementsChanges changes4 = new DisbursementsChanges(today, guardian);
			changes4.Add(itIsThePresent, 1, accountInBTC.Number, scheculeddate3, 4m);
			changes4.Remove(2);
			changes4.Remove(3);
			changes4.Remove(4);
			inprocessWithDrawal.Apply(itIsThePresent, today, changes4);

			result = accountInDollars.GetAccumulate(scheculeddate1, inprocessWithDrawal.Coin, out accumulate);
			Assert.AreEqual(false, result);

			result = accountInDollars.GetAccumulate(scheculeddate2, inprocessWithDrawal.Coin, out accumulate);
			Assert.AreEqual(false, result);

			result = accountInDollars.GetAccumulate(scheculeddate3, inprocessWithDrawal.Coin, out accumulate);
			Assert.AreEqual(false, result);

			accountInBTC.GetAccumulate(scheculeddate3, inprocessWithDrawal.Coin, out accumulate);
			Assert.AreEqual(4, accumulate.Value);

			accountInBTC.GetAccumulate(scheculeddate3, inprocessWithDrawal.Coin, out accumulate);
			Assert.AreEqual(Currencies.CODES.USD.ToString(), accumulate.CurrencyCode);

			result = accountInBTC.GetAccumulate(scheculeddate1, inprocessWithDrawal.Coin, out accumulate);
			Assert.AreEqual(false, result);

			Integration.RestoreToDefaultSettings();
			//GamesEngine.Custodian.PaymentMethodsActions.ResetInstance();
		}
		[TestMethod]
		public void DisbursementsAccumulates_In_BTC()
		{
			CurrenciesTest.AddCurrencies();
			Company cia;
			Guardian guardian;
			QueueInMemory queue;
			DateTime today = DateTime.Now;
			GuardianTest.Init(today, out cia, out guardian, out queue);
			AccountNumbers accounts = guardian.Accounts();
			bool itIsThePresent = true;
			Transactions transactions = Transactions.GetInstance();

			AccountNumber accountInBTC = accounts.Search("3d1b1b52e955530b935cbe260d1f63c26edf9f8748b4a2550ff23005710c803a");

			PaymentProcessorsAndActionsByDomains processsors = guardian.PaymentProcessors();

			PaymentProcessor processsorBtc = processsors.SearchById("TEST-Secrets-TestDepositBTC-BTC-1");

			string domainUrl = "localhost";
			int domainId = 1;

			Btc withdrawalAmount = new Btc(4);

			Operations operations = guardian.Operations();
            PendingWithDrawal withdrawal = operations.CreateWithdrawal(itIsThePresent, DateTime.Now, 1, 1, "5D2428442", withdrawalAmount, processsorBtc.Group.Id, processsorBtc, domainId, domainUrl, "Bart");

			DateTime scheculeddate1 = today.AddDays(1);
			DateTime scheculeddate2 = today.AddDays(3);
			DateTime scheculeddate3 = today.AddDays(5);
			DisbursementsChanges changes1 = new DisbursementsChanges(today, guardian);
			changes1.Add(itIsThePresent, 1, accountInBTC.Number, scheculeddate1, 1);
			changes1.Add(itIsThePresent, 2, accountInBTC.Number, scheculeddate2, 1);
			changes1.Add(itIsThePresent, 3, accountInBTC.Number, scheculeddate3, 2);
			withdrawal.Apply(itIsThePresent, today, changes1);

			Profiles profielsAuthorizedByTheOwner = new Profiles();
			profielsAuthorizedByTheOwner = new Profiles();
			profielsAuthorizedByTheOwner.Add(guardian.Profiles().SearchByName("Financial Manager"));
			profielsAuthorizedByTheOwner.Add(guardian.Profiles().SearchByName("General Manager"));
			operations.Approve(itIsThePresent, today, withdrawal.TransactionId, "<EMAIL>", profielsAuthorizedByTheOwner);

			InprocessWithDrawal inprocessWithDrawal = (InprocessWithDrawal)guardian.Operations().SearchFor(1);

			Accumulate accumulate;
			accountInBTC.GetAccumulate(scheculeddate1, inprocessWithDrawal.Coin, out accumulate);
			Assert.AreEqual(1, accumulate.Value);

			accountInBTC.GetAccumulate(scheculeddate2, inprocessWithDrawal.Coin, out accumulate);
			Assert.AreEqual(1, accumulate.Value);

			accountInBTC.GetAccumulate(scheculeddate3, inprocessWithDrawal.Coin, out accumulate);
			Assert.AreEqual(2, accumulate.Value);

			//Change the CurrencyCode of the destiny account

			DisbursementsChanges changes2 = new DisbursementsChanges(today, guardian);
			changes2.Add(itIsThePresent, 1, accountInBTC.Number, scheculeddate1, 4);
			changes2.Remove(2);
			changes2.Remove(3);
			inprocessWithDrawal.Apply(itIsThePresent, today, changes2);

			accountInBTC.GetAccumulate(scheculeddate1, inprocessWithDrawal.Coin, out accumulate);
			Assert.AreEqual(4, accumulate.Value);

			bool result = accountInBTC.GetAccumulate(scheculeddate2, inprocessWithDrawal.Coin, out accumulate);
			Assert.AreEqual(false, result);

			result = accountInBTC.GetAccumulate(scheculeddate3, inprocessWithDrawal.Coin, out accumulate);
			Assert.AreEqual(false, result);
			//GamesEngine.Custodian.PaymentMethodsActions.ResetInstance();
			Integration.RestoreToDefaultSettings();
		}


		[TestMethod]
		public void DisbursementsByDate_Validations()
		{
			CurrenciesTest.AddCurrencies();

			DisbursmentsByDate disbursmentsByDate = new DisbursmentsByDate();
			DateTime date1 = new DateTime(2020, 11, 11, 10, 10, 10);
			DateTime date1PlusOneHour = date1.AddHours(1);
			DateTime date1PlusOneDay = date1.AddDays(1);

			Assert.AreEqual(date1.Date, date1PlusOneHour.Date);
			Assert.AreNotEqual(date1.Date, date1PlusOneDay.Date);

			Disbursement disbursement = new DisbursementChange(
				1,
				new AccountNumber(1, "Number", "Number", Coinage.Coin(Currencies.CODES.USD)),
				date1,
				10
				);
			
			try
			{
				disbursmentsByDate.Remove(date1PlusOneDay, disbursement);
				Assert.Fail("Can not delete anon added item");
			}
			catch (GameEngineException e)
			{ }

			try
			{
				disbursmentsByDate.Remove(date1PlusOneDay, disbursement);
				Assert.Fail("Can not delete anon added item");
			}
			catch (GameEngineException e)
			{ }

			disbursmentsByDate.Add(disbursement);
			
			Assert.AreEqual(1, disbursmentsByDate.Count());
			Assert.AreEqual(1, disbursmentsByDate.List(date1PlusOneHour).Count());
			Assert.AreEqual(0, disbursmentsByDate.List(date1PlusOneDay).Count());
			Assert.AreEqual(disbursement, disbursmentsByDate.List(date1PlusOneHour).First());

			try
			{
				disbursmentsByDate.Remove(date1PlusOneDay, disbursement);
				Assert.Fail("Can not delete anon added item");
			}
			catch (GameEngineException e)
			{ }

			disbursmentsByDate.Remove(date1PlusOneHour, disbursement);
			Assert.AreEqual(0, disbursmentsByDate.Count());
			Assert.AreEqual(0, disbursmentsByDate.List(date1PlusOneHour).Count());

		}
		[TestMethod]
		public void Disbursements_Validations_AfterScheduled()
		{
			CurrenciesTest.AddCurrencies();
			Company cia;
			Guardian guardian;
			QueueInMemory queue;
			DateTime today = DateTime.Now;
			GuardianTest.Init(today, out cia, out guardian, out queue);
			AccountNumbers accounts = guardian.Accounts();
			bool itIsThePresent = true;
			Transactions transactions = Transactions.GetInstance();

			AccountNumber account = accounts.Search("3770-4512-5467-6352");

			PaymentProcessorsAndActionsByDomains processsors = guardian.PaymentProcessors();

			PaymentProcessor processsor  = processsors.SearchById("TEST-Cash-TestDepositUSD-USD-1");

			string domainUrl = "localhost";
			int domainId = 1;

			Dollar withdrawalAmount = new Dollar(4);

			Operations operations = guardian.Operations();
            PendingWithDrawal withdrawal = operations.CreateWithdrawal(itIsThePresent, DateTime.Now, 1, 1, "5D2428442", withdrawalAmount, processsor.Group.Id, processsor, domainId, domainUrl, "Bart");

			Accumulate accumulate;

			DateTime scheculeddate1 = today.AddMilliseconds(1);
			DisbursementsChanges changes1 = new DisbursementsChanges(today, guardian);
			changes1.Add(itIsThePresent, 1, account.Number, scheculeddate1, 1);
			changes1.Add(itIsThePresent, 2, account.Number, scheculeddate1, 3);
			withdrawal.Apply(itIsThePresent, today, changes1);

			Profiles profielsAuthorizedByTheOwner = new Profiles();
			profielsAuthorizedByTheOwner = new Profiles();
			profielsAuthorizedByTheOwner.Add(guardian.Profiles().SearchByName("Financial Manager"));
			profielsAuthorizedByTheOwner.Add(guardian.Profiles().SearchByName("General Manager"));
			operations.Approve(itIsThePresent, today, withdrawal.TransactionId, "<EMAIL>", profielsAuthorizedByTheOwner);

			InprocessWithDrawal inprocessWithDrawal = (InprocessWithDrawal)guardian.Operations().SearchFor(1);

			account.GetAccumulate(today, inprocessWithDrawal.Coin, out accumulate);
			Assert.AreEqual(withdrawalAmount.Value, accumulate.Value);
			Assert.AreEqual(true, inprocessWithDrawal.Scheduled);

			try
			{
				DisbursementsChanges changes2 = new DisbursementsChanges(today, guardian);
				changes2.Add(itIsThePresent, 2, account.Number, scheculeddate1, 2);
				inprocessWithDrawal.Apply(itIsThePresent, today, changes2);

				Assert.Fail($"{nameof(Disbursement)} was in process previously. $1 remains to be distributed");
			}
			catch (GameEngineException e)
			{ }
			
			Assert.AreEqual(true, inprocessWithDrawal.Scheduled);

			try
			{
				scheculeddate1 = today.AddMilliseconds(1);
				DisbursementsChanges changes3 = new DisbursementsChanges(today, guardian);
				changes3.Add(itIsThePresent, 1, account.Number, scheculeddate1, 1);
				changes3.Add(itIsThePresent, 2, account.Number, scheculeddate1, 3);
				inprocessWithDrawal.Apply(itIsThePresent, today, changes3);

				Assert.AreEqual(true, inprocessWithDrawal.Scheduled);

				DateTime newDate = today.AddDays(-1);
				scheculeddate1 = new DateTime(newDate.Year, newDate.Month, newDate.Day, 23, 59, 59);// El día anterior ya no se puede
				DisbursementsChanges changes4 = new DisbursementsChanges(today, guardian);
				changes4.Add(itIsThePresent, 2, account.Number, scheculeddate1, 3);
				inprocessWithDrawal.Apply(itIsThePresent, today, changes4);

				Assert.Fail($"Trying to change a {nameof(Disbursement)} that already pass");
			}
			catch (GameEngineException e)
			{ }

			Assert.AreEqual(true, withdrawal.Scheduled);

			Integration.RestoreToDefaultSettings();
			//GamesEngine.Custodian.PaymentMethodsActions.ResetInstance();
		}

		[TestMethod]
		public async Task Disbursements_ApproveAsync()
		{
			CurrenciesTest.AddCurrencies();
			Company cia;
			Guardian guardian;
			QueueInMemory queue;
			DateTime today = DateTime.Now;
			GuardianTest.Init(today, out cia, out guardian, out queue);
			AccountNumbers accounts = guardian.Accounts();
			bool itIsThePresent = true;
			Transactions transactions = Transactions.GetInstance();

			AccountNumber account = accounts.Search("3770-4512-5467-6352");

			PaymentProcessorsAndActionsByDomains processsors = guardian.PaymentProcessors();

			PaymentProcessor processsor  = processsors.SearchById("TEST-Cash-TestDepositUSD-USD-1");

			string domainUrl = "localhost";
			int domainId = 1;

			Dollar withdrawalAmount = new Dollar(4);

			Profile profile = guardian.Profiles().SearchByName("General Manager");
			int approverId = guardian.Approvers().NextApproverId();
			Approver approver = guardian.Approvers().Create(itIsThePresent, approverId, "<EMAIL>");
			profile.Add(approver);

			profile = guardian.Profiles().SearchByName("Financial Manager");
			approver = guardian.Approvers().Get("<EMAIL>");
			profile.Add(approver);

			Operations operations = guardian.Operations();
			int transactionId = operations.NewOperationNumber() ;
            PendingWithDrawal withdrawal = operations.CreateWithdrawal(itIsThePresent, DateTime.Now, transactionId, 1, "5D2428442", withdrawalAmount, processsor.Group.Id, processsor, domainId, domainUrl, "Bart");

			DateTime scheculeddate = today.AddMilliseconds(1);
			DisbursementsChanges changes = new DisbursementsChanges(today, guardian);
			changes.Add(itIsThePresent, 1, account.Number, scheculeddate, 1);
			changes.Add(itIsThePresent, 2, account.Number, scheculeddate, 3);
			withdrawal.Apply(itIsThePresent, today, changes);

			try
			{
				operations.Approve(itIsThePresent, today, transactionId, "<EMAIL>");
				Assert.Fail($"Agent can not approve a this transaction.");
			}
			catch (GameEngineException e) { }

			operations.Approve(itIsThePresent, today, withdrawal.TransactionId, "<EMAIL>");

			GuardianTest.ValidateAccountsCreations(queue);

			string msn = queue.Dequeue(Integration.Kafka.TopicForGuardianInternalOperations);
			ApproverCreationMessage accountCreationMessage = (ApproverCreationMessage)InternalOperationMessage.Factory(msn);
			Assert.AreEqual("<EMAIL>", accountCreationMessage.Email);

			msn = queue.Dequeue(Integration.Kafka.TopicForGuardianInternalOperations);
			InternalOperationUpdateMessage withdrawalFromKafka = (InternalOperationUpdateMessage) InternalOperationMessage.Factory(msn);
			Assert.AreEqual(withdrawal.Description, withdrawalFromKafka.Description);
			Assert.AreEqual(withdrawal.DisbursementAmount.Value, withdrawalFromKafka.DisbursementAmount.Value);
			Assert.AreEqual(withdrawal.DisbursementAmount.CurrencyCode, withdrawalFromKafka.DisbursementAmount.CurrencyCode);
			Assert.AreEqual(withdrawal.Group, withdrawalFromKafka.Group);
			Assert.AreEqual(withdrawal.Processor.Driver.Id, withdrawalFromKafka.ProcessorId);
			Assert.AreEqual(withdrawal.TransactionId, withdrawalFromKafka.TransactionId);
			Assert.AreEqual( false, withdrawalFromKafka.Scheduled);
			Assert.AreEqual( 0, withdrawalFromKafka.Approvals);
			Assert.AreEqual( 2, withdrawalFromKafka.ApprovalsRequired);
			Assert.AreEqual(StatusCodes.PENDING, withdrawalFromKafka.Status);

			transactions.Save(withdrawalFromKafka);

			msn = queue.Dequeue(Integration.Kafka.TopicForGuardianInternalOperations);
			withdrawalFromKafka = (InternalOperationUpdateMessage)InternalOperationMessage.Factory(msn);
			Assert.AreEqual(withdrawal.Description, withdrawalFromKafka.Description);
			Assert.AreEqual(withdrawal.DisbursementAmount.Value, withdrawalFromKafka.DisbursementAmount.Value);
			Assert.AreEqual(withdrawal.DisbursementAmount.CurrencyCode, withdrawalFromKafka.DisbursementAmount.CurrencyCode);
			Assert.AreEqual(withdrawal.Group, withdrawalFromKafka.Group);
			Assert.AreEqual(withdrawal.Processor.Driver.Id, withdrawalFromKafka.ProcessorId);
			Assert.AreEqual(withdrawal.TransactionId, withdrawalFromKafka.TransactionId);
			Assert.AreEqual(true, withdrawalFromKafka.Scheduled);
			Assert.AreEqual(0, withdrawalFromKafka.Approvals);
			Assert.AreEqual(2, withdrawalFromKafka.ApprovalsRequired);
			Assert.AreEqual(StatusCodes.PENDING, withdrawalFromKafka.Status);

			transactions.Save(withdrawalFromKafka);

			msn = queue.Dequeue(Integration.Kafka.TopicForGuardianInternalOperations);
			InternalOperationUpdateMessageWithProfiles withdrawalFromKafkaWithProfiles = (InternalOperationUpdateMessageWithProfiles)InternalOperationMessage.Factory(msn);
			Assert.AreEqual(withdrawal.Description, withdrawalFromKafkaWithProfiles.Description);
			Assert.AreEqual(withdrawal.DisbursementAmount.Value, withdrawalFromKafkaWithProfiles.DisbursementAmount.Value);
			Assert.AreEqual(withdrawal.DisbursementAmount.CurrencyCode, withdrawalFromKafkaWithProfiles.DisbursementAmount.CurrencyCode);
			Assert.AreEqual(withdrawal.Group, withdrawalFromKafkaWithProfiles.Group);
			Assert.AreEqual(withdrawal.Processor.Driver.Id, withdrawalFromKafkaWithProfiles.ProcessorId);
			Assert.AreEqual(withdrawal.TransactionId, withdrawalFromKafkaWithProfiles.TransactionId);
			Assert.AreEqual(true, withdrawalFromKafkaWithProfiles.Scheduled);
			Assert.AreEqual(2, withdrawalFromKafkaWithProfiles.Approvals);
			Assert.AreEqual(2, withdrawalFromKafkaWithProfiles.ApprovalsRequired);
			Assert.AreEqual(StatusCodes.IN_PROCESS, withdrawalFromKafkaWithProfiles.Status);

			transactions.Save(withdrawalFromKafkaWithProfiles);

			IEnumerable<StoredOperation> storedPendingOperation = await transactions.ListPendingOperationsAsync(1, OperationSchedule.Both, today.AddDays(-10), today.AddDays(10), 100, 0);
			Assert.AreEqual(true, storedPendingOperation.Count() > 0);

			Integration.RestoreToDefaultSettings();
			//GamesEngine.Custodian.PaymentMethodsActions.ResetInstance();
		}

		[TestMethod]
		public void Disbursements_Approve_by_owner()
		{
			CurrenciesTest.AddCurrencies();
			Company cia;
			Guardian guardian;
			QueueInMemory queue;
			DateTime today = DateTime.Now;
			GuardianTest.Init(today, out cia, out guardian, out queue);
			AccountNumbers accounts = guardian.Accounts();
			bool itIsThePresent = true;
			Transactions transactions = Transactions.GetInstance();

			AccountNumber account = accounts.Search("3770-4512-5467-6352");

			PaymentProcessorsAndActionsByDomains processsors = guardian.PaymentProcessors();

			PaymentProcessor processsor  = processsors.SearchById("TEST-Cash-TestDepositUSD-USD-1");

			string domainUrl = "localhost";
			int domainId = 1;

			Dollar withdrawalAmount = new Dollar(4);

			Operations operations = guardian.Operations();
            PendingWithDrawal withdrawal = operations.CreateWithdrawal(itIsThePresent, DateTime.Now, 1, 1, "5D2428442", withdrawalAmount, processsor.Group.Id, processsor, domainId, domainUrl, "Bart");

			DateTime scheculeddate = today.AddMilliseconds(1);
			DisbursementsChanges changes = new DisbursementsChanges(today, guardian);
			changes.Add(itIsThePresent, 1, account.Number, scheculeddate, 1);
			changes.Add(itIsThePresent, 2, account.Number, scheculeddate, 3);
			withdrawal.Apply(itIsThePresent, today, changes);

			Profiles profielsAuthorizedByTheOwner = new Profiles();
			profielsAuthorizedByTheOwner.Add(guardian.Profiles().SearchByName("Financial Manager"));
			profielsAuthorizedByTheOwner.Add(guardian.Profiles().SearchByName("General Manager"));
			operations.Approve(itIsThePresent, today, withdrawal.TransactionId, "<EMAIL>", profielsAuthorizedByTheOwner);

			GuardianTest.ValidateAccountsCreations(queue);

			string msn = queue.Dequeue(Integration.Kafka.TopicForGuardianInternalOperations);
			InternalOperationUpdateMessage withdrawalFromKafka = (InternalOperationUpdateMessage)InternalOperationMessage.Factory(msn);
			Assert.AreEqual(withdrawal.Description, withdrawalFromKafka.Description);
			Assert.AreEqual(withdrawal.DisbursementAmount.Value, withdrawalFromKafka.DisbursementAmount.Value);
			Assert.AreEqual(withdrawal.DisbursementAmount.CurrencyCode, withdrawalFromKafka.DisbursementAmount.CurrencyCode);
			Assert.AreEqual(withdrawal.Group, withdrawalFromKafka.Group);
			Assert.AreEqual(withdrawal.Processor.Driver.Id, withdrawalFromKafka.ProcessorId);
			Assert.AreEqual(withdrawal.TransactionId, withdrawalFromKafka.TransactionId);
			Assert.AreEqual(false, withdrawalFromKafka.Scheduled);
			Assert.AreEqual(0, withdrawalFromKafka.Approvals);
			Assert.AreEqual(2, withdrawalFromKafka.ApprovalsRequired);
			Assert.AreEqual(StatusCodes.PENDING, withdrawalFromKafka.Status);

			transactions.Save(withdrawalFromKafka);

			msn = queue.Dequeue(Integration.Kafka.TopicForGuardianInternalOperations);
			withdrawalFromKafka = (InternalOperationUpdateMessage)InternalOperationMessage.Factory(msn);
			Assert.AreEqual(withdrawal.Description, withdrawalFromKafka.Description);
			Assert.AreEqual(withdrawal.DisbursementAmount.Value, withdrawalFromKafka.DisbursementAmount.Value);
			Assert.AreEqual(withdrawal.DisbursementAmount.CurrencyCode, withdrawalFromKafka.DisbursementAmount.CurrencyCode);
			Assert.AreEqual(withdrawal.Group, withdrawalFromKafka.Group);
			Assert.AreEqual(withdrawal.Processor.Driver.Id, withdrawalFromKafka.ProcessorId);
			Assert.AreEqual(withdrawal.TransactionId, withdrawalFromKafka.TransactionId);
			Assert.AreEqual(true, withdrawalFromKafka.Scheduled);
			Assert.AreEqual(0, withdrawalFromKafka.Approvals);
			Assert.AreEqual(2, withdrawalFromKafka.ApprovalsRequired);
			Assert.AreEqual(StatusCodes.PENDING, withdrawalFromKafka.Status);

			transactions.Save(withdrawalFromKafka);

			msn = queue.Dequeue(Integration.Kafka.TopicForGuardianInternalOperations);
			InternalOperationUpdateMessageWithProfiles withdrawalFromKafkaWithProfiles = (InternalOperationUpdateMessageWithProfiles)InternalOperationMessage.Factory(msn);
			Assert.AreEqual(withdrawal.Description, withdrawalFromKafkaWithProfiles.Description);
			Assert.AreEqual(withdrawal.DisbursementAmount.Value, withdrawalFromKafkaWithProfiles.DisbursementAmount.Value);
			Assert.AreEqual(withdrawal.DisbursementAmount.CurrencyCode, withdrawalFromKafkaWithProfiles.DisbursementAmount.CurrencyCode);
			Assert.AreEqual(withdrawal.Group, withdrawalFromKafkaWithProfiles.Group);
			Assert.AreEqual(withdrawal.Processor.Driver.Id, withdrawalFromKafkaWithProfiles.ProcessorId);
			Assert.AreEqual(withdrawal.TransactionId, withdrawalFromKafkaWithProfiles.TransactionId);
			Assert.AreEqual(true, withdrawalFromKafkaWithProfiles.Scheduled);
			Assert.AreEqual(2, withdrawalFromKafkaWithProfiles.Approvals);
			Assert.AreEqual(2, withdrawalFromKafkaWithProfiles.ApprovalsRequired);
			Assert.AreEqual(StatusCodes.IN_PROCESS, withdrawalFromKafkaWithProfiles.Status);

			transactions.Save(withdrawalFromKafkaWithProfiles);

			Integration.RestoreToDefaultSettings();
			//GamesEngine.Custodian.PaymentMethodsActions.ResetInstance();
		}

		[TestMethod]
		public void Disbursements_Approve_OneTime_For_User_InBoth_Profiles()
		{
			CurrenciesTest.AddCurrencies();
			Company cia;
			Guardian guardian;
			QueueInMemory queue;
			DateTime today = DateTime.Now;
			GuardianTest.Init(today, out cia, out guardian, out queue);
			AccountNumbers accounts = guardian.Accounts();
			bool itIsThePresent = true;
			Transactions transactions = Transactions.GetInstance();

			AccountNumber account = accounts.Search("3770-4512-5467-6352");

			PaymentProcessorsAndActionsByDomains processsors = guardian.PaymentProcessors();

			PaymentProcessor processsor  = processsors.SearchById("TEST-Cash-TestDepositUSD-USD-1");

			string domainUrl = "localhost";
			int domainId = 1;

			Dollar withdrawalAmount = new Dollar(4);

			Profile generalManager = guardian.Profiles().SearchByName("General Manager");
			Profile financialManager = guardian.Profiles().SearchByName("Financial Manager");
			int approverId = guardian.Approvers().NextApproverId();
			Approver approver = guardian.Approvers().Create(itIsThePresent, approverId, "<EMAIL>");
			generalManager.Add(approver);
			financialManager.Add(approver);

			Assert.AreEqual(2, approver.ProfilesWhereWasAdded.Count());

			Operations operations = guardian.Operations();
            PendingWithDrawal withdrawal = operations.CreateWithdrawal(itIsThePresent, DateTime.Now, 1, 1, "5D2428442", withdrawalAmount, processsor.Group.Id, processsor, domainId, domainUrl, "Bart");

			DateTime scheculeddate = today.AddMilliseconds(1);
			DisbursementsChanges changes = new DisbursementsChanges(today, guardian);
			changes.Add(itIsThePresent, 1, account.Number, scheculeddate, 1);
			changes.Add(itIsThePresent, 2, account.Number, scheculeddate, 3);
			withdrawal.Apply(itIsThePresent, today, changes);

			operations.Approve(itIsThePresent, today, withdrawal.TransactionId, "<EMAIL>");
			InprocessWithDrawal inprocessWithDrawal =  (InprocessWithDrawal)guardian.Operations().FilterBy(StatusCodes.IN_PROCESS).First();

			Assert.AreEqual(true, inprocessWithDrawal.WasCreatedFrom(withdrawal));

			Integration.RestoreToDefaultSettings();
			//GamesEngine.Custodian.PaymentMethodsActions.ResetInstance();
		}

		[TestMethod]
		public void Disbursements_Change_A_Payed_Disbursement()
		{
			CurrenciesTest.AddCurrencies();
			Company cia;
			Guardian guardian;
			QueueInMemory queue;
			DateTime today = DateTime.Now;
			GuardianTest.Init(today, out cia, out guardian, out queue);

			AccountNumbers accounts = guardian.Accounts();
			bool itIsThePresent = true;
			Transactions transactions = Transactions.GetInstance();

			AccountNumber account = accounts.Search("3770-4512-5467-6352");

			PaymentProcessorsAndActionsByDomains processsors = guardian.PaymentProcessors();

			PaymentProcessor processsor  = processsors.SearchById("TEST-Cash-TestWithdrawalUSD-USD-1");

			string domainUrl = "localhost";
			int domainId = 1;

			Dollar withdrawalAmount = new Dollar(4);
			
			Operations operations = guardian.Operations();
			int transactionId = operations.NewOperationNumber();
            PendingWithDrawal withdrawal = operations.CreateWithdrawal(itIsThePresent, today, transactionId, 1, "5D2428442", withdrawalAmount, processsor.Group.Id, processsor, domainId, domainUrl, "Bart");

			DateTime scheculeddate = today.AddMilliseconds(1);
			DisbursementsChanges changes = new DisbursementsChanges(today, guardian);
			changes.Add(itIsThePresent, 1, account.Number, scheculeddate, 1);
			changes.Add(itIsThePresent, 2, account.Number, scheculeddate, 3);
			withdrawal.Apply(itIsThePresent, today, changes);

			Profiles profielsAuthorizedByTheOwner = new Profiles();
			profielsAuthorizedByTheOwner = new Profiles();
			profielsAuthorizedByTheOwner.Add(guardian.Profiles().SearchByName("Financial Manager"));
			profielsAuthorizedByTheOwner.Add(guardian.Profiles().SearchByName("General Manager"));
			operations.Approve(itIsThePresent, today, withdrawal.TransactionId, "<EMAIL>", profielsAuthorizedByTheOwner);
			PaymentsToBeExecuted paymentsToBeExecuted = new PaymentsToBeExecuted(guardian);
			paymentsToBeExecuted.Add(withdrawal.TransactionId, new List<int>() { 1 });
			int storeId = 4;
			paymentsToBeExecuted.Execute(itIsThePresent, today, "cris", storeId, "CR/Cartago");

			InprocessWithDrawal inprocessWithDrawal = (InprocessWithDrawal)operations.SearchFor(1);
			Assert.AreEqual(true, inprocessWithDrawal.Disbursements.First() is DisbursementExecuted);

			try
			{
				DateTime scheculeddate2 = today.AddMilliseconds(1);
				changes = new DisbursementsChanges(today, guardian);
				changes.Add(itIsThePresent, 1, account.Number, scheculeddate2, 1);
				inprocessWithDrawal.Apply(itIsThePresent, today, changes);
				Assert.Fail("It's not valid to change a payed disbursment.");
			}
			catch (GameEngineException e) { }

			try
			{
				changes = new DisbursementsChanges(today, guardian);
				changes.Add(itIsThePresent, 1, "A", scheculeddate, 1);
				inprocessWithDrawal.Apply(itIsThePresent, today, changes);
				Assert.Fail("It's not valid to change a payed disbursment.");
			}
			catch (GameEngineException e) { }

			try
			{
				changes = new DisbursementsChanges(today, guardian);
				changes.Add(itIsThePresent, 1, account.Number, scheculeddate, 1.1m);
				inprocessWithDrawal.Apply(itIsThePresent, today, changes);
				Assert.Fail("It's not valid to change a payed disbursment.");
			}
			catch (GameEngineException e) { }

			try
			{
				changes = new DisbursementsChanges(today, guardian);
				changes.Remove(1);
				inprocessWithDrawal.Apply(itIsThePresent, today, changes);
				Assert.Fail("It's not valid to change a payed disbursment.");
			}
			catch (GameEngineException e) { }

			scheculeddate = today.AddMilliseconds(1);
			changes = new DisbursementsChanges(today, guardian);
			changes.Add(itIsThePresent, 2, account.Number, scheculeddate, 1.5m);
			changes.Add(itIsThePresent, 3, account.Number, scheculeddate, 1.5m);
			withdrawal.Apply(itIsThePresent, today, changes);

			Assert.AreEqual(3, withdrawal.Disbursements.Count());


			inprocessWithDrawal = (InprocessWithDrawal)guardian.Operations().FilterBy(StatusCodes.IN_PROCESS).SearchFor(withdrawal.TransactionId);
			Assert.AreEqual(true, inprocessWithDrawal.SearchDisbusmentById(1).ItsApproved);
			Assert.AreEqual(false, withdrawal.SearchDisbusmentById(1).ItsApproved);
			Assert.AreEqual(true, inprocessWithDrawal.SearchDisbusmentById(1) is DisbursementExecuted);

			Integration.RestoreToDefaultSettings();
			//GamesEngine.Custodian.PaymentMethodsActions.ResetInstance();
		}

		[TestMethod]
		public void Pay_All_Disbursement()
		{
			CurrenciesTest.AddCurrencies();

			Company cia;
			Guardian guardian;
			QueueInMemory queue;
			DateTime today = DateTime.Now;
			GuardianTest.Init(today, out cia, out guardian, out queue);
			AccountNumbers accounts = guardian.Accounts();
			bool itIsThePresent = true;
			Transactions transactions = Transactions.GetInstance();

			AccountNumber account = accounts.Search("3770-4512-5467-6352");

			PaymentProcessorsAndActionsByDomains processsors = guardian.PaymentProcessors();

			PaymentProcessor processsor  = processsors.SearchById("TEST-Cash-TestWithdrawalUSD-USD-1");

			string domainUrl = "localhost";
			int domainId = 1;

			Dollar withdrawalAmount = new Dollar(4);

			Operations operations = guardian.Operations();
			int transactionId = operations.NewOperationNumber();
            PendingWithDrawal withdrawal = operations.CreateWithdrawal(itIsThePresent, today, transactionId, 1, "5D2428442", withdrawalAmount, processsor.Group.Id, processsor, domainId, domainUrl, "Bart");

			DateTime scheculeddate = today.AddMilliseconds(1);
			DisbursementsChanges changes = new DisbursementsChanges(today, guardian);
			changes.Add(itIsThePresent, 1, account.Number, scheculeddate, 4);
			withdrawal.Apply(itIsThePresent, today, changes);

			Profiles profielsAuthorizedByTheOwner = new Profiles();
			profielsAuthorizedByTheOwner = new Profiles();
			profielsAuthorizedByTheOwner.Add(guardian.Profiles().SearchByName("Financial Manager"));
			profielsAuthorizedByTheOwner.Add(guardian.Profiles().SearchByName("General Manager"));
			operations.Approve(itIsThePresent, today, withdrawal.TransactionId, "<EMAIL>", profielsAuthorizedByTheOwner);

			InprocessWithDrawal inprocessWithDrawal = (InprocessWithDrawal)guardian.Operations().FilterBy(StatusCodes.IN_PROCESS).SearchFor(withdrawal.TransactionId);

			AccountNumberAndAccumulates accountAndAccumulates = guardian.Accounts().ListAccumulates(DateTime.MinValue, DateTime.MaxValue, 0, int.MaxValue, "");
			IEnumerable<AccountNumberAndAccumulate> accountAndAccumulateList = accountAndAccumulates.List();
			Assert.AreEqual(1, accountAndAccumulateList.Count());
			int disbursmentByDate = guardian.DisbursmentsByDate().Count();
			Assert.AreEqual(1, disbursmentByDate);

			PaymentsToBeExecuted paymentsToBeExecuted = new PaymentsToBeExecuted(guardian);
			paymentsToBeExecuted.Add(inprocessWithDrawal.TransactionId, new List<int>() { 1 });
			int storeId = 4;
			paymentsToBeExecuted.Execute(itIsThePresent, today, "cris", storeId, "CR/Cartago");

			Assert.AreEqual(true, inprocessWithDrawal.Disbursements.First() is DisbursementExecuted);
			Assert.AreEqual(1, inprocessWithDrawal.Disbursements.Count());

			try
			{
				guardian.Operations().FilterBy(StatusCodes.IN_PROCESS).SearchFor(inprocessWithDrawal.TransactionId);
				Assert.Fail("This transaction must not be in memory when all disbursments were payed.");
			}
			catch (GameEngineException e) { }

			try
			{
				guardian.Operations().SearchWithdrawalByTransactionId(1);
				Assert.Fail("This transaction must not be in memory when all disbursments were payed.");
			}
			catch (GameEngineException e) { }

			accountAndAccumulates = guardian.Accounts().ListAccumulates(DateTime.MinValue, DateTime.MaxValue, 0, int.MaxValue, "");
			accountAndAccumulateList = accountAndAccumulates.List();
			Assert.AreEqual(0, accountAndAccumulateList.Count());
			disbursmentByDate = guardian.DisbursmentsByDate().Count();
			Assert.AreEqual(0, disbursmentByDate);

			Integration.RestoreToDefaultSettings();
			//GamesEngine.Custodian.PaymentMethodsActions.ResetInstance();
		}

		[TestMethod]
		public void Pay_All_Changed_Disbursement()
		{

			CurrenciesTest.AddCurrencies();
			Company cia;
			Guardian guardian;
			QueueInMemory queue;
			DateTime today = DateTime.Now;
			GuardianTest.Init(today, out cia, out guardian, out queue);
			AccountNumbers accounts = guardian.Accounts();
			bool itIsThePresent = true;
			Transactions transactions = Transactions.GetInstance();

			AccountNumber account = accounts.Search("3770-4512-5467-6352");
			AccountNumber account2 = accounts.Search("3d1b1b52e955530b935cbe260d1f63c26edf9f8748b4a2550ff23005710c803a");
			
			PaymentProcessorsAndActionsByDomains processsors = guardian.PaymentProcessors();

			PaymentProcessor processsor  = processsors.SearchById("TEST-Cash-TestWithdrawalUSD-USD-1");

			string domainUrl = "localhost";
			int domainId = 1;

			Dollar withdrawalAmount = new Dollar(4);

			Operations operations = guardian.Operations();
			int transactionId = operations.NewOperationNumber();
            PendingWithDrawal withdrawal = operations.CreateWithdrawal(itIsThePresent, today, transactionId, 1, "5D2428442", withdrawalAmount, processsor.Group.Id, processsor, domainId, domainUrl, "Bart");

			DateTime scheculeddate = today.AddMilliseconds(1);
			DateTime scheculeddateOneHourmore = scheculeddate.AddHours(1);
			DateTime scheculeddateOneDayMore = scheculeddate.AddDays(1);
			DisbursementsChanges changes = new DisbursementsChanges(today, guardian);
			changes.Add(itIsThePresent, 1, account.Number, scheculeddate, 4);
			withdrawal.Apply(itIsThePresent, today, changes);

			Profiles profielsAuthorizedByTheOwner = new Profiles();
			profielsAuthorizedByTheOwner = new Profiles();
			profielsAuthorizedByTheOwner.Add(guardian.Profiles().SearchByName("Financial Manager"));
			profielsAuthorizedByTheOwner.Add(guardian.Profiles().SearchByName("General Manager"));
			operations.Approve(itIsThePresent, today, withdrawal.TransactionId, "<EMAIL>", profielsAuthorizedByTheOwner);

			InprocessWithDrawal inprocessWithDrawal = (InprocessWithDrawal)guardian.Operations().FilterBy(StatusCodes.IN_PROCESS).SearchFor(withdrawal.TransactionId);

			changes = new DisbursementsChanges(today, guardian);
			changes.Add(itIsThePresent, 1, account2.Number, scheculeddate, 2);
			changes.Add(itIsThePresent, 2, account.Number, scheculeddateOneHourmore, 2);
			inprocessWithDrawal.Apply(itIsThePresent, today, changes);

			AccountNumberAndAccumulates accountAndAccumulates = guardian.Accounts().ListAccumulates(DateTime.MinValue, DateTime.MaxValue, 0, int.MaxValue, "");
			IEnumerable<AccountNumberAndAccumulate> accountAndAccumulateList = accountAndAccumulates.List();
			Assert.AreEqual(2, accountAndAccumulateList.Count());
			int disbursmentByDate = guardian.DisbursmentsByDate().Count();
			Assert.AreEqual(1, disbursmentByDate);

			changes = new DisbursementsChanges(today, guardian);
			changes.Add(itIsThePresent, 1, account2.Number, scheculeddate, 2);
			changes.Add(itIsThePresent, 2, account.Number, scheculeddateOneDayMore, 2);
			inprocessWithDrawal.Apply(itIsThePresent, today, changes);

			accountAndAccumulates = guardian.Accounts().ListAccumulates(DateTime.MinValue, DateTime.MaxValue, 0, int.MaxValue, "");
			accountAndAccumulateList = accountAndAccumulates.List();
			Assert.AreEqual(2, accountAndAccumulateList.Count());
			disbursmentByDate = guardian.DisbursmentsByDate().Count();
			Assert.AreEqual(2, disbursmentByDate);

			PaymentsToBeExecuted paymentsToBeExecuted = new PaymentsToBeExecuted(guardian);
			paymentsToBeExecuted.Add(inprocessWithDrawal.TransactionId, new List<int>() { 2 });
			int storeId = 4;
			paymentsToBeExecuted.Execute(itIsThePresent, today, "cris", storeId, "CR/Cartago");

			DisbursemenWithTransaction disbursement2 = (DisbursemenWithTransaction)inprocessWithDrawal.Disbursements.SearchById(2);
			Assert.AreEqual(true, disbursement2.HasExecutions());
			Assert.AreEqual(TransactionStatus.APPROVED, disbursement2.LastExecution.Status);
			Assert.AreEqual(today, disbursement2.LastExecution.Date);

			DisbursemenWithTransaction disbursement1 = (DisbursemenWithTransaction)inprocessWithDrawal.Disbursements.SearchById(1);
			Assert.AreEqual(false, disbursement1.HasExecutions());
			Assert.AreEqual(true, disbursement1.LastExecution == null);

			accountAndAccumulates = guardian.Accounts().ListAccumulates(DateTime.MinValue, DateTime.MaxValue, 0, int.MaxValue, "");
			accountAndAccumulateList = accountAndAccumulates.List();
			Assert.AreEqual(1, accountAndAccumulateList.Count());
			disbursmentByDate = guardian.DisbursmentsByDate().Count();
			Assert.AreEqual(1, disbursmentByDate);

			paymentsToBeExecuted = new PaymentsToBeExecuted(guardian);
			paymentsToBeExecuted.Add(inprocessWithDrawal.TransactionId, new List<int>() { 1 });
			paymentsToBeExecuted.Execute(itIsThePresent, today, "cris", storeId, "CR/Cartago");

			try
			{
				guardian.Operations().FilterBy(StatusCodes.IN_PROCESS).SearchFor(inprocessWithDrawal.TransactionId);
				Assert.Fail("This transaction must not be in memory when all disbursments were payed.");
			}
			catch (GameEngineException e) { }

			try
			{
				guardian.Operations().SearchWithdrawalByTransactionId(1);
				Assert.Fail("This transaction must not be in memory when all disbursments were payed.");
			}
			catch (GameEngineException e) { }

			accountAndAccumulates = guardian.Accounts().ListAccumulates(DateTime.MinValue, DateTime.MaxValue, 0, int.MaxValue, "");
			accountAndAccumulateList = accountAndAccumulates.List();
			Assert.AreEqual(0, accountAndAccumulateList.Count());
			disbursmentByDate = guardian.DisbursmentsByDate().Count();
			Assert.AreEqual(0, disbursmentByDate);

			Integration.RestoreToDefaultSettings();
			//GamesEngine.Custodian.PaymentMethodsActions.ResetInstance();
		}


		[TestMethod]
		public void Pay_Disbursment_Serializations()
		{
			Company cia;
			CurrenciesTest.AddCurrencies();
			Guardian guardian;
			QueueInMemory queue;
			DateTime today = DateTime.Now;
			GuardianTest.Init(today, out cia, out guardian, out queue);
			AccountNumbers accounts = guardian.Accounts();
			bool itIsThePresent = true;
			Transactions transactions = Transactions.GetInstance();

			AccountNumber account = accounts.Search("3770-4512-5467-6352");

			PaymentProcessorsAndActionsByDomains processsors = guardian.PaymentProcessors();

			PaymentProcessor processsor  = processsors.SearchById("TEST-Cash-TestWithdrawalUSD-USD-1");

			string domainUrl = "localhost";
			int domainId = 1;

			Dollar withdrawalAmount = new Dollar(4);

			Operations operations = guardian.Operations();
			int transactionId = operations.NewOperationNumber();
            PendingWithDrawal withdrawal1 = operations.CreateWithdrawal(itIsThePresent, today, transactionId, 1, "5D2428442", withdrawalAmount, processsor.Group.Id, processsor, domainId, domainUrl, "Bart");

			DateTime scheculeddate = today.AddMilliseconds(1);
			DisbursementsChanges changes = new DisbursementsChanges(today, guardian);
			changes.Add(itIsThePresent, 1, account.Number, scheculeddate, 3);
			changes.Add(itIsThePresent, 2, account.Number, scheculeddate, 1);
			withdrawal1.Apply(itIsThePresent, today, changes);

			transactionId = operations.NewOperationNumber();
            PendingWithDrawal withdrawal2 = operations.CreateWithdrawal(itIsThePresent, today, transactionId, 1, "5D2428442", withdrawalAmount, processsor.Group.Id, processsor, domainId, domainUrl, "Bart");

			scheculeddate = today.AddMilliseconds(1);
			changes = new DisbursementsChanges(today, guardian);
			changes.Add(itIsThePresent, 1, account.Number, scheculeddate, 2);
			changes.Add(itIsThePresent, 2, account.Number, scheculeddate, 2);
			withdrawal2.Apply(itIsThePresent, today, changes);


			Profiles profielsAuthorizedByTheOwner = new Profiles();
			profielsAuthorizedByTheOwner = new Profiles();
			profielsAuthorizedByTheOwner.Add(guardian.Profiles().SearchByName("Financial Manager"));
			profielsAuthorizedByTheOwner.Add(guardian.Profiles().SearchByName("General Manager"));
			operations.Approve(itIsThePresent, today, withdrawal1.TransactionId, "<EMAIL>", profielsAuthorizedByTheOwner);
			operations.Approve(itIsThePresent, today, withdrawal2.TransactionId, "<EMAIL>", profielsAuthorizedByTheOwner);

			InprocessWithDrawal inprocessWithDrawal1 = (InprocessWithDrawal)guardian.Operations().FilterBy(StatusCodes.IN_PROCESS).SearchFor(withdrawal1.TransactionId);
			InprocessWithDrawal inprocessWithDrawal2 = (InprocessWithDrawal)guardian.Operations().FilterBy(StatusCodes.IN_PROCESS).SearchFor(withdrawal2.TransactionId);

			AccountNumberAndAccumulates accountAndAccumulates = guardian.Accounts().ListAccumulates(DateTime.MinValue, DateTime.MaxValue, 0, int.MaxValue, "");
			IEnumerable<AccountNumberAndAccumulate> accountAndAccumulateList = accountAndAccumulates.List();
			Assert.AreEqual(1, accountAndAccumulateList.Count());
			int disbursmentByDate = guardian.DisbursmentsByDate().Count();
			Assert.AreEqual(1, disbursmentByDate);

			PaymentsToBeExecuted paymentsToBeExecuted = new PaymentsToBeExecuted(guardian);
			paymentsToBeExecuted.Add(inprocessWithDrawal1.TransactionId, new List<int>() { 1 });
			paymentsToBeExecuted.Add(inprocessWithDrawal2.TransactionId, new List<int>() { 2 });
			int storeId = 4;
			paymentsToBeExecuted.Execute(itIsThePresent, today, "cris", storeId, "CR/Cartago");

			GuardianTest.ValidateAccountsCreations(queue); 

			string msg = queue.Dequeue(Integration.Kafka.TopicForWithdrawals);

			string[] messages = KafkaMessages.Split(msg);
			
			WithdrawMessage message1 = new WithdrawMessage(messages[0]);
			var description = Validator.StringEscape(message1.Description);
			Currency amount = Currency.Factory(message1.Currency, message1.Amount);
			Assert.AreEqual(3, message1.Amount);
			Assert.AreEqual(amount.CurrencyCode, withdrawalAmount.CurrencyCode);
			Assert.AreEqual(account.Number, message1.AccountNumber);
			Assert.AreEqual("1", message1.Reference);

			WithdrawMessage message2 = new WithdrawMessage(messages[1]);
			description = Validator.StringEscape(message2.Description);
			amount = Currency.Factory(message2.Currency, message2.Amount);
			Assert.AreEqual(2, message2.Amount);
			Assert.AreEqual(amount.CurrencyCode, withdrawalAmount.CurrencyCode);
			Assert.AreEqual(account.Number, message2.AccountNumber);
			Assert.AreEqual("1", message2.Reference);

			msg = queue.Dequeue(Integration.Kafka.TopicForWithdrawalsDisbursementExecution);

			messages = KafkaMessages.Split(msg);

			DisbursementExecutionMessage message3 = new DisbursementExecutionMessage(messages[0]);
			Assert.AreEqual(3, message3.Amount);
			Assert.AreEqual(amount.CurrencyCode, message3.CurrencyCode.Iso4217Code);
			Assert.AreEqual(account.Id, message3.AccountNumberId);
			Assert.AreEqual(1, message3.TransactionId);
			Assert.AreEqual(1, message3.Id);
			Assert.AreEqual(account.Id, message3.AccountNumberId);
			Assert.AreEqual(account.CurrencyCode, message3.AccountCurrencyCode.Iso4217Code);

			DisbursementExecutionMessage message4 = new DisbursementExecutionMessage(messages[1]);
			Assert.AreEqual(2, message4.Amount);
			Assert.AreEqual(amount.CurrencyCode, message4.CurrencyCode.Iso4217Code);
			Assert.AreEqual(account.Id, message4.AccountNumberId);
			Assert.AreEqual(2, message4.TransactionId);
			Assert.AreEqual(2, message4.Id);
			Assert.AreEqual(account.Id, message4.AccountNumberId);
			Assert.AreEqual(account.CurrencyCode, message4.AccountCurrencyCode.Iso4217Code);

			Integration.RestoreToDefaultSettings();
			//GamesEngine.Custodian.PaymentMethodsActions.ResetInstance();
		}

	}
}
