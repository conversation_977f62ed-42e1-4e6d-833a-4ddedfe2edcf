﻿using Calendarizador.GamesEngine.Time.Schedulers;
using GamesEngine.Bets;
using GamesEngine.Domains;
using GamesEngine.Exchange;
using GamesEngine.Gameboards.Lotto;
using GamesEngine.Location;
using GamesEngine.Messaging;
using GamesEngine.Time;
using Nest;
using Puppeteer.EventSourcing.Libraries;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using static GamesEngine.Games.Lotto.PicksLotteryGame;
using static GamesEngine.Games.Lotto.RiskPerLottery;
using static GamesEngine.Games.Lotto.TicketsOfDraw;
using TicketType = GamesEngine.Gameboards.Lotto.TicketType;

namespace GamesEngine.Games.Lotto
{
    internal class LotteryTriz : Lottery
    {
        private readonly LotteryForPicks pick2Begining;

        private readonly LotteryForPicks pick2Ending;
        private readonly LotteryForPicks pick3Ending;
        private readonly LotteryForPicks pick4Ending;
        private readonly LotteryForPicks pick5Ending;

        private readonly PicksLotteryGame beginingPicksLotteryGameForMX;
        private readonly PicksLotteryGame endingPicksLotteryGameForMX;

        internal LotteryTriz(LotteryGamesPool lotteryGamesPool, TrizLotteryGame trizLotteryGame) : base(trizLotteryGame, trizLotteryGame.GetOrCreateState("MX", "Mexico", DateTime.Now, "N/A"))
        {
            if (lotteryGamesPool == null) throw new ArgumentNullException(nameof(lotteryGamesPool));

            this.beginingPicksLotteryGameForMX = new PicksLotteryGame(lotteryGamesPool.PicksLotteryGame);
            this.endingPicksLotteryGameForMX = new PicksLotteryGame(lotteryGamesPool.PicksLotteryGame);
             
            this.beginingPicksLotteryGameForMX.GetOrCreateState(this.State, DateTime.Now, "N/A");
            this.endingPicksLotteryGameForMX.GetOrCreateState(this.State, DateTime.Now, "N/A");

            this.pick2Begining = this.beginingPicksLotteryGameForMX.GetOrCreateLottery(2, this.State);
            
            this.pick2Ending = this.endingPicksLotteryGameForMX.GetOrCreateLottery(2, this.State);
            this.pick3Ending = this.endingPicksLotteryGameForMX.GetOrCreateLottery(3, this.State);
            this.pick4Ending = this.endingPicksLotteryGameForMX.GetOrCreateLottery(4, this.State);
            this.pick5Ending = this.endingPicksLotteryGameForMX.GetOrCreateLottery(5, this.State);
        }

        internal override int PickNumber => (int)IdOfLottery.TZ;//TRIZ revisar metodo o borrar

        protected override void Cancel(Schedule aSchedule, int dayOfWeek, DateTime now, string employeeName, Tickets tickets)
        {
            throw new NotImplementedException();//TRIZ revisar metodo o borrar
        }

        protected override void TryToCancelLottery()
        {
            throw new NotImplementedException();//TRIZ revisar metodo o borrar
        }

        internal override void Cancel(Schedule aSchedule, DateTime now, string employeeName)
        {
            throw new NotImplementedException();//TRIZ revisar metodo o borrar
        }

        internal override void Every(int hour, int minute, int dayOfWeek, DateTime now, string employeeName)
        {
            base.Every(hour, minute, dayOfWeek, now, employeeName);

            pick2Begining.Every(hour, minute, dayOfWeek, now, employeeName);

            pick2Ending.Every(hour, minute, dayOfWeek, now, employeeName);
            pick3Ending.Every(hour, minute, dayOfWeek, now, employeeName);
            pick4Ending.Every(hour, minute, dayOfWeek, now, employeeName);
            pick5Ending.Every(hour, minute, dayOfWeek, now, employeeName);
        }

        internal override void UpdateSchedule(bool itIsThePresent, string description, string newDaysOfWeek, int hour, int minute, DateTime now, string employeeName)
        {
            base.UpdateSchedule(itIsThePresent, description, newDaysOfWeek, hour, minute, now, employeeName);

            pick2Begining.UpdateSchedule(itIsThePresent, description, newDaysOfWeek, hour, minute, now, employeeName);

            pick2Ending.UpdateSchedule(itIsThePresent, description, newDaysOfWeek, hour, minute, now, employeeName);
            pick3Ending.UpdateSchedule(itIsThePresent, description, newDaysOfWeek, hour, minute, now, employeeName);
            pick4Ending.UpdateSchedule(itIsThePresent, description, newDaysOfWeek, hour, minute, now, employeeName);
            pick5Ending.UpdateSchedule(itIsThePresent, description, newDaysOfWeek, hour, minute, now, employeeName);
        }

        internal override void UpdateDescription(bool itIsThePresent, string description, int hour, int minute)
        {
            base.UpdateDescription(itIsThePresent, description, hour, minute);

            pick2Begining.UpdateDescription(itIsThePresent, description, hour, minute);

            pick2Ending.UpdateDescription(itIsThePresent, description, hour, minute);
            pick3Ending.UpdateDescription(itIsThePresent, description, hour, minute);
            pick4Ending.UpdateDescription(itIsThePresent, description, hour, minute);
            pick5Ending.UpdateDescription(itIsThePresent, description, hour, minute);
        }

        internal static string GAME_TYPE = "LotteryTriz";

        internal override string GameType()
        {
            return GAME_TYPE;
        }

        internal LotteryForPicks GetLotteryPick(int pick, bool ending = true)
        {
            if (pick < 2 || pick > 5) throw new GameEngineException($"Pick {pick} is not a valid pick for Triz Lottery");

            switch (pick)
            {
                case 2:
                    if (ending) return pick2Ending;
                    return pick2Begining;
                case 3:
                    return pick3Ending;
                case 4:
                    return pick4Ending;
                case 5:
                    return pick5Ending;
                default:
                    throw new GameEngineException($"Pick {pick} is not a valid pick for Triz Lottery");
            }
        }

        internal override IEnumerable<Ticket> FindTicketsMatchingWith(IEnumerable<int> ticketNumbers)
        {
            var result = endingPicksLotteryGameForMX.FindTicketsMatchingWith(ticketNumbers).Concat(beginingPicksLotteryGameForMX.FindTicketsMatchingWith(ticketNumbers));
            return result;
        }

        internal TicketCreatorDelegate GetTicketCreator(TicketType ticketType, bool ending)
        {
            if (ending)
            {
                return this.endingPicksLotteryGameForMX.GetTicketCreator(ticketType);
            }
            else
            {
                return this.beginingPicksLotteryGameForMX.GetTicketCreator(ticketType);
            }
        }

        private LotteryDraw GradeForTriz(DateTime date, string sequenceOfNumbers, DateTime now, string employeeName)
        {
            if (string.IsNullOrWhiteSpace(sequenceOfNumbers)) throw new ArgumentNullException(nameof(sequenceOfNumbers));
            if (sequenceOfNumbers.Length != 5) throw new GameEngineException($"Invalid {sequenceOfNumbers}.Triz winner must have 5 digits number.");
            if (!sequenceOfNumbers.All(char.IsDigit)) throw new GameEngineException($"Invalid {sequenceOfNumbers}.Triz winner must have 5 digits number.");
            if (now == DateTime.MinValue) throw new ArgumentNullException(nameof(now));
            if (string.IsNullOrWhiteSpace(employeeName)) throw new ArgumentNullException(nameof(employeeName));

            LotteryDraw currentLotteryDraw = lotteryDraws.Draw(date, sequenceOfNumbers, Lotto.LotteryDraw.WITHOUT_FIREBALL, now, employeeName);
            return currentLotteryDraw;
        }

        internal DrawingsSummaryReport DrawTriz(DateTime date, string sequenceOfNumbers, DateTime now, bool itIsThePresent, string employeeName, bool notifyEvent)
        {
            if (string.IsNullOrWhiteSpace(sequenceOfNumbers)) throw new ArgumentNullException(nameof(sequenceOfNumbers));
            if (sequenceOfNumbers.Length != 5) throw new GameEngineException($"Invalid {sequenceOfNumbers}.Triz winner must have 5 digits number.");
            if (!sequenceOfNumbers.All(char.IsDigit)) throw new GameEngineException($"Invalid {sequenceOfNumbers}.Triz winner must have 5 digits number.");
            if (string.IsNullOrWhiteSpace(employeeName)) throw new ArgumentNullException(nameof(employeeName));

            Func<LotteryDraw> gradeAction = () => this.GradeForTriz(date, sequenceOfNumbers, now, employeeName);
            DrawingsSummaryReport drawingsSummaryReport = this.ReleaseDraw(gradeAction, date, sequenceOfNumbers, now, itIsThePresent, employeeName, notifyEvent);

            var lotteryPick2Begining = (LotteryPick<Pick2>)this.beginingPicksLotteryGameForMX.GetLottery(2, State);
            DrawingsSummaryReport reportPick2Begining = lotteryPick2Begining.DrawPicks(date, sequenceOfNumbers[..2], now, itIsThePresent, employeeName, false);

            var lotteryPick2 = (LotteryPick<Pick2>)this.endingPicksLotteryGameForMX.GetLottery(2, State);
            DrawingsSummaryReport reportPick2 = lotteryPick2.DrawPicks(date, sequenceOfNumbers[3..], now, itIsThePresent, employeeName, false);

            var lotteryPick3 = (LotteryPick<Pick3>)this.endingPicksLotteryGameForMX.GetLottery(3, State);
            DrawingsSummaryReport reportPick3 = lotteryPick3.DrawPicks(date, sequenceOfNumbers[2..], now, itIsThePresent, employeeName, false);

            var lotteryPick4 = (LotteryPick<Pick4>)this.endingPicksLotteryGameForMX.GetLottery(4, State);
            DrawingsSummaryReport reportPick4 = lotteryPick4.DrawPicks(date, sequenceOfNumbers[1..], now, itIsThePresent, employeeName, false);

            var lotteryPick5 = (LotteryPick<Pick5>)this.endingPicksLotteryGameForMX.GetLottery(5, State);
            DrawingsSummaryReport reportPick5 = lotteryPick5.DrawPicks(date, sequenceOfNumbers, now, itIsThePresent, employeeName, false);

            drawingsSummaryReport.JoinReport(reportPick2Begining);
            drawingsSummaryReport.JoinReport(reportPick2);
            drawingsSummaryReport.JoinReport(reportPick3);
            drawingsSummaryReport.JoinReport(reportPick4);
            drawingsSummaryReport.JoinReport(reportPick5);

            return drawingsSummaryReport;
        }

        internal override DrawingsSummaryReport Regrade(bool itIsThePresent, DateTime date, DateTime now, string employeeName, bool notifyEvent)
        {
            if (date == DateTime.MinValue) throw new ArgumentNullException(nameof(date));
            if (now == DateTime.MinValue) throw new ArgumentNullException(nameof(now));
            if (string.IsNullOrWhiteSpace(employeeName)) throw new ArgumentNullException(nameof(employeeName));

            DrawingsSummaryReport resultReport = base.Regrade(itIsThePresent, date, now, employeeName, notifyEvent);

            var lotteryPick2Begining = (LotteryPick<Pick2>)this.beginingPicksLotteryGameForMX.GetLottery(2, State);
            DrawingsSummaryReport reportPick2Begining = lotteryPick2Begining.Regrade(itIsThePresent, date, now, employeeName);

            var lotteryPick2 = (LotteryPick<Pick2>)this.endingPicksLotteryGameForMX.GetLottery(2, State);
            DrawingsSummaryReport reportPick2 = lotteryPick2.Regrade(itIsThePresent, date, now, employeeName);

            var lotteryPick3 = (LotteryPick<Pick3>)this.endingPicksLotteryGameForMX.GetLottery(3, State);
            DrawingsSummaryReport reportPick3 = lotteryPick3.Regrade(itIsThePresent, date, now, employeeName);

            var lotteryPick4 = (LotteryPick<Pick4>)this.endingPicksLotteryGameForMX.GetLottery(4, State);
            DrawingsSummaryReport reportPick4 = lotteryPick4.Regrade(itIsThePresent, date, now, employeeName);

            var lotteryPick5 = (LotteryPick<Pick5>)this.endingPicksLotteryGameForMX.GetLottery(5, State);
            DrawingsSummaryReport reportPick5 = lotteryPick5.Regrade(itIsThePresent, date, now, employeeName);

            resultReport.JoinReport(reportPick2Begining);
            resultReport.JoinReport(reportPick2);
            resultReport.JoinReport(reportPick3);
            resultReport.JoinReport(reportPick4);
            resultReport.JoinReport(reportPick5);

            return resultReport;
        }

        internal override NoActionReport SetNoAction(bool itIsThePresent, DateTime date, DateTime now, string employeeName, bool notifyEvent)
        {
            if (date == DateTime.MinValue) throw new ArgumentNullException(nameof(date));
            if (now == DateTime.MinValue) throw new ArgumentNullException(nameof(now));
            if (string.IsNullOrWhiteSpace(employeeName)) throw new ArgumentNullException(nameof(employeeName));

            NoActionReport resultReport = base.SetNoAction(itIsThePresent, date, now, employeeName, notifyEvent);

            var lotteryPick2Begining = (LotteryPick<Pick2>)this.beginingPicksLotteryGameForMX.GetLottery(2, State);
            NoActionReport reportPick2Begining = lotteryPick2Begining.SetNoAction(itIsThePresent, date, now, employeeName);

            var lotteryPick2 = (LotteryPick<Pick2>)this.endingPicksLotteryGameForMX.GetLottery(2, State);
            NoActionReport reportPick2 = lotteryPick2.SetNoAction(itIsThePresent, date, now, employeeName);

            var lotteryPick3 = (LotteryPick<Pick3>)this.endingPicksLotteryGameForMX.GetLottery(3, State);
            NoActionReport reportPick3 = lotteryPick3.SetNoAction(itIsThePresent, date, now, employeeName);

            var lotteryPick4 = (LotteryPick<Pick4>)this.endingPicksLotteryGameForMX.GetLottery(4, State);
            NoActionReport reportPick4 = lotteryPick4.SetNoAction(itIsThePresent, date, now, employeeName);

            var lotteryPick5 = (LotteryPick<Pick5>)this.endingPicksLotteryGameForMX.GetLottery(5, State);
            NoActionReport reportPick5 = lotteryPick5.SetNoAction(itIsThePresent, date, now, employeeName);

            resultReport.JoinReport(reportPick2Begining);
            resultReport.JoinReport(reportPick2);
            resultReport.JoinReport(reportPick3);
            resultReport.JoinReport(reportPick4);
            resultReport.JoinReport(reportPick5);

            return resultReport;
        }

        internal override IEnumerable<PendingDraw> PendingDraws(HourMinute hour)
        {
            var resultPendingDraws = new List<PendingDraw>();

            var pick2BeginingPendingDraws = this.pick2Begining.Tickets.PendingDraws().Where(x => x.Schedule.Hour.Equals(hour));
            resultPendingDraws.AddRange(pick2BeginingPendingDraws);
            var pick2EndingPendingDraws = this.pick2Ending.Tickets.PendingDraws().Where(x => x.Schedule.Hour.Equals(hour));
            resultPendingDraws.AddRange(pick2EndingPendingDraws);
            var pick3EndingPendingDraws = this.pick3Ending.Tickets.PendingDraws().Where(x => x.Schedule.Hour.Equals(hour));
            resultPendingDraws.AddRange(pick3EndingPendingDraws);
            var pick4EndingPendingDraws = this.pick4Ending.Tickets.PendingDraws().Where(x => x.Schedule.Hour.Equals(hour));
            resultPendingDraws.AddRange(pick4EndingPendingDraws);
            var pick5EndingPendingDraws = this.pick5Ending.Tickets.PendingDraws().Where(x => x.Schedule.Hour.Equals(hour));
            resultPendingDraws.AddRange(pick5EndingPendingDraws);

            return resultPendingDraws;
        }

        internal override void ModifyCalendarEvent(string eventId, DayOfWeek day, int hour, int minute)
        {
            if (string.IsNullOrWhiteSpace(eventId)) throw new ArgumentNullException(nameof(eventId));
            if (!(0 >= hour || hour <= 23)) throw new GameEngineException($"Hour {hour} is not valid. It should be at 24-hour time format");
            if (!(0 >= minute || minute <= 59)) throw new GameEngineException($"Minutes {minute} is not valid. Minutes parameter should be between 0 and 59");

            base.ModifyCalendarEvent(eventId, day, hour, minute);

            this.pick2Begining.ModifyCalendarEvent(eventId, day, hour, minute);

            this.pick2Ending.ModifyCalendarEvent(eventId, day, hour, minute);
            this.pick3Ending.ModifyCalendarEvent(eventId, day, hour, minute);
            this.pick4Ending.ModifyCalendarEvent(eventId, day, hour, minute);
            this.pick5Ending.ModifyCalendarEvent(eventId, day, hour, minute);
        }

        internal override void EnableDraw(DateTime date, Domain domain, DateTime now, string employeeName)
        {
            base.EnableDraw(date, domain, now, employeeName);

            this.pick2Begining.EnableDraw(date, domain, now, employeeName);

            this.pick2Ending.EnableDraw(date, domain, now, employeeName);
            this.pick3Ending.EnableDraw(date, domain, now, employeeName);
            this.pick4Ending.EnableDraw(date, domain, now, employeeName);
            this.pick5Ending.EnableDraw(date, domain, now, employeeName);
        }

        internal override void DisableDraw(DateTime date, Domain domain, DateTime now, string employeeName)
        {
            base.DisableDraw(date, domain, now, employeeName);

            this.pick2Begining.DisableDraw(date, domain, now, employeeName);

            this.pick2Ending.DisableDraw(date, domain, now, employeeName);
            this.pick3Ending.DisableDraw(date, domain, now, employeeName);
            this.pick4Ending.DisableDraw(date, domain, now, employeeName);
            this.pick5Ending.DisableDraw(date, domain, now, employeeName);
        }

        internal override List<PayFragmentsMessage> ResendToExternalAccounting(DateTime drawDate, DateTime now, bool itIsThePresent, string employeeName)
        {
            if (drawDate == default(DateTime)) throw new GameEngineException("Date cannot have default value");
            if (now == default(DateTime)) throw new GameEngineException("Date cannot have default value");
            if (string.IsNullOrWhiteSpace(employeeName)) throw new ArgumentNullException(nameof(employeeName));

            List<PayFragmentsMessage> result = null;
            if (this.pick2Begining.Tickets.AnyTicketsInMemory(drawDate))
            {
                List<PayFragmentsMessage> resultPick2Begining = this.pick2Begining.ResendToExternalAccounting(drawDate, now, itIsThePresent, employeeName);
                if (result == null) result = resultPick2Begining;
                else result.AddRange(resultPick2Begining);
            }

            if (this.pick2Ending.Tickets.AnyTicketsInMemory(drawDate))
            {
                List<PayFragmentsMessage> resultPick2Ending = this.pick2Ending.ResendToExternalAccounting(drawDate, now, itIsThePresent, employeeName);
                if (result == null) result = resultPick2Ending;
                else result.AddRange(resultPick2Ending);
            }
            
            if (this.pick3Ending.Tickets.AnyTicketsInMemory(drawDate))
            {
                List<PayFragmentsMessage> resultPick3Ending = this.pick3Ending.ResendToExternalAccounting(drawDate, now, itIsThePresent, employeeName);
                if (result == null) result = resultPick3Ending;
                else result.AddRange(resultPick3Ending);
            }

            if (this.pick4Ending.Tickets.AnyTicketsInMemory(drawDate))
            {
                List<PayFragmentsMessage> resultPick4Ending = this.pick4Ending.ResendToExternalAccounting(drawDate, now, itIsThePresent, employeeName);
                if (result == null) result = resultPick4Ending;
                else result.AddRange(resultPick4Ending);
            }

            if (this.pick5Ending.Tickets.AnyTicketsInMemory(drawDate))
            {
                List<PayFragmentsMessage> resultPick5Ending = this.pick5Ending.ResendToExternalAccounting(drawDate, now, itIsThePresent, employeeName);
                if (result == null) result = resultPick5Ending;
                else result.AddRange(resultPick5Ending);
            }

            return result;
        }

        internal override bool ExistsPostedDrawInMemory(DateTime drawDate)
        {
            bool resultPick2Begining = this.pick2Begining.ExistsPostedDrawInMemory(drawDate);

            bool resultPick2Ending = this.pick2Ending.ExistsPostedDrawInMemory(drawDate);
            bool resultPick3Ending = this.pick3Ending.ExistsPostedDrawInMemory(drawDate);
            bool resultPick4Ending = this.pick4Ending.ExistsPostedDrawInMemory(drawDate);
            bool resultPick5Ending = this.pick5Ending.ExistsPostedDrawInMemory(drawDate);

            return resultPick2Begining || resultPick2Ending || resultPick3Ending || resultPick4Ending || resultPick5Ending;
        }

        internal override bool WasAlreadyConfirmed(DateTime drawDate)
        {
            bool resultPick2Begining = this.pick2Begining.WasAlreadyConfirmed(drawDate);

            bool resultPick2Ending = this.pick2Ending.WasAlreadyConfirmed(drawDate);
            bool resultPick3Ending = this.pick3Ending.WasAlreadyConfirmed(drawDate);
            bool resultPick4Ending = this.pick4Ending.WasAlreadyConfirmed(drawDate);
            bool resultPick5Ending = this.pick5Ending.WasAlreadyConfirmed(drawDate);

            return resultPick2Begining || resultPick2Ending || resultPick3Ending || resultPick4Ending || resultPick5Ending;
        }

        internal override List<PayFragmentsMessage> CreateMessagesToResendDraw(DateTime date, DateTime now)
        {
            if (date == default(DateTime)) throw new GameEngineException("Date cannot have default value");
            if (now == default(DateTime)) throw new GameEngineException("Date cannot have default value");
            if (date.Second != 0 || date.Millisecond != 0) throw new GameEngineException("Date of Lottery drawings can not have seconds");

            List<PayFragmentsMessage> result = null;
            if (this.pick2Begining.Tickets.AnyTicketsInMemory(date))
            {
                List<PayFragmentsMessage> resultPick2Begining = this.pick2Begining.CreateMessagesToResendDraw(date, now);
                if (result == null) result = resultPick2Begining;
                else result.AddRange(resultPick2Begining);
            }

            if (this.pick2Ending.Tickets.AnyTicketsInMemory(date))
            {
                List<PayFragmentsMessage> resultPick2Ending = this.pick2Ending.CreateMessagesToResendDraw(date, now);
                if (result == null) result = resultPick2Ending;
                else result.AddRange(resultPick2Ending);
            }

            if (this.pick3Ending.Tickets.AnyTicketsInMemory(date))
            {
                List<PayFragmentsMessage> resultPick3Ending = this.pick3Ending.CreateMessagesToResendDraw(date, now);
                if (result == null) result = resultPick3Ending;
                else result.AddRange(resultPick3Ending);
            }

            if (this.pick4Ending.Tickets.AnyTicketsInMemory(date))
            {
                List<PayFragmentsMessage> resultPick4Ending = this.pick4Ending.CreateMessagesToResendDraw(date, now);
                if (result == null) result = resultPick4Ending;
                else result.AddRange(resultPick4Ending);
            }

            if (this.pick5Ending.Tickets.AnyTicketsInMemory(date))
            {
                List<PayFragmentsMessage> resultPick5Ending = this.pick5Ending.CreateMessagesToResendDraw(date, now);
                if (result == null) result = resultPick5Ending;
                else result.AddRange(resultPick5Ending);
            }

            return result;
        }

        internal override bool HasPendingTickets(Schedule oldSchedule)
        {
            bool resultPick2Begining = this.pick2Begining.HasPendingTickets(oldSchedule);

            bool resultPick2Ending = this.pick2Ending.HasPendingTickets(oldSchedule);
            bool resultPick3Ending = this.pick3Ending.HasPendingTickets(oldSchedule);
            bool resultPick4Ending = this.pick4Ending.HasPendingTickets(oldSchedule);
            bool resultPick5Ending = this.pick5Ending.HasPendingTickets(oldSchedule);
                
            return resultPick2Begining || resultPick2Ending || resultPick3Ending || resultPick4Ending || resultPick5Ending;
        }

        internal override NoActionReport PreviewNoAction(DateTime date)
        {
            if (date == DateTime.MinValue) throw new ArgumentNullException(nameof(date));

            var lotteryPick2Begining = (LotteryPick<Pick2>)this.beginingPicksLotteryGameForMX.GetLottery(2, State);
            NoActionReport reportPick2Begining = lotteryPick2Begining.PreviewNoAction(date);

            var lotteryPick2 = (LotteryPick<Pick2>)this.endingPicksLotteryGameForMX.GetLottery(2, State);
            NoActionReport reportPick2 = lotteryPick2.PreviewNoAction(date);

            var lotteryPick3 = (LotteryPick<Pick3>)this.endingPicksLotteryGameForMX.GetLottery(3, State);
            NoActionReport reportPick3 = lotteryPick3.PreviewNoAction(date);

            var lotteryPick4 = (LotteryPick<Pick4>)this.endingPicksLotteryGameForMX.GetLottery(4, State);
            NoActionReport reportPick4 = lotteryPick4.PreviewNoAction(date);

            var lotteryPick5 = (LotteryPick<Pick5>)this.endingPicksLotteryGameForMX.GetLottery(5, State);
            NoActionReport reportPick5 = lotteryPick5.PreviewNoAction(date);

            EmptyNoActionReport resultReport = new EmptyNoActionReport(new List<Ticket>());
            resultReport.JoinReport(reportPick2Begining);
            resultReport.JoinReport(reportPick2);
            resultReport.JoinReport(reportPick3);
            resultReport.JoinReport(reportPick4);
            resultReport.JoinReport(reportPick5);

            return resultReport;
        }

        internal override DrawingsSummaryReport ConfirmDraw(DateTime drawDate, DateTime now, string employeeName, bool itIsThePresent, bool notifyEvent)
        {
            if (drawDate == default(DateTime)) throw new ArgumentNullException(nameof(drawDate));
            if (now == default(DateTime)) throw new ArgumentNullException(nameof(now));
            if (string.IsNullOrWhiteSpace(employeeName)) throw new ArgumentNullException(nameof(employeeName));

            DrawingsSummaryReport resultReport = base.ConfirmDraw(drawDate, now, employeeName, itIsThePresent, notifyEvent);

            var lotteryPick2Begining = (LotteryPick<Pick2>)this.beginingPicksLotteryGameForMX.GetLottery(2, State);
            DrawingsSummaryReport reportPick2Begining = lotteryPick2Begining.ConfirmDraw(drawDate, now, employeeName, itIsThePresent, false);

            var lotteryPick2 = (LotteryPick<Pick2>)this.endingPicksLotteryGameForMX.GetLottery(2, State);
            DrawingsSummaryReport reportPick2 = lotteryPick2.ConfirmDraw(drawDate, now, employeeName, itIsThePresent, false);

            var lotteryPick3 = (LotteryPick<Pick3>)this.endingPicksLotteryGameForMX.GetLottery(3, State);
            DrawingsSummaryReport reportPick3 = lotteryPick3.ConfirmDraw(drawDate, now, employeeName, itIsThePresent, false);

            var lotteryPick4 = (LotteryPick<Pick4>)this.endingPicksLotteryGameForMX.GetLottery(4, State);
            DrawingsSummaryReport reportPick4 = lotteryPick4.ConfirmDraw(drawDate, now, employeeName, itIsThePresent, false);

            var lotteryPick5 = (LotteryPick<Pick5>)this.endingPicksLotteryGameForMX.GetLottery(5, State);
            DrawingsSummaryReport reportPick5 = lotteryPick5.ConfirmDraw(drawDate, now, employeeName, itIsThePresent, false);

            resultReport.JoinReport(reportPick2Begining);
            resultReport.JoinReport(reportPick2);
            resultReport.JoinReport(reportPick3);
            resultReport.JoinReport(reportPick4);
            resultReport.JoinReport(reportPick5);

            return resultReport;
        }

        internal bool HasLotteryGame(LotteryGame currentLotteryGame)
        {
            if (currentLotteryGame == null) throw new ArgumentNullException(nameof(currentLotteryGame));

            return this.beginingPicksLotteryGameForMX == currentLotteryGame || this.endingPicksLotteryGameForMX == currentLotteryGame;
        }

        internal override void AttachToDate(DateTime drawDate, DateTime newDrawDate, DateTime now, string employeeName)
        {
            if (drawDate == default(DateTime)) throw new ArgumentNullException(nameof(drawDate));
            if (newDrawDate == default(DateTime)) throw new ArgumentNullException(nameof(newDrawDate));
            if (now == default(DateTime)) throw new ArgumentNullException(nameof(now));
            if (string.IsNullOrWhiteSpace(employeeName)) throw new ArgumentNullException(nameof(employeeName));

            base.AttachToDate(drawDate, newDrawDate, now, employeeName);

            this.pick2Begining.AttachToDate(drawDate, newDrawDate, now, employeeName);

            this.pick2Ending.AttachToDate(drawDate, newDrawDate, now, employeeName);
            this.pick3Ending.AttachToDate(drawDate, newDrawDate, now, employeeName);
            this.pick4Ending.AttachToDate(drawDate, newDrawDate, now, employeeName);
            this.pick5Ending.AttachToDate(drawDate, newDrawDate, now, employeeName);
        }

        internal override void EditAttachToDate(DateTime previusDrawDate, DateTime newDrawDate, DateTime now, string employeeName)
        {
            if (previusDrawDate == default(DateTime)) throw new ArgumentNullException(nameof(previusDrawDate));
            if (newDrawDate == default(DateTime)) throw new ArgumentNullException(nameof(newDrawDate));
            if (now == default(DateTime)) throw new ArgumentNullException(nameof(now));
            if (string.IsNullOrWhiteSpace(employeeName)) throw new ArgumentNullException(nameof(employeeName));

            base.EditAttachToDate(previusDrawDate, newDrawDate, now, employeeName);

            this.pick2Begining.EditAttachToDate(previusDrawDate, newDrawDate, now, employeeName);

            this.pick2Ending.EditAttachToDate(previusDrawDate, newDrawDate, now, employeeName);
            this.pick3Ending.EditAttachToDate(previusDrawDate, newDrawDate, now, employeeName);
            this.pick4Ending.EditAttachToDate(previusDrawDate, newDrawDate, now, employeeName);
            this.pick5Ending.EditAttachToDate(previusDrawDate, newDrawDate, now, employeeName);
        }

        internal override bool ExistPendingTicketsAt(DateTime date)
        {
            if (date == default(DateTime)) throw new ArgumentNullException(nameof(date));

            bool resultPick2Begining = this.pick2Begining.ExistPendingTicketsAt(date);

            bool resultPick2Ending = this.pick2Ending.ExistPendingTicketsAt(date);
            bool resultPick3Ending = this.pick3Ending.ExistPendingTicketsAt(date);
            bool resultPick4Ending = this.pick4Ending.ExistPendingTicketsAt(date);
            bool resultPick5Ending = this.pick5Ending.ExistPendingTicketsAt(date);

            return resultPick2Begining || resultPick2Ending || resultPick3Ending || resultPick4Ending || resultPick5Ending;
        }

        internal PendingDraws PendingDrawings(DateTime startDate, DateTime endDate, string uniqueDrawingId, string accountNumber, string gameType, string ticketNumber, string domainIds)
        {
            var beginningDraws = beginingPicksLotteryGameForMX.Reports.PendingDrawings(startDate, endDate, uniqueDrawingId, accountNumber, gameType, ticketNumber, domainIds);
            var endingDraws = endingPicksLotteryGameForMX.Reports.PendingDrawings(startDate, endDate, uniqueDrawingId, accountNumber, gameType, ticketNumber, domainIds);

            var combinedPendingDraws = beginningDraws.GetAll.Concat(endingDraws.GetAll);
            return new PendingDraws(combinedPendingDraws);
        }

        internal TicketsPerPlayers TicketsPerPlayersInPendingDrawing(DateTime drawDate, string uniqueDrawingId, string accountNumber, string gameType, string ticketNumber, string domainIds)
        {
            var beginningTicketsPerPlayers = beginingPicksLotteryGameForMX.Reports.TicketsPerPlayersInPendingDrawing(drawDate, uniqueDrawingId, accountNumber, gameType, ticketNumber, domainIds);
            var endingTicketsPerPlayers = endingPicksLotteryGameForMX.Reports.TicketsPerPlayersInPendingDrawing(drawDate, uniqueDrawingId, accountNumber, gameType, ticketNumber, domainIds);

            var combinedTicketsPerPlayers = beginningTicketsPerPlayers.GetAll.Concat(endingTicketsPerPlayers.GetAll);
            return new TicketsPerPlayersInPendingDraws(combinedTicketsPerPlayers);
        }

        internal TicketsPerPlayers TicketsPerPlayersInPendingDrawings(DateTime startDate, DateTime endDate, string uniqueDrawingId, string accountNumber, string gameType, string ticketNumber, string domainIds)
        {
            var beginningTicketsPerPlayers = beginingPicksLotteryGameForMX.Reports.TicketsPerPlayersInPendingDrawings(startDate, endDate, uniqueDrawingId, accountNumber, gameType, ticketNumber, domainIds);
            var endingTicketsPerPlayers = endingPicksLotteryGameForMX.Reports.TicketsPerPlayersInPendingDrawings(startDate, endDate, uniqueDrawingId, accountNumber, gameType, ticketNumber, domainIds);

            var combinedTicketsPerPlayers = beginningTicketsPerPlayers.GetAll.Concat(endingTicketsPerPlayers.GetAll);
            return new TicketsPerPlayersInPendingDraws(combinedTicketsPerPlayers);
        }

        internal WagersPerPlayerInPendingDraw WagersPerPlayerInPendingDrawing(Player player, DateTime drawDate, string drawingId, string gameType, string ticketNumber, string domainIds)
        {
            var beginningWagersPerPlayer = beginingPicksLotteryGameForMX.Reports.WagersPerPlayerInPendingDrawing(player, drawDate, drawingId, gameType, ticketNumber, domainIds);
            var endingWagersPerPlayer = endingPicksLotteryGameForMX.Reports.WagersPerPlayerInPendingDrawing(player, drawDate, drawingId, gameType, ticketNumber, domainIds);

            var combinedWagersPerPlayer = beginningWagersPerPlayer.Wagers().Concat(endingWagersPerPlayer.Wagers());
            return new WagersPerPlayerInPendingDraw(combinedWagersPerPlayer);
        }

        internal WagersPerPlayerInPendingDraw WagersPerPlayerInPendingDrawing(DateTime startDate, DateTime endDate, string drawingId, string accountNumber, string gameType, string ticketNumber, string domainIds)
        {
            var beginningWagersPerPlayer = beginingPicksLotteryGameForMX.Reports.WagersPerPlayerInPendingDrawing(startDate, endDate, drawingId, accountNumber, gameType, ticketNumber, domainIds);
            var endingWagersPerPlayer = endingPicksLotteryGameForMX.Reports.WagersPerPlayerInPendingDrawing(startDate, endDate, drawingId, accountNumber, gameType, ticketNumber, domainIds);

            var combinedWagersPerPlayer = beginningWagersPerPlayer.Wagers().Concat(endingWagersPerPlayer.Wagers());
            return new WagersPerPlayerInPendingDraw(combinedWagersPerPlayer);
        }

        internal IEnumerable<GradingPicksRecord> Grading(DateTime startDate, DateTime endDate, string uniqueDrawingId, string gameType)
        {
            var beginningGrading = beginingPicksLotteryGameForMX.Reports.Grading(startDate, endDate, uniqueDrawingId, gameType);
            var endingGrading = endingPicksLotteryGameForMX.Reports.Grading(startDate, endDate, uniqueDrawingId, gameType);

            var combinedGrading = beginningGrading.Concat(endingGrading);
            return combinedGrading;
        }

        internal TypeNumberSequence GetTypeNumberSequence(Lottery lottery)
        {
            if (lottery == null) throw new ArgumentNullException(nameof(lottery));

            if (lottery == this.pick2Begining) return TypeNumberSequence.BEGINNING;
            if (lottery == this.pick2Ending) return TypeNumberSequence.ENDING;
            if (lottery == this.pick3Ending) return TypeNumberSequence.ENDING;
            if (lottery == this.pick4Ending) return TypeNumberSequence.ENDING;
            if (lottery == this.pick5Ending) return TypeNumberSequence.ENDING;

            throw new GameEngineException($"Unkown {nameof(Lottery)} for Lottery {nameof(lottery)}");
        }

        internal override IEnumerable<Schedule> AllEnabledSchedules(DateTime date, Domain domain)
        {
            var enabledSchedules = new List<Schedule>();
            foreach (var schedule in pick2Begining.Schedules)
            {
                if (state.Enable && schedule.IsEnabled() && IsEnabledDraw(schedule.ToDateTime(date), domain))
                {
                    enabledSchedules.Add(schedule);
                }
            }
            foreach (var schedule in pick2Ending.Schedules)
            {
                if (state.Enable && schedule.IsEnabled() && IsEnabledDraw(schedule.ToDateTime(date), domain))
                {
                    enabledSchedules.Add(schedule);
                }
            }
            foreach (var schedule in pick3Ending.Schedules)
            {
                if (state.Enable && schedule.IsEnabled() && IsEnabledDraw(schedule.ToDateTime(date), domain))
                {
                    enabledSchedules.Add(schedule);
                }
            }
            foreach (var schedule in pick4Ending.Schedules)
            {
                if (state.Enable && schedule.IsEnabled() && IsEnabledDraw(schedule.ToDateTime(date), domain))
                {
                    enabledSchedules.Add(schedule);
                }
            }
            foreach (var schedule in pick5Ending.Schedules)
            {
                if (state.Enable && schedule.IsEnabled() && IsEnabledDraw(schedule.ToDateTime(date), domain))
                {
                    enabledSchedules.Add(schedule);
                }
            }
            return enabledSchedules;
        }

        internal override bool TryGetScheduleByUniqueId(int uniqueDrawingId, out Schedule schedule)
        {
            bool result = false;
            if (!pick2Begining.TryGetScheduleByUniqueId(uniqueDrawingId, out schedule))
            {
                if (!pick2Ending.TryGetScheduleByUniqueId(uniqueDrawingId, out schedule))
                {
                    if (!pick3Ending.TryGetScheduleByUniqueId(uniqueDrawingId, out schedule))
                    {
                        if (!pick4Ending.TryGetScheduleByUniqueId(uniqueDrawingId, out schedule))
                        {
                            if (!pick5Ending.TryGetScheduleByUniqueId(uniqueDrawingId, out schedule))
                            {
                                throw new GameEngineException($"UniqueDrawingId {uniqueDrawingId} not found in any Pick Lottery");
                            }
                        }
                    }
                }
            }
            return result;
        }

        internal override bool ExistsScheduleByUniqueId(int uniqueDrawingId)
        {
            Schedule schedule = null;
            if (!pick2Begining.TryGetScheduleByUniqueId(uniqueDrawingId, out schedule))
            {
                if (!pick2Ending.TryGetScheduleByUniqueId(uniqueDrawingId, out schedule))
                {
                    if (!pick3Ending.TryGetScheduleByUniqueId(uniqueDrawingId, out schedule))
                    {
                        if (!pick4Ending.TryGetScheduleByUniqueId(uniqueDrawingId, out schedule))
                        {
                            if (!pick5Ending.TryGetScheduleByUniqueId(uniqueDrawingId, out schedule))
                            {
                                return false;
                            }
                        }
                    }
                }
            }
            return true;
        }

        internal IEnumerable<SubticketWithAvailable> SubticketsExceedingToWin(PrizesPicks prizes, List<string> numbersAsText, DateTime drawDate, Domain domain, decimal betAmount, List<string> ticketTypes)
        {
            if (numbersAsText == null || numbersAsText.Count == 0) throw new ArgumentNullException(nameof(numbersAsText));
            if (drawDate == default(DateTime)) throw new GameEngineException("Date cannot have default value");
            if (drawDate.Second != 0 || drawDate.Millisecond != 0) throw new GameEngineException("Date of Lottery drawings can not have seconds");
            if (betAmount <= 0) throw new GameEngineException($"{nameof(betAmount)} {betAmount} must be greater than 0");
            if (domain == null) throw new ArgumentNullException(nameof(domain));

            var result = new List<SubticketWithAvailable>();
            int index = 0;
            foreach (var numberAsText in numbersAsText)
            {
                if (string.IsNullOrWhiteSpace(numberAsText)) throw new ArgumentNullException(nameof(numberAsText));

                bool ending = ! numberAsText.StartsWith(Pick2.SPLIT_BEGINNING);
                string fixedNumber = numberAsText.Replace(Pick2.SPLIT_BEGINNING, string.Empty);
                if (!Enum.TryParse(ticketTypes[index], true, out TicketType ticketType)) throw new GameEngineException($"Invalid {nameof(ticketType)} {ticketTypes[index]}");

                var riskProfile = Company.LotteryGamesPool.RiskProfiles.GetRiskProfile(domain);
                var pickNumber = fixedNumber.Length;
                var internalLottery = GetLotteryPick(pickNumber, ending);
                var riskPerLottery = riskProfile.Risks.Risk.GetRiskPerLottery(pickNumber, internalLottery);

                var subticket = SubTicket<IPick>.SubticketFromNumber(fixedNumber);
                var prizeForCurrentSubticket = riskPerLottery.CalculatePrizeToWin(subticket, betAmount, ticketType, domain);
                var prizeCriteriaId = Disincentives.WayOfSubticket(ticketType, subticket);
                var ruleType = RuleTypeFromTicketType(ticketType);
                var schedule = internalLottery.FindScheduleAt(drawDate);
                var remainingToWin = riskPerLottery.RemainingToWinToAccumulate(schedule, prizeCriteriaId, ruleType, subticket, domain, drawDate);
                if (prizeForCurrentSubticket > remainingToWin)
                {
                    var maxRiskForSubticket = Math.Floor((betAmount * remainingToWin / prizeForCurrentSubticket) * 100) / 100;
                    result.Add(new SubticketWithAvailable(numberAsText, maxRiskForSubticket));
                }
                index++;
            }
            return result;
        }

        internal IEnumerable<RiskPerLottery> GetRiskPerLotteries(RiskPerLotteries riskPerLotteries)
        {
            var result = new List<RiskPerLottery>();
            var riskPerLottery = riskPerLotteries.GetRiskPerLottery(2, pick2Begining);
            result.Add(riskPerLottery);
            riskPerLottery = riskPerLotteries.GetRiskPerLottery(2, pick2Ending);
            result.Add(riskPerLottery);
            riskPerLottery = riskPerLotteries.GetRiskPerLottery(3, pick3Ending);
            result.Add(riskPerLottery);
            riskPerLottery = riskPerLotteries.GetRiskPerLottery(4, pick4Ending);
            result.Add(riskPerLottery);
            riskPerLottery = riskPerLotteries.GetRiskPerLottery(5, pick5Ending);
            result.Add(riskPerLottery);
            return result;
        }
    }
}
