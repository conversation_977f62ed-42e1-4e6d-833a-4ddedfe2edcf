﻿using Connectors;
using Connectors.town.connectors.driver.transactions;
using Connectors.town.connectors.drivers.zeus;
using Microsoft.AspNetCore.Http;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using town.connectors;
using town.connectors.commons;
using town.connectors.drivers;
using town.connectors.drivers.zeus;
using static town.connectors.CustomSettings;
using static town.connectors.CustomSettings.CustomSetting;
using Balance = Connectors.town.connectors.drivers.zeus.Balance;
using ValidateCustomer = Connectors.town.connectors.drivers.zeus.ValidateCustomer;

namespace GamesEngineTests.Unit_Tests.Connectors
{
	[TestClass]
	public class ZeusConnectorTest
    {
		[TestMethod]
		public void Deposit()
		{
			//TODO disparar el docker-compose  cashier asi stub
			Variables variables = new Variables();
			var obj = new { Name = "tester" };
			DateTime now = DateTime.Now;
			CustomSettings cs = new CustomSettings(variables);
			cs.AddFixedParameter(now, "CompanyBaseUrlServices", "http://test/LottoAPI/v1/");
			cs.AddFixedParameter(now, "TokenSystemId", "LottoZeus");
			cs.AddFixedParameter(now, "TokenSystemPassword", new Secret("HBDSEqQzEVNOCc13"));

			Deposit authorization = new Deposit();

			authorization.RegisterANotifyErrorsBehavior((string message, Exception e, string[] details) =>
			{

			});
			authorization.RegisterANotifyWarningsBehavior((string message, Exception e, string[] details) =>
			{

			});

			DepositTransaction result;

			authorization.ConfigureThenPrepare(now, cs);
			DGSProcessorDriver.AppToken = "asdafds";
			using (RecordSet recordSet = authorization.CustomSettings.GetRecordSet())
			{
				recordSet.SetParameter("amount", 100);
				recordSet.SetParameter("customerId", "ABC5");
				recordSet.SetParameter("description", "Description");

				result = (DepositTransaction)authorization.Execute<DepositTransaction>(now, recordSet);
			}
		}


		[TestMethod]
		public void Withdrawal()
		{
			//TODO disparar el docker-compose  cashier asi stub
			Variables variables = new Variables();
			DateTime now = DateTime.Now;
			CustomSettings cs = new CustomSettings(variables);
			cs.AddFixedParameter(now, "CompanyBaseUrlServices", "https://test/LottoAPI/v1/");
			cs.AddFixedParameter(now, "TokenSystemId", "LottoZeus");
			cs.AddFixedParameter(now, "TokenSystemPassword", new Secret("HBDSEqQzEVNOCc13"));

			Withdrawal authorization = new Withdrawal();

			authorization.RegisterANotifyErrorsBehavior((string message, Exception e, string[] details) =>
			{
				Assert.AreEqual("This is an error", message);
				Assert.AreEqual("param 1", details[0]);
				Assert.AreEqual("param 2", details[1]);
			});
			authorization.RegisterANotifyWarningsBehavior((string message, Exception e, string[] details) =>
			{
				Assert.IsTrue(details[0].StartsWith("DeductAmount"));
			});

			WithdrawalTransaction result;

			authorization.ConfigureThenPrepare(now, cs);
			DGSProcessorDriver.AppToken = "asdafds";
			using (RecordSet recordSet = authorization.CustomSettings.GetRecordSet())
			{
				FieroConnectorTest.TestContext context = new FieroConnectorTest.TestContext();
				context.Request.Headers.Add("tk_1", "dfsf");

				recordSet.SetParameter("httpContext", context);
				recordSet.SetParameter("amount", 100);
				recordSet.SetParameter("customerId", "ABC5");
				recordSet.SetParameter("description", "Description");
				recordSet.SetParameter("referenceID", "Reference");

				result = (WithdrawalTransaction)authorization.Execute<WithdrawalTransaction>(now, recordSet);
			}
		}

		[TestMethod]
		public async Task BalanceAsync()
		{
			DGSProcessorDriver.AppToken = "token_test_123";
			//TODO disparar el docker-compose  cashier asi stub
			Variables variables = new Variables();
			DateTime now = DateTime.Now;
			CustomSettings cs = new CustomSettings(variables);
			cs.AddFixedParameter(now, "CompanyBaseUrlServices", "https://test/LottoAPI/v1/");
			cs.AddFixedParameter(now, "TokenSystemId", "LottoZeus");
			cs.AddFixedParameter(now, "TokenSystemPassword", new Secret("HBDSEqQzEVNOCc13"));

			var driver = new Balance();
			driver.RegisterANotifyErrorsBehavior((string message, Exception e, string[] details) =>
			{
				Assert.AreEqual("This is an error", message);
				Assert.AreEqual("param 1", details[0]);
				Assert.AreEqual("param 2", details[1]);
			});
			driver.RegisterANotifyWarningsBehavior((string message, Exception e, string[] details) =>
			{
				Assert.IsTrue(details[0].Count()>0);
			});

			driver.ConfigureThenPrepare(now, cs);
			using (RecordSet recordSet = driver.CustomSettings.GetRecordSet())
			{
				FieroConnectorTest.TestContext context = new FieroConnectorTest.TestContext();
				context.Request.Headers.Add("tk_1", "dfsf");

				recordSet.SetParameter("playerId", "123");
				recordSet.SetParameter("httpContext", context);
				var result = await driver.ExecuteAsync<decimal>(DateTime.Now, recordSet);
			}
		}

		[TestMethod]
		public async Task ValidateCustomerAsync()
		{
			DGSProcessorDriver.AppToken = "token_test_123";
			//TODO disparar el docker-compose  cashier asi stub
			Variables variables = new Variables();
			DateTime now = DateTime.Now;
			CustomSettings cs = new CustomSettings(variables);
			cs.AddFixedParameter(now, "CompanyBaseUrlServices", "https://test/LottoAPI/v1/");
			cs.AddFixedParameter(now, "TokenSystemId", "LottoZeus");
			cs.AddFixedParameter(now, "TokenSystemPassword", new Secret("HBDSEqQzEVNOCc13"));

			var driver = new ValidateCustomer();
			driver.ConfigureThenPrepare(now, cs);
			using (RecordSet recordSet = driver.CustomSettings.GetRecordSet())
			{
				recordSet.SetParameter("customerId", "123");
				recordSet.SetParameter("token", "123");
				var result = await driver.ExecuteAsync<bool>(DateTime.Now, recordSet);
			}
		}
	}
}
