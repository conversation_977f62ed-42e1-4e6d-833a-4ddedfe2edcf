﻿using CashierAPI.Controllers;
using GamesEngine.Bets;
using GamesEngine.Business;
using GamesEngine.Finance;
using GamesEngine.Gameboards.Lines;
using GamesEngine.Games;
using GamesEngine.Games.Lines;
using GamesEngine.Games.Tournaments;
using GamesEngine.PurchaseOrders;
using GamesEngine.PurchaseOrders.Activators.Lines;
using GamesEngine.Settings;
using GamesEngine.Time;
using GamesEngineMocks;
using GamesEngineTests.Unit_Tests.Games.Lotto;
using LinesBIAPI;
using LoyaltyAPI.Controllers;
using Microsoft.Extensions.Configuration;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using town.connectors;
using Unit.Games.Tools;
using static GamesEngine.Finance.Currencies;
using static GamesEngineTests.Unit_Tests.SportsBook.LinesTest;

namespace GamesEngineTests.Unit_Tests.SportsBook
{
	[TestClass]
	public class SportsBookTest
	{
		private RestAPISpawnerActor actor = new RestAPISpawnerActor(new CashierMocks(), "t","t","t");
		private Variables variables = new Variables();
		private decimal ConvertirAPlata(double plata)
		{
			return Convert.ToDecimal(Math.Round(plata, 2));
		}

		[TestMethod]
		public void HomeAndVisitor_Test()
		{
			DateTime now = new DateTime(2020, 11, 02);
			Company company = new Company();
			company.Sales.CreateStore(1, "Test Store").MakeCurrent();
			Tournaments tournaments = company.Tournaments;
			Tournament nba = new NBATournament(tournaments);//TODO Tournament nos esta forzando a que los juegos de FirstGame tengan game y sucesor y esten definidos
			var gameNumber = 1;
			Game game = nba.GetGameNumber(gameNumber);
			game.SetFavorite(game.TeamA);
			game.SetFavorite(game.TeamA, game.TeamB);
			var isA = game.Favorite.IsTeamA();
			var isB = game.Favorite.IsTeamB();
			var both = game.Favorite.AreBoth();

			game.Home = game.TeamA;
			game.Visitor = game.TeamB;

			Assert.AreEqual(game.Home, game.TeamA);
			Assert.AreEqual(game.Visitor, game.TeamB);

			game.Home = game.TeamB;
			game.Visitor = game.TeamA;

			Assert.AreEqual(game.Home, game.TeamB);
			Assert.AreEqual(game.Visitor, game.TeamA);

			game.Home = game.TeamB;
			game.Home = game.TeamA;

			Assert.AreEqual(game.Home, game.TeamA);
			Assert.AreEqual(game.Visitor, game.TeamB);
		}

		[TestMethod]
		public void GiveMeTheMatchesForToday_Spread()
		{
			DateTime now = new DateTime(2020, 11, 02);
			Integration.ConfigurationForAutoTest("");

			Company company = new Company();
			var domain = company.Sales.CreateDomain(false, 1, "localhost", PaymentChannels.Agents.INSIDER);
			company.Sales.CreateStore(1, "store prueba").MakeCurrent();
			company.Sales.CurrentStore.Add(domain);
			var player = company.GetOrCreateCustomerById("*********").Player;
			Tournaments tournaments = company.Tournaments;
			//una maquina por matchday
			Tournament torneo = new NBATournament(tournaments);//TODO Tournament nos esta forzando a que los juegos de FirstGame tengan game y sucesor y esten definidos
			var gameNumber = 1;
			Game game = torneo.GetGameNumber(gameNumber);
			var sport = company.Tournaments.Sports.FindById(1);
			sport.CreateAndReplaceDefaultBackground("bg", "bg.jpg");
			sport.CreateAndReplaceDefaultSideBySide("sbs", "sbs.jpg");
			var betBoard = company.Betboard(torneo);
			betBoard.AcceptFrom(torneo);
			var leagues = betBoard.Leagues(Sport.BASKETBALL);
			Assert.AreEqual(1, leagues.Count);
			foreach (var l in leagues)
			{
				Assert.AreEqual(torneo.League, l);
			}
			TournamentSchedule schedule = torneo.Schedule;

			schedule.AddGameToMatchday(game, now, game.TeamA, game.TeamA);
			var diaDeJuegos = betBoard.Matchday(now);

			var questionSL = betBoard.Catalog.CreateTierOneSpreadQuestion(1, "ss");

			game.SetFavorite(game.TeamA);
			game = torneo.GetGameNumber(gameNumber);
			var fila1 = 0;
			var lineId = betBoard.NextLineId();
			questionSL.SetParameters(2.0, -120, 105);
			var spreadVersion1 = (SpreadLine)diaDeJuegos[game][fila1].CreateLine(lineId + 3, questionSL, "N/A", now);
			var columna = spreadVersion1.Index;

			var authorizationId = 1;
			var answerA = spreadVersion1.GetTeamAnswer(game.TeamA);
			var wagerSpread1 = spreadVersion1.PlaceBet(authorizationId, player, now, answerA, 0.25m);
			var riskAssestment = diaDeJuegos[game].RiskAssestment;
			Assert.AreEqual(riskAssestment.PlacedFor(answerA), 0.25m);
			Assert.AreEqual(riskAssestment.ToPayFor(answerA), ConvertirAPlata((100 / 120.0) * 0.25));

			var answerB = spreadVersion1.GetTeamAnswer(game.TeamB);
			var wagerSpread2 = spreadVersion1.PlaceBet(authorizationId, player, now, answerB, 0.75m);
			riskAssestment = diaDeJuegos[game].RiskAssestment;
			Assert.AreEqual(riskAssestment.PlacedFor(answerA), 0.25m);
			Assert.AreEqual(riskAssestment.PlacedFor(answerB), 0.75m);
			Assert.AreEqual(riskAssestment.ToPayFor(answerA), ConvertirAPlata((100 / 120.0) * 0.25));
			Assert.AreEqual(riskAssestment.ToPayFor(answerB), ConvertirAPlata((105 / 100.0) * 0.75));

			var nowNewVersion = new DateTime(2020, 11, 02, 8, 0, 0);
			var spreadVersion2 = diaDeJuegos[game][fila1].CreateNewVersionForSpreadLine(lineId + 3, -120, 105, "N/A", nowNewVersion);
			var wagerSpread3 = spreadVersion2.PlaceBet(authorizationId, player, now, answerB, 1.25m);
			riskAssestment = diaDeJuegos[game].RiskAssestment;
			Assert.AreEqual(riskAssestment.PlacedFor(answerA), 0.25m);
			Assert.AreEqual(riskAssestment.PlacedFor(answerB), 2.0m);
			Assert.AreEqual(riskAssestment.ToPayFor(answerA), ConvertirAPlata((100 / 120.0) * 0.25));
			Assert.AreEqual(riskAssestment.ToPayFor(answerB), ConvertirAPlata((105 / 100.0) * 0.75 + (105 / 100.0) * 1.25));

			var wager4 = spreadVersion2.PlaceBet(authorizationId, player, now, answerB, 0.75m);
			riskAssestment = diaDeJuegos[game].RiskAssestment;
			Assert.AreEqual(riskAssestment.PlacedFor(answerA), 0.25m);
			Assert.AreEqual(riskAssestment.PlacedFor(answerB), 2.75m);
			Assert.AreEqual(riskAssestment.ToPayFor(answerA), ConvertirAPlata((100 / 120.0) * 0.25));
			Assert.AreEqual(riskAssestment.ToPayFor(answerB), ConvertirAPlata((105 / 100.0) * 0.75 + (105 / 100.0) * 1.25 + (105 / 100.0) * 0.75));

			var gameboards = diaDeJuegos[game].Wagers;
			Assert.AreEqual(gameboards.NextSequence(), 5);

			RiskAssestment risk = diaDeJuegos[game].RecalculateRisk();
			riskAssestment = diaDeJuegos[game].RiskAssestment;
			Assert.AreEqual(riskAssestment.PlacedFor(answerA), 0.25m);
			Assert.AreEqual(riskAssestment.PlacedFor(answerB), 2.75m);
			Assert.AreEqual(riskAssestment.ToPayFor(answerA), ConvertirAPlata((100 / 120.0) * 0.25));
			Assert.AreEqual(riskAssestment.ToPayFor(answerB), ConvertirAPlata((105 / 100.0) * 0.75 + (105 / 100.0) * 1.25 + (105 / 100.0) * 0.75));


			//Hacer TC de refund de una linea
			//Se debe poder consultar una cuenta vivo y muerto
			//En que momento pasar las lineas (authNumber) de vivos a BI
			//Reportes de gradeo, cuales fueron los partidos gradeados ayer
			//Ver que se hace con un regrade... No tenemos no action de un juego
			//Reprogramar y suspender juegos
			//DonBest
			//Servicios para el BO
			//Red Tag --> etiquetar players que saben mucho de un deporte, identificarlos para poder monitorearlos
			//TODO el grade deberia devolver un resumen como si fuera la loteria, ademas hay que hacer un Send de cuanta plata para la cuenta que gano
			//Darle prioridad a la cola de los winners, otra cola para los losers
			//TODO Faltan los rest para Cashier para comunicarnos con ASI, como si fuera ASI
			//Cuando se gradea hay que pasar las cosas a BI



			// a tal juego de este torneo, agreguele como primera version estas 3 lineas. 



			// deme los deportes, retorna Basketball .. pueden existir otros.
			// basado en que existe Basketball, deme todas las ligas activas o vigentes. Debe devolver que tiene la NBA. o si existe otras.
			// 


			// Para esta NBA, La funcion debe devolver, deme de la pizara todos los juegos de un dia del torneo (2020) de la liga (nba). 
			// -deme la pizara de este dia de la liga tal y dentro de esta pizara deme las lineas de este juego, para cada row se debe regresar las 3 lineas.
			// -debe dar una lista de lineas 
			// -luego con esas lineas, cada linea tiene un X,Y 
			// -Quiero cambiar la linea (X,Y). y con version V de tal juego de tal dia para que ahora tenga otro 'apuesta'
			// y debe devolver una nueva version (v+1) de la linea. 

		}

		[TestMethod]
		public void Purchase_SpreadLine()
		{
			var sqlStringConection = "";// "persistsecurityinfo=True;port=3306;Server=localhost;Database=fresh;user id=admin;password=******;SslMode=none";
			if (string.IsNullOrEmpty(sqlStringConection)) Assert.Inconclusive("MySQL string connection its required.");
			string connection = "Inmemory";
			DateTime now = new DateTime(2020, 11, 02);
			Integration.ConfigurationForAutoTest("");

			//Integration.UseKafka = false;
			//Integration.UseKafkaForAuto = false;
			var directory = Directory.GetCurrentDirectory();
			if (!File.Exists(directory + "//appsettings.json"))
			{
				directory = Directory.GetCurrentDirectory() + "..\\..\\..\\..\\";
			}
			if (!File.Exists(directory + "\\appsettings.json"))
			{
				directory = Directory.GetCurrentDirectory() + "\\..\\GamesEngineTests\\";
			}
			if (!File.Exists(directory + "//appsettings.json"))
			{
				directory = Directory.GetCurrentDirectory() + "//..//..//..//";
			}
			Microsoft.Extensions.Configuration.IConfiguration configuration;
			var builder = new ConfigurationBuilder()
				.SetBasePath(directory)
				.AddJsonFile("appsettings.json", optional: false, reloadOnChange: true)
				.AddJsonFile("customization/appsettings.accounting.json", optional: true)
				.AddJsonFile("customization/appsettings.security.json", optional: true)
				.AddJsonFile("customization/appsettings.kafka.json", optional: true)
				.AddJsonFile("customization/appsettings.error_emails.json", optional: true)
				.AddJsonFile("customization/appsettings.apm.json", optional: true)
				.AddJsonFile("secrets/appsettings.secrets.json", optional: true);
			configuration = builder.Build();
			HttpELKClient.Configure(configuration.GetSection("ELKIntegration").GetSection("elkserver").Value);

			Integration.Db.MySQL = sqlStringConection;
			var marketingStorage = LoyaltyAPI.Settings.GetAMarketingStorageInstance();
			new LoyaltyController().CreateConsumerForTopics(marketingStorage);
			new ApiController().CreateConsumerForTopics();
						
			Integration.SavePreviousSettings(tempUseKafka: true, tempUseKafkaForAuto: false);

			Company company = new Company();
			var domain = company.Sales.CreateDomain(false, 1, "localhost", PaymentChannels.Agents.INSIDER);
			company.Sales.CreateStore(1, "store prueba").MakeCurrent();
			company.Sales.CurrentStore.Add(domain);
			company.Accounting = new MockAccounting();
			var sport = company.Tournaments.Sports.FindById(1);
			sport.CreateAndReplaceDefaultBackground("bg", "bg.jpg");
			sport.CreateAndReplaceDefaultSideBySide("sbs", "sbs.jpg");
			var customer = company.GetOrCreateCustomerById("*********");
			string playerId = customer.Player.Id;
			var player = customer.Player;
			Tournaments tournaments = company.Tournaments;
			//{basketball}/nba una maquina por juego
			Tournament torneo = new NBATournament(tournaments);//TODO Tournament nos esta forzando a que los juegos de FirstGame tengan game y sucesor y esten definidos
			var gameNumber = 1;
			Game game = torneo.GetGameNumber(gameNumber);
			game.Reschedule(now);

			Product product = new Product(company, 1);
			product.Price = 1;

			var betBoard = company.Betboard(torneo);
			betBoard.AcceptFrom(torneo);
			var leagues = betBoard.Leagues(Sport.BASKETBALL);
			Assert.AreEqual(1, leagues.Count);
			foreach (var l in leagues)
			{
				Assert.AreEqual(torneo.League, l);
			}

			var diaDeJuegos = betBoard.Matchday(now);

			var authorizationId = 1;
			var orderNumber = 1;
			var betNumber = 1;
			var itIsThePresent = true;

			var questionSL = betBoard.Catalog.CreateTierOneSpreadQuestion(1, "ss");

			//diaDeJuegos.AgregarElJuego(game, game.TeamA, game.TeamA);
			//Haber puesto en index houstonVsDallas un ID que viene de la UI y se la envie
			//Otra forma es que la liga agarre un ID y me lo pase a una instancia de game que sea como houstonVsDallas
			game.SetFavorite(game.TeamA);
			game = torneo.GetGameNumber(gameNumber);
			var fila1 = 0;
			var lineId = betBoard.NextLineId();
			questionSL.SetParameters(2.0, -120, 105);
			var spreadVersion1 = (SpreadLine)diaDeJuegos[game][fila1].CreateLine(lineId + 3, questionSL, "N/A", now);
			spreadVersion1.Publish(itIsThePresent, now);
			var columna = spreadVersion1.Index;

			OrderCart order = company.NewOrder(customer, orderNumber++, Coinage.Coin(CODES.USD), authorizationId);
			order.AddSpreadLine(betNumber++, spreadVersion1, game.TeamA, product, 0.25m);
			company.PurchaseOrder(itIsThePresent, order, domain, now);
			var riskAssestment = diaDeJuegos[game].RiskAssestment;
			var answerA = spreadVersion1.GetTeamAnswer(game.TeamA);
			Assert.AreEqual(riskAssestment.PlacedFor(answerA), 0.25m);
			Assert.AreEqual(riskAssestment.ToPayFor(answerA), ConvertirAPlata((100 / 120.0) * 0.25));

			order = company.NewOrder(customer, orderNumber++, Coinage.Coin(CODES.USD), authorizationId);
			order.AddSpreadLine(betNumber++, spreadVersion1, game.TeamB, product, 0.75m);
			company.PurchaseOrder(itIsThePresent, order, domain, now);
			riskAssestment = diaDeJuegos[game].RiskAssestment;
			var answerB = spreadVersion1.GetTeamAnswer(game.TeamB);
			Assert.AreEqual(riskAssestment.PlacedFor(answerA), 0.25m);
			Assert.AreEqual(riskAssestment.PlacedFor(answerB), 0.75m);
			Assert.AreEqual(riskAssestment.ToPayFor(answerA), ConvertirAPlata((100 / 120.0) * 0.25));
			Assert.AreEqual(riskAssestment.ToPayFor(answerB), ConvertirAPlata((105 / 100.0) * 0.75));

			var nowNewVersion = new DateTime(2020, 11, 02, 8, 0, 0);
			var spreadVersion2 = diaDeJuegos[game][fila1].CreateNewVersionForSpreadLine(lineId + 3, -120, 105, "N/A", nowNewVersion);

			order = company.NewOrder(customer, orderNumber++, Coinage.Coin(CODES.USD), authorizationId);
			order.AddSpreadLine(betNumber++, spreadVersion2, game.TeamB, product, 1.25m);
			company.PurchaseOrder(itIsThePresent, order, domain, now);
			riskAssestment = diaDeJuegos[game].RiskAssestment;
			Assert.AreEqual(riskAssestment.PlacedFor(answerA), 0.25m);
			Assert.AreEqual(riskAssestment.PlacedFor(answerB), 2.0m);
			Assert.AreEqual(riskAssestment.ToPayFor(answerA), ConvertirAPlata((100 / 120.0) * 0.25));
			Assert.AreEqual(riskAssestment.ToPayFor(answerB), ConvertirAPlata((105 / 100.0) * 0.75 + (105 / 100.0) * 1.25));

			order = company.NewOrder(customer, orderNumber++, Coinage.Coin(CODES.USD), authorizationId);
			order.AddSpreadLine(betNumber++, spreadVersion2, game.TeamB, product, 0.75m);
			company.PurchaseOrder(itIsThePresent, order, domain, now);
			riskAssestment = diaDeJuegos[game].RiskAssestment;
			Assert.AreEqual(riskAssestment.PlacedFor(answerA), 0.25m);
			Assert.AreEqual(riskAssestment.PlacedFor(answerB), 2.75m);
			Assert.AreEqual(riskAssestment.ToPayFor(answerA), ConvertirAPlata((100 / 120.0) * 0.25));
			Assert.AreEqual(riskAssestment.ToPayFor(answerB), ConvertirAPlata((105 / 100.0) * 0.75 + (105 / 100.0) * 1.25 + (105 / 100.0) * 0.75));

			RiskAssestment risk = diaDeJuegos[game].RecalculateRisk();
			riskAssestment = diaDeJuegos[game].RiskAssestment;
			Assert.AreEqual(riskAssestment.PlacedFor(answerA), 0.25m);
			Assert.AreEqual(riskAssestment.PlacedFor(answerB), 2.75m);
			Assert.AreEqual(riskAssestment.ToPayFor(answerA), ConvertirAPlata((100 / 120.0) * 0.25));
			Assert.AreEqual(riskAssestment.ToPayFor(answerB), ConvertirAPlata((105 / 100.0) * 0.75 + (105 / 100.0) * 1.25 + (105 / 100.0) * 0.75));

			var event1 = "Begin";
			var endGame = "End";
			game.MoveToTheNextPeriod(itIsThePresent, event1, now);
			game.NewResult(itIsThePresent, now, game.TeamA, 1, game.TeamB, 2);
			Assert.AreEqual(game.CurrentPeriod.Name, Timeline.IN_PLAY);			
			//Hacer TC de refund de una linea
			//Se debe poder consultar una cuenta vivo y muerto
			//En que momento pasar las lineas (authNumber) de vivos a BI
			//Reportes de gradeo, cuales fueron los partidos gradeados ayer
			//Ver que se hace con un regrade... No tenemos no action de un juego
			//Reprogramar y suspender juegos
			//DonBest
			//Servicios para el BO
			//Red Tag --> etiquetar players que saben mucho de un deporte, identificarlos para poder monitorearlos

			string gradedBy = "User 1";
			//106 - 93
			game.NewResult(itIsThePresent, now, game.TeamA, 106, game.TeamB, 93);
			game.MoveToTheNextPeriod(itIsThePresent, endGame, now);
			diaDeJuegos[game].Grade(true, spreadVersion2, now, game.TeamA, 106, game.TeamB, 93, gradedBy);

			diaDeJuegos[game].Confirm(itIsThePresent, now, gradedBy);

			//TODO el grade deberia devolver un resumen como si fuera la loteria, ademas hay que hacer un Send de cuanta plata para la cuenta que gano
			//Darle prioridad a la cola de los winners, otra cola para los losers
			//TODO Faltan los rest para Cashier para comunicarnos con ASI, como si fuera ASI
			//Cuando se gradea hay que pasar las cosas a BI



			// a tal juego de este torneo, agreguele como primera version estas 3 lineas. 



			// deme los deportes, retorna Basketball .. pueden existir otros.
			// basado en que existe Basketball, deme todas las ligas activas o vigentes. Debe devolver que tiene la NBA. o si existe otras.
			// 


			// Para esta NBA, La funcion debe devolver, deme de la pizara todos los juegos de un dia del torneo (2020) de la liga (nba). 
			// -deme la pizara de este dia de la liga tal y dentro de esta pizara deme las lineas de este juego, para cada row se debe regresar las 3 lineas.
			// -debe dar una lista de lineas 
			// -luego con esas lineas, cada linea tiene un X,Y 
			// -Quiero cambiar la linea (X,Y). y con version V de tal juego de tal dia para que ahora tenga otro 'apuesta'
			// y debe devolver una nueva version (v+1) de la linea. 

			//Integration.UseKafkaForAuto = true;
			Integration.RestoreToDefaultSettings();
		}

		[TestMethod]
		public void GiveMeTheMatchesForToday_Money()
		{
			string connection = "Inmemory";
			DateTime now = new DateTime(2020, 11, 02);
			Integration.ConfigurationForAutoTest("");
			ReceiverOfHistorical receiver = new ReceiverOfHistorical();
			receiver.InitHistorical(HistoricalDatabaseType.InMemory, connection);
			new Consumers().CreateConsumerForTopics(receiver);
			new ApiController().CreateConsumerForTopics();
			//Integration.UseKafka = false;
			//Integration.UseKafkaForAuto = false;

			Company company = new Company();
			var domain = company.Sales.CreateDomain(false, 1, "localhost", PaymentChannels.Agents.INSIDER);
			company.Sales.CreateStore(1, "store prueba").MakeCurrent();
			company.Sales.CurrentStore.Add(domain);
			company.Accounting = new MockAccounting();
			var player = company.GetOrCreateCustomerById("*********").Player;
			Book book = new Book(company);
			Tournaments tournaments = company.Tournaments;
			//{basketball}/nba una maquina por juego
			Tournament torneo = new NBATournament(tournaments);
			var gameNumber = 1;
			Game game = torneo.GetGameNumber(gameNumber);
			var sport = company.Tournaments.Sports.FindById(1);
			sport.CreateAndReplaceDefaultBackground("bg", "bg.jpg");
			sport.CreateAndReplaceDefaultSideBySide("sbs", "sbs.jpg");
			var betBoard = company.Betboard(torneo);
			betBoard.AcceptFrom(torneo);
			var leagues = betBoard.Leagues(Sport.BASKETBALL);
			Assert.AreEqual(1, leagues.Count);
			foreach (var l in leagues)
			{
				Assert.AreEqual(torneo.League, l);
			}
			TournamentSchedule schedule = torneo.Schedule;

			schedule.AddGameToMatchday(game, now, game.TeamA, game.TeamA);
			var diaDeJuegos = betBoard.Matchday(now);

			var questionML = betBoard.Catalog.CreateTierOneMoneyQuestion(1, "mm");

			//diaDeJuegos.AgregarElJuego(game, game.TeamA, game.TeamA);
			//Haber puesto en index houstonVsDallas un ID que viene de la UI y se la envie
			//Otra forma es que la liga agarre un ID y me lo pase a una instancia de game que sea como houstonVsDallas
			game.SetFavorite(game.TeamA);
			game = torneo.GetGameNumber(gameNumber);
			var fila1 = 0;
			int lineId = 1;
			questionML.SetParameters(-120, 105);
			var moneyVersion1 = (MoneyLine)diaDeJuegos[game][fila1].CreateLine(lineId + 1, questionML, "N/A", now);
			var columna = moneyVersion1.Index;

			var authorizationId = 1;

			var answerA = moneyVersion1.GetTeamAnswer(game.TeamA);
			var wagerMoney1 = moneyVersion1.PlaceBet(authorizationId, player, now, answerA, 0.55m);
			var riskAssestment = diaDeJuegos[game].RiskAssestment;
			Assert.AreEqual(riskAssestment.PlacedFor(answerA), 0.55m);
			Assert.AreEqual(riskAssestment.ToPayFor(answerA), ConvertirAPlata((100 / 120.0) * 0.55));

			var nowNewVersion = new DateTime(2020, 11, 02, 8, 0, 0);
			var moneyVersion2 = diaDeJuegos[game][fila1].CreateNewVersionForMoneyLine(lineId + 1, -120, 105, "N/A", nowNewVersion);

			var answerB = moneyVersion2.GetTeamAnswer(game.TeamB);
			var wagerMoney2 = moneyVersion2.PlaceBet(authorizationId, player, now, answerB, 0.85m);
			riskAssestment = diaDeJuegos[game].RiskAssestment;
			Assert.AreEqual(riskAssestment.PlacedFor(answerA), 0.55m);
			Assert.AreEqual(riskAssestment.PlacedFor(answerB), 0.85m);
			Assert.AreEqual(riskAssestment.ToPayFor(answerA), ConvertirAPlata((100 / 120.0) * 0.55));
			Assert.AreEqual(riskAssestment.ToPayFor(answerB), ConvertirAPlata((105 / 100.0) * 0.85));

			var gameboards = diaDeJuegos[game].Wagers;
			Assert.AreEqual(gameboards.NextSequence(), 3);

			RiskAssestment risk = diaDeJuegos[game].RecalculateRisk();
			riskAssestment = diaDeJuegos[game].RiskAssestment;
			Assert.AreEqual(riskAssestment.PlacedFor(answerA), 0.55m);
			Assert.AreEqual(riskAssestment.PlacedFor(answerB), 0.85m);
			Assert.AreEqual(riskAssestment.ToPayFor(answerA), ConvertirAPlata((100 / 120.0) * 0.55));
			Assert.AreEqual(riskAssestment.ToPayFor(answerB), ConvertirAPlata((105 / 100.0) * 0.85));

			//Integration.UseKafkaForAuto = true;
		}

		[TestMethod]
		public void PurchaseASpreadLine()
		{
			DateTime now = new DateTime(2020, 11, 02);
			Company company = new Company();
			CurrenciesTest.AddCurrencies();
			
			Movements.Storage = new MovementStorageMemory();
			Integration.UseKafka = false;
			QueueInMemory queue = new QueueInMemory();
			Integration.Kafka = queue;
			CashierAPI.CashierAPI.Initialize(new CashierMocks());
			CashierAPI.CashierAPI.Cashier.Release(0);

			var domain = company.Sales.CreateDomain(false, 1, "localhost", PaymentChannels.Agents.INSIDER);
			var store = company.Sales.CreateStore(1, "store prueba");
			store.MakeCurrent();
			company.Sales.CurrentStore.Add(domain);
			company.Accounting = new MockAccounting();

			bool itIsThePresent = true;
			Integration.ConfigurationForAutoTest("");
			string connection = "persistsecurityinfo=True;port=3307;Server=nodo.qa4.ncubo.com;Database=linesAlexis2;user id=root;password=*********;SslMode=none";
			ReceiverOfHistorical receiver = new ReceiverOfHistorical();
			receiver.InitHistorical(HistoricalDatabaseType.InMemory, connection);
			new Consumers().CreateConsumerForTopics(receiver);
			new ApiController().CreateConsumerForTopics();
			var sport = company.Tournaments.Sports.FindById(18);
			sport.CreateAndReplaceDefaultBackground("bg", "bg.jpg");
			sport.CreateAndReplaceDefaultSideBySide("sbs", "sbs.jpg");

			var accountNumber = "*********";
			GamesEngine.PurchaseOrders.Customer customer = company.GetOrCreateCustomerById(accountNumber);
			Movements.Storage.CreateMovementsTableIfNotExistsForThisAccountAndCurrency(accountNumber, Coinage.Coin(CODES.USD));
			Movements.Storage.CreateMovementsTableIfNotExistsForThisCurrency(CODES.USD.ToString());
			GamesEngine.Bets.Player player = customer.Player;

			//-------------------
			Tournaments tournaments = company.Tournaments;
			Tournament torneo = new NBATournament(tournaments);//TODO Tournament nos esta forzando a que los juegos de FirstGame tengan game y sucesor y esten definidos
			var gameNumber = 1;
			Game game = torneo.GetGameNumber(gameNumber);
			game.Reschedule(now);

			var betBoard = company.Betboard(torneo);
			betBoard.AcceptFrom(torneo);
			var leagues = betBoard.Leagues(Sport.BASKETBALL);

			var questionSL = betBoard.Catalog.CreateTierOneSpreadQuestion(1, "ss");

			var diaDeJuegos = betBoard.Matchday(now);
			game.SetFavorite(game.TeamA);
			game = torneo.GetGameNumber(gameNumber);
			var fila1 = 0;
			var lineId = betBoard.NextLineId();
			questionSL.SetParameters(2.0, -120, 105);
			var spreadVersion1 = (SpreadLine)diaDeJuegos[game][fila1].CreateLine(lineId + 3, questionSL, "N/A", now);
			spreadVersion1.Publish(itIsThePresent, now);
			var columna = spreadVersion1.Index;
			//------------------

			Product productSpread = new Product(company, 1);
			productSpread.Price = 1;

			ABLineActivator lineActivator = new ABLineActivator(company);
			lineActivator.Line = spreadVersion1;
			lineActivator.Team = game.TeamA;
			//lineActivator.Price = 1m;

			int orderNumber = 1;
			var betNumber = 1;
			var authorizationId = 1;

			//int authorization = CashierAPI.Settings.AuthorizatorsAsync.PurchaseAsync(
			//		HttpContext,
			//		atAddress,
			//		CryptCurrency.Factory(body.CurrencyCode, body.Amount),
			//		context.StoreId,
			//		concept,
			//		referenceNumber,
			//		body.AccountNumber,
			//		new FragmentInformation()
			//		{
			//			ItsConfigured = true,
			//			Towin = context.ToWin
			//		}).Result;

			OrderCart orderCart = company.NewOrder(customer, orderNumber, Coinage.Coin(CODES.USD), authorizationId);
			orderCart.AddSpreadLine(betNumber, spreadVersion1, game.TeamA, productSpread, 1m);
			company.PurchaseOrder(itIsThePresent, orderCart, domain, now);

			//Integration.UseKafkaForAuto = true;

			Showcase sc = diaDeJuegos[game];
			string gradedBy = "Juan";
			game.Start(itIsThePresent, now, now);
			game.NewResult(itIsThePresent, now, game.TeamA, 11, game.TeamB, 12);
			game.End(itIsThePresent, now);
			sc.Grade(itIsThePresent, spreadVersion1, now, game.TeamA, 11, game.TeamB, 12, gradedBy);
			Assert.AreEqual(sc.LinesWithPendingGradingToConfirm().Count(), 1);

			AtAddress atAddress = new AtAddress(accountNumber);
			Source source = atAddress.CreateSource(itIsThePresent, DateTime.Now, 1, Coinage.Coin(CODES.USD).Iso4217Code);
			string accountNumber1 = "Cr-09123";
			AccountWithBalance account1 = atAddress.CreateAccountIfNotExists(Coinage.Coin(CODES.USD), accountNumber1);
			string who = Users.LADYBET;
			account1.Accredit(itIsThePresent, DateTime.Now, new Dollar(5), source, who, "1", store, "Test", "10-11");
			Authorization auth = atAddress.CreateAuthorization(itIsThePresent, now, new Dollar(0.10m), 1, store, "Test", "reference", accountNumber1);
			List<string> references = new List<string>();
			references.Add("1");
			auth.CreateFragments(1, 1, (IEnumerable<string>)references, (IEnumerable<string>)references, 0.10m, 0.10m);

			int initialStatus = 0;
			auth.ChangeInitialFragmentsStatus(new int[] { 1 }, initialStatus);
			atAddress.PreparePayments(itIsThePresent, auth, new int[] { 1 }, DateTime.Now, store, "", FragmentReason.Loser);
			atAddress.ApplyChanges(itIsThePresent, who);

			//sc.Confirm(itIsThePresent, spreadVersion1, now, gradedBy); //no processors loaded
		}

		[TestMethod]
		public void PurchaseASpreadLine_DeletingExpiredWagers()
		{
			Company company = new Company();
			var domain = company.Sales.CreateDomain(false, 1, "localhost", PaymentChannels.Agents.INSIDER);
			company.Sales.CreateStore(1, "store prueba").MakeCurrent();
			company.Sales.CurrentStore.Add(domain);
			company.Accounting = new MockAccounting();
			var sport = company.Tournaments.Sports.FindById(1);
			sport.CreateAndReplaceDefaultBackground("bg", "bg.jpg");
			sport.CreateAndReplaceDefaultSideBySide("sbs", "sbs.jpg");
			bool itIsThePresent = true;
			Integration.UseKafka = false;
			Integration.UseKafkaForAuto = false;
			QueueInMemory queue = new QueueInMemory();
			Integration.Kafka = queue;
			string connection = "persistsecurityinfo=True;port=3307;Server=nodo.qa4.ncubo.com;Database=linesAlexis2;user id=root;password=*********;SslMode=none";
			ReceiverOfHistorical receiver = new ReceiverOfHistorical();
			receiver.InitHistorical(HistoricalDatabaseType.InMemory, connection);
			
			GamesEngine.PurchaseOrders.Customer customer = company.GetOrCreateCustomerById("*********");
			GamesEngine.Bets.Player player = customer.Player;
			DateTime now = new DateTime(2020, 11, 02);

			//-------------------
			Tournaments tournaments = company.Tournaments;
			Tournament torneo = new NBATournament(tournaments);//TODO Tournament nos esta forzando a que los juegos de FirstGame tengan game y sucesor y esten definidos
			var gameNumber = 1;
			Game game = torneo.GetGameNumber(gameNumber);
			game.Reschedule(now);

			var betBoard = company.Betboard(torneo);
			betBoard.AcceptFrom(torneo);
			var leagues = betBoard.Leagues(Sport.BASKETBALL);

			var questionSL = betBoard.Catalog.CreateTierOneSpreadQuestion(1, "ss");

			var diaDeJuegos = betBoard.Matchday(now);
			game.SetFavorite(game.TeamA);
			game = torneo.GetGameNumber(gameNumber);
			var fila1 = 0;
			var lineId = betBoard.NextLineId();
			questionSL.SetParameters(2.0, -120, 105);
			var spreadVersion1 = (SpreadLine)diaDeJuegos[game][fila1].CreateLine(lineId + 3, questionSL, "N/A", now);
			spreadVersion1.Publish(itIsThePresent, now);
			var columna = spreadVersion1.Index;
			//------------------

			Product productSpread = new Product(company, 1);
			productSpread.Price = 1;

			ABLineActivator lineActivator = new ABLineActivator(company);
			lineActivator.Line = spreadVersion1;
			lineActivator.Team = game.TeamA;
			//lineActivator.Price = 1m;

			int orderNumber = 1;
			var betNumber = 1;
			var authorizationId = 1;
			OrderCart orderCart = company.NewOrder(customer, orderNumber, Coinage.Coin(CODES.USD), authorizationId);
			orderCart.AddSpreadLine(betNumber, spreadVersion1, game.TeamA, productSpread, 1m);
			company.PurchaseOrder(itIsThePresent, orderCart, domain, now);

			//Integration.UseKafkaForAuto = true;

			Showcase sc = diaDeJuegos[game];
			string gradedBy = "Juan";

			game.Start(itIsThePresent, now, now);
			game.NewResult(itIsThePresent, now, game.TeamA, 11, game.TeamB, 12);
			game.End(itIsThePresent, now);
			sc.Grade(itIsThePresent, spreadVersion1, now, game.TeamA, 11, game.TeamB, 12, gradedBy);
			Assert.AreEqual(sc.LinesWithPendingGradingToConfirm().Count(), 1);
			sc.Confirm(itIsThePresent, spreadVersion1, now.AddHours(company.Settings.HoursKeepingAliveWagers + 1), gradedBy);
		}

		[TestMethod]
		public void CreateLineQuestion()
		{
			#region Test case Configuration
			string connection = "persistsecurityinfo=True;port=3306;Server=localhost;Database=lines2;user id=*********;password=*********;SslMode=none";
			GamesEngine.Settings.Integration.ConfigurationForAutoTest(connection);
			ReceiverOfHistorical receiver = new ReceiverOfHistorical();
			receiver.InitHistorical(HistoricalDatabaseType.InMemory, connection);
			new LinesBIAPI.Consumers().CreateConsumerForTopics(receiver);
			Integration.SavePreviousSettings(tempUseKafka: true, tempUseKafkaForAuto: false);
			
			DateTime now = new DateTime(2020, 11, 02);			
			//Integration.UseKafkaForAuto = false;

			Company company = new Company();
			company.Sales.CreateStore(1, "Test Store").MakeCurrent();
			company.Accounting = new MockAccounting();
			var player = company.GetOrCreateCustomerById("*********").Player;
			Tournaments tournaments = company.Tournaments;
			Tournament torneo = new NBATournament(tournaments);//TODO Tournament nos esta forzando a que los juegos de FirstGame tengan game y sucesor y esten definidos
			var gameNumber = 1;
			Game game = torneo.GetGameNumber(gameNumber);
			var sport = company.Tournaments.Sports.FindById(1);
			sport.CreateAndReplaceDefaultBackground("bg", "bg.jpg");
			sport.CreateAndReplaceDefaultSideBySide("sbs", "sbs.jpg");
			var betBoard = company.Betboard(torneo);
			betBoard.AcceptFrom(torneo);
			var leagues = betBoard.Leagues(Sport.BASKETBALL);
			Assert.AreEqual(1, leagues.Count);
			foreach (var l in leagues)
			{
				Assert.AreEqual(torneo.League, l);
			}
			TournamentSchedule schedule = torneo.Schedule;
			schedule.AddGameToMatchday(game, now, game.TeamA, game.TeamA);

			var diaDeJuegos = betBoard.Matchday(now);
			Shelve shelve = diaDeJuegos[game].InsertShelve(0);

			//Haber puesto en index houstonVsDallas un ID que viene de la UI y se la envie
			//Otra forma es que la liga agarre un ID y me lo pase a una instancia de game que sea como houstonVsDallas
			game.SetFavorite(game.TeamA);
			game = torneo.GetGameNumber(gameNumber);
			#endregion
			bool itIsThePresent = false;
			LinesCatalog catalog = betBoard.Catalog;
			catalog.CreateTierOneSpreadQuestion(1, "ss");
			catalog.CreateTierOneMoneyQuestion(2, "mm");
			catalog.CreateTierOneTotalPointsQuestion(3, "tt");

			int questionId = catalog.NextQuestionId();
			SpreadQuestion spreadQuestion = (SpreadQuestion)catalog[questionId - 3];
			spreadQuestion.SetParameters(2, -120, 105);

			var fila1 = 0;
			var lineId = betBoard.NextLineId();
			var spreadVersion1 = (SpreadLine)diaDeJuegos[game][fila1].CreateLine(lineId, spreadQuestion, "N/A", now);
			var columna = spreadVersion1.Index;

			spreadVersion1.Publish(itIsThePresent, now);
			diaDeJuegos[game].SuspendLine(itIsThePresent, lineId, now);
			diaDeJuegos[game].ResumeLine(itIsThePresent, lineId, now);

			var authorizationId = 1;
			var answerA = spreadVersion1.GetTeamAnswer(game.TeamA);
			var wagerSpread1 = spreadVersion1.PlaceBet(authorizationId, player, now, answerA, 0.25m);
			var riskAssestment = diaDeJuegos[game].RiskAssestment;
			Assert.AreEqual(riskAssestment.PlacedFor(answerA), 0.25m);
			Assert.AreEqual(riskAssestment.ToPayFor(answerA), ConvertirAPlata((100 / 120.0) * 0.25));

			MoneyQuestion moneyQuestion = (MoneyQuestion)catalog[questionId - 2];
			moneyQuestion.SetParameters(-120, 105);
			lineId = betBoard.NextLineId();
			var moneyVersion1 = (MoneyLine)diaDeJuegos[game][fila1].CreateLine(lineId, moneyQuestion, "N/A", now);
			columna = moneyVersion1.Index;

			answerA = moneyVersion1.GetTeamAnswer(game.TeamA);
			var answerB = moneyVersion1.GetTeamAnswer(game.TeamB);
			var wagerMoney1 = moneyVersion1.PlaceBet(authorizationId, player, now, answerB, 0.25m);
			riskAssestment = diaDeJuegos[game].RiskAssestment;
			Assert.AreEqual(riskAssestment.PlacedFor(answerA), 0.25m);
			Assert.AreEqual(riskAssestment.PlacedFor(answerB), 0.25m);
			Assert.AreEqual(riskAssestment.ToPayFor(answerA), ConvertirAPlata((100 / 120.0) * 0.25));
			Assert.AreEqual(riskAssestment.ToPayFor(answerB), ConvertirAPlata((105 / 100.0) * 0.25));

			TotalPointsQuestion totalPointsQuestion = (TotalPointsQuestion)catalog[questionId - 1];
			totalPointsQuestion.SetParameters(200, -120, 105);
			lineId = betBoard.NextLineId();
			var totalPointsVersion1 = (TotalPointsLine)diaDeJuegos[game][fila1].CreateLine(lineId, totalPointsQuestion, "N/A", now);
			columna = totalPointsVersion1.Index;

			var answerTotalPoints = OverUnderAnswer.UNDER;
			var wagerTotalPoints1 = totalPointsVersion1.PlaceBet(authorizationId, player, now, answerTotalPoints, 1);
			riskAssestment = diaDeJuegos[game].RiskAssestment;
			Assert.AreEqual(riskAssestment.PlacedFor(answerA), 0.25m);
			Assert.AreEqual(riskAssestment.PlacedFor(answerB), 0.25m);
			Assert.AreEqual(riskAssestment.ToPayFor(answerA), ConvertirAPlata((100 / 120.0) * 0.25));
			Assert.AreEqual(riskAssestment.ToPayFor(answerB), ConvertirAPlata((105 / 100.0) * 0.25));

			string gradedBy = "User 1";

			YesNoQuestion yesNoQuestion = (YesNoQuestion)catalog.CreateTierTwoYesNoQuestion(questionId, "p", "Va a meter el penal?");
			yesNoQuestion = (YesNoQuestion)catalog[questionId];
			questionId++;
			yesNoQuestion.SetParameters(-101, 104);
			lineId = betBoard.NextLineId();
			var yesNoVersion1 = (YesNoLine)diaDeJuegos[game][fila1].CreateLine(lineId, yesNoQuestion, "N/A", now);
			columna = yesNoVersion1.Index;

			var answerNo = YesNoAnswer.NO;
			var wagerYesNo1 = yesNoVersion1.PlaceBet(authorizationId, player, now, answerNo, 1);
			riskAssestment = diaDeJuegos[game].RiskAssestment;
			Assert.AreEqual(riskAssestment.PlacedFor(answerA), 0.25m);
			Assert.AreEqual(riskAssestment.PlacedFor(answerB), 0.25m);
			Assert.AreEqual(riskAssestment.ToPayFor(answerA), ConvertirAPlata((100 / 120.0) * 0.25));
			Assert.AreEqual(riskAssestment.ToPayFor(answerB), ConvertirAPlata((105 / 100.0) * 0.25));

			// Grade yes / no, esta trantando de pasarlo como si fuera como un ABLine y es un yesNO. No ocupa marcador o algo relacionado al fin del partido.
			// La solucion es, datos del fin del partido sea de la linea en lugar de estar en el emcabezado.
			//game.Start(false, now);
			//game.NewResult(false, game.TeamA, 10, game.TeamB, 5);
			//game.End(false, now);
			diaDeJuegos[game].Grade(true, yesNoVersion1, true, now, gradedBy);

			OverUnderQuestion overUnderQuestion = (OverUnderQuestion)catalog.CreateTierTwoOverUnderQuestion(questionId, "a", "A va a ganar el primer tiempo por mas de 1 gol?");
			overUnderQuestion = (OverUnderQuestion)catalog[questionId];
			questionId++;
			overUnderQuestion.SetParameters(1, -101, 104);
			lineId = betBoard.NextLineId();
			var overUnderVersion1 = (OverUnderLine)diaDeJuegos[game][fila1].CreateLine(lineId, overUnderQuestion, "N/A", now);
			columna = overUnderVersion1.Index;

			var answerUnder = OverUnderAnswer.UNDER;
			var wagerOverUnder1 = overUnderVersion1.PlaceBet(authorizationId, player, now, answerUnder, 1);
			riskAssestment = diaDeJuegos[game].RiskAssestment;
			Assert.AreEqual(riskAssestment.PlacedFor(answerA), 0.25m);
			Assert.AreEqual(riskAssestment.PlacedFor(answerB), 0.25m);
			Assert.AreEqual(riskAssestment.ToPayFor(answerA), ConvertirAPlata((100 / 120.0) * 0.25));
			Assert.AreEqual(riskAssestment.ToPayFor(answerB), ConvertirAPlata((105 / 100.0) * 0.25));

			diaDeJuegos[game].Grade(true, overUnderVersion1, 250, now, gradedBy);

			FixedQuestion fixedQuestion = (FixedQuestion)catalog.CreateTierTwoFixedQuestion(questionId, "c", "Quien va a meter el primer gol?");
			fixedQuestion = (FixedQuestion)catalog[questionId];
			questionId++;
			fixedQuestion.SetParameters(new List<string>() { "Juan", "Pedro", "Maria" }, new List<int>() { 101, 102, 103 });
			lineId = betBoard.NextLineId();
			var fixedVersion1 = (FixedLine)diaDeJuegos[game][fila1].CreateLine(lineId, fixedQuestion, "N/A", now);
			columna = fixedVersion1.Index;

			var answerFixed = fixedVersion1.GetAnAnswer("Maria");
			var wagerFixed1 = fixedVersion1.PlaceBet(authorizationId, player, now, answerFixed, 1);
			riskAssestment = diaDeJuegos[game].RiskAssestment;
			Assert.AreEqual(riskAssestment.PlacedFor(answerA), 0.25m);
			Assert.AreEqual(riskAssestment.PlacedFor(answerB), 0.25m);
			Assert.AreEqual(riskAssestment.ToPayFor(answerA), ConvertirAPlata((100 / 120.0) * 0.25));
			Assert.AreEqual(riskAssestment.ToPayFor(answerB), ConvertirAPlata((105 / 100.0) * 0.25));

			diaDeJuegos[game].Grade(true, fixedVersion1, "Juan", now, gradedBy);

			var event1 = "Begin";
			var endGame = "End";
			game.MoveToTheNextPeriod(itIsThePresent, event1, now);
			Assert.AreEqual(game.CurrentPeriod.Name, Timeline.IN_PLAY);
			game.NewResult(itIsThePresent, now, game.TeamA, 1, game.TeamB, 2);

			game.NewResult(itIsThePresent, now, game.TeamA, 106, game.TeamB, 93);
			game.MoveToTheNextPeriod(itIsThePresent, endGame, now);
			diaDeJuegos[game].Grade(true, spreadVersion1, now, game.TeamA, 106, game.TeamB, 93, gradedBy);
			diaDeJuegos[game].Grade(true, moneyVersion1, now, game.TeamA, 106, game.TeamB, 93, gradedBy);
			diaDeJuegos[game].Grade(true, totalPointsVersion1, 106 + 93, now, gradedBy);

			var gameboards = diaDeJuegos[game].Wagers;
			Assert.AreEqual(gameboards.NextSequence(), 7);

			RiskAssestment risk = diaDeJuegos[game].RecalculateRisk();
			riskAssestment = diaDeJuegos[game].RiskAssestment;
			Assert.AreEqual(riskAssestment.PlacedFor(answerA), 0.25m);
			Assert.AreEqual(riskAssestment.PlacedFor(answerB), 0.25m);
			Assert.AreEqual(riskAssestment.ToPayFor(answerA), ConvertirAPlata((100 / 120.0) * 0.25));
			Assert.AreEqual(riskAssestment.ToPayFor(answerB), ConvertirAPlata((105 / 100.0) * 0.25));//TODO revisar porque el risk cambia luego de gradear un ABLine

			//Integration.UseKafkaForAuto = true;
			Integration.RestoreToDefaultSettings();
		}

		[TestMethod]
		public void CreateLineReplacePlaceholders()
		{
			#region Test case Configuration
			DateTime now = new DateTime(2020, 11, 02);
			Company company = new Company();
			company.Sales.CreateStore(1, "Test Store").MakeCurrent();
			company.Accounting = new MockAccounting();
			var player = company.GetOrCreateCustomerById("*********").Player;
			Tournaments tournaments = company.Tournaments;
			Tournament torneo = new NBATournament(tournaments);//TODO Tournament nos esta forzando a que los juegos de FirstGame tengan game y sucesor y esten definidos
			var gameNumber = 1;
			Game game = torneo.GetGameNumber(gameNumber);
			var sport = company.Tournaments.Sports.FindById(18);
			sport.CreateAndReplaceDefaultBackground("bg", "bg.jpg");
			sport.CreateAndReplaceDefaultSideBySide("sbs", "sbs.jpg");
			var betBoard = company.Betboard(torneo);
			betBoard.AcceptFrom(torneo);
			var leagues = betBoard.Leagues(Sport.BASKETBALL);
			Assert.AreEqual(1, leagues.Count);
			foreach (var l in leagues)
			{
				Assert.AreEqual(torneo.League, l);
			}
			TournamentSchedule schedule = torneo.Schedule;
			schedule.AddGameToMatchday(game, now, game.TeamA, game.TeamA);

			var diaDeJuegos = betBoard.Matchday(now);
			Shelve shelve = diaDeJuegos[game].InsertShelve(0);

			//Haber puesto en index houstonVsDallas un ID que viene de la UI y se la envie
			//Otra forma es que la liga agarre un ID y me lo pase a una instancia de game que sea como houstonVsDallas
			game.SetFavorite(game.TeamA);
			game = torneo.GetGameNumber(gameNumber);
			#endregion
			LinesCatalog catalog = betBoard.Catalog;
			var questionSL = catalog.CreateTierOneSpreadQuestion(1, "ss");
			questionSL.Text = "Spread Line {Spread} value";
			catalog.CreateTierOneMoneyQuestion(2, "mm");
			catalog.CreateTierOneTotalPointsQuestion(3, "tt");

			var fila1 = 0;

			questionSL.SetParameters(2.0, -120, 105);
			var spreadVersion1 = (SpreadLine)diaDeJuegos[game][fila1].CreateLine(1, questionSL, "N/A", now);
			Assert.AreEqual(spreadVersion1.Text, "Spread Line 2 value");

			OverUnderQuestion overUnderQuestion = (OverUnderQuestion)catalog.CreateTierTwoOverUnderQuestion(4, "a", "{TeamA} va a ganar el primer tiempo por mas de 1 gol?");
			overUnderQuestion = (OverUnderQuestion)catalog[4];
			overUnderQuestion.SetParameters(1, -101, 104);
			var overUnderVersion1 = (OverUnderLine)diaDeJuegos[game][fila1].CreateLine(2, overUnderQuestion, "N/A", now);
			Assert.AreEqual(overUnderVersion1.Text, "Houston va a ganar el primer tiempo por mas de 1 gol?");

			overUnderQuestion = (OverUnderQuestion)catalog.CreateTierTwoOverUnderQuestion(5, "b", "Va a ganar el primer tiempo el {TeamA} por mas de 1 gol?");
			overUnderQuestion = (OverUnderQuestion)catalog[5];
			overUnderQuestion.SetParameters(1, -102, 105);
			overUnderVersion1 = (OverUnderLine)diaDeJuegos[game][fila1].CreateLine(3, overUnderQuestion, "N/A", now);
			Assert.AreEqual(overUnderVersion1.Text, "Va a ganar el primer tiempo el Houston por mas de 1 gol?");

			overUnderQuestion = (OverUnderQuestion)catalog.CreateTierTwoOverUnderQuestion(6, "c", "Va a ganar el primer tiempo por mas de 1 gol el {TeamA}");
			overUnderQuestion = (OverUnderQuestion)catalog[6];
			overUnderQuestion.SetParameters(1, -103, 106);
			overUnderVersion1 = (OverUnderLine)diaDeJuegos[game][fila1].CreateLine(4, overUnderQuestion, "N/A", now);
			Assert.AreEqual(overUnderVersion1.Text, "Va a ganar el primer tiempo por mas de 1 gol el Houston");

			overUnderQuestion = (OverUnderQuestion)catalog.CreateTierTwoOverUnderQuestion(7, "d", "{TeamB} Va a ganar el primer tiempo por mas de 1 gol el {TeamA}");
			overUnderQuestion = (OverUnderQuestion)catalog[7];
			overUnderQuestion.SetParameters(1, -104, 107);
			overUnderVersion1 = (OverUnderLine)diaDeJuegos[game][fila1].CreateLine(5, overUnderQuestion, "N/A", now);
			Assert.AreEqual(overUnderVersion1.Text, "Detroit Va a ganar el primer tiempo por mas de 1 gol el Houston");
		}

		[TestMethod]
		public void PlayerTagsTest()
		{
			DateTime now = new DateTime(2020, 11, 02);
			//Integration.UseKafka = false;
			//Integration.UseKafkaForAuto = false;
			Integration.ConfigurationForAutoTest("");
			bool itIsThePresent = true;

			Company company = new Company();
			var domain = company.Sales.CreateDomain(false, 1, "localhost", PaymentChannels.Agents.INSIDER);
			company.Sales.CreateStore(1, "store prueba").MakeCurrent();
			company.Sales.CurrentStore.Add(domain);
			company.Accounting = new MockAccounting();
			Book book = company.Book;
			GamesEngine.PurchaseOrders.Customer customer = company.GetOrCreateCustomerById("*********");
			new LinesAPI.Consumers().CreateConsumerForTopics();
			var sport = company.Tournaments.Sports.FindById(1);
			sport.CreateAndReplaceDefaultBackground("bg", "bg.jpg");
			sport.CreateAndReplaceDefaultSideBySide("sbs", "sbs.jpg");
			Tag tag = book.RiskTags.CreateTag("Red", "http://tags/red.jpg");
			var player = company.GetOrCreateCustomerById("*********").Player;
			book.RiskTags.Tag(player, tag);
			Assert.AreEqual(Enumerable.Count(book.RiskTags.Tags(player)), 1);

			Tournaments tournaments = company.Tournaments;
			Tournament torneo = new NBATournament(tournaments);//TODO Tournament nos esta forzando a que los juegos de FirstGame tengan game y sucesor y esten definidos
			var gameNumber = 1;
			Game game = torneo.GetGameNumber(gameNumber);
			game.Reschedule(now);

			var betBoard = company.Betboard(torneo);
			betBoard.AcceptFrom(torneo);
			var leagues = betBoard.Leagues(Sport.BASKETBALL);

			var questionSL = betBoard.Catalog.CreateTierOneSpreadQuestion(1, "ss");

			var diaDeJuegos = betBoard.Matchday(now);
			game.SetFavorite(game.TeamA);
			game = torneo.GetGameNumber(gameNumber);
			var fila1 = 0;
			var lineId = betBoard.NextLineId();
			questionSL.SetParameters(2.0, -120, 105);
			var spreadVersion1 = (SpreadLine)diaDeJuegos[game][fila1].CreateLine(lineId, questionSL, "N/A", now);
			spreadVersion1.Publish(itIsThePresent, now);
			var columna = spreadVersion1.Index;

			Product productSpread = new Product(company, 1);
			productSpread.Price = 1;

			ABLineActivator lineActivator = new ABLineActivator(company);
			lineActivator.Line = spreadVersion1;
			lineActivator.Team = game.TeamA;

			int orderNumber = 1;
			var betNumber = 1;
			var authorizationId = 1;
			OrderCart orderCart = company.NewOrder(customer, orderNumber, Coinage.Coin(CODES.USD), authorizationId);
			orderCart.AddSpreadLine(betNumber, spreadVersion1, game.TeamA, productSpread, 1m);
			company.PurchaseOrder(itIsThePresent, orderCart, domain, now);

			company.Book.RiskTags.RecordActivity((Wager)player.Gameboards().First(), now);

			book.RiskTags.Untag(player, tag);
			Assert.AreEqual(Enumerable.Count(book.RiskTags.Tags(player)), 0);

			//Integration.UseKafkaForAuto = true;
		}

		[TestMethod]
		public void MarkAsNoAction_SpreadLineTest()
		{
			string connection = "Inmemory";
			GamesEngine.Settings.Integration.ConfigurationForAutoTest(connection);
			
			Company company = new Company();
			var domain = company.Sales.CreateDomain(false, 1, "localhost", PaymentChannels.Agents.INSIDER);
			company.Sales.CreateStore(1, "store prueba").MakeCurrent();
			company.Sales.CurrentStore.Add(domain);
			company.Accounting = new MockAccounting();
			var sport = company.Tournaments.Sports.FindById(1);
			sport.CreateAndReplaceDefaultBackground("bg", "bg.jpg");
			sport.CreateAndReplaceDefaultSideBySide("sbs", "sbs.jpg");
			bool itIsThePresent = true;
			//Integration.UseKafka = false;
			//Integration.UseKafkaForAuto = false;
			Integration.ConfigurationForAutoTest("");
			ReceiverOfHistorical receiver = new ReceiverOfHistorical();
			receiver.InitHistorical(HistoricalDatabaseType.InMemory, connection);
			new Consumers().CreateConsumerForTopics(receiver);
			new ApiController().CreateConsumerForTopics();

			GamesEngine.PurchaseOrders.Customer customer = company.GetOrCreateCustomerById("*********");
			GamesEngine.Bets.Player player = customer.Player;
			DateTime now = new DateTime(2020, 11, 02);

			//-------------------
			Tournaments tournaments = company.Tournaments;
			Tournament torneo = new NBATournament(tournaments);//TODO Tournament nos esta forzando a que los juegos de FirstGame tengan game y sucesor y esten definidos
			var gameNumber = 1;
			Game game = torneo.GetGameNumber(gameNumber);
			game.Reschedule(now);

			var betBoard = company.Betboard(torneo);
			betBoard.AcceptFrom(torneo);
			var leagues = betBoard.Leagues(Sport.BASKETBALL);

			var questionSL = betBoard.Catalog.CreateTierOneSpreadQuestion(1, "ss");

			var diaDeJuegos = betBoard.Matchday(now);
			game.SetFavorite(game.TeamA);
			game = torneo.GetGameNumber(gameNumber);
			var fila1 = 0;
			var lineId = betBoard.NextLineId();
			questionSL.SetParameters(2.0, -120, 105);
			var spreadVersion1 = (SpreadLine)diaDeJuegos[game][fila1].CreateLine(lineId + 3, questionSL, "N/A", now);
			spreadVersion1.Publish(itIsThePresent, now);
			var columna = spreadVersion1.Index;
			//------------------

			Product productSpread = new Product(company, 1);
			productSpread.Price = 1;

			ABLineActivator lineActivator = new ABLineActivator(company);
			lineActivator.Line = spreadVersion1;
			lineActivator.Team = game.TeamA;

			int orderNumber = 1;
			var betNumber = 1;
			var authorizationId = 1;
			OrderCart orderCart = company.NewOrder(customer, orderNumber, Coinage.Coin(CODES.USD), authorizationId);
			orderCart.AddSpreadLine(betNumber, spreadVersion1, game.TeamA, productSpread, 1m);
			company.PurchaseOrder(itIsThePresent, orderCart, domain, now);

			game.Start(false, now, now);
			game.NewResult(false, now, game.TeamA, 1, game.TeamB, 0);
			spreadVersion1.SetRealAnswer(game.TeamA, 1, game.TeamB, 0);			
			game.End(false, now);

			string gradedBy = "Juan";
			diaDeJuegos[game].SetNoAction(true, spreadVersion1, now, gradedBy);

			var gameboards = diaDeJuegos[game].Wagers;
			Assert.AreEqual(gameboards.NextSequence(), 2);

			RiskAssestment risk = diaDeJuegos[game].RecalculateRisk();
			var riskAssestment = diaDeJuegos[game].RiskAssestment;
			var answerA = spreadVersion1.GetTeamAnswer(game.TeamA);
			var answerB = spreadVersion1.GetTeamAnswer(game.TeamB);
			Assert.AreEqual(riskAssestment.PlacedFor(answerA), 1m);
			Assert.AreEqual(riskAssestment.PlacedFor(answerB), 0m);
			Assert.AreEqual(riskAssestment.ToPayFor(answerA), ConvertirAPlata((100 / 120.0) * 1));
			Assert.AreEqual(riskAssestment.ToPayFor(answerB), ConvertirAPlata((105 / 100.0) * 0));

			//Integration.UseKafkaForAuto = true;
		}

		[TestMethod]
		public void AvailabilityTimeTest()
		{
			Company company = new Company();
			var domain = company.Sales.CreateDomain(false, 1, "localhost", PaymentChannels.Agents.INSIDER);
			company.Sales.CreateStore(1, "Store Test").MakeCurrent();
			company.Sales.CurrentStore.Add(domain);
			company.Accounting = new MockAccounting();
			var sport = company.Tournaments.Sports.FindById(1);
			sport.CreateAndReplaceDefaultBackground("bg", "bg.jpg");
			sport.CreateAndReplaceDefaultSideBySide("sbs", "sbs.jpg");
			sport = company.Tournaments.Sports.FindById(18);
			sport.CreateAndReplaceDefaultBackground("bg", "bg.jpg");
			sport.CreateAndReplaceDefaultSideBySide("sbs", "sbs.jpg");
			bool itIsThePresent = true;
			Integration.UseKafka = false;
			Integration.UseKafkaForAuto = false;
			Integration.ConfigurationForAutoTest("");

			GamesEngine.PurchaseOrders.Customer customer = company.GetOrCreateCustomerById("*********");
			GamesEngine.Bets.Player player = customer.Player;
			DateTime now = new DateTime(2020, 11, 02);

			//-------------------
			Tournaments tournaments = company.Tournaments;
			Tournament nba = new NBATournament(tournaments);
			var gameNumber = 1;
			Game game = nba.GetGameNumber(gameNumber);
			game.Reschedule(now);

			var betBoard = company.Betboard(nba);
			betBoard.AcceptFrom(nba);
			var leagues = betBoard.Leagues(Sport.BASKETBALL);

			var questionSL = betBoard.Catalog.CreateTierOneSpreadQuestion(1, "ss");
			questionSL.TimeKiller(AvailabilityTime.Milestone.BeforeGameStarts, 60*5);

			var diaDeJuegos = betBoard.Matchday(now);
			game.SetFavorite(game.TeamA);
			game = nba.GetGameNumber(gameNumber);
			var fila1 = 0;
			var lineId = betBoard.NextLineId();
			questionSL.SetParameters(2.0, -120, 105);
			var spreadVersion1 = (SpreadLine)diaDeJuegos[game][fila1].CreateLine(lineId + 3, questionSL, "N/A", now);
			var columna = spreadVersion1.Index;
			//------------------
			int remainingTime = spreadVersion1.DueDate.RemainingTimeInSeconds(now.AddHours(-1));
			Assert.AreEqual(60*60 - 5*60, remainingTime);

			spreadVersion1.UnlimitedDueDate();
			remainingTime = spreadVersion1.DueDate.RemainingTimeInSeconds(now.AddHours(-1));
			Assert.AreEqual(-1, remainingTime);

			spreadVersion1.DueDate = game.GetAvailabilityTime(AvailabilityTime.Milestone.AfterLinePublishing, spreadVersion1, 300);
			remainingTime = spreadVersion1.DueDate.RemainingTimeInSeconds(now.AddHours(-1));
			Assert.AreEqual(0, remainingTime);

			spreadVersion1.Publish(itIsThePresent, now);
			remainingTime = spreadVersion1.DueDate.RemainingTimeInSeconds(now.AddHours(-1));
			Assert.AreEqual(3900, remainingTime);
		}

		[TestMethod]
		public void PublishTimeTest()
		{
			Company company = new Company();
			var domain = company.Sales.CreateDomain(false, 1, "localhost", PaymentChannels.Agents.INSIDER);
			company.Sales.CreateStore(1, "Store Test").MakeCurrent();
			company.Sales.CurrentStore.Add(domain);
			company.Accounting = new MockAccounting();
			var sport = company.Tournaments.Sports.FindById(1);
			sport.CreateAndReplaceDefaultBackground("bg", "bg.jpg");
			sport.CreateAndReplaceDefaultSideBySide("sbs", "sbs.jpg");
			bool itIsThePresent = true;
			Integration.UseKafka = false;
			Integration.UseKafkaForAuto = false;
			Integration.ConfigurationForAutoTest("");

			GamesEngine.PurchaseOrders.Customer customer = company.GetOrCreateCustomerById("*********");
			GamesEngine.Bets.Player player = customer.Player;
			DateTime now = new DateTime(2020, 11, 02);

			//-------------------
			Tournaments tournaments = company.Tournaments;
			Tournament nba = new NBATournament(tournaments);
			var gameNumber = 1;
			Game game = nba.GetGameNumber(gameNumber);
			game.Reschedule(now);

			var betBoard = company.Betboard(nba);
			betBoard.AcceptFrom(nba);
			var leagues = betBoard.Leagues(Sport.BASKETBALL);

			var questionSL = betBoard.Catalog.CreateTierOneSpreadQuestion(1, "ss");
			questionSL.PublishTime(AvailabilityTime.Milestone.BeforeGameStarts, 60 * 5);

			var diaDeJuegos = betBoard.Matchday(now);
			game.SetFavorite(game.TeamA);
			game = nba.GetGameNumber(gameNumber);
			var fila1 = 0;
			var lineId = betBoard.NextLineId();
			questionSL.SetParameters(2.0, -120, 105);
			var spreadVersion1 = (SpreadLine)diaDeJuegos[game][fila1].CreateLine(lineId + 3, questionSL, "N/A", now);
			var columna = spreadVersion1.Index;
			//------------------
			int remainingTime = spreadVersion1.PublishDate.RemainingTimeInSeconds(now.AddHours(-1));
			Assert.AreEqual(60 * 60 - 5 * 60, remainingTime);

			spreadVersion1.UndefinedPublishDate();
			remainingTime = spreadVersion1.PublishDate.RemainingTimeInSeconds(now.AddHours(-1));
			Assert.AreEqual(-1, remainingTime);

			spreadVersion1.PublishDate = game.GetAvailabilityTime(AvailabilityTime.Milestone.AfterGameStarts, 300);
			remainingTime = spreadVersion1.PublishDate.RemainingTimeInSeconds(now.AddHours(-1));
			Assert.AreEqual(3900, remainingTime);

			spreadVersion1.Publish(itIsThePresent, now);
			remainingTime = spreadVersion1.PublishDate.RemainingTimeInSeconds(now.AddHours(-1));
			Assert.AreEqual(0, remainingTime);
		}


		[TestMethod]
		public void ResumeSuspendedLineAfterChangeOdds_Bug5926()
		{
			Company company = new Company();
			var domain = company.Sales.CreateDomain(false, 1, "localhost", PaymentChannels.Agents.INSIDER);
			company.Sales.CreateStore(1, "Store Test").MakeCurrent();
			company.Sales.CurrentStore.Add(domain);
			company.Accounting = new MockAccounting();
			var sport = company.Tournaments.Sports.FindById(1);
			sport.CreateAndReplaceDefaultBackground("bg", "bg.jpg");
			sport.CreateAndReplaceDefaultSideBySide("sbs", "sbs.jpg");
			bool itIsThePresent = true;
			Integration.UseKafka = false;
			Integration.UseKafkaForAuto = false;
			Integration.ConfigurationForAutoTest("");

			GamesEngine.PurchaseOrders.Customer customer = company.GetOrCreateCustomerById("*********");
			GamesEngine.Bets.Player player = customer.Player;
			DateTime now = new DateTime(2020, 11, 02);

			//-------------------
			Tournaments tournaments = company.Tournaments;
			Tournament nba = new NBATournament(tournaments);
			var gameNumber = 1;
			Game game = nba.GetGameNumber(gameNumber);
			game.Reschedule(now);

			var betBoard = company.Betboard(nba);
			betBoard.AcceptFrom(nba);
			var leagues = betBoard.Leagues(Sport.BASKETBALL);

			var questionSL = betBoard.Catalog.CreateTierOneSpreadQuestion(1, "ss");

			var diaDeJuegos = betBoard.Matchday(now);
			game.SetFavorite(game.TeamA);
			game = nba.GetGameNumber(gameNumber);
			var fila1 = 0;
			var lineId = betBoard.NextLineId();
			questionSL.SetParameters(2.0, -120, 105);
			var spreadVersion1 = (SpreadLine)diaDeJuegos[game][fila1].CreateLine(lineId + 3, questionSL, "N/A", now);
			var columna = spreadVersion1.Index;
			spreadVersion1.Publish(itIsThePresent, now);
			spreadVersion1.Suspend(itIsThePresent, now);

			var spreadVersion2 = spreadVersion1.NewVersion(-125, 101, "N/A", now.AddSeconds(1));
			spreadVersion2.Resume(itIsThePresent, now);
			Assert.AreEqual(true, spreadVersion2.IsPublished);
		}

		[TestMethod]
		public void GradeSpreadLineAfterAnUpdateAndHavingPurchases_Bug5971()
		{
			string connection = "Inmemory";
			GamesEngine.Settings.Integration.ConfigurationForAutoTest(connection);

			Company company = new Company();
			var domain = company.Sales.CreateDomain(false, 1, "localhost", PaymentChannels.Agents.INSIDER);
			company.Sales.CreateStore(1, "store prueba").MakeCurrent();
			company.Sales.CurrentStore.Add(domain);
			company.Accounting = new MockAccounting();
			var sport = company.Tournaments.Sports.FindById(1);
			sport.CreateAndReplaceDefaultBackground("bg", "bg.jpg");
			sport.CreateAndReplaceDefaultSideBySide("sbs", "sbs.jpg");
			bool itIsThePresent = true;
			Integration.UseKafka = false;
			Integration.UseKafkaForAuto = false;
			Integration.ConfigurationForAutoTest("");
			ReceiverOfHistorical receiver = new ReceiverOfHistorical();
			receiver.InitHistorical(HistoricalDatabaseType.InMemory, connection);
			new Consumers().CreateConsumerForTopics(receiver);
			new ApiController().CreateConsumerForTopics();

			GamesEngine.PurchaseOrders.Customer customer = company.GetOrCreateCustomerById("*********");
			GamesEngine.Bets.Player player = customer.Player;
			DateTime now = new DateTime(2020, 11, 02);

			//-------------------
			Tournaments tournaments = company.Tournaments;
			Tournament nba = new NBATournament(tournaments);//TODO Tournament nos esta forzando a que los juegos de FirstGame tengan game y sucesor y esten definidos
			var gameNumber = 1;
			Game game = nba.GetGameNumber(gameNumber);
			game.Reschedule(now);

			var betBoard = company.Betboard(nba);
			betBoard.AcceptFrom(nba);
			var leagues = betBoard.Leagues(Sport.BASKETBALL);

			var questionSL = betBoard.Catalog.CreateTierOneSpreadQuestion(1, "ss");

			var diaDeJuegos = betBoard.Matchday(now);
			game.SetFavorite(game.TeamA);
			game = nba.GetGameNumber(gameNumber);
			var fila1 = 0;
			var lineId = betBoard.NextLineId();
			questionSL.SetParameters(2.0, -120, 105);
			var spreadVersion1 = (SpreadLine)diaDeJuegos[game][fila1].CreateLine(lineId + 3, questionSL, "N/A", now);
			spreadVersion1.Publish(itIsThePresent, now);
			var columna = spreadVersion1.Index;
			//------------------

			Product productSpread = new Product(company, 1);
			productSpread.Price = 1;

			ABLineActivator lineActivator = new ABLineActivator(company);
			lineActivator.Line = spreadVersion1;
			lineActivator.Team = game.TeamA;

			int orderNumber = 1;
			var betNumber = 1;
			var authorizationId = 1;
			OrderCart orderCart = company.NewOrder(customer, orderNumber, Coinage.Coin(CODES.USD), authorizationId);
			orderCart.AddSpreadLine(betNumber, spreadVersion1, game.TeamA, productSpread, 1m);
			company.PurchaseOrder(itIsThePresent, orderCart, domain, now);

			var spreadVersion2 = spreadVersion1.NewVersion(-125, 101, "N/A", now.AddSeconds(1));
			//spreadVersion2.Publish(itIsThePresent, now);

			lineActivator = new ABLineActivator(company);
			lineActivator.Line = spreadVersion2;
			lineActivator.Team = game.TeamA;

			orderNumber = 2;
			betNumber = 2;
			authorizationId = 2;
			orderCart = company.NewOrder(customer, orderNumber, Coinage.Coin(CODES.USD), authorizationId);
			orderCart.AddSpreadLine(betNumber, spreadVersion2, game.TeamA, productSpread, 1m);
			company.PurchaseOrder(itIsThePresent, orderCart, domain, now);

			game.Start(false, now, now);
			game.NewResult(false, now, game.TeamA, 11, game.TeamB, 1);
			//spreadVersion1.SetRealAnswer(game.TeamA, 1, game.TeamB, 0);
			game.End(false, now);

			string gradedBy = "Juan";
			diaDeJuegos[game].Grade(itIsThePresent, spreadVersion2, now, game.TeamA, 11, game.TeamB, 1, gradedBy);

			var gameboards = diaDeJuegos[game].Wagers;
			Assert.AreEqual(gameboards.NextSequence(), 3);

			RiskAssestment risk = diaDeJuegos[game].RecalculateRisk();
			var riskAssestment = diaDeJuegos[game].RiskAssestment;
			var answerA = spreadVersion1.GetTeamAnswer(game.TeamA);
			var answerB = spreadVersion1.GetTeamAnswer(game.TeamB);
			Assert.AreEqual(riskAssestment.PlacedFor(answerA), 2m);
			Assert.AreEqual(riskAssestment.PlacedFor(answerB), 0m);
		}

		[TestMethod]
		public void GradeSpreadLineGradeRegradeGradeWithPurchase_Bug5978()
		{
			string connection = "Inmemory";
			GamesEngine.Settings.Integration.ConfigurationForAutoTest(connection);

			Company company = new Company();
			var domain = company.Sales.CreateDomain(false, 1, "localhost", PaymentChannels.Agents.INSIDER);
			company.Sales.CreateStore(1, "store prueba").MakeCurrent();
			company.Sales.CurrentStore.Add(domain);
			company.Accounting = new MockAccounting();
			var sport = company.Tournaments.Sports.FindById(1);
			sport.CreateAndReplaceDefaultBackground("bg", "bg.jpg");
			sport.CreateAndReplaceDefaultSideBySide("sbs", "sbs.jpg");
			bool itIsThePresent = true;
			Integration.UseKafka = false;
			Integration.UseKafkaForAuto = false;
			Integration.ConfigurationForAutoTest("");
			ReceiverOfHistorical receiver = new ReceiverOfHistorical();
			receiver.InitHistorical(HistoricalDatabaseType.InMemory, connection);
			new Consumers().CreateConsumerForTopics(receiver);
			new ApiController().CreateConsumerForTopics();

			GamesEngine.PurchaseOrders.Customer customer = company.GetOrCreateCustomerById("*********");
			GamesEngine.Bets.Player player = customer.Player;
			DateTime now = new DateTime(2020, 11, 02);

			//-------------------
			Tournaments tournaments = company.Tournaments;
			Tournament nba = new NBATournament(tournaments);//TODO Tournament nos esta forzando a que los juegos de FirstGame tengan game y sucesor y esten definidos
			var gameNumber = 1;
			Game game = nba.GetGameNumber(gameNumber);
			game.Reschedule(now);

			var betBoard = company.Betboard(nba);
			betBoard.AcceptFrom(nba);
			var leagues = betBoard.Leagues(Sport.BASKETBALL);

			var questionSL = betBoard.Catalog.CreateTierOneSpreadQuestion(1, "ss");

			var diaDeJuegos = betBoard.Matchday(now);
			game.SetFavorite(game.TeamA);
			game = nba.GetGameNumber(gameNumber);
			var fila1 = 0;
			var lineId = betBoard.NextLineId();
			questionSL.SetParameters(2.0, -120, 105);
			var spreadVersion1 = (SpreadLine)diaDeJuegos[game][fila1].CreateLine(lineId + 3, questionSL, "N/A", now);
			spreadVersion1.Publish(itIsThePresent, now);
			var columna = spreadVersion1.Index;
			//------------------

			Product productSpread = new Product(company, 1);
			productSpread.Price = 1;

			ABLineActivator lineActivator = new ABLineActivator(company);
			lineActivator.Line = spreadVersion1;
			lineActivator.Team = game.TeamA;

			int orderNumber = 1;
			var betNumber = 1;
			var authorizationId = 1;
			OrderCart orderCart = company.NewOrder(customer, orderNumber, Coinage.Coin(CODES.USD), authorizationId);
			orderCart.AddSpreadLine(betNumber, spreadVersion1, game.TeamA, productSpread, 1m);
			company.PurchaseOrder(itIsThePresent, orderCart, domain, now);

			game.Start(false, now, now);
			game.NewResult(false, now, game.TeamA, 11, game.TeamB, 1);
			game.NewResult(false, now, game.TeamA, 12, game.TeamB, 10);
			game.End(false, now);

			string gradedBy = "Juan";
			diaDeJuegos[game].Grade(itIsThePresent, spreadVersion1, now, game.TeamA, 11, game.TeamB, 1, gradedBy);
			diaDeJuegos[game].Regrade(itIsThePresent, spreadVersion1, now.AddSeconds(1), gradedBy);
			diaDeJuegos[game].Grade(itIsThePresent, spreadVersion1, now, game.TeamA, 12, game.TeamB, 10, gradedBy);

			var gameboards = diaDeJuegos[game].Wagers;
			Assert.AreEqual(gameboards.NextSequence(), 2);

			RiskAssestment risk = diaDeJuegos[game].RecalculateRisk();
			var riskAssestment = diaDeJuegos[game].RiskAssestment;
			var answerA = spreadVersion1.GetTeamAnswer(game.TeamA);
			var answerB = spreadVersion1.GetTeamAnswer(game.TeamB);
			Assert.AreEqual(riskAssestment.PlacedFor(answerA), 1m);
			Assert.AreEqual(riskAssestment.PlacedFor(answerB), 0m);
		}

		[TestMethod]
		public void GradeSpreadLineGradeRegradeGradeWithPurchase_Bug5979()
		{
			string connection = "Inmemory";
			GamesEngine.Settings.Integration.ConfigurationForAutoTest(connection);

			Company company = new Company();
			var domain = company.Sales.CreateDomain(false, 1, "localhost", PaymentChannels.Agents.INSIDER);
			company.Sales.CreateStore(1, "store prueba").MakeCurrent();
			company.Sales.CurrentStore.Add(domain);
			company.Accounting = new MockAccounting();
			var sport = company.Tournaments.Sports.FindById(1);
			sport.CreateAndReplaceDefaultBackground("bg", "bg.jpg");
			sport.CreateAndReplaceDefaultSideBySide("sbs", "sbs.jpg");
			bool itIsThePresent = true;
			Integration.UseKafka = false;
			Integration.UseKafkaForAuto = false;
			Integration.ConfigurationForAutoTest("");
			ReceiverOfHistorical receiver = new ReceiverOfHistorical();
			receiver.InitHistorical(HistoricalDatabaseType.InMemory, connection);
			new Consumers().CreateConsumerForTopics(receiver);
			new ApiController().CreateConsumerForTopics();

			GamesEngine.PurchaseOrders.Customer customer = company.GetOrCreateCustomerById("*********");
			GamesEngine.Bets.Player player = customer.Player;
			DateTime now = new DateTime(2020, 11, 02);

			//-------------------
			Tournaments tournaments = company.Tournaments;
			Tournament nba = new NBATournament(tournaments);//TODO Tournament nos esta forzando a que los juegos de FirstGame tengan game y sucesor y esten definidos
			var gameNumber = 1;
			Game game = nba.GetGameNumber(gameNumber);
			game.Reschedule(now);

			var betBoard = company.Betboard(nba);
			betBoard.AcceptFrom(nba);
			var leagues = betBoard.Leagues(Sport.BASKETBALL);

			var questionSL = betBoard.Catalog.CreateTierOneSpreadQuestion(1, "ss");

			var diaDeJuegos = betBoard.Matchday(now);
			game.SetFavorite(game.TeamA);
			game = nba.GetGameNumber(gameNumber);
			var fila1 = 0;
			var lineId = betBoard.NextLineId();
			questionSL.SetParameters(4.0, -120, 105);
			var spreadVersion1 = (SpreadLine)diaDeJuegos[game][fila1].CreateLine(lineId + 3, questionSL, "N/A", now);
			spreadVersion1.Publish(itIsThePresent, now);
			var columna = spreadVersion1.Index;
			//------------------

			Product productSpread = new Product(company, 1);
			productSpread.Price = 1;

			ABLineActivator lineActivator = new ABLineActivator(company);
			lineActivator.Line = spreadVersion1;
			lineActivator.Team = game.TeamA;

			int orderNumber = 1;
			var betNumber = 1;
			var authorizationId = 1;
			OrderCart orderCart = company.NewOrder(customer, orderNumber, Coinage.Coin(CODES.USD), authorizationId);
			orderCart.AddSpreadLine(betNumber, spreadVersion1, game.TeamA, productSpread, 1m);
			company.PurchaseOrder(itIsThePresent, orderCart, domain, now);

			game.Start(false, now, now);
			game.NewResult(false, now, game.TeamA, 9, game.TeamB, 5);
			game.NewResult(false, now, game.TeamA, 10, game.TeamB, 6);
			game.End(false, now);

			string gradedBy = "Juan";
			diaDeJuegos[game].Grade(itIsThePresent, spreadVersion1, now, game.TeamA, 9, game.TeamB, 5, gradedBy);
			diaDeJuegos[game].Regrade(itIsThePresent, spreadVersion1, now.AddSeconds(1), gradedBy);
			diaDeJuegos[game].Grade(itIsThePresent, spreadVersion1, now, game.TeamA, 10, game.TeamB, 6, gradedBy);

			var gameboards = diaDeJuegos[game].Wagers;
			Assert.AreEqual(gameboards.NextSequence(), 2);

			RiskAssestment risk = diaDeJuegos[game].RecalculateRisk();
			var riskAssestment = diaDeJuegos[game].RiskAssestment;
			var answerA = spreadVersion1.GetTeamAnswer(game.TeamA);
			var answerB = spreadVersion1.GetTeamAnswer(game.TeamB);
			Assert.AreEqual(riskAssestment.PlacedFor(answerA), 1m);
			Assert.AreEqual(riskAssestment.PlacedFor(answerB), 0m);
		}

		[TestMethod]
		public void GradeFixedLineGradeRegradeGradeWithPurchase_Bug5970()
		{
			string connection = "Inmemory";
			GamesEngine.Settings.Integration.ConfigurationForAutoTest(connection);

			Company company = new Company();
			var domain = company.Sales.CreateDomain(false, 1, "localhost", PaymentChannels.Agents.INSIDER);
			company.Sales.CreateStore(1, "store prueba").MakeCurrent();
			company.Sales.CurrentStore.Add(domain);
			company.Accounting = new MockAccounting();
			var sport = company.Tournaments.Sports.FindById(1);
			sport.CreateAndReplaceDefaultBackground("bg", "bg.jpg");
			sport.CreateAndReplaceDefaultSideBySide("sbs", "sbs.jpg");
			bool itIsThePresent = true;
			Integration.UseKafka = false;
			Integration.UseKafkaForAuto = false;
			Integration.ConfigurationForAutoTest("");
			ReceiverOfHistorical receiver = new ReceiverOfHistorical();
			receiver.InitHistorical(HistoricalDatabaseType.InMemory, connection);
			new Consumers().CreateConsumerForTopics(receiver);
			new ApiController().CreateConsumerForTopics();
			receiver.InitHistorical(HistoricalDatabaseType.InMemory, connection);

			GamesEngine.PurchaseOrders.Customer customer = company.GetOrCreateCustomerById("*********");
			GamesEngine.Bets.Player player = customer.Player;
			DateTime now = new DateTime(2020, 11, 02);

			//-------------------
			Tournaments tournaments = company.Tournaments;
			Tournament nba = new NBATournament(tournaments);//TODO Tournament nos esta forzando a que los juegos de FirstGame tengan game y sucesor y esten definidos
			var gameNumber = 1;
			Game game = nba.GetGameNumber(gameNumber);
			game.Reschedule(now);

			var betBoard = company.Betboard(nba);
			betBoard.AcceptFrom(nba);
			var leagues = betBoard.Leagues(Sport.BASKETBALL);

			var diaDeJuegos = betBoard.Matchday(now);
			game.SetFavorite(game.TeamA);
			game = nba.GetGameNumber(gameNumber);
			var fila1 = 0;
			var lineId = betBoard.NextLineId();
			string gradedBy = "Juan";

			var questionFixed = betBoard.Catalog.CreateTierTwoFixedQuestion(7, "FC", "Quien va a ganar el partido?");
			questionFixed.AllowPublished(game.CurrentPeriod);
			questionFixed.SetParameters(new List<string> { "azul", "blanco", "rojo" }, new List<int> { 120, 115, -105 });
			var fixeds = (FixedLine)diaDeJuegos[game][fila1].CreateLine(lineId + 7, questionFixed, gradedBy, now);
			fixeds.Publish(itIsThePresent, now);

			Product productFixed = new Product(company, 1);
			productFixed.Price = 1;

			FixedLineActivator lineActivator = new FixedLineActivator(company);
			lineActivator.Line = fixeds;

			int orderNumber = 1;
			var betNumber = 1;
			var authorizationId = 1;
			OrderCart orderCart = company.NewOrder(customer, orderNumber, Coinage.Coin(CODES.USD), authorizationId);
			orderCart.AddFixedLine(betNumber, fixeds, "rojo", productFixed, 1m);
			company.PurchaseOrder(itIsThePresent, orderCart, domain, now);

			game.Start(false, now, now);
			game.NewResult(false, now, game.TeamA, 9, game.TeamB, 5);
			game.NewResult(false, now, game.TeamA, 10, game.TeamB, 6);
			game.End(false, now);

			diaDeJuegos[game].Grade(itIsThePresent, fixeds, "rojo", now, gradedBy);
			diaDeJuegos[game].Regrade(itIsThePresent, fixeds, now.AddSeconds(1), gradedBy);
			diaDeJuegos[game].Grade(itIsThePresent, fixeds, "rojo", now, gradedBy);

			var gameboards = diaDeJuegos[game].Wagers;
			Assert.AreEqual(gameboards.NextSequence(), 2);

			RiskAssestment risk = diaDeJuegos[game].RecalculateRisk();
			var riskAssestment = diaDeJuegos[game].RiskAssestment;
			var answer = fixeds.GetAnAnswer("rojo");
			Assert.AreEqual(riskAssestment.PlacedFor(answer), 1m);
		}

		[TestMethod]
		public void GradeFixedLineUpdateToANewVersionAndPurchase_Bug6175()
		{
			string connection = "Inmemory";
			GamesEngine.Settings.Integration.ConfigurationForAutoTest(connection);

			Company company = new Company();
			var domain = company.Sales.CreateDomain(false, 1, "localhost", PaymentChannels.Agents.INSIDER);
			company.Sales.CreateStore(1, "store prueba").MakeCurrent();
			company.Sales.CurrentStore.Add(domain);
			company.Accounting = new MockAccounting();
			var sport = company.Tournaments.Sports.FindById(1);
			sport.CreateAndReplaceDefaultBackground("bg", "bg.jpg");
			sport.CreateAndReplaceDefaultSideBySide("sbs", "sbs.jpg");
			bool itIsThePresent = true;
			Integration.UseKafka = false;
			Integration.UseKafkaForAuto = false;
			Integration.ConfigurationForAutoTest("");
			ReceiverOfHistorical receiver = new ReceiverOfHistorical();
			receiver.InitHistorical(HistoricalDatabaseType.InMemory, connection);
			new Consumers().CreateConsumerForTopics(receiver);
			new ApiController().CreateConsumerForTopics();

			GamesEngine.PurchaseOrders.Customer customer = company.GetOrCreateCustomerById("*********");
			GamesEngine.Bets.Player player = customer.Player;
			DateTime now = new DateTime(2020, 11, 02);

			//-------------------
			Tournaments tournaments = company.Tournaments;
			Tournament nba = new NBATournament(tournaments);//TODO Tournament nos esta forzando a que los juegos de FirstGame tengan game y sucesor y esten definidos
			var gameNumber = 1;
			Game game = nba.GetGameNumber(gameNumber);
			game.Reschedule(now);

			var betBoard = company.Betboard(nba);
			betBoard.AcceptFrom(nba);
			var leagues = betBoard.Leagues(Sport.BASKETBALL);

			var diaDeJuegos = betBoard.Matchday(now);
			game.SetFavorite(game.TeamA);
			game = nba.GetGameNumber(gameNumber);
			var fila1 = 0;
			var lineId = betBoard.NextLineId();
			string gradedBy = "Juan";

			var questionFixed = betBoard.Catalog.CreateTierTwoFixedQuestion(7, "FC", "Quien va a ganar el partido?");
			questionFixed.AllowPublished(game.CurrentPeriod);
			questionFixed.SetParameters(new List<string> { "azul", "blanco", "rojo" }, new List<int> { 120, 115, -105 });
			var fixeds = (FixedLine)diaDeJuegos[game][fila1].CreateLine(lineId + 7, questionFixed, gradedBy, now);
			fixeds.Publish(itIsThePresent, now);

			var fixeds2 = fixeds.NewVersion(new List<int> { 125, 110, -101 }, gradedBy, now.AddSeconds(1));

			Product productFixed = new Product(company, 1);
			productFixed.Price = 1;

			FixedLineActivator lineActivator = new FixedLineActivator(company);
			lineActivator.Line = fixeds2;

			int orderNumber = 1;
			var betNumber = 1;
			var authorizationId = 1;
			OrderCart orderCart = company.NewOrder(customer, orderNumber, Coinage.Coin(CODES.USD), authorizationId);
			orderCart.AddFixedLine(betNumber, fixeds2, "rojo", productFixed, 1m);
			company.PurchaseOrder(itIsThePresent, orderCart, domain, now);

			game.Start(false, now, now);
			game.NewResult(false, now, game.TeamA, 9, game.TeamB, 5);
			game.NewResult(false, now, game.TeamA, 10, game.TeamB, 6);
			game.End(false, now);

			diaDeJuegos[game].Grade(itIsThePresent, fixeds2, "rojo", now, gradedBy);
			diaDeJuegos[game].Regrade(itIsThePresent, fixeds2, now.AddSeconds(1), gradedBy);
			diaDeJuegos[game].Grade(itIsThePresent, fixeds2, "rojo", now, gradedBy);

			var gameboards = diaDeJuegos[game].Wagers;
			Assert.AreEqual(gameboards.NextSequence(), 2);

			RiskAssestment risk = diaDeJuegos[game].RecalculateRisk();
			var riskAssestment = diaDeJuegos[game].RiskAssestment;
			var answer = fixeds2.GetAnAnswer("rojo");
			Assert.AreEqual(riskAssestment.PlacedFor(answer), 1m);
		}

		[TestMethod]
		public void CreateSpreadMoneyTotalPointsQuestionForSpecificTime_Bug5931()
		{
			string connection = "Inmemory";
			GamesEngine.Settings.Integration.ConfigurationForAutoTest(connection);

			Company company = new Company();
			var domain = company.Sales.CreateDomain(false, 1, "localhost", PaymentChannels.Agents.INSIDER);
			company.Sales.CreateStore(1, "store prueba").MakeCurrent();
			company.Sales.CurrentStore.Add(domain);
			company.Accounting = new MockAccounting();
			var sport = company.Tournaments.Sports.FindById(1);
			sport.CreateAndReplaceDefaultBackground("bg", "bg.jpg");
			sport.CreateAndReplaceDefaultSideBySide("sbs", "sbs.jpg");
			bool itIsThePresent = true;
			Integration.UseKafka = false;
			Integration.UseKafkaForAuto = false;
			Integration.ConfigurationForAutoTest("");
			ReceiverOfHistorical receiver = new ReceiverOfHistorical();
			receiver.InitHistorical(HistoricalDatabaseType.InMemory, connection);
			new Consumers().CreateConsumerForTopics(receiver);
			new ApiController().CreateConsumerForTopics();
			receiver.InitHistorical(HistoricalDatabaseType.InMemory, connection);

			GamesEngine.PurchaseOrders.Customer customer = company.GetOrCreateCustomerById("*********");
			GamesEngine.Bets.Player player = customer.Player;
			DateTime now = new DateTime(2020, 11, 02);

			//-------------------
			Tournaments tournaments = company.Tournaments;
			Tournament nba = new NBATournament(tournaments);//TODO Tournament nos esta forzando a que los juegos de FirstGame tengan game y sucesor y esten definidos
			var gameNumber = 1;
			Game game = nba.GetGameNumber(gameNumber);
			game.Reschedule(now);

			var betBoard = company.Betboard(nba);
			betBoard.AcceptFrom(nba);
			var leagues = betBoard.Leagues(Sport.BASKETBALL);

			var questionSL = betBoard.Catalog.CreateTierTwoSpreadQuestion(1, "t2s");
			questionSL.AllowPublished(new List<string> { "First quarter" });

			var diaDeJuegos = betBoard.Matchday(now);
			game.SetFavorite(game.TeamA);
			game = nba.GetGameNumber(gameNumber);
			var fila1 = 0;
			var lineId = betBoard.NextLineId();
			questionSL.SetParameters(4.0, -120, 105);
			var spreadVersion1 = (SpreadLine)diaDeJuegos[game][fila1].CreateLine(lineId + 3, questionSL, "N/A", now);

			var gameboards = diaDeJuegos[game].Wagers;
			Assert.AreEqual(gameboards.NextSequence(), 1);

			RiskAssestment risk = diaDeJuegos[game].RecalculateRisk();
			var riskAssestment = diaDeJuegos[game].RiskAssestment;
			var answerA = spreadVersion1.GetTeamAnswer(game.TeamA);
			var answerB = spreadVersion1.GetTeamAnswer(game.TeamB);
			Assert.AreEqual(riskAssestment.PlacedFor(answerA), 0m);
			Assert.AreEqual(riskAssestment.PlacedFor(answerB), 0m);
		}

		[TestMethod]
		public void GradeSpreadLineGradeRegradeNoAction_Bug5972()
		{
			string connection = "Inmemory";
			GamesEngine.Settings.Integration.ConfigurationForAutoTest(connection);

			Company company = new Company();
			var domain = company.Sales.CreateDomain(false, 1, "localhost", PaymentChannels.Agents.INSIDER);
			company.Sales.CreateStore(1, "store prueba").MakeCurrent();
			company.Sales.CurrentStore.Add(domain);
			company.Accounting = new MockAccounting();
			var sport = company.Tournaments.Sports.FindById(1);
			sport.CreateAndReplaceDefaultBackground("bg", "bg.jpg");
			sport.CreateAndReplaceDefaultSideBySide("sbs", "sbs.jpg");
			bool itIsThePresent = true;
			Integration.UseKafka = false;
			Integration.UseKafkaForAuto = false;
			Integration.ConfigurationForAutoTest("");
			ReceiverOfHistorical receiver = new ReceiverOfHistorical();
			receiver.InitHistorical(HistoricalDatabaseType.InMemory, connection);
			new Consumers().CreateConsumerForTopics(receiver);
			new ApiController().CreateConsumerForTopics();

			GamesEngine.PurchaseOrders.Customer customer = company.GetOrCreateCustomerById("*********");
			GamesEngine.Bets.Player player = customer.Player;
			DateTime now = new DateTime(2020, 11, 02);

			//-------------------
			Tournaments tournaments = company.Tournaments;
			Tournament nba = new NBATournament(tournaments);//TODO Tournament nos esta forzando a que los juegos de FirstGame tengan game y sucesor y esten definidos
			var gameNumber = 1;
			Game game = nba.GetGameNumber(gameNumber);
			game.Reschedule(now);

			var betBoard = company.Betboard(nba);
			betBoard.AcceptFrom(nba);
			var leagues = betBoard.Leagues(Sport.BASKETBALL);

			var questionSL = betBoard.Catalog.CreateTierOneSpreadQuestion(1, "ss");

			var diaDeJuegos = betBoard.Matchday(now);
			game.SetFavorite(game.TeamA);
			game = nba.GetGameNumber(gameNumber);
			var fila1 = 0;
			var lineId = betBoard.NextLineId();
			questionSL.SetParameters(4.0, -120, 105);
			var spreadVersion1 = (SpreadLine)diaDeJuegos[game][fila1].CreateLine(lineId + 3, questionSL, "N/A", now);
			spreadVersion1.Publish(itIsThePresent, now);
			var columna = spreadVersion1.Index;
			//------------------

			Product productSpread = new Product(company, 1);
			productSpread.Price = 1;

			ABLineActivator lineActivator = new ABLineActivator(company);
			lineActivator.Line = spreadVersion1;
			lineActivator.Team = game.TeamA;

			int orderNumber = 1;
			var betNumber = 1;
			var authorizationId = 1;
			OrderCart orderCart = company.NewOrder(customer, orderNumber, Coinage.Coin(CODES.USD), authorizationId);
			orderCart.AddSpreadLine(betNumber, spreadVersion1, game.TeamA, productSpread, 1m);
			company.PurchaseOrder(itIsThePresent, orderCart, domain, now);

			lineActivator = new ABLineActivator(company);
			lineActivator.Line = spreadVersion1;
			lineActivator.Team = game.TeamB;

			orderNumber = 2;
			betNumber = 2;
			authorizationId = 2;
			orderCart = company.NewOrder(customer, orderNumber, Coinage.Coin(CODES.USD), authorizationId);
			orderCart.AddSpreadLine(betNumber, spreadVersion1, game.TeamA, productSpread, 1m);
			company.PurchaseOrder(itIsThePresent, orderCart, domain, now);

			game.Start(false, now, now);
			game.NewResult(false, now, game.TeamA, 9, game.TeamB, 8);
			game.End(false, now);

			string gradedBy = "Juan";
			diaDeJuegos[game].Grade(itIsThePresent, spreadVersion1, now, game.TeamA, 9, game.TeamB, 8, gradedBy);
			diaDeJuegos[game].Regrade(itIsThePresent, spreadVersion1, now.AddSeconds(1), gradedBy);
			diaDeJuegos[game].SetNoAction(itIsThePresent, spreadVersion1, now, gradedBy);

			var gameboards = diaDeJuegos[game].Wagers;
			Assert.AreEqual(gameboards.NextSequence(), 3);
		}

		[TestMethod]
		public void GradeTwoSpreadLine_Bug5884()
		{
			string connection = "Inmemory";
			GamesEngine.Settings.Integration.ConfigurationForAutoTest(connection);

			Company company = new Company();
			var domain = company.Sales.CreateDomain(false, 1, "localhost", PaymentChannels.Agents.INSIDER);
			company.Sales.CreateStore(1, "store prueba").MakeCurrent();
			company.Sales.CurrentStore.Add(domain);
			company.Accounting = new MockAccounting();
			var sport = company.Tournaments.Sports.FindById(1);
			sport.CreateAndReplaceDefaultBackground("bg", "bg.jpg");
			sport.CreateAndReplaceDefaultSideBySide("sbs", "sbs.jpg");
			bool itIsThePresent = true;
			Integration.UseKafka = false;
			Integration.UseKafkaForAuto = false;
			Integration.ConfigurationForAutoTest("");
			ReceiverOfHistorical receiver = new ReceiverOfHistorical();
			receiver.InitHistorical(HistoricalDatabaseType.InMemory, connection);
			new Consumers().CreateConsumerForTopics(receiver);
			new ApiController().CreateConsumerForTopics();

			GamesEngine.PurchaseOrders.Customer customer = company.GetOrCreateCustomerById("*********");
			GamesEngine.Bets.Player player = customer.Player;
			DateTime now = new DateTime(2020, 11, 02);

			//-------------------
			Tournaments tournaments = company.Tournaments;
			Tournament nba = new NBATournament(tournaments);//TODO Tournament nos esta forzando a que los juegos de FirstGame tengan game y sucesor y esten definidos
			var gameNumber = 1;
			Game game = nba.GetGameNumber(gameNumber);
			game.Reschedule(now);

			var betBoard = company.Betboard(nba);
			betBoard.AcceptFrom(nba);
			var leagues = betBoard.Leagues(Sport.BASKETBALL);

			var questionSL = betBoard.Catalog.CreateTierOneSpreadQuestion(1, "ss");

			var diaDeJuegos = betBoard.Matchday(now);
			game.SetFavorite(game.TeamA);
			game = nba.GetGameNumber(gameNumber);
			var fila1 = 0;
			var lineId = betBoard.NextLineId();

			questionSL.SetParameters(4.0, -120, 105);
			var spreadVersion1 = (SpreadLine)diaDeJuegos[game][fila1].CreateLine(lineId + 3, questionSL, "N/A", now);
			spreadVersion1.Publish(itIsThePresent, now);
			var columna = spreadVersion1.Index;

			questionSL.SetParameters(4.5, -125, 110);
			var spreadVersion2 = (SpreadLine)diaDeJuegos[game][fila1].CreateLine(lineId + 4, questionSL, "N/A", now);
			spreadVersion2.Publish(itIsThePresent, now);

			var gameboards = diaDeJuegos[game].Wagers;
			Assert.AreEqual(gameboards.NextSequence(), 1);
		}


		[TestMethod]
		public void PurchaseALineThenUpdateOddsToSeeRemainingTime_Bug6175()
		{
			string connection = "Inmemory";
			GamesEngine.Settings.Integration.ConfigurationForAutoTest(connection);

			Company company = new Company();
			var domain = company.Sales.CreateDomain(false, 1, "localhost", PaymentChannels.Agents.INSIDER);
			company.Sales.CreateStore(1, "store prueba").MakeCurrent();
			company.Sales.CurrentStore.Add(domain);
			company.Accounting = new MockAccounting();
			var sport = company.Tournaments.Sports.FindById(1);
			sport.CreateAndReplaceDefaultBackground("bg", "bg.jpg");
			sport.CreateAndReplaceDefaultSideBySide("sbs", "sbs.jpg");
			bool itIsThePresent = true;
			Integration.UseKafka = false;
			Integration.UseKafkaForAuto = false;
			Integration.ConfigurationForAutoTest("");
			ReceiverOfHistorical receiver = new ReceiverOfHistorical();
			receiver.InitHistorical(HistoricalDatabaseType.InMemory, connection);
			new Consumers().CreateConsumerForTopics(receiver);
			new ApiController().CreateConsumerForTopics();

			GamesEngine.PurchaseOrders.Customer customer = company.GetOrCreateCustomerById("*********");
			GamesEngine.Bets.Player player = customer.Player;
			DateTime now = new DateTime(2020, 11, 02);

			//-------------------
			Tournaments tournaments = company.Tournaments;
			Tournament nba = new NBATournament(tournaments);//TODO Tournament nos esta forzando a que los juegos de FirstGame tengan game y sucesor y esten definidos
			var gameNumber = 1;
			Game game = nba.GetGameNumber(gameNumber);
			game.Reschedule(now);

			var betBoard = company.Betboard(nba);
			betBoard.AcceptFrom(nba);
			var leagues = betBoard.Leagues(Sport.BASKETBALL);

			var questionSL = betBoard.Catalog.CreateTierOneSpreadQuestion(1, "ss");

			var diaDeJuegos = betBoard.Matchday(now);
			game.SetFavorite(game.TeamA);
			game = nba.GetGameNumber(gameNumber);
			var fila1 = 0;
			var lineId = betBoard.NextLineId();
			questionSL.SetParameters(4.0, -120, 105);
			var spreadVersion1 = (SpreadLine)diaDeJuegos[game][fila1].CreateLine(lineId + 3, questionSL, "N/A", now);
			spreadVersion1.Publish(itIsThePresent, now);
			var columna = spreadVersion1.Index;

			var spreadVersion2 = spreadVersion1.NewVersion(-125, 110, "N/A", now.AddSeconds(1));

			var remaining = spreadVersion1.RemainingAvailabilityTime(now.AddMinutes(1));
			Assert.AreEqual(remaining, 0);
			remaining = spreadVersion2.RemainingAvailabilityTime(now.AddMinutes(2));
			Assert.AreEqual(remaining, -1);

			Product productSpread = new Product(company, 1);
			productSpread.Price = 1;

			ABLineActivator lineActivator = new ABLineActivator(company);
			lineActivator.Line = spreadVersion2;
			lineActivator.Team = game.TeamA;

			int orderNumber = 1;
			var betNumber = 1;
			var authorizationId = 1;
			OrderCart orderCart = company.NewOrder(customer, orderNumber, Coinage.Coin(CODES.USD), authorizationId);
			orderCart.AddSpreadLine(betNumber, spreadVersion2, game.TeamA, productSpread, 1m);
			company.PurchaseOrder(itIsThePresent, orderCart, domain, now);

			game.Start(false, now, now);
			game.NewResult(false, now, game.TeamA, 9, game.TeamB, 8);
			game.End(false, now);

			string gradedBy = "Juan";
			diaDeJuegos[game].Grade(itIsThePresent, spreadVersion2, now, game.TeamA, 9, game.TeamB, 8, gradedBy);
			diaDeJuegos[game].Regrade(itIsThePresent, spreadVersion2, now.AddSeconds(1), gradedBy);
			diaDeJuegos[game].SetNoAction(itIsThePresent, spreadVersion2, now, gradedBy);

			var gameboards = diaDeJuegos[game].Wagers;
			Assert.AreEqual(gameboards.NextSequence(), 2);
		}

		[TestMethod]
		public void AssignSamePlayerToDifferentMatches_Bug5989()
		{
			string connection = "Inmemory";
			GamesEngine.Settings.Integration.ConfigurationForAutoTest(connection);

			Company company = new Company();
			CurrenciesTest.AddCurrencies();
			var domain = company.Sales.CreateDomain(false, 1, "localhost", PaymentChannels.Agents.INSIDER);
			company.Sales.CreateStore(1, "store prueba").MakeCurrent();
			company.Sales.CurrentStore.Add(domain);
			company.Accounting = new MockAccounting();

			bool itIsThePresent = true;
			Integration.UseKafka = false;
			Integration.UseKafkaForAuto = false;
			Integration.ConfigurationForAutoTest("");
			ReceiverOfHistorical receiver = new ReceiverOfHistorical();
			receiver.InitHistorical(HistoricalDatabaseType.InMemory, connection);
			new Consumers().CreateConsumerForTopics(receiver);
			new ApiController().CreateConsumerForTopics();
			receiver.InitHistorical(HistoricalDatabaseType.InMemory, connection);

			GamesEngine.PurchaseOrders.Customer customer = company.GetOrCreateCustomerById("*********");
			GamesEngine.Bets.Player player = customer.Player;
			DateTime now = new DateTime(2020, 11, 02);

			//-------------------
			Tournaments tournaments = company.Tournaments;
			var leagueId = company.Tournaments.Leagues.NextLeagueId;
			var tournamentId = company.Tournaments.NextTournamentId;
			var sport = company.Tournaments.Sports.FindById(1);
			sport.CreateAndReplaceDefaultBackground("bg", "bg.jpg");
			sport.CreateAndReplaceDefaultSideBySide("sbs", "sbs.jpg");
			var league = company.Tournaments.Leagues.Create(sport, "soccer league", "SL", leagueId);
			Tournament nba = tournaments.CreateTournament(league, tournamentId, "New Tournament");

			nba.OpenRegistration();
			var teamId = tournaments.NextTeamId();
			var teamA = tournaments.CreateTeam(league, teamId, "Team A");
			teamId = tournaments.NextTeamId();
			var teamB = tournaments.CreateTeam(league, teamId, "Team B");
			teamId = tournaments.NextTeamId();
			var teamC = tournaments.CreateTeam(league, teamId, "Team C");
			nba.Register(teamA);
			nba.Register(teamB);
			nba.Register(teamC);

			var gameNumber = nba.NextGameNumber();
			var game = nba.GetNewGame(gameNumber, teamA, teamB, now);
			game.SetFavorite(game.TeamA);
			Assert.AreEqual(teamA, game.TeamA);
			Assert.AreEqual(teamB, game.TeamB);

			gameNumber = nba.NextGameNumber();
			var game2 = nba.GetNewGame(gameNumber, teamA, teamC, now.AddDays(1));
			game2.SetFavorite(game2.TeamB);
			Assert.AreEqual(teamA, game2.TeamA);
			Assert.AreEqual(teamC, game2.TeamB);
		}

		[TestMethod]
		public void GradeOverUnderLineWithZeroAsScore_Bug6211()
		{
			string connection = "Inmemory";
			GamesEngine.Settings.Integration.ConfigurationForAutoTest(connection);

			Company company = new Company();
			var domain = company.Sales.CreateDomain(false, 1, "localhost", PaymentChannels.Agents.INSIDER);
			company.Sales.CreateStore(1, "store prueba").MakeCurrent();
			company.Sales.CurrentStore.Add(domain);
			company.Accounting = new MockAccounting();
			var sport = company.Tournaments.Sports.FindById(1);
			sport.CreateAndReplaceDefaultBackground("bg", "bg.jpg");
			sport.CreateAndReplaceDefaultSideBySide("sbs", "sbs.jpg");
			bool itIsThePresent = true;
			Integration.UseKafka = false;
			Integration.UseKafkaForAuto = false;
			Integration.ConfigurationForAutoTest("");
			ReceiverOfHistorical receiver = new ReceiverOfHistorical();
			receiver.InitHistorical(HistoricalDatabaseType.InMemory, connection);
			new Consumers().CreateConsumerForTopics(receiver);
			new ApiController().CreateConsumerForTopics();

			GamesEngine.PurchaseOrders.Customer customer = company.GetOrCreateCustomerById("*********");
			GamesEngine.Bets.Player player = customer.Player;
			DateTime now = new DateTime(2020, 11, 02);

			//-------------------
			Tournaments tournaments = company.Tournaments;
			Tournament nba = new NBATournament(tournaments);//TODO Tournament nos esta forzando a que los juegos de FirstGame tengan game y sucesor y esten definidos
			var gameNumber = 1;
			Game game = nba.GetGameNumber(gameNumber);
			game.Reschedule(now);

			var betBoard = company.Betboard(nba);
			betBoard.AcceptFrom(nba);
			var leagues = betBoard.Leagues(Sport.BASKETBALL);

			var catalog = betBoard.Catalog;
			int questionId = 1;
			OverUnderQuestion overUnderQuestion = (OverUnderQuestion)catalog.CreateTierTwoOverUnderQuestion(questionId, "a", "A va a ganar el primer tiempo por mas de 1 gol?");
			overUnderQuestion = (OverUnderQuestion)catalog[questionId];
			questionId++;
			overUnderQuestion.SetParameters(1, -101, 104);
			overUnderQuestion.AllowPublished(game.CurrentPeriod);
			var lineId = betBoard.NextLineId();
			var diaDeJuegos = betBoard.Matchday(now);
			var fila1 = 0;
			game.SetFavorite(game.TeamA);

			var overUnderVersion1 = (OverUnderLine)diaDeJuegos[game][fila1].CreateLine(lineId, overUnderQuestion, "N/A", now);
			overUnderVersion1.Publish(itIsThePresent, now);
						
			game = nba.GetGameNumber(gameNumber);

			Product productOverUnder = new Product(company, 1);
			productOverUnder.Price = 1;

			int orderNumber = 1;
			var betNumber = 1;
			var authorizationId = 1;
			OrderCart orderCart = company.NewOrder(customer, orderNumber, Coinage.Coin(CODES.USD), authorizationId);
			orderCart.AddOverUnderLine(betNumber, overUnderVersion1, false, productOverUnder, 1m);
			company.PurchaseOrder(itIsThePresent, orderCart, domain, now);

			game.Start(false, now, now);
			game.NewResult(false, now, game.TeamA, 9, game.TeamB, 8);
			game.End(false, now);

			string gradedBy = "Juan";
			diaDeJuegos[game].Grade(itIsThePresent, overUnderVersion1, 0, now, gradedBy);

			var gameboards = diaDeJuegos[game].Wagers;
			Assert.AreEqual(gameboards.NextSequence(), 2);
		}

		[TestMethod]
		public void CancelLineAfterUpdateOdds_Bug6230()
		{
			DateTime now = new DateTime(2020, 11, 02);
			Company company = new Company();
			var domain = company.Sales.CreateDomain(false, 1, "localhost", PaymentChannels.Agents.INSIDER);
			company.Sales.CreateStore(1, "store prueba").MakeCurrent();
			company.Sales.CurrentStore.Add(domain);
			company.Accounting = new MockAccounting();
			var sport = company.Tournaments.Sports.FindById(1);
			sport.CreateAndReplaceDefaultBackground("bg", "bg.jpg");
			sport.CreateAndReplaceDefaultSideBySide("sbs", "sbs.jpg");
			var customer = company.GetOrCreateCustomerById("*********");
			string playerId = customer.Player.Id;
			var player = customer.Player;
			Tournaments tournaments = company.Tournaments;
			//{basketball}/nba una maquina por juego
			Tournament torneo = new NBATournament(tournaments);//TODO Tournament nos esta forzando a que los juegos de FirstGame tengan game y sucesor y esten definidos
			var gameNumber = 1;
			Game game = torneo.GetGameNumber(gameNumber);
			game.Reschedule(now);

			Product product = new Product(company, 1);
			product.Price = 1;

			var betBoard = company.Betboard(torneo);
			betBoard.AcceptFrom(torneo);
			var leagues = betBoard.Leagues(Sport.BASKETBALL);
			Assert.AreEqual(1, leagues.Count);
			foreach (var l in leagues)
			{
				Assert.AreEqual(torneo.League, l);
			}

			var diaDeJuegos = betBoard.Matchday(now);
			var itIsThePresent = true;

			var questionSL = betBoard.Catalog.CreateTierOneSpreadQuestion(1, "ss");

			game.SetFavorite(game.TeamA);
			game = torneo.GetGameNumber(gameNumber);
			var fila1 = 0;
			var lineId = betBoard.NextLineId();
			questionSL.SetParameters(2.0, -120, 105);

			var sc = diaDeJuegos.GetShowcase(game);
			var shelve = diaDeJuegos.GetShowcase(game).GetShelve(0);
			var spreadVersion1 = (SpreadLine)diaDeJuegos[game][fila1].CreateLine(3, questionSL, "N/A", now);
			spreadVersion1.Publish(itIsThePresent, now.AddSeconds(12));

			var spreadVersion2 = shelve.CreateNewVersionForSpreadLine(3, 110, -120, "N/A", now.AddMinutes(12));
			var lineSuspended = sc.SuspendLine(true, 3, now.AddMinutes(1));
			Assert.IsNotNull(lineSuspended);
			Assert.AreEqual(3, lineSuspended.LineId);
			var lineCancel = sc.CancelLine(true, spreadVersion2.LineId, now);
			Assert.IsNotNull(lineCancel);
			Assert.AreEqual(3, lineCancel.LineId);
		}

		[TestMethod]
		public void BetBoardByTournament_Bug6548()
		{
			Company company = new Company();
			Tournaments tournaments = company.Tournaments;
			var leagueId = company.Tournaments.Leagues.NextLeagueId;
			var sport = company.Tournaments.Sports.FindById(1);
			var league = company.Tournaments.Leagues.Create(sport, "soccer league", "SL", leagueId);
			Tournament torneo = tournaments.CreateTournament(league, 1, "2020");
			Tournament torneo2 = tournaments.CreateTournament(league, 2, "2021");
			Betboard betboard = company.Betboard(torneo);
			Betboard betboard2 = company.Betboard(torneo2);
			Assert.AreEqual(1, betboard.Tournament.Id);
			Assert.AreEqual(2, betboard2.Tournament.Id);

			string result = "";
			foreach (var b in company.Book.BetBoards) 
			{
				result += b.Tournament.Id;
			}

			Assert.AreEqual("12", result);
		}
	}
}
