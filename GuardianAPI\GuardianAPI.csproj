﻿<Project Sdk="Microsoft.NET.Sdk.Web">
  <PropertyGroup>
    <TargetFramework>net5.0</TargetFramework>
    <CompilerCondition>false</CompilerCondition>
  </PropertyGroup>
  <ItemGroup>
    <Compile Remove="SocketsManager\**" />
    <Content Remove="SocketsManager\**" />
    <EmbeddedResource Remove="SocketsManager\**" />
    <None Remove="SocketsManager\**" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="..\ExchangeAPI\Controllers\CatalogConsumer.cs" Link="Controllers\CatalogConsumer.cs" />
  </ItemGroup>
  <ItemGroup>
    <None Include="..\.dockerignore" Link=".dockerignore">
      <DependentUpon>$(DockerDefaultDockerfile)</DependentUpon>
    </None>
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="Elastic.Apm.NetCoreAll" Version="1.8.1" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="3.1.7" />
    <PackageReference Include="log4net" Version="2.0.8" />
    <PackageReference Include="Microsoft.AspNetCore.Http" Version="2.2.2" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.NewtonsoftJson" Version="3.1.7" />
    <PackageReference Include="NBitcoin" Version="5.0.65" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="5.6.3" />
    <PackageReference Include="Microsoft.OpenApi" Version="1.2.3" />
    <PackageReference Include="Swashbuckle.AspNetCore.Filters" Version="6.1.0" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\GamesEngineMocks\GamesEngineMocks.csproj" />
    <ProjectReference Include="..\GamesEngine\GamesEngine.csproj" />
  </ItemGroup>
  <Target Name="Compiler" AfterTargets="Build" Condition=" '$(CompilerCondition)' ">
    <!-- Directory/Current Project Configuration/Project Framework/Current Project/File Extension-->
    <Exec Command="dotnet run --project ./../RBuilder/RBuilder.csproj $(ProjectDir)../ $(ProjectName) csproj bin $(Configuration) $(TargetFramework) ExCopy 1" />
  </Target>
</Project>