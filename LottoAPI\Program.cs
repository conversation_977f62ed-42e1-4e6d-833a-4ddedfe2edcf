﻿using GamesEngine.Settings;
using log4net;
using Microsoft.AspNetCore;
using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Puppeteer.EventSourcing;
using System;

namespace LottoAPI
{
	public class Program
    {

        public static void Main(string[] args)
        {
            var logRepository = log4net.LogManager.GetRepository(System.Reflection.Assembly.GetEntryAssembly());
            log4net.Config.XmlConfigurator.Configure(logRepository, new System.IO.FileInfo("log4net.config"));

            Loggers.GetIntance().Smart.SetLogger(LogManager.GetLogger(System.Reflection.Assembly.GetEntryAssembly(), "smart"));
            Loggers.GetIntance().Db.SetLogger(LogManager.GetLogger(System.Reflection.Assembly.GetEntryAssembly(), "database"));
            Loggers.GetIntance().AccountingServicesASI.SetLogger(LogManager.GetLogger(System.Reflection.Assembly.GetEntryAssembly(), "accountingservices-asi"));
            Loggers.GetIntance().AccountingServicesASIRemoveTransaction.SetLogger(LogManager.GetLogger(System.Reflection.Assembly.GetEntryAssembly(), "accountingservices-asi-removetransaction"));
            Loggers.GetIntance().AccountingServicesASIPostTransaction.SetLogger(LogManager.GetLogger(System.Reflection.Assembly.GetEntryAssembly(), "accountingservices-asi-posttransaction"));
            Loggers.GetIntance().AccountingServicesASIPostFreeFormTicket.SetLogger(LogManager.GetLogger(System.Reflection.Assembly.GetEntryAssembly(), "accountingservices-asi-postfreeformticket"));
            Loggers.GetIntance().AccountingServicesASIPostFreeFormWagerCollection.SetLogger(LogManager.GetLogger(System.Reflection.Assembly.GetEntryAssembly(), "accountingservices-asi-postfreeformwagercollection"));
            Loggers.GetIntance().AccountingServicesASIPostFreeFormTicketAndWagers.SetLogger(LogManager.GetLogger(System.Reflection.Assembly.GetEntryAssembly(), "accountingservices-asi-postfreeformticketandwagers"));
            Loggers.GetIntance().AccountingServicesASIGradeFreeFormWagerCollection.SetLogger(LogManager.GetLogger(System.Reflection.Assembly.GetEntryAssembly(), "accountingservices-asi-gradefreeformwagercollection"));
            Loggers.GetIntance().AccountingServicesASISvcValidateCustomer.SetLogger(LogManager.GetLogger(System.Reflection.Assembly.GetEntryAssembly(), "accountingservices-asi-svcvalidatecustomer"));
            Loggers.GetIntance().AccountingServicesASIGetLottoCustomer.SetLogger(LogManager.GetLogger(System.Reflection.Assembly.GetEntryAssembly(), "accountingservices-asi-getlottocustomer"));
            Loggers.GetIntance().AccountingServicesASIGetTicketWagers.SetLogger(LogManager.GetLogger(System.Reflection.Assembly.GetEntryAssembly(), "accountingservices-asi-getticketwagers"));

            Loggers.GetIntance().AccountingServicesDGS.SetLogger(LogManager.GetLogger(System.Reflection.Assembly.GetEntryAssembly(), "accountingservices-dgs"));
            Loggers.GetIntance().AccountingServicesDGSGetToken.SetLogger(LogManager.GetLogger(System.Reflection.Assembly.GetEntryAssembly(), "accountingservices-dgs-gettoken"));
            Loggers.GetIntance().AccountingServicesDGSDeposit.SetLogger(LogManager.GetLogger(System.Reflection.Assembly.GetEntryAssembly(), "accountingservices-dgs-deposit"));
            Loggers.GetIntance().AccountingServicesDGSWithdraw.SetLogger(LogManager.GetLogger(System.Reflection.Assembly.GetEntryAssembly(), "accountingservices-dgs-withdraw"));
            Loggers.GetIntance().AccountingServicesDGSValidateCustomer.SetLogger(LogManager.GetLogger(System.Reflection.Assembly.GetEntryAssembly(), "accountingservices-dgs-validatecustomer"));
            Loggers.GetIntance().AccountingServicesDGSCreateWagers.SetLogger(LogManager.GetLogger(System.Reflection.Assembly.GetEntryAssembly(), "accountingservices-dgs-createwagers"));
            Loggers.GetIntance().AccountingServicesDGSUpdateWagers.SetLogger(LogManager.GetLogger(System.Reflection.Assembly.GetEntryAssembly(), "accountingservices-dgs-updatewagers"));

            Loggers.GetIntance().Emails.SetLogger(LogManager.GetLogger(System.Reflection.Assembly.GetEntryAssembly(), "emails"));
            Loggers.GetIntance().Scripts.SetLogger(LogManager.GetLogger(System.Reflection.Assembly.GetEntryAssembly(), "scripts"));

            if (args.Length != 0 && args[0].ToLower() == "follower")
            {
                LottoFollower.LottoFollower.MainFollower();
                return;
            }
            else if (args.Length != 0 && args[0].ToLower() == "keno_follower")
            {
                KenoFollower.KenoFollower.MainFollower(args);
                return;
            }
            else if (args.Length != 0 && args[0].ToLower() == "keno")
            {
                LottoAPI.Actor = new RestAPIActorAsync(new GamesEngine.KenoActor());
            }
            else if (args.Length != 0 && args[0].ToLower() == "picks")
            {
                LottoAPI.Actor = new RestAPIActorAsync(new GamesEngine.LottoActor());
            }
            else
            {
                LottoAPI.Actor = new RestAPIActorAsync(new GamesEngine.LottoActor());
            }

            if (args.Length > 1)
            {
                Integration.CurrentTenantName = args[1].ToLower();
            }
            else
            {
                throw new Exception($"Tenant name is required in second argument");
            }
            var host = BuildWebHost(args);
            host.Run();
        }

        public static IWebHost BuildWebHost(string[] args) =>
            WebHost.CreateDefaultBuilder(args)
                .UseStartup<Startup>()
                .UseUrls("http://0.0.0.0:5050")
                .ConfigureLogging((hostingContext, logging) =>
                {
                    // Requires `using Microsoft.Extensions.Logging;`
                    logging.AddConfiguration(hostingContext.Configuration.GetSection("Logging"));
                    logging.AddConsole();
                    logging.AddDebug();
                    logging.AddEventSourceLogger();
                })
                .Build();
    }
}
