﻿using GamesEngine.Domains;
using GamesEngine.MessageQueuing;
using GamesEngine.PurchaseOrders;
using GamesEngine.Settings;
using GamesEngine.Time;
using MySql.Data.MySqlClient;
using Puppeteer.EventSourcing;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Diagnostics;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using static GamesEngine.Business.WholePaymentProcessor;

namespace GamesEngine.Games.Lotto
{
    public class ReceiverOfHistoricalKeno : ReceiverOfHistorical
    {
        public override void InitHistorical(HistoricalDatabaseType dbType, string connectionString)
        {
            if (String.IsNullOrEmpty(connectionString)) throw new ArgumentNullException(nameof(connectionString));
            if (dbType == HistoricalDatabaseType.MySQL)
            {
                lottoStorage = new KenoStorageMySQL(connectionString);
                lottoStorage.MakeSureThatTableExists();
            }
            else if (dbType == HistoricalDatabaseType.SQLServer)
            {
                lottoStorage = new KenoStorageSQLServer(connectionString);
                lottoStorage.MakeSureThatTableExists();
            }
            else
            {
                throw new GameEngineException($"There is no {nameof(KenoStorage)} implementation.");
            }
        }

        internal class RecordsAccumulatorForReportsKeno: RecordsAccumulatorForReports
        {
            

            protected override void ValidateTheSameDrawAndGameTypeFor(IEnumerable<TicketMessage> messages)
            {
                var firstMessage = messages.ElementAt(0);
                var drawDate = firstMessage.DrawDate();
                var gameType = firstMessage.GameType();
                var gameTypeForReports = Reports.GameTypeOf(gameType);

                foreach (var message in messages)
                {
                    var currentDrawDate = message.DrawDate();
                    var currentGameTypeForReports = Reports.GameTypeOf(message.GameType());

                    if (currentDrawDate != drawDate) throw new GameEngineException($"{nameof(message)} has a different {nameof(drawDate)}. Expected: {drawDate} actual: {currentDrawDate}");
                    if (currentGameTypeForReports != gameTypeForReports) throw new GameEngineException($"{nameof(message)} has a different {nameof(gameType)}. Expected: {gameType} actual: {message.GameType()}");
                }
            }
        }

        private abstract class KenoStorage: LottoStorage
        {
            protected KenoStorage(string connectionString): base(connectionString)
            {
                recordsForReportsAccumulator = new RecordsAccumulatorForReportsKeno();
            }
        }

        private class KenoStorageMySQL : KenoStorage
        {
            internal KenoStorageMySQL(string connectionString) : base(connectionString)
            {

            }

            internal override void MakeSureThatTableExists()
            {
                CreateStorage();
                INSERT_WINNER_CMD = $"INSERT INTO {TABLE_KENO_WINNERS}(date, account, ticket, amount, action, drawingid, domainid, creation, ticketnumber, draw, gradedby, profit, prize, bulleyePrize, prizesversion, currencyid, TIMESTAMP) VALUES ";
                INSERT_LOSER_CMD = $"INSERT INTO {TABLE_KENO_LOSERS}(date, account, ticket, amount, action, drawingid, domainid, creation, ticketnumber, draw, gradedby, profit, prizesversion, currencyid, TIMESTAMP) VALUES ";
                INSERT_NOACTION_CMD = $"INSERT INTO {TABLE_KENO_NOACTIONS}(date, account, ticket, amount, action, drawingid, domainid, creation, ticketnumber, noactionby, prizesversion, currencyid, TIMESTAMP) VALUES ";
                INSERT_LOTTO_RESULTS_CMD = $"INSERT INTO {TABLE_KENO_RESULTS}(drawingid, draw, drawdate, timestamp) VALUES ";
            }

            protected override void CreateStorage()
            {
                StringBuilder statement = new StringBuilder();
                statement.AppendLine(@$"CREATE TABLE IF NOT EXISTS {TABLE_KENO_LOSERS}
                    (
                    ID BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
                    DATE DATETIME NOT NULL,
                    ACCOUNT VARCHAR({Customer.DATABASE_FIELD_ACCOUNT_LENGHT}) NOT NULL,
                    TICKET TEXT NOT NULL,
                    AMOUNT DECIMAL(5,2) NOT NULL,
                    ACTION TINYINT UNSIGNED NOT NULL,
                    DRAWINGID INT UNSIGNED NOT NULL,
                    DOMAINID INT UNSIGNED NOT NULL DEFAULT 0,
                    CREATION DATETIME NOT NULL,
                    TICKETNUMBER BIGINT NOT NULL,
                    DRAW VARCHAR(44) NOT NULL,
                    GRADEDBY VARCHAR(50) NOT NULL,
                    PROFIT DECIMAL(8,2) NOT NULL,
                    PRIZESVERSION INT NOT NULL,
                    CURRENCYID INT UNSIGNED NOT NULL,
                    TIMESTAMP SMALLINT NOT NULL,
                    PRIMARY KEY (ID)
                    ) ENGINE=InnoDB CHARSET=utf8;");
                statement.AppendLine(@$"CREATE TABLE IF NOT EXISTS {TABLE_KENO_WINNERS}
                    (
                    ID BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
                    DATE DATETIME NOT NULL,
                    ACCOUNT VARCHAR({Customer.DATABASE_FIELD_ACCOUNT_LENGHT}) NOT NULL,
                    TICKET TEXT NOT NULL,
                    AMOUNT DECIMAL(5,2) NOT NULL,
                    ACTION TINYINT UNSIGNED NOT NULL,
                    DRAWINGID INT UNSIGNED NOT NULL,
                    DOMAINID INT UNSIGNED NOT NULL DEFAULT 0,
                    CREATION DATETIME NOT NULL,
                    TICKETNUMBER BIGINT NOT NULL,
                    DRAW VARCHAR(44) NOT NULL,
                    GRADEDBY VARCHAR(50) NOT NULL,
                    PROFIT DECIMAL(8,2) NOT NULL,
                    PRIZE DECIMAL(8,2) NOT NULL,
                    BULLEYEPRIZE DECIMAL(8,2) NOT NULL,
                    PRIZESVERSION INT NOT NULL,
                    CURRENCYID INT UNSIGNED NOT NULL,
                    TIMESTAMP SMALLINT NOT NULL,
                    PRIMARY KEY (ID)
                    ) ENGINE=InnoDB CHARSET=utf8;");
                statement.AppendLine(@$"CREATE TABLE IF NOT EXISTS {TABLE_KENO_NOACTIONS}
                    (
                    ID BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
                    DATE DATETIME NOT NULL,
                    ACCOUNT VARCHAR({Customer.DATABASE_FIELD_ACCOUNT_LENGHT}) NOT NULL,
                    TICKET TEXT NOT NULL,
                    AMOUNT DECIMAL(5,2) NOT NULL,
                    ACTION TINYINT UNSIGNED NOT NULL,
                    DRAWINGID INT UNSIGNED NOT NULL,
                    DOMAINID INT UNSIGNED NOT NULL DEFAULT 0,
                    CREATION DATETIME NOT NULL,
                    TICKETNUMBER BIGINT NOT NULL,
                    NOACTIONBY VARCHAR(50) NOT NULL,
                    PRIZESVERSION INT NOT NULL,
                    CURRENCYID INT UNSIGNED NOT NULL,
                    TIMESTAMP SMALLINT NOT NULL,
                    PRIMARY KEY (ID)
                    ) ENGINE=InnoDB CHARSET=utf8;");

                statement.AppendLine(@$"CREATE TABLE IF NOT EXISTS {TABLE_KENO_DRAWINGS}
                    (
                    ID INT UNSIGNED NOT NULL,
                    DRAWINGNAME VARCHAR(50) NULL,
                    IDOFPICK TINYINT UNSIGNED NOT NULL,
                    PRIMARY KEY (ID)
                    ) ENGINE=InnoDB CHARSET=utf8;");

                statement.AppendLine(@$"CREATE TABLE IF NOT EXISTS {TABLE_DOMAINS} 
                    (
                    ID INT UNSIGNED NOT NULL,
                    URL VARCHAR(253) NULL,
                    PRIMARY KEY (ID)
                    ) ENGINE=InnoDB CHARSET=utf8;");

                statement.AppendLine(@$"CREATE TABLE IF NOT EXISTS {TABLE_KENO_RESULTS} 
                    (
                    DRAWINGID INT UNSIGNED NOT NULL,
                    DRAWDATE DATETIME NOT NULL,
                    DRAW VARCHAR(44) NOT NULL,
                    TIMESTAMP SMALLINT NOT NULL
                    ) ENGINE=InnoDB CHARSET=utf8;");

                string sql = statement.ToString();
                ExecuteCommand(sql);
            }

            protected override void ExecuteCommand(string cmdText)
            {
                using (MySqlConnection connection = new MySqlConnection(connectionString))
                {
                    try
                    {
                        try
                        {
                            connection.Open();
                            using (MySqlCommand command = new MySqlCommand(cmdText, connection))
                            {
                                command.ExecuteNonQuery();
                            }
                        }
                        catch (Exception e)
                        {
                            Loggers.GetIntance().Db.Error($@"sql:{cmdText} type:{e.GetType()} error:{e.Message}", e);
                            throw new GameEngineException("MySQL Error [" + cmdText + "]");
                        }
                        finally
                        {
                            connection.Close();
                        }

                        WebHookClientRequest.Instance.SendWebHook(DateTime.Now, cmdText, "UnknownAPI", "Lotto");
                    }
                    catch (Exception webhookEx)
                    {
                        Loggers.GetIntance().Webhook.Error($"Webhook failed for SQL: {cmdText}", webhookEx);
                        throw;
                    }
                }
            }

            protected override void ExecuteCommand(DrawingMessage msg)
            {
                using (var connection = new MySqlConnection(connectionString))
                {
                    var cmdText = @"INSERT INTO kenodrawings(id, drawingname, idofpick) VALUES (@id, @description, @idOfLottery) ON DUPLICATE KEY UPDATE drawingname=@description";
                    try
                    {
                        try
                        {
                            connection.Open();
                            using (var command = new MySqlCommand(cmdText, connection))
                            {
                                command.Parameters.AddWithValue("@id", msg.Id);
                                command.Parameters.AddWithValue("@description", msg.Description);
                                command.Parameters.AddWithValue("@idOfLottery", (int)msg.IdOfLottery);
                                command.ExecuteNonQuery();
                            }
                        }
                        catch (Exception e)
                        {
                            Loggers.GetIntance().Db.Error($@"sql:{cmdText} type:{e.GetType()} error:{e.Message}", e);
                            throw new GameEngineException("MySQL Error [" + cmdText + "]");
                        }
                        finally
                        {
                            connection.Close();
                        }

                        WebHookClientRequest.Instance.SendWebHook(DateTime.Now, cmdText, "kenodrawings", "Lotto");
                    }
                    catch (Exception webhookEx)
                    {
                        Loggers.GetIntance().Webhook.Error($"Webhook failed for SQL: {cmdText}", webhookEx);
                        throw;
                    }
                }
            }

            internal override void IncrementTimeStamp(int drawingId, int year, int month, int day)
            {
                StringBuilder statement = new StringBuilder();
                var drawDate = new DateTime(year, month, day);
                string drawDateAsText = drawDate.ToString("yyyy-MM-dd");
                statement.AppendLine(@$"
                    UPDATE {TABLE_KENO_WINNERS}
                    SET TIMESTAMP = TIMESTAMP + 1 
                    WHERE DRAWINGID={drawingId} AND DATE(date)='{drawDateAsText}';");

                statement.AppendLine(@$"
                    UPDATE {TABLE_KENO_LOSERS}
                    SET TIMESTAMP = TIMESTAMP + 1 
                    WHERE DRAWINGID={drawingId} AND DATE(date)='{drawDateAsText}';");

                statement.AppendLine(@$"
                    UPDATE {TABLE_KENO_NOACTIONS}
                    SET TIMESTAMP = TIMESTAMP + 1 
                    WHERE DRAWINGID={drawingId} AND DATE(date)='{drawDateAsText}';");

                statement.AppendLine(@$"
                    UPDATE {TABLE_KENO_RESULTS}
                    SET TIMESTAMP = TIMESTAMP + 1 
                    WHERE DRAWINGID={drawingId} AND DATE(drawdate)='{drawDateAsText}';");

                ExecuteCommand(statement.ToString());
            }

            internal override void IncrementTimeStampForWinners(int drawingId, int year, int month, int day)
            {
                StringBuilder statement = new StringBuilder();
                var drawDate = new DateTime(year, month, day);
                string drawDateAsText = drawDate.ToString("yyyy-MM-dd");
                statement.AppendLine(@$"
                    UPDATE {TABLE_KENO_WINNERS}
                    SET TIMESTAMP = TIMESTAMP + 1 
                    WHERE DRAWINGID={drawingId} AND DATE(date)='{drawDateAsText}';");

                statement.AppendLine(@$"
                    UPDATE {TABLE_KENO_RESULTS}
                    SET TIMESTAMP = TIMESTAMP + 1 
                    WHERE DRAWINGID={drawingId} AND DATE(drawdate)='{drawDateAsText}';");

                ExecuteCommand(statement.ToString());
            }

            internal override void Store(WinnerResultsInfo info)
            {
                const int TIME_STAMP_THIS_IS_THE_CURRENT_VERSION = 0;
                string record = $@"({info.DrawingId},
                '{info.Draw}',
                '{ ToDateString(info.Year, info.Month, info.Day, info.Hour, info.Minute, 0)}',
                {TIME_STAMP_THIS_IS_THE_CURRENT_VERSION})";

                rowsOfWinnerResultsReceived.Add(record);
            }

            internal override void StoreWinner(TicketMessage msg)
            {
                var info = (WinnerKenoInfo)msg;
                const int TIME_STAMP_THIS_IS_THE_CURRENT_VERSION = 0;
                string record = $@"(
                '{ ToDateString(info.Year, info.Month, info.Day, info.Hour, info.Minute, 0)}',
                '{ info.AccountNumber}',
                '{ info.Ticket}',
                { info.Amount.ToString()},
                { (int)info.Action},
                { info.DrawingId},
                { info.DomainId},
                '{ ToDateString(info.Creation.Year, info.Creation.Month, info.Creation.Day, info.Creation.Hour, info.Creation.Minute, info.Creation.Second)}',
                { info.TicketNumber },
                '{ info.Draw}',
                '{ info.GradedBy}',
                { info.Profit.ToString()},
                { info.Prize.ToString()},
                { info.BulleyePrize},
                { info.PrizesVersion.ToString()},
                {info.CurrencyId},
                { TIME_STAMP_THIS_IS_THE_CURRENT_VERSION}
                )";

                rowsOfWinnerReceived.Add(record);
            }

            internal override void StoreLoser(TicketMessage msg)
            {
                var info = (LoserKenoInfo)msg;
                const int TIME_STAMP_THIS_IS_THE_CURRENT_VERSION = 0;
                string record = $@"(
                '{ ToDateString(info.Year, info.Month, info.Day, info.Hour, info.Minute, 0)}',
                '{ info.AccountNumber}',
                '{ info.Ticket}',
                { info.Amount.ToString()},
                { (int)info.Action},
                { info.DrawingId},
                { info.DomainId},
                '{ ToDateString(info.Creation.Year, info.Creation.Month, info.Creation.Day, info.Creation.Hour, info.Creation.Minute, info.Creation.Second)}',
                { info.TicketNumber },
                '{ info.Draw}',
                '{ info.GradedBy}',
                { info.Profit.ToString()},
                { info.PrizesVersion.ToString()},
                {info.CurrencyId},
                { TIME_STAMP_THIS_IS_THE_CURRENT_VERSION}
                )";

                rowsOfLoserReceived.Add(record);
            }

            internal override void StoreNoAction(TicketMessage msg)
            {
                var info = (NoActionKenoInfo)msg;
                const int TIME_STAMP_THIS_IS_THE_CURRENT_VERSION = 0;
                string record = $@"(
				'{ ToDateString(info.Year, info.Month, info.Day, info.Hour, info.Minute, 0)}',
				'{ info.AccountNumber}',
				'{ info.Ticket}',
				{ info.Amount.ToString()},
				{ (int)info.Action},
				{ info.DrawingId},
                { info.DomainId},
				'{ ToDateString(info.Creation.Year, info.Creation.Month, info.Creation.Day, info.Creation.Hour, info.Creation.Minute, info.Creation.Second)}',
				{ info.TicketNumber },
				'{ info.NoActionBy}',
				{ info.PrizesVersion.ToString()},
                {info.CurrencyId},
				{ TIME_STAMP_THIS_IS_THE_CURRENT_VERSION}
				)";

                rowsOfNoActionReceived.Add(record);
            }

            internal override void Store(DrawingMessage info)
            {
                ExecuteCommand(info);
            }

            internal override void Store(DomainBIMessage info)
            {
                string record = $@"INSERT INTO {TABLE_DOMAINS}(id, url) VALUES ({info.Id}, '{info.Url}') ON DUPLICATE KEY UPDATE url='{info.Url}'";

                rowsOfDomainReceived.Add(record);
            }

            internal override void EndReception()
            {
                StringBuilder cmdText = new StringBuilder();

                const int MAXIMUM_NUMBER_TO_INSERT = 1000;
                if (rowsOfDomainReceived.Count > 0)
                {
                    cmdText.Append(string.Join(";", rowsOfDomainReceived));
                    cmdText.Append(';');
                    rowsOfDomainReceived.Clear();
                }
                if (rowsOfDomainToUpdateReceived.Count > 0)
                {
                    cmdText.Append(string.Join(";", rowsOfDomainToUpdateReceived));
                    cmdText.Append(';');
                    rowsOfDomainToUpdateReceived.Clear();
                }
                if (rowsOfWinnerResultsReceived.Count > 0)
                {
                    cmdText.Append(INSERT_LOTTO_RESULTS_CMD);
                    cmdText.Append(string.Join(",", rowsOfWinnerResultsReceived));
                    cmdText.Append(';');
                    rowsOfWinnerResultsReceived.Clear();
                }

                if (rowsOfWinnerReceived.Count > 0)
                {
                    if (rowsOfWinnerReceived.Count > MAXIMUM_NUMBER_TO_INSERT)
                    {
                        int groupsOfRowsToInsert = (rowsOfWinnerReceived.Count / MAXIMUM_NUMBER_TO_INSERT) + 1;
                        for (int index = 0; index < groupsOfRowsToInsert; index++)
                        {
                            var rowsToSkip = index * MAXIMUM_NUMBER_TO_INSERT;
                            cmdText.Append(INSERT_WINNER_CMD);
                            cmdText.Append(string.Join(",", rowsOfWinnerReceived.Skip(rowsToSkip).Take(MAXIMUM_NUMBER_TO_INSERT)));
                            cmdText.Append(';');
                        }
                    }
                    else
                    {
                        cmdText.Append(INSERT_WINNER_CMD);
                        cmdText.Append(string.Join(",", rowsOfWinnerReceived));
                        cmdText.Append(';');
                    }

                    rowsOfWinnerReceived.Clear();
                }
                if (rowsOfLoserReceived.Count > 0)
                {
                    cmdText.Append(INSERT_LOSER_CMD);
                    cmdText.Append(string.Join(",", rowsOfLoserReceived));
                    cmdText.Append(';');
                    rowsOfLoserReceived.Clear();
                }
                if (rowsOfNoActionReceived.Count > 0)
                {
                    cmdText.Append(INSERT_NOACTION_CMD);
                    cmdText.Append(string.Join(",", rowsOfNoActionReceived));
                    cmdText.Append(';');
                    rowsOfNoActionReceived.Clear();
                }

                if (cmdText.Length > 0)
                {
                    ExecuteCommand(cmdText.ToString());
                }
            }
        }

        private class KenoStorageSQLServer : KenoStorage
        {
            internal KenoStorageSQLServer(string connectionString) : base(connectionString)
            {
            }

            internal override void MakeSureThatTableExists()
            {
                CreateStorage();
                INSERT_WINNER_CMD = $"INSERT INTO {TABLE_KENO_WINNERS}(date, account, ticket, amount, action, drawingid, domainid, creation, ticketnumber, draw, gradedby, profit, prize, bulleyePrize, prizesversion, currencyid, TIMESTAMP) VALUES ";
                INSERT_LOSER_CMD = $"INSERT INTO {TABLE_KENO_LOSERS}(date, account, ticket, amount, action, drawingid, domainid, creation, ticketnumber, draw, gradedby, profit, prizesversion, currencyid, TIMESTAMP) VALUES ";
                INSERT_NOACTION_CMD = $"INSERT INTO {TABLE_KENO_NOACTIONS}(date, account, ticket, amount, action, drawingid, domainid, creation, ticketnumber, noactionby, prizesversion, currencyid, TIMESTAMP) VALUES ";
                INSERT_LOTTO_RESULTS_CMD = $"INSERT INTO {TABLE_KENO_RESULTS}(drawingid, draw, drawdate, timestamp) VALUES ";
            }

            protected override void CreateStorage()
            {
                StringBuilder statement = new StringBuilder();
                statement.AppendLine(@$"IF NOT EXISTS(
                    SELECT 1 FROM INFORMATION_SCHEMA.TABLES 
                    WHERE TABLE_NAME = '{TABLE_KENO_LOSERS}')
                    BEGIN
                    CREATE TABLE {TABLE_KENO_LOSERS}
                    (
                    ID INT IDENTITY(1,1),
                    DATE DATETIME NOT NULL,
                    ACCOUNT VARCHAR({Customer.DATABASE_FIELD_ACCOUNT_LENGHT}) NOT NULL,
                    TICKET NVARCHAR(MAX) NOT NULL,
                    AMOUNT DECIMAL(5,2) NOT NULL,
                    ACTION TINYINT NOT NULL,
                    DRAWINGID INT NOT NULL,
                    DOMAINID INT NOT NULL DEFAULT 0,
                    CREATION DATETIME NOT NULL,
                    TICKETNUMBER INT NOT NULL,
                    DRAW VARCHAR(44) NOT NULL,
                    GRADEDBY VARCHAR(50) NOT NULL,
                    PROFIT DECIMAL(8,2) NOT NULL,
                    PRIZESVERSION INT NOT NULL,
                    CURRENCYID INT NOT NULL,
                    TIMESTAMP SMALLINT NOT NULL
                    );
                    CREATE INDEX {TABLE_KENO_LOSERS}_ACCOUNT_DATE ON {TABLE_KENO_LOSERS} (ACCOUNT,DATE);
                    END");

                statement.AppendLine(@$"IF NOT EXISTS(
                    SELECT 1 FROM INFORMATION_SCHEMA.TABLES 
                    WHERE TABLE_NAME = '{TABLE_KENO_WINNERS}')
                    BEGIN
                    CREATE TABLE {TABLE_KENO_WINNERS}
                    (
                    ID INT IDENTITY(1,1),
                    DATE DATETIME NOT NULL,
                    ACCOUNT VARCHAR({Customer.DATABASE_FIELD_ACCOUNT_LENGHT}) NOT NULL,
                    TICKET NVARCHAR(MAX) NOT NULL,
                    AMOUNT DECIMAL(5,2) NOT NULL,
                    ACTION TINYINT NOT NULL,
                    DRAWINGID INT NOT NULL,
                    DOMAINID INT NOT NULL DEFAULT 0,
                    CREATION DATETIME NOT NULL,
                    TICKETNUMBER INT NOT NULL,
                    DRAW VARCHAR(44) NOT NULL,
                    GRADEDBY VARCHAR(50) NOT NULL,
                    PROFIT DECIMAL(8,2) NOT NULL,
                    PRIZE DECIMAL(8,2) NOT NULL,
                    BULLEYEPRIZE DECIMAL(8,2) NOT NULL,
                    PRIZESVERSION INT NOT NULL,
                    CURRENCYID INT NOT NULL,
                    TIMESTAMP SMALLINT NOT NULL
                    );
                    CREATE INDEX {TABLE_KENO_WINNERS}_ACCOUNT_DATE ON {TABLE_KENO_WINNERS} (ACCOUNT,DATE);
                    END");
                statement.AppendLine(@$"IF NOT EXISTS(
                    SELECT 1 FROM INFORMATION_SCHEMA.TABLES 
                    WHERE TABLE_NAME = '{TABLE_KENO_NOACTIONS}')
                    BEGIN
                    CREATE TABLE {TABLE_KENO_NOACTIONS}
                    (
                    ID INT IDENTITY(1,1),
                    DATE DATETIME NOT NULL,
                    ACCOUNT VARCHAR({Customer.DATABASE_FIELD_ACCOUNT_LENGHT}) NOT NULL,
                    TICKET NVARCHAR(MAX) NOT NULL,
                    AMOUNT DECIMAL(5,2) NOT NULL,
                    ACTION TINYINT NOT NULL,
                    DRAWINGID INT NOT NULL,
                    DOMAINID INT NOT NULL DEFAULT 0,
                    CREATION DATETIME NOT NULL,
                    TICKETNUMBER INT NOT NULL,
                    NOACTIONBY VARCHAR(50) NOT NULL,
                    PRIZESVERSION INT NOT NULL,
                    CURRENCYID INT NOT NULL,
                    TIMESTAMP SMALLINT NOT NULL
                    );
                    CREATE INDEX {TABLE_KENO_NOACTIONS}_ACCOUNT_DATE ON {TABLE_KENO_NOACTIONS} (ACCOUNT,DATE);
                    END");

                statement.AppendLine(@$"CREATE TABLE IF NOT EXISTS {TABLE_KENO_DRAWINGS}
                    (
                    ID INT UNSIGNED NOT NULL,
                    DRAWINGNAME VARCHAR(50) NULL,
                    IDOFPICK TINYINT UNSIGNED NOT NULL,
                    PRIMARY KEY (ID)
                    ) ENGINE=InnoDB CHARSET=utf8;");

                statement.AppendLine(@$"CREATE TABLE IF NOT EXISTS {TABLE_DOMAINS} 
                    (
                    ID INT UNSIGNED NOT NULL,
                    URL VARCHAR(253) NULL,
                    PRIMARY KEY (ID)
                    ) ENGINE=InnoDB CHARSET=utf8;");

                statement.AppendLine(@$"CREATE TABLE IF NOT EXISTS {TABLE_KENO_RESULTS} 
                    (
                    DRAWINGID INT UNSIGNED NOT NULL,
                    DRAWDATE DATETIME NOT NULL,
                    DRAW VARCHAR(14) NOT NULL,
                    TIMESTAMP SMALLINT NOT NULL
                    ) ENGINE=InnoDB CHARSET=utf8;");
                string sql = statement.ToString();
                ExecuteCommand(sql);
            }

            protected override void ExecuteCommand(string cmdText)
            {
                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    try
                    {
                        try
                        {
                            connection.Open();
                            using (SqlCommand command = new SqlCommand(cmdText, connection))
                            {
                                command.CommandType = CommandType.Text;
                                command.ExecuteNonQuery();
                            }
                        }
                        catch (Exception e)
                        {
                            Loggers.GetIntance().Db.Error($@"sql:{cmdText} type:{e.GetType()} error:{e.Message}", e);
                            throw new GameEngineException("SQLServer Error [" + cmdText + "]");
                        }
                        finally
                        {
                            connection.Close();
                        }

                        WebHookClientRequest.Instance.SendWebHook(DateTime.Now, cmdText, "UnknownAPI", "Lotto");
                    }
                    catch (Exception webhookEx)
                    {
                        Loggers.GetIntance().Webhook.Error($"Webhook failed for SQL: {cmdText}", webhookEx);
                        throw;
                    }
                }
            }

            protected override void ExecuteCommand(DrawingMessage msg)
            {
                using (var connection = new SqlConnection(connectionString))
                {
                    var cmdText = @"If Not Exists(select * from kenodrawings where ID=@id)
                                    Begin
                                    INSERT INTO kenodrawings(id, drawingname, idofpick) VALUES (@id, @description, @idOfLottery)
                                    End
                                else
                                    Begin
                                    UPDATE kenodrawings SET drawingname = @description WHERE id=@id
                                    End";
                    try
                    {
                        try
                        {
                            connection.Open();
                            using (var command = new SqlCommand(cmdText, connection))
                            {
                                command.Parameters.AddWithValue("@id", msg.Id);
                                command.Parameters.AddWithValue("@description", msg.Description);
                                command.Parameters.AddWithValue("@idOfLottery", (int)msg.IdOfLottery);
                                command.ExecuteNonQuery();
                            }
                        }
                        catch (Exception e)
                        {
                            Loggers.GetIntance().Db.Error($@"sql:{cmdText} type:{e.GetType()} error:{e.Message}", e);
                            throw new GameEngineException("SQL Error [" + cmdText + "]");
                        }
                        finally
                        {
                            connection.Close();
                        }

                        WebHookClientRequest.Instance.SendWebHook(DateTime.Now, cmdText, "kenodrawings", "Lotto");
                    }
                    catch (Exception webhookEx)
                    {
                        Loggers.GetIntance().Webhook.Error($"Webhook failed for SQL: {cmdText}", webhookEx);
                        throw;
                    }
                }
            }

            internal override void IncrementTimeStamp(int drawingId, int year, int month, int day)
            {
                StringBuilder statement = new StringBuilder();
                var drawDate = new DateTime(year, month, day);
                string drawDateAsText = drawDate.ToString("yyyy-MM-dd");
                statement.AppendLine(@$"
                    UPDATE {TABLE_KENO_WINNERS}
                    SET TIMESTAMP=TIMESTAMP+1 
                    WHERE DRAWINGID={drawingId} AND CAST(date as DATE)='{drawDateAsText}';");

                statement.AppendLine(@$"
                    UPDATE {TABLE_KENO_LOSERS}
                    SET TIMESTAMP=TIMESTAMP+1 
                    WHERE DRAWINGID={drawingId} AND CAST(date as DATE)='{drawDateAsText}';");

                statement.AppendLine(@$"
                    UPDATE {TABLE_KENO_NOACTIONS}
                    SET TIMESTAMP=TIMESTAMP+1 
                    WHERE DRAWINGID={drawingId} AND CAST(date as DATE)='{drawDateAsText}';");

                statement.AppendLine(@$"
                    UPDATE {TABLE_KENO_RESULTS}
                    SET TIMESTAMP = TIMESTAMP + 1 
                    WHERE DRAWINGID={drawingId} AND CAST(drawdate as DATE)='{drawDateAsText}';");

                ExecuteCommand(statement.ToString());
            }

            internal override void IncrementTimeStampForWinners(int drawingId, int year, int month, int day)
            {
                StringBuilder statement = new StringBuilder();
                var drawDate = new DateTime(year, month, day);
                string drawDateAsText = drawDate.ToString("yyyy-MM-dd");
                statement.AppendLine(@$"
                    UPDATE {TABLE_KENO_WINNERS}
                    SET TIMESTAMP=TIMESTAMP+1 
                    WHERE DRAWINGID={drawingId} AND CAST(date as DATE)='{drawDateAsText}';");

                statement.AppendLine(@$"
                    UPDATE {TABLE_KENO_RESULTS}
                    SET TIMESTAMP = TIMESTAMP + 1 
                    WHERE DRAWINGID={drawingId} AND CAST(drawdate as DATE)='{drawDateAsText}';");

                ExecuteCommand(statement.ToString());
            }

            internal override void Store(WinnerResultsInfo info)
            {
                const int TIME_STAMP_THIS_IS_THE_CURRENT_VERSION = 0;
                string record = $@"({info.DrawingId},
                '{info.Draw}',
                '{ ToDateString(info.Year, info.Month, info.Day, info.Hour, info.Minute, 0)}',
                {TIME_STAMP_THIS_IS_THE_CURRENT_VERSION})";

                rowsOfWinnerResultsReceived.Add(record);
            }

            internal override void StoreWinner(TicketMessage msg)
            {
                var info = (WinnerKenoInfo)msg;
                const int TIME_STAMP_THIS_IS_THE_CURRENT_VERSION = 0;
                string record = $@"(
                '{ ToDateString(info.Year, info.Month, info.Day, info.Hour, info.Minute, 0)}',
                '{ info.AccountNumber}',
                '{ info.Ticket}',
                { info.Amount.ToString()},
                { (int)info.Action},
                { info.DrawingId},
                { info.DomainId},
                '{ ToFullDateTimeString(info.Creation)}',
                { info.TicketNumber },
                '{ info.Draw}',
                '{ info.GradedBy}',
                { info.Profit.ToString()},
                { info.Prize.ToString()},
                { info.BulleyePrize},
                { info.PrizesVersion.ToString()},
                {info.CurrencyId},
                {TIME_STAMP_THIS_IS_THE_CURRENT_VERSION})";

                rowsOfWinnerReceived.Add(record);
            }

            internal override void StoreLoser(TicketMessage msg)
            {
                var info = (LoserInfo)msg;
                const int TIME_STAMP_THIS_IS_THE_CURRENT_VERSION = 0;
                string record = $@"(
                '{ ToDateString(info.Year, info.Month, info.Day, info.Hour, info.Minute, 0)}',
                '{ info.AccountNumber}',
                '{ info.Ticket}',
                { info.Amount.ToString()},
                { (int)info.Action},
                { info.DrawingId},
                { info.DomainId},
                '{ ToFullDateTimeString(info.Creation)}',
                { info.TicketNumber },
                '{ info.Draw}',
                '{ info.GradedBy}',
                { info.Profit.ToString()},
                { info.PrizesVersion.ToString()},
                {info.CurrencyId},
                { TIME_STAMP_THIS_IS_THE_CURRENT_VERSION}
                )";

                rowsOfLoserReceived.Add(record);
            }

            internal override void StoreNoAction(TicketMessage msg)
            {
                var info = (NoActionInfo)msg;
                const int TIME_STAMP_THIS_IS_THE_CURRENT_VERSION = 0;
                string record = $@"(
				'{ ToDateString(info.Year, info.Month, info.Day, info.Hour, info.Minute, 0)}',
				'{ info.AccountNumber}',
				'{ info.Ticket}',
				{ info.Amount.ToString()},
				{ (int)info.Action},
				{ info.DrawingId},
                { info.DomainId},
				'{ ToFullDateTimeString(info.Creation)}',
				{ info.TicketNumber },
				'{ info.NoActionBy}',
				{ info.PrizesVersion.ToString()},
                {info.CurrencyId},
				{ TIME_STAMP_THIS_IS_THE_CURRENT_VERSION}
				)";

                rowsOfNoActionReceived.Add(record);
            }

            internal override void Store(DrawingMessage info)
            {
                ExecuteCommand(info);
            }

            internal override void Store(DomainBIMessage info)
            {
                string record = $@"If Not Exists(select * from {TABLE_DOMAINS} where ID={info.Id})
                                    Begin
                                    INSERT INTO {TABLE_DOMAINS}(id, url) VALUES ({info.Id}, '{info.Url}')
                                    End
                                else
                                    Begin
                                        UPDATE {TABLE_DOMAINS} SET url = '{info.Url}' WHERE id={info.Id}
                                    End";

                rowsOfDomainReceived.Add(record);
            }

            private string ToFullDateTimeString(DateTime aDate)
            {
                return aDate.ToString("yyyy-MM-dd HH:mm:ss.fff");
            }

            internal override void EndReception()
            {
                StringBuilder cmdText = new StringBuilder();

                const int MAXIMUM_NUMBER_TO_INSERT = 1000;
                if (rowsOfDomainReceived.Count > 0)
                {
                    cmdText.Append(string.Join(";", rowsOfDomainReceived));
                    cmdText.Append(';');
                    rowsOfDomainReceived.Clear();
                }
                if (rowsOfDomainToUpdateReceived.Count > 0)
                {
                    cmdText.Append(string.Join(";", rowsOfDomainToUpdateReceived));
                    cmdText.Append(';');
                    rowsOfDomainToUpdateReceived.Clear();
                }
                if (rowsOfWinnerResultsReceived.Count > 0)
                {
                    cmdText.Append(INSERT_LOTTO_RESULTS_CMD);
                    cmdText.Append(string.Join(",", rowsOfWinnerResultsReceived));
                    cmdText.Append(';');
                    rowsOfWinnerResultsReceived.Clear();
                }

                if (rowsOfWinnerReceived.Count > 0)
                {
                    if (rowsOfWinnerReceived.Count > MAXIMUM_NUMBER_TO_INSERT)
                    {
                        int groupsOfRowsToInsert = (rowsOfWinnerReceived.Count / MAXIMUM_NUMBER_TO_INSERT) + 1;
                        for (int index = 0; index < groupsOfRowsToInsert; index++)
                        {
                            var rowsToSkip = index * MAXIMUM_NUMBER_TO_INSERT;
                            cmdText.Append(INSERT_WINNER_CMD);
                            cmdText.Append(string.Join(",", rowsOfWinnerReceived.Skip(rowsToSkip).Take(MAXIMUM_NUMBER_TO_INSERT)));
                            cmdText.Append(';');
                        }
                    }
                    else
                    {
                        cmdText.Append(INSERT_WINNER_CMD);
                        cmdText.Append(string.Join(",", rowsOfWinnerReceived));
                        cmdText.Append(';');
                    }

                    rowsOfWinnerReceived.Clear();
                }
                if (rowsOfLoserReceived.Count > 0)
                {
                    cmdText.Append(INSERT_LOSER_CMD);
                    cmdText.Append(string.Join(",", rowsOfLoserReceived));
                    cmdText.Append(';');
                    rowsOfLoserReceived.Clear();
                }
                if (rowsOfNoActionReceived.Count > 0)
                {
                    cmdText.Append(INSERT_NOACTION_CMD);
                    cmdText.Append(string.Join(",", rowsOfNoActionReceived));
                    cmdText.Append(';');
                    rowsOfNoActionReceived.Clear();
                }

                if (cmdText.Length > 0)
                {
                    ExecuteCommand(cmdText.ToString());
                }
            }
        }
    }
}
