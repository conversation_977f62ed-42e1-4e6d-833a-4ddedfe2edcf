﻿using Calendarizador.GamesEngine.Time.Schedulers;
using GamesEngine.Business;
using GamesEngine.Domains;
using GamesEngine.Exchange;
using GamesEngine.Finance;
using GamesEngine.Gameboards.Lotto;
using GamesEngine.Location;
using GamesEngine.Time;
using Puppeteer.EventSourcing.Libraries;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GamesEngine.Games.Lotto
{
    internal abstract class LotteryGame : Objeto
    {
        private readonly Company company;

        private readonly OfficialLinks officialLinks;

        private readonly AverageTime average = new AverageTime();

        private readonly List<State> states = new List<State>();
        private readonly Dictionary<State, StringBuilder> statesLog = new Dictionary<State, StringBuilder>();

        protected LotteryGame(Company company)
        {
            if (company == null) throw new ArgumentNullException(nameof(company));

            this.company = company;
            this.officialLinks = new OfficialLinks(this);
            this.NextDatesAccumulator = new NextDatesAccumulator(this);
        }

        internal abstract RiskProfilesLotteries RiskProfiles { get; }

        internal abstract Risks Risks { get; }

        internal NextDatesAccumulator NextDatesAccumulator { get; }

        internal Company Company => this.company;

        internal EventsCalendar<Schedule> Calendar { get; } = new EventsCalendar<Schedule>();

        internal string StandardCurrency { get; set; } = "USD";
        internal int StandardCurrencyId => (int)Enum.Parse(typeof(Currencies.CODES), StandardCurrency);
        internal string StandardCurrencyUnicode => Currency.Unicode(StandardCurrency);
        internal Coin StandardCoin => Coinage.Coin(StandardCurrency);

        internal string RewardCurrency { get; set; } = "FP";
        internal int RewardCurrencyId => (int)Enum.Parse(typeof(Currencies.CODES), RewardCurrency);
        internal string RewardCurrencyUnicode => Currency.Unicode(RewardCurrency);
        internal Coin RewardCoin => Coinage.Coin(RewardCurrency);

        internal OfficialLinks OfficialLinks
        {
            get
            {
                return officialLinks;
            }
        }

        private State powerballDummyState;//TRIZ Remover este State, porque no es real
        internal State PowerballDummyState
        {
            get
            {
                if (powerballDummyState == null) powerballDummyState = new State("##", "-DummyState");
                return powerballDummyState;
            }
        }
        private State NoState;//TRIZ Remover este State, porque no es real
        internal State KenoNoState
        {
            get
            {
                if (NoState == null) NoState = new State("--", "-No_State");
                return NoState;
            }
        }

        internal abstract Lottery GetLottery(int pick, State state);

        internal abstract IEnumerable<Schedule> SchedulesBy(State state);

        internal abstract IEnumerable<Schedule> SchedulesByState(DateTime date, Domain domain);

        internal abstract IEnumerable<State> StatesWithLotteries();

        internal abstract IEnumerable<LotteryComplete> DrawsToBeConfirmed();

        protected List<WagerWithError> wagersGradedWithProblemsAboutIsValidTicket;
        internal abstract void AddWagerGradedWithProblemAboutIsValidTicket(int ticketNumber, int wagerNumber);

        internal State GetState(string abbreviation)
        {
            if (String.IsNullOrWhiteSpace(abbreviation)) throw new ArgumentNullException(nameof(abbreviation));
            abbreviation = abbreviation.ToUpper();
            foreach (State state in states)
            {
                if (state.Abbreviation == abbreviation)
                {
                    return state;
                }
            }
            throw new GameEngineException($"State abbreviation '{abbreviation}' does not exist");
        }

        [Obsolete]
        internal State GetOrCreateState(string abbreviation, string name)
        {
            if (String.IsNullOrWhiteSpace(abbreviation)) throw new ArgumentNullException(nameof(abbreviation));
            if (String.IsNullOrWhiteSpace(name)) throw new ArgumentNullException(nameof(name));

            const string DEFAULT_EMPLOYEE_NAME = "Admin";
            var DATE_OF_FRESH_EXECUTION_ON_PRODUCTION = new DateTime(2019, 04, 23);
            return GetOrCreateState(abbreviation, name, DATE_OF_FRESH_EXECUTION_ON_PRODUCTION, DEFAULT_EMPLOYEE_NAME);
        }

        internal State GetOrCreateState(string abbreviation, string name, DateTime now, string employeeName)
        {
            if (String.IsNullOrWhiteSpace(abbreviation)) throw new ArgumentNullException(nameof(abbreviation));
            if (String.IsNullOrWhiteSpace(name)) throw new ArgumentNullException(nameof(name));
            if (now == default(DateTime)) throw new GameEngineException("Date cannot have default value");
            if (String.IsNullOrWhiteSpace(employeeName)) throw new ArgumentNullException(nameof(employeeName));

            abbreviation = abbreviation.ToUpper();
            
            State newState = new State(abbreviation, name);
            return GetOrCreateState(newState, now, employeeName);
        }

        internal State GetOrCreateState(State state, DateTime now, string employeeName)
        {
            if (state == null) throw new ArgumentNullException(nameof(state));
            if (now == default(DateTime)) throw new GameEngineException("Date cannot have default value");
            if (String.IsNullOrWhiteSpace(employeeName)) throw new ArgumentNullException(nameof(employeeName));

            if (states.Contains(state))
            {
                return state;
            }

            if (states.Any(s => s.Abbreviation == state.Abbreviation)) throw new GameEngineException($"State {state.Abbreviation} is already in use. You can not create it.");

            states.Add(state);

            var log = $"{employeeName} creates it at {now}<br>";
            WriteLog(state, log);
            return state;
        }

        internal abstract bool HasDrawsFor(State state);

        internal IEnumerable<State> States() => states;

        internal void RenameState(State state, string newName, DateTime now, string employeeName)
        {
            if (String.IsNullOrWhiteSpace(newName)) throw new ArgumentNullException(nameof(newName));
            if (newName[0] != Char.ToUpper(newName[0])) throw new GameEngineException($"State name '{newName}' must start with capital case");
            if (!state.Enable) throw new GameEngineException($"State {newName} is disabled. You can not modify a disabled state.");
            if (states.Exists(x => x.Name == newName)) throw new GameEngineException($"There is another state called {newName}");
            if (now == default(DateTime)) throw new GameEngineException("Date cannot have default value");
            if (String.IsNullOrWhiteSpace(employeeName)) throw new ArgumentNullException(nameof(employeeName));

            state.Name = newName;
            var log = $"{employeeName} renames it at {now}. New name is {newName}<br>";
            WriteLog(state, log);
        }

        internal abstract IEnumerable<Schedule> PendingAndNoRegradedSchedulesAt(DateTime date);

        internal abstract IEnumerable<Schedule> FinishedAndRegradedSchedulesOf(DateTime date);

        internal void RecordElapsedTimeToDraw(TimeSpan newTime)
        {
            this.average.Record(newTime);
        }

        internal void RemoveNumbersTracker(int pickNumber, Lottery lottery, DateTime drawDate)
        {
            this.Risks.ForgetNumbersTracker(pickNumber, lottery, drawDate);
        }

        internal void RemoveDisposedTickets()
        {
            this.company.RemoveDisposedGameboards();
        }

        const int SevenDaysInHours = 168;
        internal virtual IEnumerable<Schedule> SchedulesByTimeOf(DateTime now, Domain domain)
        {
            if (now == default(DateTime)) throw new GameEngineException("Date cannot have default value");
            if (domain == null) throw new ArgumentNullException(nameof(domain));

            var result = new HashSet<Schedule>();
            var schedules = Calendar.SearchEvents(now, now, new TimeSpan(SevenDaysInHours, 0, 0));

            foreach (var schedule in schedules)
            {
                var drawDate = schedule.NextValidDraw(now);
                var lottery = schedule.Lottery;
                if (lottery.IsEnabledDraw(drawDate, domain))
                {
                    result.Add(schedule);
                }
            }

            return result.ToList();
        }

        internal abstract bool AnyScheduleHasChanged(IEnumerable<int> picks, IEnumerable<string> statesAbb, IEnumerable<string> hours, IEnumerable<string> withFireBalls);

        protected void WriteLog(State state, string message)
        {
            if (state == null) throw new ArgumentNullException(nameof(state));
            if (String.IsNullOrWhiteSpace(message)) throw new ArgumentNullException(nameof(message));

            if (statesLog.ContainsKey(state))
            {
                statesLog[state].Insert(0, message);
            }
            else
            {
                var messageBuilder = new StringBuilder(message);
                statesLog.Add(state, messageBuilder);
            }
        }

        internal string LogFor(State state)
        {
            if (state == null) throw new ArgumentNullException(nameof(state));
            var log = statesLog.ContainsKey(state) ? statesLog[state].ToString() : string.Empty;
            return log;
        }

        internal void RemoveState(State state)
        {
            if (state == null) throw new ArgumentNullException(nameof(state));
            if (!states.Contains(state)) throw new GameEngineException($"State {state.Abbreviation} is already remove it.");

            states.Remove(state);
        }

        internal abstract bool TryGetScheduleByUniqueId(int uniqueDrawingId, out Schedule result);
        internal abstract bool ExistsInternalSchedule(int uniqueDrawingId);

        class AverageTime
        {
            private int times = 0;
            private TimeSpan average;

            internal void Record(TimeSpan newTime)
            {
                average = average.Add(newTime);
                times++;
            }

            internal TimeSpan Average()
            {
                var result = new TimeSpan();
                if (times == 0)
                {
                    return result;
                }
                else
                {
                    result = average.Divide((double)times);
                }
                return result;
            }
        }
    }
}
