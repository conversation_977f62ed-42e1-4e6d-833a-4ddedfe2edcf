﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using System.Text;
using System.Threading.Tasks;
using GamesEngine.Games;
using GamesEngine.Middleware;
using GamesEngine.Middleware.Providers;
using LinesETLAPI;
using LinesETLAPI.Controllers;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;

namespace LinesETLAPI.Controllers
{
	public class SportsController : AuthorizeController
	{
		[HttpGet("api/sports")]
        [Authorize(Roles = "devops")]//VERIFY_ROLES
        public async Task<IActionResult> GetSportsAsync()
		{
			var resultQry = await LinesETLAPI.LinesETL.PerformQryAsync(HttpContext, $@"
				{{
					sportList = mapper.Sports;
					for(sports : sportList.GetAll)
					{{
						hasAnyAlias = sports.Aliases.HasAnyForeingAlias();
						print sports.Id sportId;
						print sports.Name sportName;
						print hasAnyAlias hasAnyAlias;

						if(hasAnyAlias)
						{{
							for(aliases : sports.Aliases.GetAllForeingAlias())
							{{
								print aliases.Name aliasName;
								print aliases.Id aliasId;
								print aliases.Provider.Id providerId;
							}}
						}}
					}}
				}}
				");
			return resultQry;
		}

		[HttpGet("api/sports/aliases")]
        [Authorize(Roles = "devops")]//VERIFY_ROLES
        public async Task<IActionResult> EnabledAliasForSportsAsync(int providerId, bool enabled)
		{
			if (!enabled) return BadRequest($"{nameof(enabled)} in false is not implemented");

			Provider provider = ProvidersCollection.Instance().ById(providerId);
			var resultQry = await LinesETLAPI.LinesETL.PerformQryAsync(HttpContext, $@"
				{{
					sportList = mapper.EnabledSports;
					for(sportAliases : sportList.AliasForProvider({provider}))
					{{
						print sportAliases.Id aliasId;
						print sportAliases.Name aliasName;
						print sportAliases.Sport.Id sportId;
						print sportAliases.Sport.Name sportName;
					}}
				}}
				");
			return resultQry;
		}
	}
}
