#See https://aka.ms/containerfastmode to understand how Visual Studio uses this Dockerfile to build your images for faster debugging.

FROM mcr.microsoft.com/dotnet/aspnet:5.0 AS base
RUN  rm -rf /etc/localtime
RUN ln -s /usr/share/zoneinfo/America/New_York /etc/localtime
RUN apt-get update -y
RUN apt-get install netcat -y 
WORKDIR /app
EXPOSE 80

FROM mcr.microsoft.com/dotnet/sdk:5.0 AS build
WORKDIR /src/ncubo
COPY GuardianAPI/GuardianAPI.csproj GuardianAPI/
COPY GamesEngineMocks/GamesEngineMocks.csproj GamesEngineMocks/
COPY Connectors/Connectors.csproj Connectors/
COPY GamesEngine/GamesEngine.csproj GamesEngine/
COPY ExternalServices/ExternalServices.csproj ExternalServices/
COPY Puppeteer/Puppeteer.csproj Puppeteer/
RUN dotnet restore "/src/ncubo/GuardianAPI/GuardianAPI.csproj"
COPY . .

FROM build AS publish
WORKDIR "/src/ncubo/GuardianAPI"
CMD ls
RUN dotnet publish "GuardianAPI.csproj" -c Release -o /app/publish

FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .
ENTRYPOINT ["dotnet", "GuardianAPI.dll"]

#docker build -t localhost:32000/livebettingln:registry -f GuardianAPI/Dockerfile .