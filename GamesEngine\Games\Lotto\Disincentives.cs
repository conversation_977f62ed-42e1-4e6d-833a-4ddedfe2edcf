﻿using GamesEngine.Gameboards.Lotto;
using GamesEngine.Location;
using GamesEngine.Time;
using Nest;
using Puppeteer.EventSourcing.Libraries;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using static GamesEngine.Games.Lotto.Disincentives;

namespace GamesEngine.Games.Lotto
{
    internal class Disincentives : Objeto
    {
        private readonly DisincentivesByGameType disincentivesPick2 = new DisincentivesByGameType(2);
        private readonly DisincentivesByGameType disincentivesPick3 = new DisincentivesByGameType(3);
        private readonly DisincentivesByGameType disincentivesPick4 = new DisincentivesByGameType(4);
        private readonly DisincentivesByGameType disincentivesPick5 = new DisincentivesByGameType(5);

        DisincentivesByGameType SelectDisincentivesByGameType(int pickNumber)
        {
            DisincentivesByGameType disincentives;
            switch (pickNumber)
            {
                case 2:
                    disincentives = disincentivesPick2;
                    break;
                case 3:
                    disincentives = disincentivesPick3;
                    break;
                case 4:
                    disincentives = disincentivesPick4;
                    break;
                case 5:
                    disincentives = disincentivesPick5;
                    break;
                default:
                    throw new GameEngineException($"There is no {nameof(Disincentives)} implementation for numbers with length '{pickNumber}'");
            }
            return disincentives;
        }

        internal static PrizeCriteriaIds PrizeCriteriaIdBasedOn(int pick, int prizeCriteria)
        {
            switch (pick)
            {
                case 2:
                    switch (prizeCriteria)
                    {
                        case PrizesPicks.PRIZE_CRITERIA:
                            return PrizeCriteriaIds.PRIZE_CRITERIA_ALL_NUMBERS_ARE_THE_SAME;
                        case PrizesPicks.PRIZE_CRITERIA_2_WAY:
                            return PrizeCriteriaIds.PRIZE_CRITERIA_2_WAY;
                        default:
                            throw new GameEngineException($"No implementation for {nameof(prizeCriteria)} {prizeCriteria}");
                    }
                case 3:
                    switch (prizeCriteria)
                    {
                        case PrizesPicks.PRIZE_CRITERIA:
                            return PrizeCriteriaIds.PRIZE_CRITERIA_ALL_NUMBERS_ARE_THE_SAME;
                        case PrizesPicks.PRIZE_CRITERIA_3_WAY:
                            return PrizeCriteriaIds.PRIZE_CRITERIA_3_WAY;
                        case PrizesPicks.PRIZE_CRITERIA_6_WAY:
                            return PrizeCriteriaIds.PRIZE_CRITERIA_6_WAY;
                        default:
                            throw new GameEngineException($"No implementation for {nameof(prizeCriteria)} {prizeCriteria}");
                    }
                case 4:
                    switch (prizeCriteria)
                    {
                        case PrizesPicks.PRIZE_CRITERIA:
                            return PrizeCriteriaIds.PRIZE_CRITERIA_ALL_NUMBERS_ARE_THE_SAME;
                        case PrizesPicks.PRIZE_CRITERIA_4_WAY:
                            return PrizeCriteriaIds.PRIZE_CRITERIA_4_WAY;
                        case PrizesPicks.PRIZE_CRITERIA_6_WAY:
                            return PrizeCriteriaIds.PRIZE_CRITERIA_6_WAY;
                        case PrizesPicks.PRIZE_CRITERIA_12_WAY:
                            return PrizeCriteriaIds.PRIZE_CRITERIA_12_WAY;
                        case PrizesPicks.PRIZE_CRITERIA_24_WAY:
                            return PrizeCriteriaIds.PRIZE_CRITERIA_24_WAY;
                        default:
                            throw new GameEngineException($"No implementation for {nameof(prizeCriteria)} {prizeCriteria}");
                    }
                case 5:
                    switch (prizeCriteria)
                    {
                        case PrizesPicks.PRIZE_CRITERIA:
                            return PrizeCriteriaIds.PRIZE_CRITERIA_ALL_NUMBERS_ARE_THE_SAME;
                        case PrizesPicks.PRIZE_CRITERIA_5_WAY:
                            return PrizeCriteriaIds.PRIZE_CRITERIA_5_WAY;
                        case PrizesPicks.PRIZE_CRITERIA_10_WAY:
                            return PrizeCriteriaIds.PRIZE_CRITERIA_10_WAY;
                        case PrizesPicks.PRIZE_CRITERIA_20_WAY:
                            return PrizeCriteriaIds.PRIZE_CRITERIA_20_WAY;
                        case PrizesPicks.PRIZE_CRITERIA_30_WAY:
                            return PrizeCriteriaIds.PRIZE_CRITERIA_30_WAY;
                        case PrizesPicks.PRIZE_CRITERIA_60_WAY:
                            return PrizeCriteriaIds.PRIZE_CRITERIA_60_WAY;
                        case PrizesPicks.PRIZE_CRITERIA_120_WAY:
                            return PrizeCriteriaIds.PRIZE_CRITERIA_120_WAY;
                        default:
                            throw new GameEngineException($"No implementation for {nameof(prizeCriteria)} {prizeCriteria}");
                    }
                default:
                    throw new GameEngineException($"No implementation for {nameof(pick)} {pick}");
            }
        }

        internal IEnumerable<string> PrizeCriteriaNames(int pick)
        {
            switch (pick)
            {
                case 2:
                    return new string[] { PrizeCriteriaName(pick, 0), PrizeCriteriaName(pick, 1) };
                case 3:
                    return new string[] { PrizeCriteriaName(pick, 0), PrizeCriteriaName(pick, 1), PrizeCriteriaName(pick, 2) };
                case 4:
                    return new string[] { PrizeCriteriaName(pick, 0), PrizeCriteriaName(pick, 1), PrizeCriteriaName(pick, 2), PrizeCriteriaName(pick, 3), PrizeCriteriaName(pick, 4) };
                case 5:
                    return new string[] { PrizeCriteriaName(pick, 0), PrizeCriteriaName(pick, 1), PrizeCriteriaName(pick, 2), PrizeCriteriaName(pick, 3), PrizeCriteriaName(pick, 4), PrizeCriteriaName(pick, 5), PrizeCriteriaName(pick, 6) };
                default:
                    throw new GameEngineException($"No implementation for {nameof(pick)} {pick}");
            }
        }

        public static string PrizeCriteriaName(int pick, int prizeCriteria)
        {
            switch (pick)
            {
                case 2:
                    switch (prizeCriteria)
                    {
                        case PrizesPicks.PRIZE_CRITERIA:
                            return "Straight";
                        case PrizesPicks.PRIZE_CRITERIA_2_WAY:
                            return "2-way";
                        default:
                            throw new GameEngineException($"No implementation for {nameof(prizeCriteria)} {prizeCriteria}");
                    }
                case 3:
                    switch (prizeCriteria)
                    {
                        case PrizesPicks.PRIZE_CRITERIA:
                            return "Straight";
                        case PrizesPicks.PRIZE_CRITERIA_3_WAY:
                            return "3-way";
                        case PrizesPicks.PRIZE_CRITERIA_6_WAY:
                            return "6-way";
                        default:
                            throw new GameEngineException($"No implementation for {nameof(prizeCriteria)} {prizeCriteria}");
                    }
                case 4:
                    switch (prizeCriteria)
                    {
                        case PrizesPicks.PRIZE_CRITERIA:
                            return "Straight";
                        case PrizesPicks.PRIZE_CRITERIA_4_WAY:
                            return "4-way";
                        case PrizesPicks.PRIZE_CRITERIA_6_WAY:
                            return "6-way";
                        case PrizesPicks.PRIZE_CRITERIA_12_WAY:
                            return "12-way";
                        case PrizesPicks.PRIZE_CRITERIA_24_WAY:
                            return "24-way";
                        default:
                            throw new GameEngineException($"No implementation for {nameof(prizeCriteria)} {prizeCriteria}");
                    }
                case 5:
                    switch (prizeCriteria)
                    {
                        case PrizesPicks.PRIZE_CRITERIA:
                            return "Straight";
                        case PrizesPicks.PRIZE_CRITERIA_5_WAY:
                            return "5-way";
                        case PrizesPicks.PRIZE_CRITERIA_10_WAY:
                            return "10-way";
                        case PrizesPicks.PRIZE_CRITERIA_20_WAY:
                            return "20-way";
                        case PrizesPicks.PRIZE_CRITERIA_30_WAY:
                            return "30-way";
                        case PrizesPicks.PRIZE_CRITERIA_60_WAY:
                            return "60-way";
                        case PrizesPicks.PRIZE_CRITERIA_120_WAY:
                            return "120-way";
                        default:
                            throw new GameEngineException($"No implementation for {nameof(prizeCriteria)} {prizeCriteria}");
                    }
                default:
                    throw new GameEngineException($"No implementation for {nameof(pick)} {pick}");
            }
        }

        internal static PrizeCriteriaIds WayOfSubticket(TicketType type, SubTicket<IPick> subTicket)
        {
            int prizeCriteria;
            switch (type)
            {
                case TicketType.P2S:
                case TicketType.P2B:
                    prizeCriteria = PrizesPicks.WayOfSubticket(subTicket[1], subTicket[2]);
                    break;
                case TicketType.P3S:
                case TicketType.P3B:
                    prizeCriteria = PrizesPicks.WayOfSubticket(subTicket[1], subTicket[2], subTicket[3]);
                    break;
                case TicketType.P4S:
                case TicketType.P4B:
                    prizeCriteria = PrizesPicks.WayOfSubticket(subTicket[1], subTicket[2], subTicket[3], subTicket[4]);
                    break;
                case TicketType.P5S:
                case TicketType.P5B:
                    prizeCriteria = PrizesPicks.WayOfSubticket(subTicket[1], subTicket[2], subTicket[3], subTicket[4], subTicket[5]);
                    break;
                default:
                    throw new GameEngineException($"The type {type} does not belong to any ticket.");
            }
            return PrizeCriteriaIdBasedOn(subTicket.Length, prizeCriteria);
        }

        [Obsolete]
        internal Disincentives Add(string subticketNumber, int salesQuantity)
        {
            return this;
        }

        internal decimal ApplyTo(Schedule schedule, PrizeCriteriaIds prizeCriteriaId, RuleType ruleType, int pickNumber, decimal maxToWin)
        {
            if (maxToWin <= 0) throw new GameEngineException($"{nameof(maxToWin)} '{maxToWin}' must be greater than 0");

            var disincentives = SelectDisincentivesByGameType(pickNumber);
            var disincentivePercentage = disincentives.DisincentivePercentage(schedule, prizeCriteriaId, ruleType);
            return maxToWin * disincentivePercentage;
        }

        internal void Calculate()
        {
            if (!disincentivesPick2.IsEmpty()) disincentivesPick2.Calculate();
            if (!disincentivesPick3.IsEmpty()) disincentivesPick3.Calculate();
            if (!disincentivesPick4.IsEmpty()) disincentivesPick4.Calculate();
            if (!disincentivesPick5.IsEmpty()) disincentivesPick5.Calculate();
        }

        internal void CloneFrom(Disincentives disincentives)
        {
            if (disincentives == null) throw new ArgumentNullException(nameof(disincentives));

            disincentivesPick2.CloneFrom(disincentives.disincentivesPick2);
            disincentivesPick3.CloneFrom(disincentives.disincentivesPick3);
            disincentivesPick4.CloneFrom(disincentives.disincentivesPick4);
            disincentivesPick5.CloneFrom(disincentives.disincentivesPick5);
        }

        internal class DisincentivesByGameType
        {
            private readonly Dictionary<Schedule, DisincentivePerSchedule> disincentivesPerSchedule = new Dictionary<Schedule, DisincentivePerSchedule>();
            internal long TotalSalesQuantity { get; private set; }

            private int pickNumber;
            public DisincentivesByGameType(int pickNumber)
            {
                this.pickNumber = pickNumber;
            }

            internal decimal DisincentivePercentage(Schedule schedule, PrizeCriteriaIds prizeCriteriaId, RuleType ruleType)
            {
                if (schedule.Lottery.PickNumber != pickNumber) throw new GameEngineException($"Schedule {schedule.GetDescription()} does not belong to pick number {pickNumber}");
                var disincentive = disincentivesPerSchedule.GetValueOrDefault(schedule);
                if (disincentive == null) return 1;
                return disincentive.DisincentivePercentage(schedule, prizeCriteriaId, ruleType);
            }

            internal IEnumerable<DisincentiveByWay> AllDisincentives()
            {
                var result = disincentivesPerSchedule.Count == 0 ? Enumerable.Empty<DisincentiveByWay>() : disincentivesPerSchedule.Values.SelectMany(x=>x.AllDisincentives());
                return result;
            }

            internal void DeleteDisincentive(Schedule schedule, PrizeCriteriaIds prizeCriteriaId, RuleType ruleType)
            {
                var disincentive = disincentivesPerSchedule.GetValueOrDefault(schedule);
                if (disincentive == null) throw new GameEngineException($"Disincentive for schedule {schedule.GetDescription()} does not exist");
                disincentive.DeleteDisincentive(schedule, prizeCriteriaId, ruleType);
            }

            internal void NewDisincentive(Schedule schedule, PrizeCriteriaIds prizeCriteriaId, RuleType ruleType, decimal disincentivePercentage)
            {
                var disincentive = disincentivesPerSchedule.GetValueOrDefault(schedule);
                if (disincentive == null)
                {
                    disincentive = new DisincentivePerSchedule(schedule);
                    disincentivesPerSchedule.Add(schedule, disincentive);
                }
                disincentive.NewDisincentive(schedule, prizeCriteriaId, ruleType, disincentivePercentage);
            }

            internal void UpdateDisincentive(Schedule schedule, PrizeCriteriaIds prizeCriteriaId, RuleType ruleType, decimal disincentivePercentage)
            {
                var disincentive = disincentivesPerSchedule.GetValueOrDefault(schedule);
                if (disincentive == null) throw new GameEngineException($"Disincentive for schedule {schedule.GetDescription()} does not exist");
                disincentive.UpdateDisincentive(schedule, prizeCriteriaId, ruleType, disincentivePercentage);
            }

            internal bool HasDisincentive(Schedule schedule, PrizeCriteriaIds prizeCriteriaId, RuleType ruleType)
            {
                if (disincentivesPerSchedule.ContainsKey(schedule))
                {
                    var disincentive = disincentivesPerSchedule[schedule];
                    return disincentive.HasDisincentive(schedule, prizeCriteriaId, ruleType);
                }
                return false;
            }

            internal void Calculate()
            {
            }

            internal bool IsEmpty()
            {
                return disincentivesPerSchedule.Count == 0;
            }

            internal void CloneFrom(DisincentivesByGameType disincentivesByGameType)
            {
                if (disincentivesByGameType == null) throw new ArgumentNullException(nameof(disincentivesByGameType));

                disincentivesPerSchedule.Clear();
                foreach (var disincentive in disincentivesByGameType.disincentivesPerSchedule)
                {
                    var newDisincentive = new DisincentivePerSchedule(disincentive.Key);
                    disincentivesPerSchedule.Add(disincentive.Key, newDisincentive);
                    newDisincentive.CloneFrom(disincentive.Value);
                }
                TotalSalesQuantity = disincentivesByGameType.TotalSalesQuantity;
            }
        }

        internal class DisincentivePerSchedule : Objeto
        {
            internal List<DisincentiveByWay> DisincentiveByWayBoxed { get; } = new List<DisincentiveByWay>();
            internal List<DisincentiveByWay> DisincentiveByWayStraight { get; } = new List<DisincentiveByWay>();

            private Schedule schedule;
            public DisincentivePerSchedule(Schedule schedule)
            {
                this.schedule = schedule;
            }
            internal IEnumerable<DisincentiveByWay> AllDisincentives()
            {
                var result = DisincentiveByWayBoxed.Count == 0 && DisincentiveByWayStraight.Count == 0 ? Enumerable.Empty<DisincentiveByWay>() : DisincentiveByWayBoxed.Concat(DisincentiveByWayStraight);
                return result;
            }

            internal decimal DisincentivePercentage(Schedule schedule, PrizeCriteriaIds prizeCriteriaId, RuleType ruleType)
            {
                if (this.schedule != schedule) throw new GameEngineException($"Schedule {schedule.GetDescription()} does not belong to {this.schedule.GetDescription()}");
                if (ruleType == RuleType.Straight)
                {
                    foreach (var disincentive in DisincentiveByWayStraight)
                    {
                        if (disincentive.Schedule == schedule && disincentive.PrizeCriteriaId == prizeCriteriaId)
                        {
                            return disincentive.DisincentivePercentage;
                        }
                    }
                }
                else
                {
                    foreach (var disincentive in DisincentiveByWayBoxed)
                    {
                        if (disincentive.Schedule == schedule && disincentive.PrizeCriteriaId == prizeCriteriaId)
                        {
                            return disincentive.DisincentivePercentage;
                        }
                    }
                }
                return 1;
            }

            internal void NewDisincentive(Schedule schedule, PrizeCriteriaIds prizeCriteriaId, RuleType ruleType, decimal disincentivePercentage)
            {
                if (this.schedule != schedule) throw new GameEngineException($"Schedule {schedule.GetDescription()} does not belong to {this.schedule.GetDescription()}");
                var disincentive = new DisincentiveByWay(schedule, prizeCriteriaId, ruleType, disincentivePercentage);
                if (ruleType == RuleType.Straight)
                {
                    DisincentiveByWayStraight.Add(disincentive);
                }
                else
                {
                    DisincentiveByWayBoxed.Add(disincentive);
                }
            }

            internal void UpdateDisincentive(Schedule schedule, PrizeCriteriaIds prizeCriteriaId, RuleType ruleType, decimal disincentivePercentage)
            {
                if (this.schedule != schedule) throw new GameEngineException($"Schedule {schedule.GetDescription()} does not belong to {this.schedule.GetDescription()}");
                if (ruleType == RuleType.Straight)
                {
                    for (int i = 0; i < DisincentiveByWayStraight.Count; i++)
                    {
                        if (DisincentiveByWayStraight[i].Schedule == schedule && DisincentiveByWayStraight[i].PrizeCriteriaId == prizeCriteriaId)
                        {
                            DisincentiveByWayStraight[i].DisincentivePercentage = disincentivePercentage;
                            return;
                        }
                    }
                }
                else
                {
                    for (int i = 0; i < DisincentiveByWayBoxed.Count; i++)
                    {
                        if (DisincentiveByWayBoxed[i].Schedule == schedule && DisincentiveByWayBoxed[i].PrizeCriteriaId == prizeCriteriaId)
                        {
                            DisincentiveByWayBoxed[i].DisincentivePercentage = disincentivePercentage;
                            return;
                        }
                    }
                }
                throw new GameEngineException("This disincentive does not exist");
            }

            internal void DeleteDisincentive(Schedule schedule, PrizeCriteriaIds prizeCriteriaId, RuleType ruleType)
            {
                if (this.schedule != schedule) throw new GameEngineException($"Schedule {schedule.GetDescription()} does not belong to {this.schedule.GetDescription()}");
                if (ruleType == RuleType.Straight)
                {
                    for (int i = 0; i < DisincentiveByWayStraight.Count; i++)
                    {
                        if (DisincentiveByWayStraight[i].Schedule == schedule && DisincentiveByWayStraight[i].PrizeCriteriaId == prizeCriteriaId)
                        {
                            DisincentiveByWayStraight.RemoveAt(i);
                            return;
                        }
                    }
                }
                else
                {
                    for (int i = 0; i < DisincentiveByWayBoxed.Count; i++)
                    {
                        if (DisincentiveByWayBoxed[i].Schedule == schedule && DisincentiveByWayBoxed[i].PrizeCriteriaId == prizeCriteriaId)
                        {
                            DisincentiveByWayBoxed.RemoveAt(i);
                            return;
                        }
                    }
                }
                throw new GameEngineException("This disincentive does not exist");
            }

            internal bool HasDisincentive(Schedule schedule, PrizeCriteriaIds prizeCriteriaId, RuleType ruleType)
            {
                if (ruleType == RuleType.Straight)
                {
                    return DisincentiveByWayStraight.Any(x => x.Schedule == schedule && x.PrizeCriteriaId == prizeCriteriaId);
                }
                return DisincentiveByWayBoxed.Any(x => x.Schedule == schedule && x.PrizeCriteriaId == prizeCriteriaId);
            }

            internal void CloneFrom(DisincentivePerSchedule disincentivePerSchedule)
            {
                if (disincentivePerSchedule == null) throw new ArgumentNullException(nameof(disincentivePerSchedule));

                DisincentiveByWayStraight.Clear();
                foreach (var disincentive in disincentivePerSchedule.DisincentiveByWayStraight)
                {
                    DisincentiveByWayStraight.Add(new DisincentiveByWay(disincentive.Schedule, disincentive.PrizeCriteriaId, disincentive.RuleType, disincentive.DisincentivePercentage));
                }

                DisincentiveByWayBoxed.Clear();
                foreach (var disincentive in disincentivePerSchedule.DisincentiveByWayBoxed)
                {
                    DisincentiveByWayBoxed.Add(new DisincentiveByWay(disincentive.Schedule, disincentive.PrizeCriteriaId, disincentive.RuleType, disincentive.DisincentivePercentage));
                }
            }
        }

        internal class DisincentiveByWay : Objeto
        {
            internal int PickNumber => Schedule.Lottery.PickNumber;
            internal State State => Schedule.Lottery.State;
            internal Schedule Schedule { get; }
            internal PrizeCriteriaIds PrizeCriteriaId { get; }
            internal RuleType RuleType { get; }
            internal string RuleTypeAsText => RuleType.ToString();
            internal decimal DisincentivePercentage { get; set; }

            public DisincentiveByWay(Schedule schedule, PrizeCriteriaIds prizeCriteriaId, RuleType ruleType, decimal disincentivePercentage)
            {
                Schedule = schedule;
                PrizeCriteriaId = prizeCriteriaId;
                RuleType = ruleType;
                DisincentivePercentage = disincentivePercentage;
            }

            internal string PrizeCriteriaName()
            {
                switch(PrizeCriteriaId)
                {
                    case PrizeCriteriaIds.PRIZE_CRITERIA_ALL_NUMBERS_ARE_THE_SAME:
                        return "Straight";
                    case PrizeCriteriaIds.PRIZE_CRITERIA_2_WAY:
                        return "2-way";
                    case PrizeCriteriaIds.PRIZE_CRITERIA_3_WAY:
                        return "3-way";
                    case PrizeCriteriaIds.PRIZE_CRITERIA_6_WAY:
                        return "6-way";
                    case PrizeCriteriaIds.PRIZE_CRITERIA_4_WAY:
                        return "4-way";
                    case PrizeCriteriaIds.PRIZE_CRITERIA_12_WAY:
                        return "12-way";
                    case PrizeCriteriaIds.PRIZE_CRITERIA_24_WAY:
                        return "24-way";
                    case PrizeCriteriaIds.PRIZE_CRITERIA_5_WAY:
                        return "5-way";
                    case PrizeCriteriaIds.PRIZE_CRITERIA_10_WAY:
                        return "10-way";
                    case PrizeCriteriaIds.PRIZE_CRITERIA_20_WAY:
                        return "20-way";
                    case PrizeCriteriaIds.PRIZE_CRITERIA_30_WAY:
                        return "30-way";
                    case PrizeCriteriaIds.PRIZE_CRITERIA_60_WAY:
                        return "60-way";
                    case PrizeCriteriaIds.PRIZE_CRITERIA_120_WAY:
                        return "120-way";
                    default:
                        throw new GameEngineException($"There is no implementation for {nameof(PrizeCriteriaId)} {PrizeCriteriaId}.");
                }
            }
        }

        public enum PrizeCriteriaIds
        {
            PRIZE_CRITERIA_ALL_NUMBERS_ARE_THE_SAME,
            PRIZE_CRITERIA_2_WAY,
            PRIZE_CRITERIA_3_WAY,
            PRIZE_CRITERIA_6_WAY,
            PRIZE_CRITERIA_4_WAY,
            PRIZE_CRITERIA_12_WAY,
            PRIZE_CRITERIA_24_WAY,
            PRIZE_CRITERIA_5_WAY,
            PRIZE_CRITERIA_10_WAY,
            PRIZE_CRITERIA_20_WAY,
            PRIZE_CRITERIA_30_WAY,
            PRIZE_CRITERIA_60_WAY,
            PRIZE_CRITERIA_120_WAY
        }

        internal IEnumerable<DisincentiveByWay> AllDisincentives(int pick)
        {
            if (pick < 2 || pick > 5) throw new GameEngineException($"{nameof(pick)} {pick} is not valid");

            switch (pick)
            {
                case 2:
                    var disincentives = disincentivesPick2.AllDisincentives();
                    return disincentives.ToList();
                case 3:
                    disincentives = disincentivesPick3.AllDisincentives();
                    return disincentives.ToList();
                case 4:
                    disincentives = disincentivesPick4.AllDisincentives();
                    return disincentives.ToList();
                case 5:
                    disincentives = disincentivesPick5.AllDisincentives();
                    return disincentives.ToList();
                default:
                    throw new GameEngineException($"There is no implementation for {nameof(pick)} {pick}.");
            }
        }

        [Obsolete]
        internal void DeleteDisincentive(int pick, string number)
        {
        }

        internal void DeleteDisincentive(Schedule schedule, PrizeCriteriaIds prizeCriteriaId, RuleType ruleType)
        {
            if (schedule == null) throw new ArgumentNullException(nameof(schedule));

            switch (schedule.Lottery.PickNumber)
            {
                case 2:
                    disincentivesPick2.DeleteDisincentive(schedule, prizeCriteriaId, ruleType);
                    break;
                case 3:
                    disincentivesPick3.DeleteDisincentive(schedule, prizeCriteriaId, ruleType);
                    break;
                case 4:
                    disincentivesPick4.DeleteDisincentive(schedule, prizeCriteriaId, ruleType);
                    break;
                case 5:
                    disincentivesPick5.DeleteDisincentive(schedule, prizeCriteriaId, ruleType);
                    break;
                default:
                    throw new GameEngineException($"There is no implementation for {nameof(schedule.Lottery.PickNumber)} {schedule.Lottery.PickNumber}.");
            }
        }

        [Obsolete]
        internal void NewDisicentive(int pick, string number, int maxPercentageOfLimitToSale)
        {
        }

        internal void NewDisincentive(Schedule schedule, PrizeCriteriaIds prizeCriteriaId, RuleType ruleType, int maxPercentageOfLimitToSale)
        {
            if (schedule == null) throw new ArgumentNullException(nameof(schedule));
            if (maxPercentageOfLimitToSale < 0 || maxPercentageOfLimitToSale > 100) throw new GameEngineException($"Percentage {maxPercentageOfLimitToSale} is not valid");
            if (HasDisincentive(schedule, prizeCriteriaId, ruleType)) throw new GameEngineException($"Disincentive for {nameof(schedule.Lottery.PickNumber)} {schedule.Lottery.PickNumber}, {nameof(schedule.UniqueId)} {schedule.UniqueId} and {nameof(prizeCriteriaId)} {prizeCriteriaId} already exist");

            var disincentivePercentage = maxPercentageOfLimitToSale / 100.00m;
            switch (schedule.Lottery.PickNumber)
            {
                case 2:
                    disincentivesPick2.NewDisincentive(schedule, prizeCriteriaId, ruleType, disincentivePercentage);
                    break;
                case 3:
                    disincentivesPick3.NewDisincentive(schedule, prizeCriteriaId, ruleType, disincentivePercentage);
                    break;
                case 4:
                    disincentivesPick4.NewDisincentive(schedule, prizeCriteriaId, ruleType, disincentivePercentage);
                    break;
                case 5:
                    disincentivesPick5.NewDisincentive(schedule, prizeCriteriaId, ruleType, disincentivePercentage);
                    break;
                default:
                    throw new GameEngineException($"There is no implementation for {nameof(schedule.Lottery.PickNumber)} {schedule.Lottery.PickNumber}.");
            }
        }

        [Obsolete]
        internal void UpdateDisincentive(int pick, string number, decimal disincentive)
        {

        }

        internal void UpdateDisincentive(Schedule schedule, PrizeCriteriaIds prizeCriteriaId, RuleType ruleType, decimal disincentive)
        {
            if (schedule == null) throw new ArgumentNullException(nameof(schedule));
            if (disincentive < 0 || disincentive > 100) throw new GameEngineException($"Percentage {disincentive} is not valid");
            if (!HasDisincentive(schedule, prizeCriteriaId, ruleType)) throw new GameEngineException($"Disincentive for {nameof(schedule.Lottery.PickNumber)} {schedule.Lottery.PickNumber}, {nameof(schedule.UniqueId)} {schedule.UniqueId} and {nameof(prizeCriteriaId)} {prizeCriteriaId} does not exist");

            var disincentivePercentage = disincentive / 100;
            switch (schedule.Lottery.PickNumber)
            {
                case 2:
                    disincentivesPick2.UpdateDisincentive(schedule, prizeCriteriaId, ruleType, disincentivePercentage);
                    break;
                case 3:
                    disincentivesPick3.UpdateDisincentive(schedule, prizeCriteriaId, ruleType, disincentivePercentage);
                    break;
                case 4:
                    disincentivesPick4.UpdateDisincentive(schedule, prizeCriteriaId, ruleType, disincentivePercentage);
                    break;
                case 5:
                    disincentivesPick5.UpdateDisincentive(schedule, prizeCriteriaId, ruleType, disincentivePercentage);
                    break;
                default:
                    throw new GameEngineException($"There is no implementation for {nameof(schedule.Lottery.PickNumber)} {schedule.Lottery.PickNumber}.");
            }
        }

        internal bool HasDisincentive(Schedule schedule, PrizeCriteriaIds prizeCriteriaId, RuleType ruleType)
        {
            if (schedule == null) throw new ArgumentNullException(nameof(schedule));

            switch (schedule.Lottery.PickNumber)
            {
                case 2:
                    return disincentivesPick2.HasDisincentive(schedule, prizeCriteriaId, ruleType);
                case 3:
                    return disincentivesPick3.HasDisincentive(schedule, prizeCriteriaId, ruleType);
                case 4:
                    return disincentivesPick4.HasDisincentive(schedule, prizeCriteriaId, ruleType);
                case 5:
                    return disincentivesPick5.HasDisincentive(schedule, prizeCriteriaId, ruleType);
                default:
                    throw new GameEngineException($"There is no implementation for {nameof(schedule.Lottery.PickNumber)} {schedule.Lottery.PickNumber}.");
            }
        }
    }
    public enum RuleType
    {
        Straight,
        Boxed,
    }
}