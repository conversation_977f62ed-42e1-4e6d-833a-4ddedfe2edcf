﻿using GamesEngine.Settings;
using MySql.Data.MySqlClient;
using Puppeteer;
using Puppeteer.EventSourcing;
using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Linq;
using System.Reflection;
using System.Text;

namespace LottoAPI.LottoFollower
{
	public class ContadorDeTicketsPorNumeroCompradores
	{
		private Dictionary<string, int> contadorTickets = new Dictionary<string, int>();

		internal void IncrementarCantidad(string cantidadTickets, int cantidad)
		{
			if (!contadorTickets.ContainsKey(cantidadTickets))
			{
				contadorTickets.Add(cantidadTickets, cantidad);
			}
			else
			{
				int valor = contadorTickets[cantidadTickets];
				contadorTickets[cantidadTickets] = valor + cantidad;
			}
		}

		internal IEnumerable<KeyValuePair<string, int>> Conteos()
		{
			return this.contadorTickets;
		}
	}

	public class CantidadCompradoresSegunNumeroDeTicketsRule : RuleWithoutActor
	{
		private const string TABLE_ACUMULADOS = "CantidadDeCompradoresSegunNumeroTickets";
		private const string TABLE_CORTES = "Cortes";
		private ContadorDeTicketsPorNumeroCompradores contador = new ContadorDeTicketsPorNumeroCompradores();
		private int inicial;
		private int final;
		private bool sqlServer = true;

		public CantidadCompradoresSegunNumeroDeTicketsRule()
		{
		}

		public override void Then(Script script)
		{
		}

		//private const string CONNECTION_STRING = "persistsecurityinfo=True;port=3306;Server=localhost;Database=lotto_statistics;user id=admin;password=***456;SslMode=none";
		private const string CONNECTION_STRING = "data source=localhost;initial catalog=lotto_statistics;user id=admin;password=***;multipleactiveresultsets=True";

		public override void Init()
		{
			CrearAlmacenamiento(CONNECTION_STRING);
			inicial = CorteDeVecesCantidadTicketsPorComprador(CONNECTION_STRING);
		}

		private int CorteDeVecesCantidadTicketsPorComprador(string connectionString)
		{
			if (sqlServer)
			{
				return CorteDeVecesCantidadTicketsPorCompradorSQLServer(connectionString);
			}
			else
			{
				return CorteDeVecesCantidadTicketsPorCompradorMySQL(connectionString);
			}
		}

		private int CorteDeVecesCantidadTicketsPorCompradorMySQL(string connectionString)
		{
			string sql = "SELECT DairyId FROM " + TABLE_CORTES + " WHERE NombreCorte = '" + TABLE_ACUMULADOS + "'";
			int dairyId = 0;
			using (MySqlConnection connection = new MySqlConnection(connectionString))
			{
				try
				{
					connection.Open();
					using (MySqlCommand command = new MySqlCommand(sql, connection))
					using (MySqlDataReader reader = command.ExecuteReader())
					{
						while (reader.Read())
						{
							dairyId = reader.GetInt32(0);
						}
						reader.Close();
					}
				}
				finally
				{
					connection.Close();
				}
			}

			return dairyId;
		}

		private int CorteDeVecesCantidadTicketsPorCompradorSQLServer(string connectionString)
		{
			string sql = "SELECT DairyId FROM " + TABLE_CORTES + " WHERE NombreCorte = '" + TABLE_ACUMULADOS + "'";
			int dairyId = 0;
			using (SqlConnection connection = new SqlConnection(connectionString))
			{
				try
				{
					connection.Open();
					using (SqlCommand command = new SqlCommand(sql, connection))
					using (SqlDataReader reader = command.ExecuteReader())
					{
						while (reader.Read())
						{
							dairyId = reader.GetInt32(0);
						}
						reader.Close();
					}
				}
				finally
				{
					connection.Close();
				}
			}

			return dairyId;
		}

		private void CrearAlmacenamiento(string connectionString)
		{
			if (sqlServer)
				CrearAlmacenamientoSQLServer(connectionString);
			else
				CrearAlmacenamientoMySQL(connectionString);
		}

		private void CrearAlmacenamientoMySQL(string connectionString)
		{
			StringBuilder statement = new StringBuilder();
			statement
				.Append("create table IF NOT EXISTS ").Append(TABLE_ACUMULADOS)
				.Append("(")
				.Append("CantidadTickets VARCHAR(10) NOT NULL PRIMARY KEY,")
				.Append("NumeroCompradores INT NOT NULL")
				.Append(");");

			statement
				.Append("create table IF NOT EXISTS ").Append(TABLE_CORTES)
				.Append("(")
				.Append("NombreCorte VARCHAR(50) NOT NULL PRIMARY KEY,")
				.Append("DairyId INT NOT NULL")
				.Append(");")
				.Append("INSERT INTO ").Append(TABLE_CORTES).Append("(NombreCorte, DairyId) VALUES (")
				.Append("'").Append(TABLE_ACUMULADOS).Append("'")
				.Append(", 0) ON DUPLICATE KEY UPDATE DairyId = DairyId;");

			string sql = statement.ToString();

			using (MySqlConnection connection = new MySqlConnection(connectionString))
			{
				connection.Open();
				using (MySqlCommand command = new MySqlCommand(sql, connection))
				{
					command.ExecuteNonQuery();
				}

				connection.Close();
			}
		}

		private void CrearAlmacenamientoSQLServer(string connectionString)
		{
			StringBuilder statement = new StringBuilder();
			statement
				.Append("IF NOT EXISTS(")
				.Append("SELECT 1 FROM INFORMATION_SCHEMA.TABLES ")
				.Append("WHERE TABLE_NAME = '").Append(TABLE_ACUMULADOS).Append("')")
				.Append(" BEGIN")
				.Append("  create table ").Append(TABLE_ACUMULADOS)
				.Append("  (")
				.Append("  CantidadTickets VARCHAR(10) NOT NULL PRIMARY KEY,")
				.Append("  NumeroCompradores INT NOT NULL")
				.Append(" );")
				.Append(" END; ");

			statement
				.Append("IF NOT EXISTS(")
				.Append("SELECT 1 FROM INFORMATION_SCHEMA.TABLES ")
				.Append("WHERE TABLE_NAME = '").Append(TABLE_CORTES).Append("')")
				.Append(" BEGIN")
				.Append("  create table ").Append(TABLE_CORTES)
				.Append("  (")
				.Append("  NombreCorte VARCHAR(50) NOT NULL PRIMARY KEY,")
				.Append("  DairyId INT NOT NULL")
				.Append(" );")
				.Append(" INSERT INTO ").Append(TABLE_CORTES).Append("(NombreCorte, DairyId) VALUES (")
				.Append("'").Append(TABLE_ACUMULADOS).Append("'")
				.Append(", 0);")
				.Append(" END;");

			string sql = statement.ToString();

			using (SqlConnection connection = new SqlConnection(connectionString))
			{
				connection.Open();
				using (SqlCommand command = new SqlCommand(sql, connection))
				{
					command.ExecuteNonQuery();
				}
					
				connection.Close();
			}
		}

		public override void PartialUpdate()
		{
			foreach (var element in contador.Conteos())
			{
				var cantidadTickets = element.Key;
				var conteo = element.Value;
				UpdateAcumulado(CONNECTION_STRING, cantidadTickets, conteo);
			}

			UpdateCorte(CONNECTION_STRING);
		}

		private void UpdateAcumulado(string connectionString, string numero, int conteo)
		{
			if (sqlServer)
				UpdateAcumuladoSQLServer(connectionString, numero, conteo);
			else
				UpdateAcumuladoMySQL(connectionString, numero, conteo);
		}

		private void UpdateAcumuladoMySQL(string connectionString, string cantidadTickets, int conteo)
		{
			string sql = $@"INSERT INTO {TABLE_ACUMULADOS}
							(CantidadTickets, NumeroCompradores)
						VALUES
							('{cantidadTickets}', {conteo})
				ON DUPLICATE KEY UPDATE NumeroCompradores = NumeroCompradores + {conteo}; ";

			using (MySqlConnection connection = new MySqlConnection(connectionString))
			{
				connection.Open();
				using (MySqlCommand command = new MySqlCommand(sql, connection))
				{
					command.ExecuteNonQuery();
				}

				connection.Close();
			}
		}

		private void UpdateAcumuladoSQLServer(string connectionString, string cantidadTickets, int conteo)
		{
			string sql = $@"
							If Not Exists(select * from {TABLE_ACUMULADOS} where CantidadTickets='{cantidadTickets}')
							BEGIN
								INSERT INTO {TABLE_ACUMULADOS} (CantidadTickets, NumeroCompradores) VALUES ('{cantidadTickets}', {conteo});
							END
							ELSE
							BEGIN
								UPDATE {TABLE_ACUMULADOS} SET NumeroCompradores = NumeroCompradores + {conteo} WHERE CantidadTickets='{cantidadTickets}'
							END;";

			using (SqlConnection connection = new SqlConnection(connectionString))
			{
				connection.Open();
				using (SqlCommand command = new SqlCommand(sql, connection))
				{
					command.ExecuteNonQuery();
				}

				connection.Close();
			}
		}

		private void UpdateCorte(string connectionString)
		{
			if (sqlServer)
				UpdateCorteSQLServer(connectionString);
			else
				UpdateCorteMySQL(connectionString);
		}

		private void UpdateCorteMySQL(string connectionString)
		{
			StringBuilder statement = new StringBuilder();
			statement
				.Append("UPDATE ").Append(TABLE_CORTES).Append(" SET DairyId = ").Append(final).Append(";");

			string sql = statement.ToString();

			using (MySqlConnection connection = new MySqlConnection(connectionString))
			{
				connection.Open();
				using (MySqlCommand command = new MySqlCommand(sql, connection))
				{
					command.ExecuteNonQuery();
				}

				connection.Close();
			}
		}

		private void UpdateCorteSQLServer(string connectionString)
		{
			StringBuilder statement = new StringBuilder();
			statement
				.Append("UPDATE ").Append(TABLE_CORTES).Append(" SET DairyId = ").Append(final).Append(";");

			string sql = statement.ToString();

			using (SqlConnection connection = new SqlConnection(connectionString))
			{
				connection.Open();
				using (SqlCommand command = new SqlCommand(sql, connection))
				{
					command.ExecuteNonQuery();
				}

				connection.Close();
			}
		}

		public override void When(Script script)
		{
			if(script.DairyId < inicial)
			{
				return;
			}

			bool conditionDeArbol = false;
			if (script.Text.IndexOf("lotto900.IsBetAmountValidFor(") != -1)
			{
				if (script.Text.IndexOf("nextDatesAccumulator = NextDatesAccumulator(") != -1)
				{
					var numeros = Numeros(script.Text);
					var cantidad = CantidadDeSorteosXDiasXNumeros(script.Text, numeros.Count);

					//if (script.Text.IndexOf(".SaleTickets") != -1 || script.Text.IndexOf(".PurchaseTickets") != -1)
					{
						if (cantidad == 1)
						{
							contador.IncrementarCantidad("01) 1", 1);
						}
						else if(cantidad == 2)
						{
							contador.IncrementarCantidad("02) 2", 1);
						}
						else if(cantidad == 3)
						{
							contador.IncrementarCantidad("03) 3", 1);
						}
						else if (cantidad == 4)
						{
							contador.IncrementarCantidad("04) 4", 1);
						}
						else if (cantidad == 5)
						{
							contador.IncrementarCantidad("05) 5", 1);
						}
						else if(cantidad == 10)
						{
							contador.IncrementarCantidad("06) 10", 1);
						}
						else if (cantidad < 10)
						{
							contador.IncrementarCantidad("07) <10", 1);
						}
						else if(cantidad == 15)
						{
							contador.IncrementarCantidad("08) 15", 1);
						}
						else if(cantidad == 20)
						{
							contador.IncrementarCantidad("09) 20", 1);
						}
						else if(cantidad == 25)
						{
							contador.IncrementarCantidad("10) 25", 1);
						}
						else if(cantidad == 30)
						{
							contador.IncrementarCantidad("11) 30", 1);
						}
						else if (cantidad == 40)
						{
							contador.IncrementarCantidad("12) 40", 1);
						}
						else if(cantidad == 50)
						{
							contador.IncrementarCantidad("13) 50", 1);
						}
						else if (cantidad < 50)
						{
							contador.IncrementarCantidad("14) <50", 1);
						}
						else if (cantidad == 60)
						{
							contador.IncrementarCantidad("15) 60", 1);
						}
						else if (cantidad == 70)
						{
							contador.IncrementarCantidad("16) 70", 1);
						}
						else if (cantidad == 75)
						{
							contador.IncrementarCantidad("17) 75", 1);
						}
						else if (cantidad == 80)
						{
							contador.IncrementarCantidad("18) 80", 1);
						}
						else if (cantidad == 90)
						{
							contador.IncrementarCantidad("19) 90", 1);
						}
						else if (cantidad == 100)
						{
							contador.IncrementarCantidad("20) 100", 1);
						}						
						else
						{
							contador.IncrementarCantidad("21) <100", 1);
						}
						conditionDeArbol = true;
					}
				}
			}

			final = script.DairyId;

			if (conditionDeArbol)
			{
                APMHelper.StartSpan(MethodBase.GetCurrentMethod().DeclaringType.Name, "RuleMatch");

                Then(script);

                APMHelper.EndSpan(MethodBase.GetCurrentMethod().DeclaringType.Name);
            }
		}

        public override void FinalizeRule()
        {
            APMHelper.DisposeSpan(MethodBase.GetCurrentMethod().DeclaringType.Name);
        }


        public decimal MontoDeLaApuesta(string script)
		{
			const string IS_VALID_BET_AMOUNT = "lotto900.IsBetAmountValidFor(";
			int inicio = script.IndexOf(IS_VALID_BET_AMOUNT);
			if (inicio == -1) return -1;

			int final = script.IndexOf(");", inicio);
			string cadena = script.Substring(inicio, final - inicio);
			string[] argumentos = cadena.Split(',');
			decimal amount = decimal.Parse(argumentos[3]);

			return amount;
		}

		public int CantidadDeSorteosXDiasXNumeros(string script, int cantidadNumeros)
		{
			const string NEXT_DATES_ACCUMULATOR = "nextDatesAccumulator = NextDatesAccumulator(";
			int inicio = script.IndexOf(NEXT_DATES_ACCUMULATOR);
			if (inicio == -1) return -1;

			int final = script.IndexOf(");", inicio);
			string cadena = script.Substring(inicio, final - inicio);
			string[] argumentos = cadena.Split('\'');
			string fechas = argumentos[3];
			string estados = argumentos[5];

			int countFechas = fechas.Count(f => f == ',') + 1;
			int countEstados = estados.Count(f => f == ',') + 1;

			return countEstados * countFechas * cantidadNumeros;
		}

		public List<string> Numeros(string script)
		{
			var tieneInputs = script.IndexOf("'MultipleInputSingleAmount'") != -1;
			var tieneBalls = script.IndexOf("'balls'") != -1;

			if(tieneInputs && tieneBalls)
			{
				return NumerosInput(script).Concat(NumerosBalls(script)).ToList();
			}
			if (tieneInputs) return NumerosInput(script);
			if (tieneBalls) return NumerosBalls(script);

			throw new System.Exception("Comando sin manejar");
		}

		//TODO: Rule #3
		//Contar tickets y plata por Sorteo!
		//2 diccionarios por ejemplo

		public List<string> NumerosInput(string script)
		{
			const string CREATE_TICKET_ORDER_1 = "myOrder = company.CreateTicketFullOrder(";
			const string CREATE_TICKET_ORDER_2 = "myOrder = company.CreateTicketOrder(";
			int inicio = script.IndexOf(CREATE_TICKET_ORDER_1);
			if(inicio == -1)
			{
				inicio = script.IndexOf(CREATE_TICKET_ORDER_2);
			}
			int final = script.IndexOf(");", inicio);

			string cadena = script.Substring(inicio, final - inicio);
			string[] argumentos = cadena.Split('\'');
			List<string> result = new List<string>();
			string[] numeros = argumentos[5].Split(',');
			NumeroConcatenado(numeros, "", result);
			numeros = result.ToArray();

			return numeros.ToList();
		}

		private void NumeroConcatenado(string [] numeros, string parcial, List<string> resultados)
		{
			if(numeros.Length == 0)
			{
				resultados.Add(parcial);
			}
			else
			{
				string[] restantes = new string[numeros.Length - 1];
				Array.Copy(numeros, 1, restantes, 0, restantes.Length);
				foreach (char c in numeros[0])
				{
					if(c == '*')
					{
						foreach(char c2 in "0***456789")
						{
							string nuevoParcial = parcial + c2;
							NumeroConcatenado(restantes, nuevoParcial, resultados);
						}
					}
					else
					{
						string nuevoParcial = parcial + c;
						NumeroConcatenado(restantes, nuevoParcial, resultados);
					}					
				}
			}
		}

		public List<string> NumerosBalls(string script)
		{
			const string CREATE_TICKET_ORDER_1 = "myOrder = company.CreateTicketOrder(";
			const string CREATE_TICKET_ORDER_2 = "myOrder = company.CreateTicketFullOrder(";
			int inicio = script.IndexOf(CREATE_TICKET_ORDER_1);
			if(inicio == -1)
			{
				inicio = script.IndexOf(CREATE_TICKET_ORDER_2);
			}
			int final = script.IndexOf(");", inicio);

			string cadena = script.Substring(inicio, final - inicio);
			string[] argumentos = cadena.Split('\'');
			string [] numeros = argumentos[5].Split(',');
			List<string> result = new List<string>();
			NumeroConcatenado(numeros, "", result);

			return result;
		}
	}
}
