﻿using GamesEngine.Finance;
using GamesEngine.PurchaseOrders;
using GamesEngine.Settings;
using MySql.Data.MySqlClient;
using Puppeteer.EventSourcing;
using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Diagnostics;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using town.connectors.drivers;

namespace GamesEngine
{
	public class MarchMadnessWomenActor : Puppeteer.EventSourcing.Actor
	{

		public MarchMadnessWomenActor() : base(nameof(MarchMadnessWomenActor))
		{
			OnLeaderInitialization += this.LeaderInitialization;
			OnAfterRecovering += this.AfterRecovering;
		}

		public void LeaderInitialization()
		{
			//TODO Cristian aqui va la implementacion de tomar el control, para que el actor sea el nuevo lider
		}

		public void AfterRecovering(DatabaseType dbType, string connection, string persona, int lastRecoveredId)
		{
			//TODO Cristian, se debe loguear info para Rollback o status
		}
	}

	public class ResourcesActor : Puppeteer.EventSourcing.Actor
    {

        public ResourcesActor() : base(nameof(ResourcesActor))
        {
            OnLeaderInitialization += this.LeaderInitialization;
            OnAfterRecovering += this.AfterRecovering;
        }

        public void LeaderInitialization()
        {
            //TODO Cristian aqui va la implementacion de tomar el control, para que el actor sea el nuevo lider
        }

        public void AfterRecovering(DatabaseType dbType, string connection, string persona, int lastRecoveredId)
        {
		}
    }

    public class MarchMadnessActor : Puppeteer.EventSourcing.Actor
    {

        public MarchMadnessActor() : base (nameof(MarchMadnessActor))
        {
            OnLeaderInitialization += this.LeaderInitialization;
            OnAfterRecovering += this.AfterRecovering;
        }

        public void LeaderInitialization()
        {
            //TODO Cristian aqui va la implementacion de tomar el control, para que el actor sea el nuevo lider
        }

        public void AfterRecovering(DatabaseType dbType, string connection, string persona, int lastRecoveredId)
        {

		}
	}

    public class LottoActor : Puppeteer.EventSourcing.Actor
    {


        public LottoActor() : base(nameof(LottoActor))
        {
            OnLeaderInitialization += this.LeaderInitialization;
            OnAfterRecovering += this.AfterRecovering;
        }

        public void LeaderInitialization()
        {
            //TODO Cristian aqui va la implementacion de tomar el control, para que el actor sea el nuevo lider
        }

        public void AfterRecovering(DatabaseType dbType, string connection, string persona, int lastRecoveredId)
        {

		}
    }

	public class KenoActor : Puppeteer.EventSourcing.Actor
	{


		public KenoActor() : base(nameof(KenoActor))
		{
			OnLeaderInitialization += this.LeaderInitialization;
			OnAfterRecovering += this.AfterRecovering;
		}

		public void LeaderInitialization()
		{
			//TODO Cristian aqui va la implementacion de tomar el control, para que el actor sea el nuevo lider
		}

		public void AfterRecovering(DatabaseType dbType, string connection, string persona, int lastRecoveredId)
		{

		}
	}

	public class CashierActor : Puppeteer.EventSourcing.Actor
    {
		public bool AreMovementsAndFragmentsTablesCreated { get; set; }

		public CashierActor(string accountNumber) : base(accountNumber)
        {
            OnLeaderInitialization += this.LeaderInitialization;
            OnAfterRecovering += this.AfterRecovering;
        }

        public void LeaderInitialization()
        {
            //TODO Cristian aqui va la implementacion de tomar el control, para que el actor sea el nuevo lider
        }

        public void AfterRecovering(DatabaseType dbType, string connection, string persona, int lastRecoveredId)
        {
		}

		public void MovementsAndFragmentsWereCreated()
		{
			AreMovementsAndFragmentsTablesCreated = true;
		}
	}

	public class CompilerActor : Puppeteer.EventSourcing.Actor
	{
		public CompilerActor() : base(nameof(CompilerActor))
		{
			OnLeaderInitialization += this.LeaderInitialization;
			OnAfterRecovering += this.AfterRecovering;
		}

		public void LeaderInitialization()
		{
			//TODO Cristian aqui va la implementacion de tomar el control, para que el actor sea el nuevo lider
		}

		public void AfterRecovering(DatabaseType dbType, string connection, string persona, int lastRecoveredId)
		{
			//TODO Cristian, se debe loguear info para Rollback o status
		}
	}
	
	public class CashierActors : Actors
	{
		public CashierActors(string dbSelected, string connectionString, string scriptBeforeRecovering) : base(dbSelected, connectionString, scriptBeforeRecovering)
		{
		}
		
		protected override async Task LoadBehindTheSceneAsync(Actor actor, string atAddressNumber)
		{
			if (dbSelected == DatabaseType.MySQL.ToString())
			{
				await EventSourcingStorageAsync(actor, DatabaseType.MySQL, connectionString, scriptBeforeRecovering);

				if (actor.ItsANewOne)
				{
					await actor.PerformCmdAsync($@"
						gameEngineVersion('6.0');

						company = Company();
						companyIDf2401f84xa94ax4db3xaec4x83f5361730ae = company;
						balancesList = company.CustomerBalancesList;

						company.Sales.CreateStore(1,'Picks Store');
						company.Sales.CreateStore(2,'Brackets Store 2019');
						company.Sales.CreateStore(3,'Brackets Store 2020');
						company.Sales.CreateStore(4,'Fiero Wallet');
						company.Sales.CreateStore(5,'Ladybet Store');
						company.Sales.CreateStore(6,'Brackets Store 2021');
						company.Sales.CreateStore(7,'Keno Store');
						atAddress = AtAddress('{atAddressNumber}');",
					IpAddress.DEFAULT,
					UserInLog.ANONYMOUS);
				}
			}
			else if (dbSelected == DatabaseType.SQLServer.ToString())
			{
				await EventSourcingStorageAsync(actor, DatabaseType.SQLServer, connectionString, scriptBeforeRecovering);

				if (actor.ItsANewOne)
				{
					await actor.PerformCmdAsync($@"
						gameEngineVersion('6.0');

						company = Company();
						companyIDf2401f84xa94ax4db3xaec4x83f5361730ae = company;
						balancesList = company.CustomerBalancesList;

						company.Sales.CreateStore(1,'Picks Store');
						company.Sales.CreateStore(2,'Brackets Store 2019');
						company.Sales.CreateStore(3,'Brackets Store 2020');
						company.Sales.CreateStore(4,'Fiero Wallet');
						company.Sales.CreateStore(5,'Ladybet Store');
						company.Sales.CreateStore(6,'Brackets Store 2021');
						company.Sales.CreateStore(7,'Keno Store');
						atAddress = AtAddress('{atAddressNumber}');",
					IpAddress.DEFAULT,
					UserInLog.ANONYMOUS);
				}
			}
			else if (String.IsNullOrWhiteSpace(dbSelected))
			{
#if DEBUG
				if (Integration.UseKafka) Integration.Kafka.OffSetResetToLatest();
				switch (Integration.MockToStart)
				{
					case 0:
						Exchangemock1((CashierActor)actor, atAddressNumber);
						break;
					case 1:
						LoadMocks((CashierActor)actor, atAddressNumber);
						break;
					case 2:
						LRCurrencyMock((CashierActor)actor, atAddressNumber);
						break;
					default:
						throw new Exception($"There is no mock for {Integration.MockToStart}");
				}
#else
					throw new Exception($"There is no connection for {Integration.Db.DBSelected}");
#endif
			}
		}

		static CashierActor Exchangemock1(CashierActor actor, string atAddressNumber)
		{
			if (atAddressNumber.Equals(RestAPISpawnerActor.GENERAL))
			{
				var x = actor.PerformCmdAsync($@"
				company = Company();
				companyIDf2401f84xa94ax4db3xaec4x83f5361730ae = company;
				balancesList = company.CustomerBalancesList;

				company.Sales.CreateStore(1,'Picks Store');
				company.Sales.CreateStore(2,'Brackets Store 2019');
				company.Sales.CreateStore(3,'Brackets Store 2020');
				company.Sales.CreateStore(4,'Fiero Wallet');
				company.Sales.CreateStore(5,'Ladybet Store');
				company.Sales.CreateStore(6,'Brackets Store 2021');
				company.Sales.CreateStore(7,'Keno Store');
				if (!company.System.Coins.ExistsIsoCode('FP'))
				{{
					coin = company.System.Coins.Add(0, 'FP', '$', 2, '$', 'free play', {CoinType.Digital});
					coin.Visible = true;
					coin.Enabled = true;
				}}
				if (!company.System.Coins.ExistsIsoCode('USD'))
				{{
					coin = company.System.Coins.Add(2, 'USD', '$', 2, '$', 'dollar', {CoinType.Fiat});
					coin.Visible = true;
					coin.Enabled = true;
				}}
				if (!company.System.Coins.ExistsIsoCode('BTC'))
				{{
					coin = company.System.Coins.Add(3, 'BTC', 'BTC', 8, '₿', 'bitcoin', {CoinType.Crypt});
					coin.Visible = true;
					coin.Enabled = true;
				}}
				if (!company.System.Coins.ExistsIsoCode('ETH'))
				{{
					coin = company.System.Coins.Add(7, 'ETH', 'ETH', 8, 'Ξ', 'ethereum', {CoinType.Crypt});
				}}

				customSettings = CustomSettingsCollection(company);
				{{
					company.System.Entities.Add(1, 'Apolo');
					artemisEntity = company.System.Entities.Add(2, 'Artemis');
					zeusEntity = company.System.Entities.Add(3, 'Zeus');
					fieroEntity = company.System.Entities.Add(4, 'Fiero');
					fieroEntity.Visible = true;
					fieroEntity.Enabled = true;
					hadesEntity = company.System.Entities.Add(5, 'Hades');
					hadesEntity.Visible = true;
					hadesEntity.Enabled = true;
					consignmentEntity = company.System.Entities.Add(6, 'Consignment');
					consignmentEntity.Visible = true;
					consignmentEntity.Enabled = true;

					company.System.Tenants.Add(1, 'Apolo');
					artemisTenant = company.System.Tenants.Add(2, 'Artemis');
					zeusTenant = company.System.Tenants.Add(3, 'Zeus');
					insiderTenant = company.System.Tenants.Add(4, 'Insider');
					hadesTenant = company.System.Tenants.Add(5, 'Hades');

					asiPM = company.System.PaymentMethods.Add(1, 'A.S.I.');
					dgsPM = company.System.PaymentMethods.Add(2, 'D.G.S.');
					dgsV2PM = company.System.PaymentMethods.Add(3, 'D.G.S. v2');
					credirCardPM = company.System.PaymentMethods.Add(4, 'Credit card');
					blockChainPM = company.System.PaymentMethods.Add(5, 'Blockchain');
					moneyTransferPM = company.System.PaymentMethods.Add(6, 'Money transfer');
					asiSimulatorPM = company.System.PaymentMethods.Add(7, 'A.S.I. Simulator');
					dsgSimulatorPM = company.System.PaymentMethods.Add(8, 'D.G.S. Simulator');
					pm = company.System.PaymentMethods.Add(9, '{PaymentMethod.Cash.ToString()}');
					pm.Visible = true;
					pm.Enabled = true;
					pm = company.System.PaymentMethods.Add(10, '{PaymentMethod.Bank.ToString()}');
					pm.Visible = true;
					pm.Enabled = true;
					pm = company.System.PaymentMethods.Add(11, '{PaymentMethod.Creditcard.ToString()}');
					pm.Visible = true;
					pm.Enabled = true;
					pm = company.System.PaymentMethods.Add(12, '{PaymentMethod.FinancialServices.ToString()}');
					pm.Visible = true;
					pm.Enabled = true;
					pm = company.System.PaymentMethods.Add(13, '{PaymentMethod.ThirdParty.ToString()}');
					pm.Visible = true;
					pm.Enabled = true;
					pm = company.System.PaymentMethods.Add(14, '{PaymentMethod.Secrets.ToString()}');
					pm.Visible = true;
					pm.Enabled = true;
					pm = company.System.PaymentMethods.Add(15, '{PaymentMethod.P2PTransfer.ToString()}');
					pm.Visible = true;
					pm.Enabled = true;
					pm.AddProvider(1,'MoneyGram');
					pm.AddProvider(2,'Rio Money Transfer');
					pm.AddProvider(3,'Sigue');
					pm.AddProvider(4,'Boss Revolution Money Transfer');
					pm.AddProvider(5,'Delgado Travel');
					pm.AddProvider(6,'Intermex International Money Transfer');
					pm.AddProvider(7,'Maxi Money Services');
					pm.AddProvider(8,'Barri Financial Group');
					pm.AddProvider(9,'La Nacional');
					pm.AddProvider(10,'Dinex');
					pm.AddProvider(11,'Remitly');

					depositTransactionType = company.System.TransactionTypes.Add(1, '{TransactionType.Deposit.ToString()}');
					depositTransactionType.Description = 'A sum of money placed in an account';
					withDrawalTransactionType = company.System.TransactionTypes.Add(2, '{TransactionType.Withdrawal.ToString()}');
					withDrawalTransactionType.Description = 'A sum of money take out of an account';
					transferTransactionType = company.System.TransactionTypes.Add(3, '{TransactionType.Transfer.ToString()}');
					transferTransactionType.Description = 'A sum of money is sent from one account to another';
					creditNoteTransactionType = company.System.TransactionTypes.Add(4, '{TransactionType.CreditNote.ToString()}');
					creditNoteTransactionType.Description = 'It is a document used by a vendor to inform the buyer of mistake on an order';
					debitNoteTransactionType = company.System.TransactionTypes.Add(5, '{TransactionType.DebitNote.ToString()}');
					debitNoteTransactionType.Description = 'It is a document used by a vendor to inform the buyer of current debt obligations';
					saleTransactionType = company.System.TransactionTypes.Add(6, '{TransactionType.Sale.ToString()}');
					saleTransactionType.Description = 'The exchange of a commodity for money';
					depositAndLockTransactionType = company.System.TransactionTypes.Add(7, '{TransactionType.Deposit.ToString()} then Lock');
					depositAndLockTransactionType.Description = 'Add and Lock money placed in an account';

					depositTransactionType.Visible = true;
					depositTransactionType.Enabled = true;
					withDrawalTransactionType.Visible = true;
					withDrawalTransactionType.Enabled = true;
					transferTransactionType.Visible = true;
					transferTransactionType.Enabled = true;
					debitNoteTransactionType.Visible = true;
					debitNoteTransactionType.Enabled = true;
					creditNoteTransactionType.Visible = true;
					creditNoteTransactionType.Enabled = true;
					saleTransactionType.Visible = true;
					saleTransactionType.Enabled = true;
					depositAndLockTransactionType.Visible = true;
					depositAndLockTransactionType.Enabled = true;

					cs = customSettings.AddFixedParameter(Now, 'CashierDriver.url', 'http://cashierapi:5000/'); 
					cs.Description = 'Url Cashier';
					cs = customSettings.AddFixedParameter(Now, 'CompanyBaseUrlServices', 'http://cashierapi:5000/'); 
					cs.Description = 'It is the base production url';
					cs = customSettings.AddFixedParameter(Now, 'TokenSystemId', '3aab83fb'); 
					cs.Description = 'App\'s token id';
					cs = customSettings.AddSecretParameter(Now, 'TokenSystemPassword', '38f845e977af7f2cd6313c5a9d35e4de7f353405100e7748ab15b410eb53d886');
					cs.Description = 'Password for token';
					cs = customSettings.AddFixedParameter(Now, 'SendWagers', true); 
					cs.Description = 'To send wagers to third party';
					cs = customSettings.AddFixedParameter(Now, 'CompanySystemId', 'N/A'); 
					cs.Description = 'company id';
					cs = customSettings.AddSecretParameter(Now, 'CompanySystemPassword', 'N/A'); 
					cs.Description = 'password';
					cs = customSettings.AddFixedParameter(Now, 'NeedsUniqueIdentifierForPaymentHub', 'PRODUCTION_DOES_NOT_NEED_IT'); 
					cs.Description = 'Setting';
					cs = customSettings.AddFixedParameter(Now, 'CompanyClerkId', 'Administrator'); 
					cs.Description = 'Clerk id';

					cs = customSettings.AddVariableParameter('Input$Provider'); 
					cs.Description = 'Consignment provider';
					cs = customSettings.AddVariableParameter('KYC$DOB'); 
					cs.Description = 'Birthdate from KYC';
					cs = customSettings.AddSecretParameter(Now, 'KYCUsername', 'testUser1');
					cs.Description = 'KYC Username for fields';
					cs = customSettings.AddSecretParameter(Now, 'KYCPassword', '12345');
					cs.Description = 'KYC Password for fields';
				}}
			",
				IpAddress.DEFAULT,
#pragma warning disable VSTHRD002 // Avoid problematic synchronous waits
			UserInLog.ANONYMOUS).Result;
#pragma warning restore VSTHRD002 // Avoid problematic synchronous waits
				return actor;

			}
			else if (atAddressNumber.Equals("5432161"))
			{
				var x = actor.PerformCmdAsync($@"
				company = Company();
				companyIDf2401f84xa94ax4db3xaec4x83f5361730ae = company;
				balancesList = company.CustomerBalancesList;

				company.Sales.CreateStore(1,'Picks Store');
				company.Sales.CreateStore(2,'Brackets Store 2019');
				company.Sales.CreateStore(3,'Brackets Store 2020');
				company.Sales.CreateStore(4,'Fiero Wallet');
				company.Sales.CreateStore(5,'Ladybet Store');
				company.Sales.CreateStore(6,'Brackets Store 2021');
				company.Sales.CreateStore(7,'Keno Store');

				atAddress = AtAddress('5432161');
				balance = atAddress.CreateAccountIfNotExists('BTC', 'cr00020003').Balance;
				store = company.Sales.StoreById(5);
				balance.Accredit(itIsThePresent, Now, Btc(1000), 'ccajero', '2', {Store.STORES_SEQUENCE_EXCHANGE}, 'Deposit BTC 1000 to cr00020003', '2');
			",
				IpAddress.DEFAULT,
#pragma warning disable VSTHRD002 // Avoid problematic synchronous waits
			UserInLog.ANONYMOUS).Result;
#pragma warning restore VSTHRD002 // Avoid problematic synchronous waits
				return actor;

			}
			else if (atAddressNumber.Equals("no562430370"))
			{
				var x = actor.PerformCmdAsync($@"
				company = Company();
				companyIDf2401f84xa94ax4db3xaec4x83f5361730ae = company;
				balancesList = company.CustomerBalancesList;

				company.Sales.CreateStore(1,'Picks Store');
				company.Sales.CreateStore(2,'Brackets Store 2019');
				company.Sales.CreateStore(3,'Brackets Store 2020');
				company.Sales.CreateStore(4,'Fiero Wallet');
				company.Sales.CreateStore(5,'Ladybet Store');
				company.Sales.CreateStore(6,'Brackets Store 2021');
				company.Sales.CreateStore(7,'Keno Store');

				atAddress = AtAddress('no562430370');
				balance = atAddress.CreateAccountIfNotExists('USD', 'cr00030004').Balance;
				store = company.Sales.StoreById(5);
				balance.Accredit(itIsThePresent, Now, Dollar(1000), 'ccajero', '2', store, 'Deposit $1000 to cr00030004', '2');
				balance = atAddress.CreateAccountIfNotExists('BTC', 'cr00030005').Balance;
				balance.Accredit(itIsThePresent, Now, Btc(1000), 'ccajero', '2', store, 'Deposit BTC 1000 to cr00030005', '2');
			",
				IpAddress.DEFAULT,
#pragma warning disable VSTHRD002 // Avoid problematic synchronous waits
			UserInLog.ANONYMOUS).Result;
#pragma warning restore VSTHRD002 // Avoid problematic synchronous waits
				return actor;

			}
			else if (atAddressNumber.Equals("no562430373"))
			{
				var x = actor.PerformCmdAsync($@"
				company = Company();
				companyIDf2401f84xa94ax4db3xaec4x83f5361730ae = company;

				company.Sales.CreateStore(1,'Picks Store');
				company.Sales.CreateStore(2,'Brackets Store 2019');
				company.Sales.CreateStore(3,'Brackets Store 2020');
				company.Sales.CreateStore(4,'Fiero Wallet');
				company.Sales.CreateStore(5,'Ladybet Store');
				company.Sales.CreateStore(6,'Brackets Store 2021');
				company.Sales.CreateStore(7,'Keno Store');

				atAddress = AtAddress('no562430373'); 
				balance = atAddress.CreateAccountIfNotExists('USD', 'cr00040006').Balance;
				store = company.Sales.StoreById(5);
				balance.Accredit(itIsThePresent, Now, Dollar(1000), 'ccajero', '2', store, 'Deposit $1000 to cr00040006', '2');

				balance = atAddress.CreateAccountIfNotExists('USD', 'USD').Balance;
				balance.Accredit(itIsThePresent, Now, Currency('USD',25000), 'ccajero', '3', store, 'Deposit $25000 to USD', '3');
			",
				IpAddress.DEFAULT,
#pragma warning disable VSTHRD002 // Avoid problematic synchronous waits
			UserInLog.ANONYMOUS).Result;
#pragma warning restore VSTHRD002 // Avoid problematic synchronous waits
				return actor;

			}
			else if (atAddressNumber.Equals("lo63738186257747081"))
			{
				var x = actor.PerformCmdAsync($@"
				company = Company();
				companyIDf2401f84xa94ax4db3xaec4x83f5361730ae = company;
				balancesList = company.CustomerBalancesList;

				company.Sales.CreateStore(1,'Picks Store');
				company.Sales.CreateStore(2,'Brackets Store 2019');
				company.Sales.CreateStore(3,'Brackets Store 2020');
				company.Sales.CreateStore(4,'Fiero Wallet');
				company.Sales.CreateStore(5,'Ladybet Store');
				company.Sales.CreateStore(6,'Brackets Store 2021');
				company.Sales.CreateStore(7,'Keno Store');

				atAddress = AtAddress('*******************');
				balance = atAddress.CreateAccountIfNotExists('USD', 'cr00010002').Balance;
				store = company.Sales.StoreById(5);
				balance.Accredit(itIsThePresent, Now, Dollar(1000), 'ccajero', '9999', store, 'Deposit $1000 to cr00010002', '9999');
			",
				IpAddress.DEFAULT,
#pragma warning disable VSTHRD002 // Avoid problematic synchronous waits
			UserInLog.ANONYMOUS).Result;
#pragma warning restore VSTHRD002 // Avoid problematic synchronous waits
				return actor;

			}
			else
			{
				var x = actor.PerformCmdAsync($@"
				company = Company();
				companyIDf2401f84xa94ax4db3xaec4x83f5361730ae = company;
				balancesList = company.CustomerBalancesList;

				atAddress = AtAddress('{atAddressNumber}');
				company.Sales.CreateStore(1,'Picks Store');
				company.Sales.CreateStore(2,'Brackets Store 2019');
				company.Sales.CreateStore(3,'Brackets Store 2020');
				company.Sales.CreateStore(4,'Fiero Wallet');
				company.Sales.CreateStore(5,'Ladybet Store');
				company.Sales.CreateStore(6,'Brackets Store 2021');
				company.Sales.CreateStore(7,'Keno Store');
			",
				IpAddress.DEFAULT,
#pragma warning disable VSTHRD002 // Avoid problematic synchronous waits
			UserInLog.ANONYMOUS).Result;
#pragma warning restore VSTHRD002 // Avoid problematic synchronous waits

				return actor;

			}
		}

		static internal CashierActor LoadMocks(CashierActor actor, string atAddressNumber)
		{
			var x = actor.PerformCmdAsync($@"
				atAddress = AtAddress('{atAddressNumber}');
				manualsource = atAddress.CreateSource(itIsThePresent, Now, 1, LR);
				firsttimesource = atAddress.CreateSource(itIsThePresent, Now, 2, LR);
				rechargesource = atAddress.CreateSource(itIsThePresent, Now, 3, LR);

				firsttimesource.Accredit(itIsThePresent, Now, LottoReward(800), 'Mau', 'ABC2240', LOTTO, 'First deposit from the book.', 'ref');
				rechargesource.Accredit(itIsThePresent, Now, LottoReward(10), 'Dave', 'ABC2241', LOTTO, 'No descrip', 'ref');
				firsttimesource.Accredit(itIsThePresent,Now, LottoReward(15.50), 'Dave', 'ABC2242', LOTTO, 'No descrip', 'ref' );
				manualsource.Accredit(itIsThePresent,Now, Dollar(10), 'Dave', 'ABC2243', LOTTO, 'No descrip', 'ref' );
				firsttimesource.Lock(itIsThePresent,Now, LottoReward(6), 'ABC123', LOTTO, 'No descrip', 'ref' );
				firsttimesource.UnLock(itIsThePresent,Now, LottoReward(4), 'Mau', 'ABC1240', LOTTO, 'No descrip', 'ref' );
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.25), 'ABC124', LOTTO, 'Thanks for your purchase #1' , 'ref');
	
	
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(1), 'ABC1240', LOTTO, 'No descrip' , 'ref');
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.5), 'ABC1241', LOTTO, 'No descrip' , 'ref');
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(1), 'ABC1242', LOTTO, 'No descrip' , 'ref');
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(1), 'ABC1243', LOTTO, 'Thanks for your purchase #5', 'ref' );
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(1), 'ABC1244', LOTTO, 'No descrip' , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.25), 'ABC1245', LOTTO, 'No descrip', 'ref' ); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.25), 'ABC1246', LOTTO, 'No descrip' , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.25), 'ABC1247', LOTTO, 'No descrip' , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.25), 'ABC1248', LOTTO, 'No descrip' , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.25), 'ABC1249', LOTTO, 'No descrip' , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.5), 'ABC12410', LOTTO, 'Thanks for your purchase #12' , 'ref');
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(5), 'ABC12411', LOTTO, 'No descrip' , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(5), 'ABC12412', LOTTO, 'No descrip' , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.5), 'ABC12413', LOTTO, 'No descrip' , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.25), 'ABC12414', LOTTO, 'No descrip' , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(1), 'ABC12415', LOTTO, 'No descrip' , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.5), 'ABC12416', LOTTO, 'No descrip' , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(1), 'ABC12417', LOTTO, 'No descrip' , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.25), 'ABC12418', LOTTO, 'No descrip' , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.25), 'ABC12419', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.25), 'ABC12420', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(5), 'ABC12421', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(1), 'ABC12422', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.25), 'ABC12423', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.5), 'ABC12424', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.25), 'ABC12425', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.25), 'ABC12426', LOTTO, 'Thanks for your purchase #30' , 'ref');
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(5), 'ABC12427', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.25), 'ABC12428', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(1), 'ABC12429', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.5), 'ABC12430', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.5), 'ABC12431', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(1), 'ABC12432', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(1), 'ABC12433', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(5), 'ABC12434', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.5), 'ABC12435', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(1), 'ABC12436', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.5), 'ABC12437', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(5), 'ABC12438', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(1), 'ABC12439', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(1), 'ABC12440', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.25), 'ABC12441', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(5), 'ABC12442', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.25), 'ABC12443', LOTTO, 'Thanks for your purchase #53' , 'ref');
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.25), 'ABC12444', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.5), 'ABC12445', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(1), 'ABC12446', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(5), 'ABC12447', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(1), 'ABC12448', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.5), 'ABC12449', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(1), 'ABC12450', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.25), 'ABC12451', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(1), 'ABC12452', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.5), 'ABC12453', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.25), 'ABC12454', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(1), 'ABC12455', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(1), 'ABC12456', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.25), 'ABC12457', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.5), 'ABC12458', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.25), 'ABC12459', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(5), 'ABC12460', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(1), 'ABC12461', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(5), 'ABC12462', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.5), 'ABC12463', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.5), 'ABC12464', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(5), 'ABC12465', LOTTO, 'Thanks for your purchase #80' , 'ref');
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(1), 'ABC12466', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.5), 'ABC12467', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(5), 'ABC12468', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(1), 'ABC12469', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(5), 'ABC12470', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.25), 'ABC12471', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(1), 'ABC12472', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.5), 'ABC12473', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.5), 'ABC12474', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.25), 'ABC12475', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(5), 'ABC12476', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.25), 'ABC12477', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(5), 'ABC12478', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(5), 'ABC12479', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(5), 'ABC12480', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(1), 'ABC12481', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.5), 'ABC12482', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.5), 'ABC12483', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(5), 'ABC12484', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.5), 'ABC12485', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.25), 'ABC12486', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(5), 'ABC12487', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.25), 'ABC12488', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(1), 'ABC12489', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(1), 'ABC12490', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.25), 'ABC12491', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(5), 'ABC12492', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(5), 'ABC12493', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(1), 'ABC12494', LOTTO, 'Thanks for your purchase #114' , 'ref');
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.25), 'ABC12495', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.25), 'ABC12496', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.25), 'ABC12497', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.5), 'ABC12498', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.25), 'ABC12499', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(1), 'ABC124100', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.5), 'ABC124101', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(5), 'ABC124102', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.25), 'ABC124103', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.5), 'ABC124104', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(1), 'ABC124105', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.5), 'ABC124106', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.5), 'ABC124107', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(5), 'ABC124108', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.25), 'ABC124109', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.5), 'ABC124110', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.25), 'ABC124111', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.25), 'ABC124112', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.5), 'ABC124113', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.25), 'ABC124114', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.5), 'ABC124115', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(5), 'ABC124116', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(1), 'ABC124117', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(5), 'ABC124118', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(5), 'ABC124119', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.5), 'ABC124120', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.5), 'ABC124121', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(1), 'ABC124122', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(1), 'ABC124123', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(5), 'ABC124124', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(5), 'ABC124125', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.25), 'ABC124126', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.5), 'ABC124127', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(1), 'ABC124128', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.5), 'ABC124129', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(5), 'ABC124130', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(5), 'ABC124131', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(1), 'ABC124132', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(5), 'ABC124133', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.5), 'ABC124134', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.25), 'ABC124135', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(1), 'ABC124136', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(1), 'ABC124137', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.25), 'ABC124138', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(1), 'ABC124139', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(5), 'ABC124140', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.5), 'ABC124141', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(5), 'ABC124142', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(5), 'ABC124143', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(5), 'ABC124144', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(1), 'ABC124145', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.25), 'ABC124146', LOTTO, 'Thanks for your purchase #130' , 'ref');
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(1), 'ABC124147', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.5), 'ABC124148', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(5), 'ABC124149', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(1), 'ABC124150', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(5), 'ABC124151', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(1), 'ABC124152', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.5), 'ABC124153', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(1), 'ABC124154', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.5), 'ABC124155', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(1), 'ABC124156', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.5), 'ABC124157', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.25), 'ABC124158', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.5), 'ABC124159', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(1), 'ABC124160', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(5), 'ABC124161', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(1), 'ABC124162', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(1), 'ABC124163', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.25), 'ABC124164', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.5), 'ABC124165', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.5), 'ABC124166', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(1), 'ABC124167', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.25), 'ABC124168', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.25), 'ABC124169', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.25), 'ABC124170', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(1), 'ABC124171', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.25), 'ABC124172', LOTTO , 'Thanks for your purchase #150'  , 'ref');
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(1), 'ABC124173', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(5), 'ABC124174', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.25), 'ABC124175', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.5), 'ABC124176', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(1), 'ABC124177', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.5), 'ABC124178', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(1), 'ABC124179', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(5), 'ABC124180', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.5), 'ABC124181', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(5), 'ABC124182', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.5), 'ABC124183', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.25), 'ABC124184', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.25), 'ABC124185', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(1), 'ABC124186', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(5), 'ABC124187', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(1), 'ABC124188', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(5), 'ABC124189', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(5), 'ABC124190', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(5), 'ABC124191', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(1), 'ABC124192', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(1), 'ABC124193', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(5), 'ABC124194', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.25), 'ABC124195', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(1), 'ABC124196', LOTTO , 'Thanks for your purchase #167'  , 'ref');
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.5), 'ABC124197', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(5), 'ABC124198', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.25), 'ABC124199', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(5), 'ABC124200', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.5), 'ABC124201', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(1), 'ABC124202', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.5), 'ABC124203', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.5), 'ABC124204', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.25), 'ABC124205', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(5), 'ABC124206', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(5), 'ABC124207', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(5), 'ABC124208', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(1), 'ABC124209', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.25), 'ABC124210', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(5), 'ABC124211', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(5), 'ABC124212', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(5), 'ABC124213', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(5), 'ABC124214', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(5), 'ABC124215', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.25), 'ABC124216', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(5), 'ABC124217', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.25), 'ABC124218', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.25), 'ABC124219', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(1), 'ABC124220', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(5), 'ABC124221', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.25), 'ABC124222', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.25), 'ABC124223', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.25), 'ABC124224', LOTTO, 'Thanks for your purchase #168' , 'ref');
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(1), 'ABC124225', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.25), 'ABC124226', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.5), 'ABC124227', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(1), 'ABC124228', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(5), 'ABC124229', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(5), 'ABC124230', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.25), 'ABC124231', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.5), 'ABC124232', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(1), 'ABC124233', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.5), 'ABC124234', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.25), 'ABC124235', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(1), 'ABC124236', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(1), 'ABC124237', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(5), 'ABC124238', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.25), 'ABC124239', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(1), 'ABC124240', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(1), 'ABC124241', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(5), 'ABC124242', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(5), 'ABC124243', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(5), 'ABC124244', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.25), 'ABC124245', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(5), 'ABC124246', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(1), 'ABC124247', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(1), 'ABC124248', LOTTO, 'No descrip'  , 'ref'); 
				firsttimesource.Withdraw(itIsThePresent,Now, LottoReward(0.25), 'ABC124249', LOTTO , 'Thanks for your purchase #180' , 'ref');

				balanceLR = atAddress.CreateBalanceIfNotExists('LR');
				print balanceLR.Type typeLR;
				print balanceLR.Locked lockedLR;
				print balanceLR.Available availableLR;

				balanceDollars = atAddress.CreateBalanceIfNotExists('USD');	
				print balanceDollars.Type typeDollar;
				print balanceDollars.Locked lockedDollar;
				print balanceDollars.Available availableDollar;
			",
			IpAddress.DEFAULT,
#pragma warning disable VSTHRD002 // Avoid problematic synchronous waits
			UserInLog.ANONYMOUS).Result;
#pragma warning restore VSTHRD002 // Avoid problematic synchronous waits

			return actor;
		}

		static internal CashierActor LRCurrencyMock(CashierActor actor, string atAddressNumber)
		{
			var x = actor.PerformCmdAsync($@"
				atAddress = AtAddress('{atAddressNumber}');
				manualsource = atAddress.CreateSource(itIsThePresent, Now, 1, LR);
				firsttimesource = atAddress.CreateSource(itIsThePresent, Now, 2, LR);
				rechargesource = atAddress.CreateSource(itIsThePresent, Now, 3, LR);

                Now = 10/12/2019 11:35:45;
				firsttimesource.Accredit(itIsThePresent, Now, LottoReward(8000), 'Mau', 'ABC2240', LOTTO, 'First deposit from the book.', 'ref');
                atAddress.CreateAuthorization(itIsThePresent, Now, LottoReward(2.0), 3, LOTTO, 'ticketPick3StraightProd', 'ref');
				authorization = atAddress.GetAuthorization(3);
	            authorization.CreateFragments(1, 1, 2.0, {{'1000002-1'}}, {{'Lotto PA 6:25PM Pick 3 10/14/2019 (3,9)-3-(3,6) $2.00 ticket for $450.00 prize'}}, 2.0, 449.5);
                ataddress.PreparePayments(itIsThePresent, 3, {{1}}, Now, LOTTO, '', Loser).ApplyChanges(itIsThePresent, 'Bart Simpson');

                Now = 10/14/2019 10:35:45;
                atAddress.CreateAuthorization(itIsThePresent, Now, LottoReward(0.75), 4, LOTTO, 'ticketPick3StraightProd', 'ref');
	            authorization = atAddress.GetAuthorization(4);
	            authorization.CreateFragments(1, 3, 0.25, {{'1000003-1', '1000003-2', '1000003-3'}}, {{'Lotto SC 6:30PM Pick 3 10/14/2019 8-0-1 $0.25 ticket for $225.00 prize', 'Lotto SC 6:30PM Pick 3 10/14/2019 8-8-1 $0.25 ticket for $225.00 prize', 'Lotto SC 6:30PM Pick 3 10/14/2019 8-8-8 $0.25 ticket for $225.00 prize'}}, 0.25, 224.75);
	            ataddress.PreparePayments(itIsThePresent, 4, {{1, 2, 3}}, Now, LOTTO, '', Loser).ApplyChanges(itIsThePresent, 'Bart Simpson');

                Now = 10/15/2019 9:35:45;
                atAddress.CreateAuthorization(itIsThePresent, Now, LottoReward(0.75), 5, LOTTO, 'ticketPick3BoxedProd', 'ref');
	            authorization = atAddress.GetAuthorization(5);
	            authorization.CreateFragments(1, 3, 0.25, {{'1000004-1', '1000004-2', '1000004-3'}}, {{'Lotto NH 6:40PM Pick 3 10/14/2019 (8-0-1) BOX $0.25 ticket for $37.50 prize', 'Lotto NH 6:40PM Pick 3 10/14/2019 (8-8-1) BOX $0.25 ticket for $75.00 prize', 'Lotto NH 6:40PM Pick 3 10/14/2019 (8-8-8) BOX $0.25 ticket for $225.00 prize'}}, 0.25, 37.25);
	            ataddress.PreparePayments(itIsThePresent, 5, {{1, 2, 3}}, Now, LOTTO, '', Loser).ApplyChanges(itIsThePresent, 'Bart Simpson');

                Now = 10/16/2019 8:35:45;
                atAddress.CreateAuthorization(itIsThePresent, Now, LottoReward(1.0), 6, LOTTO, 'ticketPick3BoxedProd', 'ref');
	            authorization = atAddress.GetAuthorization(6);
	            authorization.CreateFragments(1, 4, 0.25, {{'1000005-1', '1000005-2', '1000005-3', '1000005-4'}}, {{'Lotto VT 6:40PM Pick 3 10/14/2019 (3-3-3) BOX $0.25 ticket for $225.00 prize', 'Lotto VT 6:40PM Pick 3 10/14/2019 (3-3-6) BOX $0.25 ticket for $75.00 prize', 'Lotto VT 6:40PM Pick 3 10/14/2019 (9-3-3) BOX $0.25 ticket for $75.00 prize', 'Lotto VT 6:40PM Pick 3 10/14/2019 (9-3-6) BOX $0.25 ticket for $37.50 prize'}}, 0.25, 224.75);
	            ataddress.PreparePayments(itIsThePresent, 6, {{1, 2, 3, 4}}, Now, LOTTO, '', Loser).ApplyChanges(itIsThePresent, 'Bart Simpson');

                Now = 10/14/2019 18:35:45;
                atAddress.CreateAuthorization(itIsThePresent, Now, LottoReward(1.0), 8, LOTTO, 'ticketPick3BoxedProd', 'ref');
	            authorization = atAddress.GetAuthorization(8);
	            authorization.CreateFragments(1, 4, 0.25, {{'1000007-1', '1000007-2', '1000007-3', '1000007-4'}}, {{'Lotto TX Evening 6:43PM Pick 3 10/14/2019 (3-3-3) BOX $0.25 ticket for $225.00 prize', 'Lotto TX Evening 6:43PM Pick 3 10/14/2019 (3-3-6) BOX $0.25 ticket for $75.00 prize', 'Lotto TX Evening 6:43PM Pick 3 10/14/2019 (9-3-3) BOX $0.25 ticket for $75.00 prize', 'Lotto TX Evening 6:43PM Pick 3 10/14/2019 (9-3-6) BOX $0.25 ticket for $37.50 prize'}}, 0.25, 224.75);
                ",
			IpAddress.DEFAULT,
#pragma warning disable VSTHRD002 // Avoid problematic synchronous waits
			UserInLog.ANONYMOUS).Result;
#pragma warning restore VSTHRD002 // Avoid problematic synchronous waits
			return actor;
		}

		public List<string> ListActorsStored()
		{
			List<string> result = new List<string>();

			if (dbSelected == DatabaseType.MySQL.ToString())
			{
				Debug.WriteLine("Mysql doesn't need to upgrade tables.");
			}
			else if (dbSelected == DatabaseType.SQLServer.ToString())
			{
				string stCommand = $@"
							SELECT TABLE_NAME as Name
							FROM INFORMATION_SCHEMA.TABLES
							WHERE TABLE_NAME like 'C%' 
							AND TABLE_NAME NOT like '%_movements%'
							AND TABLE_NAME NOT like '%_authfragments';
						";
				using (SqlConnection connection = new SqlConnection(connectionString))
				{
					try
					{
						connection.Open();
						SqlCommand command = new SqlCommand(stCommand, connection);
						using (SqlDataReader reader = command.ExecuteReader())
						{
							while (reader.Read())
							{
								result.Add(reader["Name"].ToString().Substring(1));
							}
						}
					}
					catch (Exception e)
					{
						Loggers.GetIntance().Db.Error($@"sql:{stCommand} type:{e.GetType()} error:{e.Message}", e);
						throw e;
					}
					finally
					{
						connection.Close();
					}
				}
			}
			return result;
		}
	}
	
	public abstract class Actors
    {
		static Dictionary<string, Actor> actors = new Dictionary<string, Actor>();

		protected string dbSelected;
		protected string connectionString;
		protected string scriptBeforeRecovering;

		public Actors(string dbSelected, string connectionString, string scriptBeforeRecovering)
		{
			this.dbSelected = dbSelected;
			this.connectionString = connectionString;
			this.scriptBeforeRecovering = scriptBeforeRecovering;
		}

		public bool TryGetActor(string atAddressNumber, out Actor actor)
		{
			atAddressNumber = atAddressNumber.ToLower().Trim();
			return actors.TryGetValue(atAddressNumber, out actor);
		}

		public List<ActorsReleaseReponse> Release(double timeWithoutActivity)
		{
			List<ActorsReleaseReponse> response = new List<ActorsReleaseReponse>();

			foreach (ActorsReponse actor in ListActors())
			{
				if (actor.DaysWithoutActivity < timeWithoutActivity)
				{
					break;
				}

				bool result = Release(actor.Name);
				response.Add(new ActorsReleaseReponse(
					actor, result
				));
			}
			return response;
		}

		private static readonly object myLock = new object();
		private bool Release(string atAddressNumber)
		{
			atAddressNumber = atAddressNumber.Substring(1);
			atAddressNumber = atAddressNumber.ToLower().Trim();

			bool added;
			lock (myLock)
			{
				added = actors.Remove(atAddressNumber);
			}

			return added;

		}

		private const string GENERAL = "general";
		private const string PREFIX_IN_LOG = "C";
		private SemaphoreSlim addingSemaphore = new SemaphoreSlim(1, 1);
		public async Task<Actor> LoadActorAsync(string atAddressNumber)
		{
			atAddressNumber = atAddressNumber.ToLower().Trim();

			string nameInlog = "";
			if (atAddressNumber != GENERAL)
				nameInlog = PREFIX_IN_LOG + atAddressNumber;
			else
				nameInlog = atAddressNumber;

			Actor actorInMemory = null;
			if (actors.TryGetValue(atAddressNumber, out actorInMemory)) return actorInMemory;

			await addingSemaphore.WaitAsync();
			try
			{
				Actor newActorToBeAdded = null;
				bool exists = actors.TryGetValue(atAddressNumber, out newActorToBeAdded);
				int count = actors.Count;
				if (exists) return newActorToBeAdded;

				newActorToBeAdded = new GamesEngine.CashierActor(nameInlog);
				await LoadBehindTheSceneAsync(newActorToBeAdded, atAddressNumber);

				bool added = actors.TryAdd(atAddressNumber, newActorToBeAdded);
				if (!added)
				{
					throw new Exception($"exists:{exists} PreviousCount:{count} atAddressNumber:{atAddressNumber} already exists in actors loaded. Size:{actors.Count}");
				}
				return newActorToBeAdded;
			}
			finally
			{
				addingSemaphore.Release();
			}

		}

		public IEnumerable<ActorsReponse> ListActors()
		{
			if (actors == null) return new List<ActorsReponse>();

			List<ActorsReponse> response = new List<ActorsReponse>();

			foreach (Actor actor in actors.Values)
			{
				response.Add(new ActorsReponse(
					name: actor.Name,
					dateOfLastActivity: actor.DateOfLastActivity
				));
			}

			return response.OrderByDescending(x => x.DaysWithoutActivity);
		}

		internal static void EventSourcingStorage(Actor actor, DatabaseType dbtype, string connectionString, string scriptBeforeRecovering)
		{
			actor.EventSourcingStorage(dbtype, connectionString, scriptBeforeRecovering, "PRODUCTION_DOES_NOT_NEED_IT");
		}

		internal static async Task EventSourcingStorageAsync(Actor actor, DatabaseType dbtype, string connectionString, string scriptBeforeRecovering)
		{
			await actor.EventSourcingStorageAsync(dbtype, connectionString, scriptBeforeRecovering, "PRODUCTION_DOES_NOT_NEED_IT");
		}

		protected abstract Task LoadBehindTheSceneAsync(Actor actor, string key);
	}

	public class ExchangeActors: Actors
	{
		public ExchangeActors(string dbSelected, string connectionString, string scriptBeforeRecovering):base(dbSelected, connectionString, scriptBeforeRecovering)
		{

		}

		protected override async Task LoadBehindTheSceneAsync(Actor actor, string atAddressNumber)
		{
			if (dbSelected == DatabaseType.MySQL.ToString())
			{
				await EventSourcingStorageAsync(actor, DatabaseType.MySQL, connectionString, scriptBeforeRecovering);

				if (actor.ItsANewOne)
				{
					await actor.PerformCmdAsync($@"
						gameEngineVersion('6.0');

						company = Company();
						companyIDf2401f84xa94ax4db3xaec4x83f5361730ae = company;
						",
					IpAddress.DEFAULT,
					UserInLog.ANONYMOUS);
				}
			}
			else if (dbSelected == DatabaseType.SQLServer.ToString())
			{
				await EventSourcingStorageAsync(actor, DatabaseType.SQLServer, connectionString, scriptBeforeRecovering);

				if (actor.ItsANewOne)
				{
					await actor.PerformCmdAsync($@"
						gameEngineVersion('6.0');

						company = Company();
						companyIDf2401f84xa94ax4db3xaec4x83f5361730ae = company;
						",
					IpAddress.DEFAULT,
					UserInLog.ANONYMOUS);
				}
			}
			else if (String.IsNullOrWhiteSpace(dbSelected))
			{
#if DEBUG
				if (Integration.UseKafka) Integration.Kafka.OffSetResetToLatest();
				switch (Integration.MockToStart)
				{
					case 0:
						break;
					default:
						throw new Exception($"There is no mock for {Integration.MockToStart}");
				}
#else
					throw new Exception($"There is no connection for {Integration.Db.DBSelected}");
#endif
			}
		}

	}

	public class ActorsReponse
	{
		public ActorsReponse(string name, DateTime dateOfLastActivity)
		{
			this.Name = name;
			this.DateOfLastActivity = dateOfLastActivity;
		}

		public string Name { get; }
		public DateTime DateOfLastActivity { get; }
		public double DaysWithoutActivity
		{
			get
			{
				return Math.Round(((DateTime.Now - DateOfLastActivity).TotalDays), 2);
			}
		}
	}

	public class ActorsReleaseReponse : ActorsReponse
	{
		public ActorsReleaseReponse(ActorsReponse actorResponse, bool released) : base(actorResponse.Name, actorResponse.DateOfLastActivity)
		{
			this.Released = released;
		}

		public bool Released { get; }
	}

	public class LottoBIActor : Puppeteer.EventSourcing.Actor
    {
        public LottoBIActor() : base(nameof(LottoBIActor))
        {
            OnLeaderInitialization += this.LeaderInitialization;
            OnAfterRecovering += this.AfterRecovering;
        }

        public void LeaderInitialization()
        {
            //TODO Cristian aqui va la implementacion de tomar el control, para que el actor sea el nuevo lider
        }

        public void AfterRecovering(DatabaseType dbType, string connection, string persona, int lastRecoveredId)
        {

		}
    }

    public class LoyaltyActor : Puppeteer.EventSourcing.Actor
    {
        public LoyaltyActor() : base(nameof(LoyaltyActor))
        {
            OnLeaderInitialization += this.LeaderInitialization;
            OnAfterRecovering += this.AfterRecovering;
        }

        public void LeaderInitialization()
        {
            //TODO Cristian aqui va la implementacion de tomar el control, para que el actor sea el nuevo lider
        }

        public void AfterRecovering(DatabaseType dbType, string connection, string persona, int lastRecoveredId)
        {
		}
    }

    public class KnowYourCustomerActor : Actor
    {
        public KnowYourCustomerActor() : base(nameof(KnowYourCustomerActor))
        {
			OnLeaderInitialization += this.LeaderInitialization;
			OnAfterRecovering += this.AfterRecovering;
		}

		public void LeaderInitialization()
		{
			//TODO Cristian aqui va la implementacion de tomar el control, para que el actor sea el nuevo lider
		}

		public void AfterRecovering(DatabaseType dbType, string connection, string persona, int lastRecoveredId)
		{

		}
	}

	public class ExchangeActor : Actor
	{
		public ExchangeActor() : base(nameof(ExchangeActor))
		{
			OnLeaderInitialization += this.LeaderInitialization;
			OnAfterRecovering += this.AfterRecovering;
		}

		public void LeaderInitialization()
		{
			//TODO Cristian aqui va la implementacion de tomar el control, para que el actor sea el nuevo lider
		}

		public void AfterRecovering(DatabaseType dbType, string connection, string persona, int lastRecoveredId)
		{
		}
	}

	public class LinesBIActor : Actor
	{
		public LinesBIActor() : base(nameof(LinesBIActor))
		{
			OnLeaderInitialization += this.LeaderInitialization;
			OnAfterRecovering += this.AfterRecovering;
		}

		public void LeaderInitialization()
		{
			//TODO Cristian aqui va la implementacion de tomar el control, para que el actor sea el nuevo lider
		}

		public void AfterRecovering(DatabaseType dbType, string connection, string persona, int lastRecoveredId)
		{

		}
	}

	public class LinesActor : Actor
	{
		public LinesActor() : base(nameof(LinesActor))
		{
			OnLeaderInitialization += this.LeaderInitialization;
			OnAfterRecovering += this.AfterRecovering;
		}

		public void LeaderInitialization()
		{
			//TODO Cristian aqui va la implementacion de tomar el control, para que el actor sea el nuevo lider
		}

		public void AfterRecovering(DatabaseType dbType, string connection, string persona, int lastRecoveredId)
		{

		}
	}

	public class LinesETLActor : Actor
	{
		public LinesETLActor() : base(nameof(LinesETLActor))
		{
			OnLeaderInitialization += this.LeaderInitialization;
			OnAfterRecovering += this.AfterRecovering;
		}

		public void LeaderInitialization()
		{
			//TODO Cristian aqui va la implementacion de tomar el control, para que el actor sea el nuevo lider
		}

		public void AfterRecovering(DatabaseType dbType, string connection, string persona, int lastRecoveredId)
		{

		}
	}

	public class GuardianActor : Actor
	{
		public GuardianActor() : base(nameof(GuardianActor))
		{
			OnLeaderInitialization += this.LeaderInitialization;
			OnAfterRecovering += this.AfterRecovering;
		}

		public void LeaderInitialization()
		{
			//TODO Cristian aqui va la implementacion de tomar el control, para que el actor sea el nuevo lider
		}

		public void AfterRecovering(DatabaseType dbType, string connection, string persona, int lastRecoveredId)
		{
		}
	}

    public class WishmakerActor : Actor
    {
        public WishmakerActor() : base(nameof(WishmakerActor))
        {
            OnLeaderInitialization += this.LeaderInitialization;
            OnAfterRecovering += this.AfterRecovering;
        }

        public void LeaderInitialization()
        {
            //TODO Cristian aqui va la implementacion de tomar el control, para que el actor sea el nuevo lider
        }

        public void AfterRecovering(DatabaseType dbType, string connection, string persona, int lastRecoveredId)
        {
        }
    }
}
