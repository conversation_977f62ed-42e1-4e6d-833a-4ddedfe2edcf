﻿using Connectors.town.connectors.drivers.artemis;
using GamesEngine;
using GamesEngine.Domains;
using GamesEngine.Settings;
using GamesEngine.Business;
using GamesEngine.Gameboards.Lotto;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using System.Text;
using System.Threading.Tasks;
using town.connectors.drivers.artemis;
using static GamesEngine.Exchange.PaymentProcessorsAndActionsByDomains;
using static GamesEngine.Finance.PaymentChannels;
using static town.connectors.CustomSettings;
using GamesEngine.Games.Lotto;

namespace LottoAPI.Controllers
{
    public class LotteryController : AuthorizeController
    {
        [HttpGet("api/lotto/preference")]
        [Authorize(Roles = "player")]
        public async Task<IActionResult> DefaultPreferencesAsync()
        {
            var result = await LottoAPI.Actor.PerformQryAsync(HttpContext, $@"
            {{
                preferences = lotto900.DefaultPreferences.GetAll;
                for (defaultPreferences:preferences)
                {{
                    print defaultPreferences.Key() key;
                    print defaultPreferences.IsEnabled isEnabled;
                    for (optionsSelected:defaultPreferences.OptionsSelected())
                    {{
                        print optionsSelected option;
                    }}
                    for (options:defaultPreferences.Options())
                    {{
                        print options option;
                    }}
                }}
            }}
            ");
            return result;
        }

      [HttpGet("api/lotto/domain")]
      [Authorize(Roles = "ManageDomainMenu")]
        public async Task<IActionResult> DomainsAsync()
        {
            IActionResult result = await LottoAPI.Actor.PerformQryAsync(HttpContext, $@"
                {{
                    for (domains:company.Sales.AllDomains)
                    {{
                        print domains.Url url;
                        if (domains.HasResourceUrl('logout'))
                        {{
                            print domains.ResourceUrl('logout') logoutUrl;
                        }}
                        if (domains.HasResourceUrl('rules'))
                        {{
                            print domains.ResourceUrl('rules') lottoRulesUrl;
                        }}
                        if (domains.HasResourceUrl('splashWeb'))
                        {{
                            print domains.ResourceUrl('splashWeb') lottoSplashWebUrl;
                        }}
                        if (domains.HasResourceUrl('splashMobile'))
                        {{
                            print domains.ResourceUrl('splashMobile') lottoSplashMobileUrl;
                        }}
                        if (domains.HasResourceUrl('lucky77'))
                        {{
                            print domains.ResourceUrl('lucky77') lucky77;
                        }}
                        if (domains.HasResourceUrl('reloadreward'))
                        {{
                            print domains.ResourceUrl('reloadreward') reloadReward;
                        }}
                        print company.Sales.CurrentStore.IsEnabledOn(domains) enabled;
                    }}
                }}
                ");

            return result;
        }

        [HttpGet("api/lotto/domain/enable")]
        [Authorize(Roles = "Profiles,GameTypeLimits,FinancialReport,WinnersReport,DrawingsReport,ViewUsers,campaignEdit")]
        public async Task<IActionResult> DomainsEnabledAsync()
        {
            return await DomainsAsync(true);
        }

        [HttpGet("api/lotto/domain/disable")]
        [Authorize(Roles = "player")]
        public async Task<IActionResult> DomainsdisabledAsync()
        {
            return await DomainsAsync(false);
        }

        private async Task<IActionResult> DomainsAsync(bool enable)
        {
            IActionResult result = await LottoAPI.Actor.PerformQryAsync(HttpContext, $@"
                {{
                    for (domains:company.Sales.AllDomains)
                    {{
                        if( company.Sales.CurrentStore.IsEnabledOn(domains) == {enable} )
                        {{
                            print domains.Id id;
                            print domains.Url url;
                            if (domains.HasResourceUrl('logout'))
                            {{
                                print domains.ResourceUrl('logout') logoutUrl;
                            }}
                            if (domains.HasResourceUrl('rules'))
                            {{
                                print domains.ResourceUrl('rules') lottoRulesUrl;
                            }}
                            if (domains.HasResourceUrl('splashWeb'))
                            {{
                                print domains.ResourceUrl('splashWeb') lottoSplashWebUrl;
                            }}
                            if (domains.HasResourceUrl('splashMobile'))
                            {{
                                print domains.ResourceUrl('splashMobile') lottoSplashMobileUrl;
                            }}
                            if (domains.HasResourceUrl('lucky77'))
                            {{
                                print domains.ResourceUrl('lucky77') lucky77;
                            }}
                            if (domains.HasResourceUrl('reloadreward'))
                            {{
                                print domains.ResourceUrl('reloadreward') reloadReward;
                            }}
                            print {enable} enabled;
                        }}
                    }}
                }}
                ");
            return result;
        }

        [HttpGet("api/keno/domains")]
        [Authorize(Roles = "KProfiles,KGameTypeLimits,KFinancialReport,KWinnersReport,KDrawingsReport,KViewUsers")]
        public async Task<IActionResult> KenoDomainsAsync(bool? enabled)
        {
            string commandToFilterDomains = string.Empty;
            string commandToCloseFilter = string.Empty;
            if (enabled.HasValue)
            {
                commandToFilterDomains = $"if( company.Sales.CurrentStore.IsEnabledOn(domains) == {enabled.Value} ){{";
                commandToCloseFilter = $"}}";
            }
            
            IActionResult result = await LottoAPI.Actor.PerformQryAsync(HttpContext, $@"
                {{
                    for (domains:company.Sales.AllDomains)
                    {{
                        {commandToFilterDomains}
                            print domains.Id id;
                            print domains.Url url;
                            if (domains.HasResourceUrl('logout'))
                            {{
                                print domains.ResourceUrl('logout') logoutUrl;
                            }}
                            if (domains.HasResourceUrl('rules'))
                            {{
                                print domains.ResourceUrl('rules') lottoRulesUrl;
                            }}
                            if (domains.HasResourceUrl('splashWeb'))
                            {{
                                print domains.ResourceUrl('splashWeb') lottoSplashWebUrl;
                            }}
                            if (domains.HasResourceUrl('splashMobile'))
                            {{
                                print domains.ResourceUrl('splashMobile') lottoSplashMobileUrl;
                            }}
                            if (domains.HasResourceUrl('lucky77'))
                            {{
                                print domains.ResourceUrl('lucky77') lucky77;
                            }}
                            if (domains.HasResourceUrl('reloadreward'))
                            {{
                                print domains.ResourceUrl('reloadreward') reloadReward;
                            }}
                            print company.Sales.CurrentStore.IsEnabledOn(domains) enabled;
                        {commandToCloseFilter}
                    }}
                }}
                ");
            return result;
        }

        [HttpGet("api/lotto/domain/{url}")]
        [Authorize(Roles = "administrator,assistant,player,support")]
        public async Task<IActionResult> GetDomainAsync(string url)
        {
            if (String.IsNullOrWhiteSpace(url)) return NotFound($"Parameter {nameof(url)} is required");
            var result = await LottoAPI.Actor.PerformQryAsync(HttpContext, $@"
            {{
                domain = company.Sales.DomainFrom('{url}');
                print domain.Id id;
                print domain.Url url;
                if (domain.HasResourceUrl('logout'))
                {{
                    print domain.ResourceUrl('logout') logoutUrl;
                }}
                if (domain.HasResourceUrl('rules'))
                {{
                    print domain.ResourceUrl('rules') lottoRulesUrl;
                }}
                if (domain.HasResourceUrl('splashWeb'))
                {{
                    print domain.ResourceUrl('splashWeb') lottoSplashWebUrl;
                }}
                if (domain.HasResourceUrl('splashMobile'))
                {{
                    print domain.ResourceUrl('splashMobile') lottoSplashMobileUrl;
                }}
                if (domain.HasResourceUrl('lucky77'))
                {{
                    print domain.ResourceUrl('lucky77') lucky77;
                }}
                if (domain.HasResourceUrl('reloadreward'))
                {{
                    print domain.ResourceUrl('reloadreward') reloadReward;
                }}
            }}
            ");
            return result;
        }

        [HttpPost("api/lotto/domain")]
        [Authorize(Roles = "b")]
        public async Task<IActionResult> CreateDomainAsync([FromBody]DomainBody body, [FromHeader(Name = "domain-url")] string domain)
        {
            if (body == null) return BadRequest("Body is required");
            if (string.IsNullOrWhiteSpace(HttpContext.Request.Headers["domain-url"])) return BadRequest("Header 'domain-url' is not sent in request");
            var url = string.IsNullOrWhiteSpace(body.Url) ? body.Url : Validator.StringEscape(body.Url);
            var logoutUrl = string.IsNullOrWhiteSpace(body.LogoutUrl) ? body.LogoutUrl : Validator.StringEscape(body.LogoutUrl);
            if (string.IsNullOrWhiteSpace(url)) return BadRequest($"Parameter {nameof(url)} is required");
            if (string.IsNullOrWhiteSpace(logoutUrl)) return BadRequest($"Parameter {nameof(logoutUrl)} is required");

            var resourceUrls = new StringBuilder();
            if (!string.IsNullOrWhiteSpace(body.RulesUrl))
            {
                resourceUrls.AppendLine($"domain.AddResourceUrl('rules', '{body.RulesUrl}');");
            }
            if (!string.IsNullOrWhiteSpace(body.SplashWebUrl))
            {
                resourceUrls.AppendLine($"domain.AddFlexibleResourceUrl('splashWeb', '{body.SplashWebUrl}');");
            }
            if (!string.IsNullOrWhiteSpace(body.SplashMobileUrl))
            {
                resourceUrls.AppendLine($"domain.AddFlexibleResourceUrl('splashMobile', '{body.SplashMobileUrl}');");
            }
            if (!string.IsNullOrWhiteSpace(body.Lucky77Url))
            {
                resourceUrls.AppendLine($"domain.AddResourceUrl('lucky77', '{body.Lucky77Url}');");
            }
            if (!string.IsNullOrWhiteSpace(body.ReloadUrl))
            {
                resourceUrls.AppendLine($"domain.AddResourceUrl('reloadreward', '{body.ReloadUrl}');");
            }

            var result = await LottoAPI.Actor.PerformChkThenCmdAsync(HttpContext, $@"
            {{
                existsDomain = company.Sales.ExistsDomain('{domain}');
                Check(existsDomain) Error 'Tenant domain {domain} does not exist';
            }}",
            $@"
            {{
                tenantDomain = company.Sales.DomainFrom('{domain}');
                Eval('domainId =' + company.Sales.NextDomainConsecutive() + ';');
                domain = company.Sales.CreateDomain(itIsThePresent, domainId, '{url}', tenantDomain.AgentId);
                domain.AddResourceUrl('logout', '{logoutUrl}');
                company.Sales.CurrentStore.Add(domain);
                {resourceUrls.ToString()}
            }}
            ");
            return result;
        }

        [HttpPut("api/lotto/domain")]
        [Authorize(Roles = "b")]
        public async Task<IActionResult> UpdateDomainAsync([FromBody]DomainUpdating body)
        {
            if (body == null) return NotFound("Body is required");
            var newUrl = string.IsNullOrWhiteSpace(body.NewUrl) ? body.NewUrl : Validator.StringEscape(body.NewUrl);
            var logoutUrl = string.IsNullOrWhiteSpace(body.LogoutUrl) ? body.LogoutUrl : Validator.StringEscape(body.LogoutUrl);
            if (String.IsNullOrWhiteSpace(newUrl)) return NotFound($"Parameter {nameof(newUrl)} is required");
            if (String.IsNullOrWhiteSpace(logoutUrl)) return NotFound($"Parameter {nameof(logoutUrl)} is required");
            if (String.IsNullOrWhiteSpace(body.CurrentUrl)) return NotFound($"Parameter {nameof(body.CurrentUrl)} is required");

            var result = await LottoAPI.Actor.PerformCmdAsync(HttpContext, $@"
            {{
                domain = company.Sales.DomainFrom('{body.CurrentUrl}');
                domain = company.Sales.UpdateMainUrl(itIsThePresent, domain, '{newUrl}');
                domain = company.Sales.DomainFrom('{newUrl}');
                domain.AddOrUpdateResourceUrl('logout', '{logoutUrl}');
                domain.AddOrUpdateResourceUrl('rules', '{body.RulesUrl}');
                domain.AddOrUpdateFlexibleResourceUrl('splashWeb', '{body.SplashWebUrl}');
                domain.AddOrUpdateFlexibleResourceUrl('splashMobile', '{body.SplashMobileUrl}');
                domain.AddOrUpdateResourceUrl('lucky77', '{body.Lucky77Url}');
                domain.AddOrUpdateResourceUrl('reloadreward', '{body.ReloadUrl}');
            }}
            ");
            return result;
        }

        [HttpPost("api/lotto/domain/enabled")]
        [Authorize(Roles = "GameTypeLimits,EnableDisableDomains")]
        public async Task<IActionResult> EnableDomainAsync([FromBody]DomainRemoving body)
        {
            if (body == null) return BadRequest("Body is required");
            var url = string.IsNullOrWhiteSpace(body.Url) ? body.Url : Validator.StringEscape(body.Url);
            if (string.IsNullOrWhiteSpace(url)) return BadRequest($"Parameter {nameof(url)} is required");

            string employeeName = Security.UserName(HttpContext);
            var msg = "Enabled";
            var result = await LottoAPI.Actor.PerformCmdAsync(HttpContext, $@"
            {{
                domain = company.Sales.DomainFrom('{url}');
                domain.AddAnnotation('{msg}', '{employeeName}', Now);
                company.Sales.CurrentStore.EnableDomain(domain);
            }}
            ");
            return result;
        }

        [HttpPost("api/lotto/domain/disabled")]
        [Authorize(Roles = "EnableDisableDomains")]
        public async Task<IActionResult> DisableDomainAsync([FromBody]DomainRemoving body)
        {
            if (body == null) return BadRequest("Body is required");
            var url = string.IsNullOrWhiteSpace(body.Url) ? body.Url : Validator.StringEscape(body.Url);
            if (string.IsNullOrWhiteSpace(url)) return BadRequest($"Parameter {nameof(url)} is required");

            string employeeName = Security.UserName(HttpContext);
            var msg = "Disabled";
            var result = await LottoAPI.Actor.PerformCmdAsync(HttpContext, $@"
            {{
                domain = company.Sales.DomainFrom('{url}');
                domain.AddAnnotation('{msg}', '{employeeName}', Now);
                company.Sales.CurrentStore.DisableDomain(domain);
            }}
            ");
            return result;
        }

        [HttpPost("api/lotto/state")]
        [Authorize(Roles = "AddStates")]
        public async Task<IActionResult> CreateNewStateAsync([FromBody] StateBody body)
		{
            if (body == null) return NotFound($"{nameof(body)} has not a valid value");
            if (String.IsNullOrWhiteSpace(body.Abbreviation)) return NotFound($"Parameter {nameof(body.Abbreviation)} is required");
            if (String.IsNullOrWhiteSpace(body.Name)) return NotFound($"Parameter {nameof(body.Name)} is required");
            if (String.IsNullOrWhiteSpace(body.EmployeeName)) return NotFound($"Parameter {nameof(body.EmployeeName)} is required");
            var abbreviation = Validator.StringEscape(body.Abbreviation);
            var name = Validator.StringEscape(body.Name);
            var employeeName = Validator.StringEscape(body.EmployeeName);

			var result = await LottoAPI.Actor.PerformCmdAsync(HttpContext, $@"
				state = lotto900.GetOrCreateState('{abbreviation}', '{name}', Now, '{employeeName}');
				Eval ('state' + state.Abbreviation + '= state;');
            ");
            return result;
        }

        [HttpGet("api/triz/states")]
        [Authorize(Roles = "DrawsDefinition,States,DisabledDraws")]
        public async Task<IActionResult> GetTrizStatesAsync()
        {
            return await GetStatesAsync(IdOfLotteryGame.Triz);
        }

        [HttpGet("api/pick/states")]
        [Authorize(Roles = "DrawsDefinition,States,DisabledDraws")]
        public async Task<IActionResult> GetPickStatesAsync()
        {
            return await GetStatesAsync(IdOfLotteryGame.Picks);
        }

        private async Task<IActionResult> GetStatesAsync(IdOfLotteryGame idOfLotteryGame)
        {
            var result = await LottoAPI.Actor.PerformQryAsync(HttpContext, $@"
				{{
                    lotteryGame = company.LotteryGamesPool.GetLotteryGame({idOfLotteryGame});

					for (states : lotteryGame.States())
					{{
						print states.Abbreviation abbreviationState;
						print states.Name nameState;
						print states.Enable isEnable;
						print lotteryGame.HasDrawsFor(states) hasDraws;
                        print lotteryGame.LogFor(states) log;
					}}
				}}
            ");
            return result;
        }

        [HttpGet("api/lotto/statesWithDraws")]
        [AllowAnonymous]
        public async Task<IActionResult> EnabledStatesWithDrawsAsync()
        {
            var result = await LottoAPI.Actor.PerformQryAsync(HttpContext, $@"
				{{
					for (states : lotto900.EnabledStatesWithDraws())
					{{
						print states.Abbreviation abbreviationState;
						print states.Name nameState;
					}}
				}}
            ");
            return result;
        }

        [HttpPut("api/triz/state/name")]
        [Authorize(Roles = "EditStates")]
        public async Task<IActionResult> TrizUpdateStateNameAsync([FromBody] StateBody body)
        {
            return await UpdateStateNameAsync(IdOfLotteryGame.Triz, body);
        }

        [HttpPut("api/pick/state/name")]
        [Authorize(Roles = "EditStates")]
        public async Task<IActionResult> PickUpdateStateNameAsync([FromBody] StateBody body)
        {
            return await UpdateStateNameAsync(IdOfLotteryGame.Picks, body);
        }

		private async Task<IActionResult> UpdateStateNameAsync(IdOfLotteryGame idOfLotteryGame, [FromBody] StateBody body)
		{
            if (body == null) return NotFound($"{nameof(body)} has not a valid value");
            if (String.IsNullOrWhiteSpace(body.Name)) return NotFound($"Parameter {nameof(body.Name)} is required");
            if (String.IsNullOrWhiteSpace(body.EmployeeName)) return NotFound($"Parameter {nameof(body.EmployeeName)} is required");
            var name = Validator.StringEscape(body.Name);
            var employeeName = Validator.StringEscape(body.EmployeeName);

            StringBuilder commandUpdate = new StringBuilder();
            commandUpdate.AppendLine($@"lotteryGame = company.LotteryGamesPool.GetLotteryGame({idOfLotteryGame});");
            if (idOfLotteryGame == IdOfLotteryGame.Picks)
            {
                if (String.IsNullOrWhiteSpace(body.Abbreviation)) return NotFound($"Parameter {nameof(body.Abbreviation)} is required");
                var abbreviation = Validator.StringEscape(body.Abbreviation);

                commandUpdate.AppendLine($@"lotteryGame.RenameState(state{abbreviation},'{name}', Now, '{employeeName}');");
            }
            else if (idOfLotteryGame == IdOfLotteryGame.Triz)
            {
                commandUpdate.AppendLine($@"lotteryTriz = lotteryGame.GetLottery();");
                commandUpdate.AppendLine($@"state = lotteryTriz.State;");
                commandUpdate.AppendLine($@"lotteryGame.RenameState(state, '{name}', Now, '{employeeName}');");
            }
            else
            {
                return BadRequest($"Parameter {nameof(idOfLotteryGame)} is invalid");
            }

            var result = await LottoAPI.Actor.PerformCmdAsync(HttpContext,  $@"
            {{
                {commandUpdate}
            }}
            ");
            return result;
        }

        [Authorize(Roles = "EnableDisableStates")]
        [HttpPost("api/lotto/state/disabled")]
		public async Task<IActionResult> DisableStateAsync([FromBody] StateEnabling body)
		{
            if (body == null) return NotFound("Body is required");
            if (String.IsNullOrWhiteSpace(body.Abbreviation)) return NotFound($"Parameter {nameof(body.Abbreviation)} is required");
            if (String.IsNullOrWhiteSpace(body.EmployeeName)) return NotFound($"Parameter {nameof(body.EmployeeName)} is required");

            var result = await LottoAPI.Actor.PerformCmdAsync(HttpContext,  $@"
            {{
				isAssignToLottery= lotto900.IsStateAssignedToLottery('{body.Abbreviation}');
				if (isAssignToLottery) 
					lotto900.DisableState('{body.Abbreviation}', Now, '{body.EmployeeName}');
				else
					lotto900.DeleteState('{body.Abbreviation}', Now, '{body.EmployeeName}');
            }}
            ");
            return result;
        }

        [Authorize(Roles = "EnableDisableStates")]
        [HttpPost("api/lotto/state/enabled")]
		public async Task<IActionResult> EnableStateAsync([FromBody] StateEnabling body)
		{
            if (body == null) return NotFound("Body is required");
            if (String.IsNullOrWhiteSpace(body.Abbreviation)) return NotFound($"Parameter {nameof(body.Abbreviation)} is required");
            if (String.IsNullOrWhiteSpace(body.EmployeeName)) return NotFound($"Parameter {nameof(body.EmployeeName)} is required");

            var result = await LottoAPI.Actor.PerformCmdAsync(HttpContext,  $@"
            {{
				lotto900.EnableState('{body.Abbreviation}', Now, '{body.EmployeeName}');
            }}
            ");
            return result;
        }

        [HttpGet("api/triz/officialLink")]
        [Authorize(Roles = "OfficialSites")]
        public async Task<IActionResult> TrizOfficialLinksAsync()
        {
            return await OfficialLinksAsync(IdOfLotteryGame.Triz);
        }

        [HttpGet("api/pick/officialLink")]
        [Authorize(Roles = "OfficialSites")]
        public async Task<IActionResult> PicksOfficialLinksAsync()
        {
            return await OfficialLinksAsync(IdOfLotteryGame.Picks);
        }

        private async Task<IActionResult> OfficialLinksAsync(IdOfLotteryGame idOfLotteryGame)
        {
            var result = await LottoAPI.Actor.PerformQryAsync(HttpContext, $@"
            {{
                lotteryGame = company.LotteryGamesPool.GetLotteryGame({idOfLotteryGame});
                for (link : lotteryGame.OfficialLinks.LinksWithoutPowerball())
                {{
				    print link.State.Abbreviation state;
				    print link.Url url;
                }}
            }}
            ");
            return result;
        }

        [HttpGet("api/lotto/officialLink/powerball")]
        [Authorize(Roles = "a1")]
        public async Task<IActionResult> OfficialLinkPowerballAsync()
        {
            var result = await LottoAPI.Actor.PerformQryAsync(HttpContext, $@"
            {{
                print lotto900.OfficialLinks.UrlPowerball() url;
            }}
            ");
            return result;
        }

        [HttpPut("api/triz/officialLink")]
        [Authorize(Roles = "j")]
        public async Task<IActionResult> TrizUpdateStateLinkAsync([FromBody] OfficialLink body)
        {
            return await UpdateStateLinkAsync(IdOfLotteryGame.Triz, body);
        }

        [HttpPut("api/pick/officialLink")]
        [Authorize(Roles = "j")]
        public async Task<IActionResult> PickUpdateStateLinkAsync([FromBody] OfficialLink body)
        {
            return await UpdateStateLinkAsync(IdOfLotteryGame.Picks, body);
        }

        private async Task<IActionResult> UpdateStateLinkAsync(IdOfLotteryGame idOfLotteryGame, [FromBody]OfficialLink body)
        {
            var url = Validator.StringEscape(body.Url);
            if (String.IsNullOrWhiteSpace(url)) return NotFound($"Parameter {nameof(url)} is required");
            if (String.IsNullOrWhiteSpace(body.State)) return NotFound($"Parameter {nameof(body.State)} is required");

            StringBuilder commandUpdate = new StringBuilder();
            if (idOfLotteryGame == IdOfLotteryGame.Picks)
            {
                commandUpdate.AppendLine($@"lotteryGame.OfficialLinks.Update(state{body.State}, '{url}');");
            }
            else if (idOfLotteryGame == IdOfLotteryGame.Triz)
            {
                commandUpdate.AppendLine($"lotteryTriz = lotteryGame.GetLottery();");
                commandUpdate.AppendLine($"state = lotteryTriz.State;");
                commandUpdate.AppendLine($"lotteryGame.OfficialLinks.Update(state, '{url}');");
            }
            else
            {
                return BadRequest($"Parameter {nameof(idOfLotteryGame)} is invalid");
            }

            var result = await LottoAPI.Actor.PerformCmdAsync(HttpContext,  $@"
            {{
                lotteryGame = company.LotteryGamesPool.GetLotteryGame({idOfLotteryGame});
                {commandUpdate}
            }}
            ");
            return result;
        }

        [HttpPut("api/lotto/officialLink/powerball")]
        [Authorize(Roles = "z")]
        public async Task<IActionResult> UpdateOfficialLinkPowerballAsync([FromBody]OfficialLinkPowerballBody body)
        {
            var url = body.Url.Replace("\\", @"\u005C").Replace("'", "\\\'").Replace("’", @"\u2019");
            if (String.IsNullOrWhiteSpace(url)) return NotFound($"Parameter {nameof(url)} is required");
            var result = await LottoAPI.Actor.PerformCmdAsync(HttpContext, $@"
            {{
				lotto900.OfficialLinks.UpdatePowerball('{url}');
            }}
            ");
            return result;
        }

        [Authorize(Roles = "SaveDaysForward")]
        [HttpPost("api/lotto/ticket/sell/day")]
        public async Task<IActionResult> ChangeDaysToSellTicketsAsync(string riskProfile, [FromBody]DaysToSell body)
        {
            if (string.IsNullOrWhiteSpace(riskProfile)) return BadRequest($"{nameof(riskProfile)} is required");
            if (body.Days == 0) return NotFound($"Parameter {nameof(body.Days)} is required");
            if (body.Days < 0) return NotFound($"Parameter {nameof(body.Days)} cannot be negative");

            string employeeName = Security.UserName(HttpContext);
            var result = await LottoAPI.Actor.PerformCmdAsync(HttpContext, $@"
            {{
				lotto900.RiskProfiles.GetRiskProfile('{riskProfile}').UpdateDaysForwardToSell({body.Days}, '{employeeName}', Now);
            }}
            ");
            return result;
        }

        [HttpGet("api/lotto/ticket/sell/day")]
        [Authorize(Roles = "f")]
        public async Task<IActionResult> DaysToSellTicketsAsync(string riskProfile)
        {
            if (string.IsNullOrWhiteSpace(riskProfile)) return BadRequest($"{nameof(riskProfile)} is required");

            var result = await LottoAPI.Actor.PerformQryAsync(HttpContext, $@"
            {{
                print lotto900.RiskProfiles.GetRiskProfile('{riskProfile}').DaysForwardToSell maxDaysFromTodayToSellATicket;
            }}
            ");
            return result;
        }

        [HttpGet("api/lotto/ticket/sell/daysForward")]
        [Authorize(Roles = "player")]
        public async Task<IActionResult> DaysForwardToSellTicketsAsync([FromHeader(Name = "domain-url")] string domain)
        {
            if (string.IsNullOrWhiteSpace(HttpContext.Request.Headers["domain-url"])) return BadRequest("Header 'domain-url' is not sent in request");

            var result = await LottoAPI.Actor.PerformQryAsync(HttpContext, $@"
            {{
                domain = company.Sales.DomainFrom('{domain}');
                print lotto900.RiskProfiles.GetRiskProfile(domain).DaysForwardToSell maxDaysFromTodayToSellATicket;
            }}
            ");
            return result;
        }

        [HttpGet("api/lotto/gameTypes")]
        [Authorize(Roles = "l")]
        public async Task<IActionResult> GameTypesAsync(string url)
        {
            var isUrlEmpty = string.IsNullOrWhiteSpace(url);
            
            IActionResult result;
            if (isUrlEmpty)
            {
                result = await LottoAPI.Actor.PerformQryAsync(HttpContext, $@"
                {{
                    for (gameTypes:company.LotteryGamesPool.GameTypes.GetAll)
                    {{
                        key = gameTypes.KeyName;
                        print key keyName;
                        print gameTypes.Name name;
                        for (domains:company.Sales.AllDomains)
                        {{
                            print domains.Url url;
                            print company.LotteryGamesPool.GameTypes.IsEnabled(key, domains) enabled;
                        }}
                    }}
                }}
                ");
            }
            else
            {
                result = await LottoAPI.Actor.PerformQryAsync(HttpContext, $@"
                {{
                    domain = company.Sales.DomainFrom('{url}');
                    for (gameTypes:company.LotteryGamesPool.GameTypes.GetAll)
                    {{
                        key = gameTypes.KeyName;
                        print key keyName;
                        print gameTypes.Name name;
                        print company.LotteryGamesPool.GameTypes.IsEnabled(key, domain) enabled;
                    }}
                }}
                ");
            }
            return result;
        }

        [HttpGet("api/lotto/players/gameTypes")]
        [Authorize(Roles = "player")]
        public async Task<IActionResult> GameTypesForPlayerAsync(string url)
        {
            var isUrlEmpty = string.IsNullOrWhiteSpace(url);
            Agents agent = Security.Agent(User);

            string validExternalGameTypes = string.Empty;
            bool validateExternalGameTypes = false;
            if (agent == Agents.ARTEMIS)
            {
                string playerId = Security.PlayerId(HttpContext);
                if (String.IsNullOrWhiteSpace(playerId)) return BadRequest(nameof(playerId));
                string validPlayerId = Validator.StringEscape(playerId);
                var resultQry = await LottoAPI.Actor.PerformQryAsync(HttpContext, $@"
                    {{
                        player = company.Players.SearchPlayer('{validPlayerId}');
                        customer = company.CustomerByPlayer(player);
                        print customer.accountNumber AccountNumber;
                    }}
                ");
                if (!(resultQry is OkObjectResult))
                {
                    return BadRequest($@"Error:{((ObjectResult)resultQry).Value.ToString()}");
                }

                OkObjectResult o = (OkObjectResult)resultQry;
                string json2 = o.Value.ToString();
                var customerExistence = JsonConvert.DeserializeObject<CustomerExistence>(json2);

                PlayerBalanceBody infoBalance;
                var paymentProcessor = WholePaymentProcessor.Instance().SearchBalanceProcesorBy(typeof(BalanceInfo));
                using (RecordSet recordSet = paymentProcessor.GetRecordSet())
                {
                    recordSet.SetParameter("customerId", customerExistence.AccountNumber);

                    infoBalance = await paymentProcessor.ExecuteAsync<PlayerBalanceBody>(DateTime.Now, recordSet);
                }
                validExternalGameTypes = string.Join("','",infoBalance.betTypeLimit.Select(type=> string.Concat(type.ToLowerInvariant().Where(c => !char.IsWhiteSpace(c)))));
                validateExternalGameTypes = true;
            }

            var printForExternalGameTypes = string.IsNullOrWhiteSpace(validExternalGameTypes) ? string.Empty : $"print {{'{validExternalGameTypes}'}} validExternalGameTypes;";
            IActionResult result;
            if (isUrlEmpty)
            {
                result = await LottoAPI.Actor.PerformQryAsync(HttpContext, $@"
                {{
                    for (gameTypes:company.LotteryGamesPool.GameTypes.GetAll)
                    {{
                        key = gameTypes.KeyName;
                        print key keyName;
                        print gameTypes.Name name;
                        for (domains:company.Sales.AllDomains)
                        {{
                            print domains.Url url;
                            print company.LotteryGamesPool.GameTypes.IsEnabled(key, domains) enabled;
                        }}
                        if(gameTypes.IsPick)
                        {{
                            print lotto900.HasFireball(gameTypes) hasFireball;
                        }}
                    }}
                    print {validateExternalGameTypes} validateExternalGameTypes;
                    {printForExternalGameTypes}
                }}
                ");
            }
            else
            {
                result = await LottoAPI.Actor.PerformQryAsync(HttpContext, $@"
                {{
                    domain = company.Sales.DomainFrom('{url}');
                    for (gameTypes:company.LotteryGamesPool.GameTypes.GetAll)
                    {{
                        key = gameTypes.KeyName;
                        print key keyName;
                        print gameTypes.Name name;
                        print company.LotteryGamesPool.GameTypes.IsEnabled(key, domain) enabled;
                        if(gameTypes.IsPick)
                        {{
                            print lotto900.HasFireball(gameTypes) hasFireball;
                        }}
                    }}
                    print {validateExternalGameTypes} validateExternalGameTypes;
                    {printForExternalGameTypes}
                }}
                ");
            }
            return result;
        }

        [HttpPut("api/lotto/gameType/enabled")]
        [Authorize(Roles = "m")]
        public async Task<IActionResult> EnableGameTypeInDomainAsync([FromBody]GameTypesBody body)
        {
            if (body == null) return NotFound("Body is required");

            var arrayKeyNames = body.GameTypes.Select(gameType => gameType.KeyName);
            if (arrayKeyNames.Any(keyName => string.IsNullOrWhiteSpace(keyName))) return NotFound($"Any parameter in {nameof(arrayKeyNames)} is empty");
            var keyNames = string.Join(",", arrayKeyNames);

            var arrayDomains = body.GameTypes.Select(gameType => gameType.Url);
            if (arrayDomains.Any(domain => string.IsNullOrWhiteSpace(domain))) return NotFound($"Any parameter in {nameof(arrayDomains)} is empty");
            var urls = string.Join(",", arrayDomains);

            var arrayEnabled = body.GameTypes.Select(gameType => gameType.Enabled);
            var enables = string.Join(",", arrayEnabled);

            var result = await LottoAPI.Actor.PerformCmdAsync(HttpContext, $@"
            {{
                company.LotteryGamesPool.GameTypes.EnableOrDisable('{keyNames}', '{urls}', '{enables}');
            }}
            ");
            return result;
        }

        [HttpGet("api/lotto/store")]
        [Authorize(Roles = "player")]
        public async Task<IActionResult> StoreIdAsync()
        {
            var result = await LottoAPI.Actor.PerformQryAsync(HttpContext, $@"
                {{
                    store = company.Sales.CurrentStore;
                    print store.Id storeId;
                    print store.SubscriberId from;
                }}
            ");
            return result;
        }

        [HttpGet("api/currencies")]
        [Authorize(Roles = "player,i1")]
        public async Task<IActionResult> CurrenciesAsync()
        {
            var result = await LottoAPI.Actor.PerformQryAsync(HttpContext, $@"
                {{
                    print lotto900.StandardCurrency standardCurrency;
                    print lotto900.StandardCurrencyId standardCurrencyId;
                    print lotto900.StandardCurrencyUnicode standardCurrencyUnicode;
                    print lotto900.RewardCurrency rewardCurrency;
                    print lotto900.RewardCurrencyId rewardCurrencyId;
                    print lotto900.RewardCurrencyUnicode rewardCurrencyUnicode;
                }}
            ");
            return result;
        }

        [HttpGet("api/lotto/ticketEntry/powerball")]
        [Authorize(Roles = "v")]
        public async Task<IActionResult> PresetTicketEntriesAsync()
        {
            var result = await LottoAPI.Actor.PerformQryAsync(HttpContext, $@"
            {{
                presetTicketEntries = lotto900.PresetTicketEntriesPowerball;
                print presetTicketEntries.Count totalPreset;
                for (ticketEntries:presetTicketEntries.GetAll)
                {{
                    print ticketEntries.NumberOfTicketEntry numberOfTicketEntry;
                    print ticketEntries.Enabled enabled;
                    print presetTicketEntries.IsDefault(ticketEntries.NumberOfTicketEntry) isDefault;
                }}
            }}
            ");
            return result;
        }

        [HttpPost("api/lotto/ticketEntry/powerball")]
        [Authorize(Roles = "w")]
        public async Task<IActionResult> AddPresetTicketEntryAsync([FromBody]TicketEntry body)
        {
            if (body == null) return NotFound("Body is required");
            if (body.NumberOfTicketEntry <= 0) return NotFound($"Parameter {nameof(body.NumberOfTicketEntry)} must be greater than 0");

            var result = await LottoAPI.Actor.PerformCmdAsync(HttpContext, $@"
            {{
                lotto900.PresetTicketEntriesPowerball.Add({body.NumberOfTicketEntry});
            }}
            ");
            return result;
        }

        [HttpPut("api/lotto/ticketEntry/powerball")]
        [Authorize(Roles = "w")]
        public async Task<IActionResult> UpdatePresetTicketEntryAsync([FromBody]TicketEntryUpdating body)
        {
            if (body == null) return NotFound("Body is required");
            if (body.CurrentNumberOfTicketEntry <= 0) return NotFound($"Parameter {nameof(body.CurrentNumberOfTicketEntry)} must be greater than 0");
            if (body.NewNumberOfTicketEntry <= 0) return NotFound($"Parameter {nameof(body.NewNumberOfTicketEntry)} must be greater than 0");

            var result = await LottoAPI.Actor.PerformCmdAsync(HttpContext, $@"
            {{
                lotto900.PresetTicketEntriesPowerball.Update({body.CurrentNumberOfTicketEntry},{body.NewNumberOfTicketEntry});
            }}
            ");
            return result;
        }

        [HttpDelete("api/lotto/ticketEntry/powerball")]
        [Authorize(Roles = "w")]
        public async Task<IActionResult> RemovePresetTicketEntryAsync(int numberOfTicketEntry)
        {
            if (numberOfTicketEntry <= 0) return NotFound($"Parameter {nameof(numberOfTicketEntry)} must be greater than 0");
            var result = await LottoAPI.Actor.PerformCmdAsync(HttpContext, $@"
            {{
                lotto900.PresetTicketEntriesPowerball.Remove({numberOfTicketEntry});
            }}
            ");
            return result;
        }

        [HttpPost("api/lotto/ticketEntry/powerball/enabled")]
        [Authorize(Roles = "x")]
        public async Task<IActionResult> EnablePresetTicketEntryAsync([FromBody]TicketEntry body)
        {
            if (body == null) return NotFound("Body is required");
            if (body.NumberOfTicketEntry <= 0) return NotFound($"Parameter {nameof(body.NumberOfTicketEntry)} must be greater than 0");

            var result = await LottoAPI.Actor.PerformCmdAsync(HttpContext, $@"
            {{
                lotto900.PresetTicketEntriesPowerball.Enable({body.NumberOfTicketEntry});
            }}
            ");
            return result;
        }

        [HttpPost("api/lotto/ticketEntry/powerball/disabled")]
        [Authorize(Roles = "x")]
        public async Task<IActionResult> DisablePresetTicketEntryAsync([FromBody]TicketEntry body)
        {
            if (body == null) return NotFound("Body is required");
            if (body.NumberOfTicketEntry <= 0) return NotFound($"Parameter {nameof(body.NumberOfTicketEntry)} must be greater than 0");

            var result = await LottoAPI.Actor.PerformCmdAsync(HttpContext, $@"
            {{
                lotto900.PresetTicketEntriesPowerball.Disable({body.NumberOfTicketEntry});
            }}
            ");
            return result;
        }

        [HttpPut("api/lotto/ticketEntry/powerball/default")]
        [Authorize(Roles = "y")]
        public async Task<IActionResult> UpdateDefaultPresetTicketEntriesAsync([FromBody]DefaultTicketEntry body)
        {
            if (body == null) return NotFound("Body is required");
            if (body.DefaultNumberSelectedOfTicketEntry <= 0) return NotFound($"Parameter {nameof(body.DefaultNumberSelectedOfTicketEntry)} must be greater than 0");

            var result = await LottoAPI.Actor.PerformCmdAsync(HttpContext, $@"
            {{
                presetTicketEntries = lotto900.PresetTicketEntriesPowerball;
                presetTicketEntries.DefaultNumberSelectedOfTicketEntry={body.DefaultNumberSelectedOfTicketEntry};
            }}
            ");
            return result;
        }

        [HttpGet("api/keno/spots")]
        [Authorize(Roles = "player")]
        public async Task<IActionResult> CurrentSpotsAsync()
        {
            var result = await LottoAPI.Actor.PerformQryAsync(HttpContext, $@"
                {{
                    print keno.CurrentSpotsAsText currentSpots;
                    logEntries = keno.LogKenoSpots.LastEntries(5);
			        for (log:logEntries)
			        {{
				        print log.DateFormattedAsText date;
				        print log.Who who;
				        print log.Message message;
			        }}
                }}
            ");
            return result;
        }

        [HttpPost("api/keno/spots/{ticketType}")]
        [Authorize(Roles = "player")]
        public async Task<IActionResult> SetSpotsAsync(TicketType ticketType)
        {
            if (ticketType != TicketType.K10 && ticketType != TicketType.K12) return BadRequest($"{nameof(ticketType)} {ticketType} is not valid");

            string employeeName = Security.UserName(HttpContext);
            var script = ticketType == TicketType.K10 ? $"keno.Set10Spots(Now, '{employeeName}');" : $"keno.Set12Spots(Now, '{employeeName}');";
            var result = await LottoAPI.Actor.PerformCmdAsync(HttpContext, $@"
                {{
                    {script}
                }}
            ");
            return result;
        }

        [HttpGet("api/keno/multipliers")]
        [Authorize(Roles = "player")]
        public async Task<IActionResult> MultipliersAsync()
        {
            var result = await LottoAPI.Actor.PerformQryAsync(HttpContext, $@"
                {{
                    for (multipliers:keno.ValidMultipliers)
                    {{
                        multiplier = multipliers;
                        print multiplier multiplier;
                    }}
                }}
            ");
            return result;
        }

        [HttpPut("api/keno/multipliers")]
        [Authorize(Roles = "player")]
        public async Task<IActionResult> SetMultipliersAsync([FromBody] MultipliersBody body)
        {
            if (body == null) return BadRequest("Body is required");

            var multipliers = string.Join(',', body.Multipliers);

            var result = await LottoAPI.Actor.PerformCmdAsync(HttpContext, $@"
                {{
                    keno.SetValidMultipliers({{{multipliers}}});
                }}
            ");
            return result;
        }

        [DataContract(Name = "DomainAndStoreExistenceInAgent")]
        public class DomainAndStoreExistenceInAgent
        {
            [DataMember(Name = "containsDomain")]
            public bool ContainsDomain { get; set; }
            [DataMember(Name = "containsStore")]
            public bool ContainsStore { get; set; }
        }

        [DataContract(Name = "officialLink")]
        public class OfficialLink
        {
            [DataMember(Name = "state")]
            public string State { get; set; }
            [DataMember(Name = "url")]
            public string Url { get; set; }
        }

        [DataContract(Name = "officialLinkPowerballBody")]
        public class OfficialLinkPowerballBody
        {
            [DataMember(Name = "url")]
            public string Url { get; set; }
        }

        [DataContract(Name = "stateBody")]
        public class StateBody
        {
            [DataMember(Name = "abbreviation")]
            public string Abbreviation { get; set; }
            [DataMember(Name = "name")]
            public string Name { get; set; }
            [DataMember(Name = "employeeName")]
            public string EmployeeName { get; set; }
        }

        [DataContract(Name = "stateEnabling")]
        public class StateEnabling
        {
            [DataMember(Name = "abbreviation")]
            public string Abbreviation { get; set; }
            [DataMember(Name = "employeeName")]
            public string EmployeeName { get; set; }
        }

        [DataContract(Name = "domain")]
        public class DomainBody
        {
            [DataMember(Name = "url")]
            public string Url { get; set; }
            [DataMember(Name = "logoutUrl")]
            public string LogoutUrl { get; set; }
            [DataMember(Name = "lottoSplashWebUrl")]
            public string SplashWebUrl { get; set; }
            [DataMember(Name = "lottoSplashMobileUrl")]
            public string SplashMobileUrl { get; set; }
            [DataMember(Name = "lottoRulesUrl")]
            public string RulesUrl { get; set; }
            [DataMember(Name = "lucky77Url")]
            public string Lucky77Url { get; set; }
            [DataMember(Name = "reloadUrl")]
            public string ReloadUrl { get; set; }
        }

        [DataContract(Name = "domainUpdating")]
        public class DomainUpdating
        {
            [DataMember(Name = "currentUrl")]
            public string CurrentUrl { get; set; }
            [DataMember(Name = "newUrl")]
            public string NewUrl { get; set; }
            [DataMember(Name = "logoutUrl")]
            public string LogoutUrl { get; set; }
            [DataMember(Name = "lottoSplashWebUrl")]
            public string SplashWebUrl { get; set; }
            [DataMember(Name = "lottoSplashMobileUrl")]
            public string SplashMobileUrl { get; set; }
            [DataMember(Name = "lottoRulesUrl")]
            public string RulesUrl { get; set; }
            [DataMember(Name = "lucky77Url")]
            public string Lucky77Url { get; set; }
            [DataMember(Name = "reloadUrl")]
            public string ReloadUrl { get; set; }
        }

        [DataContract(Name = "domainRemoving")]
        public class DomainRemoving
        {
            [DataMember(Name = "url")]
            public string Url { get; set; }
        }

        [DataContract(Name = "daysToSell")]
        public class DaysToSell
        {
            [DataMember(Name = "days")]
            public int Days { get; set; }
        }

        [DataContract(Name = "defaultTicketEntry")]
        public class DefaultTicketEntry
        {
            [DataMember(Name = "defaultNumberSelectedOfTicketEntry")]
            public int DefaultNumberSelectedOfTicketEntry { get; set; }
        }

        [DataContract(Name = "ticketEntry")]
        public class TicketEntry
        {
            [DataMember(Name = "numberOfTicketEntry")]
            public int NumberOfTicketEntry { get; set; }
        }

        [DataContract(Name = "ticketEntryUpdating")]
        public class TicketEntryUpdating
        {
            [DataMember(Name = "newNumberOfTicketEntry")]
            public int NewNumberOfTicketEntry { get; set; }
            [DataMember(Name = "currentNumberOfTicketEntry")]
            public int CurrentNumberOfTicketEntry { get; set; }
        }

        [DataContract(Name = "gameTypesBody")]
        public class GameTypesBody
        {
            [DataMember(Name = "gameTypes")]
            public GameType[] GameTypes { get; set; }
        }

        [DataContract(Name = "gameType")]
        public class GameType
        {
            [DataMember(Name = "keyName")]
            public string KeyName { get; set; }
            [DataMember(Name = "url")]
            public string Url { get; set; }
            [DataMember(Name = "enabled")]
            public bool Enabled { get; set; }
        }

        [DataContract(Name = "CustomerExistence")]
        public class CustomerExistence
        {
            [DataMember(Name = "accountNumber")]
            public string AccountNumber { get; set; }
        }

        [DataContract(Name = "MultipliersBody")]
        public class MultipliersBody
        {
            [DataMember(Name = "multipliers")]
            public List<int> Multipliers { get; set; }
        }
    }
}
