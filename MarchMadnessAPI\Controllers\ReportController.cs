﻿using GamesEngine.Games.Tournaments;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Threading.Tasks;

namespace MarchMadnessAPI.Controllers
{
	public class ReportController : AuthorizeController
    {
		[Authorize(Roles = "b11")]
		[HttpGet("api/tournament/{gameAbbreviation}/{year:int:length(4)}/report/totalOfBracketsAndGroups")]
        public async Task<IActionResult> TotalOfBracketAndGroupsAsync(string gameAbbreviation, int year)
        {
			GameAbbreviation abbreviation;
			IActionResult result;
			if ( ! Enum.TryParse(gameAbbreviation, out abbreviation)) return NotFound($"{nameof(gameAbbreviation)} '{gameAbbreviation}' is not valid");
			switch (abbreviation)
			{
				case GameAbbreviation.mm:
					result = MarchMadness.MarchMadness.PerformQry(HttpContext, $@"
						{{
							rules = company.GetMarchMadnessEdition({year});
							tournament = rules.Tournament;

							print tournament.CountBrackets countBrackets;
							print rules.CountRooms countRooms;

							round64 = 1;
							round32 = 2;
							round16 = 3;
							bracketsCounter = tournament.BracketsCounterByRoundsReport();
							for ( fees : bracketsCounter.ValidFeesForReport )
							{{
								fee = fees;
								print fee fee;

								print bracketsCounter.CountBracketsForRound64ByFee(fee) countBracketsPerRound64;
								print bracketsCounter.CountBracketsForRound32ByFee(fee) countBracketsPerRound32;
								print bracketsCounter.CountBracketsForRound16ByFee(fee) countBracketsPerRound16;
								print bracketsCounter.CountBracketsByFee(fee) countBracketsPerRound64And32And16;

								countMembersInGeneralRoomsPerRound64 = rules.CountMembersInGeneralRoomsFor(round64,fee);
								countMembersInGeneralRoomsPerRound32 = rules.CountMembersInGeneralRoomsFor(round32,fee);
								countMembersInGeneralRoomsPerRound16 = rules.CountMembersInGeneralRoomsFor(round16,fee);
								print countMembersInGeneralRoomsPerRound64 countMembersInGeneralRoomsPerRound64;
								print countMembersInGeneralRoomsPerRound32 countMembersInGeneralRoomsPerRound32;
								print countMembersInGeneralRoomsPerRound16 countMembersInGeneralRoomsPerRound16;
								countMembersInGeneralRoomsPerRound64And32And16 = countMembersInGeneralRoomsPerRound64 + countMembersInGeneralRoomsPerRound32 + countMembersInGeneralRoomsPerRound16;
								print countMembersInGeneralRoomsPerRound64And32And16 countMembersInGeneralRoomsPerRound64And32And16;

								totalContributionInGeneralPoolsPerRound64 = rules.ContributionInGeneralPoolsFor(round64,fee);
								totalContributionInGeneralPoolsPerRound32 = rules.ContributionInGeneralPoolsFor(round32,fee);
								totalContributionInGeneralPoolsPerRound16 = rules.ContributionInGeneralPoolsFor(round16,fee);
								print totalContributionInGeneralPoolsPerRound64 totalContributionInGeneralPoolsPerRound64;
								print totalContributionInGeneralPoolsPerRound32 totalContributionInGeneralPoolsPerRound32;
								print totalContributionInGeneralPoolsPerRound16 totalContributionInGeneralPoolsPerRound16;
								totalContributionInGeneralPoolsPerRound64And32And16 = totalContributionInGeneralPoolsPerRound64 + totalContributionInGeneralPoolsPerRound32 + totalContributionInGeneralPoolsPerRound16;
								print totalContributionInGeneralPoolsPerRound64And32And16 totalContributionInGeneralPoolsPerRound64And32And16;
							}}

							countBracketsPerRound64 = bracketsCounter.CountBracketsForRound64();
							countBracketsPerRound32 = bracketsCounter.CountBracketsForRound32();
							countBracketsPerRound16 = bracketsCounter.CountBracketsForRound16();
							print countBracketsPerRound64 countBracketsPerRound64;
							print countBracketsPerRound32 countBracketsPerRound32;
							print countBracketsPerRound16 countBracketsPerRound16;
							countBracketsPerRound64And32And16 = countBracketsPerRound64 + countBracketsPerRound32 + countBracketsPerRound16;
							print countBracketsPerRound64And32And16 countBracketsPerRound64And32And16;
							print bracketsCounter.CountBracketsForSuperLeague countBracketsForSuperLeague;
							print bracketsCounter.CountBracketsForFinalFour countBracketsForFinalFour;

							countMembersInGeneralRoomsPerRound64 = rules.CountMembersInGeneralRoomsFor(round64);
							countMembersInGeneralRoomsPerRound32 = rules.CountMembersInGeneralRoomsFor(round32);
							countMembersInGeneralRoomsPerRound16 = rules.CountMembersInGeneralRoomsFor(round16);
							print countMembersInGeneralRoomsPerRound64 countMembersInGeneralRoomsPerRound64;
							print countMembersInGeneralRoomsPerRound32 countMembersInGeneralRoomsPerRound32;
							print countMembersInGeneralRoomsPerRound16 countMembersInGeneralRoomsPerRound16;
							countMembersInGeneralRoomsPerRound64And32And16 = countMembersInGeneralRoomsPerRound64 + countMembersInGeneralRoomsPerRound32 + countMembersInGeneralRoomsPerRound16;
							print countMembersInGeneralRoomsPerRound64And32And16 countMembersInGeneralRoomsPerRound64And32And16;
							print rules.CountMembersInGeneralRoomsForSuperLeague() countMembersInGeneralRoomsForSuperLeague;
							print rules.CountMembersInGeneralRoomsForFinalFour() countMembersInGeneralRoomsForFinalFour;

							totalContributionInGeneralPoolsPerRound64 = rules.ContributionInGeneralPoolsFor3FirstRoundsWithoutSpecialPools(round64);
							totalContributionInGeneralPoolsPerRound32 = rules.ContributionInGeneralPoolsFor3FirstRoundsWithoutSpecialPools(round32);
							totalContributionInGeneralPoolsPerRound16 = rules.ContributionInGeneralPoolsFor3FirstRoundsWithoutSpecialPools(round16);
							print totalContributionInGeneralPoolsPerRound64 totalContributionInGeneralPoolsPerRound64;
							print totalContributionInGeneralPoolsPerRound32 totalContributionInGeneralPoolsPerRound32;
							print totalContributionInGeneralPoolsPerRound16 totalContributionInGeneralPoolsPerRound16;
							totalContributionInGeneralPoolsPerRound64And32And16 = totalContributionInGeneralPoolsPerRound64 + totalContributionInGeneralPoolsPerRound32 + totalContributionInGeneralPoolsPerRound16;
							print totalContributionInGeneralPoolsPerRound64And32And16 totalContributionInGeneralPoolsPerRound64And32And16;
							print rules.ContributionInSuperLeague() totalContributionInSuperLeague;
							print rules.ContributionInFinalFour() totalContributionInFinalFour;
                
							print rules.CountGeneralRoomsBy(round64) countGeneralRoomsPerRound64;
							print rules.CountGeneralRoomsBy(round32) countGeneralRoomsPerRound32;
							print rules.CountGeneralRoomsBy(round16) countGeneralRoomsPerRound16;
							print rules.CountInternalRoomsBy(round64) countInternalRoomsPerRound64;
							print rules.CountInternalRoomsBy(round32) countInternalRoomsPerRound32;
							print rules.CountInternalRoomsBy(round16) countInternalRoomsPerRound16;
							print rules.CountPublicRoomsBy(round64) countPublicRoomsPerRound64;
							print rules.CountPublicRoomsBy(round32) countPublicRoomsPerRound32;
							print rules.CountPublicRoomsBy(round16) countPublicRoomsPerRound16;
							print rules.CountPrivateRoomsBy(round64) countPrivateRoomsPerRound64;
							print rules.CountPrivateRoomsBy(round32) countPrivateRoomsPerRound32;
							print rules.CountPrivateRoomsBy(round16) countPrivateRoomsPerRound16;

							print rules.CountMembersInInternalRoomsFor(round64) countMembersInInternalRoomsPerRound64;
							print rules.CountMembersInInternalRoomsFor(round32) countMembersInInternalRoomsPerRound32;
							print rules.CountMembersInInternalRoomsFor(round16) countMembersInInternalRoomsPerRound16;
							print rules.CountMembersInPublicRoomsFor(round64) countMembersInPublicRoomsPerRound64;
							print rules.CountMembersInPublicRoomsFor(round32) countMembersInPublicRoomsPerRound32;
							print rules.CountMembersInPublicRoomsFor(round16) countMembersInPublicRoomsPerRound16;
							print rules.CountMembersInPrivateRoomsFor(round64) countMembersInPrivateRoomsPerRound64;
							print rules.CountMembersInPrivateRoomsFor(round32) countMembersInPrivateRoomsPerRound32;
							print rules.CountMembersInPrivateRoomsFor(round16) countMembersInPrivateRoomsPerRound16;

							print rules.CountMembersInRoomsWithoutGeneralsFor(round64) countMembersInRoomsWithoutGeneralsPerRound64;
							print rules.CountMembersInRoomsWithoutGeneralsFor(round32) countMembersInRoomsWithoutGeneralsPerRound32;
							print rules.CountMembersInRoomsWithoutGeneralsFor(round16) countMembersInRoomsWithoutGeneralsPerRound16;
							print rules.CountMembersInAllGeneralRooms() countMembersInAllGeneralRooms;
						}}
					");
					break;
				case GameAbbreviation.mmw:
				case GameAbbreviation.tbt:
				case GameAbbreviation.euro:
					result = await MarchMadnessWomenAPI.MarchMadnessWomen.PerformQryAsync(HttpContext, $@"
						{{
							rules = company.GetMarchMadnessWomenEdition({year});
							tournament = rules.Tournament;

							print tournament.CountBrackets countBrackets;
							print rules.CountRooms countRooms;

							round64 = 1;
							round32 = 2;
							round16 = 3;
							bracketsCounter = tournament.BracketsCounterByRoundsReport();
							for ( fees : bracketsCounter.ValidFeesForReport )
							{{
								fee = fees;
								print fee fee;

								print bracketsCounter.CountBracketsForRound64ByFee(fee) countBracketsPerRound64;
								print bracketsCounter.CountBracketsForRound32ByFee(fee) countBracketsPerRound32;
								print bracketsCounter.CountBracketsForRound16ByFee(fee) countBracketsPerRound16;
								print bracketsCounter.CountBracketsByFee(fee) countBracketsPerRound64And32And16;

								countMembersInGeneralRoomsPerRound64 = rules.CountMembersInGeneralRoomsFor(round64,fee);
								countMembersInGeneralRoomsPerRound32 = rules.CountMembersInGeneralRoomsFor(round32,fee);
								countMembersInGeneralRoomsPerRound16 = rules.CountMembersInGeneralRoomsFor(round16,fee);
								print countMembersInGeneralRoomsPerRound64 countMembersInGeneralRoomsPerRound64;
								print countMembersInGeneralRoomsPerRound32 countMembersInGeneralRoomsPerRound32;
								print countMembersInGeneralRoomsPerRound16 countMembersInGeneralRoomsPerRound16;
								countMembersInGeneralRoomsPerRound64And32And16 = countMembersInGeneralRoomsPerRound64 + countMembersInGeneralRoomsPerRound32 + countMembersInGeneralRoomsPerRound16;
								print countMembersInGeneralRoomsPerRound64And32And16 countMembersInGeneralRoomsPerRound64And32And16;

								totalContributionInGeneralPoolsPerRound64 = rules.ContributionInGeneralPoolsFor(round64,fee);
								totalContributionInGeneralPoolsPerRound32 = rules.ContributionInGeneralPoolsFor(round32,fee);
								totalContributionInGeneralPoolsPerRound16 = rules.ContributionInGeneralPoolsFor(round16,fee);
								print totalContributionInGeneralPoolsPerRound64 totalContributionInGeneralPoolsPerRound64;
								print totalContributionInGeneralPoolsPerRound32 totalContributionInGeneralPoolsPerRound32;
								print totalContributionInGeneralPoolsPerRound16 totalContributionInGeneralPoolsPerRound16;
								totalContributionInGeneralPoolsPerRound64And32And16 = totalContributionInGeneralPoolsPerRound64 + totalContributionInGeneralPoolsPerRound32 + totalContributionInGeneralPoolsPerRound16;
								print totalContributionInGeneralPoolsPerRound64And32And16 totalContributionInGeneralPoolsPerRound64And32And16;
							}}

							countBracketsPerRound64 = bracketsCounter.CountBracketsForRound64();
							countBracketsPerRound32 = bracketsCounter.CountBracketsForRound32();
							countBracketsPerRound16 = bracketsCounter.CountBracketsForRound16();
							print countBracketsPerRound64 countBracketsPerRound64;
							print countBracketsPerRound32 countBracketsPerRound32;
							print countBracketsPerRound16 countBracketsPerRound16;
							countBracketsPerRound64And32And16 = countBracketsPerRound64 + countBracketsPerRound32 + countBracketsPerRound16;
							print countBracketsPerRound64And32And16 countBracketsPerRound64And32And16;

							countMembersInGeneralRoomsPerRound64 = rules.CountMembersInGeneralRoomsFor(round64);
							countMembersInGeneralRoomsPerRound32 = rules.CountMembersInGeneralRoomsFor(round32);
							countMembersInGeneralRoomsPerRound16 = rules.CountMembersInGeneralRoomsFor(round16);
							print countMembersInGeneralRoomsPerRound64 countMembersInGeneralRoomsPerRound64;
							print countMembersInGeneralRoomsPerRound32 countMembersInGeneralRoomsPerRound32;
							print countMembersInGeneralRoomsPerRound16 countMembersInGeneralRoomsPerRound16;
							countMembersInGeneralRoomsPerRound64And32And16 = countMembersInGeneralRoomsPerRound64 + countMembersInGeneralRoomsPerRound32 + countMembersInGeneralRoomsPerRound16;
							print countMembersInGeneralRoomsPerRound64And32And16 countMembersInGeneralRoomsPerRound64And32And16;
							
							totalContributionInGeneralPoolsPerRound64 = rules.ContributionInGeneralPoolsFor3FirstRoundsWithoutSpecialPools(round64);
							totalContributionInGeneralPoolsPerRound32 = rules.ContributionInGeneralPoolsFor3FirstRoundsWithoutSpecialPools(round32);
							totalContributionInGeneralPoolsPerRound16 = rules.ContributionInGeneralPoolsFor3FirstRoundsWithoutSpecialPools(round16);
							print totalContributionInGeneralPoolsPerRound64 totalContributionInGeneralPoolsPerRound64;
							print totalContributionInGeneralPoolsPerRound32 totalContributionInGeneralPoolsPerRound32;
							print totalContributionInGeneralPoolsPerRound16 totalContributionInGeneralPoolsPerRound16;
							totalContributionInGeneralPoolsPerRound64And32And16 = totalContributionInGeneralPoolsPerRound64 + totalContributionInGeneralPoolsPerRound32 + totalContributionInGeneralPoolsPerRound16;
							print totalContributionInGeneralPoolsPerRound64And32And16 totalContributionInGeneralPoolsPerRound64And32And16;
							
							print rules.CountGeneralRoomsBy(round64) countGeneralRoomsPerRound64;
							print rules.CountGeneralRoomsBy(round32) countGeneralRoomsPerRound32;
							print rules.CountGeneralRoomsBy(round16) countGeneralRoomsPerRound16;
							print rules.CountInternalRoomsBy(round64) countInternalRoomsPerRound64;
							print rules.CountInternalRoomsBy(round32) countInternalRoomsPerRound32;
							print rules.CountInternalRoomsBy(round16) countInternalRoomsPerRound16;
							print rules.CountPublicRoomsBy(round64) countPublicRoomsPerRound64;
							print rules.CountPublicRoomsBy(round32) countPublicRoomsPerRound32;
							print rules.CountPublicRoomsBy(round16) countPublicRoomsPerRound16;
							print rules.CountPrivateRoomsBy(round64) countPrivateRoomsPerRound64;
							print rules.CountPrivateRoomsBy(round32) countPrivateRoomsPerRound32;
							print rules.CountPrivateRoomsBy(round16) countPrivateRoomsPerRound16;

							print rules.CountMembersInInternalRoomsFor(round64) countMembersInInternalRoomsPerRound64;
							print rules.CountMembersInInternalRoomsFor(round32) countMembersInInternalRoomsPerRound32;
							print rules.CountMembersInInternalRoomsFor(round16) countMembersInInternalRoomsPerRound16;
							print rules.CountMembersInPublicRoomsFor(round64) countMembersInPublicRoomsPerRound64;
							print rules.CountMembersInPublicRoomsFor(round32) countMembersInPublicRoomsPerRound32;
							print rules.CountMembersInPublicRoomsFor(round16) countMembersInPublicRoomsPerRound16;
							print rules.CountMembersInPrivateRoomsFor(round64) countMembersInPrivateRoomsPerRound64;
							print rules.CountMembersInPrivateRoomsFor(round32) countMembersInPrivateRoomsPerRound32;
							print rules.CountMembersInPrivateRoomsFor(round16) countMembersInPrivateRoomsPerRound16;

							print rules.CountMembersInRoomsWithoutGeneralsFor(round64) countMembersInRoomsWithoutGeneralsPerRound64;
							print rules.CountMembersInRoomsWithoutGeneralsFor(round32) countMembersInRoomsWithoutGeneralsPerRound32;
							print rules.CountMembersInRoomsWithoutGeneralsFor(round16) countMembersInRoomsWithoutGeneralsPerRound16;
							print rules.CountMembersInAllGeneralRooms() countMembersInAllGeneralRooms;
						}}
					");
					break;
				default:
					return NotFound("Invalid game abbreviation");
			}
            return result;
        }

        [HttpGet("api/tournament/{gameAbbreviation}/{year:int:length(4)}/report/countOfBrackets")]
        [Authorize(Roles = "devops")]//VERIFY_ROLES
        public async Task<IActionResult> CountOfBracketsAsync(string gameAbbreviation, int year)
        {
			GameAbbreviation abbreviation;
			IActionResult result;
			if ( ! Enum.TryParse(gameAbbreviation, out abbreviation)) return NotFound($"{nameof(gameAbbreviation)} '{gameAbbreviation}' is not valid");
			switch (abbreviation)
			{
				case GameAbbreviation.mm:
					result = MarchMadness.MarchMadness.PerformQry(HttpContext, $@"
						{{
							rules = company.GetMarchMadnessEdition({year});
							tournament = rules.Tournament;
                
							print tournament.CountBrackets countOfBrackets;
							bracketsCounter = tournament.BracketsCounterByRoundsReport();
							print bracketsCounter.CountBracketsForRound64() countBracketsPerRound64;
							print bracketsCounter.CountBracketsForRound32() countBracketsPerRound32;
							print bracketsCounter.CountBracketsForRound16() countBracketsPerRound16;
							print bracketsCounter.CountBracketsForSuperLeague countBracketsForSuperLeague;
							print bracketsCounter.CountBracketsForFinalFour countBracketsForFinalFour;

							bracketsCountPerRoundsAndFees = tournament.BracketsCountPerRoundsAndFees();
							for ( rounds : rules.RoundsToSaleBrackets() )
							{{
								print rounds.Name roundName;
								print rounds.Number roundNumber;
								for ( countOfBrackets : bracketsCountPerRoundsAndFees.CountsFor(rounds) )
								{{
									print countOfBrackets.Fee fee;
									print countOfBrackets.Count count;
								}}
							}}
						}}
					");
					break;
				case GameAbbreviation.mmw:
				case GameAbbreviation.tbt:
				case GameAbbreviation.euro:
					result = await MarchMadnessWomenAPI.MarchMadnessWomen.PerformQryAsync(HttpContext, $@"
						{{
							rules = company.GetMarchMadnessWomenEdition({year});
							tournament = rules.Tournament;
                
							print tournament.CountBrackets countOfBrackets;
							bracketsCounter = tournament.BracketsCounterByRoundsReport();
							print bracketsCounter.CountBracketsForRound64() countBracketsPerRound64;
							print bracketsCounter.CountBracketsForRound32() countBracketsPerRound32;
							print bracketsCounter.CountBracketsForRound16() countBracketsPerRound16;
							print bracketsCounter.CountBracketsForSuperLeague countBracketsForSuperLeague;
							print bracketsCounter.CountBracketsForFinalFour countBracketsForFinalFour;

							bracketsCountPerRoundsAndFees = tournament.BracketsCountPerRoundsAndFees();
							for ( rounds : rules.RoundsToSaleBrackets() )
							{{
								print rounds.Name roundName;
								print rounds.Number roundNumber;
								for ( countOfBrackets : bracketsCountPerRoundsAndFees.CountsFor(rounds) )
								{{
									print countOfBrackets.Fee fee;
									print countOfBrackets.Count count;
								}}
							}}
						}}
					");
					break;
				default:
					return NotFound("Invalid game abbreviation");
			}
            return result;
        }
    }
}
