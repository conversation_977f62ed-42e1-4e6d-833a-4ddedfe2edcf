using GamesEngine.Settings;
using MySql.Data.MySqlClient;
using Puppeteer.EventSourcing.Libraries;
using Puppeteer.EventSourcing;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using static GamesEngine.Business.Marketing.MarketingStorage;
using System.Threading.Tasks;
using GamesEngine.Games.Lines;
using GamesEngine.Custodian.Operations;
using static GamesEngine.Business.WholePaymentProcessor;

namespace GamesEngine.Finance
{
    [Puppet]
    public abstract class SalesStorage : Objeto
    {
        private static SalesStorage instance;

        protected readonly string connectionString;
        protected const string TABLE_DAILY_SALES = "DailySales";
        protected const string TABLE_CAMPAIGN_DAILY_SALE = "CampaignDailySale";

        internal abstract void CreateStorageTable();
        internal abstract DataTable TableCampaignDailySale(int page, int pageSize, int promotionNumber, int storeId, string domainIdsOrAll, out int totalRows);

        internal SalesStorage(string connectionString)
        {
            this.connectionString = connectionString;
        }

        internal static SalesStorage CreateStorageConnection()
        {
            if (Integration.Db.DBSelected == HistoricalDatabaseType.MySQL.ToString())
            {
                if (instance == null) instance = new SalesStorageMySQL(Integration.Db.MySQL);
                return instance;
            }
            else if (Integration.Db.DBSelected == HistoricalDatabaseType.SQLServer.ToString())
            {
                if (instance == null) instance = new SalesStorageSQLServer(Integration.Db.SQLServer);
                return instance;
            }
            throw new Exception($"There is no connection for {Integration.Db.DBSelected}");
        }

        internal abstract void ReleasePlayerDailySummary(int campaignId, Currencies.CODES currency, DateTime now, int domainId, int storeId, decimal amount);


        internal abstract DateTime LastSaleDate();
        internal abstract DateTime LastDailySaleDate();
        internal abstract void UpdateDailySalesDate(DateTime dateToUpdate);

        /// <summary>
        /// For Reports
        /// </summary>

        CampaignDailySalesMatchBuilder responseCampaignDailySales = new CampaignDailySalesMatchBuilder();
        internal CampaignDailySalesMatchBuilder CampaignDailySales(int page, int pageSize, int promotionNumber, int storeId, string domainIdsOrAll)
        {
            int totalRows = 0;
            DataTable table = TableCampaignDailySale(page, pageSize, promotionNumber, storeId, domainIdsOrAll, out totalRows);
            responseCampaignDailySales.TotalRows = totalRows;
            responseCampaignDailySales.ReportData.Clear();

            foreach (DataRow row in table.Rows)
            {
                RowCampaignDailySale rowData = new RowCampaignDailySale(
                    Convert.ToInt32(row["CampaignId"]),
                    Convert.ToInt32(row["Currency"]),
                    Convert.ToInt32(row["DomainId"]),
                    Convert.ToInt32(row["StoreId"]),
                    Convert.ToDateTime(row["Date"]),
                    Convert.ToInt32(row["WeekOfTheYear"]),
                    Convert.ToDecimal(row["Amount"])
                );
                responseCampaignDailySales.ReportData.Add(rowData);
            }

            var allDomains = domainIdsOrAll == Reports.SELECTION_ALL;
            if (!allDomains)
            {
                var domainIds = domainIdsOrAll.Split(',');
                responseCampaignDailySales.FilterDomains(domainIds);
            }

            return responseCampaignDailySales;
        }

        [Puppet]
        internal class CampaignDailySalesMatchBuilder : Objeto
        {
            internal int TotalRows { get; set; }
            internal List<RowCampaignDailySale> ReportData { get; set; }

            internal CampaignDailySalesMatchBuilder()
            {
                TotalRows = 0;
                ReportData = new List<RowCampaignDailySale>();
            }

            internal int TotalRecords
            {
                get
                {
                    return ReportData.Count;
                }
            }

            internal void FilterDomains(string[] domainIds)
            {
                int totalRemoved = ReportData.RemoveAll(r => Array.IndexOf(domainIds, r.DomainId.ToString()) == -1);
                TotalRows -= totalRemoved;
            }
        }

        [Puppet]
        internal class RowCampaignDailySale : Objeto
        {
            internal int CampaignId { get; set; }
            internal int Currency { get; set; }
            internal int DomainId { get; set; }
            internal int StoreId { get; set; }
            internal DateTime Date { get; set; }
            internal int WeekOfTheYear { get; set; }
            internal decimal Amount { get; set; }

            internal RowCampaignDailySale(int campaignId, int currency, int domainId, int storeId, DateTime date, int weekOfTheYear, decimal amount)
            {
                CampaignId = campaignId;
                Currency = currency;
                DomainId = domainId;
                StoreId = storeId;
                Date = date;
                WeekOfTheYear = weekOfTheYear;
                Amount = amount;
            }
        }
    }

    public class SalesStorageMySQL : SalesStorage
    {
        internal SalesStorageMySQL(string connectionString) : base(connectionString)
        {
            CreateStorageTable();
        }

        internal override void CreateStorageTable()
        {
            StringBuilder query = new StringBuilder();

            query.AppendLine($"CREATE TABLE IF NOT EXISTS {TABLE_DAILY_SALES} (");
            query.AppendLine("CURRENCY TINYINT NOT NULL,");
            query.AppendLine("DOMAIN INT NOT NULL,");
            query.AppendLine("STORE INT NOT NULL,");
            query.AppendLine("SALEDATE DATE NOT NULL,");
            query.AppendLine("WEEKOFTHEYEAR INT NOT NULL,");
            query.AppendLine("AMOUNT DECIMAL(10,2) NOT NULL,");
            query.AppendLine("PRIMARY KEY (CURRENCY, DOMAIN, STORE, SALEDATE, WEEKOFTHEYEAR)");
            query.AppendLine(") CHARSET=utf8;");

            query.AppendLine($"CREATE TABLE IF NOT EXISTS {TABLE_CAMPAIGN_DAILY_SALE} (");
            query.AppendLine("CampaignId INT NOT NULL,");
            query.AppendLine("Currency TINYINT NOT NULL,");
            query.AppendLine("DomainId INT NOT NULL,");
            query.AppendLine("StoreId INT NOT NULL,");
            query.AppendLine("Date DATE NOT NULL,");
            query.AppendLine("WeekOfTheYear INT NOT NULL,");
            query.AppendLine("Amount DECIMAL(10,2) NOT NULL,");
            query.AppendLine("PRIMARY KEY (CampaignId, Currency, DomainId, StoreId, Date, WeekOfTheYear)");
            query.AppendLine(") CHARSET=utf8;");

            try
            {
                using (var connection = new MySqlConnection(connectionString))
                {
                    connection.Open();
                    using (var command = new MySqlCommand(query.ToString(), connection))
                    {
                        command.ExecuteNonQuery();
                    }
                }
            }
            catch (Exception ex)
            {
                throw new GameEngineException($"Error creating table {TABLE_DAILY_SALES} message: {ex.Message}");
            }
        }


        internal override void ReleasePlayerDailySummary(int campaignId, Currencies.CODES currency, DateTime now, int domainId, int storeId, decimal amount)
        {
            StringBuilder insertQueryDailySale = new StringBuilder();
            insertQueryDailySale.AppendLine($"INSERT INTO {TABLE_CAMPAIGN_DAILY_SALE} (CampaignId, Currency, DomainId, StoreId, Date, WeekOfTheYear, Amount)");
            insertQueryDailySale.AppendLine($"VALUES({campaignId}, {(int)currency}, {domainId}, {storeId}, '{now.Date.ToString("yyyy-MM-dd")}', {now.WeekOfYear()}, {amount})");
            insertQueryDailySale.AppendLine($"ON DUPLICATE KEY UPDATE Amount = Amount + VALUES(Amount)");
            string insertQuery = insertQueryDailySale.ToString();
            try
            {
                using (var connection = new MySqlConnection(connectionString))
                {
                    connection.Open();
                    using (var command = new MySqlCommand(insertQuery, connection))
                    {
                        command.ExecuteNonQuery();
                    }
                }
            }
            catch (Exception ex)
            {
                throw new GameEngineException($"Error updating daily player sale message: {ex.Message}");
            }

            try
            {
                WebHookClientRequest.Instance.SendWebHook(DateTime.Now, insertQuery, TABLE_CAMPAIGN_DAILY_SALE, "Loyalty");
            }
            catch (Exception webhookEx)
            {
                Loggers.GetIntance().Webhook.Error($"Webhook failed for MySQL:{insertQuery}", webhookEx);
                throw new GameEngineException($"Webhook error after DB update: {webhookEx.Message}");
            }
        }


        internal override DataTable TableCampaignDailySale(int page, int pageSize, int promotionNumber, int storeId, string domainIdsOrAll, out int totalRows)
        {
            StringBuilder queryCount = new StringBuilder();
            queryCount.AppendLine($"SELECT COUNT(*) FROM {TABLE_CAMPAIGN_DAILY_SALE}");

            StringBuilder query = new StringBuilder();
            // append query to get data all fields each by one, not using * to avoid problems with the order of the fields
            query.AppendLine($"SELECT CampaignId, Currency, DomainId, StoreId, Date, WeekOfTheYear, Amount FROM {TABLE_CAMPAIGN_DAILY_SALE}");

            List<string> parametersQuery = new List<string>()
            {
                $"{(promotionNumber == 0 ? "":$"CampaignId = {promotionNumber}")}",
                $"{(storeId == 0 ? "":$"StoreId = {storeId}")}",
                $"{(domainIdsOrAll == Reports.SELECTION_ALL ? "":$"DomainId IN ({domainIdsOrAll})")}"
            };

            bool whereCondition = false;
            StringBuilder whereStatement = new StringBuilder();
            foreach (string parameter in parametersQuery)
            {
                if (!string.IsNullOrWhiteSpace(parameter))
                {
                    if (!whereCondition)
                    {
                        whereCondition = true;
                        whereStatement.Append($" WHERE {parameter}");
                    }
                    else
                    {
                        whereStatement.Append($" AND {parameter}");
                    }
                }
            }
            whereStatement.AppendLine();

            queryCount.Append(whereStatement);
            query.Append(whereStatement);
            query.AppendLine($" ORDER BY CampaignId, Date");
            query.AppendLine($"LIMIT {pageSize} OFFSET {(page - 1) * pageSize};");

            //execute query count query to get totalRows value
            totalRows = 0;
            using (MySqlConnection connection = new MySqlConnection(connectionString))
            {
                try
                {
                    connection.Open();
                    using (MySqlCommand command = new MySqlCommand(queryCount.ToString(), connection))
                    using (DbDataReader reader = command.ExecuteReader())
                    {
                        if (reader.Read())
                        {
                            totalRows = reader.GetInt32(0);
                        }
                        reader.Close();
                    }
                }
                catch
                {
                    throw new GameEngineException("MySQL Error [" + queryCount + "]");
                }
                finally
                {
                    connection.Close();
                }
            }

            //execute query to get data
            DataTable dataTable = new DataTable();
            try
            {
                using (MySqlConnection connection = new MySqlConnection(connectionString))
                {
                    connection.Open();
                    using (MySqlCommand command = new MySqlCommand(query.ToString(), connection))
                    {
                        using (MySqlDataReader reader = command.ExecuteReader())
                        {
                            dataTable.Load(reader);
                            return dataTable;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error: {ex.Message}");
                return null;
            }
        }

        internal override DateTime LastSaleDate()
        {
            StringBuilder query = new StringBuilder();
            query.AppendLine($"SELECT SALEDATE FROM {LoyaltyStorage.TABLE_SALES} ORDER BY SALEDATE DESC LIMIT 1;");

            using (MySqlConnection connection = new MySqlConnection(connectionString))
            {
                try
                {
                    connection.Open();
                    using (MySqlCommand command = new MySqlCommand(query.ToString(), connection))
                    using (MySqlDataReader reader = command.ExecuteReader())
                    {
                        if (reader.Read())
                        {
                            return reader.GetDateTime(0).Date;
                        }
                        reader.Close();
                    }
                }
                catch
                {
                    throw new GameEngineException("MySQL Error [" + query + "]");
                }
                finally
                {
                    connection.Close();
                }
            }

            return DateTime.MinValue;
        }

        internal override DateTime LastDailySaleDate()
        {
            StringBuilder query = new StringBuilder();

            query.AppendLine($"SELECT SALEDATE FROM {TABLE_DAILY_SALES} ORDER BY SALEDATE DESC LIMIT 1;");

            using (MySqlConnection connection = new MySqlConnection(connectionString))
            {
                try
                {
                    connection.Open();
                    using (MySqlCommand command = new MySqlCommand(query.ToString(), connection))
                    using (MySqlDataReader reader = command.ExecuteReader())
                    {
                        if (reader.Read())
                        {
                            return reader.GetDateTime(0).Date;
                        }
                        reader.Close();
                    }
                }
                catch
                {
                    throw new GameEngineException("MySQL Error [" + query + "]");
                }
                finally
                {
                    connection.Close();
                }
            }

            return DateTime.MinValue;
        }

        internal override void UpdateDailySalesDate(DateTime dateToUpdate)
        {
            if (dateToUpdate == DateTime.MinValue) return;

            StringBuilder queryResults = new StringBuilder();

            queryResults.AppendLine(@$"
                SELECT
	                SUM(AMOUNT) AS TOTAL_AMOUNT,
	                CURRENCY,
                    SALEDATE,
                    STORE,
                    DOMAIN
                FROM
	                {LoyaltyStorage.TABLE_SALES}
                WHERE
	                SALEDATE = @DateToUpdate
                GROUP BY
	                CURRENCY, SALEDATE, STORE, DOMAIN;
            ");

            // Get the data in a datatable with try catch
            DataTable dataResults = new DataTable();

            using (MySqlConnection connection = new MySqlConnection(connectionString))
            {
                try
                {
                    connection.Open();
                    using (MySqlCommand command = new MySqlCommand(queryResults.ToString(), connection))
                    {
                        command.Parameters.AddWithValue("@DateToUpdate", dateToUpdate.Date);
                        using (MySqlDataReader reader = command.ExecuteReader())
                        {
                            dataResults.Load(reader);
                        }
                    }
                }
                catch
                {
                    throw new GameEngineException("MySQL Error [" + queryResults.ToString() + "]");
                }
                finally
                {
                    connection.Close();
                }
            }

            using (MySqlConnection connection = new MySqlConnection(connectionString))
            {
                StringBuilder insertOrUpdateQuery = new StringBuilder();
                insertOrUpdateQuery.AppendLine(@$"
                    INSERT INTO {TABLE_DAILY_SALES} (CURRENCY, DOMAIN, STORE, SALEDATE, WEEKOFTHEYEAR, AMOUNT)
                    VALUES (@Currency, @DomainId, @StoreId, @Date, @WeekOfTheYear, @Amount)
                    ON DUPLICATE KEY UPDATE AMOUNT = @Amount;
                ");

                foreach (DataRow row in dataResults.Rows)
                {
                    try
                    {
                        connection.Open();
                        using (MySqlCommand command = new MySqlCommand(insertOrUpdateQuery.ToString(), connection))
                        {
                            command.Parameters.AddWithValue("@Currency", row["CURRENCY"]);
                            command.Parameters.AddWithValue("@DomainId", row["DOMAIN"]);
                            command.Parameters.AddWithValue("@StoreId", row["STORE"]);
                            command.Parameters.AddWithValue("@Date", row["SALEDATE"]);
                            command.Parameters.AddWithValue("@WeekOfTheYear", dateToUpdate.WeekOfYear());
                            command.Parameters.AddWithValue("@Amount", row["TOTAL_AMOUNT"]);
                            command.ExecuteNonQuery();
                        }
                    }
                    catch
                    {
                        throw new GameEngineException("MySQL Error [" + insertOrUpdateQuery.ToString() + "]");
                    }
                    finally
                    {
                        connection.Close();
                    }

                    try
                    {
                        WebHookClientRequest.Instance.SendWebHook(DateTime.Now, insertOrUpdateQuery.ToString(), TABLE_DAILY_SALES, "Loyalty");
                    }
                    catch (Exception webhookEx)
                    {
                        Loggers.GetIntance().Webhook.Error($"Webhook failed for MySQL:{insertOrUpdateQuery}", webhookEx);
                        throw new GameEngineException($"Webhook error after DB update: {webhookEx.Message}");
                    }
                }
            }
        }
    }

    public class SalesStorageSQLServer : SalesStorage
    {
        internal SalesStorageSQLServer(string connectionString) : base(connectionString)
        {
            CreateStorageTable();
        }

        internal override void CreateStorageTable()
        {
            StringBuilder query = new StringBuilder();

            query.AppendLine($"IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='{TABLE_DAILY_SALES}' AND xtype='U')");
            query.AppendLine($"CREATE TABLE {TABLE_DAILY_SALES} (");
            query.AppendLine("CURRENCY TINYINT NOT NULL,");
            query.AppendLine("DOMAIN INT NOT NULL,");
            query.AppendLine("STORE INT NOT NULL,");
            query.AppendLine("SALEDATE DATE NOT NULL,");
            query.AppendLine("WEEKOFTHEYEAR  INT NOT NULL,");
            query.AppendLine("AMOUNT DECIMAL(10,2) NOT NULL,");
            query.AppendLine("PRIMARY KEY (CURRENCY, DOMAIN, STORE, SALEDATE, WEEKOFTHEYEAR)");
            query.AppendLine(")");

            // Create for TABLE_CAMPAIGN_DAILY_SALE
            query.AppendLine($"IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='{TABLE_CAMPAIGN_DAILY_SALE}' AND xtype='U')");
            query.AppendLine($"CREATE TABLE {TABLE_CAMPAIGN_DAILY_SALE} (");
            query.AppendLine("CampaignId INT NOT NULL,");
            query.AppendLine("Currency TINYINT NOT NULL,");
            query.AppendLine("DomainId INT NOT NULL,");
            query.AppendLine("StoreId INT NOT NULL,");
            query.AppendLine("Date DATE NOT NULL,");
            query.AppendLine("WeekOfTheYear INT NOT NULL,");
            query.AppendLine("Amount DECIMAL(10,2) NOT NULL,");
            query.AppendLine("PRIMARY KEY (CampaignId, Currency, DomainId, StoreId, Date, WeekOfTheYear)");
            query.AppendLine(")");

            try
            {
                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    connection.Open();
                    using (SqlCommand command = new SqlCommand(query.ToString(), connection))
                    {
                        command.ExecuteNonQuery();
                    }
                }
            }
            catch (Exception ex)
            {
                throw new GameEngineException($"Error creating table {TABLE_DAILY_SALES} message: {ex.Message}");
            }

        }

        internal override void ReleasePlayerDailySummary(int campaignId, Currencies.CODES currency, DateTime now, int domainId, int storeId, decimal amount)
        {
            StringBuilder insertQueryDailySale = new StringBuilder();
            insertQueryDailySale.AppendLine($"INSERT INTO {TABLE_CAMPAIGN_DAILY_SALE} (CampaignId, AccountNumber, Currency, DomainId, StoreId, Date, WeekOfTheYear, Amount)");
            insertQueryDailySale.AppendLine($"VALUES({campaignId}, {(int)currency}, {domainId}, {storeId}, '{now.Date.ToString("yyyy-MM-dd")}', {now.WeekOfYear()}, {amount})");
            insertQueryDailySale.AppendLine($"ON DUPLICATE KEY UPDATE Amount = Amount + VALUES(Amount)");
            string insertQuery = insertQueryDailySale.ToString();

            try
            {
                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    connection.Open();
                    using (SqlCommand command = new SqlCommand(insertQuery, connection))
                    {
                        command.ExecuteNonQuery();
                    }
                }
            }
            catch (Exception ex)
            {
                throw new GameEngineException($"Error updating daily player sale message: {ex.Message}");
            }

            try
            {
                WebHookClientRequest.Instance.SendWebHook(DateTime.Now, insertQuery, TABLE_CAMPAIGN_DAILY_SALE, "Loyalty");
            }
            catch (Exception webhookEx)
            {
                Loggers.GetIntance().Webhook.Error($"Webhook failed for SQL:{insertQuery}", webhookEx);
                throw new GameEngineException($"Error updating daily player sale message: {webhookEx.Message}");
            }
        }

        internal override DataTable TableCampaignDailySale(int page, int pageSize, int promotionNumber, int storeId, string domainIdsOrAll, out int totalRows)
        {
            StringBuilder queryCount = new StringBuilder();
            queryCount.AppendLine($"SELECT COUNT(*) FROM {TABLE_CAMPAIGN_DAILY_SALE}");

            StringBuilder query = new StringBuilder();
            query.AppendLine($"SELECT CampaignId, Currency, DomainId, StoreId, Date, WeekOfTheYear, Amount FROM {TABLE_CAMPAIGN_DAILY_SALE}");

            List<string> parametersQuery = new List<string>()
            {
                $"{(promotionNumber == 0 ? "":$"CampaignId = {promotionNumber}")}",
                $"{(storeId == 0 ? "":$"StoreId = {storeId}")}",
                $"{(domainIdsOrAll == Reports.SELECTION_ALL ? "":$"DomainId IN ({domainIdsOrAll})")}"
            };

            bool whereCondition = false;
            StringBuilder whereStatement = new StringBuilder();
            foreach (string parameter in parametersQuery)
            {
                if (!string.IsNullOrWhiteSpace(parameter))
                {
                    if (!whereCondition)
                    {
                        whereCondition = true;
                        whereStatement.Append($" WHERE {parameter}");
                    }
                    else
                    {
                        whereStatement.Append($" AND {parameter}");
                    }
                }
            }

            queryCount.Append(whereStatement);
            query.Append(whereStatement);
            query.AppendLine($" ORDER BY CampaignId, Date");
            query.AppendLine($"OFFSET {(page - 1) * pageSize} ROWS FETCH NEXT {pageSize} ROWS ONLY;");
            //execute query count query to get totalRows value
            totalRows = 0;
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                try
                {
                    connection.Open();
                    using (SqlCommand command = new SqlCommand(queryCount.ToString(), connection))
                    using (DbDataReader reader = command.ExecuteReader())
                    {
                        if (reader.Read())
                        {
                            totalRows = reader.GetInt32(0);
                        }
                        reader.Close();
                    }
                }
                catch
                {
                    throw new GameEngineException("SQL Server Error [" + queryCount + "]");
                }
                finally
                {
                    connection.Close();
                }
            }

            //execute query to get data
            DataTable dataTable = new DataTable();
            try
            {
                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    connection.Open();
                    using (SqlCommand command = new SqlCommand(query.ToString(), connection))
                    {
                        using (SqlDataReader reader = command.ExecuteReader())
                        {
                            dataTable.Load(reader);
                            return dataTable;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error: {ex.Message}");
                return null;
            }

        }

        internal override DateTime LastSaleDate()
        {
            StringBuilder query = new StringBuilder();
            query.AppendLine($"SELECT SALEDATE FROM {LoyaltyStorage.TABLE_SALES}  ORDER BY SALEDATE DESC LIMIT 1;");

            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                try
                {
                    connection.Open();
                    using (SqlCommand command = new SqlCommand(query.ToString(), connection))
                    using (SqlDataReader reader = command.ExecuteReader())
                    {
                        if (reader.Read())
                        {
                            return reader.GetDateTime(0).Date;
                        }
                        reader.Close();
                    }
                }
                catch
                {
                    throw new GameEngineException("SQL Server Error [" + query + "]");
                }
                finally
                {
                    connection.Close();
                }
            }

            return DateTime.MinValue;
        }

        internal override DateTime LastDailySaleDate()
        {
            StringBuilder query = new StringBuilder();

            query.AppendLine($"SELECT SALEDATE FROM  {TABLE_DAILY_SALES}  ORDER BY SALEDATE DESC LIMIT 1;");

            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                try
                {
                    connection.Open();
                    using (SqlCommand command = new SqlCommand(query.ToString(), connection))
                    using (SqlDataReader reader = command.ExecuteReader())
                    {
                        if (reader.Read())
                        {
                            return reader.GetDateTime(0).Date;
                        }
                        reader.Close();
                    }
                }
                catch
                {
                    throw new GameEngineException("SQL Server Error [" + query + "]");
                }
                finally
                {
                    connection.Close();
                }
            }

            return DateTime.MinValue;
        }

        internal override void UpdateDailySalesDate(DateTime dateToUpdate)
        {
            if (dateToUpdate == DateTime.MinValue) return;

            StringBuilder queryResults = new StringBuilder();

            queryResults.AppendLine(@$"
                SELECT
                    SUM(AMOUNT) AS TOTAL_AMOUNT,
                    CURRENCY,
                    SALEDATE,
                    STORE,
                    DOMAIN
                FROM
                    {LoyaltyStorage.TABLE_SALES}
                WHERE
                    SALEDATE = @DateToUpdate
                GROUP BY
                    CURRENCY, SALEDATE, STORE, DOMAIN;"
            );

            // Get the data in a datatable with try catch
            DataTable dataResults = new DataTable();
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                try
                {
                    connection.Open();
                    using (SqlCommand command = new SqlCommand(queryResults.ToString(), connection))
                    {
                        command.Parameters.AddWithValue("@DateToUpdate", dateToUpdate.Date);
                        using (SqlDataReader reader = command.ExecuteReader())
                        {
                            dataResults.Load(reader);
                        }
                    }
                }
                catch
                {
                    throw new GameEngineException("SQL Server Error [" + queryResults.ToString() + "]");
                }
                finally
                {
                    connection.Close();
                }
            }

            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                StringBuilder insertOrUpdateQuery = new StringBuilder();
                insertOrUpdateQuery.AppendLine(@$"
                    INSERT INTO {TABLE_DAILY_SALES} (CURRENCY, DOMAIN, STORE, SALEDATE, WEEKOFTHEYEAR, AMOUNT)
                    VALUES (@Currency, @DomainId, @StoreId, @Date, @WeekOfTheYear, @Amount)
                    ON DUPLICATE KEY UPDATE AMOUNT = @Amount;
                ");

                foreach (DataRow row in dataResults.Rows)
                {
                    try
                    {
                        connection.Open();
                        using (SqlCommand command = new SqlCommand(insertOrUpdateQuery.ToString(), connection))
                        {
                            command.Parameters.AddWithValue("@Currency", row["CURRENCY"]);
                            command.Parameters.AddWithValue("@DomainId", row["DOMAIN"]);
                            command.Parameters.AddWithValue("@StoreId", row["STORE"]);
                            command.Parameters.AddWithValue("@Date", row["SALEDATE"]);
                            command.Parameters.AddWithValue("@WeekOfTheYear", dateToUpdate.WeekOfYear());
                            command.Parameters.AddWithValue("@Amount", row["TOTAL_AMOUNT"]);
                            command.ExecuteNonQuery();
                        }
                    }
                    catch
                    {
                        throw new GameEngineException("SQL Server Error [" + insertOrUpdateQuery.ToString() + "]");
                    }
                    finally
                    {
                        connection.Close();
                    }

                    try
                    {
                        WebHookClientRequest.Instance.SendWebHook(DateTime.Now, insertOrUpdateQuery.ToString(), TABLE_DAILY_SALES, "Loyalty");
                    }
                    catch (Exception webhookEx)
                    {
                        Loggers.GetIntance().Webhook.Error($"Webhook failed for SQL:{insertOrUpdateQuery}", webhookEx);
                        throw new GameEngineException($"Webhook error after DB update: {webhookEx.Message}");
                    }
                }
            }

        }
    }
}

