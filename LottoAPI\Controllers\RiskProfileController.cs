﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Threading.Tasks;
using System;
using static LottoAPI.Controllers.PrizesController;
using System.Runtime.Serialization;
using GamesEngine.Domains;

namespace LottoAPI.Controllers
{
    public class RiskProfileController : AuthorizeController
    {
        [HttpGet("api/lotto/riskProfiles")]
        [Authorize(Roles = "RiskProfiles")]//VERIFY_ROLES
        public async Task<IActionResult> AllRiskProfilesAsync()
        {
            var result = await LottoAPI.Actor.PerformQryAsync(HttpContext, $@"
            {{
                riskProfiles = lotto900.RiskProfiles;
                for (profiles : riskProfiles.GetAll)
                {{
                    riskProfile = profiles;
                    print riskProfile.Name riskProfileName;
                    for (domains:riskProfile.Domains)
                    {{
                        domain = domains;
                        print domain.Id id; 
                        print domain.Url domainUrl;
                    }}
                }}

                print riskProfiles.DefaultRiskProfile.Name defaultRiskProfileName;
                for (domainsWithoutProfile:company.Sales.AllDomains)
                {{
                    domain = domainsWithoutProfile;
                    if (!riskProfiles.HasProfile(domain))
                    {{
                        print domain.Id id;
                        print domain.Url url;
                    }}
                }}
            }}
            ");
            return result;
        }

        [HttpPost("api/lotto/riskProfile")]
        [Authorize(Roles = "RiskProfiles")]//VERIFY_ROLES
        public async Task<IActionResult> CreateRiskProfileAsync([FromBody] RiskProfileBody body)
        {
            if (body == null) return BadRequest("Body is required");
            if (string.IsNullOrWhiteSpace(body.DomainUrl)) return BadRequest($"{nameof(body.DomainUrl)} is required");
            if (string.IsNullOrWhiteSpace(body.Name)) return BadRequest($"{nameof(body.Name)} is required");

            var result = await LottoAPI.Actor.PerformChkThenCmdAsync(HttpContext, $@"
            {{
                existsDomain = company.Sales.ExistsDomain('{body.DomainUrl}');
                Check(existsDomain) Error 'Domain {body.DomainUrl} does not exist';
                Check(!lotto900.RiskProfiles.ExistsProfileName('{body.Name}')) Error 'Risk profile {body.Name} already exists';
            }}", $@"
            {{
                domain = company.Sales.DomainFrom('{body.DomainUrl}');
                lotto900.RiskProfiles.CreateRiskProfile(domain, '{body.Name}');
            }}
            ");
            return result;
        }

        [HttpPost("api/lotto/riskProfile/copy")]
        [Authorize(Roles = "RiskProfiles")]//VERIFY_ROLES
        public async Task<IActionResult> CopyRiskProfileAsync([FromBody] RiskProfileBody body)
        {
            if (body == null) return BadRequest("Body is required");
            if (string.IsNullOrWhiteSpace(body.DomainUrl)) return BadRequest($"{nameof(body.DomainUrl)} is required");
            if (string.IsNullOrWhiteSpace(body.Name)) return BadRequest($"{nameof(body.Name)} is required");

            var result = await LottoAPI.Actor.PerformChkThenCmdAsync(HttpContext, $@"
            {{
                existsDomain = company.Sales.ExistsDomain('{body.DomainUrl}');
                Check(existsDomain) Error 'Domain {body.DomainUrl} does not exist';
                Check(!lotto900.RiskProfiles.ExistsProfileName('{body.Name}')) Error 'Risk profile {body.Name} already exists';
            }}", $@"
            {{
                domain = company.Sales.DomainFrom('{body.DomainUrl}');
                riskProfileOrigin = lotto900.RiskProfiles.GetRiskProfile(domain);
                lotto900.RiskProfiles.CreateRiskProfile(riskProfileOrigin, domain, '{body.Name}');
            }}
            ");
            return result;
        }

        [HttpPost("api/lotto/riskProfile/assignation")]
        [Authorize(Roles = "RiskProfiles")]//VERIFY_ROLES
        public async Task<IActionResult> AssignDomainAsync([FromBody] DomainAssignationBody body)
        {
            if (body == null) return BadRequest("Body is required");
            if (string.IsNullOrWhiteSpace(body.DomainUrl)) return BadRequest($"{nameof(body.DomainUrl)} is required");
            if (string.IsNullOrWhiteSpace(body.RiskProfileName)) return BadRequest($"{nameof(body.RiskProfileName)} is not valid");

            var result = await LottoAPI.Actor.PerformChkThenCmdAsync(HttpContext, $@"
            {{
                existsDomain = company.Sales.ExistsDomain('{body.DomainUrl}');
                Check(existsDomain) Error 'Domain {body.DomainUrl} does not exist';
                Check(lotto900.RiskProfiles.ExistsProfileName('{body.RiskProfileName}')) Error 'Risk profile {body.RiskProfileName} does not exist';
            }}", $@"
            {{
                riskProfileTarget = lotto900.RiskProfiles.GetRiskProfile('{body.RiskProfileName}');
                domain = company.Sales.DomainFrom('{body.DomainUrl}');
                lotto900.RiskProfiles.AssignDomain(domain, riskProfileTarget);
            }}
            ");
            return result;
        }

        [HttpPut("api/lotto/riskProfile/rename")]
        [Authorize(Roles = "RiskProfiles")]//VERIFY_ROLES
        public async Task<IActionResult> ChangeRiskProfileNameAsync([FromBody] RiskProfileNameBody body)
        {
            if (body == null) return BadRequest("Body is required");
            if (string.IsNullOrWhiteSpace(body.NewName)) return BadRequest($"{nameof(body.NewName)} is required");
            if (string.IsNullOrWhiteSpace(body.CurrentName)) return BadRequest($"{nameof(body.CurrentName)} is not valid");

            var result = await LottoAPI.Actor.PerformChkThenCmdAsync(HttpContext, $@"
            {{
                Check(lotto900.RiskProfiles.ExistsProfileName('{body.CurrentName}')) Error 'Risk profile {body.CurrentName} does not exist';
                Check(!lotto900.RiskProfiles.ExistsProfileName('{body.NewName}')) Error 'Risk profile {body.NewName} already exists';
            }}", $@"
            {{
                riskProfile = lotto900.RiskProfiles.GetRiskProfile('{body.CurrentName}');
                riskProfile.Name = '{body.NewName}';
            }}
            ");
            return result;
        }

        [DataContract(Name = "RiskProfileBody")]
        public class RiskProfileBody
        {
            [DataMember(Name = "domainUrl")]
            public string DomainUrl { get; set; }
            [DataMember(Name = "name")]
            public string Name { get; set; }
        }

        [DataContract(Name = "AssignationDomainBody")]
        public class DomainAssignationBody
        {
            [DataMember(Name = "domainUrl")]
            public string DomainUrl { get; set; }
            [DataMember(Name = "riskProfileName")]
            public string RiskProfileName { get; set; }
        }

        [DataContract(Name = "RiskProfileNameBody")]
        public class RiskProfileNameBody
        {
            [DataMember(Name = "currentName")]
            public string CurrentName { get; set; }
            [DataMember(Name = "newName")]
            public string NewName { get; set; }
        }
    }
}
