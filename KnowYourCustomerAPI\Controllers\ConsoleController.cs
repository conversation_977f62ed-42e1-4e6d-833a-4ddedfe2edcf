using GamesEngine.Settings;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.IO;
using System.Text;
using System.Threading.Tasks;

namespace KnowYourCustomerAPI.Controllers
{
    public class ConsoleController : AuthorizeController
    {
        [HttpPost("console/command")]
        [Authorize(Roles = "devops")]
        public async Task<IActionResult> processCommandAsync()
        {
            string body = "";

            using (StreamReader reader = new StreamReader(Request.Body, Encoding.UTF8))
            {
                body = await reader.ReadToEndAsync();
            }

            if (body == null) return NotFound("Body is required");
            if (String.IsNullOrWhiteSpace(body)) return NotFound($"Parameter {nameof(body)} is required");

            var result = await KnowYourCustomerAPI.KnowYourCustomer.PerformCmdAsync(HttpContext, body);
            return result;
        }

        [HttpPost("console/query")]
        [Authorize(Roles = "devops")]
        public async Task<IActionResult> processQueryAsync()
        {
            string body = "";

            using (StreamReader reader = new StreamReader(Request.Body, Encoding.UTF8))
            {
                body = await reader.ReadToEndAsync();
            }

            if (body == null) return NotFound("Body is required");
            if (String.IsNullOrWhiteSpace(body)) return NotFound($"Parameter {nameof(body)} is required");

            var result = await KnowYourCustomerAPI.KnowYourCustomer.PerformQryAsync(HttpContext, body);
            return result;
        }

		[HttpGet("console/scriptEnEjecucion")]
		[AllowAnonymous]
		public IActionResult ScriptEnEjecucion()
		{
			IActionResult result;
			try
			{
				result = Ok("Script executed: " + KnowYourCustomerAPI.KnowYourCustomer.ScriptEnEjecucion);
			}
			catch
			{
				result = Ok("Vuelva a ejecutar el request");
			}
			return result;
		}

		[HttpGet("producer/stop")]
        [AllowAnonymous]
        public async Task<IActionResult> StopProducerAsync()
        {
			await Consumer.StopAllConsumerActiveInMemoryAsync();

			IProducer kafka = Integration.Kafka;
            if (kafka != null)
            {
                await Integration.Kafka.StopProducerAsync();
                return Ok();
            }
            return BadRequest("Kafka is not configured.");
        }

        [HttpGet("consumer/stop")]
        [AllowAnonymous]
        public async Task<IActionResult> StopConsumerAsync()
        {
            await Consumer.StopAllConsumerActiveInMemoryAsync();
            return Ok("All consumers are stopped!");
        }

        [HttpPost("consumer/reset")]
        [Authorize(Roles = "devops")]
        public async Task<IEnumerable<int>> ResetAsync(int[] consumerIds)
        {
            return await Consumer.ResetAsync(consumerIds);
        }

        [HttpGet("console/ping")]
        [AllowAnonymous]
        public IActionResult Ping()
        {
            return Ok("pong");
        }

        [HttpGet("consumers")]
        [Authorize(Roles = "devops")]
        public List<string> List()
        {
            return Consumer.ConsumersInfo();
        }

        [HttpGet("consumer/start")]
        [AllowAnonymous]
        public IActionResult StartConsumer()
        {
            Integration.UseKafka = true;
            if (Integration.UseKafka)
            {
                new Consumers().CreateConsumerForTopics();
            }
            return Ok("Consumers restarted!");
        }
    }
}

