﻿using GamesEngine.Bets;
using GamesEngine.Business;
using GamesEngine.Domains;
using GamesEngine.Exchange;
using GamesEngine.Finance;
using GamesEngine.Gameboards.Lines;
using GamesEngine.Games;
using GamesEngine.Games.Lines;
using GamesEngine.Games.Tournaments;
using GamesEngine.PurchaseOrders;
using GamesEngine.Settings;
using GamesEngine.Time;
using GamesEngineTests.Unit_Tests.Games.Lotto;
using GamesEngineTests.UnitTests.Mocks;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using Puppeteer.EventSourcing;
using System;
using System.Collections.Generic;
using System.Linq;
using Unit.Games.Tools;

namespace GamesEngineTests.Unit_Tests.SportsBook
{
	

	[TestClass]
	public class LinesTest
	{
		internal static readonly League NBA = new League(Sport.BASKETBALL, nameof(NBA), "NBA", 2);
		internal static readonly League FIFA = new League(Sport.SOCCER, nameof(FIFA), "FIFA", 4);

		internal class EuroTournament : Tournament
		{
			private readonly int year;
			private readonly GamesEngine.PurchaseOrders.Store store;

			internal EuroTournament(Tournaments tournaments) : base(tournaments, LinesTest.FIFA, TournamentType.Knockout, 2, nameof(EuroTournament))
			{
				this.year = 2020;

				var firstGames = new FirstGames();
				var game1 = base.GetNewGame(1);
				var game2 = base.GetNewGame(2);
				firstGames.Add(game1);
				firstGames.Add(game2);

				this.OpenRegistration();
				var teamId = tournaments.NextTeamId();
				var team1 = tournaments.CreateTeam(LinesTest.FIFA, teamId++, "Germany");
				var team2 = tournaments.CreateTeam(LinesTest.FIFA, teamId++, "France");
				var team3 = tournaments.CreateTeam(LinesTest.FIFA, teamId++, "Spain");
				var team4 = tournaments.CreateTeam(LinesTest.FIFA, teamId++, "Belgium");

				this.Register(team1);
				this.Register(team2);
				this.Register(team3);
				this.Register(team4);

				game1.AssignTeamA(team1);
				game1.AssignTeamB(team2);
				game2.AssignTeamA(team3);
				game2.AssignTeamB(team4);

				this.CloseRegistration();
				this.Start();
			}

			internal int Year
			{
				get
				{
					return year;
				}
			}
		}

		[TestMethod]
		public void CreateALeagueAndTournament_test()
		{
			Company company = new Company();
			int idSport = 16;
			int leagueId = company.Tournaments.Leagues.NextLeagueId;
			int tournamentId = company.Tournaments.NextTournamentId;
			Sport sport = company.Tournaments.Sports.FindById(idSport);
			League league = company.Tournaments.Leagues.Create(sport, "NBA", "nba", leagueId);
			Assert.AreEqual(league.Id, 1);
			var tournament = company.Tournaments.CreateTournament(league, tournamentId, "Tournament 2021");
			Assert.AreEqual(tournament.Id, 1);
		}

		[TestMethod]
		public void EnableDomainOrDisableDomain()
		{
			var company = new Company();
			var domain = company.Sales.CreateDomain(false, 1, "localhost", PaymentChannels.Agents.INSIDER);
			var store = company.Sales.CreateStore(1, "store test");
			store.MakeCurrent();
			store.Add(domain);

			Tournaments tournaments = company.Tournaments;
			var leagueId = company.Tournaments.Leagues.NextLeagueId;
			var tournamentId = company.Tournaments.NextTournamentId;
			var sport = company.Tournaments.Sports.FindById(1);
			sport.CreateAndReplaceDefaultBackground("bg", "bg.jpg");
			sport.CreateAndReplaceDefaultSideBySide("sbs", "sbs.jpg");
			var league = company.Tournaments.Leagues.Create(sport, "soccer league", "SL", leagueId);
			var tournament = tournaments.CreateTournament(league, tournamentId, "soccer tournament");

			tournament.OpenRegistration();

			var gameDate = new DateTime(2020, 11, 20, 8, 0, 0);
			var teamId = tournaments.NextTeamId();
			var teamA = tournaments.CreateTeam(league, teamId, "team A");
			tournament.Register(teamA);
			teamId = tournaments.NextTeamId();
			var teamB = tournaments.CreateTeam(league, teamId, "team B");
			tournament.Register(teamB);
			var gameNumber = tournament.NextGameNumber();
			var game = tournament.GetNewGame(gameNumber, teamA, teamB, gameDate);
			game.SetFavorite(game.TeamA);

			tournament.CloseRegistration();
			tournament.Start();

			var betBoard = company.Betboard(tournament);
			betBoard.AcceptFrom(tournament);
			var catalog = betBoard.Catalog;
			int questionId = catalog.NextQuestionId();
			var spreadQuestion = catalog.CreateTierOneSpreadQuestion(questionId++, "ss");
			spreadQuestion.SetParameters(2, -120, 105);

			var matchDay = betBoard.Matchday(game.ScheduledDate.Date);
			var showcase = matchDay.GetShowcase(game);
			var shelve = showcase.InsertShelve(0);

			var lineId = betBoard.NextLineId();
			var now = new DateTime(2020, 11, 19, 23, 45, 0);
			var spread = (SpreadLine)shelve.CreateLine(lineId, spreadQuestion, "N/A", now);

			Assert.IsTrue(spread.IsEnabled(domain));
			spread.DisableDomain(domain);
			Assert.IsFalse(spread.IsEnabled(domain));
			spread.EnableDomain(domain);
			Assert.IsTrue(spread.IsEnabled(domain));

			var domain2 = company.Sales.CreateDomain(false, 2, "localhost5", PaymentChannels.Agents.INSIDER);
			store.Add(domain2);
			Assert.IsFalse(spread.IsEnabled(domain2));
		}

		[TestMethod]
		public void ActivationsInDomains()
		{
			var company = new Company();
			var domain = company.Sales.CreateDomain(false, 1, "localhost", PaymentChannels.Agents.INSIDER);
			var store = company.Sales.CreateStore(1, "store test");
			store.MakeCurrent();
			store.Add(domain);

			Tournaments tournaments = company.Tournaments;
			var leagueId = company.Tournaments.Leagues.NextLeagueId;
			var tournamentId = company.Tournaments.NextTournamentId;
			var sport = company.Tournaments.Sports.FindById(1);
			sport.CreateAndReplaceDefaultBackground("bg", "bg.jpg");
			sport.CreateAndReplaceDefaultSideBySide("sbs", "sbs.jpg");
			var league = company.Tournaments.Leagues.Create(sport, "soccer league", "SL", leagueId);
			var tournament = tournaments.CreateTournament(league, tournamentId, "soccer tournament");

			tournament.OpenRegistration();

			var gameDate = new DateTime(2020, 11, 20, 8, 0, 0);
			var teamId = tournaments.NextTeamId();
			var teamA = tournaments.CreateTeam(league, teamId, "team A");
			tournament.Register(teamA);
			teamId = tournaments.NextTeamId();
			var teamB = tournaments.CreateTeam(league, teamId, "team B");
			tournament.Register(teamB);
			var gameNumber = tournament.NextGameNumber();
			var game = tournament.GetNewGame(gameNumber, teamA, teamB, gameDate);
			game.SetFavorite(game.TeamA);

			tournament.CloseRegistration();
			tournament.Start();

			var betBoard = company.Betboard(tournament);
			betBoard.AcceptFrom(tournament);
			var catalog = betBoard.Catalog;
			int questionId = catalog.NextQuestionId();
			var spreadQuestion = catalog.CreateTierOneSpreadQuestion(questionId++, "ss");
			spreadQuestion.SetParameters(2, -120, 105);

			var matchDay = betBoard.Matchday(game.ScheduledDate.Date);
			var showcase = matchDay.GetShowcase(game);
			var shelve = showcase.InsertShelve(0);

			var lineId = betBoard.NextLineId();
			var now = new DateTime(2020, 11, 19, 23, 45, 0);
			var spread = (SpreadLine)shelve.CreateLine(lineId, spreadQuestion, "N/A", now);
			var spreadV2 = shelve.CreateNewVersionForSpreadLine(spread.LineId, 120, -105, "N/A", now.AddSeconds(5));
			Assert.AreNotEqual(spread.ActivationsInDomains, spreadV2.ActivationsInDomains);
			Assert.AreEqual(1, spread.ActivationsInDomains.IncludedDomains.Count());
			Assert.AreEqual(1, spreadV2.ActivationsInDomains.IncludedDomains.Count());
			Assert.IsTrue(spread.ActivationsInDomains.Contains(spreadV2.ActivationsInDomains.IncludedDomains.First()));
		}

		[TestMethod]
		public void GamesByTournaments_test()
		{
			DateTime now = new DateTime(2020, 11, 02);

			Company company = new Company();
			var store = company.Sales.CreateStore(1, "Test Store");
			store.MakeCurrent();
			var domain = company.Sales.CreateDomain(false, 1, "localhost", PaymentChannels.Agents.INSIDER);
			company.Sales.CurrentStore.Add(domain);
			var sport = company.Tournaments.Sports.FindById(1);
			sport.CreateAndReplaceDefaultBackground("bg", "bg.jpg");
			sport.CreateAndReplaceDefaultSideBySide("sbs", "sbs.jpg");
			sport = company.Tournaments.Sports.FindById(16);
			sport.CreateAndReplaceDefaultBackground("bg", "bg.jpg");
			sport.CreateAndReplaceDefaultSideBySide("sbs", "sbs.jpg");
			Tournaments tournaments = company.Tournaments;
			League league1 = company.Tournaments.Leagues.Create(Sport.BASKETBALL, "NBA", "nba", company.Tournaments.Leagues.NextLeagueId);
			League league2 = company.Tournaments.Leagues.Create(Sport.BASEBALL, "MLB", "mlb", company.Tournaments.Leagues.NextLeagueId);

			var tournament1 = company.Tournaments.CreateTournament(league2, company.Tournaments.NextTournamentId, "NBA 2021");
			tournament1.OpenRegistration();
			var teamId = tournaments.NextTeamId();
			var teama = company.Tournaments.CreateTeam(league2, teamId, "Chiriqui", "CH");
			teamId = tournaments.NextTeamId();
			var teamb = company.Tournaments.CreateTeam(league2, teamId, "Herrera", "HE");
			tournament1.Register(teama);
			tournament1.Register(teamb);
			var game = tournament1.GetNewGame(1, teama, teamb);
			game.Home = game.TeamA;
			game.Visitor = game.TeamB;
			game.SetFavorite(game.TeamA);
			game.UpdateDate(false, now.AddHours(2), now);
			game.Reschedule(now);

			teamId = tournaments.NextTeamId();
			teama = company.Tournaments.CreateTeam(league2, teamId, "P. Metro", "PM");
			teamId = tournaments.NextTeamId();
			teamb = company.Tournaments.CreateTeam(league2, teamId, "Los Santos", "LS");
			tournament1.Register(teama);
			tournament1.Register(teamb);
			var game2 = tournament1.GetNewGame(2, teama, teamb);
			game2.Home = game2.TeamA;
			game2.Visitor = game2.TeamB;
			game2.SetFavorite(game2.TeamB);
			game2.Reschedule(now);

			tournament1.CloseRegistration();
			tournament1.Start();

			var betBoard = company.Betboard(tournament1);
			betBoard.AcceptFrom(tournament1);

			var tournament2 = company.Tournaments.CreateTournament(league1, company.Tournaments.NextTournamentId, "MLB 2021");
			tournament2.OpenRegistration();
			teamId = tournaments.NextTeamId();
			teama = company.Tournaments.CreateTeam(league1, teamId, "Bocas", "BT");
			teamId = tournaments.NextTeamId();
			teamb = company.Tournaments.CreateTeam(league1, teamId, "Darien", "DA");
			tournament2.Register(teama);
			tournament2.Register(teamb);
			game = tournament2.GetNewGame(1, teama, teamb);
			game.Home = game.TeamA;
			game.Visitor = game.TeamB;
			game.SetFavorite(game.TeamA);

			teamId = tournaments.NextTeamId();
			teama = company.Tournaments.CreateTeam(league1, teamId, "Veraguas", "VE");
			teamId = tournaments.NextTeamId();
			teamb = company.Tournaments.CreateTeam(league1, teamId, "Colon", "CO");
			tournament2.Register(teama);
			tournament2.Register(teamb);
			game2 = tournament2.GetNewGame(2, teama, teamb);
			game2.Home = game2.TeamA;
			game2.Visitor = game2.TeamB;
			game2.SetFavorite(game2.TeamB);

			tournament2.CloseRegistration();
			tournament2.Start();
			betBoard = company.Betboard(tournament2);
			betBoard.AcceptFrom(tournament2);

			var ngames = company.Tournaments.GamesFor(now.Date);
			int i = 1;
			foreach (var ngame in ngames)
			{
				Assert.AreEqual(ngame.Number, i);
				i++;
			}
		}

		[TestMethod]
		public void LineBI_Version_Score_Winner_Loser_Test()
		{
			string connection = "persistsecurityinfo=True;port=3306;Server=localhost;Database=lines2;user id=*********;password=*********;SslMode=none";
			GamesEngine.Settings.Integration.ConfigurationForAutoTest(connection);

			ReceiverOfHistorical receiver = new ReceiverOfHistorical();
			//receiver.InitHistorical(HistoricalDatabaseType.MySQL, connection);
			receiver.InitHistorical(HistoricalDatabaseType.InMemory, connection);
			new LinesBIAPI.Consumers().CreateConsumerForTopics(receiver);

			bool itIsThePresent = true;
			DateTime now = new DateTime(2021, 01, 05, 11, 12, 13);
			string gradedBy = "Juan";

			Company company = new Company();
			CurrenciesTest.AddCurrencies();
			var customerWithoutPreference = company.GetOrCreateCustomerById("5D2430557");
			var customer = company.GetOrCreateCustomerById("5D2430383");
			customer.Name = "Tester User";
			customer.Identifier = "ID012345678901234567890123456789012346";
			var player = customer.Player;
			var sport = company.Tournaments.Sports.FindById(1);
			sport.CreateAndReplaceDefaultBackground("bg", "bg.jpg");
			sport.CreateAndReplaceDefaultSideBySide("sbs", "sbs.jpg");
			GamesEngine.Games.Tournaments.Tournament t = new Super8MockGenerator(company).
				ConfigureASuper8Tournament(2018).
				ConfigureTheGamesAndPlayersToTheTournament().Tournament();
			t.Start();

			Game game = t.GetGameNumber(1);
			Team teama = game.TeamA;
			Team teamb = game.TeamB;
			game.SetFavorite(game.TeamA);

			player.Preferences.GamesReminder.Remind(game);
			Assert.IsTrue(player.Preferences.GamesReminder.Contains(game));
			Assert.IsFalse(customerWithoutPreference.Player.Preferences.GamesReminder.Contains(game));

			var betBoard = company.Betboard(t);
			betBoard.AcceptFrom(t);

			TournamentSchedule schedule = t.Schedule;
			schedule.AddGameToMatchday(game, now.Date, game.TeamA, game.TeamA);

			Matchday matchDay = company.Betboard(t).Matchday(now.Date);
			Showcase sc = matchDay[game];

			int teamAReward = 105;
			int teamBReward = -110;
			int tieReward = -110;
			var lineId = betBoard.NextLineId();
			var fila1 = 0;

			var questionML = betBoard.Catalog.CreateTierOneMoneyQuestion(1, "mm");
			var questionMDL = betBoard.Catalog.CreateTierOneMoneyDrawQuestion(2, "dd");
			var questionSL = betBoard.Catalog.CreateTierOneSpreadQuestion(3, "ss");
			var questionTP = betBoard.Catalog.CreateTierOneTotalPointsQuestion(4, "tt");

			questionML.SetParameters(teamAReward, teamBReward);
			var ml = (MoneyLine)matchDay[game][fila1].CreateLine(lineId + 1, questionML, gradedBy, now);
			questionMDL.SetParameters(teamAReward, tieReward, teamBReward);
			var mdl = (MoneyDrawLine)matchDay[game][fila1].CreateLine(lineId + 2, questionMDL, gradedBy, now);
			questionSL.SetParameters(3, teamAReward + 1, teamBReward + 1);
			var sl = (SpreadLine)matchDay[game][fila1].CreateLine(lineId + 3, questionSL, gradedBy, now);
			questionTP.SetParameters(2.5, 110, -105);
			var tp = (TotalPointsLine)matchDay[game][fila1].CreateLine(lineId + 4, questionTP, gradedBy, now);

			// props 
			var questionYN = betBoard.Catalog.CreateTierTwoYesNoQuestion(5, "YN", "YES No Rewards TEST");
			questionYN.SetParameters(106, -110);
			var yn = (YesNoLine)matchDay[game][fila1].CreateLine(lineId + 5, questionYN, gradedBy, now);

			var questionOU = betBoard.Catalog.CreateTierTwoOverUnderQuestion(6, "OU", "Over under rewards TEST");
			questionOU.SetParameters(10.5, 120, -115);
			var ou = (OverUnderLine)matchDay[game][fila1].CreateLine(lineId + 6, questionOU, gradedBy, now);

			var questionFixed = betBoard.Catalog.CreateTierTwoFixedQuestion(7, "FC", "Quien va a ganar el partido?");
			questionFixed.SetParameters(new List<string> { "azul", "blanco", "rojo" }, new List<int> { 120, 115, -105 });
			var fixeds = (FixedLine)matchDay[game][fila1].CreateLine(lineId + 7, questionFixed, gradedBy, now);

			//ml.AllowToPublishOn(game.CurrentPeriod);
			ml.Publish(itIsThePresent, now);
			//mdl.AllowToPublishOn(game.CurrentPeriod);
			mdl.Publish(itIsThePresent, now);
			//sl.AllowToPublishOn(game.CurrentPeriod);
			sl.Publish(itIsThePresent, now);
			//tp.AllowToPublishOn(game.CurrentPeriod);
			tp.Publish(itIsThePresent, now);
			yn.AllowToPublishOn(game.CurrentPeriod);
			yn.Publish(itIsThePresent, now);
			ou.AllowToPublishOn(game.CurrentPeriod);
			ou.Publish(itIsThePresent, now);
			fixeds.AllowToPublishOn(game.CurrentPeriod);
			fixeds.Publish(itIsThePresent, now);


			// compras props
			WagerAnswer answeryn = YesNoAnswer.YES;
			yn.PlaceBet(1, player, now, answeryn, 0.33m);
			WagerAnswer answerou = OverUnderAnswer.OVER;
			ou.PlaceBet(2, player, now, answerou, 0.77m);
			WagerAnswer answerf = fixeds.GetAnAnswer("rojo");
			fixeds.PlaceBet(3, player, now, answerf, 1.33m);

			// compras tierOne
			ml.PlaceBet(4, player, now, ml.GetTeamAnswer(teamb), 2.33m);
			mdl.PlaceBet(5, player, now, mdl.GetTeamAnswer(teama), 3.37m);
			//mdl.PlaceBet(5, player, now, mdl.GetTieAnswer(teama, teamb), 3.33m);
			sl.PlaceBet(6, player, now, sl.GetTeamAnswer(teama), 3.77m);
			WagerAnswer wagertp = OverUnderAnswer.UNDER;
			tp.PlaceBet(7, player, now, wagertp, 4.33m);


			var lines = sc.PendingOrRegradedLines();
			Assert.AreEqual(7, lines.Count());
			lines = sc.GradedLines();
			Assert.AreEqual(0, lines.Count());

			var event1 = "Begin";
			var endGame = "End";
			game.MoveToTheNextPeriod(itIsThePresent, event1, now);
			WagerAnswer answeryn2 = YesNoAnswer.YES;
			yn.PlaceBet(10, player, now, answeryn, 0.33m);
			game.NewResult(itIsThePresent, now, game.TeamA, 1, game.TeamB, 2);
			Assert.AreEqual(game.CurrentPeriod.Name, Timeline.IN_PLAY);
			game.MoveToTheNextPeriod(itIsThePresent, endGame, now);

			sc.Grade(itIsThePresent, yn, true, now, gradedBy);
			sc.Grade(itIsThePresent, ou, 1, now, gradedBy);
			sc.Grade(itIsThePresent, fixeds, "rojo", now, gradedBy);

			// end game >
			game.Start(itIsThePresent, now, now);
			game.NewResult(itIsThePresent, now, game.TeamA, 10, game.TeamB, 11);
			game.End(itIsThePresent, now);
			sc.Grade(itIsThePresent, ml, now, teama, 10, teamb, 11, gradedBy);
			sc.Grade(itIsThePresent, mdl, now, teama, 10, teamb, 11, gradedBy);
			sc.Grade(itIsThePresent, tp, 10 + 11, now, gradedBy);
			sc.Grade(itIsThePresent, sl, now, teama, 10, teamb, 11, gradedBy);
			Assert.IsTrue(player.Preferences.GamesReminder.Contains(game));

			sc.Regrade(itIsThePresent, yn, now, gradedBy);
			//sc.Grade(itIsThePresent, now, teama, 10, teamb, 12, gradedBy);


			lines = sc.PendingOrRegradedLines();
			Assert.AreEqual(1, lines.Count());
			lines = sc.GradedLines();
			Assert.AreEqual(6, lines.Count());

			// FALTA 
			// 1 Regrade de las lineas anteriores.. 
			// 2 luego otro grade
			// 3 no action
			// no action a grade 

			// otro tc que solo tenga NO ACtion
		}

		[TestMethod]
		public void LinesWonLostPending_test()
		{
			string connection = "persistsecurityinfo=True;port=3306;Server=localhost;Database=lines2;user id=*********;password=*********;SslMode=none";
			GamesEngine.Settings.Integration.ConfigurationForAutoTest(connection);

			ReceiverOfHistorical receiver = new ReceiverOfHistorical();
			//receiver.InitHistorical(HistoricalDatabaseType.MySQL, connection);
			receiver.InitHistorical(HistoricalDatabaseType.InMemory, connection);
			new LinesBIAPI.Consumers().CreateConsumerForTopics(receiver);

			bool itIsThePresent = true;
			DateTime now = new DateTime(2021, 01, 05, 11, 12, 13);
			string gradedBy = "Juan";

			Company company = new Company();
			CurrenciesTest.AddCurrencies();
			var customerWithoutPreference = company.GetOrCreateCustomerById("5D2430557");
			var customer = company.GetOrCreateCustomerById("5D2430383");
			customer.Name = "Tester User";
			customer.Identifier = "ID012345678901234567890123456789012346";
			var player = customer.Player;
			var sport = company.Tournaments.Sports.FindById(1);
			sport.CreateAndReplaceDefaultBackground("bg", "bg.jpg");
			sport.CreateAndReplaceDefaultSideBySide("sbs", "sbs.jpg");
			GamesEngine.Games.Tournaments.Tournament t = new Super8MockGenerator(company).
				ConfigureASuper8Tournament(2018).
				ConfigureTheGamesAndPlayersToTheTournament().Tournament();
			t.Start();

			Game game = t.GetGameNumber(1);
			Team teama = game.TeamA;
			Team teamb = game.TeamB;
			game.SetFavorite(game.TeamA);

			player.Preferences.GamesReminder.Remind(game);
			Assert.IsTrue(player.Preferences.GamesReminder.Contains(game));
			Assert.IsFalse(customerWithoutPreference.Player.Preferences.GamesReminder.Contains(game));

			var betBoard = company.Betboard(t);
			betBoard.AcceptFrom(t);

			TournamentSchedule schedule = t.Schedule;
			schedule.AddGameToMatchday(game, now.Date, game.TeamA, game.TeamA);

			Matchday matchDay = company.Betboard(t).Matchday(now.Date);
			Showcase sc = matchDay[game];

			int teamAReward = 105;
			int teamBReward = -110;
			int tieReward = -110;
			var lineId = betBoard.NextLineId();
			var fila1 = 0;

			var questionML = betBoard.Catalog.CreateTierOneMoneyQuestion(1, "mm");
			var questionMDL = betBoard.Catalog.CreateTierOneMoneyDrawQuestion(2, "dd");
			var questionSL = betBoard.Catalog.CreateTierOneSpreadQuestion(3, "ss");
			var questionTP = betBoard.Catalog.CreateTierOneTotalPointsQuestion(4, "tt");

			questionML.SetParameters(teamAReward, teamBReward);
			var ml = (MoneyLine)matchDay[game][fila1].CreateLine(lineId + 1, questionML, gradedBy, now);
			questionMDL.SetParameters(teamAReward, tieReward, teamBReward);
			var mdl = (MoneyDrawLine)matchDay[game][fila1].CreateLine(lineId + 2, questionMDL, gradedBy, now);
			questionSL.SetParameters(3, teamAReward + 1, teamBReward + 1);
			var sl = (SpreadLine)matchDay[game][fila1].CreateLine(lineId + 3, questionSL, gradedBy, now);
			questionTP.SetParameters(2.5, 110, -105);
			var tp = (TotalPointsLine)matchDay[game][fila1].CreateLine(lineId + 4, questionTP, gradedBy, now);

			// props 
			var questionYN = betBoard.Catalog.CreateTierTwoYesNoQuestion(5, "YN", "YES No Rewards TEST");
			questionYN.SetParameters(106, -110);
			var yn = (YesNoLine)matchDay[game][fila1].CreateLine(lineId + 5, questionYN, gradedBy, now);

			var questionOU = betBoard.Catalog.CreateTierTwoOverUnderQuestion(6, "OU", "Over under rewards TEST");
			questionOU.SetParameters(10.5, 120, -115);
			var ou = (OverUnderLine)matchDay[game][fila1].CreateLine(lineId + 6, questionOU, gradedBy, now);

			var questionFixed = betBoard.Catalog.CreateTierTwoFixedQuestion(7, "FC", "Quien va a ganar el partido?");
			questionFixed.SetParameters(new List<string> { "azul", "blanco", "rojo" }, new List<int> { 120, 115, -105 });
			var fixeds = (FixedLine)matchDay[game][fila1].CreateLine(lineId + 7, questionFixed, gradedBy, now);

			//ml.AllowToPublishOn(game.CurrentPeriod);
			ml.Publish(itIsThePresent, now);
			//mdl.AllowToPublishOn(game.CurrentPeriod);
			mdl.Publish(itIsThePresent, now);
			//sl.AllowToPublishOn(game.CurrentPeriod);
			sl.Publish(itIsThePresent, now);
			//tp.AllowToPublishOn(game.CurrentPeriod);
			tp.Publish(itIsThePresent, now);
			yn.AllowToPublishOn(game.CurrentPeriod);
			yn.Publish(itIsThePresent, now);
			ou.AllowToPublishOn(game.CurrentPeriod);
			ou.Publish(itIsThePresent, now);
			fixeds.AllowToPublishOn(game.CurrentPeriod);
			fixeds.Publish(itIsThePresent, now);

			var totalToRisk = sc.RiskAssestment.TotalToRisk;
			var totalToWin = sc.RiskAssestment.TotalToWin;
			var amountToPending = sc.RiskAssestment.AmountToPending;
			var amountToWon = sc.RiskAssestment.AmountToWon;
			var amountToRisk = sc.RiskAssestment.AmountToRisk;
			Assert.AreEqual(0, sc.RiskAssestment.AmountToPending);
			Assert.AreEqual(0, sc.RiskAssestment.AmountToWon);
			Assert.AreEqual(0, sc.RiskAssestment.AmountToRisk);
			// compras props
			WagerAnswer answeryn = YesNoAnswer.YES;
			WagerAnswer answerno = YesNoAnswer.NO;
			yn.PlaceBet(1, player, now, answeryn, 0.33m);
			yn.PlaceBet(2, player, now, answerno, 0.47m);

			Assert.AreEqual(0.80m, sc.RiskAssestment.AmountToPending);
			Assert.AreEqual(0, sc.RiskAssestment.AmountToWon);
			Assert.AreEqual(0, sc.RiskAssestment.AmountToRisk);

			yn = yn.NewVersion(110, -105, gradedBy, now.AddMinutes(1));
			yn.PlaceBet(1, player, now, answeryn, 0.25m);
			Assert.AreEqual(1.05m, sc.RiskAssestment.AmountToPending);
			Assert.AreEqual(0, sc.RiskAssestment.AmountToWon);
			Assert.AreEqual(0, sc.RiskAssestment.AmountToRisk);

			amountToPending = sc.RiskAssestment.AmountToPending;
			amountToRisk = sc.RiskAssestment.AmountToRisk;
			sc.Grade(itIsThePresent, yn, true, now, gradedBy);
			amountToRisk = sc.RiskAssestment.AmountToRisk;
			totalToRisk = sc.RiskAssestment.TotalToRisk;
			totalToWin = sc.RiskAssestment.TotalToWin;
			amountToPending = sc.RiskAssestment.AmountToPending;
			amountToWon = sc.RiskAssestment.AmountToWon;


			WagerAnswer answerou = OverUnderAnswer.OVER;
			ou.PlaceBet(2, player, now, answerou, 0.77m);
			WagerAnswer answerf = fixeds.GetAnAnswer("rojo");
			fixeds.PlaceBet(3, player, now, answerf, 1.33m);

			// compras tierOne
			ml.PlaceBet(4, player, now, ml.GetTeamAnswer(teamb), 2.33m);
			mdl.PlaceBet(5, player, now, mdl.GetTeamAnswer(teama), 3.37m);
			//mdl.PlaceBet(5, player, now, mdl.GetTieAnswer(teama, teamb), 3.33m);
			sl.PlaceBet(6, player, now, sl.GetTeamAnswer(teama), 3.77m);
			WagerAnswer wagertp = OverUnderAnswer.UNDER;
			tp.PlaceBet(7, player, now, wagertp, 4.33m);

			totalToRisk = sc.RiskAssestment.TotalToRisk;
			totalToWin = sc.RiskAssestment.TotalToWin;
			amountToPending = sc.RiskAssestment.AmountToPending;
			amountToWon = sc.RiskAssestment.AmountToWon;
			amountToRisk = sc.RiskAssestment.AmountToRisk;

			Assert.AreEqual(15.90m, sc.RiskAssestment.AmountToPending);
			Assert.AreEqual(0.63m, sc.RiskAssestment.AmountToWon);
			Assert.AreEqual(0.47m, sc.RiskAssestment.AmountToRisk);

			var event1 = "Begin";
			var endGame = "End";
			game.MoveToTheNextPeriod(itIsThePresent, event1, now);
			game.NewResult(itIsThePresent, now, game.TeamA, 1, game.TeamB, 2);
			Assert.AreEqual(game.CurrentPeriod.Name, Timeline.IN_PLAY);
			game.MoveToTheNextPeriod(itIsThePresent, endGame, now);

			sc.Grade(itIsThePresent, ou, 1, now, gradedBy);
			sc.Grade(itIsThePresent, fixeds, "rojo", now, gradedBy);

			// end game >
			game.Start(itIsThePresent, now, now);
			game.NewResult(itIsThePresent, now, game.TeamA, 10, game.TeamB, 11);
			game.End(itIsThePresent, now);
			sc.Grade(itIsThePresent, ml, now, teama, 10, teamb, 11, gradedBy);
			sc.Grade(itIsThePresent, mdl, now, teama, 10, teamb, 11, gradedBy);
			sc.Grade(itIsThePresent, tp, 10 + 11, now, gradedBy);
			sc.Grade(itIsThePresent, sl, now, teama, 10, teamb, 11, gradedBy);
		}

		[TestMethod]
		public void PosibleEvents_Test()
		{
			Company company = new Company();
			company.Sales.CreateStore(1, "Test Store").MakeCurrent();
			company.Accounting = new MockAccounting();
			Tournaments tournaments = company.Tournaments;
			var nba = new NBATournament(tournaments);
			var game = nba.GetGameNumber(1);
			var events = game.CurrentPeriod.Events;
			string eventostr = "";
			foreach (var evento in events)
			{
				eventostr += evento.Name;
			}
			Assert.AreEqual(eventostr, "BeginCancel");
		}

		[TestMethod]
		public void MoveToNextPeriod_Event_Test()
		{
			DateTime now = new DateTime(2021, 01, 05, 11, 12, 13);
			Company company = new Company();
			company.Sales.CreateStore(1, "Test Store").MakeCurrent();
			bool itIsThePresent = true;
			company.Accounting = new MockAccounting();
			Tournaments tournaments = company.Tournaments;
			var nba = new NBATournament(tournaments);
			var game = nba.GetGameNumber(1);

			var eventoElegido = "Begin";
			game.MoveToTheNextPeriod(itIsThePresent, eventoElegido, now);
			Assert.AreEqual(game.CurrentPeriod.Name, Timeline.IN_PLAY);

			eventoElegido = "Begin";
			game.MoveToTheNextPeriod(itIsThePresent, eventoElegido, now);
			Assert.AreEqual(game.CurrentPeriod.Name, "First quarter");

			game.MoveToTheNextPeriod(itIsThePresent, eventoElegido, now);
			game.MoveToTheNextPeriod(itIsThePresent, eventoElegido, now);
			game.MoveToTheNextPeriod(itIsThePresent, eventoElegido, now);
			eventoElegido = "End";
			game.NewResult(itIsThePresent, now, game.TeamA, 1, game.TeamB, 2);
			game.MoveToTheNextPeriod(itIsThePresent, eventoElegido, now);
			Assert.AreEqual(game.CurrentPeriod.Name, Timeline.GAME_OVER);
		}

		[TestMethod]
		public void LineInfoHistory_Test()
		{
			string eventoElegido = "Begin";
			bool itIsThePresent = true;
			string gradedBy = "Juan";
			DateTime now = new DateTime(2021, 01, 05, 11, 12, 13);
			var dayAsText = $"{now.Month}/{now.Day}/{now.Year}";
			Company company = new Company();
			var domain = company.Sales.CreateDomain(false, 1, "localhost", PaymentChannels.Agents.INSIDER);
			company.Sales.CreateStore(1, "store prueba").MakeCurrent();
			company.Sales.CurrentStore.Add(domain);
			company.Accounting = new MockAccounting();
			Tournaments tournaments = company.Tournaments;
			var nba = new EuroTournament(tournaments);
			var game = nba.GetGameNumber(1);
			game.SetFavorite(game.TeamA);
			var sport = company.Tournaments.Sports.FindById(1);
			sport.CreateAndReplaceDefaultBackground("bg", "bg.jpg");
			sport.CreateAndReplaceDefaultSideBySide("sbs", "sbs.jpg");
			var betBoard = company.Betboard(nba);
			TournamentSchedule schedule = nba.Schedule;
			schedule.AddGameToMatchday(game, now.Date, game.TeamA, game.TeamA);

			Matchday matchDay = company.Betboard(game.Tournament).Matchday(now.Date);
			Showcase sc = matchDay[game];
			var preGamePeriod = game.CurrentPeriod;
			Assert.AreEqual(sc.RelevantsOn(preGamePeriod).Count(), 0);

			int teamAReward = 105;
			int teamBReward = -110;
			int tieReward = -110;
			var lineId = betBoard.NextLineId();
			var fila1 = 0;

			var questionML = (MoneyQuestion)betBoard.Catalog.CreateTierOneMoneyQuestion(1, "mm");
			questionML.SetParameters(teamAReward, teamBReward);
			var ml = (MoneyLine)matchDay[game][fila1].CreateLine(lineId, questionML, gradedBy, now);

			var questionMDL = (MoneyDrawQuestion)betBoard.Catalog.CreateTierOneMoneyDrawQuestion(2, "dd");
			questionMDL.SetParameters(teamAReward, tieReward, teamBReward);
			var mdl = (MoneyDrawLine)matchDay[game][fila1].CreateLine(lineId + 1, questionMDL, gradedBy, now);

			var questionSL = (SpreadQuestion)betBoard.Catalog.CreateTierOneSpreadQuestion(3, "ss");
			questionSL.SetParameters(3, teamAReward + 1, teamBReward + 1);
			var sl = (SpreadLine)matchDay[game][fila1].CreateLine(lineId + 2, questionSL, gradedBy, now);

			var questionTP = (TotalPointsQuestion)betBoard.Catalog.CreateTierOneTotalPointsQuestion(4, "tt");
			questionTP.SetParameters(2.5, 110, -105);
			var tp = (TotalPointsLine)matchDay[game][fila1].CreateLine(lineId + 3, questionTP, gradedBy, now);

			if (!ml.IsAllowedToPublishOn(game.CurrentPeriod)) ml.AllowToPublishOn(preGamePeriod);
			ml.Publish(itIsThePresent, now);
			if (!mdl.IsAllowedToPublishOn(game.CurrentPeriod)) mdl.AllowToPublishOn(preGamePeriod);
			mdl.Publish(itIsThePresent, now);
			if (!sl.IsAllowedToPublishOn(game.CurrentPeriod)) sl.AllowToPublishOn(preGamePeriod);
			sl.Publish(itIsThePresent, now);
			if (!tp.IsAllowedToPublishOn(game.CurrentPeriod)) tp.AllowToPublishOn(preGamePeriod);
			tp.Publish(itIsThePresent, now);

			game.MoveToTheNextPeriod(itIsThePresent, eventoElegido, now); // Empieza el juego >> in play 
			var inplayPeriod = game.CurrentPeriod;

			Assert.AreEqual(sc.RelevantsOn(preGamePeriod).Count(), 4);
			Assert.AreEqual(sc.RelevantsOn(inplayPeriod).Count(), 0); // inplay es current y NO trae las T1.

			game.MoveToTheNextPeriod(itIsThePresent, eventoElegido, now); // primer cuarto cuarto

			var primercuartoPeriod = game.CurrentPeriod;

			var questionYN = (YesNoQuestion)betBoard.Catalog.CreateTierTwoYesNoQuestion(5, "YN", "YES No Rewards TEST");
			questionYN.SetParameters(106, -110);
			var yn = (YesNoLine)matchDay[game][fila1].CreateLine(lineId + 4, questionYN, gradedBy, now);

			var questionOU = (OverUnderQuestion)betBoard.Catalog.CreateTierTwoOverUnderQuestion(6, "OU", "Over under rewards TEST");
			questionOU.SetParameters(10.5, 120, -115);
			var ou = (OverUnderLine)matchDay[game][fila1].CreateLine(lineId + 5, questionOU, gradedBy, now);

			var questionFixed = (FixedQuestion)betBoard.Catalog.CreateTierTwoFixedQuestion(7, "FQ", "Rojo gana.");
			questionFixed.SetParameters(new List<string> { "azul", "blanco", "rojo" }, new List<int> { 120, 115, -105 });
			var fixeds = (FixedLine)matchDay[game][fila1].CreateLine(lineId + 6, questionFixed, gradedBy, now);

			var questionYN2 = (YesNoQuestion)betBoard.Catalog.CreateTierTwoYesNoQuestion(8, "YN2", "YES No Rewards TEST2");
			questionYN2.SetParameters(120, -110);
			var yn2 = (YesNoLine)matchDay[game][fila1].CreateLine(lineId + 7, questionYN2, gradedBy, now);

			yn.AllowToPublishOn(primercuartoPeriod);
			yn.Publish(itIsThePresent, now);
			yn2.AllowToPublishOn(primercuartoPeriod);
			yn2.Publish(itIsThePresent, now);
			ou.AllowToPublishOn(primercuartoPeriod);
			ou.Publish(itIsThePresent, now);
			fixeds.AllowToPublishOn(primercuartoPeriod);
			Assert.AreEqual(sc.RelevantsOn(preGamePeriod).Count(), 4);// el periodo NO es current 
			Assert.AreEqual(sc.RelevantsOn(inplayPeriod).Count(), 0);// el periodo NO es current 
			Assert.AreEqual(sc.RelevantsOn(primercuartoPeriod).Count(), 3);// el periodo es current 
			Assert.AreEqual(game.CurrentPeriod, primercuartoPeriod);

			game.MoveToTheNextPeriod(itIsThePresent, eventoElegido, now);
			var segundoCuartoPeriod = game.CurrentPeriod;
			Assert.AreEqual(sc.RelevantsOn(segundoCuartoPeriod).Count(), 3);
			sc.Grade(itIsThePresent, yn2, true, now, gradedBy);
			Assert.AreEqual(sc.RelevantsOn(segundoCuartoPeriod).Count(), 3);
			Assert.AreEqual(sc.RelevantsOn(preGamePeriod).Count(), 4);

			game.MoveToTheNextPeriod(itIsThePresent, eventoElegido, now);
			var tercerCuartoPeriod = game.CurrentPeriod;
			Assert.AreEqual(sc.RelevantsOn(primercuartoPeriod).Count(), 3);
			Assert.AreEqual(sc.RelevantsOn(segundoCuartoPeriod).Count(), 1);
			Assert.AreEqual(sc.RelevantsOn(tercerCuartoPeriod).Count(), 2);
		}

		[TestMethod]
		public void PeriodHistory_Test()
		{
			DateTime now = new DateTime(2021, 01, 05, 11, 12, 13);
			Company company = new Company();
			company.Sales.CreateStore(1, "Test Store").MakeCurrent();
			company.Accounting = new MockAccounting();
			bool itIsThePresent = true;
			Tournaments tournaments = company.Tournaments;
			var nba = new NBATournament(tournaments);
			var game = nba.GetGameNumber(1);
			string periodosStr = "";
			foreach (var periodo in game.PeriodHistory())
			{
				periodosStr += periodo.Name;
			}
			Assert.AreEqual(periodosStr, Timeline.PRE_GAME);

			periodosStr = "";
			string eventoElegido = "Begin";
			game.MoveToTheNextPeriod(itIsThePresent, eventoElegido, now); // inPlay 
			game.MoveToTheNextPeriod(itIsThePresent, eventoElegido, now); // 1er cuarto
			game.MoveToTheNextPeriod(itIsThePresent, eventoElegido, now); // 2do cuarto
			var antes = game.CurrentPeriod;
			game.MoveToTheNextPeriod(itIsThePresent, eventoElegido, now); // 3er cuarto
			game.MoveToTheNextPeriod(itIsThePresent, eventoElegido, now); // 4to cuarto
			game.MoveToTheNextPeriod(itIsThePresent, eventoElegido, now); // overtime 1
			game.NewResult(itIsThePresent, now, game.TeamA, 1, game.TeamB, 2);
			game.MoveToTheNextPeriod(itIsThePresent, "End", now);

			var ultimo = game.CurrentPeriod;
			Assert.AreEqual((antes < ultimo), true);

			foreach (var periodo in game.PeriodHistory())
			{
				periodosStr += periodo.Name;
			}
			Assert.AreEqual(periodosStr, "PregameFirst quarterSecond quarterThird quarterFourth quarterOvertime 1Game Over");
		}

		internal class NBATournament : Tournament
		{
			private readonly int year;

			internal NBATournament(Tournaments tournaments) : base(tournaments, LinesTest.NBA, TournamentType.Knockout, 1, nameof(NBATournament))
			{
				this.year = 2020;
				var firstGames = new FirstGames();
				var game1 = base.GetNewGame(1);
				var game2 = base.GetNewGame(2);
				firstGames.Add(game1);
				firstGames.Add(game2);

				this.OpenRegistration();
				var teamId = tournaments.NextTeamId();
				var team1 = tournaments.CreateTeam(LinesTest.NBA, teamId++, "Houston");
				var team2 = tournaments.CreateTeam(LinesTest.NBA, teamId++, "Detroit");
				var team3 = tournaments.CreateTeam(LinesTest.NBA, teamId++, "Coastal Carolina");
				var team4 = tournaments.CreateTeam(LinesTest.NBA, teamId++, "Bryant");

				this.Register(team1);
				this.Register(team2);
				this.Register(team3);
				this.Register(team4);

				game1.AssignTeamA(team1);
				game1.AssignTeamB(team2);
				game2.AssignTeamA(team3);
				game2.AssignTeamB(team4);

				this.Start();
			}
		}

		[TestMethod]
		public void AllLines_Test()
		{
			bool itIsThePresent = true;
			DateTime now = new DateTime(2021, 01, 05, 11, 12, 13);
			string gradedBy = "Juan";

			Company company = new Company();
			CurrenciesTest.AddCurrencies();
			var domain = company.Sales.CreateDomain(false, 1, "localhost", PaymentChannels.Agents.INSIDER);
			var domain2 = company.Sales.CreateDomain(false, 2, "ncubo.com", PaymentChannels.Agents.INSIDER);
			company.Sales.CreateStore(1, "store prueba").MakeCurrent();
			company.Sales.CurrentStore.Add(domain2);
			company.Sales.CurrentStore.Add(domain);
			company.Accounting = new MockAccounting();
			var customer = company.GetOrCreateCustomerById("*********");
			customer.Name = "Tester User";
			customer.Identifier = "ID012345678901234567890123456789012346";
			var player = customer.Player;
			var sport = company.Tournaments.Sports.FindById(1);
			sport.CreateAndReplaceDefaultBackground("bg", "bg.jpg");
			sport.CreateAndReplaceDefaultSideBySide("sbs", "sbs.jpg");
			Tournaments tournaments = company.Tournaments;
			EuroTournament nba = new EuroTournament(tournaments);//TODO Tournament nos esta forzando a que los juegos de FirstGame tengan game y sucesor y esten definidos
			var gameNumber = 1;
			Game game = nba.GetGameNumber(gameNumber);
			game.SetFavorite(game.TeamA);

			var betBoard = company.Betboard(nba);

			LinesCatalog catalog = betBoard.Catalog;
			var spreadQuestion = catalog.CreateTierOneSpreadQuestion(1, "ss");
			var moneyQuestion = catalog.CreateTierOneMoneyQuestion(2, "mm");
			var totalPointsQuestion = catalog.CreateTierOneTotalPointsQuestion(3, "tt");
			var moneyDrawQuestion = catalog.CreateTierOneMoneyDrawQuestion(4, "dd");

			TournamentSchedule schedule = nba.Schedule;
			schedule.AddGameToMatchday(game, now.Date, game.TeamA, game.TeamA);

			Matchday matchDay = company.Betboard(nba).Matchday(now.Date);
			Showcase sc = matchDay[game];

			int teamAReward = 105;
			int teamBReward = -110;
			int tieReward = -110;
			var lineId = betBoard.NextLineId();
			var fila1 = 0;

			moneyQuestion.SetParameters(teamAReward, teamBReward);
			var ml = (MoneyLine)matchDay[game][fila1].CreateLine(lineId + 1, moneyQuestion, gradedBy, now);

			moneyDrawQuestion.SetParameters(teamAReward, tieReward, teamBReward);
			var mdl = (MoneyDrawLine)matchDay[game][fila1].CreateLine(lineId + 2, moneyDrawQuestion, gradedBy, now);

			spreadQuestion.SetParameters(3, teamAReward + 1, teamBReward + 1);
			var sl = (SpreadLine)matchDay[game][fila1].CreateLine(lineId + 3, spreadQuestion, gradedBy, now);

			totalPointsQuestion.SetParameters(2.5, 110, -105);
			var tp = (TotalPointsLine)matchDay[game][fila1].CreateLine(lineId + 4, totalPointsQuestion, gradedBy, now);

			var questionYN = betBoard.Catalog.CreateTierTwoYesNoQuestion(5, "YN", "YES No Rewards TEST");
			questionYN.SetParameters(106, -110);
			var yn = (YesNoLine)matchDay[game][fila1].CreateLine(lineId + 5, questionYN, gradedBy, now);

			var questionOU = betBoard.Catalog.CreateTierTwoOverUnderQuestion(6, "OU", "Over under rewards TEST");
			questionOU.SetParameters(10.5, 120, -115);
			var ou = (OverUnderLine)matchDay[game][fila1].CreateLine(lineId + 6, questionOU, gradedBy, now);

			var questionFixed = betBoard.Catalog.CreateTierTwoFixedQuestion(7, "FX", "Rojo gana.");
			questionFixed.SetParameters(new List<string> { "azul", "blanco", "rojo" }, new List<int> { 120, 115, -105 });
			var fixeds = (FixedLine)matchDay[game][fila1].CreateLine(lineId + 7, questionFixed, gradedBy, now);

			var questionYN2 = betBoard.Catalog.CreateTierTwoYesNoQuestion(8, "YN2", "YES No Rewards TEST2");
			questionYN2.SetParameters(120, -110);
			var yn2 = (YesNoLine)matchDay[game][fila1].CreateLine(lineId + 8, questionYN2, gradedBy, now);

			var questionYN3 = betBoard.Catalog.CreateTierTwoYesNoQuestion(9, "YN3", "YES No Rewards TEST3");
			questionYN3.SetParameters(115, -110);
			var yn3 = (YesNoLine)matchDay[game][fila1].CreateLine(lineId + 9, questionYN3, gradedBy, now);

			if (!ml.IsAllowedToPublishOn(game.CurrentPeriod)) ml.AllowToPublishOn(game.CurrentPeriod);
			ml.Publish(itIsThePresent, now);
			if (!mdl.IsAllowedToPublishOn(game.CurrentPeriod)) mdl.AllowToPublishOn(game.CurrentPeriod);
			mdl.Publish(itIsThePresent, now);
			if (!sl.IsAllowedToPublishOn(game.CurrentPeriod)) sl.AllowToPublishOn(game.CurrentPeriod);
			sl.Publish(itIsThePresent, now);
			if (!tp.IsAllowedToPublishOn(game.CurrentPeriod)) tp.AllowToPublishOn(game.CurrentPeriod);
			tp.Publish(itIsThePresent, now);
			if (!yn.IsAllowedToPublishOn(game.CurrentPeriod)) yn.AllowToPublishOn(game.CurrentPeriod);
			yn.Publish(itIsThePresent, now);
			if (!yn2.IsAllowedToPublishOn(game.CurrentPeriod)) yn2.AllowToPublishOn(game.CurrentPeriod);
			yn2.Publish(itIsThePresent, now);
			if (!ou.IsAllowedToPublishOn(game.CurrentPeriod)) ou.AllowToPublishOn(game.CurrentPeriod);
			ou.Publish(itIsThePresent, now);
			if (!fixeds.IsAllowedToPublishOn(game.CurrentPeriod)) fixeds.AllowToPublishOn(game.CurrentPeriod);

			matchDay[game].SuspendLine(itIsThePresent, lineId + 1, now);
			matchDay[game].ResumeLine(itIsThePresent, lineId + 1, now);


			Assert.AreEqual(sc.LinesOffer().ToArray().Length, 7);

			yn2.DisableDomain(domain2);

			Assert.AreEqual(sc.AvailableLines(domain, now).ToArray().Length, 7);// 7 player en dominio localhost
			Assert.AreEqual(sc.AvailableLines(domain2, now).ToArray().Length, 6);// 6 player en dominio ncubo.com

			var a1 = sc.AvailableLines(domain, now);// 7 player en dominio localhost
			var a2 = sc.AvailableLines(domain2, now);// 7 player en dominio ncubo.com

			var yn2v2 = yn2.NewVersion(130, -110, gradedBy, now.AddMinutes(1));
			var all = betBoard.AllLines(); // 8
			var allLinesOffer = sc.LinesOffer(); // 7 dashboard de bo 
			var unexpiredLiness = sc.Unexpired(); // 8 grade
			var expiredLiness = sc.Expired(game.CurrentPeriod); //  grade
			var allAvailableLines = sc.AvailableLines(domain, now); // 7 player

			Assert.AreEqual(all.ToArray().Length, 9);
			Assert.AreEqual(Enumerable.Count(allLinesOffer), 7);
			Assert.AreEqual(Enumerable.Count(allAvailableLines), 7);

			//matchDay[game].SuspendLine(lineId+1, now);
			//matchDay[game].ResumeLine(lineId+1, now);

			Assert.AreEqual(Enumerable.Count(sc.Unexpired()), 8);

			var linesGraded = sc.Expired(game.CurrentPeriod);
			Assert.AreEqual(Enumerable.Count(linesGraded), 0);

			var periodSelected = game.CurrentPeriod;
			sc.SetNoAction(itIsThePresent, yn2, now, gradedBy);
			sc.Grade(itIsThePresent, yn, true, now, gradedBy);
			sc.Grade(itIsThePresent, ou, 1, now, gradedBy);
			sc.Grade(itIsThePresent, fixeds, "rojo", now, gradedBy);

			// dar estos eventos.
			var eventos = game.CurrentPeriod.Events;
			string eventostr = "";
			foreach (var evento in eventos)
			{
				eventostr += evento.Name;
			}

			Assert.AreEqual(eventostr, "BeginCancel");

			// Controller para next de periodo
			var eventoElegido = "Begin";

			game.MoveToTheNextPeriod(itIsThePresent, eventoElegido, now);
			Assert.AreEqual(game.CurrentPeriod.Name, Timeline.IN_PLAY);

			var yn3_1 = yn3.NewVersion(121, -110, gradedBy, now.AddMinutes(2));
			Assert.AreEqual(Enumerable.Count(sc.AvailableLines(domain, now)), 0);
			Assert.AreEqual(Enumerable.Count(sc.Unexpired()), 4);
			yn3_1.AllowToPublishOn(game.CurrentPeriod);
			yn3_1.Publish(itIsThePresent, now);
			Assert.AreEqual(Enumerable.Count(sc.AvailableLines(domain, now)), 1);
			Assert.AreEqual(Enumerable.Count(sc.Unexpired()), 5);

			Assert.AreEqual(Enumerable.Count(sc.Expired(periodSelected)), 3);

			var str = "";
			foreach (var linne in sc.Expired(periodSelected))
			{
				str = linne.Text;
			}

			game.MoveToTheNextPeriod(itIsThePresent, eventoElegido, now);
			Assert.AreEqual(Enumerable.Count(sc.AvailableLines(domain, now)), 1);

		}

		[TestMethod]
		public void QuestionsInSports_Test()
		{
			bool itIsThePresent = true;
			DateTime now = new DateTime(2021, 01, 05, 11, 12, 13);
			string gradedBy = "Juan";

			Company company = new Company();
			CurrenciesTest.AddCurrencies();
			var domain = company.Sales.CreateDomain(false, 1, "localhost", PaymentChannels.Agents.INSIDER);
			company.Sales.CreateStore(1, "store prueba").MakeCurrent();
			company.Sales.CurrentStore.Add(domain);
			company.Accounting = new MockAccounting();
			var customer = company.GetOrCreateCustomerById("*********");
			customer.Name = "Tester User";
			customer.Identifier = "ID012345678901234567890123456789012346";
			var player = customer.Player;
			var sport = company.Tournaments.Sports.FindById(1);
			sport.CreateAndReplaceDefaultBackground("bg", "bg.jpg");
			sport.CreateAndReplaceDefaultSideBySide("sbs", "sbs.jpg");
			Tournaments tournaments = company.Tournaments;

			var leagueIdEuro = company.Tournaments.Leagues.NextLeagueId;
			var tournamentIdEuro = company.Tournaments.NextTournamentId;
			var leagueEuro = company.Tournaments.Leagues.Create(Sport.SOCCER, "Euro Copa", "euro", leagueIdEuro);
			var tournamentEuro = tournaments.CreateTournament(leagueEuro, tournamentIdEuro, "Euro Copa");

			var leagueIdCapaAmerica = company.Tournaments.Leagues.NextLeagueId;
			var tournamentIdCapaAmerica = company.Tournaments.NextTournamentId;
			var leagueCapaAmerica = company.Tournaments.Leagues.Create(Sport.SOCCER, "Copa America", "america", leagueIdCapaAmerica);
			var tournamentCapaAmerica = tournaments.CreateTournament(leagueCapaAmerica, tournamentIdCapaAmerica, "Copa America");

			tournamentEuro.OpenRegistration();
			var gameNumberEuro = tournamentEuro.NextGameNumber();
			var teamId = tournaments.NextTeamId();
			var team1 = tournaments.CreateTeam(leagueEuro, teamId, "Francia");
			teamId = tournaments.NextTeamId();
			var team2 = tournaments.CreateTeam(leagueEuro, teamId, "Alemania");
			var gameEuro = tournamentEuro.GetNewGame(gameNumberEuro, team1, team2, now);
			tournamentEuro.Register(team1);
			tournamentEuro.Register(team2);
			tournamentEuro.CloseRegistration();
			tournamentEuro.Start();

			tournamentCapaAmerica.OpenRegistration();
			var gameNumberCopaAmerica = tournamentCapaAmerica.NextGameNumber();
			teamId = tournaments.NextTeamId();
			var team3 = tournaments.CreateTeam(leagueCapaAmerica, teamId, "Argentina");
			teamId = tournaments.NextTeamId();
			var team4 = tournaments.CreateTeam(leagueCapaAmerica, teamId, "Brasil");
			var gameCopaAmerica = tournamentCapaAmerica.GetNewGame(gameNumberEuro, team3, team4, now);
			tournamentCapaAmerica.Register(team3);
			tournamentCapaAmerica.Register(team4);
			tournamentCapaAmerica.CloseRegistration();
			tournamentCapaAmerica.Start();

			var tournament = tournaments.FindById(1);
			var betBoard = company.Betboard(tournament);
			var catalog = betBoard.Catalog;
			var nextQuestionId = catalog.NextQuestionId();

			var spreadQuestion = catalog.CreateTierOneSpreadQuestion(nextQuestionId, "ss");
			var moneyQuestion = catalog.CreateTierOneMoneyQuestion(nextQuestionId + 1, "mm");
			var totalPointsQuestion = catalog.CreateTierOneTotalPointsQuestion(nextQuestionId + 2, "tt");
			var moneyDrawQuestion = catalog.CreateTierOneMoneyDrawQuestion(nextQuestionId + 3, "dd");

			// consulto los question para el torneo 1
			Assert.AreEqual(4, catalog.QuestionsMatchingWith("all").Count());

			tournament = tournaments.FindById(2);
			betBoard = company.Betboard(tournament);
			catalog = betBoard.Catalog;
			// consulto los question para el torneo 2
			Assert.AreEqual(4, catalog.QuestionsMatchingWith("all").Count());
		}

		[TestMethod]
		public void AllLines_Test_UsingDifferentTransitions()
		{
			bool itIsThePresent = true;
			DateTime now = new DateTime(2021, 01, 05, 11, 12, 13);
			string gradedBy = "Juan";

			Company company = new Company();
			CurrenciesTest.AddCurrencies();
			var domain = company.Sales.CreateDomain(false, 1, "localhost", PaymentChannels.Agents.INSIDER);
			company.Sales.CreateStore(1, "store prueba").MakeCurrent();
			company.Sales.CurrentStore.Add(domain);
			company.Accounting = new MockAccounting();
			var customer = company.GetOrCreateCustomerById("*********");
			customer.Name = "Tester User";
			customer.Identifier = "ID012345678901234567890123456789012346";
			var player = customer.Player;
			var sport = company.Tournaments.Sports.FindById(1);
			sport.CreateAndReplaceDefaultBackground("bg", "bg.jpg");
			sport.CreateAndReplaceDefaultSideBySide("sbs", "sbs.jpg");
			Tournaments tournaments = company.Tournaments;
			EuroTournament nba = new EuroTournament(tournaments);//TODO Tournament nos esta forzando a que los juegos de FirstGame tengan game y sucesor y esten definidos
			var gameNumber = 1;
			Game game = nba.GetGameNumber(gameNumber);
			game.SetFavorite(game.TeamA);

			var betBoard = company.Betboard(nba);

			LinesCatalog catalog = betBoard.Catalog;
			var spreadQuestion = catalog.CreateTierOneSpreadQuestion(1, "ss");
			var moneyQuestion = catalog.CreateTierOneMoneyQuestion(2, "mm");
			var totalPointsQuestion = catalog.CreateTierOneTotalPointsQuestion(3, "tt");
			var moneyDrawQuestion = catalog.CreateTierOneMoneyDrawQuestion(4, "dd");

			TournamentSchedule schedule = nba.Schedule;
			schedule.AddGameToMatchday(game, now.Date, game.TeamA, game.TeamA);

			Matchday matchDay = company.Betboard(nba).Matchday(now.Date);
			Showcase sc = matchDay[game];

			int teamAReward = 105;
			int teamBReward = -110;
			int tieReward = -110;
			var lineId = betBoard.NextLineId();
			var fila1 = 0;

			moneyQuestion.SetParameters(teamAReward, teamBReward);
			var ml = (MoneyLine)matchDay[game][fila1].CreateLine(lineId + 1, moneyQuestion, gradedBy, now);
			moneyDrawQuestion.SetParameters(teamAReward, tieReward, teamBReward);
			var mdl = (MoneyDrawLine)matchDay[game][fila1].CreateLine(lineId + 2, moneyDrawQuestion, gradedBy, now);
			spreadQuestion.SetParameters(3, teamAReward + 1, teamBReward + 1);
			var sl = (SpreadLine)matchDay[game][fila1].CreateLine(lineId + 3, spreadQuestion, gradedBy, now);
			totalPointsQuestion.SetParameters(2.5, 110, -105);
			var tp = (TotalPointsLine)matchDay[game][fila1].CreateLine(lineId + 4, totalPointsQuestion, gradedBy, now);

			var questionYN = betBoard.Catalog.CreateTierTwoYesNoQuestion(5, "YN", "YES No Rewards TEST");
			questionYN.SetParameters(106, -110);
			var yn = (YesNoLine)matchDay[game][fila1].CreateLine(lineId + 5, questionYN, gradedBy, now);

			var questionOU = betBoard.Catalog.CreateTierTwoOverUnderQuestion(6, "OU", "Over under rewards TEST");
			questionOU.SetParameters(10.5, 120, -115);
			var ou = (OverUnderLine)matchDay[game][fila1].CreateLine(lineId + 6, questionOU, gradedBy, now);

			var questionFixed = betBoard.Catalog.CreateTierTwoFixedQuestion(7, "FX", "Rojo gana.");
			questionFixed.SetParameters(new List<string> { "azul", "blanco", "rojo" }, new List<int> { 120, 115, -105 });
			var fixeds = (FixedLine)matchDay[game][fila1].CreateLine(lineId + 7, questionFixed, gradedBy, now);

			var questionYN2 = betBoard.Catalog.CreateTierTwoYesNoQuestion(8, "YN2", "YES No Rewards TEST2");
			questionYN2.SetParameters(120, -110);
			var yn2 = (YesNoLine)matchDay[game][fila1].CreateLine(lineId + 8, questionYN2, gradedBy, now);

			var questionYN3 = betBoard.Catalog.CreateTierTwoYesNoQuestion(9, "YN3", "YES No Rewards TEST3");
			questionYN3.SetParameters(115, -110);
			var yn3 = (YesNoLine)matchDay[game][fila1].CreateLine(lineId + 9, questionYN3, gradedBy, now);

			if (!ml.IsAllowedToPublishOn(game.CurrentPeriod)) ml.AllowToPublishOn(game.CurrentPeriod);
			ml.Publish(itIsThePresent, now);
			if (!mdl.IsAllowedToPublishOn(game.CurrentPeriod)) mdl.AllowToPublishOn(game.CurrentPeriod);
			mdl.Publish(itIsThePresent, now);
			if (!sl.IsAllowedToPublishOn(game.CurrentPeriod)) sl.AllowToPublishOn(game.CurrentPeriod);
			sl.Publish(itIsThePresent, now);
			if (!tp.IsAllowedToPublishOn(game.CurrentPeriod)) tp.AllowToPublishOn(game.CurrentPeriod);
			tp.Publish(itIsThePresent, now);
			if (!yn.IsAllowedToPublishOn(game.CurrentPeriod)) yn.AllowToPublishOn(game.CurrentPeriod);
			yn.Publish(itIsThePresent, now);
			if (!yn2.IsAllowedToPublishOn(game.CurrentPeriod)) yn2.AllowToPublishOn(game.CurrentPeriod);
			yn2.Publish(itIsThePresent, now);
			if (!ou.IsAllowedToPublishOn(game.CurrentPeriod)) ou.AllowToPublishOn(game.CurrentPeriod);
			ou.Publish(itIsThePresent, now);
			if (!fixeds.IsAllowedToPublishOn(game.CurrentPeriod)) fixeds.AllowToPublishOn(game.CurrentPeriod);

			sc.SetNoAction(itIsThePresent, yn2, now, gradedBy);
			sc.Regrade(itIsThePresent, yn2, now, gradedBy);
			sc.Grade(itIsThePresent, yn2, true, now, gradedBy);
			sc.Confirm(itIsThePresent, yn2, now, gradedBy);

			game.Start(itIsThePresent, now, now);
			game.NewResult(itIsThePresent, now, game.TeamA, 11, game.TeamB, 12);
			game.End(itIsThePresent, now);

			sc.SetNoAction(itIsThePresent, ml, now, gradedBy);
			sc.SetNoAction(itIsThePresent, mdl, now, gradedBy);
			sc.SetNoAction(itIsThePresent, sl, now, gradedBy);
			sc.SetNoAction(itIsThePresent, tp, now, gradedBy);

			sc.Regrade(itIsThePresent, ml, now, gradedBy);
			sc.Regrade(itIsThePresent, mdl, now, gradedBy);
			sc.Regrade(itIsThePresent, sl, now, gradedBy);
			sc.Regrade(itIsThePresent, tp, now, gradedBy);

			sc.Grade(itIsThePresent, ml, now, game.TeamA, 11, game.TeamB, 12, gradedBy);
			sc.Grade(itIsThePresent, mdl, now, game.TeamA, 11, game.TeamB, 12, gradedBy);
			sc.Grade(itIsThePresent, sl, now, game.TeamA, 11, game.TeamB, 12, gradedBy);
			sc.Grade(itIsThePresent, tp, 11 + 12, now, gradedBy);
			sc.Confirm(itIsThePresent, now, gradedBy);
		}

		[TestMethod]
		public void Bug5963()
		{
			//Integration.UseKafka = true;
			Integration.SavePreviousSettings(tempUseKafka: true, tempUseKafkaForAuto: true);
			QueueInMemory queue = new QueueInMemory();
			Integration.Kafka = queue;
			bool itIsThePresent = true;
			DateTime now = new DateTime(2020, 11, 02);

			Company company = new Company();
			CurrenciesTest.AddCurrencies();
			company.Accounting = new MockAccounting();
			var customer = company.GetOrCreateCustomerById("*********");
			var player = customer.Player;
			Tournaments tournaments = company.Tournaments;
			var sport = company.Tournaments.Sports.FindById(18);
			sport.CreateAndReplaceDefaultBackground("bg", "bg.jpg");
			sport.CreateAndReplaceDefaultSideBySide("sbs", "sbs.jpg");
			var store = company.Sales.CreateStore(1, "Test Store");
			store.MakeCurrent();
			var domain = company.Sales.CreateDomain(itIsThePresent, 1, "localhost", PaymentChannels.Agents.INSIDER);
			company.Sales.CurrentStore.Add(domain);

			Tournament torneo = new NBATournament(tournaments);//TODO Tournament nos esta forzando a que los juegos de FirstGame tengan game y sucesor y esten definidos
			var gameNumber = 1;
			Game game = torneo.GetGameNumber(gameNumber);
			game.Reschedule(now);

			var betBoard = company.Betboard(torneo);
			betBoard.AcceptFrom(torneo);
			var leagues = betBoard.Leagues(Sport.BASKETBALL);
			Assert.AreEqual(1, leagues.Count);
			foreach (var l in leagues)
			{
				Assert.AreEqual(torneo.League, l);
			}
			var matchDay = betBoard.Matchday(now);
			var showcase = matchDay.GetShowcase(game);

			game.SetFavorite(game.TeamA);

			LinesCatalog catalog = betBoard.Catalog;
			catalog.CreateTierOneSpreadQuestion(1, "ss");
			catalog.CreateTierOneMoneyQuestion(2, "mm");
			catalog.CreateTierOneTotalPointsQuestion(3, "tt");

			int questionId = catalog.NextQuestionId();
			SpreadQuestion spreadQuestion = (SpreadQuestion)catalog[questionId - 3];
			spreadQuestion.SetParameters(2, -120, 105);

			var shelve1 = showcase.InsertShelve(0);
			var lineId = betBoard.NextLineId();
			var spread1 = (SpreadLine)shelve1.CreateLine(lineId, spreadQuestion, "N/A", now);
			spread1.Publish(itIsThePresent, now);

			MoneyQuestion moneyQuestion = (MoneyQuestion)catalog[questionId - 2];
			moneyQuestion.SetParameters(-120, 105);
			lineId = betBoard.NextLineId();
			var money1 = (MoneyLine)shelve1.CreateLine(lineId, moneyQuestion, "N/A", now);
			money1.Publish(itIsThePresent, now);

			TotalPointsQuestion totalPointsQuestion = (TotalPointsQuestion)catalog[questionId - 1];
			totalPointsQuestion.SetParameters(200, -120, 105);
			lineId = betBoard.NextLineId();
			var totalPoints1 = (TotalPointsLine)shelve1.CreateLine(lineId, totalPointsQuestion, "N/A", now);
			totalPoints1.Publish(itIsThePresent, now);

			int orderNumber = 1;
			var betNumber = 1;
			var authorizationId = 1;
			var product = new Product(company, 1);
			product.Price = 1;
			var orderCart = company.NewOrder(customer, orderNumber++, Coinage.Coin(Currencies.CODES.USD), authorizationId);
			orderCart.AddSpreadLine(betNumber++, spread1, game.TeamA, product, 1m);
			company.PurchaseOrder(itIsThePresent, orderCart, domain, now);

			orderCart = company.NewOrder(customer, orderNumber++, Coinage.Coin(Currencies.CODES.USD), authorizationId);
			orderCart.AddMoneyLine(betNumber++, money1, game.TeamA, product, 1m);
			company.PurchaseOrder(itIsThePresent, orderCart, domain, now);

			orderCart = company.NewOrder(customer, orderNumber++, Coinage.Coin(Currencies.CODES.USD), authorizationId);
			orderCart.AddTotalPointsLine(betNumber++, totalPoints1, true, product, 1m);
			company.PurchaseOrder(itIsThePresent, orderCart, domain, now);

			lineId = betBoard.NextLineId();
			var spread2 = (SpreadLine)shelve1.CreateLine(lineId, spreadQuestion, "N/A", now);
			spread2.Publish(itIsThePresent, now);

			authorizationId = 2;
			product = new Product(company, 1);
			product.Price = 1;
			orderCart = company.NewOrder(customer, orderNumber++, Coinage.Coin(Currencies.CODES.USD), authorizationId);
			orderCart.AddSpreadLine(betNumber++, spread2, game.TeamA, product, 1m);
			company.PurchaseOrder(itIsThePresent, orderCart, domain, now);

			showcase.SetNoAction(itIsThePresent, spread2, now, "N/A");
			Assert.AreEqual(1, showcase.GradedLines().Count());

			var event1 = "Begin";
			var endGame = "End";
			game.MoveToTheNextPeriod(itIsThePresent, event1, now);
			Assert.AreEqual(game.CurrentPeriod.Name, Timeline.IN_PLAY);
			game.NewResult(itIsThePresent, now, game.TeamA, 1, game.TeamB, 2);

			game.NewResult(itIsThePresent, now, game.TeamA, 106, game.TeamB, 93);
			game.MoveToTheNextPeriod(itIsThePresent, endGame, now);
			showcase.Grade(itIsThePresent, spread1, now, game.TeamA, 106, game.TeamB, 93, "N/A");
			showcase.Grade(itIsThePresent, money1, now, game.TeamA, 106, game.TeamB, 93, "N/A");
			showcase.Grade(itIsThePresent, totalPoints1, now, game.TeamA, 106, game.TeamB, 93, "N/A");

			var countMessages = queue.Count(queue.TopicForLinesGrading);
			Assert.AreEqual(5, countMessages);
			string msg;
			var idsWinners = new int[] { spread1.LineId, money1.LineId };
			var idsLosers = new int[] { totalPoints1.LineId };
			for (int i = 0; i < countMessages; i++)
			{
				msg = queue.Dequeue(queue.TopicForLinesGrading).ToString();
				string[] messages = KafkaMessage.Split(msg);
				var type = (GradeLineType)Enum.Parse(typeof(GradeLineType), messages[0]);
				if (type == GradeLineType.GAME_LINES_INFO)
				{
					continue;
				}
				else if (type == GradeLineType.SCORES_PERIODS)
				{
					continue;
				}
				else
				{
					messages = KafkaMessages.Split(msg);
					foreach (string message in messages)
					{
						string[] lineMessages = KafkaMessage.Split(message);
						var lineType = (GradeLineType)Enum.Parse(typeof(GradeLineType), lineMessages[0]);
						switch (lineType)
						{
							case GradeLineType.WINNER_WAGER:
								LineWinnerInfo lineInfo = new LineWinnerInfo(message);
								if (lineInfo.IsStreamEnding() || lineInfo.IsStreamStarting()) continue;
								Assert.AreEqual(customer.AccountNumber, lineInfo.AccountNumber);
								Assert.AreEqual(1, lineInfo.Risk);
								Assert.AreEqual(0.83m, lineInfo.ToWin);
								Assert.IsTrue(idsWinners.Contains(lineInfo.LineId));
								break;
							case GradeLineType.LOSER_WAGER:
								LineLoserInfo loserInfo = new LineLoserInfo(message);
								if (loserInfo.IsStreamEnding() || loserInfo.IsStreamStarting()) continue;
								Assert.AreEqual(customer.AccountNumber, loserInfo.AccountNumber);
								Assert.AreEqual(1, loserInfo.Risk);
								Assert.AreEqual(0.83m, loserInfo.ToWin);
								Assert.IsTrue(idsLosers.Contains(loserInfo.LineId));
								break;
							case GradeLineType.NOACTION_WAGER:
								LineNoActionInfo noActionMsg = new LineNoActionInfo(message);
								break;
						}
					}
				}
			}

			Integration.RestoreToDefaultSettings();
		}

		[TestMethod]
		public void Bug5957_Grade_Regrade()
		{
			//Integration.UseKafka = true;
			QueueInMemory queue = new QueueInMemory();
			Integration.Kafka = queue;
			bool itIsThePresent = true;
			DateTime now = new DateTime(2020, 11, 02);

			Company company = new Company();
			CurrenciesTest.AddCurrencies();
			company.Sales.CreateStore(1, "Test Store").MakeCurrent();
			company.Accounting = new MockAccounting();
			var customer = company.GetOrCreateCustomerById("*********");
			var player = customer.Player;
			Tournaments tournaments = company.Tournaments;

			var domain = company.Sales.CreateDomain(itIsThePresent, 1, "localhost", PaymentChannels.Agents.INSIDER);
			company.Sales.CurrentStore.Add(domain);

			var leagueId = company.Tournaments.Leagues.NextLeagueId;
			var tournamentId = company.Tournaments.NextTournamentId;
			var sport = company.Tournaments.Sports.FindById(1);
			sport.CreateAndReplaceDefaultBackground("bg", "bg.jpg");
			sport.CreateAndReplaceDefaultSideBySide("sbs", "sbs.jpg");
			var league = company.Tournaments.Leagues.Create(sport, "soccer league", "SL", leagueId);
			var tournament = tournaments.CreateTournament(league, tournamentId, "soccer tournament");
			tournament.OpenRegistration();

			var gameNumber = tournament.NextGameNumber();
			var teamId = tournaments.NextTeamId();
			var team1 = tournaments.CreateTeam(league, teamId, "team1");
			teamId = tournaments.NextTeamId();
			var team2 = tournaments.CreateTeam(league, teamId, "team2");
			var game = tournament.GetNewGame(gameNumber, team1, team2, now);
			tournament.Register(team1);
			tournament.Register(team2);
			tournament.CloseRegistration();
			tournament.Start();

			var betBoard = company.Betboard(tournament);
			betBoard.AcceptFrom(tournament);
			var matchDay = betBoard.Matchday(now);
			var showcase = matchDay.GetShowcase(game);
			game.SetFavorite(game.TeamA);

			LinesCatalog catalog = betBoard.Catalog;
			int questionId = catalog.NextQuestionId();
			catalog.CreateTierOneSpreadQuestion(1, "ss");
			catalog.CreateTierOneMoneyQuestion(2, "mm");
			catalog.CreateTierOneTotalPointsQuestion(3, "tt");
			catalog.CreateTierOneMoneyDrawQuestion(4, "dd");

			SpreadQuestion spreadQuestion = (SpreadQuestion)catalog[questionId++];
			spreadQuestion.SetParameters(2, -120, 105);

			var shelve1 = showcase.InsertShelve(0);
			var lineId = betBoard.NextLineId();
			var spread1 = (SpreadLine)shelve1.CreateLine(lineId, spreadQuestion, "N/A", now);
			spread1.Publish(itIsThePresent, now);

			MoneyQuestion moneyQuestion = (MoneyQuestion)catalog[questionId++];
			moneyQuestion.SetParameters(-120, 105);
			lineId = betBoard.NextLineId();
			var money1 = (MoneyLine)shelve1.CreateLine(lineId, moneyQuestion, "N/A", now);
			money1.Publish(itIsThePresent, now);

			TotalPointsQuestion totalPointsQuestion = (TotalPointsQuestion)catalog[questionId++];
			totalPointsQuestion.SetParameters(200, -120, 105);
			lineId = betBoard.NextLineId();
			var totalPoints1 = (TotalPointsLine)shelve1.CreateLine(lineId, totalPointsQuestion, "N/A", now);
			totalPoints1.Publish(itIsThePresent, now);

			var moneyDrawQuestion = (MoneyDrawQuestion)catalog[questionId++];
			moneyDrawQuestion.SetParameters(-120, 114, 105);
			lineId = betBoard.NextLineId();
			var moneyDraw1 = (MoneyDrawLine)shelve1.CreateLine(lineId, moneyDrawQuestion, "N/A", now);
			moneyDraw1.Publish(itIsThePresent, now);

			int orderNumber = 1;
			var betNumber = 1;
			var authorizationId = 1;
			var product = new Product(company, 1);
			product.Price = 1;
			var orderCart = company.NewOrder(customer, orderNumber++, Coinage.Coin(Currencies.CODES.USD), authorizationId);
			orderCart.AddSpreadLine(betNumber++, spread1, game.TeamA, product, 1m);
			company.PurchaseOrder(itIsThePresent, orderCart, domain, now);

			orderCart = company.NewOrder(customer, orderNumber++, Coinage.Coin(Currencies.CODES.USD), authorizationId);
			orderCart.AddMoneyLine(betNumber++, money1, game.TeamA, product, 1m);
			company.PurchaseOrder(itIsThePresent, orderCart, domain, now);

			orderCart = company.NewOrder(customer, orderNumber++, Coinage.Coin(Currencies.CODES.USD), authorizationId);
			orderCart.AddTotalPointsLine(betNumber++, totalPoints1, true, product, 1m);
			company.PurchaseOrder(itIsThePresent, orderCart, domain, now);

			orderCart = company.NewOrder(customer, orderNumber++, Coinage.Coin(Currencies.CODES.USD), authorizationId);
			orderCart.AddMoneyDrawLine(betNumber++, moneyDraw1, game.TeamA, product, 1m);
			company.PurchaseOrder(itIsThePresent, orderCart, domain, now);

			var event1 = "Begin";
			var event2 = "End";
			game.MoveToTheNextPeriod(itIsThePresent, event1, now);
			Assert.AreEqual(game.CurrentPeriod.Name, Timeline.IN_PLAY);

			game.NewResult(itIsThePresent, now, game.TeamA, 106, game.TeamB, 93);
			game.MoveToTheNextPeriod(itIsThePresent, event2, now);
			showcase.Grade(itIsThePresent, spread1, now, game.TeamA, 106, game.TeamB, 93, "N/A");
			showcase.Grade(itIsThePresent, money1, now, game.TeamA, 106, game.TeamB, 93, "N/A");
			showcase.Grade(itIsThePresent, moneyDraw1, now, game.TeamA, 106, game.TeamB, 93, "N/A");
			showcase.Grade(itIsThePresent, totalPoints1, 106 + 93, now, "N/A");

			showcase.Regrade(itIsThePresent, spread1, now, "N/A");
			showcase.Regrade(itIsThePresent, money1, now, "N/A");
			showcase.Regrade(itIsThePresent, moneyDraw1, now, "N/A");
			showcase.Regrade(itIsThePresent, totalPoints1, now, "N/A");
			Integration.RestoreToDefaultSettings();
		}

		[TestMethod]
		public void Bug5957_Grade_NoAction()
		{
			//Integration.UseKafka = true;
			Integration.SavePreviousSettings(tempUseKafka: false, tempUseKafkaForAuto: false);
			QueueInMemory queue = new QueueInMemory();
			Integration.Kafka = queue;
			bool itIsThePresent = true;
			DateTime now = new DateTime(2020, 11, 02);

			Company company = new Company();
			CurrenciesTest.AddCurrencies();
			company.Sales.CreateStore(1, "Test Store").MakeCurrent();
			company.Accounting = new MockAccounting();
			var customer = company.GetOrCreateCustomerById("*********");
			var player = customer.Player;
			Tournaments tournaments = company.Tournaments;

			var domain = company.Sales.CreateDomain(itIsThePresent, 1, "localhost", PaymentChannels.Agents.INSIDER);
			company.Sales.CurrentStore.Add(domain);

			var leagueId = company.Tournaments.Leagues.NextLeagueId;
			var tournamentId = company.Tournaments.NextTournamentId;
			var sport = company.Tournaments.Sports.FindById(1);
			sport.CreateAndReplaceDefaultBackground("bg", "bg.jpg");
			sport.CreateAndReplaceDefaultSideBySide("sbs", "sbs.jpg");
			var league = company.Tournaments.Leagues.Create(sport, "soccer league", "SL", leagueId);
			var tournament = tournaments.CreateTournament(league, tournamentId, "soccer tournament");
			tournament.OpenRegistration();

			var gameNumber = tournament.NextGameNumber();
			var teamId = tournaments.NextTeamId();
			var team1 = tournaments.CreateTeam(league, teamId, "team1");
			teamId = tournaments.NextTeamId();
			var team2 = tournaments.CreateTeam(league, teamId, "team2");
			var game = tournament.GetNewGame(gameNumber, team1, team2, now);
			tournament.Register(team1);
			tournament.Register(team2);
			tournament.CloseRegistration();
			tournament.Start();

			var betBoard = company.Betboard(tournament);
			betBoard.AcceptFrom(tournament);
			var matchDay = betBoard.Matchday(now);
			var showcase = matchDay.GetShowcase(game);
			game.SetFavorite(game.TeamA);

			LinesCatalog catalog = betBoard.Catalog;
			int questionId = catalog.NextQuestionId();
			catalog.CreateTierOneSpreadQuestion(1, "ss");
			catalog.CreateTierOneMoneyQuestion(2, "mm");
			catalog.CreateTierOneTotalPointsQuestion(3, "tt");
			catalog.CreateTierOneMoneyDrawQuestion(4, "dd");

			SpreadQuestion spreadQuestion = (SpreadQuestion)catalog[questionId++];
			spreadQuestion.SetParameters(2, -120, 105);

			var shelve1 = showcase.InsertShelve(0);
			var lineId = betBoard.NextLineId();
			var spread1 = (SpreadLine)shelve1.CreateLine(lineId, spreadQuestion, "N/A", now);
			spread1.Publish(itIsThePresent, now);

			MoneyQuestion moneyQuestion = (MoneyQuestion)catalog[questionId++];
			moneyQuestion.SetParameters(-120, 105);
			lineId = betBoard.NextLineId();
			var money1 = (MoneyLine)shelve1.CreateLine(lineId, moneyQuestion, "N/A", now);
			money1.Publish(itIsThePresent, now);

			TotalPointsQuestion totalPointsQuestion = (TotalPointsQuestion)catalog[questionId++];
			totalPointsQuestion.SetParameters(200, -120, 105);
			lineId = betBoard.NextLineId();
			var totalPoints1 = (TotalPointsLine)shelve1.CreateLine(lineId, totalPointsQuestion, "N/A", now);
			totalPoints1.Publish(itIsThePresent, now);

			var moneyDrawQuestion = (MoneyDrawQuestion)catalog[questionId++];
			moneyDrawQuestion.SetParameters(-120, 114, 105);
			lineId = betBoard.NextLineId();
			var moneyDraw1 = (MoneyDrawLine)shelve1.CreateLine(lineId, moneyDrawQuestion, "N/A", now);
			moneyDraw1.Publish(itIsThePresent, now);

			int orderNumber = 1;
			var betNumber = 1;
			var authorizationId = 1;
			var product = new Product(company, 1);
			product.Price = 1;
			var orderCart = company.NewOrder(customer, orderNumber++, Coinage.Coin(Currencies.CODES.USD), authorizationId);
			orderCart.AddSpreadLine(betNumber++, spread1, game.TeamA, product, 1m);
			company.PurchaseOrder(itIsThePresent, orderCart, domain, now);

			orderCart = company.NewOrder(customer, orderNumber++, Coinage.Coin(Currencies.CODES.USD), authorizationId);
			orderCart.AddMoneyLine(betNumber++, money1, game.TeamA, product, 1m);
			company.PurchaseOrder(itIsThePresent, orderCart, domain, now);

			orderCart = company.NewOrder(customer, orderNumber++, Coinage.Coin(Currencies.CODES.USD), authorizationId);
			orderCart.AddTotalPointsLine(betNumber++, totalPoints1, true, product, 1m);
			company.PurchaseOrder(itIsThePresent, orderCart, domain, now);

			orderCart = company.NewOrder(customer, orderNumber++, Coinage.Coin(Currencies.CODES.USD), authorizationId);
			orderCart.AddMoneyDrawLine(betNumber++, moneyDraw1, game.TeamA, product, 1m);
			company.PurchaseOrder(itIsThePresent, orderCart, domain, now);

			var event1 = "Begin";
			var event2 = "End";
			game.MoveToTheNextPeriod(itIsThePresent, event1, now);
			Assert.AreEqual(game.CurrentPeriod.Name, Timeline.IN_PLAY);

			game.NewResult(itIsThePresent, now, game.TeamA, 106, game.TeamB, 93);
			game.MoveToTheNextPeriod(itIsThePresent, event2, now);
			showcase.Grade(itIsThePresent, spread1, now, game.TeamA, 106, game.TeamB, 93, "N/A");
			showcase.Grade(itIsThePresent, money1, now, game.TeamA, 106, game.TeamB, 93, "N/A");
			showcase.Grade(itIsThePresent, moneyDraw1, now, game.TeamA, 106, game.TeamB, 93, "N/A");
			showcase.Grade(itIsThePresent, totalPoints1, 106 + 93, now, "N/A");

			showcase.SetNoAction(itIsThePresent, spread1, now, "N/A");
			showcase.SetNoAction(itIsThePresent, money1, now, "N/A");
			showcase.SetNoAction(itIsThePresent, moneyDraw1, now, "N/A");
			showcase.SetNoAction(itIsThePresent, totalPoints1, now, "N/A");
		}

		[TestMethod]
		public void Bug5997()
		{
			Integration.UseKafka = false;
			QueueInMemory queue = new QueueInMemory();
			Integration.Kafka = queue;
			var itIsThePresent = false;

			Company company = new Company();
			CurrenciesTest.AddCurrencies();
			company.Sales.CreateStore(1, "Test Store").MakeCurrent();
			company.Accounting = new MockAccounting();
			Tournaments tournaments = company.Tournaments;
			var now = new DateTime(2020, 11, 02);

			var domain = company.Sales.CreateDomain(itIsThePresent, 1, "localhost", PaymentChannels.Agents.INSIDER);
			company.Sales.CurrentStore.Add(domain);

			var leagueId = tournaments.Leagues.NextLeagueId;
			var tournamentId = tournaments.NextTournamentId;
			var sport = company.Tournaments.Sports.FindById(1);
			sport.CreateAndReplaceDefaultBackground("bg", "bg.jpg");
			sport.CreateAndReplaceDefaultSideBySide("sbs", "sbs.jpg");
			var league = tournaments.Leagues.Create(sport, "soccer league", "SL", leagueId);
			var tournament = tournaments.CreateTournament(league, tournamentId, "soccer tournament");
			tournament.OpenRegistration();

			var betBoard = company.Betboard(tournament);
			betBoard.AcceptFrom(tournament);

			var team1 = tournaments.CreateTeam(league, 1, "team1");
			var team2 = tournaments.CreateTeam(league, 2, "team2");
			DateTime game1Date20 = new DateTime(2020, 11, 20, 16, 50, 0);
			var gameNumber = tournament.NextGameNumber();
			var game1 = tournament.GetNewGame(gameNumber, team1, team2);
			game1.Reschedule(game1Date20);
			game1.SetFavorite(game1.TeamA);

			tournament.Register(team1);
			tournament.Register(team2);
			tournament.CloseRegistration();
			tournament.Start();

			var matchDay = betBoard.Matchday(game1Date20.Date);
			var showcase = matchDay.GetShowcase(game1);
			var catalog = betBoard.Catalog;
			int questionId = catalog.NextQuestionId();
			catalog.CreateTierOneSpreadQuestion(1, "ss");

			SpreadQuestion spreadQuestion = (SpreadQuestion)catalog[questionId++];
			spreadQuestion.SetParameters(2, -120, 105);
			var shelve1 = showcase.InsertShelve(0);
			var lineId = betBoard.NextLineId();
			var spread1 = (SpreadLine)shelve1.CreateLine(lineId, spreadQuestion, "N/A", now);
			spread1.Publish(itIsThePresent, now);

			now = new DateTime(2020, 11, 3);
			var spread2 = shelve1.CreateNewVersionForSpreadLine(spread1.LineId, 120, -105, "N/A", now);

			int orderNumber = 1;
			var betNumber = 1;
			var authorizationId = 1;
			var product = new Product(company, 1);
			product.Price = 1;
			var customer = company.GetOrCreateCustomerById("*********");
			var orderCart = company.NewOrder(customer, orderNumber++, Coinage.Coin(Currencies.CODES.USD), authorizationId);
			orderCart.AddSpreadLine(betNumber++, spread2, game1.TeamA, product, 1m);
			company.PurchaseOrder(itIsThePresent, orderCart, domain, now);

			game1.Start(itIsThePresent, game1Date20, now);
			now = new DateTime(2020, 11, 21);
			game1.End(itIsThePresent, now);
			//showcase.Grade(itIsThePresent, now, game1.TeamA, 106, game1.TeamB, 93, "N/A");
		}

		private string sqlStringConection = "";//"persistsecurityinfo=True;port=3306;Server=localhost;Database=lines3;user id=*********;password=*********;SslMode=none";

		[TestMethod]
		public void WritingLinesDataInDB()
		{
			if (string.IsNullOrEmpty(sqlStringConection)) Assert.Inconclusive("sql string connection its required.");

			Integration.UseKafka = true;
			QueueInMemory queue = new QueueInMemory();
			Integration.Kafka = queue;
			bool itIsThePresent = true;

			Db bd = new Db();
			bd.MySQL = sqlStringConection;
			bd.DBSelected = DatabaseType.MySQL.ToString();
			Integration.Db = bd;

			DBDairy dbDairy = new DBDairy();
			dbDairy.DBSelected = DatabaseType.MySQL.ToString();
			Integration.DbDairy = dbDairy;

			Company company = new Company();
			company.Accounting = new MockAccounting();
			var customer = company.GetOrCreateCustomerById("NO*********");
			var customer2 = company.GetOrCreateCustomerById("NO562430370");
			Tournaments tournaments = company.Tournaments;

			var store = company.Sales.CreateStore(1, "Test Store");
			store.MakeCurrent();
			var domain = company.Sales.CreateDomain(itIsThePresent, 1, "localhost", PaymentChannels.Agents.INSIDER);
			company.Sales.CurrentStore.Add(domain);

			var leagueId = company.Tournaments.Leagues.NextLeagueId;
			var tournamentId = company.Tournaments.NextTournamentId;
			var sport = company.Tournaments.Sports.FindById(1);
			var league = company.Tournaments.Leagues.Create(sport, "soccer league", "SL", leagueId);
			var tournament = tournaments.CreateTournament(league, tournamentId, "soccer tournament");
			tournament.OpenRegistration();

			var gameDate = new DateTime(2020, 11, 20, 8, 0, 0);
			var games = new List<Game>();
			for (int i = 0; i < 23; i += 2)
			{
				var teamA = tournaments.CreateTeam(league, i + 1, $"team{i}");
				tournament.Register(teamA);
				var teamB = tournaments.CreateTeam(league, i + 2, $"team{i + 1}");
				tournament.Register(teamB);
				var gameNumber = tournament.NextGameNumber();
				gameDate = gameDate.AddHours(i);
				var game = tournament.GetNewGame(gameNumber, teamA, teamB, gameDate);
				game.SetFavorite(game.TeamA);
				games.Add(game);
			}

			tournament.CloseRegistration();
			tournament.Start();

			var betBoard = company.Betboard(tournament);
			betBoard.AcceptFrom(tournament);
			var catalog = betBoard.Catalog;

			int questionId = catalog.NextQuestionId();
			catalog.CreateTierOneSpreadQuestion(1, "ss");
			catalog.CreateTierOneMoneyQuestion(2, "mm");
			catalog.CreateTierOneMoneyDrawQuestion(3, "dd");
			catalog.CreateTierOneTotalPointsQuestion(4,"tp");

			var spreadQuestion = (SpreadQuestion)catalog[questionId++];
			spreadQuestion.SetParameters(20, -120, 105);
			var moneyQuestion = (MoneyQuestion)catalog[questionId++];
			moneyQuestion.SetParameters(120, -105);
			var moneyDrawQuestion = (MoneyDrawQuestion)catalog[questionId++];
			moneyDrawQuestion.SetParameters(-112, 114, 108);
			var totalPointsQuestion = (TotalPointsQuestion)catalog[questionId++];
			totalPointsQuestion.SetParameters(5.5d, -120, 105);
			var yesNoQuestion = catalog.CreateTierOneYesNoQuestion(questionId++, "p", "Will score the penalty?");
			yesNoQuestion.SetParameters(-102, 101);
			var questionOU = catalog.CreateTierOneOverUnderQuestion(questionId++, "OU", "Will score more than two goals?");
			questionOU.SetParameters(10.5d, 120, -115);
			var questionFixed = catalog.CreateTierOneFixedQuestion(questionId++, "FC", "Who will score the first goal?");
			questionFixed.SetParameters(new List<string> { "Bart", "Homer", "Marge" }, new List<int> { 120, 115, -105 });
			
			int orderNumber = 1;
			var betNumber = 1;
			var authorizationId = 1;
			var product = new Product(company, 1);
			product.Price = 1;
			var event1 = "Begin";
			var event2 = "End";

			var nowGameDate = gameDate;
			var now = new DateTime(2020, 11, 19, 23, 45, 0);
			var min = 0;
			foreach (var game in games)
			{
				var matchDay = betBoard.Matchday(game.ScheduledDate.Date);
				var showcase = matchDay.GetShowcase(game);
				var shelve = showcase.InsertShelve(0);

				var lineId = betBoard.NextLineId();
				var spread = (SpreadLine)shelve.CreateLine(lineId, spreadQuestion, "N/A", now);
				spread.Publish(itIsThePresent, now);
				lineId = betBoard.NextLineId();
				var money = (MoneyLine)shelve.CreateLine(lineId, moneyQuestion, "N/A", now);
				money.Publish(itIsThePresent, now);
				lineId = betBoard.NextLineId();
				var moneyDraw = (MoneyDrawLine)shelve.CreateLine(lineId, moneyDrawQuestion, "N/A", now);
				moneyDraw.Publish(itIsThePresent, now);
				lineId = betBoard.NextLineId();
				var totalPoints = (TotalPointsLine)shelve.CreateLine(lineId, totalPointsQuestion, "N/A", now);
				totalPoints.Publish(itIsThePresent, now);
				var overUnder = (OverUnderLine)shelve.CreateLine(betBoard.NextLineId(), questionOU, "N/A", now);
				overUnder.Publish(itIsThePresent, now);
				lineId = betBoard.NextLineId();
				var yesNo = (YesNoLine)shelve.CreateLine(lineId, yesNoQuestion, "N/A", now);
				yesNo.Publish(itIsThePresent, now);
				var fixeds = (FixedLine)shelve.CreateLine(betBoard.NextLineId(), questionFixed, "N/A", now);
				fixeds.Publish(itIsThePresent, now);

				now = now.AddMinutes(1);
				var orderCart = company.NewOrder(customer, orderNumber++, Coinage.Coin(Currencies.CODES.USD), authorizationId++);
				orderCart.AddSpreadLine(betNumber++, spread, game.TeamA, product, 1m);
				company.PurchaseOrder(itIsThePresent, orderCart, domain, now);

				now = now.AddMinutes(1);
				orderCart = company.NewOrder(customer, orderNumber++, Coinage.Coin(Currencies.CODES.USD), authorizationId++);
				orderCart.AddMoneyLine(betNumber++, money, game.TeamA, product, 1m);
				company.PurchaseOrder(itIsThePresent, orderCart, domain, now);

				now = now.AddMinutes(1);
				orderCart = company.NewOrder(customer, orderNumber++, Coinage.Coin(Currencies.CODES.USD), authorizationId++);
				orderCart.AddMoneyDrawLine(betNumber++, moneyDraw, game.TeamA, product, 1m);
				company.PurchaseOrder(itIsThePresent, orderCart, domain, now);

				now = now.AddMinutes(1);
				orderCart = company.NewOrder(customer, orderNumber++, Coinage.Coin(Currencies.CODES.USD), authorizationId++);
				orderCart.AddTotalPointsLine(betNumber++, totalPoints, true, product, 1m);
				company.PurchaseOrder(itIsThePresent, orderCart, domain, now);

				now = now.AddMinutes(1);
				orderCart = company.NewOrder(customer, orderNumber++, Coinage.Coin(Currencies.CODES.USD), authorizationId++);
				orderCart.AddOverUnderLine(betNumber++, overUnder, true, product, 1m);
				company.PurchaseOrder(itIsThePresent, orderCart, domain, now);

				now = now.AddMinutes(1);
				orderCart = company.NewOrder(customer, orderNumber++, Coinage.Coin(Currencies.CODES.USD), authorizationId++);
				orderCart.AddYesNoLine(betNumber++, yesNo, true, product, 1m);
				company.PurchaseOrder(itIsThePresent, orderCart, domain, now);

				now = now.AddMinutes(1);
				orderCart = company.NewOrder(customer, orderNumber++, Coinage.Coin(Currencies.CODES.USD), authorizationId++);
				orderCart.AddFixedLine(betNumber++, fixeds, "Bart", product, 1m);
				company.PurchaseOrder(itIsThePresent, orderCart, domain, now);

				now = now.AddMinutes(1);
				orderCart = company.NewOrder(customer2, orderNumber++, Coinage.Coin(Currencies.CODES.USD), authorizationId++);
				orderCart.AddSpreadLine(betNumber++, spread, game.TeamA, product, 1m);
				company.PurchaseOrder(itIsThePresent, orderCart, domain, now);

				now = now.AddMinutes(1);
				orderCart = company.NewOrder(customer2, orderNumber++, Coinage.Coin(Currencies.CODES.USD), authorizationId++);
				orderCart.AddMoneyLine(betNumber++, money, game.TeamA, product, 1m);
				company.PurchaseOrder(itIsThePresent, orderCart, domain, now);

				now = now.AddMinutes(1);
				orderCart = company.NewOrder(customer2, orderNumber++, Coinage.Coin(Currencies.CODES.USD), authorizationId++);
				orderCart.AddMoneyDrawLine(betNumber++, moneyDraw, game.TeamA, product, 1m);
				company.PurchaseOrder(itIsThePresent, orderCart, domain, now);

				now = now.AddMinutes(1);
				orderCart = company.NewOrder(customer2, orderNumber++, Coinage.Coin(Currencies.CODES.USD), authorizationId++);
				orderCart.AddTotalPointsLine(betNumber++, totalPoints, true, product, 1m);
				company.PurchaseOrder(itIsThePresent, orderCart, domain, now);

				now = now.AddMinutes(1);
				orderCart = company.NewOrder(customer2, orderNumber++, Coinage.Coin(Currencies.CODES.USD), authorizationId++);
				orderCart.AddOverUnderLine(betNumber++, overUnder, true, product, 1m);
				company.PurchaseOrder(itIsThePresent, orderCart, domain, now);

				now = now.AddMinutes(1);
				orderCart = company.NewOrder(customer2, orderNumber++, Coinage.Coin(Currencies.CODES.USD), authorizationId++);
				orderCart.AddYesNoLine(betNumber++, yesNo, true, product, 1m);
				company.PurchaseOrder(itIsThePresent, orderCart, domain, now);

				now = now.AddMinutes(1);
				orderCart = company.NewOrder(customer2, orderNumber++, Coinage.Coin(Currencies.CODES.USD), authorizationId++);
				orderCart.AddFixedLine(betNumber++, fixeds, "Bart", product, 1m);
				company.PurchaseOrder(itIsThePresent, orderCart, domain, now);

				var spread2 = shelve.CreateNewVersionForSpreadLine(spread.LineId, -115, 105, "N/A", now.AddMinutes(1));
				var spread3 = shelve.CreateNewVersionForSpreadLine(spread.LineId, -115, 110, "N/A", now.AddMinutes(2));

				nowGameDate = nowGameDate.AddMinutes(1);
				game.MoveToTheNextPeriod(itIsThePresent, event1, nowGameDate);
				game.NewResult(itIsThePresent, nowGameDate, game.TeamA, 103, game.TeamB, 97);
				game.MoveToTheNextPeriod(itIsThePresent, event2, nowGameDate);

				nowGameDate = nowGameDate.AddMinutes(1);
				showcase.Grade(itIsThePresent, spread3, nowGameDate, game.TeamA, 103, game.TeamB, 97, "N/A");
				showcase.Grade(itIsThePresent, money, nowGameDate, game.TeamA, 103, game.TeamB, 97, "N/A");
				showcase.Grade(itIsThePresent, moneyDraw, nowGameDate, game.TeamA, 103, game.TeamB, 97, "N/A");
				showcase.Grade(itIsThePresent, totalPoints, nowGameDate, game.TeamA, 103, game.TeamB, 97, "N/A");
				showcase.Grade(itIsThePresent, overUnder, nowGameDate, game.TeamA, 103, game.TeamB, 97, "N/A");
				showcase.Grade(itIsThePresent, yesNo, false, nowGameDate, "N/A");
				showcase.Grade(itIsThePresent, fixeds, "Bart", nowGameDate, "N/A");
				min++;
			}

			var countMessages = queue.Count(queue.TopicForLinesGrading);
			Assert.AreEqual(121, countMessages);//36 periods: 12 games, 3 periods changes
											   //30 remaining: 12 games, (24)1 game info and 1 lines info
											   //6 extras unknown
			DailyProfitReport report = new DailyProfitReport();
			string msg;
			for (int i = 0; i < countMessages; i++)
			{
				msg = queue.Dequeue(queue.TopicForLinesGrading).ToString();
				DeserializeMessages(report, msg);
			}
		}

		int tournamentId = 0;
		int gameNumber = 0;
		DateTime gameDate = new DateTime();
		private void DeserializeMessages(DailyProfitReport report, string completeMessage)
		{
			var receiver = new ReceiverOfHistorical();
			receiver.InitHistorical(HistoricalDatabaseType.MySQL, Integration.Db.MySQL);

			string[] messages = KafkaMessage.Split(completeMessage);
			var type = (GradeLineType)Enum.Parse(typeof(GradeLineType), messages[0]);
			if (type == GradeLineType.GAME_LINES_INFO)
			{
				LinesScoreMessageGame scoresMsg = new LinesScoreMessageGame(completeMessage);
				receiver.Receive(scoresMsg);

				tournamentId = scoresMsg.TournamentId;
				gameNumber = scoresMsg.GameNumber;
				gameDate = scoresMsg.StartGameDate.Date;
			}
			else if (type == GradeLineType.SCORES_PERIODS)
			{
				LinesScoreByPeriodsMessageGame scoresMsg = new LinesScoreByPeriodsMessageGame(completeMessage);
				receiver.Receive(scoresMsg);
			}
			else
			{
				messages = KafkaMessages.Split(completeMessage);
				foreach (string message in messages)
				{
					string[] lineMessages = KafkaMessage.Split(message);
					var lineType = (GradeLineType)Enum.Parse(typeof(GradeLineType), lineMessages[0]);
					switch (lineType)
					{
						case GradeLineType.WINNER_WAGER:
							LineWinnerInfo winnerMsg = new LineWinnerInfo(message);

							if (winnerMsg.IsStreamStarting())
							{
								string msg0 = "";
							}
							else if (winnerMsg.IsStreamEnding())
							{
								string msg1 = "";

							}
							else
							{
								receiver.Receive(winnerMsg);
							}
							break;
						case GradeLineType.LOSER_WAGER:
							LineLoserInfo loserMsg = new LineLoserInfo(message);
							if (loserMsg.IsStreamStarting())
							{
								string msg2 = "";
							}
							else if (loserMsg.IsStreamEnding())
							{
								string msg3 = "";
								report.Accumulate(gameDate, tournamentId, gameNumber);
							}
							else
							{
								receiver.Receive(loserMsg);
							}
							break;
						case GradeLineType.NOACTION_WAGER:
							LineNoActionInfo noActionMsg = new LineNoActionInfo(message);
							if (noActionMsg.IsStreamStarting())
							{
								string msg4 = "";
							}
							else if (noActionMsg.IsStreamEnding())
							{
								string msg5 = "";
							}
							else
							{
								receiver.Receive(noActionMsg);
							}
							break;
						default:
							string msg6 = "";
							break;
					}
				}
			}
		}

		[TestMethod]
		public void GameReminder_Test()
		{
			Company company = new Company();
			CurrenciesTest.AddCurrencies();
			GamesEngine.Games.Tournaments.Tournament t = new Super8MockGenerator(company).
				ConfigureASuper8Tournament(2018).
				ConfigureTheGamesAndPlayersToTheTournament().Tournament();
			t.Start();

			var customer = company.GetOrCreateCustomerById("*********");
			customer.Name = "Tester User";
			customer.Identifier = "ID012345678901234567890123456789012346";
			var player = customer.Player;

			Game game1 = t.GetGameNumber(1);
			Game game2 = t.GetGameNumber(2);
			Game game4 = t.GetGameNumber(4);
			Game game6 = t.GetGameNumber(6);
			Game game3 = t.GetGameNumber(3); // add 
			GamesSet gamesSet = new GamesSet();
			gamesSet.Add(game2);
			gamesSet.Add(game4);
			gamesSet.Add(game6);
			gamesSet.Add(game3);

			gamesSet.Remove(game4);
			gamesSet.Contains(game4);
			gamesSet.Remove(game6);
			gamesSet.Contains(game3);

			gamesSet.Add(game1);
			gamesSet.Remove(game6);
			player.Preferences.GamesReminder.Remind(game3);
			player.Preferences.GamesReminder.Remove(game3);
		}

		[TestMethod]
		public void FirsLinePublish_test()
		{
			bool itIsThePresent = true;
			DateTime now = new DateTime(2021, 01, 05, 11, 12, 13);
			string gradedBy = "Juan";

			Company company = new Company();
			CurrenciesTest.AddCurrencies();
			var domain = company.Sales.CreateDomain(false, 1, "localhost", PaymentChannels.Agents.INSIDER);
			company.Sales.CreateStore(1, "store prueba").MakeCurrent();
			company.Sales.CurrentStore.Add(domain);
			company.Accounting = new MockAccounting();
			var customer = company.GetOrCreateCustomerById("*********");
			customer.Name = "Tester User";
			customer.Identifier = "ID012345678901234567890123456789012346";
			var player = customer.Player;
			var sport = company.Tournaments.Sports.FindById(1);
			sport.CreateAndReplaceDefaultBackground("bg", "bg.jpg");
			sport.CreateAndReplaceDefaultSideBySide("sbs", "sbs.jpg");
			Tournaments tournaments = company.Tournaments;
			EuroTournament nba = new EuroTournament(tournaments);//TODO Tournament nos esta forzando a que los juegos de FirstGame tengan game y sucesor y esten definidos
			var gameNumber = 1;
			Game game = nba.GetGameNumber(gameNumber);
			game.SetFavorite(game.TeamA);

			var betBoard = company.Betboard(nba);

			LinesCatalog catalog = betBoard.Catalog;
			TournamentSchedule schedule = nba.Schedule;
			schedule.AddGameToMatchday(game, now.Date, game.TeamA, game.TeamA);

			Matchday matchDay = company.Betboard(nba).Matchday(now.Date);
			Showcase sc = matchDay[game];

			int teamAReward = 105;
			int teamBReward = -110;
			int tieReward = -110;
			var lineId = betBoard.NextLineId();
			var fila1 = 0;

			var questionML = (MoneyQuestion)betBoard.Catalog.CreateTierOneMoneyQuestion(1, "mm");
			questionML.SetParameters(teamAReward, teamBReward);
			var ml = (MoneyLine)matchDay[game][fila1].CreateLine(lineId, questionML, gradedBy, now);

			var questionMDL = (MoneyDrawQuestion)betBoard.Catalog.CreateTierOneMoneyDrawQuestion(2, "dd");
			questionMDL.SetParameters(teamAReward, tieReward, teamBReward);
			var mdl = (MoneyDrawLine)matchDay[game][fila1].CreateLine(lineId + 1, questionMDL, gradedBy, now);

			var questionYN = (YesNoQuestion)betBoard.Catalog.CreateTierTwoYesNoQuestion(3, "YN", "YES No Rewards TEST");
			questionYN.SetParameters(106, -110);
			var yn = (YesNoLine)matchDay[game][fila1].CreateLine(lineId + 4, questionYN, gradedBy, now);

			var questionYN2 = (YesNoQuestion)betBoard.Catalog.CreateTierTwoYesNoQuestion(4, "YN2", "YES No Rewards TEST2");
			questionYN2.SetParameters(120, -110);
			var yn2 = (YesNoLine)matchDay[game][fila1].CreateLine(lineId + 7, questionYN2, gradedBy, now);

			Assert.IsTrue(sc.IsFirstLineToBePublished());

			if (!ml.IsAllowedToPublishOn(game.CurrentPeriod)) ml.AllowToPublishOn(game.CurrentPeriod);
			ml.Publish(itIsThePresent, now);

			Assert.IsFalse(sc.IsFirstLineToBePublished());

			if (!mdl.IsAllowedToPublishOn(game.CurrentPeriod)) mdl.AllowToPublishOn(game.CurrentPeriod);
			mdl.Publish(itIsThePresent, now);
			Assert.IsFalse(sc.IsFirstLineToBePublished());

			if (!yn.IsAllowedToPublishOn(game.CurrentPeriod)) yn.AllowToPublishOn(game.CurrentPeriod);
			yn.Publish(itIsThePresent, now);
			Assert.IsFalse(sc.IsFirstLineToBePublished());

			if (!yn2.IsAllowedToPublishOn(game.CurrentPeriod)) yn2.AllowToPublishOn(game.CurrentPeriod);
			yn2.Publish(itIsThePresent, now);
			Assert.IsFalse(sc.IsFirstLineToBePublished());

		}

		[TestMethod]
		public void GamesTrend_Test()
		{
			bool itIsThePresent = true;
			DateTime now = new DateTime(2021, 01, 05, 11, 12, 13);
			string gradedBy = "Juan";

			Company company = new Company();
			Tournaments tournaments = company.Tournaments;
			CurrenciesTest.AddCurrencies();
			var customerWithoutPreference = company.GetOrCreateCustomerById("5D2430557");
			var sport = company.Tournaments.Sports.FindById(1);
			sport.CreateAndReplaceDefaultBackground("bg", "bg.jpg");
			sport.CreateAndReplaceDefaultSideBySide("sbs", "sbs.jpg");
			var customer = company.GetOrCreateCustomerById("5D2430383");
			customer.Name = "Tester User";
			customer.Identifier = "ID012345678901234567890123456789012346";
			var player = customer.Player;

			GamesEngine.Games.Tournaments.Tournament super8Soccer = new Super8MockGenerator(company).
				ConfigureASuper8Tournament(2018).
				ConfigureTheGamesAndPlayersToTheTournament().Tournament();
			super8Soccer.Start();

			var leagueId = company.Tournaments.Leagues.NextLeagueId;
			var leagueNBA = company.Tournaments.Leagues.Create(Sport.BASKETBALL, "NBA", "NBA", leagueId);
			var domain = company.Sales.DomainFrom("localhost");

			var NBAT = tournaments.CreateTournament(leagueNBA, company.Tournaments.NextTournamentId, "soccer tournament");

			NBAT.OpenRegistration();

			var teamId = tournaments.NextTeamId();
			var teamA = tournaments.CreateTeam(leagueNBA, teamId, "team A");
			NBAT.Register(teamA);
			teamId = tournaments.NextTeamId();
			var teamB = tournaments.CreateTeam(leagueNBA, teamId, "team B");
			NBAT.Register(teamB);
			var gameNumber = NBAT.NextGameNumber();
			var game1NBA = NBAT.GetNewGame(gameNumber, teamA, teamB, now);
			NBAT.CloseRegistration();
			NBAT.Start();

			var betBoard = company.Betboard(super8Soccer);
			betBoard.AcceptFrom(super8Soccer);

			var betBoardNBA = company.Betboard(NBAT);
			betBoardNBA.AcceptFrom(NBAT);

			Game game1 = super8Soccer.GetGameNumber(1);
			Game game2 = super8Soccer.GetGameNumber(2);
			Game game3 = super8Soccer.GetGameNumber(3);
			Game game4 = super8Soccer.GetGameNumber(4);
			Game game5 = super8Soccer.GetGameNumber(5);
			Game game6 = super8Soccer.GetGameNumber(6);
			Game game7 = super8Soccer.GetGameNumber(7);

			game1.SetFavorite(game1.TeamA);
			game2.SetFavorite(game2.TeamB);
			game3.SetFavorite(game3.TeamA);
			game4.SetFavorite(game4.TeamB);

			game1.Reschedule(now.AddHours(1));
			game2.Reschedule(now.AddHours(3));
			game3.Reschedule(now.AddHours(28));
			game4.Reschedule(now.AddHours(25));

			game1NBA.SetFavorite(teamA);
			game1NBA.Reschedule(now.AddHours(1));


			Assert.AreEqual(player.Preferences.GamesReminder.FirstMatchInPreGame(now.AddDays(-1).Date).Count(), 0);
			Assert.AreEqual(player.Preferences.GamesReminder.FirstMatchInPlay(now.AddDays(-1).Date).Count(), 0);
			Assert.AreEqual(player.Preferences.GamesReminder.FirstMatchInPreGame(now.Date).Count(), 0);
			Assert.AreEqual(player.Preferences.GamesReminder.FirstMatchInPlay(now.Date).Count(), 0);

			player.Preferences.GamesReminder.Remind(game1);
			player.Preferences.GamesReminder.Remind(game2);
			player.Preferences.GamesReminder.Remind(game1NBA);

			player.Preferences.Sports.Mark(Sport.SOCCER);
			player.Preferences.Sports.Mark(Sport.FOOTBALL);
			player.Preferences.Sports.Mark(Sport.BASEBALL);

			Assert.AreEqual(player.Preferences.GamesReminder.FirstMatchInPreGame(now.AddDays(1).Date).Count(), 0);
			Assert.AreEqual(player.Preferences.GamesReminder.FirstMatchInPlay(now.AddDays(1).Date).Count(), 0);
			Assert.AreEqual(player.Preferences.GamesReminder.FirstMatchInPreGame(now.Date).Count(), 1);
			Assert.AreEqual(player.Preferences.GamesReminder.FirstMatchInPlay(now.Date).Count(), 0);

			Assert.IsTrue(player.Preferences.GamesReminder.Contains(game1));
			Assert.IsFalse(customerWithoutPreference.Player.Preferences.GamesReminder.Contains(game1));

			Assert.AreEqual(player.Preferences.GamesReminder.FirstMatchInPreGame(now.Date).Count(), 1);
			Assert.AreEqual(player.Preferences.GamesReminder.FirstMatchInPlay(now.Date).Count(), 0);

			string result = "";
			foreach (var g in player.Preferences.GamesReminder.NextTo(now.AddDays(1).Date)) // coming 
			{
				result = result + g.Number + g.Tournament.Id;
			}
			Assert.AreEqual(result, "112112");

			player.Preferences.GamesReminder.Remove(game1NBA);

			// Caso 1 y 2 de tendencia, match mas apostados que estan jugandose en ese momento el live del deporte que siguo.
			Matchday matchDay = company.Betboard(super8Soccer).Matchday(now.Date);
			Matchday matchDayNBA = company.Betboard(NBAT).Matchday(now.Date);

			int teamAReward = 105, teamBReward = -110;
			var fila1 = 0;
			var questionSL = betBoard.Catalog.CreateTierOneSpreadQuestion(tournaments.NextQuestionId(), "ss");
			var questionTP = betBoard.Catalog.CreateTierOneTotalPointsQuestion(tournaments.NextQuestionId(), "tt");
			var questionSLNBA = betBoardNBA.Catalog.CreateTierOneSpreadQuestion(tournaments.NextQuestionId(), "ss");


			questionSL.SetParameters(3, teamAReward + 1, teamBReward + 1);
			questionSLNBA.SetParameters(3, teamAReward + 2, teamBReward + 3);
			var sl = (SpreadLine)matchDay[game1][fila1].CreateLine(betBoard.NextLineId(), questionSL, gradedBy, now);
			var sl2 = (SpreadLine)matchDay[game2][fila1].CreateLine(betBoard.NextLineId(), questionSL, gradedBy, now.AddSeconds(2));
			var sl3 = (SpreadLine)matchDayNBA[game1NBA][fila1].CreateLine(betBoard.NextLineId(), questionSLNBA, gradedBy, now.AddSeconds(3));

			questionTP.SetParameters(2.5, 110, -105);
			var tp = (TotalPointsLine)matchDay[game1][fila1].CreateLine(betBoard.NextLineId(), questionTP, gradedBy, now);

			// props 
			var questionYN = betBoard.Catalog.CreateTierTwoYesNoQuestion(tournaments.NextQuestionId(), "YN", "YES No Rewards TEST");
			var questionYNNBA = betBoardNBA.Catalog.CreateTierTwoYesNoQuestion(tournaments.NextQuestionId(), "YN", "YES No Rewards TEST-NBA");
			questionYN.SetParameters(106, -110);
			questionYNNBA.SetParameters(110, -115);
			var yn = (YesNoLine)matchDay[game1][fila1].CreateLine(betBoard.NextLineId(), questionYN, gradedBy, now);
			var yn2 = (YesNoLine)matchDay[game2][fila1].CreateLine(betBoard.NextLineId(), questionYN, gradedBy, now.AddSeconds(2));
			var yn3 = (YesNoLine)matchDayNBA[game1NBA][fila1].CreateLine(betBoard.NextLineId(), questionYNNBA, gradedBy, now.AddSeconds(3));

			var questionOU = betBoard.Catalog.CreateTierTwoOverUnderQuestion(tournaments.NextQuestionId(), "OU", "Over under rewards TEST");
			questionOU.SetParameters(10.5, 120, -115);
			var ou = (OverUnderLine)matchDay[game1][fila1].CreateLine(betBoard.NextLineId(), questionOU, gradedBy, now);

			var questionFixed = betBoard.Catalog.CreateTierTwoFixedQuestion(tournaments.NextQuestionId(), "FC", "Quien va a ganar el partido?");
			questionFixed.SetParameters(new List<string> { "azul", "blanco", "rojo" }, new List<int> { 120, 115, -105 });
			var fixeds = (FixedLine)matchDay[game1][fila1].CreateLine(betBoard.NextLineId(), questionFixed, gradedBy, now);

			//game1 torneo de soccer
			sl.Publish(itIsThePresent, now);
			tp.Publish(itIsThePresent, now);
			yn.AllowToPublishOn(game1.CurrentPeriod);
			yn.Publish(itIsThePresent, now);
			ou.AllowToPublishOn(game1.CurrentPeriod);
			ou.Publish(itIsThePresent, now);
			fixeds.AllowToPublishOn(game1.CurrentPeriod);
			fixeds.Publish(itIsThePresent, now);

			//game2 torneo de soccer
			sl2.Publish(itIsThePresent, now);
			yn2.AllowToPublishOn(game2.CurrentPeriod);
			yn2.Publish(itIsThePresent, now);

			//game1 torneo nba
			sl3.Publish(itIsThePresent, now);
			yn3.AllowToPublishOn(game1NBA.CurrentPeriod);
			yn3.Publish(itIsThePresent, now);

			// compras props
			WagerAnswer answeryn = YesNoAnswer.YES;
			yn.PlaceBet(1, player, now, answeryn, 0.33m);
			yn2.PlaceBet(2, player, now, answeryn, 0.34m);
			yn3.PlaceBet(3, player, now, answeryn, 0.35m);

			WagerAnswer answerou = OverUnderAnswer.OVER;
			ou.PlaceBet(2, player, now, answerou, 0.77m);
			WagerAnswer answerf = fixeds.GetAnAnswer("rojo");
			fixeds.PlaceBet(3, player, now, answerf, 1.33m);

			// compras tierOne
			sl.PlaceBet(6, player, now, sl.GetTeamAnswer(game1.TeamA), 3.77m);
			WagerAnswer wagertp = OverUnderAnswer.UNDER;
			tp.PlaceBet(7, player, now, wagertp, 4.33m);
			game1.Start(itIsThePresent, now, now);
			game1NBA.Start(itIsThePresent, now, now);
			game2.Start(itIsThePresent, now, now);

			var misSportFav = "";
			result = "";
			foreach (var mysport in player.Preferences.Sports.Favorites())
			{
				misSportFav = misSportFav + mysport.Name;
				foreach (var gamesInPlay in super8Soccer.Tournaments.GamesInPlayTo(mysport, domain, now.Date))
				{
					result = result + gamesInPlay.Number + gamesInPlay.Tournament.Id;
					var showcaseGame = matchDay[gamesInPlay];
					Assert.IsTrue(showcaseGame.RiskAssestment.NumberOfBet > 0);
				}
			}
			Assert.AreEqual(result, "1121");
			Assert.AreEqual(misSportFav, "SoccerFootballBaseball");

			// caso row 3
			result = "";
			foreach (var mysport in player.Preferences.Sports.Favorites())
			{
				foreach (var gameInPlay in super8Soccer.Tournaments.FirstGameToPlayInPregame(mysport, now.Date))
				{
					result = result + gameInPlay.Number + gameInPlay.Tournament.Id;
				}
			}
			Assert.AreEqual(result, "");

			player.Preferences.Sports.Mark(Sport.SOCCER);
			result = "";

			foreach (var mysport in player.Preferences.Sports.Favorites())
			{
				foreach (var gamesInPlay in super8Soccer.Tournaments.FirstGameToPlayInPregame(mysport, now.AddDays(1).Date))
				{
					result = result + mysport.Name;
				}
			}
			Assert.AreEqual(result, "Soccer");

			// caso row 6
			Assert.AreEqual(player.Preferences.GamesReminder.FirstMatchInPreGame(now.Date).Count(), 0); // debe regresar el mas proximo o el primero en la lista de agregados?

			// caso 7 

		}

		[TestMethod]
		public void Games_NewResult_Test()
		{
			string connection = "persistsecurityinfo=True;port=3306;Server=localhost;Database=lines2;user id=*********;password=*********;SslMode=none";
			GamesEngine.Settings.Integration.ConfigurationForAutoTest(connection);

			ReceiverOfHistorical receiver = new ReceiverOfHistorical();
			//receiver.InitHistorical(HistoricalDatabaseType.MySQL, connection);
			receiver.InitHistorical(HistoricalDatabaseType.InMemory, connection);
			new LinesBIAPI.Consumers().CreateConsumerForTopics(receiver);

			bool itIsThePresent = true;
			DateTime now = new DateTime(2021, 01, 05, 11, 12, 13);
			string gradedBy = "Juan";

			Company company = new Company();
			CurrenciesTest.AddCurrencies();
			var customerWithoutPreference = company.GetOrCreateCustomerById("5D2430557");
			var sport = company.Tournaments.Sports.FindById(1);
			sport.CreateAndReplaceDefaultBackground("bg", "bg.jpg");
			sport.CreateAndReplaceDefaultSideBySide("sbs", "sbs.jpg");
			var customer = company.GetOrCreateCustomerById("5D2430383");
			customer.Name = "Tester User";
			customer.Identifier = "ID012345678901234567890123456789012346";
			var player = customer.Player;

			GamesEngine.Games.Tournaments.Tournament t = new Super8MockGenerator(company).
				ConfigureASuper8Tournament(2018).
				ConfigureTheGamesAndPlayersToTheTournament().Tournament();
			t.Start();

			Game game = t.GetGameNumber(1);
			Team teama = game.TeamA;
			Team teamb = game.TeamB;
			game.SetFavorite(game.TeamA);

			player.Preferences.GamesReminder.Remind(game);
			Assert.IsTrue(player.Preferences.GamesReminder.Contains(game));
			Assert.IsFalse(customerWithoutPreference.Player.Preferences.GamesReminder.Contains(game));

			var betBoard = company.Betboard(t);
			betBoard.AcceptFrom(t);

			TournamentSchedule schedule = t.Schedule;
			schedule.AddGameToMatchday(game, now.Date, game.TeamA, game.TeamA);

			Matchday matchDay = company.Betboard(t).Matchday(now.Date);

			var event1 = "Begin";
			var endGame = "End";

			game.MoveToTheNextPeriod(itIsThePresent, event1, now);
			Assert.AreEqual(game.CurrentPeriod.Name, Timeline.IN_PLAY);
			game.NewResult(itIsThePresent, now, game.TeamA, 0, game.TeamB, 0);

			game.MoveToTheNextPeriod(itIsThePresent, event1, now.AddSeconds(100));
			Assert.AreEqual(game.CurrentPeriod.Name, "First half");
			game.NewResult(itIsThePresent, now, game.TeamA, 1, game.TeamB, 2);

			game.MoveToTheNextPeriod(itIsThePresent, event1, now.AddSeconds(200));
			Assert.AreEqual(game.CurrentPeriod.Name, "Second half");
			game.NewResult(itIsThePresent, now, game.TeamA, 2, game.TeamB, 2);

			game.MoveToTheNextPeriod(itIsThePresent, event1, now.AddSeconds(300));
			Assert.AreEqual(game.CurrentPeriod.Name, "First half extra");
			game.NewResult(itIsThePresent, now, game.TeamA, 2, game.TeamB, 3);

			game.MoveToTheNextPeriod(itIsThePresent, event1, now.AddSeconds(400));
			Assert.AreEqual(game.CurrentPeriod.Name, "Second half extra");
			game.NewResult(itIsThePresent, now, game.TeamA, 2, game.TeamB, 4);

			game.MoveToTheNextPeriod(itIsThePresent, endGame, now);
		}

		[TestMethod]
		public void AvailabilityTimeNextPeriodGame_Test()
		{
			Company company = new Company();
			CurrenciesTest.AddCurrencies();
			var domain = company.Sales.CreateDomain(false, 1, "localhost", PaymentChannels.Agents.INSIDER);
			company.Sales.CreateStore(1, "Store Test").MakeCurrent();
			company.Sales.CurrentStore.Add(domain);
			company.Accounting = new MockAccounting();
			var sport = company.Tournaments.Sports.FindById(18);
			sport.CreateAndReplaceDefaultBackground("bg", "bg.jpg");
			sport.CreateAndReplaceDefaultSideBySide("sbs", "sbs.jpg");
			bool itIsThePresent = true;
			Integration.UseKafka = false;
			Integration.UseKafkaForAuto = false;
			Integration.ConfigurationForAutoTest("");

			Customer customer = company.GetOrCreateCustomerById("*********");
			Player player = customer.Player;
			var noww = DateTime.Now;
			var now1 = noww.AddMinutes(-30);
			var now2 = noww.AddSeconds(10);
			//-------------------
			Tournaments tournaments = company.Tournaments;
			Tournament nba = new NBATournament(tournaments);
			var gameNumber = 1;
			Game game = nba.GetGameNumber(gameNumber);

			// TODO: Erick, 
			game.Reschedule(now1); // con este now, el TC pasa (que es el caso reportado en el bug) 
			//game.Reschedule(noww); // con este now (el cual parece mas real) el bug no se repro (obviamente el tc se cae xq esta quemado el time en 0 )

			var betBoard = company.Betboard(nba);
			betBoard.AcceptFrom(nba);
			var leagues = betBoard.Leagues(Sport.BASKETBALL);

			var questionSL = betBoard.Catalog.CreateTierOneSpreadQuestion(1, "ss");
			Assert.AreEqual("None", questionSL.TimeKillerMomentAsString);

			var questionYN = betBoard.Catalog.CreateTierTwoYesNoQuestion(2, "m10", "Messi marcara?");
			questionYN.UnlimitedTime();
			Assert.AreEqual("None", questionYN.TimeKillerMomentAsString);

			var questionYN2 = betBoard.Catalog.CreateTierTwoYesNoQuestion(3, "m10red", "Messi sera expulsado?");
			questionYN2.SetParameters(-110, 110);
			questionYN2.TimeKiller(AvailabilityTime.Milestone.AfterGameStarts, 100);
			questionYN2.AllowPublished(game.CurrentPeriod);

			var questionYN3 = betBoard.Catalog.CreateTierTwoYesNoQuestion(4, "g11", "gol?");
			questionYN3.SetParameters(-115, 110);
			questionYN3.TimeKiller(AvailabilityTime.Milestone.AfterLinePublishing, 100);
			questionYN3.AllowPublished(game.CurrentPeriod);
			Assert.AreEqual("AfterGameStarts", questionYN2.TimeKillerMomentAsString);

			var questionYN4 = betBoard.Catalog.CreateTierTwoYesNoQuestion(5, "g14", "gol?");
			questionYN4.SetParameters(-115, 110);
			questionYN4.AllowPublished(game.CurrentPeriod);

			var diaDeJuegos = betBoard.Matchday(new DateTime(now1.Year, now1.Month, now1.Day));
			Showcase sc = diaDeJuegos[game];

			game.SetFavorite(game.TeamA);
			game = nba.GetGameNumber(gameNumber);
			var fila1 = 0;
			var lineId = betBoard.NextLineId();

			questionSL.SetParameters(2.0, -120, 105);
			var spreadVersion1 = (SpreadLine)diaDeJuegos[game][fila1].CreateLine(lineId, questionSL, "N/A", now1);
			spreadVersion1.Publish(itIsThePresent, now1);

			lineId = betBoard.NextLineId();
			var yesNoVersion1 = (YesNoLine)diaDeJuegos[game][fila1].CreateLine(lineId, questionYN2, "N/A", now1);
			yesNoVersion1.Publish(itIsThePresent, now1);

			var yesNoVersion2 = (YesNoLine)diaDeJuegos[game][fila1].CreateLine(betBoard.NextLineId(), questionYN3, "N/A", now1);
			yesNoVersion2.Publish(itIsThePresent, now1);

			lineId = betBoard.NextLineId();
			var yesNo2 = (YesNoLine)diaDeJuegos[game][fila1].CreateLine(lineId, questionYN3, "N/A", now1);
			yesNo2.Publish(itIsThePresent, now1);
			Assert.AreEqual("AfterLinePublishing", yesNo2.DueDate.MilestoneAsString());

			lineId = betBoard.NextLineId();
			var yesNo3 = (YesNoLine)diaDeJuegos[game][fila1].CreateLine(lineId, questionYN4, "N/A", now1);
			yesNo3.Publish(itIsThePresent, now1);
			Assert.AreEqual("AfterLinePublishing", yesNo2.DueDate.MilestoneAsString());


			var line1 = betBoard.GetLine(1);
			var line2 = betBoard.GetLine(2);

			Assert.AreEqual(line1.BasedOn.Shortcut, "ss");
			Assert.AreEqual(line2.BasedOn.Shortcut, "m10red");

			Assert.AreEqual("Pregame", game.CurrentPeriod.Name);

			Assert.AreEqual("Limits", line1.DueDate.MilestoneAsString());
			Assert.AreEqual(-1, line1.RemainingAvailabilityTime(now1));
			Assert.IsTrue(line1.DueDate.IsUnlimited);

			Assert.AreEqual("AfterGameStarts", line2.DueDate.MilestoneAsString());
			Assert.AreEqual(100, line2.DueDate.TimeInSeconds);
			Assert.IsTrue(line2.RemainingAvailabilityTime(now2) != 0);

			foreach (var liness in sc.AllLinesOffered())
			{
				if (liness.DueDate.MilestoneAsString() == "AfterGameStarts")
				{
					Assert.AreEqual(100, liness.DueDate.TimeInSeconds);
					Assert.IsTrue(liness.RemainingAvailabilityTime(now2) != 0);
				}
			}

			game.MoveToTheNextPeriod(itIsThePresent, "Begin", noww);

			foreach (var liness in sc.AllLinesOffered())
			{
				if (liness.DueDate.MilestoneAsString() == "AfterGameStarts")
				{
					Assert.AreEqual(100, liness.DueDate.TimeInSeconds);
					Assert.IsTrue(liness.RemainingAvailabilityTime(now2) != 0);
				}
			}
			line1 = betBoard.GetLine(1);
			line2 = betBoard.GetLine(2);

			Assert.AreEqual("In play", game.CurrentPeriod.Name);

			Assert.AreEqual("Limits", line1.DueDate.MilestoneAsString());
			Assert.AreEqual(-1, line1.RemainingAvailabilityTime(now2));
			Assert.IsTrue(line1.DueDate.IsUnlimited);

			Assert.AreEqual("AfterGameStarts", line2.DueDate.MilestoneAsString());
			Assert.AreEqual(90, line2.RemainingAvailabilityTime(now2));
			Assert.AreEqual(100, line2.DueDate.TimeInSeconds);
		}

		[TestMethod]
		public void bug6305_HasBackgroundLine()
		{
			var company = new Company();
			var domain = company.Sales.CreateDomain(false, 1, "localhost", PaymentChannels.Agents.INSIDER);
			var store = company.Sales.CreateStore(1, "store test");
			store.MakeCurrent();
			store.Add(domain);

			var tournaments = company.Tournaments;
			var leagueId = company.Tournaments.Leagues.NextLeagueId;
			var tournamentId = company.Tournaments.NextTournamentId;
			var sport = company.Tournaments.Sports.FindById(1);
			sport.CreateAndReplaceDefaultBackground("bg", "bg.jpg");
			sport.CreateAndReplaceDefaultSideBySide("sbs", "sbs.jpg");
			var league = company.Tournaments.Leagues.Create(sport, "soccer league", "SL", leagueId);
			var tournament = tournaments.CreateTournament(league, tournamentId, "soccer tournament");

			tournament.OpenRegistration();

			var gameDate = new DateTime(2020, 11, 20, 8, 0, 0);
			var teamId = tournaments.NextTeamId();
			var teamA = tournaments.CreateTeam(league, teamId++, "team A");
			tournament.Register(teamA);
			var teamB = tournaments.CreateTeam(league, teamId++, "team B");
			tournament.Register(teamB);
			var gameNumber = tournament.NextGameNumber();
			var game = tournament.GetNewGame(gameNumber, teamA, teamB, gameDate);
			game.SetFavorite(game.TeamA);

			tournament.CloseRegistration();
			tournament.Start();

			var betBoard = company.Betboard(tournament);
			betBoard.AcceptFrom(tournament);
			var catalog = betBoard.Catalog;
			int questionId = catalog.NextQuestionId();
			var spreadQuestion = catalog.CreateTierOneSpreadQuestion(questionId++, "ss");
			spreadQuestion.SetParameters(2, -120, 105);

			var matchDay = betBoard.Matchday(gameDate.Date);
			var showcase = matchDay.GetShowcase(game);
			var shelve1 = showcase.InsertShelve(0);
			var lineId = betBoard.NextLineId();
			var now = new DateTime(2020, 11, 02);

			Assert.IsFalse(spreadQuestion.HasBackground());
			var background = catalog.GetOrCreateBackground("ball.jpg");
			spreadQuestion.Background = background;
			Assert.IsTrue(spreadQuestion.HasBackground());

			var spread1 = (SpreadLine)shelve1.CreateLine(lineId, spreadQuestion, "N/A", now);
			var itIsThePresent = false;
			spread1.Publish(itIsThePresent, now);
			Assert.IsTrue(spread1.HasBackground());

			var yesNoQuestion = catalog.CreateTierTwoYesNoQuestion(questionId++, "p", "Will score the penalty?");
			yesNoQuestion.SetParameters(-105, 101);

			Assert.IsFalse(yesNoQuestion.HasBackground());
			var backgroundYN = catalog.GetOrCreateBackground("test_bg.jpg");
			yesNoQuestion.Background = backgroundYN;
			Assert.IsTrue(yesNoQuestion.HasBackground());

			lineId = betBoard.NextLineId();
			var yesNo1 = (YesNoLine)shelve1.CreateLine(lineId, yesNoQuestion, "N/A", now);
			Assert.IsTrue(yesNo1.HasBackground());

			now = new DateTime(2020, 11, 3);
			var spread2 = shelve1.CreateNewVersionForSpreadLine(spread1.LineId, 120, -105, "N/A", now);
			Assert.IsTrue(spread2.HasBackground());
			Assert.AreEqual("ball.jpg", spread2.Background.Url);

			var yesNo2 = shelve1.CreateNewVersionForYesNoLine(yesNo1.LineId, 120, -105, "N/A", now);
			Assert.IsTrue(yesNo2.HasBackground());
			Assert.AreEqual("test_bg.jpg", yesNo2.Background.Url);
		}

		[TestMethod]
		public void bug5991()
		{
			Integration.SavePreviousSettings(tempUseKafka: true, tempUseKafkaForAuto: false);
			QueueInMemory queue = new QueueInMemory();
			Integration.Kafka = queue;

			var company = new Company();
			CurrenciesTest.AddCurrencies();
			company.Accounting = new MockAccounting();
			bool itIsThePresent = true;
			DateTime now = new DateTime(2021, 01, 05, 11, 12, 13);
			var domain = company.Sales.CreateDomain(false, 1, "localhost", PaymentChannels.Agents.INSIDER);
			var store = company.Sales.CreateStore(1, "store test");
			store.MakeCurrent();
			store.Add(domain);
			var customer = company.GetOrCreateCustomerById("*********");
			var player = customer.Player;

			var tournaments = company.Tournaments;
			var leagueId = company.Tournaments.Leagues.NextLeagueId;
			var tournamentId = company.Tournaments.NextTournamentId;
			var sport = company.Tournaments.Sports.FindById(1);
			sport.CreateAndReplaceDefaultBackground("bg", "bg.jpg");
			sport.CreateAndReplaceDefaultSideBySide("sbs", "sbs.jpg");
			var league = company.Tournaments.Leagues.Create(sport, "soccer league", "SL", leagueId);
			var tournament = tournaments.CreateTournament(league, tournamentId, "soccer tournament");

			tournament.OpenRegistration();

			var gameDate = new DateTime(2020, 11, 20, 8, 0, 0);
			var teamId = tournaments.NextTeamId();
			var team1 = tournaments.CreateTeam(league, teamId++, "Real Madrid");
			var team2 = tournaments.CreateTeam(league, teamId++, "Barcelona");
			tournament.Register(team1);
			tournament.Register(team2);

			var team3 = tournaments.CreateTeam(league, teamId++, "Betis");
			var team4 = tournaments.CreateTeam(league, teamId++, "Sevilla");
			tournament.Register(team3);
			tournament.Register(team4);


			var gameNumber = tournament.NextGameNumber();
			var game1 = tournament.GetNewGame(gameNumber, team1, team2, gameDate);

			tournament.CloseRegistration();
			tournament.Start();

			var betBoard = company.Betboard(tournament);
			betBoard.AcceptFrom(tournament);
			var catalog = betBoard.Catalog;
			int questionId = catalog.NextQuestionId();
			var spreadQuestion = catalog.CreateTierOneSpreadQuestion(questionId++, "ss");
			spreadQuestion.SetParameters(2, -120, 105);

			var matchDay = betBoard.Matchday(gameDate.Date);
			var showcase = matchDay.GetShowcase(game1);
			var shelve1 = showcase.InsertShelve(0);
			var lineId = betBoard.NextLineId();

			Assert.IsFalse(spreadQuestion.HasBackground());
			var background = catalog.GetOrCreateBackground("ball.jpg");
			spreadQuestion.Background = background;
			Assert.IsTrue(spreadQuestion.HasBackground());

			var spread1 = (SpreadLine)shelve1.CreateLine(lineId, spreadQuestion, "N/A", now);
			spread1.Publish(itIsThePresent, now);

			int orderNumber = 1;
			var betNumber = 1;
			var authorizationId = 1;
			var product = new Product(company, 1);
			product.Price = 1;
			var orderCart = company.NewOrder(customer, orderNumber++, Coinage.Coin(Currencies.CODES.USD), authorizationId);
			orderCart.AddSpreadLine(betNumber, spread1, game1.TeamA, product, 1m);
			company.PurchaseOrder(itIsThePresent, orderCart, domain, now);

			game1.MoveToTheNextPeriod(itIsThePresent, "Begin", now);
			game1.NewResult(itIsThePresent, now, game1.TeamA, 1, game1.TeamB, 2);
			game1.MoveToTheNextPeriod(itIsThePresent, "End", now);
			showcase.Grade(itIsThePresent, spread1, now, game1.TeamA, 1, game1.TeamB, 2, "N/A");
		}

		[TestMethod]
		public void Bug6605()
		{
			Integration.UseKafka = true;
			QueueInMemory queue = new QueueInMemory();
			Integration.Kafka = queue;
			bool itIsThePresent = true;
			DateTime now = new DateTime(2020, 11, 02);

			Company company = new Company();
			CurrenciesTest.AddCurrencies();
			company.Sales.CreateStore(1, "Test Store").MakeCurrent();
			company.Accounting = new MockAccounting();
			var customer = company.GetOrCreateCustomerById("*********");
			var player = customer.Player;
			Tournaments tournaments = company.Tournaments;

			var domain = company.Sales.CreateDomain(itIsThePresent, 1, "localhost", PaymentChannels.Agents.INSIDER);
			company.Sales.CurrentStore.Add(domain);
			Book book = company.Book;
			Tag tag = book.RiskTags.CreateTag("Red", "http://tags/red.jpg");
			book.RiskTags.Tag(player, tag);

			var leagueId = company.Tournaments.Leagues.NextLeagueId;
			var tournamentId = company.Tournaments.NextTournamentId;
			var sport = company.Tournaments.Sports.FindById(1);
			sport.CreateAndReplaceDefaultBackground("bg", "bg.jpg");
			sport.CreateAndReplaceDefaultSideBySide("sbs", "sbs.jpg");
			var league = company.Tournaments.Leagues.Create(sport, "soccer league", "SL", leagueId);
			var tournament = tournaments.CreateTournament(league, tournamentId, "soccer tournament");
			tournament.OpenRegistration();

			var gameNumber = tournament.NextGameNumber();
			var teamId = tournaments.NextTeamId();
			var team1 = tournaments.CreateTeam(league, teamId, "team1");
			teamId = tournaments.NextTeamId();
			var team2 = tournaments.CreateTeam(league, teamId, "team2");
			var game = tournament.GetNewGame(gameNumber, team1, team2, now);
			tournament.Register(team1);
			tournament.Register(team2);
			tournament.CloseRegistration();
			tournament.Start();

			var betBoard = company.Betboard(tournament);
			betBoard.AcceptFrom(tournament);
			var matchDay = betBoard.Matchday(now);
			var showcase = matchDay.GetShowcase(game);
			game.SetFavorite(game.TeamA);

			LinesCatalog catalog = betBoard.Catalog;
			int questionId = catalog.NextQuestionId();
			catalog.CreateTierOneMoneyDrawQuestion(1, "dd");
			var shelve1 = showcase.InsertShelve(0);

			var moneyDrawQuestion = (MoneyDrawQuestion)catalog[questionId++];
			moneyDrawQuestion.SetParameters(-120, 114, 105);
			var lineId = betBoard.NextLineId();
			var moneyDraw1 = (MoneyDrawLine)shelve1.CreateLine(lineId, moneyDrawQuestion, "N/A", now);
			moneyDraw1.Publish(itIsThePresent, now);

			int orderNumber = 1;
			var betNumber = 1;
			var authorizationId = 1;
			var product = new Product(company, 1);
			product.Price = 1;

			var orderCart = company.NewOrder(customer, orderNumber++, Coinage.Coin(Currencies.CODES.USD), authorizationId);
			orderCart.AddMoneyDrawLine(betNumber++, moneyDraw1, game, product, 1m);
			company.PurchaseOrder(itIsThePresent, orderCart, domain, now);

			orderCart = company.NewOrder(customer, orderNumber++, Coinage.Coin(Currencies.CODES.USD), authorizationId);
			orderCart.AddMoneyDrawLine(betNumber++, moneyDraw1, game.TeamA, product, 1m);
			company.PurchaseOrder(itIsThePresent, orderCart, domain, now);

			var event1 = "Begin";
			var event2 = "End";
			game.MoveToTheNextPeriod(itIsThePresent, event1, now);
			Assert.AreEqual(game.CurrentPeriod.Name, Timeline.IN_PLAY);

			game.NewResult(itIsThePresent, now, game.TeamA, 93, game.TeamB, 93);
			game.MoveToTheNextPeriod(itIsThePresent, event2, now);
			showcase.Grade(itIsThePresent, moneyDraw1, now, game.TeamA, 93, game.TeamB, 93, "N/A");
		}
	}
}
