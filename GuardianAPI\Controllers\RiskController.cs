﻿using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;
using GamesEngine.Custodian.Persistance;
using Microsoft.AspNetCore.Mvc;
using System;
using Newtonsoft.Json;
using GamesEngine.Exchange;
using GamesEngine.Finance;
using GuardianAPI;
using Microsoft.AspNetCore.Authorization;
using GamesEngine.Custodian;
using static GamesEngine.Custodian.RiskRatingTypes;
using System.Runtime.Serialization;

namespace GuardianAPI.Controllers
{
	public class RiskController : AuthorizeController
	{
		[HttpGet("api/profiles/{profileId}/riskAssignments")]
		[Authorize(Roles = "h22")]
		public async Task<IActionResult> RiskAssignmentsForProfileAsync(int profileId)
		{
			if (profileId <= 0) return BadRequest($"{nameof(profileId)} must be greater than 0");

            var result = await GuardianManagerAPI.GuardianManager.PerformQryAsync(HttpContext, $@"
                {{
                    profiles = guardian.Profiles();
                    profile = profiles.SearchById({profileId});
                    riskAssignmentsList = guardian.RiskAssignments();
                    for(riskAssignments : riskAssignmentsList.SearchBy(profile))
                    {{
						riskAssignment = riskAssignments;
                        print riskAssignment.RiskRatingType.Name riskRatingTypeName;
                        print riskAssignment.RiskRatingType.Description riskRatingTypeDescription;
                        amountRangeByRiskRating = riskAssignment.AmountRangeByCurrencies;
                        for(amountRangesByCurrency : amountRangeByRiskRating.ListAmountRanges())
                        {{
                            print amountRangesByCurrency.MaxAmount.Value maxAmount;
							print amountRangesByCurrency.MaxAmount.ToDisplayFormat() maxAmountFormatted;
                            print amountRangesByCurrency.MinAmount.Value minAmount;
                            print amountRangesByCurrency.MinAmount.ToDisplayFormat() minAmountFormatted;
                            print amountRangesByCurrency.MaxAmount.CurrencyCodeAsText currencyCode;
                        }}
                        for(transactionTypes : amountRangeByRiskRating.ListTransactionTypes())
                        {{
                            print transactionTypes transactionType;
                        }}
                    }}
                }}
            ");

			return result;
		}
            
		[HttpGet("api/riskassignments")]
		[Authorize(Roles = "h19")]
		public async Task<IActionResult> ListRiskassignmentsAsync(string riskRatingType)
		{
			var commandsToPrintRiskAssignments = $@"
				for( riskAssignments : riskAssignmentsStored.SearchByRiskRatingType(riskRatingType))
				{{
					specificRiskRatingType = riskAssignments.RiskRatingType;
					print specificRiskRatingType.Name riskRatingTypeName;
					print specificRiskRatingType.Description riskRatingTypeDescription;
					amountRangeByRiskRating = riskAssignments.AmountRangeByCurrencies;
					for(amountRangesByCurrency : amountRangeByRiskRating.ListAmountRanges())
					{{
						print amountRangesByCurrency.MaxAmount.Value maxAmount;
                        print amountRangesByCurrency.MaxAmount.ToDisplayFormat() maxAmountFormatted;
						print amountRangesByCurrency.MinAmount.Value minAmount;
						print amountRangesByCurrency.MinAmount.ToDisplayFormat() minAmountFormatted;
						print amountRangesByCurrency.MaxAmount.CurrencyCodeAsText currencyCode;
					}}
					for(transactionTypes : amountRangeByRiskRating.ListTransactionTypes())
					{{
						print transactionTypes transactionType;
					}}
					for(profiles : riskAssignments.Profiles.List())
					{{
						print profiles.Id Id;
						print profiles.Name Name;

						for(approvers : profiles.ListApprovers())
						{{
							print approvers.EmailAddress EmailAddress;
						}}
					}}
				}}
			";

			var commandsToPrintProfiles = $@"
                profilesInMemory = guardian.Profiles();
                for(profiles : profilesInMemory.List())
                {{
                    print profiles.Id Id;
                    print profiles.Name Name;
                }}";

			IActionResult result;
			if (string.IsNullOrWhiteSpace(riskRatingType))
			{
				result = await GuardianManagerAPI.GuardianManager.PerformQryAsync(HttpContext, $@"
                {{
					riskAssignmentsStored = guardian.RiskAssignments();
					riskRatingTypes = company.RiskRatingTypes();
					for(types : riskRatingTypes.Types)
					{{
						riskRatingType = types;

						{commandsToPrintRiskAssignments}
					}}
					{commandsToPrintProfiles}
                }}
            ");
			}
			else
			{
				result = await GuardianManagerAPI.GuardianManager.PerformQryAsync(HttpContext, $@"
                {{
					riskAssignmentsStored = guardian.RiskAssignments();
					riskRatingTypes = company.RiskRatingTypes();
                    if(riskRatingTypes.ExistsName('{riskRatingType}'))
					{{
						riskRatingType = riskRatingTypes.FindRiskRatingType('{riskRatingType}');

						{commandsToPrintRiskAssignments}
					}}
					{commandsToPrintProfiles}
                }}
            ");
			}

			return result;
		}

		[HttpPost("api/riskassignments")]
		[Authorize(Roles = "h20")]
		public async Task<IActionResult> CreateRiskassignmentsAsync([FromBody] RiskAssignmentsModel payloads)
		{
			if (payloads == null) return BadRequest($"{nameof(payloads)} is required");
			if (!payloads.AllRiskRatingTypesAreValid) return BadRequest($"There are RiskRatingTypeName empty.");

			foreach (RiskAssignmentModel payload in payloads.Items)
			{
				foreach (var amountRangeByCurrency in payload.AmountRangesByCurrencies)
				{
					var currencyCode = amountRangeByCurrency.CurrencyCode;
					if (string.IsNullOrWhiteSpace(currencyCode)) return BadRequest($"{nameof(currencyCode)} is required");
					var maxAmount = amountRangeByCurrency.MaxAmount;
					var minAmount = amountRangeByCurrency.MinAmount;
					if (!Coinage.Exists(currencyCode)) return BadRequest($"{nameof(currencyCode)} '{currencyCode}' is not valid");
					if (maxAmount < 0) return GuardianManagerAPI.PrefabBadRequest($"{nameof(maxAmount)} must be greater or equal than 0");
					if (minAmount < 0) return GuardianManagerAPI.PrefabBadRequest($"{nameof(minAmount)} must be greater or equal than 0");
					if (maxAmount < minAmount) return GuardianManagerAPI.PrefabBadRequest($"{nameof(minAmount)} cannot be greater than {nameof(maxAmount)}");
				}
			}

			string employeeName = Security.UserName(HttpContext);
			StringBuilder commandsForProfiles = new StringBuilder();
			string riskRatingTypeName = string.Empty;
			foreach (RiskAssignmentModel payload in payloads.Items)
			{
				commandsForProfiles.Append("profilesSelected = Profiles();").AppendLine();
				if (payload.ProfilesIds != null && payload.ProfilesIds.Length > 0)
				{
					foreach (int profileId in payload.ProfilesIds)
					{
						commandsForProfiles.Append($"profilesSelected.Add(profiles.SearchById({profileId}));").AppendLine();
					}
				}

				commandsForProfiles.Append("ranges = AmountRanges();").AppendLine();
				commandsForProfiles.Append("amountRangeByCurrencies = ranges.CreateAmountRangeByCurrencies(riskRating, {").Append(RiskAssignmentModel.TransactionTypesAsText()).Append("});").AppendLine();
				foreach (var amountRangeByCurrency in payload.AmountRangesByCurrencies)
				{
					var currencyCode = amountRangeByCurrency.CurrencyCode;
					var maxAmount = amountRangeByCurrency.MaxAmount;
					var minAmount = amountRangeByCurrency.MinAmount;
					var coin = Coinage.Coin(currencyCode);
					if (!Coinage.Exists(coin.Iso4217Code)) return BadRequest($"{nameof(currencyCode)} '{currencyCode}' is not valid");
					commandsForProfiles.AppendLine($"amountRangeByCurrencies.SetAmountRange(Currency('{currencyCode}',{maxAmount}), Currency('{currencyCode}',{minAmount}), Now, '{employeeName}');").AppendLine();
				}

				if (!riskRatingTypeName.Equals(payload.RiskRatingTypeName))
				{
					commandsForProfiles.Append("riskRatingType = riskRatingTypes.FindRiskRatingType('").Append(payload.RiskRatingTypeName).Append("');").AppendLine();
					commandsForProfiles.Append("riskAssignments.StartupRiskAssignment(riskRatingType);").AppendLine();
					riskRatingTypeName = payload.RiskRatingTypeName;
				}
				commandsForProfiles.Append("riskAssignments.NewRiskAssignment(riskRatingType, amountRangeByCurrencies, profilesSelected);").AppendLine();
			}

			IActionResult result = await GuardianManagerAPI.GuardianManager.PerformCmdAsync(HttpContext, $@"
				{{
					riskRatingTypes = company.RiskRatingTypes();
					profiles = guardian.Profiles();
					riskRating = guardian.RiskRating();
					riskAssignments = guardian.RiskAssignments();

					{commandsForProfiles.ToString()}
				}}
				");

			return result;
		}

		[HttpGet("api/riskRatingTypes")]
        [Authorize(Roles = "h18")]
        public async Task<IActionResult> RiskRatingTypesAsync()
		{
			var result = await GuardianManagerAPI.GuardianManager.PerformQryAsync(HttpContext, $@"
                {{
					riskRatingTypes = company.RiskRatingTypes();
					for(types : riskRatingTypes.Types)
					{{
						type = types;
						print type.Name name;
						print type.Description description;
						countRiskAssignments = guardian.RiskAssignments().CountRiskAssignments(type);
                        print countRiskAssignments countRiskAssignments;
						lastEntries = type.Log.Entries();
						for (log:lastEntries)
						{{
							print log.DateFormattedAsText date;
							print log.Who who;
							print log.Message message;
						}}
                    }}
                }}
            ");
			return result;
		}

		[HttpPost("api/riskRatingType")]
        [Authorize(Roles = "h20")]
        public async Task<IActionResult> CreateRiskRatingTypeAsync([FromBody] RiskRatingTypeBody body)
		{
			if (body == null) return BadRequest($"{nameof(body)} is required");
			if (string.IsNullOrWhiteSpace(body.Name)) return BadRequest($"{nameof(body.Name)} is required");

			var commandForDescription = string.IsNullOrWhiteSpace(body.Description) ? string.Empty : $@"riskRatingType.Description = '{body.Description}';";
			string employeeName = Security.UserName(HttpContext);
			var result = await GuardianManagerAPI.GuardianManager.PerformChkThenCmdAsync(HttpContext, $@"
					riskRatingTypes = company.RiskRatingTypes();
					Check(!riskRatingTypes.ExistsName('{body.Name}')) Error 'Name {body.Name} already exists';
				",$@"
                {{
					riskRatingTypes = company.RiskRatingTypes();
					riskRatingType = riskRatingTypes.NewRiskRatingType('{body.Name}');
                    {commandForDescription}
                    riskRatingType.AddAnnotation('Risk rating type created', '{employeeName}', Now);
                }}
            ");
			return result;
		}

        [HttpPut("api/riskRatingType")]
        [Authorize(Roles = "h20")]
        public async Task<IActionResult> UpdateRiskRatingTypeAsync([FromBody] RiskRatingTypeToUpdateBody body)
		{
            if (body == null) return BadRequest($"{nameof(body)} is required");
            if (string.IsNullOrWhiteSpace(body.CurrentName)) return BadRequest($"{nameof(body.CurrentName)} is required");

			var commandForDescription = string.Empty;
			var commandForNewName = string.Empty;
			var messageForAnnotation = string.Empty;
            if (!string.IsNullOrWhiteSpace(body.NewName) && !body.CurrentName.Equals(body.NewName, StringComparison.OrdinalIgnoreCase))
            {
                commandForNewName = $"riskRatingType.Name = '{body.NewName}';";
                messageForAnnotation = $"Name updated to {body.NewName}";
            }
            if (!string.IsNullOrWhiteSpace(body.Description))
			{
				commandForDescription = $"riskRatingType.Description = '{body.Description}';";
				messageForAnnotation += string.IsNullOrWhiteSpace(messageForAnnotation) ? "Description updated" : ", description updated";
            }
			
			if (string.IsNullOrWhiteSpace(commandForNewName) && string.IsNullOrWhiteSpace(commandForDescription)) return BadRequest("No changes detected");

            string employeeName = Security.UserName(HttpContext);
			var result = await GuardianManagerAPI.GuardianManager.PerformCmdAsync(HttpContext, $@"
                {{
                    riskRatingTypes = company.RiskRatingTypes();
                    riskRatingType = riskRatingTypes.FindRiskRatingType('{body.CurrentName}');
                    {commandForNewName}
                    {commandForDescription}
                    riskRatingType.AddAnnotation('{messageForAnnotation}', '{employeeName}', Now);
                }}"); 
			return result;
		}

        [DataContract(Name = "RiskRatingTypeBody")]
        public class RiskRatingTypeBody
        {
            [DataMember(Name = "name")]
            public string Name { get; set; }
			[DataMember(Name = "description")]
            public string Description { get; set; }
        }

        [DataContract(Name = "RiskRatingTypeToUpdateBody")]
        public class RiskRatingTypeToUpdateBody
        {
            [DataMember(Name = "currentName")]
            public string CurrentName { get; set; }
            [DataMember(Name = "newName")]
            public string NewName { get; set; }
            [DataMember(Name = "description")]
            public string Description { get; set; }
        }
    }
}
