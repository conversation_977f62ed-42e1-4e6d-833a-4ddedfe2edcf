using GamesEngine.Business;
using GamesEngine.Marketing.Campaigns;
using GamesEngine.Messaging;
using GamesEngine.PurchaseOrders;
using GamesEngine.Settings;
using MySql.Data.MySqlClient;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Text;
using static GamesEngine.Messaging.Message;

namespace GamesEngine.Loyalty
{
	public class NotificationStorage
    {
        private readonly LoyaltyStorage loyaltyStorage;

        public NotificationStorage(HistoricalDatabaseType dbType, string connectionString)
        {
            if (String.IsNullOrEmpty(connectionString)) throw new ArgumentNullException(nameof(connectionString));
            if (dbType == HistoricalDatabaseType.MySQL)
            {
                loyaltyStorage = new LoyaltyStorageMySQL(connectionString);
            }
            else if (dbType == HistoricalDatabaseType.SQLServer)
            {
                loyaltyStorage = new LoyaltyStorageSQLServer(connectionString);
            }
            else
            {
                throw new GameEngineException($"There is no {nameof(LoyaltyStorage)} implementation.");
            }
        }

		private string UnEscapeSQLServer(string script)
		{
			StringBuilder result = new StringBuilder();
			foreach (char c in script)
			{
				switch (c)
				{
					case '\'':
						break;
					case '\"':
						result.Append('"');
						break;
					case '\\':
						result.Append('\\');
						break;
					default:
						result.Append(c);
						break;
				}
			}
			return result.ToString();
		}

		public int StoreNotification(string message)
        {
			int lastMessageIdSaved = 0;
			message = UnEscapeSQLServer(message);
			if (string.IsNullOrWhiteSpace(message)) throw new ArgumentNullException(nameof(message));
            if (loyaltyStorage != null)
            {
				lastMessageIdSaved= loyaltyStorage.StoreNotification(message);
            }

			return lastMessageIdSaved;
        }

		public int GetMessageIdFor(string message)
		{
			int lastMessageIdSaved = 0;
			message = UnEscapeSQLServer(message);
			if (string.IsNullOrWhiteSpace(message)) throw new ArgumentNullException(nameof(message));
			if (loyaltyStorage != null)
			{
				lastMessageIdSaved = loyaltyStorage.GetMessageIdFor(message);
			}

			return lastMessageIdSaved;
		}

		public void StorePositiveFeedback(int feedbackId, string message, string accountNumber, DateTime createdOn, int status, int storeId)
		{
			if (string.IsNullOrWhiteSpace(message)) throw new ArgumentNullException(nameof(message));
			if (string.IsNullOrWhiteSpace(accountNumber)) throw new ArgumentNullException(nameof(accountNumber));
			if (createdOn == default(DateTime)) throw new GameEngineException("Date cannot have default value");

			if (loyaltyStorage != null)
			{
				message = UnEscapeSQLServer(message);
				loyaltyStorage.StorePositiveFeedback(feedbackId, message, accountNumber, createdOn, status, storeId);
			}
		}

		public void StoreNegativeFeedback(int feedbackId, string message, string accountNumber, string device, string browser, DateTime createdOn, int status, int storeId)
		{
			if (string.IsNullOrWhiteSpace(message)) throw new ArgumentNullException(nameof(message));
			if (string.IsNullOrWhiteSpace(accountNumber)) throw new ArgumentNullException(nameof(accountNumber));
			if (string.IsNullOrWhiteSpace(accountNumber)) throw new ArgumentNullException(nameof(device));
			if (string.IsNullOrWhiteSpace(accountNumber)) throw new ArgumentNullException(nameof(browser));
			if (createdOn == default(DateTime)) throw new GameEngineException("Date cannot have default value");


			if (loyaltyStorage != null)
			{
				message = UnEscapeSQLServer(message);
				loyaltyStorage.StoreNegativeFeedback(feedbackId, message, accountNumber, device, browser, createdOn, status, storeId);
			}
		}

		public void StoreReplyMessages(int replyId, int messageId, string accountNumber, int feedbackId, DateTime repliedDate, int storeId)
		{
			if (messageId == 0) throw new ArgumentNullException(nameof(messageId));
			if (string.IsNullOrWhiteSpace(accountNumber)) throw new ArgumentNullException(nameof(accountNumber));
			if (feedbackId < 1) throw new GameEngineException("Feedback is required to create a reply");
			if (repliedDate == default(DateTime)) throw new GameEngineException("Date cannot have default value");

			if (loyaltyStorage != null)
			{
				loyaltyStorage.StoreReplyMessages(replyId, messageId, accountNumber, feedbackId, repliedDate, storeId);
			}
		}

		public int StoreSegment(string expression, int campaignId, string accountNumber, DateTime createdOn)
		{
			if (string.IsNullOrWhiteSpace(expression)) throw new ArgumentNullException(nameof(expression));
			if (string.IsNullOrWhiteSpace(accountNumber)) throw new ArgumentNullException(nameof(accountNumber));
			if (campaignId < 1) throw new GameEngineException("Campaign is required to create a segment");
			if (createdOn == default(DateTime)) throw new GameEngineException("Date cannot have default value");

			int lastSegmentIdSaved = 0;
			if (loyaltyStorage != null)
			{
				lastSegmentIdSaved = loyaltyStorage.StoreSegment(expression, campaignId, accountNumber, createdOn);
			}

			return lastSegmentIdSaved;
		}

		public void StoreNotificationsSegment(int segmentId, int notificationId, string accountNumber, int status, DateTime createdOn)
		{
			if (string.IsNullOrWhiteSpace(accountNumber)) throw new ArgumentNullException(nameof(accountNumber));
			if (segmentId < 1) throw new GameEngineException("Segment is required to create a Notification");
			if (notificationId < 1) throw new GameEngineException("Message is required to create a Notification");
			if (status < 1) throw new GameEngineException("Status is required to create a Notification");
			if (createdOn == default(DateTime)) throw new GameEngineException("Date cannot have default value");

			if (loyaltyStorage != null)
			{
				loyaltyStorage.StoreNotificationsSegment(segmentId, notificationId, accountNumber, status, createdOn);
			}
		}

		public void UpdateNotificationsSegment(int messageId, int segmentId, DateTime sentOn, string sentBy)
		{
			if (loyaltyStorage != null)
			{
				loyaltyStorage.UpdateNotificationsSegment(messageId, segmentId, sentOn, sentBy);
			}
		}

		public void UpdateNotificationsSegment(int messageId, DateTime readOn, int status)
		{
			if (loyaltyStorage != null)
			{
				loyaltyStorage.UpdateNotificationsSegment(messageId, readOn, status);
			}
		}

		public void UpdatePositiveFeedback(int feedbackId, int status)
		{
			if (loyaltyStorage != null)
			{
				loyaltyStorage.UpdatePositiveFeedback(feedbackId, status);
			}
		}

		public void UpdateNegativeFeedback(int feedbackId, int status)
		{
			if (loyaltyStorage != null)
			{
				loyaltyStorage.UpdateNegativeFeedback(feedbackId, status);
			}
		}

		public void AttachGameboardNumberOnPositiveFeedback(int feedbackId, string gameboardNumber)
		{
			if (loyaltyStorage != null)
			{
				loyaltyStorage.AttachGameboardNumberOnPositiveFeedback(feedbackId, gameboardNumber);
			}
		}

		public void AttachGameboardNumberOnNegativeFeedback(int feedbackId, string gameboardNumber)
		{
			if (loyaltyStorage != null)
			{
				loyaltyStorage.AttachGameboardNumberOnNegativeFeedback(feedbackId, gameboardNumber);
			}
		}

		internal IEnumerable<Message> RetrieveAllFeedbacks(Company company, Store store)
		{
			List<Message> result = new List<Message>();
			if (loyaltyStorage != null)
			{
				result.AddRange(loyaltyStorage.PositiveFeedback(company, store));
				result.AddRange(loyaltyStorage.NegativeFeedback(company, store));
			}
			return result;
		}

		internal PersistenceMessage RetrieveFeedback(Company company, Store store, int feedbackId)
		{
			if (feedbackId <= 0) throw new GameEngineException($"{ nameof(feedbackId) } '{feedbackId}' is not valid");

			PersistenceMessage result = null;
			if (loyaltyStorage != null)
			{
				result = loyaltyStorage.RetrievePositiveFeedback(company, store, feedbackId);
				if (result == null)
				{
					result = loyaltyStorage.RetrieveNegativeFeedback(company, store, feedbackId);
				}

			}
			if (result == null) throw new GameEngineException($"{nameof(feedbackId)} '{feedbackId}' does not exist");
			return result;
		}

		internal PagedMessages FeedbackMessages(Company company, Store store, string feedbackType, DateTime startDate, DateTime endDate, int initialIndex, int amountOfRows)
		{
			if (string.IsNullOrWhiteSpace(feedbackType)) throw new ArgumentNullException(nameof(feedbackType));
			if (startDate.Hour != 0 || startDate.Minute != 0 || startDate.Second != 0) throw new GameEngineException($"{nameof(startDate)} {startDate} is not valid");
			if (endDate.Hour != 0 || endDate.Minute != 0 || endDate.Second != 0) throw new GameEngineException($"{nameof(endDate)} {endDate} is not valid");
			if (initialIndex < 0) throw new GameEngineException($"{nameof(initialIndex)} must be greater or equal than 0");
			if (amountOfRows <= 0) throw new GameEngineException($"{nameof(amountOfRows)} must be greater than 0");

			PagedMessages result = null;
			if (loyaltyStorage != null)
			{
				var allFeedbackSelected = feedbackType == "all";
				if (allFeedbackSelected)
				{
					result = loyaltyStorage.AllFeedbacks(company, store, startDate, endDate, initialIndex, amountOfRows);
				}
				else if (feedbackType == FeedbackType.Positive.ToString().ToLower())
				{
					result = loyaltyStorage.PositiveFeedbacks(company, store, startDate, endDate, initialIndex, amountOfRows);
				}
				else if (feedbackType == FeedbackType.Negative.ToString().ToLower())
				{
					result = loyaltyStorage.NegativeFeedbacks(company, store, startDate, endDate, initialIndex, amountOfRows);
				}
				else
				{
					throw new GameEngineException($"{nameof(feedbackType)} '{feedbackType}' is not valid");
				}
			}
			return result;
		}

		internal PagedMessages FeedbackMessagesWithoutPagination(Company company, Store store, string feedbackType, DateTime startDate, DateTime endDate)
		{
			if (string.IsNullOrWhiteSpace(feedbackType)) throw new ArgumentNullException(nameof(feedbackType));
			if (startDate.Hour != 0 || startDate.Minute != 0 || startDate.Second != 0) throw new GameEngineException($"{nameof(startDate)} {startDate} is not valid");
			if (endDate.Hour != 0 || endDate.Minute != 0 || endDate.Second != 0) throw new GameEngineException($"{nameof(endDate)} {endDate} is not valid");

			PagedMessages result = null;
			if (loyaltyStorage != null)
			{
				var allFeedbackSelected = feedbackType == "all";
				if (allFeedbackSelected)
				{
					result = loyaltyStorage.AllFeedbacksWithoutPagination(company, store, startDate, endDate);
				}
				else if (feedbackType == FeedbackType.Positive.ToString().ToLower())
				{
					result = loyaltyStorage.PositiveFeedbacksWithoutPagination(company, store, startDate, endDate);
				}
				else if (feedbackType == FeedbackType.Negative.ToString().ToLower())
				{
					result = loyaltyStorage.NegativeFeedbacksWithoutPagination(company, store, startDate, endDate);
				}
				else
				{
					throw new GameEngineException($"{nameof(feedbackType)} '{feedbackType}' is not valid");
				}
			}
			return result;
		}

		public enum FeedbackType { Positive, Negative }

		private abstract class LoyaltyStorage
        {
            protected readonly string connectionString;
            protected const string TABLE_MESSAGES = "Message";
            protected const string TABLE_POSITIVE_FEEDBACK = "PositiveFeedback";
            protected const string TABLE_NEGATIVE_FEEDBACK = "NegativeFeedback";
            protected const string TABLE_SEGMENT = "Segment";
            protected const string TABLE_REPLY_MESSAGE = "ReplyMessage";
            protected const string TABLE_NOTIFICATIONS_SEGMENT = "NotificationsSegment";
            protected string INSERT_MESSAGE;
            protected string INSERT_POSITIVE_FEEDBACK;
            protected string INSERT_NEGATIVE_FEEDBACK;
            protected string INSERT_SEGMENT;
            protected string INSERT_REPLY_MESSAGE;
            protected string INSERT_NOTIFICATIONS_SEGMENT;

            protected LoyaltyStorage(string connectionString)
            {
                this.connectionString = connectionString;
            }

            internal abstract int StoreNotification(string message);

			internal abstract int GetMessageIdFor(string message);

            internal abstract void StorePositiveFeedback(int feedbackId, string message, string accountNumber, DateTime createdOn, int status, int storeId);
            internal abstract void StoreNegativeFeedback(int feedbackId, string message, string accountNumber, string device, string browser, DateTime createdOn, int status, int storeId);
            internal abstract void StoreReplyMessages(int replyId, int messageId, string accountNumber, int feedbackId, DateTime repliedDate, int storeId);

            internal abstract int StoreSegment(string expression, int campaignId, string account, DateTime createdOn);

            internal abstract void StoreNotificationsSegment(int segmentId, int notificationId, string accountNumber, int status, DateTime createdOn);

            internal abstract void UpdateNotificationsSegment(int messageId, int segmentId, DateTime sentOn, string sentBy);
            internal abstract void UpdateNotificationsSegment(int messageId, DateTime readOn, int status);
            internal abstract void UpdatePositiveFeedback(int feedbackId, int status);
            internal abstract void UpdateNegativeFeedback(int feedbackId, int status);
            internal abstract void AttachGameboardNumberOnPositiveFeedback(int feedbackId, string gameboardNumber);
            internal abstract void AttachGameboardNumberOnNegativeFeedback(int feedbackId, string gameboardNumber);

			internal abstract IEnumerable<Message> PositiveFeedback(Company company, Store store);
			internal abstract IEnumerable<Message> NegativeFeedback(Company company, Store store);

			internal abstract PagedMessages PositiveFeedbacks(Company company, Store store, DateTime startDate, DateTime endDate, int initialIndex, int amountOfRows);
			internal PagedMessages PositiveFeedbacksWithoutPagination(Company company, Store store, DateTime startDate, DateTime endDate)
			{
				return PositiveFeedbacks(company, store, startDate, endDate, 0, 0);
			}
			internal abstract PagedMessages NegativeFeedbacks(Company company, Store store, DateTime startDate, DateTime endDate, int initialIndex, int amountOfRows);
			internal PagedMessages NegativeFeedbacksWithoutPagination(Company company, Store store, DateTime startDate, DateTime endDate)
			{
				return NegativeFeedbacks(company, store, startDate, endDate, 0, 0);
			}
			internal abstract PagedMessages AllFeedbacks(Company company, Store store, DateTime startDate, DateTime endDate, int initialIndex, int amountOfRows);
			internal PagedMessages AllFeedbacksWithoutPagination(Company company, Store store, DateTime startDate, DateTime endDate)
			{
				return AllFeedbacks(company, store, startDate, endDate, 0, 0);
			}
			protected abstract void RetrieveReplyBasedOnFeedback(Store store, int feedbackId, out DateTime repliedDate, out bool hasAReply);
			internal abstract PersistenceMessage RetrievePositiveFeedback(Company company, Store store, int feedbackId);
			internal abstract PersistenceMessage RetrieveNegativeFeedback(Company company, Store store, int feedbackId);

			protected string ToDateString(int yyyy, int mm, int dd, int hh, int min, int s)
			{
				DateTime result = new DateTime(yyyy, mm, dd, hh, min, s);
				return result.ToString("yyyy-MM-dd HH:mm:ss");
			}

			protected string ToDayString(DateTime day)
			{
				return day.ToString("yyyy-MM-dd");
			}

			protected string ToFullDateTimeString(DateTime day)
			{
				return day.ToString("yyyy-MM-dd HH:mm:ss.fff");
			}
		}

        private class LoyaltyStorageMySQL : LoyaltyStorage
        {

            internal LoyaltyStorageMySQL(string connectionString) : base(connectionString)
            {
				if (!ExistTable())
				{
					CrearStorage();
				}
			}

			private bool ExistTable()
            {
                bool exists = true;
                string sql = "SELECT 1 FROM " + TABLE_POSITIVE_FEEDBACK + " LIMIT 1";
                using (MySqlConnection connection = new MySqlConnection(connectionString))
                {
                    try
                    {
                        connection.Open();
                        using (MySqlCommand command = new MySqlCommand(sql, connection))
                        {
                            var dataReader = command.ExecuteReader();
                            dataReader.Close();
                        }
                    }
                    catch
                    {
                        exists = false;
                    }
                    finally
                    {
                        connection.Close();
                    }
                }
                return exists;
            }

            private void CrearStorage()
            {
                StringBuilder statement = new StringBuilder();

                statement
                    .Append("create table ").Append(TABLE_MESSAGES)
                    .Append("(")
                    .Append("ID BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,")
                    .Append("MESSAGE VARCHAR(512) NOT NULL,")
                    .Append("PRIMARY KEY (ID)")
                    .Append(") CHARSET=utf8;");
                statement
                    .Append("create table ").Append(TABLE_POSITIVE_FEEDBACK)
                    .Append("(")
                    .Append("ID INT NOT NULL,")
                    .Append("MESSAGE VARCHAR(1024) NOT NULL,")
                    .Append($"ACCOUNT VARCHAR({Customer.DATABASE_FIELD_ACCOUNT_LENGHT}) NOT NULL,")
                    .Append("CREATEDON DATETIME NOT NULL,")
					.Append("STATUS TINYINT NOT NULL,")
					.Append("STORE TINYINT NOT NULL,")
					.Append("GAMEBOARD_NUMBER VARCHAR(45),")
					.Append("PRIMARY KEY (ID)")
                    .Append(") CHARSET=utf8;");
                statement
                    .Append("create table ").Append(TABLE_NEGATIVE_FEEDBACK)
                    .Append("(")
                    .Append("ID INT NOT NULL,")
                    .Append("MESSAGE VARCHAR(1024) NOT NULL,")
                    .Append($"ACCOUNT VARCHAR({Customer.DATABASE_FIELD_ACCOUNT_LENGHT}) NOT NULL,")
					.Append("DEVICE VARCHAR(100) NOT NULL,")
					.Append("BROWSER VARCHAR(100) NOT NULL,")
					.Append("CREATEDON DATETIME NOT NULL,")
					.Append("STATUS TINYINT NOT NULL,")
					.Append("STORE TINYINT NOT NULL,")
					.Append("GAMEBOARD_NUMBER VARCHAR(45),")
					.Append("PRIMARY KEY (ID)")
                    .Append(") CHARSET=utf8;");
                statement
                    .Append("create table ").Append(TABLE_REPLY_MESSAGE)
                    .Append("(")
                    .Append("ID INT NOT NULL,")
                    .Append("MESSAGE INT NOT NULL,")
                    .Append($"ACCOUNT VARCHAR({Customer.DATABASE_FIELD_ACCOUNT_LENGHT}) NOT NULL,")
                    .Append($"FEEDBACK INT NOT NULL,")
                    .Append("CREATEDON DATETIME NOT NULL,")
					.Append("STORE TINYINT NOT NULL,")
					.Append("PRIMARY KEY (ID)")
                    .Append(") CHARSET=utf8;");
                statement
                    .Append("create table ").Append(TABLE_SEGMENT)
                    .Append("(")
                    .Append("ID BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,")
                    .Append("EXPRESSION VARCHAR(500) NOT NULL,")
					.Append("CAMPAIGN INT NOT NULL,")
					.Append($"ACCOUNT TEXT NOT NULL,")
					.Append("CREATEDON DATETIME NOT NULL,")
                    .Append("PRIMARY KEY (ID)")
                    .Append(") CHARSET=utf8;");
                statement
                    .Append("create table ").Append(TABLE_NOTIFICATIONS_SEGMENT)
                    .Append("(")
                    .Append("ID BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,")
                    .Append("SEGMENT INT NOT NULL,")
                    .Append("MESSAGE INT NOT NULL,")
                    .Append($"ACCOUNT VARCHAR({Customer.DATABASE_FIELD_ACCOUNT_LENGHT}) NOT NULL,")
                    .Append("STATUS TINYINT NOT NULL,")
                    .Append("CREATEDON DATETIME NOT NULL,")
                    .Append("READON DATETIME NULL,")
                    .Append("SENTON DATETIME NULL,")
                    .Append("SENTBY VARCHAR(50) NULL,")
                    .Append("PRIMARY KEY (ID)")
                    .Append(") CHARSET=utf8;");

                string sql = statement.ToString();
                using (MySqlConnection connection = new MySqlConnection(connectionString))
                {
                    try
                    {
                        connection.Open();
                        using (MySqlCommand command = new MySqlCommand(sql, connection))
                        {
                            command.ExecuteNonQuery();
                        }
                    }
                    finally
                    {
                        connection.Close();
                    }
                }
            }

			internal override int StoreNotification(string message)
			{
                string record = $"INSERT INTO {TABLE_MESSAGES} (message) VALUES ('{message}'); SELECT LAST_INSERT_ID() AS ID;";
				int messageIdCreated = 0;

				using (MySqlConnection connection = new MySqlConnection(connectionString))
				{
					try
					{
						connection.Open();
						using (MySqlCommand command = new MySqlCommand(record, connection))
						{
							command.CommandType = CommandType.Text;
							messageIdCreated = Convert.ToInt32(command.ExecuteScalar());
						}
					}
					catch
					{
						throw new GameEngineException("MySQL Error [" + record + "]");
					}
					finally
					{
						connection.Close();
					}
				}

				return messageIdCreated;
			}

			internal override int GetMessageIdFor(string message)
			{
				int result = -1;
				var command = $@"select Id from {TABLE_MESSAGES} where message = '{message}';";
				using (MySqlConnection connection = new MySqlConnection(connectionString))
				{
					connection.Open();
					using (MySqlCommand cmd = new MySqlCommand(command, connection))
					{
						using (MySqlDataReader reader = cmd.ExecuteReader())
						{
							while (reader.Read())
							{
								result = reader.GetInt32(0);
							}
						}
					}
					connection.Close();
				}
				return result;
			}

			internal override int StoreSegment(string expression, int campaignId, string accountNumber, DateTime createdOn)
			{
				var date = ToDateString(createdOn.Year, createdOn.Month, createdOn.Day, createdOn.Hour, createdOn.Minute, createdOn.Second);
				string record = $@"INSERT INTO {TABLE_SEGMENT} (expression, campaign, account, createdon) VALUES ('{expression}',
									{campaignId},
									'{accountNumber}',
									'{date}'
								); SELECT LAST_INSERT_ID() AS ID;";
				int segmentIdCreated = 0;

				using (MySqlConnection connection = new MySqlConnection(connectionString))
				{
					try
					{
						connection.Open();
						using (MySqlCommand command = new MySqlCommand(record, connection))
						{
							command.CommandType = CommandType.Text;
							segmentIdCreated = Convert.ToInt32(command.ExecuteScalar());
						}
					}
					catch
					{
						throw new GameEngineException("MySQL Error [" + record + "]");
					}
					finally
					{
						connection.Close();
					}
				}
				return segmentIdCreated;
			}

            internal override void StoreNotificationsSegment(int segmentId, int notificationId, string accountNumber, int status, DateTime createdOn)
			{
				var date = ToDateString(createdOn.Year, createdOn.Month, createdOn.Day, createdOn.Hour, createdOn.Minute, createdOn.Second);
				string record = $@"INSERT INTO {TABLE_NOTIFICATIONS_SEGMENT} (segment, message, account, status, createdon) VALUES ({segmentId},
									{notificationId},
									'{accountNumber}',
									{status},
									'{date}'
								)";

				using (MySqlConnection connection = new MySqlConnection(connectionString))
				{
					try
					{
						connection.Open();
						using (MySqlCommand command = new MySqlCommand(record, connection))
						{
							command.CommandType = CommandType.Text;
							command.ExecuteNonQuery();
						}
					}
					catch
					{
						throw new GameEngineException("MySQL Error [" + record + "]");
					}
					finally
					{
						connection.Close();
					}
				}
			}

			internal override void StorePositiveFeedback(int feedbackId, string message, string accountNumber, DateTime createdOn, int status, int storeId)
			{
				var date = ToDateString(createdOn.Year, createdOn.Month, createdOn.Day, createdOn.Hour, createdOn.Minute, createdOn.Second);
				string record = $@"INSERT INTO {TABLE_POSITIVE_FEEDBACK} (ID, MESSAGE, ACCOUNT, CREATEDON, STATUS, STORE) VALUES ({feedbackId},
									'{message}',
									'{accountNumber}',
									'{date}',
									'{status}',
									{storeId}
								)";

				using (MySqlConnection connection = new MySqlConnection(connectionString))
				{
					try
					{
						connection.Open();
						using (MySqlCommand command = new MySqlCommand(record, connection))
						{
							command.CommandType = CommandType.Text;
							command.ExecuteNonQuery();
						}
					}
					catch
					{
						throw new GameEngineException("MySQL Error [" + record + "]");
					}
					finally
					{
						connection.Close();
					}
				}
			}

			internal override void StoreNegativeFeedback(int feedbackId, string message, string accountNumber, string device, string browser, DateTime createdOn, int status, int storeId)
			{
				var date = ToDateString(createdOn.Year, createdOn.Month, createdOn.Day, createdOn.Hour, createdOn.Minute, createdOn.Second);
				string record = $@"INSERT INTO {TABLE_NEGATIVE_FEEDBACK} (ID, MESSAGE, ACCOUNT, DEVICE, BROWSER, CREATEDON, STATUS, STORE) VALUES ({feedbackId},
									'{message}',
									'{accountNumber}',
									'{device}',
									'{browser}',
									'{date}',
									'{status}',
									{storeId}
								)";

				using (MySqlConnection connection = new MySqlConnection(connectionString))
				{
					try
					{
						connection.Open();
						using (MySqlCommand command = new MySqlCommand(record, connection))
						{
							command.CommandType = CommandType.Text;
							command.ExecuteNonQuery();
						}
					}
					catch
					{
						throw new GameEngineException("MySQL Error [" + record + "]");
					}
					finally
					{
						connection.Close();
					}
				}
			}
			internal override void StoreReplyMessages(int replyId, int messageId, string accountNumber, int feedbackId, DateTime repliedDate, int storeId)
			{
				var date = ToDateString(repliedDate.Year, repliedDate.Month, repliedDate.Day, repliedDate.Hour, repliedDate.Minute, repliedDate.Second);
				string record = $@"INSERT INTO {TABLE_REPLY_MESSAGE} (ID, MESSAGE, ACCOUNT, FEEDBACK, CREATEDON, STORE) VALUES ({replyId},
									{messageId},
									'{accountNumber}',
									{feedbackId},
									'{date}',
									{storeId}
								)";

				using (MySqlConnection connection = new MySqlConnection(connectionString))
				{
					try
					{
						connection.Open();
						using (MySqlCommand command = new MySqlCommand(record, connection))
						{
							command.CommandType = CommandType.Text;
							command.ExecuteNonQuery();
						}
					}
					catch
					{
						throw new GameEngineException("MySQL Error [" + record + "]");
					}
					finally
					{
						connection.Close();
					}
				}
			}

			internal override void UpdateNotificationsSegment(int messageId, int segmentId, DateTime sentOn, string sentBy)
			{
				{
					//TODO Ver como implementar el update y como hacer el read on para la notificacion de un usuario y el status
					var sentDate = ToDateString(sentOn.Year, sentOn.Month, sentOn.Day, sentOn.Hour, sentOn.Minute, sentOn.Second);
					string record = $@"UPDATE {TABLE_NOTIFICATIONS_SEGMENT} SET SENTON = '{sentDate}', SENTBY = '{sentBy}' WHERE MESSAGE = {messageId} AND SEGMENT = {segmentId};";

					using (MySqlConnection connection = new MySqlConnection(connectionString))
					{
						try
						{
							connection.Open();
							using (MySqlCommand command = new MySqlCommand(record, connection))
							{
								command.CommandType = CommandType.Text;
								command.ExecuteNonQuery();
							}
						}
						catch
						{
							throw new GameEngineException("MySQL Error [" + record + "]");
						}
						finally
						{
							connection.Close();
						}
					}
				}
			}

			internal override void UpdateNotificationsSegment(int messageId, DateTime readOn, int status)
			{

			}

			internal override void UpdatePositiveFeedback(int feedbackId, int status)
			{
				string record = $@"UPDATE {TABLE_POSITIVE_FEEDBACK} SET STATUS = '{status}' WHERE ID = {feedbackId};";

				using (MySqlConnection connection = new MySqlConnection(connectionString))
				{
					try
					{
						connection.Open();
						using (MySqlCommand command = new MySqlCommand(record, connection))
						{
							command.CommandType = CommandType.Text;
							command.ExecuteNonQuery();
						}
					}
					catch
					{
						throw new GameEngineException("MySQL Error [" + record + "]");
					}
					finally
					{
						connection.Close();
					}
				}
			}

			internal override void UpdateNegativeFeedback(int feedbackId, int status)
			{
				string record = $@"UPDATE {TABLE_NEGATIVE_FEEDBACK} SET STATUS = '{status}' WHERE ID = {feedbackId};";

				using (MySqlConnection connection = new MySqlConnection(connectionString))
				{
					try
					{
						connection.Open();
						using (MySqlCommand command = new MySqlCommand(record, connection))
						{
							command.CommandType = CommandType.Text;
							command.ExecuteNonQuery();
						}
					}
					catch
					{
						throw new GameEngineException("MySQL Error [" + record + "]");
					}
					finally
					{
						connection.Close();
					}
				}
			}

			internal override void AttachGameboardNumberOnPositiveFeedback(int feedbackId, string gameboardNumber)
			{
				string record = $@"UPDATE {TABLE_POSITIVE_FEEDBACK} SET GAMEBOARD_NUMBER = '{gameboardNumber}' WHERE ID = {feedbackId};";

				using (MySqlConnection connection = new MySqlConnection(connectionString))
				{
					try
					{
						connection.Open();
						using (MySqlCommand command = new MySqlCommand(record, connection))
						{
							command.CommandType = CommandType.Text;
							command.ExecuteNonQuery();
						}
					}
					catch
					{
						throw new GameEngineException("MySQL Error [" + record + "]");
					}
					finally
					{
						connection.Close();
					}
				}
			}

			internal override void AttachGameboardNumberOnNegativeFeedback(int feedbackId, string gameboardNumber)
			{
				string record = $@"UPDATE {TABLE_NEGATIVE_FEEDBACK} SET GAMEBOARD_NUMBER = '{gameboardNumber}' WHERE ID = {feedbackId};";

				using (MySqlConnection connection = new MySqlConnection(connectionString))
				{
					try
					{
						connection.Open();
						using (MySqlCommand command = new MySqlCommand(record, connection))
						{
							command.CommandType = CommandType.Text;
							command.ExecuteNonQuery();
						}
					}
					catch
					{
						throw new GameEngineException("MySQL Error [" + record + "]");
					}
					finally
					{
						connection.Close();
					}
				}
			}

			private Message RetrieveReplyBasedOnFeedback(Message feedbackMessage, int storeId, int feedbackId)
			{
				string sql = $@"SELECT TOP 1 CREATEDON FROM ReplyMessage WHERE STORE = {storeId} AND FEEDBACK = {feedbackId} ORDER BY CREATEDON desc;";
				using (MySqlConnection connection = new MySqlConnection(connectionString))
				{
					try
					{
						connection.Open();
						using (MySqlCommand command = new MySqlCommand(sql, connection))
						{
							var dataReader = command.ExecuteReader();

							while (dataReader.Read())
							{
								DateTime createdOn = dataReader.GetDateTime(0);

								feedbackMessage.RepliedDate = createdOn;
								feedbackMessage.HasAReply = true;
							}
							dataReader.Close();
						}
					}
					catch (Exception e)
					{
						throw new GameEngineException("MySQL Error [" + e + "]");
					}
					finally
					{
						connection.Close();
					}
				}

				return feedbackMessage;
			}

			protected override void RetrieveReplyBasedOnFeedback(Store store, int feedbackId, out DateTime repliedDate, out bool hasAReply)
			{
				repliedDate = default(DateTime);
				hasAReply = false;
				string sql = $@"SELECT CREATEDON 
								FROM {TABLE_REPLY_MESSAGE} 
								WHERE STORE = {store.Id} AND FEEDBACK = {feedbackId} 
								ORDER BY CREATEDON desc LIMIT 1;";
				using (MySqlConnection connection = new MySqlConnection(connectionString))
				{
					try
					{
						connection.Open();
						using (MySqlCommand command = new MySqlCommand(sql, connection))
						{
							var dataReader = command.ExecuteReader();

							while (dataReader.Read())
							{
								DateTime createdOn = dataReader.GetDateTime(0);

								repliedDate = createdOn;
								hasAReply = true;
							}
							dataReader.Close();
						}
					}
					catch (Exception e)
					{
						throw new GameEngineException("MySQL Error [" + e + "]");
					}
					finally
					{
						connection.Close();
					}
				}
			}

			internal override PersistenceMessage RetrievePositiveFeedback(Company company, Store store, int feedbackId)
			{
				string sql = $@"SELECT TOP 1 ID, MESSAGE, ACCOUNT, CREATEDON, STATUS, GAMEBOARD_NUMBER
								FROM {TABLE_POSITIVE_FEEDBACK} 
								WHERE ID = {feedbackId}
								;";

				PersistenceMessage notification = null;
				int recordsTotal = 0;
				using (MySqlConnection connection = new MySqlConnection(connectionString))
				{
					try
					{
						connection.Open();
						using (MySqlCommand command = new MySqlCommand(sql, connection))
						{
							var dataReader = command.ExecuteReader();

							while (dataReader.Read())
							{
								int id = dataReader.GetInt32(0);
								string message = dataReader.GetString(1);
								string account = dataReader.GetString(2);
								DateTime createdOn = dataReader.GetDateTime(3);
								int statusNumber = dataReader.GetByte(4);
								MessageStatus status;
								bool isValid = Enum.TryParse(statusNumber.ToString(), true, out status);
								if (!isValid)
								{
									throw new GameEngineException($@"There is no status for number {statusNumber}");
								}
								string gameboardNumber = dataReader.IsDBNull(5) ? null : dataReader.GetString(5);
								recordsTotal = dataReader.GetInt32(6);

								notification = new PersistenceMessage()
								{
									Id = id,
									Body = message,
									AccountNumber = account,
									CreationDate = createdOn,
									IsRead = status == MessageStatus.READ,
									GameboardNumber = gameboardNumber,
									HasGameboardAttached = !string.IsNullOrWhiteSpace(gameboardNumber),
									FeedbackType = FeedbackType.Positive
								};

								DateTime repliedDate;
								bool hasAReply;
								RetrieveReplyBasedOnFeedback(store, id, out repliedDate, out hasAReply);
								notification.RepliedDate = repliedDate;
								notification.HasAReply = hasAReply;
							}
							dataReader.Close();
						}
					}
					catch (Exception e)
					{
						throw new GameEngineException("MySQL Error [" + e + "]");
					}
					finally
					{
						connection.Close();
					}
				}
				return notification;
			}

			internal override PersistenceMessage RetrieveNegativeFeedback(Company company, Store store, int feedbackId)
			{
				string sql = $@"SELECT TOP 1 ID, MESSAGE, ACCOUNT, DEVICE, BROWSER, CREATEDON, STATUS, GAMEBOARD_NUMBER 
								FROM {TABLE_NEGATIVE_FEEDBACK} 
								WHERE ID = {feedbackId} AND status<>{(int)MessageStatus.DELETED}
							;";

				PersistenceMessage notification = null;
				using (MySqlConnection connection = new MySqlConnection(connectionString))
				{
					try
					{
						connection.Open();
						using (MySqlCommand command = new MySqlCommand(sql, connection))
						{
							var dataReader = command.ExecuteReader();

							while (dataReader.Read())
							{
								int id = dataReader.GetInt32(0);
								string message = dataReader.GetString(1);
								string account = dataReader.GetString(2);
								string device = dataReader.GetString(3);
								string browser = dataReader.GetString(4);
								DateTime createdOn = dataReader.GetDateTime(5);
								int statusNumber = dataReader.GetByte(6);
								MessageStatus status;
								bool isValid = Enum.TryParse(statusNumber.ToString(), true, out status);
								if (!isValid)
								{
									throw new GameEngineException($@"There is no status for number {statusNumber}");
								}
								string gameboardNumber = dataReader.IsDBNull(7) ? null : dataReader.GetString(7);

								notification = new PersistenceMessage()
								{
									Id = id,
									Body = message,
									AccountNumber = account,
									CreationDate = createdOn,
									IsRead = status == MessageStatus.READ,
									GameboardNumber = gameboardNumber,
									HasGameboardAttached = !string.IsNullOrWhiteSpace(gameboardNumber),
									FeedbackType = FeedbackType.Negative,
									BrowserName = browser,
									DeviceName = device
								};

								DateTime repliedDate;
								bool hasAReply;
								RetrieveReplyBasedOnFeedback(store, id, out repliedDate, out hasAReply);
								notification.RepliedDate = repliedDate;
								notification.HasAReply = hasAReply;
							}
							dataReader.Close();
						}
					}
					catch (Exception e)
					{
						throw new GameEngineException("MySQL Error [" + e + "]");
					}
					finally
					{
						connection.Close();
					}
				}
				return notification;
			}

			internal override IEnumerable<Message> PositiveFeedback(Company company, Store store)
			{
				string sql = $@"SELECT ID, MESSAGE, ACCOUNT, CREATEDON, STATUS, GAMEBOARD_NUMBER 
								FROM PositiveFeedback 
								WHERE STORE = {store.Id} AND status<>{(int)MessageStatus.DELETED}
								ORDER BY CREATEDON desc;";
				List<Message> result = new List<Message>();
				using (MySqlConnection connection = new MySqlConnection(connectionString))
				{
					try
					{
						connection.Open();
						using (MySqlCommand command = new MySqlCommand(sql, connection))
						{
							var dataReader = command.ExecuteReader();

							while (dataReader.Read())
							{
								int id = dataReader.GetInt32(0);
								string message = dataReader.GetString(1);
								string account = dataReader.GetString(2);
								DateTime createdOn = dataReader.GetDateTime(3);
								int statusNumber = dataReader.GetByte(4);
								MessageStatus status;
								bool isValid = Enum.TryParse("" + statusNumber, true, out status);
								if (!isValid)
								{
									throw new GameEngineException($@"There is no status for number {statusNumber}");
								}
								string gameboardNumber = dataReader.IsDBNull(5) ? null : dataReader.GetString(5);

								Customer customer = company.CustomerByAccountNumber(account);
								FeedbackMessage notification = new ThumbsUpFeedback(customer.Player, message);
								notification.CreationDate = createdOn;
								if (!string.IsNullOrWhiteSpace(gameboardNumber))
								{
									notification.GameboardNumber = gameboardNumber;
									notification.HasGameboardAttached = true;
								}

								var feedbackWithReply = RetrieveReplyBasedOnFeedback(notification, store.Id, id);
								result.Add(notification);
							}
							dataReader.Close();
						}
					}
					catch (Exception e)
					{
						throw new GameEngineException("MySQL Error [" + e + "]");
					}
					finally
					{
						connection.Close();
					}
				}
				return result;
			}

			internal override IEnumerable<Message> NegativeFeedback(Company company, Store store)
			{
				string sql = $@"SELECT ID, MESSAGE, ACCOUNT, DEVICE, BROWSER, CREATEDON, STATUS, GAMEBOARD_NUMBER 
								FROM NegativeFeedback 
								WHERE STORE = {store.Id} AND status<>{(int)MessageStatus.DELETED}
								ORDER BY CREATEDON desc;";
				List<Message> result = new List<Message>();
				using (MySqlConnection connection = new MySqlConnection(connectionString))
				{
					try
					{
						connection.Open();
						using (MySqlCommand command = new MySqlCommand(sql, connection))
						{
							var dataReader = command.ExecuteReader();

							while (dataReader.Read())
							{
								int id = dataReader.GetInt32(0);
								string message = dataReader.GetString(1);
								string account = dataReader.GetString(2);
								string device = dataReader.GetString(3);
								string browser = dataReader.GetString(4);
								DateTime createdOn = dataReader.GetDateTime(5);
								int statusNumber = dataReader.GetByte(6);
								MessageStatus status;
								bool isValid = Enum.TryParse("" + statusNumber, true, out status);
								if (!isValid)
								{
									throw new GameEngineException($@"There is no status for number {statusNumber}");
								}
								string gameboardNumber = dataReader.IsDBNull(7) ? null : dataReader.GetString(7);

								Customer customer = company.CustomerByAccountNumber(account);
								FeedbackMessage notification = new ThumbsDownFeedback(customer.Player, message, device, browser);
								notification.CreationDate = createdOn;
								if (!string.IsNullOrWhiteSpace(gameboardNumber))
								{
									notification.GameboardNumber = gameboardNumber;
									notification.HasGameboardAttached = true;
								}

								var feedbackWithReply = RetrieveReplyBasedOnFeedback(notification, store.Id, id);
								result.Add(notification);
							}
							dataReader.Close();
						}
					}
					catch (Exception e)
					{
						throw new GameEngineException("MySQL Error [" + e + "]");
					}
					finally
					{
						connection.Close();
					}
				}
				return result;
			}

			internal override PagedMessages PositiveFeedbacks(Company company, Store store, DateTime startDate, DateTime endDate, int initialIndex, int amountOfRows)
			{
				var isPaginationRequired = initialIndex != 0 || amountOfRows != 0;
				var linesToPage = string.Empty;
				if (isPaginationRequired)
				{
					linesToPage = $"LIMIT {amountOfRows} OFFSET {initialIndex}";
				}
				string sql = $@"SELECT ID, MESSAGE, ACCOUNT, CREATEDON, STATUS, GAMEBOARD_NUMBER, COUNT(*) OVER()
								FROM {TABLE_POSITIVE_FEEDBACK} 
								WHERE STORE = {store.Id} AND DATE(CREATEDON) <= '{ToFullDateTimeString(endDate)}' AND DATE(CREATEDON) >= '{ToFullDateTimeString(startDate)}' AND status<>{(int)MessageStatus.DELETED}
								{linesToPage}
								;";

				var result = new List<PersistenceMessage>();
				int recordsTotal = 0;
				using (MySqlConnection connection = new MySqlConnection(connectionString))
				{
					try
					{
						connection.Open();
						using (MySqlCommand command = new MySqlCommand(sql, connection))
						{
							var dataReader = command.ExecuteReader();

							while (dataReader.Read())
							{
								int id = dataReader.GetInt32(0);
								string message = dataReader.GetString(1);
								string account = dataReader.GetString(2);
								DateTime createdOn = dataReader.GetDateTime(3);
								int statusNumber = dataReader.GetByte(4);
								MessageStatus status;
								bool isValid = Enum.TryParse(statusNumber.ToString(), true, out status);
								if (!isValid)
								{
									throw new GameEngineException($@"There is no status for number {statusNumber}");
								}
								string gameboardNumber = dataReader.IsDBNull(5) ? null : dataReader.GetString(5);
								recordsTotal = dataReader.GetInt32(6);

								var notification = new PersistenceMessage()
								{
									Id = id,
									Body = message,
									AccountNumber = account,
									CreationDate = createdOn,
									IsRead = status == MessageStatus.READ,
									GameboardNumber = gameboardNumber,
									HasGameboardAttached = !string.IsNullOrWhiteSpace(gameboardNumber),
									FeedbackType = FeedbackType.Positive
								};

								DateTime repliedDate;
								bool hasAReply;
								RetrieveReplyBasedOnFeedback(store, id, out repliedDate, out hasAReply);
								notification.RepliedDate = repliedDate;
								notification.HasAReply = hasAReply;

								result.Add(notification);
							}
							dataReader.Close();
						}
					}
					catch (Exception e)
					{
						throw new GameEngineException("MySQL Error [" + e + "]");
					}
					finally
					{
						connection.Close();
					}
				}
				return new PagedMessages(result, recordsTotal);
			}

			internal override PagedMessages NegativeFeedbacks(Company company, Store store, DateTime startDate, DateTime endDate, int initialIndex, int amountOfRows)
			{
				var isPaginationRequired = initialIndex != 0 || amountOfRows != 0;
				var linesToPage = string.Empty;
				if (isPaginationRequired)
				{
					linesToPage = $"LIMIT {amountOfRows} OFFSET {initialIndex}";
				}
				string sql = $@"SELECT ID, MESSAGE, ACCOUNT, DEVICE, BROWSER, CREATEDON, STATUS, GAMEBOARD_NUMBER, COUNT(*) OVER()
								FROM {TABLE_NEGATIVE_FEEDBACK} 
								WHERE STORE = {store.Id} AND DATE(CREATEDON) <= '{ToFullDateTimeString(endDate)}' AND DATE(CREATEDON) >= '{ToFullDateTimeString(startDate)}' AND status<>{(int)MessageStatus.DELETED}
								{linesToPage}
							;";

				var result = new List<PersistenceMessage>();
				int recordsTotal = 0;
				using (MySqlConnection connection = new MySqlConnection(connectionString))
				{
					try
					{
						connection.Open();
						using (MySqlCommand command = new MySqlCommand(sql, connection))
						{
							var dataReader = command.ExecuteReader();

							while (dataReader.Read())
							{
								int id = dataReader.GetInt32(0);
								string message = dataReader.GetString(1);
								string account = dataReader.GetString(2);
								string device = dataReader.GetString(3);
								string browser = dataReader.GetString(4);
								DateTime createdOn = dataReader.GetDateTime(5);
								int statusNumber = dataReader.GetByte(6);
								MessageStatus status;
								bool isValid = Enum.TryParse(statusNumber.ToString(), true, out status);
								if (!isValid)
								{
									throw new GameEngineException($@"There is no status for number {statusNumber}");
								}
								string gameboardNumber = dataReader.IsDBNull(7) ? null : dataReader.GetString(7);
								recordsTotal = dataReader.GetInt32(8);

								var notification = new PersistenceMessage()
								{
									Id = id,
									Body = message,
									AccountNumber = account,
									CreationDate = createdOn,
									IsRead = status == MessageStatus.READ,
									GameboardNumber = gameboardNumber,
									HasGameboardAttached = !string.IsNullOrWhiteSpace(gameboardNumber),
									FeedbackType = FeedbackType.Negative,
									BrowserName = browser,
									DeviceName = device
								};

								DateTime repliedDate;
								bool hasAReply;
								RetrieveReplyBasedOnFeedback(store, id, out repliedDate, out hasAReply);
								notification.RepliedDate = repliedDate;
								notification.HasAReply = hasAReply;

								result.Add(notification);
							}
							dataReader.Close();
						}
					}
					catch (Exception e)
					{
						throw new GameEngineException("MySQL Error [" + e + "]");
					}
					finally
					{
						connection.Close();
					}
				}
				return new PagedMessages(result, recordsTotal);
			}

			internal override PagedMessages AllFeedbacks(Company company, Store store, DateTime startDate, DateTime endDate, int initialIndex, int amountOfRows)
			{
				var isPaginationRequired = initialIndex != 0 || amountOfRows != 0;
				var linesToPage = string.Empty;
				if (isPaginationRequired)
				{
					linesToPage = $"LIMIT {amountOfRows} OFFSET {initialIndex}";
				}
				string sql = $@"SELECT ID, MESSAGE, ACCOUNT, DEVICE, BROWSER, CREATEDON, STATUS, GAMEBOARD_NUMBER, COUNT(*) OVER()
								FROM (
									SELECT ID, MESSAGE, ACCOUNT, DEVICE, BROWSER, CREATEDON, STATUS, GAMEBOARD_NUMBER
									FROM {TABLE_NEGATIVE_FEEDBACK} 
									WHERE STORE = {store.Id} AND DATE(CREATEDON) <= '{ToFullDateTimeString(endDate)}' AND DATE(CREATEDON) >= '{ToFullDateTimeString(startDate)}' AND status<>{(int)MessageStatus.DELETED}
									UNION
									SELECT ID, MESSAGE, ACCOUNT, NULL AS DEVICE, NULL AS BROWSER, CREATEDON, STATUS, GAMEBOARD_NUMBER
									FROM {TABLE_POSITIVE_FEEDBACK} 
									WHERE STORE = {store.Id} AND DATE(CREATEDON) <= '{ToFullDateTimeString(endDate)}' AND DATE(CREATEDON) >= '{ToFullDateTimeString(startDate)}' AND status<>{(int)MessageStatus.DELETED}
								) t
								{linesToPage}
							;";

				var result = new List<PersistenceMessage>();
				int recordsTotal = 0;
				using (MySqlConnection connection = new MySqlConnection(connectionString))
				{
					try
					{
						connection.Open();
						using (MySqlCommand command = new MySqlCommand(sql, connection))
						{
							var dataReader = command.ExecuteReader();

							while (dataReader.Read())
							{
								int id = dataReader.GetInt32(0);
								string message = dataReader.GetString(1);
								string account = dataReader.GetString(2);
								string device = dataReader.IsDBNull(3) ? null : dataReader.GetString(3);
								string browser = dataReader.IsDBNull(4) ? null : dataReader.GetString(4);
								DateTime createdOn = dataReader.GetDateTime(5);
								int statusNumber = dataReader.GetByte(6);
								MessageStatus status;
								bool isValid = Enum.TryParse(statusNumber.ToString(), true, out status);
								if (!isValid)
								{
									throw new GameEngineException($@"There is no status for number {statusNumber}");
								}
								string gameboardNumber = dataReader.IsDBNull(7) ? null : dataReader.GetString(7);
								recordsTotal = dataReader.GetInt32(8);

								var isPositive = device == null && browser == null;
								var notification = new PersistenceMessage()
								{
									Id = id,
									Body = message,
									AccountNumber = account,
									CreationDate = createdOn,
									IsRead = status == MessageStatus.READ,
									GameboardNumber = gameboardNumber,
									HasGameboardAttached = !string.IsNullOrWhiteSpace(gameboardNumber),
									FeedbackType = isPositive ? FeedbackType.Positive : FeedbackType.Negative,
									BrowserName = browser,
									DeviceName = device
								};

								DateTime repliedDate;
								bool hasAReply;
								RetrieveReplyBasedOnFeedback(store, id, out repliedDate, out hasAReply);
								notification.RepliedDate = repliedDate;
								notification.HasAReply = hasAReply;

								result.Add(notification);
							}
							dataReader.Close();
						}
					}
					catch (Exception e)
					{
						throw new GameEngineException("MySQL Error [" + e + "]");
					}
					finally
					{
						connection.Close();
					}
				}
				return new PagedMessages(result, recordsTotal);
			}
		}

        private class LoyaltyStorageSQLServer : LoyaltyStorage
        {

            internal LoyaltyStorageSQLServer(string connectionString) : base(connectionString)
            {
                if (!ExistTable())
                {
                    CrearStorage();
                }
            }

            private bool ExistTable()
            {
                bool exists = false;
                StringBuilder statement = new StringBuilder();

                statement.Append("IF EXISTS(")
                    .Append("SELECT 1 FROM INFORMATION_SCHEMA.TABLES ")
                    .Append("WHERE TABLE_NAME = '" + TABLE_POSITIVE_FEEDBACK + "')")
                    .Append("SELECT 1 ELSE SELECT 0;");
                string sql = statement.ToString();

                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    try
                    {
                        connection.Open();
                        using (SqlCommand command = new SqlCommand(sql, connection))
                        {
                            var result = (int)command.ExecuteScalar();
                            exists = result == 1;
                        }
                    }
                    catch
                    {
                        exists = false;
                    }
                    finally
                    {
                        connection.Close();
                    }
                }
                return exists;
            }

            private void CrearStorage()
            {
                StringBuilder statement = new StringBuilder();

                statement
                    .Append("create table ").Append(TABLE_MESSAGES)
                    .Append("(")
                    .Append("ID INT IDENTITY(1,1),")
                    .Append("MESSAGE VARCHAR(512) NOT NULL,")
                    .Append(");");
                statement
                    .Append("create table ").Append(TABLE_POSITIVE_FEEDBACK)
                    .Append("(")
                    .Append("ID INT NOT NULL,")
                    .Append("MESSAGE VARCHAR(1024) NOT NULL,")
                    .Append($"ACCOUNT VARCHAR({Customer.DATABASE_FIELD_ACCOUNT_LENGHT}) NOT NULL,")
                    .Append("CREATEDON DATETIME NOT NULL,")
					.Append("STATUS TINYINT NOT NULL,")
					.Append("STORE TINYINT NOT NULL,")
					.Append("GAMEBOARD_NUMBER VARCHAR(45)")
					.Append(");");
                statement
                    .Append("create table ").Append(TABLE_NEGATIVE_FEEDBACK)
                    .Append("(")
                    .Append("ID INT NOT NULL,")
                    .Append("MESSAGE VARCHAR(1024) NOT NULL,")
                    .Append($"ACCOUNT VARCHAR({Customer.DATABASE_FIELD_ACCOUNT_LENGHT}) NOT NULL,")
					.Append("DEVICE VARCHAR(100) NOT NULL,")
					.Append("BROWSER VARCHAR(100) NOT NULL,")
					.Append("CREATEDON DATETIME NOT NULL,")
					.Append("STATUS TINYINT NOT NULL,")
					.Append("STORE TINYINT NOT NULL,")
					.Append("GAMEBOARD_NUMBER VARCHAR(45)")
					.Append(");");
                statement
                    .Append("create table ").Append(TABLE_REPLY_MESSAGE)
                    .Append("(")
                    .Append("ID INT NOT NULL,")
                    .Append("MESSAGE INT NOT NULL,")
                    .Append($"ACCOUNT VARCHAR({Customer.DATABASE_FIELD_ACCOUNT_LENGHT}) NOT NULL,")
					.Append($"FEEDBACK INT NOT NULL,")
					.Append("CREATEDON DATETIME NOT NULL,")
					.Append("STORE TINYINT NOT NULL,")
					.Append(");");
                statement
                    .Append("create table ").Append(TABLE_SEGMENT)
                    .Append("(")
                    .Append("ID INT IDENTITY(1,1),")
                    .Append("EXPRESSION VARCHAR(500) NOT NULL,")
					.Append("CAMPAIGN INT NOT NULL,")
					.Append($"ACCOUNT VARCHAR(MAX) NOT NULL,")
					.Append("CREATEDON DATETIME NOT NULL,")
                    .Append(");");
                statement
                    .Append("create table ").Append(TABLE_NOTIFICATIONS_SEGMENT)
                    .Append("(")
                    .Append("ID INT IDENTITY(1,1),")
                    .Append("SEGMENT INT NOT NULL,")
                    .Append("MESSAGE INT NOT NULL,")
                    .Append($"ACCOUNT VARCHAR({Customer.DATABASE_FIELD_ACCOUNT_LENGHT}) NOT NULL,")
                    .Append("STATUS TINYINT NOT NULL,")
                    .Append("CREATEDON DATETIME NOT NULL,")
                    .Append("READON DATETIME NULL,")
                    .Append("SENTON DATETIME NULL,")
                    .Append("SENTBY VARCHAR(50) NULL,")
                    .Append(");");

                statement.Append($"ALTER TABLE {TABLE_MESSAGES} ADD CONSTRAINT PK_{TABLE_MESSAGES} PRIMARY KEY (ID);");
                statement.Append($"ALTER TABLE {TABLE_POSITIVE_FEEDBACK} ADD CONSTRAINT PK_{TABLE_POSITIVE_FEEDBACK} PRIMARY KEY (ID);");
                statement.Append($"ALTER TABLE {TABLE_NEGATIVE_FEEDBACK} ADD CONSTRAINT PK_{TABLE_NEGATIVE_FEEDBACK} PRIMARY KEY (ID);");
                statement.Append($"ALTER TABLE {TABLE_REPLY_MESSAGE} ADD CONSTRAINT PK_{TABLE_REPLY_MESSAGE} PRIMARY KEY (ID);");
                statement.Append($"ALTER TABLE {TABLE_SEGMENT} ADD CONSTRAINT PK_{TABLE_SEGMENT} PRIMARY KEY (ID);");
                statement.Append($"ALTER TABLE {TABLE_NOTIFICATIONS_SEGMENT} ADD CONSTRAINT PK_{TABLE_NOTIFICATIONS_SEGMENT} PRIMARY KEY (ID);");
                string sql = statement.ToString();
                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    try
                    {
                        connection.Open();
                        using (SqlCommand command = new SqlCommand(sql, connection))
                        {
                            command.ExecuteNonQuery();
                        }
                    }
                    finally
                    {
                        connection.Close();
                    }
                }
            }

			internal override int StoreNotification(string message)
			{
                string record = $"INSERT INTO {TABLE_MESSAGES} (message) VALUES ('{message}'); SELECT SCOPE_IDENTITY() AS SCOPE_IDENTITY;";
				int messageIdCreated = 0;

				using (SqlConnection connection = new SqlConnection(connectionString))
				{
					try
					{
						connection.Open();
						using (SqlCommand command = new SqlCommand(record, connection))
						{
							command.CommandType = CommandType.Text;
							messageIdCreated= Convert.ToInt32(command.ExecuteScalar());
						}
					}
					catch
					{
						throw new GameEngineException("SQLServer Error [" + record + "]");
					}
					finally
					{
						connection.Close();
					}
				}

				return messageIdCreated;
			}

			internal override int GetMessageIdFor(string message)
			{
				int result = -1;
				var command = $@"select Id from {TABLE_MESSAGES} where message = '{message}';";
				using (SqlConnection connection = new SqlConnection(connectionString))
				{
					connection.Open();
					using (SqlCommand cmd = new SqlCommand(command, connection))
					{
						using (SqlDataReader reader = cmd.ExecuteReader())
						{
							while (reader.Read())
							{
								result = reader.GetInt32(0);
							}
						}
					}
					connection.Close();
				}
				return result;
			}

			internal override int StoreSegment(string expression, int campaignId, string account, DateTime createdOn)
			{
				var date = ToDateString(createdOn.Year, createdOn.Month, createdOn.Day, createdOn.Hour, createdOn.Minute, createdOn.Second);
				string record = $@"INSERT INTO {TABLE_SEGMENT} (expression, campaign, account, createdon) VALUES ('{expression}',
									{campaignId},
									'{account}',
									'{date}'
								); SELECT SCOPE_IDENTITY() AS SCOPE_IDENTITY;";
				int segmentIdCreated = 0;

				using (SqlConnection connection = new SqlConnection(connectionString))
				{
					try
					{
						connection.Open();
						using (SqlCommand command = new SqlCommand(record, connection))
						{
							command.CommandType = CommandType.Text;
							segmentIdCreated = Convert.ToInt32(command.ExecuteScalar());
						}
					}
					catch
					{
						throw new GameEngineException("SQLServer Error [" + record + "]");
					}
					finally
					{
						connection.Close();
					}
				}
				return segmentIdCreated;
			}

            internal override void StoreNotificationsSegment(int segmentId, int notificationId, string accountNumber, int status, DateTime createdOn)
			{
				var date = ToDateString(createdOn.Year, createdOn.Month, createdOn.Day, createdOn.Hour, createdOn.Minute, createdOn.Second);
				string record = $@"INSERT INTO {TABLE_NOTIFICATIONS_SEGMENT} (segment, message, account, status, createdon) VALUES ({segmentId},
									{notificationId},
									'{accountNumber}',
									{status},
									'{date}'
								)";

				using (SqlConnection connection = new SqlConnection(connectionString))
				{
					try
					{
						connection.Open();
						using (SqlCommand command = new SqlCommand(record, connection))
						{
							command.CommandType = CommandType.Text;
							command.ExecuteNonQuery();
						}
					}
					catch
					{
						throw new GameEngineException("SQLServer Error [" + record + "]");
					}
					finally
					{
						connection.Close();
					}
				}
			}

			internal override void StorePositiveFeedback(int feedbackId, string message, string accountNumber, DateTime createdOn, int status, int storeId)
			{
				var date = ToDateString(createdOn.Year, createdOn.Month, createdOn.Day, createdOn.Hour, createdOn.Minute, createdOn.Second);
				string record = $@"INSERT INTO {TABLE_POSITIVE_FEEDBACK} (ID, MESSAGE, ACCOUNT, CREATEDON, STATUS, STORE) VALUES ({feedbackId},
									'{message}',
									'{accountNumber}',
									'{date}',
									'{status}',
									{storeId}
								)";

				using (SqlConnection connection = new SqlConnection(connectionString))
				{
					try
					{
						connection.Open();
						using (SqlCommand command = new SqlCommand(record, connection))
						{
							command.CommandType = CommandType.Text;
							command.ExecuteNonQuery();
						}
					}
					catch
					{
						throw new GameEngineException("SQLServer Error [" + record + "]");
					}
					finally
					{
						connection.Close();
					}
				}
			}

			internal override void StoreNegativeFeedback(int feedbackId, string message, string accountNumber, string device, string browser, DateTime createdOn, int status, int storeId)
			{
				var date = ToDateString(createdOn.Year, createdOn.Month, createdOn.Day, createdOn.Hour, createdOn.Minute, createdOn.Second);
				string record = $@"INSERT INTO {TABLE_NEGATIVE_FEEDBACK} (ID, MESSAGE, ACCOUNT, DEVICE, BROWSER, CREATEDON, STATUS, STORE) VALUES ({feedbackId},
									'{message}',
									'{accountNumber}',
									'{device}',
									'{browser}',
									'{date}',
									'{status}',
									{storeId}
								)";

				using (SqlConnection connection = new SqlConnection(connectionString))
				{
					try
					{
						connection.Open();
						using (SqlCommand command = new SqlCommand(record, connection))
						{
							command.CommandType = CommandType.Text;
							command.ExecuteNonQuery();
						}
					}
					catch
					{
						throw new GameEngineException("SQLServer Error [" + record + "]");
					}
					finally
					{
						connection.Close();
					}
				}
			}

			internal override void StoreReplyMessages(int replyId, int messageId, string accountNumber, int feedbackId, DateTime repliedDate, int storeId)
			{
				var date = ToDateString(repliedDate.Year, repliedDate.Month, repliedDate.Day, repliedDate.Hour, repliedDate.Minute, repliedDate.Second);
				string record = $@"INSERT INTO {TABLE_REPLY_MESSAGE} (ID, MESSAGE, ACCOUNT, FEEDBACK, CREATEDON, STORE) VALUES ({replyId},
									{messageId},
									'{accountNumber}',
									{feedbackId},
									'{date}',
									{storeId}
								)";

				using (SqlConnection connection = new SqlConnection(connectionString))
				{
					try
					{
						connection.Open();
						using (SqlCommand command = new SqlCommand(record, connection))
						{
							command.CommandType = CommandType.Text;
							command.ExecuteNonQuery();
						}
					}
					catch
					{
						throw new GameEngineException("SQLServer Error [" + record + "]");
					}
					finally
					{
						connection.Close();
					}
				}
			}

			internal override void UpdateNotificationsSegment(int messageId, int segmentId, DateTime sentOn, string sentBy)
			{
				//TODO Ver como implementar el update y como hacer el readed on para la notificacion de un usuario y el status
				var sentDate = ToDateString(sentOn.Year, sentOn.Month, sentOn.Day, sentOn.Hour, sentOn.Minute, sentOn.Second);
				string record = $@"UPDATE {TABLE_NOTIFICATIONS_SEGMENT} SET SENTON = '{sentDate}', SENTBY = '{sentBy}' WHERE MESSAGE = {messageId} AND SEGMENT = {segmentId};";

				using (SqlConnection connection = new SqlConnection(connectionString))
				{
					try
					{
						connection.Open();
						using (SqlCommand command = new SqlCommand(record, connection))
						{
							command.CommandType = CommandType.Text;
							command.ExecuteNonQuery();
						}
					}
					catch
					{
						throw new GameEngineException("SQLServer Error [" + record + "]");
					}
					finally
					{
						connection.Close();
					}
				}
			}

			internal override void UpdateNotificationsSegment(int messageId, DateTime readOn, int status)
			{
				var readDate = ToDateString(readOn.Year, readOn.Month, readOn.Day, readOn.Hour, readOn.Minute, readOn.Second);
				string record = $@"UPDATE {TABLE_NOTIFICATIONS_SEGMENT} SET READON = '{readDate}', STATUS = '{status}' WHERE MESSAGE = {messageId};";

				using (SqlConnection connection = new SqlConnection(connectionString))
				{
					try
					{
						connection.Open();
						using (SqlCommand command = new SqlCommand(record, connection))
						{
							command.CommandType = CommandType.Text;
							command.ExecuteNonQuery();
						}
					}
					catch
					{
						throw new GameEngineException("SQLServer Error [" + record + "]");
					}
					finally
					{
						connection.Close();
					}
				}
			}

			internal override void UpdatePositiveFeedback(int feedbackId, int status)
			{
				string record = $@"UPDATE {TABLE_POSITIVE_FEEDBACK} SET STATUS = '{status}' WHERE ID = {feedbackId};";

				using (SqlConnection connection = new SqlConnection(connectionString))
				{
					try
					{
						connection.Open();
						using (SqlCommand command = new SqlCommand(record, connection))
						{
							command.CommandType = CommandType.Text;
							command.ExecuteNonQuery();
						}
					}
					catch
					{
						throw new GameEngineException("SQLServer Error [" + record + "]");
					}
					finally
					{
						connection.Close();
					}
				}
			}

			internal override void UpdateNegativeFeedback(int feedbackId, int status)
			{
				string record = $@"UPDATE {TABLE_NEGATIVE_FEEDBACK} SET STATUS = '{status}' WHERE ID = {feedbackId};";

				using (SqlConnection connection = new SqlConnection(connectionString))
				{
					try
					{
						connection.Open();
						using (SqlCommand command = new SqlCommand(record, connection))
						{
							command.CommandType = CommandType.Text;
							command.ExecuteNonQuery();
						}
					}
					catch
					{
						throw new GameEngineException("SQLServer Error [" + record + "]");
					}
					finally
					{
						connection.Close();
					}
				}
			}

			internal override void AttachGameboardNumberOnPositiveFeedback(int feedbackId, string gameboardNumber)
			{
				string record = $@"UPDATE {TABLE_POSITIVE_FEEDBACK} SET GAMEBOARD_NUMBER = '{gameboardNumber}' WHERE ID = {feedbackId};";

				using (SqlConnection connection = new SqlConnection(connectionString))
				{
					try
					{
						connection.Open();
						using (SqlCommand command = new SqlCommand(record, connection))
						{
							command.CommandType = CommandType.Text;
							command.ExecuteNonQuery();
						}
					}
					catch
					{
						throw new GameEngineException("SQLServer Error [" + record + "]");
					}
					finally
					{
						connection.Close();
					}
				}
			}

			internal override void AttachGameboardNumberOnNegativeFeedback(int feedbackId, string gameboardNumber)
			{
				string record = $@"UPDATE {TABLE_NEGATIVE_FEEDBACK} SET GAMEBOARD_NUMBER = '{gameboardNumber}' WHERE ID = {feedbackId};";

				using (SqlConnection connection = new SqlConnection(connectionString))
				{
					try
					{
						connection.Open();
						using (SqlCommand command = new SqlCommand(record, connection))
						{
							command.CommandType = CommandType.Text;
							command.ExecuteNonQuery();
						}
					}
					catch
					{
						throw new GameEngineException("SQLServer Error [" + record + "]");
					}
					finally
					{
						connection.Close();
					}
				}
			}

			private Message RetrieveReplyBasedOnFeedback(Message feedbackMessage, int storeId, int feedbackId)
			{
				string sql = $@"SELECT TOP 1 CREATEDON FROM ReplyMessage WHERE STORE = {storeId} AND FEEDBACK = {feedbackId} ORDER BY CREATEDON desc;";
				using (SqlConnection connection = new SqlConnection(connectionString))
				{
					try
					{
						connection.Open();
						using (SqlCommand command = new SqlCommand(sql, connection))
						{
							var dataReader = command.ExecuteReader();

							while (dataReader.Read())
							{
								DateTime createdOn = dataReader.GetDateTime(0);

								feedbackMessage.RepliedDate = createdOn;
								feedbackMessage.HasAReply = true;
							}
							dataReader.Close();
						}
					}
					finally
					{
						connection.Close();
					}
				}
				return feedbackMessage;
			}

			protected override void RetrieveReplyBasedOnFeedback(Store store, int feedbackId, out DateTime repliedDate, out bool hasAReply)
			{
				repliedDate = default(DateTime);
				hasAReply = false;
				string sql = $@"SELECT TOP 1 CREATEDON 
								FROM {TABLE_REPLY_MESSAGE} 
								WHERE STORE = {store.Id} AND FEEDBACK = {feedbackId} 
								ORDER BY CREATEDON desc
							;";
				using (SqlConnection connection = new SqlConnection(connectionString))
				{
					try
					{
						connection.Open();
						using (SqlCommand command = new SqlCommand(sql, connection))
						{
							var dataReader = command.ExecuteReader();

							while (dataReader.Read())
							{
								DateTime createdOn = dataReader.GetDateTime(0);

								repliedDate = createdOn;
								hasAReply = true;
							}
							dataReader.Close();
						}
					}
					catch (Exception e)
					{
						throw new GameEngineException("SQLServer Error [" + e + "]");
					}
					finally
					{
						connection.Close();
					}
				}
			}

			internal override PersistenceMessage RetrievePositiveFeedback(Company company, Store store, int feedbackId)
			{
				string sql = $@"SELECT TOP 1 ID, MESSAGE, ACCOUNT, CREATEDON, STATUS, GAMEBOARD_NUMBER
								FROM {TABLE_POSITIVE_FEEDBACK} 
								WHERE ID = {feedbackId}
								;";

				PersistenceMessage notification = null;
				using (SqlConnection connection = new SqlConnection(connectionString))
				{
					try
					{
						connection.Open();
						using (SqlCommand command = new SqlCommand(sql, connection))
						{
							var dataReader = command.ExecuteReader();

							while (dataReader.Read())
							{
								int id = dataReader.GetInt32(0);
								string message = dataReader.GetString(1);
								string account = dataReader.GetString(2);
								DateTime createdOn = dataReader.GetDateTime(3);
								int statusNumber = dataReader.GetByte(4);
								MessageStatus status;
								bool isValid = Enum.TryParse(statusNumber.ToString(), true, out status);
								if (!isValid)
								{
									throw new GameEngineException($@"There is no status for number {statusNumber}");
								}
								string gameboardNumber = dataReader.IsDBNull(5) ? null : dataReader.GetString(5);

								notification = new PersistenceMessage()
								{
									Id = id,
									Body = message,
									AccountNumber = account,
									CreationDate = createdOn,
									IsRead = status == MessageStatus.READ,
									GameboardNumber = gameboardNumber,
									HasGameboardAttached = !string.IsNullOrWhiteSpace(gameboardNumber),
									FeedbackType = FeedbackType.Positive
								};

								DateTime repliedDate;
								bool hasAReply;
								RetrieveReplyBasedOnFeedback(store, id, out repliedDate, out hasAReply);
								notification.RepliedDate = repliedDate;
								notification.HasAReply = hasAReply;
							}
							dataReader.Close();
						}
					}
					catch (Exception e)
					{
						throw new GameEngineException("SQLServer Error [" + e + "]");
					}
					finally
					{
						connection.Close();
					}
				}
				return notification;
			}

			internal override PersistenceMessage RetrieveNegativeFeedback(Company company, Store store, int feedbackId)
			{
				string sql = $@"SELECT TOP 1 ID, MESSAGE, ACCOUNT, DEVICE, BROWSER, CREATEDON, STATUS, GAMEBOARD_NUMBER 
								FROM {TABLE_NEGATIVE_FEEDBACK} 
								WHERE ID = {feedbackId} AND status<>{(int)MessageStatus.DELETED}
							;";

				PersistenceMessage notification = null;
				using (SqlConnection connection = new SqlConnection(connectionString))
				{
					try
					{
						connection.Open();
						using (SqlCommand command = new SqlCommand(sql, connection))
						{
							var dataReader = command.ExecuteReader();

							while (dataReader.Read())
							{
								int id = dataReader.GetInt32(0);
								string message = dataReader.GetString(1);
								string account = dataReader.GetString(2);
								string device = dataReader.GetString(3);
								string browser = dataReader.GetString(4);
								DateTime createdOn = dataReader.GetDateTime(5);
								int statusNumber = dataReader.GetByte(6);
								MessageStatus status;
								bool isValid = Enum.TryParse(statusNumber.ToString(), true, out status);
								if (!isValid)
								{
									throw new GameEngineException($@"There is no status for number {statusNumber}");
								}
								string gameboardNumber = dataReader.IsDBNull(7) ? null : dataReader.GetString(7);

								notification = new PersistenceMessage()
								{
									Id = id,
									Body = message,
									AccountNumber = account,
									CreationDate = createdOn,
									IsRead = status == MessageStatus.READ,
									GameboardNumber = gameboardNumber,
									HasGameboardAttached = !string.IsNullOrWhiteSpace(gameboardNumber),
									FeedbackType = FeedbackType.Negative,
									BrowserName = browser,
									DeviceName = device
								};

								DateTime repliedDate;
								bool hasAReply;
								RetrieveReplyBasedOnFeedback(store, id, out repliedDate, out hasAReply);
								notification.RepliedDate = repliedDate;
								notification.HasAReply = hasAReply;
							}
							dataReader.Close();
						}
					}
					catch (Exception e)
					{
						throw new GameEngineException("SQLServer Error [" + e + "]");
					}
					finally
					{
						connection.Close();
					}
				}
				return notification;
			}

			internal override IEnumerable<Message> PositiveFeedback(Company company, Store store)
			{
				string sql = $@"SELECT ID, MESSAGE, ACCOUNT, CREATEDON, STATUS, GAMEBOARD_NUMBER 
								FROM PositiveFeedback 
								WHERE STORE = {store.Id} AND status<>{(int)MessageStatus.DELETED}
								ORDER BY CREATEDON desc;";
				List<Message> result = new List<Message>();

				using (SqlConnection connection = new SqlConnection(connectionString))
				{
					try
					{
						connection.Open();
						using (SqlCommand command = new SqlCommand(sql, connection))
						{
							var dataReader = command.ExecuteReader();

							while (dataReader.Read())
							{
								int id = dataReader.GetInt32(0);
								string message = dataReader.GetString(1);
								string account = dataReader.GetString(2);
								DateTime createdOn = dataReader.GetDateTime(3);
								int statusNumber = dataReader.GetByte(4);
								MessageStatus status;
								bool isValid = Enum.TryParse("" + statusNumber, true, out status);
								if (!isValid)
								{
									throw new GameEngineException($@"There is no status for number {statusNumber}");
								}
								string gameboardNumber = dataReader.IsDBNull(5) ? null : dataReader.GetString(5);

								Customer customer = company.CustomerByAccountNumber(account);
								FeedbackMessage notification = new ThumbsUpFeedback(customer.Player, message);
								notification.CreationDate = createdOn;
								if (!string.IsNullOrWhiteSpace(gameboardNumber))
								{
									notification.GameboardNumber = gameboardNumber;
									notification.HasGameboardAttached = true;
								}

								var feedbackWithReply = RetrieveReplyBasedOnFeedback(notification, store.Id, id);
								result.Add(notification);
							}
							dataReader.Close();
						}
					}
					catch (Exception e)
					{
						throw new GameEngineException("SQLServer Error [" + e + "]");
					}
					finally
					{
						connection.Close();
					}
				}
				return result;
			}

			internal override PagedMessages PositiveFeedbacks(Company company, Store store, DateTime startDate, DateTime endDate, int initialIndex, int amountOfRows)
			{
				var isPaginationRequired = initialIndex != 0 || amountOfRows != 0;
				var linesToPage = string.Empty;
				if (isPaginationRequired)
				{
					linesToPage = $@"OFFSET {initialIndex} ROWS
									FETCH NEXT {amountOfRows} ROWS ONLY";
				}
				string sql = $@"SELECT ID, MESSAGE, ACCOUNT, CREATEDON, STATUS, GAMEBOARD_NUMBER, COUNT(*) OVER()
								FROM {TABLE_POSITIVE_FEEDBACK} 
								WHERE STORE = {store.Id} AND (CREATEDON BETWEEN '{ToDayString(startDate)}' AND '{ToDayString(endDate.AddDays(1))}') AND status<>{(int)MessageStatus.DELETED}
								ORDER BY ID
								{linesToPage}
								;";

				var result = new List<PersistenceMessage>();
				int recordsTotal = 0;
				using (SqlConnection connection = new SqlConnection(connectionString))
				{
					try
					{
						connection.Open();
						using (SqlCommand command = new SqlCommand(sql, connection))
						{
							var dataReader = command.ExecuteReader();

							while (dataReader.Read())
							{
								int id = dataReader.GetInt32(0);
								string message = dataReader.GetString(1);
								string account = dataReader.GetString(2);
								DateTime createdOn = dataReader.GetDateTime(3);
								int statusNumber = dataReader.GetByte(4);
								MessageStatus status;
								bool isValid = Enum.TryParse(statusNumber.ToString(), true, out status);
								if (!isValid)
								{
									throw new GameEngineException($@"There is no status for number {statusNumber}");
								}
								string gameboardNumber = dataReader.IsDBNull(5) ? null : dataReader.GetString(5);
								recordsTotal = dataReader.GetInt32(6);

								var notification = new PersistenceMessage()
								{
									Id = id,
									Body = message,
									AccountNumber = account,
									CreationDate = createdOn,
									IsRead = status == MessageStatus.READ,
									GameboardNumber = gameboardNumber,
									HasGameboardAttached = !string.IsNullOrWhiteSpace(gameboardNumber),
									FeedbackType = FeedbackType.Positive
								};

								DateTime repliedDate;
								bool hasAReply;
								RetrieveReplyBasedOnFeedback(store, id, out repliedDate, out hasAReply);
								notification.RepliedDate = repliedDate;
								notification.HasAReply = hasAReply;

								result.Add(notification);
							}
							dataReader.Close();
						}
					}
					catch (Exception e)
					{
						throw new GameEngineException("SQLServer Error [" + e + "]");
					}
					finally
					{
						connection.Close();
					}
				}
				return new PagedMessages(result, recordsTotal);
			}

			internal override IEnumerable<Message> NegativeFeedback(Company company, Store store)
			{
				string sql = $@"SELECT ID, MESSAGE, ACCOUNT, DEVICE, BROWSER, CREATEDON, STATUS, GAMEBOARD_NUMBER FROM NegativeFeedback WHERE STORE = {store.Id} ORDER BY CREATEDON desc;";
				List<Message> result = new List<Message>();

				using (SqlConnection connection = new SqlConnection(connectionString))
				{
					try
					{
						connection.Open();
						using (SqlCommand command = new SqlCommand(sql, connection))
						{
							var dataReader = command.ExecuteReader();

							while (dataReader.Read())
							{
								int id = dataReader.GetInt32(0);
								string message = dataReader.GetString(1);
								string account = dataReader.GetString(2);
								string device = dataReader.GetString(3);
								string browser = dataReader.GetString(4);
								DateTime createdOn = dataReader.GetDateTime(5);
								int statusNumber = dataReader.GetByte(6);
								MessageStatus status;
								bool isValid = Enum.TryParse("" + statusNumber, true, out status);
								if (!isValid)
								{
									throw new GameEngineException($@"There is no status for number {statusNumber}");
								}
								string gameboardNumber = dataReader.IsDBNull(7) ? null : dataReader.GetString(7);

								Customer customer = company.CustomerByAccountNumber(account);
								FeedbackMessage notification = new ThumbsDownFeedback(customer.Player, message, device, browser);
								notification.CreationDate = createdOn;
								if (!string.IsNullOrWhiteSpace(gameboardNumber))
								{
									notification.GameboardNumber = gameboardNumber;
									notification.HasGameboardAttached = true;
								}

								var feedbackWithReply = RetrieveReplyBasedOnFeedback(notification, store.Id, id);
								result.Add(notification);
							}
							dataReader.Close();
						}
					}
					catch (Exception e)
					{
						throw new GameEngineException("SQLServer Error [" + e + "]");
					}
					finally
					{
						connection.Close();
					}
				}
				return result;
			}

			internal override PagedMessages NegativeFeedbacks(Company company, Store store, DateTime startDate, DateTime endDate, int initialIndex, int amountOfRows)
			{
				var isPaginationRequired = initialIndex != 0 || amountOfRows != 0;
				var linesToPage = string.Empty;
				if (isPaginationRequired)
				{
					linesToPage = $@"OFFSET {initialIndex} ROWS
									FETCH NEXT {amountOfRows} ROWS ONLY";
				}
				string sql = $@"SELECT ID, MESSAGE, ACCOUNT, DEVICE, BROWSER, CREATEDON, STATUS, GAMEBOARD_NUMBER, COUNT(*) OVER()
								FROM {TABLE_NEGATIVE_FEEDBACK} 
								WHERE STORE = {store.Id} AND (CREATEDON BETWEEN '{ToDayString(startDate)}' AND '{ToDayString(endDate.AddDays(1))}') AND status<>{(int)MessageStatus.DELETED}
								ORDER BY ID
								{linesToPage}
							;";

				var result = new List<PersistenceMessage>();
				int recordsTotal = 0;
				using (SqlConnection connection = new SqlConnection(connectionString))
				{
					try
					{
						connection.Open();
						using (SqlCommand command = new SqlCommand(sql, connection))
						{
							var dataReader = command.ExecuteReader();

							while (dataReader.Read())
							{
								int id = dataReader.GetInt32(0);
								string message = dataReader.GetString(1);
								string account = dataReader.GetString(2);
								string device = dataReader.GetString(3);
								string browser = dataReader.GetString(4);
								DateTime createdOn = dataReader.GetDateTime(5);
								int statusNumber = dataReader.GetByte(6);
								MessageStatus status;
								bool isValid = Enum.TryParse(statusNumber.ToString(), true, out status);
								if (!isValid)
								{
									throw new GameEngineException($@"There is no status for number {statusNumber}");
								}
								string gameboardNumber = dataReader.IsDBNull(7) ? null : dataReader.GetString(7);
								recordsTotal = dataReader.GetInt32(8);

								var notification = new PersistenceMessage()
								{
									Id = id,
									Body = message,
									AccountNumber = account,
									CreationDate = createdOn,
									IsRead = status == MessageStatus.READ,
									GameboardNumber = gameboardNumber,
									HasGameboardAttached = !string.IsNullOrWhiteSpace(gameboardNumber),
									FeedbackType = FeedbackType.Negative,
									BrowserName = browser,
									DeviceName = device
								};

								DateTime repliedDate;
								bool hasAReply;
								RetrieveReplyBasedOnFeedback(store, id, out repliedDate, out hasAReply);
								notification.RepliedDate = repliedDate;
								notification.HasAReply = hasAReply;

								result.Add(notification);
							}
							dataReader.Close();
						}
					}
					catch (Exception e)
					{
						throw new GameEngineException("SQLServer Error [" + e + "]");
					}
					finally
					{
						connection.Close();
					}
				}
				return new PagedMessages(result, recordsTotal);
			}

			internal override PagedMessages AllFeedbacks(Company company, Store store, DateTime startDate, DateTime endDate, int initialIndex, int amountOfRows)
			{
				var isPaginationRequired = initialIndex != 0 || amountOfRows != 0;
				var linesToPage = string.Empty;
				if (isPaginationRequired)
				{
					linesToPage = $@"OFFSET {initialIndex} ROWS
									FETCH NEXT {amountOfRows} ROWS ONLY";
				}
				string sql = $@"SELECT ID, MESSAGE, ACCOUNT, DEVICE, BROWSER, CREATEDON, STATUS, GAMEBOARD_NUMBER, COUNT(*) OVER()
								FROM (
									SELECT ID, MESSAGE, ACCOUNT, DEVICE, BROWSER, CREATEDON, STATUS, GAMEBOARD_NUMBER
									FROM {TABLE_NEGATIVE_FEEDBACK} 
									WHERE STORE = {store.Id} AND (CREATEDON BETWEEN '{ToDayString(startDate)}' AND '{ToDayString(endDate.AddDays(1))}') AND status<>{(int)MessageStatus.DELETED}
									UNION
									SELECT ID, MESSAGE, ACCOUNT, NULL AS DEVICE, NULL AS BROWSER, CREATEDON, STATUS, GAMEBOARD_NUMBER
									FROM {TABLE_POSITIVE_FEEDBACK} 
									WHERE STORE = {store.Id} AND (CREATEDON BETWEEN '{ToDayString(startDate)}' AND '{ToDayString(endDate.AddDays(1))}') AND status<>{(int)MessageStatus.DELETED}
								) t
								ORDER BY ID
								{linesToPage}
							;";

				var result = new List<PersistenceMessage>();
				int recordsTotal = 0;
				using (SqlConnection connection = new SqlConnection(connectionString))
				{
					try
					{
						connection.Open();
						using (SqlCommand command = new SqlCommand(sql, connection))
						{
							var dataReader = command.ExecuteReader();

							while (dataReader.Read())
							{
								int id = dataReader.GetInt32(0);
								string message = dataReader.GetString(1);
								string account = dataReader.GetString(2);
								string device = dataReader.IsDBNull(3) ? null : dataReader.GetString(3);
								string browser = dataReader.IsDBNull(4) ? null : dataReader.GetString(4);
								DateTime createdOn = dataReader.GetDateTime(5);
								int statusNumber = dataReader.GetByte(6);
								MessageStatus status;
								bool isValid = Enum.TryParse(statusNumber.ToString(), true, out status);
								if (!isValid)
								{
									throw new GameEngineException($@"There is no status for number {statusNumber}");
								}
								string gameboardNumber = dataReader.IsDBNull(7) ? null : dataReader.GetString(7);
								recordsTotal = dataReader.GetInt32(8);

								var isPositive = device == null && browser == null;
								var notification = new PersistenceMessage()
								{
									Id = id,
									Body = message,
									AccountNumber = account,
									CreationDate = createdOn,
									IsRead = status == MessageStatus.READ,
									GameboardNumber = gameboardNumber,
									HasGameboardAttached = !string.IsNullOrWhiteSpace(gameboardNumber),
									FeedbackType = isPositive ? FeedbackType.Positive : FeedbackType.Negative,
									BrowserName = browser,
									DeviceName = device
								};

								DateTime repliedDate;
								bool hasAReply;
								RetrieveReplyBasedOnFeedback(store, id, out repliedDate, out hasAReply);
								notification.RepliedDate = repliedDate;
								notification.HasAReply = hasAReply;

								result.Add(notification);
							}
							dataReader.Close();
						}
					}
					catch (Exception e)
					{
						throw new GameEngineException("SQLServer Error [" + e + "]");
					}
					finally
					{
						connection.Close();
					}
				}
				return new PagedMessages(result, recordsTotal);
			}
		}
    }
}
