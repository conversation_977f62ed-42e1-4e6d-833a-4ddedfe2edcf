﻿using GamesEngine;
using GamesEngine.Finance;
using GamesEngine.Settings;
using Puppeteer.EventSourcing;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using town.connectors.drivers;
using static GamesEngine.Exchange.PaymentProcessorsAndActionsByDomains;
using static GamesEngine.Finance.PaymentChannels;

namespace GamesEngineMocks
{
    public class KenoMockGenerator : Mock
    {
		private Puppeteer.EventSourcing.Actor actor;

		public String Perform(String script)
        {
            string result;
            try
            {
#pragma warning disable VSTHRD002 // Avoid problematic synchronous waits
				result = actor.PerformCmdAsync(script, IpAddress.DEFAULT, UserInLog.ANONYMOUS).Result;
#pragma warning restore VSTHRD002 // Avoid problematic synchronous waits
				result = CheckForAssert(result);
			}
			catch (Exception e)
			{
				throw e.InnerException;
			}
			return result;
		}

        public KenoMockGenerator(Puppeteer.EventSourcing.Actor actor)
        {
			this.actor = actor;
				Perform($@"
                    company = Company();
				    if (!company.System.Coins.ExistsIsoCode('FP'))
				    {{
					    coin = company.System.Coins.Add(0, 'FP', 'FP', 2, 'F', 'free play', {CoinType.Digital});
						coin.Visible = true;
					    coin.Enabled = true;
				    }}
				    if (!company.System.Coins.ExistsIsoCode('USD'))
				    {{
					    coin = company.System.Coins.Add(2, 'USD', '$', 2, '$', 'dollar', {CoinType.Fiat});
					    coin.Visible = true;
					    coin.Enabled = true;
				    }}
				    if (!company.System.Coins.ExistsIsoCode('BTC'))
				    {{
					    company.System.Coins.Add(3, 'BTC', 'BTC', 8, '₿', 'bitcoin', {CoinType.Crypt});
				    }}
				    if (!company.System.Coins.ExistsIsoCode('ETH'))
				    {{
					    company.System.Coins.Add(7, 'ETH', 'ETH', 8, 'Ξ', 'ethereum', {CoinType.Crypt});
				    }}

				    company.Accounting= RealAccounting();
                    companyIDf2401f84xa94ax4db3xaec4x83f5361730ae = company;
                    store = company.Sales.CreateStore(7,'Keno Store');
					store.Alias = 'keno';
					store.MakeCurrent();
                    lotteries = company.Lotto900();
                    keno = lotteries.GetKeno();

                    domain = company.Sales.CreateDomain(false, 1, 'localhost', {Agents.INSIDER});
                    company.Sales.CurrentStore.Add(domain);
                    company.Sales.CreateDomain(false, 2, 'ncubo.com', {Agents.INSIDER});

                    kenoSingleProd = lotteries.GetOrCreateProductById(1);
                    kenoSingleProd.Description = 'Simple Keno Ticket.';
                    kenoSingleProd.Price = 1;

                    kenoWithMultiplierProd = lotteries.GetOrCreateProductById(2);
                    kenoWithMultiplierProd.Description = 'Keno Ticket with multiplier.';
                    kenoWithMultiplierProd.Price = 1;

                    kenoWithBullEyeProd = lotteries.GetOrCreateProductById(3);
                    kenoWithBullEyeProd.Description = 'Keno Ticket with bulleye.';
                    kenoWithBullEyeProd.Price = 1;

                    kenoWithMultiplierAndBulleyeProd = lotteries.GetOrCreateProductById(4);
                    kenoWithMultiplierAndBulleyeProd.Description = 'Keno Ticket with multiplier and bulleye.';
                    kenoWithMultiplierAndBulleyeProd.Price = 1;

                    domain = company.Sales.DomainFrom('localhost');
                    betRangesKeno = lotteries.BetRangesKeno;
                    betRangesKeno.Add(Now, domain, 0.75, 0.25, 'Admin');
             
                    presetKeno = lotteries.PresetBetAmountsForKeno;
                    presetKeno.Add(0.5, 'Admin', Now);
                    lotteries.PlayersProfiles.Profiles.Add(domain, 'Test profile 1');

                    keno.Every('*','[0,10,20,30,40,50]','*', now, 'Admin');

					marketplace = Marketplace(company, 'CR');
					cartagoAgent = marketplace.AddAgent('Cartago');
					agent1 = marketplace.AddAgent('1');
					marketplace.AddAgent('San José');
					marketplace.AddAgent('Heredia');

                    customSettings = CustomSettingsCollection(company);
					{{
						company.System.Entities.Add(1, 'Apolo');
						artemisEntity = company.System.Entities.Add(2, 'Artemis');
						zeusEntity = company.System.Entities.Add(3, 'Zeus');
						fieroEntity = company.System.Entities.Add(4, 'Fiero');
						fieroEntity.Visible = true;
						fieroEntity.Enabled = true;
						hadesEntity = company.System.Entities.Add(5, 'Hades');
						hadesEntity.Visible = true;
						hadesEntity.Enabled = true;
						consignmentEntity = company.System.Entities.Add(6, 'Consignment');
						consignmentEntity.Visible = true;
						consignmentEntity.Enabled = true;

						company.System.Tenants.Add(1, 'Apolo');
						artemisTenant = company.System.Tenants.Add(2, 'Artemis');
						zeusTenant = company.System.Tenants.Add(3, 'Zeus');
						insiderTenant = company.System.Tenants.Add(4, 'Insider');
						hadesTenant = company.System.Tenants.Add(5, 'Hades');
						hadesTenant.MakeCurrent();

						asiPM = company.System.PaymentMethods.Add(1, 'A.S.I.');
						dgsPM = company.System.PaymentMethods.Add(2, 'D.G.S.');
						dgsV2PM = company.System.PaymentMethods.Add(3, 'D.G.S. v2');
						credirCardPM = company.System.PaymentMethods.Add(4, 'Credit card');
						blockChainPM = company.System.PaymentMethods.Add(5, 'Blockchain');
						moneyTransferPM = company.System.PaymentMethods.Add(6, 'Money transfer');
						asiSimulatorPM = company.System.PaymentMethods.Add(7, 'A.S.I. Simulator');
						dsgSimulatorPM = company.System.PaymentMethods.Add(8, 'D.G.S. Simulator');
						pm = company.System.PaymentMethods.Add(9, '{PaymentMethod.Cash.ToString()}');
						pm.Visible = true;
						pm.Enabled = true;
						pm = company.System.PaymentMethods.Add(10, '{PaymentMethod.Bank.ToString()}');
						pm.Visible = true;
						pm.Enabled = true;
						pm = company.System.PaymentMethods.Add(11, '{PaymentMethod.Creditcard.ToString()}');
						pm.Visible = true;
						pm.Enabled = true;
						pm = company.System.PaymentMethods.Add(12, '{PaymentMethod.FinancialServices.ToString()}');
						pm.Visible = true;
						pm.Enabled = true;
						pm = company.System.PaymentMethods.Add(13, '{PaymentMethod.ThirdParty.ToString()}');
						pm.Visible = true;
						pm.Enabled = true;
						pm = company.System.PaymentMethods.Add(14, '{PaymentMethod.Secrets.ToString()}');
						pm.Visible = true;
						pm.Enabled = true;
						pm = company.System.PaymentMethods.Add(15, '{PaymentMethod.P2PTransfer.ToString()}');
						pm.Visible = true;
						pm.Enabled = true;
						pm.AddProvider(1,'MoneyGram');
						pm.AddProvider(2,'Rio Money Transfer');
						pm.AddProvider(3,'Sigue');
						pm.AddProvider(4,'Boss Revolution Money Transfer');
						pm.AddProvider(5,'Delgado Travel');
						pm.AddProvider(6,'Intermex International Money Transfer');
						pm.AddProvider(7,'Maxi Money Services');
						pm.AddProvider(8,'Barri Financial Group');
						pm.AddProvider(9,'La Nacional');
						pm.AddProvider(10,'Dinex');
						pm.AddProvider(11,'Remitly');

						depositTransactionType = company.System.TransactionTypes.Add(1, '{TransactionType.Deposit.ToString()}');
						depositTransactionType.Description = 'A sum of money placed in an account';
						withDrawalTransactionType = company.System.TransactionTypes.Add(2, '{TransactionType.Withdrawal.ToString()}');
						withDrawalTransactionType.Description = 'A sum of money take out of an account';
						transferTransactionType = company.System.TransactionTypes.Add(3, '{TransactionType.Transfer.ToString()}');
						transferTransactionType.Description = 'A sum of money is sent from one account to another';
						creditNoteTransactionType = company.System.TransactionTypes.Add(4, '{TransactionType.CreditNote.ToString()}');
						creditNoteTransactionType.Description = 'It is a document used by a vendor to inform the buyer of mistake on an order';
						debitNoteTransactionType = company.System.TransactionTypes.Add(5, '{TransactionType.DebitNote.ToString()}');
						debitNoteTransactionType.Description = 'It is a document used by a vendor to inform the buyer of current debt obligations';
						saleTransactionType = company.System.TransactionTypes.Add(6, '{TransactionType.Sale.ToString()}');
						saleTransactionType.Description = 'The exchange of a commodity for money';
						depositAndLockTransactionType = company.System.TransactionTypes.Add(7, '{TransactionType.Deposit.ToString()} then Lock');
						depositAndLockTransactionType.Description = 'Add and Lock money placed in an account';

						depositTransactionType.Visible = true;
						depositTransactionType.Enabled = true;
						withDrawalTransactionType.Visible = true;
						withDrawalTransactionType.Enabled = true;
						transferTransactionType.Visible = true;
						transferTransactionType.Enabled = true;
						debitNoteTransactionType.Visible = true;
						debitNoteTransactionType.Enabled = true;
						creditNoteTransactionType.Visible = true;
						creditNoteTransactionType.Enabled = true;
						saleTransactionType.Visible = true;
						saleTransactionType.Enabled = true;
						depositAndLockTransactionType.Visible = true;
						depositAndLockTransactionType.Enabled = true;

						cs = customSettings.AddFixedParameter(Now, 'CashierDriver.url', 'http://cashierapi:5000/'); 
						cs.Description = 'Url Cashier';
						cs = customSettings.AddFixedParameter(Now, 'CompanyBaseUrlServices', 'http://cashierapi:5000/'); 
						cs.Description = 'It is the base production url';
						cs = customSettings.AddFixedParameter(Now, 'TokenSystemId', '3aab83fb'); 
						cs.Description = 'App\'s token id';
						cs = customSettings.AddSecretParameter(Now, 'TokenSystemPassword', '38f845e977af7f2cd6313c5a9d35e4de7f353405100e7748ab15b410eb53d886');
						cs.Description = 'Password for token';
						cs = customSettings.AddFixedParameter(Now, 'SendWagers', true); 
						cs.Description = 'To send wagers to third party';
						cs = customSettings.AddFixedParameter(Now, 'CompanySystemId', 'N/A'); 
						cs.Description = 'company id';
						cs = customSettings.AddSecretParameter(Now, 'CompanySystemPassword', 'N/A'); 
						cs.Description = 'password';
						cs = customSettings.AddFixedParameter(Now, 'NeedsUniqueIdentifierForPaymentHub', 'PRODUCTION_DOES_NOT_NEED_IT'); 
						cs.Description = 'Setting';
						cs = customSettings.AddFixedParameter(Now, 'CompanyClerkId', 'Administrator'); 
						cs.Description = 'Clerk id';

						cs = customSettings.AddVariableParameter('Input$Provider'); 
						cs.Description = 'Consignment provider';
						cs = customSettings.AddVariableParameter('KYC$DOB'); 
						cs.Description = 'Birthdate from KYC';
						cs = customSettings.AddSecretParameter(Now, 'KYCUsername', 'testUser1');
						cs.Description = 'KYC Username for fields';
						cs = customSettings.AddSecretParameter(Now, 'KYCPassword', '12345');
						cs.Description = 'KYC Password for fields';
					}}

					company.IsAlreadyRegisteredTenantAndStore = true;
                ");
			CreatePlayers();
		}

		Dictionary<string, string> custommersById = new Dictionary<string, string>();
		public const string ACCOUNT_NUMBER_1 = "*********";
		public const string ACCOUNT_NUMBER_2 = "*********";
		int identificationDocumentNumber = 0;
		public KenoMockGenerator CreatePlayers()
		{
			custommersById.Add(ACCOUNT_NUMBER_1, "ID012345678901234567890123456789012345");
			custommersById.Add(ACCOUNT_NUMBER_2, "iCDlytS7kCxVPfgc0uSVvfJDtkgoUtRUtOSb09HFuQk=");
			var agent = Agents.INSIDER;
			foreach (var accountById in custommersById)
			{
				identificationDocumentNumber += 1;
				StringBuilder linesToCreatePlayer = new StringBuilder();
				linesToCreatePlayer.Append($@"
                    custNO{accountById.Key} = company.CreateCustomer('NO{accountById.Key}',{agent});
					customer = custNO{accountById.Key};
					customer.AffiliateId = 1;
                    custNO{accountById.Key}.Name = 'Tester User';
					custNO{accountById.Key}.Identifier='10{identificationDocumentNumber}';
					custNO{accountById.Key}.AffiliateId = {identificationDocumentNumber};
                    playerNO{accountById.Key} = custNO{accountById.Key}.Player;
                    player = custNO{accountById.Key}.Player;
                    thePlayerItsNew = custNO{accountById.Key}.ItsNewOne();
                    if(thePlayerItsNew)
                    {{
                        custNO{accountById.Key}.ReplicateInOtherNodes(itIsThePresent, true, Now, 'NickName User', 'https://intranet.ncubo.com/ResourcesQA5/api/file/IDDefault/Lotto/Lotto/avatars/avatar2.png');
                    }}

                    profile = lotteries.PlayersProfiles.Profiles.Profile('Test profile 1');
                    lotteries.PlayersProfiles.Assign(Now,playerNO{accountById.Key},profile);
                ");
				var result = Perform(linesToCreatePlayer.ToString());
			}
			return this;
		}

		public KenoMockGenerator CreatePlayers(Dictionary<string, string> custommersById)
		{
			var agent = Agents.INSIDER;
			foreach (var accountById in custommersById)
			{
				identificationDocumentNumber += 1;
				StringBuilder linesToCreatePlayer = new StringBuilder();
				linesToCreatePlayer.Append($@"
                    cust{accountById.Key} = company.CreateCustomer('{accountById.Key}',{agent});
					customer = cust{accountById.Key};
					customer.AffiliateId = 1;
                    cust{accountById.Key}.Name = 'Tester User';
					cust{accountById.Key}.Identifier='10{identificationDocumentNumber}';
                    player = cust{accountById.Key}.Player;
                    thePlayerItsNew = cust{accountById.Key}.ItsNewOne();
                    if(thePlayerItsNew)
                    {{
                        cust{accountById.Key}.ReplicateInOtherNodes(itIsThePresent, true, Now, 'NickName User', 'https://intranet.ncubo.com/ResourcesQA5/api/file/IDDefault/Lotto/Lotto/avatars/avatar2.png');
                    }}  
                ");
				var result = Perform(linesToCreatePlayer.ToString());
			}
			return this;
		}

		int authorizationId = 500;
		public KenoMockGenerator BuyKenoTicket(string playerId, decimal amount, int[] numbers, bool hasMultiplier, bool hasBullsEye)
		{
			var selection = string.Join(",", numbers);
			var script = $@"
                drawDate = lotteries.CalculateNextPendingAndNoRegradedSchedulesAtForKeno(Now);
                nextDraw = lotteries.NextPendingAndNoRegradedSchedulesAtForKeno(Now); 

				domain = company.Sales.DomainFrom('localhost');
				player = company.Players.SearchPlayer('{playerId}');
				customer = company.CustomerByPlayer(player);
				{{
                    Eval('orderNumber = ' + company.IdentityOrderNumber + ';');
					myOrder = company.GetNewOrder(customer);
					lotteries.IsBetAmountValidForKeno(player, domain, {amount});
					myOrder = company.CreateKenoOrder(player, {{{selection}}}, {hasMultiplier}, {hasBullsEye}, {amount}, nextDraw.IdPrefix, Now, myOrder, domain, kenoSingleProd, kenoWithMultiplierProd, kenoWithBullEyeProd, kenoWithMultiplierAndBulleyeProd);
					company.AddOrders(myOrder);
                    company.PurchaseTickets(itIsThePresent, myOrder, Now, {authorizationId++}, domain, orderNumber);
				}}
				";
			var result = Perform(script);
			return this;
		}
	}
}
