﻿using GamesEngine.Domains;
using GamesEngine.Gameboards;
using GamesEngine.MessageQueuing;
using GamesEngine.PurchaseOrders;
using GamesEngine.Settings;
using GamesEngine.Time;
using Microsoft.VisualBasic;
using MySql.Data.MySqlClient;
using Puppeteer.EventSourcing;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using static GamesEngine.Exchange.PaymentProcessorsAndActionsByDomains;

namespace GamesEngine.Games.Lotto
{
    public class ReceiverOfHistoricalPicks : ReceiverOfHistorical
    {
        public ReceiverOfHistoricalPicks()
        {
            streamHandler = new StreamHandlerPicks();
        }

        public override void InitHistorical(HistoricalDatabaseType dbType, string connectionString)
        {
            if (String.IsNullOrEmpty(connectionString)) throw new ArgumentNullException(nameof(connectionString));
            if (dbType == HistoricalDatabaseType.MySQL)
            {
                lottoStorage = new LottoStorageMySQL(connectionString);
                lottoStorage.MakeSureThatTableExists();
            }
            else if (dbType == HistoricalDatabaseType.SQLServer)
            {
                lottoStorage = new LottoStorageSQLServer(connectionString);
                lottoStorage.MakeSureThatTableExists();
            }
            else
            {
                throw new GameEngineException($"There is no {nameof(LottoPicksStorage)} implementation.");
            }
        }

        public override void ReceiveWinner(TicketMessage msg)
        {
            base.ReceiveWinner(msg);

            var info = (WinnerInfo)msg;
            var pickType = info.Ticket.Substring(0, 2);
            if (info.Action == GameboardStatus.GRADED)
            {
                DrawingMessage drawingMsg;
                if (!this.recentReceivedDrawings.TryGetValue(info.DrawingId, out drawingMsg))
                {
                    throw new GameEngineException($"Drawing message {info.DrawingId} corresponding to this winner has not been received");
                }

                Reports.ClassifyWinner(pickType, info.Draw, info.Prize, drawingMsg.Description, new DateTime(info.Year, info.Month, info.Day, info.Hour, info.Minute, 0));
            }
            else if (info.Action == GameboardStatus.REGRADED)
            {
                DrawingMessage drawingMsg;
                if (!this.recentReceivedDrawings.TryGetValue(info.DrawingId, out drawingMsg))
                {
                    throw new GameEngineException($"Drawing message {info.DrawingId} corresponding to this winner has not been received");
                }
                Reports.UpdateWinnerAfterRegrade(pickType, info.Draw, info.Prize, drawingMsg.Description, new DateTime(info.Year, info.Month, info.Day, info.Hour, info.Minute, 0));
            }
        }

        class RecordsAccumulatorForReportsPicks : RecordsAccumulatorForReports
        {
            

            public RecordsAccumulatorForReportsPicks()
            {
                if (queryMakerOfHistorical == null) queryMakerOfHistorical = new QueryMakerOfHistoricalPicks();
                if (((QueryMakerOfHistoricalPicks)queryMakerOfHistorical).ExistsDailyTotalProfitStorage())
                {
                    lastDateAccumulating = ((QueryMakerOfHistoricalPicks)queryMakerOfHistorical).LastDateInDailyTotalProfit();
                }
            }

            protected override void ValidateTheSameDrawAndGameTypeFor(IEnumerable<TicketMessage> messages)
            {
                var firstMessage = (TicketPicksMessage)messages.ElementAt(0);
                var state = firstMessage.StateAbb;
                var drawDate = firstMessage.DrawDate();
                var gameType = firstMessage.GameType();
                var gameTypeForReports = Reports.GameTypeOf(gameType);

                foreach (TicketPicksMessage message in messages)
                {
                    var currentState = message.StateAbb;
                    var currentDrawDate = message.DrawDate();
                    var currentGameTypeForReports = Reports.GameTypeOf(message.GameType());

                    if (currentState != state) throw new GameEngineException($"{nameof(message)} has a different {nameof(state)}. Expected: {state} actual: {currentState}");
                    if (currentDrawDate != drawDate) throw new GameEngineException($"{nameof(message)} has a different {nameof(drawDate)}. Expected: {drawDate} actual: {currentDrawDate}");
                    if (currentGameTypeForReports != gameTypeForReports) throw new GameEngineException($"{nameof(message)} has a different {nameof(gameType)}. Expected: {gameType} actual: {message.GameType()}");
                }
            }
        }

        private abstract class LottoPicksStorage : LottoStorage
        {

            protected LottoPicksStorage(string connectionString) : base(connectionString)
            {
            }

        }

        private class LottoStorageMySQL : LottoPicksStorage
        {
            internal LottoStorageMySQL(string connectionString) : base(connectionString)
            {

            }

            internal override void MakeSureThatTableExists()
            {
                CreateStorage();
                INSERT_WINNER_CMD = $"INSERT INTO {TABLE_WINNERS}(state, date, account, ticket, count, amount, selection, action, drawingid, domainid, creation, ordernumber, ticketnumber, subticketsAndWagerNumbers, draw, fireball, gradedby, profit, prize, prizesversion, currencyid, TIMESTAMP) VALUES ";
                INSERT_LOSER_CMD = $"INSERT INTO {TABLE_LOOSERS}(state, date, account, ticket, count, amount, selection, action, drawingid, domainid, creation, ordernumber, ticketnumber, subticketsAndWagerNumbers, draw, fireball, gradedby, profit, prizesversion, currencyid, TIMESTAMP) VALUES ";
                INSERT_NOACTION_CMD = $"INSERT INTO {TABLE_NOACTIONS}(state, date, account, ticket, count, amount, selection, action, drawingid, domainid, creation, ordernumber, ticketnumber, subticketsAndWagerNumbers, noactionby, prizesversion, currencyid, TIMESTAMP) VALUES ";
                INSERT_LOTTO_RESULTS_CMD = $"INSERT INTO {TABLE_LOTTO_RESULTS}(drawingid, draw, fireball, drawdate, timestamp) VALUES ";

                recordsForReportsAccumulator = new RecordsAccumulatorForReportsPicks();
            }

            protected override void CreateStorage()
            {
                StringBuilder statement = new StringBuilder();

                statement
                    .Append("CREATE TABLE IF NOT EXISTS ").Append(TABLE_LOOSERS)
                    .Append("(")
                    .Append("ID BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,")
                    .Append("STATE CHAR(2) NOT NULL,")
                    .Append("DATE DATETIME NOT NULL,")
                    .Append($"ACCOUNT VARCHAR({Customer.DATABASE_FIELD_ACCOUNT_LENGHT}) NOT NULL,")
                    .Append("TICKET TEXT NOT NULL,")
                    .Append("COUNT SMALLINT UNSIGNED NOT NULL,")
                    .Append("AMOUNT DECIMAL(5,2) NOT NULL,")
                    .Append("SELECTION TINYINT UNSIGNED NOT NULL,")
                    .Append("ACTION TINYINT UNSIGNED NOT NULL,")
                    .Append("DRAWINGID INT UNSIGNED NOT NULL,")
                    .Append("DOMAINID INT UNSIGNED NOT NULL DEFAULT 0,")
                    .Append("CREATION DATETIME NOT NULL,")
                    .Append("ORDERNUMBER BIGINT NOT NULL,")
                    .Append("TICKETNUMBER BIGINT NOT NULL,")
                    .Append("SUBTICKETSANDWAGERNUMBERS LONGTEXT NOT NULL,")
                    .Append("DRAW VARCHAR(14) NOT NULL,")
                    .Append("FIREBALL TINYINT UNSIGNED NULL,")
                    .Append("GRADEDBY VARCHAR(50) NOT NULL,")
                    .Append("PROFIT DECIMAL(8,2) NOT NULL,")
                    .Append("PRIZESVERSION INT NOT NULL,")
                    .Append("CURRENCYID INT UNSIGNED NOT NULL,")
                    .Append("TIMESTAMP SMALLINT NOT NULL,")
                    .Append("INDEX IDX_ACCOUNT_DATE(ACCOUNT,DATE),")
                    .Append("INDEX IDX_DRAWINGID_DATE(DRAWINGID,DATE),")
                    .Append("PRIMARY KEY (ID)")
                    .Append(") ENGINE=InnoDB CHARSET=utf8;");

                statement
                    .Append("CREATE TABLE IF NOT EXISTS ").Append(TABLE_WINNERS)
                    .Append("(")
                    .Append("ID BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,")
                    .Append("STATE CHAR(2) NOT NULL,")
                    .Append("DATE DATETIME NOT NULL,")
                    .Append($"ACCOUNT VARCHAR({Customer.DATABASE_FIELD_ACCOUNT_LENGHT}) NOT NULL,")
                    .Append("TICKET TEXT NOT NULL,")
                    .Append("COUNT SMALLINT UNSIGNED NOT NULL,")
                    .Append("AMOUNT DECIMAL(5,2) NOT NULL,")
                    .Append("SELECTION TINYINT UNSIGNED NOT NULL,")
                    .Append("ACTION TINYINT UNSIGNED NOT NULL,")
                    .Append("DRAWINGID INT UNSIGNED NOT NULL,")
                    .Append("DOMAINID INT UNSIGNED NOT NULL DEFAULT 0,")
                    .Append("CREATION DATETIME NOT NULL,")
                    .Append("ORDERNUMBER BIGINT NOT NULL,")
                    .Append("TICKETNUMBER BIGINT NOT NULL,")
                    .Append("SUBTICKETSANDWAGERNUMBERS LONGTEXT NOT NULL,")
                    .Append("DRAW VARCHAR(14) NOT NULL,")
                    .Append("FIREBALL TINYINT UNSIGNED NULL,")
                    .Append("GRADEDBY VARCHAR(50) NOT NULL,")
                    .Append("PROFIT DECIMAL(8,2) NOT NULL,")
                    .Append("PRIZE DECIMAL(8,2) NOT NULL,")
                    .Append("PRIZESVERSION INT NOT NULL,")
                    .Append("CURRENCYID INT UNSIGNED NOT NULL,")
                    .Append("TIMESTAMP SMALLINT NOT NULL,")
                    .Append("INDEX IDX_ACCOUNT_DATE(ACCOUNT,DATE),")
                    .Append("PRIMARY KEY (ID)")
                    .Append(") ENGINE=InnoDB CHARSET=utf8;");

                statement
                    .Append("CREATE TABLE IF NOT EXISTS ").Append(TABLE_NOACTIONS)
                    .Append("(")
                    .Append("ID BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,")
                    .Append("STATE CHAR(2) NOT NULL,")
                    .Append("DATE DATETIME NOT NULL,")
                    .Append($"ACCOUNT VARCHAR({Customer.DATABASE_FIELD_ACCOUNT_LENGHT}) NOT NULL,")
                    .Append("TICKET TEXT NOT NULL,")
                    .Append("COUNT SMALLINT UNSIGNED NOT NULL,")
                    .Append("AMOUNT DECIMAL(5,2) NOT NULL,")
                    .Append("SELECTION TINYINT UNSIGNED NOT NULL,")
                    .Append("ACTION TINYINT UNSIGNED NOT NULL,")
                    .Append("DRAWINGID INT UNSIGNED NOT NULL,")
                    .Append("DOMAINID INT UNSIGNED NOT NULL DEFAULT 0,")
                    .Append("CREATION DATETIME NOT NULL,")
                    .Append("ORDERNUMBER BIGINT NOT NULL,")
                    .Append("TICKETNUMBER BIGINT NOT NULL,")
                    .Append("SUBTICKETSANDWAGERNUMBERS LONGTEXT NOT NULL,")
                    .Append("NOACTIONBY VARCHAR(50) NOT NULL,")
                    .Append("PRIZESVERSION INT NOT NULL,")
                    .Append("CURRENCYID INT UNSIGNED NOT NULL,")
                    .Append("TIMESTAMP SMALLINT NOT NULL,")
                    .Append("INDEX IDX_ACCOUNT_DATE(ACCOUNT,DATE),")
                    .Append("PRIMARY KEY (ID)")
                    .Append(") ENGINE=InnoDB CHARSET=utf8;");

                statement
                    .AppendLine("CREATE TABLE IF NOT EXISTS ").Append(TABLE_DRAWINGS)
                    .AppendLine("(")
                    .AppendLine("ID INT UNSIGNED NOT NULL,")
                    .AppendLine("UNIQUEID INT UNSIGNED NOT NULL,")
                    .AppendLine("DRAWINGNAME VARCHAR(50) NULL,")
                    .AppendLine("IDOFPICK TINYINT UNSIGNED NOT NULL,")
                    .AppendLine("POSITION TINYINT UNSIGNED NOT NULL,")
                    .AppendLine("INDEX IDX_UNIQUEID (UNIQUEID),")
                    .AppendLine("PRIMARY KEY (ID)")
                    .AppendLine(") ENGINE=InnoDB CHARSET=utf8;")
                    ;

                statement
                    .AppendLine($"CREATE TABLE IF NOT EXISTS {TABLE_DOMAINS} ")
                    .AppendLine("(")
                    .AppendLine("ID INT UNSIGNED NOT NULL,")
                    .AppendLine("URL VARCHAR(253) NULL,")
                    .AppendLine("PRIMARY KEY (ID)")
                    .AppendLine(") ENGINE=InnoDB CHARSET=utf8;");

                statement
                    .AppendLine("CREATE TABLE IF NOT EXISTS ").Append(TABLE_LOTTO_RESULTS)
                    .AppendLine("(")
                    .AppendLine("DRAWINGID INT UNSIGNED NOT NULL,")
                    .AppendLine("DRAWDATE DATETIME NOT NULL,")
                    .AppendLine("DRAW VARCHAR(14) NOT NULL,")
                    .AppendLine("FIREBALL TINYINT UNSIGNED NULL,")
                    .AppendLine("TIMESTAMP SMALLINT NOT NULL")
                    .AppendLine(") ENGINE=InnoDB CHARSET=utf8;");

                string sql = statement.ToString();
                ExecuteCommand(sql);
            }

            protected override void ExecuteCommand(string cmdText)
            {
                using (MySqlConnection connection = new MySqlConnection(connectionString))
                {
                    try
                    {
                        try
                        {
                            connection.Open();
                            using (MySqlCommand command = new MySqlCommand(cmdText, connection))
                            {
                                command.ExecuteNonQuery();
                            }
                        }
                        catch (Exception e)
                        {
                            Loggers.GetIntance().Db.Error($@"sql:{cmdText} type:{e.GetType()} error:{e.Message}", e);
                            throw new GameEngineException("MySQL Error [" + cmdText + "]");
                        }
                        finally
                        {
                            connection.Close();
                        }

                        WebHookClientRequest.Instance.SendWebHook(DateTime.Now, cmdText, "UnknownAPI", "Lotto");
                    }
                    catch (Exception webhookEx)
                    {
                        Loggers.GetIntance().Webhook.Error($"Webhook failed for SQL: {cmdText}", webhookEx);
                        throw;
                    }
                }
            }

            protected override void ExecuteCommand(DrawingMessage msg)
            {
                using (var connection = new MySqlConnection(connectionString))
                {
                    var cmdText = @"INSERT INTO l900drawings(id, uniqueid, drawingname, idofpick, position) VALUES (@id, @uniqueId, @description, @idOfLottery, @position)
                                    ON DUPLICATE KEY UPDATE drawingname = @description";
                    try
                    {
                        try
                        {
                            connection.Open();
                            using (var command = new MySqlCommand(cmdText, connection))
                            {
                                command.Parameters.AddWithValue("@id", msg.Id);
                                command.Parameters.AddWithValue("@uniqueId", msg.UniqueId);
                                command.Parameters.AddWithValue("@description", msg.Description);
                                command.Parameters.AddWithValue("@idOfLottery", (int)msg.IdOfLottery);
                                command.Parameters.AddWithValue("@position", (int)msg.Position);
                                command.ExecuteNonQuery();
                            }
                        }
                        catch (Exception e)
                        {
                            Loggers.GetIntance().Db.Error($@"sql:{cmdText} type:{e.GetType()} error:{e.Message}", e);
                            throw new GameEngineException("MySQL Error [" + cmdText + "]");
                        }
                        finally
                        {
                            connection.Close();
                        }

                        WebHookClientRequest.Instance.SendWebHook(DateTime.Now, cmdText, "l900drawings", "Lotto");
                    }
                    catch (Exception webhookEx)
                    {
                        Loggers.GetIntance().Webhook.Error($"Webhook failed for SQL: {cmdText}", webhookEx);
                        throw;
                    }
                }
            }

            internal override void IncrementTimeStamp(int drawingId, int year, int month, int day)
            {
                StringBuilder statement = new StringBuilder();
                var drawDate = new DateTime(year, month, day);
                string drawDateAsText = drawDate.ToString("yyyy-MM-dd");
                statement
                    .Append("UPDATE ")
                    .Append(TABLE_WINNERS)
                    .Append(" SET TIMESTAMP = TIMESTAMP + 1 ")
                    .Append("WHERE DRAWINGID=")
                    .Append(drawingId)
                    .Append($" AND DATE(date)='{drawDateAsText}'")
                    .Append(';');

                statement
                    .Append("UPDATE ")
                    .Append(TABLE_LOOSERS)
                    .Append(" SET TIMESTAMP = TIMESTAMP + 1 ")
                    .Append("WHERE DRAWINGID=")
                    .Append(drawingId)
                    .Append($" AND DATE(date)='{drawDateAsText}'")
                    .Append(';');

                statement
                    .Append("UPDATE ")
                    .Append(TABLE_NOACTIONS)
                    .Append(" SET TIMESTAMP = TIMESTAMP + 1 ")
                    .Append("WHERE DRAWINGID=")
                    .Append(drawingId)
                    .Append($" AND DATE(date)='{drawDateAsText}'")
                    .Append(';');

                statement
                    .Append("UPDATE ")
                    .Append(TABLE_LOTTO_RESULTS)
                    .Append(" SET TIMESTAMP = TIMESTAMP + 1 ")
                    .Append("WHERE DRAWINGID=")
                    .Append(drawingId)
                    .Append($" AND DATE(drawdate)='{drawDateAsText}'")
                    .Append(';');

                ExecuteCommand(statement.ToString());
            }

            internal override void IncrementTimeStampForWinners(int drawingId, int year, int month, int day)
            {
                StringBuilder statement = new StringBuilder();
                var drawDate = new DateTime(year, month, day);
                string drawDateAsText = drawDate.ToString("yyyy-MM-dd");
                statement
                    .Append("UPDATE ")
                    .Append(TABLE_WINNERS)
                    .Append(" SET TIMESTAMP = TIMESTAMP + 1 ")
                    .Append("WHERE DRAWINGID=")
                    .Append(drawingId)
                    .Append($" AND DATE(date)='{drawDateAsText}'")
                    .Append(';');

                statement
                    .Append("UPDATE ")
                    .Append(TABLE_LOTTO_RESULTS)
                    .Append(" SET TIMESTAMP = TIMESTAMP + 1 ")
                    .Append("WHERE DRAWINGID=")
                    .Append(drawingId)
                    .Append($" AND DATE(drawdate)='{drawDateAsText}'")
                    .Append(';');

                ExecuteCommand(statement.ToString());
            }

            internal override void Store(WinnerResultsInfo info)
            {
                const int TIME_STAMP_THIS_IS_THE_CURRENT_VERSION = 0;
                string record = $@"({info.DrawingId},
                '{info.Draw}',
                {(info.Fireball != LotteryDraw.WITHOUT_FIREBALL ? info.Fireball : "NULL")},
                '{ ToDateString(info.Year, info.Month, info.Day, info.Hour, info.Minute, 0)}',
                {TIME_STAMP_THIS_IS_THE_CURRENT_VERSION})";

                rowsOfWinnerResultsReceived.Add(record);
            }

            internal override void StoreWinner(TicketMessage msg)
            {
                var info = (WinnerInfo)msg;
                const int TIME_STAMP_THIS_IS_THE_CURRENT_VERSION = 0;

                StringBuilder recordBuilder = new StringBuilder();
                recordBuilder.Append("('")
                            .Append(info.StateAbb)
                            .Append("', '")
                            .Append(ToDateString(info.Year, info.Month, info.Day, info.Hour, info.Minute, 0))
                            .Append("', '")
                            .Append(info.AccountNumber)
                            .Append("', '")
                            .Append(info.Ticket)
                            .Append("', ")
                            .Append(info.CountOfTickets)
                            .Append(", ")
                            .Append(info.Amount)
                            .Append(", ")
                            .Append((int)info.SelectionMode)
                            .Append(", ")
                            .Append((int)info.Action)
                            .Append(", ")
                            .Append(info.DrawingId)
                            .Append(", ")
                            .Append(info.DomainId)
                            .Append(", '")
                            .Append(ToDateString(info.Creation.Year, info.Creation.Month, info.Creation.Day, info.Creation.Hour, info.Creation.Minute, info.Creation.Second))
                            .Append("', ")
                            .Append(info.OrderNumber)
                            .Append(", ")
                            .Append(info.TicketNumber)
                            .Append(", '")
                            .Append(info.SubticketsAndWagerNumbers)
                            .Append("', '")
                            .Append(info.Draw)
                            .Append("', ");
                if (info.Fireball != LotteryDraw.WITHOUT_FIREBALL)
                {
                    recordBuilder.Append(info.Fireball); 
                }
                else 
                { 
                    recordBuilder.Append("NULL");
                }
                recordBuilder.Append(", '")
                            .Append(info.GradedBy)
                            .Append("', ")
                            .Append(info.Profit)
                            .Append(", ")
                            .Append(info.Prize)
                            .Append(", ")
                            .Append(info.PrizesVersion)
                            .Append(", ")
                            .Append(info.CurrencyId)
                            .Append(", ")
                            .Append(TIME_STAMP_THIS_IS_THE_CURRENT_VERSION)
                            .Append(")");

                rowsOfWinnerReceived.Add(recordBuilder.ToString());
            }

            internal override void StoreLoser(TicketMessage msg)
            {
                var info = (LoserInfo)msg;
                const int TIME_STAMP_THIS_IS_THE_CURRENT_VERSION = 0;

                StringBuilder recordBuilder = new StringBuilder();
                recordBuilder.Append("('")
                      .Append(info.StateAbb)
                      .Append("', '")
                      .Append(ToDateString(info.Year, info.Month, info.Day, info.Hour, info.Minute, 0))
                      .Append("', '")
                      .Append(info.AccountNumber)
                      .Append("', '")
                      .Append(info.Ticket)
                      .Append("', ")
                      .Append(info.CountOfTickets)
                      .Append(", ")
                      .Append(info.Amount)
                      .Append(", ")
                      .Append((int)info.SelectionMode)
                      .Append(", ")
                      .Append((int)info.Action)
                      .Append(", ")
                      .Append(info.DrawingId)
                      .Append(", ")
                      .Append(info.DomainId)
                      .Append(", '")
                      .Append(ToDateString(info.Creation.Year, info.Creation.Month, info.Creation.Day, info.Creation.Hour, info.Creation.Minute, info.Creation.Second))
                      .Append("', ")
                      .Append(info.OrderNumber)
                      .Append(", ")
                      .Append(info.TicketNumber)
                      .Append(", '")
                      .Append(info.SubticketsAndWagerNumbers)
                      .Append("', '")
                      .Append(info.Draw)
                      .Append("', ");
                if (info.Fireball != LotteryDraw.WITHOUT_FIREBALL)
                {
                    recordBuilder.Append(info.Fireball);
                }
                else
                {
                    recordBuilder.Append("NULL");
                }
                recordBuilder.Append(", '")
                      .Append(info.GradedBy)
                      .Append("', ")
                      .Append(info.Profit)
                      .Append(", ")
                      .Append(info.PrizesVersion)
                      .Append(", ")
                      .Append(info.CurrencyId)
                      .Append(", ")
                      .Append(TIME_STAMP_THIS_IS_THE_CURRENT_VERSION)
                      .Append(")");

                rowsOfLoserReceived.Add(recordBuilder.ToString());
            }

            internal override void StoreNoAction(TicketMessage msg)
            {
                var info = (NoActionInfo)msg;
                const int TIME_STAMP_THIS_IS_THE_CURRENT_VERSION = 0;
                string record = $@"('{info.StateAbb}',
				'{ ToDateString(info.Year, info.Month, info.Day, info.Hour, info.Minute, 0)}',
				'{ info.AccountNumber}',
				'{ info.Ticket}',
				{ info.CountOfTickets},
				{ info.Amount.ToString()},
				{ (int)info.SelectionMode},
				{ (int)info.Action},
				{ info.DrawingId},
                { info.DomainId},
				'{ ToDateString(info.Creation.Year, info.Creation.Month, info.Creation.Day, info.Creation.Hour, info.Creation.Minute, info.Creation.Second)}',
                {info.OrderNumber},
				{ info.TicketNumber },
				'{ info.SubticketsAndWagerNumbers }',
				'{ info.NoActionBy}',
				{ info.PrizesVersion.ToString()},
                {info.CurrencyId},
				{ TIME_STAMP_THIS_IS_THE_CURRENT_VERSION}
				)";

                rowsOfNoActionReceived.Add(record);
            }

            internal override void Store(DrawingMessage info)
            {
                ExecuteCommand(info);
            }

            internal override void Store(DomainBIMessage info)
            {
                string record = $@"INSERT INTO {TABLE_DOMAINS}(id, url) VALUES ({info.Id}, '{info.Url}') ON DUPLICATE KEY UPDATE url='{info.Url}'";

                rowsOfDomainReceived.Add(record);
            }

            internal override void EndReception()
            {
                StringBuilder cmdText = new StringBuilder();

                const int MAXIMUM_NUMBER_TO_INSERT = 1000;
                if (rowsOfDomainReceived.Count > 0)
                {
                    cmdText.Append(string.Join(";", rowsOfDomainReceived));
                    cmdText.Append(';');
                    rowsOfDomainReceived.Clear();
                }
                if (rowsOfDomainToUpdateReceived.Count > 0)
                {
                    cmdText.Append(string.Join(";", rowsOfDomainToUpdateReceived));
                    cmdText.Append(';');
                    rowsOfDomainToUpdateReceived.Clear();
                }
                if (rowsOfWinnerResultsReceived.Count > 0)
                {
                    cmdText.Append(INSERT_LOTTO_RESULTS_CMD);
                    cmdText.Append(string.Join(",", rowsOfWinnerResultsReceived));
                    cmdText.Append(';');
                    rowsOfWinnerResultsReceived.Clear();
                }

                if (rowsOfLoserReceived.Count > 0)
                {
                    if (rowsOfLoserReceived.Count > MAXIMUM_NUMBER_TO_INSERT)
                    {
                        int groupsOfRowsToInsert = (rowsOfLoserReceived.Count / MAXIMUM_NUMBER_TO_INSERT) + 1;
                        for (int index = 0; index < groupsOfRowsToInsert; index++)
                        {
                            var rowsToSkip = index * MAXIMUM_NUMBER_TO_INSERT;
                            cmdText.Append(INSERT_LOSER_CMD);
                            cmdText.Append(string.Join(",", rowsOfLoserReceived.Skip(rowsToSkip).Take(MAXIMUM_NUMBER_TO_INSERT)));
                            cmdText.Append(';');
                        }
                    }
                    else
                    {
                        cmdText.Append(INSERT_LOSER_CMD);
                        cmdText.Append(string.Join(",", rowsOfLoserReceived));
                        cmdText.Append(';');
                    }

                    rowsOfLoserReceived.Clear();
                }
                if (rowsOfWinnerReceived.Count > 0)
                {
                    cmdText.Append(INSERT_WINNER_CMD);
                    cmdText.Append(string.Join(",", rowsOfWinnerReceived));
                    cmdText.Append(';');
                    rowsOfWinnerReceived.Clear();
                }
                if (rowsOfNoActionReceived.Count > 0)
                {
                    cmdText.Append(INSERT_NOACTION_CMD);
                    cmdText.Append(string.Join(",", rowsOfNoActionReceived));
                    cmdText.Append(';');
                    rowsOfNoActionReceived.Clear();
                }

                if (cmdText.Length > 0)
                {
                    ExecuteCommand(cmdText.ToString());
                }
            }
        }

        private class LottoStorageSQLServer : LottoPicksStorage
        {
            internal LottoStorageSQLServer(string connectionString) : base(connectionString)
            {
            }

            internal override void MakeSureThatTableExists()
            {
                CreateStorage();
                INSERT_WINNER_CMD = $"INSERT INTO {TABLE_WINNERS}(state, date, account, ticket, count, amount, selection, action, drawingid, domainid, creation, ordernumber, ticketnumber, subticketsAndWagerNumbers, draw, fireball, gradedby, profit, prize, prizesversion, currencyid) VALUES ";
                INSERT_LOSER_CMD = $"INSERT INTO {TABLE_LOOSERS}(state, date, account, ticket, count, amount, selection, action, drawingid, domainid, creation, ordernumber, ticketnumber, subticketsAndWagerNumbers, draw, fireball, gradedby, profit, prizesversion, currencyid) VALUES ";
                INSERT_NOACTION_CMD = $"INSERT INTO {TABLE_NOACTIONS}(state, date, account, ticket, count, amount, selection, action, drawingid, domainid, creation, ordernumber, ticketnumber, subticketsAndWagerNumbers, noactionby, prizesversion, currencyid) VALUES ";
                INSERT_LOTTO_RESULTS_CMD = $"INSERT INTO {TABLE_LOTTO_RESULTS}(drawingid, draw, fireball, drawdate, timestamp) VALUES ";


            }

            protected override void CreateStorage()
            {
                StringBuilder statement = new StringBuilder();

                statement
                    .AppendLine("IF NOT EXISTS(")
                    .AppendLine("SELECT 1 FROM INFORMATION_SCHEMA.TABLES ")
                    .AppendLine($"WHERE TABLE_NAME = '{TABLE_LOOSERS}')")
                    .AppendLine("BEGIN")
                    .AppendLine($"CREATE TABLE {TABLE_LOOSERS}")
                    .AppendLine("(")
                    .AppendLine("ID INT IDENTITY(1,1),")
                    .AppendLine("STATE CHAR(2) NOT NULL,")
                    .AppendLine("DATE DATETIME NOT NULL,")
                    .AppendLine($"ACCOUNT VARCHAR({Customer.DATABASE_FIELD_ACCOUNT_LENGHT}) NOT NULL,")
                    .AppendLine("TICKET NVARCHAR(MAX) NOT NULL,")
                    .AppendLine("COUNT SMALLINT NOT NULL,")
                    .AppendLine("AMOUNT DECIMAL(5,2) NOT NULL,")
                    .AppendLine("SELECTION TINYINT NOT NULL CHECK (SELECTION IN (0,1)),")
                    .AppendLine("ACTION TINYINT NOT NULL,")
                    .AppendLine("DRAWINGID INT NOT NULL,")
                    .AppendLine("DOMAINID INT NOT NULL DEFAULT 0,")
                    .AppendLine("CREATION DATETIME NOT NULL,")
                    .AppendLine("ORDERNUMBER INT NOT NULL,")
                    .AppendLine("TICKETNUMBER INT NOT NULL,")
                    .AppendLine("SUBTICKETSANDWAGERNUMBERS NVARCHAR(MAX) NOT NULL,")
                    .AppendLine("DRAW VARCHAR(14) NOT NULL,")
                    .AppendLine("FIREBALL TINYINT UNSIGNED NULL,")
                    .AppendLine("GRADEDBY VARCHAR(50) NOT NULL,")
                    .AppendLine("PROFIT DECIMAL(8,2) NOT NULL,")
                    .AppendLine("PRIZESVERSION INT NOT NULL,")
                    .AppendLine("CURRENCYID INT NOT NULL,")
                    .AppendLine("TIMESTAMP SMALLINT NOT NULL")
                    .AppendLine(");")
                    .AppendLine($"CREATE INDEX IDX_ACCOUNT_DATE ON {TABLE_LOOSERS} (ACCOUNT,DATE);")
                    .AppendLine($"CREATE INDEX IDX_DRAWINGID_DATE ON {TABLE_LOOSERS} (DRAWINGID,DATE);")
                    .AppendLine("END");

                statement
                    .AppendLine("IF NOT EXISTS(")
                    .AppendLine("SELECT 1 FROM INFORMATION_SCHEMA.TABLES ")
                    .AppendLine($"WHERE TABLE_NAME = '{TABLE_WINNERS}')")
                    .AppendLine("BEGIN")
                    .AppendLine($"CREATE TABLE {TABLE_WINNERS}")
                    .AppendLine("(")
                    .AppendLine("ID INT IDENTITY(1,1),")
                    .AppendLine("STATE CHAR(2) NOT NULL,")
                    .AppendLine("DATE DATETIME NOT NULL,")
                    .AppendLine($"ACCOUNT VARCHAR({Customer.DATABASE_FIELD_ACCOUNT_LENGHT}) NOT NULL,")
                    .AppendLine("TICKET NVARCHAR(MAX) NOT NULL,")
                    .AppendLine("COUNT SMALLINT NOT NULL,")
                    .AppendLine("AMOUNT DECIMAL(5,2) NOT NULL,")
                    .AppendLine("SELECTION TINYINT NOT NULL CHECK (SELECTION IN (0,1)),")
                    .AppendLine("ACTION TINYINT NOT NULL,")
                    .AppendLine("DRAWINGID INT NOT NULL,")
                    .AppendLine("DOMAINID INT NOT NULL DEFAULT 0,")
                    .AppendLine("CREATION DATETIME NOT NULL,")
                    .AppendLine("ORDERNUMBER INT NOT NULL,")
                    .AppendLine("TICKETNUMBER INT NOT NULL,")
                    .AppendLine("SUBTICKETSANDWAGERNUMBERS NVARCHAR(MAX) NOT NULL,")
                    .AppendLine("DRAW VARCHAR(14) NOT NULL,")
                    .AppendLine("FIREBALL TINYINT UNSIGNED NULL,")
                    .AppendLine("GRADEDBY VARCHAR(50) NOT NULL,")
                    .AppendLine("PROFIT DECIMAL(8,2) NOT NULL,")
                    .AppendLine("PRIZE DECIMAL(8,2) NOT NULL,")
                    .AppendLine("PRIZESVERSION INT NOT NULL,")
                    .AppendLine("CURRENCYID INT NOT NULL,")
                    .AppendLine("TIMESTAMP SMALLINT NOT NULL")
                    .AppendLine(");")
                    .AppendLine($"CREATE INDEX {TABLE_WINNERS}_ACCOUNT_DATE ON {TABLE_WINNERS} (ACCOUNT,DATE);")
                    .AppendLine("END");
                statement
                    .AppendLine("IF NOT EXISTS(")
                    .AppendLine("SELECT 1 FROM INFORMATION_SCHEMA.TABLES ")
                    .AppendLine($"WHERE TABLE_NAME = '{TABLE_NOACTIONS}')")
                    .AppendLine("BEGIN")
                    .AppendLine($"CREATE TABLE {TABLE_NOACTIONS}")
                    .AppendLine("(")
                    .AppendLine("ID INT IDENTITY(1,1),")
                    .AppendLine("STATE CHAR(2) NOT NULL,")
                    .AppendLine("DATE DATETIME NOT NULL,")
                    .AppendLine($"ACCOUNT VARCHAR({Customer.DATABASE_FIELD_ACCOUNT_LENGHT}) NOT NULL,")
                    .AppendLine("TICKET NVARCHAR(MAX) NOT NULL,")
                    .AppendLine("COUNT SMALLINT NOT NULL,")
                    .AppendLine("AMOUNT DECIMAL(5,2) NOT NULL,")
                    .AppendLine("SELECTION TINYINT NOT NULL CHECK (SELECTION IN (0,1)),")
                    .AppendLine("ACTION TINYINT NOT NULL,")
                    .AppendLine("DRAWINGID INT NOT NULL,")
                    .AppendLine("DOMAINID INT NOT NULL DEFAULT 0,")
                    .AppendLine("CREATION DATETIME NOT NULL,")
                    .AppendLine("ORDERNUMBER INT NOT NULL,")
                    .AppendLine("TICKETNUMBER INT NOT NULL,")
                    .AppendLine("SUBTICKETSANDWAGERNUMBERS NVARCHAR(MAX) NOT NULL,")
                    .AppendLine("NOACTIONBY VARCHAR(50) NOT NULL,")
                    .AppendLine("PRIZESVERSION INT NOT NULL,")
                    .AppendLine("CURRENCYID INT NOT NULL,")
                    .AppendLine("TIMESTAMP SMALLINT NOT NULL")
                    .AppendLine(");")
                    .AppendLine($"CREATE INDEX {TABLE_NOACTIONS}_ACCOUNT_DATE ON {TABLE_NOACTIONS} (ACCOUNT,DATE);")
                    .AppendLine("END");

                statement
                    .AppendLine("IF NOT EXISTS(")
                    .AppendLine("SELECT 1 FROM INFORMATION_SCHEMA.TABLES ")
                    .AppendLine("WHERE TABLE_NAME = '").Append(TABLE_DRAWINGS).Append("')")
                    .AppendLine("BEGIN")
                    .AppendLine("CREATE TABLE ").Append(TABLE_DRAWINGS)
                    .AppendLine("(")
                    .AppendLine("ID INT NOT NULL,")
                    .AppendLine("UNIQUEID INT NOT NULL,")
                    .AppendLine("DRAWINGNAME VARCHAR(50) NULL,")
                    .AppendLine("IDOFPICK TINYINT NOT NULL,")
                    .AppendLine("POSITION TINYINT NOT NULL")
                    .AppendLine(");")
                    .AppendLine("ALTER TABLE ").Append(TABLE_DRAWINGS).Append(" ADD CONSTRAINT PK_").Append(TABLE_DRAWINGS).Append(" PRIMARY KEY (ID);")
                    .AppendLine("CREATE INDEX ").Append(TABLE_DRAWINGS).Append("_UNIQUEID ON ").Append(TABLE_DRAWINGS).Append(" (UNIQUEID);")
                    .AppendLine("END");

                statement
                    .AppendLine("IF NOT EXISTS(")
                    .AppendLine("SELECT 1 FROM INFORMATION_SCHEMA.TABLES ")
                    .AppendLine($"WHERE TABLE_NAME = '{TABLE_DOMAINS}')")
                    .AppendLine("BEGIN")
                    .AppendLine($"CREATE TABLE {TABLE_DOMAINS}")
                    .AppendLine("(")
                    .AppendLine("ID INT NOT NULL,")
                    .AppendLine("URL VARCHAR(253) NULL")
                    .AppendLine(");")
                    .AppendLine($"ALTER TABLE {TABLE_DOMAINS} ADD CONSTRAINT PK_{TABLE_DOMAINS} PRIMARY KEY (ID);")
                    .AppendLine("END");

                statement
                    .AppendLine("IF NOT EXISTS(")
                    .AppendLine("SELECT 1 FROM INFORMATION_SCHEMA.TABLES ")
                    .AppendLine($"WHERE TABLE_NAME = '{TABLE_LOTTO_RESULTS}')")
                    .AppendLine("BEGIN")
                    .AppendLine($"CREATE TABLE {TABLE_LOTTO_RESULTS}")
                    .AppendLine("(")
                    .AppendLine("DRAWINGID INT NOT NULL,")
                    .AppendLine("DRAWDATE DATETIME NOT NULL,")
                    .AppendLine("DRAW VARCHAR(14) NOT NULL,")
                    .AppendLine("TIMESTAMP SMALLINT NOT NULL")
                    .AppendLine(");")
                    .AppendLine($"ALTER TABLE {TABLE_LOTTO_RESULTS} ADD CONSTRAINT PK_{TABLE_LOTTO_RESULTS} PRIMARY KEY (ID);")
                    .AppendLine("END");

                string sql = statement.ToString();
                ExecuteCommand(sql);
            }

            protected override void ExecuteCommand(string cmdText)
            {
                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    try
                    {
                        try
                        {
                            connection.Open();
                            using (SqlCommand command = new SqlCommand(cmdText, connection))
                            {
                                command.CommandType = CommandType.Text;
                                command.ExecuteNonQuery();
                            }
                        }
                        catch (Exception e)
                        {
                            Loggers.GetIntance().Db.Error($@"sql:{cmdText} type:{e.GetType()} error:{e.Message}", e);
                            throw new GameEngineException("SQLServer Error [" + cmdText + "]");
                        }
                        finally
                        {
                            connection.Close();
                        }

                        WebHookClientRequest.Instance.SendWebHook(DateTime.Now, cmdText, "UnknownAPI", "Lotto");
                    }
                    catch (Exception webhookEx)
                    {
                        Loggers.GetIntance().Webhook.Error($"Webhook failed for SQL: {cmdText}", webhookEx);
                        throw;
                    }
                }
            }

            protected override void ExecuteCommand(DrawingMessage msg)
            {
                using (var connection = new SqlConnection(connectionString))
                {
                    var cmdText = @"If Not Exists(select * from l900drawings where ID=@id)
                                    Begin
                                    INSERT INTO l900drawings(id, uniqueid, drawingname, idofpick, position) VALUES (@id, @uniqueId, @description, @idOfLottery, @position)
                                    End
                                else
                                    Begin
                                    UPDATE l900drawings SET drawingname = @description WHERE id=@uniqueId
                                    End";
                    try
                    {
                        try
                        {
                            connection.Open();
                            using (var command = new SqlCommand(cmdText, connection))
                            {
                                command.Parameters.AddWithValue("@id", msg.Id);
                                command.Parameters.AddWithValue("@uniqueId", msg.UniqueId);
                                command.Parameters.AddWithValue("@description", msg.Description);
                                command.Parameters.AddWithValue("@idOfLottery", (int)msg.IdOfLottery);
                                command.Parameters.AddWithValue("@position", (int)msg.Position);
                                command.ExecuteNonQuery();
                            }
                        }
                        catch (Exception e)
                        {
                            Loggers.GetIntance().Db.Error($@"sql:{cmdText} type:{e.GetType()} error:{e.Message}", e);
                            throw new GameEngineException("SQL Error [" + cmdText + "]");
                        }
                        finally
                        {
                            connection.Close();
                        }

                        WebHookClientRequest.Instance.SendWebHook(DateTime.Now, cmdText, "l900drawings", "Lotto");
                    }
                    catch (Exception webhookEx)
                    {
                        Loggers.GetIntance().Webhook.Error($"Webhook failed for SQL: {cmdText}", webhookEx);
                        throw;
                    }
                }
            }

            internal override void IncrementTimeStamp(int drawingId, int year, int month, int day)
            {
                StringBuilder statement = new StringBuilder();
                var drawDate = new DateTime(year, month, day);
                string drawDateAsText = drawDate.ToString("yyyy-MM-dd");
                statement
                    .Append("UPDATE ")
                    .Append(TABLE_WINNERS)
                    .Append(" SET TIMESTAMP=TIMESTAMP+1 ")
                    .Append("WHERE DRAWINGID=")
                    .Append(drawingId)
                    .Append($" AND CAST(date as DATE)='{drawDateAsText}'")
                    .Append(';');

                statement
                    .Append("UPDATE ")
                    .Append(TABLE_LOOSERS)
                    .Append(" SET TIMESTAMP=TIMESTAMP+1 ")
                    .Append("WHERE DRAWINGID=")
                    .Append(drawingId)
                    .Append($" AND CAST(date as DATE)='{drawDateAsText}'")
                    .Append(';');

                statement
                    .Append("UPDATE ")
                    .Append(TABLE_NOACTIONS)
                    .Append(" SET TIMESTAMP=TIMESTAMP+1 ")
                    .Append("WHERE DRAWINGID=")
                    .Append(drawingId)
                    .Append($" AND CAST(date as DATE)='{drawDateAsText}'")
                    .Append(';');

                statement
                    .Append("UPDATE ")
                    .Append(TABLE_LOTTO_RESULTS)
                    .Append(" SET TIMESTAMP = TIMESTAMP + 1 ")
                    .Append("WHERE DRAWINGID=")
                    .Append(drawingId)
                    .Append($" AND CAST(drawdate as DATE)='{drawDateAsText}'")
                    .Append(';');

                ExecuteCommand(statement.ToString());
            }

            internal override void IncrementTimeStampForWinners(int drawingId, int year, int month, int day)
            {
                StringBuilder statement = new StringBuilder();
                var drawDate = new DateTime(year, month, day);
                string drawDateAsText = drawDate.ToString("yyyy-MM-dd");
                statement
                    .Append("UPDATE ")
                    .Append(TABLE_WINNERS)
                    .Append(" SET TIMESTAMP=TIMESTAMP+1 ")
                    .Append("WHERE DRAWINGID=")
                    .Append(drawingId)
                    .Append($" AND CAST(date as DATE)='{drawDateAsText}'")
                    .Append(';');

                statement
                    .Append("UPDATE ")
                    .Append(TABLE_LOTTO_RESULTS)
                    .Append(" SET TIMESTAMP = TIMESTAMP + 1 ")
                    .Append("WHERE DRAWINGID=")
                    .Append(drawingId)
                    .Append($" AND CAST(drawdate as DATE)='{drawDateAsText}'")
                    .Append(';');

                ExecuteCommand(statement.ToString());
            }

            internal override void StoreWinner(TicketMessage msg)
            {
                var info = (WinnerInfo)msg;
                const int TIME_STAMP_THIS_IS_THE_CURRENT_VERSION = 0;
                var recordBuilder = new StringBuilder();
                recordBuilder.Append("('");
                recordBuilder.Append(info.StateAbb);
                recordBuilder.Append("', '");
                recordBuilder.Append(ToDateString(info.Year, info.Month, info.Day, info.Hour, info.Minute, 0));
                recordBuilder.Append("', '");
                recordBuilder.Append(info.AccountNumber);
                recordBuilder.Append("', '");
                recordBuilder.Append(info.Ticket);
                recordBuilder.Append("', ");
                recordBuilder.Append(info.CountOfTickets);
                recordBuilder.Append(", ");
                recordBuilder.Append(info.Amount);
                recordBuilder.Append(", ");
                recordBuilder.Append(((int)info.SelectionMode));
                recordBuilder.Append(", ");
                recordBuilder.Append(((int)info.Action));
                recordBuilder.Append(", ");
                recordBuilder.Append(info.DrawingId);
                recordBuilder.Append(", ");
                recordBuilder.Append(info.DomainId);
                recordBuilder.Append(", '");
                recordBuilder.Append(ToFullDateTimeString(info.Creation));
                recordBuilder.Append("', ");
                recordBuilder.Append(info.OrderNumber);
                recordBuilder.Append(", ");
                recordBuilder.Append(info.TicketNumber);
                recordBuilder.Append(", '");
                recordBuilder.Append(info.SubticketsAndWagerNumbers);
                recordBuilder.Append("', '");
                recordBuilder.Append(info.Draw);
                recordBuilder.Append("', ");
                if (info.Fireball != LotteryDraw.WITHOUT_FIREBALL)
                {
                    recordBuilder.Append(info.Fireball);
                }
                else
                {
                    recordBuilder.Append("NULL");
                }
                recordBuilder.Append(", '");
                recordBuilder.Append(info.GradedBy);
                recordBuilder.Append("', ");
                recordBuilder.Append(info.Profit);
                recordBuilder.Append(", ");
                recordBuilder.Append(info.Prize);
                recordBuilder.Append(", ");
                recordBuilder.Append(info.PrizesVersion);
                recordBuilder.Append(", ");
                recordBuilder.Append(info.CurrencyId);
                recordBuilder.Append(", ");
                recordBuilder.Append(TIME_STAMP_THIS_IS_THE_CURRENT_VERSION);
                recordBuilder.Append(")");

                rowsOfWinnerReceived.Add(recordBuilder.ToString());
            }

            internal override void StoreLoser(TicketMessage msg)
            {
                var info = (LoserInfo)msg;
                const int TIME_STAMP_THIS_IS_THE_CURRENT_VERSION = 0;

                StringBuilder record = new StringBuilder();
                record.Append("('");
                record.Append(info.StateAbb);
                record.Append("', '");
                record.Append(ToDateString(info.Year, info.Month, info.Day, info.Hour, info.Minute, 0));
                record.Append("', '");
                record.Append(info.AccountNumber);
                record.Append("', '");
                record.Append(info.Ticket);
                record.Append("', ");
                record.Append(info.CountOfTickets);
                record.Append(", ");
                record.Append(info.Amount);
                record.Append(", ");
                record.Append((int)info.SelectionMode);
                record.Append(", ");
                record.Append((int)info.Action);
                record.Append(", ");
                record.Append(info.DrawingId);
                record.Append(", ");
                record.Append(info.DomainId);
                record.Append(", '");
                record.Append(ToFullDateTimeString(info.Creation));
                record.Append("', ");
                record.Append(info.OrderNumber);
                record.Append(", ");
                record.Append(info.TicketNumber);
                record.Append(", '");
                record.Append(info.SubticketsAndWagerNumbers);
                record.Append("', '");
                record.Append(info.Draw);
                record.Append("', ");
                if (info.Fireball != LotteryDraw.WITHOUT_FIREBALL)
                {
                    record.Append(info.Fireball);
                }
                else
                {
                    record.Append("NULL");
                }
                record.Append(", '");
                record.Append(info.GradedBy);
                record.Append("', ");
                record.Append(info.Profit);
                record.Append(", ");
                record.Append(info.PrizesVersion);
                record.Append(", ");
                record.Append(info.CurrencyId);
                record.Append(", ");
                record.Append(TIME_STAMP_THIS_IS_THE_CURRENT_VERSION);
                record.Append(")");

                rowsOfLoserReceived.Add(record.ToString());
            }

            internal override void StoreNoAction(TicketMessage msg)
            {
                var info = (NoActionInfo)msg;
                const int TIME_STAMP_THIS_IS_THE_CURRENT_VERSION = 0;
                string record = $@"('{info.StateAbb}',
				'{ ToDateString(info.Year, info.Month, info.Day, info.Hour, info.Minute, 0)}',
				'{ info.AccountNumber}',
				'{ info.Ticket}',
				{ info.CountOfTickets},
				{ info.Amount.ToString()},
				{ (int)info.SelectionMode},
				{ (int)info.Action},
				{ info.DrawingId},
                { info.DomainId},
				'{ ToFullDateTimeString(info.Creation)}',
                {info.OrderNumber},
				{ info.TicketNumber },
				'{ info.SubticketsAndWagerNumbers }',
				'{ info.NoActionBy}',
				{ info.PrizesVersion.ToString()},
                {info.CurrencyId},
				{ TIME_STAMP_THIS_IS_THE_CURRENT_VERSION}
				)";

                rowsOfNoActionReceived.Add(record);

            }
            internal override void Store(DrawingMessage info)
            {
                ExecuteCommand(info);
            }

            internal override void Store(DomainBIMessage info)
            {
                string record = $@"If Not Exists(select * from {TABLE_DOMAINS} where ID={info.Id})
                                    Begin
                                    INSERT INTO {TABLE_DOMAINS}(id, url) VALUES ({info.Id}, '{info.Url}')
                                    End
                                else
                                    Begin
                                        UPDATE {TABLE_DOMAINS} SET url = '{info.Url}' WHERE id={info.Id}
                                    End";

                rowsOfDomainReceived.Add(record);
            }

            private string ToFullDateTimeString(DateTime aDate)
            {
                return aDate.ToString("yyyy-MM-dd HH:mm:ss.fff");
            }

            internal override void EndReception()
            {
                StringBuilder cmdText = new StringBuilder();

                const int MAXIMUM_NUMBER_TO_INSERT = 1000;
                if (rowsOfDomainReceived.Count > 0)
                {
                    cmdText.Append(string.Join(";", rowsOfDomainReceived));
                    cmdText.Append(';');
                    rowsOfDomainReceived.Clear();
                }
                if (rowsOfDomainToUpdateReceived.Count > 0)
                {
                    cmdText.Append(string.Join(";", rowsOfDomainToUpdateReceived));
                    cmdText.Append(';');
                    rowsOfDomainToUpdateReceived.Clear();
                }
                if (rowsOfWinnerResultsReceived.Count > 0)
                {
                    cmdText.Append(INSERT_LOTTO_RESULTS_CMD);
                    cmdText.Append(string.Join(",", rowsOfWinnerResultsReceived));
                    cmdText.Append(';');
                    rowsOfWinnerResultsReceived.Clear();
                }

                if (rowsOfLoserReceived.Count > 0)
                {
                    if (rowsOfLoserReceived.Count > MAXIMUM_NUMBER_TO_INSERT)
                    {
                        int groupsOfRowsToInsert = (rowsOfLoserReceived.Count / MAXIMUM_NUMBER_TO_INSERT) + 1;
                        for (int index = 0; index < groupsOfRowsToInsert; index++)
                        {
                            var rowsToSkip = index * MAXIMUM_NUMBER_TO_INSERT;
                            cmdText.Append(INSERT_LOSER_CMD);
                            cmdText.Append(string.Join(",", rowsOfLoserReceived.Skip(rowsToSkip).Take(MAXIMUM_NUMBER_TO_INSERT)));
                            cmdText.Append(';');
                        }
                    }
                    else
                    {
                        cmdText.Append(INSERT_LOSER_CMD);
                        cmdText.Append(string.Join(",", rowsOfLoserReceived));
                        cmdText.Append(';');
                    }

                    rowsOfLoserReceived.Clear();
                }
                if (rowsOfWinnerReceived.Count > 0)
                {
                    cmdText.Append(INSERT_WINNER_CMD);
                    cmdText.Append(string.Join(",", rowsOfWinnerReceived));
                    cmdText.Append(';');
                    rowsOfWinnerReceived.Clear();
                }
                if (rowsOfNoActionReceived.Count > 0)
                {
                    cmdText.Append(INSERT_NOACTION_CMD);
                    cmdText.Append(string.Join(",", rowsOfNoActionReceived));
                    cmdText.Append(';');
                    rowsOfNoActionReceived.Clear();
                }

                if (cmdText.Length > 0)
                {
                    ExecuteCommand(cmdText.ToString());
                }
            }

            internal override void Store(WinnerResultsInfo info)
            {
                const int TIME_STAMP_THIS_IS_THE_CURRENT_VERSION = 0;
                string record = $@"({info.DrawingId},
                '{info.Draw}',
                {(info.Fireball != LotteryDraw.WITHOUT_FIREBALL ? info.Fireball : "NULL")},
                '{ ToDateString(info.Year, info.Month, info.Day, info.Hour, info.Minute, 0)}',
                {TIME_STAMP_THIS_IS_THE_CURRENT_VERSION})";

                rowsOfWinnerResultsReceived.Add(record);
            }
        }
    }

    class StreamHandlerPicks: StreamHandler
    {
        internal override void CompleteInfo(TicketMessage info)
        {
            if (info == null) throw new ArgumentNullException(nameof(info));
            if (!IsInitialized()) throw new GameEngineException($"Stream must be initialized first");

            if (info is WinnerInfo)
            {
                ((WinnerInfo)info).CompleteInfo(message.StateAbb, message.DrawDate, message.DrawingId, message.GradedBy, message.Draw, message.Fireball);
            }
            else if (info is LoserInfo)
            {
                ((LoserInfo)info).CompleteInfo(message.StateAbb, message.DrawDate, message.DrawingId, message.GradedBy, message.Draw, message.Fireball);
            }
            else if (info is NoActionInfo)
            {
                ((NoActionInfo)info).CompleteInfo(message.StateAbb, message.DrawDate, message.DrawingId, message.GradedBy);
            }
        }
    }
}