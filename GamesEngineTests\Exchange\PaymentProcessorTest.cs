﻿using GamesEngine;
using GamesEngine.Business;
using GamesEngine.Custodian;
using GamesEngine.Domains;
using GamesEngine.Exchange;
using GamesEngine.Finance;
using GamesEngine.PurchaseOrders;
using GamesEngineTests.Custodian;
using GamesEngineTests.Unit_Tests.Games.Lotto;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using System;
using System.Linq;
using town.connectors.drivers;
using Unit.Games.Tools;
using static GamesEngine.Business.WholePaymentMethods;
using static GamesEngine.Business.WholePaymentProcessor;
using static GamesEngineTests.Exchange.SaleTransactionsTest;
using static town.connectors.drivers.Result;

namespace GamesEngineTests.Exchange
{

	[TestClass]
	public class PaymentProcessorTest
	{
		[TestMethod]
		public void Exchange_Processor_Instanciation()
		{
			CurrenciesTest.AddCurrencies();
			Company company;
			Guardian guardian;
			QueueInMemory queue;
			DateTime today = DateTime.Now;
			GuardianTest.Init(today, out company, out guardian, out queue);

			Marketplace marketplace = new Marketplace(company, "CR");
			
			#region Domains
			Domain localhost = company.Sales.CreateDomain(false, 1, "localhost", PaymentChannels.Agents.INSIDER);
			Domain qa4 = company.Sales.CreateDomain(false, 2, "nodo.qa4.ncubo.com", PaymentChannels.Agents.INSIDER);
			company.Sales.CurrentStore.Add(localhost);
			company.Sales.CurrentStore.Add(qa4);
			#endregion

			GuardianTest.LoadProcessors(company);
			PaymentProcessorsAndActionsByDomains processors = marketplace.PaymentProcessors();

			#region Payment Driver

			PaymentProcessor ASIDriver = processors.SearchById("TEST-Cash-TestDepositUSD-USD-1");
			PaymentProcessor GuardianBTCDriver = processors.SearchById("TEST-Secrets-TestDepositBTC-BTC-1");
			PaymentProcessor GuardianBODriver = processors.SearchById("TEST-Cash-TestWithdrawalUSD-USD-1");

			#endregion

			processors.Disable(GuardianBTCDriver);
			processors.Enable(GuardianBTCDriver);

			try
			{
				processors.Enable(GuardianBTCDriver);
				Assert.Fail($"{nameof(GuardianBTCDriver)} it's already added to {nameof(qa4)} domain.");
			}
			catch (GameEngineException e)
			{
			}

			try
			{
				processors.Enable(GuardianBTCDriver);
				Assert.Fail("Processors can not repeat the id.");
			}
			catch (GameEngineException e) { }

			PaymentProcessor result = processors.SearchById(GuardianBTCDriver.Driver.Id);
			Assert.AreEqual(GuardianBTCDriver, result);

			Assert.AreEqual(0, processors.ListAllBanksProcessor().Count());
			Assert.AreEqual(5, processors.ListAllCardsProcessor().Count());
	
			Assert.AreEqual(3, processors.ListAllBtcProcessor().Count());
		}

		[TestMethod]
		public void EnableOrDisableNotificationsSetting()
		{
			Company company = new Company();
			CurrenciesTest.AddCurrencies();
			var store = company.Sales.CreateStore(1, "Test Store");
			store.MakeCurrent();
			Marketplace marketplace = new Marketplace(company, "CR");

			#region Domains
			Domain localhost = company.Sales.CreateDomain(false, 1, "localhost", PaymentChannels.Agents.INSIDER);
			Domain qa4 = company.Sales.CreateDomain(false, 2, "nodo.qa4.ncubo.com", PaymentChannels.Agents.INSIDER);
			store.Add(localhost);
			store.Add(qa4);
			#endregion
			GuardianTest.LoadProcessors(company);
			PaymentProcessorsAndActionsByDomains processors = marketplace.PaymentProcessors();

			#region Payment Driver

			PaymentProcessor ASIDriver = processors.SearchById("TEST-Cash-TestDepositUSD-USD-1");
			#endregion

			var settings = processors.ListSettings(ASIDriver.Driver.Id);
			Assert.AreEqual(1, settings.Count());
			var setting = settings.ElementAt(0);
			Assert.IsTrue(setting.IsNotificationEnabled(TransactionStatus.APPROVED));
			Assert.AreEqual(TransactionType.Deposit.ToString(), setting.TransactionType.Name);

			processors.DisableNotification(ASIDriver.Driver.Id, company.System.TransactionTypes.SearchByName<ProcessorTransaction>(TransactionType.Deposit.ToString()), TransactionStatus.APPROVED);
			Assert.IsFalse(setting.IsNotificationEnabled(TransactionStatus.APPROVED));
			var enabled = processors.IsNotificationEnabled(ASIDriver.Driver.Id, company.System.TransactionTypes.SearchByName<ProcessorTransaction>(TransactionType.Deposit.ToString()), TransactionStatus.APPROVED);
			Assert.IsFalse(enabled);

			processors.EnableNotification(ASIDriver.Driver.Id, company.System.TransactionTypes.SearchByName<ProcessorTransaction>(TransactionType.Deposit.ToString()), TransactionStatus.APPROVED);
			Assert.IsTrue(setting.IsNotificationEnabled(TransactionStatus.APPROVED));
			enabled = processors.IsNotificationEnabled(ASIDriver.Driver.Id, company.System.TransactionTypes.SearchByName<ProcessorTransaction>(TransactionType.Deposit.ToString()), TransactionStatus.APPROVED);
			Assert.IsTrue(enabled);
		}

		[TestMethod]
		public void EnableOrDisableTransactionsSetting()
		{
			Company company = new Company();
			CurrenciesTest.AddCurrencies();
			var store = company.Sales.CreateStore(1, "Test Store");
			store.MakeCurrent();
			Marketplace marketplace = new Marketplace(company, "CR");

			#region Domains
			Domain localhost = company.Sales.CreateDomain(false, 1, "localhost", PaymentChannels.Agents.INSIDER);
			Domain qa4 = company.Sales.CreateDomain(false, 2, "nodo.qa4.ncubo.com", PaymentChannels.Agents.INSIDER);
			store.Add(localhost);
			store.Add(qa4);
			#endregion
			GuardianTest.LoadProcessors(company);
			PaymentProcessorsAndActionsByDomains processors = marketplace.PaymentProcessors();
			
			#region Payment Driver

			PaymentProcessor ASIDriver = processors.SearchById("TEST-Cash-TestDepositUSD-USD-1");

			#endregion

			var settings = processors.ListSettings(ASIDriver.Driver.Id);
			Assert.AreEqual(1, settings.Count());
			var setting = settings.ElementAt(0);
			Assert.IsTrue(setting.IsTransactionEnabled);
			Assert.AreEqual(company.System.TransactionTypes.SearchByName<ProcessorTransaction>(TransactionType.Deposit.ToString()), setting.TransactionType);

			processors.DisableTransactionType(ASIDriver.Driver.Id, company.System.TransactionTypes.SearchByName<ProcessorTransaction>(TransactionType.Deposit.ToString()));
			Assert.IsFalse(setting.IsTransactionEnabled);
			var enabled = processors.IsTransactionTypeEnabled(ASIDriver.Driver.Id, company.System.TransactionTypes.SearchByName<ProcessorTransaction>(TransactionType.Deposit.ToString()));
			Assert.IsFalse(enabled);

			processors.EnableTransactionType(ASIDriver.Driver.Id, company.System.TransactionTypes.SearchByName<ProcessorTransaction>(TransactionType.Deposit.ToString()));
			Assert.IsTrue(setting.IsTransactionEnabled);
			enabled = processors.IsTransactionTypeEnabled(ASIDriver.Driver.Id, company.System.TransactionTypes.SearchByName<ProcessorTransaction>(TransactionType.Deposit.ToString()));
			Assert.IsTrue(enabled);
		}

		[TestMethod]
		public void ExistsNotificationSetting()
		{
			CurrenciesTest.AddCurrencies();
			Company company;
			Guardian guardian;
			QueueInMemory queue;
			DateTime today = DateTime.Now;
			GuardianTest.Init(today, out company, out guardian, out queue);

			//Company company = new Company();
			CurrenciesTest.AddCurrencies();
			Store store;
			if (!company.Sales.HasStore(1))
			{
				store = company.Sales.CreateStore(1, "Test Store");
			}
            else
            {
				store = company.Sales.StoreById(1);
			}
			//store.MakeCurrent();
			Marketplace marketplace = new Marketplace(company, "CR");

			#region Domains
			Domain localhost = company.Sales.CreateDomain(false, 1, "localhost", PaymentChannels.Agents.INSIDER);
			Domain qa4 = company.Sales.CreateDomain(false, 2, "nodo.qa4.ncubo.com", PaymentChannels.Agents.INSIDER);
			//store.Add(localhost);
			store.Add(qa4);
			#endregion
			GuardianTest.LoadProcessors(company);
			PaymentProcessorsAndActionsByDomains processors = marketplace.PaymentProcessors();

			#region Payment Driver

			PaymentProcessor ASIDriver = processors.SearchById("TEST-Cash-TestDepositUSD-USD-1");
			PaymentProcessor GuardianBTCDriver = processors.SearchById("TEST-Secrets-TestDepositBTC-BTC-1");

			#endregion

			var exists = processors.ExistsSetting(ASIDriver.Driver.Id, company.System.TransactionTypes.SearchByName<ProcessorTransaction>(TransactionType.Deposit.ToString()));
			Assert.IsTrue(exists);
			exists = processors.ExistsSetting(ASIDriver.Driver.Id, company.System.TransactionTypes.SearchByName<ProcessorTransaction>(TransactionType.Withdrawal.ToString()));
			Assert.IsFalse(exists);
			exists = processors.ExistsSetting(ASIDriver.Driver.Id, company.System.TransactionTypes.SearchByName<ProcessorTransaction>(TransactionType.Deposit.ToString()));
			Assert.IsTrue(exists);
			exists = processors.ExistsSetting(GuardianBTCDriver.Driver.Id, company.System.TransactionTypes.SearchByName<ProcessorTransaction>(TransactionType.Deposit.ToString()));
			Assert.IsTrue(exists);
		}

		[TestMethod]
		public void IsOnlyOneDriverEnabled()
		{
			Company company = new Company();
			CurrenciesTest.AddCurrencies();
			var store = company.Sales.CreateStore(1, "Test Store");
			store.MakeCurrent();
			Marketplace marketplace = new Marketplace(company, "CR");

			#region Domains
			Domain localhost = company.Sales.CreateDomain(false, 1, "localhost", PaymentChannels.Agents.INSIDER);
			Domain qa4 = company.Sales.CreateDomain(false, 2, "nodo.qa4.ncubo.com", PaymentChannels.Agents.INSIDER);
			store.Add(localhost);
			store.Add(qa4);
			#endregion
			GuardianTest.LoadProcessors(company);
			PaymentProcessorsAndActionsByDomains processors = marketplace.PaymentProcessors();
			
			#region Payment Driver

			PaymentProcessor ASIDriver = processors.SearchById("TEST-Cash-TestDepositUSD-USD-1");
			PaymentProcessor GuardianBODriver = processors.SearchById("TEST-Creditcard-TestDepositUSD2-USD-1");
			#endregion

			var enabled = processors.IsOnlyOneDriverEnabled(Coinage.Coin(Currencies.CODES.USD), company.System.TransactionTypes.SearchByName<ProcessorTransaction>(TransactionType.Deposit.ToString()));
			Assert.IsFalse(enabled);

			enabled = processors.IsOnlyOneDriverEnabled(Coinage.Coin(Currencies.CODES.USD), company.System.TransactionTypes.SearchByName<ProcessorTransaction>(TransactionType.Deposit.ToString()));
			Assert.IsFalse(enabled);

			processors.Disable(GuardianBODriver);

			enabled = processors.IsOnlyOneDriverEnabled(Coinage.Coin(Currencies.CODES.USD), company.System.TransactionTypes.SearchByName<ProcessorTransaction>(TransactionType.Deposit.ToString()));
			Assert.IsFalse(enabled);

			processors.DisableTransactionType(ASIDriver.Driver.Id, company.System.TransactionTypes.SearchByName<ProcessorTransaction>(TransactionType.Deposit.ToString()));
			enabled = processors.IsOnlyOneDriverEnabled(Coinage.Coin(Currencies.CODES.USD), company.System.TransactionTypes.SearchByName<ProcessorTransaction>(TransactionType.Deposit.ToString()));
			Assert.IsFalse(enabled);
		}
	}
}
