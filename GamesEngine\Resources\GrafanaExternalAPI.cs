﻿using Nest;
using Newtonsoft.Json;
using Puppeteer.EventSourcing;
using Puppeteer.EventSourcing.Libraries;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;

namespace GamesEngine.Resources
{
    //create a class GrafanaExternalAPI with singleton pattern
    [Puppet]
    public class GrafanaExternalAPI : Objeto
    {
        private static GrafanaExternalAPI instance = null;

        internal bool IS_CONFIGURED { get; set; }
        internal string PORT { get; set; }
        internal string HOST { get; set; }
        private string TOKEN { get; set; }

        private Dictionary<string, Dashboard> dashboards;// UID, DATA-Dashboard

        private GrafanaExternalAPI()
        {
            dashboards = new Dictionary<string, Dashboard>();
        }

        public static GrafanaExternalAPI Instance
        {
            get
            {
                if (instance == null)
                {
                    instance = new GrafanaExternalAPI();
                }
                return instance;
            }
        }

        public void Configure(string host, string port, string token)
        {
            HOST = host;
            PORT = port;
            TOKEN = token;

            LoadDashboards();
            IS_CONFIGURED = true;
        }

        // create a method that load all the dashboards from grafana
        public void LoadDashboards()
        {
            // create a string with the url of the dashboards
            // create a request GET to the url, with the token in the header
            // check if the request is successful
            String HOSTANDPORT;
            if (string.IsNullOrWhiteSpace(PORT))
            {
                HOSTANDPORT = $"{HOST}";
            }
            else
            {
                HOSTANDPORT = $"{HOST}:{PORT}";
            }

            string dashboardsUrl = $"http://{HOSTANDPORT}/api/search?folderIds=0&query=&starred=false";
            HttpRequestMessage request = new HttpRequestMessage(HttpMethod.Get, dashboardsUrl);
            request.Headers.Add("Accept", $"application/json");
            request.Headers.Add("Authorization", $"Bearer {TOKEN}");
            HttpResponseMessage response = new HttpClient().SendAsync(request).Result;
            if (response.IsSuccessStatusCode)
            {
                // get the content of the response
                // create logic for access the content with class Dashboard and add it to the list of dashboards
                string content = response.Content.ReadAsStringAsync().Result;
                dynamic dashboards = JsonConvert.DeserializeObject(content);
                foreach (var dashboard in dashboards)
                {
                    string uid = dashboard.uid;
                    string title = dashboard.title;
                    string uri = dashboard.uri;
                    string url = dashboard.url;
                    // add to the list of dashboards
                    this.dashboards.Add(uid, new Dashboard { uid = uid, title = title, uri = uri, url = url });
                }
            }
            else
            {
                // throw an exception
                throw new GameEngineException("Grafana API is not configured");
            }
        }

        public string DashboardURL(string dashboardNameOrTag)
        {
            // if the api is not configured
            if (!IS_CONFIGURED)
            {
                // throw an exception
                throw new GameEngineException("Grafana API is not configured");
            }

            String HOSTANDPORT;
            if (string.IsNullOrWhiteSpace(PORT))
            {
                HOSTANDPORT = $"{HOST}";
            }
            else
            {
                HOSTANDPORT = $"{HOST}:{PORT}";
            }

            Dashboard dashboard = dashboards.FirstOrDefault(
                item => item.Value.title.ToLower().Trim() == dashboardNameOrTag.ToLower().Trim() || item.Value.uri.Replace("db/", "").Contains(dashboardNameOrTag.ToLower().Trim())
             ).Value;
            if (dashboard == null)
            {
                throw new GameEngineException("Dashboard does not exist");
            }
            // create a string tagBoard with separeted by / the host, port, uid and uri of the dashboard
            string tagBoard = dashboard.uri.Replace("db/", "");
            string result = $"http://{HOSTANDPORT}/d/{dashboard.uid}/{tagBoard}";
            return result;
        }

        internal IEnumerable<Dashboard> AllDashboard()
        {
            return dashboards.Values;
        }

        [Puppet]
        internal class Dashboard : Objeto
        {
            public string uid;
            public string title;
            public string uri;
            public string url;
        }

    }

}
