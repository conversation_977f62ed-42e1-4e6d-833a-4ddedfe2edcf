﻿using GamesEngine.Bets;
using GamesEngine.Marketing.Campaigns.Base;
using GamesEngine.Messaging;
using GamesEngine.RealTime.Events;
using GamesEngine.RealTime;
using GamesEngine.Resources;
using Puppeteer.EventSourcing.Libraries;
using Puppeteer.EventSourcing;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using static GamesEngine.Marketing.Campaigns.RewardBased;
using GamesEngine.Business;
using GamesEngine.Finance;
using GamesEngine.PurchaseOrders;
using GamesEngine.Settings;
using static GamesEngine.Marketing.Campaigns.ActionerStreak;
using GamesEngine.Time;

namespace GamesEngine.Marketing.Campaigns.Rewarders
{
    internal abstract class CashbackRewarder : CampaignRewarder
    {
        private Dictionary<Player, CashbackData> campaignPlayersData;
        protected List<StepCashback> rangeCashbackStages;

        protected CashbackRewarder(CampaignBase campaign) : base(campaign)
        {
            rangeCashbackStages = new List<StepCashback>();
            this.campaignPlayersData = new Dictionary<Player, CashbackData>();
        }

        internal bool OperatorWasDefined => rangeCashbackStages.Any();
        internal IEnumerable<StepCashback> Stages => rangeCashbackStages;

        internal decimal TotalAmountAccrued { get; set; }
        internal int TotalClaimTimes { get; set; }
        internal override int TotalPlayers => campaignPlayersData.Keys.Count;

        private decimal maxAccruedPlayer;
        internal decimal MaxAccruedPlayer
        {
            get
            {
                return this.maxAccruedPlayer;
            }
            set
            {
                if (value <= 0) throw new GameEngineException($"{nameof(MaxAccruedPlayer)} must not be set to zero or negative.");
                this.maxAccruedPlayer = value;
            }
        }

        internal void CalculateCashbackAmount(bool itIsThePresent, DateTime now, Player player, decimal purchaseAmount, Subscriber from)
        {
            if (player == null) throw new ArgumentException(nameof(player));
            if (purchaseAmount < 0) throw new ArgumentException(nameof(purchaseAmount));

            CashbackData playerData;
            if (!campaignPlayersData.TryGetValue(player, out playerData))
            {
                playerData.CurrentIndexStep = -1;
                campaignPlayersData.Add(player, playerData);
            }

            decimal calculatedAccrued = CashbackAmount(player, purchaseAmount, playerData);
            TotalAmountAccrued -= playerData.Accrued;
            TotalAmountAccrued += calculatedAccrued;

            playerData.TotalAccrued -= playerData.Accrued;
            playerData.TotalAccrued += calculatedAccrued;

            playerData.TotalPurchase += purchaseAmount;

            playerData.Accrued = calculatedAccrued;
            playerData.Purchase += purchaseAmount;
            playerData = UpdatePlayerLevel(itIsThePresent, now, player, playerData, from);
            campaignPlayersData[player] = playerData;
        }

        private CashbackData UpdatePlayerLevel(bool itIsThePresent, DateTime now, Player player, CashbackData lastCashbackData, Subscriber from)
        {
            StepCashback newStep = StageCashback(lastCashbackData.Purchase);
            if (newStep == null) throw new GameEngineException($"Please check the player purchase total.");

            int newIndexStep = rangeCashbackStages.IndexOf(newStep);
            if (lastCashbackData.CurrentIndexStep != newIndexStep)
            {
                lastCashbackData.CurrentIndexStep = newIndexStep;

                if (itIsThePresent)
                {
                    player.AddMessage(from, $"{Campaign.Name}: Congratulations! You just unlock {newStep.PercentageTxt}% Cashback.");
                    int indexStep = rangeCashbackStages.IndexOf(newStep);
                    SaleCampaignEvent campaignEvent;
                    LabeledResource resource = Campaign.Resources.FirstOrDefault(labelResource => labelResource.Name == RewardBasedEvent.KeyResource);
                    if (resource == null)
                    {
                        campaignEvent = new RewardBasedEvent(now, Campaign.PromotionNumber, Campaign.MaxLevels, Campaign.CampaignType, Campaign.RewardType, Campaign.ScaleType, player.Id, indexStep, newStep.PercentageCashback, string.Empty);
                    }
                    else
                    {
                        campaignEvent = new RewardBasedEvent(now, Campaign.PromotionNumber, Campaign.MaxLevels, Campaign.CampaignType, Campaign.RewardType, Campaign.ScaleType, player.Id, indexStep, newStep.PercentageCashback, resource.Url);
                    }

                    PlatformMonitor.GetInstance().WhenNewEvent(campaignEvent);
                }

            }
            return lastCashbackData;
        }

        private decimal CashbackAmount(Player player, decimal purchaseAmount, CashbackData playerData)
        {
            decimal totalInPurchase = playerData.Purchase + purchaseAmount;

            StepCashback step = StageCashback(totalInPurchase);
            if (step == null) throw new GameEngineException($"Please check the player purchase total and player purcharse amount.");

            StepCashback lastStep = StageCashback(playerData.Purchase);

            decimal result = totalInPurchase * step.PercentageCashback;
            result = Math.Round(result, 2, MidpointRounding.AwayFromZero);
            if (result > MaxAccruedPlayer)
            {
                result = MaxAccruedPlayer;
            }

            if (lastStep != null && step != lastStep)
            {
                lastStep.AppendAccrued(0, playerData.Accrued);
                lastStep.CashbackAccruedInStep(player, 0, playerData.Accrued);
                step.AppendAccrued(result, 0);
                step.CashbackAccruedInStep(player, result, 0);
            }
            else
            {
                step.AppendAccrued(result, playerData.Accrued);
                step.CashbackAccruedInStep(player, result, playerData.Accrued);
            }

            return result;
        }

        internal StepCashback StageCashback(decimal amount)
        {
            if (amount < 0) throw new ArgumentException(nameof(amount));
            if (this is FlatCashbackRewarder)
            {
                if (!rangeCashbackStages.Any()) throw new GameEngineException("Flat was not setup yet.");
                StepCashback result = rangeCashbackStages.First();
                return result;
            }
            else if (this is TieredCashbackRewarder)
            {
                decimal topLimit = rangeCashbackStages.Last().UpperLimit;
                if (amount >= topLimit)
                {
                    return rangeCashbackStages.Last();
                }
                if (!rangeCashbackStages.Any()) throw new GameEngineException("Tiered was not setup yet.");
                StepCashback result = rangeCashbackStages.Find(stage => amount >= stage.LowerLimit && amount < stage.UpperLimit);
                return result;
            }
            else
            {
                throw new GameEngineException("This type operator is not supported yet.");
            }
        }

        internal override bool IsConfigured()
        {
            return OperatorWasDefined;
        }

        internal decimal ClaimCashback(bool itIsThePresent, int storeId, DateTime now, Player player, string who, int domainId)
        {
            if (now == DateTime.MinValue) throw new ArgumentException(nameof(now));
            if (storeId <= 0) throw new GameEngineException($"The Store ID {storeId} is invalid.");
            if (player == null) throw new ArgumentException(nameof(player));
            if (string.IsNullOrWhiteSpace(who)) throw new GameEngineException($"Who is Invalid.");
            if (domainId <= 0) throw new GameEngineException($"The Domain ID {domainId} is invalid.");

            string campaignCountryCode = Campaign.TimeZone;
            string serverCountryCode = Campaign.Company.TimeZone;
            DateTime fixedTzNow = TimeZoneConverter.Instance.ConvertTime(now, serverCountryCode, campaignCountryCode);
            if (fixedTzNow < Campaign.StartDate) throw new GameEngineException("It is not allowed to register a purchase in the campaign if it has not started yet");

            string exceptionMessage = Campaign.IsApplicableAtMainAction(fixedTzNow, player, 0, storeId);
            if (!string.IsNullOrEmpty(exceptionMessage)) throw new GameEngineException($"Not applicable action: " + exceptionMessage);

            if (!Campaign.IsParticipating(player)) throw new GameEngineException($"Player {player.AccountNumber} does not exist in Cashback campaign.");

            CashbackData playerData;
            if (!campaignPlayersData.TryGetValue(player, out playerData))
            {
                throw new GameEngineException($"Player {player.AccountNumber} does not exist in Cashback campaign.");
            }

            if (Campaign.Budget.CurrencyCode == Currencies.CODES.FP.ToString() || Campaign.Budget.CurrencyCode == Currencies.CODES.KRWFP.ToString())
            {
                Campaign.IncrementGivenTimesNumber();

                decimal amount = playerData.Accrued;
                string currencyCode = Campaign.Budget.CurrencyCode.ToString();
                Currency currency = Currency.Factory(currencyCode, amount);
                Campaign.Budget.Disburse(currency);

                Customer customer = player.Customer;
                Campaign.RegisterBeneficiary(customer);
            }

            if (itIsThePresent)
            {
                string reference = $"{Campaign.PromotionNumber}-{fixedTzNow.ToString("yy")}{fixedTzNow.ToString("HHmmss")}{fixedTzNow.DayOfYear.ToString("000")}{fixedTzNow.ToString("yy")}";
                if (reference.Length > Movement.MAX_REFERENCE_LENGTH) reference = reference.Substring(0, Movement.MAX_REFERENCE_LENGTH);
                string accountNumber = Campaign.Budget.CurrencyCode.ToString();
                using (KafkaMessagesBuffer buffer = new KafkaMessagesBuffer(itIsThePresent, Integration.Kafka.TopicForDeposits))
                {
                    DepositMessage deposit = new DepositMessage(
                        player.Customer.AccountNumber,
                        Campaign.PromotionNumber,
                        Coinage.Coin(Campaign.Budget.CurrencyCode),
                        storeId,
                        playerData.Accrued,
                        who,
                        Campaign.Description,
                        reference,
                        accountNumber,
                        WholePaymentProcessor.NoPaymentProcessor,
                        string.Empty,
                        player.Agent,
                        Campaign.Name
                        );
                    buffer.Send(deposit);
                }
            }
            decimal result = playerData.Accrued;
            decimal lastPurchase = playerData.Purchase;
            playerData.TotalAccruedDeposited += playerData.Accrued;
            Campaign.TotalAmountDeposited += playerData.Accrued;
            TotalAmountAccrued -= result;

            playerData.Purchase = 0;
            playerData.CurrentIndexStep = -1;
            playerData.Accrued = 0;

            TotalClaimTimes++;
            playerData.ClaimCounter++;
            campaignPlayersData[player] = playerData;


            StepCashback step = StageCashback(lastPurchase);
            if (step == null) throw new GameEngineException($"Please check the player purchase total, does not match any stage.");
            step.TotalClaimTimes++;
            step.CashbackAccruedInStep(player, 0, result);
            step.AppendDepositedInStep(player, result);
            step.AppendAccrued(0, result);
            step.TotalDeposited += result;

            int stageIndex = rangeCashbackStages.IndexOf(step);
            Campaign.StoreCampaignReward(itIsThePresent, player.Customer.AccountNumber, -1, step.Title, result, stageIndex, fixedTzNow, storeId, domainId);

            return result;
        }

        internal bool CanPlayerClaim(DateTime now, Player player)
        {
            if (now == DateTime.MinValue) throw new ArgumentException(nameof(now));
            if (player == null) throw new ArgumentException(nameof(player));


            bool isParticipating = Campaign.IsParticipating(player);
            if (!isParticipating) return false;

            bool isInClaimInterval = Campaign.IsInClaimInterval(now);

            bool playerHasAccruedAmount = false;
            foreach (StepCashback step in rangeCashbackStages)
            {
                decimal accrued = step.CashbackAccruedInStepByPlayer(player);
                if (accrued > 0)
                {
                    playerHasAccruedAmount = true;
                    break;
                }
            }

            return isInClaimInterval && playerHasAccruedAmount;
        }

        internal decimal AccruedCashback(Player player)
        {
            if (player == null) throw new ArgumentException(nameof(player));

            CashbackData playerData;
            if (!campaignPlayersData.TryGetValue(player, out playerData))
            {
                return 0;
            }
            return playerData.Accrued;
        }

        internal int ClaimCounter(Player player)
        {
            if (player == null) throw new ArgumentException(nameof(player));

            CashbackData playerData;
            if (!campaignPlayersData.TryGetValue(player, out playerData))
            {
                return 0;
            }
            return playerData.ClaimCounter;
        }

        internal decimal TotalPurchase(Player player)
        {
            if (player == null) throw new ArgumentException(nameof(player));

            CashbackData playerData;
            if (!campaignPlayersData.TryGetValue(player, out playerData))
            {
                return 0;
            }
            return playerData.TotalPurchase;
        }

        internal override decimal TotalAmountDepositedByPlayer(Player player)
        {
            if (player == null) throw new ArgumentException(nameof(player));

            CashbackData playerData;
            if (!campaignPlayersData.TryGetValue(player, out playerData))
            {
                return 0;
            }
            return playerData.TotalAccruedDeposited;
        }

        internal override decimal TotalAmountByPlayer(Player player)
        {
            if (player == null) throw new ArgumentException(nameof(player));

            CashbackData playerData;
            if (!campaignPlayersData.TryGetValue(player, out playerData))
            {
                return 0;
            }
            return playerData.TotalPurchase;
        }

        internal bool IsParticipating(Player playerId)
        {
            if (playerId == null) throw new ArgumentException(nameof(playerId));
            return campaignPlayersData.ContainsKey(playerId);
        }

        internal int PlayerLevel(Player player)
        {
            if (player == null) throw new ArgumentException(nameof(player));

            CashbackData playerData;
            if (!campaignPlayersData.TryGetValue(player, out playerData))
            {
                return -1;
            }
            return playerData.CurrentIndexStep;
        }

        internal override void Clear()
        {
            for (int index = 0; index < this.campaignPlayersData.Count; index++)
            {
                Player player = this.campaignPlayersData.Keys.ElementAt(index);
                CashbackData playerData = campaignPlayersData[player];
                //Limpiando Estadisticas
                TotalAmountAccrued += playerData.Accrued;
                //Limpiando el Player
                playerData.CurrentIndexStep = -1;
                playerData.Purchase = 0;
                playerData.Accrued = 0;
                this.campaignPlayersData[player] = playerData;
            }
        }
    }

    internal class FlatCashbackRewarder : CashbackRewarder
    {
        internal FlatCashbackRewarder(CampaignBase campaign) : base(campaign)
        {
        }

        internal static string CheckCashbackStage(decimal percentage)
        {
            if (percentage < 0 || percentage > 1) return $"The percentage amount {percentage} cannot be lower than 0 or greater than 1.";

            return string.Empty;
        }

        internal override CampaignRewarder Copy(CampaignBase campaignBase)
        {
            FlatCashbackRewarder cashbackRewarder = new FlatCashbackRewarder(campaignBase);
            foreach (StepCashback step in rangeCashbackStages)
            {
                cashbackRewarder.rangeCashbackStages.Add(step.Copy());
            }
            return cashbackRewarder;
        }

        internal void SetupCashbackStage(decimal percentage)
        {
            if (percentage < 0 || percentage > 1) throw new GameEngineException($"The percentage {percentage} cannot be lower than 0 or greater than 1.");

            StepCashback step = new StepCashback(0, 0, percentage);
            step.Title = $"%{step.PercentageTxt} in cashback";

            rangeCashbackStages.Clear();
            rangeCashbackStages.Add(step);
        }
    }

    internal class TieredCashbackRewarder : CashbackRewarder
    {
        internal TieredCashbackRewarder(CampaignBase campaign) : base(campaign)
        {
        }

        internal static string CheckCashbackStage(decimal minPurchaseAllowed, List<decimal> lowerLimits, List<decimal> upperLimits, List<decimal> percentages)
        {
            if (lowerLimits == null) return $"The {lowerLimits} list cannot be Null.";
            if (upperLimits == null) return $"The {upperLimits} list cannot be Null.";
            if (percentages == null) return $"The {percentages} list cannot be Null.";
            if (!lowerLimits.Any() || !upperLimits.Any() || !percentages.Any()) return $"The setup list does not contain any records.";
            if (lowerLimits.Count != upperLimits.Count && lowerLimits.Count != percentages.Count) return "The size of lists are different.";

            decimal firstStage = lowerLimits.First();
            if (firstStage != minPurchaseAllowed) return $"The first stage {firstStage} does not match with the min purchase allowed {minPurchaseAllowed}.";

            decimal lastLimit = 0;
            for (int i = 0; i < lowerLimits.Count; i++)
            {
                decimal lower = lowerLimits[i];
                decimal upper = upperLimits[i];
                if (upper <= lower) return $"The level {i + 1}, variable {nameof(upper)}={upper} needs to be greater than  variable {nameof(lower)}={lower}.";
                if (upper <= lastLimit) return $"The level {i + 1}, variable {nameof(upper)}={upper} needs to be greater than variable {nameof(lastLimit)}={lastLimit}.";

                if (i == 0)
                {
                    lastLimit = upper;
                    continue;
                }

                if (lastLimit != lower)
                {
                    return $"The current limit {nameof(lower)}={lower} in level {i + 1} does not match with previous limit {nameof(lastLimit)}={lastLimit} level {i}.";
                }

                lastLimit = upper;
            }
            return string.Empty;
        }

        internal override CampaignRewarder Copy(CampaignBase campaignBase)
        {
            TieredCashbackRewarder cashbackRewarder = new TieredCashbackRewarder(campaignBase);
            foreach (StepCashback step in rangeCashbackStages)
            {
                cashbackRewarder.rangeCashbackStages.Add(step.Copy());
            }
            return cashbackRewarder;
        }

        internal void SetupCashbackStage(List<decimal> lowerLimits, List<decimal> upperLimits, List<decimal> percentages)
        {
            string errorMessage = CheckCashbackStage(Campaign.MinPurchaseAllowed, lowerLimits, upperLimits, percentages);
            if (!string.IsNullOrEmpty(errorMessage)) throw new GameEngineException(errorMessage);

            rangeCashbackStages.Clear();
            int totalSteps = lowerLimits.Count;
            for (int i = 0; i < totalSteps; i++)
            {
                decimal lower = lowerLimits[i];
                decimal upper = upperLimits[i];
                decimal percentage = percentages[i];

                StepCashback step = new StepCashback(lower, upper, percentage);
                if (i == totalSteps - 1)
                {
                    step.Title = $"More than ${step.LowerLimit}";
                }
                else
                {
                    step.Title = $"Between ${step.LowerLimit} to ${step.UpperLimit}";
                }
                rangeCashbackStages.Add(step);
            }
        }
    }

    internal abstract class StepBase : Objeto
    {
        internal decimal LowerLimit { get; set; }
        internal decimal UpperLimit { get; set; }
        internal int TotalClaimTimes { get; set; }
        internal decimal TotalDeposited { get; set; }

        internal decimal TotalAccrued { get; private set; }
        internal string Title { get; set; }

        protected Dictionary<Player, StepPlayerData> playerDataInStep;

        internal abstract string StepType { get; }

        internal StepBase(decimal lowerLimit, decimal upperLimit)
        {
            if (lowerLimit < 0) throw new ArgumentException(nameof(lowerLimit));
            if (upperLimit < 0) throw new ArgumentException(nameof(upperLimit));

            this.LowerLimit = lowerLimit;
            this.UpperLimit = upperLimit;
            this.TotalAccrued = 0;
            this.TotalClaimTimes = 0;

            this.playerDataInStep = new Dictionary<Player, StepPlayerData>();
        }

        internal int TotalPlayer
        {
            get 
            {
                int totalPlayersAccrued = 0;
                foreach (var playerData in playerDataInStep)
                {
                    if (playerData.Value.Accrued > 0)
                    {
                        totalPlayersAccrued++;
                    }
                }
                return totalPlayersAccrued;
            }
        }

        internal void AppendAccrued(decimal amount, decimal lastAmount)
        {
            if (amount < 0) throw new ArgumentException(nameof(amount));
            if (lastAmount < 0) throw new ArgumentException(nameof(lastAmount));
            TotalAccrued -= lastAmount;
            TotalAccrued += amount;
        }

        internal void CashbackAccruedInStep(Player player, decimal result, decimal lastAmount)
        {
            if (player == null) throw new ArgumentNullException(nameof(player));
            if (result < 0) throw new ArgumentException(nameof(result));
            if (lastAmount < 0) throw new ArgumentException(nameof(lastAmount));

            StepPlayerData playerAccruedInStep;
            if (!playerDataInStep.TryGetValue(player, out playerAccruedInStep))
            {
                playerDataInStep.Add(player, playerAccruedInStep);
            }
            playerAccruedInStep.Accrued -= lastAmount;
            playerAccruedInStep.Accrued += result;

            

            playerDataInStep[player] = playerAccruedInStep;
        }

        internal decimal CashbackAccruedInStepByPlayer(Player player)
        {
            if (player == null) throw new ArgumentNullException(nameof(player));

            StepPlayerData playerAccruedInStep;
            if (!playerDataInStep.TryGetValue(player, out playerAccruedInStep))
            {
                return 0;
            }
            return playerAccruedInStep.Accrued;
        }

        internal void AppendDepositedInStep(Player player, decimal result)
        {
            if (player == null) throw new ArgumentNullException(nameof(player));
            if (result < 0) throw new ArgumentException(nameof(result));

            StepPlayerData playerAccruedInStep;
            if (!playerDataInStep.TryGetValue(player, out playerAccruedInStep))
            {
                throw new GameEngineException($"Player {player.AccountNumber} was not found.");
            }
            playerAccruedInStep.Deposited += result;

            playerDataInStep[player] = playerAccruedInStep;
        }

        internal decimal DepositedInStepByPlayer(Player player)
        {
            if (player == null) throw new ArgumentNullException(nameof(player));

            StepPlayerData playerAccruedInStep;
            if (!playerDataInStep.TryGetValue(player, out playerAccruedInStep))
            {
                return 0;
            }
            return playerAccruedInStep.Deposited;
        }

        internal void Clear()
        {
            this.playerDataInStep.Clear();
        }

        internal abstract StepBase Copy();


        internal struct StepPlayerData
        {
            internal decimal Accrued { get; set; }
            internal decimal Deposited { get; set; }
        }

    }

    [Puppet]
    internal class StepSpin : StepBase
    {
        internal bool WasModified { get; set; }

        internal override string StepType => this.GetType().Name;

        public StepSpin(decimal lowerLimit, decimal upperLimit) : base(lowerLimit, upperLimit)
        {
        }

        internal override StepSpin Copy()
        {
            StepSpin stepSpin = new StepSpin(this.LowerLimit, this.UpperLimit);
            stepSpin.WasModified = this.WasModified;
            return stepSpin;
        }
    }

    [Puppet]
    internal class StepCashback : StepBase
    {
        internal override string StepType => this.GetType().Name;

        internal decimal PercentageCashback { get; private set; }
        internal string PercentageTxt => (PercentageCashback * 100).ToString("F0");

        internal StepCashback(decimal lowerLimit, decimal upperLimit, decimal percentage) : base(lowerLimit, upperLimit)
        {
            if (lowerLimit < 0) throw new ArgumentException(nameof(lowerLimit));
            if (upperLimit < 0) throw new ArgumentException(nameof(upperLimit));
            if (percentage < 0 || percentage > 1) throw new GameEngineException($"The {percentage} cannot be lower than 0 or greater than 1.");

            this.PercentageCashback = percentage;
        }

        internal override StepCashback Copy()
        {
            StepCashback stepCashback = new StepCashback(this.LowerLimit, this.UpperLimit, this.PercentageCashback);
            return stepCashback;
        }
    }
}
