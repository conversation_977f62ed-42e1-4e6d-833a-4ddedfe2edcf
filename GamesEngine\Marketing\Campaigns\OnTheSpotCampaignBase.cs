﻿using GamesEngine.Bets;
using GamesEngine.Business;
using GamesEngine.Business.Marketing;
using GamesEngine.Finance;
using GamesEngine.Marketing.Campaigns.Base;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GamesEngine.Marketing.Campaigns
{
    internal abstract class OnTheSpotCampaignBase : CampaignBase
    {
        internal OnTheSpotCampaignBase(bool itIsThePresent, Company company, int promotionNumber, DateTime createdDate, string name, Budget budget, string timeZone) : base(company, promotionNumber, createdDate, name, budget, timeZone)
        {
            if (company == null) throw new GameEngineException(nameof(company));
            if (createdDate == DateTime.MinValue) throw new GameEngineException(nameof(createdDate));
            if (string.IsNullOrWhiteSpace(name)) throw new GameEngineException(nameof(name));
            if (budget == null) throw new GameEngineException(nameof(budget));
            if (string.IsNullOrWhiteSpace(timeZone)) throw new GameEngineException(nameof(timeZone));

            if (itIsThePresent)
            {
                MarketingStorage marketingStorage = MarketingStorageCampaignInstance();
                if (marketingStorage != null)
                {
                    DateTime startDateCampaign = StartDate;
                    DateTime deadLine = DeadLine;
                    string currencyCode = SpinKick.CURRENCY_CODE;
                    marketingStorage.StoreCampaign(promotionNumber, createdDate, name, currencyCode, currencyCode, InitialBudget, IsUnlimitedBudget, startDateCampaign, deadLine, 1, Description, deadLine, CampaignType, CampaignType, CampaignType);
                }

                // crear la cuenta en cashier y depositar todos los premios
                //Integration.MarkFragmentsTableAsCreated(AccountNumber);
            }
        }

        internal override void ClearSaleCampaign(bool itIsThePresent, DateTime now, bool wait)
        {
            // No need to clear the campaign
        }

        internal override int CopyCampaign(bool itIsThePresent, DateTime now, string newName, DateTime newStartDate)
        {
            throw new GameEngineException("This campaign cannot be copied.");
        }

        internal override bool IsApplicable(DateTime now, Player player, int domainID)
        {
            // NOTHIN TO CHECK
            return false;
        }

        internal override bool IsApplicable(DateTime now, int domainId, int storeId, Currencies.CODES currency)
        {
            // NOTHIN TO CHECK
            return false;
        }

        internal override bool IsApplicable(DateTime now, int domainId, int storeId)
        {
            // NOTHIN TO CHECK
            return false;
        }

        internal override string IsApplicableAtMainAction(DateTime now, Player player, int domainId, int storeId)
        {
            // NOTHIN TO CHECK
            return string.Empty;
        }

        internal override bool IsApplicableAtRegisterPurchase(DateTime now, Player player, int domainId, int storeId, Currencies.CODES currency, decimal amount)
        {
            // NOTHIN TO CHECK
            return false;
        }

        internal override bool IsCampaignActive(DateTime now)
        {
            bool result = (now.Date >= startDate && now <= DeadLine);
            return result;
        }

        internal override bool IsPurchaseApplicable(bool itIsThePresent, Player player, DateTime purchaseDate, decimal purchaseAmount, int storeId, int domainId, int agentId, Currencies.CODES currency)
        {
            // NOTHIN TO CHECK
            return false;
        }

        internal override void RaiseNotification(int notificationNumber, DateTime now, string playerId)
        {
            // NO NOTIFICATION TO RAISE
        }

        internal override void RegisterPurchase(bool itIsThePresent, Player player, DateTime purchaseDate, decimal purchaseAmount, int storeId, int domainId, int agentId, Currencies.CODES currency)
        {
            // NO PURCHASE TO REGISTER
        }

        internal override int TimeOfNextAvailable(DateTime now)
        {
            if (now == DateTime.MinValue) throw new GameEngineException(nameof(now));

            // NO TIME OF NEXT AVAILABLE
            return 0;
        }

        internal override void UpdateCampaignState(DateTime now)
        {
            // NO CAMPAIGN STATE TO UPDATE
        }

        internal override int PlayerLevel(Player playerId)
        {
            if (playerId == null) throw new GameEngineException(nameof(playerId));

            if (IsParticipating(playerId)) return 0;

            // NO PLAYER LEVEL IS -1
            return -1;
        }

        internal override int PlayerLevelNoIndex(Player playerId)
        {
            // NO PLAYER LEVEL NO INDEX IS 0
            return 0;
        }
    }
}
