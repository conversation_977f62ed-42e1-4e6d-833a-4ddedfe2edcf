﻿<?xml version="1.0" encoding="utf-8"?>
<log4net>
  <appender name="ConsoleAppender" type="log4net.Appender.ConsoleAppender" >
    <layout type="log4net.Layout.PatternLayout">
      <conversionPattern value="%date %thread | %message%newline" />
    </layout>
  </appender>
  <appender name="database" type="log4net.Appender.RollingFileAppender">
    <file value="/tmp/linebiapi/database.log" />
    <appendToFile value="true" />
    <rollingStyle value="Size" />
    <maxSizeRollBackups value="-1" />
    <maximumFileSize value="5MB" />
    <staticLogFileName value="true" />
    <countDirection value="1"/>
    <layout type="log4net.Layout.PatternLayout">
      <conversionPattern value="%date %thread | %message%newline" />
    </layout>
  </appender>
  <appender name="emails" type="log4net.Appender.RollingFileAppender">
    <file value="/tmp/linebiapi/emails.log" />
    <appendToFile value="true" />
    <rollingStyle value="Size" />
    <maxSizeRollBackups value="-1" />
    <maximumFileSize value="5MB" />
    <staticLogFileName value="true" />
    <countDirection value="1"/>
    <layout type="log4net.Layout.PatternLayout">
      <conversionPattern value="%date %thread | %message%newline" />
    </layout>
  </appender>
  <appender name="scripts" type="log4net.Appender.RollingFileAppender">
    <file value="/tmp/linebiapi/scripts.log" />
    <appendToFile value="true" />
    <rollingStyle value="Size" />
    <maxSizeRollBackups value="-1" />
    <maximumFileSize value="5MB" />
    <staticLogFileName value="true" />
    <countDirection value="1"/>
    <layout type="log4net.Layout.PatternLayout">
      <conversionPattern value="%date %thread | %message%newline" />
    </layout>
  </appender>
  <root>
    <level value="ALL" />
    <appender-ref ref="ConsoleAppender" />
  </root>
  <logger additivity="false" name="database">
    <level value="DEBUG"/>
    <appender-ref ref="database" />
  </logger>
  <logger additivity="false" name="emails">
    <level value="DEBUG"/>
    <appender-ref ref="emails" />
  </logger>
  <logger additivity="false" name="scripts">
    <level value="DEBUG"/>
    <appender-ref ref="scripts" />
  </logger>
</log4net>