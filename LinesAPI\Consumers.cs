﻿using Elasticsearch.Net;
using GamesEngine.Exchange.Persistance;
using GamesEngine.Middleware;
using GamesEngine.Middleware.Providers.Bet365;
using GamesEngine.Settings;
using Puppeteer.EventSourcing;
using System;
using System.Collections.Generic;
using GamesEngine.Middleware.Providers;
using System.Text;
using static GamesEngine.Middleware.Providers.Response;
using GamesEngine;
using System.Runtime.Serialization;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using System.Linq;
using GamesEngine.Games;
using System.Globalization;
using Newtonsoft.Json.Linq;
using GamesEngine.Games.Lines;
using static GamesEngineMocks.LinesMocks;
using System.Threading.Tasks;
using System.IO;
using GamesEngine.Finance;

namespace LinesAPI
{
	public class Consumers
	{
		public static Storage Storage { get; } = new Storage();
		public void CreateConsumerForTopics()
		{
			new CustomersConsumer($"{Integration.Kafka.Group}", $"{Integration.Kafka.TopicForCustomers}").StartListening();
			new BetAPIConsumer($"{Integration.Kafka.Group}", $"{Integration.Kafka.TopicForLinesETL}").StartListening();
			new CatalogConsumer(ValidateMessageOwnership, GetActor, $"{KafkaMessage.LINES_CONSUMER_PREFIX}{Integration.Kafka.Group}", Integration.Kafka.TopicForCatalog).StartListening();
			//BetAPIConsumer consumer = new BetAPIConsumer($"{Integration.Kafka.Group}", $"{Integration.Kafka.TopicForLinesETL}");

			//consumer.OnMessageBeforeCommit("5￻2￻N/A￻N/A￻0￻N/A￻3￻1￻[\r\n  {\r\n    \"marketId\": \"1.185769399\",\r\n    \"marketName\": \"Over/Under 3.5 Goals\",\r\n    \"marketStartTime\": \"2021-07-29T00:00:00Z\",\r\n    \"description\": {\r\n      \"persistenceEnabled\": true,\r\n      \"bspMarket\": false,\r\n      \"marketTime\": \"2021-07-29T00:00:00Z\",\r\n      \"suspendTime\": \"2021-07-29T00:00:00Z\",\r\n      \"bettingType\": \"ODDS\",\r\n      \"turnInPlayEnabled\": true,\r\n      \"marketType\": \"OVER_UNDER_35\",\r\n      \"regulator\": \"MALTA LOTTERIES AND GAMBLING AUTHORITY\",\r\n      \"marketBaseRate\": 6.5,\r\n      \"discountAllowed\": true,\r\n      \"wallet\": \"UK wallet\",\r\n      \"rules\": \"<!--Football - Over/Unders --><br>How many goals will be scored in this match.<br> All bets apply to Full Time according to the match officials, plus any stoppage time. Extra-time/penalty shoot-outs are not included.<br><br></b>For further information please see <a href=http://content.betfair.com/aboutus/content.asp?sWhichKey=Rules%20and%20Regulations#undefined.do style=color:0163ad; text-decoration: underline; target=_blank>Rules & Regs<br><br>\\n\",\r\n      \"rulesHasDate\": true,\r\n      \"priceLadderDescription\": {\r\n        \"type\": \"CLASSIC\"\r\n      }\r\n    },\r\n    \"totalMatched\": 0.0,\r\n    \"runners\": [\r\n      {\r\n        \"selectionId\": 1222344,\r\n        \"runnerName\": \"Under 3.5 Goals\",\r\n        \"handicap\": 0.0,\r\n        \"sortPriority\": 1,\r\n        \"metadata\": {\r\n          \"runnerId\": \"1222344\"\r\n        },\r\n        \"price\": 0.0\r\n      },\r\n      {\r\n        \"selectionId\": 1222345,\r\n        \"runnerName\": \"Over 3.5 Goals\",\r\n        \"handicap\": 0.0,\r\n        \"sortPriority\": 2,\r\n        \"metadata\": {\r\n          \"runnerId\": \"1222345\"\r\n        },\r\n        \"price\": 0.0\r\n      }\r\n    ],\r\n    \"eventType\": {\r\n      \"id\": \"1\",\r\n      \"name\": \"Soccer\"\r\n    },\r\n    \"competition\": {\r\n      \"id\": \"3057583\",\r\n      \"name\": \"Ecuadorian Serie B\"\r\n    },\r\n    \"event\": {\r\n      \"id\": \"30736701\",\r\n      \"name\": \"Guayaquil SC v Chacaritas SC\",\r\n      \"countryCode\": \"EC\",\r\n      \"timezone\": \"GMT\",\r\n      \"openDate\": \"2021-07-29T00:00:00Z\"\r\n    }\r\n  },\r\n  {\r\n    \"marketId\": \"1.185769400\",\r\n    \"marketName\": \"Asian Handicap\",\r\n    \"marketStartTime\": \"2021-07-29T00:00:00Z\",\r\n    \"description\": {\r\n      \"persistenceEnabled\": true,\r\n      \"bspMarket\": false,\r\n      \"marketTime\": \"2021-07-29T00:00:00Z\",\r\n      \"suspendTime\": \"2021-07-29T00:00:00Z\",\r\n      \"bettingType\": \"ASIAN_HANDICAP_DOUBLE_LINE\",\r\n      \"turnInPlayEnabled\": true,\r\n      \"marketType\": \"ASIAN_HANDICAP\",\r\n      \"regulator\": \"MALTA LOTTERIES AND GAMBLING AUTHORITY\",\r\n      \"marketBaseRate\": 6.5,\r\n      \"discountAllowed\": true,\r\n      \"wallet\": \"UK wallet\",\r\n      \"rules\": \"<!--Football - Asian Handicap --><br>Who will win this match with the relevant handicaps applied?<br><br> Each under/over line will be settled, and commission charged, individually. <br>For further information on Asian Handicap betting please click on this link <a href=\\\"https://support.betfair.com/app/answers/detail/a_id/6418\\\">Asian Handicap Betting Explained</a> <br> All bets apply to Full Time according to the match officials, plus any stoppage time. Extra-time/penalty shoot-outs are not included.<br><br></b>For further information please see <a href=http://content.betfair.com/aboutus/content.asp?sWhichKey=Rules%20and%20Regulations#undefined.do style=color:0163ad; text-decoration: underline; target=_blank>Rules & Regs<br><br>\\n\",\r\n      \"rulesHasDate\": true,\r\n      \"lineRangeInfo\": {\r\n        \"maxUnitValue\": 4.0,\r\n        \"minUnitValue\": -4.0,\r\n        \"interval\": 0.25,\r\n        \"marketUnit\": \"Goals\"\r\n      },\r\n      \"priceLadderDescription\": {\r\n        \"type\": \"FINEST\"\r\n      }\r\n    },\r\n    \"totalMatched\": 41.523316,\r\n    \"runners\": [\r\n      {\r\n        \"selectionId\": 38844732,\r\n        \"runnerName\": \"Guayaquil SC\",\r\n        \"handicap\": -4.0,\r\n        \"sortPriority\": 1,\r\n        \"price\": 0.0\r\n      },\r\n      {\r\n        \"selectionId\": 25889141,\r\n        \"runnerName\": \"Chacaritas SC\",\r\n        \"handicap\": 4.0,\r\n        \"sortPriority\": 2,\r\n        \"price\": 0.0\r\n      },\r\n      {\r\n        \"selectionId\": 38844732,\r\n        \"runnerName\": \"Guayaquil SC\",\r\n        \"handicap\": -3.75,\r\n        \"sortPriority\": 3,\r\n        \"price\": 0.0\r\n      },\r\n      {\r\n        \"selectionId\": 25889141,\r\n        \"runnerName\": \"Chacaritas SC\",\r\n        \"handicap\": 3.75,\r\n        \"sortPriority\": 4,\r\n        \"price\": 0.0\r\n      },\r\n      {\r\n        \"selectionId\": 38844732,\r\n        \"runnerName\": \"Guayaquil SC\",\r\n        \"handicap\": -3.5,\r\n        \"sortPriority\": 5,\r\n        \"price\": 0.0\r\n      },\r\n      {\r\n        \"selectionId\": 25889141,\r\n        \"runnerName\": \"Chacaritas SC\",\r\n        \"handicap\": 3.5,\r\n        \"sortPriority\": 6,\r\n        \"price\": 0.0\r\n      },\r\n      {\r\n        \"selectionId\": 38844732,\r\n        \"runnerName\": \"Guayaquil SC\",\r\n        \"handicap\": -3.25,\r\n        \"sortPriority\": 7,\r\n        \"price\": 0.0\r\n      },\r\n      {\r\n        \"selectionId\": 25889141,\r\n        \"runnerName\": \"Chacaritas SC\",\r\n        \"handicap\": 3.25,\r\n        \"sortPriority\": 8,\r\n        \"price\": 0.0\r\n      },\r\n      {\r\n        \"selectionId\": 38844732,\r\n        \"runnerName\": \"Guayaquil SC\",\r\n        \"handicap\": -3.0,\r\n        \"sortPriority\": 9,\r\n        \"price\": 0.0\r\n      },\r\n      {\r\n        \"selectionId\": 25889141,\r\n        \"runnerName\": \"Chacaritas SC\",\r\n        \"handicap\": 3.0,\r\n        \"sortPriority\": 10,\r\n        \"price\": 0.0\r\n      },\r\n      {\r\n        \"selectionId\": 38844732,\r\n        \"runnerName\": \"Guayaquil SC\",\r\n        \"handicap\": -2.75,\r\n        \"sortPriority\": 11,\r\n        \"price\": 0.0\r\n      },\r\n      {\r\n        \"selectionId\": 25889141,\r\n        \"runnerName\": \"Chacaritas SC\",\r\n        \"handicap\": 2.75,\r\n        \"sortPriority\": 12,\r\n        \"price\": 0.0\r\n      },\r\n      {\r\n        \"selectionId\": 38844732,\r\n        \"runnerName\": \"Guayaquil SC\",\r\n        \"handicap\": -2.5,\r\n        \"sortPriority\": 13,\r\n        \"price\": 0.0\r\n      },\r\n      {\r\n        \"selectionId\": 25889141,\r\n        \"runnerName\": \"Chacaritas SC\",\r\n        \"handicap\": 2.5,\r\n        \"sortPriority\": 14,\r\n        \"price\": 0.0\r\n      },\r\n      {\r\n        \"selectionId\": 38844732,\r\n        \"runnerName\": \"Guayaquil SC\",\r\n        \"handicap\": -2.25,\r\n        \"sortPriority\": 15,\r\n        \"price\": 0.0\r\n      },\r\n      {\r\n        \"selectionId\": 25889141,\r\n        \"runnerName\": \"Chacaritas SC\",\r\n        \"handicap\": 2.25,\r\n        \"sortPriority\": 16,\r\n        \"price\": 0.0\r\n      },\r\n      {\r\n        \"selectionId\": 38844732,\r\n        \"runnerName\": \"Guayaquil SC\",\r\n        \"handicap\": -2.0,\r\n        \"sortPriority\": 17,\r\n        \"price\": 0.0\r\n      },\r\n      {\r\n        \"selectionId\": 25889141,\r\n        \"runnerName\": \"Chacaritas SC\",\r\n        \"handicap\": 2.0,\r\n        \"sortPriority\": 18,\r\n        \"price\": 0.0\r\n      },\r\n      {\r\n        \"selectionId\": 38844732,\r\n        \"runnerName\": \"Guayaquil SC\",\r\n        \"handicap\": -1.75,\r\n        \"sortPriority\": 19,\r\n        \"price\": 0.0\r\n      },\r\n      {\r\n        \"selectionId\": 25889141,\r\n        \"runnerName\": \"Chacaritas SC\",\r\n        \"handicap\": 1.75,\r\n        \"sortPriority\": 20,\r\n        \"price\": 0.0\r\n      },\r\n      {\r\n        \"selectionId\": 38844732,\r\n        \"runnerName\": \"Guayaquil SC\",\r\n        \"handicap\": -1.5,\r\n        \"sortPriority\": 21,\r\n        \"price\": 0.0\r\n      },\r\n      {\r\n        \"selectionId\": 25889141,\r\n        \"runnerName\": \"Chacaritas SC\",\r\n        \"handicap\": 1.5,\r\n        \"sortPriority\": 22,\r\n        \"price\": 0.0\r\n      },\r\n      {\r\n        \"selectionId\": 38844732,\r\n        \"runnerName\": \"Guayaquil SC\",\r\n        \"handicap\": -1.25,\r\n        \"sortPriority\": 23,\r\n        \"price\": 0.0\r\n      },\r\n      {\r\n        \"selectionId\": 25889141,\r\n        \"runnerName\": \"Chacaritas SC\",\r\n        \"handicap\": 1.25,\r\n        \"sortPriority\": 24,\r\n        \"price\": 0.0\r\n      },\r\n      {\r\n        \"selectionId\": 38844732,\r\n        \"runnerName\": \"Guayaquil SC\",\r\n        \"handicap\": -1.0,\r\n        \"sortPriority\": 25,\r\n        \"price\": 0.0\r\n      },\r\n      {\r\n        \"selectionId\": 25889141,\r\n        \"runnerName\": \"Chacaritas SC\",\r\n        \"handicap\": 1.0,\r\n        \"sortPriority\": 26,\r\n        \"price\": 0.0\r\n      },\r\n      {\r\n        \"selectionId\": 38844732,\r\n        \"runnerName\": \"Guayaquil SC\",\r\n        \"handicap\": -0.75,\r\n        \"sortPriority\": 27,\r\n        \"price\": 0.0\r\n      },\r\n      {\r\n        \"selectionId\": 25889141,\r\n        \"runnerName\": \"Chacaritas SC\",\r\n        \"handicap\": 0.75,\r\n        \"sortPriority\": 28,\r\n        \"price\": 0.0\r\n      },\r\n      {\r\n        \"selectionId\": 38844732,\r\n        \"runnerName\": \"Guayaquil SC\",\r\n        \"handicap\": -0.5,\r\n        \"sortPriority\": 29,\r\n        \"price\": 0.0\r\n      },\r\n      {\r\n        \"selectionId\": 25889141,\r\n        \"runnerName\": \"Chacaritas SC\",\r\n        \"handicap\": 0.5,\r\n        \"sortPriority\": 30,\r\n        \"price\": 0.0\r\n      },\r\n      {\r\n        \"selectionId\": 38844732,\r\n        \"runnerName\": \"Guayaquil SC\",\r\n        \"handicap\": -0.25,\r\n        \"sortPriority\": 31,\r\n        \"price\": 2.18\r\n      },\r\n      {\r\n        \"selectionId\": 25889141,\r\n        \"runnerName\": \"Chacaritas SC\",\r\n        \"handicap\": 0.25,\r\n        \"sortPriority\": 32,\r\n        \"price\": 0.0\r\n      },\r\n      {\r\n        \"selectionId\": 38844732,\r\n        \"runnerName\": \"Guayaquil SC\",\r\n        \"handicap\": 0.0,\r\n        \"sortPriority\": 33,\r\n        \"price\": 0.0\r\n      },\r\n      {\r\n        \"selectionId\": 25889141,\r\n        \"runnerName\": \"Chacaritas SC\",\r\n        \"handicap\": 0.0,\r\n        \"sortPriority\": 34,\r\n        \"price\": 2.14\r\n      },\r\n      {\r\n        \"selectionId\": 38844732,\r\n        \"runnerName\": \"Guayaquil SC\",\r\n        \"handicap\": 0.25,\r\n        \"sortPriority\": 35,\r\n        \"price\": 0.0\r\n      },\r\n      {\r\n        \"selectionId\": 25889141,\r\n        \"runnerName\": \"Chacaritas SC\",\r\n        \"handicap\": -0.25,\r\n        \"sortPriority\": 36,\r\n        \"price\": 0.0\r\n      },\r\n      {\r\n        \"selectionId\": 38844732,\r\n        \"runnerName\": \"Guayaquil SC\",\r\n        \"handicap\": 0.5,\r\n        \"sortPriority\": 37,\r\n        \"price\": 0.0\r\n      },\r\n      {\r\n        \"selectionId\": 25889141,\r\n        \"runnerName\": \"Chacaritas SC\",\r\n        \"handicap\": -0.5,\r\n        \"sortPriority\": 38,\r\n        \"price\": 0.0\r\n      },\r\n      {\r\n        \"selectionId\": 38844732,\r\n        \"runnerName\": \"Guayaquil SC\",\r\n        \"handicap\": 0.75,\r\n        \"sortPriority\": 39,\r\n        \"price\": 0.0\r\n      },\r\n      {\r\n        \"selectionId\": 25889141,\r\n        \"runnerName\": \"Chacaritas SC\",\r\n        \"handicap\": -0.75,\r\n        \"sortPriority\": 40,\r\n        \"price\": 0.0\r\n      },\r\n      {\r\n        \"selectionId\": 38844732,\r\n        \"runnerName\": \"Guayaquil SC\",\r\n        \"handicap\": 1.0,\r\n        \"sortPriority\": 41,\r\n        \"price\": 0.0\r\n      },\r\n      {\r\n        \"selectionId\": 25889141,\r\n        \"runnerName\": \"Chacaritas SC\",\r\n        \"handicap\": -1.0,\r\n        \"sortPriority\": 42,\r\n        \"price\": 0.0\r\n      },\r\n      {\r\n        \"selectionId\": 38844732,\r\n        \"runnerName\": \"Guayaquil SC\",\r\n        \"handicap\": 1.25,\r\n        \"sortPriority\": 43,\r\n        \"price\": 0.0\r\n      },\r\n      {\r\n        \"selectionId\": 25889141,\r\n        \"runnerName\": \"Chacaritas SC\",\r\n        \"handicap\": -1.25,\r\n        \"sortPriority\": 44,\r\n        \"price\": 0.0\r\n      },\r\n      {\r\n        \"selectionId\": 38844732,\r\n        \"runnerName\": \"Guayaquil SC\",\r\n        \"handicap\": 1.5,\r\n        \"sortPriority\": 45,\r\n        \"price\": 0.0\r\n      },\r\n      {\r\n        \"selectionId\": 25889141,\r\n        \"runnerName\": \"Chacaritas SC\",\r\n        \"handicap\": -1.5,\r\n        \"sortPriority\": 46,\r\n        \"price\": 0.0\r\n      },\r\n      {\r\n        \"selectionId\": 38844732,\r\n        \"runnerName\": \"Guayaquil SC\",\r\n        \"handicap\": 1.75,\r\n        \"sortPriority\": 47,\r\n        \"price\": 0.0\r\n      },\r\n      {\r\n        \"selectionId\": 25889141,\r\n        \"runnerName\": \"Chacaritas SC\",\r\n        \"handicap\": -1.75,\r\n        \"sortPriority\": 48,\r\n        \"price\": 0.0\r\n      },\r\n      {\r\n        \"selectionId\": 38844732,\r\n        \"runnerName\": \"Guayaquil SC\",\r\n        \"handicap\": 2.0,\r\n        \"sortPriority\": 49,\r\n        \"price\": 0.0\r\n      },\r\n      {\r\n        \"selectionId\": 25889141,\r\n        \"runnerName\": \"Chacaritas SC\",\r\n        \"handicap\": -2.0,\r\n        \"sortPriority\": 50,\r\n        \"price\": 0.0\r\n      },\r\n      {\r\n        \"selectionId\": 38844732,\r\n        \"runnerName\": \"Guayaquil SC\",\r\n        \"handicap\": 2.25,\r\n        \"sortPriority\": 51,\r\n        \"price\": 0.0\r\n      },\r\n      {\r\n        \"selectionId\": 25889141,\r\n        \"runnerName\": \"Chacaritas SC\",\r\n        \"handicap\": -2.25,\r\n        \"sortPriority\": 52,\r\n        \"price\": 0.0\r\n      },\r\n      {\r\n        \"selectionId\": 38844732,\r\n        \"runnerName\": \"Guayaquil SC\",\r\n        \"handicap\": 2.5,\r\n        \"sortPriority\": 53,\r\n        \"price\": 0.0\r\n      },\r\n      {\r\n        \"selectionId\": 25889141,\r\n        \"runnerName\": \"Chacaritas SC\",\r\n        \"handicap\": -2.5,\r\n        \"sortPriority\": 54,\r\n        \"price\": 0.0\r\n      },\r\n      {\r\n        \"selectionId\": 38844732,\r\n        \"runnerName\": \"Guayaquil SC\",\r\n        \"handicap\": 2.75,\r\n        \"sortPriority\": 55,\r\n        \"price\": 0.0\r\n      },\r\n      {\r\n        \"selectionId\": 25889141,\r\n        \"runnerName\": \"Chacaritas SC\",\r\n        \"handicap\": -2.75,\r\n        \"sortPriority\": 56,\r\n        \"price\": 0.0\r\n      },\r\n      {\r\n        \"selectionId\": 38844732,\r\n        \"runnerName\": \"Guayaquil SC\",\r\n        \"handicap\": 3.0,\r\n        \"sortPriority\": 57,\r\n        \"price\": 0.0\r\n      },\r\n      {\r\n        \"selectionId\": 25889141,\r\n        \"runnerName\": \"Chacaritas SC\",\r\n        \"handicap\": -3.0,\r\n        \"sortPriority\": 58,\r\n        \"price\": 0.0\r\n      },\r\n      {\r\n        \"selectionId\": 38844732,\r\n        \"runnerName\": \"Guayaquil SC\",\r\n        \"handicap\": 3.25,\r\n        \"sortPriority\": 59,\r\n        \"price\": 0.0\r\n      },\r\n      {\r\n        \"selectionId\": 25889141,\r\n        \"runnerName\": \"Chacaritas SC\",\r\n        \"handicap\": -3.25,\r\n        \"sortPriority\": 60,\r\n        \"price\": 0.0\r\n      },\r\n      {\r\n        \"selectionId\": 38844732,\r\n        \"runnerName\": \"Guayaquil SC\",\r\n        \"handicap\": 3.5,\r\n        \"sortPriority\": 61,\r\n        \"price\": 0.0\r\n      },\r\n      {\r\n        \"selectionId\": 25889141,\r\n        \"runnerName\": \"Chacaritas SC\",\r\n        \"handicap\": -3.5,\r\n        \"sortPriority\": 62,\r\n        \"price\": 0.0\r\n      },\r\n      {\r\n        \"selectionId\": 38844732,\r\n        \"runnerName\": \"Guayaquil SC\",\r\n        \"handicap\": 3.75,\r\n        \"sortPriority\": 63,\r\n        \"price\": 0.0\r\n      },\r\n      {\r\n        \"selectionId\": 25889141,\r\n        \"runnerName\": \"Chacaritas SC\",\r\n        \"handicap\": -3.75,\r\n        \"sortPriority\": 64,\r\n        \"price\": 0.0\r\n      },\r\n      {\r\n        \"selectionId\": 38844732,\r\n        \"runnerName\": \"Guayaquil SC\",\r\n        \"handicap\": 4.0,\r\n        \"sortPriority\": 65,\r\n        \"metadata\": {\r\n          \"runnerId\": \"38844732\"\r\n        },\r\n        \"price\": 0.0\r\n      },\r\n      {\r\n        \"selectionId\": 25889141,\r\n        \"runnerName\": \"Chacaritas SC\",\r\n        \"handicap\": -4.0,\r\n        \"sortPriority\": 66,\r\n        \"metadata\": {\r\n          \"runnerId\": \"25889141\"\r\n        },\r\n        \"price\": 0.0\r\n      }\r\n    ],\r\n    \"eventType\": {\r\n      \"id\": \"1\",\r\n      \"name\": \"Soccer\"\r\n    },\r\n    \"competition\": {\r\n      \"id\": \"3057583\",\r\n      \"name\": \"Ecuadorian Serie B\"\r\n    },\r\n    \"event\": {\r\n      \"id\": \"30736701\",\r\n      \"name\": \"Guayaquil SC v Chacaritas SC\",\r\n      \"countryCode\": \"EC\",\r\n      \"timezone\": \"GMT\",\r\n      \"openDate\": \"2021-07-29T00:00:00Z\"\r\n    }\r\n  },\r\n  {\r\n    \"marketId\": \"1.185769397\",\r\n    \"marketName\": \"Over/Under 4.5 Goals\",\r\n    \"marketStartTime\": \"2021-07-29T00:00:00Z\",\r\n    \"description\": {\r\n      \"persistenceEnabled\": true,\r\n      \"bspMarket\": false,\r\n      \"marketTime\": \"2021-07-29T00:00:00Z\",\r\n      \"suspendTime\": \"2021-07-29T00:00:00Z\",\r\n      \"bettingType\": \"ODDS\",\r\n      \"turnInPlayEnabled\": true,\r\n      \"marketType\": \"OVER_UNDER_45\",\r\n      \"regulator\": \"MALTA LOTTERIES AND GAMBLING AUTHORITY\",\r\n      \"marketBaseRate\": 6.5,\r\n      \"discountAllowed\": true,\r\n      \"wallet\": \"UK wallet\",\r\n      \"rules\": \"<!--Football - Over/Unders --><br>How many goals will be scored in this match.<br> All bets apply to Full Time according to the match officials, plus any stoppage time. Extra-time/penalty shoot-outs are not included.<br><br></b>For further information please see <a href=http://content.betfair.com/aboutus/content.asp?sWhichKey=Rules%20and%20Regulations#undefined.do style=color:0163ad; text-decoration: underline; target=_blank>Rules & Regs<br><br>\\n\",\r\n      \"rulesHasDate\": true,\r\n      \"priceLadderDescription\": {\r\n        \"type\": \"CLASSIC\"\r\n      }\r\n    },\r\n    \"totalMatched\": 9.289208,\r\n    \"runners\": [\r\n      {\r\n        \"selectionId\": 1222347,\r\n        \"runnerName\": \"Under 4.5 Goals\",\r\n        \"handicap\": 0.0,\r\n        \"sortPriority\": 1,\r\n        \"metadata\": {\r\n          \"runnerId\": \"1222347\"\r\n        },\r\n        \"price\": 1.07\r\n      },\r\n      {\r\n        \"selectionId\": 1222346,\r\n        \"runnerName\": \"Over 4.5 Goals\",\r\n        \"handicap\": 0.0,\r\n        \"sortPriority\": 2,\r\n        \"metadata\": {\r\n          \"runnerId\": \"1222346\"\r\n        },\r\n        \"price\": 14.0\r\n      }\r\n    ],\r\n    \"eventType\": {\r\n      \"id\": \"1\",\r\n      \"name\": \"Soccer\"\r\n    },\r\n    \"competition\": {\r\n      \"id\": \"3057583\",\r\n      \"name\": \"Ecuadorian Serie B\"\r\n    },\r\n    \"event\": {\r\n      \"id\": \"30736701\",\r\n      \"name\": \"Guayaquil SC v Chacaritas SC\",\r\n      \"countryCode\": \"EC\",\r\n      \"timezone\": \"GMT\",\r\n      \"openDate\": \"2021-07-29T00:00:00Z\"\r\n    }\r\n  },\r\n  {\r\n    \"marketId\": \"1.185769398\",\r\n    \"marketName\": \"Over/Under 1.5 Goals\",\r\n    \"marketStartTime\": \"2021-07-29T00:00:00Z\",\r\n    \"description\": {\r\n      \"persistenceEnabled\": true,\r\n      \"bspMarket\": false,\r\n      \"marketTime\": \"2021-07-29T00:00:00Z\",\r\n      \"suspendTime\": \"2021-07-29T00:00:00Z\",\r\n      \"bettingType\": \"ODDS\",\r\n      \"turnInPlayEnabled\": true,\r\n      \"marketType\": \"OVER_UNDER_15\",\r\n      \"regulator\": \"MALTA LOTTERIES AND GAMBLING AUTHORITY\",\r\n      \"marketBaseRate\": 6.5,\r\n      \"discountAllowed\": true,\r\n      \"wallet\": \"UK wallet\",\r\n      \"rules\": \"<!--Football - Over/Unders --><br>How many goals will be scored in this match.<br> All bets apply to Full Time according to the match officials, plus any stoppage time. Extra-time/penalty shoot-outs are not included.<br><br></b>For further information please see <a href=http://content.betfair.com/aboutus/content.asp?sWhichKey=Rules%20and%20Regulations#undefined.do style=color:0163ad; text-decoration: underline; target=_blank>Rules & Regs<br><br>\\n\",\r\n      \"rulesHasDate\": true,\r\n      \"priceLadderDescription\": {\r\n        \"type\": \"CLASSIC\"\r\n      }\r\n    },\r\n    \"totalMatched\": 0.0,\r\n    \"runners\": [\r\n      {\r\n        \"selectionId\": 1221385,\r\n        \"runnerName\": \"Under 1.5 Goals\",\r\n        \"handicap\": 0.0,\r\n        \"sortPriority\": 1,\r\n        \"metadata\": {\r\n          \"runnerId\": \"1221385\"\r\n        },\r\n        \"price\": 0.0\r\n      },\r\n      {\r\n        \"selectionId\": 1221386,\r\n        \"runnerName\": \"Over 1.5 Goals\",\r\n        \"handicap\": 0.0,\r\n        \"sortPriority\": 2,\r\n        \"metadata\": {\r\n          \"runnerId\": \"1221386\"\r\n        },\r\n        \"price\": 0.0\r\n      }\r\n    ],\r\n    \"eventType\": {\r\n      \"id\": \"1\",\r\n      \"name\": \"Soccer\"\r\n    },\r\n    \"competition\": {\r\n      \"id\": \"3057583\",\r\n      \"name\": \"Ecuadorian Serie B\"\r\n    },\r\n    \"event\": {\r\n      \"id\": \"30736701\",\r\n      \"name\": \"Guayaquil SC v Chacaritas SC\",\r\n      \"countryCode\": \"EC\",\r\n      \"timezone\": \"GMT\",\r\n      \"openDate\": \"2021-07-29T00:00:00Z\"\r\n    }\r\n  },\r\n  {\r\n    \"marketId\": \"1.185769395\",\r\n    \"marketName\": \"Over/Under 5.5 Goals\",\r\n    \"marketStartTime\": \"2021-07-29T00:00:00Z\",\r\n    \"description\": {\r\n      \"persistenceEnabled\": true,\r\n      \"bspMarket\": false,\r\n      \"marketTime\": \"2021-07-29T00:00:00Z\",\r\n      \"suspendTime\": \"2021-07-29T00:00:00Z\",\r\n      \"bettingType\": \"ODDS\",\r\n      \"turnInPlayEnabled\": true,\r\n      \"marketType\": \"OVER_UNDER_55\",\r\n      \"regulator\": \"MALTA LOTTERIES AND GAMBLING AUTHORITY\",\r\n      \"marketBaseRate\": 6.5,\r\n      \"discountAllowed\": true,\r\n      \"wallet\": \"UK wallet\",\r\n      \"rules\": \"<!--Football - Over/Unders --><br>How many goals will be scored in this match.<br> All bets apply to Full Time according to the match officials, plus any stoppage time. Extra-time/penalty shoot-outs are not included.<br><br></b>For further information please see <a href=http://content.betfair.com/aboutus/content.asp?sWhichKey=Rules%20and%20Regulations#undefined.do style=color:0163ad; text-decoration: underline; target=_blank>Rules & Regs<br><br>\\n\",\r\n      \"rulesHasDate\": true,\r\n      \"priceLadderDescription\": {\r\n        \"type\": \"CLASSIC\"\r\n      }\r\n    },\r\n    \"totalMatched\": 0.0,\r\n    \"runners\": [\r\n      {\r\n        \"selectionId\": 1485567,\r\n        \"runnerName\": \"Under 5.5 Goals\",\r\n        \"handicap\": 0.0,\r\n        \"sortPriority\": 1,\r\n        \"metadata\": {\r\n          \"runnerId\": \"1485567\"\r\n        },\r\n        \"price\": 0.0\r\n      },\r\n      {\r\n        \"selectionId\": 1485568,\r\n        \"runnerName\": \"Over 5.5 Goals\",\r\n        \"handicap\": 0.0,\r\n        \"sortPriority\": 2,\r\n        \"metadata\": {\r\n          \"runnerId\": \"1485568\"\r\n        },\r\n        \"price\": 0.0\r\n      }\r\n    ],\r\n    \"eventType\": {\r\n      \"id\": \"1\",\r\n      \"name\": \"Soccer\"\r\n    },\r\n    \"competition\": {\r\n      \"id\": \"3057583\",\r\n      \"name\": \"Ecuadorian Serie B\"\r\n    },\r\n    \"event\": {\r\n      \"id\": \"30736701\",\r\n      \"name\": \"Guayaquil SC v Chacaritas SC\",\r\n      \"countryCode\": \"EC\",\r\n      \"timezone\": \"GMT\",\r\n      \"openDate\": \"2021-07-29T00:00:00Z\"\r\n    }\r\n  },\r\n  {\r\n    \"marketId\": \"1.185769396\",\r\n    \"marketName\": \"Over/Under 2.5 Goals\",\r\n    \"marketStartTime\": \"2021-07-29T00:00:00Z\",\r\n    \"description\": {\r\n      \"persistenceEnabled\": true,\r\n      \"bspMarket\": false,\r\n      \"marketTime\": \"2021-07-29T00:00:00Z\",\r\n      \"suspendTime\": \"2021-07-29T00:00:00Z\",\r\n      \"bettingType\": \"ODDS\",\r\n      \"turnInPlayEnabled\": true,\r\n      \"marketType\": \"OVER_UNDER_25\",\r\n      \"regulator\": \"MALTA LOTTERIES AND GAMBLING AUTHORITY\",\r\n      \"marketBaseRate\": 6.5,\r\n      \"discountAllowed\": true,\r\n      \"wallet\": \"UK wallet\",\r\n      \"rules\": \"<!--Football - Over/Unders --><br>How many goals will be scored in this match.<br> All bets apply to Full Time according to the match officials, plus any stoppage time. Extra-time/penalty shoot-outs are not included.<br><br></b>For further information please see <a href=http://content.betfair.com/aboutus/content.asp?sWhichKey=Rules%20and%20Regulations#undefined.do style=color:0163ad; text-decoration: underline; target=_blank>Rules & Regs<br><br>\\n\",\r\n      \"rulesHasDate\": true,\r\n      \"priceLadderDescription\": {\r\n        \"type\": \"CLASSIC\"\r\n      }\r\n    },\r\n    \"totalMatched\": 19.12075,\r\n    \"runners\": [\r\n      {\r\n        \"selectionId\": 47972,\r\n        \"runnerName\": \"Under 2.5 Goals\",\r\n        \"handicap\": 0.0,\r\n        \"sortPriority\": 1,\r\n        \"metadata\": {\r\n          \"runnerId\": \"47972\"\r\n        }\r\n      },\r\n      {\r\n        \"selectionId\": 47973,\r\n        \"runnerName\": \"Over 2.5 Goals\",\r\n        \"handicap\": 0.0,\r\n        \"sortPriority\": 2,\r\n        \"metadata\": {\r\n          \"runnerId\": \"47973\"\r\n        }\r\n      }\r\n    ],\r\n    \"eventType\": {\r\n      \"id\": \"1\",\r\n      \"name\": \"Soccer\"\r\n    },\r\n    \"competition\": {\r\n      \"id\": \"3057583\",\r\n      \"name\": \"Ecuadorian Serie B\"\r\n    },\r\n    \"event\": {\r\n      \"id\": \"30736701\",\r\n      \"name\": \"Guayaquil SC v Chacaritas SC\",\r\n      \"countryCode\": \"EC\",\r\n      \"timezone\": \"GMT\",\r\n      \"openDate\": \"2021-07-29T00:00:00Z\"\r\n    }\r\n  },\r\n  {\r\n    \"marketId\": \"1.185769393\",\r\n    \"marketName\": \"Over/Under 0.5 Goals\",\r\n    \"marketStartTime\": \"2021-07-29T00:00:00Z\",\r\n    \"description\": {\r\n      \"persistenceEnabled\": true,\r\n      \"bspMarket\": false,\r\n      \"marketTime\": \"2021-07-29T00:00:00Z\",\r\n      \"suspendTime\": \"2021-07-29T00:00:00Z\",\r\n      \"bettingType\": \"ODDS\",\r\n      \"turnInPlayEnabled\": true,\r\n      \"marketType\": \"OVER_UNDER_05\",\r\n      \"regulator\": \"MALTA LOTTERIES AND GAMBLING AUTHORITY\",\r\n      \"marketBaseRate\": 6.5,\r\n      \"discountAllowed\": true,\r\n      \"wallet\": \"UK wallet\",\r\n      \"rules\": \"<!--Football - Over/Unders --><br>How many goals will be scored in this match.<br> All bets apply to Full Time according to the match officials, plus any stoppage time. Extra-time/penalty shoot-outs are not included.<br><br></b>For further information please see <a href=http://content.betfair.com/aboutus/content.asp?sWhichKey=Rules%20and%20Regulations#undefined.do style=color:0163ad; text-decoration: underline; target=_blank>Rules & Regs<br><br>\\n\",\r\n      \"rulesHasDate\": true,\r\n      \"priceLadderDescription\": {\r\n        \"type\": \"CLASSIC\"\r\n      }\r\n    },\r\n    \"totalMatched\": 41.718,\r\n    \"runners\": [\r\n      {\r\n        \"selectionId\": 5851482,\r\n        \"runnerName\": \"Under 0.5 Goals\",\r\n        \"handicap\": 0.0,\r\n        \"sortPriority\": 1,\r\n        \"metadata\": {\r\n          \"runnerId\": \"5851482\"\r\n        }\r\n      },\r\n      {\r\n        \"selectionId\": 5851483,\r\n        \"runnerName\": \"Over 0.5 Goals\",\r\n        \"handicap\": 0.0,\r\n        \"sortPriority\": 2,\r\n        \"metadata\": {\r\n          \"runnerId\": \"5851483\"\r\n        }\r\n      }\r\n    ],\r\n    \"eventType\": {\r\n      \"id\": \"1\",\r\n      \"name\": \"Soccer\"\r\n    },\r\n    \"competition\": {\r\n      \"id\": \"3057583\",\r\n      \"name\": \"Ecuadorian Serie B\"\r\n    },\r\n    \"event\": {\r\n      \"id\": \"30736701\",\r\n      \"name\": \"Guayaquil SC v Chacaritas SC\",\r\n      \"countryCode\": \"EC\",\r\n      \"timezone\": \"GMT\",\r\n      \"openDate\": \"2021-07-29T00:00:00Z\"\r\n    }\r\n  },\r\n  {\r\n    \"marketId\": \"1.185769394\",\r\n    \"marketName\": \"Over/Under 6.5 Goals\",\r\n    \"marketStartTime\": \"2021-07-29T00:00:00Z\",\r\n    \"description\": {\r\n      \"persistenceEnabled\": true,\r\n      \"bspMarket\": false,\r\n      \"marketTime\": \"2021-07-29T00:00:00Z\",\r\n      \"suspendTime\": \"2021-07-29T00:00:00Z\",\r\n      \"bettingType\": \"ODDS\",\r\n      \"turnInPlayEnabled\": true,\r\n      \"marketType\": \"OVER_UNDER_65\",\r\n      \"regulator\": \"MALTA LOTTERIES AND GAMBLING AUTHORITY\",\r\n      \"marketBaseRate\": 6.5,\r\n      \"discountAllowed\": true,\r\n      \"wallet\": \"UK wallet\",\r\n      \"rules\": \"<!--Football - Over/Unders --><br>How many goals will be scored in this match.<br> All bets apply to Full Time according to the match officials, plus any stoppage time. Extra-time/penalty shoot-outs are not included.<br><br></b>For further information please see <a href=http://content.betfair.com/aboutus/content.asp?sWhichKey=Rules%20and%20Regulations#undefined.do style=color:0163ad; text-decoration: underline; target=_blank>Rules & Regs<br><br>\\n\",\r\n      \"rulesHasDate\": true,\r\n      \"priceLadderDescription\": {\r\n        \"type\": \"CLASSIC\"\r\n      }\r\n    },\r\n    \"totalMatched\": 0.0,\r\n    \"runners\": [\r\n      {\r\n        \"selectionId\": 2542448,\r\n        \"runnerName\": \"Under 6.5 Goals\",\r\n        \"handicap\": 0.0,\r\n        \"sortPriority\": 1,\r\n        \"metadata\": {\r\n          \"runnerId\": \"2542448\"\r\n        }\r\n      },\r\n      {\r\n        \"selectionId\": 2542449,\r\n        \"runnerName\": \"Over 6.5 Goals\",\r\n        \"handicap\": 0.0,\r\n        \"sortPriority\": 2,\r\n        \"metadata\": {\r\n          \"runnerId\": \"2542449\"\r\n        }\r\n      }\r\n    ],\r\n    \"eventType\": {\r\n      \"id\": \"1\",\r\n      \"name\": \"Soccer\"\r\n    },\r\n    \"competition\": {\r\n      \"id\": \"3057583\",\r\n      \"name\": \"Ecuadorian Serie B\"\r\n    },\r\n    \"event\": {\r\n      \"id\": \"30736701\",\r\n      \"name\": \"Guayaquil SC v Chacaritas SC\",\r\n      \"countryCode\": \"EC\",\r\n      \"timezone\": \"GMT\",\r\n      \"openDate\": \"2021-07-29T00:00:00Z\"\r\n    }\r\n  },\r\n  {\r\n    \"marketId\": \"1.185769391\",\r\n    \"marketName\": \"Both teams to Score?\",\r\n    \"marketStartTime\": \"2021-07-29T00:00:00Z\",\r\n    \"description\": {\r\n      \"persistenceEnabled\": true,\r\n      \"bspMarket\": false,\r\n      \"marketTime\": \"2021-07-29T00:00:00Z\",\r\n      \"suspendTime\": \"2021-07-29T00:00:00Z\",\r\n      \"bettingType\": \"ODDS\",\r\n      \"turnInPlayEnabled\": true,\r\n      \"marketType\": \"BOTH_TEAMS_TO_SCORE\",\r\n      \"regulator\": \"MALTA LOTTERIES AND GAMBLING AUTHORITY\",\r\n      \"marketBaseRate\": 6.5,\r\n      \"discountAllowed\": true,\r\n      \"wallet\": \"UK wallet\",\r\n      \"rules\": \"<!--Football - BTTS --><br>Will both teams score at least one goal in this match? <br> All bets apply to Full Time according to the match officials, plus any stoppage time. Extra-time/penalty shoot-outs are not included.<br><br></b>For further information please see <a href=http://content.betfair.com/aboutus/content.asp?sWhichKey=Rules%20and%20Regulations#undefined.do style=color:0163ad; text-decoration: underline; target=_blank>Rules & Regs<br><br>\\n\",\r\n      \"rulesHasDate\": true,\r\n      \"priceLadderDescription\": {\r\n        \"type\": \"CLASSIC\"\r\n      }\r\n    },\r\n    \"totalMatched\": 1.4462240000000002,\r\n    \"runners\": [\r\n      {\r\n        \"selectionId\": 30246,\r\n        \"runnerName\": \"Yes\",\r\n        \"handicap\": 0.0,\r\n        \"sortPriority\": 1,\r\n        \"metadata\": {\r\n          \"runnerId\": \"30246\"\r\n        }\r\n      },\r\n      {\r\n        \"selectionId\": 110503,\r\n        \"runnerName\": \"No\",\r\n        \"handicap\": 0.0,\r\n        \"sortPriority\": 2,\r\n        \"metadata\": {\r\n          \"runnerId\": \"110503\"\r\n        }\r\n      }\r\n    ],\r\n    \"eventType\": {\r\n      \"id\": \"1\",\r\n      \"name\": \"Soccer\"\r\n    },\r\n    \"competition\": {\r\n      \"id\": \"3057583\",\r\n      \"name\": \"Ecuadorian Serie B\"\r\n    },\r\n    \"event\": {\r\n      \"id\": \"30736701\",\r\n      \"name\": \"Guayaquil SC v Chacaritas SC\",\r\n      \"countryCode\": \"EC\",\r\n      \"timezone\": \"GMT\",\r\n      \"openDate\": \"2021-07-29T00:00:00Z\"\r\n    }\r\n  },\r\n  {\r\n    \"marketId\": \"1.185769392\",\r\n    \"marketName\": \"Match Odds\",\r\n    \"marketStartTime\": \"2021-07-29T00:00:00Z\",\r\n    \"description\": {\r\n      \"persistenceEnabled\": true,\r\n      \"bspMarket\": false,\r\n      \"marketTime\": \"2021-07-29T00:00:00Z\",\r\n      \"suspendTime\": \"2021-07-29T00:00:00Z\",\r\n      \"bettingType\": \"ODDS\",\r\n      \"turnInPlayEnabled\": true,\r\n      \"marketType\": \"MATCH_ODDS\",\r\n      \"regulator\": \"MALTA LOTTERIES AND GAMBLING AUTHORITY\",\r\n      \"marketBaseRate\": 6.5,\r\n      \"discountAllowed\": true,\r\n      \"wallet\": \"UK wallet\",\r\n      \"rules\": \"<!--Football - Match Odds --><br>Predict the result of this match.<br> All bets apply to Full Time according to the match officials, plus any stoppage time. Extra-time/penalty shoot-outs are not included.<br><br></b>For further information please see <a href=http://content.betfair.com/aboutus/content.asp?sWhichKey=Rules%20and%20Regulations#undefined.do style=color:0163ad; text-decoration: underline; target=_blank>Rules & Regs<br><br>\\n\",\r\n      \"rulesHasDate\": true,\r\n      \"priceLadderDescription\": {\r\n        \"type\": \"CLASSIC\"\r\n      }\r\n    },\r\n    \"totalMatched\": 159.515726,\r\n    \"runners\": [\r\n      {\r\n        \"selectionId\": 38844732,\r\n        \"runnerName\": \"Guayaquil SC\",\r\n        \"handicap\": 0.0,\r\n        \"sortPriority\": 1,\r\n        \"metadata\": {\r\n          \"runnerId\": \"38844732\"\r\n        }\r\n      },\r\n      {\r\n        \"selectionId\": 25889141,\r\n        \"runnerName\": \"Chacaritas SC\",\r\n        \"handicap\": 0.0,\r\n        \"sortPriority\": 2,\r\n        \"metadata\": {\r\n          \"runnerId\": \"25889141\"\r\n        }\r\n      },\r\n      {\r\n        \"selectionId\": 58805,\r\n        \"runnerName\": \"The Draw\",\r\n        \"handicap\": 0.0,\r\n        \"sortPriority\": 3,\r\n        \"metadata\": {\r\n          \"runnerId\": \"58805\"\r\n        }\r\n      }\r\n    ],\r\n    \"eventType\": {\r\n      \"id\": \"1\",\r\n      \"name\": \"Soccer\"\r\n    },\r\n    \"competition\": {\r\n      \"id\": \"3057583\",\r\n      \"name\": \"Ecuadorian Serie B\"\r\n    },\r\n    \"event\": {\r\n      \"id\": \"30736701\",\r\n      \"name\": \"Guayaquil SC v Chacaritas SC\",\r\n      \"countryCode\": \"EC\",\r\n      \"timezone\": \"GMT\",\r\n      \"openDate\": \"2021-07-29T00:00:00Z\"\r\n    }\r\n  },\r\n  {\r\n    \"marketId\": \"1.185769389\",\r\n    \"marketName\": \"Over/Under 7.5 Goals\",\r\n    \"marketStartTime\": \"2021-07-29T00:00:00Z\",\r\n    \"description\": {\r\n      \"persistenceEnabled\": true,\r\n      \"bspMarket\": false,\r\n      \"marketTime\": \"2021-07-29T00:00:00Z\",\r\n      \"suspendTime\": \"2021-07-29T00:00:00Z\",\r\n      \"bettingType\": \"ODDS\",\r\n      \"turnInPlayEnabled\": true,\r\n      \"marketType\": \"OVER_UNDER_75\",\r\n      \"regulator\": \"MALTA LOTTERIES AND GAMBLING AUTHORITY\",\r\n      \"marketBaseRate\": 6.5,\r\n      \"discountAllowed\": true,\r\n      \"wallet\": \"UK wallet\",\r\n      \"rules\": \"<!--Football - Over/Unders --><br>How many goals will be scored in this match.<br> All bets apply to Full Time according to the match officials, plus any stoppage time. Extra-time/penalty shoot-outs are not included.<br><br></b>For further information please see <a href=http://content.betfair.com/aboutus/content.asp?sWhichKey=Rules%20and%20Regulations#undefined.do style=color:0163ad; text-decoration: underline; target=_blank>Rules & Regs<br><br>\\n\",\r\n      \"rulesHasDate\": true,\r\n      \"priceLadderDescription\": {\r\n        \"type\": \"CLASSIC\"\r\n      }\r\n    },\r\n    \"totalMatched\": 0.0,\r\n    \"runners\": [\r\n      {\r\n        \"selectionId\": 1485572,\r\n        \"runnerName\": \"Under 7.5 Goals\",\r\n        \"handicap\": 0.0,\r\n        \"sortPriority\": 1,\r\n        \"metadata\": {\r\n          \"runnerId\": \"1485572\"\r\n        }\r\n      },\r\n      {\r\n        \"selectionId\": 1485573,\r\n        \"runnerName\": \"Over 7.5 Goals\",\r\n        \"handicap\": 0.0,\r\n        \"sortPriority\": 2,\r\n        \"metadata\": {\r\n          \"runnerId\": \"1485573\"\r\n        }\r\n      }\r\n    ],\r\n    \"eventType\": {\r\n      \"id\": \"1\",\r\n      \"name\": \"Soccer\"\r\n    },\r\n    \"competition\": {\r\n      \"id\": \"3057583\",\r\n      \"name\": \"Ecuadorian Serie B\"\r\n    },\r\n    \"event\": {\r\n      \"id\": \"30736701\",\r\n      \"name\": \"Guayaquil SC v Chacaritas SC\",\r\n      \"countryCode\": \"EC\",\r\n      \"timezone\": \"GMT\",\r\n      \"openDate\": \"2021-07-29T00:00:00Z\"\r\n    }\r\n  },\r\n  {\r\n    \"marketId\": \"1.185769390\",\r\n    \"marketName\": \"Over/Under 8.5 Goals\",\r\n    \"marketStartTime\": \"2021-07-29T00:00:00Z\",\r\n    \"description\": {\r\n      \"persistenceEnabled\": true,\r\n      \"bspMarket\": false,\r\n      \"marketTime\": \"2021-07-29T00:00:00Z\",\r\n      \"suspendTime\": \"2021-07-29T00:00:00Z\",\r\n      \"bettingType\": \"ODDS\",\r\n      \"turnInPlayEnabled\": true,\r\n      \"marketType\": \"OVER_UNDER_85\",\r\n      \"regulator\": \"MALTA LOTTERIES AND GAMBLING AUTHORITY\",\r\n      \"marketBaseRate\": 6.5,\r\n      \"discountAllowed\": true,\r\n      \"wallet\": \"UK wallet\",\r\n      \"rules\": \"<!--Football - Over/Unders --><br>How many goals will be scored in this match.<br> All bets apply to Full Time according to the match officials, plus any stoppage time. Extra-time/penalty shoot-outs are not included.<br><br></b>For further information please see <a href=http://content.betfair.com/aboutus/content.asp?sWhichKey=Rules%20and%20Regulations#undefined.do style=color:0163ad; text-decoration: underline; target=_blank>Rules & Regs<br><br>\\n\",\r\n      \"rulesHasDate\": true,\r\n      \"priceLadderDescription\": {\r\n        \"type\": \"CLASSIC\"\r\n      }\r\n    },\r\n    \"totalMatched\": 0.0,\r\n    \"runners\": [\r\n      {\r\n        \"selectionId\": 2407528,\r\n        \"runnerName\": \"Under 8.5 Goals\",\r\n        \"handicap\": 0.0,\r\n        \"sortPriority\": 1,\r\n        \"metadata\": {\r\n          \"runnerId\": \"2407528\"\r\n        }\r\n      },\r\n      {\r\n        \"selectionId\": 2407529,\r\n        \"runnerName\": \"Over 8.5 Goals\",\r\n        \"handicap\": 0.0,\r\n        \"sortPriority\": 2,\r\n        \"metadata\": {\r\n          \"runnerId\": \"2407529\"\r\n        }\r\n      }\r\n    ],\r\n    \"eventType\": {\r\n      \"id\": \"1\",\r\n      \"name\": \"Soccer\"\r\n    },\r\n    \"competition\": {\r\n      \"id\": \"3057583\",\r\n      \"name\": \"Ecuadorian Serie B\"\r\n    },\r\n    \"event\": {\r\n      \"id\": \"30736701\",\r\n      \"name\": \"Guayaquil SC v Chacaritas SC\",\r\n      \"countryCode\": \"EC\",\r\n      \"timezone\": \"GMT\",\r\n      \"openDate\": \"2021-07-29T00:00:00Z\"\r\n    }\r\n  }\r\n]");
			//consumer.OnMessageBeforeCommit("7￻2￻BetFair API￻1.185769392￺False￺MATCH_ODDS￺3￺1￺38844732￺2.46￺25889141￺3.25￺58805￺3.2");
			//consumer.OnMessageBeforeCommit("2￻2￻N/A￻N/A￻0￻321319￻0￻0￻[{\"event\":{\"id\":\"30721124\",\"name\":\"Operario PR v Coritiba\",\"countryCode\":\"BR\",\"timezone\":\"GMT\",\"openDate\":\"2021-07-25T21:15:00.000Z\"},\"marketCount\":12},{\"event\":{\"id\":\"30721125\",\"name\":\"Avai v Brasil de Pelotas\",\"countryCode\":\"BR\",\"timezone\":\"GMT\",\"openDate\":\"2021-07-25T23:30:00.000Z\"},\"marketCount\":1},{\"event\":{\"id\":\"30721126\",\"name\":\"Sampaio Correa FC v CRB\",\"countryCode\":\"BR\",\"timezone\":\"GMT\",\"openDate\":\"2021-07-25T23:30:00.000Z\"},\"marketCount\":1},{\"event\":{\"id\":\"30721063\",\"name\":\"Ponte Preta v Goias\",\"countryCode\":\"BR\",\"timezone\":\"GMT\",\"openDate\":\"2021-07-23T23:00:00.000Z\"},\"marketCount\":12},{\"event\":{\"id\":\"30721079\",\"name\":\"Confianca v Botafogo\",\"countryCode\":\"BR\",\"timezone\":\"GMT\",\"openDate\":\"2021-07-24T19:30:00.000Z\"},\"marketCount\":12},{\"event\":{\"id\":\"30721111\",\"name\":\"CSA v EC Vitoria Salvador\",\"countryCode\":\"BR\",\"timezone\":\"GMT\",\"openDate\":\"2021-07-25T00:00:00.000Z\"},\"marketCount\":12},{\"event\":{\"id\":\"30721121\",\"name\":\"Vasco Da Gama v Guarani\",\"countryCode\":\"BR\",\"timezone\":\"GMT\",\"openDate\":\"2021-07-25T00:00:00.000Z\"},\"marketCount\":12},{\"event\":{\"id\":\"30721107\",\"name\":\"Vila Nova v Cruzeiro MG\",\"countryCode\":\"BR\",\"timezone\":\"GMT\",\"openDate\":\"2021-07-24T19:30:00.000Z\"},\"marketCount\":12},{\"event\":{\"id\":\"30721113\",\"name\":\"Nautico PE v Brusque FC\",\"countryCode\":\"BR\",\"timezone\":\"GMT\",\"openDate\":\"2021-07-24T22:30:00.000Z\"},\"marketCount\":12}]");

			//update for ODD_OR_EVEN
			//consumer.OnMessageBeforeCommit(@$"7￻2￻N/A￻N/A￻0￻N/A￻2￻7￻{\"MarketId\":\"1.185922861\",\"IsClosed\":false,\"Snap\":{\"MarketDefinition\":{\"bettingType\":\"ODDS\",\"status\":\"OPEN\",\"timezone\":\"GMT\",\"regulators\":[\"MR_INT\"],\"marketType\":\"ODD_OR_EVEN\",\"marketBaseRate\":5.0,\"numberOfWinners\":1,\"countryCode\":\"RU\",\"inPlay\":false,\"betDelay\":0,\"bspMarket\":false,\"numberOfActiveRunners\":2,\"eventId\":\"30750783\",\"crossMatching\":true,\"runnersVoidable\":false,\"turnInPlayEnabled\":true,\"suspendTime\":\"2021-08-07T17:00:00Z\",\"discountAllowed\":true,\"persistenceEnabled\":true,\"runners\":[{\"status\":\"ACTIVE\",\"sortPriority\":1,\"id\":955466},{\"status\":\"ACTIVE\",\"sortPriority\":2,\"id\":955467}],\"version\":3950220757,\"eventTypeId\":\"1\",\"complete\":true,\"openDate\":\"2021-08-07T17:00:00Z\",\"marketTime\":\"2021-08-07T17:00:00Z\",\"bspReconciled\":false},\"MarketId\":\"1.185922861\",\"MarketRunners\":[{\"RunnerId\":{\"SelectionId\":955466,\"Handicap\":null},\"Definition\":{\"status\":\"ACTIVE\",\"sortPriority\":1,\"id\":955466},\"Prices\":{\"AvailableToLay\":[{\"Price\":2.22,\"Size\":199.75}],\"AvailableToBack\":[{\"Price\":1.91,\"Size\":244.02}],\"Traded\":[],\"StartingPriceBack\":[],\"StartingPriceLay\":[],\"BestAvailableToBack\":[{\"Level\":0,\"Price\":1.91,\"Size\":244.02}],\"BestAvailableToLay\":[{\"Level\":0,\"Price\":2.22,\"Size\":199.75}],\"BestDisplayAvailableToBack\":[{\"Level\":0,\"Price\":1.91,\"Size\":244.02},{\"Level\":1,\"Price\":1.81,\"Size\":245.0},{\"Level\":2,\"Price\":0.0,\"Size\":0.0},{\"Level\":3,\"Price\":0.0,\"Size\":0.0},{\"Level\":4,\"Price\":0.0,\"Size\":0.0},{\"Level\":5,\"Price\":0.0,\"Size\":0.0},{\"Level\":6,\"Price\":0.0,\"Size\":0.0},{\"Level\":7,\"Price\":0.0,\"Size\":0.0},{\"Level\":8,\"Price\":0.0,\"Size\":0.0},{\"Level\":9,\"Price\":0.0,\"Size\":0.0}],\"BestDisplayAvailableToLay\":[{\"Level\":0,\"Price\":2.1,\"Size\":146.79},{\"Level\":1,\"Price\":2.14,\"Size\":44.48},{\"Level\":2,\"Price\":2.22,\"Size\":199.75},{\"Level\":3,\"Price\":0.0,\"Size\":0.0},{\"Level\":4,\"Price\":0.0,\"Size\":0.0},{\"Level\":5,\"Price\":0.0,\"Size\":0.0},{\"Level\":6,\"Price\":0.0,\"Size\":0.0},{\"Level\":7,\"Price\":0.0,\"Size\":0.0},{\"Level\":8,\"Price\":0.0,\"Size\":0.0},{\"Level\":9,\"Price\":0.0,\"Size\":0.0}],\"LastTradedPrice\":0.0,\"StartingPriceNear\":0.0,\"StartingPriceFar\":0.0,\"TradedVolume\":0.0}},{\"RunnerId\":{\"SelectionId\":955467,\"Handicap\":null},\"Definition\":{\"status\":\"ACTIVE\",\"sortPriority\":2,\"id\":955467},\"Prices\":{\"AvailableToLay\":[{\"Price\":2.22,\"Size\":199.75}],\"AvailableToBack\":[{\"Price\":1.91,\"Size\":161.39},{\"Price\":1.88,\"Size\":50.63}],\"Traded\":[],\"StartingPriceBack\":[],\"StartingPriceLay\":[],\"BestAvailableToBack\":[{\"Level\":0,\"Price\":1.91,\"Size\":161.39},{\"Level\":1,\"Price\":1.88,\"Size\":50.63}],\"BestAvailableToLay\":[{\"Level\":0,\"Price\":2.22,\"Size\":199.75}],\"BestDisplayAvailableToBack\":[{\"Level\":0,\"Price\":1.91,\"Size\":161.39},{\"Level\":1,\"Price\":1.88,\"Size\":50.63},{\"Level\":2,\"Price\":1.81,\"Size\":245.0},{\"Level\":3,\"Price\":0.0,\"Size\":0.0},{\"Level\":4,\"Price\":0.0,\"Size\":0.0},{\"Level\":5,\"Price\":0.0,\"Size\":0.0},{\"Level\":6,\"Price\":0.0,\"Size\":0.0},{\"Level\":7,\"Price\":0.0,\"Size\":0.0},{\"Level\":8,\"Price\":0.0,\"Size\":0.0},{\"Level\":9,\"Price\":0.0,\"Size\":0.0}],\"BestDisplayAvailableToLay\":[{\"Level\":0,\"Price\":2.1,\"Size\":221.94},{\"Level\":1,\"Price\":2.22,\"Size\":199.75},{\"Level\":2,\"Price\":0.0,\"Size\":0.0},{\"Level\":3,\"Price\":0.0,\"Size\":0.0},{\"Level\":4,\"Price\":0.0,\"Size\":0.0},{\"Level\":5,\"Price\":0.0,\"Size\":0.0},{\"Level\":6,\"Price\":0.0,\"Size\":0.0},{\"Level\":7,\"Price\":0.0,\"Size\":0.0},{\"Level\":8,\"Price\":0.0,\"Size\":0.0},{\"Level\":9,\"Price\":0.0,\"Size\":0.0}],\"LastTradedPrice\":0.0,\"StartingPriceNear\":0.0,\"StartingPriceFar\":0.0,\"TradedVolume\":0.0}}],\"TradedVolume\":0.0}}");
			//creation for ODD_OR_EVEN
			//consumer.OnMessageBeforeCommit("5￻2￻N/A￻N/A￻0￻N/A￻2￻1￻[\r\n  {\r\n    \"marketId\": \"1.185028444\",\r\n    \"marketName\": \"Over/Under 1.5 Goals\",\r\n    \"marketStartTime\": \"2021-08-14T20:00:00Z\",\r\n    \"description\": {\r\n      \"persistenceEnabled\": true,\r\n      \"bspMarket\": false,\r\n      \"marketTime\": \"2021-08-14T20:00:00Z\",\r\n      \"suspendTime\": \"2021-08-14T20:00:00Z\",\r\n      \"bettingType\": \"ODDS\",\r\n      \"turnInPlayEnabled\": true,\r\n      \"marketType\": \"OVER_UNDER_15\",\r\n      \"regulator\": \"MALTA LOTTERIES AND GAMBLING AUTHORITY\",\r\n      \"marketBaseRate\": 6.5,\r\n      \"discountAllowed\": true,\r\n      \"wallet\": \"UK wallet\",\r\n      \"rules\": \"<!--Football - Over/Unders --><br>How many goals will be scored in this match.<br> All bets apply to Full Time according to the match officials, plus any stoppage time. Extra-time/penalty shoot-outs are not included.<br><br></b>For further information please see <a href=http://content.betfair.com/aboutus/content.asp?sWhichKey=Rules%20and%20Regulations#undefined.do style=color:0163ad; text-decoration: underline; target=_blank>Rules & Regs<br><br>\\n\",\r\n      \"rulesHasDate\": true,\r\n      \"priceLadderDescription\": {\r\n        \"type\": \"CLASSIC\"\r\n      }\r\n    },\r\n    \"totalMatched\": 0.0,\r\n    \"runners\": [\r\n      {\r\n        \"selectionId\": 1221385,\r\n        \"runnerName\": \"Under 1.5 Goals\",\r\n        \"handicap\": 0.0,\r\n        \"sortPriority\": 1,\r\n        \"metadata\": {\r\n          \"runnerId\": \"1221385\"\r\n        },\r\n        \"price\": 0.0\r\n      },\r\n      {\r\n        \"selectionId\": 1221386,\r\n        \"runnerName\": \"Over 1.5 Goals\",\r\n        \"handicap\": 0.0,\r\n        \"sortPriority\": 2,\r\n        \"metadata\": {\r\n          \"runnerId\": \"1221386\"\r\n        },\r\n        \"price\": 0.0\r\n      }\r\n    ],\r\n    \"eventType\": {\r\n      \"id\": \"1\",\r\n      \"name\": \"Soccer\"\r\n    },\r\n    \"competition\": {\r\n      \"id\": \"117\",\r\n      \"name\": \"Spanish La Liga\"\r\n    },\r\n    \"event\": {\r\n      \"id\": \"30668012\",\r\n      \"name\": \"Alaves v Real Madrid\",\r\n      \"countryCode\": \"ES\",\r\n      \"timezone\": \"Europe/London\",\r\n      \"openDate\": \"2021-08-14T19:00:00Z\"\r\n    }\r\n  },\r\n  {\r\n    \"marketId\": \"1.185028545\",\r\n    \"marketName\": \"Corners Over/Under 10.5\",\r\n    \"marketStartTime\": \"2021-08-14T20:00:00Z\",\r\n    \"description\": {\r\n      \"persistenceEnabled\": false,\r\n      \"bspMarket\": false,\r\n      \"marketTime\": \"2021-08-14T20:00:00Z\",\r\n      \"suspendTime\": \"2021-08-14T20:00:00Z\",\r\n      \"bettingType\": \"ODDS\",\r\n      \"turnInPlayEnabled\": false,\r\n      \"marketType\": \"OVER_UNDER_105_CORNR\",\r\n      \"regulator\": \"MALTA LOTTERIES AND GAMBLING AUTHORITY\",\r\n      \"marketBaseRate\": 6.5,\r\n      \"discountAllowed\": true,\r\n      \"wallet\": \"UK wallet\",\r\n      \"rules\": \"<!--Football - Corners Over/Unders --><br>How many corners will be taken in the match? Only corners that are taken will be counted.<br> All bets apply to Full Time according to the match officials, plus any stoppage time. Extra-time/penalty shoot-outs are not included.<br><br></b>For further information please see <a href=http://content.betfair.com/aboutus/content.asp?sWhichKey=Rules%20and%20Regulations#undefined.do style=color:0163ad; text-decoration: underline; target=_blank>Rules & Regs<br><br>\\n\",\r\n      \"rulesHasDate\": true,\r\n      \"priceLadderDescription\": {\r\n        \"type\": \"CLASSIC\"\r\n      }\r\n    },\r\n    \"totalMatched\": 0.0,\r\n    \"runners\": [\r\n      {\r\n        \"selectionId\": 6393482,\r\n        \"runnerName\": \"Under 10.5 Corners\",\r\n        \"handicap\": 0.0,\r\n        \"sortPriority\": 1,\r\n        \"metadata\": {\r\n          \"runnerId\": \"6393482\"\r\n        },\r\n        \"price\": 0.0\r\n      },\r\n      {\r\n        \"selectionId\": 6393483,\r\n        \"runnerName\": \"Over 10.5 Corners\",\r\n        \"handicap\": 0.0,\r\n        \"sortPriority\": 2,\r\n        \"metadata\": {\r\n          \"runnerId\": \"6393483\"\r\n        },\r\n        \"price\": 0.0\r\n      }\r\n    ],\r\n    \"eventType\": {\r\n      \"id\": \"1\",\r\n      \"name\": \"Soccer\"\r\n    },\r\n    \"competition\": {\r\n      \"id\": \"117\",\r\n      \"name\": \"Spanish La Liga\"\r\n    },\r\n    \"event\": {\r\n      \"id\": \"30668012\",\r\n      \"name\": \"Alaves v Real Madrid\",\r\n      \"countryCode\": \"ES\",\r\n      \"timezone\": \"Europe/London\",\r\n      \"openDate\": \"2021-08-14T19:00:00Z\"\r\n    }\r\n  },\r\n  {\r\n    \"marketId\": \"1.185028509\",\r\n    \"marketName\": \"Corners Over/Under 13.5\",\r\n    \"marketStartTime\": \"2021-08-14T20:00:00Z\",\r\n    \"description\": {\r\n      \"persistenceEnabled\": false,\r\n      \"bspMarket\": false,\r\n      \"marketTime\": \"2021-08-14T20:00:00Z\",\r\n      \"suspendTime\": \"2021-08-14T20:00:00Z\",\r\n      \"bettingType\": \"ODDS\",\r\n      \"turnInPlayEnabled\": false,\r\n      \"marketType\": \"OVER_UNDER_135_CORNR\",\r\n      \"regulator\": \"MALTA LOTTERIES AND GAMBLING AUTHORITY\",\r\n      \"marketBaseRate\": 6.5,\r\n      \"discountAllowed\": true,\r\n      \"wallet\": \"UK wallet\",\r\n      \"rules\": \"<!--Football - Corners Over/Unders --><br>How many corners will be taken in the match? Only corners that are taken will be counted.<br> All bets apply to Full Time according to the match officials, plus any stoppage time. Extra-time/penalty shoot-outs are not included.<br><br></b>For further information please see <a href=http://content.betfair.com/aboutus/content.asp?sWhichKey=Rules%20and%20Regulations#undefined.do style=color:0163ad; text-decoration: underline; target=_blank>Rules & Regs<br><br>\\n\",\r\n      \"rulesHasDate\": true,\r\n      \"priceLadderDescription\": {\r\n        \"type\": \"CLASSIC\"\r\n      }\r\n    },\r\n    \"totalMatched\": 0.0,\r\n    \"runners\": [\r\n      {\r\n        \"selectionId\": 6384614,\r\n        \"runnerName\": \"Under 13.5 Corners\",\r\n        \"handicap\": 0.0,\r\n        \"sortPriority\": 1,\r\n        \"metadata\": {\r\n          \"runnerId\": \"6384614\"\r\n        },\r\n        \"price\": 0.0\r\n      },\r\n      {\r\n        \"selectionId\": 6384613,\r\n        \"runnerName\": \"Over 13.5 Corners\",\r\n        \"handicap\": 0.0,\r\n        \"sortPriority\": 2,\r\n        \"metadata\": {\r\n          \"runnerId\": \"6384613\"\r\n        },\r\n        \"price\": 0.0\r\n      }\r\n    ],\r\n    \"eventType\": {\r\n      \"id\": \"1\",\r\n      \"name\": \"Soccer\"\r\n    },\r\n    \"competition\": {\r\n      \"id\": \"117\",\r\n      \"name\": \"Spanish La Liga\"\r\n    },\r\n    \"event\": {\r\n      \"id\": \"30668012\",\r\n      \"name\": \"Alaves v Real Madrid\",\r\n      \"countryCode\": \"ES\",\r\n      \"timezone\": \"Europe/London\",\r\n      \"openDate\": \"2021-08-14T19:00:00Z\"\r\n    }\r\n  },\r\n  {\r\n    \"marketId\": \"1.185028548\",\r\n    \"marketName\": \"Over/Under 3.5 Goals\",\r\n    \"marketStartTime\": \"2021-08-14T20:00:00Z\",\r\n    \"description\": {\r\n      \"persistenceEnabled\": true,\r\n      \"bspMarket\": false,\r\n      \"marketTime\": \"2021-08-14T20:00:00Z\",\r\n      \"suspendTime\": \"2021-08-14T20:00:00Z\",\r\n      \"bettingType\": \"ODDS\",\r\n      \"turnInPlayEnabled\": true,\r\n      \"marketType\": \"OVER_UNDER_35\",\r\n      \"regulator\": \"MALTA LOTTERIES AND GAMBLING AUTHORITY\",\r\n      \"marketBaseRate\": 6.5,\r\n      \"discountAllowed\": true,\r\n      \"wallet\": \"UK wallet\",\r\n      \"rules\": \"<!--Football - Over/Unders --><br>How many goals will be scored in this match.<br> All bets apply to Full Time according to the match officials, plus any stoppage time. Extra-time/penalty shoot-outs are not included.<br><br></b>For further information please see <a href=http://content.betfair.com/aboutus/content.asp?sWhichKey=Rules%20and%20Regulations#undefined.do style=color:0163ad; text-decoration: underline; target=_blank>Rules & Regs<br><br>\\n\",\r\n      \"rulesHasDate\": true,\r\n      \"priceLadderDescription\": {\r\n        \"type\": \"CLASSIC\"\r\n      }\r\n    },\r\n    \"totalMatched\": 0.0,\r\n    \"runners\": [\r\n      {\r\n        \"selectionId\": 1222344,\r\n        \"runnerName\": \"Under 3.5 Goals\",\r\n        \"handicap\": 0.0,\r\n        \"sortPriority\": 1,\r\n        \"metadata\": {\r\n          \"runnerId\": \"1222344\"\r\n        },\r\n        \"price\": 0.0\r\n      },\r\n      {\r\n        \"selectionId\": 1222345,\r\n        \"runnerName\": \"Over 3.5 Goals\",\r\n        \"handicap\": 0.0,\r\n        \"sortPriority\": 2,\r\n        \"metadata\": {\r\n          \"runnerId\": \"1222345\"\r\n        },\r\n        \"price\": 0.0\r\n      }\r\n    ],\r\n    \"eventType\": {\r\n      \"id\": \"1\",\r\n      \"name\": \"Soccer\"\r\n    },\r\n    \"competition\": {\r\n      \"id\": \"117\",\r\n      \"name\": \"Spanish La Liga\"\r\n    },\r\n    \"event\": {\r\n      \"id\": \"30668012\",\r\n      \"name\": \"Alaves v Real Madrid\",\r\n      \"countryCode\": \"ES\",\r\n      \"timezone\": \"Europe/London\",\r\n      \"openDate\": \"2021-08-14T19:00:00Z\"\r\n    }\r\n  },\r\n  {\r\n    \"marketId\": \"1.185028504\",\r\n    \"marketName\": \"Over/Under 0.5 Goals\",\r\n    \"marketStartTime\": \"2021-08-14T20:00:00Z\",\r\n    \"description\": {\r\n      \"persistenceEnabled\": true,\r\n      \"bspMarket\": false,\r\n      \"marketTime\": \"2021-08-14T20:00:00Z\",\r\n      \"suspendTime\": \"2021-08-14T20:00:00Z\",\r\n      \"bettingType\": \"ODDS\",\r\n      \"turnInPlayEnabled\": true,\r\n      \"marketType\": \"OVER_UNDER_05\",\r\n      \"regulator\": \"MALTA LOTTERIES AND GAMBLING AUTHORITY\",\r\n      \"marketBaseRate\": 6.5,\r\n      \"discountAllowed\": true,\r\n      \"wallet\": \"UK wallet\",\r\n      \"rules\": \"<!--Football - Over/Unders --><br>How many goals will be scored in this match.<br> All bets apply to Full Time according to the match officials, plus any stoppage time. Extra-time/penalty shoot-outs are not included.<br><br></b>For further information please see <a href=http://content.betfair.com/aboutus/content.asp?sWhichKey=Rules%20and%20Regulations#undefined.do style=color:0163ad; text-decoration: underline; target=_blank>Rules & Regs<br><br>\\n\",\r\n      \"rulesHasDate\": true,\r\n      \"priceLadderDescription\": {\r\n        \"type\": \"CLASSIC\"\r\n      }\r\n    },\r\n    \"totalMatched\": 0.0,\r\n    \"runners\": [\r\n      {\r\n        \"selectionId\": 5851482,\r\n        \"runnerName\": \"Under 0.5 Goals\",\r\n        \"handicap\": 0.0,\r\n        \"sortPriority\": 1,\r\n        \"metadata\": {\r\n          \"runnerId\": \"5851482\"\r\n        },\r\n        \"price\": 0.0\r\n      },\r\n      {\r\n        \"selectionId\": 5851483,\r\n        \"runnerName\": \"Over 0.5 Goals\",\r\n        \"handicap\": 0.0,\r\n        \"sortPriority\": 2,\r\n        \"metadata\": {\r\n          \"runnerId\": \"5851483\"\r\n        },\r\n        \"price\": 0.0\r\n      }\r\n    ],\r\n    \"eventType\": {\r\n      \"id\": \"1\",\r\n      \"name\": \"Soccer\"\r\n    },\r\n    \"competition\": {\r\n      \"id\": \"117\",\r\n      \"name\": \"Spanish La Liga\"\r\n    },\r\n    \"event\": {\r\n      \"id\": \"30668012\",\r\n      \"name\": \"Alaves v Real Madrid\",\r\n      \"countryCode\": \"ES\",\r\n      \"timezone\": \"Europe/London\",\r\n      \"openDate\": \"2021-08-14T19:00:00Z\"\r\n    }\r\n  },\r\n  {\r\n    \"marketId\": \"1.185028436\",\r\n    \"marketName\": \"Over/Under 2.5 Goals\",\r\n    \"marketStartTime\": \"2021-08-14T20:00:00Z\",\r\n    \"description\": {\r\n      \"persistenceEnabled\": true,\r\n      \"bspMarket\": false,\r\n      \"marketTime\": \"2021-08-14T20:00:00Z\",\r\n      \"suspendTime\": \"2021-08-14T20:00:00Z\",\r\n      \"bettingType\": \"ODDS\",\r\n      \"turnInPlayEnabled\": true,\r\n      \"marketType\": \"OVER_UNDER_25\",\r\n      \"regulator\": \"MALTA LOTTERIES AND GAMBLING AUTHORITY\",\r\n      \"marketBaseRate\": 6.5,\r\n      \"discountAllowed\": true,\r\n      \"wallet\": \"UK wallet\",\r\n      \"rules\": \"<!--Football - Over/Unders --><br>How many goals will be scored in this match.<br> All bets apply to Full Time according to the match officials, plus any stoppage time. Extra-time/penalty shoot-outs are not included.<br><br></b>For further information please see <a href=http://content.betfair.com/aboutus/content.asp?sWhichKey=Rules%20and%20Regulations#undefined.do style=color:0163ad; text-decoration: underline; target=_blank>Rules & Regs<br><br>\\n\",\r\n      \"rulesHasDate\": true,\r\n      \"priceLadderDescription\": {\r\n        \"type\": \"CLASSIC\"\r\n      }\r\n    },\r\n    \"totalMatched\": 7.34315,\r\n    \"runners\": [\r\n      {\r\n        \"selectionId\": 47972,\r\n        \"runnerName\": \"Under 2.5 Goals\",\r\n        \"handicap\": 0.0,\r\n        \"sortPriority\": 1,\r\n        \"metadata\": {\r\n          \"runnerId\": \"47972\"\r\n        }\r\n      },\r\n      {\r\n        \"selectionId\": 47973,\r\n        \"runnerName\": \"Over 2.5 Goals\",\r\n        \"handicap\": 0.0,\r\n        \"sortPriority\": 2,\r\n        \"metadata\": {\r\n          \"runnerId\": \"47973\"\r\n        }\r\n      }\r\n    ],\r\n    \"eventType\": {\r\n      \"id\": \"1\",\r\n      \"name\": \"Soccer\"\r\n    },\r\n    \"competition\": {\r\n      \"id\": \"117\",\r\n      \"name\": \"Spanish La Liga\"\r\n    },\r\n    \"event\": {\r\n      \"id\": \"30668012\",\r\n      \"name\": \"Alaves v Real Madrid\",\r\n      \"countryCode\": \"ES\",\r\n      \"timezone\": \"Europe/London\",\r\n      \"openDate\": \"2021-08-14T19:00:00Z\"\r\n    }\r\n  },\r\n  {\r\n    \"marketId\": \"1.185028434\",\r\n    \"marketName\": \"Corners Over/Under 8.5\",\r\n    \"marketStartTime\": \"2021-08-14T20:00:00Z\",\r\n    \"description\": {\r\n      \"persistenceEnabled\": false,\r\n      \"bspMarket\": false,\r\n      \"marketTime\": \"2021-08-14T20:00:00Z\",\r\n      \"suspendTime\": \"2021-08-14T20:00:00Z\",\r\n      \"bettingType\": \"ODDS\",\r\n      \"turnInPlayEnabled\": false,\r\n      \"marketType\": \"OVER_UNDER_85_CORNR\",\r\n      \"regulator\": \"MALTA LOTTERIES AND GAMBLING AUTHORITY\",\r\n      \"marketBaseRate\": 6.5,\r\n      \"discountAllowed\": true,\r\n      \"wallet\": \"UK wallet\",\r\n      \"rules\": \"<!--Football - Corners Over/Unders --><br>How many corners will be taken in the match? Only corners that are taken will be counted.<br> All bets apply to Full Time according to the match officials, plus any stoppage time. Extra-time/penalty shoot-outs are not included.<br><br></b>For further information please see <a href=http://content.betfair.com/aboutus/content.asp?sWhichKey=Rules%20and%20Regulations#undefined.do style=color:0163ad; text-decoration: underline; target=_blank>Rules & Regs<br><br>\\n\",\r\n      \"rulesHasDate\": true,\r\n      \"priceLadderDescription\": {\r\n        \"type\": \"CLASSIC\"\r\n      }\r\n    },\r\n    \"totalMatched\": 0.0,\r\n    \"runners\": [\r\n      {\r\n        \"selectionId\": 7076459,\r\n        \"runnerName\": \"Under 8.5 Corners\",\r\n        \"handicap\": 0.0,\r\n        \"sortPriority\": 1,\r\n        \"metadata\": {\r\n          \"runnerId\": \"7076459\"\r\n        }\r\n      },\r\n      {\r\n        \"selectionId\": 7076458,\r\n        \"runnerName\": \"Over 8.5 Corners\",\r\n        \"handicap\": 0.0,\r\n        \"sortPriority\": 2,\r\n        \"metadata\": {\r\n          \"runnerId\": \"7076458\"\r\n        }\r\n      }\r\n    ],\r\n    \"eventType\": {\r\n      \"id\": \"1\",\r\n      \"name\": \"Soccer\"\r\n    },\r\n    \"competition\": {\r\n      \"id\": \"117\",\r\n      \"name\": \"Spanish La Liga\"\r\n    },\r\n    \"event\": {\r\n      \"id\": \"30668012\",\r\n      \"name\": \"Alaves v Real Madrid\",\r\n      \"countryCode\": \"ES\",\r\n      \"timezone\": \"Europe/London\",\r\n      \"openDate\": \"2021-08-14T19:00:00Z\"\r\n    }\r\n  },\r\n  {\r\n    \"marketId\": \"1.185028508\",\r\n    \"marketName\": \"Correct Score\",\r\n    \"marketStartTime\": \"2021-08-14T20:00:00Z\",\r\n    \"description\": {\r\n      \"persistenceEnabled\": true,\r\n      \"bspMarket\": false,\r\n      \"marketTime\": \"2021-08-14T20:00:00Z\",\r\n      \"suspendTime\": \"2021-08-14T20:00:00Z\",\r\n      \"bettingType\": \"ODDS\",\r\n      \"turnInPlayEnabled\": true,\r\n      \"marketType\": \"CORRECT_SCORE\",\r\n      \"regulator\": \"MALTA LOTTERIES AND GAMBLING AUTHORITY\",\r\n      \"marketBaseRate\": 6.5,\r\n      \"discountAllowed\": true,\r\n      \"wallet\": \"UK wallet\",\r\n      \"rules\": \"<!--Football - Correct Score --><br>Predict the score of this match.<br>This market will not be partially settled during the fixture and will be fully settled at full time.<br> All bets apply to Full Time according to the match officials, plus any stoppage time. Extra-time/penalty shoot-outs are not included.<br><br></b>For further information please see <a href=http://content.betfair.com/aboutus/content.asp?sWhichKey=Rules%20and%20Regulations#undefined.do style=color:0163ad; text-decoration: underline; target=_blank>Rules & Regs<br><br>\\n\",\r\n      \"rulesHasDate\": true,\r\n      \"priceLadderDescription\": {\r\n        \"type\": \"CLASSIC\"\r\n      }\r\n    },\r\n    \"totalMatched\": 0.0,\r\n    \"runners\": [\r\n      {\r\n        \"selectionId\": 1,\r\n        \"runnerName\": \"0 - 0\",\r\n        \"handicap\": 0.0,\r\n        \"sortPriority\": 1,\r\n        \"metadata\": {\r\n          \"runnerId\": \"1\"\r\n        }\r\n      },\r\n      {\r\n        \"selectionId\": 4,\r\n        \"runnerName\": \"0 - 1\",\r\n        \"handicap\": 0.0,\r\n        \"sortPriority\": 2,\r\n        \"metadata\": {\r\n          \"runnerId\": \"4\"\r\n        }\r\n      },\r\n      {\r\n        \"selectionId\": 9,\r\n        \"runnerName\": \"0 - 2\",\r\n        \"handicap\": 0.0,\r\n        \"sortPriority\": 3,\r\n        \"metadata\": {\r\n          \"runnerId\": \"9\"\r\n        }\r\n      },\r\n      {\r\n        \"selectionId\": 16,\r\n        \"runnerName\": \"0 - 3\",\r\n        \"handicap\": 0.0,\r\n        \"sortPriority\": 4,\r\n        \"metadata\": {\r\n          \"runnerId\": \"16\"\r\n        }\r\n      },\r\n      {\r\n        \"selectionId\": 2,\r\n        \"runnerName\": \"1 - 0\",\r\n        \"handicap\": 0.0,\r\n        \"sortPriority\": 5,\r\n        \"metadata\": {\r\n          \"runnerId\": \"2\"\r\n        }\r\n      },\r\n      {\r\n        \"selectionId\": 3,\r\n        \"runnerName\": \"1 - 1\",\r\n        \"handicap\": 0.0,\r\n        \"sortPriority\": 6,\r\n        \"metadata\": {\r\n          \"runnerId\": \"3\"\r\n        }\r\n      },\r\n      {\r\n        \"selectionId\": 8,\r\n        \"runnerName\": \"1 - 2\",\r\n        \"handicap\": 0.0,\r\n        \"sortPriority\": 7,\r\n        \"metadata\": {\r\n          \"runnerId\": \"8\"\r\n        }\r\n      },\r\n      {\r\n        \"selectionId\": 15,\r\n        \"runnerName\": \"1 - 3\",\r\n        \"handicap\": 0.0,\r\n        \"sortPriority\": 8,\r\n        \"metadata\": {\r\n          \"runnerId\": \"15\"\r\n        }\r\n      },\r\n      {\r\n        \"selectionId\": 5,\r\n        \"runnerName\": \"2 - 0\",\r\n        \"handicap\": 0.0,\r\n        \"sortPriority\": 9,\r\n        \"metadata\": {\r\n          \"runnerId\": \"5\"\r\n        }\r\n      },\r\n      {\r\n        \"selectionId\": 6,\r\n        \"runnerName\": \"2 - 1\",\r\n        \"handicap\": 0.0,\r\n        \"sortPriority\": 10,\r\n        \"metadata\": {\r\n          \"runnerId\": \"6\"\r\n        }\r\n      },\r\n      {\r\n        \"selectionId\": 7,\r\n        \"runnerName\": \"2 - 2\",\r\n        \"handicap\": 0.0,\r\n        \"sortPriority\": 11,\r\n        \"metadata\": {\r\n          \"runnerId\": \"7\"\r\n        }\r\n      },\r\n      {\r\n        \"selectionId\": 14,\r\n        \"runnerName\": \"2 - 3\",\r\n        \"handicap\": 0.0,\r\n        \"sortPriority\": 12,\r\n        \"metadata\": {\r\n          \"runnerId\": \"14\"\r\n        }\r\n      },\r\n      {\r\n        \"selectionId\": 10,\r\n        \"runnerName\": \"3 - 0\",\r\n        \"handicap\": 0.0,\r\n        \"sortPriority\": 13,\r\n        \"metadata\": {\r\n          \"runnerId\": \"10\"\r\n        }\r\n      },\r\n      {\r\n        \"selectionId\": 11,\r\n        \"runnerName\": \"3 - 1\",\r\n        \"handicap\": 0.0,\r\n        \"sortPriority\": 14,\r\n        \"metadata\": {\r\n          \"runnerId\": \"11\"\r\n        }\r\n      },\r\n      {\r\n        \"selectionId\": 12,\r\n        \"runnerName\": \"3 - 2\",\r\n        \"handicap\": 0.0,\r\n        \"sortPriority\": 15,\r\n        \"metadata\": {\r\n          \"runnerId\": \"12\"\r\n        }\r\n      },\r\n      {\r\n        \"selectionId\": 13,\r\n        \"runnerName\": \"3 - 3\",\r\n        \"handicap\": 0.0,\r\n        \"sortPriority\": 16,\r\n        \"metadata\": {\r\n          \"runnerId\": \"13\"\r\n        }\r\n      },\r\n      {\r\n        \"selectionId\": 9063254,\r\n        \"runnerName\": \"Any Other Home Win\",\r\n        \"handicap\": 0.0,\r\n        \"sortPriority\": 17,\r\n        \"metadata\": {\r\n          \"runnerId\": \"9063254\"\r\n        }\r\n      },\r\n      {\r\n        \"selectionId\": 9063255,\r\n        \"runnerName\": \"Any Other Away Win\",\r\n        \"handicap\": 0.0,\r\n        \"sortPriority\": 18,\r\n        \"metadata\": {\r\n          \"runnerId\": \"9063255\"\r\n        }\r\n      },\r\n      {\r\n        \"selectionId\": 9063256,\r\n        \"runnerName\": \"Any Other Draw\",\r\n        \"handicap\": 0.0,\r\n        \"sortPriority\": 19,\r\n        \"metadata\": {\r\n          \"runnerId\": \"9063256\"\r\n        }\r\n      }\r\n    ],\r\n    \"eventType\": {\r\n      \"id\": \"1\",\r\n      \"name\": \"Soccer\"\r\n    },\r\n    \"competition\": {\r\n      \"id\": \"117\",\r\n      \"name\": \"Spanish La Liga\"\r\n    },\r\n    \"event\": {\r\n      \"id\": \"30668012\",\r\n      \"name\": \"Alaves v Real Madrid\",\r\n      \"countryCode\": \"ES\",\r\n      \"timezone\": \"Europe/London\",\r\n      \"openDate\": \"2021-08-14T19:00:00Z\"\r\n    }\r\n  },\r\n  {\r\n    \"marketId\": \"1.185028439\",\r\n    \"marketName\": \"Over/Under 4.5 Goals\",\r\n    \"marketStartTime\": \"2021-08-14T20:00:00Z\",\r\n    \"description\": {\r\n      \"persistenceEnabled\": true,\r\n      \"bspMarket\": false,\r\n      \"marketTime\": \"2021-08-14T20:00:00Z\",\r\n      \"suspendTime\": \"2021-08-14T20:00:00Z\",\r\n      \"bettingType\": \"ODDS\",\r\n      \"turnInPlayEnabled\": true,\r\n      \"marketType\": \"OVER_UNDER_45\",\r\n      \"regulator\": \"MALTA LOTTERIES AND GAMBLING AUTHORITY\",\r\n      \"marketBaseRate\": 6.5,\r\n      \"discountAllowed\": true,\r\n      \"wallet\": \"UK wallet\",\r\n      \"rules\": \"<!--Football - Over/Unders --><br>How many goals will be scored in this match.<br> All bets apply to Full Time according to the match officials, plus any stoppage time. Extra-time/penalty shoot-outs are not included.<br><br></b>For further information please see <a href=http://content.betfair.com/aboutus/content.asp?sWhichKey=Rules%20and%20Regulations#undefined.do style=color:0163ad; text-decoration: underline; target=_blank>Rules & Regs<br><br>\\n\",\r\n      \"rulesHasDate\": true,\r\n      \"priceLadderDescription\": {\r\n        \"type\": \"CLASSIC\"\r\n      }\r\n    },\r\n    \"totalMatched\": 0.0,\r\n    \"runners\": [\r\n      {\r\n        \"selectionId\": 1222347,\r\n        \"runnerName\": \"Under 4.5 Goals\",\r\n        \"handicap\": 0.0,\r\n        \"sortPriority\": 1,\r\n        \"metadata\": {\r\n          \"runnerId\": \"1222347\"\r\n        }\r\n      },\r\n      {\r\n        \"selectionId\": 1222346,\r\n        \"runnerName\": \"Over 4.5 Goals\",\r\n        \"handicap\": 0.0,\r\n        \"sortPriority\": 2,\r\n        \"metadata\": {\r\n          \"runnerId\": \"1222346\"\r\n        }\r\n      }\r\n    ],\r\n    \"eventType\": {\r\n      \"id\": \"1\",\r\n      \"name\": \"Soccer\"\r\n    },\r\n    \"competition\": {\r\n      \"id\": \"117\",\r\n      \"name\": \"Spanish La Liga\"\r\n    },\r\n    \"event\": {\r\n      \"id\": \"30668012\",\r\n      \"name\": \"Alaves v Real Madrid\",\r\n      \"countryCode\": \"ES\",\r\n      \"timezone\": \"Europe/London\",\r\n      \"openDate\": \"2021-08-14T19:00:00Z\"\r\n    }\r\n  },\r\n  {\r\n    \"marketId\": \"1.185028507\",\r\n    \"marketName\": \"Over/Under 5.5 Goals\",\r\n    \"marketStartTime\": \"2021-08-14T20:00:00Z\",\r\n    \"description\": {\r\n      \"persistenceEnabled\": true,\r\n      \"bspMarket\": false,\r\n      \"marketTime\": \"2021-08-14T20:00:00Z\",\r\n      \"suspendTime\": \"2021-08-14T20:00:00Z\",\r\n      \"bettingType\": \"ODDS\",\r\n      \"turnInPlayEnabled\": true,\r\n      \"marketType\": \"OVER_UNDER_55\",\r\n      \"regulator\": \"MALTA LOTTERIES AND GAMBLING AUTHORITY\",\r\n      \"marketBaseRate\": 6.5,\r\n      \"discountAllowed\": true,\r\n      \"wallet\": \"UK wallet\",\r\n      \"rules\": \"<!--Football - Over/Unders --><br>How many goals will be scored in this match.<br> All bets apply to Full Time according to the match officials, plus any stoppage time. Extra-time/penalty shoot-outs are not included.<br><br></b>For further information please see <a href=http://content.betfair.com/aboutus/content.asp?sWhichKey=Rules%20and%20Regulations#undefined.do style=color:0163ad; text-decoration: underline; target=_blank>Rules & Regs<br><br>\\n\",\r\n      \"rulesHasDate\": true,\r\n      \"priceLadderDescription\": {\r\n        \"type\": \"CLASSIC\"\r\n      }\r\n    },\r\n    \"totalMatched\": 0.0,\r\n    \"runners\": [\r\n      {\r\n        \"selectionId\": 1485567,\r\n        \"runnerName\": \"Under 5.5 Goals\",\r\n        \"handicap\": 0.0,\r\n        \"sortPriority\": 1,\r\n        \"metadata\": {\r\n          \"runnerId\": \"1485567\"\r\n        }\r\n      },\r\n      {\r\n        \"selectionId\": 1485568,\r\n        \"runnerName\": \"Over 5.5 Goals\",\r\n        \"handicap\": 0.0,\r\n        \"sortPriority\": 2,\r\n        \"metadata\": {\r\n          \"runnerId\": \"1485568\"\r\n        }\r\n      }\r\n    ],\r\n    \"eventType\": {\r\n      \"id\": \"1\",\r\n      \"name\": \"Soccer\"\r\n    },\r\n    \"competition\": {\r\n      \"id\": \"117\",\r\n      \"name\": \"Spanish La Liga\"\r\n    },\r\n    \"event\": {\r\n      \"id\": \"30668012\",\r\n      \"name\": \"Alaves v Real Madrid\",\r\n      \"countryCode\": \"ES\",\r\n      \"timezone\": \"Europe/London\",\r\n      \"openDate\": \"2021-08-14T19:00:00Z\"\r\n    }\r\n  },\r\n  {\r\n    \"marketId\": \"1.185028506\",\r\n    \"marketName\": \"Over/Under 6.5 Goals\",\r\n    \"marketStartTime\": \"2021-08-14T20:00:00Z\",\r\n    \"description\": {\r\n      \"persistenceEnabled\": true,\r\n      \"bspMarket\": false,\r\n      \"marketTime\": \"2021-08-14T20:00:00Z\",\r\n      \"suspendTime\": \"2021-08-14T20:00:00Z\",\r\n      \"bettingType\": \"ODDS\",\r\n      \"turnInPlayEnabled\": true,\r\n      \"marketType\": \"OVER_UNDER_65\",\r\n      \"regulator\": \"MALTA LOTTERIES AND GAMBLING AUTHORITY\",\r\n      \"marketBaseRate\": 6.5,\r\n      \"discountAllowed\": true,\r\n      \"wallet\": \"UK wallet\",\r\n      \"rules\": \"<!--Football - Over/Unders --><br>How many goals will be scored in this match.<br> All bets apply to Full Time according to the match officials, plus any stoppage time. Extra-time/penalty shoot-outs are not included.<br><br></b>For further information please see <a href=http://content.betfair.com/aboutus/content.asp?sWhichKey=Rules%20and%20Regulations#undefined.do style=color:0163ad; text-decoration: underline; target=_blank>Rules & Regs<br><br>\\n\",\r\n      \"rulesHasDate\": true,\r\n      \"priceLadderDescription\": {\r\n        \"type\": \"CLASSIC\"\r\n      }\r\n    },\r\n    \"totalMatched\": 0.0,\r\n    \"runners\": [\r\n      {\r\n        \"selectionId\": 2542448,\r\n        \"runnerName\": \"Under 6.5 Goals\",\r\n        \"handicap\": 0.0,\r\n        \"sortPriority\": 1,\r\n        \"metadata\": {\r\n          \"runnerId\": \"2542448\"\r\n        }\r\n      },\r\n      {\r\n        \"selectionId\": 2542449,\r\n        \"runnerName\": \"Over 6.5 Goals\",\r\n        \"handicap\": 0.0,\r\n        \"sortPriority\": 2,\r\n        \"metadata\": {\r\n          \"runnerId\": \"2542449\"\r\n        }\r\n      }\r\n    ],\r\n    \"eventType\": {\r\n      \"id\": \"1\",\r\n      \"name\": \"Soccer\"\r\n    },\r\n    \"competition\": {\r\n      \"id\": \"117\",\r\n      \"name\": \"Spanish La Liga\"\r\n    },\r\n    \"event\": {\r\n      \"id\": \"30668012\",\r\n      \"name\": \"Alaves v Real Madrid\",\r\n      \"countryCode\": \"ES\",\r\n      \"timezone\": \"Europe/London\",\r\n      \"openDate\": \"2021-08-14T19:00:00Z\"\r\n    }\r\n  },\r\n  {\r\n    \"marketId\": \"1.185028496\",\r\n    \"marketName\": \"Match Odds\",\r\n    \"marketStartTime\": \"2021-08-14T20:00:00Z\",\r\n    \"description\": {\r\n      \"persistenceEnabled\": true,\r\n      \"bspMarket\": false,\r\n      \"marketTime\": \"2021-08-14T20:00:00Z\",\r\n      \"suspendTime\": \"2021-08-14T20:00:00Z\",\r\n      \"bettingType\": \"ODDS\",\r\n      \"turnInPlayEnabled\": true,\r\n      \"marketType\": \"MATCH_ODDS\",\r\n      \"regulator\": \"MALTA LOTTERIES AND GAMBLING AUTHORITY\",\r\n      \"marketBaseRate\": 6.5,\r\n      \"discountAllowed\": true,\r\n      \"wallet\": \"UK wallet\",\r\n      \"rules\": \"<!--Football - Match Odds --><br>Predict the result of this match.<br> All bets apply to Full Time according to the match officials, plus any stoppage time. Extra-time/penalty shoot-outs are not included.<br><br></b>For further information please see <a href=http://content.betfair.com/aboutus/content.asp?sWhichKey=Rules%20and%20Regulations#undefined.do style=color:0163ad; text-decoration: underline; target=_blank>Rules & Regs<br><br>\\n\",\r\n      \"rulesHasDate\": true,\r\n      \"priceLadderDescription\": {\r\n        \"type\": \"CLASSIC\"\r\n      }\r\n    },\r\n    \"totalMatched\": 504.62681000000003,\r\n    \"runners\": [\r\n      {\r\n        \"selectionId\": 60324,\r\n        \"runnerName\": \"Alaves\",\r\n        \"handicap\": 0.0,\r\n        \"sortPriority\": 1,\r\n        \"metadata\": {\r\n          \"runnerId\": \"60324\"\r\n        }\r\n      },\r\n      {\r\n        \"selectionId\": 2426,\r\n        \"runnerName\": \"Real Madrid\",\r\n        \"handicap\": 0.0,\r\n        \"sortPriority\": 2,\r\n        \"metadata\": {\r\n          \"runnerId\": \"2426\"\r\n        }\r\n      },\r\n      {\r\n        \"selectionId\": 58805,\r\n        \"runnerName\": \"The Draw\",\r\n        \"handicap\": 0.0,\r\n        \"sortPriority\": 3,\r\n        \"metadata\": {\r\n          \"runnerId\": \"58805\"\r\n        }\r\n      }\r\n    ],\r\n    \"eventType\": {\r\n      \"id\": \"1\",\r\n      \"name\": \"Soccer\"\r\n    },\r\n    \"competition\": {\r\n      \"id\": \"117\",\r\n      \"name\": \"Spanish La Liga\"\r\n    },\r\n    \"event\": {\r\n      \"id\": \"30668012\",\r\n      \"name\": \"Alaves v Real Madrid\",\r\n      \"countryCode\": \"ES\",\r\n      \"timezone\": \"Europe/London\",\r\n      \"openDate\": \"2021-08-14T19:00:00Z\"\r\n    }\r\n  },\r\n  {\r\n    \"marketId\": \"1.185028499\",\r\n    \"marketName\": \"Total Goals Odd/Even\",\r\n    \"marketStartTime\": \"2021-08-14T20:00:00Z\",\r\n    \"description\": {\r\n      \"persistenceEnabled\": true,\r\n      \"bspMarket\": false,\r\n      \"marketTime\": \"2021-08-14T20:00:00Z\",\r\n      \"suspendTime\": \"2021-08-14T20:00:00Z\",\r\n      \"bettingType\": \"ODDS\",\r\n      \"turnInPlayEnabled\": true,\r\n      \"marketType\": \"ODD_OR_EVEN\",\r\n      \"regulator\": \"MALTA LOTTERIES AND GAMBLING AUTHORITY\",\r\n      \"marketBaseRate\": 6.5,\r\n      \"discountAllowed\": true,\r\n      \"wallet\": \"UK wallet\",\r\n      \"rules\": \"<!--Football - Odd or Even --><br>Will the sum of the goals in this match equal an odd or even number. In the event of zero goals being scored in this match, the result will be settled as EVEN.<br> All bets apply to Full Time according to the match officials, plus any stoppage time. Extra-time/penalty shoot-outs are not included.<br><br></b>For further information please see <a href=http://content.betfair.com/aboutus/content.asp?sWhichKey=Rules%20and%20Regulations#undefined.do style=color:0163ad; text-decoration: underline; target=_blank>Rules & Regs<br><br>\\n\",\r\n      \"rulesHasDate\": true,\r\n      \"priceLadderDescription\": {\r\n        \"type\": \"CLASSIC\"\r\n      }\r\n    },\r\n    \"totalMatched\": 0.0,\r\n    \"runners\": [\r\n      {\r\n        \"selectionId\": 955466,\r\n        \"runnerName\": \"Odd\",\r\n        \"handicap\": 0.0,\r\n        \"sortPriority\": 1,\r\n        \"metadata\": {\r\n          \"runnerId\": \"955466\"\r\n        }\r\n      },\r\n      {\r\n        \"selectionId\": 955467,\r\n        \"runnerName\": \"Even\",\r\n        \"handicap\": 0.0,\r\n        \"sortPriority\": 2,\r\n        \"metadata\": {\r\n          \"runnerId\": \"955467\"\r\n        }\r\n      }\r\n    ],\r\n    \"eventType\": {\r\n      \"id\": \"1\",\r\n      \"name\": \"Soccer\"\r\n    },\r\n    \"competition\": {\r\n      \"id\": \"117\",\r\n      \"name\": \"Spanish La Liga\"\r\n    },\r\n    \"event\": {\r\n      \"id\": \"30668012\",\r\n      \"name\": \"Alaves v Real Madrid\",\r\n      \"countryCode\": \"ES\",\r\n      \"timezone\": \"Europe/London\",\r\n      \"openDate\": \"2021-08-14T19:00:00Z\"\r\n    }\r\n  },\r\n  {\r\n    \"marketId\": \"1.185028485\",\r\n    \"marketName\": \"Over/Under 8.5 Goals\",\r\n    \"marketStartTime\": \"2021-08-14T20:00:00Z\",\r\n    \"description\": {\r\n      \"persistenceEnabled\": true,\r\n      \"bspMarket\": false,\r\n      \"marketTime\": \"2021-08-14T20:00:00Z\",\r\n      \"suspendTime\": \"2021-08-14T20:00:00Z\",\r\n      \"bettingType\": \"ODDS\",\r\n      \"turnInPlayEnabled\": true,\r\n      \"marketType\": \"OVER_UNDER_85\",\r\n      \"regulator\": \"MALTA LOTTERIES AND GAMBLING AUTHORITY\",\r\n      \"marketBaseRate\": 6.5,\r\n      \"discountAllowed\": true,\r\n      \"wallet\": \"UK wallet\",\r\n      \"rules\": \"<!--Football - Over/Unders --><br>How many goals will be scored in this match.<br> All bets apply to Full Time according to the match officials, plus any stoppage time. Extra-time/penalty shoot-outs are not included.<br><br></b>For further information please see <a href=http://content.betfair.com/aboutus/content.asp?sWhichKey=Rules%20and%20Regulations#undefined.do style=color:0163ad; text-decoration: underline; target=_blank>Rules & Regs<br><br>\\n\",\r\n      \"rulesHasDate\": true,\r\n      \"priceLadderDescription\": {\r\n        \"type\": \"CLASSIC\"\r\n      }\r\n    },\r\n    \"totalMatched\": 0.0,\r\n    \"runners\": [\r\n      {\r\n        \"selectionId\": 2407528,\r\n        \"runnerName\": \"Under 8.5 Goals\",\r\n        \"handicap\": 0.0,\r\n        \"sortPriority\": 1,\r\n        \"metadata\": {\r\n          \"runnerId\": \"2407528\"\r\n        }\r\n      },\r\n      {\r\n        \"selectionId\": 2407529,\r\n        \"runnerName\": \"Over 8.5 Goals\",\r\n        \"handicap\": 0.0,\r\n        \"sortPriority\": 2,\r\n        \"metadata\": {\r\n          \"runnerId\": \"2407529\"\r\n        }\r\n      }\r\n    ],\r\n    \"eventType\": {\r\n      \"id\": \"1\",\r\n      \"name\": \"Soccer\"\r\n    },\r\n    \"competition\": {\r\n      \"id\": \"117\",\r\n      \"name\": \"Spanish La Liga\"\r\n    },\r\n    \"event\": {\r\n      \"id\": \"30668012\",\r\n      \"name\": \"Alaves v Real Madrid\",\r\n      \"countryCode\": \"ES\",\r\n      \"timezone\": \"Europe/London\",\r\n      \"openDate\": \"2021-08-14T19:00:00Z\"\r\n    }\r\n  },\r\n  {\r\n    \"marketId\": \"1.185028490\",\r\n    \"marketName\": \"Over/Under 7.5 Goals\",\r\n    \"marketStartTime\": \"2021-08-14T20:00:00Z\",\r\n    \"description\": {\r\n      \"persistenceEnabled\": true,\r\n      \"bspMarket\": false,\r\n      \"marketTime\": \"2021-08-14T20:00:00Z\",\r\n      \"suspendTime\": \"2021-08-14T20:00:00Z\",\r\n      \"bettingType\": \"ODDS\",\r\n      \"turnInPlayEnabled\": true,\r\n      \"marketType\": \"OVER_UNDER_75\",\r\n      \"regulator\": \"MALTA LOTTERIES AND GAMBLING AUTHORITY\",\r\n      \"marketBaseRate\": 6.5,\r\n      \"discountAllowed\": true,\r\n      \"wallet\": \"UK wallet\",\r\n      \"rules\": \"<!--Football - Over/Unders --><br>How many goals will be scored in this match.<br> All bets apply to Full Time according to the match officials, plus any stoppage time. Extra-time/penalty shoot-outs are not included.<br><br></b>For further information please see <a href=http://content.betfair.com/aboutus/content.asp?sWhichKey=Rules%20and%20Regulations#undefined.do style=color:0163ad; text-decoration: underline; target=_blank>Rules & Regs<br><br>\\n\",\r\n      \"rulesHasDate\": true,\r\n      \"priceLadderDescription\": {\r\n        \"type\": \"CLASSIC\"\r\n      }\r\n    },\r\n    \"totalMatched\": 0.0,\r\n    \"runners\": [\r\n      {\r\n        \"selectionId\": 1485572,\r\n        \"runnerName\": \"Under 7.5 Goals\",\r\n        \"handicap\": 0.0,\r\n        \"sortPriority\": 1,\r\n        \"metadata\": {\r\n          \"runnerId\": \"1485572\"\r\n        }\r\n      },\r\n      {\r\n        \"selectionId\": 1485573,\r\n        \"runnerName\": \"Over 7.5 Goals\",\r\n        \"handicap\": 0.0,\r\n        \"sortPriority\": 2,\r\n        \"metadata\": {\r\n          \"runnerId\": \"1485573\"\r\n        }\r\n      }\r\n    ],\r\n    \"eventType\": {\r\n      \"id\": \"1\",\r\n      \"name\": \"Soccer\"\r\n    },\r\n    \"competition\": {\r\n      \"id\": \"117\",\r\n      \"name\": \"Spanish La Liga\"\r\n    },\r\n    \"event\": {\r\n      \"id\": \"30668012\",\r\n      \"name\": \"Alaves v Real Madrid\",\r\n      \"countryCode\": \"ES\",\r\n      \"timezone\": \"Europe/London\",\r\n      \"openDate\": \"2021-08-14T19:00:00Z\"\r\n    }\r\n  }\r\n]")

			//huge price issue
			//consumer.OnMessageBeforeCommit(@$"5￻2￻N/A￻N/A￻0￻N/A￻2￻6￻[ {{ ""marketId"": ""1.185986770"", ""marketName"": ""Over/Under 5.5 Goals"", ""marketStartTime"": ""2021-08-04T19:30:00Z"", ""description"": {{ ""persistenceEnabled"": true, ""bspMarket"": false, ""marketTime"": ""2021-08-04T19:30:00Z"", ""suspendTime"": ""2021-08-04T19:30:00Z"", ""bettingType"": ""ODDS"", ""turnInPlayEnabled"": true, ""marketType"": ""OVER_UNDER_55"", ""regulator"": ""MALTA LOTTERIES AND GAMBLING AUTHORITY"", ""marketBaseRate"": 6.5, ""discountAllowed"": true, ""wallet"": ""UK wallet"", ""rules"": ""<!--Football - Over/Unders --><br>How many goals will be scored in this match.<br> All bets apply to Full Time according to the match officials, plus any stoppage time. Extra-time/penalty shoot-outs are not included.<br><br></b>For further information please see <a href=http://content.betfair.com/aboutus/content.asp?sWhichKey=Rules%20and%20Regulations#undefined.do style=color:0163ad; text-decoration: underline; target=_blank>Rules & Regs<br><br>\n"", ""rulesHasDate"": true, ""priceLadderDescription"": {{ ""type"": ""CLASSIC"" }} }}, ""totalMatched"": 3169.09992, ""runners"": [ {{ ""selectionId"": 1485567, ""runnerName"": ""Under 5.5 Goals"", ""handicap"": 0.0, ""sortPriority"": 1, ""metadata"": {{ ""runnerId"": ""1485567"" }}, ""price"": 1.01 }}, {{ ""selectionId"": 1485568, ""runnerName"": ""Over 5.5 Goals"", ""handicap"": 0.0, ""sortPriority"": 2, ""metadata"": {{ ""runnerId"": ""1485568"" }}, ""price"": 110.0 }} ], ""eventType"": {{ ""id"": ""1"", ""name"": ""Soccer"" }}, ""competition"": {{ ""id"": ""89219"", ""name"": ""Brazilian Cup"" }}, ""event"": {{ ""id"": ""30756093"", ""name"": ""CRB v Fortaleza EC"", ""countryCode"": ""BR"", ""timezone"": ""GMT"", ""openDate"": ""2021-08-04T19:30:00Z"" }} }}, {{ ""marketId"": ""1.185986773"", ""marketName"": ""Over/Under 4.5 Goals"", ""marketStartTime"": ""2021-08-04T19:30:00Z"", ""description"": {{ ""persistenceEnabled"": true, ""bspMarket"": false, ""marketTime"": ""2021-08-04T19:30:00Z"", ""suspendTime"": ""2021-08-04T19:30:00Z"", ""bettingType"": ""ODDS"", ""turnInPlayEnabled"": true, ""marketType"": ""OVER_UNDER_45"", ""regulator"": ""MALTA LOTTERIES AND GAMBLING AUTHORITY"", ""marketBaseRate"": 6.5, ""discountAllowed"": true, ""wallet"": ""UK wallet"", ""rules"": ""<!--Football - Over/Unders --><br>How many goals will be scored in this match.<br> All bets apply to Full Time according to the match officials, plus any stoppage time. Extra-time/penalty shoot-outs are not included.<br><br></b>For further information please see <a href=http://content.betfair.com/aboutus/content.asp?sWhichKey=Rules%20and%20Regulations#undefined.do style=color:0163ad; text-decoration: underline; target=_blank>Rules & Regs<br><br>\n"", ""rulesHasDate"": true, ""priceLadderDescription"": {{ ""type"": ""CLASSIC"" }} }}, ""totalMatched"": 3811.2054960000005, ""runners"": [ {{ ""selectionId"": 1222347, ""runnerName"": ""Under 4.5 Goals"", ""handicap"": 0.0, ""sortPriority"": 1, ""metadata"": {{ ""runnerId"": ""1222347"" }}, ""price"": 1.03 }}, {{ ""selectionId"": 1222346, ""runnerName"": ""Over 4.5 Goals"", ""handicap"": 0.0, ""sortPriority"": 2, ""metadata"": {{ ""runnerId"": ""1222346"" }}, ""price"": 34.0 }} ], ""eventType"": {{ ""id"": ""1"", ""name"": ""Soccer"" }}, ""competition"": {{ ""id"": ""89219"", ""name"": ""Brazilian Cup"" }}, ""event"": {{ ""id"": ""30756093"", ""name"": ""CRB v Fortaleza EC"", ""countryCode"": ""BR"", ""timezone"": ""GMT"", ""openDate"": ""2021-08-04T19:30:00Z"" }} }} ]");*/
			//newline
			//"5￻2￻N/A￻N/A￻0￻N/A￻1￻2￻[\r\n  {\r\n    \"marketId\": \"1.186028063\",\r\n    \"marketName\": \"Match Odds\",\r\n    \"marketStartTime\": \"2021-08-07T08:15:00Z\",\r\n    \"description\": {\r\n      \"persistenceEnabled\": true,\r\n      \"bspMarket\": false,\r\n      \"marketTime\": \"2021-08-07T08:15:00Z\",\r\n      \"suspendTime\": \"2021-08-07T08:15:00Z\",\r\n      \"bettingType\": \"ODDS\",\r\n      \"turnInPlayEnabled\": true,\r\n      \"marketType\": \"MATCH_ODDS\",\r\n      \"regulator\": \"MALTA LOTTERIES AND GAMBLING AUTHORITY\",\r\n      \"marketBaseRate\": 6.5,\r\n      \"discountAllowed\": true,\r\n      \"wallet\": \"UK wallet\",\r\n      \"rules\": \"<!--Football - Match Odds --><br>Predict the result of this match.<br> All bets apply to Full Time according to the match officials, plus any stoppage time. Extra-time/penalty shoot-outs are not included.<br><br></b>For further information please see <a href=http://content.betfair.com/aboutus/content.asp?sWhichKey=Rules%20and%20Regulations#undefined.do style=color:0163ad; text-decoration: underline; target=_blank>Rules & Regs<br><br>\\n\",\r\n      \"rulesHasDate\": true,\r\n      \"priceLadderDescription\": {\r\n        \"type\": \"CLASSIC\"\r\n      }\r\n    },\r\n    \"totalMatched\": 0.0,\r\n    \"runners\": [\r\n      {\r\n        \"selectionId\": 25182164,\r\n        \"runnerName\": \"FK Jablonec B\",\r\n        \"handicap\": 0.0,\r\n        \"sortPriority\": 1,\r\n        \"metadata\": {\r\n          \"runnerId\": \"25182164\"\r\n        }\r\n      },\r\n      {\r\n        \"selectionId\": 4145048,\r\n        \"runnerName\": \"Usti nad Orlici\",\r\n        \"handicap\": 0.0,\r\n        \"sortPriority\": 2,\r\n        \"metadata\": {\r\n          \"runnerId\": \"4145048\"\r\n        }\r\n      },\r\n      {\r\n        \"selectionId\": 58805,\r\n        \"runnerName\": \"The Draw\",\r\n        \"handicap\": 0.0,\r\n        \"sortPriority\": 3,\r\n        \"metadata\": {\r\n          \"runnerId\": \"58805\"\r\n        }\r\n      }\r\n    ],\r\n    \"eventType\": {\r\n      \"id\": \"1\",\r\n      \"name\": \"Soccer\"\r\n    },\r\n    \"competition\": {\r\n      \"id\": \"892425\",\r\n      \"name\": \"Czech 3 Liga\"\r\n    },\r\n    \"event\": {\r\n      \"id\": \"30760530\",\r\n      \"name\": \"FK Jablonec B v Usti nad Orlici\",\r\n      \"countryCode\": \"CZ\",\r\n      \"timezone\": \"GMT\",\r\n      \"openDate\": \"2021-08-07T08:15:00Z\"\r\n    }\r\n  },\r\n  {\r\n    \"marketId\": \"1.186028067\",\r\n    \"marketName\": \"Over/Under 2.5 Goals\",\r\n    \"marketStartTime\": \"2021-08-07T08:15:00Z\",\r\n    \"description\": {\r\n      \"persistenceEnabled\": true,\r\n      \"bspMarket\": false,\r\n      \"marketTime\": \"2021-08-07T08:15:00Z\",\r\n      \"suspendTime\": \"2021-08-07T08:15:00Z\",\r\n      \"bettingType\": \"ODDS\",\r\n      \"turnInPlayEnabled\": true,\r\n      \"marketType\": \"OVER_UNDER_25\",\r\n      \"regulator\": \"MALTA LOTTERIES AND GAMBLING AUTHORITY\",\r\n      \"marketBaseRate\": 6.5,\r\n      \"discountAllowed\": true,\r\n      \"wallet\": \"UK wallet\",\r\n      \"rules\": \"<!--Football - Over/Unders --><br>How many goals will be scored in this match.<br> All bets apply to Full Time according to the match officials, plus any stoppage time. Extra-time/penalty shoot-outs are not included.<br><br></b>For further information please see <a href=http://content.betfair.com/aboutus/content.asp?sWhichKey=Rules%20and%20Regulations#undefined.do style=color:0163ad; text-decoration: underline; target=_blank>Rules & Regs<br><br>\\n\",\r\n      \"rulesHasDate\": true,\r\n      \"priceLadderDescription\": {\r\n        \"type\": \"CLASSIC\"\r\n      }\r\n    },\r\n    \"totalMatched\": 0.0,\r\n    \"runners\": [\r\n      {\r\n        \"selectionId\": 47972,\r\n        \"runnerName\": \"Under 2.5 Goals\",\r\n        \"handicap\": 0.0,\r\n        \"sortPriority\": 1,\r\n        \"metadata\": {\r\n          \"runnerId\": \"47972\"\r\n        }\r\n      },\r\n      {\r\n        \"selectionId\": 47973,\r\n        \"runnerName\": \"Over 2.5 Goals\",\r\n        \"handicap\": 0.0,\r\n        \"sortPriority\": 2,\r\n        \"metadata\": {\r\n          \"runnerId\": \"47973\"\r\n        }\r\n      }\r\n    ],\r\n    \"eventType\": {\r\n      \"id\": \"1\",\r\n      \"name\": \"Soccer\"\r\n    },\r\n    \"competition\": {\r\n      \"id\": \"892425\",\r\n      \"name\": \"Czech 3 Liga\"\r\n    },\r\n    \"event\": {\r\n      \"id\": \"30760530\",\r\n      \"name\": \"FK Jablonec B v Usti nad Orlici\",\r\n      \"countryCode\": \"CZ\",\r\n      \"timezone\": \"GMT\",\r\n      \"openDate\": \"2021-08-07T08:15:00Z\"\r\n    }\r\n  }\r\n]"

			//update
			//string aaa = "7￻2￻N/A￻N/A￻0￻N/A￻1￻1￻{\"MarketId\":\"1.186058594\",\"IsClosed\":false,\"Snap\":{\"MarketDefinition\":{\"bettingType\":\"ODDS\",\"status\":\"OPEN\",\"timezone\":\"GMT\",\"regulators\":[\"MR_INT\"],\"marketType\":\"MATCH_ODDS\",\"marketBaseRate\":5.0,\"numberOfWinners\":1,\"countryCode\":\"BD\",\"inPlay\":false,\"betDelay\":0,\"bspMarket\":false,\"numberOfActiveRunners\":3,\"eventId\":\"30762251\",\"crossMatching\":true,\"runnersVoidable\":false,\"turnInPlayEnabled\":true,\"suspendTime\":\"2021-08-05T10:00:00Z\",\"discountAllowed\":true,\"persistenceEnabled\":true,\"runners\":[{\"status\":\"ACTIVE\",\"sortPriority\":1,\"id\":27312088},{\"status\":\"ACTIVE\",\"sortPriority\":2,\"id\":8643477},{\"status\":\"ACTIVE\",\"sortPriority\":3,\"id\":58805}],\"version\":3948988636,\"eventTypeId\":\"1\",\"complete\":true,\"openDate\":\"2021-08-05T10:00:00Z\",\"marketTime\":\"2021-08-05T10:00:00Z\",\"bspReconciled\":false},\"MarketId\":\"1.186058594\",\"MarketRunners\":[{\"RunnerId\":{\"SelectionId\":27312088,\"Handicap\":null},\"Definition\":{\"status\":\"ACTIVE\",\"sortPriority\":1,\"id\":27312088},\"Prices\":{\"AvailableToLay\":[{\"Price\":9.0,\"Size\":5.72},{\"Price\":9.2,\"Size\":10.55}],\"AvailableToBack\":[{\"Price\":4.0,\"Size\":18.83},{\"Price\":2.2,\"Size\":12.5},{\"Price\":1.02,\"Size\":5.0},{\"Price\":1.01,\"Size\":190.0}],\"Traded\":[],\"StartingPriceBack\":[],\"StartingPriceLay\":[],\"BestAvailableToBack\":[{\"Level\":0,\"Price\":4.0,\"Size\":18.83},{\"Level\":1,\"Price\":2.2,\"Size\":12.5},{\"Level\":2,\"Price\":1.02,\"Size\":5.0},{\"Level\":3,\"Price\":1.01,\"Size\":190.0}],\"BestAvailableToLay\":[{\"Level\":0,\"Price\":9.0,\"Size\":5.72},{\"Level\":1,\"Price\":9.2,\"Size\":10.55}],\"BestDisplayAvailableToBack\":[{\"Level\":0,\"Price\":4.0,\"Size\":18.83},{\"Level\":1,\"Price\":2.76,\"Size\":22.71},{\"Level\":2,\"Price\":2.74,\"Size\":46.57},{\"Level\":3,\"Price\":2.2,\"Size\":12.5},{\"Level\":4,\"Price\":1.02,\"Size\":5.0},{\"Level\":5,\"Price\":1.01,\"Size\":190.0},{\"Level\":6,\"Price\":0.0,\"Size\":0.0},{\"Level\":7,\"Price\":0.0,\"Size\":0.0},{\"Level\":8,\"Price\":0.0,\"Size\":0.0},{\"Level\":9,\"Price\":0.0,\"Size\":0.0}],\"BestDisplayAvailableToLay\":[{\"Level\":0,\"Price\":5.6,\"Size\":7.83},{\"Level\":1,\"Price\":9.0,\"Size\":5.72},{\"Level\":2,\"Price\":9.2,\"Size\":10.86},{\"Level\":3,\"Price\":0.0,\"Size\":0.0},{\"Level\":4,\"Price\":0.0,\"Size\":0.0},{\"Level\":5,\"Price\":0.0,\"Size\":0.0},{\"Level\":6,\"Price\":0.0,\"Size\":0.0},{\"Level\":7,\"Price\":0.0,\"Size\":0.0},{\"Level\":8,\"Price\":0.0,\"Size\":0.0},{\"Level\":9,\"Price\":0.0,\"Size\":0.0}],\"LastTradedPrice\":0.0,\"StartingPriceNear\":0.0,\"StartingPriceFar\":0.0,\"TradedVolume\":0.0}},{\"RunnerId\":{\"SelectionId\":8643477,\"Handicap\":null},\"Definition\":{\"status\":\"ACTIVE\",\"sortPriority\":2,\"id\":8643477},\"Prices\":{\"AvailableToLay\":[{\"Price\":2.52,\"Size\":20.01},{\"Price\":2.54,\"Size\":10.55}],\"AvailableToBack\":[{\"Price\":1.87,\"Size\":23.46},{\"Price\":1.34,\"Size\":44.12},{\"Price\":1.02,\"Size\":5.0},{\"Price\":1.01,\"Size\":190.0}],\"Traded\":[],\"StartingPriceBack\":[],\"StartingPriceLay\":[],\"BestAvailableToBack\":[{\"Level\":0,\"Price\":1.87,\"Size\":23.46},{\"Level\":1,\"Price\":1.34,\"Size\":44.12},{\"Level\":2,\"Price\":1.02,\"Size\":5.0},{\"Level\":3,\"Price\":1.01,\"Size\":190.0}],\"BestAvailableToLay\":[{\"Level\":0,\"Price\":2.52,\"Size\":20.01},{\"Level\":1,\"Price\":2.54,\"Size\":10.55}],\"BestDisplayAvailableToBack\":[{\"Level\":0,\"Price\":1.87,\"Size\":23.46},{\"Level\":1,\"Price\":1.46,\"Size\":22.61},{\"Level\":2,\"Price\":1.44,\"Size\":46.68},{\"Level\":3,\"Price\":1.34,\"Size\":44.12},{\"Level\":4,\"Price\":1.02,\"Size\":5.0},{\"Level\":5,\"Price\":1.01,\"Size\":190.0},{\"Level\":6,\"Price\":0.0,\"Size\":0.0},{\"Level\":7,\"Price\":0.0,\"Size\":0.0},{\"Level\":8,\"Price\":0.0,\"Size\":0.0},{\"Level\":9,\"Price\":0.0,\"Size\":0.0}],\"BestDisplayAvailableToLay\":[{\"Level\":0,\"Price\":2.16,\"Size\":20.51},{\"Level\":1,\"Price\":2.5,\"Size\":12.4},{\"Level\":2,\"Price\":2.52,\"Size\":20.01},{\"Level\":3,\"Price\":2.54,\"Size\":10.55},{\"Level\":4,\"Price\":0.0,\"Size\":0.0},{\"Level\":5,\"Price\":0.0,\"Size\":0.0},{\"Level\":6,\"Price\":0.0,\"Size\":0.0},{\"Level\":7,\"Price\":0.0,\"Size\":0.0},{\"Level\":8,\"Price\":0.0,\"Size\":0.0},{\"Level\":9,\"Price\":0.0,\"Size\":0.0}],\"LastTradedPrice\":0.0,\"StartingPriceNear\":0.0,\"StartingPriceFar\":0.0,\"TradedVolume\":0.0}},{\"RunnerId\":{\"SelectionId\":58805,\"Handicap\":null},\"Definition\":{\"status\":\"ACTIVE\",\"sortPriority\":3,\"id\":58805},\"Prices\":{\"AvailableToLay\":[{\"Price\":5.2,\"Size\":5.38},{\"Price\":5.4,\"Size\":10.55},{\"Price\":32.0,\"Size\":5.0},{\"Price\":950.0,\"Size\":2.2}],\"AvailableToBack\":[{\"Price\":3.5,\"Size\":12.66},{\"Price\":2.86,\"Size\":26.88},{\"Price\":2.4,\"Size\":35.71},{\"Price\":2.0,\"Size\":15.0},{\"Price\":1.03,\"Size\":2.0},{\"Price\":1.01,\"Size\":30.0}],\"Traded\":[],\"StartingPriceBack\":[],\"StartingPriceLay\":[],\"BestAvailableToBack\":[{\"Level\":0,\"Price\":3.5,\"Size\":12.66},{\"Level\":1,\"Price\":2.86,\"Size\":26.88},{\"Level\":2,\"Price\":2.4,\"Size\":35.71},{\"Level\":3,\"Price\":2.0,\"Size\":15.0},{\"Level\":4,\"Price\":1.03,\"Size\":2.0},{\"Level\":5,\"Price\":1.01,\"Size\":30.0}],\"BestAvailableToLay\":[{\"Level\":0,\"Price\":5.2,\"Size\":5.38},{\"Level\":1,\"Price\":5.4,\"Size\":10.55},{\"Level\":2,\"Price\":32.0,\"Size\":5.0},{\"Level\":3,\"Price\":950.0,\"Size\":2.2}],\"BestDisplayAvailableToBack\":[{\"Level\":0,\"Price\":3.5,\"Size\":12.66},{\"Level\":1,\"Price\":2.86,\"Size\":26.88},{\"Level\":2,\"Price\":2.4,\"Size\":35.71},{\"Level\":3,\"Price\":2.02,\"Size\":24.96},{\"Level\":4,\"Price\":2.0,\"Size\":15.53},{\"Level\":5,\"Price\":1.03,\"Size\":2.0},{\"Level\":6,\"Price\":1.01,\"Size\":30.0},{\"Level\":7,\"Price\":0.0,\"Size\":0.0},{\"Level\":8,\"Price\":0.0,\"Size\":0.0},{\"Level\":9,\"Price\":0.0,\"Size\":0.0}],\"BestDisplayAvailableToLay\":[{\"Level\":0,\"Price\":4.7,\"Size\":9.33},{\"Level\":1,\"Price\":5.2,\"Size\":5.38},{\"Level\":2,\"Price\":5.4,\"Size\":10.55},{\"Level\":3,\"Price\":32.0,\"Size\":5.0},{\"Level\":4,\"Price\":950.0,\"Size\":2.32},{\"Level\":5,\"Price\":0.0,\"Size\":0.0},{\"Level\":6,\"Price\":0.0,\"Size\":0.0},{\"Level\":7,\"Price\":0.0,\"Size\":0.0},{\"Level\":8,\"Price\":0.0,\"Size\":0.0},{\"Level\":9,\"Price\":0.0,\"Size\":0.0}],\"LastTradedPrice\":0.0,\"StartingPriceNear\":0.0,\"StartingPriceFar\":0.0,\"TradedVolume\":0.0}}],\"TradedVolume\":0.0}}";
			//new BetAPIConsumer($"{Integration.Kafka.Group}", $"{Integration.Kafka.TopicForLinesETL}").OnMessageBeforeCommit(aaa);
			//update
			//"7￻2￻N/A￻N/A￻0￻N/A￻4￻1￻{\"MarketId\":\"1.186080441\",\"IsClosed\":false,\"Snap\":{\"MarketDefinition\":{\"bettingType\":\"ODDS\",\"status\":\"OPEN\",\"timezone\":\"GMT\",\"regulators\":[\"MR_INT\"],\"marketType\":\"MATCH_ODDS\",\"marketBaseRate\":5.0,\"numberOfWinners\":1,\"countryCode\":\"BR\",\"inPlay\":false,\"betDelay\":0,\"bspMarket\":false,\"numberOfActiveRunners\":3,\"eventId\":\"30764884\",\"crossMatching\":true,\"runnersVoidable\":false,\"turnInPlayEnabled\":true,\"suspendTime\":\"2021-08-05T18:00:00Z\",\"discountAllowed\":true,\"persistenceEnabled\":true,\"runners\":[{\"status\":\"ACTIVE\",\"sortPriority\":1,\"id\":9468394},{\"status\":\"ACTIVE\",\"sortPriority\":2,\"id\":39717467},{\"status\":\"ACTIVE\",\"sortPriority\":3,\"id\":58805}],\"version\":3949771018,\"eventTypeId\":\"1\",\"complete\":true,\"openDate\":\"2021-08-05T18:00:00Z\",\"marketTime\":\"2021-08-05T18:00:00Z\",\"bspReconciled\":false},\"MarketId\":\"1.186080441\",\"MarketRunners\":[{\"RunnerId\":{\"SelectionId\":9468394,\"Handicap\":null},\"Definition\":{\"status\":\"ACTIVE\",\"sortPriority\":1,\"id\":9468394},\"Prices\":{\"AvailableToLay\":[{\"Price\":1000.0,\"Size\":0.05}],\"AvailableToBack\":[{\"Price\":9.0,\"Size\":11.26},{\"Price\":1.02,\"Size\":5.0},{\"Price\":1.01,\"Size\":180.0}],\"Traded\":[{\"Price\":9.0,\"Size\":8.06}],\"StartingPriceBack\":[],\"StartingPriceLay\":[],\"BestAvailableToBack\":[{\"Level\":0,\"Price\":9.0,\"Size\":11.26},{\"Level\":1,\"Price\":1.02,\"Size\":5.0},{\"Level\":2,\"Price\":1.01,\"Size\":180.0}],\"BestAvailableToLay\":[{\"Level\":0,\"Price\":1000.0,\"Size\":0.05}],\"BestDisplayAvailableToBack\":[{\"Level\":0,\"Price\":9.0,\"Size\":11.26},{\"Level\":1,\"Price\":3.55,\"Size\":1.96},{\"Level\":2,\"Price\":1.02,\"Size\":5.0},{\"Level\":3,\"Price\":1.01,\"Size\":180.0},{\"Level\":4,\"Price\":0.0,\"Size\":0.0},{\"Level\":5,\"Price\":0.0,\"Size\":0.0},{\"Level\":6,\"Price\":0.0,\"Size\":0.0},{\"Level\":7,\"Price\":0.0,\"Size\":0.0},{\"Level\":8,\"Price\":0.0,\"Size\":0.0},{\"Level\":9,\"Price\":0.0,\"Size\":0.0}],\"BestDisplayAvailableToLay\":[{\"Level\":0,\"Price\":30.0,\"Size\":2.25},{\"Level\":1,\"Price\":1000.0,\"Size\":0.05},{\"Level\":2,\"Price\":0.0,\"Size\":0.0},{\"Level\":3,\"Price\":0.0,\"Size\":0.0},{\"Level\":4,\"Price\":0.0,\"Size\":0.0},{\"Level\":5,\"Price\":0.0,\"Size\":0.0},{\"Level\":6,\"Price\":0.0,\"Size\":0.0},{\"Level\":7,\"Price\":0.0,\"Size\":0.0},{\"Level\":8,\"Price\":0.0,\"Size\":0.0},{\"Level\":9,\"Price\":0.0,\"Size\":0.0}],\"LastTradedPrice\":9.0,\"StartingPriceNear\":0.0,\"StartingPriceFar\":0.0,\"TradedVolume\":8.06}},{\"RunnerId\":{\"SelectionId\":39717467,\"Handicap\":null},\"Definition\":{\"status\":\"ACTIVE\",\"sortPriority\":2,\"id\":39717467},\"Prices\":{\"AvailableToLay\":[{\"Price\":1.39,\"Size\":5.0},{\"Price\":1000.0,\"Size\":0.05}],\"AvailableToBack\":[{\"Price\":1.27,\"Size\":8.4},{\"Price\":1.26,\"Size\":62.01},{\"Price\":1.02,\"Size\":5.0},{\"Price\":1.01,\"Size\":180.0}],\"Traded\":[{\"Price\":1.26,\"Size\":0.04}],\"StartingPriceBack\":[],\"StartingPriceLay\":[],\"BestAvailableToBack\":[{\"Level\":0,\"Price\":1.27,\"Size\":8.4},{\"Level\":1,\"Price\":1.26,\"Size\":62.01},{\"Level\":2,\"Price\":1.02,\"Size\":5.0},{\"Level\":3,\"Price\":1.01,\"Size\":180.0}],\"BestAvailableToLay\":[{\"Level\":0,\"Price\":1.39,\"Size\":5.0},{\"Level\":1,\"Price\":1000.0,\"Size\":0.05}],\"BestDisplayAvailableToBack\":[{\"Level\":0,\"Price\":1.27,\"Size\":8.4},{\"Level\":1,\"Price\":1.26,\"Size\":62.01},{\"Level\":2,\"Price\":1.02,\"Size\":5.0},{\"Level\":3,\"Price\":1.01,\"Size\":180.0},{\"Level\":4,\"Price\":0.0,\"Size\":0.0},{\"Level\":5,\"Price\":0.0,\"Size\":0.0},{\"Level\":6,\"Price\":0.0,\"Size\":0.0},{\"Level\":7,\"Price\":0.0,\"Size\":0.0},{\"Level\":8,\"Price\":0.0,\"Size\":0.0},{\"Level\":9,\"Price\":0.0,\"Size\":0.0}],\"BestDisplayAvailableToLay\":[{\"Level\":0,\"Price\":1.39,\"Size\":5.0},{\"Level\":1,\"Price\":1.4,\"Size\":46.65},{\"Level\":2,\"Price\":1000.0,\"Size\":0.05},{\"Level\":3,\"Price\":0.0,\"Size\":0.0},{\"Level\":4,\"Price\":0.0,\"Size\":0.0},{\"Level\":5,\"Price\":0.0,\"Size\":0.0},{\"Level\":6,\"Price\":0.0,\"Size\":0.0},{\"Level\":7,\"Price\":0.0,\"Size\":0.0},{\"Level\":8,\"Price\":0.0,\"Size\":0.0},{\"Level\":9,\"Price\":0.0,\"Size\":0.0}],\"LastTradedPrice\":1.26,\"StartingPriceNear\":0.0,\"StartingPriceFar\":0.0,\"TradedVolume\":0.04}},{\"RunnerId\":{\"SelectionId\":58805,\"Handicap\":null},\"Definition\":{\"status\":\"ACTIVE\",\"sortPriority\":3,\"id\":58805},\"Prices\":{\"AvailableToLay\":[{\"Price\":950.0,\"Size\":2.2},{\"Price\":1000.0,\"Size\":0.05}],\"AvailableToBack\":[{\"Price\":5.8,\"Size\":11.26},{\"Price\":1.03,\"Size\":2.0},{\"Price\":1.01,\"Size\":20.0}],\"Traded\":[{\"Price\":5.8,\"Size\":3.0}],\"StartingPriceBack\":[],\"StartingPriceLay\":[],\"BestAvailableToBack\":[{\"Level\":0,\"Price\":5.8,\"Size\":11.26},{\"Level\":1,\"Price\":1.03,\"Size\":2.0},{\"Level\":2,\"Price\":1.01,\"Size\":20.0}],\"BestAvailableToLay\":[{\"Level\":0,\"Price\":950.0,\"Size\":2.2},{\"Level\":1,\"Price\":1000.0,\"Size\":0.05}],\"BestDisplayAvailableToBack\":[{\"Level\":0,\"Price\":5.8,\"Size\":11.26},{\"Level\":1,\"Price\":3.55,\"Size\":1.96},{\"Level\":2,\"Price\":1.03,\"Size\":2.0},{\"Level\":3,\"Price\":1.01,\"Size\":20.0},{\"Level\":4,\"Price\":0.0,\"Size\":0.0},{\"Level\":5,\"Price\":0.0,\"Size\":0.0},{\"Level\":6,\"Price\":0.0,\"Size\":0.0},{\"Level\":7,\"Price\":0.0,\"Size\":0.0},{\"Level\":8,\"Price\":0.0,\"Size\":0.0},{\"Level\":9,\"Price\":0.0,\"Size\":0.0}],\"BestDisplayAvailableToLay\":[{\"Level\":0,\"Price\":10.0,\"Size\":1.07},{\"Level\":1,\"Price\":10.5,\"Size\":7.44},{\"Level\":2,\"Price\":950.0,\"Size\":2.2},{\"Level\":3,\"Price\":1000.0,\"Size\":0.05},{\"Level\":4,\"Price\":0.0,\"Size\":0.0},{\"Level\":5,\"Price\":0.0,\"Size\":0.0},{\"Level\":6,\"Price\":0.0,\"Size\":0.0},{\"Level\":7,\"Price\":0.0,\"Size\":0.0},{\"Level\":8,\"Price\":0.0,\"Size\":0.0},{\"Level\":9,\"Price\":0.0,\"Size\":0.0}],\"LastTradedPrice\":5.8,\"StartingPriceNear\":0.0,\"StartingPriceFar\":0.0,\"TradedVolume\":3.0}}],\"TradedVolume\":11.1}}"

		}

		bool ValidateMessageOwnership(string storeAlias)
		{
			var result = LinesAPI.Lines.PerformQry(@"
                    {{
					    print company.Sales.CurrentStore.Alias storeAlias;
                    }}"
				);
			if (!(result is OkObjectResult))
			{
				throw new Exception($@"Error:{((ObjectResult)result).Value}");
			}
			var o = (OkObjectResult)result;
			var json = o.Value.ToString();
			var storeData = JsonConvert.DeserializeObject<StoreData>(json);
			return storeData.storeAlias == storeAlias;
		}
		RestAPIActorAsync GetActor()
		{
			return LinesAPI.Lines;
		}

		struct StoreData
		{
			public string storeAlias { get; set; }
		}

		private class CustomersConsumer : GamesEngine.Settings.Consumer
		{
			public CustomersConsumer(string group, string topic) : base(group, topic)
			{
			}


			public override void OnMessageBeforeCommit(string msg)
			{
				if (String.IsNullOrEmpty(msg)) throw new Exception(nameof(msg));

			}
		}

		private class BetAPIConsumer : GamesEngine.Settings.Consumer
		{
			public BetAPIConsumer(string group, string topic) : base(group, topic)
			{
				ScriptGeneratorPerLineType.ResgisterProcessForEachLineType();
			}
			private void AddLineDocument(List<object> documentsForELK, Provider provider, 
				string documentId, string idInTheProvider, string marketType, string sportName, 
				string sportId, string leagueName, string leagueId, string eventId, string eventName, string eventCountryCode, string eventTimezone,
				string eventOpenDate)
			{
				documentsForELK.Add(
					new
					{
						index =
						new
						{
							_index = "lines",
							_type = "line",
							_id = documentId
						}
					});
				documentsForELK.Add(
					new
					{
						id = documentId,
						idInTheProvider = idInTheProvider,
						lineType = marketType,
						sportName = sportName,
						sportId = sportId,
						leagueName = leagueName,
						leagueId = leagueId,
						eventId = eventId,
						eventName = eventName,
						eventCountryCode = eventCountryCode,
						eventTimezone = eventTimezone,
						eventOpenDate = eventOpenDate,
						providerId = documentId
					});
			}
			private void AddUnSupportedLineDocument(List<object> documentsForELK, Provider provider,
				string documentId, string idInTheProvider, string marketType, string sportName,
				string sportId, string leagueName, string leagueId, string eventId, string eventName, string eventCountryCode, string eventTimezone,
				string eventOpenDate, string reason)
			{
				documentsForELK.Add(
					new
					{
						index =
						new
						{
							_index = "unsupported_lines",
							_type = "line",
							_id = documentId
						}
					});
				documentsForELK.Add(
					new
					{
						id = documentId,
						idInTheProvider = idInTheProvider,
						lineType = marketType,
						sportName = sportName,
						sportId = sportId,
						leagueName = leagueName,
						leagueId = leagueId,
						eventId = eventId,
						eventName = eventName,
						eventCountryCode = eventCountryCode,
						eventTimezone = eventTimezone,
						eventOpenDate = eventOpenDate,
						providerId = documentId,
						reason= reason
					});
			}
			private void AddLeagueDocument(List<object> documentsForELK, Provider provider, string documentId, string idInTheProvider, string name, int sportId, string sportName, string region, string createdOn)
			{
				documentsForELK.Add(
					new
					{
						index =
						new
						{
							_index = "leagues",
							_type = "league",
							_id = provider.Id + documentId
						}
					});
				documentsForELK.Add(
					new
					{
						id = provider.Id + documentId,
						idInTheProvider = idInTheProvider,
						name = name,
						providerId = provider.Id,
						providerName = provider.Name,
						sportId = sportId,
						sportName = sportName,
						region = region,
						createdOn = createdOn
					});
			}

			private void AddTournamentDocument(List<object> documentsForELK, Provider provider, string documentId, string idInTheProvider, string name, string leagueIdInTheProvider)
			{
				documentsForELK.Add(
					new
					{
						index =
						new
						{
							_index = "tournaments",
							_type = "tournament",
							_id = provider.Id + documentId,
						}
					});
				documentsForELK.Add(
					new
					{
						id = provider.Id + documentId,
						idInTheProvider = idInTheProvider,
						name = name,
						providerId = provider.Id,
						providerName = provider.Name,
						leagueIdInTheProvider = leagueIdInTheProvider
					});
			}
			
			private void WhenNewBet365League(List<object> documentsForELK, NormalizedProviderResponse leaguesUpdateFromKafka)
			{
				dynamic leaguesUpdateFromTheAPI = leaguesUpdateFromKafka.EvalPayload();
				foreach (dynamic league in leaguesUpdateFromTheAPI.results)
				{
					AddLeagueDocument(
						documentsForELK,
						leaguesUpdateFromKafka.Provider,
						leaguesUpdateFromKafka.Details.SportId + league.id.ToString(),
						league.id.ToString(),
						league.name.ToString(),
						leaguesUpdateFromKafka.Details.SportId,
						leaguesUpdateFromKafka.Details.SportName,
						"None",
						DateTime.Now.ToString()
					);
				}
			}

			private void WhenNewBetfairLeague(List<object> documentsForELK, NormalizedProviderResponse leaguesUpdateFromKafka)
			{
				dynamic leaguesUpdateFromTheAPI = leaguesUpdateFromKafka.EvalPayloadAsArray();
				var documentIds = new List<string>();
				foreach (dynamic league in leaguesUpdateFromTheAPI)
				{
					string competitionId = league.competition.id;
					string competitionName = league.competition.name;
					string competitionRegion = league.competitionRegion;
					string documentId = $"{leaguesUpdateFromKafka.Details.SportId}{competitionId}";
					AddLeagueDocument(
						documentsForELK,
						leaguesUpdateFromKafka.Provider,
						documentId,
						competitionId,
						competitionName,
						leaguesUpdateFromKafka.Details.SportId,
						leaguesUpdateFromKafka.Details.SportName,
						competitionRegion,
						DateTime.Now.ToString()
					);

					AddTournamentDocument(
						documentsForELK,
						leaguesUpdateFromKafka.Provider,
						documentId,
						competitionId,
						competitionName,
						leaguesUpdateFromKafka.Provider.Id + leaguesUpdateFromKafka.Details.SportId + competitionId);

					documentIds.Add($"\"{documentId}\"");
				}

				var obsoleteDocumentIds = ObsoleteLeagues(documentIds);
				DeleteLeagues(obsoleteDocumentIds);
				DeleteTournaments(obsoleteDocumentIds);
				DeleteMatches(obsoleteDocumentIds);
				DeleteTeams(obsoleteDocumentIds);
			}

			IEnumerable<string> ObsoleteLeagues(IEnumerable<string> documentIds)
			{
				var documentIdsAsText = string.Join(',', documentIds);
				string query = $@"
				{{
					""query"": {{
					""bool"": {{
						""must_not"": [
						{{
							""terms"": {{
							""id"": [{documentIdsAsText}]
							}}
						}}
						]
					}}
					}}
				}}
				";

				HttpELKClient client = HttpELKClient.GetInstance();
				JArray result = null;
				var response = client.Search("leagues", query);
				var reader = new JsonTextReader(new StringReader(response.Body));
				reader.FloatParseHandling = FloatParseHandling.Decimal;

				JObject obj = null;
				bool success = response.Success;
				if (success)
				{
					obj = JObject.Load(reader);
					result = (JArray)obj["hits"]["hits"];
				}
				
				var obsoleteDocumentIds = new List<string>();
				if (result != null)
                {
					foreach (var json in result)
					{
						var id = json["_source"]["id"].ToString();
						obsoleteDocumentIds.Add($"\"{id}\"");
					}
				}
                
				return obsoleteDocumentIds;
			}

			void DeleteLeagues(IEnumerable<string> documentIds)
			{
				var documentIdsAsText = string.Join(',', documentIds);
				string query = $@"
				{{
					""query"": {{
					""bool"": {{
						""must"": [
						{{
							""terms"": {{
							""id"": [{documentIdsAsText}]
							}}
						}}
						]
					}}
					}}
				}}
				";

				HttpELKClient client = HttpELKClient.GetInstance();
				client.DeleteDocumentByQuery("leagues", query);
			}

			void DeleteTournaments(IEnumerable<string> documentIds)
			{
				var documentIdsAsText = string.Join(',', documentIds);
				string query = $@"
				{{
					""query"": {{
					""bool"": {{
						""must"": [
						{{
							""terms"": {{
							""leagueIdInTheProvider"": [{documentIdsAsText}]
							}}
						}}
						]
					}}
					}}
				}}
				";

				HttpELKClient client = HttpELKClient.GetInstance();
				client.DeleteDocumentByQuery("tournaments", query);
			}

			void DeleteMatches(IEnumerable<string> documentIds)
			{
				var documentIdsAsText = string.Join(',', documentIds);
				string query = $@"
				{{
					""query"": {{
					""bool"": {{
						""must"": [
						{{
							""terms"": {{
							""leagueIdInTheProvider"": [{documentIdsAsText}]
							}}
						}}
						]
					}}
					}}
				}}
				";

				HttpELKClient client = HttpELKClient.GetInstance();
				client.DeleteDocumentByQuery("matches", query);
			}

			void DeleteTeams(IEnumerable<string> documentIds)
			{
				var documentIdsAsText = string.Join(',', documentIds);
				string query = $@"
				{{
					""query"": {{
					""bool"": {{
						""must"": [
						{{
							""terms"": {{
							""leagueIdInTheProvider"": [{documentIdsAsText}]
							}}
						}}
						]
					}}
					}}
				}}
				";

				HttpELKClient client = HttpELKClient.GetInstance();
				client.DeleteDocumentByQuery("teams", query);
			}

			private void WhenNewBet365Season(List<object> documentsForELK, NormalizedProviderResponse seasonUpdateFromKafka)
			{
				dynamic seasonUpdateFromTheAPI = seasonUpdateFromKafka.EvalPayload();

				AddTournamentDocument(
						documentsForELK,
						seasonUpdateFromKafka.Provider,
						seasonUpdateFromKafka.Provider.Id + seasonUpdateFromKafka.Details.LeagueId,
						seasonUpdateFromKafka.Details.LeagueId,
						seasonUpdateFromTheAPI.results.season.name.ToString(),
						seasonUpdateFromKafka.Provider.Id + seasonUpdateFromTheAPI.results.season.sport_id.ToString() + seasonUpdateFromKafka.Details.LeagueId);
			}

			private void WhenNewBet365Team(List<object> documentsForELK, NormalizedProviderResponse teamsUpdateFromKafka)
			{
				dynamic teamsUpdateFromTheAPI = teamsUpdateFromKafka.EvalPayload();
				foreach (dynamic team in teamsUpdateFromTheAPI.results)
				{
					documentsForELK.Add(
						new
						{
							index =
							new
							{
								_index = "teams",
								_type = "team",
								_id = teamsUpdateFromKafka.Provider.Id + team.id.ToString()
							}
						});
					documentsForELK.Add(
						new
						{
							Id = teamsUpdateFromKafka.Provider.Id + team.id.ToString(),
							IdInTheProvider = team.id.ToString(),
							Name = team.name.ToString(),
							ProviderId = teamsUpdateFromKafka.Provider.Id
						});
				}
			}
			private void WhenNewBetfairTeam(List<object> documentsForELK, NormalizedProviderResponse teamsUpdateFromKafka)
			{
				dynamic teamsUpdateFromTheAPI = teamsUpdateFromKafka.EvalPayloadAsArray();
				string leagueId = teamsUpdateFromKafka.Details.LeagueId;
				var providerId = teamsUpdateFromKafka.Provider.Id;
				var providerName = teamsUpdateFromKafka.Provider.Name;
				var sportId = teamsUpdateFromKafka.Details.SportId;
				const string TOKEN_FOR_SOCCER = " v ";
				const string TOKEN_FOR_BASKETBALL = " @ ";
				foreach (dynamic team in teamsUpdateFromTheAPI)
				{
					dynamic eventItem = ((Newtonsoft.Json.Linq.JObject)team)["event"];
					string eventId = eventItem.id.ToString();
					string eventName = eventItem.name;
					string token = string.Empty;
					GamesEngine.Games.Sport sport = new Sports().FindById(sportId);
					if (GamesEngine.Games.Sport.SOCCER.Id == sportId)
                    {
						token = TOKEN_FOR_SOCCER;
					}
					else if (GamesEngine.Games.Sport.BASKETBALL.Id == sportId || GamesEngine.Games.Sport.BASEBALL.Id == sportId)
					{
						token = TOKEN_FOR_BASKETBALL;
					}
					else throw new GameEngineException($"Token is not implemented for sport id '{sportId}'.");

					string[] teamNames = NormalizedProviderResponse.SplitEventName(token, eventName);
					if (teamNames.Length == 2)
					{
						string teamAName = teamNames[0].Trim();
						string teamANameAsId = teamAName.Replace(" ", "").ToLower();
						string teamBName = teamNames[1].Trim();
						string teamBNameAsId = teamBName.Replace(" ", "").ToLower();
						var teamAId = $"{providerId}{leagueId}{teamANameAsId}";
						var teamBId = $"{providerId}{leagueId}{teamBNameAsId}";

						documentsForELK.Add(
							new
							{
								index =
								new
								{
									_index = "teams",
									_type = "team",
									_id = teamAId
								}
							});
						documentsForELK.Add(
							new
							{
								id = teamAId,
								idInTheProvider = "",
								name = teamAName,
								leagueIdInTheProvider = leagueId,
								providerId = providerId
							});
						documentsForELK.Add(
						new
						{
							index =
							new
							{
								_index = "teams",
								_type = "team",
								_id = teamBId
							}
						});
						documentsForELK.Add(
							new
							{
								id = teamBId,
								idInTheProvider = "",
								name = teamBName,
								leagueIdInTheProvider = leagueId,
								providerId = providerId
							});

						documentsForELK.Add(
						new
						{
							index =
							new
							{
								_index = "matches",
								_type = "match",
								_id = teamsUpdateFromKafka.Provider.Id + leagueId + eventId
							}
						});

						DateTime openDate = DateTime.ParseExact(eventItem.openDate.ToString(), "M/d/yyyy h:m:ss tt", Integration.CultureInfoEnUS);
						DateTimeOffset openDateInEngland = new DateTimeOffset(openDate, new TimeSpan(1, 0, 0));
						documentsForELK.Add(
							new
							{
								id = teamsUpdateFromKafka.Provider.Id + leagueId + eventId,
								idInTheProvider = eventId,
								name = eventName,
								leagueIdInTheProvider = leagueId,
								providerId = providerId,
								providerName = providerName,
								teamAName = teamAName,
								teamAId = teamAId,
								teamBName = teamBName,
								teamBId = teamBId,
								countryCode = eventItem.countryCode.ToString(),
								timezone = eventItem.timezone.ToString(),
								openDate = openDateInEngland.ToLocalTime().ToString(),
								sportId = sportId
							});
					}
					else throw new GameEngineException($"Only two teams are expected.");
				}
			}

			private void WhenNewSport(List<object> documentsForELK, SportResponseFromKafka betsAPITeamResponseFromKafka)
			{
				if (betsAPITeamResponseFromKafka == null) throw new ArgumentNullException(nameof(betsAPITeamResponseFromKafka));
				if (betsAPITeamResponseFromKafka.Details == null) throw new ArgumentNullException(nameof(betsAPITeamResponseFromKafka.Details));

				Provider provider = betsAPITeamResponseFromKafka.Provider;
				int sportId = betsAPITeamResponseFromKafka.Details.SportId;
				//Get Alias in memory
				var result = LinesAPI.Lines.PerformQry($@"
					{{
						sport = tournaments.Sports.FindById({sportId});
						hasAliasForProvider = sport.Aliases.HasForProvider({provider.Id});
						print sport.Id sportId;
						print sport.Name sportName;
						print hasAliasForProvider hasAliasForProvider;
							
						if(hasAliasForProvider)
						{{
							for(aliases : sport.Aliases.ForProvider({provider.Id}))
							{{
								print aliases.Name aliasName;
								print aliases.Id aliasId;
							}}
						}}
					}}
				");

				if (!(result is OkObjectResult))
				{
					throw new Exception($@"Error:{((ObjectResult)result).Value.ToString()}");
				}
				OkObjectResult o = (OkObjectResult)result;
				string json = o.Value.ToString();
				var aliasResponse = JsonConvert.DeserializeObject<SportResponse>(json);
				if (!aliasResponse.HasAliasForProvider) throw new Exception($"There are no aliases for sport id '{sportId}' and provider id '{provider.Id}'");
				if (aliasResponse.Aliases.Count > 1) throw new Exception($"Only one alias is expected for sport id '{sportId}' and provider id '{provider.Id}'");

				var aliasId = aliasResponse.Aliases[0].AliasId;
				var aliasName = aliasResponse.Aliases[0].AliasName;
				dynamic joResult = betsAPITeamResponseFromKafka.EvalPayload();
				var wasThereAnyEnabledDomainForSport = joResult.wasThereAnyEnabledDomainForSport.ToString().Equals("true", StringComparison.OrdinalIgnoreCase);
				var anyEnabledDomainForSport = joResult.anyEnabledDomainForSport.ToString().Equals("true", StringComparison.OrdinalIgnoreCase);
				aliasResponse.WasThereAnyEnabledDomainForSport = wasThereAnyEnabledDomainForSport;
				aliasResponse.AnyEnabledDomainForSport = anyEnabledDomainForSport;

				if (wasThereAnyEnabledDomainForSport && !anyEnabledDomainForSport)
				{
					DeleteSport($"{provider.Id}{aliasId}");
				}
				else if (! wasThereAnyEnabledDomainForSport && anyEnabledDomainForSport)
				{
					documentsForELK.Add(
					new
					{
						index =
						new
						{
							_index = "sports",
							_type = "sport",
							_id = provider.Id + aliasId
						}
					});
					documentsForELK.Add(
						new
						{
							id = provider.Id + aliasId,
							idInTheProvider = aliasId,
							idInTheActor = sportId,
							name = aliasName,
							providerId = provider.Id
						});
				}
				else
                {
					throw new Exception($"There was no change in domain activation for sport id '{sportId}'");
				}

				var sport = new Sports().FindById(sportId);
				json = JsonConvert.SerializeObject(aliasResponse);
				var response = new SportResponseFromKafka(sport, provider, json, new NormalizedProviderResponse.Attachments() { SportId = sportId });
				Integration.Kafka.Send(true, $"{Integration.Kafka.TopicForLinesETL}Inverted", NormalizedProviderResponse.Serialize(response));
			}

			void DeleteSport(string documentId)
			{
				if (string.IsNullOrWhiteSpace(documentId)) throw new ArgumentNullException(nameof(documentId));

				string query = $@"
				{{
					""query"": {{
					""bool"": {{
						""must"": [
						{{
							""match"": {{
							""id"": ""{documentId}""
							}}
						}}
						]
					}}
					}}
				}}
				";

				HttpELKClient client = HttpELKClient.GetInstance();
				client.DeleteDocumentByQuery("sports", query);
			}

			private void WhenBetfairLineInsertUpdate(List<object> documentsForELK, int tournamentId, int gameId, LineMessageBody lineBody)
			{
				//TODO cris kenneth si es close deberia cerrarse
				//TODO cris kenneth si estaba closed y ahora es active con precio, se abre.
				//TODO cris kenneth 1ro update y luego insert

				var output = LinesAPI.Lines.PerformQry($@"
					{{
						tournament = company.Tournaments.FindById({tournamentId});
						game = tournament.GetGameNumber({gameId});
						betBoard = company.Betboard(tournament);
						exists = betBoard.ExistLineByAlias({ProvidersCollection.BetFair.Id}, '{lineBody.IdInTheProvider}');
						print exists lineExists;
						if(exists)
						{{
							line = betBoard.SearchLineByAlias({ProvidersCollection.BetFair.Id}, '{lineBody.IdInTheProvider}');
							print line.IsDraft isDraft;
							print line.IsSuspended isSuspended;
							print line.IsCanceled isCanceled;
							print line.IsPublished isPublished;
						}}
					}}
					");

				if (!(output is OkObjectResult))
				{
					AddUnSupportedLineDocument(
						documentsForELK,
						lineBody.Provider,
						lineBody.DocumentId,
						lineBody.IdInTheProvider,
						lineBody.MarketType,
						lineBody.SportName,
						lineBody.SportId,
						lineBody.LeagueName,
						lineBody.LeagueId,
						lineBody.EventId,
						lineBody.EventName,
						lineBody.EventCountryCode,
						lineBody.EventTimezone,
						lineBody.EventOpenDate,
						$@"Update Error:{((ContentResult)output).Content}");

				}

				OkObjectResult o = (OkObjectResult)output;
				string json = o.Value.ToString();
				LineValidations context = JsonConvert.DeserializeObject<LineValidations>(json);

				string error = "";
				bool success = false;
				IActionResult result = null;
				if (context.LineExists)
				{
					success = ScriptGeneratorPerLineType.ActualizationProcess(lineBody, out error);
					string changeStatusScript = "";
					if (! context.IsSuspended && ( lineBody.Status == "SUSPENDED" || lineBody.Status == "INACTIVE"))
					{
						changeStatusScript = $@"
							if (! newLine.IsDraft)
							{{
								newLine.Suspend(itIsThePresent, Now);
								newLine.AddAnnotation('Line was suspended', 'N/A', Now);
							}}
							";
					}
					else if (! context.IsCanceled && lineBody.Status == "CLOSED")
					{
						changeStatusScript = $@"
							newLine.Cancel(itIsThePresent, Now);
							newLine.AddAnnotation('Line was cancelled', 'N/A', Now);";
					}
					else if (context.IsDraft && lineBody.Status == "OPEN" && lineBody.HasRewards)
					{
						changeStatusScript = @$"if (!newLine.IsAllowedToPublishOn(game.CurrentPeriod)) 
												{{
													newLine.AllowToPublishOn(game.CurrentPeriod);
												}}
												newLine.Publish(itIsThePresent, Now);";
					}
					else if (context.IsSuspended && lineBody.Status == "OPEN")
					{
						changeStatusScript = $@"
							newLine.Resume(itIsThePresent, Now);
							newLine.AddAnnotation('Line was cancelled', 'N/A', Now);";
					}

					result = LinesAPI.Lines.PerformCmd($@"
					{{
						tournament = company.Tournaments.FindById({tournamentId});
						game = tournament.GetGameNumber({gameId});
						betBoard = company.Betboard(tournament);
						line = betBoard.SearchLineByAlias({ProvidersCollection.BetFair.Id}, '{lineBody.IdInTheProvider}');
						{lineBody.ScriptSettingParameters}
						{changeStatusScript}
					}}
					");
				}
				else
				{
					success = ScriptGeneratorPerLineType.CreationProcess(lineBody, out error);
					var order = 0;
					result = LinesAPI.Lines.PerformCmd($@"
						{{
							tournament = tournaments.FindById({tournamentId});
							betBoard = company.Betboard(tournament);
							game = tournament.GetGameNumber({gameId});
							catalog = tournament.LinesCatalog;
							question = catalog.SearchQuestionByAliasId({lineBody.Provider.Id},'{lineBody.AliasId}');
							Eval('lineId = ' + betBoard.NextLineId() + ';');
							scheduledDate = game.ScheduledDate.Fecha();
							matchDay = betBoard.Matchday(scheduledDate);
							shelve = matchDay.GetShowcase(game).InsertShelve({order});
							{lineBody.ScriptSettingParameters}
							line = shelve.CreateLine(lineId, question, 'Admin', Now);
							line.Aliases.Add(ForeingAlias({lineBody.Provider.Id}, '{lineBody.IdInTheProvider}', 'Market {lineBody.MarketType}'));
							{lineBody.ScriptForPublishing}
						}}
						");
				}

				if (!(result is OkObjectResult)) error = $@"Update Error:{((ContentResult)result).Content}";
				if (!success || !string.IsNullOrEmpty(error))
				{
					AddUnSupportedLineDocument(
						documentsForELK,
						lineBody.Provider,
						lineBody.DocumentId,
						lineBody.IdInTheProvider,
						lineBody.MarketType,
						lineBody.SportName,
						lineBody.SportId,
						lineBody.LeagueName,
						lineBody.LeagueId,
						lineBody.EventId,
						lineBody.EventName,
						lineBody.EventCountryCode,
						lineBody.EventTimezone,
						lineBody.EventOpenDate,
						error);

					return;
				}
			}
			private void WhenNewBetfairLineUpdate(List<object> documentsForELK, NormalizedProviderResponse linesUpdateFromKafka)
			{
				//TODO cris kenneth 1ro update y luego insert
				//TODO cris kenneth segun los estados de betafait se puede crear una l[inea pero no publicarla o ponerla en pause

				LineMessageBody lineBody = LineMessageBody.CreateBaseOn(linesUpdateFromKafka);

				WhenBetfairLineInsertUpdate(documentsForELK, linesUpdateFromKafka.Details.TournamentId, linesUpdateFromKafka.Details.GameId, lineBody);
			}
			private void WhenNewBetfairLine(List<object> documentsForELK, NormalizedProviderResponse linesUpdateFromKafka)
			{
				//TODO cris kenneth si es close deberia cerrarse
				//TODO cris kenneth 1ro update y luego insert

				dynamic linesUpdateFromTheAPI = linesUpdateFromKafka.EvalPayloadAsArray();
				List<LineMessageBody> lineBodyies = new List<LineMessageBody>();
				foreach (dynamic line in linesUpdateFromTheAPI)
				{
					LineMessageBody lineBody = LineMessageBody.CreateBaseOn(linesUpdateFromKafka, line);

					WhenBetfairLineInsertUpdate(documentsForELK, linesUpdateFromKafka.Details.TournamentId, linesUpdateFromKafka.Details.GameId, lineBody);

				}
			}


			public class LineMessageBody
			{
				public Provider Provider { get; private set; }
				public string IdInTheProvider { get; private set; }
				public string DocumentId { get; private set; }
				public string MarketType { get; private set; }
				public string AliasId { get; internal set; }
				public decimal Spread { get; internal set; }
				public string ScriptSettingParameters { get; internal set; }
				public int AmountRunners { get; }
				public List<LineMessageBodyOptions> Options { get; }
				public string SportName { get; }
				public string SportId { get; }
				public string LeagueName { get; }
				public string LeagueId { get; }
				public string EventId { get; }
				public string EventName { get; }
				public string EventCountryCode { get; }
				public string EventTimezone { get; }
				public string EventOpenDate { get; }
				public string ScriptForPublishing { get; internal set; }
				public bool HasRewards { get; internal set; }
				public string Status { get; internal set; }
				public LineMessageBody(Provider provider, string idInTheProvider, string documentId, string marketType, List<LineMessageBodyOptions> options, string status)
				{
					Provider = provider;
					IdInTheProvider = idInTheProvider;
					DocumentId = documentId;
					MarketType = marketType;
					AliasId = marketType;
					if (marketType == "MATCH_ODDS") AliasId = $"MATCH_ODDS{options.Count()}";
					AmountRunners = options.Count();
					Options = options;
					Status = status;
				}
				public LineMessageBody(Provider provider, string idInTheProvider, string documentId, string marketType, string sportName, string sportId, string leagueName, string leagueId, string eventId, string eventName, string eventCountryCode, string eventTimezone, string eventOpenDate, List<LineMessageBodyOptions> options, string scriptSettingParameters, string scriptForPublishing, string status) :
				this(provider, idInTheProvider, documentId, marketType, options, status)
				{
					SportName = sportName;
					SportId = sportId;
					LeagueName = leagueName;
					LeagueId = leagueId;
					EventId = eventId;
					EventName = eventName;
					EventCountryCode = eventCountryCode;
					EventTimezone = eventTimezone;
					EventOpenDate = eventOpenDate;
					ScriptSettingParameters = scriptSettingParameters;
					ScriptForPublishing = scriptForPublishing;
				}

				internal static LineMessageBody CreateBaseOn(NormalizedProviderResponse lineUpdate)
				{
					Newtonsoft.Json.Linq.JObject obj = JObject.Parse(lineUpdate.Payload);
					JObject market = (JObject)obj["Market"];
					string status = obj["Status"].ToString();
					string marketType = market["Snap"]["MarketDefinition"]["marketType"].ToString();
					string marketId = market["MarketId"].ToString();
					string idInTheProvider = marketId;
					string documentId = "U" + lineUpdate.Provider.Id + idInTheProvider;

					Provider provider = lineUpdate.Provider;

					return new LineMessageBody(provider, idInTheProvider, documentId, marketType, LineMessageBody.LineMessageBodyOptions.Generate(market), status);
				}

				internal static LineMessageBody CreateBaseOn(NormalizedProviderResponse linesUpdateFromKafka, dynamic line)
				{
					string marketType = line.description.marketType;

					Provider provider = linesUpdateFromKafka.Provider;
					string idInTheProvider = line.marketId;
					string documentId = "C" + linesUpdateFromKafka.Provider.Id + idInTheProvider;
					string sportName = line.eventType.name;
					string sportId = line.eventType.id;
					string leagueName = line.competition.name;
					string leagueId = line.competition.id;
					var lineObj = ((Newtonsoft.Json.Linq.JObject)line);
					dynamic eventItem = lineObj["event"];
					string eventId = eventItem.id;
					string eventName = eventItem.name;
					string eventCountryCode = eventItem.countryCode;
					string eventTimezone = eventItem.timezone;
					string eventOpenDate = eventItem.openDate;
					JToken runners = lineObj["runners"];
					string scriptSettingParameters = string.Empty;
					string scriptForPublishing = string.Empty;
					string status = line.status;

					return new LineMessageBody(
						provider,
						idInTheProvider,
						documentId,
						marketType,
						sportName,
						sportId,
						leagueName,
						leagueId,
						eventId,
						eventName,
						eventCountryCode,
						eventTimezone,
						eventOpenDate,
						LineMessageBodyOptions.Generate(runners),
						scriptSettingParameters,
						scriptForPublishing,
						status
					);
				}

				public class LineMessageBodyOptions
				{
					public LineMessageBodyOptions(string id, decimal price)
					{
						Id = id;
						Price = price;
					}

					public string Id { get; }
					public decimal Price { get; }
					public decimal Handicap { get; private set; }
					public string RunnerName { get; private set; }

					public static List<LineMessageBodyOptions> Generate(Newtonsoft.Json.Linq.JObject market)
					{
						List<LineMessageBodyOptions> result = new List<LineMessageBodyOptions>();
						foreach (JObject runner in market["Snap"]["MarketRunners"])
						{
							string handicapValue = runner["RunnerId"]["Handicap"].ToString();
							if (string.IsNullOrEmpty(handicapValue)) handicapValue = "0.0";
							string selectionId = runner["RunnerId"]["SelectionId"].ToString();

							decimal handicap = Convert.ToDecimal(handicapValue);

							string id = selectionId + handicap;
							decimal price = 0m;
							if (runner["Prices"] != null && runner["Prices"]["BestAvailableToBack"] != null && runner["Prices"]["BestAvailableToBack"].Any() && runner["Prices"]["BestAvailableToBack"].Last() != null)
							{
								price = (decimal)runner["Prices"]["BestAvailableToBack"].Last()["Price"];
							}

							LineMessageBodyOptions item = new LineMessageBodyOptions(id, price);
							item.Handicap = handicap;
							result.Add(item);
						}

						return result;
					}

					public static List<LineMessageBodyOptions> Generate(JToken runners)
					{
						List<LineMessageBodyOptions> result = new List<LineMessageBodyOptions>();
						foreach (JToken runner in runners)
						{
							decimal handicap = runner["handicap"] == null ? 0m : Convert.ToDecimal(runner["handicap"]);
							string selectionId = runner["selectionId"].ToString();

							string id = selectionId + handicap;
							decimal price = runner["price"] == null ? 0m : (decimal)runner["price"];
							string runnerName = runner["runnerName"].ToString();
							LineMessageBodyOptions item = new LineMessageBodyOptions(id, price);
							item.Handicap = handicap;
							item.RunnerName = runnerName;
							result.Add(item);
						}

						return result;
					}
				}
			}

			internal class ScriptGeneratorPerLineType
			{
				class ActionsPerLineType
				{
					class Actions
					{
						public Action<LineMessageBody> Setup { get; internal set; }
						public Action<LineMessageBody> CalculateLinePublishingcript { get; internal set; }
						public ScriptGeneratorPerLineType CalculateLineCreationScript { get; internal set; }
					}

					private Dictionary<string, Actions> behavioursPerLineType = new Dictionary<string, Actions>();

					internal void Add(string key, 
						ScriptGeneratorPerLineType lineBehaviourFunctions,
						Action<LineMessageBody> calculatePublishingScriptFunction,
						Action<LineMessageBody> customization)
					{
						if (string.IsNullOrEmpty(key)) throw new GameEngineException(nameof(key));
						if (lineBehaviourFunctions==null) throw new GameEngineException(nameof(lineBehaviourFunctions));
						if (customization == null) throw new GameEngineException(nameof(customization));
						if (calculatePublishingScriptFunction == null) throw new GameEngineException(nameof(calculatePublishingScriptFunction));
						if (behavioursPerLineType.ContainsKey(key)) throw new GameEngineException($"{key} it already registered.");

						Actions actions = new Actions()
						{
							Setup = customization,
							CalculateLineCreationScript = lineBehaviourFunctions,
							CalculateLinePublishingcript = calculatePublishingScriptFunction
						};


						behavioursPerLineType.Add(key, actions);
					}

					internal bool CreationProcess(string key, LineMessageBody lineCreationBody, out string error)
					{
						error = "";
						Actions actions;

						if (behavioursPerLineType.TryGetValue(key, out actions))
						{
							actions.Setup(lineCreationBody);
							error = actions.CalculateLineCreationScript.WhenCreationWith(lineCreationBody);
							if (!string.IsNullOrEmpty(error)) return false;
							actions.CalculateLinePublishingcript(lineCreationBody);

							return true;
						}

						return false;
					}

					internal bool ActualizationProcess(string key, LineMessageBody lineCreationBody, out string error)
					{
						error = "";
						Actions actions;

						if (behavioursPerLineType.TryGetValue(key, out actions))
						{
							actions.Setup(lineCreationBody);
							error = actions.CalculateLineCreationScript.WhenUpdateWith(lineCreationBody);
							if (!string.IsNullOrEmpty(error)) return false;

							return true;
						}

						return false;
					}
				}

				private static ActionsPerLineType actionsPerLineType = null;


				internal ScriptGeneratorPerLineType(Func<LineMessageBody, string> whenCreate, Func<LineMessageBody, string> whenUpdate)
				{
					WhenCreationWith = whenCreate;
					WhenUpdateWith = whenUpdate;
				}

				public Func<LineMessageBody, string> WhenCreationWith { get; }
				public Func<LineMessageBody, string> WhenUpdateWith { get; }

				internal static bool CreationProcess(LineMessageBody result, out string error)
				{
					return actionsPerLineType.CreationProcess(result.AliasId, result, out error);
				}
				internal static bool ActualizationProcess(LineMessageBody result, out string error)
				{
					return actionsPerLineType.ActualizationProcess(result.AliasId, result, out error);
				}

				const int DEFAULT_REWARD = 100;
				internal static void ResgisterProcessForEachLineType()
				{
					if (actionsPerLineType == null)
					{
						actionsPerLineType = new ActionsPerLineType();

						#region All functions to process betfair messages
						Action<LineMessageBody> calculatePublishingScriptFunction = (LineMessageBody lineCreationBody) =>
						{
							if (lineCreationBody.HasRewards && lineCreationBody.Status== "OPEN")
							{
								lineCreationBody.ScriptForPublishing = @"if (!line.IsAllowedToPublishOn(game.CurrentPeriod)) {{
																	line.AllowToPublishOn(game.CurrentPeriod);
																	}}
																	line.Publish(itIsThePresent, Now);";
							}
						};
						ScriptGeneratorPerLineType nFixedRunnersProcess = new ScriptGeneratorPerLineType(
							delegate (LineMessageBody result)
							{
								if (result.Options.Count() < 2)
								{
									string reason = $"marketType: {result.AliasId} with {result.Options.Count()} runners its not supported.";
									return reason;
								}

								var options = new List<string>();
								var rewards = new List<int>();
								result.HasRewards = true;
								foreach (var option in result.Options)
                                {
									decimal handicapRunner = Convert.ToDecimal(option.Handicap);
									bool thereIsAHandicapDistinctThanZero = handicapRunner != 0;
									if (thereIsAHandicapDistinctThanZero)
									{
										string reason = $"marketType: {result.AliasId} with 0!= handicap its not supported.";
										return reason;
									}

									var selection = option.RunnerName;
									decimal decimalReward = option.Price;
									int americanReward = decimalReward == 0m ? DEFAULT_REWARD : Line.ConvertDecimalOddsToAmerican(decimalReward);
									options.Add($"'{selection}'");
									rewards.Add(americanReward);
									if (result.HasRewards) result.HasRewards = americanReward != DEFAULT_REWARD;
								}

								var optionsAsText = string.Join(',', options);
								var rewardsAsText = string.Join(',', rewards);
								result.ScriptSettingParameters = $"question.SetParameters({{{optionsAsText}}}, {{{rewardsAsText}}});";
								return "";
							},
							delegate (LineMessageBody result) {

								if (result.AmountRunners < 2)
								{
									string reason = $"marketType: {result.AliasId} with {result.AmountRunners} runners its not supported.";
									return reason;
								}

								var rewards = new List<int>();
								result.HasRewards = true;
								foreach (var option in result.Options)
                                {
									int americanReward = DEFAULT_REWARD;
									if (option.Price != 0)
									{
										americanReward = Line.ConvertDecimalOddsToAmerican(option.Price);
									}
									rewards.Add(americanReward);
									if (result.HasRewards) result.HasRewards = americanReward != DEFAULT_REWARD;
								}

								var rewardsAsText = string.Join(',', rewards);
								result.ScriptSettingParameters = $"newLine = line.NewVersion({{{rewardsAsText}}}, 'ETL', Now);";
								return "";
							}
						);
						ScriptGeneratorPerLineType twoRunnersProcess = new ScriptGeneratorPerLineType(
							delegate (LineMessageBody result)
							{
								if (result.AmountRunners != 2)
								{
									string reason = $"marketType: {result.AliasId} with {result.AmountRunners} runners its not supported.";
									return reason;
								}

								decimal handicapRunner0 = Convert.ToDecimal(result.Options[0].Handicap);
								decimal handicapRunner1 = Convert.ToDecimal(result.Options[1].Handicap);

								bool thereIsAHandicapDistinctThanZero = handicapRunner0 != 0 || handicapRunner1 != 0;
								if (thereIsAHandicapDistinctThanZero)
								{
									string reason = $"marketType: {result.AliasId} with 0!= handicap its not supported.";
									return reason;
								}

								var decimalTeamAReward = result.Options[0].Price;
								var decimalTeamBReward = result.Options[1].Price;
								int americanTeamAReward = DEFAULT_REWARD;
								int americanTeamBReward = -DEFAULT_REWARD;
								if ((decimalTeamAReward != 0) && (decimalTeamBReward != 0))
								{
									americanTeamAReward = decimalTeamAReward == 0m ? DEFAULT_REWARD : Line.ConvertDecimalOddsToAmerican(decimalTeamAReward);
									americanTeamBReward = decimalTeamBReward == 0m ? -DEFAULT_REWARD : Line.ConvertDecimalOddsToAmerican(decimalTeamBReward);
								}

								result.HasRewards = (americanTeamAReward != DEFAULT_REWARD && americanTeamBReward != -DEFAULT_REWARD);
								result.ScriptSettingParameters = $"question.SetParameters({americanTeamAReward}, {americanTeamBReward});";
								return "";
							},
							delegate (LineMessageBody result) {

								if (result.AmountRunners != 2)
								{
									string reason = $"marketType: {result.AliasId} with {result.AmountRunners} runners its not supported.";
									return reason;
								}

								decimal handicapRunner0 = result.Options[0].Handicap;
								decimal handicapRunner1 = result.Options[1].Handicap;
								bool thereIsAHandicapDistinctThanZero = handicapRunner0 != 0 || handicapRunner1 != 0;
								if (thereIsAHandicapDistinctThanZero)
								{
									string reason = $"marketType: {result.AliasId} with 0!= handicap its not supported.";
									return reason;
								}

								var decimalTeamAReward = (decimal)result.Options[0].Price;
								var decimalTeamBReward = (decimal)result.Options[1].Price;

								int americanTeamAReward = decimalTeamAReward == 0m ? DEFAULT_REWARD : Line.ConvertDecimalOddsToAmerican((decimal)decimalTeamAReward);
								int americanTeamBReward = decimalTeamBReward == 0m ? -DEFAULT_REWARD : Line.ConvertDecimalOddsToAmerican((decimal)decimalTeamBReward);

								result.HasRewards = (americanTeamAReward != DEFAULT_REWARD && americanTeamBReward != -DEFAULT_REWARD);
								result.ScriptSettingParameters = $"newLine = line.NewVersion({americanTeamAReward}, {americanTeamBReward}, 'ETL', Now);";

								return "";
							}
						);
						ScriptGeneratorPerLineType threeRunnersProcess = new ScriptGeneratorPerLineType(
							delegate (LineMessageBody result)
							{
								if (result.AmountRunners != 3)
								{
									string reason = $"marketType: {result.AliasId} with {result.AmountRunners} runners its not supported.";
									return reason;
								}

								decimal handicapRunner0 = Convert.ToDecimal(result.Options[0].Handicap);
								decimal handicapRunner1 = Convert.ToDecimal(result.Options[1].Handicap);
								decimal handicapRunner2 = Convert.ToDecimal(result.Options[2].Handicap);

								bool thereIsAHandicapDistinctThanZero = handicapRunner0 != 0 || handicapRunner1 != 0 || handicapRunner2 != 0;
								if (thereIsAHandicapDistinctThanZero)
								{
									string reason = $"marketType: {result.AliasId} with 0!= handicap its not supported.";
									return reason;
								}

								var decimalTeamAReward = result.Options[0].Price;
								var decimalTeamBReward = result.Options[1].Price;
								var decimalTieReward = result.Options[02].Price;
								int americanTeamAReward = DEFAULT_REWARD;
								int americanTeamBReward = -DEFAULT_REWARD;
								int americanTieReward = DEFAULT_REWARD;
								if ((decimalTeamAReward != 0) && (decimalTeamBReward != 0) && (decimalTieReward != 0))
								{
									americanTeamAReward = decimalTeamAReward == 0m ? DEFAULT_REWARD : Line.ConvertDecimalOddsToAmerican(decimalTeamAReward);
									americanTeamBReward = decimalTeamBReward == 0m ? -DEFAULT_REWARD : Line.ConvertDecimalOddsToAmerican(decimalTeamBReward);
									americanTieReward = decimalTieReward == 0m ? DEFAULT_REWARD : Line.ConvertDecimalOddsToAmerican(decimalTieReward);
								}

								result.HasRewards = (americanTeamAReward != DEFAULT_REWARD && americanTeamBReward != -DEFAULT_REWARD && americanTieReward != DEFAULT_REWARD);
								result.ScriptSettingParameters = $"question.SetParameters({americanTeamAReward}, {americanTieReward}, {americanTeamBReward});";
								return "";
							},
							delegate (LineMessageBody result) {

								if (result.AmountRunners != 3)
								{
									string reason = $"marketType: {result.AliasId} with {result.AmountRunners} runners its not supported.";
									return reason;
								}

								decimal handicapRunner0 = result.Options[0].Handicap;
								decimal handicapRunner1 = result.Options[1].Handicap;
								decimal handicapRunner2 = result.Options[2].Handicap;
								bool thereIsAHandicapDistinctThanZero = handicapRunner0 != 0 || handicapRunner1  !=0 || handicapRunner2 !=0;
								if (thereIsAHandicapDistinctThanZero)
								{
									string reason = $"marketType: {result.AliasId} with 0!= handicap its not supported.";
									return reason;
								}

								decimal decimalTeamAReward = (decimal)result.Options[0].Price;
								decimal decimalTeamBReward = (decimal)result.Options[1].Price;
								decimal decimalTieReward = (decimal)result.Options[2].Price;

								int americanTeamAReward = decimalTeamAReward == 0m ? DEFAULT_REWARD : Line.ConvertDecimalOddsToAmerican((decimal)decimalTeamAReward);
								int americanTeamBReward = decimalTeamBReward == 0m ? -DEFAULT_REWARD : Line.ConvertDecimalOddsToAmerican((decimal)decimalTeamBReward);
								int americanTieReward = decimalTieReward == 0m ? DEFAULT_REWARD : Line.ConvertDecimalOddsToAmerican((decimal)decimalTieReward);

								result.HasRewards = (americanTeamAReward != DEFAULT_REWARD && americanTeamBReward != -DEFAULT_REWARD && americanTieReward != DEFAULT_REWARD);
								result.ScriptSettingParameters = $"newLine = line.NewVersion({americanTeamAReward}, {americanTieReward}, {americanTeamBReward}, 'ETL', Now);";

								return "";
							}
						);
						ScriptGeneratorPerLineType RunnersWithSpreadProcess = new ScriptGeneratorPerLineType(
							delegate (LineMessageBody result)
							{
								if (result.AmountRunners != 2)
								{
									string reason = $"marketType: {result.AliasId} with {result.AmountRunners} runners its not supported.";
									return reason;
								}

								decimal handicapRunner0 = result.Options[0].Handicap;
								decimal handicapRunner1 = result.Options[1].Handicap;

								bool thereIsAHandicapDistinctThanZero = handicapRunner0 != 0 || handicapRunner1 != 0;
								if (thereIsAHandicapDistinctThanZero)
								{
									string reason = $"marketType: {result.AliasId} with 0!= handicap its not supported.";
									return reason;
								}

								var decimalTeamAReward = result.Options[0].Price;
								var decimalTeamBReward = result.Options[1].Price;
								int americanTeamAReward = DEFAULT_REWARD;
								int americanTeamBReward = -DEFAULT_REWARD;
								if ((decimalTeamAReward != 0) && (decimalTeamBReward != 0))
								{
									americanTeamAReward = decimalTeamAReward == 0m ? DEFAULT_REWARD : Line.ConvertDecimalOddsToAmerican(decimalTeamAReward);
									americanTeamBReward = decimalTeamBReward == 0m ? -DEFAULT_REWARD : Line.ConvertDecimalOddsToAmerican(decimalTeamBReward);
								}

								result.HasRewards = (americanTeamAReward != DEFAULT_REWARD && americanTeamBReward != -DEFAULT_REWARD);
								result.ScriptSettingParameters = $"question.SetParameters({result.Spread}, {americanTeamAReward}, {americanTeamBReward});";
								return "";
							},
							delegate (LineMessageBody result) {
								if (result.AmountRunners != 2)
								{
									string reason = $"marketType: {result.AliasId} with {result.AmountRunners} runners its not supported.";
									return reason;
								}
								
								decimal handicapRunner0 = result.Options[0].Handicap;
								decimal handicapRunner1 = result.Options[1].Handicap;
								bool thereIsAHandicapDistinctThanZero = handicapRunner0 !=0 || handicapRunner1 !=0;
								if (thereIsAHandicapDistinctThanZero)
								{
									string reason = $"marketType: {result.AliasId} with 0!= handicap its not supported.";
									return reason;
								}

								var decimalTeamAReward = (decimal)result.Options[0].Price;
								var decimalTeamBReward = (decimal)result.Options[1].Price;

								int americanTeamAReward = decimalTeamAReward == 0m ? DEFAULT_REWARD : Line.ConvertDecimalOddsToAmerican((decimal)decimalTeamAReward);
								int americanTeamBReward = decimalTeamBReward == 0m ? -DEFAULT_REWARD : Line.ConvertDecimalOddsToAmerican((decimal)decimalTeamBReward);

								result.HasRewards = (americanTeamAReward != DEFAULT_REWARD && americanTeamBReward != -DEFAULT_REWARD);
								result.ScriptSettingParameters = $"newLine = line.NewVersion( {americanTeamAReward}, {americanTeamBReward}, 'ETL', Now);";

								return "";
							}
						);
						#endregion
						
						actionsPerLineType.Add("MATCH_BET", twoRunnersProcess, calculatePublishingScriptFunction, (LineMessageBody lineMessageBody) => { });
						actionsPerLineType.Add("MONEY_LINE", twoRunnersProcess, calculatePublishingScriptFunction, (LineMessageBody lineMessageBody) => { });
						actionsPerLineType.Add("MATCH_ODDS2", twoRunnersProcess, calculatePublishingScriptFunction, (LineMessageBody lineMessageBody) =>{});
						actionsPerLineType.Add("MATCH_ODDS3", threeRunnersProcess, calculatePublishingScriptFunction, (LineMessageBody lineCreationBody) =>{});
						actionsPerLineType.Add("HALF_MATCH_ODDS", twoRunnersProcess, calculatePublishingScriptFunction, (LineMessageBody lineCreationBody) =>{});
						actionsPerLineType.Add("HAT_TRICKED_SCORED", twoRunnersProcess, calculatePublishingScriptFunction, (LineMessageBody lineCreationBody) =>{});
						actionsPerLineType.Add("BOTH_TEAMS_TO_SCORE", twoRunnersProcess, calculatePublishingScriptFunction, (LineMessageBody lineCreationBody) =>{});
						actionsPerLineType.Add("PENALTY_TAKEN", twoRunnersProcess, calculatePublishingScriptFunction, (LineMessageBody lineCreationBody) => { });
						actionsPerLineType.Add("SENDING_OFF", twoRunnersProcess, calculatePublishingScriptFunction, (LineMessageBody lineCreationBody) => { });
						actionsPerLineType.Add("SCORE_CAST", nFixedRunnersProcess, calculatePublishingScriptFunction, (LineMessageBody lineCreationBody) => { });
						actionsPerLineType.Add("TO_SCORE_2_OR_MORE", nFixedRunnersProcess, calculatePublishingScriptFunction, (LineMessageBody lineCreationBody) => { });
						actionsPerLineType.Add("CORNER_ODDS", nFixedRunnersProcess, calculatePublishingScriptFunction, (LineMessageBody lineCreationBody) => { });
						actionsPerLineType.Add("HALF_TIME", threeRunnersProcess, calculatePublishingScriptFunction, (LineMessageBody lineCreationBody) => { });
						actionsPerLineType.Add("RT_MATCH_ODDS", threeRunnersProcess, calculatePublishingScriptFunction, (LineMessageBody lineCreationBody) => { });
						actionsPerLineType.Add("ODD_OR_EVEN", nFixedRunnersProcess, calculatePublishingScriptFunction, (LineMessageBody lineCreationBody) => { });
						actionsPerLineType.Add("CORRECT_SCORE", nFixedRunnersProcess, calculatePublishingScriptFunction, (LineMessageBody lineCreationBody) => { });
						actionsPerLineType.Add("CORRECT_SCORE2_2", nFixedRunnersProcess, calculatePublishingScriptFunction, (LineMessageBody lineCreationBody) => { });
						actionsPerLineType.Add("CORRECT_SCORE2_3", nFixedRunnersProcess, calculatePublishingScriptFunction, (LineMessageBody lineCreationBody) => { });
						actionsPerLineType.Add("HALF_TIME_SCORE", nFixedRunnersProcess, calculatePublishingScriptFunction, (LineMessageBody lineCreationBody) => { });
						actionsPerLineType.Add("HALF_TIME_FULL_TIME", nFixedRunnersProcess, calculatePublishingScriptFunction, (LineMessageBody lineCreationBody) => { });
						actionsPerLineType.Add("FIRST_GOAL_ODDS", nFixedRunnersProcess, calculatePublishingScriptFunction, (LineMessageBody lineCreationBody) => { });
						actionsPerLineType.Add("TO_SCORE", nFixedRunnersProcess, calculatePublishingScriptFunction, (LineMessageBody lineCreationBody) => { });
						actionsPerLineType.Add("TO_SCORE_HATTRICK", nFixedRunnersProcess, calculatePublishingScriptFunction, (LineMessageBody lineCreationBody) => { });
						actionsPerLineType.Add("SHOWN_A_CARD", nFixedRunnersProcess, calculatePublishingScriptFunction, (LineMessageBody lineCreationBody) => { });
						actionsPerLineType.Add("TO_QUALIFY", nFixedRunnersProcess, calculatePublishingScriptFunction, (LineMessageBody lineCreationBody) => { });
						actionsPerLineType.Add("CORNER_MATCH_BET", nFixedRunnersProcess, calculatePublishingScriptFunction, (LineMessageBody lineCreationBody) => { });
						actionsPerLineType.Add("BOOKING_ODDS", nFixedRunnersProcess, calculatePublishingScriptFunction, (LineMessageBody lineCreationBody) => { });
						actionsPerLineType.Add("OVER_UNDER_05", RunnersWithSpreadProcess, calculatePublishingScriptFunction, (LineMessageBody lineCreationBody) =>
						{
							lineCreationBody.Spread = 0.5m;
						});
						actionsPerLineType.Add("OVER_UNDER_05_CORNR", RunnersWithSpreadProcess, calculatePublishingScriptFunction, (LineMessageBody lineCreationBody) =>
						{
							lineCreationBody.Spread = 0.5m;
						});
						actionsPerLineType.Add("OVER_UNDER_15", RunnersWithSpreadProcess, calculatePublishingScriptFunction, (LineMessageBody lineCreationBody) =>
						{
							lineCreationBody.Spread = 1.5m;
						});
						actionsPerLineType.Add("OVER_UNDER_15_CORNR", RunnersWithSpreadProcess, calculatePublishingScriptFunction, (LineMessageBody lineCreationBody) =>
						{
							lineCreationBody.Spread = 1.5m;
						});
						actionsPerLineType.Add("OVER_UNDER_25", RunnersWithSpreadProcess, calculatePublishingScriptFunction, (LineMessageBody lineCreationBody) =>
						{
							lineCreationBody.Spread = 2.5m;
						});
						actionsPerLineType.Add("OVER_UNDER_25_CORNR", RunnersWithSpreadProcess, calculatePublishingScriptFunction, (LineMessageBody lineCreationBody) =>
						{
							lineCreationBody.Spread = 2.5m;
						});
						actionsPerLineType.Add("OVER_UNDER_35", RunnersWithSpreadProcess, calculatePublishingScriptFunction, (LineMessageBody lineCreationBody) =>
						{
							lineCreationBody.Spread = 3.5m;
						});
						actionsPerLineType.Add("OVER_UNDER_35_CORNR", RunnersWithSpreadProcess, calculatePublishingScriptFunction, (LineMessageBody lineCreationBody) =>
						{
							lineCreationBody.Spread = 3.5m;
						});
						actionsPerLineType.Add("OVER_UNDER_45", RunnersWithSpreadProcess, calculatePublishingScriptFunction, (LineMessageBody lineCreationBody) =>
						{
							lineCreationBody.Spread = 4.5m;
						});
						actionsPerLineType.Add("OVER_UNDER_45_CORNR", RunnersWithSpreadProcess, calculatePublishingScriptFunction, (LineMessageBody lineCreationBody) =>
						{
							lineCreationBody.Spread = 4.5m;
						});
						actionsPerLineType.Add("OVER_UNDER_55", RunnersWithSpreadProcess, calculatePublishingScriptFunction, (LineMessageBody lineCreationBody) =>
						{
							lineCreationBody.Spread = 5.5m;
						});
						actionsPerLineType.Add("OVER_UNDER_55_CORNR", RunnersWithSpreadProcess, calculatePublishingScriptFunction, (LineMessageBody lineCreationBody) =>
						{
							lineCreationBody.Spread = 5.5m;
						});
						actionsPerLineType.Add("OVER_UNDER_65", RunnersWithSpreadProcess, calculatePublishingScriptFunction, (LineMessageBody lineCreationBody) =>
						{
							lineCreationBody.Spread = 6.5m;
						});
						actionsPerLineType.Add("OVER_UNDER_65_CORNR", RunnersWithSpreadProcess, calculatePublishingScriptFunction, (LineMessageBody lineCreationBody) =>
						{
							lineCreationBody.Spread = 6.5m;
						});
						actionsPerLineType.Add("OVER_UNDER_75", RunnersWithSpreadProcess, calculatePublishingScriptFunction, (LineMessageBody lineCreationBody) =>
						{
							lineCreationBody.Spread = 7.5m;
						});
						actionsPerLineType.Add("OVER_UNDER_75_CORNR", RunnersWithSpreadProcess, calculatePublishingScriptFunction, (LineMessageBody lineCreationBody) =>
						{
							lineCreationBody.Spread = 7.5m;
						});
						actionsPerLineType.Add("OVER_UNDER_85", RunnersWithSpreadProcess, calculatePublishingScriptFunction, (LineMessageBody lineCreationBody) =>
						{
							lineCreationBody.Spread = 8.5m;
						});
						actionsPerLineType.Add("OVER_UNDER_85_CORNR", RunnersWithSpreadProcess, calculatePublishingScriptFunction, (LineMessageBody lineCreationBody) =>
						{
							lineCreationBody.Spread = 8.5m;
						});
						actionsPerLineType.Add("OVER_UNDER_95", RunnersWithSpreadProcess, calculatePublishingScriptFunction, (LineMessageBody lineCreationBody) =>
						{
							lineCreationBody.Spread = 9.5m;
						});
						actionsPerLineType.Add("OVER_UNDER_95_CORNR", RunnersWithSpreadProcess, calculatePublishingScriptFunction, (LineMessageBody lineCreationBody) =>
						{
							lineCreationBody.Spread = 9.5m;
						});
						actionsPerLineType.Add("OVER_UNDER_105", RunnersWithSpreadProcess, calculatePublishingScriptFunction, (LineMessageBody lineCreationBody) =>
						{
							lineCreationBody.Spread = 10.5m;
						});
						actionsPerLineType.Add("OVER_UNDER_105_CORNR", RunnersWithSpreadProcess, calculatePublishingScriptFunction, (LineMessageBody lineCreationBody) =>
						{
							lineCreationBody.Spread = 10.5m;
						});
						actionsPerLineType.Add("OVER_UNDER_115", RunnersWithSpreadProcess, calculatePublishingScriptFunction, (LineMessageBody lineCreationBody) =>
						{
							lineCreationBody.Spread = 11.5m;
						});
						actionsPerLineType.Add("OVER_UNDER_115_CORNR", RunnersWithSpreadProcess, calculatePublishingScriptFunction, (LineMessageBody lineCreationBody) =>
						{
							lineCreationBody.Spread = 11.5m;
						});
						actionsPerLineType.Add("OVER_UNDER_125", RunnersWithSpreadProcess, calculatePublishingScriptFunction, (LineMessageBody lineCreationBody) =>
						{
							lineCreationBody.Spread = 12.5m;
						});
						actionsPerLineType.Add("OVER_UNDER_125_CORNR", RunnersWithSpreadProcess, calculatePublishingScriptFunction, (LineMessageBody lineCreationBody) =>
						{
							lineCreationBody.Spread = 12.5m;
						});
						actionsPerLineType.Add("OVER_UNDER_135", RunnersWithSpreadProcess, calculatePublishingScriptFunction, (LineMessageBody lineCreationBody) =>
						{
							lineCreationBody.Spread = 13.5m;
						});
						actionsPerLineType.Add("OVER_UNDER_135_CORNR", RunnersWithSpreadProcess, calculatePublishingScriptFunction, (LineMessageBody lineCreationBody) =>
						{
							lineCreationBody.Spread = 13.5m;
						});
						actionsPerLineType.Add("OVER_UNDER_145", RunnersWithSpreadProcess, calculatePublishingScriptFunction, (LineMessageBody lineCreationBody) =>
						{
							lineCreationBody.Spread = 14.5m;
						});
						actionsPerLineType.Add("OVER_UNDER_145_CORNR", RunnersWithSpreadProcess, calculatePublishingScriptFunction, (LineMessageBody lineCreationBody) =>
						{
							lineCreationBody.Spread = 14.5m;
						});
						actionsPerLineType.Add("OVER_UNDER_155", RunnersWithSpreadProcess, calculatePublishingScriptFunction, (LineMessageBody lineCreationBody) =>
						{
							lineCreationBody.Spread = 15.5m;
						});
						actionsPerLineType.Add("OVER_UNDER_155_CORNR", RunnersWithSpreadProcess, calculatePublishingScriptFunction, (LineMessageBody lineCreationBody) =>
						{
							lineCreationBody.Spread = 15.5m;
						});
						actionsPerLineType.Add("OVER_UNDER_165", RunnersWithSpreadProcess, calculatePublishingScriptFunction, (LineMessageBody lineCreationBody) =>
						{
							lineCreationBody.Spread = 16.5m;
						});
						actionsPerLineType.Add("OVER_UNDER_165_CORNR", RunnersWithSpreadProcess, calculatePublishingScriptFunction, (LineMessageBody lineCreationBody) =>
						{
							lineCreationBody.Spread = 16.5m;
						});
						actionsPerLineType.Add("OVER_UNDER_175", RunnersWithSpreadProcess, calculatePublishingScriptFunction, (LineMessageBody lineCreationBody) =>
						{
							lineCreationBody.Spread = 17.5m;
						});
						actionsPerLineType.Add("OVER_UNDER_175_CORNR", RunnersWithSpreadProcess, calculatePublishingScriptFunction, (LineMessageBody lineCreationBody) =>
						{
							lineCreationBody.Spread = 17.5m;
						});
						actionsPerLineType.Add("OVER_UNDER_185", RunnersWithSpreadProcess, calculatePublishingScriptFunction, (LineMessageBody lineCreationBody) =>
						{
							lineCreationBody.Spread = 18.5m;
						});
						actionsPerLineType.Add("OVER_UNDER_185_CORNR", RunnersWithSpreadProcess, calculatePublishingScriptFunction, (LineMessageBody lineCreationBody) =>
						{
							lineCreationBody.Spread = 18.5m;
						});
						actionsPerLineType.Add("OVER_UNDER_195", RunnersWithSpreadProcess, calculatePublishingScriptFunction, (LineMessageBody lineCreationBody) =>
						{
							lineCreationBody.Spread = 19.5m;
						});
						actionsPerLineType.Add("OVER_UNDER_195_CORNR", RunnersWithSpreadProcess, calculatePublishingScriptFunction, (LineMessageBody lineCreationBody) =>
						{
							lineCreationBody.Spread = 19.5m;
						});
					}
				}
			}
			
			public override void OnMessageBeforeCommit(string msg)
			{
				Response providerResponse = NormalizedProviderResponse.DeSerialize(msg);
				List<object> documentsForELK = new List<object>();
				StringBuilder sportsSubcommand = null;

				if (providerResponse.Type == ResponseType.LEAGUE)
				{
					NormalizedProviderResponse leaguesUpdateFromKafka = (NormalizedProviderResponse)providerResponse;

					if (leaguesUpdateFromKafka.Provider == ProvidersCollection.Bet365Mock || leaguesUpdateFromKafka.Provider == ProvidersCollection.Bet365)
					{
						WhenNewBet365League(documentsForELK, leaguesUpdateFromKafka);
					}
					else if (leaguesUpdateFromKafka.Provider == ProvidersCollection.BetFair)
					{
						WhenNewBetfairLeague(documentsForELK, leaguesUpdateFromKafka);
					}
					else
					{
						throw new GameEngineException($"{leaguesUpdateFromKafka.Provider} it's not implemented yet.");
					}
				}
				else if (providerResponse.Type == ResponseType.SEASON)
				{
					NormalizedProviderResponse seasonUpdateFromKafka = (NormalizedProviderResponse)providerResponse;
					if (seasonUpdateFromKafka.Provider == ProvidersCollection.Bet365Mock || seasonUpdateFromKafka.Provider == ProvidersCollection.Bet365)
					{
						WhenNewBet365Season(documentsForELK, seasonUpdateFromKafka);
					}
					else
					{
						throw new GameEngineException($"{seasonUpdateFromKafka.Provider} it's not implemented yet.");
					}
				}
				else if (providerResponse.Type == ResponseType.TEAM)
				{
					NormalizedProviderResponse teamsUpdateFromKafka = (NormalizedProviderResponse)providerResponse;

					if (teamsUpdateFromKafka.Provider == ProvidersCollection.Bet365Mock || teamsUpdateFromKafka.Provider == ProvidersCollection.Bet365)
					{
						WhenNewBet365Team(documentsForELK, teamsUpdateFromKafka);
					}
					else if (teamsUpdateFromKafka.Provider == ProvidersCollection.BetFair)
					{
						WhenNewBetfairTeam(documentsForELK, teamsUpdateFromKafka);
					}
					else
					{
						throw new GameEngineException($"{teamsUpdateFromKafka.Provider} it's not implemented yet.");
					}
				}
				else if (providerResponse.Type == ResponseType.SPORTS)
				{
					SportResponseFromKafka betsAPITeamResponseFromKafka = (SportResponseFromKafka)providerResponse;
					WhenNewSport(documentsForELK, betsAPITeamResponseFromKafka);

				}
				else if (providerResponse.Type == ResponseType.LINES)
				{
					NormalizedProviderResponse linesUpdateFromKafka = (NormalizedProviderResponse)providerResponse;
					if (linesUpdateFromKafka.Provider == ProvidersCollection.BetFair)
					{
						WhenNewBetfairLine(documentsForELK, linesUpdateFromKafka);
					}
					else
					{
						throw new GameEngineException($"{linesUpdateFromKafka.Provider} it's not implemented yet.");
					}
				}
				else if (providerResponse.Type == ResponseType.LINE_UPDATE)
				{
					NormalizedProviderResponse linesUpdateFromKafka = (NormalizedProviderResponse)providerResponse;

					if (linesUpdateFromKafka.Provider == ProvidersCollection.BetFair)
					{
						WhenNewBetfairLineUpdate(documentsForELK, linesUpdateFromKafka);
					}
					else
					{
						throw new GameEngineException($"{linesUpdateFromKafka.Provider} it's not implemented yet.");
					}
				}

				if (documentsForELK.Count > 0)
				{
					//var postData = PostData.MultiJson(documentsForELK);
					HttpELKClient client = HttpELKClient.GetInstance();
					var response = client.CreateBulk(documentsForELK);
				}
			}
		}
	}

	[DataContract(Name = "LineValidations")]
	public class LineValidations
	{
		[DataMember(Name = "lineExists")]
		public bool LineExists { get; set; }
		[DataMember(Name = "isDraft")]
		public bool IsDraft { get; set; }
		[DataMember(Name = "isSuspended")]
		public bool IsSuspended { get; set; }
		[DataMember(Name = "isCanceled")]
		public bool IsCanceled { get; set; }
		[DataMember(Name = "isPublished")]
		public bool IsPublished { get; set; }
	}

	public class Sport
	{
		public string Id { get; internal set; }
		public string IdInTheProvider { get; internal set; }
		public string Name { get; internal set; }
		public int ProviderId { get; internal set; }
	}
	public class LeagueCreationResponse
	{
		public int LeagueId { set; get; }
	}

	[DataContract(Name = "sportsResponse")]
	public class SportsResponse
	{
		[DataMember(Name = "sports")]
		public List<SportResponse> Sports { get; set; } = new List<SportResponse>();
	}

	[DataContract(Name = "SportResponse")]
	public class SportResponse
	{
		[DataMember(Name = "aliases")]
		public List<AliasResponse> Aliases { get; set; } = new List<AliasResponse>();
		[DataMember(Name = "hasAliasForProvider")]
		public bool HasAliasForProvider { get; set; }
		[DataMember(Name = "sportId")]
		public int SportId { get; set; }
		[DataMember(Name = "sportName")]
		public string SportName { get; set; }
		[DataMember(Name = "wasThereAnyEnabledDomainForSport")]
		public bool WasThereAnyEnabledDomainForSport { get; set; }
		[DataMember(Name = "anyEnabledDomainForSport")]
		public bool AnyEnabledDomainForSport { get; set; }
		internal bool WithThisAlias(string newAliasId, string newAliasName)
		{
			return Aliases.FirstOrDefault(item =>
				 item.AliasId == newAliasId &&
				item.AliasName == newAliasName) != null;
		}

		internal bool WithThisAliasIdButDifferentName(string newAliasId, string newAliasName)
		{
			return Aliases.FirstOrDefault(item =>
						item.AliasId == newAliasId &&
			item.AliasName != newAliasName) != null;
		}
	}
	[DataContract(Name = "AliasResponse")]
	public class AliasResponse
	{
		[DataMember(Name = "aliasName")]
		public string AliasName { get; set; }
		[DataMember(Name = "aliasId")]
		public string AliasId { get; set; }
	}
}
