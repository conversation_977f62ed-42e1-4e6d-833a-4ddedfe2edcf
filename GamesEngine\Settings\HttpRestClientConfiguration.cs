﻿using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using RestSharp;
using System;
using System.Collections.Generic;
using System.IO;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Runtime.Serialization.Json;
using System.Text;
using System.Threading.Tasks;

namespace GamesEngine.Settings
{
	public class HttpRestClientConfiguration
	{
		private readonly HttpClient _clientWithBasicConfiguration;
		private readonly HttpClient _clientWithBasicConfigurationAndToken;
		private readonly HttpClient _clientWithFormEncoding;
		private readonly RestClient _restClient;
		private static HttpRestClientConfiguration instance = null;

		public static HttpRestClientConfiguration GetInstance()
		{
			if ( instance == null)
			{
				instance = new HttpRestClientConfiguration();
			}
			return instance;
		}

		private HttpRestClientConfiguration()
		{
			_clientWithBasicConfiguration = new HttpClient();
			_clientWithBasicConfiguration.DefaultRequestHeaders.Accept.Clear();
			_clientWithBasicConfiguration.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));

			_clientWithBasicConfigurationAndToken = new HttpClient();
			_clientWithBasicConfigurationAndToken.DefaultRequestHeaders.Accept.Clear();
			_clientWithBasicConfigurationAndToken.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));

			_clientWithFormEncoding = new HttpClient();
			_clientWithFormEncoding.DefaultRequestHeaders.Accept.Clear();
			_clientWithFormEncoding.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/x-www-form-urlencoded"));

			_restClient = new RestClient();

		}

		private HttpClient CreateClientWithBasicConfiguration(Token token)
		{
			if (token == null)
			{
				return _clientWithBasicConfiguration;
			}
			else
			{
				_clientWithBasicConfigurationAndToken.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token.access_token);
				return _clientWithBasicConfigurationAndToken;
			}
		}

		private RestClient CreateRestClientWithBasicConfiguration()
		{
			return _restClient;
		}

		private HttpClient CreateClientWithFormEncoding()
		{
			return _clientWithFormEncoding;
		}

		private HttpClient CreateClient(Token token)
		{
			HttpClient client = CreateClientWithBasicConfiguration(token);
			return client;
		}
		private RestClient CreateRestClient()
		{
			RestClient client = CreateRestClientWithBasicConfiguration();
			return client;
		}

		private HttpClient CreateClientForSubmit()
		{
			HttpClient client = CreateClientWithFormEncoding();
			return client;
		}

		public async Task<IActionResult> GetAsync(string path)
		{
			HttpClient client = CreateClient(null);
			HttpResponseMessage response = await client.GetAsync(path);

			return await CreateResponseAsync(response);
		}

		public IActionResult Get(string path)
		{
			RestClient client = CreateRestClient();
			var request = new RestRequest(path, Method.GET);
			var response = client.Execute(request);

			return CreateResponse(response);
		}

		public async Task<IActionResult> GetAsync(HttpContext context, string path)
		{
			var accesToken = context.Request.Headers["Authorization"].ToString();
			if (accesToken.StartsWith("Bearer "))
			{
				accesToken = accesToken.Replace("Bearer ", "");
			}
			Token token = new Token();
			token.access_token = accesToken;

			HttpClient client = CreateClient(token);
			HttpResponseMessage response = await client.GetAsync(path);

			return await CreateResponseAsync(response);
		}

		public async Task<IActionResult> GetAsync(Token token, string path)
		{
			HttpClient client = CreateClient(token);
			HttpResponseMessage response = await client.GetAsync(path);

			return await CreateResponseAsync(response);
		}

		public async Task<IActionResult> SubmitAsync(string path, string data)
		{
			HttpClient client = CreateClientForSubmit();
			var httpContent = new StringContent(data, System.Text.Encoding.UTF8, "application/x-www-form-urlencoded");

			HttpResponseMessage response = await client.PostAsync(path, httpContent);

			return await CreateResponseAsync(response);
		}

		public async Task<IActionResult> PostAsync(Token token, string path, string data)
		{
			HttpClient client = CreateClient(token);
			var httpContent = new StringContent(data, System.Text.Encoding.UTF8, "application/json");

			HttpResponseMessage response = await client.PostAsync(path, httpContent);

			return await CreateResponseAsync(response);
		}

		public async Task<IActionResult> PostAsync(string path, object instance)
		{
			HttpClient client = CreateClient(null);
			var jsonString = ToJson(instance);
			var httpContent = new StringContent(jsonString, Encoding.UTF8, "application/json");

			HttpResponseMessage response = await client.PostAsync(path, httpContent);

			return await CreateResponseAsync(response);
		}

		public async Task<IActionResult> PostAsync(Token token, string path, object instance)
		{
			HttpClient client = CreateClient(token);
			var jsonString = ToJson(instance);
			var httpContent = new StringContent(jsonString, Encoding.UTF8, "application/json");

			HttpResponseMessage response = await client.PostAsync(path, httpContent);

			return await CreateResponseAsync(response);
		}

		public async Task<IActionResult> PostAsync(HttpContext context, string path, object instance)
		{
			var accesToken = context.Request.Headers["Authorization"].ToString();
			if (accesToken.StartsWith("Bearer "))
			{
				accesToken = accesToken.Replace("Bearer ", "");
			}
			Token token = new Token();
			token.access_token = accesToken;

			HttpClient client = CreateClient(token);
			var jsonString = ToJson(instance);
			var httpContent = new StringContent(jsonString, Encoding.UTF8, "application/json");

			HttpResponseMessage response = await client.PostAsync(path, httpContent);

			return await CreateResponseAsync(response);
		}

		public async Task<IActionResult> PutAsync(Token token, string path, string data)
		{
			HttpClient client = CreateClient(token);
			var httpContent = new StringContent(data, System.Text.Encoding.UTF8, "application/json");

			HttpResponseMessage response = await client.PutAsync(path, httpContent);

			return await CreateResponseAsync(response);
		}

		private IActionResult CreateResponse(IRestResponse response)
		{
			try
			{
				string jsonString = response.Content;
				var reader = new JsonTextReader(new StringReader(jsonString));
				reader.FloatParseHandling = FloatParseHandling.Decimal;

				JObject obj = JObject.Load(reader);

				return StringToIActionResult((int)response.StatusCode, obj);
			}
			catch (Exception e)
			{
				ErrorsSender.Send(e);
			}

			object json = JsonConvert.DeserializeObject<object>("{}");
			return StringToIActionResult((int)response.StatusCode, json);
		}

		private async Task<IActionResult> CreateResponseAsync(HttpResponseMessage response)
		{
			object json = JsonConvert.DeserializeObject<object>("{}");
			try
			{
				string jsonString = await response.Content.ReadAsStringAsync();
				json = JsonConvert.DeserializeObject<object>(jsonString);
			}
			catch (Exception e)
			{
				ErrorsSender.Send(e);
			}
			return StringToIActionResult((int)response.StatusCode, json);
		}

		private IActionResult StringToIActionResult(int status, object json)
		{
			if (200 == status || 201 == status || 204 == status)
			{
				return new OkObjectResult(json);
			}
			else if (400 == status || 404 == status)
			{
				return new NotFoundObjectResult(json);
			}
			else
			{
				return new InternalServerErrorObjectResult(json);
			}
		}
		internal async Task<IActionResult> DeleteAsync(Token token, string path, string data)
		{
			HttpClient client = CreateClient(token);
			HttpRequestMessage request = new HttpRequestMessage
			{
				Content = new StringContent(data, System.Text.Encoding.UTF8, "application/json"),
				Method = HttpMethod.Delete,
				RequestUri = new Uri(path)
			};
			HttpResponseMessage response = await client.SendAsync(request);
			return await CreateResponseAsync(response);
		}

		private string ToJson(object instance)
		{
			using (MemoryStream mst = new MemoryStream())
			{
				var serializer = new DataContractJsonSerializer(instance.GetType());
				serializer.WriteObject(mst, instance);
				mst.Position = 0;
				using (StreamReader r = new StreamReader(mst))
				{
					return r.ReadToEnd();
				}
			}
		}

	}

	public class InternalServerErrorObjectResult : ObjectResult
	{
		public InternalServerErrorObjectResult(object value) : base(value)
		{
			StatusCode = StatusCodes.Status500InternalServerError;
		}

		public InternalServerErrorObjectResult() : this(null)
		{
			StatusCode = StatusCodes.Status500InternalServerError;
		}
	}

	public class NotFoundErrorObjectResult : ObjectResult
	{
		public NotFoundErrorObjectResult(object value) : base(value)
		{
			StatusCode = StatusCodes.Status404NotFound;
		}

		public NotFoundErrorObjectResult() : this(null)
		{
			StatusCode = StatusCodes.Status404NotFound;
		}
	}
}
