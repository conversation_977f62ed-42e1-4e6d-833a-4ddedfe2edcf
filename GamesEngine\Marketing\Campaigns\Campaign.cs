﻿using GamesEngine.Business;
using GamesEngine.Business.Marketing;
using GamesEngine.Finance;
using GamesEngine.Loyalty;
using GamesEngine.Marketing.Segments;
using GamesEngine.PurchaseOrders;
using GamesEngine.Settings;
using GamesEngine.Time;
using Puppeteer.EventSourcing.Libraries;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace GamesEngine.Marketing.Campaigns
{
    public abstract class Campaign : Objeto
    {
        private int id;
        private int consecutive = 1;
        private DateTime initialDate;
        private DateTime finalDate;
        private readonly DateTime createdDate;
        private DateTime sentDate;
        private readonly Company company;
        private bool visible = true;
        private bool isEnabled = true;

        protected int givenTimesNumber = 0;

        protected NotificationStorage notificationStorage;
        private static MarketingStorage marketingStorageInstance;

        protected SalesStorage salesStorage;
        protected TotalizerSales totalizerSales;

        protected BeneficiaryCustomers beneficiaryCustomers;

        internal Campaign(Company company)
		{
            if (company == null) throw new ArgumentNullException(nameof(company));
            this.id = consecutive++;
			this.company = company;

            beneficiaryCustomers = new BeneficiaryCustomers();
        }

		internal  Campaign(Company company, DateTime createdDate)
        {
            if (company == null) throw new ArgumentNullException(nameof(company));
            this.id = consecutive++;
            this.company = company;
            this.createdDate = createdDate;

            beneficiaryCustomers = new BeneficiaryCustomers();
        }

        internal int Id
        {
            get
            {
                return this.id;
            }
        }

        internal virtual string CampaignType => "None";

        internal int GivenTimesNumber => givenTimesNumber;

        internal void IncrementGivenTimesNumber()
        {
            givenTimesNumber++;
        }

        internal DateTime InitialDate
        {
            set
            {
                this.initialDate = value;
            }
            get
            {
                return this.initialDate;
            }
        }

        internal DateTime FinalDate
        {
            set
            {
                this.finalDate = value;
            }
            get
            {
                return this.finalDate;
            }
        }

		internal Company Company
		{
			get
			{
				return this.company;
			}
		}

		internal DateTime CreatedDate
		{
			get
			{
				return this.createdDate;
			}
		}

		internal DateTime SentDate
		{
			get
			{
				return this.sentDate;
			}
			set
			{
				this.sentDate = value;
			}
		}

        internal virtual bool IsEnabled
        {
            get
            {
                return isEnabled;
            }
            set
            {
                isEnabled = value;
            }
        }

        internal virtual bool IsActive(DateTime now)
        {
            return IsEnabled;
        }

        protected void CreateConnectionToDBNotificationStorage()
        {
            this.notificationStorage = null;
            if (Integration.Db.DBSelected == HistoricalDatabaseType.MySQL.ToString())
            {
                this.notificationStorage = new NotificationStorage(HistoricalDatabaseType.MySQL, Integration.Db.MySQL);
            }
            else if (Integration.Db.DBSelected == HistoricalDatabaseType.SQLServer.ToString())
            {
                this.notificationStorage = new NotificationStorage(HistoricalDatabaseType.SQLServer, Integration.Db.SQLServer);
            }
            else if (!String.IsNullOrWhiteSpace(Integration.Db.DBSelected))
            {
                throw new Exception($"There is no connection for {Integration.Db.DBSelected}");
            }
        }

        internal static MarketingStorage MarketingStorageCampaignInstance()
        {
            if (Integration.Db?.DBSelected == HistoricalDatabaseType.MySQL.ToString())
            {
                if (marketingStorageInstance == null) marketingStorageInstance = new MarketingStorage(HistoricalDatabaseType.MySQL, Integration.Db.MySQL);
            }
            else if (Integration.Db?.DBSelected == HistoricalDatabaseType.SQLServer.ToString())
            {
                if (marketingStorageInstance == null) marketingStorageInstance = new MarketingStorage(HistoricalDatabaseType.SQLServer, Integration.Db.SQLServer);
            }
            else if (!String.IsNullOrWhiteSpace(Integration.Db?.DBSelected))
            {
                throw new Exception($"There is no connection for {Integration.Db.DBSelected}");
            }
            return marketingStorageInstance;
        }

        internal IEnumerable<Customer> LastBeneficiaries => beneficiaryCustomers.LastBeneficiaries;

        internal void RegisterBeneficiary(Customer customer)
        {
            if (customer == null) throw new ArgumentNullException(nameof(customer));

            if (!beneficiaryCustomers.Exists(customer)) beneficiaryCustomers.Add(customer);
            beneficiaryCustomers.RegisterAsTheLast(customer);
        }

        protected class BeneficiaryCustomers
        {
            const int MaxNumberOfCustomers = 10;
            HashSet<Customer> uniqueCustomers = new HashSet<Customer>();

            internal List<Customer> LastBeneficiaries { get; } = new List<Customer>();

            internal int Count => uniqueCustomers.Count;

            internal void Add(Customer customer)
            {
                if (customer == null) throw new ArgumentNullException(nameof(customer));
                if (Exists(customer)) throw new GameEngineException($"{nameof(customer)} '{customer.AccountNumber}' is already registered as gift card beneficiary");

                uniqueCustomers.Add(customer);
            }

            internal void RegisterAsTheLast(Customer customer)
            {
                if (customer == null) throw new ArgumentNullException(nameof(customer));

                if (LastBeneficiaries.Count == MaxNumberOfCustomers) LastBeneficiaries.Remove(LastBeneficiaries.Last());
                LastBeneficiaries.Insert(0, customer);
            }

            internal bool Exists(Customer customer)
            {
                if (customer == null) throw new ArgumentNullException(nameof(customer));

                return uniqueCustomers.Contains(customer);
            }

            internal void Clear()
            {
                uniqueCustomers.Clear();
            }
        }

    }
}
