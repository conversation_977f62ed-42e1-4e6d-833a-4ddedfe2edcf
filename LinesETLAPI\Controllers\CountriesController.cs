﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using System.Text;
using System.Threading.Tasks;
using GamesEngine.Games;
using GamesEngine.Middleware;
using GamesEngine.Middleware.Providers;
using LinesETLAPI;
using LinesETLAPI.Controllers;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace LinesETLAPI.Controllers
{
	public class CountriesController : AuthorizeController
	{
		[HttpGet("api/countries")]
        [Authorize(Roles = "devops")]//VERIFY_ROLES
        public async Task<IActionResult> GetCountriesAsync()
		{
			var resultQry = await LinesETLAPI.LinesETL.PerformQryAsync(HttpContext, $@"
					{{
						countriesList = mapper.Countries;
						for(countries : countriesList.GetAll())
						{{
							hasAnyAlias = countries.Aliases.HasAnyForeingAlias();
							print hasAnyAlias hasAnyAlias;
							print countries.Code code;
							print countries.Name sportName;

							if(hasAnyAlias)
							{{
								for(aliases : countries.Aliases.GetAllForeingAlias())
								{{
									print aliases.Name aliasName;
									print aliases.Id aliasId;
								}}
							}}
						}}
					}}
				");
			return resultQry;
		}

		[HttpDelete("api/countries/{countyCode}")]
        [Authorize(Roles = "devops")]//VERIFY_ROLES
        public async Task<IActionResult> AddCountriesToBetrackedAsync(string countyCode)
		{
			var result = await LinesETLAPI.LinesETL.PerformQryAsync(HttpContext, $@"
					{{
						countries = mapper.Countries;
						countries.Remove('{countyCode}');
					}}
				");
			return result;
		}

		[HttpPost("api/countries")]
        [Authorize(Roles = "devops")]//VERIFY_ROLES
        public async Task<IActionResult> RemoveCountriesToBetrackedAsync([FromBody] CountryPayload body)
		{
			var result = await LinesETLAPI.LinesETL.PerformQryAsync(HttpContext, $@"
					{{
						countries = mapper.Countries;
						countries.Add('{body.Code}','{body.Name}');
					}}
				");
			return result;
		}
	}

	[DataContract(Name = "CountryPayload")]
	public class CountryPayload
	{
		[DataMember(Name = "code")]
		public string Code { get; set; }
		[DataMember(Name = "name")]
		public string Name { get; set; }
	}
	
}
