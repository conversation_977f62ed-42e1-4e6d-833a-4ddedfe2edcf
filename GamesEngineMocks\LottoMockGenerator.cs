﻿using GamesEngine;
using GamesEngine.Finance;
using GamesEngine.Settings;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Newtonsoft.Json;
using Puppeteer.EventSourcing;
using Puppeteer.EventSourcing.Libraries;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using town.connectors.drivers;
using static GamesEngine.Exchange.PaymentProcessorsAndActionsByDomains;
using static GamesEngine.Finance.PaymentChannels;
using static GamesEngineMocks.LottoMocks;
using static GamesEngineMocks.LottoMocks.GameTypes;

namespace GamesEngineMocks
{
	public class LottoMockGenerator : Mock
	{
		Dictionary<string, string> custommersById = new Dictionary<string, string>();
		public const string ACCOUNT_NUMBER_1 = "*********";
		public const string ACCOUNT_NUMBER_2 = "*********";

		public Puppeteer.EventSourcing.Actor actor;
		Actor actorBI;
		public IConfiguration Configuration { get; }

		private decimal funds = 80000;
		public struct Draw
		{
			public string State { get; set; }
			public string Date { get; set; }
		}

		public String Perform(String script)
		{
			string result;
			try
			{
#pragma warning disable VSTHRD002 // Avoid problematic synchronous waits
				result = actor.PerformCmdAsync(script, IpAddress.DEFAULT, UserInLog.ANONYMOUS).Result;
#pragma warning restore VSTHRD002 // Avoid problematic synchronous waits
				result = CheckForAssert(result);
			}
			catch (Exception e)
			{
				throw e.InnerException;
			}
			return result;
		}

		private class ApiError
		{
			public ApiError(string message, string stack, string comandoConError, string errorInfo)
			{
				this.message = message;
				this.stacktrace = stack;
				this.commandInfo = comandoConError;
				this.requestInfo = errorInfo;
			}
			public string commandInfo { get; set; }
			public string message { get; set; }
			public string type { get { return "error"; } }
			public string stacktrace { get; set; }
			public string requestInfo { get; set; }
		}

		public IActionResult PerformCmd(IpAddress ip, UserInLog user, HttpContext context, string script)
		{
			if (String.IsNullOrEmpty(script)) return new NotFoundErrorObjectResult();
			try
			{
#pragma warning disable VSTHRD002 // Avoid problematic synchronous waits
				string output = actor.PerformCmdAsync(script, ip, user).Result;
#pragma warning restore VSTHRD002 // Avoid problematic synchronous waits
				return new OkObjectResult(output);
			}
			catch (Exception e)
			{
				string message;
				if (e.InnerException == null)
					message = e.Message;
				else if (e.InnerException.InnerException != null)
					message = e.InnerException.InnerException.Message;
				else
					message = "no message";
				const string SERVER_ERROR_MESSAGE_TO_THE_END_USER = "An error has been detected. Our support team was notified and they are working on it.";
				try
				{
					string stack;
					if (e.StackTrace != null)
					{
						stack = e.StackTrace;
						if (e.InnerException != null)
						{
							stack = stack + e.InnerException.StackTrace;
							if (e.InnerException.InnerException != null)
							{
								stack = stack + e.InnerException.StackTrace + e.InnerException.InnerException.StackTrace;
							}
						}
					}
					else if (e.InnerException != null)
						stack = e.StackTrace + e.InnerException.StackTrace;
					else if (e.InnerException.InnerException != null)
						stack = e.StackTrace + e.InnerException.StackTrace + e.InnerException.InnerException.StackTrace;
					else
						stack = "no stack";
					
					var httpMethod = "no http";
					var httpPath = "";
					var httpParameters = "";
					if (context != null)
					{
						httpMethod = context.Request.Method;
						httpPath = context.Request.Path;
						if (context.Request.Body.CanSeek)
						{
							context.Request.Body.Position = 0;
						}
#pragma warning disable VSTHRD002 // Avoid problematic synchronous waits
						httpParameters = new StreamReader(context.Request.Body).ReadToEndAsync().Result;
#pragma warning restore VSTHRD002 // Avoid problematic synchronous waits
					}

					var errorInfo = $"Method:{httpMethod}, Path:{httpPath}, WithTheFollowingParameters:[{{{httpParameters}}}]";
					var comandoConError = actor.CommandLineError;
					ApiError error = new ApiError(message, stack, comandoConError, errorInfo);

					if (e.InnerException.InnerException is GameEngineException || e.InnerException.InnerException is ArgumentNullException)
					{
						var contentResult = new ContentResult
						{
							ContentType = "application/json",
							Content = JsonConvert.SerializeObject(error),
							StatusCode = 400
						};
						return new BadRequestObjectResult(contentResult);
					}
					else
					{
						error.message = SERVER_ERROR_MESSAGE_TO_THE_END_USER;
						var contentResult = new ContentResult
						{
							ContentType = "application/json",
							Content = JsonConvert.SerializeObject(error),
							StatusCode = 500
						};
						return new InternalServerErrorObjectResult(contentResult);
					}
				}
				catch
				{
					return new NotFoundErrorObjectResult();
				}
			}
		}

		public String PerformBI(String script)
        {
			string result;
			try
			{
#pragma warning disable VSTHRD002 // Avoid problematic synchronous waits
				result = actorBI.PerformCmdAsync(script, IpAddress.DEFAULT, UserInLog.ANONYMOUS).Result;
#pragma warning restore VSTHRD002 // Avoid problematic synchronous waits
				result = CheckForAssert(result);
			}
			catch (Exception e)
			{
				throw e.InnerException;
			}
			return result;
		}

		public LottoMockGenerator(Puppeteer.EventSourcing.Actor actor)
		{
			this.actor = actor;
			var directory = Directory.GetCurrentDirectory();
			if (!File.Exists(directory + "//appsettings.json"))
			{
				directory = Directory.GetCurrentDirectory() + "..\\..\\..\\..\\";

				if (!File.Exists(directory + "\\appsettings.json"))
				{
					directory = Directory.GetCurrentDirectory() + "\\..\\GamesEngineTests\\";

					if (!File.Exists(directory + "//appsettings.json"))
					{
						directory = Directory.GetCurrentDirectory() + "//..//..//..//";
					}
				}
			}

			var builder = new ConfigurationBuilder()
				.SetBasePath(directory)
				.AddJsonFile("appsettings.json", optional: false, reloadOnChange: true)
				.AddJsonFile("appsettings.json", optional: false, reloadOnChange: true)
				.AddJsonFile("customization/appsettings.accounting.json", optional: true)
				.AddJsonFile("customization/appsettings.security.json", optional: true)
				.AddJsonFile("customization/appsettings.kafka.json", optional: true)
				.AddJsonFile("customization/appsettings.error_emails.json", optional: true)
				.AddJsonFile("customization/appsettings.apm.json", optional: true)
				.AddJsonFile("secrets/appsettings.secrets.json", optional: true);
			Configuration = builder.Build();

			var biIntegration = Configuration.GetSection("BIIntegration");
			GamesEngine.Settings.Integration.Configure(KafkaMessage.Prefix.NoTransacction, biIntegration);

			Perform($@"
                company = Company();
				if (!company.System.Coins.ExistsIsoCode('FP'))
				{{
					coin = company.System.Coins.Add(0, 'FP', 'FP', 2, 'F', 'free play', {CoinType.Digital});
					coin.Visible = true;
					coin.Enabled = true;
				}}
				if (!company.System.Coins.ExistsIsoCode('USD'))
				{{
					coin = company.System.Coins.Add(2, 'USD', '$', 2, '$', 'dollar', {CoinType.Fiat});
					coin.Visible = true;
					coin.Enabled = true;
				}}
				if (!company.System.Coins.ExistsIsoCode('BTC'))
				{{
					company.System.Coins.Add(3, 'BTC', 'BTC', 8, '₿', 'bitcoin', {CoinType.Crypt});
				}}
				if (!company.System.Coins.ExistsIsoCode('ETH'))
				{{
					company.System.Coins.Add(7, 'ETH', 'ETH', 8, 'Ξ', 'ethereum', {CoinType.Crypt});
				}}

				company.Accounting = MockAccounting();
                companyIDf2401f84xa94ax4db3xaec4x83f5361730ae = company;
                Eval ('company' + company.Id + ' = company;');
				store = company.Sales.CreateStore(1,'Picks Store');
				store.Alias = 'lotto';
				store.MakeCurrent();
                localhostDomain = company.Sales.CreateDomain(false, 1, 'localhost', {Agents.INSIDER});
				company.Sales.CurrentStore.Add(localhostDomain);
				
				qaDomain = company.Sales.CreateDomain(false, 2, 'nodo.qatest.ncubo.com', {Agents.INSIDER});
                qaDomain.AddResourceUrl('logout','localhost/logout');
				company.Sales.CurrentStore.Add(qaDomain);

				marketplace = MarketPlace(company, 'CR');
				cartagoAgent = marketplace.AddAgent('Cartago');
				agent1 = marketplace.AddAgent('1');
				marketplace.AddAgent('San José');
				marketplace.AddAgent('Heredia');

				agent1.AssignToCurrentStore(localhostDomain);
				agent1.AssignToCurrentStore(qaDomain);
				agent1.Assign(7, 'Keno Store', localhostDomain);
				agent1.EnableDomain(store.Id, localhostDomain);
				agent1.EnableDomain(store.Id, qaDomain);

				customSettings = CustomSettingsCollection(company);
				{{
					company.System.Entities.Add(1, 'Apolo');
					artemisEntity = company.System.Entities.Add(2, 'Artemis');
					artemisEntity.Visible = true;
					artemisEntity.Enabled = true;
					zeusEntity = company.System.Entities.Add(3, 'Zeus');
					fieroEntity = company.System.Entities.Add(4, 'Fiero');
					fieroEntity.Visible = true;
					fieroEntity.Enabled = true;
					hadesEntity = company.System.Entities.Add(5, 'Hades');
					hadesEntity.Visible = true;
					hadesEntity.Enabled = true;
					consignmentEntity = company.System.Entities.Add(6, 'Consignment');
					consignmentEntity.Visible = true;
					consignmentEntity.Enabled = true;

					company.System.Tenants.Add(1, 'Apolo');
					artemisTenant = company.System.Tenants.Add(2, 'Artemis');
					zeusTenant = company.System.Tenants.Add(3, 'Zeus');
					insiderTenant = company.System.Tenants.Add(4, 'Insider');
					hadesTenant = company.System.Tenants.Add(5, 'Hades');
					hadesTenant.MakeCurrent();

					asiPM = company.System.PaymentMethods.Add(1, 'A.S.I.');
					dgsPM = company.System.PaymentMethods.Add(2, 'D.G.S.');
					dgsV2PM = company.System.PaymentMethods.Add(3, 'D.G.S. v2');
					credirCardPM = company.System.PaymentMethods.Add(4, 'Credit card');
					blockChainPM = company.System.PaymentMethods.Add(5, 'Blockchain');
					moneyTransferPM = company.System.PaymentMethods.Add(6, 'Money transfer');
					asiSimulatorPM = company.System.PaymentMethods.Add(7, 'A.S.I. Simulator');
					dsgSimulatorPM = company.System.PaymentMethods.Add(8, 'D.G.S. Simulator');
					pm = company.System.PaymentMethods.Add(9, '{PaymentMethod.Cash.ToString()}');
					pm.Visible = true;
					pm.Enabled = true;
					pm = company.System.PaymentMethods.Add(10, '{PaymentMethod.Bank.ToString()}');
					pm.Visible = true;
					pm.Enabled = true;
					pm = company.System.PaymentMethods.Add(11, '{PaymentMethod.Creditcard.ToString()}');
					pm.Visible = true;
					pm.Enabled = true;
					pm = company.System.PaymentMethods.Add(12, '{PaymentMethod.FinancialServices.ToString()}');
					pm.Visible = true;
					pm.Enabled = true;
					pm = company.System.PaymentMethods.Add(13, '{PaymentMethod.ThirdParty.ToString()}');
					pm.Visible = true;
					pm.Enabled = true;
					pm = company.System.PaymentMethods.Add(14, '{PaymentMethod.Secrets.ToString()}');
					pm.Visible = true;
					pm.Enabled = true;
					pm = company.System.PaymentMethods.Add(15, '{PaymentMethod.P2PTransfer.ToString()}');
					pm.Visible = true;
					pm.Enabled = true;
					pm.AddProvider(1,'MoneyGram');
					pm.AddProvider(2,'Rio Money Transfer');
					pm.AddProvider(3,'Sigue');
					pm.AddProvider(4,'Boss Revolution Money Transfer');
					pm.AddProvider(5,'Delgado Travel');
					pm.AddProvider(6,'Intermex International Money Transfer');
					pm.AddProvider(7,'Maxi Money Services');
					pm.AddProvider(8,'Barri Financial Group');
					pm.AddProvider(9,'La Nacional');
					pm.AddProvider(10,'Dinex');
					pm.AddProvider(11,'Remitly');

					depositTransactionType = company.System.TransactionTypes.Add(1, '{TransactionType.Deposit.ToString()}');
					depositTransactionType.Description = 'A sum of money placed in an account';
					withDrawalTransactionType = company.System.TransactionTypes.Add(2, '{TransactionType.Withdrawal.ToString()}');
					withDrawalTransactionType.Description = 'A sum of money take out of an account';
					transferTransactionType = company.System.TransactionTypes.Add(3, '{TransactionType.Transfer.ToString()}');
					transferTransactionType.Description = 'A sum of money is sent from one account to another';
					creditNoteTransactionType = company.System.TransactionTypes.Add(4, '{TransactionType.CreditNote.ToString()}');
					creditNoteTransactionType.Description = 'It is a document used by a vendor to inform the buyer of mistake on an order';
					debitNoteTransactionType = company.System.TransactionTypes.Add(5, '{TransactionType.DebitNote.ToString()}');
					debitNoteTransactionType.Description = 'It is a document used by a vendor to inform the buyer of current debt obligations';
					saleTransactionType = company.System.TransactionTypes.Add(6, '{TransactionType.Sale.ToString()}');
					saleTransactionType.Description = 'The exchange of a commodity for money';
					depositAndLockTransactionType = company.System.TransactionTypes.Add(7, '{TransactionType.Deposit.ToString()} then Lock');
					depositAndLockTransactionType.Description = 'Add and Lock money placed in an account';

					depositTransactionType.Visible = true;
					depositTransactionType.Enabled = true;
					withDrawalTransactionType.Visible = true;
					withDrawalTransactionType.Enabled = true;
					transferTransactionType.Visible = true;
					transferTransactionType.Enabled = true;
					debitNoteTransactionType.Visible = true;
					debitNoteTransactionType.Enabled = true;
					creditNoteTransactionType.Visible = true;
					creditNoteTransactionType.Enabled = true;
					saleTransactionType.Visible = true;
					saleTransactionType.Enabled = true;
					depositAndLockTransactionType.Visible = true;
					depositAndLockTransactionType.Enabled = true;

					cs = customSettings.AddFixedParameter(Now, 'CashierDriver.url', 'http://cashierapi:5000/'); 
					cs.Description = 'Url Cashier';
					cs = customSettings.AddFixedParameter(Now, 'CompanyBaseUrlServices', 'http://cashierapi:5000/'); 
					cs.Description = 'It is the base production url';
					cs = customSettings.AddFixedParameter(Now, 'TokenSystemId', '3aab83fb'); 
					cs.Description = 'App\'s token id';
					cs = customSettings.AddSecretParameter(Now, 'TokenSystemPassword', '38f845e977af7f2cd6313c5a9d35e4de7f353405100e7748ab15b410eb53d886');
					cs.Description = 'Password for token';
					cs = customSettings.AddFixedParameter(Now, 'SendWagers', true); 
					cs.Description = 'To send wagers to third party';
					cs = customSettings.AddFixedParameter(Now, 'CompanySystemId', 'N/A'); 
					cs.Description = 'company id';
					cs = customSettings.AddSecretParameter(Now, 'CompanySystemPassword', 'N/A'); 
					cs.Description = 'password';
					cs = customSettings.AddFixedParameter(Now, 'NeedsUniqueIdentifierForPaymentHub', 'PRODUCTION_DOES_NOT_NEED_IT'); 
					cs.Description = 'Setting';
					cs = customSettings.AddFixedParameter(Now, 'CompanyClerkId', 'Administrator'); 
					cs.Description = 'Clerk id';

					cs = customSettings.AddVariableParameter('Input$Provider'); 
					cs.Description = 'Consignment provider';
					cs = customSettings.AddVariableParameter('KYC$DOB'); 
					cs.Description = 'Birthdate from KYC';
					cs = customSettings.AddSecretParameter(Now, 'KYCUsername', 'testUser1');
					cs.Description = 'KYC Username for fields';
					cs = customSettings.AddSecretParameter(Now, 'KYCPassword', '12345');
					cs.Description = 'KYC Password for fields';
				}}

				company.IsAlreadyRegisteredTenantAndStore = true;
");
					
			CreatePlayers();

			Perform($@"
                lotto900 = company.Lotto900();
                Eval(lotto900.CreateVariablesForDairy());

                ticketPick3StraightProd = lotto900.GetOrCreateProductById(1);
                ticketPick3StraightProd.Description = 'Ticket pick 3 straight';
                ticketPick3StraightProd.Price = 1;

                ticketPick4StraightProd = lotto900.GetOrCreateProductById(2);
                ticketPick4StraightProd.Description = 'Ticket pick 4 straight';
                ticketPick4StraightProd.Price = 1;

                ticketPick3BoxedProd = lotto900.GetOrCreateProductById(3);
                ticketPick3BoxedProd.Description = 'Ticket pick 3 boxed';
                ticketPick3BoxedProd.Price = 1;

                ticketPick4BoxedProd = lotto900.GetOrCreateProductById(4);
                ticketPick4BoxedProd.Description = 'Ticket pick 4 boxed';
                ticketPick4BoxedProd.Price = 1;
              
                preset = lotto900.PresetBetAmountsByPick(3);
                preset.Add(0.25, 'Bart Simpson', Now);
                preset.Add(0.5, 'Bart Simpson', Now);
                preset.Add(1, 'Bart Simpson', Now);
                preset.Add(2, 'Bart Simpson', Now);
                preset.Add(3, 'Bart Simpson', Now);
                preset.Add(4, 'Bart Simpson', Now);
                preset.Add(5, 'Bart Simpson', Now);
                preset.Add(6, 'Bart Simpson', Now);
                preset.Add(7, 'Bart Simpson', Now);
                preset.Add(8, 'Bart Simpson', Now);
                preset.Add(9, 'Bart Simpson', Now);
                preset.Add(10, 'Bart Simpson', Now);
                preset.Add(15, 'Bart Simpson', Now);
                preset.Add(25, 'Bart Simpson', Now);
                preset.Add(50, 'Bart Simpson', Now);
                preset.Add(100, 'Bart Simpson', Now);

                preset = lotto900.PresetBetAmountsByPick(4);
                preset.Add(0.25, 'Bart Simpson', Now);
                preset.Add(0.5, 'Bart Simpson', Now);
                preset.Add(1, 'Bart Simpson', Now);
                preset.Add(2, 'Bart Simpson', Now);
                preset.Add(3, 'Bart Simpson', Now);
                preset.Add(4, 'Bart Simpson', Now);
                preset.Add(5, 'Bart Simpson', Now);
                preset.Add(6, 'Bart Simpson', Now);
                preset.Add(7, 'Bart Simpson', Now);
                preset.Add(8, 'Bart Simpson', Now);
                preset.Add(9, 'Bart Simpson', Now);
                preset.Add(10, 'Bart Simpson', Now);
                preset.Add(15, 'Bart Simpson', Now);
                preset.Add(25, 'Bart Simpson', Now);

                domain = company.Sales.DomainFrom('localhost');
                lotto900.BetRangesByPick(3).Add(Now, domain, 100, 0.25, 'Bart Simpson');
                lotto900.BetRangesByPick(4).Add(Now, domain, 25, 0.25, 'Bart Simpson');

                lotto900.PlayersProfiles.Profiles.Add(domain, 'Test profile');
                lotto900.PlayersProfiles.Profiles.Add(domain, 'Test profile 1');
                profile = lotto900.PlayersProfiles.Profiles.Profile('Test profile');
                lotto900.PlayersProfiles.Assign(Now,playerNO{custommersById.ElementAt(1).Key},profile);
                
                lotto900.PlayersProfiles.BetRangeProfilesByPick(3).Add(profile, domain, 50, 0.1, Now, 'Bart Simpson');
                lotto900.PlayersProfiles.BetRangeProfilesByPick(4).Add(profile, domain, 20, 0.1, Now, 'Bart Simpson');

                ticketPick2StraightProd = lotto900.GetOrCreateProductById(5);
                ticketPick2StraightProd.Description = 'Ticket pick 2 straight';
                ticketPick2StraightProd.Price = 1;

                ticketPick2BoxedProd = lotto900.GetOrCreateProductById(6);
                ticketPick2BoxedProd.Description = 'Ticket pick 2 boxed';
                ticketPick2BoxedProd.Price = 1;
                
                ticketPick5StraightProd = lotto900.GetOrCreateProductById(7);
                ticketPick5StraightProd.Description = 'Ticket pick 5 straight';
                ticketPick5StraightProd.Price = 1;

                ticketPick5BoxedProd = lotto900.GetOrCreateProductById(8);
                ticketPick5BoxedProd.Description = 'Ticket pick 5 boxed';
                ticketPick5BoxedProd.Price = 1;

                preset = lotto900.PresetBetAmountsByPick(2);
                preset.Add(0.25, 'Bart Simpson', Now);
                preset.Add(0.5, 'Bart Simpson', Now);
                preset.Add(1, 'Bart Simpson', Now);
                preset.Add(2, 'Bart Simpson', Now);
                preset.Add(3, 'Bart Simpson', Now);
                preset.Add(4, 'Bart Simpson', Now);
                preset.Add(5, 'Bart Simpson', Now);
                preset.Add(6, 'Bart Simpson', Now);
                preset.Add(7, 'Bart Simpson', Now);
                preset.Add(8, 'Bart Simpson', Now);
                preset.Add(9, 'Bart Simpson', Now);
                preset.Add(10, 'Bart Simpson', Now);
                preset.Add(15, 'Bart Simpson', Now);
                preset.Add(25, 'Bart Simpson', Now);
                preset.Add(50, 'Bart Simpson', Now);
                preset.Add(150, 'Bart Simpson', Now);

                preset = lotto900.PresetBetAmountsByPick(5);
                preset.Add(0.25, 'Bart Simpson', Now);
                preset.Add(0.5, 'Bart Simpson', Now);
                preset.Add(1, 'Bart Simpson', Now);
                preset.Add(2, 'Bart Simpson', Now);
                preset.Add(3, 'Bart Simpson', Now);
                preset.Add(4, 'Bart Simpson', Now);
                preset.Add(5, 'Bart Simpson', Now);
                preset.Add(6, 'Bart Simpson', Now);
                preset.Add(7, 'Bart Simpson', Now);
                preset.Add(8, 'Bart Simpson', Now);
                preset.Add(9, 'Bart Simpson', Now);
                preset.Add(10, 'Bart Simpson', Now);

                lotto900.BetRangesByPick(2).Add(Now, domain, 150, 0.25, 'Bart Simpson');
                lotto900.BetRangesByPick(5).Add(Now, domain, 10, 0.25, 'Bart Simpson');
                lotto900.PlayersProfiles.BetRangeProfilesByPick(2).Add(profile, domain, 150, 0.1, Now, 'Bart Simpson');
                lotto900.PlayersProfiles.BetRangeProfilesByPick(5).Add(profile, domain, 20, 0.1, Now, 'Bart Simpson');

                ticketPowerBallSingleProd = lotto900.GetOrCreateProductById(13);
                ticketPowerBallSingleProd.Description = 'Ticket Powerball without powerplay';
                ticketPowerBallSingleProd.Price = 1;

                ticketPowerBallPowerPlayProd = lotto900.GetOrCreateProductById(14);
                ticketPowerBallPowerPlayProd.Description = 'Ticket Powerball with powerplay';
                ticketPowerBallPowerPlayProd.Price = 1;

                betRangesPowerball = lotto900.BetRangesForPowerBall;
                betRangesPowerball.Add(Now, domain, 10, 0.25, 'Admin');

                presetPowerball = lotto900.PresetBetAmountsForPowerball;
                presetPowerball.Add(0.3, 'Admin', Now);
                presetPowerball.Add(0.6, 'Admin', Now);
                presetPowerball.Add(0.9, 'Admin', Now);
                presetPowerball.Add(1.2, 'Admin', Now);
                presetPowerball.Add(3, 'Admin', Now);
                presetPowerball.Add(4, 'Admin', Now);
                presetPowerball.Add(5, 'Admin', Now);
                presetPowerball.Add(6, 'Admin', Now);
                presetPowerball.Add(7, 'Admin', Now);
                presetPowerball.Add(8, 'Admin', Now);
                presetPowerball.Add(9, 'Admin', Now);
                presetPowerball.Add(10, 'Admin', Now);

                lotteryPowerball = lotto900.GetPowerball();
                lotteryPowerball.Every(22,59,3);lotteryPowerball.Every(22,59,6);

                presetTicketEntries = lotto900.PresetTicketEntriesPowerball;
                presetTicketEntries.Add(2);
                presetTicketEntries.Add(5);
                presetTicketEntries.Add(10);
                presetTicketEntries.Add(20);

                presetTicketEntries.DefaultNumberSelectedOfTicketEntry=5;
                presetTicketEntries.NumberMaximumOfTicketEntries=500;
				withoutExcludes = ExcludeSubtickets();
            ");
		}

		public LottoMockGenerator(Actor actor, string actorMock)
		{
			if (actorMock == "BI")
			{
				actorBI = actor;
				PerformBI($@"
                    company = Company();
					company.System.Coins.Add(0, 'FP', 'FP', 2, 'F', 'free play', {CoinType.Digital});
					company.System.Coins.Add(2, 'USD', '$', 2, '$', 'dollar', {CoinType.Fiat});
					company.System.Coins.Add(3, 'BTC', 'BTC', 8, '₿', 'bitcoin', {CoinType.Crypt});
					company.System.Coins.Add(7, 'ETH', 'ETH', 8, 'Ξ', 'ethereum', {CoinType.Crypt});

                    company.Accounting = MockAccounting();
                    companyIDf2401f84xa94ax4db3xaec4x83f5361730ae = company;
                    lotto900 = company.Lotto900();

                    customer1 = company.GetOrCreateCustomerById('NO*********');
                    playerNO********* = customer1.Player;

                    customer2 = company.GetOrCreateCustomerById('NO*********');
                    playerNO********* = customer2.Player;
					withoutExcludes = ExcludeSubtickets();

					marketplace = Marketplace(company, 'CR');
					cartagoAgent = marketplace.AddAgent('Cartago');
					agent1 = marketplace.AddAgent('1');
					marketplace.AddAgent('San José');
					marketplace.AddAgent('Heredia');
                ");
			}
			else throw new GameEngineException($"There is no implementation for {actorMock}");
		}

		public LottoMockGenerator GetLastUUIDdOf(string variable, out string ID)
		{
			string result = Perform($"print {variable}.Id 'id';");
			result = result.Substring(result.IndexOf(':') + 1);
			result = result.Substring(0, result.IndexOf('}'));
			ID = result;
			return this;
		}
		int identificationDocumentNumber = 0;
		public LottoMockGenerator CreatePlayers()
		{
			custommersById.Add(ACCOUNT_NUMBER_1, "ID012345678901234567890123456789012345");
			custommersById.Add(ACCOUNT_NUMBER_2, "iCDlytS7kCxVPfgc0uSVvfJDtkgoUtRUtOSb09HFuQk=");
			var agent = Agents.INSIDER;
			foreach (var accountById in custommersById)
			{
				identificationDocumentNumber += 1;
				StringBuilder linesToCreatePlayer = new StringBuilder();
				linesToCreatePlayer.Append($@"
                    custNO{accountById.Key} = company.CreateCustomer('NO{accountById.Key}',{agent});
					customer = custNO{accountById.Key};
					customer.AffiliateId = 1;
                    custNO{accountById.Key}.Name = 'Tester User';
					custNO{accountById.Key}.Identifier='10{identificationDocumentNumber}';
					custNO{accountById.Key}.AffiliateId = {identificationDocumentNumber};
                    playerNO{accountById.Key} = custNO{accountById.Key}.Player;
                    player = custNO{accountById.Key}.Player;
                    thePlayerItsNew = custNO{accountById.Key}.ItsNewOne();
                    if(thePlayerItsNew)
                    {{
                        custNO{accountById.Key}.ReplicateInOtherNodes(itIsThePresent, true, Now, 'NickName User', 'https://intranet.ncubo.com/ResourcesQA5/api/file/IDDefault/Lotto/Lotto/avatars/avatar2.png');
                    }}  
                ");
				var result = Perform(linesToCreatePlayer.ToString());
			}
			return this;
		}

		public LottoMockGenerator CreatePlayers(Dictionary<string, string> custommersById)
		{
			var agent = Agents.INSIDER;
			foreach (var accountById in custommersById)
			{
				identificationDocumentNumber += 1;
				StringBuilder linesToCreatePlayer = new StringBuilder();
				linesToCreatePlayer.Append($@"
                    cust{accountById.Key} = company.CreateCustomer('{accountById.Key}',{agent});
					customer = cust{accountById.Key};
					customer.AffiliateId = 1;
                    cust{accountById.Key}.Name = 'Tester User';
					cust{accountById.Key}.Identifier='10{identificationDocumentNumber}';
                    player = cust{accountById.Key}.Player;
                    thePlayerItsNew = cust{accountById.Key}.ItsNewOne();
                    if(thePlayerItsNew)
                    {{
                        cust{accountById.Key}.ReplicateInOtherNodes(itIsThePresent, true, Now, 'NickName User', 'https://intranet.ncubo.com/ResourcesQA5/api/file/IDDefault/Lotto/Lotto/avatars/avatar2.png');
                    }}  
                ");
				var result = Perform(linesToCreatePlayer.ToString());
			}
			return this;
		}

		public LottoMockGenerator CreatePlayer(string accountNumber, Agents agent)
		{
			identificationDocumentNumber += 1;
			StringBuilder linesToCreatePlayer = new StringBuilder();
			linesToCreatePlayer.Append($@"
                cust{accountNumber} = company.CreateCustomer('{accountNumber}',{agent});
				customer = cust{accountNumber};
				customer.AffiliateId = 1;
                cust{accountNumber}.Name = 'Tester User {identificationDocumentNumber}';
				cust{accountNumber}.Identifier='10{identificationDocumentNumber}';
                player = cust{accountNumber}.Player;
                thePlayerItsNew = cust{accountNumber}.ItsNewOne();
                if(thePlayerItsNew)
                {{
                    cust{accountNumber}.ReplicateInOtherNodes(itIsThePresent, true, Now, 'NickName User', 'https://intranet.ncubo.com/ResourcesQA5/api/file/IDDefault/Lotto/Lotto/avatars/avatar2.png');
                }}  
            ");
			var result = Perform(linesToCreatePlayer.ToString());
			
			return this;
		}

		private int cont = 0;
		public LottoMockGenerator CreateCustomer(string identificationDocumentNumber)
		{
			cont++;
			var agent = Agents.INSIDER;
			var result = $@"
				customer = company.CreateCustomer('******************{cont}',{agent});
				customer.AffiliateId = 1;
				customer.Identifier='{identificationDocumentNumber}';
				player = customer.Player;
				playerLM0Mz6pghATLiFYeIfXmeYwD2BdIb1ZxfcJcdR0ZbiagbGtLPW6l2yjHVa0gGxLl = player;
                ";
			Perform(result);
			return this;
		}

		public LottoMockGenerator LotteriesInTheMorning(int[] noSundays, int[] everyday)
		{
			string result = Perform($@"
                {ScriptScheduleLottery("TN", "pick4", 1, 15, noSundays, "TN Morning 10:15AM")} 
                {ScriptScheduleLottery("TN", "pick3", 1, 15, noSundays, "TN Morning 10:15AM")}
                {ScriptScheduleLottery("TX", "pick4", 1, 44, noSundays, "TX Morning 10:44AM")}
                {ScriptScheduleLottery("TX", "pick3", 1, 44, noSundays, "TX Morning 10:44AM")}
                {ScriptScheduleLottery("NY", "pick3", 2, 10, everyday, "NY Midday 12:10PM")}
                {ScriptScheduleLottery("NY", "pick4", 2, 10, everyday, "NY Midday 12:10PM")}
                {ScriptScheduleLottery("MD", "pick3", 2, 13, everyday, "MD Midday 12:13PM")}
                {ScriptScheduleLottery("MD", "pick4", 2, 13, everyday, "MD Midday 12:13PM")}
                {ScriptScheduleLottery("GA", "pick3", 2, 14, everyday, "GA Midday 12:14PM")}
                {ScriptScheduleLottery("GA", "pick4", 2, 14, everyday, "GA Midday 12:14PM")}
                {ScriptScheduleLottery("OH", "pick3", 2, 14, everyday, "OH Midday 12:14PM")}
                {ScriptScheduleLottery("OH", "pick4", 2, 14, everyday, "OH Midday 12:14PM")}
                {ScriptScheduleLottery("MI", "pick3", 2, 24, everyday, "MI Midday 12:24PM")}
                {ScriptScheduleLottery("MI", "pick4", 2, 24, everyday, "MI Midday 12:24PM")}
                {ScriptScheduleLottery("SC", "pick4", 2, 30, noSundays, "SC Midday 12:30PM")}
                {ScriptScheduleLottery("SC", "pick3", 2, 30, noSundays, "SC Midday 12:30PM")}
                {ScriptScheduleLottery("MA", "pick4", 2, 30, everyday, "MA Midday 12:30PM")}
                {ScriptScheduleLottery("NJ", "pick3", 2, 40, everyday, "NJ Midday 12:40PM")}
                {ScriptScheduleLottery("NJ", "pick4", 2, 40, everyday, "NJ Midday 12:40PM")}
                {ScriptScheduleLottery("VT", "pick4", 2, 50, everyday, "VT Midday 12:50PM")}
                {ScriptScheduleLottery("VT", "pick3", 2, 50, everyday, "VT Midday 12:50PM")}
                {ScriptScheduleLottery("NH", "pick3", 2, 50, everyday, "NH Midday 12:50PM")}
                {ScriptScheduleLottery("NH", "pick4", 2, 50, everyday, "NH Midday 12:50PM")}
                {ScriptScheduleLottery("PA", "pick4", 2, 50, everyday, "PA Midday 12:50PM")}
                {ScriptScheduleLottery("PA", "pick3", 2, 50, everyday, "PA Midday 12:50PM")}
                {ScriptScheduleLottery("FL", "pick3", 3, 00, everyday, "FL Midday 1:00PM")}
                {ScriptScheduleLottery("FL", "pick4", 3, 00, everyday, "FL Midday 1:00PM")}
                {ScriptScheduleLottery("IA", "pick3", 3, 00, everyday, "IA Midday 1:00PM")}
                {ScriptScheduleLottery("IA", "pick4", 3, 00, everyday, "IA Midday 1:00PM")}
                {ScriptScheduleLottery("IN", "pick4", 3, 00, everyday, "IN Midday 1:00PM")}
                {ScriptScheduleLottery("IN", "pick3", 3, 00, everyday, "IN Midday 1:00PM")}
                {ScriptScheduleLottery("TX", "pick4", 3, 10, noSundays, "TX Day 1:10PM")}
                {ScriptScheduleLottery("TX", "pick3", 3, 10, noSundays, "TX Day 1:10PM ")}
                {ScriptScheduleLottery("KY", "pick3", 3, 10, everyday, "KY Midday 1:10PM")}
                {ScriptScheduleLottery("KY", "pick4", 3, 10, everyday, "KY Midday 1:10PM")}
                {ScriptScheduleLottery("TN", "pick4", 3, 15, everyday, "TN Midday 1:15PM")}
                {ScriptScheduleLottery("TN", "pick3", 3, 15, everyday, "TN Midday 1:15PM")}
                {ScriptScheduleLottery("RI", "pick4", 3, 15, everyday, "RI Midday 1:15PM")}
                {ScriptScheduleLottery("DE", "pick3", 3, 25, everyday, "DE Midday 1:25PM")}
                {ScriptScheduleLottery("DE", "pick4", 3, 25, everyday, "DE Midday 1:25PM")}
                {ScriptScheduleLottery("IL", "pick3", 3, 25, everyday, "IL Midday 1:25PM")}
                {ScriptScheduleLottery("IL", "pick4", 3, 25, everyday, "IL Midday 1:25PM")}
                {ScriptScheduleLottery("VA", "pick4", 3, 38, everyday, "VA Midday 1:38PM")}
                {ScriptScheduleLottery("VA", "pick3", 3, 38, everyday, "VA Midday 1:38PM")}
                {ScriptScheduleLottery("MO", "pick3", 3, 40, everyday, "MO Midday 1:40PM")}
                {ScriptScheduleLottery("MO", "pick4", 3, 40, everyday, "MO Midday 1:40PM")}
                {ScriptScheduleLottery("AR", "pick3", 3, 44, noSundays, "AR Midday 1:44PM")}
                {ScriptScheduleLottery("AR", "pick4", 3, 44, noSundays, "AR Midday 1:44PM")}
                {ScriptScheduleLottery("CT", "pick3", 3, 45, everyday, "CT Midday 1:45PM")}
                {ScriptScheduleLottery("CT", "pick4", 3, 45, everyday, "CT Midday 1:45PM")}
                {ScriptScheduleLottery("DC", "pick3", 3, 45, everyday, "DC Midday 1:45PM")}
                {ScriptScheduleLottery("DC", "pick4", 3, 45, everyday, "DC Midday 1:45PM")}
                {ScriptScheduleLottery("ON", "pick3", 3, 45, everyday, "ON Midday 1:45PM")}
                {ScriptScheduleLottery("ON", "pick4", 3, 45, everyday, "ON Midday 1:45PM")}
                {ScriptScheduleLottery("KS", "pick3", 3, 55, everyday, "KS Midday 1:55PM")}
                {ScriptScheduleLottery("NC", "pick3", 4, 45, everyday, "NC Midday 2:45PM")}
                {ScriptScheduleLottery("NC", "pick4", 4, 45, everyday, "NC Midday 2:45PM")}
                {ScriptScheduleLottery("NM", "pick3", 4, 45, everyday, "NM Midday 2:45PM")}
                {ScriptScheduleLottery("CO", "pick3", 5, 10, everyday, "CO Midday 3:10PM")}
                {ScriptScheduleLottery("OR", "pick4", 5, 44, everyday, "OR (1pm) 3:44PM")}
                {ScriptScheduleLottery("ID", "pick3", 5, 44, everyday, "ID Midday 3:44PM")}
                {ScriptScheduleLottery("CA", "pick3", 5, 55, everyday, "CA Midday 3:55PM")}
                {ScriptScheduleLottery("RI", "pick4", 8, 15, everyday, "RI 6:15PM")}
                {ScriptScheduleLottery("PA", "pick4", 8, 25, everyday, "PA 6:25PM")}
                {ScriptScheduleLottery("PA", "pick3", 8, 25, everyday, "PA 6:25PM")}
                {ScriptScheduleLottery("SC", "pick4", 1, 30, everyday, "SC 6:30PM")}
                {ScriptScheduleLottery("SC", "pick3", 1, 30, everyday, "SC 6:30PM")}
                {ScriptScheduleLottery("WV", "pick4", 1, 34, everyday, "WV 6:34PM")}
                {ScriptScheduleLottery("WV", "pick3", 1, 34, everyday, "WV 6:34PM")}
                {ScriptScheduleLottery("VT", "pick4", 1, 40, everyday, "VT 6:40PM")}
                {ScriptScheduleLottery("VT", "pick3", 1, 40, everyday, "VT 6:40PM")}
                {ScriptScheduleLottery("NH", "pick3", 1, 40, everyday, "NH 6:40PM")}
                {ScriptScheduleLottery("NH", "pick4", 1, 40, everyday, "NH 6:40PM")}
                {ScriptScheduleLottery("MA", "pick4", 1, 42, everyday, "MA 6:42PM")}
                {ScriptScheduleLottery("TX", "pick4", 1, 43, everyday, "TX Evening 6:43PM")}
                {ScriptScheduleLottery("TX", "pick3", 1, 43, everyday, "TX Evening 6:43PM")}
                {ScriptScheduleLottery("GA", "pick3", 1, 44, everyday, "GA Evening 6:44PM")}
                {ScriptScheduleLottery("GA", "pick4", 1, 44, everyday, "GA Evening 6:44PM")}
                {ScriptScheduleLottery("OR", "pick4", 1, 44, everyday, "OR (4pm) 6:44PM")} // verificar
                {ScriptScheduleLottery("MI", "pick4", 1, 53, everyday, "MI 6:53PM")}
                {ScriptScheduleLottery("MI", "pick3", 1, 53, everyday, "MI 6:53PM")}
                {ScriptScheduleLottery("DE", "pick3", 1, 00, everyday, "DE 7:00PM")}
                {ScriptScheduleLottery("DE", "pick4", 1, 00, everyday, "DE 7:00PM")}
                {ScriptScheduleLottery("WI", "pick3", 1, 00, everyday, "WI 7:00PM")}
                {ScriptScheduleLottery("WI", "pick4", 1, 00, everyday, "WI 7:00PM")}
                {ScriptScheduleLottery("MN", "pick3", 1, 10, everyday, "MN 7:10PM")}
                {ScriptScheduleLottery("OH", "pick3", 1, 14, everyday, "OH 7:14PM")}
                {ScriptScheduleLottery("OH", "pick4", 1, 14, everyday, "OH 7:14PM")}
                {ScriptScheduleLottery("TN", "pick3", 3, 25, everyday, "TN Evening 7:15PM")}
                {ScriptScheduleLottery("TN", "pick4", 3, 25, everyday, "TN Evening 7:15PM")}
                {ScriptScheduleLottery("NY", "pick3", 1, 20, everyday, "NY 7:20PM")}
                {ScriptScheduleLottery("NY", "pick4", 1, 20, everyday, "NY 7:20PM")}
                {ScriptScheduleLottery("FL", "pick3", 1, 30, everyday, "FL 7:30PM")}
                {ScriptScheduleLottery("FL", "pick4", 1, 30, everyday, "FL 7:30PM")}
                {ScriptScheduleLottery("NJ", "pick3", 1, 40, everyday, "NJ 7:40PM")}
                {ScriptScheduleLottery("NJ", "pick4", 1, 40, everyday, "NJ 7:40PM")}
                {ScriptScheduleLottery("MD", "pick3", 1, 41, everyday, "MD 7:41PM")}
                {ScriptScheduleLottery("MD", "pick4", 1, 41, everyday, "MD 7:41PM")}
                {ScriptScheduleLottery("AR", "pick3", 1, 44, everyday, "AR 7:44PM")}
                {ScriptScheduleLottery("AR", "pick4", 1, 44, everyday, "AR 7:44PM")}
                {ScriptScheduleLottery("DC", "pick3", 1, 45, everyday, "DC 7:45PM")}
                {ScriptScheduleLottery("DC", "pick4", 1, 45, everyday, "DC 7:45PM")}
                {ScriptScheduleLottery("WC", "pick3", 2, 50, everyday, "W.Canada 8:50PM")}
                {ScriptScheduleLottery("QC", "pick3", 2, 50, everyday, "QC 8:50PM")}
                {ScriptScheduleLottery("QC", "pick4", 2, 50, everyday, "QC 8:50PM")}
                {ScriptScheduleLottery("IA", "pick3", 2, 00, everyday, "IA Evening 9:00PM")}
                {ScriptScheduleLottery("IA", "pick4", 2, 00, everyday, "IA Evening 9:00PM")}
                {ScriptScheduleLottery("NE", "pick3", 2, 15, everyday, "NE 9:15PM")}
                {ScriptScheduleLottery("CO", "pick3", 2, 21, everyday, "CO 9:21 PM")}
                {ScriptScheduleLottery("CA", "pick3", 2, 25, everyday, "CA 9:25PM")}
                {ScriptScheduleLottery("CA", "pick4", 2, 25, everyday, "CA 9:25PM")}
                {ScriptScheduleLottery("IN", "pick4", 2, 30, everyday, "IN 9:30PM")}
                {ScriptScheduleLottery("IN", "pick3", 2, 30, everyday, "IN 9:30PM")}
                {ScriptScheduleLottery("MO", "pick3", 2, 40, everyday, "MO 9:40PM")}
                {ScriptScheduleLottery("MO", "pick4", 2, 40, everyday, "MO 9:40PM")}
                {ScriptScheduleLottery("OR", "pick4", 2, 44, everyday, "OR (7pm) 9:44PM")} // verificar
                {ScriptScheduleLottery("ID", "pick3", 2, 45, everyday, "ID 9:45PM")}
                {ScriptScheduleLottery("OK", "pick3", 2, 50, everyday, "OK 9:50PM")}
                {ScriptScheduleLottery("AZ", "pick3", 2, 54, everyday, "AZ 9:54PM")}
                {ScriptScheduleLottery("KS", "pick3", 2, 55, everyday, "KS 9:55PM")}
                {ScriptScheduleLottery("IL", "pick4", 2, 05, everyday, "IL 10:05PM")}
                {ScriptScheduleLottery("IL", "pick3", 2, 05, everyday, "IL 10:05PM")}
                {ScriptScheduleLottery("CT", "pick3", 2, 14, everyday, "CT 10:14PM")}
                {ScriptScheduleLottery("CT", "pick4", 2, 14, everyday, "CT 10:14PM")}
                {ScriptScheduleLottery("ON", "pick3", 2, 15, everyday, "ON 10:15PM")}
                {ScriptScheduleLottery("ON", "pick4", 2, 15, everyday, "ON 10:15PM")}
                {ScriptScheduleLottery("LA", "pick3", 2, 25, everyday, "LA 10:25PM")}
                {ScriptScheduleLottery("LA", "pick4", 2, 25, everyday, "LA 10:25PM")}
                {ScriptScheduleLottery("WA", "pick3", 2, 30, everyday, "WA 10:30PM")}
                {ScriptScheduleLottery("VA", "pick4", 2, 30, everyday, "VA 10:30PM")}
                {ScriptScheduleLottery("VA", "pick3", 2, 30, everyday, "VA 10:30PM")}
                {ScriptScheduleLottery("KY", "pick3", 22, 45, everyday, "KY 10:45PM")}
                {ScriptScheduleLottery("KY", "pick4", 22, 45, everyday, "KY 10:45PM")}
                {ScriptScheduleLottery("TX", "pick4", 22, 55, everyday, "TX Night 10:55PM")}
                {ScriptScheduleLottery("TX", "pick3", 22, 55, everyday, "TX Night 10:55PM")}
                {ScriptScheduleLottery("NC", "pick3", 23, 07, everyday, "NC 11:07PM")}
                {ScriptScheduleLottery("NC", "pick4", 23, 07, everyday, "NC 11:07PM")}
                {ScriptScheduleLottery("NM", "pick3", 23, 15, everyday, "NM 11:15PM")}
                {ScriptScheduleLottery("GA", "pick3", 23, 19, everyday, "GA Night 11:19PM")}
                {ScriptScheduleLottery("GA", "pick4", 23, 19, everyday, "GA Night 11:19PM")}
                {ScriptScheduleLottery("OR", "pick4", 23, 59, everyday, "OR (10pm) 11:59PM")}
             ");
			return this;
		}

		public LottoMockGenerator ScheduleDefaultLotteries(int[] noSundays, int[] everyday, params int[] powerballDays)
		{
			Perform($@"
                {ScriptScheduleLottery("TN", "pick4", 10, 15, noSundays, "TN Morning 10:15AM")} 
                {ScriptScheduleLottery("TN", "pick3", 10, 15, noSundays, "TN Morning 10:15AM")}
                {ScriptScheduleLottery("TX", "pick4", 10, 44, noSundays, "TX Morning 10:44AM")}
                {ScriptScheduleLottery("TX", "pick3", 10, 44, noSundays, "TX Morning 10:44AM")}
                {ScriptScheduleLottery("NY", "pick3", 12, 10, everyday, "NY Midday 12:10PM")}
                {ScriptScheduleLottery("NY", "pick4", 12, 10, everyday, "NY Midday 12:10PM")}
                {ScriptScheduleLottery("MD", "pick3", 12, 13, everyday, "MD Midday 12:13PM")}
                {ScriptScheduleLottery("MD", "pick4", 12, 13, everyday, "MD Midday 12:13PM")}
                {ScriptScheduleLottery("GA", "pick3", 12, 14, everyday, "GA Midday 12:14PM")}
                {ScriptScheduleLottery("GA", "pick4", 12, 14, everyday, "GA Midday 12:14PM")}
                {ScriptScheduleLottery("OH", "pick3", 12, 14, everyday, "OH Midday 12:14PM")}
                {ScriptScheduleLottery("OH", "pick4", 12, 14, everyday, "OH Midday 12:14PM")}
                {ScriptScheduleLottery("MI", "pick3", 12, 24, everyday, "MI Midday 12:24PM")}
                {ScriptScheduleLottery("MI", "pick4", 12, 24, everyday, "MI Midday 12:24PM")}
                {ScriptScheduleLottery("SC", "pick4", 12, 30, noSundays, "SC Midday 12:30PM")}
                {ScriptScheduleLottery("SC", "pick3", 12, 30, noSundays, "SC Midday 12:30PM")}
                {ScriptScheduleLottery("MA", "pick4", 12, 30, everyday, "MA Midday 12:30PM")}
                {ScriptScheduleLottery("NJ", "pick3", 12, 40, everyday, "NJ Midday 12:40PM")}
                {ScriptScheduleLottery("NJ", "pick4", 12, 40, everyday, "NJ Midday 12:40PM")}
                {ScriptScheduleLottery("VT", "pick4", 12, 50, everyday, "VT Midday 12:50PM")}
                {ScriptScheduleLottery("VT", "pick3", 12, 50, everyday, "VT Midday 12:50PM")}
                {ScriptScheduleLottery("NH", "pick3", 12, 50, everyday, "NH Midday 12:50PM")}
                {ScriptScheduleLottery("NH", "pick4", 12, 50, everyday, "NH Midday 12:50PM")}
                {ScriptScheduleLottery("PA", "pick4", 12, 50, everyday, "PA Midday 12:50PM")}
                {ScriptScheduleLottery("PA", "pick3", 12, 50, everyday, "PA Midday 12:50PM")}
                {ScriptScheduleLottery("FL", "pick3", 13, 00, everyday, "FL Midday 1:00PM")}
                {ScriptScheduleLottery("FL", "pick4", 13, 00, everyday, "FL Midday 1:00PM")}
                {ScriptScheduleLottery("IA", "pick3", 13, 00, everyday, "IA Midday 1:00PM")}
                {ScriptScheduleLottery("IA", "pick4", 13, 00, everyday, "IA Midday 1:00PM")}
                {ScriptScheduleLottery("IN", "pick4", 13, 00, everyday, "IN Midday 1:00PM")}
                {ScriptScheduleLottery("IN", "pick3", 13, 00, everyday, "IN Midday 1:00PM")}
                {ScriptScheduleLottery("TX", "pick4", 13, 10, noSundays, "TX Day 1:10PM")} 
                {ScriptScheduleLottery("TX", "pick3", 13, 10, noSundays, "TX Day 1:10PM ")}
                {ScriptScheduleLottery("KY", "pick3", 13, 10, everyday, "KY Midday 1:10PM")}
                {ScriptScheduleLottery("KY", "pick4", 13, 10, everyday, "KY Midday 1:10PM")}
                {ScriptScheduleLottery("TN", "pick4", 13, 15, noSundays, "TN Midday 1:15PM")}
                {ScriptScheduleLottery("TN", "pick3", 13, 15, noSundays, "TN Midday 1:15PM")}
                {ScriptScheduleLottery("RI", "pick4", 13, 15, everyday, "RI Midday 1:15PM")}
                {ScriptScheduleLottery("DE", "pick3", 13, 25, noSundays, "DE Midday 1:25PM")}
                {ScriptScheduleLottery("DE", "pick4", 13, 25, noSundays, "DE Midday 1:25PM")}
                {ScriptScheduleLottery("IL", "pick3", 13, 25, everyday, "IL Midday 1:25PM")}
                {ScriptScheduleLottery("IL", "pick4", 13, 25, everyday, "IL Midday 1:25PM")}
                {ScriptScheduleLottery("VA", "pick4", 13, 38, everyday, "VA Midday 1:38PM")}
                {ScriptScheduleLottery("VA", "pick3", 13, 38, everyday, "VA Midday 1:38PM")}
                {ScriptScheduleLottery("MO", "pick3", 13, 40, everyday, "MO Midday 1:40PM")}
                {ScriptScheduleLottery("MO", "pick4", 13, 40, everyday, "MO Midday 1:40PM")}
                {ScriptScheduleLottery("AR", "pick3", 13, 44, noSundays, "AR Midday 1:44PM")}
                {ScriptScheduleLottery("AR", "pick4", 13, 44, noSundays, "AR Midday 1:44PM")}
                {ScriptScheduleLottery("CT", "pick3", 13, 45, everyday, "CT Midday 1:45PM")}
                {ScriptScheduleLottery("CT", "pick4", 13, 45, everyday, "CT Midday 1:45PM")}
                {ScriptScheduleLottery("DC", "pick3", 13, 45, everyday, "DC Midday 1:45PM")}
                {ScriptScheduleLottery("DC", "pick4", 13, 45, everyday, "DC Midday 1:45PM")}
                {ScriptScheduleLottery("ON", "pick3", 13, 45, everyday, "ON Midday 1:45PM")}
                {ScriptScheduleLottery("ON", "pick4", 13, 45, everyday, "ON Midday 1:45PM")}
                {ScriptScheduleLottery("KS", "pick3", 13, 55, everyday, "KS Midday 1:55PM")}
                {ScriptScheduleLottery("NC", "pick3", 14, 45, everyday, "NC Midday 2:45PM")}
                {ScriptScheduleLottery("NC", "pick4", 14, 45, everyday, "NC Midday 2:45PM")}
                {ScriptScheduleLottery("NM", "pick3", 14, 45, everyday, "NM Midday 2:45PM")}
                {ScriptScheduleLottery("CO", "pick3", 15, 10, everyday, "CO Midday 3:10PM")}
                {ScriptScheduleLottery("OR", "pick4", 15, 44, everyday, "OR (1pm) 3:44PM")}
                {ScriptScheduleLottery("ID", "pick3", 15, 44, everyday, "ID Midday 3:44PM")}
                {ScriptScheduleLottery("CA", "pick3", 15, 55, everyday, "CA Midday 3:55PM")}
                {ScriptScheduleLottery("RI", "pick4", 18, 15, everyday, "RI 6:15PM")}
                {ScriptScheduleLottery("PA", "pick4", 18, 25, everyday, "PA 6:25PM")}
                {ScriptScheduleLottery("PA", "pick3", 18, 25, everyday, "PA 6:25PM")}
                {ScriptScheduleLottery("SC", "pick4", 18, 30, everyday, "SC 6:30PM")}
                {ScriptScheduleLottery("SC", "pick3", 18, 30, everyday, "SC 6:30PM")}
                {ScriptScheduleLottery("WV", "pick4", 18, 34, noSundays, "WV 6:34PM")} // no domingos?
                {ScriptScheduleLottery("WV", "pick3", 18, 34, noSundays, "WV 6:34PM")} // no domingos?
             ");
			Perform($@"
                {ScriptScheduleLottery("VT", "pick4", 18, 40, everyday, "VT 6:40PM")}
                {ScriptScheduleLottery("VT", "pick3", 18, 40, everyday, "VT 6:40PM")}
                {ScriptScheduleLottery("NH", "pick3", 18, 40, everyday, "NH 6:40PM")}
                {ScriptScheduleLottery("NH", "pick4", 18, 40, everyday, "NH 6:40PM")}
                {ScriptScheduleLottery("MA", "pick4", 18, 42, everyday, "MA 6:42PM")}
                {ScriptScheduleLottery("TX", "pick4", 18, 43, noSundays, "TX Evening 6:43PM")} // no domingos?
                {ScriptScheduleLottery("TX", "pick3", 18, 43, noSundays, "TX Evening 6:43PM")} // no domingos?
                {ScriptScheduleLottery("GA", "pick3", 18, 44, everyday, "GA Evening 6:44PM")}
                {ScriptScheduleLottery("GA", "pick4", 18, 44, everyday, "GA Evening 6:44PM")}
                {ScriptScheduleLottery("OR", "pick4", 18, 44, everyday, "OR (4pm) 6:44PM")} // verificar
                {ScriptScheduleLottery("MI", "pick4", 18, 53, everyday, "MI 6:53PM")}
                {ScriptScheduleLottery("MI", "pick3", 18, 53, everyday, "MI 6:53PM")}
                {ScriptScheduleLottery("DE", "pick3", 19, 00, everyday, "DE 7:00PM")}
                {ScriptScheduleLottery("DE", "pick4", 19, 00, everyday, "DE 7:00PM")}
                {ScriptScheduleLottery("WI", "pick3", 19, 00, everyday, "WI 7:00PM")}
                {ScriptScheduleLottery("WI", "pick4", 19, 00, everyday, "WI 7:00PM")}
                {ScriptScheduleLottery("MN", "pick3", 19, 10, everyday, "MN 7:10PM")}
                {ScriptScheduleLottery("OH", "pick3", 19, 14, everyday, "OH 7:14PM")}
                {ScriptScheduleLottery("OH", "pick4", 19, 14, everyday, "OH 7:14PM")}
                {ScriptScheduleLottery("TN", "pick3", 19, 15, everyday, "TN Evening 7:15PM")}
                {ScriptScheduleLottery("TN", "pick4", 19, 15, everyday, "TN Evening 7:15PM")}
                {ScriptScheduleLottery("NY", "pick3", 19, 20, everyday, "NY 7:20PM")}
                {ScriptScheduleLottery("NY", "pick4", 19, 20, everyday, "NY 7:20PM")}
                {ScriptScheduleLottery("FL", "pick3", 19, 30, everyday, "FL 7:30PM")}
                {ScriptScheduleLottery("FL", "pick4", 19, 30, everyday, "FL 7:30PM")}
                {ScriptScheduleLottery("NJ", "pick3", 19, 40, everyday, "NJ 7:40PM")}
                {ScriptScheduleLottery("NJ", "pick4", 19, 40, everyday, "NJ 7:40PM")}
                {ScriptScheduleLottery("MD", "pick3", 19, 41, everyday, "MD 7:41PM")}
                {ScriptScheduleLottery("MD", "pick4", 19, 41, everyday, "MD 7:41PM")}
                {ScriptScheduleLottery("AR", "pick3", 19, 44, everyday, "AR 7:44PM")}
                {ScriptScheduleLottery("AR", "pick4", 19, 44, everyday, "AR 7:44PM")}
                {ScriptScheduleLottery("DC", "pick3", 19, 45, everyday, "DC 7:45PM")}
                {ScriptScheduleLottery("DC", "pick4", 19, 45, everyday, "DC 7:45PM")}
                {ScriptScheduleLottery("WC", "pick3", 20, 50, everyday, "W.Canada 8:50PM")}
                {ScriptScheduleLottery("QC", "pick3", 20, 50, everyday, "QC 8:50PM")}
                {ScriptScheduleLottery("QC", "pick4", 20, 50, everyday, "QC 8:50PM")}
                {ScriptScheduleLottery("IA", "pick3", 21, 00, everyday, "IA Evening 9:00PM")}
                {ScriptScheduleLottery("IA", "pick4", 21, 00, everyday, "IA Evening 9:00PM")}
                {ScriptScheduleLottery("NE", "pick3", 21, 15, noSundays, "NE 9:15PM")} // no domingos?
                {ScriptScheduleLottery("CO", "pick3", 21, 21, everyday, "CO 9:21 PM")}
                {ScriptScheduleLottery("CA", "pick3", 21, 25, everyday, "CA 9:25PM")}
                {ScriptScheduleLottery("CA", "pick4", 21, 25, everyday, "CA 9:25PM")}
                {ScriptScheduleLottery("IN", "pick4", 21, 30, everyday, "IN 9:30PM")}
                {ScriptScheduleLottery("IN", "pick3", 21, 30, everyday, "IN 9:30PM")}
                {ScriptScheduleLottery("MO", "pick3", 21, 40, everyday, "MO 9:40PM")}
                {ScriptScheduleLottery("MO", "pick4", 21, 40, everyday, "MO 9:40PM")}
                {ScriptScheduleLottery("OR", "pick4", 21, 44, everyday, "OR (7pm) 9:44PM")} // verificar
                {ScriptScheduleLottery("ID", "pick3", 21, 45, everyday, "ID 9:45PM")}
                {ScriptScheduleLottery("OK", "pick3", 21, 50, everyday, "OK 9:50PM")}
                {ScriptScheduleLottery("AZ", "pick3", 21, 54, noSundays, "AZ 9:54PM")} // no domingos?
                {ScriptScheduleLottery("KS", "pick3", 21, 55, everyday, "KS 9:55PM")}
                {ScriptScheduleLottery("IL", "pick4", 22, 05, everyday, "IL 10:05PM")}
                {ScriptScheduleLottery("IL", "pick3", 22, 05, everyday, "IL 10:05PM")}
                {ScriptScheduleLottery("CT", "pick3", 22, 14, everyday, "CT 10:14PM")}
                {ScriptScheduleLottery("CT", "pick4", 22, 14, everyday, "CT 10:14PM")}
                {ScriptScheduleLottery("ON", "pick3", 22, 15, everyday, "ON 10:15PM")}
                {ScriptScheduleLottery("ON", "pick4", 22, 15, everyday, "ON 10:15PM")}
                {ScriptScheduleLottery("LA", "pick3", 22, 25, everyday, "LA 10:25PM")}
                {ScriptScheduleLottery("LA", "pick4", 22, 25, everyday, "LA 10:25PM")}
                {ScriptScheduleLottery("WA", "pick3", 22, 30, everyday, "WA 10:30PM")}
                {ScriptScheduleLottery("VA", "pick4", 22, 30, everyday, "VA 10:30PM")}
                {ScriptScheduleLottery("VA", "pick3", 22, 30, everyday, "VA 10:30PM")}
                {ScriptScheduleLottery("KY", "pick3", 22, 45, everyday, "KY 10:45PM")}
                {ScriptScheduleLottery("KY", "pick4", 22, 45, everyday, "KY 10:45PM")}
                {ScriptScheduleLottery("TX", "pick4", 22, 55, noSundays, "TX Night 10:55PM")} // no domingos?
                {ScriptScheduleLottery("TX", "pick3", 22, 55, noSundays, "TX Night 10:55PM")} // no domingos?
                {ScriptScheduleLottery("NC", "pick3", 23, 07, everyday, "NC 11:07PM")}
                {ScriptScheduleLottery("NC", "pick4", 23, 07, everyday, "NC 11:07PM")}
                {ScriptScheduleLottery("NM", "pick3", 23, 15, everyday, "NM 11:15PM")}
                {ScriptScheduleLottery("GA", "pick3", 23, 19, everyday, "GA Night 11:19PM")}
                {ScriptScheduleLottery("GA", "pick4", 23, 19, everyday, "GA Night 11:19PM")}    
                {ScriptScheduleLottery("OR", "pick4", 23, 59, everyday, "OR (10pm) 11:59PM")}
                
                {ScriptScheduleLottery("DC", "pick2", 20, 35, everyday, "DC - Evening 8:35 PM")}
                {ScriptScheduleLottery("DC", "pick2", 14, 20, everyday, "DC - Day 2:20 PM")}
                {ScriptScheduleLottery("DC", "pick5", 20, 35, everyday, "DC - Evening 8:35 PM")}
                {ScriptScheduleLottery("DC", "pick5", 14, 20, everyday, "DC - Day 2:20 PM")}
                {ScriptScheduleLottery("FL", "pick2", 22, 45, everyday, "FL - Evening 10:45 PM")}
                {ScriptScheduleLottery("FL", "pick2", 13, 30, everyday, "FL - Midday 1:30 PM")}
                {ScriptScheduleLottery("FL", "pick5", 13, 30, everyday, "FL - Midday 1:30 PM")}
                {ScriptScheduleLottery("FL", "pick5", 22, 45, everyday, "FL - Evening 10:45 PM")}
                {ScriptScheduleLottery("OH", "pick5", 19, 29, everyday, "OH - Evening 7:29 PM")}
                {ScriptScheduleLottery("OH", "pick5", 12, 29, everyday, "OH - Midday 12:29 PM")}
                {ScriptScheduleLottery("ON", "pick2", 14, 00, everyday, "ON - Midday 2:00 PM")}
                {ScriptScheduleLottery("ON", "pick2", 22, 30, everyday, "ON - Evening 10:30 PM")}
                {ScriptScheduleLottery("PA", "pick2", 18, 59, everyday, "PA - Evening 6:59 PM")}
                {ScriptScheduleLottery("PA", "pick2", 13, 35, everyday, "PA - Midday 1:35 PM")}
                {ScriptScheduleLottery("PA", "pick5", 18, 59, everyday, "PA - Evening  6:59 PM")}
                {ScriptScheduleLottery("PA", "pick5", 13, 35, everyday, "PA - Midday 1:35 PM")}
             ");
			return this;
		}

		public LottoMockGenerator AddDrawPreferenceForPlayer(string game, string state, string playerId, string[] dates)
		{
			var linesToUpdateSchedule = "";
			foreach (var stringDate in dates)
			{
				linesToUpdateSchedule += $"state=state{state};";
				linesToUpdateSchedule += game == "pick3" ? $"lottery = lotto900.GetOrCreateLottery(3, state);" : $"lottery = lotto900.GetOrCreateLottery(4, state);";
				linesToUpdateSchedule += $"schedule = lottery.FindScheduleAt({ stringDate});";
				linesToUpdateSchedule += $"preferences.AddDraw(state,schedule);";
			}
#pragma warning disable VSTHRD002 // Avoid problematic synchronous waits
			var x = actor.PerformCmdAsync($@"
            {{
                preferences = playerNO{playerId}.LottoPreferences();
                {linesToUpdateSchedule}
                preferences.Enable('Draws');
            }}
            ", IpAddress.DEFAULT, UserInLog.ANONYMOUS).Result;
#pragma warning restore VSTHRD002 // Avoid problematic synchronous waits
			return this;
		}

		public LottoMockGenerator ScheduleLottery(string state, string game, int hour, int minutes, int[] daysOfWeek, string description)
		{
			string result = Perform($@"
                {ScriptScheduleLottery(state, game, hour, minutes, daysOfWeek, description)}
            ");
			return this;
		}

		private string ScriptToCreateSchedule(string state, int pickNumber, int hour, int minutes, int[] daysOfWeek, string description)
		{
			string lineToGetLottery = "";
			switch (pickNumber)
			{
				case 2:
					lineToGetLottery = $"lottery = lotto900.GetOrCreateLottery(2, state{state});";
					break;
				case 3:
					lineToGetLottery = $"lottery = lotto900.GetOrCreateLottery(3, state{state});";
					break;
				case 4:
					lineToGetLottery = $"lottery = lotto900.GetOrCreateLottery(4, state{state});";
					break;
				case 5:
					lineToGetLottery = $"lottery = lotto900.GetOrCreateLottery(5, state{state});";
					break;
				default:
					throw new GameEngineException("The pick does not exist");
			}

			string linesForEveryDate = "";
			foreach (int dayOfWeek in daysOfWeek)
			{
				linesForEveryDate += $"lottery.Every({hour},{minutes},{dayOfWeek}, Now, 'Games Admin');";
			}
			string script = $@"
                {lineToGetLottery}
                {linesForEveryDate}
                schedule = lottery.GetSchedule({hour},{minutes});
                schedule.UpdateDescription(itIsThePresent, '{description}');
            ";
			return script;
		}

		private string ScriptScheduleLottery(string state, string game, int hour, int minutes, int[] daysOfWeek, string description)
		{
			string lineToGetLottery = "";
			if (game.Equals("pick2"))
			{
				lineToGetLottery = $"lottery = lotto900.GetOrCreateLottery(2, state{state});";
			}
			else if (game.Equals("pick3"))
			{
				lineToGetLottery = $"lottery = lotto900.GetOrCreateLottery(3, state{state});";
			}
			else if (game.Equals("pick4"))
			{
				lineToGetLottery = $"lottery = lotto900.GetOrCreateLottery(4, state{state});";
			}
			else if (game.Equals("pick5"))
			{
				lineToGetLottery = $"lottery = lotto900.GetOrCreateLottery(5, state{state});";
			}
			else if (game.Equals("powerball"))
			{
				lineToGetLottery = $"lottery = lotto900.GetPowerball();";
			}
			else if (game.Equals("keno"))
			{
				lineToGetLottery = $"lottery = lotto900.GetKeno();";
			}
			else
			{
				throw new GameEngineException("The pick does not exist");
			}

			StringBuilder linesForEveryDateAndUpdateDescription = new StringBuilder();
			foreach (int dayOfWeek in daysOfWeek)
			{
				linesForEveryDateAndUpdateDescription.Append($"lottery.Every({hour},{minutes},{dayOfWeek});");
			}
			if (game != "powerball")
			{
				linesForEveryDateAndUpdateDescription.Append($"schedule = lottery.GetSchedule({hour},{minutes});");
				linesForEveryDateAndUpdateDescription.Append($"schedule.UpdateDescription(itIsThePresent, '{description}');");
			}
			string script = $@"
                {lineToGetLottery}
                {linesForEveryDateAndUpdateDescription}
            ";
			return script;
		}

		public LottoMockGenerator CreateWagers(int lowestBetId, int theHighestBetId, int lowWagerNumber, int amountOfWagerNumbers, int ticketNumber, int orderNumber)
		{
			var result = Perform($@"
            {{
				lotto900.CreateWagers(itIsThePresent, {lowestBetId}, {theHighestBetId}, {lowWagerNumber}, {amountOfWagerNumbers}, {ticketNumber}, {orderNumber}, Now);
			}}
            ");
			return this;
		}
        
        public LottoMockGenerator BuyTicketPick3Straight(string ticketCost, string playerId, string state, string drawDate, string selection, string[] numbers, int authorizationId = 222222, string[] excludedNumbers = null, string now = "")
		{
			string excludes = "";
			
			if (excludedNumbers != null)
			{
				excludes = string.Join(",", excludedNumbers);
			}

			StringBuilder linesNumberActivator = new StringBuilder();
			if (selection == "inputs")
			{
				string arrayOfNumbers = "";
				if (numbers != null)
				{
					arrayOfNumbers = string.Join(",", numbers.Select(number=>$"'{number}'"));
				}
				linesNumberActivator.Append($"activator.Numbers = {{{arrayOfNumbers}}};");
			}
			else
			{
				linesNumberActivator.Append($"activator.Number1 = '{numbers[0]}';");
				linesNumberActivator.Append($"activator.Number2 = '{numbers[1]}';");
				linesNumberActivator.Append($"activator.Number3 = '{numbers[2]}';");
			}
			var scriptNow = string.IsNullOrWhiteSpace(now) ? "" : $"Now = {now};";
			var result = Perform($@"
            {{
                {scriptNow}
                player = playerNO{playerId};
				domain = company.Sales.DomainFrom(1);
                lotto900.IsBetAmountValidFor(3, player, domain, {ticketCost});

                activator = Pick3StraightTicketActivator(lotto900);
                activator.Player = player;
                activator.State = state{state};
                activator.DrawDate = {drawDate};
                {linesNumberActivator.ToString()}
                activator.SelectionMode = '{selection}';
                activator.TicketCost = {ticketCost};
                activator.ExcludedNumbers = '{excludes}';

                customer = company.CustomerByPlayer(player);
                myOrder = company.GetNewOrder( customer );
				myOrder.AuthorizationId = {authorizationId};
                myOrder.Add( ticketPick3StraightProd, activator, {ticketCost} );
				agent = marketplace.SearchAgent('CR/1');
                myOrder.Agent = agent;
                company.AddOrders(myOrder, {funds});
                company.SaleTickets(itIsThePresent, myOrder, Now, {funds}, {authorizationId}, domain);
            }}
            ");

            return this;
		}

		public LottoMockGenerator BuyTicketPick3Boxed(string ticketCost, string playerId, string state, string drawDate, string selection, string[] numbers, int authorizationId = 222222, string[] excludedNumbers = null)
		{
			string excludes = "";
			if (excludedNumbers != null)
			{
				excludes = string.Join(",", excludedNumbers);
			}

			StringBuilder linesNumberActivator = new StringBuilder();
			if (selection == "inputs")
			{
				string arrayOfNumbers = "";
				if (numbers != null)
				{
					arrayOfNumbers = string.Join(",", numbers.Select(number => $"'{number}'"));
				}
				linesNumberActivator.Append($"activator.Numbers = {{{arrayOfNumbers}}};");
			}
			else
			{
				linesNumberActivator.Append($"activator.Number1 = '{numbers[0]}';");
				linesNumberActivator.Append($"activator.Number2 = '{numbers[1]}';");
				linesNumberActivator.Append($"activator.Number3 = '{numbers[2]}';");
			}

			var result = Perform($@"
            {{
                player = playerNO{playerId};
				domain = company.Sales.DomainFrom(1);
                lotto900.IsBetAmountValidFor(3, player, domain, {ticketCost});

                activator = Pick3BoxedTicketActivator(lotto900);
                activator.Player = player;
                activator.State = state{state};
                activator.DrawDate = {drawDate};
                {linesNumberActivator.ToString()}
                activator.SelectionMode = '{selection}';
                activator.TicketCost = {ticketCost};
                activator.ExcludedNumbers = '{excludes}';

                customer = company.CustomerByPlayer(player);
                myOrder = company.GetNewOrder( customer );
				myOrder.AuthorizationId = {authorizationId};
                myOrder.Add( ticketPick3BoxedProd, activator, {ticketCost} );
				agent = marketplace.SearchAgent('CR/1');
                myOrder.Agent = agent;
                company.AddOrders(myOrder, {funds});
                company.SaleTickets(itIsThePresent, myOrder, Now, {funds}, {authorizationId}, domain);
            }}
            ");
			return this;
		}

		[Obsolete]
		public LottoMockGenerator BuyManyTicketFor(GameType pick, string typeOfTicket, Dictionary<string, string> datesByState, params string[] playerIds)
		{
			List<DateAndState> temp = new List<DateAndState>();
			foreach (var dateByState in datesByState)
			{
				temp.Add(new DateAndState(dateByState.Key, dateByState.Value));
			}
			return BuyManyTicketFor(pick, typeOfTicket, temp, playerIds);
		}
		public LottoMockGenerator BuyManyTicketFor(GameType pick, string typeOfTicket, List<DateAndState> datesByState, params string[] playerIds)
		{
			if (datesByState == null && datesByState.Count == 0) throw new ArgumentException(nameof(datesByState));

			Random r = new Random();
			string[] numbers = new string[] { "0", "0", "0", "0" };
			string betAmount = pick == GameTypes.Pick3 ? "2.0" : "4.0";
			string product = null, activator = null, listOfNumbers = null;
			string selectionMode = "balls"; // balls, input, quickPick
			string lineForValidation = string.Empty;
			string excludes = string.Empty;
			foreach (var playerId in playerIds)
			{
				foreach (DateAndState dateByState in datesByState)
				{
					numbers[0] = r.Next(0, 2).ToString() + r.Next(2, 4).ToString();
					numbers[1] = r.Next(2, 4).ToString() + r.Next(4, 6).ToString();
					numbers[2] = r.Next(4, 6).ToString() + r.Next(0, 2).ToString();
					numbers[3] = r.Next(0, 2).ToString() + r.Next(2, 4).ToString();
					if (pick == GameTypes.Pick3)
					{
						listOfNumbers = ($@"
                            activator.Number1 = '{numbers[0]}';
                            activator.Number2 = '{numbers[1]}';
                            activator.Number3 = '{numbers[2]}';");
						if (typeOfTicket == "Straight")
						{
							activator = "Pick3StraightTicketActivator";
							product = "ticketPick3StraightProd";
						}
						else if (typeOfTicket == "Boxed")
						{
							activator = "Pick3BoxedTicketActivator";
							product = "ticketPick3BoxedProd";
						}
						lineForValidation = $"lotto900.IsBetAmountValidFor(3, player, domain, {betAmount});";
					}
					else if (pick == GameTypes.Pick4)
					{
						listOfNumbers = ($@"
                            activator.Number1 = '{numbers[0]}';
                            activator.Number2 = '{numbers[1]}';
                            activator.Number3 = '{numbers[2]}';
                            activator.Number4 = '{numbers[3]}';");
						if (typeOfTicket == "Straight")
						{
							activator = "Pick4StraightTicketActivator";
							product = "ticketPick4StraightProd";
						}
						else if (typeOfTicket == "Boxed")
						{
							activator = "Pick4BoxedTicketActivator";
							product = "ticketPick4BoxedProd";
						}
						lineForValidation = $"lotto900.IsBetAmountValidFor(4, player, domain, {betAmount});";
					}
					else
					{
						throw new GameEngineException("UPS!! Check the pick..");
					}

					var result = Perform($@"
                    {{
                        player = playerNO{playerId};
						domain = company.Sales.DomainFrom(1);
                        {lineForValidation}
                        activator = {activator}(lotto900);
                        activator.Player = player;
                        activator.State = state{dateByState.State};
                        activator.DrawDate = {dateByState.Date};
                        {listOfNumbers}
                        activator.SelectionMode = '{selectionMode}';
                        activator.TicketCost = {betAmount};

                        customer = company.CustomerByPlayer(player);
                        myOrder = company.GetNewOrder( customer );
                        myOrder.Add( {product}, activator, {betAmount} );
						myOrder.AuthorizationId = 123;
						agent = marketplace.SearchAgent('CR/1');
						myOrder.Agent = agent;
                        company.AddOrders(myOrder, {funds});
                        company.SaleTickets(itIsThePresent, myOrder, Now, {funds}, 222222, domain);
                    }}
                    ");
				}
			}

			return this;
		}

		public LottoMockGenerator BuyTicketPick4Straight(string ticketCost, string playerId, string state, string drawDate, string selection, string[] numbers, int authorizationId = 222222, string[] excludedNumbers = null)
		{
			string excludes = "";
			if (excludedNumbers != null)
			{
				excludes = string.Join(",", excludedNumbers);
			}

			StringBuilder linesNumberActivator = new StringBuilder();
			if (selection == "inputs")
			{
				string arrayOfNumbers = "";
				if (numbers != null)
				{
					arrayOfNumbers = string.Join(",", numbers.Select(number => $"'{number}'"));
				}
				linesNumberActivator.Append($"activator.Numbers = {{{arrayOfNumbers}}};");
			}
			else
			{
				linesNumberActivator.Append($"activator.Number1 = '{numbers[0]}';");
				linesNumberActivator.Append($"activator.Number2 = '{numbers[1]}';");
				linesNumberActivator.Append($"activator.Number3 = '{numbers[2]}';");
				linesNumberActivator.Append($"activator.Number4 = '{numbers[3]}';");
			}

			var result = Perform($@"
            {{
                player = playerNO{playerId};
				domain = company.Sales.DomainFrom(1);
                lotto900.IsBetAmountValidFor(4, player, domain, {ticketCost});

                activator = Pick4StraightTicketActivator(lotto900);
                activator.Player = player;
                activator.State = state{state};
                activator.DrawDate = {drawDate};
                {linesNumberActivator.ToString()}
                activator.SelectionMode = '{selection}';
                activator.TicketCost = {ticketCost};
                activator.ExcludedNumbers = '{excludes}';

                customer = company.CustomerByPlayer(player);
                myOrder = company.GetNewOrder( customer );
				myOrder.AuthorizationId = {authorizationId};
                myOrder.Add( ticketPick4StraightProd, activator, {ticketCost} );
				agent = marketplace.SearchAgent('CR/1');
                myOrder.Agent = agent;
                company.AddOrders(myOrder, {funds});
                company.SaleTickets(itIsThePresent, myOrder, Now, {funds}, {authorizationId}, domain);
            }}
            ");
			return this;
		}

		public LottoMockGenerator BuyTicketPick5Straight(string ticketCost, string playerId, string state, string drawDate, string selection, string[] numbers, int authorizationId = 222222, string[] excludedNumbers = null)
		{
			string excludes = "";
			if (excludedNumbers != null)
			{
				excludes = string.Join(",", excludedNumbers);
			}

			StringBuilder linesNumberActivator = new StringBuilder();
			if (selection == "inputs")
			{
				string arrayOfNumbers = "";
				if (numbers != null)
				{
					arrayOfNumbers = string.Join(",", numbers);
				}
				linesNumberActivator.Append($"activator.Numbers = '{arrayOfNumbers}';");
			}
			else
			{
				linesNumberActivator.Append($"activator.Number1 = '{numbers[0]}';");
				linesNumberActivator.Append($"activator.Number2 = '{numbers[1]}';");
				linesNumberActivator.Append($"activator.Number3 = '{numbers[2]}';");
				linesNumberActivator.Append($"activator.Number4 = '{numbers[3]}';");
				linesNumberActivator.Append($"activator.Number5 = '{numbers[4]}';");
			}

			var result = Perform($@"
            {{
                player = playerNO{playerId};
				domain = company.Sales.DomainFrom(1);
                lotto900.IsBetAmountValidFor(5, player, domain, {ticketCost});

                activator = Pick5StraightTicketActivator(company);
                activator.Player = player;
                activator.State = state{state};
                activator.DrawDate = {drawDate};
                {linesNumberActivator.ToString()}
                activator.SelectionMode = '{selection}';
                activator.TicketCost = {ticketCost};
                activator.ExcludedNumbers = '{excludes}';

                customer = company.CustomerByPlayer(player);
                myOrder = company.GetNewOrder( customer );
				myOrder.AuthorizationId = {authorizationId};
                myOrder.Add( ticketPick4StraightProd, activator, {ticketCost} );
				agent = marketplace.SearchAgent('CR/1');
                myOrder.Agent = agent;
                company.AddOrders(myOrder, {funds});
                company.SaleTickets(itIsThePresent, myOrder, Now, {funds}, {authorizationId}, domain);
            }}
            ");
			return this;
		}

		public LottoMockGenerator BuyTicketPick4Boxed(string ticketCost, string playerId, string state, string drawDate, string selection, string[] numbers, int authorizationId = 222222, string[] excludedNumbers = null)
		{
			string excludes = "";
			if (excludedNumbers != null)
			{
				excludes = string.Join(",", excludedNumbers);
			}

			StringBuilder linesNumberActivator = new StringBuilder();
			if (selection == "inputs")
			{
				string arrayOfNumbers = "";
				if (numbers != null)
				{
					arrayOfNumbers = string.Join(",", numbers.Select(number => $"'{number}'"));
				}
				linesNumberActivator.Append($"activator.Numbers = {{{arrayOfNumbers}}};");
			}
			else
			{
				linesNumberActivator.Append($"activator.Number1 = '{numbers[0]}';");
				linesNumberActivator.Append($"activator.Number2 = '{numbers[1]}';");
				linesNumberActivator.Append($"activator.Number3 = '{numbers[2]}';");
				linesNumberActivator.Append($"activator.Number4 = '{numbers[3]}';");
			}

			var result = Perform($@"
            {{
                player = playerNO{playerId};
				domain = company.Sales.DomainFrom(1);
                lotto900.IsBetAmountValidFor(4, player, domain, {ticketCost});

                activator = Pick4BoxedTicketActivator(lotto900);
                activator.Player = player;
                activator.State = state{state};
                activator.DrawDate = {drawDate};
                {linesNumberActivator.ToString()}
                activator.SelectionMode = '{selection}';
                activator.TicketCost = {ticketCost};
                activator.ExcludedNumbers = '{excludes}';

                customer = company.CustomerByPlayer(player);
                myOrder = company.GetNewOrder( customer );
				myOrder.AuthorizationId = {authorizationId};
                myOrder.Add( ticketPick4BoxedProd, activator, {ticketCost} );
				agent = marketplace.SearchAgent('CR/1');
                myOrder.Agent = agent;
                company.AddOrders(myOrder, {funds});
                company.SaleTickets(itIsThePresent, myOrder, Now, {funds}, {authorizationId}, domain);
            }}
            ");
			return this;
		}

		public LottoMockGenerator DrawLottery(string game, string state, string drawDate, string winnerNumbers, string now = "")
		{
			//string lineToGetLottery = game == "pick3" ? $"lottery = lotto900.GetOrCreateLottery(3, state{state});" : $"lottery = lotto900.GetOrCreateLottery(4, state{state});";
			string lineToGetLottery = "";
			if (game.Equals("pick2"))
			{
				lineToGetLottery = $"lottery = lotto900.GetOrCreateLottery(2, state{state});";
			}
			else if (game.Equals("pick3"))
			{
				lineToGetLottery = $"lottery = lotto900.GetOrCreateLottery(3, state{state});";
			}
			else if (game.Equals("pick4"))
			{
				lineToGetLottery = $"lottery = lotto900.GetOrCreateLottery(4, state{state});";
			}
			else if (game.Equals("pick5"))
			{
				lineToGetLottery = $"lottery = lotto900.GetOrCreateLottery(5, state{state});";
			}
			else
			{
				throw new GameEngineException("The pick does not exist");
			}

			var gradedDate = string.IsNullOrWhiteSpace(now) ? drawDate : now;
			var result = Perform($@"
                {lineToGetLottery}
                lottery.LotteryDraw({drawDate},'{winnerNumbers}', {drawDate}, false, 'Bart Simpson');
            ");
			return this;
		}

		public LottoMockGenerator ConfirmDraw(string game, string state, string drawDate)
		{
			string lineToGetLottery = game == "pick3" ? $"lottery = lotto900.GetOrCreateLottery(3, state{state});" : $"lottery = lotto900.GetOrCreateLottery(4, state{state});";
			var result = Perform($@"
                {lineToGetLottery}
                lottery.ConfirmDraw({drawDate}, Now, 'Lisa Simpson', itIsThePresent);
            ");
			return this;
		}

		public LottoMockGenerator SetNoAction(string game, string state, string drawDate)
		{
			string lineToGetLottery = game == "pick3" ? $"lottery = lotto900.GetOrCreateLottery(3, state{state});" : $"lottery = lotto900.GetOrCreateLottery(4, state{state});";
			var result = Perform($@"
                {lineToGetLottery}
                lottery.SetNoAction(itIsThePresent, {drawDate}, Now, 'Marge Simpson');
            ");
			return this;
		}

		public LottoMockGenerator SetAction(string game, string state, string drawDate)
		{
			string lineToGetLottery = game == "pick3" ? $"lottery = lotto900.GetOrCreateLottery(3, state{state});" : $"lottery = lotto900.GetOrCreateLottery(4, state{state});";
			var result = Perform($@"
                {lineToGetLottery}
                lottery.SetAction(itIsThePresent, {drawDate}, Now, 'Homer Simpson');
            ");
			return this;
		}

		public LottoMockGenerator DisableDraw(string game, string state, string drawDate, string domainUrl)
		{
			string lineToGetLottery = game == "pick3" ? $"lottery = lotto900.GetOrCreateLottery(3, state{state});" : $"lottery = lotto900.GetOrCreateLottery(4, state{state});";
			var result = Perform($@"
                {lineToGetLottery}
                domain=company.Sales.DomainFrom('{domainUrl}');
                lottery.DisableDraw({drawDate}, domain, Now, 'Maggie Simpson');
            ");
			return this;
		}

		public LottoMockGenerator EnableDraw(string game, string state, string drawDate, string domainUrl)
		{
			string lineToGetLottery = game == "pick3" ? $"lottery = lotto900.GetOrCreateLottery(3, state{state});" : $"lottery = lotto900.GetOrCreateLottery(4, state{state});";
			var result = Perform($@"
                {lineToGetLottery}
                domain=company.Sales.DomainFrom('{domainUrl}');
                lottery.EnableDraw({drawDate}, domain, Now, 'Mr. Burns');
            ");
			return this;
		}

		private void PartialShuffle<T>(IList<T> source, int count, Random random)
		{
			for (int i = 0; i < count; i++)
			{
				int index = i + random.Next(source.Count - i);
				T tmp = source[index];
				source[index] = source[i];
				source[i] = tmp;
			}
		}

		Random random = new Random();
		private string GenerateRandomNumber(int number)
		{
			var validNumbers = new List<int>() { 0, 1, 2, 3, 4, 5 };
			PartialShuffle(validNumbers, number, random);
			var numbersTaken = validNumbers.Take(number);
			var winnerNumber = string.Join("", numbersTaken);
			return winnerNumber;
		}

		public LottoMockGenerator DrawLottery(string game, Dictionary<string, string> datesByState)
		{
			string winnerNumbers = "";
			foreach (var dateByState in datesByState)
			{
				winnerNumbers = GenerateRandomNumber(game == "pick3" ? 3 : 4);
				string lineToGetLottery = game == "pick3" ? $"lottery = lotto900.GetOrCreateLottery(3, state{dateByState.Value});" : $"lottery = lotto900.GetOrCreateLottery(4, state{dateByState.Value});";
				var result = Perform($@"
                {lineToGetLottery}
                lottery.LotteryDraw({dateByState.Key},'{winnerNumbers}', {dateByState.Key}, true, 'Bart Simpson');
                lottery.ConfirmDraw({dateByState.Key}, Now, 'Lisa Simpson', itIsThePresent);
            ");
			}

			return this;
		}

		public LottoMockGenerator DrawLottery(string game, List<DateAndState> datesByState)
		{
			string winnerNumbers = "";
			foreach (var dateByState in datesByState)
			{
				winnerNumbers = GenerateRandomNumber(game == "pick3" ? 3 : 4);
				string lineToGetLottery = game == "pick3" ? $"lottery = lotto900.GetOrCreateLottery(3, state{dateByState.State});" : $"lottery = lotto900.GetOrCreateLottery(4, state{dateByState.State});";
				var result = Perform($@"
                {lineToGetLottery}
                lottery.LotteryDraw({dateByState.Date},'{winnerNumbers}', {dateByState.Date}, true, 'Bart Simpson');
                lottery.ConfirmDraw({dateByState.Date}, Now, 'Lisa Simpson', itIsThePresent);
            ");
			}

			return this;
		}

		public LottoMockGenerator SaveFavoriteNumbers(string game, string ruleType, string state, string playerId, decimal amount, string name, string[] drawDates, string[][] numberCombinations)
		{
			string linesToAddSchedules = "schedules = FavoriteSchedules();";
			string stringAmount = amount.ToString(Integration.CultureInfoEnUS);
			foreach (var drawDate in drawDates)
			{
				linesToAddSchedules += $"schedule=lottery.FindScheduleAt({drawDate});";
				linesToAddSchedules += $"schedules.Add(schedule);";
			}

			string lineToGetLottery = game == "pick3" ? $"lottery = lotto900.GetOrCreateLottery(3, state{state});" : $"lottery = lotto900.GetOrCreateLottery(4, state{state});";
			StringBuilder linesToAddNumbers = new StringBuilder();
			linesToAddNumbers.Append($"numbers = FavoriteNumbers();");
			if (game == "pick3")
			{
				foreach (var numbers in numberCombinations)
				{
					linesToAddNumbers.Append($"numbers.Add('{numbers[0]}', '{numbers[1]}', '{numbers[2]}');");
				}
			}
			else
			{
				foreach (var numbers in numberCombinations)
				{
					linesToAddNumbers.Append($"numbers.Add('{numbers[0]}', '{numbers[1]}', '{numbers[2]}', '{numbers[3]}');");
				}
			}
#pragma warning disable VSTHRD002 // Avoid problematic synchronous waits
			var x = actor.PerformCmdAsync($@"
                groups = playerNO{playerId}.Favorites();
                {lineToGetLottery}
                {linesToAddNumbers}
                {linesToAddSchedules}
                favorite = groups.CreateFavorite(itIsThePresent, '{name}','{ruleType}','balls',{stringAmount},true,true,true, numbers, schedules);
            ", IpAddress.DEFAULT, UserInLog.ANONYMOUS).Result;
#pragma warning restore VSTHRD002 // Avoid problematic synchronous waits
			return this;
		}

		public LottoMockGenerator CancelSchedules(string state, string game, int hour, int minutes, int[] daysOfWeek, bool hasTickets)
		{
			CheckExistenceOfSchedules(state, game, daysOfWeek, true);

			var result = Perform($@"
                {CancelSchedules(state, game, hour, minutes, daysOfWeek)}
             ");

			CheckExistenceOfSchedules(state, game, daysOfWeek, hasTickets);

			return this;
		}

		public void CheckExistenceOfSchedules(string state, string game, int[] daysOfWeek, bool hasTickets)
		{
			string lineToGetLottery = game == "pick3" ? $"lottery = lotto900.GetOrCreateLottery(3, state{state});" : $"lottery = lotto900.GetOrCreateLottery(4, state{state});";
			string linesToPrintExistenceOfSchedules = "";
			foreach (int dayOfWeek in daysOfWeek)
			{
				linesToPrintExistenceOfSchedules += $"print lottery.IsScheduledAt({dayOfWeek}) existSchedule{dayOfWeek};";
			}
			string result = Perform($@"
                {lineToGetLottery}
                {linesToPrintExistenceOfSchedules}
             ");

			string[] existSchedules = new string[daysOfWeek.Length];
			for (int index = 0; index < daysOfWeek.Length; index++)
			{
				existSchedules[index] = $"\"existSchedule{daysOfWeek[index]}\":{hasTickets.ToString().ToLower()}";
			}
			var jsonExpected = $"{{{string.Join(",", existSchedules)}}}";
		}

		public string CancelSchedules(string state, string game, int hour, int minutes, params int[] daysOfWeek)
		{
			string lineToGetLottery = game == "pick3" ? $"lottery = lotto900.GetOrCreateLottery(3, state{state});" : $"lottery = lotto900.GetOrCreateLottery(4, state{state});";
			string linesForEveryDate = "";
			linesForEveryDate = $"schedule = lottery.GetScheduleOrNullFor(01/01/2040 {hour}:{minutes}:00);lottery.Cancel(schedule, Now,'Games Admin');";

			string script = $@"
                {lineToGetLottery}
                {linesForEveryDate}
            ";
			return script;
		}

		public LottoMockGenerator BuyTicketPick3StraightForListOfDraws(decimal ticketCost, string playerId, List<Draw> draws, string[] numbers, string[] excludedNumbers = null)
		{
			string excludes = "";
			if (excludedNumbers != null)
			{
				excludes = string.Join(",", excludedNumbers);
			}
			var strTicketCost = ticketCost.ToString(Integration.CultureInfoEnUS);
			StringBuilder script = new StringBuilder();
			foreach (var draw in draws)
			{
				script.Append($@"
                {{
                    lotto900.IsBetAmountValidFor(3, player, domain, {ticketCost});
                    activator = Pick3StraightTicketActivator(lotto900);
                    activator.Player = player;
                    activator.State = state{draw.State};
                    activator.DrawDate = {draw.Date};
                    activator.Number1 = '{numbers[0]}';
                    activator.Number2 = '{numbers[1]}';
                    activator.Number3 = '{numbers[2]}';
                    activator.SelectionMode = 'balls';
                    activator.TicketCost = {strTicketCost};
                    activator.ExcludedNumbers = '{excludes}';

                    myOrder.Add( ticketPick3StraightProd, activator, {strTicketCost} );
                }}
                ");
			}
			var result = Perform($@"
                {{
                    player = playerNO{playerId};
					domain = company.Sales.CreateDomain(false, 4, 'abc.com', {Agents.INSIDER});
                    customer = company.CustomerByPlayer(player);
                    myOrder = company.GetNewOrder( customer );
					myOrder.AuthorizationId = 123;
                    {script.ToString()}
                    
                    company.AddOrders(myOrder, {funds});
                    company.SaleTickets(itIsThePresent, myOrder, Now, {funds}, 222222, domain);
                }}
            ");
			return this;
		}

		public LottoMockGenerator BuyTicketPick3BoxedForListOfDraws(string ticketCost, string playerId, List<Draw> draws, string[] numbers, string[] excludedNumbers = null)
		{
			string excludes = "";
			if (excludedNumbers != null)
			{
				excludes = string.Join(",", excludedNumbers);
			}
			var strTicketCost = ticketCost.ToString(Integration.CultureInfoEnUS);
			StringBuilder script = new StringBuilder();
			foreach (var draw in draws)
			{
				script.Append($@"
                {{
                    lotto900.IsBetAmountValidFor(3, player, domain, {ticketCost});
                    activator = Pick3BoxedTicketActivator(lotto900);
                    activator.Player = player;
                    activator.State = state{draw.State};
                    activator.DrawDate = {draw.Date};
                    activator.Number1 = '{numbers[0]}';
                    activator.Number2 = '{numbers[1]}';
                    activator.Number3 = '{numbers[2]}';
                    activator.SelectionMode = 'balls';
                    activator.TicketCost = {strTicketCost};
                    activator.ExcludedNumbers = '{excludes}';

                    myOrder.Add( ticketPick3BoxedProd, activator, {strTicketCost} );
                }}
                ");
			}
			var result = Perform($@"
                {{
                    player = playerNO{playerId};
					domain = company.Sales.CreateDomain(false, 4, 'abc.com', {Agents.INSIDER});
                    customer = company.CustomerByPlayer(player);
                    myOrder = company.GetNewOrder( customer );
					myOrder.AuthorizationId = 123;
                    {script.ToString()}

                    company.AddOrders(myOrder, {funds});
                    company.SaleTickets(itIsThePresent, myOrder, Now, {funds}, 222222, domain);
                }}
            ");
			return this;
		}

		public LottoMockGenerator BuyTicketPick4StraightForListOfDraws(string ticketCost, string playerId, List<Draw> draws, string[] numbers, string[] excludedNumbers = null)
		{
			string excludes = "";
			if (excludedNumbers != null)
			{
				excludes = string.Join(",", excludedNumbers);
			}
			var strTicketCost = ticketCost.ToString(Integration.CultureInfoEnUS);
			StringBuilder script = new StringBuilder();
			foreach (var draw in draws)
			{
				script.Append($@"
                {{
                    lotto900.IsBetAmountValidFor(4, player, domain, {strTicketCost});
                    activator = Pick4StraightTicketActivator(lotto900);
                    activator.Player = player;
                    activator.State = state{draw.State};
                    activator.DrawDate = {draw.Date};
                    activator.Number1 = '{numbers[0]}';
                    activator.Number2 = '{numbers[1]}';
                    activator.Number3 = '{numbers[2]}';
                    activator.Number4 = '{numbers[3]}';
                    activator.SelectionMode = 'balls';
                    activator.TicketCost = {strTicketCost};
                    activator.ExcludedNumbers = '{excludes}';

                    myOrder.Add( ticketPick4StraightProd, activator, {strTicketCost} );
                }}
                ");
			}
			var result = Perform($@"
                {{
                    player = playerNO{playerId};
					domain = company.Sales.CreateDomain(false, 4, 'abc.com', {Agents.INSIDER});
                    customer = company.CustomerByPlayer(player);
                    myOrder = company.GetNewOrder( customer );
					myOrder.AuthorizationId = 123;
                    {script.ToString()}

                    company.AddOrders(myOrder, {funds});
                    company.SaleTickets(itIsThePresent, myOrder, Now, {funds}, 222222, domain);
                }}
            ");
			return this;
		}

		public LottoMockGenerator BuyTicketPick4BoxedForListOfDraws(string ticketCost, string playerId, List<Draw> draws, string[] numbers, string[] excludedNumbers = null)
		{
			string excludes = "";
			if (excludedNumbers != null)
			{
				excludes = string.Join(",", excludedNumbers);
			}
			var strTicketCost = ticketCost.ToString(Integration.CultureInfoEnUS);
			StringBuilder script = new StringBuilder();
			foreach (var draw in draws)
			{
				script.Append($@"
                {{
                    lotto900.IsBetAmountValidFor(4, player, domain, {strTicketCost});
                    activator = Pick4BoxedTicketActivator(lotto900);
                    activator.Player = player;
                    activator.State = state{draw.State};
                    activator.DrawDate = {draw.Date};
                    activator.Number1 = '{numbers[0]}';
                    activator.Number2 = '{numbers[1]}';
                    activator.Number3 = '{numbers[2]}';
                    activator.Number4 = '{numbers[3]}';
                    activator.SelectionMode = 'balls';
                    activator.TicketCost = {strTicketCost};
                    activator.ExcludedNumbers = '{excludes}';

                    myOrder.Add( ticketPick4BoxedProd, activator, {strTicketCost} );
                }}
                ");
			}
			var result = Perform($@"
                {{
                    player = playerNO{playerId};
					domain = company.Sales.CreateDomain(false, 4, 'abc.com', {Agents.INSIDER});
                    customer = company.CustomerByPlayer(player);
                    myOrder = company.GetNewOrder( customer );
					myOrder.AuthorizationId = 123;
                    {script.ToString()}

                    company.AddOrders(myOrder, {funds});
                    company.SaleTickets(itIsThePresent, myOrder, Now, {funds}, 222222, domain);
                }}
            ");
			return this;
		}

		internal class TicketPowerball
		{
			public decimal Amount { get; set; }
			public string DozenOfNumber { get; set; }
			public bool Powerplay { get; set; }
			public string Date { get; set; }
		}

		internal class OrderResponse
		{
			public decimal Total { get; set; }
			public int Number { get; set; }
			public string AccountNumber { get; set; }
		}
		int ticketNumber = 1;
		internal LottoMockGenerator BuyTicketPowerball(string playerId, List<TicketPowerball> tickets)
		{
			var drawDates = tickets.Select(x => x.Date);
			var amounts = tickets.Select(x => x.Amount);
			var dozenOfNumbers = tickets.Select(x => x.DozenOfNumber);
			var powerplays = tickets.Select(x => x.Powerplay);

			var strDates = string.Join(",", drawDates);
			var strAmounts = string.Join(",", amounts);
			var strDozenOfNumbers = string.Join("','", dozenOfNumbers);
			var strPowerplays = string.Join(",", powerplays);
			var resultCreatingOrder = Perform($@"
                {{
                    domain = company.Sales.DomainFrom('localhost.com');
                    player = playerNO{playerId};
                    customer = company.CustomerByPlayer(player);
				    {{
                        myOrder = company.GetNewOrder(customer);
						myOrder.AuthorizationId = 123;
					    myOrder = company.CreatePowerballOrder(player, {{{strDates}}}, {{'{strDozenOfNumbers}'}}, {{{strPowerplays}}}, {{{strAmounts}}}, myOrder, domain, ticketPowerBallSingleProd, ticketPowerBallPowerPlayProd);
                        company.AddOrders(myOrder, {funds});
                        print myOrder.Total() total;
                        print myOrder.Number number;
                        print myOrder.Customer.AccountNumber accountNumber;
				    }}
                }}
            ");

			var orderResponse = JsonConvert.DeserializeObject<OrderResponse>(resultCreatingOrder);
#pragma warning disable VSTHRD002 // Avoid problematic synchronous waits
			ticketNumber++;
#pragma warning restore VSTHRD002 // Avoid problematic synchronous waits
			var result = Perform($@"
                {{
                    myOrder = company.OrderByNumber({orderResponse.Number});
                    company.SaleTickets(itIsThePresent, myOrder, Now, {funds}, {ticketNumber}, domain);
                }}
            ");
			return this;
		}

		internal LottoMockGenerator DrawPB(string playerId, string stringDate, string winnerNumbers, int multiplier)
		{
			var result = Perform($@"
                {{
                    lottery = lotto900.GetPowerball();
                    report = lottery.LotteryDraw({stringDate},'{winnerNumbers}',{multiplier}, Now, ItIsThePresent, 'Bar');
                }}
            ");
            return this;
        }
	}
}
