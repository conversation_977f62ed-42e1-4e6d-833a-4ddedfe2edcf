﻿using GamesEngine.Settings;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc.Authorization;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.IdentityModel.Tokens;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Security.Cryptography.X509Certificates;
using System.Threading.Tasks;

namespace LinesAPI
{
	public class Security : SecurityConfiguration
    {
		internal static void Configure(IServiceCollection services, Microsoft.Extensions.Configuration.IConfiguration Configuration)
        {
            var securitySettings = Configuration.GetSection("Security");
            if (string.IsNullOrEmpty(securitySettings.GetSection("Clerks")["Certificate"])) throw new Exception("Add to Certificate to the app settings.");
            if (string.IsNullOrEmpty(securitySettings.GetSection("Clerks")["PublicKey"])) throw new Exception("Add to PublicKey to the app settings.");
            if (securitySettings.GetSection("Clerks").GetSection("Clients") == null) throw new Exception("Add to Clients to the app settings.");
            if (string.IsNullOrEmpty(securitySettings.GetSection("Players")["Certificate"])) throw new Exception("Add to Certificate to the app settings.");
            if (string.IsNullOrEmpty(securitySettings.GetSection("Players")["PublicKey"])) throw new Exception("Add to PublicKey to the app settings.");
            if (securitySettings.GetSection("Players").GetSection("Clients") == null) throw new Exception("Add to Clients to the app settings.");

            Configure(securitySettings);
            if (!ItsSecuritySchemeConfigured())
            {
                services.AddMvc(opts =>
                {
                    opts.Filters.Add(new AllowAnonymousFilter());
                });
            }
            else
            {
                X509Certificate2 clerksCertificate = new X509Certificate2(
                    Convert.FromBase64String(securitySettings.GetSection("Clerks")["Certificate"]),
                    securitySettings.GetSection("Clerks")["PublicKey"]);
                SecurityKey clerksKey = new X509SecurityKey(clerksCertificate);

                X509Certificate2 playersCertificate = new X509Certificate2(
                    Convert.FromBase64String(securitySettings.GetSection("Players")["Certificate"]),
                    securitySettings.GetSection("Players")["PublicKey"]);
                SecurityKey playersKey = new X509SecurityKey(playersCertificate);

                services.AddAuthentication(options =>
                {
                    options.DefaultAuthenticateScheme = JwtBearerDefaults.AuthenticationScheme;
                    options.DefaultScheme = JwtBearerDefaults.AuthenticationScheme;
                    options.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;
                })
                .AddJwtBearer(JwtBearerDefaults.AuthenticationScheme, options =>
                {
                    options.Events = new JwtBearerEvents()
                    {
                        OnAuthenticationFailed = c =>
                        {
                            return Task.CompletedTask;
                        },
                        OnTokenValidated = async ctx =>
                        {
                            await Task.Run(() =>
                            {
                                string payloadInJson = ((JwtSecurityToken)ctx.SecurityToken).Payload.SerializeToJson();
                                Jwt jwt = JsonConvert.DeserializeObject<Jwt>(payloadInJson);

                                List<Claim> claims = new List<Claim>();
                                if (jwt.resource_access.lines != null)
                                {
                                    foreach (string role in jwt.resource_access.lines.roles)
                                    {
                                        claims.Add(new Claim(ClaimTypes.Role, role));
                                    }
                                }
                                claims.Add(new Claim(ClaimTypes.PrimarySid, jwt.sub));

                                var appIdentity = new ClaimsIdentity(claims);

                                ctx.Principal.AddIdentity(appIdentity);
                            });

                        }
                    };
                    options.TokenValidationParameters =
                         new TokenValidationParameters
                         {
                             ValidateIssuer = false,
                             ValidateAudience = true,
                             ValidateLifetime = true,
                             ValidateIssuerSigningKey = true,

                             ValidIssuer = securitySettings.GetSection("Clerks")["Authority"],
                             ValidAudience = "lines",
                             IssuerSigningKey = clerksKey
                         };
                }).AddJwtBearer("LinesPlayers", options =>
                {
                    options.Events = new JwtBearerEvents()
                    {
                        OnAuthenticationFailed = c =>
                        {
                            return Task.CompletedTask;
                        },
                        OnTokenValidated = async ctx =>
                        {
                            await Task.Run(() =>
                            {
                                string payloadInJson = ((JwtSecurityToken)ctx.SecurityToken).Payload.SerializeToJson();
                                Jwt jwt = JsonConvert.DeserializeObject<Jwt>(payloadInJson);

                                List<Claim> claims = new List<Claim>();
                                foreach (string role in jwt.realm_access.roles)
                                {
                                    claims.Add(new Claim(ClaimTypes.Role, role));
                                }
                                claims.Add(new Claim(ClaimTypes.PrimarySid, jwt.sub));

                                var appIdentity = new ClaimsIdentity(claims);

                                ctx.Principal.AddIdentity(appIdentity);
                            });

                        }
                    };
                    options.TokenValidationParameters =
                         new TokenValidationParameters
                         {
                             ValidateIssuer = false,
                             ValidateAudience = true,
                             ValidateLifetime = true,
                             ValidateIssuerSigningKey = true,

                             ValidIssuer = securitySettings.GetSection("Players")["Authority"],
                             ValidAudience = "lines",
                             IssuerSigningKey = playersKey
                         };
                }).AddJwtBearer("InternalAdminClerks", options =>
                {
                    options.Events = new JwtBearerEvents()
                    {
                        OnAuthenticationFailed = c =>
                        {
                            return Task.CompletedTask;
                        },
                        OnTokenValidated = async ctx =>
                        {
                            await Task.Run(() =>
                            {
                                string payloadInJson = ((JwtSecurityToken)ctx.SecurityToken).Payload.SerializeToJson();
                                Jwt jwt = JsonConvert.DeserializeObject<Jwt>(payloadInJson);

                                List<Claim> claims = new List<Claim>();
                                if (jwt.resource_access.internal_admin != null)
                                {
                                    foreach (string role in jwt.resource_access.internal_admin.roles)
                                    {
                                        claims.Add(new Claim(ClaimTypes.Role, role));
                                    }
                                }
                                claims.Add(new Claim(ClaimTypes.PrimarySid, jwt.sub));

                                var appIdentity = new ClaimsIdentity(claims);

                                ctx.Principal.AddIdentity(appIdentity);
                            });

                        }
                    };
                    options.TokenValidationParameters =
                         new TokenValidationParameters
                         {
                             ValidateIssuer = false,
                             ValidateAudience = true,
                             ValidateLifetime = true,
                             ValidateIssuerSigningKey = true,

                             ValidIssuer = securitySettings.GetSection("Clerks")["Authority"],
                             ValidAudience = "internal_admin",
                             IssuerSigningKey = clerksKey
                         };
                })
                ;


                services.AddAuthorization(options =>
                {
                    options.DefaultPolicy = new AuthorizationPolicyBuilder()
                        .RequireAuthenticatedUser()
                        .AddAuthenticationSchemes(JwtBearerDefaults.AuthenticationScheme, "LinesPlayers", "InternalAdminClerks") // Support several authorization providers
                        .Build();

#pragma warning disable VSTHRD002 // Avoid problematic synchronous waits
                    List<RoleMapping> rolesClerks = Security.ClerksAPI().AvailableClientRolesAsync("lines").Result;
                    List<RoleMapping> rolesPlayers = Security.PlayersAPI().GetAvailableRealmRolesAsync("lines").Result;
#pragma warning restore VSTHRD002 // Avoid problematic synchronous waits
                    foreach (RoleMapping role in rolesClerks)
                    {
                        options.AddPolicy(role.Name, policy => policy.RequireRole(role.Name));
                    }
                    foreach (RoleMapping role in rolesPlayers)
                    {
                        options.AddPolicy(role.Name, policy => policy.RequireRole(role.Name));
                    }
                });
            }
        }

	}
}
