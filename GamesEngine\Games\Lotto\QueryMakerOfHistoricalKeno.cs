using GamesEngine.Business;
using GamesEngine.Gameboards;
using GamesEngine.PurchaseOrders;
using GamesEngine.Settings;
using MySql.Data.MySqlClient;
using Puppeteer.EventSourcing;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GamesEngine.Games.Lotto
{
	public class QueryMakerOfHistoricalKeno : QueryMakerOfHistorical
	{
		public const string ID_K12_NO_MUL_NO_BULL = "k12NMNB";
		public const string ID_K10_NO_MUL_NO_BULL = "k10NMNB";
		public const string ID_K12_MUL_NO_BULL = "k12MNB";
		public const string ID_K10_MUL_NO_BULL = "k10MNB";
		public const string ID_K12_NO_MUL_BULL = "k12NMB";
		public const string ID_K10_NO_MUL_BULL = "k10NMB";
		public const string ID_K12_MUL_BULL = "k12MB";
		public const string ID_K10_MUL_BULL = "k10MB";
		public const string ID_KENO = "KN";

		readonly KenoDBHandler lottoDBHandler;

		public QueryMakerOfHistoricalKeno()
		{
			if (Integration.Db?.DBSelected == HistoricalDatabaseType.MySQL.ToString())
			{
				lottoDBHandler = new KenoDBHandlerMySQL(Integration.Db.MySQL);
			}
			else if (Integration.Db?.DBSelected == HistoricalDatabaseType.SQLServer.ToString())
			{
				lottoDBHandler = new KenoDBHandlerSQLServer(Integration.Db.SQLServer);
			}
			else
			{
#if DEBUG
				lottoDBHandler = new KenoDBHandlerInMemory();
#else
                throw new Exception($"There is no connection for {Integration.Db.DBSelected}");
#endif
			}
		}

		internal QueryMakerOfHistoricalKeno(Company company) : base(company)
		{
			if (Integration.Db?.DBSelected == HistoricalDatabaseType.MySQL.ToString())
			{
				lottoDBHandler = new KenoDBHandlerMySQL(this.company, Integration.Db.MySQL);
			}
			else if (Integration.Db?.DBSelected == HistoricalDatabaseType.SQLServer.ToString())
			{
				lottoDBHandler = new KenoDBHandlerSQLServer(this.company, Integration.Db.SQLServer);
			}
			else
			{
#if DEBUG
				lottoDBHandler = new KenoDBHandlerInMemory(this.company);
#else
                throw new Exception($"There is no connection for {Integration.Db.DBSelected}");
#endif
			}
		}

		internal IEnumerable<CompletedKenoTicket> WinnerTicketsOfPlayerBetween(DateTime startDate, DateTime endDate, string accountNumber)
		{
			if (startDate == default(DateTime)) throw new GameEngineException("Date cannot have default value");
			if (endDate == default(DateTime)) throw new GameEngineException("Date cannot have default value");
			if (string.IsNullOrWhiteSpace(accountNumber)) throw new ArgumentNullException(nameof(accountNumber));

			var result = lottoDBHandler.WinnerTicketsOfPlayerBetween(startDate, endDate, accountNumber);
			return result;
		}

		internal IEnumerable<CompletedKenoTicket> LoserTicketsOfPlayerBetween(DateTime startDate, DateTime endDate, string accountNumber)
		{
			if (startDate == default(DateTime)) throw new GameEngineException("Date cannot have default value");
			if (endDate == default(DateTime)) throw new GameEngineException("Date cannot have default value");
			if (string.IsNullOrWhiteSpace(accountNumber)) throw new ArgumentNullException(nameof(accountNumber));

			var result = lottoDBHandler.LoserTicketsOfPlayerBetween(startDate, endDate, accountNumber);
			return result;
		}

		internal IEnumerable<CompletedKenoTicket> NoActionTicketsOfPlayerBetween(DateTime startDate, DateTime endDate, string accountNumber)
		{
			if (startDate == default(DateTime)) throw new GameEngineException("Date cannot have default value");
			if (endDate == default(DateTime)) throw new GameEngineException("Date cannot have default value");
			if (string.IsNullOrWhiteSpace(accountNumber)) throw new ArgumentNullException(nameof(accountNumber));

			var result = lottoDBHandler.NoActionTicketsOfPlayerBetween(startDate, endDate, accountNumber);
			return result;
		}

		internal IEnumerable<CompletedKenoTicket> WinnerTicketsOfPlayer(DateTime drawDate, string accountNumber)
		{
			if (drawDate == default(DateTime)) throw new GameEngineException("Date cannot have default value");
			if (string.IsNullOrWhiteSpace(accountNumber)) throw new ArgumentNullException(nameof(accountNumber));

			var result = lottoDBHandler.WinnerTicketsOfPlayer(drawDate, accountNumber);
			return result;
		}

		internal IEnumerable<CompletedKenoTicket> LoserTicketsOfPlayer(DateTime drawDate, string accountNumber)
		{
			if (drawDate == default(DateTime)) throw new GameEngineException("Date cannot have default value");
			if (string.IsNullOrWhiteSpace(accountNumber)) throw new ArgumentNullException(nameof(accountNumber));

			var result = lottoDBHandler.LoserTicketsOfPlayer(drawDate, accountNumber);
			return result;
		}

		internal IEnumerable<CompletedKenoTicket> NoActionTicketsOfPlayer(DateTime drawDate, string accountNumber)
		{
			if (drawDate == default(DateTime)) throw new GameEngineException("Date cannot have default value");
			if (string.IsNullOrWhiteSpace(accountNumber)) throw new ArgumentNullException(nameof(accountNumber));

			var result = lottoDBHandler.NoActionTicketsOfPlayer(drawDate, accountNumber);
			return result;
		}

		public void InsertAffiliateIfNotExist(int id, string name, string accountNumber)
		{
			lottoDBHandler.InsertAffiliateIfNotExist(id, name, accountNumber);
		}

		internal override void AccumulateTotalProfitByDrawing(DateTime now)
		{
			if (now == default(DateTime)) throw new GameEngineException($"{nameof(now)} cannot have default value");

			lottoDBHandler.AccumulateTotalProfitByDrawingKeno(now);
		}

		internal override List<DailyTotalProfitRecord2> AccumulateDailyTotalProfit(DateTime now)
		{
			if (now == default(DateTime)) throw new GameEngineException($"{nameof(now)} cannot have default value");

			return lottoDBHandler.AccumulateDailyTotalProfitKeno(now);
		}

        internal override DateTime LastDateInDailyTotalProfit()
        {
            var result = lottoDBHandler.LastDateInDailyTotalProfit();
            return result;
        }

        internal CompletedKenoDraws GenerateDrawingsReport(DateTime startDate, DateTime endDate, string accountNumber, string ticketNumber, string domainIds)
		{
			if (startDate == default(DateTime)) throw new GameEngineException($"{nameof(startDate)} cannot have default value");
			if (endDate == default(DateTime)) throw new GameEngineException($"{nameof(endDate)} cannot have default value");

			var result = lottoDBHandler.GenerateDrawingsReport(startDate, endDate, accountNumber, ticketNumber, domainIds);
			return result;
		}

		internal TotalProfitByDrawingReport GenerateTotalProfitByDrawingReport(DateTime startDate, DateTime endDate, DateTime now, string domainIds)
		{
			if (startDate == default(DateTime)) throw new GameEngineException($"{nameof(startDate)} cannot have default value");
			if (endDate == default(DateTime)) throw new GameEngineException($"{nameof(endDate)} cannot have default value");

			var result = lottoDBHandler.GenerateTotalProfitByDrawingReport(startDate, endDate, now, domainIds);
			return result;
		}

		internal TicketsPerPlayersInCompletedKenoDraws GenerateTicketsPerPlayersInDrawingReport(DateTime startDate, DateTime endDate, string accountNumber, string ticketNumber, string domainIds)
		{
			if (startDate == default(DateTime)) throw new GameEngineException($"{nameof(startDate)} cannot have default value");
			if (endDate == default(DateTime)) throw new GameEngineException($"{nameof(endDate)} cannot have default value");

			var result = lottoDBHandler.GenerateTicketsPerPlayersInDrawingReport(startDate, endDate, accountNumber, ticketNumber, domainIds);
			return result;
		}

		internal TicketsPerPlayersInCompletedKenoDraws GenerateTicketsPerPlayersInDrawingReport(DateTime drawDate, string accountNumber, string ticketNumber, string domainIds)
		{
			if (drawDate == default(DateTime)) throw new GameEngineException($"{nameof(drawDate)} cannot have default value");

			var result = lottoDBHandler.GenerateTicketsPerPlayersInDrawingReport(drawDate, accountNumber, ticketNumber, domainIds);
			return result;
		}

		internal IEnumerable<KenoWinnerRecord> GenerateWinnersReport(DateTime startDate, DateTime endDate, string accountNumber, string domainIds)
		{
			if (startDate == default(DateTime)) throw new GameEngineException($"{nameof(startDate)} cannot have default value");
			if (endDate == default(DateTime)) throw new GameEngineException($"{nameof(endDate)} cannot have default value");

			var result = lottoDBHandler.GenerateWinnersReport(startDate, endDate, accountNumber, domainIds);
			return result;
		}

		internal DailyTotalProfitReport GenerateDailyTotalProfitReport(DateTime startDate, DateTime endDate, DateTime now, string domainIds)
		{
			if (startDate == default(DateTime)) throw new GameEngineException($"{nameof(startDate)} cannot have default value");
			if (endDate == default(DateTime)) throw new GameEngineException($"{nameof(endDate)} cannot have default value");

			var result = lottoDBHandler.GenerateDailyTotalProfitReport(startDate, endDate, now, domainIds);
			return result;
		}

		internal CompletedKenoDraws LastPlayedDrawingsOfPlayer(string accountNumber)
		{
			if (string.IsNullOrWhiteSpace(accountNumber)) throw new ArgumentNullException(nameof(accountNumber));

			var result = lottoDBHandler.LastPlayedDrawingsOfPlayer(accountNumber);
			return result;
		}

		private abstract class KenoDBHandler : LottoDBHandler
		{
			protected const string TABLE_KENO_TOTAL_PROFIT_BY_DRAWING = "kenototalprofitbydrawing";
			protected const string TABLE_KENO_DAILY_TOTAL_PROFIT = "kenodailytotalprofit";

			protected const string COMMON_SELECT_FOR_KENO_WINNERS = "select date, account, ticket, amount, draw, gradedby, action, creation, ticketnumber, profit, prize, bulleyeprize, prizesversion, drawingid, domainid, url, currencyid";
			protected const string COMMON_SELECT_FOR_KENO_LOSERS = "select date, account, ticket, amount, draw, gradedby, action, creation, ticketnumber, profit, prizesversion, drawingid, domainid, url, currencyid";
			protected const string COMMON_SELECT_FOR_KENO_NOACTION = "select date, account, ticket, amount, action, creation, ticketnumber, noactionby, prizesversion, drawingid, domainid, url, currencyid";

			protected KenoDBHandler(string connectionString) : base(connectionString)
			{
			}

			protected KenoDBHandler(Company company, string connectionString) : base(company, connectionString)
			{
			}

			protected abstract CompletedKenoTicketsWithAccounts WinnerKenoTicketsWithAccounts(string command);
			protected abstract IEnumerable<CompletedKenoTicket> WinnerKenoTickets(string command);
			protected abstract IEnumerable<CompletedKenoTicket> LoserKenoTickets(string command);
			protected abstract IEnumerable<CompletedKenoTicket> NoActionKenoTickets(string command);
			protected abstract List<TotalProfitByDrawingRecord> TotalProfitByDrawingRecords(string command);
			protected abstract List<DailyTotalProfitRecord2> DailyTotalProfitRecords2(string command);

			protected abstract CompletedKenoDraws GetDrawings(string command);

			internal abstract IEnumerable<CompletedKenoTicket> WinnerTicketsOfPlayerBetween(DateTime startedDate, DateTime endedDate, string accountNumber);
			internal abstract IEnumerable<CompletedKenoTicket> LoserTicketsOfPlayerBetween(DateTime startedDate, DateTime endedDate, string accountNumber);
			internal abstract IEnumerable<CompletedKenoTicket> NoActionTicketsOfPlayerBetween(DateTime startedDate, DateTime endedDate, string accountNumber);

			internal abstract IEnumerable<CompletedKenoTicket> WinnerTicketsOfPlayer(DateTime drawDate, string accountNumber);
			internal abstract IEnumerable<CompletedKenoTicket> LoserTicketsOfPlayer(DateTime drawDate, string accountNumber);
			internal abstract IEnumerable<CompletedKenoTicket> NoActionTicketsOfPlayer(DateTime drawDate, string accountNumber);

			protected abstract IEnumerable<CompletedKenoTicket> NoActionKenoTicketsForDrawingsReport(DateTime startDate, string hour, string minute, string accountNumber, string gameType, string ticketNumber, string domainIds);
			protected abstract IEnumerable<CompletedKenoTicket> WinnerKenoTicketsForDrawingsReport(DateTime startDate, string hour, string minute, string accountNumber, string gameType, string ticketNumber, string domainIds);
			protected abstract IEnumerable<CompletedKenoTicket> LoserKenoTicketsForDrawingsReport(DateTime startDate, string hour, string minute, string accountNumber, string gameType, string ticketNumber, string domainIds);
			
			protected abstract IEnumerable<CompletedKenoTicket> WinnerKenoTicketsForDailyTotalProfit(DateTime startDate, string gameType, string domainIds);
			protected abstract IEnumerable<CompletedKenoTicket> LoserKenoTicketsForDailyTotalProfit(DateTime drawDate, string gameType, string domainIds);
			protected abstract IEnumerable<CompletedKenoTicket> NoActionKenoTicketsForDailyTotalProfit(DateTime drawDate, string gameType, string domainIds);

			protected abstract IEnumerable<CompletedKenoTicket> WinnerTicketsForDrawingsReport(DateTime startDate, DateTime endDate, string accountNumber, string ticketNumber, string domainIds);
			protected abstract IEnumerable<CompletedKenoTicket> LoserTicketsForDrawingsReport(DateTime startDate, DateTime endDate, string accountNumber, string ticketNumber, string domainIds);
			protected abstract IEnumerable<CompletedKenoTicket> NoActionTicketsForDrawingsReport(DateTime startDate, DateTime endDate, string accountNumber, string ticketNumber, string domainIds);

			protected abstract IEnumerable<CompletedKenoTicket> WinnerTicketsForDrawingsReport(DateTime drawDate, string accountNumber, string ticketNumber, string domainIds);
			protected abstract IEnumerable<CompletedKenoTicket> LoserTicketsForDrawingsReport(DateTime drawDate, string accountNumber, string ticketNumber, string domainIds);
			protected abstract IEnumerable<CompletedKenoTicket> NoActionTicketsForDrawingsReport(DateTime drawDate, string accountNumber, string ticketNumber, string domainIds);

			protected abstract List<TotalProfitByDrawingRecord> FilteredTotalProfitByDrawingRecords(DateTime startDate, DateTime endDate, string domainIds);

			protected abstract IEnumerable<CompletedKenoTicket> WinnerTicketsForWinnersReport(DateTime startDate, DateTime endDate, string accountNumber, string domainIds);

			internal abstract CompletedKenoDraws LastPlayedDrawingsOfPlayer(string accountNumber);

			internal CompletedKenoDraws GenerateDrawingsReport(DateTime startDate, DateTime endDate, string accountNumber, string ticketNumber, string domainIds)
			{
				var winners = WinnerTicketsForDrawingsReport(startDate, endDate, accountNumber, ticketNumber, domainIds);
				var losers = LoserTicketsForDrawingsReport(startDate, endDate, accountNumber, ticketNumber, domainIds);
				var noActions = NoActionTicketsForDrawingsReport(startDate, endDate, accountNumber, ticketNumber, domainIds);
				var report = GenerateRecordsForDrawingReport(winners, losers, noActions);
				return report;
			}

			internal TicketsPerPlayersInCompletedKenoDraws GenerateTicketsPerPlayersInDrawingReport(DateTime startDate, DateTime endDate, string accountNumber, string ticketNumber, string domainIds)
			{
				var winners = WinnerTicketsForDrawingsReport(startDate, endDate, accountNumber, ticketNumber, domainIds);
				var losers = LoserTicketsForDrawingsReport(startDate, endDate, accountNumber, ticketNumber, domainIds);
				var noActions = NoActionTicketsForDrawingsReport(startDate, endDate, accountNumber, ticketNumber, domainIds);
				var report = GenerateRecordsForTicketsPerPlayersInDrawingReport(winners, losers, noActions);
				return report;
			}

			internal TicketsPerPlayersInCompletedKenoDraws GenerateTicketsPerPlayersInDrawingReport(DateTime drawDate, string accountNumber, string ticketNumber, string domainIds)
			{
				var winners = WinnerTicketsForDrawingsReport(drawDate, accountNumber, ticketNumber, domainIds);
				var losers = LoserTicketsForDrawingsReport(drawDate, accountNumber, ticketNumber, domainIds);
				var noActions = NoActionTicketsForDrawingsReport(drawDate, accountNumber, ticketNumber, domainIds);
				var report = GenerateRecordsForTicketsPerPlayersInDrawingReport(winners, losers, noActions);
				return report;
			}

			protected CompletedKenoDraws GenerateRecordsForDrawingReport(IEnumerable<CompletedKenoTicket> winners, IEnumerable<CompletedKenoTicket> losers, IEnumerable<CompletedKenoTicket> noActions)
			{
				var completedDraws = new CompletedKenoDraws(winners, losers, noActions);
				return completedDraws;
			}

			protected TicketsPerPlayersInCompletedKenoDraws GenerateRecordsForTicketsPerPlayersInDrawingReport(IEnumerable<CompletedKenoTicket> winners, IEnumerable<CompletedKenoTicket> losers, IEnumerable<CompletedKenoTicket> noActions)
			{
				var completedDraws = new CompletedKenoDraws(winners, losers, noActions);
				var ticketsPerPlayers = new TicketsPerPlayersInCompletedKenoDraws(completedDraws);
				return ticketsPerPlayers;
			}

			internal TotalProfitByDrawingReport GenerateTotalProfitByDrawingReport(DateTime startDate, DateTime endDate, DateTime now, string domainIds)
			{
				var totalProfitRecordsForToday = new List<TotalProfitByDrawingRecord>();
				var isIncludedToday = endDate.Date == now.Date;
				if (isIncludedToday)
				{
					var winners = WinnerTicketsForDrawingsReport(now, endDate, string.Empty, string.Empty, domainIds);
					var losers = LoserTicketsForDrawingsReport(now, endDate, string.Empty, string.Empty, domainIds);
					var noActions = NoActionTicketsForDrawingsReport(now, endDate, string.Empty, string.Empty, domainIds);
					totalProfitRecordsForToday = AccumulateTotalProfitByDrawingRecords(winners, losers, noActions);
				}

				var totalProfitByDrawingRecords = FilteredTotalProfitByDrawingRecords(startDate, endDate, domainIds);
				var report = new TotalProfitByDrawingReport(totalProfitByDrawingRecords, totalProfitRecordsForToday, now);
				return report;
			}

			internal IEnumerable<KenoWinnerRecord> GenerateWinnersReport(DateTime startDate, DateTime endDate, string accountNumber, string domainIds)
			{
				var winners = WinnerTicketsForWinnersReport(startDate, endDate, accountNumber, domainIds);
				var records = GenerateRecordsForWinnersReport(winners);
				return records.ToList();
			}

			protected IEnumerable<KenoWinnerRecord> GenerateRecordsForWinnersReport(IEnumerable<CompletedKenoTicket> winners)
			{
				var records = winners.Select(x => new KenoWinnerRecord
				{
					AccountNumber = x.AccountNumber,
					Date = x.DrawDate,
					TicketNumber = x.TicketNumber.ToString(),
					DrawId = x.DrawId,
					DomainUrl = x.DomainUrl,
					DomainId = x.DomainId,
					AffiliateId = x.AffiliateId,
					Amount = x.BetAmount,
					TicketCost = x.TicketAmount,
					WinnerNumber = x.WinnerNumber,
					Prize = x.Prize
				});
				return records;
			}

			internal DailyTotalProfitReport GenerateDailyTotalProfitReport(DateTime startDate, DateTime endDate, DateTime now, string domainIds)
			{
				var dailyTotalProfitRecords = FilteredDailyTotalProfitRecords2(startDate, endDate, domainIds);
				var report = new DailyTotalProfitReport(dailyTotalProfitRecords, Enumerable.Empty<DailyTotalProfitRecord2>());
				return report;
			}

			protected abstract List<DailyTotalProfitRecord2> FilteredDailyTotalProfitRecords2(DateTime startDate, DateTime endDate, string domainIds);

			internal List<DailyTotalProfitRecord2> AccumulateDailyTotalProfitKeno(DateTime now)
			{
				var gameTypeAll = ALL;
				var domainIdAll = ALL;

				bool tablesAlreadyExists = ExistsTable(TABLE_KENO_DAILY_TOTAL_PROFIT);
				if (!tablesAlreadyExists) CreateDailyTotalProfitStorage();

				var winners = WinnerKenoTicketsForDailyTotalProfit(now, gameTypeAll, domainIdAll);
				var losers = LoserKenoTicketsForDailyTotalProfit(now, gameTypeAll, domainIdAll);
				var noActions = NoActionKenoTicketsForDailyTotalProfit(now, gameTypeAll, domainIdAll);
				var dailyTotalProfitRecords = AccumulateDailyTotalProfitRecords2(winners, losers, noActions);
				SaveInDailyTotalProfitStorage(dailyTotalProfitRecords);
				return dailyTotalProfitRecords;
			}

			internal void AccumulateTotalProfitByDrawingKeno(DateTime now)
			{
				if (!ExistsTable(TABLE_KENO_TOTAL_PROFIT_BY_DRAWING)) CreateTotalProfitByDrawingStorage();

				var allHours = ALL;
				var allMinutes = ALL;
				var allAccountNumber = ALL;
				var allGameType = ALL;
				var allTicketNumbers = ALL;
				var allDomainId = ALL;

				var winners = WinnerKenoTicketsForDrawingsReport(now, allHours, allMinutes, allAccountNumber, allGameType, allTicketNumbers, allDomainId);
				var losers = LoserKenoTicketsForDrawingsReport(now, allHours, allMinutes, allAccountNumber, allGameType, allTicketNumbers, allDomainId);
				var noActions = NoActionKenoTicketsForDrawingsReport(now, allHours, allMinutes, allAccountNumber, allGameType, allTicketNumbers, allDomainId);
				var totalProfitByDrawingRecords = AccumulateTotalProfitByDrawingRecords(winners, losers, noActions);

				SaveInTotalProfitByDrawingStorage(totalProfitByDrawingRecords);
			}

            internal abstract DateTime LastDateInDailyTotalProfit();

            internal List<DailyTotalProfitRecord2> AccumulateDailyTotalProfitRecords2(IEnumerable<CompletedKenoTicket> winners, IEnumerable<CompletedKenoTicket> losers, IEnumerable<CompletedKenoTicket> noActions = null)
			{
				var tickets = new SortedDictionary<DailyProfitKey, DailyTotalProfitRecord2>();
				foreach (var winner in winners)
				{
                    var key = new DailyProfitKey(winner.DrawDate.Date, IdOfLottery.KN.ToString(), winner.DomainId, winner.AffiliateId, winner.CurrencyId);
                    var value = tickets.GetValueOrDefault(key);
					if (value == null)
					{
						value = new DailyTotalProfitRecord2(winner.DrawDate.Date, IdOfLottery.KN.ToString(), winner.DomainId, winner.AffiliateId, winner.DomainUrl, winner.CurrencyId);
						tickets.Add(key, value);
					}
					value.AddTicketInfo(winner, true);
				}

				foreach (var loser in losers)
				{
                    var key = new DailyProfitKey(loser.DrawDate.Date, IdOfLottery.KN.ToString(), loser.DomainId, loser.AffiliateId, loser.CurrencyId);
                    var value = tickets.GetValueOrDefault(key);
					if (value == null)
					{
						value = new DailyTotalProfitRecord2(loser.DrawDate.Date, IdOfLottery.KN.ToString(), loser.DomainId, loser.AffiliateId, loser.DomainUrl, loser.CurrencyId);
						tickets.Add(key, value);
					}
					value.AddTicketInfo(loser, false);
				}

				if (noActions != null)
				{
					foreach (var noAction in noActions)
					{
                        var key = new DailyProfitKey(noAction.DrawDate.Date, IdOfLottery.KN.ToString(), noAction.DomainId, noAction.AffiliateId, noAction.CurrencyId);
						var value = tickets.GetValueOrDefault(key);
						if (value == null)
						{
							value = new DailyTotalProfitRecord2(noAction.DrawDate.Date, IdOfLottery.KN.ToString(), noAction.DomainId, noAction.AffiliateId, noAction.DomainUrl, noAction.CurrencyId);
							tickets.Add(key, value);
						}
						value.AddTicketNoActionInfo(noAction);
					}
				}

				return tickets.Values.ToList();
			}

			internal List<TotalProfitByDrawingRecord> AccumulateTotalProfitByDrawingRecords(IEnumerable<CompletedKenoTicket> winners, IEnumerable<CompletedKenoTicket> losers, IEnumerable<CompletedKenoTicket> noActions)
			{
				var tickets = new SortedDictionary<DateTime, SortedDictionary<int, SortedDictionary<int, TotalProfitByKenoDrawingRecord>>>();
				foreach (var winner in winners)
				{
					var totalProfitByDrawingPerAffiliates = tickets.GetValueOrDefault(winner.DrawDate);
					if (totalProfitByDrawingPerAffiliates == null)
					{
						totalProfitByDrawingPerAffiliates = new SortedDictionary<int, SortedDictionary<int, TotalProfitByKenoDrawingRecord>>();
						tickets.Add(winner.DrawDate, totalProfitByDrawingPerAffiliates);
					}
					var totalProfitByDrawingPerDomains = totalProfitByDrawingPerAffiliates.GetValueOrDefault(winner.AffiliateId);
					if (totalProfitByDrawingPerDomains == null)
					{
						totalProfitByDrawingPerDomains = new SortedDictionary<int, TotalProfitByKenoDrawingRecord>();
						totalProfitByDrawingPerAffiliates.Add(winner.AffiliateId, totalProfitByDrawingPerDomains);
					}
					var value = totalProfitByDrawingPerDomains.GetValueOrDefault(winner.DomainId);
					if (value == null)
					{
						value = new TotalProfitByKenoDrawingRecord(winner.DrawDate, winner.AffiliateId, winner.DomainId);
						totalProfitByDrawingPerDomains.Add(winner.DomainId, value);
					}

					value.AddTicketInfo(winner, true);
				}

				foreach (var loser in losers)
				{
					var totalProfitByDrawingPerAffiliates = tickets.GetValueOrDefault(loser.DrawDate);
					if (totalProfitByDrawingPerAffiliates == null)
					{
						totalProfitByDrawingPerAffiliates = new SortedDictionary<int, SortedDictionary<int, TotalProfitByKenoDrawingRecord>>();
						tickets.Add(loser.DrawDate, totalProfitByDrawingPerAffiliates);
					}
					var totalProfitByDrawingPerDomains = totalProfitByDrawingPerAffiliates.GetValueOrDefault(loser.AffiliateId);
					if (totalProfitByDrawingPerDomains == null)
					{
						totalProfitByDrawingPerDomains = new SortedDictionary<int, TotalProfitByKenoDrawingRecord>();
						totalProfitByDrawingPerAffiliates.Add(loser.AffiliateId, totalProfitByDrawingPerDomains);
					}
					var value = totalProfitByDrawingPerDomains.GetValueOrDefault(loser.DomainId);
					if (value == null)
					{
						value = new TotalProfitByKenoDrawingRecord(loser.DrawDate, loser.AffiliateId, loser.DomainId);
						totalProfitByDrawingPerDomains.Add(loser.DomainId, value);
					}

					value.AddTicketInfo(loser, true);
				}

				foreach (var noAction in noActions)
				{
					var totalProfitByDrawingPerAffiliates = tickets.GetValueOrDefault(noAction.DrawDate);
					if (totalProfitByDrawingPerAffiliates == null)
					{
						totalProfitByDrawingPerAffiliates = new SortedDictionary<int, SortedDictionary<int, TotalProfitByKenoDrawingRecord>>();
						tickets.Add(noAction.DrawDate, totalProfitByDrawingPerAffiliates);
					}
					var totalProfitByDrawingPerDomains = totalProfitByDrawingPerAffiliates.GetValueOrDefault(noAction.AffiliateId);
					if (totalProfitByDrawingPerDomains == null)
					{
						totalProfitByDrawingPerDomains = new SortedDictionary<int, TotalProfitByKenoDrawingRecord>();
						totalProfitByDrawingPerAffiliates.Add(noAction.AffiliateId, totalProfitByDrawingPerDomains);
					}
					var value = totalProfitByDrawingPerDomains.GetValueOrDefault(noAction.DomainId);
					if (value == null)
					{
						value = new TotalProfitByKenoDrawingRecord(noAction.DrawDate, noAction.AffiliateId, noAction.DomainId);
						totalProfitByDrawingPerDomains.Add(noAction.DomainId, value);
					}

					value.AddTicketNoActionInfo(noAction);
				}

				return tickets.Values.SelectMany(draws=>draws.Values.SelectMany(draws => draws.Values)).Cast<TotalProfitByDrawingRecord>().ToList();
			}
		}

		private class KenoDBHandlerMySQL : KenoDBHandler
		{
			private const string COMMON_JOIN_FOR_TICKETS_KENO = @"INNER JOIN kenodrawings DR ON T.DRAWINGID = DR.ID
                                                            INNER JOIN l900domains DO ON T.DOMAINID = DO.ID ";

			internal KenoDBHandlerMySQL(string connectionString) : base(connectionString)
			{
			}

			internal KenoDBHandlerMySQL(Company company, string connectionString) : base(company, connectionString)
			{
			}

			internal override bool ExistsTable(string table)
			{
				bool exists = true;
				string sql = $"SELECT 1 FROM { table } LIMIT 1";
				using (MySqlConnection connection = new MySqlConnection(connectionString))
				{
					try
					{
						connection.Open();
						using (MySqlCommand command = new MySqlCommand(sql, connection))
						{
							var dataReader = command.ExecuteReader();
							dataReader.Close();
						}
					}
					catch
					{
						exists = false;
					}
					finally
					{
						connection.Close();
					}
				}
				return exists;
			}

            internal override DateTime LastDateInDailyTotalProfit()
            {
                if (!ExistsTable(TABLE_KENO_DAILY_TOTAL_PROFIT)) throw new GameEngineException($"There is no table {TABLE_KENO_DAILY_TOTAL_PROFIT} to get date");

                DateTime lastDate = default;
                var sql = $"SELECT MAX(DATE) FROM {TABLE_KENO_DAILY_TOTAL_PROFIT};";

                using (MySqlConnection connection = new MySqlConnection(connectionString))
                {
                    try
                    {
                        connection.Open();
                        using (MySqlCommand command = new MySqlCommand(sql, connection))
                        {
                            var result = command.ExecuteScalar();
                            if (result != null && result != DBNull.Value)
                            {
                                lastDate = (DateTime)result;
                            }
                        }
                    }
                    catch (Exception e)
                    {
                        throw new GameEngineException($"MySQL Error [{sql}]. {e.Message}");
                    }
                }

                return lastDate;
            }

            protected override void ExecuteCommand(string cmdText)
			{
				using (MySqlConnection connection = new MySqlConnection(connectionString))
				{
					try
					{
						connection.Open();
						using (MySqlCommand command = new MySqlCommand(cmdText, connection))
						{
							command.ExecuteNonQuery();
						}
					}
					catch (Exception e)
					{
						Loggers.GetIntance().Db.Error($@"sql:{cmdText} type:{e.GetType()} error:{e.Message}", e);
						throw new GameEngineException("MySQL Error [" + cmdText + "]");
					}
					finally
					{
						connection.Close();
					}
				}
			}

			protected override IEnumerable<CompletedKenoTicket> WinnerKenoTickets(string command)
			{
				DateTime tempDate;
				var winnerList = new List<CompletedKenoTicket>();
				using (MySqlConnection connection = new MySqlConnection(connectionString))
				{
					connection.Open();
					using (MySqlCommand cmd = new MySqlCommand(command, connection))
					{
						using (MySqlDataReader reader = cmd.ExecuteReader())
						{
							while (reader.Read())
							{
								tempDate = reader.GetDateTime(0);
								var info = new CompletedKenoTicket(
									company: this.Company,
									draw: reader.GetString(4),
									gradedBy: reader.GetString(5),
									prize: reader.GetDecimal(10),
									bulleyePrize: reader.GetDecimal(11),
									drawDate: tempDate,
									accountNumber: reader.GetString(1),
									ticket: reader.GetString(2),
									amount: reader.GetDecimal(3),
									action: (GameboardStatus)reader.GetByte(6),
									drawingId: reader.GetInt32(13),
									creation: reader.GetDateTime(7),
									ticketNumber: reader.GetInt32(8),
									profit: reader.GetDecimal(9),
									prizesVersion: reader.GetInt32(12),
									domainId: reader.GetInt32(14),
									domainUrl: reader.GetString(15),
									currencyId: reader.GetInt32(16),
									isWinner: true,
									isNoAction: false
								);

								winnerList.Add(info);
							}
						}
					}
					connection.Close();
				}
				return winnerList;
			}

			protected override IEnumerable<CompletedKenoTicket> LoserKenoTickets(string command)
			{
				DateTime tempDate;
				var looserList = new List<CompletedKenoTicket>();
				using (MySqlConnection connection = new MySqlConnection(connectionString))
				{
					connection.Open();
					using (MySqlCommand cmd = new MySqlCommand(command, connection))
					{
						using (MySqlDataReader reader = cmd.ExecuteReader())
						{
							while (reader.Read())
							{
								tempDate = reader.GetDateTime(0);
								var info = new CompletedKenoTicket(
									company: this.Company,
									draw: reader.GetString(4),
									gradedBy: reader.GetString(5),
									drawDate: tempDate,
									accountNumber: reader.GetString(1),
									ticket: reader.GetString(2),
									amount: reader.GetDecimal(3),
									action: (GameboardStatus)reader.GetByte(6),
									drawingId: reader.GetInt32(11),
									creation: reader.GetDateTime(7),
									ticketNumber: reader.GetInt32(8),
									profit: reader.GetDecimal(9),
									prizesVersion: reader.GetInt32(10),
									domainId: reader.GetInt32(12),
									domainUrl: reader.GetString(13),
									currencyId: reader.GetInt32(14),
									prize:0,
									bulleyePrize:0,
									isWinner:false,
									isNoAction:false
								);
								looserList.Add(info);
							}
						}
					}
					connection.Close();
				}
				return looserList;
			}

			protected override IEnumerable<CompletedKenoTicket> NoActionKenoTickets(string command)
			{
				DateTime tempDate;
				var noActionList = new List<CompletedKenoTicket>();
				using (MySqlConnection connection = new MySqlConnection(connectionString))
				{
					connection.Open();
					using (MySqlCommand cmd = new MySqlCommand(command, connection))
					{
						using (MySqlDataReader reader = cmd.ExecuteReader())
						{
							while (reader.Read())
							{
								tempDate = reader.GetDateTime(0);
								var info = new CompletedKenoTicket(
									company: this.Company,
									gradedBy: reader.GetString(7),
									drawDate: tempDate,
									accountNumber: reader.GetString(1),
									ticket: reader.GetString(2),
									amount: reader.GetDecimal(3),
									action: (GameboardStatus)reader.GetByte(4),
									drawingId: reader.GetInt32(9),
									creation: reader.GetDateTime(5),
									ticketNumber: reader.GetInt32(6),
									prizesVersion: reader.GetInt32(8),
									domainId: reader.GetInt32(10),
									domainUrl: reader.GetString(11),
									currencyId: reader.GetInt32(12),
									draw:string.Empty,
									profit:0,
									prize: 0,
									bulleyePrize: 0,
									isWinner: false,
									isNoAction: true
								);

								noActionList.Add(info);
							}
						}
					}
					connection.Close();
				}
				return noActionList;
			}

			protected override CompletedKenoTicketsWithAccounts WinnerKenoTicketsWithAccounts(string command)
			{
				DateTime tempDate;
				CompletedKenoTicketsWithAccounts winners = new CompletedKenoTicketsWithAccounts();

				using (MySqlConnection connection = new MySqlConnection(connectionString))
				{
					connection.Open();
					using (MySqlCommand cmd = new MySqlCommand(command, connection))
					{
						using (DbDataReader reader = cmd.ExecuteReader())
						{
							while (reader.Read())
							{
								string accountNumber = reader.GetString(1);
								int affiliateId = reader.GetInt32(12);

								AffiliateWithAccount account = winners.AddAccount(accountNumber, affiliateId);

								tempDate = reader.GetDateTime(0);
								var info = new CompletedKenoTicket(
									company: Company,
									drawDate: tempDate,
									ticket: reader.GetString(2),
									amount: reader.GetDecimal(3),
									draw: reader.GetString(4),
									gradedBy: reader.GetString(5),
									action: (GameboardStatus)reader.GetByte(6),
									creation: reader.GetDateTime(7),
									ticketNumber: reader.GetInt32(8),
									profit: reader.GetDecimal(9),
									prize: reader.GetDecimal(10),
									bulleyePrize: reader.GetDecimal(11),
									accountNumber: accountNumber,
									domainId: reader.GetInt32(15),
									domainUrl: reader.GetString(16),
									currencyId: reader.GetInt32(17),
									drawingId: reader.GetInt32(14),
									prizesVersion: reader.GetInt32(13),
									isWinner: true,
									isNoAction: false
								);
								info.AffiliateId = affiliateId;
								winners.Add(info);
							}
						}
					}
					connection.Close();
				}
				return winners;
			}

			protected override List<TotalProfitByDrawingRecord> TotalProfitByDrawingRecords(string command)
			{
				var records = new List<TotalProfitByDrawingRecord>();
				using (MySqlConnection connection = new MySqlConnection(connectionString))
				{
					connection.Open();
					using (MySqlCommand cmd = new MySqlCommand(command, connection))
					{
						using (MySqlDataReader reader = cmd.ExecuteReader())
						{
							while (reader.Read())
							{
								var info = new TotalProfitByKenoDrawingRecord(
									date: reader.GetDateTime(0),
									ticketsCount: reader.GetInt32(1),
									playersCount: reader.GetInt32(2),
									sold: reader.GetDecimal(3),
									prizes: reader.GetDecimal(4),
									profits: reader.GetDecimal(5),
									affiliateId: reader.GetInt32(6),
									domainId: reader.GetInt32(7),
									domainUrl: reader.GetString(8),
									drawId: reader.GetInt32(9)
								);
								records.Add(info);
							}
						}
					}
					connection.Close();
				}
				return records;
			}

			protected override List<DailyTotalProfitRecord2> DailyTotalProfitRecords2(string command)
			{
				var records = new List<DailyTotalProfitRecord2>();
				using (MySqlConnection connection = new MySqlConnection(connectionString))
				{
					connection.Open();
					using (MySqlCommand cmd = new MySqlCommand(command, connection))
					{
						using (DbDataReader reader = cmd.ExecuteReader())
						{
							DailyTotalProfitRecord2 info;
							while (reader.Read())
							{
								info = new DailyTotalProfitRecord2(
									date: reader.GetDateTime(0),
									gameType: reader.GetString(1),
									domainId: reader.GetInt32(7),
									affiliateId: reader.GetInt32(9),
									currencyId: reader.GetInt32(10),
									ticketsCount: reader.GetInt32(2),
									winnersCount: reader.GetInt32(3),
									sold: reader.GetDecimal(4),
									prizes: reader.GetDecimal(5),
									profits: reader.GetDecimal(6),
									domainUrl: reader.GetString(8)
								);
								records.Add(info);
							}
						}
					}
					connection.Close();
				}
				return records;
			}



			protected override CompletedKenoDraws GetDrawings(string command)
			{
				var result = new CompletedKenoDraws();
				using (MySqlConnection connection = new MySqlConnection(connectionString))
				{
					connection.Open();
					using (MySqlCommand cmd = new MySqlCommand(command, connection))
					{
						using (MySqlDataReader reader = cmd.ExecuteReader())
						{
							while (reader.Read())
							{
								var info = new CompletedKenoDraw(
									drawDate: reader.GetDateTime(0),
									drawId: reader.GetInt32(1)
								);

								result.Add(info);
							}
						}
					}
					connection.Close();
				}
				return result;
			}

			internal override IEnumerable<CompletedKenoTicket> WinnerTicketsOfPlayerBetween(DateTime startDate, DateTime endDate, string accountNumber)
			{
				var command = $@"{COMMON_SELECT_FOR_KENO_WINNERS}
                    from {LottoStorage.TABLE_KENO_WINNERS} T {COMMON_JOIN_FOR_TICKETS_KENO}   
                    where DATE(date)<='{DateToString(endDate)}' and DATE(date)>='{DateToString(startDate)}' and account='{accountNumber.ToUpper()}' 
						and timestamp = 0
                    order by date;";
				var result = WinnerKenoTickets(command);
				return result;
			}

			internal override IEnumerable<CompletedKenoTicket> LoserTicketsOfPlayerBetween(DateTime startDate, DateTime endDate, string accountNumber)
			{
				var command = $@"{COMMON_SELECT_FOR_KENO_LOSERS}
                    from {LottoStorage.TABLE_KENO_LOSERS} T {COMMON_JOIN_FOR_TICKETS_KENO} 
                    where DATE(date)<='{DateToString(endDate)}' and DATE(date)>='{DateToString(startDate)}' and account='{accountNumber.ToUpper()}' 
						and timestamp = 0
                    order by date;";
				var result = LoserKenoTickets(command);
				return result;
			}

			internal override IEnumerable<CompletedKenoTicket> NoActionTicketsOfPlayerBetween(DateTime startDate, DateTime endDate, string accountNumber)
			{
				var command = $@"{COMMON_SELECT_FOR_KENO_NOACTION}
                    from {LottoStorage.TABLE_KENO_NOACTIONS} T {COMMON_JOIN_FOR_TICKETS_KENO}
                    where DATE(date)<='{DateToString(endDate)}' and DATE(date)>='{DateToString(startDate)}' and account='{accountNumber.ToUpper()}' 
						and timestamp = 0
                    order by date;";
				var result = NoActionKenoTickets(command);
				return result;
			}

			internal override IEnumerable<CompletedKenoTicket> WinnerTicketsOfPlayer(DateTime drawDate, string accountNumber)
			{
				var command = $@"{COMMON_SELECT_FOR_KENO_WINNERS}
                    from {LottoStorage.TABLE_KENO_WINNERS} T {COMMON_JOIN_FOR_TICKETS_KENO}   
                    where date='{DateTimeToString(drawDate)}' and account='{accountNumber.ToUpper()}' 
						and timestamp = 0
                    order by date;";
				var result = WinnerKenoTickets(command);
				return result;
			}

			internal override IEnumerable<CompletedKenoTicket> LoserTicketsOfPlayer(DateTime drawDate, string accountNumber)
			{
				var command = $@"{COMMON_SELECT_FOR_KENO_LOSERS}
                    from {LottoStorage.TABLE_KENO_LOSERS} T {COMMON_JOIN_FOR_TICKETS_KENO} 
                    where date='{DateTimeToString(drawDate)}' and account='{accountNumber.ToUpper()}' 
						and timestamp = 0
                    order by date;";
				var result = LoserKenoTickets(command);
				return result;
			}

			internal override IEnumerable<CompletedKenoTicket> NoActionTicketsOfPlayer(DateTime drawDate, string accountNumber)
			{
				var command = $@"{COMMON_SELECT_FOR_KENO_NOACTION}
                    from {LottoStorage.TABLE_KENO_NOACTIONS} T {COMMON_JOIN_FOR_TICKETS_KENO}
                    where date='{DateTimeToString(drawDate)}' and account='{accountNumber.ToUpper()}' 
						and timestamp = 0
                    order by date;";
				var result = NoActionKenoTickets(command);
				return result;
			}

			protected override IEnumerable<CompletedKenoTicket> WinnerKenoTicketsForDrawingsReport(DateTime drawDate, string hour, string minute, string accountNumber, string gameType, string ticketNumber, string domainIds)
			{
				var hourFilter = string.IsNullOrWhiteSpace(hour) ? string.Empty : $" and hour(date)={hour}";
				var minuteFilter = string.IsNullOrWhiteSpace(minute) ? string.Empty : $" and minute(date)={minute}";
				var accountNumberFilter = string.IsNullOrWhiteSpace(accountNumber) ? string.Empty : $" and T.account='{accountNumber.ToUpper()}'";
				var ticketAndWagerNumber = ticketNumber.Split('-');
				var ticketNumberFilter = string.IsNullOrWhiteSpace(ticketAndWagerNumber[0]) ? string.Empty : $" and ticketnumber={ticketAndWagerNumber[0]}";
				var gameTypeFilter = string.IsNullOrWhiteSpace(gameType) ? string.Empty : $" and left(ticket,2) ='{gameType}'";
				var domainIdFilter = string.IsNullOrWhiteSpace(domainIds) ? string.Empty : $" and domainid in ({domainIds})";

				var command = $@"select date, T.account, ticket, amount, draw, gradedby, action, creation, ticketnumber, profit, prize, bulleyeprize, IFNULL(affiliateid,{AffiliateData.DefaultAffiliateId}), prizesversion, drawingid, domainid, url, currencyid
                    from {LottoStorage.TABLE_KENO_WINNERS} T {COMMON_JOIN_FOR_TICKETS_KENO}
					LEFT OUTER JOIN {TABLE_USER_PER_AFFILIATE} US on T.account = US.account
                    where DATE(date)='{DateToString(drawDate)}' 
						and timestamp = 0 
                    {hourFilter} {minuteFilter} {accountNumberFilter} {ticketNumberFilter} {gameTypeFilter} {domainIdFilter}";
				var winners = WinnerKenoTicketsWithAccounts(command);
				var result = winners.Tickets;
				return result;
			}

			protected override IEnumerable<CompletedKenoTicket> LoserKenoTicketsForDrawingsReport(DateTime drawDate, string hour, string minute, string accountNumber, string gameType, string ticketNumber, string domainIds)
			{
				var hourFilter = string.IsNullOrWhiteSpace(hour) ? string.Empty : $" and hour(date)={hour}";
				var minuteFilter = string.IsNullOrWhiteSpace(minute) ? string.Empty : $" and minute(date)={minute}";
				var accountNumberFilter = string.IsNullOrWhiteSpace(accountNumber) ? string.Empty : $" and T.account='{accountNumber.ToUpper()}'";
				var ticketAndWagerNumber = ticketNumber.Split('-');
				var ticketNumberFilter = string.IsNullOrWhiteSpace(ticketAndWagerNumber[0]) ? string.Empty : $" and ticketnumber={ticketAndWagerNumber[0]}";
				var gameTypeFilter = string.IsNullOrWhiteSpace(gameType) ? string.Empty : $" and left(ticket,2) ='{gameType}'";
				var domainIdFilter = string.IsNullOrWhiteSpace(domainIds) ? string.Empty : $" and domainid in ({domainIds})";

				var command = $@"select date, T.account, ticket, amount, draw, gradedby, action, creation, ticketnumber, profit, 0.0, 0.0, IFNULL(affiliateid,{AffiliateData.DefaultAffiliateId}), prizesversion, drawingid, domainid, url, currencyid
                    from {LottoStorage.TABLE_KENO_LOSERS} T {COMMON_JOIN_FOR_TICKETS_KENO}
					LEFT OUTER JOIN {TABLE_USER_PER_AFFILIATE} US on T.account = US.account
                    where DATE(date)='{DateToString(drawDate)}' 
						and timestamp = 0 
                    {hourFilter} {minuteFilter} {accountNumberFilter} {ticketNumberFilter} {gameTypeFilter} {domainIdFilter}";
				var winners = WinnerKenoTicketsWithAccounts(command);
				var result = winners.Tickets;
				return result;
			}

			protected override IEnumerable<CompletedKenoTicket> NoActionKenoTicketsForDrawingsReport(DateTime drawDate, string hour, string minute, string accountNumber, string gameType, string ticketNumber, string domainIds)
			{
				var hourFilter = string.IsNullOrWhiteSpace(hour) ? string.Empty : $" and hour(date)={hour}";
				var minuteFilter = string.IsNullOrWhiteSpace(minute) ? string.Empty : $" and minute(date)={minute}";
				var accountNumberFilter = string.IsNullOrWhiteSpace(accountNumber) ? string.Empty : $" and T.account='{accountNumber.ToUpper()}'";
				var ticketAndWagerNumber = ticketNumber.Split('-');
				var ticketNumberFilter = string.IsNullOrWhiteSpace(ticketAndWagerNumber[0]) ? string.Empty : $" and ticketnumber={ticketAndWagerNumber[0]}";
				var gameTypeFilter = string.IsNullOrWhiteSpace(gameType) ? string.Empty : $" and left(ticket,2) ='{gameType}'";
				var domainIdFilter = string.IsNullOrWhiteSpace(domainIds) ? string.Empty : $" and domainid in ({domainIds})";

				var command = $@"select date, T.account, ticket, amount, '{NO_DRAW}', noactionby, action, creation, ticketnumber, 0.0, 0.0, 0.0, IFNULL(affiliateid,{AffiliateData.DefaultAffiliateId}), prizesversion, drawingid, domainid, url, currencyid
                    from {LottoStorage.TABLE_KENO_NOACTIONS} T {COMMON_JOIN_FOR_TICKETS_KENO}
					LEFT OUTER JOIN {TABLE_USER_PER_AFFILIATE} US on T.account = US.account
                    where DATE(date)='{DateToString(drawDate)}'  
						and timestamp = 0 
                    {hourFilter} {minuteFilter} {accountNumberFilter} {ticketNumberFilter} {gameTypeFilter} {domainIdFilter}";
				var winners = WinnerKenoTicketsWithAccounts(command);
				var result = winners.Tickets;
				return result;
			}

			protected override IEnumerable<CompletedKenoTicket> WinnerKenoTicketsForDailyTotalProfit(DateTime startDate, string gameType, string domainIds)
			{
				var gameTypeFilter = string.IsNullOrWhiteSpace(gameType) ? string.Empty : $" and left(ticket,2) ='{gameType}'";
				var domainIdFilter = string.IsNullOrWhiteSpace(domainIds) ? string.Empty : $" and domainid in ({domainIds})";

				var command = $@"select date, T.account, ticket, amount, draw, gradedby, action, creation, ticketnumber, profit, prize, bulleyeprize, IFNULL(affiliateid,{AffiliateData.DefaultAffiliateId}), prizesversion, drawingid, domainid, url, currencyid
					from {LottoStorage.TABLE_KENO_WINNERS} T {COMMON_JOIN_FOR_TICKETS_KENO} 
					LEFT OUTER JOIN {TABLE_USER_PER_AFFILIATE} US on T.account = US.account
					where DATE(date)='{DateToString(startDate)}' {gameTypeFilter} {domainIdFilter}
						and timestamp = 0 
					order by date;";
				var winners = WinnerKenoTicketsWithAccounts(command);
				var result = winners.Tickets;
				return result;
			}

			protected override IEnumerable<CompletedKenoTicket> LoserKenoTicketsForDailyTotalProfit(DateTime startDate, string gameType, string domainIds)
			{
				var gameTypeFilter = string.IsNullOrWhiteSpace(gameType) ? string.Empty : $" and left(ticket,2) ='{gameType}'";
				var domainIdFilter = string.IsNullOrWhiteSpace(domainIds) ? string.Empty : $" and domainid in ({domainIds})";

				var command = $@"select date, T.account, ticket, amount, draw, gradedby, action, creation, ticketnumber, profit, 0.0, 0.0, IFNULL(affiliateid,{AffiliateData.DefaultAffiliateId}), prizesversion, drawingid, domainid, url, currencyid
					from {LottoStorage.TABLE_KENO_LOSERS} T {COMMON_JOIN_FOR_TICKETS_KENO}
					LEFT OUTER JOIN {TABLE_USER_PER_AFFILIATE} US on T.account = US.account
					where DATE(date)='{DateToString(startDate)}' {gameTypeFilter} {domainIdFilter}
						and timestamp = 0
					order by date;";
				var winners = WinnerKenoTicketsWithAccounts(command);
				var result = winners.Tickets;
				return result;
			}

			protected override IEnumerable<CompletedKenoTicket> NoActionKenoTicketsForDailyTotalProfit(DateTime startDate, string gameType, string domainIds)
			{
				var gameTypeFilter = string.IsNullOrWhiteSpace(gameType) ? string.Empty : $" and left(ticket,2) ='{gameType}'";
				var domainIdFilter = string.IsNullOrWhiteSpace(domainIds) ? string.Empty : $" and domainid in ({domainIds})";

				var command = $@"select date, T.account, ticket, amount, '{NO_DRAW}', noactionby, action, creation, ticketnumber, 0.0, 0.0, 0.0, IFNULL(affiliateid,{AffiliateData.DefaultAffiliateId}), prizesversion, drawingid, domainid, url, currencyid
					from {LottoStorage.TABLE_KENO_NOACTIONS} T {COMMON_JOIN_FOR_TICKETS_KENO}
					LEFT OUTER JOIN {TABLE_USER_PER_AFFILIATE} US on T.account = US.account
					where DATE(date)='{DateToString(startDate)}' {gameTypeFilter} {domainIdFilter}
						and timestamp = 0
					order by date;";
				var winners = WinnerKenoTicketsWithAccounts(command);
				var result = winners.Tickets;
				return result;
			}

			internal override void InsertAffiliateIfNotExist(int id, string name, string accountNumber)
			{
				bool tablesAlreadyExists = ExistsTable(TABLE_KENO_DAILY_TOTAL_PROFIT);
				if (!tablesAlreadyExists) CreateDailyTotalProfitStorage();

				var sql = $"SELECT ID FROM {TABLE_AFFILIATES} WHERE ID = '{id}'";
				using (MySqlConnection connection = new MySqlConnection(connectionString))
				{
					try
					{
						connection.Open();
						using (MySqlCommand command = new MySqlCommand(sql.ToString(), connection))
						{
							bool itMustBeCreated = false;
							using (DbDataReader reader = command.ExecuteReader())
							{
								if (!reader.Read())
								{
									itMustBeCreated = true;
								}
							}
							if (itMustBeCreated)
							{
								sql = $"INSERT INTO {TABLE_AFFILIATES}(ID, NAME) VALUES ({id}, '{name}');";
								using (MySqlCommand command2 = new MySqlCommand(sql.ToString(), connection))
								{
									command2.CommandType = CommandType.Text;
									command2.ExecuteNonQuery();
								}
							}
						}

						sql = $"SELECT AFFILIATEID, ACCOUNT FROM {TABLE_USER_PER_AFFILIATE} WHERE ACCOUNT ='{accountNumber}' AND AFFILIATEID = {id};";
						using (MySqlCommand command = new MySqlCommand(sql.ToString(), connection))
						{
							bool itMustBeCreated = false;
							using (DbDataReader reader = command.ExecuteReader())
							{
								if (!reader.Read())
								{
									itMustBeCreated = true;
								}
							}

							if (itMustBeCreated)
							{
								sql = $"INSERT INTO {TABLE_USER_PER_AFFILIATE}(AFFILIATEID, ACCOUNT) VALUES ({id}, '{accountNumber}');";
								using (MySqlCommand command2 = new MySqlCommand(sql.ToString(), connection))
								{
									command2.CommandType = CommandType.Text;
									command2.ExecuteNonQuery();
								}
							}
						}
					}
					catch (Exception e)
					{
						throw new GameEngineException($"MySQL Error [{ sql.ToString() }]. {e.Message}");
					}
					finally
					{
						connection.Close();
					}
				}
			}


			protected override IEnumerable<CompletedKenoTicket> WinnerTicketsForDrawingsReport(DateTime startDate, DateTime endDate, string accountNumber, string ticketNumber, string domainIds)
			{
				var accountNumberFilter = string.IsNullOrWhiteSpace(accountNumber) ? string.Empty : $" and T.account='{accountNumber.ToUpper()}'";
				var ticketAndWagerNumber = ticketNumber.Split('-');
				var ticketNumberFilter = string.IsNullOrWhiteSpace(ticketAndWagerNumber[0]) ? string.Empty : $" and ticketnumber={ticketAndWagerNumber[0]}";
				var domainIdFilter = string.IsNullOrWhiteSpace(domainIds) ? string.Empty : $" and domainid in ({domainIds})";

				var command = $@"select date, T.account, ticket, amount, draw, gradedby, action, creation, ticketnumber, profit, prize, bulleyeprize, IFNULL(affiliateid,{AffiliateData.DefaultAffiliateId}), prizesversion, drawingid, domainid, url, currencyid
                    from {LottoStorage.TABLE_KENO_WINNERS} T {COMMON_JOIN_FOR_TICKETS_KENO}
					LEFT OUTER JOIN {TABLE_USER_PER_AFFILIATE} US on T.account = US.account
                    where DATE(date)>='{DateToString(startDate)}' and DATE(date)<='{DateToString(endDate)}' 
						and timestamp = 0 
                    {accountNumberFilter} {ticketNumberFilter} {domainIdFilter}";
				var winners = WinnerKenoTicketsWithAccounts(command);
				var result = winners.Tickets;
				return result;
			}

			protected override IEnumerable<CompletedKenoTicket> WinnerTicketsForDrawingsReport(DateTime drawDate, string accountNumber, string ticketNumber, string domainIds)
			{
				var accountNumberFilter = string.IsNullOrWhiteSpace(accountNumber) ? string.Empty : $" and T.account='{accountNumber.ToUpper()}'";
				var ticketAndWagerNumber = ticketNumber.Split('-');
				var ticketNumberFilter = string.IsNullOrWhiteSpace(ticketAndWagerNumber[0]) ? string.Empty : $" and ticketnumber={ticketAndWagerNumber[0]}";
				var domainIdFilter = string.IsNullOrWhiteSpace(domainIds) ? string.Empty : $" and domainid in ({domainIds})";

				var command = $@"select date, T.account, ticket, amount, draw, gradedby, action, creation, ticketnumber, profit, prize, bulleyeprize, IFNULL(affiliateid,{AffiliateData.DefaultAffiliateId}), prizesversion, drawingid, domainid, url, currencyid
                    from {LottoStorage.TABLE_KENO_WINNERS} T {COMMON_JOIN_FOR_TICKETS_KENO}
					LEFT OUTER JOIN {TABLE_USER_PER_AFFILIATE} US on T.account = US.account
                    where date='{DateTimeToString(drawDate)}' 
						and timestamp = 0 
                    {accountNumberFilter} {ticketNumberFilter} {domainIdFilter}";
				var winners = WinnerKenoTicketsWithAccounts(command);
				var result = winners.Tickets;
				return result;
			}

			protected override IEnumerable<CompletedKenoTicket> LoserTicketsForDrawingsReport(DateTime startDate, DateTime endDate, string accountNumber, string ticketNumber, string domainIds)
			{
				var accountNumberFilter = string.IsNullOrWhiteSpace(accountNumber) ? string.Empty : $" and T.account='{accountNumber.ToUpper()}'";
				var ticketAndWagerNumber = ticketNumber.Split('-');
				var ticketNumberFilter = string.IsNullOrWhiteSpace(ticketAndWagerNumber[0]) ? string.Empty : $" and ticketnumber={ticketAndWagerNumber[0]}";
				var domainIdFilter = string.IsNullOrWhiteSpace(domainIds) ? string.Empty : $" and domainid in ({domainIds})";

				var command = $@"select date, T.account, ticket, amount, draw, gradedby, action, creation, ticketnumber, profit, 0.0, 0.0, IFNULL(affiliateid,{AffiliateData.DefaultAffiliateId}), prizesversion, drawingid, domainid, url, currencyid
                    from {LottoStorage.TABLE_KENO_LOSERS} T {COMMON_JOIN_FOR_TICKETS_KENO}
					LEFT OUTER JOIN {TABLE_USER_PER_AFFILIATE} US on T.account = US.account
                    where DATE(date)>='{DateToString(startDate)}' and DATE(date)<='{DateToString(endDate)}' 
						and timestamp = 0 
                    {accountNumberFilter} {ticketNumberFilter} {domainIdFilter}";
				var winners = WinnerKenoTicketsWithAccounts(command);
				var result = winners.Tickets;
				return result;
			}

			protected override IEnumerable<CompletedKenoTicket> LoserTicketsForDrawingsReport(DateTime drawDate, string accountNumber, string ticketNumber, string domainIds)
			{
				var accountNumberFilter = string.IsNullOrWhiteSpace(accountNumber) ? string.Empty : $" and T.account='{accountNumber.ToUpper()}'";
				var ticketAndWagerNumber = ticketNumber.Split('-');
				var ticketNumberFilter = string.IsNullOrWhiteSpace(ticketAndWagerNumber[0]) ? string.Empty : $" and ticketnumber={ticketAndWagerNumber[0]}";
				var domainIdFilter = string.IsNullOrWhiteSpace(domainIds) ? string.Empty : $" and domainid in ({domainIds})";

				var command = $@"select date, T.account, ticket, amount, draw, gradedby, action, creation, ticketnumber, profit, 0.0, 0.0, IFNULL(affiliateid,{AffiliateData.DefaultAffiliateId}), prizesversion, drawingid, domainid, url, currencyid
                    from {LottoStorage.TABLE_KENO_LOSERS} T {COMMON_JOIN_FOR_TICKETS_KENO}
					LEFT OUTER JOIN {TABLE_USER_PER_AFFILIATE} US on T.account = US.account
                    where date='{DateTimeToString(drawDate)}' 
						and timestamp = 0 
                    {accountNumberFilter} {ticketNumberFilter} {domainIdFilter}";
				var winners = WinnerKenoTicketsWithAccounts(command);
				var result = winners.Tickets;
				return result;
			}

			protected override IEnumerable<CompletedKenoTicket> NoActionTicketsForDrawingsReport(DateTime startDate, DateTime endDate, string accountNumber, string ticketNumber, string domainIds)
			{
				var accountNumberFilter = string.IsNullOrWhiteSpace(accountNumber) ? string.Empty : $" and T.account='{accountNumber.ToUpper()}'";
				var ticketAndWagerNumber = ticketNumber.Split('-');
				var ticketNumberFilter = string.IsNullOrWhiteSpace(ticketAndWagerNumber[0]) ? string.Empty : $" and ticketnumber={ticketAndWagerNumber[0]}";
				var domainIdFilter = string.IsNullOrWhiteSpace(domainIds) ? string.Empty : $" and domainid in ({domainIds})";

				var command = $@"select date, T.account, ticket, amount, '{NO_DRAW}', noactionby, action, creation, ticketnumber, 0.0, 0.0, 0.0, IFNULL(affiliateid,{AffiliateData.DefaultAffiliateId}), prizesversion, drawingid, domainid, url, currencyid
                    from {LottoStorage.TABLE_KENO_NOACTIONS} T {COMMON_JOIN_FOR_TICKETS_KENO}
					LEFT OUTER JOIN {TABLE_USER_PER_AFFILIATE} US on T.account = US.account
                    where DATE(date)>='{DateToString(startDate)}' and DATE(date)<='{DateToString(endDate)}' 
						and timestamp = 0 
                    {accountNumberFilter} {ticketNumberFilter} {domainIdFilter}";
				var winners = WinnerKenoTicketsWithAccounts(command);
				var result = winners.Tickets;
				return result;
			}

			protected override IEnumerable<CompletedKenoTicket> NoActionTicketsForDrawingsReport(DateTime drawDate, string accountNumber, string ticketNumber, string domainIds)
			{
				var accountNumberFilter = string.IsNullOrWhiteSpace(accountNumber) ? string.Empty : $" and T.account='{accountNumber.ToUpper()}'";
				var ticketAndWagerNumber = ticketNumber.Split('-');
				var ticketNumberFilter = string.IsNullOrWhiteSpace(ticketAndWagerNumber[0]) ? string.Empty : $" and ticketnumber={ticketAndWagerNumber[0]}";
				var domainIdFilter = string.IsNullOrWhiteSpace(domainIds) ? string.Empty : $" and domainid in ({domainIds})";

				var command = $@"select date, T.account, ticket, amount, '{NO_DRAW}', noactionby, action, creation, ticketnumber, 0.0, 0.0, 0.0, IFNULL(affiliateid,{AffiliateData.DefaultAffiliateId}), prizesversion, drawingid, domainid, url, currencyid
                    from {LottoStorage.TABLE_KENO_NOACTIONS} T {COMMON_JOIN_FOR_TICKETS_KENO}
					LEFT OUTER JOIN {TABLE_USER_PER_AFFILIATE} US on T.account = US.account
                    where date='{DateTimeToString(drawDate)}' 
						and timestamp = 0 
                    {accountNumberFilter} {ticketNumberFilter} {domainIdFilter}";
				var winners = WinnerKenoTicketsWithAccounts(command);
				var result = winners.Tickets;
				return result;
			}

			protected override List<TotalProfitByDrawingRecord> FilteredTotalProfitByDrawingRecords(DateTime startDate, DateTime endDate, string domainIds)
			{
				var domainIdFilter = string.IsNullOrWhiteSpace(domainIds) ? string.Empty : $" and domainid in ({domainIds})";

				var command = $@"select date, ticketscount, playerscount, sold, prizes, profits, affiliateid, domainid, url, drawingid
                    from {TABLE_KENO_TOTAL_PROFIT_BY_DRAWING} T {COMMON_JOIN_FOR_TICKETS_KENO}
                    where DATE(date)>='{DateToString(startDate)}' and DATE(date)<='{DateToString(endDate)}' 
                        {domainIdFilter}
                    order by date;";

				var result = TotalProfitByDrawingRecords(command);
				return result;
			}

			protected override List<DailyTotalProfitRecord2> FilteredDailyTotalProfitRecords2(DateTime startDate, DateTime endDate, string domainIds)
			{
				var domainIdFilter = string.IsNullOrWhiteSpace(domainIds) ? string.Empty : $" and domainid in ({domainIds})";

				var command = $@"select date, gametype, ticketscount, winnerscount, sold, prizes, profits, domainid, url, affiliateid
					from {TABLE_KENO_DAILY_TOTAL_PROFIT} T INNER JOIN {LottoStorage.TABLE_DOMAINS} DO ON T.DOMAINID = DO.ID
					where DATE(date)>='{DateToString(startDate)}' and DATE(date)<='{DateToString(endDate)}' {domainIdFilter}
					order by date;";
				var result = DailyTotalProfitRecords2(command);
				return result;
			}

			protected override IEnumerable<CompletedKenoTicket> WinnerTicketsForWinnersReport(DateTime startDate, DateTime endDate, string accountNumber, string domainIds)
			{
				var accountNumberFilter = string.IsNullOrWhiteSpace(accountNumber) ? string.Empty : $" and T.account='{accountNumber.ToUpper()}'";
				var domainIdFilter = string.IsNullOrWhiteSpace(domainIds) ? string.Empty : $" and domainid in ({domainIds})";

				var command = $@"select date, T.account, ticket, amount, draw, gradedby, action, creation, ticketnumber, profit, prize, bulleyeprize, IFNULL(affiliateid,{AffiliateData.DefaultAffiliateId}), prizesversion, drawingid, domainid, url, currencyid
                    from {LottoStorage.TABLE_KENO_WINNERS} T {COMMON_JOIN_FOR_TICKETS_KENO}
					LEFT OUTER JOIN {TABLE_USER_PER_AFFILIATE} US on T.account = US.account
                    where DATE(date)>='{DateToString(startDate)}' and DATE(date)<='{DateToString(endDate)}' {accountNumberFilter} {domainIdFilter}
						and timestamp = 0 
                    order by date;";
				var winners = WinnerKenoTicketsWithAccounts(command);
				var result = winners.Tickets;
				return result;
			}

			internal override CompletedKenoDraws LastPlayedDrawingsOfPlayer(string accountNumber)
			{
				var command = $@"(SELECT date, drawingid
                                FROM {LottoStorage.TABLE_KENO_LOSERS} T
                                WHERE account ='{accountNumber.ToUpper()}')
                                UNION
                                (SELECT date, drawingid
                                FROM {LottoStorage.TABLE_KENO_WINNERS} T
                                WHERE account ='{accountNumber.ToUpper()}')
                                UNION
                                (SELECT date, drawingid
                                FROM {LottoStorage.TABLE_KENO_NOACTIONS} T
                                WHERE account ='{accountNumber.ToUpper()}')
                                ORDER BY date DESC
                                LIMIT 6 
                                ";
				var result = GetDrawings(command);
				return result;
			}

			protected override void CreateDailyTotalProfitStorage()
			{
				StringBuilder statement = new StringBuilder();

				statement.AppendLine(@$"CREATE TABLE IF NOT EXISTS {TABLE_KENO_DAILY_TOTAL_PROFIT}
					(
					DATE DATETIME NOT NULL,
					GAMETYPE CHAR(2) NOT NULL,
					AFFILIATEID INT NOT NULL,
					TICKETSCOUNT MEDIUMINT UNSIGNED NOT NULL,
					WINNERSCOUNT SMALLINT UNSIGNED NOT NULL,
					SOLD DECIMAL(10,2) UNSIGNED NOT NULL,
					PRIZES DECIMAL(10,2) UNSIGNED NOT NULL,
					PROFITS DECIMAL(10,2) NOT NULL,
					DOMAINID INT UNSIGNED NOT NULL DEFAULT 0,
					CURRENCYID INT UNSIGNED NOT NULL DEFAULT 2,
					PRIMARY KEY (DATE, CURRENCYID, DOMAINID, GAMETYPE, AFFILIATEID)
					);");

				statement
					.AppendLine(@$"CREATE TABLE IF NOT EXISTS {TABLE_USER_PER_AFFILIATE}
					(
					ACCOUNT VARCHAR({Customer.DATABASE_FIELD_ACCOUNT_LENGHT}) NOT NULL,
					AFFILIATEID INT NOT NULL,
					PRIMARY KEY (ACCOUNT)
					);");

				statement
					.AppendLine(@$"CREATE TABLE IF NOT EXISTS {TABLE_AFFILIATES}
					(
					ID INT NOT NULL,
					NAME VARCHAR(30) NOT NULL,
					PRIMARY KEY (ID)
					);");

				string sql = statement.ToString();
				ExecuteCommand(sql);
			}

			protected override void CreateTotalProfitByDrawingStorage()
			{
				StringBuilder statement = new StringBuilder();

				statement.AppendLine(@$"CREATE TABLE {TABLE_KENO_TOTAL_PROFIT_BY_DRAWING}
					(
					DATE DATETIME NOT NULL,
					GAMETYPE CHAR(2) NOT NULL,
					DRAWINGID INT UNSIGNED NOT NULL,
					TICKETSCOUNT MEDIUMINT UNSIGNED NOT NULL,
					PLAYERSCOUNT MEDIUMINT UNSIGNED NOT NULL,
					SOLD DECIMAL(10,2) UNSIGNED NOT NULL,
					PRIZES DECIMAL(10,2) UNSIGNED NOT NULL,
					PROFITS DECIMAL(10,2) NOT NULL,
					AFFILIATEID INT NOT NULL DEFAULT 0,
					DOMAINID INT UNSIGNED NOT NULL DEFAULT 0,
					PRIMARY KEY ( DATE, GAMETYPE, AFFILIATEID, DOMAINID )
					);");

				string sql = statement.ToString();
				ExecuteCommand(sql);
			}

			protected override void SaveInDailyTotalProfitStorage(List<DailyTotalProfitRecord2> dailyTotalProfitRecords)
			{
				StringBuilder sql = new StringBuilder();

				if (dailyTotalProfitRecords.Count > 0)
				{
					foreach (var record in dailyTotalProfitRecords)
					{
						string recordToInsert = $@"INSERT INTO { TABLE_KENO_DAILY_TOTAL_PROFIT } (DATE, GAMETYPE, TICKETSCOUNT, WINNERSCOUNT, SOLD, PRIZES, PROFITS, DOMAINID, CURRENCYID, AFFILIATEID) VALUES 
							('{ ToDateString(record.Date.Year, record.Date.Month, record.Date.Day, 0, 0, 0)}',
							'{ record.GameType}',
							'{ record.TicketsCount}',
							'{ record.WinnersCount}',
							'{ record.Sold}',
							'{ record.Prizes}',
							'{ record.Profits}',
							'{ record.DomainId}',
							'{record.CurrencyId}',
							{ record.AffiliateId}
						) ON DUPLICATE KEY UPDATE 
							TICKETSCOUNT = '{ record.TicketsCount}',
							WINNERSCOUNT = '{ record.WinnersCount}',
							SOLD = '{ record.Sold}',
							PRIZES = '{ record.Prizes}',
							PROFITS = '{ record.Profits}';
						";

						sql.AppendLine(recordToInsert);
					}
				}

				if (sql.Length > 0)
				{
					ExecuteCommand(sql.ToString());
				}
			}

			protected override void SaveInTotalProfitByDrawingStorage(List<TotalProfitByDrawingRecord> totalProfitByDrawingRecords)
			{
				StringBuilder sql = new StringBuilder();

				if (totalProfitByDrawingRecords.Count > 0)
				{
					int groupsOfRowsToInsert = totalProfitByDrawingRecords.Count > MAXIMUM_NUMBER_OF_ROWS_TO_INSERT ? (totalProfitByDrawingRecords.Count / MAXIMUM_NUMBER_OF_ROWS_TO_INSERT) + 1 : MINIMUM_SET_OF_ROWS_TO_INSERT;
					for (int index = 0; index < groupsOfRowsToInsert; index++)
					{
						var rowsToSkip = index * MAXIMUM_NUMBER_OF_ROWS_TO_INSERT;
						foreach (TotalProfitByKenoDrawingRecord record in totalProfitByDrawingRecords.Skip(rowsToSkip).Take(MAXIMUM_NUMBER_OF_ROWS_TO_INSERT))
						{
							string recordToInsert = $@"INSERT INTO {TABLE_KENO_TOTAL_PROFIT_BY_DRAWING}(DATE, GAMETYPE, DRAWINGID, TICKETSCOUNT, PLAYERSCOUNT, SOLD, PRIZES, PROFITS, DOMAINID, AFFILIATEID) VALUES 
								('{ ToDateString(record.DrawDate.Year, record.DrawDate.Month, record.DrawDate.Day, record.DrawDate.Hour, record.DrawDate.Minute, 0)}',
								'{ IdOfLottery.KN}',
								'{ record.DrawingId}',
								'{ record.TicketsCount}',
								'{ record.PlayersCount}',
								'{ record.Sold}',
								'{ record.Prizes}',
								'{ record.Profits}',
								'{ record.DomainId}',
								{ record.AffiliateId}
                            ) ON DUPLICATE KEY UPDATE 
								DRAWINGID = '{ record.DrawingId}',
								TICKETSCOUNT = '{ record.TicketsCount}',
								PLAYERSCOUNT = '{ record.PlayersCount}',
								SOLD = '{ record.Sold}',
								PRIZES = '{ record.Prizes}',
								PROFITS = '{ record.Profits}';
							";
							sql.AppendLine(recordToInsert);
						}
					}
				}

				if (sql.Length > 0)
				{
					ExecuteCommand(sql.ToString());
				}
			}
		}

		private class KenoDBHandlerSQLServer : KenoDBHandler
		{
			private const string COMMON_JOIN_FOR_TICKETS_KENO = @"INNER JOIN kenodrawings DR WITH (nolock) ON T.DRAWINGID = DR.ID
                                                            INNER JOIN l900domains DO WITH (nolock) ON T.DOMAINID = DO.ID ";

			internal KenoDBHandlerSQLServer(string connectionString) : base(connectionString)
			{
			}

			internal KenoDBHandlerSQLServer(Company company, string connectionString) : base(company, connectionString)
			{
			}

			internal override bool ExistsTable(string table)
			{
				bool exists = true;
				string sql = $"SELECT TOP 1 * FROM { table }";
				using (SqlConnection connection = new SqlConnection(connectionString))
				{
					try
					{
						connection.Open();
						using (SqlCommand command = new SqlCommand(sql, connection))
						{
							var dataReader = command.ExecuteReader();
							dataReader.Close();
						}
					}
					catch
					{
						exists = false;
					}
					finally
					{
						connection.Close();
					}
				}
				return exists;
			}

            internal override DateTime LastDateInDailyTotalProfit()
            {
                if (!ExistsTable(TABLE_KENO_DAILY_TOTAL_PROFIT)) throw new GameEngineException($"There is no table {TABLE_KENO_DAILY_TOTAL_PROFIT} to get date");

                DateTime lastDate = default;
                var sql = $"SELECT MAX(DATE) FROM {TABLE_KENO_DAILY_TOTAL_PROFIT};";

                using (var connection = new SqlConnection(connectionString))
                {
                    try
                    {
                        connection.Open();
                        using (var command = new SqlCommand(sql, connection))
                        {
                            var result = command.ExecuteScalar();
                            if (result != null && result != DBNull.Value)
                            {
                                lastDate = (DateTime)result;
                            }
                        }
                    }
                    catch (Exception e)
                    {
                        throw new GameEngineException($"SQL Error [{sql}]. {e.Message}");
                    }
                }

                return lastDate;
            }

            protected override void ExecuteCommand(string cmdText)
			{
				using (SqlConnection connection = new SqlConnection(connectionString))
				{
					try
					{
						connection.Open();
						using (SqlCommand command = new SqlCommand(cmdText, connection))
						{
							command.CommandType = CommandType.Text;
							command.ExecuteNonQuery();
						}
					}
					catch (Exception e)
					{
						Loggers.GetIntance().Db.Error($@"sql:{cmdText} type:{e.GetType()} error:{e.Message}", e);
						throw new GameEngineException("SQLServer Error [" + cmdText + "]");
					}
					finally
					{
						connection.Close();
					}
				}
			}

			protected override IEnumerable<CompletedKenoTicket> WinnerKenoTickets(string command)
			{
				DateTime tempDate;
				var winnerList = new List<CompletedKenoTicket>();
				using (SqlConnection connection = new SqlConnection(connectionString))
				{
					connection.Open();
					using (SqlCommand cmd = new SqlCommand(command, connection))
					{
						using (SqlDataReader reader = cmd.ExecuteReader())
						{
							while (reader.Read())
							{
								tempDate = reader.GetDateTime(0);
								var info = new CompletedKenoTicket(
									company: this.Company,
									draw: reader.GetString(4),
									gradedBy: reader.GetString(5),
									prize: reader.GetDecimal(10),
									bulleyePrize: reader.GetDecimal(11),
									drawDate: tempDate,
									accountNumber: reader.GetString(1),
									ticket: reader.GetString(2),
									amount: reader.GetDecimal(3),
									action: (GameboardStatus)reader.GetByte(6),
									drawingId: reader.GetInt32(13),
									creation: reader.GetDateTime(7),
									ticketNumber: reader.GetInt32(8),
									profit: reader.GetDecimal(9),
									prizesVersion: reader.GetInt32(12),
									domainId: reader.GetInt32(14),
									domainUrl: reader.GetString(15),
									currencyId: reader.GetInt32(16),
									isWinner: true,
									isNoAction: false
								);

								winnerList.Add(info);
							}
						}
					}
					connection.Close();
				}

				return winnerList;
			}

			protected override IEnumerable<CompletedKenoTicket> LoserKenoTickets(string command)
			{
				DateTime tempDate;
				var looserList = new List<CompletedKenoTicket>();
				using (SqlConnection connection = new SqlConnection(connectionString))
				{
					connection.Open();
					using (SqlCommand cmd = new SqlCommand(command, connection))
					{
						using (SqlDataReader reader = cmd.ExecuteReader())
						{
							while (reader.Read())
							{
								tempDate = reader.GetDateTime(0);
								var info = new CompletedKenoTicket(
									company: this.Company,
									draw: reader.GetString(4),
									gradedBy: reader.GetString(5),
									drawDate: tempDate,
									accountNumber: reader.GetString(1),
									ticket: reader.GetString(2),
									amount: reader.GetDecimal(3),
									action: (GameboardStatus)reader.GetByte(6),
									drawingId: reader.GetInt32(11),
									creation: reader.GetDateTime(7),
									ticketNumber: reader.GetInt32(8),
									profit: reader.GetDecimal(9),
									prizesVersion: reader.GetInt32(10),
									domainId: reader.GetInt16(12),
									domainUrl: reader.GetString(13),
									currencyId: reader.GetInt32(14),
									prize: 0,
									bulleyePrize: 0,
									isWinner: false,
									isNoAction: false
								);
								looserList.Add(info);
							}
						}
					}
					connection.Close();
				}
				return looserList;
			}

			protected override IEnumerable<CompletedKenoTicket> NoActionKenoTickets(string command)
			{
				DateTime tempDate;
				var notActionList = new List<CompletedKenoTicket>();
				using (SqlConnection connection = new SqlConnection(connectionString))
				{
					connection.Open();
					using (SqlCommand cmd = new SqlCommand(command, connection))
					{
						using (SqlDataReader reader = cmd.ExecuteReader())
						{
							while (reader.Read())
							{
								tempDate = reader.GetDateTime(0);
								var info = new CompletedKenoTicket(
									company: this.Company,
									gradedBy: reader.GetString(7),
									drawDate: tempDate,
									accountNumber: reader.GetString(1),
									ticket: reader.GetString(2),
									amount: reader.GetDecimal(3),
									action: (GameboardStatus)reader.GetByte(4),
									drawingId: reader.GetInt32(9),
									creation: reader.GetDateTime(5),
									ticketNumber: reader.GetInt32(6),
									prizesVersion: reader.GetInt32(8),
									domainId: reader.GetInt16(10),
									domainUrl: reader.GetString(11),
									currencyId: reader.GetInt32(12),	
									draw: string.Empty,
									profit: 0,
									prize: 0,
									bulleyePrize: 0,
									isWinner: false,
									isNoAction: true
								);

								notActionList.Add(info);
							}
						}
					}
					connection.Close();
				}
				return notActionList;
			}

			protected override CompletedKenoTicketsWithAccounts WinnerKenoTicketsWithAccounts(string command)
			{
				DateTime tempDate;
				CompletedKenoTicketsWithAccounts winners = new CompletedKenoTicketsWithAccounts();
				using (SqlConnection connection = new SqlConnection(connectionString))
				{
					connection.Open();
					using (SqlCommand cmd = new SqlCommand(command, connection))
					{
						using (SqlDataReader reader = cmd.ExecuteReader())
						{
							while (reader.Read())
							{
								string accountNumber = reader.GetString(1);
								int affiliateId = reader.GetInt32(12);

								AffiliateWithAccount account = winners.AddAccount(accountNumber, affiliateId);

								tempDate = reader.GetDateTime(0);
								var info = new CompletedKenoTicket(
									company: Company,
									drawDate: tempDate,
									ticket: reader.GetString(2),
									amount: reader.GetDecimal(3),
									draw: reader.GetString(4),
									gradedBy: reader.GetString(5),
									action: (GameboardStatus)reader.GetByte(6),
									creation: reader.GetDateTime(7),
									ticketNumber: reader.GetInt32(8),
									profit: reader.GetDecimal(9),
									prize: reader.GetDecimal(10),
									bulleyePrize: reader.GetDecimal(11),
									accountNumber: accountNumber,
									domainId: reader.GetInt16(15),
									domainUrl: reader.GetString(16),
									currencyId: reader.GetInt32(17),
									drawingId: reader.GetInt32(14),
									prizesVersion: reader.GetInt32(13),
									isWinner: true,
									isNoAction: false
								);
								info.AffiliateId = affiliateId;
								winners.Add(info);
							}
						}
					}
					connection.Close();
				}

				return winners;
			}

			protected override List<TotalProfitByDrawingRecord> TotalProfitByDrawingRecords(string command)
			{
				var records = new List<TotalProfitByDrawingRecord>();
				using (SqlConnection connection = new SqlConnection(connectionString))
				{
					connection.Open();
					using (SqlCommand cmd = new SqlCommand(command, connection))
					{
						using (SqlDataReader reader = cmd.ExecuteReader())
						{
							while (reader.Read())
							{
								var info = new TotalProfitByKenoDrawingRecord(
									date: reader.GetDateTime(0),
									ticketsCount: reader.GetInt32(1),
									playersCount: reader.GetInt32(2),
									sold: reader.GetDecimal(3),
									prizes: reader.GetDecimal(4),
									profits: reader.GetDecimal(5),
									affiliateId: reader.GetInt32(6),
									domainId: reader.GetInt32(7),
									domainUrl: reader.GetString(8),
									drawId: reader.GetInt32(9)
								);
								records.Add(info);
							}
						}
					}
					connection.Close();
				}
				return records;
			}

			protected override List<DailyTotalProfitRecord2> DailyTotalProfitRecords2(string command)
			{
				var records = new List<DailyTotalProfitRecord2>();
				using (SqlConnection connection = new SqlConnection(connectionString))
				{
					connection.Open();
					using (SqlCommand cmd = new SqlCommand(command, connection))
					{
						using (SqlDataReader reader = cmd.ExecuteReader())
						{
							DailyTotalProfitRecord2 info;
							while (reader.Read())
							{
								info = new DailyTotalProfitRecord2(
									date: reader.GetDateTime(0),
									gameType: reader.GetString(1),
									domainId: reader.GetInt16(7),
									affiliateId: reader.GetInt32(9),
									currencyId: reader.GetInt32(10),
									ticketsCount: reader.GetInt32(2),
									winnersCount: reader.GetInt32(3),
									sold: reader.GetDecimal(4),
									prizes: reader.GetDecimal(5),
									profits: reader.GetDecimal(6),
									domainUrl: reader.GetString(8)
								);
								records.Add(info);
							}
						}
					}
					connection.Close();
				}
				return records;
			}

			protected override CompletedKenoDraws GetDrawings(string command)
			{
				var result = new CompletedKenoDraws();
				using (var connection = new SqlConnection(connectionString))
				{
					connection.Open();
					using (var cmd = new SqlCommand(command, connection))
					{
						using (SqlDataReader reader = cmd.ExecuteReader())
						{
							while (reader.Read())
							{
								var info = new CompletedKenoDraw(
									drawDate: reader.GetDateTime(0),
									drawId: reader.GetInt32(1)
								);

								result.Add(info);
							}
						}
					}
					connection.Close();
				}
				return result;
			}

			internal override IEnumerable<CompletedKenoTicket> WinnerTicketsOfPlayerBetween(DateTime startDate, DateTime endDate, string accountNumber)
			{
				var command = $@"{COMMON_SELECT_FOR_KENO_WINNERS}
                    from {LottoStorage.TABLE_KENO_WINNERS} T WITH (nolock) {COMMON_JOIN_FOR_TICKETS_KENO}   
                    where cast(date as date)<='{DateToString(endDate)}' and cast(date as date)>='{DateToString(startDate)}' and account='{accountNumber.ToUpper()}' 
						and timestamp = 0 
                    order by date;";
				var result = WinnerKenoTickets(command);
				return result;
			}

			internal override IEnumerable<CompletedKenoTicket> LoserTicketsOfPlayerBetween(DateTime startDate, DateTime endDate, string accountNumber)
			{
				var command = $@"{COMMON_SELECT_FOR_KENO_LOSERS}
                    from {LottoStorage.TABLE_KENO_LOSERS} T WITH (nolock) {COMMON_JOIN_FOR_TICKETS_KENO} 
                    where cast(date as date)<='{DateToString(endDate)}' and cast(date as date)>='{DateToString(startDate)}' and account='{accountNumber.ToUpper()}' 
						and timestamp = 0 
                    order by date;";
				var result = LoserKenoTickets(command);
				return result;
			}

			internal override IEnumerable<CompletedKenoTicket> NoActionTicketsOfPlayerBetween(DateTime startDate, DateTime endDate, string accountNumber)
			{
				var command = $@"{COMMON_SELECT_FOR_KENO_NOACTION}
                    from {LottoStorage.TABLE_KENO_NOACTIONS} T WITH (nolock) {COMMON_JOIN_FOR_TICKETS_KENO}
                    where cast(date as date)<='{DateToString(endDate)}' and cast(date as date)>='{DateToString(startDate)}' and account='{accountNumber.ToUpper()}' 
						and timestamp = 0 
                    order by date;";
				var result = NoActionKenoTickets(command);
				return result;
			}

			internal override IEnumerable<CompletedKenoTicket> WinnerTicketsOfPlayer(DateTime drawDate, string accountNumber)
			{
				var command = $@"{COMMON_SELECT_FOR_KENO_WINNERS}
                    from {LottoStorage.TABLE_KENO_WINNERS} T WITH (nolock) {COMMON_JOIN_FOR_TICKETS_KENO}   
                    where cast(date as date)='{DateToString(drawDate)}' and account='{accountNumber.ToUpper()}' 
						and timestamp = 0 
                    order by date;";
				var result = WinnerKenoTickets(command);
				return result;
			}

			internal override IEnumerable<CompletedKenoTicket> LoserTicketsOfPlayer(DateTime drawDate, string accountNumber)
			{
				var command = $@"{COMMON_SELECT_FOR_KENO_LOSERS}
                    from {LottoStorage.TABLE_KENO_LOSERS} T WITH (nolock) {COMMON_JOIN_FOR_TICKETS_KENO} 
                    where cast(date as date)='{DateToString(drawDate)}' and account='{accountNumber.ToUpper()}' 
						and timestamp = 0 
                    order by date;";
				var result = LoserKenoTickets(command);
				return result;
			}

			internal override IEnumerable<CompletedKenoTicket> NoActionTicketsOfPlayer(DateTime drawDate, string accountNumber)
			{
				var command = $@"{COMMON_SELECT_FOR_KENO_NOACTION}
                    from {LottoStorage.TABLE_KENO_NOACTIONS} T WITH (nolock) {COMMON_JOIN_FOR_TICKETS_KENO}
                    where cast(date as date)='{DateToString(drawDate)}' and account='{accountNumber.ToUpper()}' 
						and timestamp = 0 
                    order by date;";
				var result = NoActionKenoTickets(command);
				return result;
			}

			protected override IEnumerable<CompletedKenoTicket> WinnerKenoTicketsForDrawingsReport(DateTime drawDate, string hour, string minute, string accountNumber, string gameType, string ticketNumber, string domainIds)
			{
				var hourFilter = string.IsNullOrWhiteSpace(hour) ? string.Empty : $" and DATEPART(hour,date)={hour}";
				var minuteFilter = string.IsNullOrWhiteSpace(minute) ? string.Empty : $" and DATEPART(minute,date)={minute}";
				var accountNumberFilter = string.IsNullOrWhiteSpace(accountNumber) ? string.Empty : $" and T.account.='{accountNumber.ToUpper()}'";
				var ticketAndWagerNumber = ticketNumber.Split('-');
				var ticketNumberFilter = string.IsNullOrWhiteSpace(ticketAndWagerNumber[0]) ? string.Empty : $" and ticketnumber={ticketAndWagerNumber[0]}";
				var gameTypeFilter = string.IsNullOrWhiteSpace(gameType) ? string.Empty : $" and left(ticket,2) ='{gameType}'";
				var domainIdFilter = string.IsNullOrWhiteSpace(domainIds) ? string.Empty : $" and domainid in ({domainIds})";

				var command = $@"select date, T.account, ticket, amount, draw, gradedby, action, creation, ticketnumber, profit, prize, bulleyeprize, isnull(affiliateid,{AffiliateData.DefaultAffiliateId}), prizesversion, drawingid, domainid, url, currencyid
                    from {LottoStorage.TABLE_KENO_WINNERS} T WITH (nolock) {COMMON_JOIN_FOR_TICKETS_KENO}
					LEFT OUTER JOIN {TABLE_USER_PER_AFFILIATE} US on T.account = US.account
                    where cast(date as date)='{DateToString(drawDate)}' 
						and timestamp = 0 
                    {hourFilter} {minuteFilter} {accountNumberFilter} {ticketNumberFilter} {gameTypeFilter} {domainIdFilter}";
				var winners = WinnerKenoTicketsWithAccounts(command);
				var result = winners.Tickets;
				return result;
			}

			protected override IEnumerable<CompletedKenoTicket> LoserKenoTicketsForDrawingsReport(DateTime drawDate, string hour, string minute, string accountNumber, string gameType, string ticketNumber, string domainIds)
			{
				var hourFilter = string.IsNullOrWhiteSpace(hour) ? string.Empty : $" and DATEPART(hour,date)={hour}";
				var minuteFilter = string.IsNullOrWhiteSpace(minute) ? string.Empty : $" and DATEPART(minute,date)={minute}";
				var accountNumberFilter = string.IsNullOrWhiteSpace(accountNumber) ? string.Empty : $" and T.account='{accountNumber.ToUpper()}'";
				var ticketAndWagerNumber = ticketNumber.Split('-');
				var ticketNumberFilter = string.IsNullOrWhiteSpace(ticketAndWagerNumber[0]) ? string.Empty : $" and ticketnumber={ticketAndWagerNumber[0]}";
				var gameTypeFilter = string.IsNullOrWhiteSpace(gameType) ? string.Empty : $" and left(ticket,2) ='{gameType}'";
				var domainIdFilter = string.IsNullOrWhiteSpace(domainIds) ? string.Empty : $" and domainid in ({domainIds})";

				var command = $@"select date, T.account, ticket, amount, draw, gradedby, action, creation, ticketnumber, profit, 0.0, 0.0, isnull(affiliateid,{AffiliateData.DefaultAffiliateId}), prizesversion, drawingid, domainid, url, currencyid
                    from {LottoStorage.TABLE_KENO_LOSERS} T WITH (nolock) {COMMON_JOIN_FOR_TICKETS_KENO}
					LEFT OUTER JOIN {TABLE_USER_PER_AFFILIATE} US on T.account = US.account
                    where cast(date as date)='{DateToString(drawDate)}' 
						and timestamp = 0 
                    {hourFilter} {minuteFilter} {accountNumberFilter} {ticketNumberFilter} {gameTypeFilter} {domainIdFilter}";
				var winners = WinnerKenoTicketsWithAccounts(command);
				var result = winners.Tickets;
				return result;
			}

			protected override IEnumerable<CompletedKenoTicket> NoActionKenoTicketsForDrawingsReport(DateTime drawDate, string hour, string minute, string accountNumber, string gameType, string ticketNumber, string domainIds)
			{
				var hourFilter = string.IsNullOrWhiteSpace(hour) ? string.Empty : $" and DATEPART(hour,date)={hour}";
				var minuteFilter = string.IsNullOrWhiteSpace(minute) ? string.Empty : $" and DATEPART(minute,date)={minute}";
				var accountNumberFilter = string.IsNullOrWhiteSpace(accountNumber) ? string.Empty : $" and T.account='{accountNumber.ToUpper()}'";
				var ticketAndWagerNumber = ticketNumber.Split('-');
				var ticketNumberFilter = string.IsNullOrWhiteSpace(ticketAndWagerNumber[0]) ? string.Empty : $" and ticketnumber={ticketAndWagerNumber[0]}";
				var gameTypeFilter = string.IsNullOrWhiteSpace(gameType) ? string.Empty : $" and left(ticket,2) ='{gameType}'";
				var domainIdFilter = string.IsNullOrWhiteSpace(domainIds) ? string.Empty : $" and domainid in ({domainIds})";

				var command = $@"select date, T.account, ticket, amount, '{NO_DRAW}', noactionby, action, creation, ticketnumber, 0.0, 0.0, 0.0, isnull(affiliateid,{AffiliateData.DefaultAffiliateId}), prizesversion, drawingid, domainid, url, currencyid
                    from {LottoStorage.TABLE_KENO_NOACTIONS} T WITH (nolock) {COMMON_JOIN_FOR_TICKETS_KENO}
					LEFT OUTER JOIN {TABLE_USER_PER_AFFILIATE} US on T.account = US.account
                    where cast(date as date)='{DateToString(drawDate)}'  
						and timestamp = 0 
                    {hourFilter} {minuteFilter} {accountNumberFilter} {ticketNumberFilter} {gameTypeFilter} {domainIdFilter}";
				var winners = WinnerKenoTicketsWithAccounts(command);
				var result = winners.Tickets;
				return result;
			}

			protected override IEnumerable<CompletedKenoTicket> WinnerKenoTicketsForDailyTotalProfit(DateTime startDate, string gameType, string domainIds)
			{
				var gameTypeFilter = string.IsNullOrWhiteSpace(gameType) ? "" : $" and left(ticket,2) ='{gameType}'";
				var domainIdFilter = string.IsNullOrWhiteSpace(domainIds) ? string.Empty : $" and domainid in ({domainIds})";

				var command = $@"select date, T.account, ticket, amount, draw, gradedby, action, creation, ticketnumber, profit, prize, bulleyeprize, isnull(affiliateid,{AffiliateData.DefaultAffiliateId}), prizesversion, drawingid, domainid, url, currencyid
					from {LottoStorage.TABLE_KENO_WINNERS} T WITH (nolock) {COMMON_JOIN_FOR_TICKETS_KENO}
					LEFT OUTER JOIN {TABLE_USER_PER_AFFILIATE} US on T.account = US.account
					where cast(date as date)='{DateToString(startDate)}' {gameTypeFilter} {domainIdFilter}
						and timestamp = 0
					order by date;";
				var winners = WinnerKenoTicketsWithAccounts(command);
				var result = winners.Tickets;
				return result;
			}

			protected override IEnumerable<CompletedKenoTicket> LoserKenoTicketsForDailyTotalProfit(DateTime startDate, string gameType, string domainIds)
			{
				var gameTypeFilter = string.IsNullOrWhiteSpace(gameType) ? "" : $" and left(ticket,2) ='{gameType}'";
				var domainIdFilter = string.IsNullOrWhiteSpace(domainIds) ? string.Empty : $" and domainid in ({domainIds})";

				var command = $@"select date, T.account, ticket, amount, draw, gradedby, action, creation, ticketnumber, profit, 0.0, 0.0, isnull(affiliateid,{AffiliateData.DefaultAffiliateId}), prizesversion, drawingid, domainid, url, currencyid
					from {LottoStorage.TABLE_KENO_LOSERS} T WITH (nolock) {COMMON_JOIN_FOR_TICKETS_KENO}
					LEFT OUTER JOIN {TABLE_USER_PER_AFFILIATE} US on T.account = US.account
					where cast(date as date)='{DateToString(startDate)}' {gameTypeFilter} {domainIdFilter}
						and timestamp = 0
					order by date;";
				var winners = WinnerKenoTicketsWithAccounts(command);
				var result = winners.Tickets;
				return result;
			}

			protected override IEnumerable<CompletedKenoTicket> NoActionKenoTicketsForDailyTotalProfit(DateTime startDate, string gameType, string domainIds)
			{
				var gameTypeFilter = string.IsNullOrWhiteSpace(gameType) ? "" : $" and left(ticket,2) ='{gameType}'";
				var domainIdFilter = string.IsNullOrWhiteSpace(domainIds) ? string.Empty : $" and domainid in ({domainIds})";

				var command = $@"select date, T.account, ticket, amount, '{NO_DRAW}', noactionby, action, creation, ticketnumber, 0.0, 0.0, 0.0, isnull(affiliateid,{AffiliateData.DefaultAffiliateId}), prizesversion, drawingid, domainid, url, currencyid
					from {LottoStorage.TABLE_KENO_NOACTIONS} T WITH (nolock) {COMMON_JOIN_FOR_TICKETS_KENO}
					LEFT OUTER JOIN {TABLE_USER_PER_AFFILIATE} US on T.account = US.account
					where cast(date as date)='{DateToString(startDate)}' {gameTypeFilter} {domainIdFilter}
						and timestamp = 0
					order by date;";
				var winners = WinnerKenoTicketsWithAccounts(command);
				var result = winners.Tickets;
				return result;
			}

			internal override void InsertAffiliateIfNotExist(int id, string name, string accountNumber)
			{
				bool tablesAlreadyExists = ExistsTable(TABLE_KENO_DAILY_TOTAL_PROFIT);
				if (!tablesAlreadyExists) CreateDailyTotalProfitStorage();

				var sql = $"SELECT ID FROM {TABLE_AFFILIATES} WHERE ID = '{id}'";
				using (SqlConnection connection = new SqlConnection(connectionString))
				{
					try
					{
						connection.Open();
						using (SqlCommand command = new SqlCommand(sql.ToString(), connection))
						{
							bool itMustBeCreated = false;

							using (SqlDataReader reader = command.ExecuteReader())
							{
								if (!reader.Read())
								{
									itMustBeCreated = true;
								}
							}
							if (itMustBeCreated)
							{
								sql = $"INSERT INTO {TABLE_AFFILIATES}(ID, NAME) VALUES ({id}, '{name}');";
								using (SqlCommand command2 = new SqlCommand(sql.ToString(), connection))
								{
									command2.CommandType = CommandType.Text;
									command2.ExecuteNonQuery();
								}
							}
						}

						sql = $"SELECT AFFILIATEID, ACCOUNT FROM {TABLE_USER_PER_AFFILIATE} WHERE ACCOUNT ='{accountNumber}' AND AFFILIATEID = {id} AND AFFILIATEID <> {AffiliateData.DefaultAffiliateId}; ";
						using (SqlCommand command = new SqlCommand(sql.ToString(), connection))
						{
							bool itMustBeCreated = false;
							using (SqlDataReader reader = command.ExecuteReader())
							{
								if (!reader.Read())
								{
									itMustBeCreated = true;
								}
							}

							if (itMustBeCreated)
							{
								sql = $"INSERT INTO {TABLE_USER_PER_AFFILIATE}(AFFILIATEID, ACCOUNT) VALUES ({id}, '{accountNumber}');";
								using (SqlCommand command2 = new SqlCommand(sql.ToString(), connection))
								{
									command2.CommandType = CommandType.Text;
									command2.ExecuteNonQuery();
								}
							}
						}

					}
					catch (Exception e)
					{
						throw new GameEngineException($"SQL Server Error [{ sql.ToString() }]. {e.Message}");
					}
					finally
					{
						connection.Close();
					}
				}
			}

			protected override IEnumerable<CompletedKenoTicket> WinnerTicketsForDrawingsReport(DateTime startDate, DateTime endDate, string accountNumber, string ticketNumber, string domainIds)
			{
				var accountNumberFilter = string.IsNullOrWhiteSpace(accountNumber) ? string.Empty : $" and T.account='{accountNumber.ToUpper()}'";
				var ticketAndWagerNumber = ticketNumber.Split('-');
				var ticketNumberFilter = string.IsNullOrWhiteSpace(ticketAndWagerNumber[0]) ? string.Empty : $" and ticketnumber={ticketAndWagerNumber[0]}";
				var domainIdFilter = string.IsNullOrWhiteSpace(domainIds) ? string.Empty : $" and domainid in ({domainIds})";

				var command = $@"select date, T.account, ticket, amount, draw, gradedby, action, creation, ticketnumber, profit, prize, bulleyeprize, isnull(affiliateid,{AffiliateData.DefaultAffiliateId}), prizesversion, drawingid, domainid, url, currencyid
                    from {LottoStorage.TABLE_KENO_WINNERS} T WITH (nolock) {COMMON_JOIN_FOR_TICKETS_KENO}
					LEFT OUTER JOIN {TABLE_USER_PER_AFFILIATE} US on T.account = US.account
                    where cast(date as date)>='{DateToString(startDate)}' and cast(date as date)<='{DateToString(endDate)}' 
						and timestamp = 0 
                    {accountNumberFilter} {ticketNumberFilter} {domainIdFilter}";
				var winners = WinnerKenoTicketsWithAccounts(command);
				var result = winners.Tickets;
				return result;
			}

			protected override IEnumerable<CompletedKenoTicket> WinnerTicketsForDrawingsReport(DateTime drawDate, string accountNumber, string ticketNumber, string domainIds)
			{
				var accountNumberFilter = string.IsNullOrWhiteSpace(accountNumber) ? string.Empty : $" and T.account='{accountNumber.ToUpper()}'";
				var ticketAndWagerNumber = ticketNumber.Split('-');
				var ticketNumberFilter = string.IsNullOrWhiteSpace(ticketAndWagerNumber[0]) ? string.Empty : $" and ticketnumber={ticketAndWagerNumber[0]}";
				var domainIdFilter = string.IsNullOrWhiteSpace(domainIds) ? string.Empty : $" and domainid in ({domainIds})";

				var command = $@"select date, T.account, ticket, amount, draw, gradedby, action, creation, ticketnumber, profit, prize, bulleyeprize, isnull(affiliateid,{AffiliateData.DefaultAffiliateId}), prizesversion, drawingid, domainid, url, currencyid
                    from {LottoStorage.TABLE_KENO_WINNERS} T WITH (nolock) {COMMON_JOIN_FOR_TICKETS_KENO}
					LEFT OUTER JOIN {TABLE_USER_PER_AFFILIATE} US on T.account = US.account
                    where cast(date as date)='{DateTimeToString(drawDate)}' 
						and timestamp = 0 
                    {accountNumberFilter} {ticketNumberFilter} {domainIdFilter}";
				var winners = WinnerKenoTicketsWithAccounts(command);
				var result = winners.Tickets;
				return result;
			}

			protected override IEnumerable<CompletedKenoTicket> LoserTicketsForDrawingsReport(DateTime startDate, DateTime endDate, string accountNumber, string ticketNumber, string domainIds)
			{
				var accountNumberFilter = string.IsNullOrWhiteSpace(accountNumber) ? string.Empty : $" and T.account='{accountNumber.ToUpper()}'";
				var ticketAndWagerNumber = ticketNumber.Split('-');
				var ticketNumberFilter = string.IsNullOrWhiteSpace(ticketAndWagerNumber[0]) ? string.Empty : $" and ticketnumber={ticketAndWagerNumber[0]}";
				var domainIdFilter = string.IsNullOrWhiteSpace(domainIds) ? string.Empty : $" and domainid in ({domainIds})";

				var command = $@"select date, T.account, ticket, amount, draw, gradedby, action, creation, ticketnumber, profit, 0.0, 0.0, isnull(affiliateid,{AffiliateData.DefaultAffiliateId}), prizesversion, drawingid, domainid, url, currencyid
                    from {LottoStorage.TABLE_KENO_LOSERS} T WITH (nolock) {COMMON_JOIN_FOR_TICKETS_KENO}
					LEFT OUTER JOIN {TABLE_USER_PER_AFFILIATE} US on T.account = US.account
                    where cast(date as date)>='{DateToString(startDate)}' and cast(date as date)<='{DateToString(endDate)}' 
						and timestamp = 0 
                    {accountNumberFilter} {ticketNumberFilter} {domainIdFilter}";
				var winners = WinnerKenoTicketsWithAccounts(command);
				var result = winners.Tickets;
				return result;
			}

			protected override IEnumerable<CompletedKenoTicket> LoserTicketsForDrawingsReport(DateTime drawDate, string accountNumber, string ticketNumber, string domainIds)
			{
				var accountNumberFilter = string.IsNullOrWhiteSpace(accountNumber) ? string.Empty : $" and T.account='{accountNumber.ToUpper()}'";
				var ticketAndWagerNumber = ticketNumber.Split('-');
				var ticketNumberFilter = string.IsNullOrWhiteSpace(ticketAndWagerNumber[0]) ? string.Empty : $" and ticketnumber={ticketAndWagerNumber[0]}";
				var domainIdFilter = string.IsNullOrWhiteSpace(domainIds) ? string.Empty : $" and domainid in ({domainIds})";

				var command = $@"select date, T.account, ticket, amount, draw, gradedby, action, creation, ticketnumber, profit, 0.0, 0.0, isnull(affiliateid,{AffiliateData.DefaultAffiliateId}), prizesversion, drawingid, domainid, url, currencyid
                    from {LottoStorage.TABLE_KENO_LOSERS} T WITH (nolock) {COMMON_JOIN_FOR_TICKETS_KENO}
					LEFT OUTER JOIN {TABLE_USER_PER_AFFILIATE} US on T.account = US.account
                    where cast(date as date)='{DateTimeToString(drawDate)}' 
						and timestamp = 0 
                    {accountNumberFilter} {ticketNumberFilter} {domainIdFilter}";
				var winners = WinnerKenoTicketsWithAccounts(command);
				var result = winners.Tickets;
				return result;
			}

			protected override IEnumerable<CompletedKenoTicket> NoActionTicketsForDrawingsReport(DateTime startDate, DateTime endDate, string accountNumber, string ticketNumber, string domainIds)
			{
				var accountNumberFilter = string.IsNullOrWhiteSpace(accountNumber) ? string.Empty : $" and T.account='{accountNumber.ToUpper()}'";
				var ticketAndWagerNumber = ticketNumber.Split('-');
				var ticketNumberFilter = string.IsNullOrWhiteSpace(ticketAndWagerNumber[0]) ? string.Empty : $" and ticketnumber={ticketAndWagerNumber[0]}";
				var domainIdFilter = string.IsNullOrWhiteSpace(domainIds) ? string.Empty : $" and domainid in ({domainIds})";

				var command = $@"select date, T.account, ticket, amount, '{NO_DRAW}', noactionby, action, creation, ticketnumber, 0.0, 0.0, 0.0, isnull(affiliateid,{AffiliateData.DefaultAffiliateId}), prizesversion, drawingid, domainid, url, currencyid
                    from {LottoStorage.TABLE_KENO_NOACTIONS} T WITH (nolock) {COMMON_JOIN_FOR_TICKETS_KENO}
					LEFT OUTER JOIN {TABLE_USER_PER_AFFILIATE} US on T.account = US.account
                    where cast(date as date)>='{DateToString(startDate)}' and cast(date as date)<='{DateToString(endDate)}' 
						and timestamp = 0 
                    {accountNumberFilter} {ticketNumberFilter} {domainIdFilter}";
				var winners = WinnerKenoTicketsWithAccounts(command);
				var result = winners.Tickets;
				return result;
			}

			protected override IEnumerable<CompletedKenoTicket> NoActionTicketsForDrawingsReport(DateTime drawDate, string accountNumber, string ticketNumber, string domainIds)
			{
				var accountNumberFilter = string.IsNullOrWhiteSpace(accountNumber) ? string.Empty : $" and T.account='{accountNumber.ToUpper()}'";
				var ticketAndWagerNumber = ticketNumber.Split('-');
				var ticketNumberFilter = string.IsNullOrWhiteSpace(ticketAndWagerNumber[0]) ? string.Empty : $" and ticketnumber={ticketAndWagerNumber[0]}";
				var domainIdFilter = string.IsNullOrWhiteSpace(domainIds) ? string.Empty : $" and domainid in ({domainIds})";

				var command = $@"select date, T.account, ticket, amount, '{NO_DRAW}', noactionby, action, creation, ticketnumber, 0.0, 0.0, 0.0, isnull(affiliateid,{AffiliateData.DefaultAffiliateId}), prizesversion, drawingid, domainid, url, currencyid
                    from {LottoStorage.TABLE_KENO_NOACTIONS} T WITH (nolock) {COMMON_JOIN_FOR_TICKETS_KENO}
					LEFT OUTER JOIN {TABLE_USER_PER_AFFILIATE} US on T.account = US.account
                    where cast(date as date)='{DateToString(drawDate)}' 
						and timestamp = 0 
                    {accountNumberFilter} {ticketNumberFilter} {domainIdFilter}";
				var winners = WinnerKenoTicketsWithAccounts(command);
				var result = winners.Tickets;
				return result;
			}

			protected override List<TotalProfitByDrawingRecord> FilteredTotalProfitByDrawingRecords(DateTime startDate, DateTime endDate, string domainIds)
			{
				var domainIdFilter = string.IsNullOrWhiteSpace(domainIds) ? string.Empty : $" and domainid in ({domainIds})";

				var command = $@"select date, ticketscount, playerscount, sold, prizes, profits, affiliateid, domainid, url, drawingid
                    from {TABLE_KENO_TOTAL_PROFIT_BY_DRAWING} T WITH (nolock) {COMMON_JOIN_FOR_TICKETS_KENO}
                    where cast(date as date)>='{DateToString(startDate)}' and cast(date as date)<='{DateToString(endDate)}' 
                        {domainIdFilter}
                    order by date;";

				var result = TotalProfitByDrawingRecords(command);
				return result;
			}

			protected override List<DailyTotalProfitRecord2> FilteredDailyTotalProfitRecords2(DateTime startDate, DateTime endDate, string domainIds)
			{
				var domainIdFilter = string.IsNullOrWhiteSpace(domainIds) ? string.Empty : $" and domainid in ({domainIds})";

				var command = $@"select date, gametype, ticketscount, winnerscount, sold, prizes, profits, domainid, url, affiliateid
					from {TABLE_KENO_DAILY_TOTAL_PROFIT} T WITH (nolock) INNER JOIN {LottoStorage.TABLE_DOMAINS} DO WITH (nolock) ON T.DOMAINID = DO.ID
					where cast(date as date)>='{DateToString(startDate)}' and cast(date as date)<='{DateToString(endDate)}' {domainIdFilter}
					order by date;";
				var result = DailyTotalProfitRecords2(command);
				return result;
			}

			protected override IEnumerable<CompletedKenoTicket> WinnerTicketsForWinnersReport(DateTime startDate, DateTime endDate, string accountNumber, string domainIds)
			{
				var accountNumberFilter = String.IsNullOrWhiteSpace(accountNumber) ? "" : $" and T.account='{accountNumber.ToUpper()}'";
				var domainIdFilter = string.IsNullOrWhiteSpace(domainIds) ? string.Empty : $" and domainid in ({domainIds})";

				var command = $@"select date, T.account, ticket, amount, draw, gradedby, action, creation, ticketnumber, profit, prize, bulleyeprize, isnull(affiliateid,{AffiliateData.DefaultAffiliateId}), prizesversion, drawingid, domainid, url, currencyid
                    from {LottoStorage.TABLE_KENO_WINNERS} T WITH (nolock) {COMMON_JOIN_FOR_TICKETS_KENO}
					LEFT OUTER JOIN {TABLE_USER_PER_AFFILIATE} US on T.account = US.account
                    where cast(date as date)>='{DateToString(startDate)}' and cast(date as date)<='{DateToString(endDate)}' {accountNumberFilter} {domainIdFilter}
                    order by date;";
				var winners = WinnerKenoTicketsWithAccounts(command);
				var result = winners.Tickets;
				return result;
			}

			internal override CompletedKenoDraws LastPlayedDrawingsOfPlayer(string accountNumber)
			{
				var command = $@"(SELECT date, state
                                FROM {LottoStorage.TABLE_KENO_LOSERS} T WITH (nolock)
                                WHERE accountnumber ='{accountNumber.ToUpper()}')
                                UNION
                                (SELECT date, state
                                FROM {LottoStorage.TABLE_KENO_WINNERS} T WITH (nolock)
                                WHERE accountnumber ='{accountNumber.ToUpper()}')
                                UNION
                                (SELECT date, state
                                FROM {LottoStorage.TABLE_KENO_NOACTIONS} T WITH (nolock)
                                WHERE accountnumber ='{accountNumber.ToUpper()}')
                                ORDER BY date DESC
                                ";
				var result = GetDrawings(command);
				return result;
			}

			protected override void CreateDailyTotalProfitStorage()
			{
				StringBuilder statement = new StringBuilder();

				statement.AppendLine(@$"IF NOT EXISTS(
					SELECT 1 FROM INFORMATION_SCHEMA.TABLES 
					WHERE TABLE_NAME = '{TABLE_KENO_DAILY_TOTAL_PROFIT}')
					BEGIN
					CREATE TABLE {TABLE_KENO_DAILY_TOTAL_PROFIT}
					(
					DATE DATETIME NOT NULL,
					GAMETYPE CHAR(2) NOT NULL,
					AFFILIATEID INT NOT NULL,
					TICKETSCOUNT INT NOT NULL,
					WINNERSCOUNT INT NOT NULL,
					SOLD DECIMAL(10,2) NOT NULL,
					PRIZES DECIMAL(10,2) NOT NULL,
					PROFITS DECIMAL(10,2) NOT NULL,
					DOMAINID SMALLINT NOT NULL DEFAULT 0,
					CURRENCYID INT NOT NULL DEFAULT 2,
					PRIMARY KEY ( DATE, CURRENCYID, DOMAINID, GAMETYPE, AFFILIATEID )
					);
					END");

				statement.AppendLine(@$"IF NOT EXISTS(
					SELECT 1 FROM INFORMATION_SCHEMA.TABLES 
					WHERE TABLE_NAME = '{TABLE_USER_PER_AFFILIATE}')
					BEGIN
					CREATE TABLE {TABLE_USER_PER_AFFILIATE}
					(
					ACCOUNT VARCHAR({Customer.DATABASE_FIELD_ACCOUNT_LENGHT}) NOT NULL,
					AFFILIATEID INT NOT NULL,
					PRIMARY KEY (ACCOUNT)
					);
					END");

				statement.AppendLine(@$"IF NOT EXISTS(
					SELECT 1 FROM INFORMATION_SCHEMA.TABLES 
					WHERE TABLE_NAME = '{TABLE_AFFILIATES}')
					BEGIN
					CREATE TABLE {TABLE_AFFILIATES}
					(
					ID INT IDENTITY(1,1) PRIMARY KEY,
					NAME VARCHAR(30) NOT NULL
					);
					END");

				string sql = statement.ToString();
				ExecuteCommand(sql);
			}

			protected override void CreateTotalProfitByDrawingStorage()
			{
				StringBuilder statement = new StringBuilder();

				statement.AppendLine(@$"CREATE TABLE {TABLE_KENO_TOTAL_PROFIT_BY_DRAWING}
					(
					DATE DATETIME NOT NULL,
					GAMETYPE CHAR(2) NOT NULL,
					DRAWINGID INT NOT NULL,
					TICKETSCOUNT INT NOT NULL,
					PLAYERSCOUNT INT NOT NULL,
					SOLD DECIMAL(10,2) NOT NULL,
					PRIZES DECIMAL(10,2) NOT NULL,
					PROFITS DECIMAL(10,2) NOT NULL,
					AFFILIATEID INT NOT NULL DEFAULT 0,
					DOMAINID INT NOT NULL DEFAULT 0,
					PRIMARY KEY ( DATE, STATE, GAMETYPE, AFFILIATEID, DOMAINID )
					);");

				string sql = statement.ToString();
				ExecuteCommand(sql);
			}

			protected override void SaveInDailyTotalProfitStorage(List<DailyTotalProfitRecord2> dailyTotalProfitRecords)
			{
				StringBuilder sql = new StringBuilder();

				if (dailyTotalProfitRecords.Count > 0)
				{
					var commandToInsert = $"INSERT INTO {TABLE_KENO_DAILY_TOTAL_PROFIT}(DATE, GAMETYPE, TICKETSCOUNT, WINNERSCOUNT, SOLD, PRIZES, PROFITS, DOMAINID, CURRENCYID, AFFILIATEID) VALUES ";
					sql.Append(commandToInsert);
					var dailyTotalProfitValues = new List<string>();
					foreach (var record in dailyTotalProfitRecords)
					{
						string recordToInsert = $@"('{ ToDateString(record.Date.Year, record.Date.Month, record.Date.Day, 0, 0, 0)}',
						'{ record.GameType}',
						'{ record.TicketsCount}',
						'{ record.WinnersCount}',
						'{ record.Sold}',
						'{ record.Prizes}',
						'{ record.Profits}',
						'{ record.DomainId}',
						'{record.CurrencyId}',
						{record.AffiliateId}
						)";

						dailyTotalProfitValues.Add(recordToInsert);
					}

					sql.Append(string.Join(",", dailyTotalProfitValues));
					sql.Append(';');
				}

				if (sql.Length > 0)
				{
					ExecuteCommand(sql.ToString());
				}
			}

			protected override void SaveInTotalProfitByDrawingStorage(List<TotalProfitByDrawingRecord> totalProfitByDrawingRecords)
			{
				StringBuilder cmdText = new StringBuilder();

				if (totalProfitByDrawingRecords.Count > 0)
				{
					int groupsOfRowsToInsert = totalProfitByDrawingRecords.Count > MAXIMUM_NUMBER_OF_ROWS_TO_INSERT ? (totalProfitByDrawingRecords.Count / MAXIMUM_NUMBER_OF_ROWS_TO_INSERT) + 1 : MINIMUM_SET_OF_ROWS_TO_INSERT;
					var totalProfitValues = new List<string>();
					for (int index = 0; index < groupsOfRowsToInsert; index++)
					{
						var rowsToSkip = index * MAXIMUM_NUMBER_OF_ROWS_TO_INSERT;
						cmdText.Append($"INSERT INTO {TABLE_KENO_TOTAL_PROFIT_BY_DRAWING}(DATE, GAMETYPE, DRAWINGID, TICKETSCOUNT, PLAYERSCOUNT, SOLD, PRIZES, PROFITS, DOMAINID, AFFILIATEID) VALUES ");

						foreach (TotalProfitByKenoDrawingRecord record in totalProfitByDrawingRecords.Skip(rowsToSkip).Take(MAXIMUM_NUMBER_OF_ROWS_TO_INSERT))
						{
							string recordToInsert = $@"('{ ToDateString(record.DrawDate.Year, record.DrawDate.Month, record.DrawDate.Day, record.DrawDate.Hour, record.DrawDate.Minute, 0)}',
                            '{ IdOfLottery.KN}',
                            '{ record.DrawingId}',
                            '{ record.TicketsCount}',
                            '{ record.PlayersCount}',
                            '{ record.Sold}',
                            '{ record.Prizes}',
                            '{ record.Profits}',
							'{ record.DomainId}',
							{ record.AffiliateId}
                            )";

							totalProfitValues.Add(recordToInsert);
						}

						cmdText.Append(string.Join(",", totalProfitValues));
						cmdText.Append(';');
						totalProfitValues.Clear();
					}
				}

				if (cmdText.Length > 0)
				{
					ExecuteCommand(cmdText.ToString());
				}
			}

		}

		private class KenoDBHandlerInMemory : KenoDBHandler
		{
			const string DefaultConnectionString= "-";
			internal KenoDBHandlerInMemory() : base(DefaultConnectionString)
			{
			}

			internal KenoDBHandlerInMemory(Company company) : base(company, DefaultConnectionString)
			{
			}

            internal override DateTime LastDateInDailyTotalProfit() 
			{ 
				throw new NotImplementedException(); 
			}

            protected override void CreateDailyTotalProfitStorage()
            {
                throw new NotImplementedException();
            }

            protected override void CreateTotalProfitByDrawingStorage()
            {
                throw new NotImplementedException();
            }

            protected override List<DailyTotalProfitRecord2> DailyTotalProfitRecords2(string command)
            {
                throw new NotImplementedException();
            }

            protected override void ExecuteCommand(string cmdText)
            {
                throw new NotImplementedException();
            }

            protected override List<DailyTotalProfitRecord2> FilteredDailyTotalProfitRecords2(DateTime startDate, DateTime endDate, string domainIds)
            {
                throw new NotImplementedException();
            }

            protected override List<TotalProfitByDrawingRecord> FilteredTotalProfitByDrawingRecords(DateTime startDate, DateTime endDate, string domainIds)
            {
                throw new NotImplementedException();
            }

            protected override CompletedKenoDraws GetDrawings(string command)
            {
                throw new NotImplementedException();
            }

            protected override IEnumerable<CompletedKenoTicket> LoserKenoTickets(string command)
            {
                throw new NotImplementedException();
            }

            protected override IEnumerable<CompletedKenoTicket> LoserKenoTicketsForDailyTotalProfit(DateTime drawDate, string gameType, string domainIds)
            {
                throw new NotImplementedException();
            }

            protected override IEnumerable<CompletedKenoTicket> LoserKenoTicketsForDrawingsReport(DateTime startDate, string hour, string minute, string accountNumber, string gameType, string ticketNumber, string domainIds)
            {
                throw new NotImplementedException();
            }

            protected override IEnumerable<CompletedKenoTicket> LoserTicketsForDrawingsReport(DateTime startDate, DateTime endDate, string accountNumber, string ticketNumber, string domainIds)
            {
                throw new NotImplementedException();
            }

            protected override IEnumerable<CompletedKenoTicket> LoserTicketsForDrawingsReport(DateTime drawDate, string accountNumber, string ticketNumber, string domainIds)
            {
                throw new NotImplementedException();
            }

            protected override IEnumerable<CompletedKenoTicket> NoActionKenoTickets(string command)
            {
                throw new NotImplementedException();
            }

            protected override IEnumerable<CompletedKenoTicket> NoActionKenoTicketsForDailyTotalProfit(DateTime drawDate, string gameType, string domainIds)
            {
                throw new NotImplementedException();
            }

            protected override IEnumerable<CompletedKenoTicket> NoActionKenoTicketsForDrawingsReport(DateTime startDate, string hour, string minute, string accountNumber, string gameType, string ticketNumber, string domainIds)
            {
                throw new NotImplementedException();
            }

            protected override IEnumerable<CompletedKenoTicket> NoActionTicketsForDrawingsReport(DateTime startDate, DateTime endDate, string accountNumber, string ticketNumber, string domainIds)
            {
                throw new NotImplementedException();
            }

            protected override IEnumerable<CompletedKenoTicket> NoActionTicketsForDrawingsReport(DateTime drawDate, string accountNumber, string ticketNumber, string domainIds)
            {
                throw new NotImplementedException();
            }

            protected override void SaveInDailyTotalProfitStorage(List<DailyTotalProfitRecord2> dailyTotalProfitRecords)
            {
                throw new NotImplementedException();
            }

            protected override void SaveInTotalProfitByDrawingStorage(List<TotalProfitByDrawingRecord> dailyTotalProfitRecords)
            {
                throw new NotImplementedException();
            }

            protected override List<TotalProfitByDrawingRecord> TotalProfitByDrawingRecords(string command)
            {
                throw new NotImplementedException();
            }

            protected override IEnumerable<CompletedKenoTicket> WinnerKenoTickets(string command)
            {
                throw new NotImplementedException();
            }

            protected override IEnumerable<CompletedKenoTicket> WinnerKenoTicketsForDailyTotalProfit(DateTime startDate, string gameType, string domainIds)
            {
                throw new NotImplementedException();
            }

            protected override IEnumerable<CompletedKenoTicket> WinnerKenoTicketsForDrawingsReport(DateTime startDate, string hour, string minute, string accountNumber, string gameType, string ticketNumber, string domainIds)
            {
                throw new NotImplementedException();
            }

            protected override CompletedKenoTicketsWithAccounts WinnerKenoTicketsWithAccounts(string command)
            {
                throw new NotImplementedException();
            }

            protected override IEnumerable<CompletedKenoTicket> WinnerTicketsForDrawingsReport(DateTime startDate, DateTime endDate, string accountNumber, string ticketNumber, string domainIds)
            {
                throw new NotImplementedException();
            }

            protected override IEnumerable<CompletedKenoTicket> WinnerTicketsForDrawingsReport(DateTime drawDate, string accountNumber, string ticketNumber, string domainIds)
            {
                throw new NotImplementedException();
            }

            protected override IEnumerable<CompletedKenoTicket> WinnerTicketsForWinnersReport(DateTime startDate, DateTime endDate, string accountNumber, string domainIds)
            {
                throw new NotImplementedException();
            }

            internal override bool ExistsTable(string table)
            {
                throw new NotImplementedException();
            }

            internal override void InsertAffiliateIfNotExist(int inet, string name, string accountNumber)
            {
                throw new NotImplementedException();
            }

            internal override CompletedKenoDraws LastPlayedDrawingsOfPlayer(string accountNumber)
            {
                throw new NotImplementedException();
            }

            internal override IEnumerable<CompletedKenoTicket> LoserTicketsOfPlayer(DateTime drawDate, string accountNumber)
            {
                throw new NotImplementedException();
            }

            internal override IEnumerable<CompletedKenoTicket> LoserTicketsOfPlayerBetween(DateTime startedDate, DateTime endedDate, string accountNumber)
            {
                throw new NotImplementedException();
            }

            internal override IEnumerable<CompletedKenoTicket> NoActionTicketsOfPlayer(DateTime drawDate, string accountNumber)
            {
                throw new NotImplementedException();
            }

            internal override IEnumerable<CompletedKenoTicket> NoActionTicketsOfPlayerBetween(DateTime startedDate, DateTime endedDate, string accountNumber)
            {
                throw new NotImplementedException();
            }

            internal override IEnumerable<CompletedKenoTicket> WinnerTicketsOfPlayer(DateTime drawDate, string accountNumber)
            {
                throw new NotImplementedException();
            }

            internal override IEnumerable<CompletedKenoTicket> WinnerTicketsOfPlayerBetween(DateTime startedDate, DateTime endedDate, string accountNumber)
            {
                throw new NotImplementedException();
            }
        }

		public sealed class CompletedKenoTicketsWithAccounts : AffiliateAccounts
		{
			internal List<CompletedKenoTicket> Tickets { get; } = new List<CompletedKenoTicket>();

			internal void Add(CompletedKenoTicket info)
			{
				Tickets.Add(info);
			}

		}


	}


}
