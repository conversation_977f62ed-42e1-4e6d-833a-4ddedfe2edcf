﻿using Microsoft.VisualStudio.TestTools.UnitTesting;
using System;
using System.Linq;
using town.connectors;
using static town.connectors.CustomSettings;
using static town.connectors.CustomSettings.CustomSetting;

namespace GamesEngineTests.Unit_Tests.Connectors
{

	[TestClass]
	public class CustomSettingsTest
	{

		[TestMethod]
		public void Create_Custom_Settings()
		{
			Variables variables = new Variables();
			var obj = new { Name = "tester" };
			DateTime now = DateTime.Now;


			CustomSettings cs = new CustomSettings(variables);
			cs.AddFixedParameter(now, "key1", "value");
			cs.AddFixedParameter(now, "key2", 1);
			cs.AddFixedParameter(now, "key3", 1.2m);
			cs.AddFixedParameter(now, "key4", obj);


			try
			{
				cs.AddFixedParameter(now, "key4", "value2");
				Assert.Fail("type is not valid.");
			}
			catch (Exception ex) { }

			try
			{
				cs.AddFixedParameter(now, "key3", "value2");
				Assert.Fail("type is not valid.");
			}
			catch (Exception ex) { }

			try
			{
				cs.AddFixedParameter(now, "key2", "value2");
				Assert.Fail("type is not valid.");
			}
			catch (Exception ex) { }

			cs.AddFixedParameter(now, "key1", "value2");

			Assert.AreEqual("value2", cs.Get(now, "key1").AsString);
			Assert.AreEqual(1, cs.Get(now, "key2").AsInt);
			Assert.AreEqual(1.2m, cs.Get(now, "key3").AsDecimal);
			Assert.AreEqual(obj, cs.Get(now, "key4").AsObject);
			Assert.AreEqual(4, cs.Count);


		}

		[TestMethod]
		public void Secret_Custom_Settings()
		{
			Variables variables = new Variables();
			var obj = new { Name = "tester" };
			DateTime now = DateTime.Now;

			CustomSettings cs = new CustomSettings(variables);
			cs.AddFixedParameter(now, "password", new Secret("value"));

			try
			{
				var result = cs.Get(now, "password").AsString;
				Assert.Fail("type is not valid.");
			}
			catch (Exception ex) { }

			Assert.AreEqual("value", cs.Get(now, "password").AsSecret.ToString());
			Assert.AreEqual(typeof(Secret), cs.Get(now, "password").AsSecret.GetType());
			Assert.AreEqual(1, cs.Count);


		}


		[TestMethod]
		public void Change_Custom_Settings()
		{
			Variables variables = new Variables();
			var obj = new { Name = "tester" };
			DateTime now = DateTime.Now;
			DateTime dateToChangeValue = now.AddDays(1);
			DateTime dateToTest1 = now.AddDays(1);
			DateTime dateToTest2 = now.AddDays(1).AddMilliseconds(1);

			CustomSettings cs = new CustomSettings(variables);
			cs.AddFixedParameter(now, "key1", "value");
			cs.AddFixedParameter(now, "key2", 1);
			cs.AddFixedParameter(now, "key3", 1.2m);
			cs.AddFixedParameter(now, "key4", obj);

			Assert.AreEqual(0, cs.Get(now, "key1").Changes.Count());
			Assert.AreEqual(1, cs.Get(now, "key1").Version);

			try
			{
				cs.Get(now, "key1").ChangeTo(now.AddMilliseconds(-1), "Jhon", "value2");
				Assert.Fail("Can not schedule a change before now.");
			}
			catch (Exception ex) { }

			//Schedule a Change
			cs.Get(now, "key1").ChangeTo(dateToChangeValue, "Jhon", "value2");

			Assert.AreEqual(1, cs.Get(now, "key1").Changes.Count());
			Assert.AreEqual(0, cs.Get(now, "key1").Logs.Count());
			Assert.AreEqual(1, cs.Get(now, "key1").Version);
			Assert.AreEqual("value", cs.Get(now, "key1").AsString);

			//Apply change
			cs.Get(dateToTest1, "key1");

			Assert.AreEqual("value2", cs.Get(dateToTest1, "key1").AsString);
			Assert.AreEqual(1, cs.Get(now, "key1").Logs.Count());
			Assert.AreEqual("value2", cs.Get(dateToTest2, "key1").AsString);
			Assert.AreEqual(2, cs.Get(now, "key1").Version);

		}

		[TestMethod]
		public void Sort_Changes_queue()
		{
			Variables variables = new Variables();
			var obj = new { Name = "tester" };
			DateTime now = DateTime.Now;
			DateTime dateToChangeValue1 = now.AddDays(1);
			DateTime dateToChangeValue2 = dateToChangeValue1.AddDays(1);
			DateTime dateToChangeValue3 = dateToChangeValue2.AddDays(1);

			CustomSettings cs = new CustomSettings(variables);
			cs.AddFixedParameter(now, "key1", "value");
			cs.AddFixedParameter(now, "key2", 1);
			cs.AddFixedParameter(now, "key3", 1.2m);
			cs.AddFixedParameter(now, "key4", obj);

			//Schedule a Change
			cs.Get(now, "key1").ChangeTo(dateToChangeValue3, "Jhon", "value2");
			cs.Get(now, "key1").ChangeTo(dateToChangeValue2, "Jhon1", "value3");
			cs.Get(now, "key1").ChangeTo(dateToChangeValue1, "Jhon1", "value3");

			Assert.AreEqual(dateToChangeValue1, cs.Get(now, "key1").Changes.ElementAt(0).DateToChangeValue);
			Assert.AreEqual(dateToChangeValue2, cs.Get(now, "key1").Changes.ElementAt(1).DateToChangeValue);
			Assert.AreEqual(dateToChangeValue3, cs.Get(now, "key1").Changes.ElementAt(2).DateToChangeValue);

			//Apply change
			cs.Get(dateToChangeValue1, "key1");

			Assert.AreEqual(dateToChangeValue2, cs.Get(now, "key1").Changes.ElementAt(0).DateToChangeValue);
			Assert.AreEqual(dateToChangeValue3, cs.Get(now, "key1").Changes.ElementAt(1).DateToChangeValue);


		}

		[TestMethod]
		public void Variables_Custom_Settings()
		{
			Variables variables = new Variables();
			var obj = new { Name = "tester" };
			DateTime now = DateTime.Now;

			CustomSettings cs = new CustomSettings(variables);
			CustomSettings cs1 = new CustomSettings(variables);
			cs.AddFixedParameter(now, "key1", "value");
			cs.AddFixedParameter(now, "key2", 1);
			cs.AddFixedParameter(now, "key3", 1.2m);
			cs.AddFixedParameter(now, "key4", obj);

			try
			{
				cs.AddFixedParameter(now, "key1", 1);
				Assert.Fail("Type is not valid.");
			}
			catch (Exception ex) { }

			cs.AddFixedParameter(now, "key1", "value");

			Assert.AreEqual(4, variables.Count);

		}

		[TestMethod]
		public void CountSettings()
		{
			Variables variables = new Variables();
			DateTime now = DateTime.Now;

			CustomSettings cs = new CustomSettings(variables);
			cs.AddFixedParameter(now, "key1", "value");
			cs.AddFixedParameter(now, "key2", 1);
			cs.AddFixedParameter(now, "key3", 1.2m);
			cs.AddFixedParameter(now, "key4", "value1");
			cs.AddFixedParameter(now, "key5", 2);
			cs.AddFixedParameter(now, "key6", 1.3m);
			cs.AddFixedParameter(now, "key7", "value2");
			cs.AddFixedParameter(now, "key8", 3);
			cs.AddFixedParameter(now, "key9", 1.4m);
			cs.AddFixedParameter(now, "key10", "value");
			cs.AddFixedParameter(now, "key11", 1);
			cs.AddFixedParameter(now, "key12", 1.5m);

			CustomSetting amount = cs.AddVariableParameter("Amount");
			CustomSetting name = cs.AddVariableParameter("Name");
			CustomSetting id = cs.AddVariableParameter("id");

			Assert.AreEqual(15, Enumerable.Count(cs.Settings));
		}
		[TestMethod]
		public void error_adding_new_parameter_after_prepare()
		{

			//TODO disparar el docker-compose  cashier asi stub
			Variables variables = new Variables();
			var obj = new { Name = "tester" };
			DateTime now = DateTime.Now;
			CustomSettings cs = new CustomSettings(variables);
			cs.AddFixedParameter(now, "CompanyBaseUrlServices", "http://Test.com");
			cs.AddFixedParameter(now, "TokenSystemId", "3aab83fb");
			cs.AddFixedParameter(now, "TokenSystemPassword", new Secret("38f845e977af7f2cd6313c5a9d35e4de7f353405100e7748ab15b410eb53d886"));

			CustomSetting amount = cs.AddVariableParameter("Amount");
			CustomSetting name = cs.AddVariableParameter("Name");
			CustomSetting id = cs.AddVariableParameter("id");

			cs.Prepare();

			try 
			{
				CustomSetting id2 = cs.AddVariableParameter("id2");
				Assert.Fail($"Is not valid to add new parameter after prepare the {nameof(CustomSetting)} instance.");
			}catch (Exception ex) { }
			

		}
		[TestMethod]
		public void Schedule_a_change_in_a_custom_setting()
		{

			//TODO disparar el docker-compose  cashier asi stub
			Variables variables = new Variables();
			var obj = new { Name = "tester" };
			DateTime now = DateTime.Now;
			CustomSettings cs = new CustomSettings(variables);
			cs.AddFixedParameter(now, "CompanyBaseUrlServices", "http://Test.com");
			cs.AddFixedParameter(now, "TokenSystemId", "3aab83fb");
			cs.AddFixedParameter(now, "TokenSystemPassword", new Secret("38f845e977af7f2cd6313c5a9d35e4de7f353405100e7748ab15b410eb53d886"));

			CustomSetting amount = cs.AddVariableParameter("Amount");
			CustomSetting name = cs.AddVariableParameter("Name");
			CustomSetting id = cs.AddVariableParameter("id");

			cs.Prepare();

			using (RecordSet recordSet = cs.GetRecordSet())
			{
				recordSet.SetParameter(amount, 100);
				recordSet.SetParameter(name, "Cris");
				recordSet.SetParameter(id, "ABC");

			}

			DateTime tomorrow = now.AddDays(1);

			Assert.AreEqual("3aab83fb", cs.CustomParameters.Collection.ElementAt(0).CustomSettings[1].Val);

			cs.ChangeValueStartingOn(tomorrow, "TokenSystemId", "2222222", "Cristian");

			Assert.AreEqual("http://Test.com", cs.CustomParameters.Collection.ElementAt(0).CustomSettings[0].Val);
			Assert.AreEqual(true, cs.ThereArePendingChanges);
			Assert.AreEqual(1, cs.AmountOfPendingChanges);

			cs.Get(tomorrow, "TokenSystemId");

			Assert.AreEqual(false, cs.ThereArePendingChanges);
			Assert.AreEqual(0, cs.AmountOfPendingChanges);

			Assert.AreEqual("2222222", cs.CustomParameters.Collection.ElementAt(0).CustomSettings[1].Val);
			Assert.AreEqual(new Secret("38f845e977af7f2cd6313c5a9d35e4de7f353405100e7748ab15b410eb53d886"), cs.CustomParameters.Collection.ElementAt(0).CustomSettings[2].Val);
			Assert.AreEqual("http://Test.com", cs.CustomParameters.Collection.ElementAt(0).CustomSettings[0].Val);
			Assert.AreEqual("2222222", cs.CustomParameters.Collection.ElementAt(1).CustomSettings[1].Val);
			Assert.AreEqual("2222222", cs.CustomParameters.Collection.ElementAt(2).CustomSettings[1].Val);
			Assert.AreEqual("2222222", cs.CustomParameters.Collection.ElementAt(3).CustomSettings[1].Val);
			Assert.AreEqual("2222222", cs.CustomParameters.Collection.ElementAt(4).CustomSettings[1].Val);
			Assert.AreEqual("2222222", cs.CustomParameters.Collection.ElementAt(5).CustomSettings[1].Val);
			Assert.AreEqual("2222222", cs.CustomParameters.Collection.ElementAt(6).CustomSettings[1].Val);
			Assert.AreEqual("2222222", cs.CustomParameters.Collection.ElementAt(7).CustomSettings[1].Val);
			Assert.AreEqual("2222222", cs.CustomParameters.Collection.ElementAt(8).CustomSettings[1].Val);
			Assert.AreEqual("2222222", cs.CustomParameters.Collection.ElementAt(9).CustomSettings[1].Val);
			Assert.AreEqual(10, cs.CustomParameters.Collection.Count());

		}

		[TestMethod]
		public void Schedule_several_changes_in_a_custom_setting_at_same_date()
		{

			//TODO disparar el docker-compose  cashier asi stub
			Variables variables = new Variables();
			var obj = new { Name = "tester" };
			DateTime now = DateTime.Now;
			CustomSettings cs = new CustomSettings(variables);
			cs.AddFixedParameter(now, "CompanyBaseUrlServices", "http://Test.com");
			cs.AddFixedParameter(now, "TokenSystemId", "3aab83fb");
			cs.AddFixedParameter(now, "TokenSystemPassword", new Secret("38f845e977af7f2cd6313c5a9d35e4de7f353405100e7748ab15b410eb53d886"));

			CustomSetting amount = cs.AddVariableParameter("Amount");
			CustomSetting name = cs.AddVariableParameter("Name");
			CustomSetting id = cs.AddVariableParameter("id");

			cs.Prepare();

			using (RecordSet recordSet = cs.GetRecordSet())
			{
				recordSet.SetParameter(amount, 100);
				recordSet.SetParameter(name, "Cris");
				recordSet.SetParameter(id, "ABC");

			}

			DateTime tomorrow = now.AddDays(1);

			Assert.AreEqual("3aab83fb", cs.CustomParameters.Collection.ElementAt(0).CustomSettings[1].Val);

			cs.ChangeValueStartingOn(tomorrow, "TokenSystemId", "2222222", "Cristian");
			cs.ChangeValueStartingOn(tomorrow, "TokenSystemId", "33333", "Cristian");
			cs.ChangeValueStartingOn(tomorrow.AddMilliseconds(-1), "TokenSystemId", "44444", "Cristian");
			
			Assert.AreEqual(true, cs.ThereArePendingChanges);
			Assert.AreEqual(3, cs.AmountOfPendingChanges);

			Assert.AreEqual("http://Test.com", cs.CustomParameters.Collection.ElementAt(0).CustomSettings[0].Val);

			cs.Get(tomorrow, "TokenSystemId");

			Assert.AreEqual(false, cs.ThereArePendingChanges);
			Assert.AreEqual(0, cs.AmountOfPendingChanges);

			Assert.AreEqual("33333", cs.CustomParameters.Collection.ElementAt(0).CustomSettings[1].Val);
			Assert.AreEqual(new Secret("38f845e977af7f2cd6313c5a9d35e4de7f353405100e7748ab15b410eb53d886"), cs.CustomParameters.Collection.ElementAt(0).CustomSettings[2].Val);
			Assert.AreEqual("http://Test.com", cs.CustomParameters.Collection.ElementAt(0).CustomSettings[0].Val);
			Assert.AreEqual("33333", cs.CustomParameters.Collection.ElementAt(1).CustomSettings[1].Val);
			Assert.AreEqual("33333", cs.CustomParameters.Collection.ElementAt(2).CustomSettings[1].Val);
			Assert.AreEqual("33333", cs.CustomParameters.Collection.ElementAt(3).CustomSettings[1].Val);
			Assert.AreEqual("33333", cs.CustomParameters.Collection.ElementAt(4).CustomSettings[1].Val);
			Assert.AreEqual("33333", cs.CustomParameters.Collection.ElementAt(5).CustomSettings[1].Val);
			Assert.AreEqual("33333", cs.CustomParameters.Collection.ElementAt(6).CustomSettings[1].Val);
			Assert.AreEqual("33333", cs.CustomParameters.Collection.ElementAt(7).CustomSettings[1].Val);
			Assert.AreEqual("33333", cs.CustomParameters.Collection.ElementAt(8).CustomSettings[1].Val);
			Assert.AreEqual("33333", cs.CustomParameters.Collection.ElementAt(9).CustomSettings[1].Val);
			Assert.AreEqual(10, cs.CustomParameters.Collection.Count());

		}

		[TestMethod]
		public void Schedule_several_changes()
		{

			//TODO disparar el docker-compose  cashier asi stub
			Variables variables = new Variables();
			var obj = new { Name = "tester" };
			DateTime now = DateTime.Now;
			CustomSettings cs = new CustomSettings(variables);
			cs.AddFixedParameter(now, "CompanyBaseUrlServices", "http://Test.com");
			cs.AddFixedParameter(now, "TokenSystemId", "3aab83fb");
			cs.AddFixedParameter(now, "TokenSystemPassword", new Secret("38f845e977af7f2cd6313c5a9d35e4de7f353405100e7748ab15b410eb53d886"));

			CustomSetting amount = cs.AddVariableParameter("Amount");
			CustomSetting name = cs.AddVariableParameter("Name");
			CustomSetting id = cs.AddVariableParameter("id");

			cs.Prepare();

			using (RecordSet recordSet = cs.GetRecordSet())
			{
				recordSet.SetParameter(amount, 100);
				recordSet.SetParameter(name, "Cris");
				recordSet.SetParameter(id, "ABC");

			}

			DateTime tomorrow = now.AddDays(1);

			Assert.AreEqual("3aab83fb", cs.CustomParameters.Collection.ElementAt(0).CustomSettings[1].Val);

			cs.ChangeValueStartingOn(tomorrow, "TokenSystemId", "2222222", "Cristian");
			cs.ChangeValueStartingOn(tomorrow, "TokenSystemId", "33333", "Cristian");
			cs.ChangeValueStartingOn(tomorrow.AddMilliseconds(-1), "TokenSystemId", "44444", "Cristian");
			cs.ChangeValueStartingOn(tomorrow.AddDays(1), "CompanyBaseUrlServices", "http://Test.com", "Cristian");
			cs.ChangeValueStartingOn(tomorrow.AddDays(2), "TokenSystemPassword", new Secret("38f845e977fghgfhfga9d35e4de7f353405100e7748ab15b410eb53d886"), "Cristian");

			Assert.AreEqual(true, cs.ThereArePendingChanges);
			Assert.AreEqual(5, cs.AmountOfPendingChanges);

			Assert.AreEqual("http://Test.com", cs.CustomParameters.Collection.ElementAt(0).CustomSettings[0].Val);

			cs.Get(tomorrow, "TokenSystemId");

			Assert.AreEqual(true, cs.ThereArePendingChanges);
			Assert.AreEqual(2, cs.AmountOfPendingChanges);

			Assert.AreEqual("33333", cs.CustomParameters.Collection.ElementAt(0).CustomSettings[1].Val);
			Assert.AreEqual(new Secret("38f845e977af7f2cd6313c5a9d35e4de7f353405100e7748ab15b410eb53d886"), cs.CustomParameters.Collection.ElementAt(0).CustomSettings[2].Val);
			Assert.AreEqual("http://Test.com", cs.CustomParameters.Collection.ElementAt(0).CustomSettings[0].Val);
			Assert.AreEqual("33333", cs.CustomParameters.Collection.ElementAt(1).CustomSettings[1].Val);
			Assert.AreEqual("33333", cs.CustomParameters.Collection.ElementAt(2).CustomSettings[1].Val);
			Assert.AreEqual("33333", cs.CustomParameters.Collection.ElementAt(3).CustomSettings[1].Val);
			Assert.AreEqual("33333", cs.CustomParameters.Collection.ElementAt(4).CustomSettings[1].Val);
			Assert.AreEqual("33333", cs.CustomParameters.Collection.ElementAt(5).CustomSettings[1].Val);
			Assert.AreEqual("33333", cs.CustomParameters.Collection.ElementAt(6).CustomSettings[1].Val);
			Assert.AreEqual("33333", cs.CustomParameters.Collection.ElementAt(7).CustomSettings[1].Val);
			Assert.AreEqual("33333", cs.CustomParameters.Collection.ElementAt(8).CustomSettings[1].Val);
			Assert.AreEqual("33333", cs.CustomParameters.Collection.ElementAt(9).CustomSettings[1].Val);
			Assert.AreEqual(10, cs.CustomParameters.Collection.Count());

		}

		[TestMethod]
		public void NextDateToChangeAndNextValue()
		{
			Variables variables = new Variables();
			DateTime now = DateTime.Now;
			DateTime dateToChangeValue1 = now.AddDays(1);
			DateTime dateToChangeValue2 = dateToChangeValue1.AddDays(1);
			DateTime dateToChangeValue3 = dateToChangeValue2.AddDays(1);

			CustomSettings cs = new CustomSettings(variables);
			cs.AddFixedParameter(now, "key1", "value");
			cs.AddFixedParameter(now, "key2", 1);
			cs.Get(now, "key1").ChangeTo(dateToChangeValue3, "N/A", "value03");
			cs.Get(now, "key1").ChangeTo(dateToChangeValue2, "N/A", "value02");
			cs.Get(now, "key1").ChangeTo(dateToChangeValue1, "N/A", "value01");

			Assert.AreEqual(dateToChangeValue1, cs.Get(now, "key1").NextDateToChange());
			Assert.AreEqual("value01", cs.Get(now, "key1").NextValue());
			Assert.AreEqual(DateTime.MaxValue, cs.Get(now, "key2").NextDateToChange());
			Assert.AreEqual(dateToChangeValue2, cs.Get(dateToChangeValue1.AddHours(1), "key1").NextDateToChange());
			Assert.AreEqual("value02", cs.Get(dateToChangeValue1.AddHours(1), "key1").NextValue());
			Assert.AreEqual(2, Enumerable.Count(cs.Settings));
		}

		[TestMethod]
		public void HasScheduledChange()
		{
			Variables variables = new Variables();
			DateTime now = DateTime.Now;
			DateTime dateToChangeValue1 = now.AddDays(1);

			CustomSettings cs = new CustomSettings(variables);
			cs.AddFixedParameter(now, "key1", "value");
			cs.AddFixedParameter(now, "key2", 1);
			cs.Get(now, "key1").ChangeTo(dateToChangeValue1, "N/A", "value0");

			Assert.IsTrue(cs.Get(now, "key1").HasScheduledChange());
			Assert.IsFalse(cs.Get(now, "key2").HasScheduledChange());
		}
			
		[TestMethod]
		public void Type()
		{
			Variables variables = new Variables();
			DateTime now = DateTime.Now;

			CustomSettings cs = new CustomSettings(variables);
			var cs1 = cs.AddFixedParameter(now, "key1", "value");
			var cs2 = cs.AddFixedParameter(now, "key2", 1);
			var cs3 = cs.AddFixedParameter(now, "key3", 1.2m);

			Assert.AreEqual("String", cs1.Type);
			Assert.AreEqual("Int32", cs2.Type);
			Assert.AreEqual("Decimal", cs3.Type);
		}
	}
}
