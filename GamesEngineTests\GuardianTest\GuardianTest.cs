﻿using Connectors.town.connectors.driver.transactions;
using GamesEngine.Business;
using GamesEngine.Custodian;
using GamesEngine.Custodian.Operations;
using GamesEngine.Custodian.Persistance;
using GamesEngine.Customers;
using GamesEngine.Domains;
using GamesEngine.Exchange;
using GamesEngine.Finance;
using GamesEngine.Settings;
using GamesEngineTests.Exchange;
using GamesEngineTests.Unit_Tests.Games.Lotto;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using town.connectors;
using town.connectors.drivers;
using town.connectors.drivers.hades;
using Unit.Games.Tools;
using static GamesEngine.Business.WholePaymentMethods;
using static GamesEngine.Business.WholePaymentProcessor;
using static GamesEngine.Custodian.Operations.DisbursementExecutionResponse;

using static town.connectors.drivers.Result;
using DepositTransaction = Connectors.town.connectors.driver.transactions.DepositTransaction;
using WithdrawalTransaction = Connectors.town.connectors.driver.transactions.WithdrawalTransaction;

namespace GamesEngineTests.Custodian
{
	public class TestDepositBTC : ProcessorDriver
	{
		private int authorizationNumber = 0;
		public TestDepositBTC()
			: base("TEST", PaymentMethod.Secrets, "BTC", TransactionType.Deposit, 1)
		{
		}

		public override string Description => throw new NotImplementedException();
		public override string Fabricator => throw new NotImplementedException();

        public override DateTime ReleaseDate => throw new NotImplementedException();

        public override T Execute<T>(DateTime now, CustomSettings.RecordSet recordSet)
		{
			var result = new DepositTransaction(++authorizationNumber, TransactionStatus.APPROVED, Entity, PaymentMethod, CurrencyIsoCodes);
			return (T)Convert.ChangeType(result, typeof(T));
		}

		public override Task<T> ExecuteAsync<T>(DateTime now, CustomSettings.RecordSet recordSet)
		{
			throw new NotImplementedException();
		}

		public override void Prepare(DateTime now)
		{
		}
	}

	public class TestDepositBTC2 : ProcessorDriver
	{
		private int authorizationNumber = 0;
		public TestDepositBTC2()
			: base("TEST", PaymentMethod.Secrets, "BTC", TransactionType.Deposit, 2)
		{
		}

		public override string Description => throw new NotImplementedException();
		public override string Fabricator => throw new NotImplementedException();

		public override DateTime ReleaseDate => throw new NotImplementedException();

		public override T Execute<T>(DateTime now, CustomSettings.RecordSet recordSet)
		{
			var result = new DepositTransaction(++authorizationNumber, TransactionStatus.APPROVED, Entity, PaymentMethod, CurrencyIsoCodes);
			return (T)Convert.ChangeType(result, typeof(T));
		}

		public override Task<T> ExecuteAsync<T>(DateTime now, CustomSettings.RecordSet recordSet)
		{
			throw new NotImplementedException();
		}

		public override void Prepare(DateTime now)
		{
		}
	}

	public class TestWithdrawalBTC : ProcessorDriver
	{
		private int authorizationNumber = 0;
		public TestWithdrawalBTC()
			: base("TEST", PaymentMethod.Secrets, "BTC", TransactionType.Withdrawal, 1)
		{
		}

		public override string Description => throw new NotImplementedException();
		public override string Fabricator => throw new NotImplementedException();

		public override DateTime ReleaseDate => throw new NotImplementedException();

		public override T Execute<T>(DateTime now, CustomSettings.RecordSet recordSet)
		{
			var result = new WithdrawalTransaction(++authorizationNumber, TransactionStatus.APPROVED, Entity, PaymentMethod, CurrencyIsoCodes);
			return (T)Convert.ChangeType(result, typeof(T));
		}

		public override Task<T> ExecuteAsync<T>(DateTime now, CustomSettings.RecordSet recordSet)
		{
			throw new NotImplementedException();
		}

		public override void Prepare(DateTime now)
		{
			//CustomSettings.Prepare();
		}
	}

	public class TestDepositUSD : ProcessorDriver
	{
		private int authorizationNumber = 0;
		public TestDepositUSD()
			: base("TEST", PaymentMethod.Cash, "USD", TransactionType.Deposit, 1)
		{
		}

		public override string Description => throw new NotImplementedException();
		public override string Fabricator => throw new NotImplementedException();

		public override DateTime ReleaseDate => throw new NotImplementedException();

		public override T Execute<T>(DateTime now, CustomSettings.RecordSet recordSet)
		{
			var result = new DepositTransaction(++authorizationNumber, TransactionStatus.APPROVED, Entity, PaymentMethod, CurrencyIsoCodes);
			return (T)Convert.ChangeType(result, typeof(T));
		}

		public override Task<T> ExecuteAsync<T>(DateTime now, CustomSettings.RecordSet recordSet)
		{
			throw new NotImplementedException();
		}

		public override void Prepare(DateTime now)
		{
			//CustomSettings.Prepare();
		}
	}

	public class TestWithdrawalUSD : ProcessorDriver
	{
		private int authorizationNumber = 0;
		public TestWithdrawalUSD()
			: base("TEST", PaymentMethod.Cash, "USD", TransactionType.Withdrawal, 1)
		{
		}

		public override string Description => throw new NotImplementedException();
		public override string Fabricator => throw new NotImplementedException();

		public override DateTime ReleaseDate => throw new NotImplementedException();

		public override T Execute<T>(DateTime now, CustomSettings.RecordSet recordSet)
		{
			var result = new WithdrawalTransaction(++authorizationNumber, TransactionStatus.APPROVED, Entity, PaymentMethod, CurrencyIsoCodes);
			return (T)Convert.ChangeType(result, typeof(T));
		}

		public override Task<T> ExecuteAsync<T>(DateTime now, CustomSettings.RecordSet recordSet)
		{
			throw new NotImplementedException();
		}

		public override void Prepare(DateTime now)
		{
			//CustomSettings.Prepare();
		}
	}

	public class TestDepositUSD2 : ProcessorDriver
	{
		private int authorizationNumber = 0;
		public TestDepositUSD2()
			: base("TEST", PaymentMethod.Creditcard, "USD", TransactionType.Deposit, 1)
		{
		}

		public override string Description => throw new NotImplementedException();
		public override string Fabricator => throw new NotImplementedException();

		public override DateTime ReleaseDate => throw new NotImplementedException();

		public override T Execute<T>(DateTime now, CustomSettings.RecordSet recordSet)
		{
			var result = new DepositTransaction(++authorizationNumber, TransactionStatus.APPROVED, Entity, PaymentMethod, CurrencyIsoCodes);
			return (T)Convert.ChangeType(result, typeof(T));
		}

		public override Task<T> ExecuteAsync<T>(DateTime now, CustomSettings.RecordSet recordSet)
		{
			throw new NotImplementedException();
		}

		public override void Prepare(DateTime now)
		{
			//CustomSettings.Prepare();
		}
	}

	public class TestDepositUSD00 : ProcessorDriver
	{
		private int authorizationNumber = 0;
		public TestDepositUSD00()
			: base("TEST0", PaymentMethod.Creditcard, "USD", TransactionType.Deposit, 1)
		{
		}

		public override string Description => throw new NotImplementedException();
		public override string Fabricator => throw new NotImplementedException();

		public override DateTime ReleaseDate => throw new NotImplementedException();

		public override T Execute<T>(DateTime now, CustomSettings.RecordSet recordSet)
		{
			var result = new DepositTransaction(++authorizationNumber, TransactionStatus.APPROVED, Entity, PaymentMethod, CurrencyIsoCodes);
			return (T)Convert.ChangeType(result, typeof(T));
		}

		public override Task<T> ExecuteAsync<T>(DateTime now, CustomSettings.RecordSet recordSet)
		{
			throw new NotImplementedException();
		}

		public override void Prepare(DateTime now)
		{
			//CustomSettings.Prepare();
		}
	}

	public class TestWithdrawalUSD00 : ProcessorDriver
	{
		private int authorizationNumber = 0;
		public TestWithdrawalUSD00()
			: base("TEST0", PaymentMethod.Creditcard, "USD", TransactionType.Withdrawal, 1)
		{
		}

		public override string Description => throw new NotImplementedException();
		public override string Fabricator => throw new NotImplementedException();

		public override DateTime ReleaseDate => throw new NotImplementedException();

		public override T Execute<T>(DateTime now, CustomSettings.RecordSet recordSet)
		{
			var result = new WithdrawalTransaction(++authorizationNumber, TransactionStatus.APPROVED, Entity, PaymentMethod, CurrencyIsoCodes);
			return (T)Convert.ChangeType(result, typeof(T));
		}

		public override Task<T> ExecuteAsync<T>(DateTime now, CustomSettings.RecordSet recordSet)
		{
			throw new NotImplementedException();
		}

		public override void Prepare(DateTime now)
		{
			//CustomSettings.Prepare();
		}
	}

	public class TestDepositUSD01 : ProcessorDriver
	{
		private int authorizationNumber = 0;
		public TestDepositUSD01()
			: base("TEST1", PaymentMethod.Creditcard, "USD", TransactionType.Deposit, 1)
		{
		}

		public override string Description => throw new NotImplementedException();
		public override string Fabricator => throw new NotImplementedException();

		public override DateTime ReleaseDate => throw new NotImplementedException();

		public override T Execute<T>(DateTime now, CustomSettings.RecordSet recordSet)
		{
			var result = new DepositTransaction(++authorizationNumber, TransactionStatus.APPROVED, Entity, PaymentMethod, CurrencyIsoCodes);
			return (T)Convert.ChangeType(result, typeof(T));
		}

		public override Task<T> ExecuteAsync<T>(DateTime now, CustomSettings.RecordSet recordSet)
		{
			throw new NotImplementedException();
		}

		public override void Prepare(DateTime now)
		{
			//CustomSettings.Prepare();
		}
	}

	public class TestWithdrawalUSD01 : ProcessorDriver
	{
		private int authorizationNumber = 0;
		public TestWithdrawalUSD01()
			: base("TEST1", PaymentMethod.Creditcard, "USD", TransactionType.Withdrawal, 1)
		{
		}

		public override string Description => throw new NotImplementedException();
		public override string Fabricator => throw new NotImplementedException();

		public override DateTime ReleaseDate => throw new NotImplementedException();

		public override T Execute<T>(DateTime now, CustomSettings.RecordSet recordSet)
		{
			var result = new WithdrawalTransaction(++authorizationNumber, TransactionStatus.APPROVED, Entity, PaymentMethod, CurrencyIsoCodes);
			return (T)Convert.ChangeType(result, typeof(T));
		}

		public override Task<T> ExecuteAsync<T>(DateTime now, CustomSettings.RecordSet recordSet)
		{
			throw new NotImplementedException();
		}

		public override void Prepare(DateTime now)
		{
			//CustomSettings.Prepare();
		}
	}

	[TestClass]
	public class GuardianTest
	{
		static bool loaded = false;
		public static bool LoadProcessors(Company company)
		{
			CatalogMember entity;
			if (!company.System.Entities.Exists("TEST"))
			{
				entity = company.System.Entities.Add(5, "TEST");
			}
			else
			{
				entity = company.System.Entities.Find(5);
			}
			entity.Visible = true;
			entity.Enabled = true;

			if (!company.System.Entities.Exists("TEST0"))
			{
				entity = company.System.Entities.Add(6, "TEST0");
			}
			else
			{
				entity = company.System.Entities.Find(6);
			}
			entity.Visible = true;
			entity.Enabled = true;
			if (!company.System.Entities.Exists("TEST1"))
			{
				entity = company.System.Entities.Add(7, "TEST1");
			}
			else
			{
				entity = company.System.Entities.Find(7);
			}
			entity.Visible = true;
			entity.Enabled = true;

			CatalogMember pm;
			if (!company.System.PaymentMethods.Exists($"{PaymentMethod.Cash}"))
			{
				pm = company.System.PaymentMethods.Add(9, $"{PaymentMethod.Cash}");
			}
			else
			{
				pm = company.System.PaymentMethods.Find(9);
			}
			pm.Visible = true;
			pm.Enabled = true;
			var processorPaymentMethod = (ProcessorPaymentMethod)pm;
			processorPaymentMethod.AddProvider(1, "provider1");

			if (!company.System.PaymentMethods.Exists($"{PaymentMethod.Bank}"))
			{
				pm = company.System.PaymentMethods.Add(10, $"{PaymentMethod.Bank}");
			}
			else
			{
				pm = company.System.PaymentMethods.Find(10);
			}
			pm.Visible = true;
			pm.Enabled = true;
			if (!company.System.PaymentMethods.Exists($"{PaymentMethod.Creditcard}"))
			{
				pm = company.System.PaymentMethods.Add(11, $"{PaymentMethod.Creditcard}");
			}
			else
			{
				pm = company.System.PaymentMethods.Find(11);
			}
			pm.Visible = true;
			pm.Enabled = true;
			processorPaymentMethod = (ProcessorPaymentMethod)pm;
			processorPaymentMethod.AddProvider(1, "provider1");
			if (!company.System.PaymentMethods.Exists($"{PaymentMethod.FinancialServices}"))
			{
				pm = company.System.PaymentMethods.Add(12, $"{PaymentMethod.FinancialServices}");
			}
			else
			{
				pm = company.System.PaymentMethods.Find(12);
			}
			pm.Visible = true;
			pm.Enabled = true;
			if (!company.System.PaymentMethods.Exists($"{PaymentMethod.ThirdParty}"))
			{
				pm = company.System.PaymentMethods.Add(13, $"{PaymentMethod.ThirdParty}");
			}
			else
			{
				pm = company.System.PaymentMethods.Find(13);
			}
			pm.Visible = true;
			pm.Enabled = true;
			if (!company.System.PaymentMethods.Exists($"{PaymentMethod.Secrets}"))
			{
				pm = company.System.PaymentMethods.Add(14, $"{PaymentMethod.Secrets}");
			}
			else
			{
				pm = company.System.PaymentMethods.Find(14);
			}
			pm.Visible = true;
			pm.Enabled = true;

			CatalogMember type;
			if (!company.System.TransactionTypes.Exists($"{TransactionType.Deposit}"))
			{
				type = company.System.TransactionTypes.Add(1, $"{TransactionType.Deposit}");
			}
			else
			{
				type = company.System.TransactionTypes.Find(1);
			}
			type.Visible = true;
			type.Enabled = true;

			if (!company.System.TransactionTypes.Exists($"{TransactionType.Withdrawal}"))
			{
				type = company.System.TransactionTypes.Add(2, $"{TransactionType.Withdrawal}");
			}
			else
			{
				type = company.System.TransactionTypes.Find(2);
			}
			type.Visible = true;
			type.Enabled = true;

			var coin = Coinage.Coin("LR");
			CatalogMember processorCoin;
			if (!company.System.Coins.ExistsIsoCode("LR"))
			{
				processorCoin = company.System.Coins.Add(coin);
			}
			else
			{
				processorCoin = company.System.Coins.Find(coin.Id);
			}
			processorCoin.Visible = true;
			processorCoin.Enabled = true;

			coin = Coinage.Coin("USD");
			if (!company.System.Coins.ExistsIsoCode("USD"))
			{
				processorCoin = company.System.Coins.Add(coin);
			}
			else
			{
				processorCoin = company.System.Coins.Find(coin.Id);
			}
			processorCoin.Visible = true;
			processorCoin.Enabled = true;

			coin = Coinage.Coin("BTC");
			if (!company.System.Coins.ExistsIsoCode("BTC"))
			{
				processorCoin = company.System.Coins.Add(coin);
			}
			else
			{
				processorCoin = company.System.Coins.Find(coin.Id);
			}
			processorCoin.Visible = true;
			processorCoin.Enabled = true;

			var customSettings = new CustomSettingsCollection(company);
			var cs = customSettings.AddFixedParameter(DateTime.Now, "CashierDriver.url", "http://cashierapi:5000/");
			cs = customSettings.AddFixedParameter(DateTime.Now, "CompanyBaseUrlServices", "http://cashierapi:5000/");
			cs = customSettings.AddFixedParameter(DateTime.Now, "CompanySystemId", "N/A");
			cs = customSettings.AddSecretParameter(DateTime.Now, "CompanySystemPassword", "N/A");
			cs = customSettings.AddFixedParameter(DateTime.Now, "CompanyClerkId", "Administrator");
			if (loaded)
			{
				company.System.PaymentProcessor.Reload();
				return true;
			}
			else
			{
				//loaded = true;
				var success = company.System.PaymentProcessor.Load(false, DateTime.Now, customSettings);
				return success;
			}

		}

		internal static void Init(DateTime today, out Company company, out Guardian guardian, out QueueInMemory queue)
		{
			company = new Company();
			if (!company.Sales.HasStore(1)) company.Sales.CreateStore(1, "Test Store").MakeCurrent();
			guardian = new Guardian(company);
			bool itsThePresent = true;
			LoadProcessors(company);

			#region Database creation
			Db bd = new Db();
			Integration.Db = bd;
			Transactions transactions = Transactions.GetInstance();
			transactions.CreateSchema();
			#endregion

			#region Kafka in memory
			queue = new QueueInMemory();
			Integration.Kafka = queue;
			Integration.SavePreviousSettings(tempUseKafka: true, tempUseKafkaForAuto: true);
			//Integration.UseKafka = true;
			//Integration.UseKafkaForAuto = false;
			#endregion

			Profiles profiles = guardian.Profiles();
			int profileId = profiles.NextProfileId();
			Profile generalManagerProfile = profiles.CreateProfile(itsThePresent, profileId, "General Manager");

			profileId = profiles.NextProfileId();
			Profile financialManagerProfile = profiles.CreateProfile(itsThePresent, profileId, "Financial Manager");

			profileId = profiles.NextProfileId();
			Profile ownerProfile = profiles.CreateOwnerProfile(itsThePresent, profileId, "Owner");

			profileId = profiles.NextProfileId();
			Profile agentProfile = profiles.CreateProfile(itsThePresent, profileId, "Agent/Cashier");

			#region Users to profiles
			Profile profile = guardian.Profiles().SearchByName("Agent/Cashier");
			int approverId = guardian.Approvers().NextApproverId();
			Approver approver = guardian.Approvers().Create(itsThePresent, approverId, "<EMAIL>");
			profile.Add(approver);

			profile = guardian.Profiles().SearchByName("General Manager");
			approverId = guardian.Approvers().NextApproverId();
			approver = guardian.Approvers().Create(itsThePresent, approverId, "<EMAIL>");
			profile.Add(approver);

			profile = guardian.Profiles().SearchByName("Financial Manager");
			approverId = guardian.Approvers().NextApproverId();
			approver = guardian.Approvers().Create(itsThePresent, approverId, "<EMAIL>");
			profile.Add(approver);

			profile = guardian.Profiles().SearchByName("Owner");
			approverId = guardian.Approvers().NextApproverId();
			approver = guardian.Approvers().Create(itsThePresent, approverId, "<EMAIL>");
			profile.Add(approver);
			#endregion

			#region Exchange Accounts creation
			AccountNumbers accounts = guardian.Accounts();

			int accountId = accounts.NextAccountId();
			accounts.Create(itsThePresent, accountId, "3770-4512-5467-6352", "3770-4512-5467-6352", Coinage.Coin(Currencies.CODES.USD));

			accountId = accounts.NextAccountId();
			accounts.Create(itsThePresent, accountId, "3770-4512-5467-6353", "3770-4512-5467-6353", Coinage.Coin(Currencies.CODES.USD));

			accountId = accounts.NextAccountId();
			accounts.Create(itsThePresent, accountId, "6770-4512-5467-6353", "6770-4512-5467-6353", Coinage.Coin(Currencies.CODES.USD));

			accountId = accounts.NextAccountId();
			accounts.Create(itsThePresent, accountId, "3d1b1b52e955530b935cbe260d1f63c26edf9f8748b4a2550ff23005710c803a", "3d1b1b52e955530b935cbe260d1f63c26edf9f8748b4a2550ff23005710c803a", Coinage.Coin(Currencies.CODES.BTC));

			accountId = accounts.NextAccountId();
			accounts.Create(itsThePresent, accountId, "a09532f743460dfe56812c174fd73c5d2286f7ee6b4cac9d4d869853d64d0a47", "a09532f743460dfe56812c174fd73c5d2286f7ee6b4cac9d4d869853d64d0a47", Coinage.Coin(Currencies.CODES.BTC));

			accountId = accounts.NextAccountId();
			accounts.Create(itsThePresent, accountId, "8b6ad54777c363e1e41b33ce6076341491fdd774fa198a4a142831408d62dbf7", "8b6ad54777c363e1e41b33ce6076341491fdd774fa198a4a142831408d62dbf7", Coinage.Coin(Currencies.CODES.BTC));

            foreach (var processor in guardian.PaymentProcessorsWithoutAccounts())
            {
				accountId = accounts.NextAccountId();
				accounts.Create(false, accountId, processor.ProcessorKey, processor.ProcessorKey, processor.Coin);
			}
			#endregion

			#region Customer Types
			var riskRatingTypes = company.RiskRatingTypes();
			var riskRatingType = riskRatingTypes.ExistsName("Demo") ? riskRatingTypes.FindRiskRatingType("Demo") : riskRatingTypes.NewRiskRatingType("Demo");
			riskRatingType.Description = "Demo account type allows the user to goes to all application workflow in production mode with the only exception that disbursements will not apply.";
			riskRatingType = riskRatingTypes.ExistsName("Standard") ? riskRatingTypes.FindRiskRatingType("Standard") : riskRatingTypes.NewRiskRatingType("Standard");
			riskRatingType.Description = "Standard account type will be the type by default to all new customers added in the platform.";
			#endregion

			#region Risk Ratings
			riskRatingType = riskRatingTypes.FindRiskRatingType("Standard");

			RiskRating riskRating = guardian.RiskRating();

			var transactionTypesAsText = RiskAssignmentModel.TransactionTypes();

			AmountRanges ranges = new AmountRanges();
			AmountRangeByCurrencies amountRangeByCurrencies = ranges.CreateAmountRangeByCurrencies(riskRating, transactionTypesAsText);
			amountRangeByCurrencies.SetAmountRange(new Dollar(800), new Dollar(0.5m), today, "N/A");
			amountRangeByCurrencies.SetAmountRange(new Btc(0.6m), new Btc(0.00000523m), today, "N/A");
			amountRangeByCurrencies.SetAmountRange(new Eth(0.6m), new Eth(0.00004217m), today, "N/A");

			profiles = guardian.Profiles();
			Profiles profilesSelected = new Profiles();
			profilesSelected.Add(profiles.SearchById(1));
			profilesSelected.Add(profiles.SearchById(2));

			RiskAssignments riskAssignments = guardian.RiskAssignments();
			riskAssignments.StartupRiskAssignment(riskRatingType);
			RiskAssignment riskAssignment = riskAssignments.NewRiskAssignment(riskRatingType, amountRangeByCurrencies, profilesSelected);
			#endregion
		}

		internal class UT_USD_Driver : ASIProcessorDriver
		{
			private const float VERSION = 1.0F;
			private int authorization = 0;

			public UT_USD_Driver()
			  : base(TransactionType.Sale, VERSION)
			{
			}
			public override async Task<T> ExecuteAsync<T>(DateTime now, CustomSettings.RecordSet recordSet)
			{
				await Task.Yield();
				AuthorizationTransaction auth = new AuthorizationTransaction(++authorization, TransactionStatus.APPROVED, Entity, PaymentMethod, CurrencyIsoCodes);
				return (T)Convert.ChangeType(auth, typeof(T));
			}

			public override T Execute<T>(DateTime now, CustomSettings.RecordSet recordSet)
			{

				AuthorizationTransaction auth = new AuthorizationTransaction(++authorization, TransactionStatus.APPROVED, Entity, PaymentMethod, CurrencyIsoCodes);
				return (T)Convert.ChangeType(auth, typeof(T));
			}

			public override void Prepare(DateTime now)
			{
				throw new NotImplementedException();
			}
		}
		internal class UT_BTC_Driver : ASIProcessorDriver
		{
			private const float VERSION = 1.0F;
			private int authorization = 0;
			public UT_BTC_Driver()
			  : base(TransactionType.Sale, VERSION)
			{
			}
			public override async Task<T> ExecuteAsync<T>(DateTime now, CustomSettings.RecordSet recordSet)
			{
				await Task.Yield();
				AuthorizationTransaction auth = new AuthorizationTransaction(++authorization, TransactionStatus.APPROVED, Entity, PaymentMethod, CurrencyIsoCodes);
				return (T)Convert.ChangeType(auth, typeof(T));
			}

			public override T Execute<T>(DateTime now, CustomSettings.RecordSet recordSet)
			{

				AuthorizationTransaction auth = new AuthorizationTransaction(++authorization, TransactionStatus.APPROVED, Entity, PaymentMethod, CurrencyIsoCodes);
				return (T)Convert.ChangeType(auth, typeof(T));
			}

			public override void Prepare(DateTime now)
			{
				throw new NotImplementedException();
			}
		}

		internal static void ValidateAccountsCreations(QueueInMemory queue)
		{
			string msn = queue.Dequeue(Integration.Kafka.TopicForGuardianInternalOperations);
			ProfileCreationMessage profileCreationMessage = (ProfileCreationMessage)InternalOperationMessage.Factory(msn);
			Assert.AreEqual("General Manager", profileCreationMessage.Name);

			msn = queue.Dequeue(Integration.Kafka.TopicForGuardianInternalOperations);
			profileCreationMessage = (ProfileCreationMessage)InternalOperationMessage.Factory(msn);
			Assert.AreEqual("Financial Manager", profileCreationMessage.Name);

			msn = queue.Dequeue(Integration.Kafka.TopicForGuardianInternalOperations);
			profileCreationMessage = (ProfileCreationMessage)InternalOperationMessage.Factory(msn);
			Assert.AreEqual("Owner", profileCreationMessage.Name);

			msn = queue.Dequeue(Integration.Kafka.TopicForGuardianInternalOperations);
			profileCreationMessage = (ProfileCreationMessage)InternalOperationMessage.Factory(msn);
			Assert.AreEqual("Agent/Cashier", profileCreationMessage.Name);

			msn = queue.Dequeue(Integration.Kafka.TopicForGuardianInternalOperations);
			ApproverCreationMessage approverCreationMessage = (ApproverCreationMessage)InternalOperationMessage.Factory(msn);
			Assert.AreEqual("<EMAIL>", approverCreationMessage.Email);

			msn = queue.Dequeue(Integration.Kafka.TopicForGuardianInternalOperations);
			approverCreationMessage = (ApproverCreationMessage)InternalOperationMessage.Factory(msn);
			Assert.AreEqual("<EMAIL>", approverCreationMessage.Email);

			msn = queue.Dequeue(Integration.Kafka.TopicForGuardianInternalOperations);
			approverCreationMessage = (ApproverCreationMessage)InternalOperationMessage.Factory(msn);
			Assert.AreEqual("<EMAIL>", approverCreationMessage.Email);

			msn = queue.Dequeue(Integration.Kafka.TopicForGuardianInternalOperations);
			approverCreationMessage = (ApproverCreationMessage)InternalOperationMessage.Factory(msn);
			Assert.AreEqual("<EMAIL>", approverCreationMessage.Email);

			msn = queue.Dequeue(Integration.Kafka.TopicForGuardianInternalOperations);
			var accountCreationMessage = (AccountNumberCreationMessage)InternalOperationMessage.Factory(msn);
			Assert.AreEqual("3770-4512-5467-6352", accountCreationMessage.Number);

			msn = queue.Dequeue(Integration.Kafka.TopicForGuardianInternalOperations);
			accountCreationMessage = (AccountNumberCreationMessage)InternalOperationMessage.Factory(msn);
			Assert.AreEqual("3770-4512-5467-6353", accountCreationMessage.Number);

			msn = queue.Dequeue(Integration.Kafka.TopicForGuardianInternalOperations);
			accountCreationMessage = (AccountNumberCreationMessage)InternalOperationMessage.Factory(msn);
			Assert.AreEqual("6770-4512-5467-6353", accountCreationMessage.Number);

			msn = queue.Dequeue(Integration.Kafka.TopicForGuardianInternalOperations);
			accountCreationMessage = (AccountNumberCreationMessage)InternalOperationMessage.Factory(msn);
			Assert.AreEqual("3d1b1b52e955530b935cbe260d1f63c26edf9f8748b4a2550ff23005710c803a", accountCreationMessage.Number);

			msn = queue.Dequeue(Integration.Kafka.TopicForGuardianInternalOperations);
			accountCreationMessage = (AccountNumberCreationMessage)InternalOperationMessage.Factory(msn);
			Assert.AreEqual("a09532f743460dfe56812c174fd73c5d2286f7ee6b4cac9d4d869853d64d0a47", accountCreationMessage.Number);

			msn = queue.Dequeue(Integration.Kafka.TopicForGuardianInternalOperations);
			accountCreationMessage = (AccountNumberCreationMessage)InternalOperationMessage.Factory(msn);
			Assert.AreEqual("8b6ad54777c363e1e41b33ce6076341491fdd774fa198a4a142831408d62dbf7", accountCreationMessage.Number);
		}
		[TestMethod]
		public async Task WithDrawalAsync()
		{
			CurrenciesTest.AddCurrencies();
			Company cia;
			Guardian guardian;
			QueueInMemory queue;
			DateTime today = DateTime.Now;
			GuardianTest.Init(today, out cia, out guardian, out queue);

			Db bd = new Db();
			Integration.Db = bd;
			Transactions transactions = Transactions.GetInstance();
			transactions.CreateSchema();

			queue = new QueueInMemory();
			Integration.Kafka = queue;
			//DateTime today = DateTime.Now;
			bool itIsThePresent = true;

			Company company = new Company();
			CurrenciesTest.AddCurrencies();
			company.Sales.CreateStore(1, "Test Store").MakeCurrent();
			GuardianTest.LoadProcessors(company);

			//Guardian guardian = new Guardian(company);
			PaymentProcessorsAndActionsByDomains processsors = guardian.PaymentProcessors();

			PaymentProcessor processsor = processsors.SearchById("TEST-Secrets-TestDepositBTC-BTC-1");

			string domainUrl = "localhost";
			int domainId = 1;

			Operations operations = guardian.Operations();
			PendingWithDrawal withdrawal = operations.CreateWithdrawal(itIsThePresent, DateTime.Now, 1, 1, "5D2428442", new Dollar(4), processsor.Group.Id, processsor, domainId, domainUrl, "Bart");

			string msn = queue.Dequeue(Integration.Kafka.TopicForGuardianInternalOperations);
			InternalOperationUpdateMessage withdrawalFromKafka = (InternalOperationUpdateMessage)InternalOperationMessage.Factory(msn);
			Assert.AreEqual(withdrawal.Description, withdrawalFromKafka.Description);
			Assert.AreEqual(withdrawal.DisbursementAmount.Value, withdrawalFromKafka.DisbursementAmount.Value);
			Assert.AreEqual(withdrawal.DisbursementAmount.CurrencyCode, withdrawalFromKafka.DisbursementAmount.CurrencyCode);
			Assert.AreEqual(withdrawal.Group, withdrawalFromKafka.Group);
			Assert.AreEqual(withdrawal.Processor.Driver.Id, withdrawalFromKafka.ProcessorId);
			Assert.AreEqual(withdrawal.TransactionId, withdrawalFromKafka.TransactionId);

			transactions.Save(withdrawalFromKafka);

			IEnumerable<StoredOperation> storedPendingOperation = await transactions.ListPendingOperationsAsync(1, OperationSchedule.Both, today.AddDays(-10), today.AddDays(10), 100, 0);
			Assert.AreEqual(true, storedPendingOperation.Count() > 0);
			//GamesEngine.Custodian.PaymentMethodsActions.ResetInstance();
		}
		public const string UNIQUE_ACCOUNT_NUMBER = "3770-4512-5467-6352";
		[TestMethod]
		public async Task DepositAsync()
		{
			CurrenciesTest.AddCurrencies();
			Company cia;
			Guardian guardian;
			QueueInMemory queue;
			DateTime today = DateTime.Now;
			GuardianTest.Init(today, out cia, out guardian, out queue);

			Db bd = new Db();
			Integration.Db = bd;
			Transactions transactions = Transactions.GetInstance();
			transactions.CreateSchema();

			queue = new QueueInMemory();
			Integration.Kafka = queue;
			//DateTime today = DateTime.Now;
			bool itIsThePresent = true;

			Company company = new Company();
			CurrenciesTest.AddCurrencies();
			company.Sales.CreateStore(1, "Test Store").MakeCurrent();
			GuardianTest.LoadProcessors(company);

			//Guardian guardian = new Guardian(company);
			PaymentProcessorsAndActionsByDomains processsors = guardian.PaymentProcessors();

			PaymentProcessor processsor = processsors.SearchById("TEST-Secrets-TestDepositBTC-BTC-1");

			string domainUrl = "localhost";
			int domainId = 1;

			Operations operations = guardian.Operations();
			PendingDeposit pendingDeposit = operations.CreateDeposit(itIsThePresent, DateTime.Now, 1, 1, "5D2428442", UNIQUE_ACCOUNT_NUMBER, new Dollar(4), "Description", processsor.Group.Id, processsor, domainId, domainUrl);
			int storeId = 4;
			CompleteDeposit completeDeposit = pendingDeposit.Execute(itIsThePresent, DateTime.Now, TransactionStatus.DENIED, "Error test", "cris", storeId);

			string msn = queue.Dequeue(Integration.Kafka.TopicForGuardianInternalOperations);
			InternalOperationUpdateMessage depositFromKafka = (InternalOperationUpdateMessage)InternalOperationMessage.Factory(msn);
			Assert.AreEqual(pendingDeposit.Description, depositFromKafka.Description);
			Assert.AreEqual(pendingDeposit.Amount.Value, depositFromKafka.DisbursementAmount.Value);
			Assert.AreEqual(pendingDeposit.Amount.CurrencyCode, depositFromKafka.DisbursementAmount.CurrencyCode);
			Assert.AreEqual(pendingDeposit.Group, depositFromKafka.Group);
			Assert.AreEqual(pendingDeposit.Processor.Driver.Id, depositFromKafka.ProcessorId);
			Assert.AreEqual(pendingDeposit.TransactionId, depositFromKafka.TransactionId);

			transactions.Save(depositFromKafka);

			IEnumerable<StoredOperation> storedPendingOperation = await transactions.ListPendingOperationsAsync(1, OperationSchedule.Both, today.AddDays(-10), today.AddDays(10), 100, 0);
			Assert.AreEqual(true, storedPendingOperation.Count() > 0);

			string msg = queue.Dequeue(Integration.Kafka.TopicForWithdrawalsDisbursementExecution);

			string[] messages = KafkaMessages.Split(msg);

			DisbursementExecutionMessage message3 = new DisbursementExecutionMessage(messages[0]);
			Assert.AreEqual(4, message3.Amount);
			Assert.AreEqual(new Dollar(4).CurrencyCode, message3.CurrencyCode.Iso4217Code);
			Assert.AreEqual(1, message3.AccountNumberId);
			Assert.AreEqual(1, message3.TransactionId);
			Assert.AreEqual(1, message3.Id);
			Assert.AreEqual(1, message3.AccountNumberId);
			Assert.AreEqual(new Dollar(4).CurrencyCode, message3.AccountCurrencyCode.Iso4217Code);
			//GamesEngine.Custodian.PaymentMethodsActions.ResetInstance();
		}

		[TestMethod]
		public void PaymentProcessorsWithoutAccounts()
		{
			var queue = new QueueInMemory();
			Integration.Kafka = queue;

			Company company = new Company();
			CurrenciesTest.AddCurrencies();
			company.Sales.CreateStore(1, "Test Store").MakeCurrent();
			GuardianTest.LoadProcessors(company);

			Guardian guardian = new Guardian(company);
			var processors = guardian.PaymentProcessorsWithoutAccounts();
			Assert.AreEqual(9, processors.Count());

			var accounts = guardian.Accounts();
			var accountId = accounts.NextAccountId();
			foreach (var processorWithoutAccounts in processors)
			{
				accounts.Create(false, accountId, processorWithoutAccounts.ProcessorKey, processorWithoutAccounts.ProcessorKey, processorWithoutAccounts.Coin);
				accountId++;
			}

			processors = guardian.PaymentProcessorsWithoutAccounts();
			Assert.AreEqual(0, processors.Count());
		}
	}
}
