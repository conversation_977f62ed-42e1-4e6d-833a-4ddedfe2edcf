﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using System.Threading.Tasks;

namespace LinesAPI.Controllers
{
    public class StoreController : AuthorizeController
    {
        [HttpGet("api/store/preferences/activations")]
        [Authorize(Roles = "devops")]//VERIFY_ROLES
        public async Task<IActionResult> StorePreferencesByDomainAsync(int domainId)
        {
            if (domainId <= 0) return BadRequest($"{nameof(domainId)} must be greater than 0");

            var result = await LinesAPI.Lines.PerformQryAsync(HttpContext, $@"
            {{
                domain = company.Sales.DomainFrom({domainId});
                storePreferences = company.Sales.CurrentStore.Preferences;
                for (preferences:storePreferences.AvailablePreferences)
                {{
                    print preferences.Id id;
                    print preferences.Name name;
                    print storePreferencess.IsEnabled(preferences, domain) enabled;
                }}
            }}
            ");
            return result;
        }
       
        [HttpGet("api/store/preferences/activations/ingame")]
        [Authorize(Roles = "devops")]//VERIFY_ROLES
        public async Task<IActionResult> StorePreferencesInGameAsync()
        {
            string domainUrl = HttpContext.Request.Host.Host;

            var result = await LinesAPI.Lines.PerformQryAsync(HttpContext, $@"
            {{
                domain = company.Sales.DomainFrom('{domainUrl}');
                storePreferences = company.Sales.CurrentStore.Preferences;
                for (preferences:storePreferences.AvailablePreferences)
                {{
                    print preferences.Id id;
                    print preferences.Name name;
                    print storePreferences.IsEnabled(preferences, domain) enabled;
                }}
            }}
            ");
            return result;
        }

        [HttpPost("api/store/preferences/activation")]
        [Authorize(Roles = "l64")]
        public async Task<IActionResult> EnablePreferenceAsync([FromBody] PreferenceActivationBody body)
        {
            if (body == null) return BadRequest("Body is required");
            if (body.DomainId <= 0) return BadRequest($"{nameof(body.DomainId)} must be greater than 0");
            if (body.PreferenceId <= 0) return BadRequest($"{nameof(body.PreferenceId)} must be greater than 0");

            string employeeName = Security.UserName(HttpContext);
            string msg = "Enabled";
            var result = await LinesAPI.Lines.PerformCmdAsync(HttpContext, $@"
            {{
                domain = company.Sales.DomainFrom({body.DomainId});
                preferences = company.Sales.CurrentStore.Preferences;
                preference = preferences.FindPreference({body.PreferenceId});
                activation = preferences.Enable(preference, domain);
                activation.AddAnnotation(domain.Id.ToString(), '{msg}', '{employeeName}', Now);
            }}
            ");
            return result;
        }

        [HttpPost("api/store/preferences/deactivation")]
        [Authorize(Roles = "l64")]
        public async Task<IActionResult> DisablePreferenceAsync([FromBody] PreferenceActivationBody body)
        {
            if (body == null) return BadRequest("Body is required");
            if (body.DomainId <= 0) return BadRequest($"{nameof(body.DomainId)} must be greater than 0");
            if (body.PreferenceId <= 0) return BadRequest($"{nameof(body.PreferenceId)} must be greater than 0");

            string employeeName = Security.UserName(HttpContext);
            string msg = "Disabled";
            var result = await LinesAPI.Lines.PerformCmdAsync(HttpContext, $@"
            {{
                domain = company.Sales.DomainFrom({body.DomainId});
                preferences = company.Sales.CurrentStore.Preferences;
                preference = preferences.FindPreference({body.PreferenceId});
                activation = preferences.Disable(preference, domain);
                activation.AddAnnotation(domain.Id.ToString(), '{msg}', '{employeeName}', Now);
            }}
            ");
            return result;
        }

        [DataContract(Name = "PreferenceActivationBody")]
        public class PreferenceActivationBody
        {
            [DataMember(Name = "domainId")]
            public int DomainId { get; set; }
            [DataMember(Name = "preferenceId")]
            public int PreferenceId { get; set; }
        }
    }
}
