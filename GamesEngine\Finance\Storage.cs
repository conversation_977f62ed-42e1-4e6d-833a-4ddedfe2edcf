﻿using GamesEngine.Business;
using GamesEngine.PurchaseOrders;
using GamesEngine.Settings;
using GamesEngine.Tools;
using MySql.Data.MySqlClient;
using Puppeteer.EventSourcing;
using Puppeteer.EventSourcing.Libraries;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using Utilities;
using static GamesEngine.Business.WholePaymentProcessor;
using static GamesEngine.Finance.Authorizations;
using static GamesEngine.Finance.LongTermControllerUtils;

namespace GamesEngine.Finance
{
	public abstract class MovementStorage
	{
		protected readonly string connectionString;
		protected const int DOCUMENT_NUMBER_FOR_ALL = 0;
		public const string ATADDRESS_GENERAL = "general";

		internal abstract void CreateFragmentIfNotExists(string number);

		private const char PREFIX_IN_LOG = 'C';
		private const char PREFIX_IN_LOG_LOWERCASE = 'c';

		protected const int MAX_MONTHS_TO_KEEP_ARCHIVED_MOVEMENTS = 3;
		protected const int MAX_DAYS_TO_KEEP_ARCHIVED_FRAGMENTS = 40;

		public string ConnectionString { get { return connectionString; } }

		public static string GetTableName(string address, Coin suffix, bool needPrefix = true)
		{
			string name;
			if (needPrefix)
			{
				name = $"{PREFIX_IN_LOG}{address}_movements_{suffix.Iso4217Code.ToString()}";
			}
			else
			{
				name = $"{address}_movements_{suffix.Iso4217Code.ToString()}";
			}
			return name.ToLower().Trim();
		}
		public static void AddTableName(StringBuilder builder, string address, Currencies.CODES suffix)
		{
			builder.Append(PREFIX_IN_LOG_LOWERCASE).Append(address).Append("_movements_").Append(suffix.ToLowercaseString());
		}

		public static string GetTableName(string address, Coin suffix)
		{
			string name = $"{PREFIX_IN_LOG}{address}_movements_{suffix.Iso4217Code.ToString()}";
			return name.ToLower().Trim();
		}

		public static string GetTableName(string address, string suffix)
		{
			string name = $"{PREFIX_IN_LOG}{address}_movements_{suffix}";
			return name.ToLower().Trim();
		}
		internal void AddTableName(StringBuilder builder, Currencies.CODES suffix)
		{
			builder.Append("movements_").Append(suffix.ToLowercaseString());
		}

		internal string GetTableName(Coin suffix)
		{
			var name = $"movements_{suffix.Iso4217Code.ToString()}";
			return name.ToLower().Trim();
		}
		internal string GetTableName(string suffix)
		{
			var name = $"movements_{suffix}";
			return name.ToLower().Trim();
		}
		string GetFragmentsTableName(string atAddress)
		{
			var name = $"C{atAddress}_authfragments";
			return name.ToLower().Trim();
		}
		protected string ToFullDateTimeString(DateTime day)
		{
			return day.ToString("yyyy-MM-dd HH:mm:ss.fff");
		}

		protected string ToDateTimeString(DateTime day)
		{
			return day.ToString("yyyy-MM-dd HH:mm:ss");
		}

		protected string ToDateString(DateTime day)
		{
			return day.ToString("yyyy-MM-dd");
		}

		internal static bool ItsADebitMovement(Movement.type movementType)
		{
			switch (movementType)
			{
				case Movement.type.Debit:
					return true;
				case Movement.type.Credit:
					return false;
				case Movement.type.Lock:
					return true;
				case Movement.type.Unlock:
					return false;
				default:
					throw new GameEngineException($@"There is no implementation for type {movementType}");
			}
		}

		protected MovementStorage(string connectionString)
		{
			this.connectionString = connectionString;
		}

		public abstract int InsertUserIfNotExists(int storeId, string user);
		public abstract void CreateUserMovementsTableIfNotExists();

		internal abstract string CreateMovementsStorage(string tableName, Currencies.CODES currencyCode, bool isAddress = true);
		internal abstract MovementsFiltered ListMovement(int sourceNumber, string atddress, int storeId, Coin currencyCode, DateTime initDate, DateTime endDate, int whoId, int inicialIndex, int amountOfRows, int movementTypeId, string accountNumber, int paymentProcessorId);
		internal abstract MovementsFiltered ListMovement(int sourceNumber, string atddress, int storeId, Coin currencyCode, DateTime initDate, DateTime endDate, int whoId, int inicialIndex, int amountOfRows);
		internal abstract MovementsFiltered ListMovement(int inicialIndex, int amountOfRows, string atddress, int storeId, DateTime initDate, DateTime endDate, Coin currencyCod);
		internal abstract MovementsReport MovementsReportFilteredBy(string atAddress, Coin currencyCode, int storeId, DateTime initDate, DateTime endDate, int initialIndex, int amountOfRows, int documentNumber, int referenceNumber);
		internal abstract MovementDetailReport MovementDetailReportBy(AtAddress atAddress, int authorizationNumber, Coin currencyCode, int initialIndex, int amountOfRows);
		internal abstract MovementsReport MovementsFullDetailReportBy(AtAddress atAddress, Coin currencyCode, int storeId, DateTime initDate, DateTime endDate, int initialIndex, int amountOfRows);

		internal abstract string AtAddressFrom(int documentNumber);
		internal abstract IEnumerable<User> ListUsers(int storeId);

		internal abstract void SavePayedFragment(bool itIsThePresent, MovementsCollector movements);
		public abstract void SaveBalanceMovements(bool itIsThePresent, int whosId, IEnumerable<Movement> movements, DataTable movementTableByAccount, DataTable movementTableByCurrency);
		internal abstract Authorization RetrieveFragments(int authorizationNumber, AtAddress atAddress, Purpose purpose);

		public abstract void CreateAuthorizationSequenceIfNotExists();
		internal abstract void CreateFragmentReasonsIfNotExists();
		public abstract int NextAuthorizationNumber();

		public abstract IEnumerable<AuthorizationFragmentStruct> AuthFragmentsToArchived(string tableName);
		public abstract IEnumerable<MovementStruct> MovementsToArchived(string tableName, int max_months_to_archive);
		public abstract void MarkArchivedAuthFragmentsInDB(string tableName);
		public abstract void MarkArchivedMovements(string tableName, int max_months_to_archive);
		public abstract void DeleteAuthFragments(string atAddress);
		public abstract void DeleteMovements(string atAddress, Currencies.CODES currencyCode);
        public abstract IEnumerable<string> AllCashierAccounts();
        internal abstract bool NeedsToRemoveExpiredFragments(string aNameAuthFragments, DateTime now);
        internal abstract void RemoveExpiredFragments(string aNameAuthFragments, DateTime now);


        internal object fragmentsAndMovementsSemaphore = new object();

		protected abstract bool ExistsTable(string tableName);
		public bool ExistMovementsTableForThisAccountAndCurrency(string atAddress, Coin currencyCode)
        {
			var tableName = GetTableName(atAddress, currencyCode);
			return ExistsTable(tableName);
		}
		public void CreateMovementsTableIfNotExistsForThisAccountAndCurrency(string atAddress, Coin currencyCode)
		{
			bool exists = ExistMovementsTableForThisAccountAndCurrency(atAddress, currencyCode);
			if (!exists)
			{
				CreateMovementsTableForThisAccountAndCurrency(atAddress, currencyCode);
			}
		}
		protected abstract void CreateMovementsTableForThisAccountAndCurrency(string atAddress, Coin currencyCode);
		internal abstract string CreateMovementsStorage(string atAddress, Coin currencyCode);

		protected bool ExistMovementsTableForThisCurrency(string currencyCode)
        {
			var tableName = GetTableName(currencyCode);
			return ExistsTable(tableName);
		}
		public void CreateMovementsTableIfNotExistsForThisCurrency(string currencyCode)
		{
			bool exists = ExistMovementsTableForThisCurrency(currencyCode);
			if (!exists)
			{
				CreateMovementsTableForThisCurrency(currencyCode);
			}
		}
		protected abstract void CreateMovementsTableForThisCurrency(string currencyCode);
		internal abstract string CreateMovementsStorage(string currencyCode);

		protected bool ExistFragmentsTableForThisAccount(string atAddress)
        {
			var tableName = GetFragmentsTableName(atAddress);
			return ExistsTable(tableName);
		}
		public void CreateFragmentsTableIfNotExistsForThisAccount(string atAddress)
		{
			var atAddressNumber = atAddress.Trim().ToLower();
			bool exists = ExistFragmentsTableForThisAccount(atAddressNumber);
			if (!exists)
			{
				CreateFragmentsTableForThisAccount(atAddressNumber);
			}
		}
		protected abstract void CreateFragmentsTableForThisAccount(string atAddress);
		internal abstract string CreateFragmentsStorage(string atAddress);

		internal abstract string CreateFragmentsStorage(string atAddress, bool subfixRequired = true);
	}

	internal struct FragmentInfo
	{
		private readonly AtAddress atAddress;
		internal AtAddress AtAddress
		{
			get
			{
				return atAddress;
			}
		}

		private readonly string documentNumber;
		internal string DocumentNumber
		{
			get
			{
				return documentNumber;
			}
		}

		internal int DocumentNumberAsInt
		{
			get
			{
				return int.Parse(documentNumber);
			}
		}

		private readonly DateTime creationDate;
		internal DateTime CreationDate
		{
			get
			{
				return creationDate;
			}
		}

		private readonly decimal totalAmount;
		internal decimal TotalAmount
		{
			get
			{
				return totalAmount;
			}
		}

		private readonly decimal newBalance;
		internal decimal NewBalance
		{
			get
			{
				return newBalance;
			}
		}

		private readonly bool isPending;
		internal bool IsPending
		{
			get
			{
				return isPending;
			}
		}

		private readonly int? number;
		internal int? Number
		{
			get
			{
				return number;
			}
		}

		private readonly decimal? risk;
		internal decimal? Risk
		{
			get
			{
				return risk;
			}
		}

		private readonly decimal? toWin;
		internal decimal? ToWin
		{
			get
			{
				return toWin;
			}
		}

		private readonly decimal? adjustedWinAmount;
		internal decimal? AdjustedWinAmount
		{
			get
			{
				return adjustedWinAmount;
			}
		}

		private readonly decimal? adjustedLossAmount;
		internal decimal? AdjustedLossAmount
		{
			get
			{
				return adjustedLossAmount;
			}
		}

		private readonly FragmentReason reason;
		internal FragmentReason Reason
		{
			get
			{
				return reason;
			}
		}

		private readonly string description;
		internal string Description
		{
			get
			{
				return description;
			}
		}

		internal bool IsCompletelyPending
		{
			get
			{
				return number == null && risk == null && toWin == null && adjustedLossAmount == null && adjustedWinAmount == null;
			}
		}

		public FragmentInfo(AtAddress atAddress, string documentNumber, DateTime creationDate, decimal totalAmount, decimal newBalance, bool isPending, int? number, decimal? risk, decimal? toWin, decimal? adjustedWinAmount, decimal? adjustedLossAmount, FragmentReason reason, string description)
		{
			this.atAddress = atAddress;
			this.documentNumber = documentNumber;
			this.creationDate = creationDate;
			this.totalAmount = totalAmount;
			this.newBalance = newBalance;
			this.isPending = isPending;
			this.number = number;
			this.risk = risk;
			this.toWin = toWin;
			this.adjustedWinAmount = adjustedWinAmount;
			this.adjustedLossAmount = adjustedLossAmount;
			this.reason = reason;
			this.description = description;
		}
	}

	[Puppet]
	public sealed class MovementsFiltered : Objeto
	{
		private readonly List<Movement> movementsFiltered;
		internal IEnumerable<Movement> Movements
		{
			get
			{
				return movementsFiltered;
			}
		}

		internal int Count
		{
			get
			{
				return movementsFiltered.Count;
			}
		}

		private readonly int countFoundRows;
		internal int CountFoundRows
		{
			get
			{
				return countFoundRows;
			}
		}

		internal MovementsFiltered(List<Movement> movementsFiltered, int countFoundRows)
		{
			this.movementsFiltered = movementsFiltered;
			this.countFoundRows = countFoundRows;
		}
	}

	public class MovementStorageMySQL : MovementStorage
	{
		public MovementStorageMySQL(string connectionString) : base(connectionString)
		{
			CreateFragmentReasonsIfNotExists();
		}

		private void ExecuteCommand(string cmdText)
		{
			try
			{
				using (MySqlConnection connection = new MySqlConnection(connectionString))
				{
					try
					{
						connection.Open();
						using (MySqlCommand command = new MySqlCommand(cmdText, connection))
						{
							var dataReader = command.ExecuteReader();
							dataReader.Close();
						}
					}
					catch (Exception e)
					{
						Loggers.GetIntance().Db.Error($@"sql:{cmdText}", e);
						ErrorsSender.Send(e, cmdText);
						throw e;
					}
					finally
					{
						connection.Close();
					}
				}
			}
			catch (Exception e)
			{
				Loggers.GetIntance().Db.Error($@"sql:{cmdText}", e);
				ErrorsSender.Send(e, $@"sql:{cmdText}");
				throw;
			}
		}

		protected override bool ExistsTable(string tableName)
		{
			if (string.IsNullOrEmpty(tableName)) return false;

			bool exists = true;
			string sql = $"SELECT 1 FROM {tableName} LIMIT 1";
			using (MySqlConnection connection = new MySqlConnection(connectionString))
			{
				try
				{
					connection.Open();
					using (MySqlCommand command = new MySqlCommand(sql, connection))
					{
						var dataReader = command.ExecuteReader();
						dataReader.Close();
					}
				}
				catch
				{
					exists = false;
				}
				finally
				{
					connection.Close();
				}
			}
			return exists;
		}

        internal override bool NeedsToRemoveExpiredFragments(string name, DateTime now)
        {
            bool needsTrim = false;
            const string AUTHFRAGMENTSPOSTFIX = "_authfragments";
            var authFragmentsTable = "C" + name + AUTHFRAGMENTSPOSTFIX;

            string sql = $"SELECT 1 FROM {authFragmentsTable} NOLOCK WHERE USELESS < '{now.ToString("yyyy-MM-dd HH:mm:ss")}' LIMIT 1;";

            using (MySqlConnection connection = new MySqlConnection(connectionString))
            {
                try
                {
                    connection.Open();
                    using (MySqlCommand command = new MySqlCommand(sql.ToString(), connection))
                    using (MySqlDataReader reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            needsTrim = true;
                        }
                        reader.Close();
                    }
                }
                catch
                {
                }
                finally
                {
                    connection.Close();
                }
            }

            return needsTrim;
        }

        internal override void RemoveExpiredFragments(string name, DateTime now)
        {
            StringBuilder sql = new StringBuilder();
            const string POSTFIX = "_$OLD";
            const string AUTHFRAGMENTSPOSTFIX = "_authfragments";
            var authFragmentsTable = "C" + name + AUTHFRAGMENTSPOSTFIX;

            using (MySqlConnection connection = new MySqlConnection(connectionString))
            {
                try
                {
                    connection.Open();

                    /*ARMA EL SCRIPT EL RENAME*/
                    sql.Append("RENAME TABLE ");
                    sql.Append(authFragmentsTable);
                    sql.Append(" TO ");
                    sql.Append(authFragmentsTable);
                    sql.Append(POSTFIX);
                    sql.Append(";");

                    using (MySqlCommand command = new MySqlCommand(sql.ToString(), connection))
                    {
                        command.ExecuteNonQuery();
                    }

                    sql.Clear();

                    ExecuteCommand(CreateFragmentsStorageQuery(name, true));

                    /*ARMA EL SCRIPT DE CRATE TABLE, INDICES Y DATA*/
                    sql.Append("INSERT INTO ");
                    sql.Append(authFragmentsTable);
                    sql.Append("(AUTHORIZATION_ID, FRAGMENT_ID, STATUS, RISK, REFERENCE, DESCRIPTION, TO_WIN, ADJUSTED_WIN, ADJUSTED_LOSS, CURRENCY, REASON, ACCOUNT_NUMBER, EDIT_MODE, USELESS) ");
                    sql.Append(" SELECT AUTHORIZATION_ID, FRAGMENT_ID, STATUS, RISK, REFERENCE, DESCRIPTION, TO_WIN, ADJUSTED_WIN, ADJUSTED_LOSS, CURRENCY, REASON, ACCOUNT_NUMBER, EDIT_MODE, USELESS FROM ");
                    sql.Append(authFragmentsTable);
                    sql.Append(POSTFIX);
                    sql.Append($" WHERE USELESS >= '{now.ToString("yyyy-MM-dd HH:mm:ss")}'");
                    sql.Append(';');

                    /*ARMA EL SCRIPT DE DROP TABLE*/
                    sql.AppendLine();
                    sql.Append("DROP TABLE ");
                    sql.Append(authFragmentsTable);
                    sql.Append(POSTFIX);
                    sql.Append(';');

                    using (MySqlCommand command = new MySqlCommand(sql.ToString(), connection))
                    {
                        command.CommandTimeout = 200;
                        command.CommandType = CommandType.Text;
                        command.ExecuteNonQuery();
                    }
                }
                catch (SqlException e)
                {
                    Loggers.GetIntance().Db.Error($@"sql:{sql} type:{e.GetType()} error:{e.Message}", e);

                    throw new Exception("Error al escribir en SQLServer el Script en el Dairy: [" + sql + "]. " + e.Message);
                }
                finally
                {
                    connection.Close();
                    sql.Clear();
                }
            }
        }

        public override IEnumerable<AuthorizationFragmentStruct> AuthFragmentsToArchived(string tableName)
		{
			StringBuilder sqlString = new StringBuilder();
			List<AuthorizationFragmentStruct> allAuthorizationFragments = new List<AuthorizationFragmentStruct>();
			sqlString.Append($"SELECT AUTHORIZATION_ID, FRAGMENT_ID, STATUS, RISK, REFERENCE, DESCRIPTION, TO_WIN, ADJUSTED_WIN, ADJUSTED_LOSS, CURRENCY, REASON, EDIT_MODE, USELESS FROM {tableName} WHERE CURRENT_TIMESTAMP() >= USELESS;");

			lock (fragmentsAndMovementsSemaphore)
			{
				using (MySqlConnection connection = new MySqlConnection(connectionString))
				{
					try
					{
						connection.Open();
						using (MySqlCommand command = new MySqlCommand(sqlString.ToString(), connection))
						using (DbDataReader reader = command.ExecuteReader())
						{
							while (reader.Read())
							{
								AuthorizationFragmentStruct authorizationFragment = new AuthorizationFragmentStruct();
								authorizationFragment.AUTHORIZATION_ID = reader.GetInt32(0);
								authorizationFragment.FRAGMENT_ID = reader.GetInt32(1);
								authorizationFragment.STATUS = reader.GetInt32(2);
								authorizationFragment.RISK = reader.GetDecimal(3);
								authorizationFragment.REFERENCE = reader.GetString(4);
								authorizationFragment.DESCRIPTION = reader.GetString(5);
								authorizationFragment.TO_WIN = reader.GetDecimal(6);
								authorizationFragment.ADJUSTED_WIN = reader.GetDecimal(7);
								authorizationFragment.ADJUSTED_LOSS = reader.GetDecimal(8);
								authorizationFragment.CURRENCY = reader.GetInt32(9);
								authorizationFragment.REASON = reader.GetInt32(10);
								authorizationFragment.EDIT_MODE = reader.GetInt32(11);
								allAuthorizationFragments.Add(authorizationFragment);
							}
							reader.Close();
						}
					}
					finally
					{
						connection.Close();
					}
				}
			}

			return allAuthorizationFragments;
		}

        private int GetTotalMovementsCount(int sourceNumber, string atAddress, int storeId, Coin currencyCode, DateTime startDate, DateTime endDate, int whoId, int movementTypeId, string accountNumberToFilter, 
			int paymentProcessorId, string fromTable, List<MySqlParameter> parameters, string baseWhereClause, string movementTypeWhereClause)
        {
            string cte = $@"
            WITH SingleDocumentNumbers AS (
                SELECT m_cte.DOCUMENTNUMBER
                FROM {fromTable} m_cte
                GROUP BY m_cte.DOCUMENTNUMBER
                HAVING COUNT(*) = 1
            )
        ";

            string countSql = $@"{cte}
            SELECT COUNT(*)
            FROM {fromTable} m
            LEFT JOIN movements_users u ON u.ID = m.WHO
            LEFT JOIN SingleDocumentNumbers sdn ON m.DOCUMENTNUMBER = sdn.DOCUMENTNUMBER
            WHERE {baseWhereClause} AND {movementTypeWhereClause}";

            using (MySqlConnection connection = new MySqlConnection(connectionString))
            {
                connection.Open();
                using (MySqlCommand command = new MySqlCommand(countSql, connection))
                {
                    command.Parameters.AddRange(parameters.ToArray());

                    object result = command.ExecuteScalar();
                    if (result != null && result != DBNull.Value)
                    {
                        return Convert.ToInt32(result);
                    }
                }
            }
            return 0;
        }

        internal override MovementsFiltered ListMovement(int sourceNumber, string atAddress, int storeId, Coin currencyCode, DateTime startDate, DateTime endDate, int whoId, int initialIndex, int amountOfRows, 
			int movementTypeId, string accountNumberToFilter, int paymentProcessorId)
		{
			if (startDate.Minute != 0 || startDate.Second != 0) throw new GameEngineException($"{nameof(startDate)} {startDate} is not valid");
			if (endDate.Minute != 0 || endDate.Second != 0) throw new GameEngineException($"{nameof(endDate)} {endDate} is not valid");

            List<Movement> movements = new List<Movement>();
            int totalMovementsCount = 0;
            string sql = string.Empty;

            try
            {
                var parameters = new List<MySqlParameter>();
                var whereConditions = new StringBuilder();

                whereConditions.Append(" m.STORE = @storeId ");
                parameters.Add(new MySqlParameter("@storeId", storeId));
                DateTime effectiveEndDate = endDate.AddDays(1);

                whereConditions.Append(" AND m.DAY >= @startDate AND m.DAY < @effectiveEndDate ");
                parameters.Add(new MySqlParameter("@startDate", ToFullDateTimeString(startDate)));
                parameters.Add(new MySqlParameter("@effectiveEndDate", ToFullDateTimeString(effectiveEndDate)));

                if (whoId != Users.NO_USER)
                {
                    whereConditions.Append(" AND u.ID = @whoId ");
                    parameters.Add(new MySqlParameter("@whoId", whoId));
                }

                if (sourceNumber != Sources.NO_SOURCE_ID)
                {
                    whereConditions.Append(" AND m.SOURCE = @sourceNumber ");
                    parameters.Add(new MySqlParameter("@sourceNumber", sourceNumber));
                }

                if (!string.IsNullOrWhiteSpace(accountNumberToFilter))
                {
                    whereConditions.Append(" AND m.ACCOUNT_NUMBER = @accountNumberToFilter ");
                    parameters.Add(new MySqlParameter("@accountNumberToFilter", accountNumberToFilter));
                }

                if (paymentProcessorId != WholePaymentProcessor.NoPaymentProcessor)
                {
                    whereConditions.Append(" AND m.PROCESSORID = @paymentProcessorId ");
                    parameters.Add(new MySqlParameter("@paymentProcessorId", paymentProcessorId));
                }

                string baseWhereClause = whereConditions.ToString();
                string fromTable;
                string selectedAtAddressColumn;

                var isGeneral = atAddress == ATADDRESS_GENERAL; 
                if (isGeneral)
                {
                    fromTable = GetTableName(currencyCode);
                    selectedAtAddressColumn = "m.ATADDRESS"; 
                }
                else
                {
                    fromTable = GetTableName(atAddress, currencyCode);
                    selectedAtAddressColumn = "@atAddressParam AS ATADDRESS"; 
                    parameters.Add(new MySqlParameter("@atAddressParam", atAddress));
                }

                string movementTypeWhereClause;
                if (movementTypeId == Movement.ALL_MOVEMENT_TYPE)
                {
                    movementTypeWhereClause = $@"
                    (m.MOVEMENT IN ('{Movement.type.Credit}', '{Movement.type.Debit}')
                        OR (m.MOVEMENT = '{Movement.type.Lock}' AND sdn.DOCUMENTNUMBER IS NOT NULL)
                    )";
                }
                else
                {
                    Movement.type type = (Movement.type)movementTypeId;
                    movementTypeWhereClause = "m.MOVEMENT = @movementType";
                    parameters.Add(new MySqlParameter("@movementType", type.ToString()));
                }

                var countParameters = new List<MySqlParameter>();
                foreach (var p in parameters)
                {
                    countParameters.Add(new MySqlParameter(p.ParameterName, p.Value));
                }
                
                totalMovementsCount = GetTotalMovementsCount(sourceNumber, atAddress, storeId, currencyCode, startDate, endDate, whoId, movementTypeId, accountNumberToFilter, paymentProcessorId,
                                                            fromTable, countParameters, baseWhereClause, movementTypeWhereClause);

                string cteForDataQuery = $@"
                WITH SingleDocumentNumbers AS (
                    SELECT m_cte.DOCUMENTNUMBER
                    FROM {fromTable} m_cte
                    GROUP BY m_cte.DOCUMENTNUMBER
                    HAVING COUNT(*) = 1
                )
            ";

                sql = $@"{cteForDataQuery}
                SELECT m.DAY, m.SOURCE, m.MOVEMENT, m.AMOUNT, m.NEWBALANCE, m.DOCUMENTNUMBER,
                       u.NAME, m.STORE, IFNULL(m.CONCEPT, '') as CONCEPT, IFNULL(m.REFERENCE, '') as REFERENCE,
                       {selectedAtAddressColumn}, m.NEWLOCKBALANCE, m.ACCOUNT_NUMBER, m.PROCESSORID
                FROM {fromTable} m
                LEFT JOIN movements_users u ON u.ID = m.WHO
                LEFT JOIN SingleDocumentNumbers sdn ON m.DOCUMENTNUMBER = sdn.DOCUMENTNUMBER
                WHERE {baseWhereClause} AND {movementTypeWhereClause}
                LIMIT @amountOfRows OFFSET @initialIndex
            ";

                parameters.Add(new MySqlParameter("@amountOfRows", amountOfRows));
                parameters.Add(new MySqlParameter("@initialIndex", initialIndex));


                using (MySqlConnection connection = new MySqlConnection(connectionString))
                {
                    connection.Open();
                    using (MySqlCommand command = new MySqlCommand(sql, connection))
                    {
                        command.Parameters.AddRange(parameters.ToArray());

                        using (var dataReader = command.ExecuteReader())
                        {
                            int colIdx_AtAddress = 10;
                            int colIdx_NewLockBalance = 11;
                            int colIdx_AccountNumber = 12;
                            int colIdx_ProcessorId = 13;

                            while (dataReader.Read())
                            {
                                DateTime day = dataReader.GetDateTime(0);
                                int sourceAsInt = dataReader.GetByte(1); 
                                string movementStr = dataReader.GetString(2);
                                decimal amount = dataReader.GetDecimal(3);
                                decimal newBalance = dataReader.GetDecimal(4);
                                string documentNumber = dataReader.GetString(5);
                                string who = dataReader.IsDBNull(6) ? Users.None : dataReader.GetString(6);
                                // int currentStoreId = dataReader.GetByte(7);
                                string concept = dataReader.GetString(8);
                                string reference = dataReader.GetString(9);

                                string actualAtAddress = dataReader.GetString(colIdx_AtAddress);
                                decimal newLock = dataReader.GetDecimal(colIdx_NewLockBalance);
                                string accountNumber = dataReader.IsDBNull(colIdx_AccountNumber) ? string.Empty : dataReader.GetString(colIdx_AccountNumber);
                                int processorId = dataReader.GetInt32(colIdx_ProcessorId);


                                Movement.type type;
                                if (!Enum.TryParse(movementStr, true, out type))
                                {
                                    throw new GameEngineException($@"There is no Movement.type for type {movementStr}");
                                }

                                Currency currency = Currency.Factory(currencyCode.Iso4217Code, amount);
                                Movement m;
                                if (ItsADebitMovement(type))
                                {
                                    m = Movement.GenerateADebitMovement(sourceAsInt, actualAtAddress, day, type, currency, newBalance, who, documentNumber, storeId, concept, reference, newLock, accountNumber, processorId);
                                }
                                else
                                {
                                    m = Movement.GenerateACreditMovement(sourceAsInt, actualAtAddress, day, type, currency, newBalance, who, documentNumber, storeId, concept, reference, newLock, accountNumber, processorId);
                                }
                                movements.Add(m);
                            }
                        } 
                    } 
                } 
            }
            catch (Exception e)
            {
                Loggers.GetIntance().Db.Error($@"SQL for data (approximate due to params): {sql} || Error: {e.Message}", e);
                ErrorsSender.Send(e, $"SQL (approximate): {sql}");
                throw;
            }
            return new MovementsFiltered(movements, totalMovementsCount);
        }

		internal override MovementsFiltered ListMovement(int sourceNumber, string atAddress, int storeId, Coin currencyCode, DateTime startDate, DateTime endDate, int whoId, int initialIndex, int amountOfRows)
		{
			if (startDate.Minute != 0 || startDate.Second != 0) throw new GameEngineException($"{nameof(startDate)} {startDate} is not valid");
			if (endDate.Minute != 0 || endDate.Second != 0) throw new GameEngineException($"{nameof(endDate)} {endDate} is not valid");

			List<Movement> movements = new List<Movement>();
			int countMovements = 0;
			string sql = string.Empty;
			try
			{
				string whereForWhoId = (whoId == Users.NO_USER) ? string.Empty : $@" AND u.ID = {whoId} ";
				string whereForSource = (sourceNumber == Sources.NO_SOURCE_ID) ? string.Empty : $@" AND SOURCE = {sourceNumber} ";
				string whereForMovementType = $@"(MOVEMENT = '{Movement.type.Lock}' OR MOVEMENT = '{Movement.type.Credit}')";

				var from = GetTableName(atAddress, currencyCode);

				sql = $@"
					SELECT DAY, SOURCE, MOVEMENT, AMOUNT, NEWBALANCE, DOCUMENTNUMBER, u.NAME, m.STORE, IFNULL(CONCEPT, '') as CONCEPT, IFNULL(REFERENCE, '') as REFERENCE, COUNT(*) OVER(), NEWLOCKBALANCE, ACCOUNT_NUMBER, PROCESSORID
					FROM {from} m
					LEFT JOIN movements_users u ON u.ID = m.WHO
					WHERE m.STORE = {(int)storeId} AND 
                        {whereForMovementType} AND
                        DAY < '{ToFullDateTimeString(endDate.AddDays(1))}' AND DAY >= '{ToFullDateTimeString(startDate)}' 
                        {whereForWhoId} {whereForSource}
					ORDER BY DAY DESC
					LIMIT {amountOfRows} OFFSET {initialIndex}
				";
				using (MySqlConnection connection = new MySqlConnection(connectionString))
				{
					try
					{
						connection.Open();
						using (MySqlCommand command = new MySqlCommand(sql, connection))
						{
							var dataReader = command.ExecuteReader();

							while (dataReader.Read())
							{
								DateTime day = dataReader.GetDateTime(0);
								int sourceAsInt = dataReader.GetByte(1);
								string movement = dataReader.GetString(2);
								decimal amount = dataReader.GetDecimal(3);
								decimal newBalance = dataReader.GetDecimal(4);
								string documentNumber = dataReader.GetString(5);
								string who = Users.None;
								if (!dataReader.IsDBNull(6))
								{
									who = dataReader.GetString(6);
								}
								storeId = dataReader.GetByte(7);
								string concept = dataReader.GetString(8);
								string reference = dataReader.GetString(9);
								countMovements = dataReader.GetInt32(10);

								decimal newLock = dataReader.GetDecimal(11);
								string accountNumber = dataReader.GetString(12);
								int processorId = dataReader.GetInt32(13);

								Movement.type type;
								bool result = Enum.TryParse(movement, true, out type);
								if (!result)
								{
									throw new GameEngineException($@"There is no Movement.type for type {movement}");
								}

								Currency currency = Currency.Factory(currencyCode.Iso4217Code, amount);
								Movement m = null;
								var isDebit = ItsADebitMovement(type);
								if (isDebit)
								{
									m = Movement.GenerateADebitMovement(sourceAsInt, atAddress, day, type, currency, newBalance, who, documentNumber, storeId, concept, reference, newLock, accountNumber, processorId);
								}
								else
								{
									m = Movement.GenerateACreditMovement(sourceAsInt, atAddress, day, type, currency, newBalance, who, documentNumber, storeId, concept, reference, newLock, accountNumber, processorId);
								}

								movements.Add(m);
							}
							dataReader.Close();
						}
					}
					finally
					{
						connection.Close();
					}
				}
			}
			catch (Exception e)
			{
				Loggers.GetIntance().Db.Error($@"sql:{sql}", e);
				ErrorsSender.Send(e, sql);
				throw;
			}
			return new MovementsFiltered(movements, countMovements);
		}

		internal override MovementsReport MovementsReportFilteredBy(string atAddress, Coin currencyCode, int storeId, DateTime startDate, DateTime endDate, int initialIndex, int amountOfRows, int paramDocumentNumber, int referenceNumber)
		{
			if (startDate.Minute != 0 || startDate.Second != 0) throw new GameEngineException($"{nameof(startDate)} {startDate} is not valid");
			if (endDate.Minute != 0 || endDate.Second != 0) throw new GameEngineException($"{nameof(endDate)} {endDate} is not valid");

			var movements = new List<MovementRecord>();
			int countMovements = 0;
			string sql = string.Empty;
			try
			{
				var whereForDocumentNumber = (paramDocumentNumber == DOCUMENT_NUMBER_FOR_ALL) ? string.Empty : $" AND M1.DOCUMENTNUMBER = {paramDocumentNumber} ";
				var whereForReferenceNumber = (referenceNumber == DOCUMENT_NUMBER_FOR_ALL) ? string.Empty : $" AND M1.REFERENCE = {referenceNumber} ";
				var isGeneral = atAddress == ATADDRESS_GENERAL;
				if (isGeneral)
				{
					var from = GetTableName(currencyCode);
					sql = $@"
					SELECT DOCUMENTNUMBER, DAY, AMOUNT, NEWBALANCE, 
						CASE WHEN ((SELECT SUM(AMOUNT) 
							FROM {from} M2
							WHERE MOVEMENT = '{Movement.type.Unlock.ToString()}' AND M1.DOCUMENTNUMBER=M2.DOCUMENTNUMBER) = SUM(AMOUNT)) 
							THEN 0 ELSE 1 END AS ISPENDING, 
						COUNT(*) OVER(),
						ATADDRESS, MOVEMENT, PROCESSORID, REFERENCE 
					FROM {from} M1
					WHERE MOVEMENT IN ('{Movement.type.Lock}', '{Movement.type.Credit}') AND
						STORE = {storeId} AND 
						DAY < '{ToFullDateTimeString(endDate.AddDays(1))}' AND DAY >= '{ToFullDateTimeString(startDate)}'
						{whereForDocumentNumber} {whereForReferenceNumber}
					GROUP BY DOCUMENTNUMBER, DAY, AMOUNT, NEWBALANCE, ATADDRESS, MOVEMENT, PROCESSORID, REFERENCE 
					LIMIT {amountOfRows} OFFSET {initialIndex}
				";
					using (MySqlConnection connection = new MySqlConnection(connectionString))
					{
						try
						{
							connection.Open();
							using (MySqlCommand command = new MySqlCommand(sql, connection))
							{
								var dataReader = command.ExecuteReader();

								while (dataReader.Read())
								{
									string documentNumber = dataReader.GetString(0);
									DateTime day = dataReader.GetDateTime(1);
									decimal amount = dataReader.GetDecimal(2);
									decimal newBalance = dataReader.GetDecimal(3);
									bool isPending = dataReader.GetBoolean(4);
									countMovements = dataReader.GetInt32(5);
									string atAddressNumber = dataReader.GetString(6);
									string movement = dataReader.GetString(7);
									Movement.type type;
									bool result = Enum.TryParse(movement, true, out type);
									if (!result)
									{
										throw new GameEngineException($@"There is no Movement.type for type {movement}");
									}
									int processorId = dataReader.GetInt32(8);
									string reference = dataReader.GetString(9);

									movements.Add(new MovementRecord(currencyCode, atAddressNumber, documentNumber, day, isPending, amount, newBalance, type, processorId, reference));
								}
								dataReader.Close();
							}
						}
						finally
						{
							connection.Close();
						}
					}
				}
				else
				{
					var from = GetTableName(atAddress, currencyCode);
					sql = $@"
					WITH UnlockSums AS (
						SELECT DOCUMENTNUMBER, SUM(AMOUNT) AS TOTAL_UNLOCK_AMOUNT
						FROM {from}
						WHERE MOVEMENT = '{Movement.type.Unlock}'
						GROUP BY DOCUMENTNUMBER
					),
					LockCreditSums AS (
						SELECT DOCUMENTNUMBER, SUM(AMOUNT) AS TOTAL_LOCK_CREDIT_AMOUNT
						FROM {from}
						WHERE MOVEMENT IN ('{Movement.type.Lock}', '{Movement.type.Credit}')
						GROUP BY DOCUMENTNUMBER
					)
					SELECT M1.DOCUMENTNUMBER, M1.DAY, M1.AMOUNT, M1.NEWBALANCE, 
						CASE 
							WHEN COALESCE(U.TOTAL_UNLOCK_AMOUNT, 0) = COALESCE(L.TOTAL_LOCK_CREDIT_AMOUNT, 0) 
							THEN 0 
							ELSE 1 
						END AS ISPENDING, 
						COUNT(*) OVER() AS TOTAL_COUNT, 
						M1.MOVEMENT, M1.PROCESSORID, M1.REFERENCE 
					FROM {from} M1
	                LEFT JOIN UnlockSums U ON M1.DOCUMENTNUMBER = U.DOCUMENTNUMBER
					LEFT JOIN LockCreditSums L ON M1.DOCUMENTNUMBER = L.DOCUMENTNUMBER
					WHERE M1.MOVEMENT IN ('{Movement.type.Lock}', '{Movement.type.Credit}') AND 
						M1.STORE = {storeId} AND 
						M1.DAY < '{ToFullDateTimeString(endDate.AddDays(1))}' AND M1.DAY >= '{ToFullDateTimeString(startDate)}'
						{whereForDocumentNumber} {whereForReferenceNumber}
					LIMIT {amountOfRows} OFFSET {initialIndex}
				";
					using (MySqlConnection connection = new MySqlConnection(connectionString))
					{
						try
						{
							connection.Open();
							using (MySqlCommand command = new MySqlCommand(sql, connection))
							{
								var dataReader = command.ExecuteReader();

								while (dataReader.Read())
								{
									string documentNumber = dataReader.GetString(0);
									DateTime day = dataReader.GetDateTime(1);
									decimal amount = dataReader.GetDecimal(2);
									decimal newBalance = dataReader.GetDecimal(3);
									bool isPending = dataReader.GetBoolean(4);
									countMovements = dataReader.GetInt32(5);
									string movement = dataReader.GetString(6);
									Movement.type type;
									bool result = Enum.TryParse(movement, true, out type);
									if (!result)
									{
										throw new GameEngineException($@"There is no Movement.type for type {movement}");
									}
									int processorId = dataReader.GetInt32(7);
									string reference = dataReader.GetString(8);

									movements.Add(new MovementRecord(currencyCode, atAddress, documentNumber, day, isPending, amount, newBalance, type, processorId, reference));
								}
								dataReader.Close();
							}
						}
						finally
						{
							connection.Close();
						}
					}
				}
			}
			catch (Exception e)
			{
				Loggers.GetIntance().Db.Error($@"sql:{sql}", e);
				ErrorsSender.Send(e, sql);
				throw;
			}
			return new MovementsReport(movements, countMovements);
		}

		internal override string AtAddressFrom(int documentNumber)
		{
			string atAddress = string.Empty;
			string sql = string.Empty;
			try
			{
				var from = GetTableName(Coinage.Coin( Currencies.CODES.FP));
				sql = $@"SELECT ATADDRESS
						FROM {from}
						WHERE MOVEMENT = '{Movement.type.Lock.ToString()}' AND 
							STORE = {Store.STORES_SEQUENCE_LOTTO} AND 
							DOCUMENTNUMBER = {documentNumber}
				";
				using (MySqlConnection connection = new MySqlConnection(connectionString))
				{
					try
					{
						connection.Open();
						using (MySqlCommand command = new MySqlCommand(sql, connection))
						{
							var dataReader = command.ExecuteReader();

							while (dataReader.Read())
							{
								atAddress = dataReader.GetString(0);
							}
							dataReader.Close();
						}
					}
					finally
					{
						connection.Close();
					}
				}
			}
			catch (Exception e)
			{
				Loggers.GetIntance().Db.Error($@"sql:{sql}", e);
				ErrorsSender.Send(e, sql);
				throw;
			}
			return atAddress;
		}

		internal override MovementsReport MovementsFullDetailReportBy(AtAddress atAddress, Coin currencyCode, int storeId, DateTime startDate, DateTime endDate, int initialIndex, int amountOfRows)
		{
			if (startDate.Minute != 0 || startDate.Second != 0) throw new GameEngineException($"{nameof(startDate)} {startDate} is not valid");
			if (endDate.Minute != 0 || endDate.Second != 0) throw new GameEngineException($"{nameof(endDate)} {endDate} is not valid");

			var fragmentsInfo = new List<FragmentInfo>();
			int countMovements = 0;
			string sql = string.Empty;
			try
			{
				var from = GetTableName(atAddress.Number, currencyCode);
				sql = $@"SELECT MLR.DOCUMENTNUMBER, MLR.DAY, MLR.AMOUNT, MLR.NEWBALANCE, MLR.ISPENDING, 
                            F.FRAGMENT_ID, F.RISK, F.TO_WIN, F.REASON, F.DESCRIPTION, F.ADJUSTED_WIN, F.ADJUSTED_LOSS, COUNT(*) OVER() 
                        FROM (SELECT DOCUMENTNUMBER, DAY, AMOUNT, NEWBALANCE, 
							CASE WHEN ((SELECT SUM(AMOUNT) 
								FROM {from} M2
								WHERE MOVEMENT = @typeUnlock AND M1.DOCUMENTNUMBER=M2.DOCUMENTNUMBER) = SUM(AMOUNT)) 
								THEN 0 ELSE 1 END AS ISPENDING
						    FROM {from} M1 
						    WHERE MOVEMENT = @typeLock AND 
                                STORE = @store AND 
                                DAY < @endDate AND DAY >= @startDate
						    GROUP BY DOCUMENTNUMBER
						) MLR
                        LEFT JOIN C{atAddress.NumberForTables}_authfragments F ON F.AUTHORIZATION_ID = MLR.DOCUMENTNUMBER
                        ORDER BY MLR.DOCUMENTNUMBER, F.FRAGMENT_ID
					";
				using (MySqlConnection connection = new MySqlConnection(connectionString))
				{
					try
					{
						connection.Open();
						using (MySqlCommand command = new MySqlCommand(sql, connection))
						{
							command.Parameters.AddWithValue("@typeUnlock", Movement.type.Unlock.ToString());
							command.Parameters.AddWithValue("@typeLock", Movement.type.Lock.ToString());
							command.Parameters.AddWithValue("@store", storeId);
							command.Parameters.AddWithValue("@startDate", ToFullDateTimeString(startDate));
							command.Parameters.AddWithValue("@endDate", ToFullDateTimeString(endDate.AddDays(1)));
							var dataReader = command.ExecuteReader();

							while (dataReader.Read())
							{
								string documentNumber = dataReader.GetString(0);
								DateTime day = dataReader.GetDateTime(1);
								decimal totalAmount = dataReader.GetDecimal(2);
								decimal newBalance = dataReader.GetDecimal(3);
								bool isPending = dataReader.GetBoolean(4);

								int? fragmentId = null;
								decimal? risk = null;
								decimal? toWin = null;
								decimal? adjustedWinAmount = null;
								decimal? adjustedLossAmount = null;
								string fragmentDescription = string.Empty;
								FragmentReason fragmentReason = FragmentReason.Pending;

								var isCompletelyPending = dataReader.IsDBNull(5);
								if (!isCompletelyPending)
								{
									fragmentId = dataReader.GetInt16(5);
									risk = dataReader.GetDecimal(6);
									toWin = dataReader.GetDecimal(7);
									fragmentReason = (FragmentReason)dataReader.GetInt32(8);
									fragmentDescription = dataReader.GetString(9);
									adjustedWinAmount = dataReader.GetDecimal(10);
									adjustedLossAmount = dataReader.GetDecimal(11);
								}

								countMovements = dataReader.GetInt32(12);

								fragmentsInfo.Add(new FragmentInfo(
									atAddress,
									documentNumber,
									day,
									totalAmount,
									newBalance,
									isPending,
									fragmentId,
									risk,
									toWin,
									adjustedWinAmount,
									adjustedLossAmount,
									fragmentReason,
									fragmentDescription
									));
							}
							dataReader.Close();
						}
					}
					finally
					{
						connection.Close();
					}
				}
			}
			catch (Exception e)
			{
				Loggers.GetIntance().Db.Error($@"sql:{sql}", e);
				ErrorsSender.Send(e, sql);
				throw;
			}
			return new MovementsReport(currencyCode, fragmentsInfo, countMovements, initialIndex, amountOfRows);
		}

		public override int InsertUserIfNotExists(int storeId, string userName)
		{
			string sql = "";
			try
			{
				sql = $@"SELECT id FROM movements_users WHERE NAME = '{userName}' and STORE = {storeId} LIMIT 1";
				bool exist = false;
				int id = 0;
				using (MySqlConnection connection = new MySqlConnection(connectionString))
				{
					connection.Open();
					using (MySqlCommand command = new MySqlCommand(sql, connection))
					using (var reader = command.ExecuteReader())
					{
						exist = reader.Read();
						if (exist)
						{
							id = reader.GetInt32(0);
						}

						reader.Close();
					}
					if (!exist)
					{
						sql = $@"INSERT INTO movements_users(STORE, NAME) VALUES(@store, @userName);";
						using (MySqlCommand command = new MySqlCommand(sql, connection))
						{
							command.Parameters.AddWithValue("@userName", userName);
							command.Parameters.AddWithValue("@store", storeId);
							command.ExecuteNonQuery();
							id = (int)command.LastInsertedId;
						}

					}


					connection.Close();
				}
				return id;
			}
			catch (Exception e)
			{
				string message = $@"sql:{sql} @userName:{userName} @store:{storeId}";

				Loggers.GetIntance().Db.Error(message, e);
				ErrorsSender.Send(e, sql, message);
				throw;
			}
		}
		public override void CreateUserMovementsTableIfNotExists()
		{
			StringBuilder statement = new StringBuilder();
			statement
			.Append("create table  IF NOT EXISTS movements_users")
			.Append("(")
			.Append("ID BIGINT NOT NULL AUTO_INCREMENT,")
			.Append("STORE TINYINT NOT NULL,")
			.Append("NAME VARCHAR(50) NOT NULL,")
			.Append("PRIMARY KEY (ID)")
			.Append(");");

			ExecuteCommand(statement.ToString());
		}

		internal override IEnumerable<User> ListUsers(int storeId)
		{
			List<User> users = new List<User>();
			string sql = "";
			try
			{
				sql = $@"
				select ID, NAME
				from 
				movements_users
				where STORE = {storeId}
				ORDER by NAME";
				using (MySqlConnection connection = new MySqlConnection(connectionString))
				{
					try
					{
						connection.Open();
						using (MySqlCommand command = new MySqlCommand(sql, connection))
						{
							var dataReader = command.ExecuteReader();

							while (dataReader.Read())
							{
								int id = dataReader.GetInt32(0);
								string name = dataReader.GetString(1);

								users.Add(new User(storeId, id, name));
							}
							dataReader.Close();
						}
					}
					finally
					{
						connection.Close();
					}
				}
			}
			catch (Exception e)
			{
				Loggers.GetIntance().Db.Error($@"sql:{sql}", e);
				ErrorsSender.Send(e, sql);
				throw;
			}
			return users;
		}

		internal override void SavePayedFragment(bool itIsThePresent, MovementsCollector movements)
		{
			if (!itIsThePresent) return;

			FragmentInserts(movements);
			FragmentUpdates(movements);
		}

		private void FragmentInserts(MovementsCollector movements)
		{
			StringBuilder sql = new StringBuilder();
			sql.Append("BEGIN;");
			bool thereAreChanges = false;

			foreach (AuthorizationFragment fragment in movements.FragmentsToAdd())
			{
				int authorizationNumber = fragment.Authorization.Number;
				var atAddressNumber = fragment.Authorization.AtAddress.NumberForTables;
				thereAreChanges = true;

				sql.Append($@"
					INSERT INTO C{atAddressNumber}_authfragments (AUTHORIZATION_ID, FRAGMENT_ID, STATUS, RISK, REFERENCE, DESCRIPTION, TO_WIN, CURRENCY, REASON, ADJUSTED_WIN, ADJUSTED_LOSS, ACCOUNT_NUMBER, USELESS)
					VALUES
					(
						{authorizationNumber},
						{fragment.Number},
						{(int)fragment.Status},
						{fragment.Risk},
						'{Commons.EscapeMySQLServer(fragment.Reference)}',
						'{Commons.EscapeMySQLServer(fragment.Description)}',
						{fragment.ToWin},
						{fragment.Price.Coin.Id},
						{(int)fragment.Reason},
						{fragment.AdjustedWinAmount},
						{fragment.AdjustedLossAmount},
						'{fragment.Account.Number}',
						'{ToDateTimeString(fragment.Authorization.Useless)}'
					);");
			}
			sql.Append("COMMIT;");
			if (thereAreChanges)
			{
				try
				{
					using (MySqlConnection connection = new MySqlConnection(connectionString))
					{
						try
						{
							connection.Open();
							using (MySqlCommand command = new MySqlCommand(sql.ToString(), connection))
							{
								command.CommandType = CommandType.Text;
								command.ExecuteNonQuery();
							}
						}
						finally
						{
							connection.Close();
						}
					}
				}
				catch (Exception e)
				{
					Loggers.GetIntance().Db.Error($@"sql:{sql}", e);
					ErrorsSender.Send(e, sql.ToString());
					throw;
				}
			}
		}

		private void FragmentUpdates(MovementsCollector movements)
		{
			StringBuilder sql = new StringBuilder();
			sql.Append("BEGIN;");
			bool thereAreChanges = false;
			string atAddressNumber = string.Empty;

            foreach (AuthorizationFragment fragment in movements.FragmentsToUpdate())
			{
				int authorizationNumber = fragment.Authorization.Number;
				atAddressNumber = fragment.Authorization.AtAddress.NumberForTables;
				thereAreChanges = true;

				sql.Append($@"
							UPDATE C{atAddressNumber}_authfragments SET STATUS = {(int)fragment.Status}, REASON = {(int)fragment.Reason}, ADJUSTED_WIN = {fragment.AdjustedWinAmount}, ADJUSTED_LOSS = {fragment.AdjustedLossAmount},
							EDIT_MODE = 0 
							WHERE AUTHORIZATION_ID = {authorizationNumber} AND FRAGMENT_ID = {fragment.Number}
						;");
			}
			sql.Append("COMMIT;");
			if (thereAreChanges)
			{
				try
				{
					using (MySqlConnection connection = new MySqlConnection(connectionString))
					{
						try
						{
							connection.Open();
							using (MySqlCommand command = new MySqlCommand(sql.ToString(), connection))
							{
								command.CommandType = CommandType.Text;
								command.ExecuteNonQuery();
							}
						}
						finally
						{
							connection.Close();

                            WebHookClientRequest.Instance.SendWebHook(DateTime.Now, sql.ToString(), $"C{atAddressNumber}_authfragments", "Cashier");
                        }
					}
				}
				catch (Exception e)
				{
					Loggers.GetIntance().Db.Error($@"sql:{sql}", e);
					ErrorsSender.Send(e, sql.ToString());
					throw;
				}
			}
		}

		internal override void CreateFragmentIfNotExists(string number)
		{
			string sql = "";
			try
			{
				using (MySqlConnection connection = new MySqlConnection(connectionString))
				{

					try
					{

						StringBuilder statement = new StringBuilder();

						statement
						.Append("CREATE TABLE IF NOT EXISTS C").Append(number).Append("_authfragments")
						.Append("(")
						.Append("AUTHORIZATION_ID INT NOT NULL,")
						.Append("FRAGMENT_ID SMALLINT NOT NULL,")
						.Append("STATUS TINYINT NOT NULL,")
						.Append("RISK DECIMAL(10,2) NOT NULL,")
						.Append("REFERENCE VARCHAR(20) NOT NULL,")
						.Append("DESCRIPTION TEXT NOT NULL,")
						.Append("TO_WIN DECIMAL(10,2) NOT NULL,")
						.Append("ADJUSTED_WIN DECIMAL(10,2) NOT NULL,")
						.Append("ADJUSTED_LOSS DECIMAL(10,2) NOT NULL,")
						.Append("CURRENCY TINYINT NOT NULL,")
						.Append("REASON INT NOT NULL,")
						.Append("ACCOUNT_NUMBER VARCHAR(60) NOT NULL,")
						.Append("USELESS DATETIME NOT NULL,")
						.Append("EDIT_MODE TINYINT(1) NOT NULL DEFAULT 0,")
						.Append("INDEX IDX_AUTHORIZATION_ID(AUTHORIZATION_ID)")
						.Append(") CHARSET=utf8;");

						sql = statement.ToString();

						using (MySqlCommand command = new MySqlCommand(sql, connection))
						{
							command.ExecuteNonQuery();
						}
					}
					catch (Exception e)
					{
						Loggers.GetIntance().Db.Error($@"sql:{sql}", e);
						ErrorsSender.Send(e, sql);
						throw;
					}
					finally
					{
						connection.Close();
					}
				}

			}
			catch (Exception e)
			{
				Loggers.GetIntance().Db.Error($@"sql:{sql}", e);
				ErrorsSender.Send(e, sql);
				throw;
			}
		}

		private string ScriptToInserFragmentReasons()
		{
			StringBuilder script = new StringBuilder();
			foreach (FragmentReason reason in Enum.GetValues(typeof(FragmentReason)))
			{
				script.Append($"INSERT INTO authfragments_reasons (ID, REASON) VALUES ({(int)reason},'{reason.ToString()}');");
			}
			return script.ToString();
		}

		private bool ExistAuthFragmentReasonsTable()
		{
			bool exists = true;
			string sql = "SELECT 1 FROM authfragments_reasons LIMIT 1";
			using (MySqlConnection connection = new MySqlConnection(connectionString))
			{
				try
				{
					connection.Open();
					using (MySqlCommand command = new MySqlCommand(sql, connection))
					{
						var dataReader = command.ExecuteReader();
						dataReader.Close();
					}
				}
				catch
				{
					exists = false;
				}
				finally
				{
					connection.Close();
				}
			}
			return exists;
		}

		internal override void CreateFragmentReasonsIfNotExists()
		{
			StringBuilder statement = new StringBuilder();
			statement
				.Append("CREATE TABLE IF NOT EXISTS authfragments_reasons")
				.Append("(")
				.Append("ID INT NOT NULL,")
				.Append($"REASON VARCHAR(10) NOT NULL")
				.Append(") CHARSET=utf8;");

			statement.Append(ScriptToInserFragmentReasons());

			string sql = statement.ToString();

			try
			{
				using (MySqlConnection connection = new MySqlConnection(connectionString))
				{
					try
					{
						connection.Open();
						MySqlScript script = new MySqlScript(connection, sql);
						script.Execute();
					}
					catch (Exception e)
					{
						Loggers.GetIntance().Db.Error($@"sql:{sql}", e);
						ErrorsSender.Send(e, sql);
						throw;
					}
					finally
					{
						connection.Close();
					}
				}
			}
			catch (Exception e)
			{
				Loggers.GetIntance().Db.Error($@"sql:{sql}", e.InnerException ?? e);
				ErrorsSender.Send(e.InnerException ?? e, sql);
				throw;
			}
		}

		internal override MovementsFiltered ListMovement(int inicialIndex, int amountOfRows, string atAddress, int storeId, DateTime initDate, DateTime endDate, Coin currencyCode)
		{
			List<Movement> movements = new List<Movement>();
			int countMovements = 0;
			string sql = "";
			try
			{
				var from = GetTableName(atAddress, currencyCode);
				sql = $@"
					SELECT DAY, SOURCE, MOVEMENT, AMOUNT, NEWBALANCE, DOCUMENTNUMBER, WHO, m.STORE, IFNULL(CONCEPT, '') as CONCEPT, IFNULL(REFERENCE, '') as REFERENCE, COUNT(*) OVER(), NEWLOCKBALANCE, ACCOUNT_NUMBER, PROCESSORID
					FROM {from} m
					WHERE m.STORE = {storeId} AND DAY < '{ToFullDateTimeString(endDate.AddDays(1))}' AND DAY >= '{ToFullDateTimeString(initDate)}'
					LIMIT {amountOfRows} OFFSET {inicialIndex}
				";

				using (MySqlConnection connection = new MySqlConnection(connectionString))
				{
					try
					{
						connection.Open();
						using (MySqlCommand command = new MySqlCommand(sql, connection))
						{
							var dataReader = command.ExecuteReader();

							while (dataReader.Read())
							{
								DateTime day = dataReader.GetDateTime(0);
								int sourceAsInt = dataReader.GetByte(1);
								string movement = dataReader.GetString(2);
								decimal amount = dataReader.GetDecimal(3);
								decimal newBalance = dataReader.GetDecimal(4);
								string documentNumber = dataReader.GetString(5);
								string who = Users.None;
								if (!dataReader.IsDBNull(6))
								{
									who = dataReader.GetString(6);
								}
								storeId = dataReader.GetByte(7);
								string concept = dataReader.GetString(8);
								string reference = dataReader.GetString(9);
								countMovements = dataReader.GetInt32(10);

								decimal newLock = dataReader.GetDecimal(11);
								string accountNumber = dataReader.GetString(12);
								int processorId = dataReader.GetInt32(13);

								Movement.type type;
								bool result = Enum.TryParse(movement, true, out type);
								if (!result)
								{
									throw new GameEngineException($@"There is no Movement.type for type {movement}");
								}

								Currency currency = Currency.Factory(currencyCode.Iso4217Code, amount);
								Movement m = null;

								var isDebit = ItsADebitMovement(type);
								if (isDebit)
								{
									m = Movement.GenerateADebitMovement(sourceAsInt, atAddress, day, type, currency, newBalance, who, documentNumber, storeId, concept, reference, newLock, accountNumber, processorId);
								}
								else
								{
									m = Movement.GenerateACreditMovement(sourceAsInt, atAddress, day, type, currency, newBalance, who, documentNumber, storeId, concept, reference, newLock, accountNumber, processorId);
								}

								movements.Add(m);
							}
							dataReader.Close();
						}
					}
					finally
					{
						connection.Close();
					}
				}
			}
			catch (Exception e)
			{
				Loggers.GetIntance().Db.Error($@"sql:{sql}", e);
				ErrorsSender.Send(e, sql);
				throw;
			}
			return new MovementsFiltered(movements, countMovements);
		}

		public override void SaveBalanceMovements(bool itIsThePresent, int whosId, IEnumerable<Movement> movements, DataTable movementTableByAccount, DataTable movementTableByCurrency)
		{
			if (!itIsThePresent) return;

			var firstMovement = movements.First();
			var currentDay = firstMovement.Day;
			var dateAsText = ToFullDateTimeString(currentDay);
			var currentConcept = Validator.StringEscape(firstMovement.Concept);
			var currentAtAddress = firstMovement.AtAddress;
			var currentCurrency = firstMovement.Currency;

			movementTableByCurrency.TableName = GetTableName(currentCurrency).ToString();
			movementTableByAccount.TableName = GetTableName(currentAtAddress, currentCurrency).ToString();
			foreach (Movement movement in movements)
			{
				if (currentConcept != Validator.StringEscape(movement.Concept)) throw new GameEngineException($"{nameof(currentConcept)} was {currentConcept} but now is {movement.Concept}. It must be the same for all movements in the group");
				if (currentDay != movement.Day)
				{
					currentDay = movement.Day;
					dateAsText = ToFullDateTimeString(currentDay);
				}

				var row = movementTableByCurrency.NewRow();
				row["DAY"] = dateAsText;
				row["ATADDRESS"] = movement.AtAddress;
				row["SOURCE"] = movement.Source;
				row["CURRENCY"] = movement.Currency.ToString();
				row["MOVEMENT"] = movement.Type.ToString();
				row["AMOUNT"] = movement.CurrentAmount;
				row["NEWBALANCE"] = movement.NewBalance;
				if (whosId == Users.NO_USER)
				{
					row["WHO"] = DBNull.Value;
				}
				else
				{
					row["WHO"] = whosId;
				}
				row["DOCUMENTNUMBER"] = movement.DocumentNumber;
				row["STORE"] = movement.StoreId;
				row["REFERENCE"] = movement.Reference;
				row["CONCEPT"] = currentConcept;
				row["NEWLOCKBALANCE"] = movement.NewLock;
				row["ACCOUNT_NUMBER"] = movement.AccountNumber;
				row["PROCESSORID"] = movement.ProcessorId;
				movementTableByCurrency.Rows.Add(row);

				row = movementTableByAccount.NewRow();
				row["DAY"] = dateAsText;
				row["SOURCE"] = movement.Source;
				row["MOVEMENT"] = movement.Type.ToString();
				row["AMOUNT"] = movement.CurrentAmount;
				row["NEWBALANCE"] = movement.NewBalance;
				if (whosId == Users.NO_USER)
				{
					row["WHO"] = DBNull.Value;
				}
				else
				{
					row["WHO"] = whosId;
				}
				row["DOCUMENTNUMBER"] = movement.DocumentNumber;
				row["STORE"] = movement.StoreId;
				row["REFERENCE"] = movement.Reference;
				row["CONCEPT"] = currentConcept;
				row["NEWLOCKBALANCE"] = movement.NewLock;
				row["ACCOUNT_NUMBER"] = movement.AccountNumber;
				row["PROCESSORID"] = movement.ProcessorId;
				movementTableByAccount.Rows.Add(row);
			}

			InsertInMovementsTable(movementTableByCurrency, movementTableByAccount);
		}

		void InsertInMovementsTable(DataTable movementTableByCurrency, DataTable movementTableByAccount)
		{
			using (var connection = new MySqlConnection(connectionString))
			{
				try
				{
					connection.Open();
					using (MySqlTransaction tran = connection.BeginTransaction(IsolationLevel.Serializable))
					{
						using (MySqlCommand cmd = new MySqlCommand())
						{
							cmd.Connection = connection;
							cmd.Transaction = tran;
							cmd.CommandText = $"SELECT * FROM " + movementTableByCurrency.TableName + " limit 0";

							using (MySqlDataAdapter adapter = new MySqlDataAdapter(cmd))
							{
								adapter.UpdateBatchSize = 10000;
								using (MySqlCommandBuilder cb = new MySqlCommandBuilder(adapter))
								{
									cb.SetAllValues = true;
									adapter.Update(movementTableByCurrency);
								}
							};

							cmd.CommandText = $"SELECT * FROM " + movementTableByAccount.TableName + " limit 0";

							using (MySqlDataAdapter adapter = new MySqlDataAdapter(cmd))
							{
								adapter.UpdateBatchSize = 10000;
								using (MySqlCommandBuilder cb = new MySqlCommandBuilder(adapter))
								{
									cb.SetAllValues = true;
									adapter.Update(movementTableByAccount);
									tran.Commit();
								}
							};
						}
					}
				}
				catch (Exception e)
				{
					Loggers.GetIntance().Db.Error($@"table:{movementTableByCurrency.TableName} content:{movementTableByCurrency.ToString()}", e);
					ErrorsSender.Send(e, movementTableByCurrency.ToString());
					throw;
				}
				finally
				{
					connection.Close();
					movementTableByCurrency.Clear();
					movementTableByAccount.Clear();
				}
			}
		}

		internal override Authorization RetrieveFragments(int authorizationNumber, AtAddress atAddress, Purpose purpose)
		{
			string sql = $"SELECT AUTHORIZATION_ID, FRAGMENT_ID, RISK, STATUS, CURRENCY, REFERENCE, DESCRIPTION, TO_WIN, REASON, ADJUSTED_WIN, ADJUSTED_LOSS, ACCOUNT_NUMBER, USELESS FROM C{atAddress.NumberForTables}_authfragments nolock WHERE AUTHORIZATION_ID = @AuthorizationNumber";
			var storedFragmentsInfo = new List<PartialFragment>();
			int lowerId = Int32.MaxValue;
			int higherId = Int32.MinValue;
			Currency amountOfEachFragment = null;
			decimal totalAmount = 0;
			decimal fragmentRisk = 0;
			string accountNumber = "";
			DateTime useless = DateTime.MinValue;
			try
			{
				using (MySqlConnection connection = new MySqlConnection(connectionString))
				{
					try
					{
						connection.Open();
						using (MySqlCommand command = new MySqlCommand(sql.ToString(), connection))
						{
							command.Parameters.AddWithValue("@AuthorizationNumber", authorizationNumber);

							var dataReader = command.ExecuteReader();

							while (dataReader.Read())
							{
								var coin = Coinage.GetById(dataReader.GetByte(4));
								fragmentRisk = dataReader.GetDecimal(2);
								Currency price = Currency.Factory(coin, fragmentRisk);
								totalAmount = totalAmount + fragmentRisk;

								int fragmentId = dataReader.GetInt16(1);
								FragmentStatus status = (FragmentStatus)dataReader.GetByte(3);
								if (lowerId > fragmentId) lowerId = fragmentId;
								if (higherId < fragmentId) higherId = fragmentId;
								if (amountOfEachFragment == null) amountOfEachFragment = price;

								string fragmentReference = dataReader.GetString(5);
								string fragmentDescription = dataReader.GetString(6);
								decimal fragmentToWin = dataReader.GetDecimal(7);
								FragmentReason fragmentReason = (FragmentReason)dataReader.GetInt32(8);

								decimal adjustedWinAmount = dataReader.GetDecimal(9);
								decimal adjustedLossAmount = dataReader.GetDecimal(10);

								accountNumber = dataReader.GetString(11);
								useless = dataReader.GetDateTime(12);
								storedFragmentsInfo.Add(new PartialFragment(fragmentId, status, fragmentReference, fragmentDescription, fragmentRisk, fragmentToWin, fragmentReason, adjustedWinAmount, adjustedLossAmount, accountNumber, useless));
							}
						}
					}
					finally
					{
						connection.Close();
					}
				}
			}
			catch (Exception e)
			{
				string message = $@"sql:{sql} @AuthorizationNumber:{authorizationNumber}";
				Loggers.GetIntance().Db.Error(message, e.InnerException ?? e);
				ErrorsSender.Send(e.InnerException ?? e, message);
				throw;
			}

			if (storedFragmentsInfo.Count == 0) throw new GameEngineException($"Authorization number {authorizationNumber} does not exist");

			var amount = Currency.Factory(amountOfEachFragment.CurrencyCode, totalAmount);
			if (purpose == Purpose.ForUpdate)
			{
				string sqlUpdate = $"UPDATE C{atAddress.NumberForTables}_authfragments SET EDIT_MODE = 1 WHERE AUTHORIZATION_ID = {authorizationNumber}";
				using (MySqlConnection connection = new MySqlConnection(connectionString))
				{
					try
					{
						connection.Open();
						using (MySqlCommand command = new MySqlCommand(sqlUpdate.ToString(), connection))
						{
							command.CommandType = CommandType.Text;
							command.ExecuteNonQuery();
						}
					}
					finally
					{
						connection.Close();
					}
				}
			}

			AccountWithBalance account = atAddress.SearchAccountByNumber(accountNumber);

			Authorization authorization = new Authorization(authorizationNumber, amount, account, useless);
			if (fragmentRisk==0 && storedFragmentsInfo.Count == 1)
            {
				var firstFragment = storedFragmentsInfo.First();
				authorization.CreateFakeFragment(firstFragment.Reference, firstFragment.Description, firstFragment.ToWin);
			}
			else 
				authorization.CreateFragments(lowerId, higherId, fragmentRisk, storedFragmentsInfo);
			return authorization;
		}

		internal override MovementDetailReport MovementDetailReportBy(AtAddress atAddress, int authorizationNumber, Coin currencyCode, int initialIndex, int amountOfRows)
		{
			var movementDetails = new List<MovementDetailRecord>();
			int countMovements = 0;
			var sql = $@"SELECT FRAGMENT_ID, RISK, TO_WIN, REASON, DESCRIPTION, COUNT(*) OVER(), ADJUSTED_WIN, ADJUSTED_LOSS
						FROM C{atAddress.NumberForTables}_authfragments
						WHERE AUTHORIZATION_ID = @authorizationNumber AND CURRENCY = @currencyCode
						LIMIT @amountOfRows OFFSET @initialIndex;";
			try
			{
				using (MySqlConnection connection = new MySqlConnection(connectionString))
				{
					try
					{
						connection.Open();
						using (MySqlCommand command = new MySqlCommand(sql.ToString(), connection))
						{
							command.Parameters.AddWithValue("@authorizationNumber", authorizationNumber);
							command.Parameters.AddWithValue("@currencyCode", currencyCode.Id);
							command.Parameters.AddWithValue("@amountOfRows", amountOfRows);
							command.Parameters.AddWithValue("@initialIndex", initialIndex);
							var dataReader = command.ExecuteReader();

							while (dataReader.Read())
							{
								int id = dataReader.GetInt16(0);
								decimal risk = dataReader.GetDecimal(1);
								decimal toWin = dataReader.GetDecimal(2);
								int reasonId = dataReader.GetInt16(3);
								FragmentReason fragmentReason = (FragmentReason)reasonId;
								string description = dataReader.GetString(4);
								countMovements = dataReader.GetInt32(5);
								decimal adjustedWinAmount = dataReader.GetDecimal(6);
								decimal adjustedLossAmount = dataReader.GetDecimal(7);

								movementDetails.Add(new MovementDetailRecord(currencyCode, authorizationNumber, id, risk, toWin, description, fragmentReason, adjustedWinAmount, adjustedLossAmount));
							}
						}
					}
					finally
					{
						connection.Close();
					}
				}
			}
			catch (Exception e)
			{
				string message = $@"sql:{sql} @AuthorizationNumber:{authorizationNumber}";
				Loggers.GetIntance().Db.Error(message, e.InnerException ?? e);
				ErrorsSender.Send(e.InnerException ?? e, message);
				throw;
			}

			return new MovementDetailReport(currencyCode, movementDetails, countMovements, atAddress, authorizationNumber, initialIndex, amountOfRows);
		}

		public override void CreateAuthorizationSequenceIfNotExists()
		{
			string sql = @"CREATE TABLE IF NOT EXISTS sequence (
							seq_name varchar(100) NOT NULL,
							increment int NOT NULL DEFAULT 1,
							min_value int NOT NULL DEFAULT 1,
							max_value int NOT NULL DEFAULT 2147483647,
							cur_value int DEFAULT 1,
							cycle boolean NOT NULL DEFAULT FALSE,
							PRIMARY KEY(seq_name)
						) ENGINE = MyISAM; 
						
						INSERT INTO sequence (seq_name, cycle) VALUE ('authorizations', true) ON DUPLICATE KEY UPDATE cycle=cycle;

						DROP FUNCTION IF EXISTS nextval;

						DELIMITER $$
						CREATE FUNCTION nextval (arg_name varchar(100))
						RETURNS bigint DETERMINISTIC
						BEGIN
							DECLARE cur_val bigint;
 
							SELECT
								cur_value INTO cur_val
							FROM
								sequence
							WHERE
								seq_name = arg_name;
 
							IF cur_val IS NOT NULL THEN
								UPDATE
									sequence
								SET
									cur_value = IF (
										(cur_value + increment) > max_value OR (cur_value + increment) < min_value,
										IF (
											cycle = TRUE,
											IF (
												(cur_value + increment) > max_value,
												min_value, 
												max_value 
											),
											NULL
										),
										cur_value + increment
									)
								WHERE
									seq_name = arg_name;
							END IF; 
							RETURN cur_val;
						END $$
 
						DELIMITER ;";
			try
			{
				using (MySqlConnection connection = new MySqlConnection(connectionString))
				{
					try
					{
						connection.Open();
						MySqlScript script = new MySqlScript(connection, sql);
						script.Execute();
					}
					catch (Exception e)
					{
						Loggers.GetIntance().Db.Error($@"sql:{sql}", e);
						ErrorsSender.Send(e, sql);
						throw;
					}
					finally
					{
						connection.Close();
					}
				}

			}
			catch (Exception e)
			{
				Loggers.GetIntance().Db.Error($@"sql:{sql}", e.InnerException ?? e);
				ErrorsSender.Send(e.InnerException ?? e, sql);
				throw;
			}
		}

		public override int NextAuthorizationNumber()
		{
			string sql = "SELECT nextval('authorizations') as next_sequence;";
			int result = -1;
			try
			{
				using (MySqlConnection connection = new MySqlConnection(connectionString))
				{
					try
					{
						connection.Open();
						using (MySqlCommand command = new MySqlCommand(sql, connection))
						{
							var dataReader = command.ExecuteReader();

							dataReader.Read();

							result = dataReader.GetInt32(0);

							dataReader.Close();
						}
					}
					catch (Exception e)
					{
						Loggers.GetIntance().Db.Error($@"sql:{sql}", e);
						ErrorsSender.Send(e, sql);
						throw;
					}
					finally
					{
						connection.Close();
					}
				}
			}
			catch (Exception e)
			{
				Loggers.GetIntance().Db.Error($@"sql:{sql}", e.InnerException ?? e);
				ErrorsSender.Send(e.InnerException ?? e, sql);
				throw;
			}

			return result;
		}

		protected override void CreateMovementsTableForThisAccountAndCurrency(string atAddress, Coin currencyCode)
		{
			atAddress = atAddress.Trim().ToLower();

			string sql = CreateMovementsStorage(atAddress, currencyCode);

			ExecuteCommand(sql);
		}

		internal override string CreateMovementsStorage(string atAddress, Coin currencyCode)
		{
			StringBuilder statement = new StringBuilder();
			string tableName = GetTableName(atAddress, currencyCode);

			var strMovements = string.Join(",", Enum.GetNames(typeof(Movement.type)).Select(x => $"'{x.ToString()}'").ToArray());

			statement
				.Append("CREATE TABLE IF NOT EXISTS ").Append(tableName)
				.Append("(")
				.Append("DAY DATETIME(3) NOT NULL,")
				.Append("SOURCE TINYINT NOT NULL,")
				.Append($"MOVEMENT ENUM({strMovements}) NOT NULL,")
				.Append("AMOUNT DECIMAL(16,8) NOT NULL,")
				.Append("NEWBALANCE DECIMAL(16,8) NOT NULL,")
				.Append("NEWLOCKBALANCE DECIMAL(16,8) NOT NULL,")
				.Append("ACCOUNT_NUMBER VARCHAR(60) NOT NULL,")
				.Append("PROCESSORID INT NOT NULL,")
				.Append("WHO BIGINT,")
				.Append("DOCUMENTNUMBER VARCHAR(45) NOT NULL,")
				.Append("REFERENCE VARCHAR(30) NULL,")
				.Append("CONCEPT TEXT,")
				.Append("STORE TINYINT NOT NULL,")
				.Append($"INDEX {tableName}_DAY (DAY ASC)")
				.Append(") CHARSET=utf8;");

			return statement.ToString();
		}

		protected override void CreateMovementsTableForThisCurrency(string currencyCode)
		{
			string sql = CreateMovementsStorage(currencyCode);
			try
			{
				using (MySqlConnection connection = new MySqlConnection(connectionString))
				{
					connection.Open();
					try
					{
						using (MySqlCommand command = new MySqlCommand(sql, connection))
						{
							command.ExecuteNonQuery();
						}
					}
					catch (Exception e)
					{

						Loggers.GetIntance().Db.Error($@"sql:{sql}", e);
						ErrorsSender.Send(e, sql);
						throw;
					}
					finally
					{
						connection.Close();
					}
				}

			}
			catch (Exception e)
			{
				Loggers.GetIntance().Db.Error($@"sql:{sql}", e);
				ErrorsSender.Send(e, sql);
				throw;
			}
		}

		internal override string CreateMovementsStorage(string currencyCode)
		{
			string tableName = GetTableName(currencyCode);

			var strMovements = string.Join(",", Enum.GetNames(typeof(Movement.type)).Select(x => $"'{x.ToString()}'").ToArray());
			StringBuilder statement = new StringBuilder();
			statement.Append($"CREATE TABLE IF NOT EXISTS {tableName}").AppendLine()
				.Append("(").AppendLine()
				.Append("DAY DATETIME(3) NOT NULL,").AppendLine()
				.Append($"ATADDRESS VARCHAR({Customer.DATABASE_FIELD_ACCOUNT_LENGHT}) NOT NULL,").AppendLine()
				.Append("SOURCE TINYINT NOT NULL,").AppendLine()
				.Append($"MOVEMENT ENUM({strMovements}) NOT NULL,").AppendLine()
				.Append("AMOUNT DECIMAL(16,8) NOT NULL,").AppendLine()
				.Append("NEWBALANCE DECIMAL(16,8) NOT NULL,").AppendLine()
				.Append("NEWLOCKBALANCE DECIMAL(16,8) NOT NULL,").AppendLine()
				.Append("ACCOUNT_NUMBER VARCHAR(60) NOT NULL,").AppendLine()
				.Append("PROCESSORID INT NOT NULL,").AppendLine()
				.Append("WHO BIGINT,").AppendLine()
				.Append("DOCUMENTNUMBER VARCHAR(45) NOT NULL,").AppendLine()
				.Append("REFERENCE VARCHAR(30) NULL,").AppendLine()
				.Append("CONCEPT TEXT,").AppendLine()
				.Append("STORE TINYINT NOT NULL,").AppendLine()
                .Append("INDEX idx_store_movement (STORE, MOVEMENT),")
                .Append("INDEX idx_documentnumber (DOCUMENTNUMBER),")
                .Append("INDEX idx_day (DAY),")
                .Append($"INDEX {tableName}_ATADDRESS (ATADDRESS ASC)").AppendLine()
				.Append(") CHARSET=utf8;")
				.AppendLine();
			return statement.ToString();
		}

		protected override void CreateFragmentsTableForThisAccount(string atAddressNumber)
		{
			string sql = CreateFragmentsStorage(atAddressNumber);
			try
			{
				using (MySqlConnection connection = new MySqlConnection(connectionString))
				{
					connection.Open();
					try
					{
						using (MySqlCommand command = new MySqlCommand(sql, connection))
						{
							command.ExecuteNonQuery();
						}
					}
					catch (Exception e)
					{

						Loggers.GetIntance().Db.Error($@"sql:{sql}", e);
						ErrorsSender.Send(e, sql);
						throw;
					}
					finally
					{
						connection.Close();
					}
				}

			}
			catch (Exception e)
			{
				Loggers.GetIntance().Db.Error($@"sql:{sql}", e);
				ErrorsSender.Send(e, sql);
				throw;
			}
		}

		internal override string CreateFragmentsStorage(string atAddress)
		{
			StringBuilder statement = new StringBuilder();
			statement
			.Append("CREATE TABLE IF NOT EXISTS C").Append(atAddress).Append("_authfragments")
			.Append("(")
			.Append("AUTHORIZATION_ID INT NOT NULL,")
			.Append("FRAGMENT_ID SMALLINT NOT NULL,")
			.Append("STATUS TINYINT NOT NULL,")
			.Append("RISK DECIMAL(10,2) NOT NULL,")
			.Append("REFERENCE VARCHAR(20) NOT NULL,")
			.Append("DESCRIPTION TEXT NOT NULL,")
			.Append("TO_WIN DECIMAL(10,2) NOT NULL,")
			.Append("ADJUSTED_WIN DECIMAL(10,2) NOT NULL,")
			.Append("ADJUSTED_LOSS DECIMAL(10,2) NOT NULL,")
			.Append("CURRENCY TINYINT NOT NULL,")
			.Append("REASON INT NOT NULL,")
			.Append("ACCOUNT_NUMBER VARCHAR(60) NOT NULL,")
			.Append("EDIT_MODE TINYINT(1) NOT NULL DEFAULT 0,")
			.Append("INDEX IDX_AUTHORIZATION_ID(AUTHORIZATION_ID),")
			.Append("USELESS DATETIME NOT NULL")
			.Append(") CHARSET=utf8;");

			return statement.ToString();
		}

		internal static string CreateFragmentsStorageQuery(string atAddress, bool subfixRequired = true)
		{
			if (subfixRequired) atAddress = "C" + atAddress;

			StringBuilder statement = new StringBuilder();
			statement
			.Append("CREATE TABLE IF NOT EXISTS ").Append(atAddress).Append("_authfragments")
			.Append("(")
			.Append("AUTHORIZATION_ID INT NOT NULL,")
			.Append("FRAGMENT_ID SMALLINT NOT NULL,")
			.Append("STATUS TINYINT NOT NULL,")
			.Append("RISK DECIMAL(10,2) NOT NULL,")
			.Append("REFERENCE VARCHAR(20) NOT NULL,")
			.Append("DESCRIPTION TEXT NOT NULL,")
			.Append("TO_WIN DECIMAL(10,2) NOT NULL,")
			.Append("ADJUSTED_WIN DECIMAL(10,2) NOT NULL,")
			.Append("ADJUSTED_LOSS DECIMAL(10,2) NOT NULL,")
			.Append("CURRENCY TINYINT NOT NULL,")
			.Append("REASON INT NOT NULL,")
			.Append("ACCOUNT_NUMBER VARCHAR(60) NOT NULL,")
			.Append("USELESS DATETIME NOT NULL,")
			.Append("EDIT_MODE TINYINT(1) NOT NULL DEFAULT 0,")
			.Append("INDEX IDX_AUTHORIZATION_ID(AUTHORIZATION_ID)")
			.Append(") CHARSET=utf8;");

			return statement.ToString();
		}

		internal override string CreateFragmentsStorage(string atAddress, bool subfixRequired = true)
		{
			return CreateFragmentsStorageQuery(atAddress, subfixRequired);
		}

        public override void DeleteAuthFragments(string atAddress)
        {
			string tableName = atAddress + "_authfragments";
			if (!ExistsTable(tableName)) return;

			if (!ExistsInAuthFragments(tableName)) return;

			StringBuilder sql = new StringBuilder();
			const string POSTFIX = "_$OLD";

			string createQueryTable = CreateFragmentsStorage(atAddress, false);

			//SCRIPT FOR Authfragments
			sql.AppendLine($"RENAME TABLE {tableName} TO {tableName + POSTFIX};");
			sql.AppendLine(createQueryTable);
			sql.AppendLine($"INSERT INTO {tableName}(AUTHORIZATION_ID, FRAGMENT_ID, STATUS, RISK, REFERENCE, DESCRIPTION, TO_WIN, ADJUSTED_WIN, ADJUSTED_LOSS, CURRENCY, REASON, ACCOUNT_NUMBER, EDIT_MODE, USELESS) SELECT AUTHORIZATION_ID, FRAGMENT_ID, STATUS, RISK, REFERENCE, DESCRIPTION, TO_WIN, ADJUSTED_WIN, ADJUSTED_LOSS, CURRENCY, REASON, ACCOUNT_NUMBER, EDIT_MODE, USELESS FROM {tableName + POSTFIX} WHERE CURRENT_TIMESTAMP() >= USELESS;");
			sql.AppendLine($"DROP TABLE { tableName + POSTFIX};");


			lock (fragmentsAndMovementsSemaphore)
			{
				ExecuteCommand(sql.ToString());
			}
		}

        public override void DeleteMovements(string atAddress, Currencies.CODES currencyCode)
        {
			string tableName = GetTableName(atAddress, Coinage.Coin(currencyCode), false);
			if (!ExistsTable(tableName)) return;

			if (!ExistsInMovements(tableName, LongTermController.MonthsToArchivedMovements)) return;
			if (!LongTermController.ENABLED) return;

			StringBuilder sql = new StringBuilder();
			const string POSTFIX = "_$OLD";

			IEnumerable<MovementStruct> allMovements = MovementsToArchived(tableName, LongTermController.MonthsToArchivedMovements);
			bool success = LongTermController.Instance().SaveMovementsLongTerm(tableName, allMovements);
			if (success) MarkArchivedMovements(tableName, LongTermController.MonthsToArchivedMovements);

			string createQueryTable = CreateMovementsStorage(tableName, currencyCode, false);

			sql.AppendLine($"RENAME TABLE {tableName} TO {tableName + POSTFIX};");
			sql.AppendLine(createQueryTable);
			sql.AppendLine($"INSERT INTO {tableName}(DAY, SOURCE, MOVEMENT, AMOUNT, NEWBALANCE, NEWLOCKBALANCE, ACCOUNT_NUMBER, WHO, DOCUMENTNUMBER, REFERENCE, CONCEPT, STORE, ARCHIVED) SELECT DAY, SOURCE, MOVEMENT, AMOUNT, NEWBALANCE, NEWLOCKBALANCE, ACCOUNT_NUMBER, WHO, DOCUMENTNUMBER, REFERENCE, CONCEPT, STORE, ARCHIVED FROM {tableName + POSTFIX} WHERE ARCHIVED = 0;");
			sql.AppendLine($"DROP TABLE { tableName + POSTFIX};");

			lock (fragmentsAndMovementsSemaphore)
			{
				ExecuteCommand(sql.ToString());
			}
		}

		private bool ExistsInAuthFragments(string tableName)
		{
			bool result = false;
			string sqlString = $"SELECT 1 WHERE EXISTS(SELECT * FROM {tableName} WHERE CURRENT_TIMESTAMP() >= USELESS);";

			using (MySqlConnection connection = new MySqlConnection(connectionString))
			{
				try
				{
					connection.Open();
					using (MySqlCommand command = new MySqlCommand(sqlString, connection))
					using (DbDataReader reader = command.ExecuteReader())
					{
						while (reader.Read())
						{
							result = true;
						}
						reader.Close();
					}
				}
				finally
				{
					connection.Close();
				}
			}

			return result;
		}

		private bool ExistsInMovements(string tableName, int max_months_to_archive)
		{
			bool result = false;
			string sqlString = $"SELECT 1 WHERE EXISTS(SELECT * FROM {tableName} WHERE TIMESTAMPDIFF(month, DAY, CURRENT_TIMESTAMP()) >= {max_months_to_archive});";

			using (MySqlConnection connection = new MySqlConnection(connectionString))
			{
				try
				{
					connection.Open();
					using (MySqlCommand command = new MySqlCommand(sqlString, connection))
					using (DbDataReader reader = command.ExecuteReader())
					{
						while (reader.Read())
						{
							result = true;
						}
						reader.Close();
					}
				}
				finally
				{
					connection.Close();
				}
			}

			return result;
		}

		public override IEnumerable<string> AllCashierAccounts()
        {
			List<string> allAccounts = new List<string>();
			string sqlString = $"SELECT Cuenta FROM accountsLRU ORDER BY TIMESTAMPDIFF(DAY, FechaHora, CURRENT_TIMESTAMP()) ASC;";
			try
			{
				using (MySqlConnection connection = new MySqlConnection(connectionString))
				{
					try
					{
						connection.Open();
						using (MySqlCommand command = new MySqlCommand(sqlString, connection))
						using (DbDataReader reader = command.ExecuteReader())
						{
							while (reader.Read())
							{
								allAccounts.Add(reader.GetString(0));
							}
							reader.Close();
						}
					}
					finally
					{
						connection.Close();
					}
				}
			}
			catch
			{
			}
			return allAccounts;
		}

        public override IEnumerable<MovementStruct> MovementsToArchived(string tableName, int max_months_to_archive)
        {
			List<MovementStruct> allMovements = new List<MovementStruct>();
			string sqlString = $"SELECT DAY, SOURCE, MOVEMENT, AMOUNT, NEWBALANCE, NEWLOCKBALANCE, ACCOUNT_NUMBER, WHO, DOCUMENTNUMBER, REFERENCE, CONCEPT, STORE FROM {tableName} WHERE TIMESTAMPDIFF(month, DAY, CURRENT_TIMESTAMP()) >= {max_months_to_archive};";

			lock (fragmentsAndMovementsSemaphore)
			{
				using (MySqlConnection connection = new MySqlConnection(connectionString))
				{
					try
					{
						connection.Open();
						using (MySqlCommand command = new MySqlCommand(sqlString, connection))
						using (DbDataReader reader = command.ExecuteReader())
						{
							while (reader.Read())
							{
								int index = 0;
								MovementStruct movement = new MovementStruct();
								movement.DAY = reader.GetDateTime(index++);
								movement.SOURCE = reader.GetInt32(index++);

								string movementTxt = reader.GetString(index++);
								int posicion = LongTermControllerUtils.posicionDelMovement(movementTxt);
								movement.MOVEMENT = posicion;

								movement.AMOUNT = reader.GetInt32(index++);
								movement.NEWBALANCE = reader.GetDecimal(index++);
								movement.NEWLOCKBALANCE = reader.GetDecimal(index++);
								movement.ACCOUNT_NUMBER = reader.GetString(index++);
								movement.WHO = reader.GetInt32(index++);
								movement.DOCUMENTNUMBER = reader.GetString(index++);
								movement.REFERENCE = reader.GetString(index++);
								movement.CONCEPT = reader.GetString(index++);
								movement.STORE = reader.GetInt32(index++);
								allMovements.Add(movement);
							}
							reader.Close();
						}
					}
					finally
					{
						connection.Close();
					}
				}
			}

			return allMovements;
		}

        public override void MarkArchivedAuthFragmentsInDB(string tableName)
        {
			string updateQuery = $"UPDATE {tableName} SET ARCHIVED = 1 WHERE CURRENT_TIMESTAMP() >= USELESS;";

			using (MySqlConnection connection = new MySqlConnection(connectionString))
			{
				try
				{
					connection.Open();
					using (MySqlCommand command = new MySqlCommand(updateQuery, connection))
					{
						command.CommandType = CommandType.Text;
						command.ExecuteNonQuery();
					}
				}
				finally
				{
					connection.Close();
				}
			}
		}

        public override void MarkArchivedMovements(string tableName, int max_months_to_archive)
        {
			StringBuilder updateQuery = new StringBuilder();
			updateQuery.Append($"UPDATE {tableName} SET ARCHIVED = 1 WHERE TIMESTAMPDIFF(month, DAY, CURRENT_TIMESTAMP()) >= {max_months_to_archive};");

			using (MySqlConnection connection = new MySqlConnection(connectionString))
			{
				try
				{
					connection.Open();
					using (MySqlCommand command = new MySqlCommand(updateQuery.ToString(), connection))
					{
						command.CommandType = CommandType.Text;
						command.ExecuteNonQuery();
					}
				}
				finally
				{
					connection.Close();
				}
			}
		}

		internal static string CreateMovementsStorageQuery(string tableName, Currencies.CODES currencyCode, bool isAddress = true)
		{
			StringBuilder statement = new StringBuilder();
			if (isAddress)
			{
				tableName = GetTableName(tableName, Coinage.Coin(currencyCode));
			}
			else
			{
				tableName = tableName.ToLower();
			}

			var strMovements = string.Join(",", Enum.GetNames(typeof(Movement.type)).Select(x => $"'{x.ToString()}'").ToArray());

			statement
				.Append("CREATE TABLE IF NOT EXISTS ").Append(tableName)
				.Append("(")
				.Append("DAY DATETIME(3) NOT NULL,")
				.Append("SOURCE TINYINT NOT NULL,")
				.Append($"MOVEMENT ENUM({strMovements}) NOT NULL,")
				.Append("AMOUNT DECIMAL(16,8) NOT NULL,")
				.Append("NEWBALANCE DECIMAL(16,8) NOT NULL,")
				.Append("NEWLOCKBALANCE DECIMAL(16,8) NOT NULL,")
				.Append("ACCOUNT_NUMBER VARCHAR(60) NOT NULL,")
				.Append("WHO BIGINT,")
				.Append("DOCUMENTNUMBER VARCHAR(45) NOT NULL,")
				.Append("REFERENCE VARCHAR(30) NULL,")
				.Append("CONCEPT TEXT,")
				.Append("STORE TINYINT NOT NULL,")
                .Append("INDEX idx_store_movement (STORE, MOVEMENT),")
                .Append("INDEX idx_documentnumber (DOCUMENTNUMBER),")
                //.Append("ARCHIVED TINYINT NOT NULL DEFAULT 0,")
                .Append($"INDEX {tableName}_DAY (DAY ASC)")
				.Append(");");

			return statement.ToString();
		}
        internal override string CreateMovementsStorage(string tableName, Currencies.CODES currencyCode, bool isAddress = true)
        {
			return CreateMovementsStorageQuery(tableName, currencyCode, isAddress);
		}
    }

	public class MovementStorageMemory : MovementStorage
	{
		private readonly List<Movement> allMovementsDB = new List<Movement>();
		private readonly Dictionary<string, List<Movement>> movementsDB = new Dictionary<string, List<Movement>>();
		private readonly Dictionary<string, List<AuthorizationBD>> authorizationByAccountNumber = new Dictionary<string, List<AuthorizationBD>>(StringComparer.OrdinalIgnoreCase);
		private readonly List<GamesEngine.Finance.User> usersDb = new List<User>();
		public MovementStorageMemory() : base("")
		{
			autorizations = 0;
		}

		protected override bool ExistsTable(string tableName)
		{
			bool result = movementsDB.ContainsKey(tableName);
			return result;
		}

		internal override MovementsFiltered ListMovement(int sourceNumber, string atAddress, int storeId, Coin currencyCode, DateTime startDate, DateTime endDate, int whoId, int initialIndex, int amountOfRows, 
			int movementTypeId, string accountNumber, int paymentProcessorId)
		{
			if (startDate.Minute != 0 || startDate.Second != 0) throw new GameEngineException($"{nameof(startDate)} {startDate} is not valid");
			if (endDate.Minute != 0 || endDate.Second != 0) throw new GameEngineException($"{nameof(endDate)} {endDate} is not valid");

			List<Movement> movements = new List<Movement>();
			int countMovements = 0;
			string tableName = string.Empty;
			var isGeneral = atAddress == ATADDRESS_GENERAL;
			if (isGeneral)
			{
				tableName = GetTableName(currencyCode);
			}
			else
			{
				tableName = GetTableName(atAddress, currencyCode);
			}

			List<Movement> allMovements = movementsDB[tableName].ToList();
			countMovements = allMovements.Count;
			if (whoId == Users.NO_USER)
			{
				var atAddressStd = atAddress.ToLower().Trim();
				foreach (var movement in allMovements)
				{
					if (movement.AtAddress.ToLower().Trim() == atAddressStd &&
						(movement.Source == sourceNumber || sourceNumber == Sources.NO_SOURCE_ID) &&
						movement.StoreId == storeId &&
						((movementTypeId == Movement.ALL_MOVEMENT_TYPE && (movement.Type == Movement.type.Credit || movement.Type == Movement.type.Debit)) || (int)movement.Type == movementTypeId) &&
						movement.Day.Date >= startDate && movement.Day.Date <= endDate
						)
						movements.Add(movement);
				}
			}
			else
			{
				User userObj = (from user in usersDb
								where user.Id == whoId
								select user).DefaultIfEmpty().First();

				foreach (var movement in allMovements)
				{
					if (movement.AtAddress == atAddress &&
						(movement.Source == sourceNumber || sourceNumber == Sources.NO_SOURCE_ID) &&
						movement.StoreId == storeId &&
						((movementTypeId == Movement.ALL_MOVEMENT_TYPE && (movement.Type == Movement.type.Credit || movement.Type == Movement.type.Debit)) || (int)movement.Type == movementTypeId) &&
						movement.Who == userObj.Name &&
						movement.Day.Date >= startDate && movement.Day.Date <= endDate
						)
						movements.Add(movement);
				}
			}

			movements.Reverse();

			if (initialIndex >= movements.Count) return new MovementsFiltered(movements, countMovements);
			if (initialIndex + amountOfRows > movements.Count) amountOfRows = movements.Count - initialIndex;
			return new MovementsFiltered(movements.GetRange(initialIndex, amountOfRows), countMovements);
		}


        internal override bool NeedsToRemoveExpiredFragments(string aNameAuthFragments, DateTime now)
        {
            throw new NotImplementedException();
        }

        internal override void RemoveExpiredFragments(string aNameAuthFragments, DateTime now)
        {
            throw new NotImplementedException();
        }


        internal override MovementsFiltered ListMovement(int sourceNumber, string atAddress, int storeId, Coin currencyCode, DateTime startDate, DateTime endDate, int whoId, int initialIndex, int amountOfRows)
		{
			if (startDate.Minute != 0 || startDate.Second != 0) throw new GameEngineException($"{nameof(startDate)} {startDate} is not valid");
			if (endDate.Minute != 0 || endDate.Second != 0) throw new GameEngineException($"{nameof(endDate)} {endDate} is not valid");

			List<Movement> movements = new List<Movement>();
			int countMovements = 0;
			string tableName = GetTableName(atAddress, currencyCode);

			List<Movement> allMovements = movementsDB[tableName].ToList();
			countMovements = allMovements.Count;
			if (whoId == Users.NO_USER)
			{
				var atAddressStd = atAddress.ToLower().Trim();
				foreach (var movement in allMovements)
				{
					if (movement.AtAddress.ToLower().Trim() == atAddressStd &&
						(movement.Source == sourceNumber || sourceNumber == Sources.NO_SOURCE_ID) &&
						movement.StoreId == storeId &&
						(movement.Type == Movement.type.Credit || movement.Type == Movement.type.Debit) &&
						movement.Day.Date >= startDate && movement.Day.Date <= endDate
						)
						movements.Add(movement);
				}
			}
			else
			{
				User userObj = (from user in usersDb
								where user.Id == whoId
								select user).DefaultIfEmpty().First();

				foreach (var movement in allMovements)
				{
					if (movement.AtAddress == atAddress &&
						(movement.Source == sourceNumber || sourceNumber == Sources.NO_SOURCE_ID) &&
						movement.StoreId == storeId &&
						(movement.Type == Movement.type.Credit || movement.Type == Movement.type.Debit) &&
						movement.Who == userObj.Name &&
						movement.Day.Date >= startDate && movement.Day.Date <= endDate
						)
						movements.Add(movement);
				}
			}

			movements.Reverse();

			if (initialIndex >= movements.Count) return new MovementsFiltered(movements, countMovements);
			if (initialIndex + amountOfRows > movements.Count) amountOfRows = movements.Count - initialIndex;
			return new MovementsFiltered(movements.GetRange(initialIndex, amountOfRows), countMovements);
		}

		public override int InsertUserIfNotExists(int storeId, string userName)
		{
			User userObj = (from user in usersDb
							where user.Name == userName && user.StoreId == storeId
							select user).DefaultIfEmpty().First();

			if (userObj == null)
			{
				userObj = new User(storeId, usersDb.Count() + 1, userName);

				usersDb.Add(userObj);
			}
			return userObj.Id;
		}

		public override void CreateUserMovementsTableIfNotExists()
		{

		}

		internal override IEnumerable<User> ListUsers(int storeId)
		{
			return usersDb;
		}

		private void FragmentInserts(MovementsCollector movements)
		{
			var fragmentsToAdd = movements.FragmentsToAdd();
			var firstFragment = fragmentsToAdd.ElementAt(0);
			int authorizationNumber = firstFragment.Authorization.Number;
			var atAddressNumber = firstFragment.Authorization.AtAddress.Number.ToLower().Trim();

			if (!authorizationByAccountNumber.ContainsKey(atAddressNumber))
			{
				List<AuthorizationBD> authorizations = new List<AuthorizationBD>();
				AuthorizationBD authorizationBD = new AuthorizationBD(authorizationNumber, firstFragment.Account);
				authorizations.Add(authorizationBD);
				authorizationByAccountNumber.Add(atAddressNumber, authorizations);
				authorizationBD.Add(fragmentsToAdd);
			}
			else
			{
				List<AuthorizationBD> authorizations = authorizationByAccountNumber[atAddressNumber];

				foreach (AuthorizationBD authorizationBD in authorizations)
				{
					if (authorizationBD.HasThisNumber(authorizationNumber))
					{
						authorizationBD.Add(fragmentsToAdd);
						return;
					}
				}

				AuthorizationBD newAuthorizationBD = new AuthorizationBD(authorizationNumber, firstFragment.Account);
				newAuthorizationBD.Add(fragmentsToAdd);
				authorizations.Add(newAuthorizationBD);
			}
		}

		public override void SaveBalanceMovements(bool itIsThePresent, int whosId, IEnumerable<Movement> movements, DataTable movementTableByAccount, DataTable movementTableByCurrency)
		{
			if (!itIsThePresent) return;

			foreach (Movement movement in movements)
			{
				if (whosId != Users.NO_USER)
				{
					InsertUserIfNotExists(movement.StoreId, movement.Who);
				}

				string tableName = GetTableName(movement.AtAddress, movement.Currency);
				if (!movementsDB.ContainsKey(tableName)) throw new GameEngineException($"{nameof(tableName)} '{tableName}' does not exist in {nameof(movementsDB)}");
				movementsDB[tableName].Add(movement);
				tableName = GetTableName(movement.Currency);
				if (!movementsDB.ContainsKey(tableName)) throw new GameEngineException($"{nameof(tableName)} '{tableName}' does not exist in {nameof(movementsDB)}");
				movementsDB[tableName].Add(movement);
				allMovementsDB.Add(movement);
			}
		}

		internal override void SavePayedFragment(bool itIsThePresent, MovementsCollector movements)
		{
			FragmentInserts(movements);
		}

		internal override void CreateFragmentIfNotExists(string number)
		{

		}

		internal override void CreateFragmentReasonsIfNotExists()
		{

		}

		internal override MovementsFiltered ListMovement(int inicialIndex, int amountOfRows, string atddress, int storeId, DateTime initDate, DateTime endDate, Coin currencyCode)
		{
			List<Movement> movements = new List<Movement>();

			string tableName1 = GetTableName(currencyCode);

			List<Movement> allMovements =
				movementsDB[tableName1].
				ToList();
			int countMovements = allMovements.Count;
			movements = (from movement in allMovements
						 where movement.AtAddress == atddress && movement.StoreId == storeId && movement.Day >= initDate && movement.Day <= endDate
						 select movement).
										ToList();

			movements.Reverse();

			if (inicialIndex >= movements.Count) return new MovementsFiltered(null, countMovements);
			if (inicialIndex + amountOfRows > movements.Count) amountOfRows = movements.Count - inicialIndex;
			return new MovementsFiltered(movements.GetRange(inicialIndex, amountOfRows), countMovements);
		}

		internal override MovementsReport MovementsReportFilteredBy(string atAddress, Coin currencyCode, int storeId, DateTime startDate, DateTime endDate, int initialIndex, int amountOfRows, int documentNumber, int referenceNumber)
		{
			if (startDate.Minute != 0 || startDate.Second != 0) throw new GameEngineException($"{nameof(startDate)} {startDate} is not valid");
			if (endDate.Minute != 0 || endDate.Second != 0) throw new GameEngineException($"{nameof(endDate)} {endDate} is not valid");

			var movements = new List<MovementRecord>();
			int countMovements = 0;
			string tableName = string.Empty;
			var isGeneral = atAddress == ATADDRESS_GENERAL;
			if (isGeneral)
			{
				tableName = GetTableName(currencyCode);
			}
			else
			{
				tableName = GetTableName(atAddress, currencyCode);
			}

			List<Movement> allMovements = movementsDB[tableName].ToList();
			countMovements = allMovements.Count;

			foreach (var currentMovement in allMovements)
			{
				bool isPending = true;
				foreach (var m in allMovements)
				{
					if (currentMovement.DocumentNumber == m.DocumentNumber &&
						m.Type == Movement.type.Debit &&
						m.StoreId == storeId && m.Day >= startDate && m.Day <= endDate)
					{
						isPending = false;
						break;
					}
				}
				if (currentMovement.Type == Movement.type.Lock &&
					currentMovement.StoreId == storeId &&
					(currentMovement.DocumentNumber == documentNumber.ToString() || documentNumber == DOCUMENT_NUMBER_FOR_ALL) &&
					currentMovement.Day >= startDate && currentMovement.Day <= endDate)
				{
					movements.Add(new MovementRecord(
						currencyCode,
						currentMovement.AtAddress,
						currentMovement.DocumentNumber,
						currentMovement.Day,
						isPending,
						currentMovement.CurrentAmount,
						currentMovement.NewBalance,
						currentMovement.Type,
						currentMovement.ProcessorId,
						currentMovement.Reference
						));
				}
			}

			if (initialIndex + amountOfRows > movements.Count)
			{
				amountOfRows = movements.Count - initialIndex;
			}
			var movementsInRange = movements.GetRange(initialIndex, amountOfRows);
			return new MovementsReport(movementsInRange, countMovements);
		}

		internal override string AtAddressFrom(int documentNumber)
		{
			var fragmentsInfo = new List<FragmentInfo>();
			int countMovements = 0;
			string tableName = GetTableName(Coinage.Coin(Currencies.CODES.LR));

			var movementDetails = new List<MovementDetailRecord>();
			List<Movement> allMovements = movementsDB[tableName].ToList();
			countMovements = allMovements.Count;
			string atAddress = string.Empty;
			foreach (var currentMovement in allMovements)
			{
				if (currentMovement.DocumentNumber == documentNumber.ToString() &&
					currentMovement.Type == Movement.type.Lock &&
					currentMovement.StoreId == Store.STORES_SEQUENCE_LOTTO)
				{
					return currentMovement.AtAddress;
				}
			}
			throw new GameEngineException($"There is no {nameof(AtAddress)} for {nameof(documentNumber)} '{documentNumber}'");
		}

		internal override MovementsReport MovementsFullDetailReportBy(AtAddress atAddress, Coin currencyCode, int storeId, DateTime startDate, DateTime endDate, int initialIndex, int amountOfRows)
		{
			if (startDate.Minute != 0 || startDate.Second != 0) throw new GameEngineException($"{nameof(startDate)} {startDate} is not valid");
			if (endDate.Minute != 0 || endDate.Second != 0) throw new GameEngineException($"{nameof(endDate)} {endDate} is not valid");

			var fragmentsInfo = new List<FragmentInfo>();
			int countMovements = 0;
			string tableName = GetTableName(atAddress.Number, currencyCode);

			List<Movement> allMovements = movementsDB[tableName].ToList();
			countMovements = allMovements.Count;

			foreach (var currentMovement in allMovements)
			{
				if (currentMovement.Type == Movement.type.Lock &&
					currentMovement.StoreId == storeId &&
					currentMovement.Day >= startDate && currentMovement.Day <= endDate)
				{
					var totalAmount = 0m;
					foreach (var m in allMovements)
					{
						if (currentMovement.DocumentNumber == m.DocumentNumber &&
							m.Type == Movement.type.Unlock &&
							m.StoreId == storeId &&
							m.Day >= startDate && m.Day <= endDate)
						{
							totalAmount += m.CurrentAmount;
						}
					}

					bool isPending = totalAmount != currentMovement.CurrentAmount;
					var authorizationNumber = int.Parse(currentMovement.DocumentNumber);
					AuthorizationBD authorizationBD = authorizationByAccountNumber[atAddress.Number.ToLower()].FirstOrDefault(x => x.HasThisNumber(authorizationNumber));

					if (authorizationBD != null)
					{
						foreach (var fragment in authorizationBD.Fragments())
						{
							var fragmentInfo = new FragmentInfo(atAddress,
								currentMovement.DocumentNumber,
								currentMovement.Day,
								currentMovement.CurrentAmount,
								currentMovement.NewBalance,
								isPending,
								fragment.Number,
								fragment.Risk,
								fragment.ToWin,
								fragment.AdjustedWinAmount,
								fragment.AdjustedLossAmount,
								fragment.Reason,
								fragment.Description
								);
							fragmentsInfo.Add(fragmentInfo);
						}
					}
					else
					{
						var fragmentInfo = new FragmentInfo(atAddress,
								currentMovement.DocumentNumber,
								currentMovement.Day,
								currentMovement.CurrentAmount,
								currentMovement.NewBalance,
								isPending,
								null,
								null,
								null,
								null,
								null,
								FragmentReason.Pending,
								string.Empty
								);
						fragmentsInfo.Add(fragmentInfo);
					}
				}
			}

			return new MovementsReport(currencyCode, fragmentsInfo, countMovements, initialIndex, amountOfRows);
		}

		internal override Authorization RetrieveFragments(int authorizationNumber, AtAddress atAddress, Purpose purpose)
		{
			AuthorizationBD authorizationBD = authorizationByAccountNumber[atAddress.Number].First(x => x.HasThisNumber(authorizationNumber));
			var totalAmount = authorizationBD.Fragments().Sum(x => x.Price.Value);
			var amount = Currency.Factory(authorizationBD.Fragments().First().Price.CurrencyCode, totalAmount);
			Authorization authorization = new Authorization(authorizationNumber, amount, authorizationBD.Account, DateTime.Now.AddDays(40));
			int lowerId = authorizationBD.Fragments().Min(x => x.Number);
			int higherId = authorizationBD.Fragments().Max(x => x.Number);
			var storedFragmentsInfo = authorizationBD.Fragments().Select(x => new PartialFragment(x.Number, x.Status, x.Reference, x.Description, x.Risk, x.ToWin, x.Reason, x.AdjustedWinAmount, x.AdjustedLossAmount, authorization.Account.Number, DateTime.Now.AddDays(40)));
			decimal amountForEachFragment = storedFragmentsInfo.Select(x => x.Risk).First();
			authorization.CreateFragments(lowerId, higherId, amountForEachFragment, storedFragmentsInfo);
			return authorization;
		}

		internal override MovementDetailReport MovementDetailReportBy(AtAddress atAddress, int authorizationNumber, Coin currencyCode, int initialIndex, int amountOfRows)
		{
			AuthorizationBD authorizationBD = authorizationByAccountNumber[atAddress.Number].FirstOrDefault(x => x.HasThisNumber(authorizationNumber));

			var movementDetails = new List<MovementDetailRecord>();
			if (authorizationBD != null)
			{
				foreach (var fragment in authorizationBD.Fragments())
				{
					movementDetails.Add(new MovementDetailRecord(currencyCode, authorizationNumber, fragment.Number, fragment.Risk, fragment.ToWin, fragment.Description, fragment.Reason, fragment.AdjustedWinAmount, fragment.AdjustedLossAmount));
				}
			}

			if (initialIndex + amountOfRows > movementDetails.Count)
			{
				amountOfRows = movementDetails.Count - initialIndex;
			}
			var movementDetailInRange = movementDetails.GetRange(initialIndex, amountOfRows);
			return new MovementDetailReport(currencyCode, movementDetailInRange, movementDetails.Count, atAddress, authorizationNumber, initialIndex, amountOfRows);
		}

		public override void CreateAuthorizationSequenceIfNotExists()
		{

		}

		private static int autorizations;
		public override int NextAuthorizationNumber()
		{
			if (autorizations == int.MaxValue) autorizations = 0;
			autorizations = autorizations + 1;
			int result = autorizations;
			return result;
		}

		protected override void CreateMovementsTableForThisAccountAndCurrency(string atAddress, Coin currencyCode)
		{
			string tableName = GetTableName(atAddress, currencyCode);
			movementsDB.Add(tableName, new List<Movement>());
		}

		internal override string CreateMovementsStorage(string atAddress, Coin currencyCode)
		{
			throw new NotImplementedException();
		}

		protected override void CreateMovementsTableForThisCurrency(string currencyCode)
		{
			string tableName = GetTableName(currencyCode);
			movementsDB.Add(tableName, new List<Movement>());
		}

		internal override string CreateMovementsStorage(string currencyCode)
		{
			throw new NotImplementedException();
		}

		protected override void CreateFragmentsTableForThisAccount(string atAddressNumber)
		{

		}

		internal override string CreateFragmentsStorage(string atAddress)
		{
			throw new NotImplementedException();
		}

		public override void DeleteAuthFragments(string atAddress)
        {
            throw new NotImplementedException();
        }

        public override void DeleteMovements(string atAddress, Currencies.CODES currencyCode)
        {
            throw new NotImplementedException();
        }

        public override IEnumerable<string> AllCashierAccounts()
        {
            throw new NotImplementedException();
        }

        public override IEnumerable<AuthorizationFragmentStruct> AuthFragmentsToArchived(string tableName)
        {
            throw new NotImplementedException();
        }

        public override IEnumerable<MovementStruct> MovementsToArchived(string tableName, int max_months_to_archive)
        {
            throw new NotImplementedException();
        }

        public override void MarkArchivedAuthFragmentsInDB(string tableName)
        {
            throw new NotImplementedException();
        }

        public override void MarkArchivedMovements(string tableName, int max_months_to_archive)
        {
            throw new NotImplementedException();
        }

        internal override string CreateFragmentsStorage(string atAddress, bool subfixRequired = true)
        {
            throw new NotImplementedException();
        }

        internal override string CreateMovementsStorage(string tableName, Currencies.CODES currencyCode, bool isAddress = true)
        {
            throw new NotImplementedException();
        }

        class AuthorizationBD
		{
			private List<AuthorizationFragment> fragments = new List<AuthorizationFragment>();
			private int authorizationNumber;

			public AccountWithBalance Account { get; }

			internal AuthorizationBD(int authorizationNumber, AccountWithBalance account)
			{
				this.authorizationNumber = authorizationNumber;
				Account = account;
			}

			internal void Add(IEnumerable<AuthorizationFragment> fragmentsGroup)
			{
				fragments.AddRange(fragmentsGroup);
			}

			internal bool HasThisNumber(int authorizationNumber)
			{
				return this.authorizationNumber == authorizationNumber;
			}

			internal IEnumerable<AuthorizationFragment> Fragments()
			{
				return fragments;
			}
		}
	}

	public class MovementStorageSQLServer : MovementStorage
	{
		public MovementStorageSQLServer(string connectionString) : base(connectionString)
		{
			CreateFragmentReasonsIfNotExists();
		}

		private void ExecuteCommand(string cmdText)
		{
			try
			{
				using (SqlConnection connection = new SqlConnection(connectionString))
				{
					try
					{
						connection.Open();
						using (SqlCommand command = new SqlCommand(cmdText, connection))
						{
							command.ExecuteNonQuery();
						}
					}
					catch (Exception e)
					{
						Loggers.GetIntance().Db.Error($@"sql:{cmdText}", e);
						ErrorsSender.Send(e, cmdText);
						throw e;
					}
					finally
					{
						connection.Close();
					}
				}
			}
			catch (Exception e)
			{
				Loggers.GetIntance().Db.Error($@"sql:{cmdText}", e);
				ErrorsSender.Send(e, $@"sql:{cmdText}");
				throw e;
			}
		}

        internal override bool NeedsToRemoveExpiredFragments(string aNameAuthFragments, DateTime now)
        {
            throw new NotImplementedException();
        }

        internal override void RemoveExpiredFragments(string aNameAuthFragments, DateTime now)
        {
            throw new NotImplementedException();
        }

        protected override bool ExistsTable(string tableName)
		{
			bool exists = true;
			StringBuilder statement = new StringBuilder();
			statement.Append("IF EXISTS(")
				.Append("SELECT 1 FROM INFORMATION_SCHEMA.TABLES ")
				.Append($"WHERE TABLE_NAME = '{ tableName }')")
				.Append("SELECT 1 ELSE SELECT 0;");
			string sql = statement.ToString();

			using (SqlConnection connection = new SqlConnection(connectionString))
			{
				try
				{
					connection.Open();
					using (SqlCommand command = new SqlCommand(sql, connection))
					{
						var resultado = (int)command.ExecuteScalar();
						exists = resultado == 1;
					}
				}
				catch
				{
					exists = false;
				}
				finally
				{
					connection.Close();
				}
			}

			return exists;
		}

		internal override MovementsFiltered ListMovement(int sourceNumber, string atAddress, int storeId, Coin currencyCode, DateTime startDate, DateTime endDate, int whoId, int initialIndex, int amountOfRows, 
			int movementTypeId, string accountNumberToFilter, int paymentProcessorId)
		{
			if (startDate.Minute != 0 || startDate.Second != 0) throw new GameEngineException($"{nameof(startDate)} {startDate} is not valid");
			if (endDate.Minute != 0 || endDate.Second != 0) throw new GameEngineException($"{nameof(endDate)} {endDate} is not valid");

			List<Movement> movements = new List<Movement>();
			int countMovements = 0;
			string sql = string.Empty;

			try
			{
				string whereForWhoId = (whoId == Users.NO_USER) ? string.Empty : $@" AND u.ID = {whoId} ";
				string whereForSource = (sourceNumber == Sources.NO_SOURCE_ID) ? string.Empty : $@" AND SOURCE = {sourceNumber} ";
				string whereForAccountNumber = string.IsNullOrWhiteSpace(accountNumberToFilter) ? string.Empty : $@" AND ACCOUNT_NUMBER = '{accountNumberToFilter}' ";
				string whereForPaymentProcessor = (paymentProcessorId == WholePaymentProcessor.NoPaymentProcessor) ? string.Empty : $" AND PROCESSORID = {paymentProcessorId} ";
				string whereForMovementType = movementTypeId == Movement.ALL_MOVEMENT_TYPE ?
					$"(MOVEMENT = '{(int)Movement.type.Lock}' OR MOVEMENT = '{(int)Movement.type.Credit}' OR (MOVEMENT = '{(int)Movement.type.Debit}' AND CONVERT(VARCHAR,CONCEPT) <> ''))" :
					$"MOVEMENT = '{movementTypeId}'";
				var isGeneral = atAddress == ATADDRESS_GENERAL;
				if (isGeneral)
				{
					var from = GetTableName(currencyCode);
					sql = $@"
						SELECT DAY, SOURCE, MOVEMENT, AMOUNT, NEWBALANCE, DOCUMENTNUMBER, u.NAME, m.STORE, ISNULL(CONCEPT, '') as CONCEPT, ISNULL(REFERENCE, '') as REFERENCE, COUNT(*) OVER(), ATADDRESS, NEWLOCKBALANCE, ACCOUNT_NUMBER, PROCESSORID
						FROM {from} m
						LEFT JOIN movements_users u ON u.ID = m.WHO
						WHERE m.STORE = {storeId} AND 
                            {whereForMovementType} AND 
                            (DAY BETWEEN '{ToDateString(startDate)}' AND '{ToDateString(endDate.AddDays(1))}')  
                            {whereForWhoId} {whereForSource} {whereForAccountNumber} {whereForPaymentProcessor}
						ORDER BY DAY
						OFFSET {initialIndex} ROWS
						FETCH NEXT {amountOfRows} ROWS ONLY
					";

					using (SqlConnection connection = new SqlConnection(connectionString))
					{
						try
						{
							connection.Open();
							using (SqlCommand command = new SqlCommand(sql, connection))
							{
								var dataReader = command.ExecuteReader();

								while (dataReader.Read())
								{
									DateTime day = dataReader.GetDateTime(0);
									int sourceAsInt = dataReader.GetByte(1);
									string movement = dataReader.GetString(2);
									decimal amount = dataReader.GetDecimal(3);
									decimal newBalance = dataReader.GetDecimal(4);
									string documentNumber = dataReader.GetString(5);
									string who = Users.None;
									if (!dataReader.IsDBNull(6))
									{
										who = dataReader.GetString(6);
									}
									storeId = dataReader.GetByte(7);
									string concept = dataReader.GetString(8).Replace("\n", " ").Replace("\r", " ");
									string reference = dataReader.GetString(9);
									countMovements = dataReader.GetInt32(10);
									string atAddressNumber = dataReader.GetString(11);

									decimal newLock = dataReader.GetDecimal(12);
									string accountNumber = dataReader.GetString(13);
									int processorId = dataReader.GetInt32(14);

									Movement.type type;
									bool result = Enum.TryParse(movement, true, out type);
									if (!result)
									{
										throw new GameEngineException($@"There is no Movement.type for type {movement}");
									}

									Currency currency = Currency.Factory(currencyCode.Iso4217Code, amount);
									Movement m = null;

									var isDebit = ItsADebitMovement(type);
									if (isDebit)
									{
										m = Movement.GenerateADebitMovement(sourceAsInt, atAddressNumber, day, type, currency, newBalance, who, documentNumber, storeId, concept, reference, newLock, accountNumber, processorId);
									}
									else
									{
										m = Movement.GenerateACreditMovement(sourceAsInt, atAddressNumber, day, type, currency, newBalance, who, documentNumber, storeId, concept, reference, newLock, accountNumber, processorId);
									}

									movements.Add(m);
								}

								dataReader.Close();
							}
						}
						finally
						{
							connection.Close();
						}
					}
				}
				else
				{
					var from = GetTableName(atAddress, currencyCode);
					sql = $@"
						SELECT DAY, SOURCE, MOVEMENT, AMOUNT, NEWBALANCE, DOCUMENTNUMBER, u.NAME, m.STORE, ISNULL(CONCEPT, '') as CONCEPT, ISNULL(REFERENCE, '') as REFERENCE, COUNT(*) OVER(), NEWLOCKBALANCE, ACCOUNT_NUMBER, PROCESSORID
						FROM {from} m
						LEFT JOIN movements_users u ON u.ID = m.WHO
						WHERE m.STORE = {storeId} AND 
                            {whereForMovementType} AND 
                            (DAY BETWEEN '{ToDateString(startDate)}' AND '{ToDateString(endDate.AddDays(1))}') 
                            {whereForWhoId} {whereForSource} {whereForAccountNumber}
						ORDER BY DAY
						OFFSET {initialIndex} ROWS
						FETCH NEXT {amountOfRows} ROWS ONLY
					";

					using (SqlConnection connection = new SqlConnection(connectionString))
					{
						try
						{
							connection.Open();
							using (SqlCommand command = new SqlCommand(sql, connection))
							{
								var dataReader = command.ExecuteReader();

								while (dataReader.Read())
								{
									DateTime day = dataReader.GetDateTime(0);
									int sourceAsInt = dataReader.GetByte(1);
									string movement = dataReader.GetString(2);
									decimal amount = dataReader.GetDecimal(3);
									decimal newBalance = dataReader.GetDecimal(4);
									string documentNumber = dataReader.GetString(5);
									string who = Users.None;
									if (!dataReader.IsDBNull(6))
									{
										who = dataReader.GetString(6);
									}
									storeId = dataReader.GetByte(7);
									string concept = dataReader.GetString(8).Replace("\n", " ").Replace("\r", " ");
									string reference = dataReader.GetString(9);
									countMovements = dataReader.GetInt32(10);

									decimal newLock = dataReader.GetDecimal(11);
									string accountNumber = dataReader.GetString(12);
									int processorId = dataReader.GetInt32(13);

									Movement.type type;
									bool result = Enum.TryParse(movement, true, out type);
									if (!result)
									{
										throw new GameEngineException($@"There is no Movement.type for type {movement}");
									}

									Currency currency = Currency.Factory(currencyCode.Iso4217Code, amount);
									Movement m = null;

									var isDebit = ItsADebitMovement(type);
									if (isDebit)
									{
										m = Movement.GenerateADebitMovement(sourceAsInt, atAddress, day, type, currency, newBalance, who, documentNumber, storeId, concept, reference, newLock, accountNumber, processorId);
									}
									else
									{
										m = Movement.GenerateACreditMovement(sourceAsInt, atAddress, day, type, currency, newBalance, who, documentNumber, storeId, concept, reference, newLock, accountNumber, processorId);
									}

									movements.Add(m);
								}

								dataReader.Close();
							}
						}
						finally
						{
							connection.Close();
						}
					}
				}
			}
			catch (Exception e)
			{
				Loggers.GetIntance().Db.Error($@"sql:{sql}", e);
				ErrorsSender.Send(e, sql);
				throw;
			}
			return new MovementsFiltered(movements, countMovements);
		}

		internal override MovementsFiltered ListMovement(int sourceNumber, string atAddress, int storeId, Coin currencyCode, DateTime startDate, DateTime endDate, int whoId, int initialIndex, int amountOfRows)
		{
			if (startDate.Minute != 0 || startDate.Second != 0) throw new GameEngineException($"{nameof(startDate)} {startDate} is not valid");
			if (endDate.Minute != 0 || endDate.Second != 0) throw new GameEngineException($"{nameof(endDate)} {endDate} is not valid");

			List<Movement> movements = new List<Movement>();
			int countMovements = 0;
			string sql = string.Empty;

			try
			{
				string whereForWhoId = (whoId == Users.NO_USER) ? string.Empty : $@" AND u.ID = {whoId} ";
				string whereForSource = (sourceNumber == Sources.NO_SOURCE_ID) ? string.Empty : $@" AND SOURCE = {sourceNumber} ";
				string whereForMovementType = $"(MOVEMENT = '{(int)Movement.type.Lock}' OR MOVEMENT = '{(int)Movement.type.Credit}')";

				var from = GetTableName(atAddress, currencyCode);
				sql = $@"
					SELECT DAY, SOURCE, MOVEMENT, AMOUNT, NEWBALANCE, DOCUMENTNUMBER, u.NAME, m.STORE, ISNULL(CONCEPT, '') as CONCEPT, ISNULL(REFERENCE, '') as REFERENCE, COUNT(*) OVER(), NEWLOCKBALANCE, ACCOUNT_NUMBER, PROCESSORID
					FROM {from} m
					LEFT JOIN movements_users u ON u.ID = m.WHO
					WHERE m.STORE = {storeId} AND 
                        {whereForMovementType} AND 
                        (DAY BETWEEN '{ToDateString(startDate)}' AND '{ToDateString(endDate.AddDays(1))}') 
                        {whereForWhoId} {whereForSource}
					ORDER BY DAY DESC
					OFFSET {initialIndex} ROWS
					FETCH NEXT {amountOfRows} ROWS ONLY
				";

				using (SqlConnection connection = new SqlConnection(connectionString))
				{
					try
					{
						connection.Open();
						using (SqlCommand command = new SqlCommand(sql, connection))
						{
							var dataReader = command.ExecuteReader();

							while (dataReader.Read())
							{
								DateTime day = dataReader.GetDateTime(0);
								int sourceAsInt = dataReader.GetByte(1);
								string movement = dataReader.GetString(2);
								decimal amount = dataReader.GetDecimal(3);
								decimal newBalance = dataReader.GetDecimal(4);
								string documentNumber = dataReader.GetString(5);
								string who = Users.None;
								if (!dataReader.IsDBNull(6))
								{
									who = dataReader.GetString(6);
								}
								storeId = dataReader.GetByte(7);
								string concept = dataReader.GetString(8).Replace("\n", " ").Replace("\r", " ");
								string reference = dataReader.GetString(9);
								countMovements = dataReader.GetInt32(10);

								decimal newLock = dataReader.GetDecimal(11);
								string accountNumber = dataReader.GetString(12);
								int processorId = dataReader.GetInt32(13);

								Movement.type type;
								bool result = Enum.TryParse(movement, true, out type);
								if (!result)
								{
									throw new GameEngineException($@"There is no Movement.type for type {movement}");
								}

								Currency currency = Currency.Factory(currencyCode.Iso4217Code, amount);
								Movement m = null;

								var isDebit = ItsADebitMovement(type);
								if (isDebit)
								{
									m = Movement.GenerateADebitMovement(sourceAsInt, atAddress, day, type, currency, newBalance, who, documentNumber, storeId, concept, reference, newLock, accountNumber, processorId);
								}
								else
								{
									m = Movement.GenerateACreditMovement(sourceAsInt, atAddress, day, type, currency, newBalance, who, documentNumber, storeId, concept, reference, newLock, accountNumber, processorId);
								}

								movements.Add(m);
							}

							dataReader.Close();
						}
					}
					finally
					{
						connection.Close();
					}
				}
			}
			catch (Exception e)
			{
				Loggers.GetIntance().Db.Error($@"sql:{sql}", e);
				ErrorsSender.Send(e, sql);
				throw;
			}
			return new MovementsFiltered(movements, countMovements);
		}

		internal override MovementsReport MovementsReportFilteredBy(string atAddress, Coin currencyCode, int storeId, DateTime startDate, DateTime endDate, int initialIndex, int amountOfRows, int paramDocumentNumber, int referenceNumber)
		{
			if (startDate.Minute != 0 || startDate.Second != 0) throw new GameEngineException($"{nameof(startDate)} {startDate} is not valid");
			if (endDate.Minute != 0 || endDate.Second != 0) throw new GameEngineException($"{nameof(endDate)} {endDate} is not valid");

			var movements = new List<MovementRecord>();
			int countMovements = 0;
			string sql = string.Empty;
			try
			{
				var whereForDocumentNumber = (paramDocumentNumber == DOCUMENT_NUMBER_FOR_ALL) ? string.Empty : $@" AND DOCUMENTNUMBER = {paramDocumentNumber} ";
				var whereForReferenceNumber = (referenceNumber == DOCUMENT_NUMBER_FOR_ALL) ? string.Empty : $" AND REFERENCE = {referenceNumber} ";
				var isGeneral = atAddress == ATADDRESS_GENERAL;
				if (isGeneral)
				{
					var from = GetTableName(currencyCode);
					sql = $@"
						SELECT DOCUMENTNUMBER, DAY, AMOUNT, NEWBALANCE, 
							CASE WHEN ((SELECT SUM(AMOUNT) 
								FROM {from} M2
								WHERE MOVEMENT = '{(int)Movement.type.Unlock}' AND M1.DOCUMENTNUMBER=M2.DOCUMENTNUMBER) = SUM(AMOUNT)) 
								THEN 0 ELSE 1 END AS ISPENDING, 
							COUNT(*) OVER(),
							ATADDRESS, MOVEMENT, PROCESSORID, REFERENCE 
						FROM {from} M1
						WHERE (MOVEMENT = '{(int)Movement.type.Lock}' OR MOVEMENT = '{(int)Movement.type.Credit}') AND 
                            STORE = {storeId} AND 
                            (DAY BETWEEN '{ToDateString(startDate)}' AND '{ToDateString(endDate.AddDays(1))}')
                            {whereForDocumentNumber} {whereForReferenceNumber} 
						GROUP BY DOCUMENTNUMBER, DAY, AMOUNT, NEWBALANCE, ATADDRESS, MOVEMENT, PROCESSORID, REFERENCE 
						ORDER BY DOCUMENTNUMBER
						OFFSET {initialIndex} ROWS
						FETCH NEXT {amountOfRows} ROWS ONLY
					";
					using (SqlConnection connection = new SqlConnection(connectionString))
					{
						try
						{
							connection.Open();
							using (SqlCommand command = new SqlCommand(sql, connection))
							{
								var dataReader = command.ExecuteReader();

								while (dataReader.Read())
								{
									string documentNumber = dataReader.GetString(0);
									DateTime day = dataReader.GetDateTime(1);
									decimal amount = dataReader.GetDecimal(2);
									decimal newBalance = dataReader.GetDecimal(3);
									int pending = dataReader.GetInt32(4);
									if (pending != 0 && pending != 1) throw new GameEngineException($"{nameof(pending)} value '{pending}' is not valid");
									bool isPending = pending == 1;
									countMovements = dataReader.GetInt32(5);
									string atAddressNumber = dataReader.GetString(6);
									string movement = dataReader.GetString(7);
									Movement.type type;
									bool result = Enum.TryParse(movement, true, out type);
									if (!result)
									{
										throw new GameEngineException($@"There is no Movement.type for type {movement}");
									}
									int processorId = dataReader.GetInt32(8);
									string reference = dataReader.GetString(9);

									movements.Add(new MovementRecord(currencyCode, atAddressNumber, documentNumber, day, isPending, amount, newBalance, type, processorId, reference));
								}
								dataReader.Close();
							}
						}
						finally
						{
							connection.Close();
						}
					}
				}
				else
				{
					var from = GetTableName(atAddress, currencyCode);
					sql = $@"
						SELECT DOCUMENTNUMBER, DAY, AMOUNT, NEWBALANCE, 
							CASE WHEN ((SELECT SUM(AMOUNT) 
								FROM {from} M2
								WHERE MOVEMENT = '{(int)Movement.type.Unlock}' AND M1.DOCUMENTNUMBER=M2.DOCUMENTNUMBER) = SUM(AMOUNT)) 
								THEN 0 ELSE 1 END AS ISPENDING, 
							COUNT(*) OVER(), 
							MOVEMENT, PROCESSORID, REFERENCE 
						FROM {from} M1
						WHERE (MOVEMENT = '{(int)Movement.type.Lock}' OR MOVEMENT = '{(int)Movement.type.Credit}') AND 
                            STORE = {storeId} AND 
                            (DAY BETWEEN '{ToDateString(startDate)}' AND '{ToDateString(endDate.AddDays(1))}')
                            {whereForDocumentNumber} {whereForReferenceNumber} 
						GROUP BY DOCUMENTNUMBER, DAY, AMOUNT, NEWBALANCE, MOVEMENT, PROCESSORID, REFERENCE 
						ORDER BY DOCUMENTNUMBER
						OFFSET {initialIndex} ROWS
						FETCH NEXT {amountOfRows} ROWS ONLY
					";
					using (SqlConnection connection = new SqlConnection(connectionString))
					{
						try
						{
							connection.Open();
							using (SqlCommand command = new SqlCommand(sql, connection))
							{
								var dataReader = command.ExecuteReader();

								while (dataReader.Read())
								{
									string documentNumber = dataReader.GetString(0);
									DateTime day = dataReader.GetDateTime(1);
									decimal amount = dataReader.GetDecimal(2);
									decimal newBalance = dataReader.GetDecimal(3);
									int pending = dataReader.GetInt32(4);
									if (pending != 0 && pending != 1) throw new GameEngineException($"{nameof(pending)} value '{pending}' is not valid");
									bool isPending = pending == 1;
									countMovements = dataReader.GetInt32(5);
									string movement = dataReader.GetString(6);
									Movement.type type;
									bool result = Enum.TryParse(movement, true, out type);
									if (!result)
									{
										throw new GameEngineException($@"There is no Movement.type for type {movement}");
									}
									int processorId = dataReader.GetInt32(7);
									string reference = dataReader.GetString(8);

									movements.Add(new MovementRecord(currencyCode, atAddress, documentNumber, day, isPending, amount, newBalance, type, processorId, reference));
								}
								dataReader.Close();
							}
						}
						finally
						{
							connection.Close();
						}
					}
				}
			}
			catch (Exception e)
			{
				Loggers.GetIntance().Db.Error($@"sql:{sql}", e);
				ErrorsSender.Send(e, sql);
				throw;
			}
			return new MovementsReport(movements, countMovements);
		}

		internal override string AtAddressFrom(int documentNumber)
		{
			string atAddress = string.Empty;
			string sql = string.Empty;
			try
			{
				var from = GetTableName(Coinage.Coin( Currencies.CODES.LR));
				sql = $@"SELECT ATADDRESS
						FROM {from}
						WHERE MOVEMENT = {(int)Movement.type.Lock} AND 
							STORE = {Store.STORES_SEQUENCE_LOTTO} AND 
							DOCUMENTNUMBER = {documentNumber}
				";
				using (SqlConnection connection = new SqlConnection(connectionString))
				{
					try
					{
						connection.Open();
						using (SqlCommand command = new SqlCommand(sql, connection))
						{
							var dataReader = command.ExecuteReader();

							while (dataReader.Read())
							{
								atAddress = dataReader.GetString(0);
							}
							dataReader.Close();
						}
					}
					finally
					{
						connection.Close();
					}
				}
			}
			catch (Exception e)
			{
				Loggers.GetIntance().Db.Error($@"sql:{sql}", e);
				ErrorsSender.Send(e, sql);
				throw;
			}
			return atAddress;
		}

		internal override MovementsReport MovementsFullDetailReportBy(AtAddress atAddress, Coin currencyCode, int storeId, DateTime startDate, DateTime endDate, int initialIndex, int amountOfRows)
		{
			if (startDate.Minute != 0 || startDate.Second != 0) throw new GameEngineException($"{nameof(startDate)} {startDate} is not valid");
			if (endDate.Minute != 0 || endDate.Second != 0) throw new GameEngineException($"{nameof(endDate)} {endDate} is not valid");

			var fragmentsInfo = new List<FragmentInfo>();
			int countMovements = 0;
			string sql = string.Empty;
			try
			{
				var from = GetTableName(atAddress.Number, currencyCode);
				sql = $@"
						SELECT MLR.DOCUMENTNUMBER, MLR.DAY, MLR.AMOUNT, MLR.NEWBALANCE, MLR.ISPENDING, 
                            F.FRAGMENT_ID, F.RISK, F.TO_WIN, F.REASON, F.DESCRIPTION, F.ADJUSTED_WIN, F.ADJUSTED_LOSS, COUNT(*) OVER() 
                        FROM (SELECT DOCUMENTNUMBER, DAY, AMOUNT, NEWBALANCE, 
							    CASE WHEN ((SELECT SUM(AMOUNT) 
								    FROM {from} M2
								    WHERE MOVEMENT = @typeUnlock AND M1.DOCUMENTNUMBER=M2.DOCUMENTNUMBER) = SUM(AMOUNT)) 
								    THEN 0 ELSE 1 END AS ISPENDING 
						    FROM {from} M1
						    WHERE MOVEMENT = @typeLock AND 
                                STORE = @store AND 
                                (DAY BETWEEN @startDate AND @endDate)
						    GROUP BY DOCUMENTNUMBER, DAY, AMOUNT, NEWBALANCE
                        ) MLR
                        LEFT JOIN C{atAddress.NumberForTables}_authfragments F ON F.AUTHORIZATION_ID = MLR.DOCUMENTNUMBER
                        ORDER BY MLR.DOCUMENTNUMBER, F.FRAGMENT_ID
					";
				using (SqlConnection connection = new SqlConnection(connectionString))
				{
					try
					{
						connection.Open();
						using (SqlCommand command = new SqlCommand(sql, connection))
						{
							command.Parameters.AddWithValue("@typeUnlock", (int)Movement.type.Unlock);
							command.Parameters.AddWithValue("@typeLock", (int)Movement.type.Lock);
							command.Parameters.AddWithValue("@store", storeId);
							command.Parameters.AddWithValue("@startDate", ToFullDateTimeString(startDate));
							command.Parameters.AddWithValue("@endDate", ToFullDateTimeString(endDate.AddDays(1)));
							var dataReader = command.ExecuteReader();

							while (dataReader.Read())
							{
								string documentNumber = dataReader.GetString(0);
								DateTime day = dataReader.GetDateTime(1);
								decimal totalAmount = dataReader.GetDecimal(2);
								decimal newBalance = dataReader.GetDecimal(3);
								int pending = dataReader.GetInt32(4);
								if (pending != 0 && pending != 1) throw new GameEngineException($"{nameof(pending)} value '{pending}' is not valid");
								bool isPending = pending == 1;

								int? fragmentId = null;
								decimal? risk = null;
								decimal? toWin = null;
								decimal? adjustedWinAmount = null;
								decimal? adjustedLossAmount = null;
								string fragmentDescription = string.Empty;
								FragmentReason fragmentReason = FragmentReason.Pending;

								var isCompletelyPending = dataReader.IsDBNull(5);
								if (!isCompletelyPending)
								{
									fragmentId = dataReader.GetInt16(5);
									risk = dataReader.GetDecimal(6);
									toWin = dataReader.GetDecimal(7);
									fragmentReason = (FragmentReason)dataReader.GetInt32(8);
									fragmentDescription = dataReader.GetString(9);
									adjustedWinAmount = dataReader.GetDecimal(10);
									adjustedLossAmount = dataReader.GetDecimal(11);
								}

								countMovements = dataReader.GetInt32(12);

								fragmentsInfo.Add(new FragmentInfo(
									atAddress,
									documentNumber,
									day,
									totalAmount,
									newBalance,
									isPending,
									fragmentId,
									risk,
									toWin,
									adjustedWinAmount,
									adjustedLossAmount,
									fragmentReason,
									fragmentDescription
									));
							}
							dataReader.Close();
						}
					}
					finally
					{
						connection.Close();
					}
				}
			}
			catch (Exception e)
			{
				Loggers.GetIntance().Db.Error($@"sql:{sql}", e);
				ErrorsSender.Send(e, sql);
				throw;
			}
			return new MovementsReport(currencyCode, fragmentsInfo, countMovements, initialIndex, amountOfRows);
		}

		public override int InsertUserIfNotExists(int storeId, string userName)
		{
			string sql = "";
			try
			{
				sql = $@"SELECT TOP(1) id FROM movements_users WHERE NAME = '{userName}' AND STORE={storeId}";
				bool exist = false;
				int id = 0;
				using (SqlConnection connection = new SqlConnection(connectionString))
				{
					connection.Open();
					using (SqlCommand command = new SqlCommand(sql, connection))
					using (var reader = command.ExecuteReader())
					{
						exist = reader.Read();
						if (exist)
						{
							id = reader.GetInt32(0);
						}

						reader.Close();
					}
					if (!exist)
					{
						sql = $@"INSERT INTO movements_users(STORE, NAME) VALUES({storeId}, '{userName}')SELECT SCOPE_IDENTITY();";
						using (SqlCommand command = new SqlCommand(sql, connection))
						{
							id = Convert.ToInt32(command.ExecuteScalar());
						}

					}
					connection.Close();
				}
				return id;
			}
			catch (Exception e)
			{
				string message = $@"sql:{sql} @userName:{userName} @store:{storeId}";
				Loggers.GetIntance().Db.Error(message, e);
				ErrorsSender.Send(e, message, $@"username:{userName}");
				throw;
			}
		}

		public override void CreateUserMovementsTableIfNotExists()
		{
			string sql = $@"
			if not exists (SELECT 1 FROM INFORMATION_SCHEMA.TABLES where TABLE_NAME='movements_users')
			BEGIN
				create table movements_users
				(
					ID INT IDENTITY(1,1) PRIMARY KEY,
					STORE TINYINT NOT NULL,
					NAME VARCHAR(50) NOT NULL
				);
			END
			";

			ExecuteCommand(sql);
		}

		internal override IEnumerable<User> ListUsers(int storeId)
		{
			List<User> users = new List<User>();
			string sql = "";
			try
			{
				sql = $@"
				select ID, NAME
				from 
				movements_users
				where STORE = {storeId}
				ORDER by NAME";
				using (SqlConnection connection = new SqlConnection(connectionString))
				{
					try
					{
						connection.Open();
						using (SqlCommand command = new SqlCommand(sql, connection))
						{
							var dataReader = command.ExecuteReader();

							while (dataReader.Read())
							{
								int id = dataReader.GetInt32(0);
								string name = dataReader.GetString(1);

								users.Add(new User(storeId, id, name));
							}
							dataReader.Close();
						}
					}
					finally
					{
						connection.Close();
					}
				}
			}
			catch (Exception e)
			{
				Loggers.GetIntance().Db.Error($@"sql:{sql}", e);
				ErrorsSender.Send(e, sql);
				throw;
			}
			return users;
		}

		internal override void SavePayedFragment(bool itIsThePresent, MovementsCollector movements)
		{
			if (!itIsThePresent) return;

			FragmentInserts(movements);
			FragmentUpdates(movements);
		}

		private void FragmentInserts(MovementsCollector movements)
		{
			var fragmentsToAdd = movements.FragmentsToAdd();
			StringBuilder sql = new StringBuilder();
			foreach (AtAddress address in fragmentsToAdd.Select(x => x.Authorization.AtAddress).Distinct())
			{
				bool thereAreChanges = false;
				var atAddressNumber = address.NumberForTables;
				sql.Append($"Select Top 0 AUTHORIZATION_ID, FRAGMENT_ID, STATUS, RISK, REFERENCE, DESCRIPTION, TO_WIN, CURRENCY, REASON, ADJUSTED_WIN, ADJUSTED_LOSS, ACCOUNT_NUMBER, USELESS into #NewTable from C{atAddressNumber}_authfragments;");
				sql.Append($"INSERT INTO #NewTable (AUTHORIZATION_ID, FRAGMENT_ID, STATUS, RISK, REFERENCE, DESCRIPTION, TO_WIN, CURRENCY, REASON, ADJUSTED_WIN, ADJUSTED_LOSS, ACCOUNT_NUMBER, USELESS) VALUES ");
				const int MAXIMUM_ROWS = 1000;
				int currRows = 0;
				try
				{
					using (SqlConnection connection = new SqlConnection(connectionString))
					{
						try
						{
							connection.Open();
							foreach (AuthorizationFragment fragment in fragmentsToAdd.Where(x => x.Authorization.AtAddress == address))
							{
								if (currRows < MAXIMUM_ROWS)
								{
									int authorizationNumber = fragment.Authorization.Number;

									if (thereAreChanges)
										sql.Append(',');
									sql.Append($@"({authorizationNumber}, {fragment.Number}, {(int)fragment.Status}, {fragment.Risk}, '{Commons.EscapeSQLServer(fragment.Reference)}',
											'{Commons.EscapeSQLServer(fragment.Description)}', {fragment.ToWin}, {fragment.Price.Coin.Id}, {(int)fragment.Reason}, {fragment.AdjustedWinAmount}, 
											{fragment.AdjustedLossAmount}, '{fragment.Account.Number}', '{ToDateTimeString(fragment.Authorization.Useless)}')");
									thereAreChanges = true;
									currRows++;
								}
								else
								{
									sql.Append(';');
									using (SqlCommand command = new SqlCommand(sql.ToString(), connection))
									{
										command.CommandType = CommandType.Text;
										command.ExecuteNonQuery();
									}
									sql.Clear();
									currRows = 0;
									sql.Append($"INSERT INTO #NewTable (AUTHORIZATION_ID, FRAGMENT_ID, STATUS, RISK, REFERENCE, DESCRIPTION, TO_WIN, CURRENCY, REASON, ADJUSTED_WIN, ADJUSTED_LOSS, ACCOUNT_NUMBER, USELESS) VALUES ");
								}
							}
							sql.Append(';');
							sql.Append($"INSERT INTO C{atAddressNumber}_authfragments (AUTHORIZATION_ID, FRAGMENT_ID, STATUS, RISK, REFERENCE, DESCRIPTION, TO_WIN, CURRENCY, REASON, ADJUSTED_WIN, ADJUSTED_LOSS, ACCOUNT_NUMBER, USELESS) SELECT AUTHORIZATION_ID, FRAGMENT_ID, STATUS, RISK, REFERENCE, DESCRIPTION, TO_WIN, CURRENCY, REASON, ADJUSTED_WIN, ADJUSTED_LOSS, ACCOUNT_NUMBER, USELESS FROM #NewTable;");
							sql.Append("DROP TABLE #NewTable;");
							using (SqlCommand command = new SqlCommand(sql.ToString(), connection))
							{
								command.CommandType = CommandType.Text;
								command.ExecuteNonQuery();
							}
						}
						finally
						{
							connection.Close();
						}
					}
				}
				catch (Exception e)
				{
					Loggers.GetIntance().Db.Error($@"sql:{sql}", e);
					ErrorsSender.Send(e, sql.ToString());
					throw;
				}
				sql.Clear();
			}
		}

		private void FragmentUpdates(MovementsCollector movements)
		{
			var fragmentsToUpdate = movements.FragmentsToUpdate();
			foreach (AtAddress address in fragmentsToUpdate.Select(x => x.Authorization.AtAddress).Distinct())
			{
				StringBuilder sql = new StringBuilder();
				bool thereAreChanges = false;
				var atAddressNumber = address.NumberForTables;
				sql.Append($"Select Top 0 AUTHORIZATION_ID, FRAGMENT_ID, STATUS, REASON, ADJUSTED_WIN, ADJUSTED_LOSS, ACCOUNT_NUMBER, USELESS into #NewTable from C{atAddressNumber}_authfragments;");
				sql.Append($"INSERT INTO #NewTable (AUTHORIZATION_ID, FRAGMENT_ID, STATUS, REASON, ADJUSTED_WIN, ADJUSTED_LOSS, ACCOUNT_NUMBER, USELESS) VALUES ");
				const int MAXIMUN_ROWS = 1000;
				int currRows = 0;
				try
				{
					using (SqlConnection connection = new SqlConnection(connectionString))
					{
						try
						{
							connection.Open();
							foreach (AuthorizationFragment fragment in fragmentsToUpdate.Where(x => x.Authorization.AtAddress == address))
							{
								if (currRows < MAXIMUN_ROWS)
								{
									int authorizationNumber = fragment.Authorization.Number;

									if (thereAreChanges)
										sql.Append(',');
									sql.Append($"({authorizationNumber}, {fragment.Number}, {(int)fragment.Status}, {(int)fragment.Reason}, {fragment.AdjustedWinAmount}, {fragment.AdjustedLossAmount}, '{fragment.Account.Number}', '{ToDateTimeString(fragment.Authorization.Useless)}')");
									thereAreChanges = true;
									currRows++;
								}
								else
								{
									sql.Append(';');
									using (SqlCommand command = new SqlCommand(sql.ToString(), connection))
									{
										command.CommandType = CommandType.Text;
										command.ExecuteNonQuery();
									}
									sql.Clear();
									currRows = 0;
									sql.Append($"INSERT INTO #NewTable (AUTHORIZATION_ID, FRAGMENT_ID, STATUS, REASON, ADJUSTED_WIN, ADJUSTED_LOSS, ACCOUNT_NUMBER, USELESS) VALUES ");
								}
							}
							sql.Append(';');
							sql.Append($@"
							UPDATE C{atAddressNumber}_authfragments
								SET STATUS = source.STATUS, REASON = source.REASON, EDIT_MODE = 0, ADJUSTED_WIN = source.ADJUSTED_WIN, ADJUSTED_LOSS = source.ADJUSTED_LOSS, ACCOUNT_NUMBER = source.ACCOUNT_NUMBER, USELESS = source.USELESS
								FROM 
									C{atAddressNumber}_authfragments target
									INNER JOIN #NewTable source 
									ON target.AUTHORIZATION_ID = source.AUTHORIZATION_ID AND target.FRAGMENT_ID = source.FRAGMENT_ID
							;");
							sql.Append("DROP TABLE #NewTable;");
							using (SqlCommand command = new SqlCommand(sql.ToString(), connection))
							{
								command.CommandType = CommandType.Text;
								command.ExecuteNonQuery();
							}
						}
						finally
						{
							connection.Close();
						}
					}
				}
				catch (Exception e)
				{
					Loggers.GetIntance().Db.Error($@"sql:{sql}", e);
					ErrorsSender.Send(e, sql.ToString());
					throw;
				}
			}
		}

		internal override void CreateFragmentIfNotExists(string number)
		{
			string sql = "";
			try
			{
				bool exists = true;

				StringBuilder statement = new StringBuilder();

				statement.Append("IF EXISTS(")
					.Append("SELECT 1 FROM INFORMATION_SCHEMA.TABLES ")
					.Append("WHERE TABLE_NAME = 'C" + number + "_authfragments')")
					.Append("SELECT 1 ELSE SELECT 0;");
				sql = statement.ToString();

				using (SqlConnection connection = new SqlConnection(connectionString))
				{
					try
					{
						connection.Open();
						using (SqlCommand command = new SqlCommand(sql, connection))
						{
							var result = (int)command.ExecuteScalar();
							exists = result == 1;
						}
					}
					catch
					{
						exists = false;
					}

					try
					{
						if (exists)
						{
							connection.Close();
							return;
						}
						statement = new StringBuilder();

						statement
						.Append("CREATE TABLE IF NOT EXISTS C").Append(number).Append("_authfragments")
						.Append('(')
						.Append("AUTHORIZATION_ID INT NOT NULL,")
						.Append("FRAGMENT_ID Smallint NOT NULL,")
						.Append("STATUS TINYINT NOT NULL,")
						.Append("RISK DECIMAL(10,2) NOT NULL,")
						.Append("REFERENCE VARCHAR(20) NOT NULL,")
						.Append("DESCRIPTION TEXT NOT NULL,")
						.Append("TO_WIN DECIMAL(10,2) NOT NULL,")
						.Append("ADJUSTED_WIN DECIMAL(10,2) NOT NULL,")
						.Append("ADJUSTED_LOSS DECIMAL(10,2) NOT NULL,")
						.Append("CURRENCY TINYINT NOT NULL,")
						.Append("REASON INT NOT NULL,")
						.Append("ACCOUNT_NUMBER VARCHAR(60) NOT NULL,")
						.Append("USELESS DATETIME NOT NULL,")
						.Append("EDIT_MODE BIT NOT NULL DEFAULT 0")
						.Append(");")

						.Append("CREATE INDEX IDX_C").Append(number).Append("_authfragments")
						.Append(" ON C").Append(number).Append("_authfragments")
						.Append("(AUTHORIZATION_ID);");


						sql = statement.ToString();

						using (SqlCommand command = new SqlCommand(sql, connection))
						{
							command.ExecuteNonQuery();
						}
					}
					catch (Exception e)
					{
						Loggers.GetIntance().Db.Error($@"sql:{sql}", e);
						ErrorsSender.Send(e, sql);
						throw;
					}
					finally
					{
						connection.Close();
					}
				}
			}
			catch (Exception e)
			{
				Loggers.GetIntance().Db.Error($@"sql:{sql}", e);
				ErrorsSender.Send(e, sql);
				throw;
			}
		}

		private string ScriptToInserFragmentReasons()
		{
			StringBuilder script = new StringBuilder();
			foreach (FragmentReason reason in Enum.GetValues(typeof(FragmentReason)))
			{
				script.Append($"INSERT INTO authfragments_reasons (ID, REASON) VALUES ({(int)reason},'{reason.ToString()}');");
			}
			return script.ToString();
		}

		private bool ExistAuthFragmentReasonsTable()
		{
			bool exists = false;
			StringBuilder statement = new StringBuilder();

			statement.Append("IF EXISTS(")
				.Append("SELECT 1 FROM INFORMATION_SCHEMA.TABLES ")
				.Append("WHERE TABLE_NAME = 'authfragments_reasons')")
				.Append("SELECT 1 ELSE SELECT 0;");
			string sql = statement.ToString();

			using (SqlConnection connection = new SqlConnection(connectionString))
			{
				try
				{
					connection.Open();
					using (SqlCommand command = new SqlCommand(sql, connection))
					{
						var result = (int)command.ExecuteScalar();
						exists = result == 1;
					}
				}
				catch
				{
					exists = false;
				}
				finally
				{
					connection.Close();
				}
			}
			return exists;
		}

		internal override void CreateFragmentReasonsIfNotExists()
		{
			if (ExistAuthFragmentReasonsTable()) return;

			StringBuilder statement = new StringBuilder();
			statement
						.Append("CREATE TABLE IF NOT EXISTS authfragments_reasons")
						.Append("(")
						.Append("ID INT NOT NULL,")
						.Append($"REASON VARCHAR(10) NOT NULL")
						.Append(");");

			statement.Append(ScriptToInserFragmentReasons());

			string sql = statement.ToString();

			using (SqlConnection connection = new SqlConnection(connectionString))
			{
				try
				{
					connection.Open();
					using (SqlCommand command = new SqlCommand(sql, connection))
					{
						command.ExecuteNonQuery();
					}
				}
				catch (Exception e)
				{
					Loggers.GetIntance().Db.Error($@"sql:{sql}", e);
					ErrorsSender.Send(e, sql);
					throw;
				}
				finally
				{
					connection.Close();
				}
			}
		}

		internal override MovementsFiltered ListMovement(int inicialIndex, int amountOfRows, string atAddress, int storeId, DateTime initDate, DateTime endDate, Coin currencyCode)
		{
			List<Movement> movements = new List<Movement>();
			int countMovements = 0;
			string sql = "";

			try
			{
				var from = GetTableName(atAddress, currencyCode);
				sql = $@"
						SELECT DAY, SOURCE, MOVEMENT, AMOUNT, NEWBALANCE, DOCUMENTNUMBER, WHO, m.STORE, ISNULL(CONCEPT, '') AS CONCEPT, ISNULL(REFERENCE, '') AS REFERENCE, COUNT(*) OVER(), NEWLOCKBALANCE, ACCOUNT_NUMBER, PROCESSORID
						FROM {from} m
						WHERE m.STORE = {storeId} AND CAST(DAY AS DATE) <= '{ToFullDateTimeString(endDate)}' AND CAST(DAY AS DATE) >= '{ToFullDateTimeString(initDate)}'
						ORDER BY DAY
						OFFSET {inicialIndex} ROWS
						FETCH NEXT {amountOfRows} ROWS ONLY
					";

				using (SqlConnection connection = new SqlConnection(connectionString))
				{
					try
					{
						connection.Open();
						using (SqlCommand command = new SqlCommand(sql, connection))
						{
							var dataReader = command.ExecuteReader();

							while (dataReader.Read())
							{
								DateTime day = dataReader.GetDateTime(0);
								int sourceAsInt = dataReader.GetByte(1);
								string movement = dataReader.GetString(2);
								decimal amount = dataReader.GetDecimal(3);
								decimal newBalance = dataReader.GetDecimal(4);
								string documentNumber = dataReader.GetString(5);
								string who = Users.None;
								if (!dataReader.IsDBNull(6))
								{
									who = dataReader.GetString(6);
								}
								storeId = dataReader.GetByte(7);
								string concept = dataReader.GetString(8);
								string reference = dataReader.GetString(9);
								countMovements = dataReader.GetInt32(10);

								decimal newLock = dataReader.GetDecimal(11);
								string accountNumber = dataReader.GetString(12);
								int processorId = dataReader.GetInt32(13);

								Movement.type type;
								bool result = Enum.TryParse(movement, true, out type);
								if (!result)
								{
									throw new GameEngineException($@"There is no Movement.type for type {movement}");
								}

								Currency currency = Currency.Factory(currencyCode.Iso4217Code, amount);
								Movement m = null;

								var isDebit = ItsADebitMovement(type);
								if (isDebit)
								{
									m = Movement.GenerateADebitMovement(sourceAsInt, atAddress, day, type, currency, newBalance, who, documentNumber, storeId, concept, reference, newLock, accountNumber, processorId);
								}
								else
								{
									m = Movement.GenerateACreditMovement(sourceAsInt, atAddress, day, type, currency, newBalance, who, documentNumber, storeId, concept, reference, newLock, accountNumber, processorId);
								}

								movements.Add(m);
							}

							dataReader.Close();
						}
					}
					finally
					{
						connection.Close();
					}
				}
			}
			catch (Exception e)
			{
				Loggers.GetIntance().Db.Error($@"sql:{sql}", e);
				ErrorsSender.Send(e, sql);
				throw;
			}
			return new MovementsFiltered(movements, countMovements);
		}

		private string UnEscapeSQLServer(string script)
		{
			StringBuilder result = new StringBuilder();
			foreach (char c in script)
			{
				switch (c)
				{
					case '\'':
						result.Append("\'\'");
						break;
					case '\"':
						result.Append('"');
						break;
					case '\\':
						result.Append('\\');
						break;
					default:
						result.Append(c);
						break;
				}
			}
			return result.ToString();
		}

		public override void SaveBalanceMovements(bool itIsThePresent, int whosId, IEnumerable<Movement> movements, DataTable movementTableByAccount, DataTable movementTableByCurrency)
		{
			if (!itIsThePresent) return;

			var firstMovement = movements.First();
			var currentDay = firstMovement.Day;
			var dateAsText = ToFullDateTimeString(currentDay);
			var currentConcept = UnEscapeSQLServer(firstMovement.Concept);
			var currentAtAddress = firstMovement.AtAddress;
			var currentCurrency = firstMovement.Currency;

			movementTableByCurrency.TableName = GetTableName(currentCurrency).ToString();
			movementTableByAccount.TableName = GetTableName(currentAtAddress, currentCurrency).ToString();
			foreach (Movement movement in movements)
			{
				if (currentConcept != movement.Concept) throw new GameEngineException($"{nameof(currentConcept)} was {currentConcept} but now is {movement.Concept}. It must be the same for all movements in the group");
				if (currentDay != movement.Day)
				{
					currentDay = movement.Day;
					dateAsText = ToFullDateTimeString(currentDay);
				}

				var row = movementTableByCurrency.NewRow();
				row["DAY"] = dateAsText;
				row["ATADDRESS"] = movement.AtAddress;
				row["SOURCE"] = movement.Source;
				row["CURRENCY"] = movement.Currency.ToString();
				row["MOVEMENT"] = ((int)movement.Type).ToString();
				row["AMOUNT"] = movement.CurrentAmount;
				row["NEWBALANCE"] = movement.NewBalance;
				if (whosId == Users.NO_USER)
				{
					row["WHO"] = DBNull.Value;
				}
				else
				{
					row["WHO"] = whosId;
				}
				row["DOCUMENTNUMBER"] = movement.DocumentNumber;
				row["STORE"] = (int)movement.StoreId;
				row["REFERENCE"] = movement.Reference;
				row["CONCEPT"] = currentConcept;
				row["NEWLOCKBALANCE"] = movement.NewLock;
				row["ACCOUNT_NUMBER"] = movement.AccountNumber;
				row["PROCESSORID"] = movement.ProcessorId;
				movementTableByCurrency.Rows.Add(row);

				row = movementTableByAccount.NewRow();
				row["DAY"] = dateAsText;
				row["SOURCE"] = movement.Source;
				row["MOVEMENT"] = ((int)movement.Type).ToString();
				row["AMOUNT"] = movement.CurrentAmount;
				row["NEWBALANCE"] = movement.NewBalance;
				if (whosId == Users.NO_USER)
				{
					row["WHO"] = DBNull.Value;
				}
				else
				{
					row["WHO"] = whosId;
				}
				row["DOCUMENTNUMBER"] = movement.DocumentNumber;
				row["STORE"] = movement.StoreId;
				row["REFERENCE"] = movement.Reference;
				row["CONCEPT"] = currentConcept;
				row["NEWLOCKBALANCE"] = movement.NewLock;
				row["ACCOUNT_NUMBER"] = movement.AccountNumber;
				row["PROCESSORID"] = movement.ProcessorId;
				movementTableByAccount.Rows.Add(row);
			}

			InsertInMovementsTable(movementTableByCurrency, movementTableByAccount);
		}

		void InsertInMovementsTable(DataTable movementTableByCurrency, DataTable movementTableByAccount)
		{
			using (var connection = new SqlConnection(connectionString))
			{
				try
				{
					connection.Open();
					using (SqlBulkCopy copy = new SqlBulkCopy(connection))
					{
						copy.DestinationTableName = movementTableByCurrency.TableName;
						copy.WriteToServer(movementTableByCurrency);
						copy.DestinationTableName = movementTableByAccount.TableName;
						copy.WriteToServer(movementTableByAccount);
					}
				}
				catch (Exception e)
				{
					Loggers.GetIntance().Db.Error($@"table:{movementTableByCurrency.TableName} content:{movementTableByCurrency.ToString()}", e);
					ErrorsSender.Send(e, movementTableByCurrency.ToString());
					throw;
				}
				finally
				{
					connection.Close();
					movementTableByCurrency.Clear();
					movementTableByAccount.Clear();
				}
			}
		}

		internal override Authorization RetrieveFragments(int authorizationNumber, AtAddress atAddress, Purpose purpose)
		{
			string sql = $"SELECT AUTHORIZATION_ID, FRAGMENT_ID, RISK, STATUS, CURRENCY, REFERENCE, DESCRIPTION, TO_WIN, REASON, ADJUSTED_WIN, ADJUSTED_LOSS, ACCOUNT_NUMBER, USELESS FROM C{atAddress.NumberForTables}_authfragments with(nolock) WHERE AUTHORIZATION_ID = {authorizationNumber}";
			var storedFragmentsInfo = new List<PartialFragment>();
			int lowerId = Int32.MaxValue;
			int higherId = Int32.MinValue;
			Currency amountOfEachFragment = null;
			decimal totalAmount = 0;
			decimal fragmentRisk = 0;
			string accountNumber = "";
			DateTime useless = DateTime.MinValue;
			try
			{
				using (SqlConnection connection = new SqlConnection(connectionString))
				{
					try
					{
						connection.Open();
						using (SqlCommand command = new SqlCommand(sql.ToString(), connection))
						{
							var dataReader = command.ExecuteReader();

							while (dataReader.Read())
							{
								var coin = Coinage.GetById(dataReader.GetByte(4));
								fragmentRisk = dataReader.GetDecimal(2);
								Currency price = Currency.Factory(coin, fragmentRisk);
								totalAmount = totalAmount + fragmentRisk;

								int fragmentId = dataReader.GetInt16(1);
								FragmentStatus status = (FragmentStatus)dataReader.GetByte(3);

								if (lowerId > fragmentId) lowerId = fragmentId;
								if (higherId < fragmentId) higherId = fragmentId;
								if (amountOfEachFragment == null) amountOfEachFragment = price;

								string fragmentReference = dataReader.GetString(5);
								string fragmentDescription = dataReader.GetString(6);
								decimal fragmentToWin = dataReader.GetDecimal(7);
								FragmentReason fragmentReason = (FragmentReason)dataReader.GetInt32(8);
								decimal adjustedWinAmount = dataReader.GetDecimal(9);
								decimal adjustedLossAmount = dataReader.GetDecimal(10);
								accountNumber = dataReader.GetString(11);
								useless = dataReader.GetDateTime(12);

								storedFragmentsInfo.Add(new PartialFragment(fragmentId, status, fragmentReference, fragmentDescription, fragmentRisk, fragmentToWin, fragmentReason, adjustedWinAmount, adjustedLossAmount, accountNumber, useless));
							}
						}
					}
					finally
					{
						connection.Close();
					}
				}
			}
			catch (Exception e)
			{
				string message = $@"sql:{sql} @AuthorizationNumber:{authorizationNumber}";
				Loggers.GetIntance().Db.Error(message, e.InnerException ?? e);
				ErrorsSender.Send(e.InnerException ?? e, message);
				throw;
			}

			if (storedFragmentsInfo.Count == 0) throw new GameEngineException($"Authorization number {authorizationNumber} does not exist");

			var amount = Currency.Factory(amountOfEachFragment.CurrencyCode, totalAmount);

			if (purpose == Purpose.ForUpdate)
			{
				string sqlUpdate = $"UPDATE C{atAddress.NumberForTables}_authfragments SET EDIT_MODE = 1 WHERE AUTHORIZATION_ID = {authorizationNumber}";
				using (SqlConnection connection = new SqlConnection(connectionString))
				{
					try
					{
						connection.Open();
						using (SqlCommand command = new SqlCommand(sqlUpdate.ToString(), connection))
						{
							command.CommandType = CommandType.Text;
							command.ExecuteNonQuery();
						}
					}
					finally
					{
						connection.Close();
					}
				}
			}

			AccountWithBalance account = atAddress.SearchAccountByNumber(accountNumber);

			Authorization authorization = new Authorization(authorizationNumber, amount, account, useless);
			authorization.CreateFragments(lowerId, higherId, fragmentRisk, storedFragmentsInfo);
			return authorization;
		}

		internal override MovementDetailReport MovementDetailReportBy(AtAddress atAddress, int authorizationNumber, Coin currencyCode, int initialIndex, int amountOfRows)
		{
			var movementDetails = new List<MovementDetailRecord>();
			int countMovements = 0;
			var sql = $@"SELECT FRAGMENT_ID, RISK, TO_WIN, REASON, DESCRIPTION, COUNT(*) OVER(), ADJUSTED_WIN, ADJUSTED_LOSS
						FROM C{atAddress.NumberForTables}_authfragments
						WHERE AUTHORIZATION_ID = @authorizationNumber AND CURRENCY = @currencyCode
						ORDER BY FRAGMENT_ID
						OFFSET @initialIndex ROWS
						FETCH NEXT @amountOfRows ROWS ONLY;";
			try
			{
				using (SqlConnection connection = new SqlConnection(connectionString))
				{
					try
					{
						connection.Open();
						using (SqlCommand command = new SqlCommand(sql.ToString(), connection))
						{
							command.Parameters.AddWithValue("@authorizationNumber", authorizationNumber);
							command.Parameters.AddWithValue("@currencyCode", currencyCode.Id);
							command.Parameters.AddWithValue("@amountOfRows", amountOfRows);
							command.Parameters.AddWithValue("@initialIndex", initialIndex);
							var dataReader = command.ExecuteReader();

							while (dataReader.Read())
							{
								int id = dataReader.GetInt16(0);
								decimal risk = dataReader.GetDecimal(1);
								decimal toWin = dataReader.GetDecimal(2);
								int reasonId = dataReader.GetInt32(3);
								FragmentReason reason = (FragmentReason)reasonId;
								string description = dataReader.GetString(4);
								countMovements = dataReader.GetInt32(5);
								decimal adjustedWinAmount = dataReader.GetDecimal(6);
								decimal adjustedLossAmount = dataReader.GetDecimal(7);

								movementDetails.Add(new MovementDetailRecord(currencyCode, authorizationNumber, id, risk, toWin, description, reason, adjustedWinAmount, adjustedLossAmount));
							}
						}
					}
					finally
					{
						connection.Close();
					}
				}
			}
			catch (Exception e)
			{
				string message = $@"sql:{sql} @authorizationNumber:{authorizationNumber}";
				Loggers.GetIntance().Db.Error(message, e.InnerException ?? e);
				ErrorsSender.Send(e.InnerException ?? e, message);
				throw;
			}

			return new MovementDetailReport(currencyCode, movementDetails, countMovements, atAddress, authorizationNumber, initialIndex, amountOfRows);
		}

		public override void CreateAuthorizationSequenceIfNotExists()
		{
			string sql = "";
			try
			{
				StringBuilder statement = new StringBuilder();

				statement.Append($@"
				if not exists (SELECT 1 FROM sys.sequences where [name]='authorizations')
				BEGIN
					CREATE SEQUENCE authorizations AS int 
					START WITH 1
					INCREMENT BY 1
					MINVALUE 1
					CYCLE
				END");

				sql = statement.ToString();
				using (SqlConnection connection = new SqlConnection(connectionString))
				{
					try
					{
						connection.Open();
						using (SqlCommand command = new SqlCommand(sql, connection))
						{
							command.ExecuteNonQuery();
						}
					}
					finally
					{
						connection.Close();
					}
				}
			}
			catch (Exception e)
			{
				Loggers.GetIntance().Db.Error($@"sql:{sql}", e);
				ErrorsSender.Send(e, $@"sql:{sql}");
				throw;
			}
		}

		public override int NextAuthorizationNumber()
		{
			string sql = "SELECT NEXT VALUE FOR authorizations";
			int result = -1;
			try
			{
				using (SqlConnection connection = new SqlConnection(connectionString))
				{
					try
					{
						connection.Open();
						using (SqlCommand command = new SqlCommand(sql, connection))
						{
							var dataReader = command.ExecuteReader();

							dataReader.Read();

							result = dataReader.GetInt32(0);

							dataReader.Close();
						}
					}
					catch (Exception e)
					{
						Loggers.GetIntance().Db.Error($@"sql:{sql}", e);
						ErrorsSender.Send(e, sql);
						throw;
					}
					finally
					{
						connection.Close();
					}
				}
			}
			catch (Exception e)
			{
				Loggers.GetIntance().Db.Error($@"sql:{sql}", e.InnerException ?? e);
				ErrorsSender.Send(e.InnerException ?? e, sql);
				throw;
			}

			return result;
		}

		protected override void CreateMovementsTableForThisAccountAndCurrency(string atAddress, Coin currencyCode)
		{
			atAddress = atAddress.Trim().ToLower();

			string sql = CreateMovementsStorage(atAddress, currencyCode);

			ExecuteCommand(sql);
		}

		internal override string CreateMovementsStorage(string atAddress, Coin currencyCode)
		{
			StringBuilder statement = new StringBuilder();
			string tableName = GetTableName(atAddress, currencyCode);

			statement.Append("CREATE TABLE IF NOT EXISTS ").Append(tableName)
				.Append("(")
				.Append("DAY DATETIME NOT NULL,")
				.Append("SOURCE TINYINT NOT NULL,")
				.Append("MOVEMENT VARCHAR(1) NOT NULL,")
				.Append("AMOUNT DECIMAL(16,8) NOT NULL,")
				.Append("NEWBALANCE DECIMAL(16,8) NOT NULL,")
				.Append("NEWLOCKBALANCE DECIMAL(16,8) NOT NULL,")
				.Append("ACCOUNT_NUMBER VARCHAR(60) NOT NULL,")
				.Append("PROCESSORID INT NOT NULL,")
				.Append("WHO INT,")
				.Append("DOCUMENTNUMBER VARCHAR(45) NOT NULL,")
				.Append("STORE TINYINT NOT NULL,")
				.Append("REFERENCE VARCHAR(30) NULL,")
				.Append("CONCEPT TEXT")
				.Append(");");

			statement.Append($"CREATE INDEX {tableName}_DAY ON {tableName} (DAY);");

			return statement.ToString();
		}

		protected override void CreateMovementsTableForThisCurrency(string currencyCode)
		{
			string sql = CreateMovementsStorage(currencyCode);
			try
			{
				using (SqlConnection connection = new SqlConnection(connectionString))
				{
					connection.Open();
					try
					{
						using (SqlCommand command = new SqlCommand(sql, connection))
						{
							command.ExecuteNonQuery();
						}
					}
					catch (Exception e)
					{

						Loggers.GetIntance().Db.Error($@"sql:{sql}", e);
						ErrorsSender.Send(e, sql);
						throw;
					}
					finally
					{
						connection.Close();
					}
				}

			}
			catch (Exception e)
			{
				Loggers.GetIntance().Db.Error($@"sql:{sql}", e);
				ErrorsSender.Send(e, sql);
				throw;
			}
		}

		internal override string CreateMovementsStorage(string currencyCode)
		{
			string tableName = GetTableName(currencyCode);
			StringBuilder statement = new StringBuilder();
			statement.Append($"CREATE TABLE IF NOT EXISTS {tableName}").AppendLine()
				.Append("(").AppendLine()
				.Append("DAY DATETIME NOT NULL,").AppendLine()
				.Append($"ATADDRESS VARCHAR({Customer.DATABASE_FIELD_ACCOUNT_LENGHT}) NOT NULL,").AppendLine()
				.Append("SOURCE TINYINT NOT NULL,").AppendLine()
				.Append("MOVEMENT VARCHAR(1) NOT NULL,").AppendLine()
				.Append("AMOUNT DECIMAL(16,8) NOT NULL,").AppendLine()
				.Append("NEWBALANCE DECIMAL(16,8) NOT NULL,").AppendLine()
				.Append("NEWLOCKBALANCE DECIMAL(16,8) NOT NULL,").AppendLine()
				.Append("ACCOUNT_NUMBER VARCHAR(60) NOT NULL,").AppendLine()
				.Append("PROCESSORID INT NOT NULL,").AppendLine()
				.Append("WHO INT,").AppendLine()
				.Append("DOCUMENTNUMBER VARCHAR(45) NOT NULL,").AppendLine()
				.Append("STORE TINYINT NOT NULL,").AppendLine()
				.Append("REFERENCE VARCHAR(30) NULL,").AppendLine()
				.Append("CONCEPT TEXT").AppendLine()
				.Append(");").AppendLine()
			.Append("CREATE INDEX idx_store_movement_").Append(currencyCode)
            .Append(" ON ").Append(tableName).Append(" (STORE, MOVEMENT);")
            .Append("CREATE INDEX idx_documentnumber_").Append(currencyCode)
            .Append(" ON ").Append(tableName).Append(" (DOCUMENTNUMBER);")
            .Append("CREATE INDEX idx_day_").Append(currencyCode)
            .Append(" ON ").Append(tableName).Append(" (DAY);");

            return statement.ToString();
		}


		protected override void CreateFragmentsTableForThisAccount(string atAddressNumber)
		{
			StringBuilder statement = new StringBuilder();
			statement.Append(CreateFragmentsStorage(atAddressNumber));

			string sql = "";
			try
			{
				using (SqlConnection connection = new SqlConnection(connectionString))
				{
					connection.Open();
					try
					{
						sql = statement.ToString();

						using (SqlCommand command = new SqlCommand(sql, connection))
						{
							command.ExecuteNonQuery();
						}
					}
					catch (Exception e)
					{

						Loggers.GetIntance().Db.Error($@"sql:{sql}", e);
						ErrorsSender.Send(e, sql);
						throw;
					}
					finally
					{
						connection.Close();
					}
				}

			}
			catch (Exception e)
			{
				Loggers.GetIntance().Db.Error($@"sql:{sql}", e);
				ErrorsSender.Send(e, sql);
				throw;
			}
		}

		internal override string CreateFragmentsStorage(string number)
		{
			StringBuilder statement = new StringBuilder();

			statement
			.Append("CREATE TABLE IF NOT EXISTS C").Append(number).Append("_authfragments")
			.Append('(')
			.Append("AUTHORIZATION_ID INT NOT NULL,")
			.Append("FRAGMENT_ID Smallint NOT NULL,")
			.Append("STATUS TINYINT NOT NULL,")
			.Append("RISK DECIMAL(10,2) NOT NULL,")
			.Append("REFERENCE VARCHAR(20) NOT NULL,")
			.Append("DESCRIPTION TEXT NOT NULL,")
			.Append("TO_WIN DECIMAL(10,2) NOT NULL,")
			.Append("ADJUSTED_WIN DECIMAL(10,2) NOT NULL,")
			.Append("ADJUSTED_LOSS DECIMAL(10,2) NOT NULL,")
			.Append("CURRENCY TINYINT NOT NULL,")
			.Append("REASON INT NOT NULL,")
			.Append("ACCOUNT_NUMBER VARCHAR(60) NOT NULL,")
			.Append("EDIT_MODE BIT NOT NULL DEFAULT 0,")
			.Append("USELESS DATETIME NOT NULL")
			.Append(");")

			.Append("CREATE INDEX IDX_C").Append(number).Append("_authfragments")
			.Append(" ON C").Append(number).Append("_authfragments")
			.Append("(AUTHORIZATION_ID);");


			return statement.ToString();
		}

        public override void DeleteMovements(string atAddress, Currencies.CODES currencyCode)
        {
			string tableName = GetTableName(atAddress, Coinage.Coin(currencyCode));
			if (!ExistsTable(tableName)) return;

			if (!ExistsMovements(tableName, LongTermController.MonthsToArchivedMovements)) return;
			if (!LongTermController.ENABLED) return;

			StringBuilder sql = new StringBuilder();
			const string POSTFIX = "_$OLD";

			IEnumerable<MovementStruct> allMovements = MovementsToArchived(tableName, LongTermController.MonthsToArchivedMovements);
			bool success = LongTermController.Instance().SaveMovementsLongTerm(tableName, allMovements);
			if (success) MarkArchivedMovements(tableName, LongTermController.MonthsToArchivedMovements);

			string createQueryTable = CreateMovementsStorage(atAddress, currencyCode, false);

			sql.AppendLine($"IF NOT EXISTS (SELECT 1 FROM sys.tables WHERE name = '{tableName + POSTFIX}')");
			sql.AppendLine($"BEGIN");
			sql.AppendLine($"BEGIN TRY");
			sql.AppendLine($"EXEC sp_rename '{tableName}', '{tableName + POSTFIX}';");
			sql.AppendLine($"BEGIN TRY");
			sql.AppendLine(createQueryTable);
			sql.AppendLine($"BEGIN TRY");
			sql.AppendLine($"INSERT INTO {tableName}(DAY, SOURCE, MOVEMENT, AMOUNT, NEWBALANCE, WHO, DOCUMENTNUMBER, STORE, REFERENCE, CONCEPT, ARCHIVED) SELECT DAY, SOURCE, MOVEMENT, AMOUNT, NEWBALANCE, WHO, DOCUMENTNUMBER, STORE, REFERENCE, CONCEPT, ARCHIVED FROM {tableName + POSTFIX} WITH (nolock) WHERE ARCHIVED = 0;");
			sql.AppendLine($"BEGIN TRY");
			sql.AppendLine($"DROP TABLE {tableName + POSTFIX};");
			sql.AppendLine($"END TRY");
			sql.AppendLine($"BEGIN CATCH");
			sql.AppendLine($"END CATCH");
			sql.AppendLine($"END TRY");
			sql.AppendLine($"BEGIN CATCH");
			sql.AppendLine($"DROP TABLE {tableName};");
			sql.AppendLine($"EXEC sp_rename '{tableName + POSTFIX}', '{tableName}';");
			sql.AppendLine($"END CATCH");
			sql.AppendLine($"END TRY");
			sql.AppendLine($"BEGIN CATCH");
			sql.AppendLine($"EXEC sp_rename '{tableName + POSTFIX}', '{tableName}';");
			sql.AppendLine($"END CATCH");
			sql.AppendLine($"END TRY");
			sql.AppendLine($"BEGIN CATCH");
			sql.AppendLine($"END CATCH");
			sql.AppendLine($"END");

			lock (fragmentsAndMovementsSemaphore)
			{
				ExecuteCommand(sql.ToString());
			}
		}

		private bool ExistsMovements(string tableName, int monthsToArchivedMovements)
		{
			bool result = false;
			string sqlString = $"IF EXISTS (SELECT * FROM {tableName} WITH (nolock) WHERE DATEDIFF(month, DAY, GETDATE()) >= {monthsToArchivedMovements}) SELECT 1;";

			lock (fragmentsAndMovementsSemaphore)
			{
				try
				{
					using (SqlConnection connection = new SqlConnection(connectionString))
					{
						try
						{
							connection.Open();
							using (SqlCommand command = new SqlCommand(sqlString, connection))
							using (SqlDataReader reader = command.ExecuteReader())
							{
								while (reader.Read())
								{
									result = true;
								}
								reader.Close();
							}
						}
						finally
						{
							connection.Close();
						}

					}
				}
				catch
				{

				}
			}
			return result;
		}

		public override void DeleteAuthFragments(string atAddress)
        {
			string tableName = "C" + atAddress + "_authfragments";
			if (!ExistsTable(tableName)) return;

			StringBuilder sql = new StringBuilder();
			const string POSTFIX = "_$OLD";

			string createQueryTable = CreateFragmentsStorage(atAddress, false);

			sql.AppendLine($"IF NOT EXISTS (SELECT 1 FROM sys.tables WHERE name = '{tableName + POSTFIX}')");
			sql.AppendLine($"BEGIN");
			sql.AppendLine($"IF EXISTS (SELECT * FROM {tableName} WITH (nolock) WHERE GETDATE() >= USELESS");
			sql.AppendLine($"BEGIN");
			sql.AppendLine($"BEGIN TRY");
			sql.AppendLine($"EXEC sp_rename '{tableName}', '{tableName + POSTFIX}';");
			sql.AppendLine($"BEGIN TRY");
			sql.AppendLine(createQueryTable);
			sql.AppendLine($"BEGIN TRY");
			sql.AppendLine($"INSERT INTO {tableName}(AUTHORIZATION_ID, FRAGMENT_ID, STATUS, RISK, REFERENCE, DESCRIPTION, TO_WIN, ADJUSTED_WIN, ADJUSTED_LOSS, CURRENCY, REASON, EDIT_MODE, ARCHIVED) SELECT AUTHORIZATION_ID, FRAGMENT_ID, STATUS, RISK, REFERENCE, DESCRIPTION, TO_WIN, ADJUSTED_WIN, ADJUSTED_LOSS, CURRENCY, REASON, EDIT_MODE, ARCHIVED FROM {tableName + POSTFIX} WHERE ARCHIVED = 0;");
			sql.AppendLine($"BEGIN TRY");
			sql.AppendLine($"DROP TABLE {tableName + POSTFIX};");
			sql.AppendLine($"END TRY");
			sql.AppendLine($"BEGIN CATCH");
			sql.AppendLine($"END CATCH");
			sql.AppendLine($"END TRY");
			sql.AppendLine($"BEGIN CATCH");
			sql.AppendLine($"DROP TABLE {tableName};");
			sql.AppendLine($"EXEC sp_rename '{tableName + POSTFIX}', '{tableName}';");
			sql.AppendLine($"END CATCH");
			sql.AppendLine($"END TRY");
			sql.AppendLine($"BEGIN CATCH");
			sql.AppendLine($"EXEC sp_rename '{tableName + POSTFIX}', '{tableName}';");
			sql.AppendLine($"END CATCH");
			sql.AppendLine($"END TRY");
			sql.AppendLine($"BEGIN CATCH");
			sql.AppendLine($"END CATCH");
			sql.AppendLine($"END");
			sql.AppendLine($"END");

			lock (fragmentsAndMovementsSemaphore)
			{
				ExecuteCommand(sql.ToString());
			}
		}

        public override IEnumerable<string> AllCashierAccounts()
        {
            List<string> allAccounts = new List<string>();

            string sqlString = $"SELECT Cuenta FROM accountsLRU ORDER BY DATEDIFF(day, FechaHora, GETDATE()) ASC;";

            try
            {
                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    try
                    {
                        connection.Open();
                        using (SqlCommand command = new SqlCommand(sqlString, connection))
                        using (SqlDataReader reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                allAccounts.Add(reader.GetString(0));
                            }
                            reader.Close();
                        }
                    }
                    finally
                    {
                        connection.Close();
                    }

                }
            }
            catch
            {

            }

            return allAccounts;
        }

        public override IEnumerable<AuthorizationFragmentStruct> AuthFragmentsToArchived(string tableName)
        {
			List<AuthorizationFragmentStruct> allAuthFragments = new List<AuthorizationFragmentStruct>();
			string sqlString = $"SELECT AUTHORIZATION_ID, FRAGMENT_ID, STATUS, RISK, REFERENCE, DESCRIPTION, TO_WIN, ADJUSTED_WIN, ADJUSTED_LOSS, CURRENCY, REASON, EDIT_MODE, USELESS FROM {tableName} WITH (nolock)) WHERE GETDATE() >= USELESS;";

			using (SqlConnection connection = new SqlConnection(connectionString))
			{
				try
				{
					connection.Open();
					using (SqlCommand command = new SqlCommand(sqlString, connection))
					using (SqlDataReader reader = command.ExecuteReader())
					{
						while (reader.Read())
						{
							AuthorizationFragmentStruct authorizationFragment = new AuthorizationFragmentStruct();
							authorizationFragment.AUTHORIZATION_ID = reader.GetInt32(0);
							authorizationFragment.FRAGMENT_ID = reader.GetInt32(1);
							authorizationFragment.STATUS = reader.GetInt32(2);
							authorizationFragment.RISK = reader.GetDecimal(3);
							authorizationFragment.REFERENCE = reader.GetString(4);
							authorizationFragment.DESCRIPTION = reader.GetString(5);
							authorizationFragment.TO_WIN = reader.GetDecimal(6);
							authorizationFragment.ADJUSTED_WIN = reader.GetDecimal(7);
							authorizationFragment.ADJUSTED_LOSS = reader.GetDecimal(8);
							authorizationFragment.CURRENCY = reader.GetInt32(9);
							authorizationFragment.REASON = reader.GetInt32(10);
							authorizationFragment.EDIT_MODE = reader.GetInt32(11);
							allAuthFragments.Add(authorizationFragment);
						}
						reader.Close();
					}
				}
				finally
				{
					connection.Close();
				}

			}
			return allAuthFragments;
		}

        public override IEnumerable<MovementStruct> MovementsToArchived(string tableName, int max_months_to_archive)
        {
			List<MovementStruct> allMovements = new List<MovementStruct>();

			string sqlString = $"SELECT DAY, SOURCE, MOVEMENT, AMOUNT, NEWBALANCE, NEWLOCKBALANCE, ACCOUNT_NUMBER, WHO, DOCUMENTNUMBER, STORE, REFERENCE, CONCEPT FROM {tableName} WITH (nolock) WHERE DATEDIFF(month, DAY, GETDATE()) >= {max_months_to_archive};";

			using (SqlConnection connection = new SqlConnection(connectionString))
			{
				try
				{
					connection.Open();
					using (SqlCommand command = new SqlCommand(sqlString, connection))
					using (SqlDataReader reader = command.ExecuteReader())
					{
						while (reader.Read())
						{
							int index = 0;
							MovementStruct movement = new MovementStruct();
							movement.DAY = reader.GetDateTime(index++);
							movement.SOURCE = reader.GetInt32(index++);

							string movementTxt = reader.GetString(index++);
							int posicion = LongTermControllerUtils.posicionDelMovement(movementTxt);
							movement.MOVEMENT = posicion;

							movement.AMOUNT = reader.GetInt32(index++);
							movement.NEWBALANCE = reader.GetDecimal(index++);
							movement.NEWLOCKBALANCE = reader.GetDecimal(index++);
							movement.ACCOUNT_NUMBER = reader.GetString(index++);
							movement.WHO = reader.GetInt32(index++);
							movement.DOCUMENTNUMBER = reader.GetString(index++);
							movement.STORE = reader.GetInt32(index++);
							movement.REFERENCE = reader.GetString(index++);
							movement.CONCEPT = reader.GetString(index++);
							allMovements.Add(movement);
						}
						reader.Close();
					}
				}
				finally
				{
					connection.Close();
				}

			}

			return allMovements;
		}

        public override void MarkArchivedAuthFragmentsInDB(string tableName)
        {
			string updateQuery = $"UPDATE {tableName} SET ARCHIVED = 1 WHERE GETDATE() >= USELESS;";

			ExecuteCommand(updateQuery);
		}

        public override void MarkArchivedMovements(string tableName, int max_months_to_archive)
        {
			string updateQuery = $"UPDATE {tableName} SET ARCHIVED = 1 WHERE DATEDIFF(DAY, DAY, GETDATE()) >= {max_months_to_archive};";

			ExecuteCommand(updateQuery);
		}

		internal static string CreateFragmentsStorageQuery(string atAddress, bool subfixRequired = true)
		{
			if (subfixRequired) atAddress = "C" + atAddress;

			StringBuilder statement = new StringBuilder();

			statement
			.Append("CREATE TABLE IF NOT EXISTS ").Append(atAddress).Append("_authfragments")
			.Append('(')
			.Append("AUTHORIZATION_ID INT NOT NULL,")
			.Append("FRAGMENT_ID Smallint NOT NULL,")
			.Append("STATUS TINYINT NOT NULL,")
			.Append("RISK DECIMAL(10,2) NOT NULL,")
			.Append("REFERENCE VARCHAR(20) NOT NULL,")
			.Append("DESCRIPTION TEXT NOT NULL,")
			.Append("TO_WIN DECIMAL(10,2) NOT NULL,")
			.Append("ADJUSTED_WIN DECIMAL(10,2) NOT NULL,")
			.Append("ADJUSTED_LOSS DECIMAL(10,2) NOT NULL,")
			.Append("CURRENCY TINYINT NOT NULL,")
			.Append("REASON INT NOT NULL,")
			.Append("ACCOUNT_NUMBER VARCHAR(60) NOT NULL,")
			.Append("USELESS DATETIME NOT NULL,")
			.Append("EDIT_MODE BIT NOT NULL DEFAULT 0")
			.Append(");")

			.Append("CREATE INDEX IDX_").Append(atAddress).Append("_authfragments")
			.Append(" ON ").Append(atAddress).Append("_authfragments")
			.Append("(AUTHORIZATION_ID);");


			return statement.ToString();
		}

        internal override string CreateFragmentsStorage(string atAddress, bool subfixRequired = true)
        {
			return CreateFragmentsStorageQuery(atAddress, subfixRequired);
		}

		internal static string CreateMovementsStorageQuery(string tableName, Currencies.CODES currencyCode, bool isAddress = true)
		{
			StringBuilder statement = new StringBuilder();
			if (isAddress)
			{
				tableName = GetTableName(tableName, Coinage.Coin(currencyCode));
			}

			statement.Append("CREATE TABLE IF NOT EXISTS ").Append(tableName)
				.Append("(")
				.Append("DAY DATETIME NOT NULL,")
				.Append("SOURCE TINYINT NOT NULL,")
				.Append("MOVEMENT VARCHAR(1) NOT NULL,")
				.Append("AMOUNT DECIMAL(16,8) NOT NULL,")
				.Append("NEWBALANCE DECIMAL(16,8) NOT NULL,")
				.Append("NEWLOCKBALANCE DECIMAL(16,8) NOT NULL,")
				.Append("ACCOUNT_NUMBER VARCHAR(60) NOT NULL,")
				.Append("PROCESSORID INT NOT NULL,")
				.Append("WHO INT,")
				.Append("DOCUMENTNUMBER VARCHAR(45) NOT NULL,")
				.Append("STORE TINYINT NOT NULL,")
				.Append("REFERENCE VARCHAR(30) NULL,")
				.Append("CONCEPT TEXT,")
				//.Append("ARCHIVED TINYINT NOT NULL DEFAULT 0")
				.Append(");");

			statement.Append($"CREATE INDEX {tableName}_DAY ON {tableName} (DAY);");

			return statement.ToString();
		}

		internal override string CreateMovementsStorage(string tableName, Currencies.CODES currencyCode, bool isAddress = true)
        {
			return CreateMovementsStorageQuery(tableName, currencyCode, isAddress);
		}
    }
}