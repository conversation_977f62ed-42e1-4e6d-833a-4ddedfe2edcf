﻿using GamesEngine.PurchaseOrders;
using GamesEngine.Settings;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace LoyaltyAPI.Controllers
{
	public class ReportController : AuthorizeController
	{
		[HttpGet("api/loyalty/marketing/avatars")]
		[Authorize(Roles = "a18,b46")]
		public async Task<IActionResult> AvatarsReportAsync(string fromDate, string toDate, bool includePlayersWithoutAvatar)
		{
			if (string.IsNullOrEmpty(fromDate)) return NotFound($"{nameof(fromDate)} has not a valid value");
			if (string.IsNullOrEmpty(toDate)) return NotFound($"{nameof(toDate)} has not a valid value");

			var scriptQuery = includePlayersWithoutAvatar ?
				$"report = marketingKPIs.PlayersWithoutAvatarAndWithAvatarUpdatedBetween('{fromDate}','{toDate}');" :
				$"report = marketingKPIs.PlayersWithAvatarUpdatedBetween('{fromDate}','{toDate}');";
			var result = await LoyaltyAPI.Loyalty.PerformQryAsync(HttpContext, $@"
                {{
                    marketingKPIs = company.MarketingKPIs();
                    {scriptQuery}
                    print report.TotalPlayers totalPlayers;
                    print report.PercentageAvatarsUpdated percentageAvatarsUpdated;
                    for (players : report.Players)
                    {{
                        player = players;
                        print player.Nickname nickname;
                        print player.Avatar.Path avatar;
                        print company.CustomerByPlayer(player).AccountNumber accountNumber;                        
                        if (marketingKPIs.HasRegisteredAvatarUpdateFor(player))
                        {{
                            print marketingKPIs.LastAvatarUpdateFor(player) lastAvatarUpdate;
                        }}
                    }}
                }}
            ");
			return result;
		}

		public enum CalendarInterval
		{
			minute,
			hour,
			day,
			week,
			month,
			quarter,
			year
		}

		[HttpGet("api/loyalty/{storeId:int}/report/sales")]
		[Authorize(Roles = "g2")]
		public async Task<IActionResult> SalesAsync(int storeId, CalendarInterval? timeInterval, DateTime endDate, DateTime initialDate, int from)
		{
			if (storeId <= 0) return BadRequest($"Parameter {nameof(storeId)} is not valid");
			if (!timeInterval.HasValue) timeInterval = CalendarInterval.day;
			if (endDate == null || endDate == default(DateTime)) endDate = DateTime.Now;
			if (initialDate == null || initialDate == default(DateTime)) initialDate = endDate.AddMonths(-4);
			if (from < 0) return BadRequest($"{nameof(from)} must be greater than 0.");

			const int size = 100;
			string query = $@"
				{{
				""from"":{from},
				""size"":{size},
				""query"": {{
					""bool"": {{
					  ""filter"": [
						{{
						  ""match"": {{
							""storeId"": ""{storeId}""
						  }}
						}},
						{{
						""range"" : {{
							""eventDate"" : {{
								""gte"": ""{initialDate.ToString("yyyy-MM-ddTHH:mm:ss")}"", 
								""lte"": ""{endDate.ToString("yyyy-MM-ddTHH:mm:ss")}""
							}}
						}}
						}}
					  ]
					}}
				  }},
					""aggs"" : {{
						""sales_per_time"" : {{
							""date_histogram"" : {{
								""field"" : ""eventDate"",
								""calendar_interval"" : ""{timeInterval.Value}""
							}},
							""aggs"": {{
								""sales"": {{
									""sum"": {{
										""field"": ""amount""
									}}
								}}
							}}
						}},
						""sum_day_sales"": {{
							""sum_bucket"": {{
								""buckets_path"": ""sales_per_time>sales"" 
							}}
						}},
						""customer_count"": {{
							""cardinality"": {{
								""field"": ""accountNumber""
							}}
						}},
						""avg_amount"": {{
							""avg"":{{
								""field"": ""amount""
							}}
						}},
						""last_eventDate"": {{
							""max"": {{
								""field"": ""eventDate""
							}}
						}}
					}}
				}}
			";
			var searchResponse = await HttpELKClient.GetInstance().SearchInSalesAsync(query);
			var successful = searchResponse.Success;
			var responseJson = searchResponse.Body;
			return Ok(responseJson);
		}

		[HttpGet("api/loyalty/{storeId:int}/report/loyalty")]
		[Authorize(Roles = "g1,g3,g4")]
		public async Task<IActionResult> LoyaltyAsync(int storeId, CalendarInterval? timeInterval, DateTime endDate, DateTime initialDate, int from)
		{
			if (storeId <= 0) return BadRequest($"Parameter {nameof(storeId)} is not valid");
			if (!timeInterval.HasValue) timeInterval = CalendarInterval.day;
			if (endDate == null || endDate == default(DateTime)) endDate = DateTime.Now;
			if (initialDate == null || initialDate == default(DateTime)) initialDate = endDate.AddMonths(-4);
			if (from < 0) return BadRequest($"{nameof(from)} must be greater than 0.");

			const int size = 100;
			string queryAmountOfAdquisitionsBeforeSpecificDate = $@"
				{{
					""size"":0,
					""query"": {{
						""bool"": {{
							""filter"": [
								{{
								  ""match"": {{
									""storeId"": ""{storeId}""
								  }}
								}},
								{{
									""match"" : {{
										""eventType"" : {{
										""query"" : ""adquisition""
										}}
									}}
								}}
							]
						}}
					}},
					""post_filter"":
					{{
						""range"" : {{
							""eventDate"" : {{
							""lte"": ""{initialDate.ToString("yyyy-MM-ddTHH:mm:ss")}""
							}}
						}}
					}},
					""aggs"" : {{
						""customer_count"": {{
							""cardinality"": {{
								""field"": ""accountNumber""
							}}
						}}
					}}
				}}";

			string totalOfAdquisitions = (await HttpELKClient.GetInstance().SearchInLoyaltyAsync(queryAmountOfAdquisitionsBeforeSpecificDate)).Body;
			string stringToFind = "\"value\":";
			int inicialPartOfTheresponse = totalOfAdquisitions.IndexOf(stringToFind);
			inicialPartOfTheresponse += stringToFind.Length;
			int endPartOfTheresponse = totalOfAdquisitions.IndexOf(",", inicialPartOfTheresponse);
			int amountOfAdquisitionsBeforeThePeriod = int.Parse(totalOfAdquisitions.Substring(inicialPartOfTheresponse, endPartOfTheresponse - inicialPartOfTheresponse));

			string queryRetentionAggregations = $@"
			{{
				""from"":{from},
				""size"":{size},
				""query"": {{
					""bool"": {{
				  ""must"" : [ 
					{{ ""terms"": {{ ""eventType"": [""retentionper{timeInterval.Value}"",""adquisition""] }} }}
				  ],
					  ""filter"": [
						{{
							""match"": {{
							""storeId"": ""{storeId}""
							}}
						}},
						{{
							""range"" : {{
								""eventDate"" : {{
									""gte"": ""{initialDate.ToString("yyyy-MM-ddTHH:mm:ss")}"", 
									""lte"": ""{endDate.ToString("yyyy-MM-ddTHH:mm:ss")}""
								}}
							}}
						}}
					  ]
					}}
				}},
				""aggs"" : {{
					""loyalty_per_time"" : {{
						""date_histogram"" : {{
							""field"" : ""eventDate"",
							""calendar_interval"" : ""{timeInterval.Value}""
						}},
						""aggs"": {{
							""retentions"" : {{
								""filter"" : {{ 
									""term"": {{ ""eventType"": ""retentionper{timeInterval.Value}"" }} 
								}},
								""aggs"":{{
									""count"" : {{ ""value_count"" : {{ ""field"" : ""eventType.keyword"" }} }}
								}}
							}},
							""adquisitions"" : {{
								""filter"" : {{ 
									""term"": {{ ""eventType"": ""adquisition"" }} 
								}},
								""aggs"":{{
									""count"" : {{ ""value_count"" : {{ ""field"" : ""eventType.keyword"" }} }}
								}}
							}},
							""attrition"":{{ 
								""bucket_script"":{{ 
									""buckets_path"":{{
										""totalOfRetations"":""retentions>count""
									}},
									""script"":"" {amountOfAdquisitionsBeforeThePeriod} - params.totalOfRetations""
								}}
							}}
						}}
					}},
					""total_adquisitions"": {{
						""sum_bucket"": {{
							""buckets_path"": ""loyalty_per_time>adquisitions>count"" 
						}}
					}},
					""total_retentions"": {{
						""sum_bucket"": {{
							""buckets_path"": ""loyalty_per_time>retentions>count"" 
						}}
					}},
					""total_attrition"": {{
						""sum_bucket"": {{
							""buckets_path"": ""loyalty_per_time>attrition"" 
						}}
					}}
				}}
			}}";

			var acquisitionAndRetentions = await HttpELKClient.GetInstance().SearchInLoyaltyAsync(queryRetentionAggregations);

			string queryLastSales = $@"
			{{
				""size"":0,
				""aggs"": {{
					""group"": {{
						""terms"": {{
							""field"": ""accountNumber""
						}},
						""aggs"": {{
							""last_sales"": {{
								""top_hits"": {{
									""size"": 1,
										""sort"": [
										{{
											""eventDate"": {{
												""order"": ""desc""
												}}
										}}
										]
									}}
							}}
						}}
					}}
				}}
			}}";

			var lastSales = await HttpELKClient.GetInstance().SearchInSalesAsync(queryLastSales);

			return Ok($@"
				{{
					""totalOfAdquisitions"":{totalOfAdquisitions},
					""acquisitionsAndRetentions"":{acquisitionAndRetentions.Body},
					""lastSales"":{lastSales.Body}
				}}
			");
		}
	}

}
