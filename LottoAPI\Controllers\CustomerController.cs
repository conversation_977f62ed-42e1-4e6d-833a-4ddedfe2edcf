﻿using GamesEngine;
using GamesEngine.Finance;
using GamesEngine.Settings;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Newtonsoft.Json;
using System;
using System.IO;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Runtime.Serialization;
using System.Threading.Tasks;

using System.Runtime.Serialization.Json;
using System.Text;
using System.Net;
using static GamesEngine.Exchange.PaymentProcessorsAndActionsByDomains;
using GamesEngine.Exchange;
using Microsoft.AspNetCore.Authorization;
using GamesEngine.Domains;
using static GamesEngine.Finance.PaymentChannels;
using static town.connectors.CustomSettings;
using Connectors.town.connectors.drivers.hades;
using town.connectors;
using town.connectors.drivers;
using GamesEngine.Business;
using Connectors.town.connectors.drivers.artemis;
using town.connectors.drivers.artemis;
using GamesEngine.MessageQueuing;

namespace LottoAPI.Controllers
{
    public class CustomerController : AuthorizeController
    {
		[Obsolete]
		protected readonly IHostingEnvironment HostingEnvironment;

		[Obsolete]
		public CustomerController(IConfiguration configuration, IHostingEnvironment hostingEnv)
        {
            this.HostingEnvironment = hostingEnv;
        }

		[HttpPost("api/customer")]
        [Authorize(Roles = "player")]
        public async Task<IActionResult> ValidateAndCreateCustomerAsync([FromBody]Customer customer, [FromHeader(Name = "domain-url")] string domain)
		{
			if (String.IsNullOrWhiteSpace(customer.AccountNumber)) return NotFound($"Parameter {nameof(customer.AccountNumber)} is required");
			if (String.IsNullOrWhiteSpace(customer.Token)) return NotFound($"Parameter {nameof(customer.Token)} is required");
			if (String.IsNullOrWhiteSpace(customer.DefaultNickNameIfNotExists)) return NotFound($"Parameter {nameof(customer.DefaultNickNameIfNotExists)} is required");
			if (String.IsNullOrWhiteSpace(customer.DefaultAvatarIfNotExists)) return NotFound($"Parameter {nameof(customer.DefaultAvatarIfNotExists)} is required");
            if (String.IsNullOrWhiteSpace(HttpContext.Request.Headers["domain-url"])) return NotFound("Header 'domain-url' is not sent in request");

            var transaction = APMHelper.CurrentTransaction();
            try
            {
                bool isValid = await PaymentChannels.ValidateAsync(customer.Agent, customer.AccountNumber, customer.Token);
                if (!isValid)
                {
                    var msgError = $"User {customer.AccountNumber} is not valid.";
                    var ex = new GameEngineException(msgError);
                    APMHelper.CaptureException(ex);
                    return BadRequest(msgError);
                }
                var result = await LottoAPI.Actor.PerformQryAsync(HttpContext, $@"
                    {{
                        ItsNewOne = company.ExistsCustomer('{customer.AccountNumber}');
                        print ! ItsNewOne thePlayerItsNew;
                    }}
                ");

                if (!(result is OkObjectResult))
                {
                    GameEngineException ex;
                    if (result is ContentResult contentResult) ex = new GameEngineException($"Error: {contentResult.Content}");
                    else if (result is ObjectResult objResult) ex = new GameEngineException($"Error: {objResult.Value}");
                    else ex = new GameEngineException($"Error: {result}");
                    APMHelper.CaptureException(ex);
                    throw ex;
                }

                OkObjectResult o = (OkObjectResult)result;
                string json = o.Value.ToString();
                ExistCustomer existCustomer = JsonConvert.DeserializeObject<ExistCustomer>(json);

                bool itsNewOne = existCustomer.ThePlayerItsNew;
                if (itsNewOne)
                {
                    var existsInOldGame = false;
                    if ((Agents)customer.Agent == Agents.TEST_BOOK)
                    {
                        var paymentProcessor = WholePaymentProcessor.Instance().SearchOtherProcessorBy(typeof(CustomerInformation));
                        using (RecordSet recordSet = paymentProcessor.GetRecordSet())
                        {
                            recordSet.SetParameter("customerId", customer.AccountNumber);

                            existsInOldGame = await paymentProcessor.ExecuteAsync<bool>(DateTime.Now, recordSet);
                        }
                    }
                    var replicateCommand = $"customer.ReplicateInOtherNodes(itIsThePresent, true, now,'{customer.DefaultNickNameIfNotExists}','{customer.DefaultAvatarIfNotExists}');";
                    var scriptForAffiliate = new StringBuilder();
                    if (customer.Agent == (int)Agents.ARTEMIS)
                    {
                        PlayerBalanceBody infoBalance;
                        var paymentProcessor = WholePaymentProcessor.Instance().SearchBalanceProcesorBy(typeof(BalanceInfo));
                        using (RecordSet recordSet = paymentProcessor.GetRecordSet())
                        {
                            recordSet.SetParameter("customerId", customer.AccountNumber);

                            infoBalance = await paymentProcessor.ExecuteAsync<PlayerBalanceBody>(DateTime.Now, recordSet);
                        }

                        replicateCommand = $"customer.ReplicateInOtherNodes(itIsThePresent, true, now,'{customer.DefaultNickNameIfNotExists}','{customer.DefaultAvatarIfNotExists}', {infoBalance.idAgent}, '{infoBalance.agent}');";
                    
                        result = await LottoAPI.Actor.PerformQryAsync(HttpContext, $@"
                            {{
                                path = marketplace.Name + '/{infoBalance.idAgent}';
                                existsAgent = marketplace.ExistsAgent(path);
                                print existsAgent existsAgent;
                                print marketplace.Name marketplaceName;
                                currentStore = company.Sales.CurrentStore;
                                print currentStore.Id storeId;
                                print currentStore.Name storeName;
                                print currentStore.Alias storeAlias;
                                existsDomain = company.Sales.ExistsDomain('{domain}');
                                print existsDomain existsDomain;
                                if (existsAgent)
                                {{
                                    agent = marketplace.SearchAgent(path);
                                    containsStore = agent.ContainsStore(currentStore.Id);
                                    print containsStore containsStore;
                                    if (existsDomain)
                                    {{
                                        domain = company.Sales.DomainFrom('{domain}');
                                        containsDomain = agent.ContainsDomain(currentStore.Id, domain);
                                        print containsDomain containsDomain;
                                    }}
                                }}
                            }}
                        ");

                        if (!(result is OkObjectResult))
                        {
                            GameEngineException ex;
                            if (result is ContentResult contentResult) ex = new GameEngineException($"Error: {contentResult.Content}");
                            else if (result is ObjectResult objResult) ex = new GameEngineException($"Error: {objResult.Value}");
                            else ex = new GameEngineException($"Error: {result}");
                            APMHelper.CaptureException(ex);
                            throw ex;
                        }

                        o = (OkObjectResult)result;
                        json = o.Value.ToString();
                        var agentExistence = JsonConvert.DeserializeObject<CatalogMemberExistence>(json);
                        if (!agentExistence.existsAgent)
                        {
                            scriptForAffiliate.Append("agent = marketplace.AddAgent('").Append(infoBalance.idAgent).Append("');");
                        }
                        if (!agentExistence.existsDomain)
                        {
                            scriptForAffiliate.Append("Eval('domainId =' + company.Sales.NextDomainConsecutive() + ';');");
                            scriptForAffiliate.Append("domain = company.Sales.CreateDomain(ItIsThePresent, domainId, '").Append(domain).Append("', ").Append(Agents.ARTEMIS).Append(");");
                            scriptForAffiliate.Append("company.Sales.CurrentStore.Add(domain);");
                            scriptForAffiliate.Append("company.Sales.CurrentStore.DisableDomain(domain);");
                        }

                        var path = $"{agentExistence.marketplaceName}/{infoBalance.idAgent}";
                        if (!agentExistence.containsDomain)
                        {
                            if (agentExistence.existsAgent)
                            {
                                scriptForAffiliate.Append("agent = marketplace.SearchAgent('").Append(path).Append("');");
                            }
                            if (agentExistence.existsDomain)
                            {
                                scriptForAffiliate.Append("domain = company.Sales.DomainFrom('").Append(domain).Append("');");
                            }
                            scriptForAffiliate.Append("agent.AssignToCurrentStore(domain);");
                        }
                        if (Integration.UseKafka || Integration.UseKafkaForAuto)
                        {
                            var msg = new AgentAddMessage(infoBalance.idAgent, path, domain, agentExistence.storeId);
                            Integration.Kafka.Send(true, Integration.Kafka.TopicForCatalog, msg);
                        }
                    }

                    result = await LottoAPI.Actor.PerformCmdAsync(HttpContext, $@"
					customer = company.CreateCustomer('{customer.AccountNumber}',{((Agents)customer.Agent).ToString()});
					    {{
                            print !{existsInOldGame} itsNewOne;
						    print customer.Player.Id password;
						    print customer.Player.Id playerId;
                            {replicateCommand}
                            {scriptForAffiliate}
					    }}
				    ");
                    if (!(result is OkObjectResult))
                    {
                        GameEngineException ex;
                        if (result is ContentResult contentResult) ex = new GameEngineException($"Error: {contentResult.Content}");
                        else if (result is ObjectResult objResult) ex = new GameEngineException($"Error: {objResult.Value}");
                        else ex = new GameEngineException($"Error: {result}");
                        APMHelper.CaptureException(ex);
                        throw ex;
                    }

                    return result;
                }
                else
                {
                    result = await LottoAPI.Actor.PerformQryAsync(HttpContext, $@"
					    {{
						    customer = company.CustomerByAccountNumber('{customer.AccountNumber}');
						    print false itsNewOne;
						    print customer.Player.Id password;
						    print customer.Player.Id playerId;
					    }}
				    ");
                    if (!(result is OkObjectResult))
                    {
                        GameEngineException ex;
                        if (result is ContentResult contentResult) ex = new GameEngineException($"Error: {contentResult.Content}");
                        else if (result is ObjectResult objResult) ex = new GameEngineException($"Error: {objResult.Value}");
                        else ex = new GameEngineException($"Error: {result}");
                        APMHelper.CaptureException(ex);
                        throw ex;
                    }

                    return result;
                }
            }
            catch (Exception ex)
            {
                APMHelper.CaptureException(ex, $"AccountNumber: {customer.AccountNumber}");
                throw;
            }
            finally
            {
                APMHelper.EndTransaction();
            }
        }

        struct CatalogMemberExistence
        {
            public string marketplaceName { get; set; }
            public int storeId { get; set; }
            public string storeName { get; set; }
            public string storeAlias { get; set; }
            public bool existsAgent { get; set; }
            public bool existsDomain { get; set; }
            public bool containsDomain { get; set; }
            public bool containsStore { get; set; }
        }

        /// <summary>
        /// Get account number
        /// </summary>
        /// <param name="id" >Example: ID012345678901234567890123456789012345</param>   
        [HttpGet("api/customer/accountNumber")]
        [Authorize(Roles = "player")]
        public async Task<IActionResult> PlayerAccountNumberAsync()
        {
            string playerId = Security.PlayerId(HttpContext);
            if (String.IsNullOrWhiteSpace(playerId)) return BadRequest(nameof(playerId));
            string validPlayerId = Validator.StringEscape(playerId);

            var result = await LottoAPI.Actor.PerformQryAsync(HttpContext, $@"
                {{
                    player = company.Players.SearchPlayer('{validPlayerId}');
                    customer = company.CustomerByPlayer(player);
                    print customer.accountNumber AccountNumber;
                }}
            ");
            return result;
        }

        [HttpGet("api/customer/{playerId}/accountNumber")]
        [Authorize(Roles = "devops")]
        public async Task<IActionResult> PlayerAccountNumberAsync(string playerId)
        {
            if (string.IsNullOrWhiteSpace(playerId)) return BadRequest(nameof(playerId));
            string validPlayerId = Validator.StringEscape(playerId);

            var result = await LottoAPI.Actor.PerformQryAsync(HttpContext, $@"
                {{
                    player = company.Players.SearchPlayer('{validPlayerId}');
                    customer = company.CustomerByPlayer(player);
                    print customer.accountNumber AccountNumber;
                }}
            ");
            return result;
        }

        [HttpGet("api/customer/affiliate")]
        [Authorize(Roles = "player")]
        public async Task<IActionResult> PlayerAffiliateAsync()
        {
            string playerId = Security.PlayerId(HttpContext);
            if (String.IsNullOrWhiteSpace(playerId)) return BadRequest(nameof(playerId));
            string validPlayerId = Validator.StringEscape(playerId);

            var result = await LottoAPI.Actor.PerformQryAsync(HttpContext, $@"
                {{
                    player = company.Players.SearchPlayer('{validPlayerId}');
                    customer = company.CustomerByPlayer(player);
                    print player.Id playerId;
                    print customer.AffiliateId affiliateId;
                }}
            ");
            return result;
        }

        [HttpGet("api/customer/{accountNumber}/existence")]
        [Authorize(Roles = "player")]
        public async Task<IActionResult> ExistsCustomerInCurrentOrOldGameAsync(string accountNumber)
        {
            if (String.IsNullOrEmpty(accountNumber)) return NotFound($"{nameof(accountNumber)} is required");
            var result = await LottoAPI.Actor.PerformQryAsync(HttpContext, $@"
                {{
                    exists = company.ExistsCustomer('{accountNumber}');
                    print exists exists;
                }}
            ");

            if (!(result is OkObjectResult))
            {
                throw new GameEngineException($@"Error:{((ObjectResult)result).Value.ToString()}");
            }

            OkObjectResult o = (OkObjectResult)result;
            string json = o.Value.ToString();
            var customerExistence = JsonConvert.DeserializeObject<CustomerExistence>(json);
            if ( ! customerExistence.Exists)
            {
                var paymentProcessor = WholePaymentProcessor.Instance().SearchOtherProcessorBy(typeof(CustomerInformation));
                var existsInOldGame = false;
                using (RecordSet recordSet = paymentProcessor.GetRecordSet())
                {
                    recordSet.SetParameter("customerId", accountNumber);

                    existsInOldGame = await paymentProcessor.ExecuteAsync<bool>(DateTime.Now, recordSet);
                }
                
                if (existsInOldGame)
                {
                    result = await LottoAPI.Actor.PerformQryAsync(HttpContext, $@"
                    {{
                        print {existsInOldGame} exists;
                    }}
                ");
                }
            }
            return result;
        }

        [HttpGet("api/customer/{accountNumber}/playerId")]
        [Authorize(Roles = "player,devops")]
        public async Task<IActionResult> PlayerIdByAccountNumberAsync(string accountNumber)
        {
            if (String.IsNullOrWhiteSpace(accountNumber)) return NotFound($"Parameter {nameof(accountNumber)} is required");
            var result = await LottoAPI.Actor.PerformQryAsync(HttpContext, $@"
                {{
                    player = company.CustomerByAccountNumber('{accountNumber}').Player;
                    print player.Id playerId;
                }}
            ");
            return result;
        }

        [HttpGet("api/customer/existence")]
        [Authorize(Roles = "player")]
        public async Task<IActionResult> ExistsAccountNumberAsync(string accountNumber)
        {
            if (String.IsNullOrEmpty(accountNumber)) return NotFound($"{nameof(accountNumber)} is required");
            var result = await LottoAPI.Actor.PerformQryAsync(HttpContext, $@"
                {{
                    exists = company.ExistsCustomer('{accountNumber}');
                    print exists exists;
                }}
            ");
            return result;
        }
        
        [HttpGet("api/domains/{domain}/logoutUrl")]
        [Authorize(Roles = "player")]
        public async Task<IActionResult> LogoutUrlAsync(string domain)
        {
            if (String.IsNullOrEmpty(domain)) return NotFound($"{nameof(domain)}");
            IPAddress ipAddress;
            if (IPAddress.TryParse(domain, out ipAddress))
            {
                return Ok($"{{\"logoutUrl\":\"http://nimbus.la/portafolio/\"}}");
            }
            var result = await LottoAPI.Actor.PerformQryAsync(HttpContext, $@"
                {{
					if(company.Sales.ExistsDomain('{domain}'))
					{{
						domain = company.Sales.DomainFrom('{domain}');
                        print domain.ResourceUrl('logout') logoutUrl;
					}}
                }}
            ");
            return result;
        }

        [DataContract(Name = "customer")]
        public class Customer
        {
            [DataMember(Name = "accountNumber")]
            public string AccountNumber { get; set; }
            [DataMember(Name = "token")]
            public string Token { get; set; }
            [DataMember(Name = "defaultNickNameIfNotExists")]
            public string DefaultNickNameIfNotExists { get; set; }
            [DataMember(Name = "defaultAvatarIfNotExists")]
            public string DefaultAvatarIfNotExists { get; set; }
            [DataMember(Name = "balance")]
            public decimal Balance { get; set; }
            [DataMember(Name = "agent")]
            public int Agent { get;  set; }
		}

        [DataContract(Name = "finance")]
        public class Finance
        {
            [DataMember(Name = "balance")]
            public decimal Balance { get; set; }
        }

        [DataContract(Name = "ExistCustomer")]
		public class ExistCustomer
		{
			[DataMember(Name = "thePlayerItsNew")]
			public bool ThePlayerItsNew { get; set; }
		}

        [DataContract(Name = "ExistCustomer")]
        public class CustomerExistence
        {
            [DataMember(Name = "exists")]
            public bool Exists { get; set; }
        }
    }
}
