﻿using GamesEngine;
using GamesEngine.Business;
using GamesEngine.Custodian;
using GamesEngine.Custodian.Operations;
using GamesEngine.Exchange;
using GamesEngine.Finance;
using GamesEngine.Settings;
using MySql.Data.MySqlClient;
using Puppeteer.EventSourcing;
using System;
using System.Collections.Generic;
using System.Data.Common;
using System.Diagnostics;
using System.Text;
using System.Threading.Channels;
using System.Threading.Tasks;
using town.connectors.drivers;
using static GamesEngine.Business.WholePaymentProcessor;
using static GamesEngine.Custodian.Operations.Operation;
using static town.connectors.CustomSettings;
using static town.connectors.drivers.Result;

namespace GamesEngine.Custodian.Persistance
{
	public abstract class Storage
	{
		internal abstract Task<bool> TestAsync();
		internal abstract void Create();
		internal abstract long SaveDomainIfNotExist(string domain);
		internal abstract void SaveApprover(ApproverCreationMessage approver);
		internal abstract Task<IEnumerable<StoredOperation>> ListPendingOperationsAsync(int profileId, OperationSchedule scheduled, DateTime initDate, DateTime finalDate, int amountOfRows, long inicialIndex);
		internal abstract Task<IEnumerable<StoredOperation>> ListInprocessOperationsAsync(int approver, OperationExecution scheduled, DateTime initDate, DateTime finalDate, int amountOfRows, long inicialIndex);
		internal abstract Task<IEnumerable<StoredOperation>> ListCompleteOperationsAsync(int approver, DateTime initDate, DateTime finalDate, int amountOfRows, long inicialIndex, TransactionType[] transactionTypes);
		internal abstract void InsertOperation(InternalOperationUpdateMessage operation, long domainId);
		internal abstract void IncreaseTimestamp(int transactionId);
		internal abstract Task<List<ApproverStored>> ListPendingOperationApproversAsync(int pendingOperationId);

		internal abstract void InsertOperationWithProfiles(InternalOperationUpdateMessageWithProfiles operation, long domainId);
		internal abstract void Save(List<DisbursementExecutionMessage> disbursementExecutionMessages);
		internal abstract Task<DisbursementExecutedResponse> DisbursementExecutedResponseAsync(int transactionId);
		internal abstract void SaveProfile(ProfileCreationMessage operationMessage);
		internal abstract Task<List<ProfileCreationMessage>> ListProfileAsync();
		internal abstract void SaveAccountNumber(AccountNumberCreationMessage operationMessage);
		internal abstract void SaveCurrency(CoinCreationMessage msg);
	}

	public enum OperationSchedule
    {
		Both, 
		Scheduled, 
		Unscheduled
	}

	public enum OperationExecution
	{
		Any,
		Scheduled,
		Unexecuted
	}

	internal class MysqlConnection : Storage
	{
		private string connectionString;

		internal MysqlConnection(string connectionString)
		{
			if (string.IsNullOrEmpty(connectionString)) throw new GameEngineException(nameof(connectionString));

			this.connectionString = connectionString;
		}

		internal override void Create()
		{
			string sql = $@"
				CREATE TABLE IF NOT EXISTS currency (
					id INT NOT NULL AUTO_INCREMENT,
					sign VARCHAR(5) NOT NULL,
					isoCode VARCHAR(5) NOT NULL,
					uniCode VARCHAR(5) NOT NULL,
					decimalPrecision TINYINT NOT NULL,
					PRIMARY KEY (id));
				SET sql_mode='NO_AUTO_VALUE_ON_ZERO';
				INSERT IGNORE INTO currency(id, sign, isoCode, uniCode, decimalPrecision) VALUES
					(0, '$', 'FP', '$', 2),
					(2, '$', 'USD', '$', 2);

				CREATE TABLE IF NOT EXISTS accountNumbers (
					id INT NOT NULL AUTO_INCREMENT,
					number VARCHAR(100) NOT NULL,
					processorKey VARCHAR(60) NOT NULL,
					currencyId INT NOT NULL,
					INDEX CURRENCYID_FK (currencyId),
					FOREIGN KEY (currencyId)
						REFERENCES currency(id)
						ON DELETE CASCADE,
					PRIMARY KEY (id));

				CREATE TABLE IF NOT EXISTS operations (
					id INT NOT NULL AUTO_INCREMENT,
					transactionId INT NOT NULL,
					reference INT NOT NULL,
					amount DECIMAL(16, 8) NOT NULL,
					currencyId INT NOT NULL,
					creationDate DATETIME NOT NULL,
					date DATETIME NOT NULL,
					abbreviation ENUM('Withdrawal', 'Deposit') NOT NULL,
					fees DECIMAL(16, 8) NOT NULL,
					domainId INT NOT NULL,
					approvals INT NOT NULL,
					required_approvals INT NOT NULL,
					scheduled TINYINT NOT NULL,
					hasExecutions TINYINT NOT NULL,
					status ENUM('{StatusCodes.PENDING.ToString()}', '{StatusCodes.IN_PROCESS.ToString()}', '{StatusCodes.COMPLETE.ToString()}') NOT NULL,
					executionPercentage INT NOT NULL,
					identificationDocumentNumber VARCHAR(100) NOT NULL,
					accountId INT NOT NULL,
					INDEX ACCOUNTID_FK (accountId),
					FOREIGN KEY (accountId)
						REFERENCES accountNumbers(id)
						ON DELETE CASCADE,
					INDEX CURRENCYID_FK (currencyId),
					FOREIGN KEY (currencyId)
						REFERENCES currency(id)
						ON DELETE CASCADE,
					timestamp SMALLINT NOT NULL,
					PRIMARY KEY(id));

				CREATE TABLE IF NOT EXISTS domains (
					id INT NOT NULL AUTO_INCREMENT,
					url VARCHAR(100) NOT NULL,
					PRIMARY KEY (id));

				CREATE TABLE IF NOT EXISTS approvers (
					id INT NOT NULL AUTO_INCREMENT,
					name VARCHAR(100) NOT NULL,
					PRIMARY KEY (id), 
					UNIQUE INDEX name_UNIQUE(`name` ASC) VISIBLE);

				CREATE TABLE IF NOT EXISTS profiles (
					id INT NOT NULL AUTO_INCREMENT,
					name VARCHAR(100) NOT NULL,
					PRIMARY KEY (id), 
					UNIQUE INDEX name_UNIQUE(`name` ASC) VISIBLE);

				CREATE TABLE IF NOT EXISTS operationsApprovers (
					transactionId INT NOT NULL,
					approverId INT NOT NULL);

				CREATE TABLE IF NOT EXISTS operationsProfiles (
					transactionId INT NOT NULL,
					profileId INT NOT NULL);

				CREATE TABLE IF NOT EXISTS disbursementExecution (
					id INT NOT NULL AUTO_INCREMENT,
					transactionId INT NOT NULL,
					disbursementId INT NOT NULL,
					amount DECIMAL(16,8) NOT NULL,
					currencyId INT NOT NULL,
					date DATETIME NOT NULL,
					scheduledDate DATETIME NOT NULL,
					accountNumberId INT NOT NULL,
					status ENUM('APPROVED', 'REJECTED') NOT NULL,
					user VARCHAR(100) NOT NULL,
					INDEX CURRENCYID_FK (currencyId),
					FOREIGN KEY (currencyId)
						REFERENCES currency(id)
						ON DELETE CASCADE,
					PRIMARY KEY (id));
			";

			try
			{
				MySqlConnection conn = new MySqlConnection(connectionString);
				conn.Open();

				MySqlCommand cmd = new MySqlCommand(sql, conn);
				cmd.ExecuteNonQuery();

				conn.Close();
			}
			catch (Exception e)
			{
				Loggers.GetIntance().Db.Error($@"sql:{sql}", e);
				ErrorsSender.Send(e, sql);
				throw;
			}
		}
		internal override async Task<bool> TestAsync()
		{
			try
			{
				using (MySqlConnection connection = new MySqlConnection(connectionString))
				{
					await connection.OpenAsync();
					return true;
				}
			}
			catch (Exception)
			{
				return false;
			}
		}
		internal override long SaveDomainIfNotExist(string domain)
		{
			if (string.IsNullOrEmpty(domain)) domain = "N/A";

			long result = 0;
			bool found = false;
			string sql = "";
			try
			{
				sql = $@"
				select 
					id
				from domains
				WHERE  
					url = '{domain}'
				LIMIT 1 ";

				using (MySqlConnection conn = new MySqlConnection(connectionString))
				{
					conn.Open();
					using (MySqlCommand cmd = new MySqlCommand(sql, conn))
					{
						using (DbDataReader rdr = cmd.ExecuteReader())
						{
							while (rdr.Read())
							{
								result = rdr.GetInt32(0);
								found = true;
							}
						}
					}
					conn.Close();
				}
			}
			catch (Exception e)
			{
				Loggers.GetIntance().Db.Error($@"sql:{sql}", e);
				ErrorsSender.Send(e, sql);
				throw;
			}

			if (found) return result;

			try
			{
				sql = $@"
						INSERT INTO domains
							(url)
						VALUES
							('{domain}');";

				MySqlConnection conn = new MySqlConnection(connectionString);
				conn.Open();
				MySqlCommand cmd = new MySqlCommand(sql, conn);
				cmd.ExecuteNonQuery();
				result = cmd.LastInsertedId;
				conn.Close();
			}
			catch (Exception e)
			{
				Loggers.GetIntance().Db.Error($@"sql:{sql}", e);
				ErrorsSender.Send(e, sql);
				throw;
			}

			return result;
		}
        internal override void SaveApprover(ApproverCreationMessage approver)
        {
            if (approver == null) throw new ArgumentNullException(nameof(approver));
            string sql = "";
            try
            {
                try
                {
                    sql = $@"
                        INSERT INTO approvers
                            (id, name)
                        VALUES
                            ({approver.AccountId}, '{approver.Email}')
                        ON DUPLICATE KEY UPDATE name = '{approver.Email}'; ";

                    MySqlConnection conn = new MySqlConnection(connectionString);
                    conn.Open();
                    MySqlCommand cmd = new MySqlCommand(sql, conn);
                    cmd.ExecuteNonQuery();
                    conn.Close();
                }
                catch (Exception e)
                {
                    Loggers.GetIntance().Db.Error($@"sql:{sql}", e);
                    ErrorsSender.Send(e, sql);
                    throw;
                }

                WebHookClientRequest.Instance.SendWebHook(DateTime.Now, sql, "approvers", "Guardian");
            }
            catch (Exception webhookEx)
            {
                Loggers.GetIntance().Webhook.Error($@"sql:{sql}", webhookEx);
                throw;
            }
        }
		internal override async Task<IEnumerable<StoredOperation>> ListPendingOperationsAsync(int profileId, OperationSchedule scheduled, DateTime initDate, DateTime finalDate, int amountOfRows, long inicialIndex)
		{
			string filterByApprovers = profileId == 0 ? "" : $"AND op.profileId = '{profileId}'";

			string filterByScheduled = string.Empty;
			switch (scheduled)
			{
				case OperationSchedule.Both:
					break;
				case OperationSchedule.Scheduled:
					filterByScheduled = $"AND scheduled = true";
					break;
				case OperationSchedule.Unscheduled:
					filterByScheduled = $"AND scheduled = false";
					break;
				default:
					throw new GameEngineException($"No implementation for schedule '{scheduled}'");
			}

			string sql = $@"
				SELECT DISTINCT 
					po.id,
					po.transactionId,
					reference,
					amount,
					po.currencyId,
					accountId,
					creationDate,
					abbreviation,
					fees,
					domainId,
					approvals,
					required_approvals,
					scheduled,
					date,
					do.url url,
					status,
					hasExecutions,
					executionPercentage,
					identificationDocumentNumber
				FROM operations po 
					LEFT OUTER JOIN domains do ON do.id = po.domainId
					LEFT OUTER JOIN operationsProfiles op ON op.transactionId = po.transactionId
					LEFT OUTER JOIN profiles p ON p.id = op.profileId
				WHERE  
					status = '{StatusCodes.PENDING}'
					AND timestamp = 0 
					AND DATE(date) >= '{initDate.ToString("yyyy-MM-dd")}'
					AND DATE(date) <= '{finalDate.ToString("yyyy-MM-dd")}'
					{filterByApprovers}
					{filterByScheduled}
				ORDER BY date ASC
				LIMIT {amountOfRows} OFFSET {inicialIndex} ";

			List<StoredOperation> result = new List<StoredOperation>();

			try
			{
				using (MySqlConnection conn = new MySqlConnection(connectionString))
				{
					await conn.OpenAsync();
					using (MySqlCommand cmd = new MySqlCommand(sql, conn))
					{
						using (DbDataReader rdr = await cmd.ExecuteReaderAsync())
						{
							while (await rdr.ReadAsync())
							{
								StoredOperation obj = new StoredOperation();
								obj.Id = rdr.GetInt32(0);
								obj.TransactionId = rdr.GetInt32(1);
								obj.Reference = rdr.GetInt32(2);
								obj.Value = rdr.GetDecimal(3);
								obj.CurrencyId = rdr.GetInt32(4);
								obj.ProcessorAccountId = rdr.GetInt32(5);
								obj.CreationDate = rdr.GetDateTime(6);
								obj.CreationDateFormated = obj.CreationDate.ToString("dd/MM/yyyy h:mm:ss tt");
								obj.Abbreviation = rdr.GetString(7);
								obj.Fees = rdr.GetDecimal(8);
								obj.DomainId = rdr.GetInt32(9);
								obj.Approvals = rdr.GetInt32(10);
								obj.Required_approvals = rdr.GetInt32(11);
								obj.Scheduled = rdr.GetBoolean(12);
								obj.Date = rdr.GetDateTime(13);
								obj.DateDateFormated = obj.Date.ToString("dd/MM/yyyy h:mm:ss tt");
								obj.DomainUrl = rdr.GetString(14);
								obj.Status = rdr.GetString(15);
								obj.HasExecutions = rdr.GetBoolean(16);
								obj.PercentageExecuted = rdr.GetInt32(17);
								obj.CustomerId = rdr.GetString(18);
								Currency amount = Currency.Factory(obj.Coin, obj.Value);
								obj.ValueFormatted = amount.ToDisplayFormat();

								result.Add(obj);
							}
						}
					}
				}
			}
			catch (Exception e)
			{
				Loggers.GetIntance().Db.Error($@"sql:{sql}", e);
				ErrorsSender.Send(e, sql);
				throw;
			}
			return result;
		}
		internal override async Task<IEnumerable<StoredOperation>> ListInprocessOperationsAsync(int profileId, OperationExecution executed, DateTime initDate, DateTime finalDate, int amountOfRows, long inicialIndex)
		{
			string filterByApprovers = profileId == 0 ? "" : $"AND op.profileId = '{profileId}'";
			string filterByScheduled = string.Empty;
			switch (executed)
			{
				case OperationExecution.Any:
					break;
				case OperationExecution.Scheduled:
					filterByScheduled = $"AND scheduled = true";
					break;
				case OperationExecution.Unexecuted:
					filterByScheduled = $"AND executionPercentage >= 0 AND executionPercentage < 100";
					break;
				default:
					throw new GameEngineException($"No implementation for schedule '{executed}'");
			}
			string sql = $@"
				SELECT DISTINCT 
					po.id,
					po.transactionId,
					reference,
					amount,
					po.currencyId,
					accountId,
					creationDate,
					abbreviation,
					fees,
					domainId,
					approvals,
					required_approvals,
					scheduled,
					date,
					do.url,
					status,
					hasExecutions,
					executionPercentage,
					identificationDocumentNumber
				FROM operations po 
					LEFT OUTER JOIN domains do on do.id = po.domainId
					LEFT OUTER JOIN operationsProfiles op ON op.transactionId = po.transactionId
					LEFT OUTER JOIN profiles p ON p.id = op.profileId
				WHERE
					status = '{StatusCodes.IN_PROCESS}'
					AND timestamp = 0 
					AND DATE(date) >= '{initDate.ToString("yyyy-MM-dd")}'
					AND DATE(date) <= '{finalDate.ToString("yyyy-MM-dd")}'
					{filterByApprovers}
					{filterByScheduled}
				order by date ASC
				LIMIT {amountOfRows} OFFSET {inicialIndex} ";

			List<StoredOperation> result = new List<StoredOperation>();

			try
			{
				using (MySqlConnection conn = new MySqlConnection(connectionString))
				{
					await conn.OpenAsync();
					using (MySqlCommand cmd = new MySqlCommand(sql, conn))
					{
						using (DbDataReader rdr = await cmd.ExecuteReaderAsync())
						{
							while (await rdr.ReadAsync())
							{
								StoredOperation obj = new StoredOperation();
								obj.Id = rdr.GetInt32(0);
								obj.TransactionId = rdr.GetInt32(1);
								obj.Reference = rdr.GetInt32(2);
								obj.Value = rdr.GetDecimal(3);
								obj.CurrencyId = rdr.GetInt32(4);
								obj.ProcessorAccountId = rdr.GetInt32(5);
								obj.CreationDate = rdr.GetDateTime(6);
								obj.CreationDateFormated = obj.CreationDate.ToString("dd/MM/yyyy h:mm:ss tt");
								obj.Abbreviation = rdr.GetString(7);
								obj.Fees = rdr.GetDecimal(8);
								obj.DomainId = rdr.GetInt32(9);
								obj.Approvals = rdr.GetInt32(10);
								obj.Required_approvals = rdr.GetInt32(11);
								obj.Scheduled = rdr.GetBoolean(12);
								obj.Date = rdr.GetDateTime(13);
								obj.DateDateFormated = obj.Date.ToString("dd/MM/yyyy h:mm:ss tt");
								obj.DomainUrl = rdr.GetString(14);
								obj.Status = rdr.GetString(15);
								obj.HasExecutions = rdr.GetBoolean(16);
								obj.PercentageExecuted = rdr.GetInt32(17);
								obj.CustomerId = rdr.GetString(18);
								Currency amount = Currency.Factory(obj.Coin, obj.Value);
								obj.ValueFormatted = amount.ToDisplayFormat();

								result.Add(obj);
							}
						}
					}
				}
			}
			catch (Exception e)
			{
				Loggers.GetIntance().Db.Error($@"sql:{sql}", e);
				ErrorsSender.Send(e, sql);
				throw;
			}
			return result;
		}
		internal override async Task<IEnumerable<StoredOperation>> ListCompleteOperationsAsync(int profileId, DateTime initDate, DateTime finalDate, int amountOfRows, long inicialIndex, TransactionType[] transactionTypes)
		{
			string filterByApprovers = profileId == 0 ? "" : $"AND op.profileId = '{profileId}'";
			string filterByTransactionTypes = (transactionTypes == null || transactionTypes.Length == 0) ? "" : $"AND abbreviation in ('{string.Join("','", transactionTypes)}')";
			string sql = $@"
				SELECT DISTINCT 
					po.id,
					po.transactionId,
					reference,
					amount,
					po.currencyId,
					accountId,
					creationDate,
					abbreviation,
					fees,
					domainId,
					approvals,
					required_approvals,
					scheduled,
					date,
					do.url,
					status,
					hasExecutions,
					executionPercentage,
					identificationDocumentNumber
				FROM operations po 
					LEFT OUTER JOIN domains do on do.id = po.domainId
					LEFT OUTER JOIN operationsProfiles op ON op.transactionId = po.transactionId
					LEFT OUTER JOIN profiles p ON p.id = op.profileId
				WHERE
					( status = '{StatusCodes.COMPLETE}'
					OR ( po.hasExecutions = true AND status = '{StatusCodes.IN_PROCESS}' ) )
					AND timestamp = 0 
					AND DATE(date) >= '{initDate.ToString("yyyy-MM-dd")}'
					AND  DATE(date) <= '{finalDate.ToString("yyyy-MM-dd")}'
					{filterByApprovers}
					{filterByTransactionTypes}
				order by date ASC
				LIMIT {amountOfRows} OFFSET {inicialIndex} ";

			List<StoredOperation> result = new List<StoredOperation>();

			try
			{
				using (MySqlConnection conn = new MySqlConnection(connectionString))
				{
					await conn.OpenAsync();
					using (MySqlCommand cmd = new MySqlCommand(sql, conn))
					{
						using (DbDataReader rdr = await cmd.ExecuteReaderAsync())
						{
							while (await rdr.ReadAsync())
							{
								StoredOperation obj = new StoredOperation();
								obj.Id = rdr.GetInt32(0);
								obj.TransactionId = rdr.GetInt32(1);
								obj.Reference = rdr.GetInt32(2);
								obj.Value = rdr.GetDecimal(3);
								obj.CurrencyId = rdr.GetInt32(4);
								obj.ProcessorAccountId = rdr.GetInt32(5);
								obj.CreationDate = rdr.GetDateTime(6);
								obj.CreationDateFormated = obj.CreationDate.ToString("dd/MM/yyyy h:mm:ss tt");
								obj.Abbreviation = rdr.GetString(7);
								obj.Fees = rdr.GetDecimal(8);
								obj.DomainId = rdr.GetInt32(9);
								obj.Approvals = rdr.GetInt32(10);
								obj.Required_approvals = rdr.GetInt32(11);
								obj.Scheduled = rdr.GetBoolean(12);
								obj.Date = rdr.GetDateTime(13);
								obj.DateDateFormated = obj.Date.ToString("dd/MM/yyyy h:mm:ss tt");
								obj.DomainUrl = rdr.GetString(14);
								obj.Status = rdr.GetString(15);
								obj.HasExecutions = rdr.GetBoolean(16);
								obj.PercentageExecuted = rdr.GetInt32(17);
								obj.CustomerId = rdr.GetString(18);
								Currency amount = Currency.Factory(obj.Coin, obj.Value);
								obj.ValueFormatted = amount.ToDisplayFormat();

								result.Add(obj);
							}
						}
					}
				}
			}
			catch (Exception e)
			{
				Loggers.GetIntance().Db.Error($@"sql:{sql}", e);
				ErrorsSender.Send(e, sql);
				throw;
			}
			return result;
		}
		internal override async Task<List<ApproverStored>> ListPendingOperationApproversAsync(int pendingOperationId)
		{
			string sql;
			List<ApproverStored> result = new List<ApproverStored>();

			try
			{
				sql = $@"
				select 
					id,
					name
				from approvers po
				INNER JOIN 
					operationsApprovers poa on poa.approverId = po.id
				WHERE
					poa.transactionId = {pendingOperationId} ";

				using (MySqlConnection conn = new MySqlConnection(connectionString))
				{
					await conn.OpenAsync();
					using (MySqlCommand cmd = new MySqlCommand(sql, conn))
					{
						using (DbDataReader rdr = await cmd.ExecuteReaderAsync())
						{
							while (await rdr.ReadAsync())
							{
								int id = rdr.GetInt32(0);
								string name = rdr.GetString(1);
								ApproverStored approver = new ApproverStored(id, name);
								result.Add(approver);
							}
						}
					}
					await conn.CloseAsync();
				}
			}
			catch (Exception e)
			{
				throw;
			}
			return result;
		}
		internal override void InsertOperation(InternalOperationUpdateMessage operation, long domainId)
		{
			StringBuilder sql = new StringBuilder();
			try
			{
				decimal fees = 0;
				if (operation.Fees.AnyFee())
				{
					fees = operation.Fees.Total().Value;
				}
				var coin = Coinage.Coin(operation.DisbursementAmount.CurrencyCode);
				sql.AppendLine($@"
						INSERT INTO operations
							(transactionId,
							reference,
							amount,
							currencyId,
							accountId,
							creationDate,
							date,
							abbreviation,
							fees, 
							domainId,
							required_approvals,
							approvals,
							scheduled, 
							hasExecutions,
							status,
							executionPercentage, 
							identificationDocumentNumber,
							timestamp)
						VALUES
							({operation.TransactionId},
							'{operation.ReferenceNumber}',
							{operation.DisbursementAmount.Value},
							'{coin.Id}',
							'{operation.AccountId}',
							'{operation.CreationDate.ToString("yyyy-MM-dd H:mm:ss")}',
							'{operation.Date.ToString("yyyy-MM-dd H:mm:ss")}',
							'{operation.Abbreviation.ToString()}',
							{fees},
							{domainId},
							{operation.ApprovalsRequired},
							{operation.Approvals},
							{operation.Scheduled},
							{operation.HasExecutions},
							'{operation.Status.ToString()}',
							{operation.PercentageExecuted}, 
							'{operation.Identifier}',
							0);");

				if (operation.Status == StatusCodes.IN_PROCESS)
				{
					sql.Append("INSERT INTO operationsApprovers(transactionId, approverId) VALUES ");
					foreach (Approver approver in operation.Approvers.List())
					{
						sql.Append($"({operation.TransactionId}, {approver.Id}),");
					}
					sql.Length = sql.Length - 1;
				}

				MySqlConnection conn = new MySqlConnection(connectionString);
				conn.Open();
				MySqlCommand cmd = new MySqlCommand(sql.ToString(), conn);
				cmd.ExecuteNonQuery();

				conn.Close();
			}
			catch (Exception e)
			{
				Loggers.GetIntance().Db.Error($@"sql:{sql}", e);
				ErrorsSender.Send(e, sql.ToString());
				throw;
			}
		}

        internal override void IncreaseTimestamp(int transactionId)
        {
            string sql = string.Empty;
            
            try
            {
                sql = $"UPDATE operations SET TIMESTAMP = TIMESTAMP + 1 WHERE transactionId = {transactionId};";

                MySqlConnection conn = new MySqlConnection(connectionString);
                conn.Open();
                MySqlCommand cmd = new MySqlCommand(sql, conn);
                cmd.ExecuteNonQuery();
                conn.Close();
            }
            catch (Exception e)
            {
                Loggers.GetIntance().Db.Error($@"sql:{sql}", e);
                ErrorsSender.Send(e, sql);
                throw;
            }
			try
			{
				WebHookClientRequest.Instance.SendWebHook(DateTime.Now, sql, "operations", "Guardian");
			}
			catch (Exception webhookEx)
			{
				Loggers.GetIntance().Webhook.Error($@"sql:{sql}", webhookEx);
				throw;
			}
        }

		internal override void Save(List<DisbursementExecutionMessage> disbursementExecutionMessages)
		{
			StringBuilder sql = new StringBuilder();
			try
			{
				try
				{
					foreach (DisbursementExecutionMessage disbursementExecutionMessage in disbursementExecutionMessages)
					{
						sql.AppendLine($"INSERT INTO disbursementExecution(" +
							$"transactionId, " +
							$"disbursementId, " +
							$"amount, " +
							$"currencyId, " +
							$"date, " +
							$"user, " +
							$"status, " +
							$"scheduledDate, " +
							$"accountNumberId " +
							$")" +
							$" values(" +
							$"{disbursementExecutionMessage.TransactionId}, " +
							$"{disbursementExecutionMessage.Id}, " +
							$"{disbursementExecutionMessage.Amount}, " +
							$"'{disbursementExecutionMessage.CurrencyCode.Id}', " +
							$"'{disbursementExecutionMessage.Date.ToString("yyyy-MM-dd H:mm:ss")}', " +
							$"'{disbursementExecutionMessage.User}', " +
							$"'{disbursementExecutionMessage.Status.ToString()}', " +
							$"'{disbursementExecutionMessage.ScheduledDate.ToString("yyyy-MM-dd H:mm:ss")}', " +
							$"{disbursementExecutionMessage.AccountNumberId} " +
							$");");
					}

					MySqlConnection conn = new MySqlConnection(connectionString);
					conn.Open();
					MySqlCommand cmd = new MySqlCommand(sql.ToString(), conn);
					cmd.ExecuteNonQuery();

					conn.Close();
				}
				catch (Exception e)
				{
					Loggers.GetIntance().Db.Error($@"sql:{sql.ToString()}", e);
					ErrorsSender.Send(e, sql.ToString());
					throw;
				}

				WebHookClientRequest.Instance.SendWebHook(DateTime.Now, sql.ToString(), "disbursementExecution", "Guardian");
			}
			catch (Exception webhookEx)
			{
				Loggers.GetIntance().Webhook.Error($"Webhook failed for SQL: {sql.ToString()}", webhookEx);
				throw;
			}
		}
		internal override async Task<DisbursementExecutedResponse> DisbursementExecutedResponseAsync(int transactionId)
		{
			string sql = "";

			DisbursementExecutedResponse result = new DisbursementExecutedResponse();
			try
			{
				sql = $@"
				select 
					de.id,
					de.disbursementId,
					de.amount,
					de.currencyId,
					de.date, 
					de.status,
					de.user,
					de.accountNumberId,
					de.scheduledDate,
					a.currencyId,
					a.number
				from disbursementExecution de
					inner join accountNumbers a on a.Id = de.accountNumberId
					inner join operations op on op.transactionId = de.transactionId
				WHERE
					de.transactionId = {transactionId} AND op.abbreviation = '{TransactionType.Withdrawal}' AND op.status = '{StatusCodes.COMPLETE}'
				ORDER BY de.id ";

				using (MySqlConnection conn = new MySqlConnection(connectionString))
				{
					await conn.OpenAsync();
					using (MySqlCommand cmd = new MySqlCommand(sql, conn))
					{
						using (DbDataReader rdr = await cmd.ExecuteReaderAsync())
						{
							DisbursementExecutionStored LastDisbursementExecutionMessage = null;
							while (await rdr.ReadAsync())
							{
								int id = rdr.GetInt32(0);
								int disbursementId = rdr.GetInt32(1);
								decimal amount = rdr.GetDecimal(2);
								int currencyId = rdr.GetInt32(3);
								DateTime date = rdr.GetDateTime(4);
								string status = rdr.GetString(5);
								string user = rdr.GetString(6);
								int accountNumberId = rdr.GetInt32(7);
								DateTime scheduledDate = rdr.GetDateTime(8);
								string accountCurrency = rdr.GetString(9);
								string accountNumber = rdr.GetString(10);

								AccountNumberStored ac = new AccountNumberStored(accountNumberId, accountNumber, (Currencies.CODES)Enum.Parse(typeof(Currencies.CODES), accountCurrency));

								LastDisbursementExecutionMessage = new DisbursementExecutionStored(
									date,
									(TransactionStatus)Enum.Parse(typeof(TransactionStatus), status),
									transactionId,
									id,
									amount,
									Coinage.GetById(currencyId),
									disbursementId,
									user,
									scheduledDate,
									ac
									);

								result.Add(LastDisbursementExecutionMessage);
							}
						}
					}
					await conn.CloseAsync();
				}

				sql = $@"
				select 
					distinct p.Id,
					p.name
				from profiles p
					inner join operationsProfiles op on op.profileId = p.Id
				WHERE
					op.transactionId = {transactionId} 
				";

				using (MySqlConnection conn = new MySqlConnection(connectionString))
				{
					await conn.OpenAsync();
					using (MySqlCommand cmd = new MySqlCommand(sql, conn))
					{
						using (DbDataReader rdr = await cmd.ExecuteReaderAsync())
						{
							while (await rdr.ReadAsync())
							{
								int profileId = rdr.GetInt32(0);
								string profileName = rdr.GetString(1);
								result.Add(new ProfileStored()
								{
									Id = profileId,
									Name = profileName,
									itAlreadyApproved = true
								});
							}
						}
					}
					await conn.CloseAsync();
				}
			}
			catch (Exception e)
			{
				Loggers.GetIntance().Db.Error($@"sql:{sql}", e);
				ErrorsSender.Send(e, sql);
				throw;
			}
			return result;
		}
		internal override void SaveProfile(ProfileCreationMessage profileCreationMessage)
		{
			if (profileCreationMessage == null) throw new ArgumentNullException(nameof(profileCreationMessage));
			string sql = "";

            try
            {
                try
                {
                    sql = $@"
						INSERT INTO profiles
							(id, name)
						VALUES
							({profileCreationMessage.ProfileId}, '{profileCreationMessage.Name}')
						ON DUPLICATE KEY UPDATE name = '{profileCreationMessage.Name}'; ";

                    MySqlConnection conn = new MySqlConnection(connectionString);
                    conn.Open();
                    MySqlCommand cmd = new MySqlCommand(sql, conn);
                    cmd.ExecuteNonQuery();
                    conn.Close();
                }
                catch (Exception e)
                {
                    Loggers.GetIntance().Db.Error($@"sql:{sql}", e);
                    ErrorsSender.Send(e, sql);
                    throw;
                }

                WebHookClientRequest.Instance.SendWebHook(DateTime.Now, sql, "profiles", "Guardian");
            }
            catch (Exception webhookEx)
            {
                Loggers.GetIntance().Webhook.Error($@"sql:{sql}", webhookEx);
				throw;
            }
        }
		internal override async Task<List<ProfileCreationMessage>> ListProfileAsync()
		{
			string sql = "";
			List<ProfileCreationMessage> result = new List<ProfileCreationMessage>();

			try
			{
				sql = $@"
				select 
					id,
					name
				from profiles ";

				using (MySqlConnection conn = new MySqlConnection(connectionString))
				{
					await conn.OpenAsync();
					using (MySqlCommand cmd = new MySqlCommand(sql, conn))
					{
						using (DbDataReader rdr = await cmd.ExecuteReaderAsync())
						{
							while (await rdr.ReadAsync())
							{
								int id = rdr.GetInt32(0);
								string name = rdr.GetString(1);
								ProfileCreationMessage profileCreationMessage = new ProfileCreationMessage(id, name);
								result.Add(profileCreationMessage);
							}
						}
					}
					await conn.CloseAsync();
				}
			}
			catch (Exception e)
			{
				Loggers.GetIntance().Db.Error($@"sql:{sql}", e);
				ErrorsSender.Send(e, sql);
				throw;
			}
			return result;
		}
		internal override void SaveAccountNumber(AccountNumberCreationMessage operationMessage)
		{
			if (operationMessage == null) throw new ArgumentNullException(nameof(operationMessage));
			string sql = "";
			try
			{
				try
				{
					var coin = Coinage.Coin(operationMessage.CurrencyCode);
					sql = $@"
							INSERT INTO accountNumbers
								(id, number, currencyId, processorKey)
							VALUES
								({operationMessage.Id}, '{operationMessage.Number}', '{coin.Id}', '{operationMessage.ProcessorKey}'); ";

					MySqlConnection conn = new MySqlConnection(connectionString);
					conn.Open();
					MySqlCommand cmd = new MySqlCommand(sql, conn);
					cmd.ExecuteNonQuery();

					conn.Close();
				}
				catch (Exception e)
				{
					Loggers.GetIntance().Db.Error($@"sql:{sql}", e);
					ErrorsSender.Send(e, sql);
					throw;
				}

				WebHookClientRequest.Instance.SendWebHook(DateTime.Now, sql, "accountNumbers", "Guardian");
			}
			catch (Exception webhookEx)
			{
				Loggers.GetIntance().Webhook.Error($"Webhook failed for SQL: {sql}", webhookEx);
				throw;
			}
		}
		internal override void InsertOperationWithProfiles(InternalOperationUpdateMessageWithProfiles operation, long domainId)
		{
			StringBuilder sql = new StringBuilder();
			try
			{
				try
				{
					decimal fees = 0;
					if (operation.Fees.AnyFee())
					{
						fees = operation.Fees.Total().Value;
					}
					var coin = Coinage.Coin(operation.DisbursementAmount.CurrencyCode);
					sql.AppendLine($@"
							INSERT INTO operations
								(transactionId,
								reference,
								amount,
								currencyId,
								accountId,
								creationDate,
								date,
								abbreviation,
								fees,
								domainId,
								required_approvals,
								approvals,
								scheduled,
								hasExecutions,
								status,
								executionPercentage,
								identificationDocumentNumber,
								timestamp)
							VALUES
								({operation.TransactionId},
								'{operation.ReferenceNumber}',
								{operation.DisbursementAmount.Value},
								'{coin.Id}',
								'{operation.AccountId}',
								'{operation.CreationDate.ToString("yyyy-MM-dd H:mm:ss")}',
								'{operation.Date.ToString("yyyy-MM-dd H:mm:ss")}',
								'{operation.Abbreviation.ToString()}',
								{fees},
								{domainId},
								{operation.ApprovalsRequired},
								{operation.Approvals},
								{operation.Scheduled},
								{operation.HasExecutions},
								'{operation.Status.ToString()}',
								{operation.PercentageExecuted},
								'{operation.Identifier}',
								0);");

					if (operation.Status == StatusCodes.IN_PROCESS)
					{
						sql.Append("INSERT INTO operationsApprovers(transactionId, approverId) VALUES ");
						foreach (Approver approver in operation.Approvers.List())
						{
							sql.Append($"({operation.TransactionId}, {approver.Id}),");
						}
						sql.Length = sql.Length - 1;
						sql.Append(";");
					}

					if (operation.RequiredProfilesIds != null && operation.RequiredProfilesIds.Count > 0)
					{
						sql.Append("INSERT INTO operationsProfiles(transactionId, profileId) VALUES ");
						foreach (int profileId in operation.RequiredProfilesIds)
						{
							sql.Append($"({operation.TransactionId}, {profileId}),");
						}
						sql.Length = sql.Length - 1;
						sql.Append(";");
					}

					MySqlConnection conn = new MySqlConnection(connectionString);
					conn.Open();
					MySqlCommand cmd = new MySqlCommand(sql.ToString(), conn);
					cmd.ExecuteNonQuery();

					conn.Close();
				}
				catch (Exception e)
				{
					Loggers.GetIntance().Db.Error($@"sql:{sql}", e);
					ErrorsSender.Send(e, sql.ToString());
					throw;
				}

				WebHookClientRequest.Instance.SendWebHook(DateTime.Now, sql.ToString(), "operations", "Guardian");
			}
			catch (Exception webhookEx)
			{
				Loggers.GetIntance().Webhook.Error($"Webhook failed for SQL: {sql.ToString()}", webhookEx);
				throw;
			}
		}

		internal override void SaveCurrency(CoinCreationMessage msg)
		{
			string sql = string.Empty;
			try
			{
				try
				{
					sql = $@"INSERT INTO currency(id, sign, isoCode, uniCode, decimalPrecision) VALUES (
						{msg.Coin.Id},
						'{msg.Coin.Sign}',
						'{msg.Coin.Iso4217Code}',
						'{msg.Coin.UnicodeAsText}',
						{msg.Coin.DecimalPrecision}
						);";

					MySqlConnection conn = new MySqlConnection(connectionString);
					conn.Open();
					MySqlCommand cmd = new MySqlCommand(sql.ToString(), conn);
					cmd.ExecuteNonQuery();

					conn.Close();
				}
				catch (Exception e)
				{
					Loggers.GetIntance().Db.Error($@"sql:{sql}", e);
					ErrorsSender.Send(e, sql.ToString());
					throw;
				}

				WebHookClientRequest.Instance.SendWebHook(DateTime.Now, sql, "currency", "Guardian");
			}
			catch (Exception webhookEx)
			{
				Loggers.GetIntance().Webhook.Error($"Webhook failed for SQL: {sql}", webhookEx);
				throw;
			}
		}
	}

	internal class StorageInMemory : Storage
	{
		internal override void Create()
		{

		}

		internal override Task<IEnumerable<StoredOperation>> ListCompleteOperationsAsync(int approver, DateTime initDate, DateTime finalDate, int amountOfRows, long inicialIndex, TransactionType[] transactionTypes)
		{
			throw new NotImplementedException();
		}

		internal override Task<DisbursementExecutedResponse> DisbursementExecutedResponseAsync(int transactionId)
		{
			throw new NotImplementedException();
		}

		internal override Task<IEnumerable<StoredOperation>> ListInprocessOperationsAsync(int approver, OperationExecution scheduled, DateTime initDate, DateTime finalDate, int amountOfRows, long inicialIndex)
		{
			throw new NotImplementedException();
		}

		internal override Task<List<ApproverStored>> ListPendingOperationApproversAsync(int pendingOperationId)
		{
			throw new NotImplementedException();
		}

		internal override async Task<IEnumerable<StoredOperation>> ListPendingOperationsAsync(int approver, OperationSchedule scheduled, DateTime initDate, DateTime finalDate, int amountOfRows, long inicialIndex)
		{
			await Task.Yield();
			return internalOperationUpdateMessages;
		}

		private List<StoredOperation> internalOperationUpdateMessages = new List<StoredOperation>();
		internal override void InsertOperation(InternalOperationUpdateMessage operation, long domainId)
		{

			internalOperationUpdateMessages.Add(new StoredOperation()
			{
				Id = internalOperationUpdateMessages.Count + 1
			}); 
		}

		internal override void IncreaseTimestamp(int transactionId)
		{
		}

		internal override void Save(List<DisbursementExecutionMessage> disbursementExecutionMessages)
		{
			throw new NotImplementedException();
		}

		internal override void SaveApprover(ApproverCreationMessage approver)
		{
			throw new NotImplementedException();
		}
		private List<string> domains = new List<string>();
		internal override long SaveDomainIfNotExist(string domain)
		{
			domains.Add(domain);
			return domains.Count;
		}

		internal override Task<bool> TestAsync()
		{
			throw new NotImplementedException();
		}

		internal override void SaveProfile(ProfileCreationMessage operationMessage)
		{
			throw new NotImplementedException();
		}

		internal override Task<List<ProfileCreationMessage>> ListProfileAsync()
		{
			throw new NotImplementedException();
		}

		internal override void SaveAccountNumber(AccountNumberCreationMessage operationMessage)
		{
			throw new NotImplementedException();
		}

		private List<StoredOperation> internalOperationUpdateMessageWithProfiles = new List<StoredOperation>();
		internal override void InsertOperationWithProfiles(InternalOperationUpdateMessageWithProfiles operation, long domainId)
		{
			internalOperationUpdateMessageWithProfiles.Add(new StoredOperation
			{
				DomainUrl = ""+domainId,
				Approvals = operation.Approvals,
				CreationDate = operation.CreationDate,
			}) ;
		}

        internal override void SaveCurrency(CoinCreationMessage msg)
        {
            throw new NotImplementedException();
        }
    }
}
