﻿using GamesEngine;
using GamesEngine.Business;
using GamesEngine.Custodian;
using GamesEngine.Custodian.Operations;
using GamesEngine.Custodian.Persistance;
using GamesEngine.Exchange;
using GamesEngine.Finance;
using GamesEngine.Gameboards.Lotto;
using GamesEngine.Games.Lotto;
using GamesEngine.RealTime;
using GamesEngine.RealTime.Events;
using GamesEngine.Settings;
using GamesEngineTests.Unit_Tests.Games.Lotto;
using Microsoft.AspNetCore.SignalR.Client;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Threading.Tasks;
using town.connectors.drivers;
using Unit.Games.Tools;
using static GamesEngine.Business.WholePaymentProcessor;

namespace GamesEngineTests.Custodian
{

	[TestClass]
	public class GuardianMonitorTest
	{
		public class TestListener : IPlatformListener
		{
			private List<long> ids;
			HubConnectionState state = HubConnectionState.Connected;

			public TestListener(List<long> ids)
			{
				this.ids = ids;
			}

			public string ListenerId => string.Empty;

            public string State => state.ToString();

			public void SendMessage(PlatformEvent monitorEvent)
			{
				ids.Add(monitorEvent.Id);
			}
			public async Task SendMessageAsync(PlatformEvent monitorEvent)
			{
				SendMessage(monitorEvent);
				await Task.Yield();
			}

			public void Disconnect()
            {
				state = HubConnectionState.Disconnected;
		}

			public void Connect()
			{
				state = HubConnectionState.Connected;
			}
		}

		[TestInitialize]
		public void Initialize()
		{
			typeof(WholePaymentProcessor).GetField("_wholePaymentProcessor", System.Reflection.BindingFlags.NonPublic | BindingFlags.Static).SetValue(null, null);
		}

		[TestMethod]
		public void Connect_Disconnect()
		{
            CurrenciesTest.AddCurrencies();
            List<long> monitorIds = new List<long>();
			PlatformMonitor monitor = PlatformMonitor.GetInstance();
			var testListener = new TestListener(monitorIds);
			monitor.Register(testListener);

			Company company = new Company();
			company.Accounting = new MockAccounting();
			DateTime now = new DateTime(2019, 04, 17, 9, 00, 00);
			var player = company.GetOrCreateCustomerById("*********").Player;

			var giftCardEvent = new GiftCardEvent(now, 5m, "FP", "FP", player.Id);
			monitor.WhenNewEvent(giftCardEvent);
			Assert.AreEqual(1, monitor.List(x => true).Count());

			testListener.Disconnect();
			monitor.WhenNewEvent(giftCardEvent);

			testListener.Connect();
			monitor.Register(testListener);
			monitor.WhenNewEvent(giftCardEvent);
			Assert.AreEqual(3, monitor.List(x => true).Count());

			PlatformMonitor.ResetInstance();
		}

		[TestMethod]
		public void Disbursements_MonitorTest()
		{
			Company cia;
			CurrenciesTest.AddCurrencies();
			Guardian guardian;
			QueueInMemory queue;
			DateTime today = DateTime.Now;
			GuardianTest.Init(today, out cia, out guardian, out queue);
			AccountNumbers accounts = guardian.Accounts();
			bool itIsThePresent = true;
			Transactions transactions = Transactions.GetInstance();

			List<long> monitorIds = new List<long>();
			PlatformMonitor monitor = PlatformMonitor.GetInstance();
			monitor.Register(new TestListener(monitorIds));
			monitor.ResetData();

			AccountNumber account = accounts.Search("3770-4512-5467-6352");

			PaymentProcessorsAndActionsByDomains processsors = guardian.PaymentProcessors();

			PaymentProcessor processsor  = processsors.SearchById("TEST-Cash-TestWithdrawalUSD-USD-1");

			string domainUrl = "localhost";
			int domainId = 1;

			Dollar withdrawalAmount = new Dollar(4);

			Operations operations = guardian.Operations();
			int transactionId = operations.NewOperationNumber();
			PendingWithDrawal withdrawal = operations.CreateWithdrawal(itIsThePresent, today, transactionId, 1, "5D2428442", withdrawalAmount, processsor.Group.Id, processsor, domainId, domainUrl, "Bart");

			DateTime scheculeddate = today.AddMilliseconds(1);
			DisbursementsChanges changes = new DisbursementsChanges(today, guardian);
			changes.Add(itIsThePresent, 1, account.Number, scheculeddate, 1);
			changes.Add(itIsThePresent, 2, account.Number, scheculeddate, 3);
			withdrawal.Apply(itIsThePresent, today, changes);

			PaymentsToBeExecuted paymentsToBeExecuted;
			int storeId = 4;
			try
			{
				operations = guardian.Operations();
				PendingWithDrawal withDrawal = (PendingWithDrawal)operations.SearchWithdrawalByTransactionId(transactionId);
				paymentsToBeExecuted = new PaymentsToBeExecuted(guardian);
				paymentsToBeExecuted.Add(withdrawal.TransactionId, new List<int>() { 1 });
				paymentsToBeExecuted.Execute(itIsThePresent, today, "cris", storeId, "CR/Cartago");

				Assert.Fail("Unapproved disbursment can not be payed.");
			}
			catch (GameEngineException e) { }
			Assert.AreEqual(0, monitor.List(x => true).Count());

			Profiles profielsAuthorizedByTheOwner = new Profiles();
			profielsAuthorizedByTheOwner.Add(guardian.Profiles().SearchByName("Financial Manager"));
			operations.Approve(itIsThePresent, today, withdrawal.TransactionId, "<EMAIL>", profielsAuthorizedByTheOwner);
			Assert.AreEqual(0, monitor.List(x => true).Count());

			try
			{
				operations = guardian.Operations();
				withdrawal = (PendingWithDrawal)operations.SearchWithdrawalByTransactionId(4);
				paymentsToBeExecuted = new PaymentsToBeExecuted(guardian);
				paymentsToBeExecuted.Add(withdrawal.TransactionId, new List<int>() { 1 });
				paymentsToBeExecuted.Execute(itIsThePresent, today, "cris", storeId, "CR/Cartago");
				Assert.Fail("Unapproved transaction can not be payed.");
			}
			catch (GameEngineException e) { }
			Assert.AreEqual(0, monitor.List(x => true).Count());

			profielsAuthorizedByTheOwner = new Profiles();
			profielsAuthorizedByTheOwner.Add(guardian.Profiles().SearchByName("Financial Manager"));
			profielsAuthorizedByTheOwner.Add(guardian.Profiles().SearchByName("General Manager"));
			operations.Approve(itIsThePresent, today, withdrawal.TransactionId, "<EMAIL>", profielsAuthorizedByTheOwner);
			paymentsToBeExecuted = new PaymentsToBeExecuted(guardian);
			paymentsToBeExecuted.Add(withdrawal.TransactionId, new List<int>() { 1 });
			paymentsToBeExecuted.Execute(itIsThePresent, today, "cris", storeId, "CR/Cartago");

			Assert.AreEqual(1, monitor.List(x => true).Count());

			try
			{
				profielsAuthorizedByTheOwner = new Profiles();
				profielsAuthorizedByTheOwner.Add(guardian.Profiles().SearchByName("Financial Manager"));
				profielsAuthorizedByTheOwner.Add(guardian.Profiles().SearchByName("General Manager"));
				operations.Approve(itIsThePresent, today, withdrawal.TransactionId, "<EMAIL>", profielsAuthorizedByTheOwner);
				Assert.Fail("Transaction its already approved.");
			}
			catch (GameEngineException e) { }

			paymentsToBeExecuted = new PaymentsToBeExecuted(guardian);
			paymentsToBeExecuted.Add(withdrawal.TransactionId, new List<int>() { 2 });
			paymentsToBeExecuted.Execute(itIsThePresent, today, "cris", storeId, "CR/Cartago");

			Assert.AreEqual(2, monitor.List(x => true).Count());
			Assert.AreEqual(today.Ticks, monitorIds[0]);
			Assert.AreEqual(today.Ticks, monitorIds[0]);

			Integration.RestoreToDefaultSettings();
			PlatformMonitor.ResetInstance();
            //GamesEngine.Custodian.PaymentMethodsActions.ResetInstance();
		}

		[TestMethod]
		public void Disbursements_Monitor_MultipleEventsTest()
		{
			Company cia;
			CurrenciesTest.AddCurrencies();
			Guardian guardian;
			QueueInMemory queue;
			DateTime today = DateTime.Now;
			DateTime tomorrow = today.AddDays(1);
			GuardianTest.Init(today, out cia, out guardian, out queue);
			AccountNumbers accounts = guardian.Accounts();
			bool itIsThePresent = true;
			Transactions transactions = Transactions.GetInstance();

			List<long> monitorIds = new List<long>();
			PlatformMonitor monitor = PlatformMonitor.GetInstance();
			monitor.Register(new TestListener(monitorIds));
			monitor.ResetData();

			AccountNumber account = accounts.Search("3770-4512-5467-6352");

			PaymentProcessorsAndActionsByDomains processsors = guardian.PaymentProcessors();

			PaymentProcessor processsor  = processsors.SearchById("TEST-Cash-TestWithdrawalUSD-USD-1");

			string domainUrl = "localhost";
			int domainId = 1;

			Dollar withdrawalAmount = new Dollar(200);

			Operations operations = guardian.Operations();
			int transactionId = operations.NewOperationNumber();
			PendingWithDrawal withdrawal = operations.CreateWithdrawal(itIsThePresent, today, transactionId, 1, "5D2428442", withdrawalAmount, processsor.Group.Id, processsor, domainId, domainUrl, "Bart");

			DateTime scheculeddate = today.AddMilliseconds(1);
			DisbursementsChanges changes = new DisbursementsChanges(today, guardian);
			changes.Generate(itIsThePresent, account.Number, scheculeddate, 100, 2);
			withdrawal.Apply(itIsThePresent, today, changes);

			PaymentsToBeExecuted paymentsToBeExecuted;
			int storeId = 4;
			try
			{
				operations = guardian.Operations();
				PendingWithDrawal withDrawal = (PendingWithDrawal)operations.SearchWithdrawalByTransactionId(transactionId);
				paymentsToBeExecuted = new PaymentsToBeExecuted(guardian);
				paymentsToBeExecuted.Add(withdrawal.TransactionId, new List<int>() { 1 });
				paymentsToBeExecuted.Execute(itIsThePresent, today, "cris", storeId, "CR/Cartago");
				Assert.Fail("Unapproved disbursment can not be payed.");
			}
			catch (GameEngineException e) { }
			Assert.AreEqual(0, monitor.List(x => true).Count());

			Profiles profielsAuthorizedByTheOwner = new Profiles();
			profielsAuthorizedByTheOwner.Add(guardian.Profiles().SearchByName("Financial Manager"));
			operations.Approve(itIsThePresent, today, withdrawal.TransactionId, "<EMAIL>", profielsAuthorizedByTheOwner);
			Assert.AreEqual(0, monitor.List(x => true).Count());

			try
			{
				operations = guardian.Operations();
				withdrawal = (PendingWithDrawal)operations.SearchWithdrawalByTransactionId(4);
				paymentsToBeExecuted = new PaymentsToBeExecuted(guardian);
				paymentsToBeExecuted.Add(withdrawal.TransactionId, new List<int>() { 1 });
				paymentsToBeExecuted.Execute(itIsThePresent, today, "cris", storeId, "CR/Cartago");
				Assert.Fail("Unapproved transaction can not be payed.");
			}
			catch (GameEngineException e) { }
			Assert.AreEqual(0, monitor.List(x => true).Count());

			profielsAuthorizedByTheOwner = new Profiles();
			profielsAuthorizedByTheOwner.Add(guardian.Profiles().SearchByName("Financial Manager"));
			profielsAuthorizedByTheOwner.Add(guardian.Profiles().SearchByName("General Manager"));
			operations.Approve(itIsThePresent, today, withdrawal.TransactionId, "<EMAIL>", profielsAuthorizedByTheOwner);
			paymentsToBeExecuted = new PaymentsToBeExecuted(guardian);
			paymentsToBeExecuted.Add(withdrawal.TransactionId, new List<int>() { 1 });
			paymentsToBeExecuted.Execute(itIsThePresent, today, "cris", storeId, "CR/Cartago");

			Assert.AreEqual(1, monitor.List(x => true).Count());

			try
			{
				profielsAuthorizedByTheOwner = new Profiles();
				profielsAuthorizedByTheOwner.Add(guardian.Profiles().SearchByName("Financial Manager"));
				profielsAuthorizedByTheOwner.Add(guardian.Profiles().SearchByName("General Manager"));
				operations.Approve(itIsThePresent, today, withdrawal.TransactionId, "<EMAIL>", profielsAuthorizedByTheOwner);
				Assert.Fail("Transaction its already approved.");
			}
			catch (GameEngineException e) { }

			paymentsToBeExecuted = new PaymentsToBeExecuted(guardian);
			paymentsToBeExecuted.Add(withdrawal.TransactionId, new List<int>() { 2, 3, 4, 5, 6, 7, 8, 9, 11, 33, 44, 55, 66, 77, 87, 88, 90 });
			paymentsToBeExecuted.Execute(itIsThePresent, tomorrow, "cris", storeId, "CR/Cartago");

			Assert.AreEqual(18, monitor.List(x => true).Count());
			Assert.AreEqual(today.Ticks, monitorIds[0]);
			//Assert.AreEqual(tomorrow.Ticks, monitorIds[1]);
			Assert.AreEqual(2, monitorIds.Count());

			Integration.RestoreToDefaultSettings();
			PlatformMonitor.ResetInstance();
            //GamesEngine.Custodian.PaymentMethodsActions.ResetInstance();
		}
	}
}
