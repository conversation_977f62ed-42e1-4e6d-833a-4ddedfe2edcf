﻿using GamesEngine;
using GamesEngine.Business;
using GamesEngine.Domains;
using GamesEngine.Finance;
using GamesEngine.Exchange;
using GamesEngine.Games.Lotto;
using GamesEngine.Location;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Utilities;
using static GamesEngine.Finance.PaymentChannels;

namespace GamesEngineTests.Unit_Tests.Games.Lotto
{
    [TestClass]
    public class NextDateAccumulator
    {
        [TestMethod]
        public void AnyTicketIsExpired()
        {
            Company c = new Company();
            c.Sales.CreateStore(1, "Test Store").MakeCurrent();
            c.Accounting = new MockAccounting();
            var lotteries = c.Lotto900();
            DateTime now = new DateTime(2019, 04, 17, 9, 00, 00);
            State state = lotteries.GetOrCreateState("VA", "Virginia", now, "Bart");
            LotteryPick<Pick3> lottery = (LotteryPick<Pick3>)lotteries.GetOrCreateLottery(3, state);
            for (int i = 0; i <= 6; i++) lottery.Every(10, 20, i, now, "Bart");

            string states = "VA";
            string hours = "10:20 AM";
            string strIsPresentPlayerBeforeToCloseStore = "true";
            string strUseNextDate = "true";
            string dates = "04/17/2019";
            string pickNumber = "3";

            Domain domain = c.Sales.CreateDomain(false, 1, "localhost", PaymentChannels.Agents.INSIDER);
            var nextDatesAccumulator = new NextDatesAccumulator(lotteries, domain, now, pickNumber, dates, states, hours, strIsPresentPlayerBeforeToCloseStore, strUseNextDate);
            Assert.IsFalse(nextDatesAccumulator.AnyTicketIsExpired);
            foreach (var nextDate in nextDatesAccumulator.GetAll)
            {
                Assert.AreEqual(nextDate.Date, nextDate.DesiredDate);
            }

            now = new DateTime(2019, 04, 17, 11, 00, 00);
            nextDatesAccumulator = new NextDatesAccumulator(lotteries, domain, now, pickNumber, dates, states, hours, strIsPresentPlayerBeforeToCloseStore, strUseNextDate);
            Assert.IsFalse(nextDatesAccumulator.AnyTicketIsExpired);
            foreach (var nextDate in nextDatesAccumulator.GetAll)
            {
                Assert.AreNotEqual(nextDate.Date, nextDate.DesiredDate);
            }

            strUseNextDate = "false";
            now = new DateTime(2019, 04, 17, 9, 00, 00);
            nextDatesAccumulator = new NextDatesAccumulator(lotteries, domain, now, pickNumber, dates, states, hours, strIsPresentPlayerBeforeToCloseStore, strUseNextDate);
            Assert.IsFalse(nextDatesAccumulator.AnyTicketIsExpired);
            foreach (var nextDate in nextDatesAccumulator.GetAll)
            {
                Assert.AreEqual(nextDate.Date, nextDate.DesiredDate);
            }

            now = new DateTime(2019, 04, 17, 11, 00, 00);
            nextDatesAccumulator = new NextDatesAccumulator(lotteries, domain, now, pickNumber, dates, states, hours, strIsPresentPlayerBeforeToCloseStore, strUseNextDate);
            Assert.IsTrue(nextDatesAccumulator.AnyTicketIsExpired);
            foreach (var nextDate in nextDatesAccumulator.GetAll)
            {
                Assert.AreNotEqual(nextDate.Date, nextDate.DesiredDate);
            }
        }

        [TestMethod]
        public void Calculate1()
        {
            Company c = new Company();
            c.Sales.CreateStore(1, "Test Store").MakeCurrent();
            c.Accounting = new MockAccounting();
            var lotteries = c.Lotto900();
            DateTime now = new DateTime(2024, 04, 17, 9, 00, 00);
            State state = lotteries.GetOrCreateState("VA", "Virginia", now, "Bart");
            LotteryPick<Pick3> lottery = (LotteryPick<Pick3>)lotteries.GetOrCreateLottery(3, state);
            for (int i = 0; i <= 6; i++) lottery.Every(10, 20, i, now, "Bart");
            for (int i = 0; i <= 6; i++) lottery.Every(14, 20, i, now, "Bart");

            string states = "VA,VA";
            string hours = "10:20 AM,2:20 PM";
            string withFireBalls = "false,false";
            string strIsPresentPlayerBeforeToCloseStore = "true,true";
            string strUseNextDate = "true,true";
            string dates = "04/17/2024,04/18/2024";
            string pickNumber = "3";
            IdOfLottery idOfLottery = IdOfLottery.P3;

            Domain domain = c.Sales.CreateDomain(false, 1, "localhost", Agents.INSIDER);
            lotteries.NextDatesAccumulator.Calculate(domain, now, pickNumber, dates, states, hours, withFireBalls, strIsPresentPlayerBeforeToCloseStore, strUseNextDate);
            var nextDates = lotteries.NextDatesAccumulator.TakeDates(state, withFireBall: false, 4, idOfLottery: idOfLottery);
            Assert.AreEqual(4, nextDates.Count());
        }


        [TestMethod]
        public void TakeDates()
        {
            Company c = new Company();
            c.Sales.CreateStore(1, "Test Store").MakeCurrent();
            c.Accounting = new MockAccounting();
            var lotteries = c.Lotto900();
            DateTime now = new DateTime(2024, 04, 17, 9, 00, 00);
            State state = lotteries.GetOrCreateState("VA", "Virginia", now, "Bart");
            LotteryPick<Pick3> lottery = (LotteryPick<Pick3>)lotteries.GetOrCreateLottery(3, state);
            for (int i = 0; i <= 6; i++) lottery.Every(10, 20, i, now, "Bart");
            var lottery4 = (LotteryPick<Pick4>)lotteries.GetOrCreateLottery(4, state);
            for (int i = 0; i <= 6; i++) lottery4.Every(10, 20, i, now, "Bart");

            var states = "VA,VA" ;
            var hours = "10:20 AM,10:20 AM";
            var withFireBalls = "false,false";
            var dates = "04/17/2024";
            var pickNumber = "3";
            IdOfLottery idOfLottery = IdOfLottery.P3;

            Domain domain = c.Sales.CreateDomain(false, 1, "localhost", Agents.INSIDER);
            lotteries.NextDatesAccumulator.Calculate(domain, now, pickNumber, dates, states, hours, withFireBalls);
            
            var nextDates = lotteries.NextDatesAccumulator.TakeDates(state, withFireBall: false, 1, idOfLottery: idOfLottery);
            Assert.AreEqual(1, nextDates.Count());
            Assert.AreEqual("VA", nextDates.First().State.Abbreviation);
            var date = new DateTime(2024, 04, 17, 10, 20, 00);
            Assert.AreEqual(date, nextDates.First().Date);

            nextDates = lotteries.NextDatesAccumulator.TakeDates(state, withFireBall: false, 1, idOfLottery: idOfLottery);
            Assert.AreEqual(1, nextDates.Count());
            Assert.AreEqual("VA", nextDates.First().State.Abbreviation);
            date = new DateTime(2024, 04, 18, 10, 20, 00);
            Assert.AreEqual(date, nextDates.First().Date);
        }

        [TestMethod]
        public void GetAll()
        {
            Company c = new Company();
            c.Sales.CreateStore(1, "Test Store").MakeCurrent();
            c.Accounting = new MockAccounting();
            var lotteries = c.Lotto900();
            DateTime now = new DateTime(2019, 04, 17, 9, 00, 00);
            State state = lotteries.GetOrCreateState("VA", "Virginia", now, "Bart");
            LotteryPick<Pick3> lottery = (LotteryPick<Pick3>)lotteries.GetOrCreateLottery(3, state);
            for (int i = 0; i <= 6; i++) lottery.Every(10, 20, i, now, "Bart");
            var lottery4 = (LotteryPick<Pick4>)lotteries.GetOrCreateLottery(4, state);
            for (int i = 0; i <= 6; i++) lottery4.Every(10, 20, i, now, "Bart");

            var states = new string[] { "VA", "VA" };
            var hours = new string[] { "10:20 AM", "10:20 AM" };
            var strIsPresentPlayerBeforeToCloseStore = new string[] { "true", "true" };
            var strUseNextDate = new string[] { "true", "true" };
            var dates = "04/17/2019";
            var pickNumber = new int[] { 3, 4 };

            Domain domain = c.Sales.CreateDomain(false, 1, "localhost", PaymentChannels.Agents.INSIDER);
            var nextDatesAccumulator = new NextDatesAccumulator(lotteries, domain, now, pickNumber, dates, states, hours, strIsPresentPlayerBeforeToCloseStore, strUseNextDate);
            Assert.IsFalse(nextDatesAccumulator.AnyTicketIsExpired);
            foreach (var nextDate in nextDatesAccumulator.GetAll)
            {
                Assert.AreEqual(nextDate.Date, nextDate.DesiredDate);
            }
        }

        [TestMethod]
        public void Calculate()
        {
            Company c = new Company();
            c.Sales.CreateStore(1, "Test Store").MakeCurrent();
            c.Accounting = new MockAccounting();
            var lotteries = c.Lotto900();
            DateTime now = new DateTime(2019, 04, 17, 9, 00, 00);
            State state = lotteries.GetOrCreateState("VA", "Virginia", now, "Bart");
            LotteryPick<Pick3> lottery = (LotteryPick<Pick3>)lotteries.GetOrCreateLottery(3, state);
            for (int i = 0; i <= 6; i++) lottery.Every(10, 20, i, now, "Bart");
            var lottery4 = (LotteryPick<Pick4>)lotteries.GetOrCreateLottery(4, state);
            for (int i = 0; i <= 6; i++) lottery4.Every(10, 20, i, now, "Bart");

            var states = new string[] { "VA", "VA" };
            var hours = new string[] { "10:20 AM", "10:20 AM" };
            var withFireBalls = new string[] { "false", "false" };
            var strIsPresentPlayerBeforeToCloseStore = new string[] { "true", "true" };
            var strUseNextDate = new string[] { "true", "true" };
            var dates = "04/17/2019";
            var pickNumber = new int[] { 3, 4 };

            Domain domain = c.Sales.CreateDomain(false, 1, "localhost", PaymentChannels.Agents.INSIDER);
            lotteries.NextDatesAccumulator.Calculate(domain, now, pickNumber, dates, states, hours, withFireBalls, strIsPresentPlayerBeforeToCloseStore, strUseNextDate);
            Assert.IsFalse(lotteries.NextDatesAccumulator.AnyTicketIsExpired);
            foreach (var nextDate in lotteries.NextDatesAccumulator.GetAll)
            {
                Assert.AreEqual(nextDate.Date, nextDate.DesiredDate);
            }
        }

        [TestMethod]
        public void TakeDatesByPick()
        {
            Company c = new Company();
            c.Sales.CreateStore(1, "Test Store").MakeCurrent();
            c.Accounting = new MockAccounting();
            var lotteries = c.Lotto900();
            DateTime now = new DateTime(2019, 04, 17, 9, 00, 00);
            State state = lotteries.GetOrCreateState("VA", "Virginia", now, "Bart");
            LotteryPick<Pick3> lottery = (LotteryPick<Pick3>)lotteries.GetOrCreateLottery(3, state);
            for (int i = 0; i <= 6; i++) lottery.Every(10, 20, i, now, "Bart");
            var lottery4 = (LotteryPick<Pick4>)lotteries.GetOrCreateLottery(4, state);
            for (int i = 0; i <= 6; i++) lottery4.Every(10, 20, i, now, "Bart");

            var states = new string[] { "VA", "VA" };
            var hours = new string[] { "10:20 AM", "10:20 AM" };
            var withFireBalls = new string[] { "false", "false" };
            var strIsPresentPlayerBeforeToCloseStore = new string[] { "true", "true" };
            var strUseNextDate = new string[] { "true", "true" };
            var dates = "04/17/2019";
            var pickNumber = new int[] { 3, 4 };

            Domain domain = c.Sales.CreateDomain(false, 1, "localhost", PaymentChannels.Agents.INSIDER);
            lotteries.NextDatesAccumulator.Calculate(domain, now, pickNumber, dates, states, hours, withFireBalls, strIsPresentPlayerBeforeToCloseStore, strUseNextDate);
            var nextDates = lotteries.NextDatesAccumulator.TakeDatesByPick(IdOfLottery.P2);
            Assert.AreEqual(0, nextDates.Count());

            nextDates = lotteries.NextDatesAccumulator.TakeDatesByPick(IdOfLottery.P3);
            Assert.AreEqual(1, nextDates.Count());
            Assert.AreEqual("VA", nextDates.First().State.Abbreviation);
            var date = new DateTime(2019, 04, 17, 10, 20, 00);
            Assert.AreEqual(date, nextDates.First().Date);

            nextDates = lotteries.NextDatesAccumulator.TakeDatesByPick(IdOfLottery.P4);
            Assert.AreEqual(1, nextDates.Count());
            Assert.AreEqual("VA", nextDates.First().State.Abbreviation);
            Assert.AreEqual(date, nextDates.First().Date);
        }
    }
}