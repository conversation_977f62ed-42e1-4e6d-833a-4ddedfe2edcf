﻿using Nest;
using Puppeteer.EventSourcing;
using Puppeteer.EventSourcing.Libraries;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace GamesEngine.PurchaseOrders
{
    internal class PrizesIndexer
    {
        private List<decimal> prizes = new List<decimal>();
        internal IEnumerable<decimal> Prizes
        {
            get
            {
                return prizes;
            }
        }

        private List<int> indexes = new List<int>();
        internal IEnumerable<int> Indexes
        {
            get
            {
                return indexes;
            }
        }

        public PrizesIndexer()
        {
        }

        internal int Index(decimal prize)
        {
            if (prize <= 0) throw new ArgumentException("Prize must be greater than 0");

            var index = prizes.IndexOf(prize);
            if (index == -1)
            {
                prizes.Add(prize);
                index = prizes.Count - 1;
            }
            indexes.Add(index);
            return index;
        }
    }

    [Puppet]
    internal class PrizesSet : Objeto
    {
        private IEnumerable<decimal> prizes;
        internal IEnumerable<decimal> GetAll
        {
            get
            {
                return prizes;
            }
        }
        [Obsolete]
        public PrizesSet(IEnumerable<string> prizes)
        {
            if (prizes == null) throw new ArgumentNullException($"{nameof(prizes)} cannot be null");
            if (!prizes.Any()) throw new ArgumentException($"{nameof(prizes)} cannot be empty");

            this.prizes = prizes.Select(x=>decimal.Parse(x));
        }
        [Obsolete]
        internal decimal Prize(int index)
        {
            if (index < 0 || index >= prizes.Count()) throw new ArgumentException("Index out of range");

            return prizes.ElementAt(index);
        }
        [Obsolete]
        internal List<string> Prizes(IEnumerable<int> indexes)
        {
            var prizesAsText = new List<string>();
            foreach (var index in indexes)
            {
                if (index < 0 || index >= prizes.Count()) throw new ArgumentException("Index out of range");

                prizesAsText.Add(prizes.ElementAt(index).ToString());
            }
            return prizesAsText;
        }
    }
}
