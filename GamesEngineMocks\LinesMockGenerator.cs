﻿using Elasticsearch.Net;
using GamesEngine;
using GamesEngine.Domains;
using GamesEngine.Exchange;
using GamesEngine.Finance;
using GamesEngine.Games;
using GamesEngine.Middleware;
using GamesEngine.Middleware.Providers;
using GamesEngine.Settings;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Puppeteer.EventSourcing;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Runtime.Serialization;
using System.Text;
using town.connectors.drivers;
using static GamesEngine.Exchange.PaymentProcessorsAndActionsByDomains;
using static GamesEngine.Finance.PaymentChannels;
using static GamesEngine.Middleware.Synchronizator;
using static GamesEngineMocks.LinesMocks;

namespace GamesEngineMocks
{
	public class LinesMockGenerator : Mock
	{
		Actor actor;
		int id = 1;
		public String Perform(String script)
		{
			string result;
			try
			{
#pragma warning disable VSTHRD002 // Avoid problematic synchronous waits
				result = actor.PerformCmdAsync(script, IpAddress.DEFAULT, UserInLog.ANONYMOUS).Result;
#pragma warning restore VSTHRD002 // Avoid problematic synchronous waits
				result = CheckForAssert(result);
			}
			catch (Exception e)
			{
				throw e.InnerException;
			}
			return result;
		}

		public LinesMockGenerator(Actor actor)
		{
			this.actor = actor;
			Perform($@"
				company = Company();
				if (!company.System.Coins.ExistsIsoCode('FP'))
				{{
					coin = company.System.Coins.Add(0, 'FP', 'FP', 2, 'F', 'free play', {CoinType.Digital});
					coin.Visible = true;
					coin.Enabled = true;
				}}
				if (!company.System.Coins.ExistsIsoCode('USD'))
				{{
					coin = company.System.Coins.Add(2, 'USD', '$', 2, '$', 'dollar', {CoinType.Fiat});
					coin.Visible = true;
					coin.Enabled = true;
				}}
				if (!company.System.Coins.ExistsIsoCode('BTC'))
				{{
					company.System.Coins.Add(3, 'BTC', 'BTC', 8, '₿', 'bitcoin', {CoinType.Crypt});
				}}
				if (!company.System.Coins.ExistsIsoCode('ETH'))
				{{
					company.System.Coins.Add(7, 'ETH', 'ETH', 8, 'Ξ', 'ethereum', {CoinType.Crypt});
				}}
				companyIDf2401f84xa94ax4db3xaec4x83f5361730ae = company;
				company.Accounting = MockAccounting();
				store = company.Sales.CreateStore(5,'Ladybet Store');
				store.Alias = 'ladybet';
				store.MakeCurrent();
				
				book = company.Book;
				tournaments = company.Tournaments;
				domain = company.Sales.CreateDomain(false, 1, 'localhost', {Agents.INSIDER});
				company.Sales.CurrentStore.Add(domain);
				product = company.GetOrCreateProductById(1);
				product.Description = 'Wager for lines';
				product.Price = 1;

				preference = StorePreference(1, 'Show pregame lines');
				store.AddPreference(preference);
				preference = StorePreference(2, 'Ask password in betslip');
				store.AddPreference(preference);

				sport = tournaments.Sports.FindById(1);
				sport.Aliases.Add(ForeingAlias(1, '1234', 'Futbol'));
				sport.Aliases.Add(ForeingAlias({ProvidersCollection.BetFair.Id}, '1', 'Soccer'));
				company.Sales.CurrentStore.AddDefaultSport(sport);
				sport.CreateAndReplaceDefaultBackground('soccer.png', 'images/bg_sbs/soccer.png');
				sport.CreateAndReplaceDefaultSideBySide('coming_soon_picture', 'images/side-by-side.png');

				sport = tournaments.Sports.FindById(18);
				sport.Aliases.Add(ForeingAlias({ProvidersCollection.BetFair.Id}, '7522', 'Basketball'));
				company.Sales.CurrentStore.AddDefaultSport(sport);
				sport.CreateAndReplaceDefaultBackground('basketball.png', 'images/bg_sbs/basketball.png');
				sport.CreateAndReplaceDefaultSideBySide('coming_soon_picture', 'images/side-by-side.png');

				sport = tournaments.Sports.FindById(16);
				sport.Aliases.Add(ForeingAlias({ProvidersCollection.BetFair.Id}, '7511', 'Baseball'));
				company.Sales.CurrentStore.AddDefaultSport(sport);
				sport.CreateAndReplaceDefaultBackground('baseball.png', 'images/bg_sbs/baseball.png');
				sport.CreateAndReplaceDefaultSideBySide('coming_soon_picture', 'images/side-by-side.png');

				sport = tournaments.Sports.FindById(12);
				sport.Aliases.Add(ForeingAlias({ProvidersCollection.BetFair.Id}, '6423', 'American Football'));
				company.Sales.CurrentStore.AddDefaultSport(sport);
				sport.CreateAndReplaceDefaultBackground('football.png', 'images/bg_sbs/football.png');
				sport.CreateAndReplaceDefaultSideBySide('coming_soon_picture', 'images/side-by-side.png');

				sport = tournaments.Sports.FindById(17);
				sport.Aliases.Add(ForeingAlias({ProvidersCollection.BetFair.Id}, '7524', 'Ice Hockey'));
				company.Sales.CurrentStore.AddDefaultSport(sport);
				sport.CreateAndReplaceDefaultBackground('hockey.png', 'images/bg_sbs/hockey.png');
				sport.CreateAndReplaceDefaultSideBySide('coming_soon_picture', 'images/side-by-side.png');

				marketplace = Marketplace(company, 'CR');
				cartagoAgent = marketplace.AddAgent('Cartago');
				agent1 = marketplace.AddAgent('1');
				marketplace.AddAgent('San José');
				marketplace.AddAgent('Heredia');

				customSettings = CustomSettingsCollection(company);
				{{
					company.System.Entities.Add(1, 'Apolo');
					artemisEntity = company.System.Entities.Add(2, 'Artemis');
					zeusEntity = company.System.Entities.Add(3, 'Zeus');
					fieroEntity = company.System.Entities.Add(4, 'Fiero');
					fieroEntity.Visible = true;
					fieroEntity.Enabled = true;
					hadesEntity = company.System.Entities.Add(5, 'Hades');
					hadesEntity.Visible = true;
					hadesEntity.Enabled = true;
					consignmentEntity = company.System.Entities.Add(6, 'Consignment');
					consignmentEntity.Visible = true;
					consignmentEntity.Enabled = true;

					company.System.Tenants.Add(1, 'Apolo');
					artemisTenant = company.System.Tenants.Add(2, 'Artemis');
					zeusTenant = company.System.Tenants.Add(3, 'Zeus');
					insiderTenant = company.System.Tenants.Add(4, 'Insider');
					hadesTenant = company.System.Tenants.Add(5, 'Hades');
					hadesTenant.MakeCurrent();

					asiPM = company.System.PaymentMethods.Add(1, 'A.S.I.');
					dgsPM = company.System.PaymentMethods.Add(2, 'D.G.S.');
					dgsV2PM = company.System.PaymentMethods.Add(3, 'D.G.S. v2');
					credirCardPM = company.System.PaymentMethods.Add(4, 'Credit card');
					blockChainPM = company.System.PaymentMethods.Add(5, 'Blockchain');
					moneyTransferPM = company.System.PaymentMethods.Add(6, 'Money transfer');
					asiSimulatorPM = company.System.PaymentMethods.Add(7, 'A.S.I. Simulator');
					dsgSimulatorPM = company.System.PaymentMethods.Add(8, 'D.G.S. Simulator');
					pm = company.System.PaymentMethods.Add(9, '{PaymentMethod.Cash.ToString()}');
					pm.Visible = true;
					pm.Enabled = true;
					pm = company.System.PaymentMethods.Add(10, '{PaymentMethod.Bank.ToString()}');
					pm.Visible = true;
					pm.Enabled = true;
					pm = company.System.PaymentMethods.Add(11, '{PaymentMethod.Creditcard.ToString()}');
					pm.Visible = true;
					pm.Enabled = true;
					pm = company.System.PaymentMethods.Add(12, '{PaymentMethod.FinancialServices.ToString()}');
					pm.Visible = true;
					pm.Enabled = true;
					pm = company.System.PaymentMethods.Add(13, '{PaymentMethod.ThirdParty.ToString()}');
					pm.Visible = true;
					pm.Enabled = true;
					pm = company.System.PaymentMethods.Add(14, '{PaymentMethod.Secrets.ToString()}');
					pm.Visible = true;
					pm.Enabled = true;
					pm = company.System.PaymentMethods.Add(15, '{PaymentMethod.P2PTransfer.ToString()}');
					pm.Visible = true;
					pm.Enabled = true;
					pm.AddProvider(1,'MoneyGram');
					pm.AddProvider(2,'Rio Money Transfer');
					pm.AddProvider(3,'Sigue');
					pm.AddProvider(4,'Boss Revolution Money Transfer');
					pm.AddProvider(5,'Delgado Travel');
					pm.AddProvider(6,'Intermex International Money Transfer');
					pm.AddProvider(7,'Maxi Money Services');
					pm.AddProvider(8,'Barri Financial Group');
					pm.AddProvider(9,'La Nacional');
					pm.AddProvider(10,'Dinex');
					pm.AddProvider(11,'Remitly');

					depositTransactionType = company.System.TransactionTypes.Add(1, '{TransactionType.Deposit.ToString()}');
					depositTransactionType.Description = 'A sum of money placed in an account';
					withDrawalTransactionType = company.System.TransactionTypes.Add(2, '{TransactionType.Withdrawal.ToString()}');
					withDrawalTransactionType.Description = 'A sum of money take out of an account';
					transferTransactionType = company.System.TransactionTypes.Add(3, '{TransactionType.Transfer.ToString()}');
					transferTransactionType.Description = 'A sum of money is sent from one account to another';
					creditNoteTransactionType = company.System.TransactionTypes.Add(4, '{TransactionType.CreditNote.ToString()}');
					creditNoteTransactionType.Description = 'It is a document used by a vendor to inform the buyer of mistake on an order';
					debitNoteTransactionType = company.System.TransactionTypes.Add(5, '{TransactionType.DebitNote.ToString()}');
					debitNoteTransactionType.Description = 'It is a document used by a vendor to inform the buyer of current debt obligations';
					saleTransactionType = company.System.TransactionTypes.Add(6, '{TransactionType.Sale.ToString()}');
					saleTransactionType.Description = 'The exchange of a commodity for money';
					depositAndLockTransactionType = company.System.TransactionTypes.Add(7, '{TransactionType.Deposit.ToString()} then Lock');
					depositAndLockTransactionType.Description = 'Add and Lock money placed in an account';

					depositTransactionType.Visible = true;
					depositTransactionType.Enabled = true;
					withDrawalTransactionType.Visible = true;
					withDrawalTransactionType.Enabled = true;
					transferTransactionType.Visible = true;
					transferTransactionType.Enabled = true;
					debitNoteTransactionType.Visible = true;
					debitNoteTransactionType.Enabled = true;
					creditNoteTransactionType.Visible = true;
					creditNoteTransactionType.Enabled = true;
					saleTransactionType.Visible = true;
					saleTransactionType.Enabled = true;
					depositAndLockTransactionType.Visible = true;
					depositAndLockTransactionType.Enabled = true;

					cs = customSettings.AddFixedParameter(Now, 'CashierDriver.url', 'http://cashierapi:5000/'); 
					cs.Description = 'Url Cashier';
					cs = customSettings.AddFixedParameter(Now, 'CompanyBaseUrlServices', 'http://cashierapi:5000/'); 
					cs.Description = 'It is the base production url';
					cs = customSettings.AddFixedParameter(Now, 'TokenSystemId', '3aab83fb'); 
					cs.Description = 'App\'s token id';
					cs = customSettings.AddSecretParameter(Now, 'TokenSystemPassword', '38f845e977af7f2cd6313c5a9d35e4de7f353405100e7748ab15b410eb53d886');
					cs.Description = 'Password for token';
					cs = customSettings.AddFixedParameter(Now, 'SendWagers', true); 
					cs.Description = 'To send wagers to third party';
					cs = customSettings.AddFixedParameter(Now, 'CompanySystemId', 'N/A'); 
					cs.Description = 'company id';
					cs = customSettings.AddSecretParameter(Now, 'CompanySystemPassword', 'N/A'); 
					cs.Description = 'password';
					cs = customSettings.AddFixedParameter(Now, 'NeedsUniqueIdentifierForPaymentHub', 'PRODUCTION_DOES_NOT_NEED_IT'); 
					cs.Description = 'Setting';
					cs = customSettings.AddFixedParameter(Now, 'CompanyClerkId', 'Administrator'); 
					cs.Description = 'Clerk id';

					cs = customSettings.AddVariableParameter('Input$Provider'); 
					cs.Description = 'Consignment provider';
					cs = customSettings.AddVariableParameter('KYC$DOB'); 
					cs.Description = 'Birthdate from KYC';
					cs = customSettings.AddSecretParameter(Now, 'KYCUsername', 'testUser1');
					cs.Description = 'KYC Username for fields';
					cs = customSettings.AddSecretParameter(Now, 'KYCPassword', '12345');
					cs.Description = 'KYC Password for fields';
				}}

				company.IsAlreadyRegisteredTenantAndStore = true;
			");

			CreateQuestionForEveySport();

		}
		public void CreateQuestionForEveySport()
		{
			CreateQuestions(1);//Soccer
							   //CreateQuestions(13);//Tennis
							   //CreateQuestions(78);//Handball
							   //CreateQuestions(17);//Hockey
							   //CreateQuestions(12);//Football
							   //CreateQuestions(83);//Futsal
							   //CreateQuestions(92);//TableTennis
							   //CreateQuestions(8);//RugbyUnion
							   //CreateQuestions(9);//BoxingUFC
							   //CreateQuestions(90);//Floorball
							   //CreateQuestions(110);//WaterPolo
							   //CreateQuestions(151);//ESports
							   //CreateQuestions(91);//Volleyball
			CreateQuestions(16);//Baseball
								//CreateQuestions(14);//Snooker
								//CreateQuestions(3);//Cricket
								//CreateQuestions(15);//Darts
								//CreateQuestions(94);//Badminton
								//CreateQuestions(19);//RugbyLeague
								//CreateQuestions(66);//Bowls
								//CreateQuestions(75);//GaelicSports
								//CreateQuestions(95);//BeachVolleyball
								//CreateQuestions(107);//Squash
			CreateQuestions(18);//BasketBall
		}

		public LinesMockGenerator GetLastLineId(out int ID)
		{
			string result = Perform($"print newLine.LineId lineId;");
			result = result.Substring(result.IndexOf(':') + 1);
			result = result.Substring(0, result.IndexOf('}'));
			ID = int.Parse(result);
			return this;
		}

		public LinesMockGenerator GetLastLeagueId(out int ID)
		{
			string result = Perform($"print league.Id 'id';");
			result = result.Substring(result.IndexOf(':') + 1);
			result = result.Substring(0, result.IndexOf('}'));
			ID = int.Parse(result);
			return this;
		}
		public LinesMockGenerator GetLastTournamentId(out int ID)
		{
			string result = Perform($"print tournament.Id 'id';");
			result = result.Substring(result.IndexOf(':') + 1);
			result = result.Substring(0, result.IndexOf('}'));
			ID = int.Parse(result);
			return this;
		}

		internal LinesMockGenerator Mock0()
		{
			int gameNumber = 1;
			int gameNumber2 = 2;
			int fila1 = 0;
			var options = "'1-2','3-6','7-9','10-13','14-16','17-20','21+'";
			var rewards = "881,489,881,1078,2157,2451,2451";
			var rewards2 = "832,440,685,685,1372,1568,1372";
			var today = DateTime.Now;
			var tomorrow = DateTime.Now.AddDays(1);

			Perform($@"
				
				nba = tournaments.GetNBATournamentOf(2020);
				betBoard = company.Betboard(nba);
				betBoard.AcceptFrom(nba);
				catalog = betBoard.Catalog;
				domain = company.Sales.DomainFrom('localhost');
				catalog.IncludeDomainForActivations(domain);
				betBoard.PresetBetAmounts.Add(0.25, 'N/A', Now);
				betBoard.PresetBetAmounts.Add(0.5, 'N/A', Now);
				betBoard.PresetBetAmounts.Add(1, 'N/A', Now);
				betBoard.BetRanges.Add(Now, domain, 1, 0.25, 'N/A');

				questionId = catalog.NextQuestionId();
				spreadQuestion = catalog.CreateTierOneSpreadQuestion(questionId, 'ss');
				questionId = catalog.NextQuestionId();
				moneyQuestion = catalog.CreateTierOneMoneyQuestion(questionId, 'mm');
				
				catalog.LinkAliasToQuestion({ProvidersCollection.BetFair.Id},'MONEY_LINE',moneyQuestion);

				questionId = catalog.NextQuestionId();
				totalPointsQuestion = catalog.CreateTierOneTotalPointsQuestion(questionId, 'tt');
				questionId = catalog.NextQuestionId();
				yesNoQuestion = catalog.CreateTierTwoYesNoQuestion(questionId, 'BT', 'Both Teams to Score Second Half');
				questionId = catalog.NextQuestionId();
				yesNoQuestion2 = catalog.CreateTierOneYesNoQuestion(questionId, 'GGO', 'Will The Game Go to Overtime?');
				questionId = catalog.NextQuestionId();
				overUnderQuestion = catalog.CreateTierOneOverUnderQuestion(questionId, 'GT', 'Game Total');
				questionId = catalog.NextQuestionId();
				fixedQuestion = catalog.CreateTierOneFixedQuestion(questionId, 'WM', 'Winning Margin');

				schedule = nba.Schedule;
				game = nba.GetGameNumber({gameNumber});
				game.Reschedule({today.Month}/{today.Day}/{today.Year} 12:10:15);
				schedule.AddGameToMatchday(game, {today.Month}/{today.Day}/{today.Year}, game.TeamA, game.TeamA);
				matchDay = betBoard.Matchday({today.Month}/{today.Day}/{today.Year});
				game.SetFavorite(game.TeamA);

				showcase = matchDay.GetShowcase(game);
				spreadQuestion.SetParameters(14.5, -109, -131);
				lineId = betBoard.NextLineId();
				shelve = showcase.InsertShelve({fila1});
				shelve.CreateLine(lineId, spreadQuestion, 'N/A', Now);
				moneyQuestion.SetParameters(342, -681);
				lineId = betBoard.NextLineId();
				shelve = showcase.InsertShelve({fila1});
				shelve.CreateLine(lineId, moneyQuestion, 'N/A', Now);
				totalPointsQuestion.SetParameters(2, -120, 105);
				lineId = betBoard.NextLineId();
				shelve = showcase.InsertShelve({fila1});
				shelve.CreateLine(lineId, totalPointsQuestion, 'N/A', Now);
				yesNoQuestion.SetParameters(293, -498);
				lineId = betBoard.NextLineId();
				shelve = showcase.InsertShelve({fila1});
				shelve.CreateLine(lineId, yesNoQuestion, 'N/A', Now);
				yesNoQuestion2.SetParameters(759, -4014);
				lineId = betBoard.NextLineId();
				shelve = showcase.InsertShelve({fila1});
				shelve.CreateLine(lineId, yesNoQuestion2, 'N/A', Now);
				overUnderQuestion.SetParameters(175.5, -115, -125);
				lineId = betBoard.NextLineId();
				shelve = showcase.InsertShelve({fila1});
				shelve.CreateLine(lineId, overUnderQuestion, 'N/A', Now);
				fixedQuestion.SetParameters({{{options}}},{{{rewards}}});
				lineId = betBoard.NextLineId();
				shelve = showcase.InsertShelve({fila1});
				shelve.CreateLine(lineId, fixedQuestion, 'N/A', Now);

				game = nba.GetGameNumber({gameNumber});
				schedule.AddGameToMatchday(game, {tomorrow.Month}/{tomorrow.Day}/{tomorrow.Year}, game.TeamA, game.TeamA);
				matchDay = betBoard.Matchday({tomorrow.Month}/{tomorrow.Day}/{tomorrow.Year});
				game.SetFavorite(game.TeamA);
				lineId = betBoard.NextLineId();
				spreadVersion = matchDay.GetShowcase(game).GetShelve({fila1}).CreateMoneyLine(lineId, -121, 105, 'N/A', Now);

				schedule = nba.Schedule;
				game = nba.GetGameNumber({gameNumber2});
				game.Reschedule({today.Month}/{today.Day}/{today.Year} 18:10:15);
				schedule.AddGameToMatchday(game, {today.Month}/{today.Day}/{today.Year}, game.TeamA, game.TeamA);
				matchDay = betBoard.Matchday({today.Month}/{today.Day}/{today.Year});
				game.SetFavorite(game.TeamA);

				showcase = matchDay.GetShowcase(game);
				spreadQuestion.SetParameters(14.5, -109, 131);
				lineId = betBoard.NextLineId();
				shelve2 = showcase.InsertShelve({fila1});
				shelve2.CreateLine(lineId, spreadQuestion, 'N/A', Now);
				moneyQuestion.SetParameters(-342, 681);
				lineId = betBoard.NextLineId();
				shelve2 = showcase.InsertShelve({fila1});
				shelve2.CreateLine(lineId, moneyQuestion, 'N/A', Now);
				totalPointsQuestion.SetParameters(2, -110, 115);
				lineId = betBoard.NextLineId();
				shelve2 = showcase.InsertShelve({fila1});
				shelve2.CreateLine(lineId, totalPointsQuestion, 'N/A', Now);
				yesNoQuestion.SetParameters(-498, 293);
				lineId = betBoard.NextLineId();
				shelve2 = showcase.InsertShelve({fila1});
				shelve2.CreateLine(lineId, yesNoQuestion, 'N/A', Now);
				yesNoQuestion2.SetParameters(-401, 759);
				lineId = betBoard.NextLineId();
				shelve2 = showcase.InsertShelve({fila1});
				shelve2.CreateLine(lineId, yesNoQuestion2, 'N/A', Now);
				overUnderQuestion.SetParameters(165.5, -125, -115);
				lineId = betBoard.NextLineId();
				shelve2 = showcase.InsertShelve({fila1});
				shelve2.CreateLine(lineId, overUnderQuestion, 'N/A', Now);
				fixedQuestion.SetParameters({{{options}}},{{{rewards2}}});
				lineId = betBoard.NextLineId();
				shelve2 = showcase.InsertShelve({fila1});
				shelve2.CreateLine(lineId, fixedQuestion, 'N/A', Now);

				game = nba.GetGameNumber({gameNumber2});
				schedule.AddGameToMatchday(game, {tomorrow.Month}/{tomorrow.Day}/{tomorrow.Year}, game.TeamA, game.TeamA);
				matchDay = betBoard.Matchday({tomorrow.Month}/{tomorrow.Day}/{tomorrow.Year});
				game.SetFavorite(game.TeamA);
				lineId = betBoard.NextLineId();
				spreadVersion1 = matchDay.GetShowcase(game).GetShelve({fila1}).CreateMoneyLine(lineId, -120, 105, 'N/A', Now);
			
			");

			return this;
		}

		internal LinesMockGenerator CreateTournament(int leagueId, string name)
		{
			Perform($@"
				tournamentId = company.Tournaments.NextTournamentId;
				league = company.Tournaments.Leagues.FindById({leagueId});
				tournament = company.Tournaments.CreateTournament(league, tournamentId, '{name}');
			");

			return this;
		}

		internal LinesMockGenerator OpenRegistrationTournament(int tournamentId)
		{
			Perform($@"
				tournament = company.Tournaments.FindById({tournamentId});
				tournament.OpenRegistration();
			");

			return this;
		}

		internal LinesMockGenerator CloseRegistrationTournament(int tournamentId)
		{
			Perform($@"
				tournament = company.Tournaments.FindById({tournamentId});
				tournament.CloseRegistration();
			");

			return this;
		}
		internal LinesMockGenerator StartTournament(int tournamentId)
		{
			Perform($@"
				tournament = company.Tournaments.FindById({tournamentId});
				tournament.Start();
			");

			return this;
		}

		internal LinesMockGenerator GradeThisGame(int tournamentId, int gameNumber, int lineId, int scoreTeamA, int scoreTeamB)
		{
			bool itIsThePresent = true;
			Perform($@"
				{{
					tournament = company.Tournaments.FindById({tournamentId});
					betBoard = company.Betboard(tournament);
					game = tournament.GetGameNumber({gameNumber});
					scheduledDate = game.ScheduledDate.Fecha();
					matchDay = betBoard.Matchday(scheduledDate);
					showcase = matchDay.GetShowcase(game);
					line = betBoard.GetLine({lineId});
					showcase.Grade({itIsThePresent}, line, Now, game.TeamA, {scoreTeamA}, game.TeamB, {scoreTeamB}, 'N/A');
				}}
			");

			return this;
		}

		internal LinesMockGenerator GradeAYesNOLine(int tournamentId, int gameNumber, int lineId, bool score)
		{
			bool itIsThePresent = true;
			Perform($@"
				{{
					tournament = company.Tournaments.FindById({tournamentId});
					betBoard = company.Betboard(tournament);
					game = tournament.GetGameNumber({gameNumber});
					scheduledDate = game.ScheduledDate.Fecha();
					matchDay = betBoard.Matchday(scheduledDate);
					showcase = matchDay.GetShowcase(game);
					line = betBoard.GetLine({lineId});
					showcase.Grade({itIsThePresent}, line, {score}, Now, 'N/A');
				}}
			");

			return this;
		}

		internal LinesMockGenerator NewResultForThisGame(int tournamentId, int gameNumber, int scoreTeamA, int scoreTeamB)
		{
			bool itIsThePresent = true;
			Perform($@"
				tournament = company.Tournaments.FindById({tournamentId});
				game = tournament.GetGameNumber({gameNumber});
				game.NewResult({itIsThePresent}, Now, game.TeamA, {scoreTeamA}, game.TeamB, {scoreTeamB});
			");

			return this;
		}
		//

		internal LinesMockGenerator SetGameWithTeam(int gameNumber, int tournamentId, int leagueId, string teamA, string teamB, string shortNameA, string shortNameB)
		{
			//print game.Tournament.StartedDate tournamentOpenDate;
			string json = Perform($@"
				tournament = tournaments.FindById({tournamentId});
				league = tournaments.Leagues.FindById({leagueId});   
				Eval('teamId =' + company.Tournaments.NextTeamId() + ';');
				teama = tournaments.CreateTeam(league, teamId, '{teamA}', '{shortNameA}');   
				Eval('teamId =' + company.Tournaments.NextTeamId() + ';');
				teamb = tournaments.CreateTeam(league, teamId, '{teamB}', '{shortNameB}');
				tournament.Register(teama);
				tournament.Register(teamb);
				game = tournament.GetNewGame({gameNumber},teama,teamb);
				game.Home = game.TeamA;
				game.Visitor = game.TeamB;
				game.SetFavorite(game.TeamA);

				{Synchronizator.GameInfoToSendToSearchEngine}
			");

			GameRelatedInformation newDocument = JsonConvert.DeserializeObject<GameRelatedInformation>(json);

			Synchronizator.CreateOrUpdateOfficialDocuments(newDocument);

			return this;
		}



		internal LinesMockGenerator RescheduleGame(int gameNumber, int tournamentId, DateTime now)
		{
			var startDate = $"{now.Month}/{now.Day}/{now.Year} {now.Hour}:{now.Minute}:{now.Second}";

			Perform($@"
				tournament = company.Tournaments.FindById({tournamentId});
				game = tournament.GetGameNumber({gameNumber});
                game.Reschedule({startDate});
			");

			return this;
		}

		internal LinesMockGenerator StartGame(int gameNumber, int tournamentId)
		{
			bool itIsThePresent = true;
			var now = DateTime.Now;
			var startDate = $"{now.Month}/{now.Day}/{now.Year} {now.Hour}:{now.Minute}:{now.Second}";

			Perform($@"
				tournament = company.Tournaments.FindById({tournamentId});
				game = tournament.GetGameNumber({gameNumber});
				game.Start({itIsThePresent}, {startDate}, Now);
			");

			return this;
		}

		internal LinesMockGenerator GameMoveToTheNextPeriod(int gameNumber, int tournamentId, string anEvent)
		{
			var now = DateTime.Now;
			var date = $"{now.Month}/{now.Day}/{now.Year} {now.Hour}:{now.Minute}:{now.Second}";

			Perform($@"
				tournament = company.Tournaments.FindById({tournamentId});
				game = tournament.GetGameNumber({gameNumber});
				game.MoveToTheNextPeriod(itIsThePresent, '{anEvent}', {date});
			");

			return this;
		}

		internal LinesMockGenerator CreateLeague(int idSport, string name, string shortname)
		{
			Perform($@"
				leagueId = company.Tournaments.Leagues.NextLeagueId;
				tournamentId = company.Tournaments.NextTournamentId;
				sport = company.Tournaments.Sports.FindById({idSport});
				league = company.Tournaments.Leagues.Create(sport, '{name}', '{shortname}', leagueId);
			");

			return this;
		}

		internal LinesMockGenerator AddGameToMatchday(int gameNumber, int tournamentId)
		{
			var now = DateTime.Now;
			var dayAsText = $"{now.Month}/{now.Day}/{now.Year}";
			Perform($@"
				tournament = company.Tournaments.FindById({tournamentId});
				game = tournament.GetGameNumber({gameNumber});
				schedule = tournament.Schedule;
				schedule.AddGameToMatchday(game, {dayAsText}, game.TeamA, game.TeamA);
			");

			return this;
		}

		internal LinesMockGenerator RemindA(int gameNumber, int tournamentId, string player)
		{

			Perform($@"
				tournament = company.Tournaments.FindById({tournamentId});
                game = tournament.GetGameNumber({gameNumber});
                player = company.GetOrCreateCustomerById('{player}').Player;
                player.Preferences.GamesReminder.Remind(game);
			");

			return this;
		}

		internal LinesMockGenerator CheckSport(int tournamentId, int gameNumber, string player)
		{

			Perform($@"
				tournament = company.Tournaments.FindById({tournamentId});
                game = tournament.GetGameNumber({gameNumber});
                player = company.GetOrCreateCustomerById('{player}').Player;
				player.Preferences.Sports.Mark(game.Sport);
			");

			return this;
		}

		private static bool existT1QuestionsForBasket = false;
		private static bool existT1QuestionsForSoccer = false;
		private static bool existT1QuestionsForFootball = false;
		private static bool existT1QuestionsForBaseball = false;
		private static bool existT1QuestionsForHockey = false;

		internal LinesMockGenerator CreateQuestions(int idSport)
		{

			//Soccer
			if (idSport == 1)
			{
				JObject questionIds = null;
				var scriptForSoccerQuestionIds = $@"questions = catalog_SOCCER.Questions;
					for(question:questions)
					{{
						if(question.Text == 'Spread Line' && question.Tier.Name == 'T1')
						{{
							print question.Id SpreadQuestion;
						}}
						else if(question.Text == 'Money Line' && question.Tier.Name == 'T1')
						{{
							print question.Id MoneyQuestion;
						}}
						else if(question.Text == 'Money Draw Line' && question.Tier.Name == 'T1')
						{{
							print question.Id MoneyDrawQuestion;
						}}
						else if(question.Text == 'Total Points Line' && question.Tier.Name == 'T1')
						{{
							print question.Id TotalPointsQuestion;
						}}
					}}";
				if (!existT1QuestionsForSoccer)
				{
					questionIds = JObject.Parse(Perform($@"sport_SOCCER = tournaments.Sports.FindById(1);
							catalog_SOCCER = tournaments.Catalog(sport_SOCCER);
							questionId = catalog_SOCCER.NextQuestionId();
							question = catalog_SOCCER.CreateTierOneMoneyQuestion(questionId, 'mm');
							background = catalog_SOCCER.GetOrCreateBackground('images/bg_lines/basketball/money_total_spread/win-black1.jpg');
							question.Background = background;
							questionId = catalog_SOCCER.NextQuestionId();
							question = catalog_SOCCER.CreateTierOneMoneyDrawQuestion(questionId, 'dd');
							background = catalog_SOCCER.GetOrCreateBackground('images/bg_lines/basketball/money_total_spread/win-black2.jpg');
							question.Background = background;
							questionId = catalog_SOCCER.NextQuestionId();
							question = catalog_SOCCER.CreateTierOneSpreadQuestion(questionId, 'ss');
							background = catalog_SOCCER.GetOrCreateBackground('images/bg_lines/basketball/money_total_spread/win-black3.jpg');
							question.Background = background;
							questionId = catalog_SOCCER.NextQuestionId();
							question = catalog_SOCCER.CreateTierOneTotalPointsQuestion(questionId, 'tt');
							background = catalog_SOCCER.GetOrCreateBackground('images/bg_lines/basketball/money_total_spread/win-black4.jpg');
							question.Background = background;
							{scriptForSoccerQuestionIds}"
					));

					existT1QuestionsForSoccer = true;
				}
				else
				{
					questionIds = JObject.Parse(Perform($"{scriptForSoccerQuestionIds}"));
				}

				Perform($@"
					question = catalog_SOCCER.FindQuestionById({Convert.ToInt32(questionIds["question"][0]["MoneyQuestion"])});
					catalog_SOCCER.LinkAliasToQuestion({ProvidersCollection.BetFair.Id},'MONEY_LINE',question);
					background = catalog_SOCCER.GetOrCreateBackground('images/bg_lines/soccer/money_total_spread/win-black1.jpg');
					question.Background = background;

					question = catalog_SOCCER.FindQuestionById({Convert.ToInt32(questionIds["question"][0]["MoneyQuestion"])});
					catalog_SOCCER.LinkAliasToQuestion({ProvidersCollection.BetFair.Id}, 'MATCH_ODDS2', 'MATCH_ODDS', question);
					background = catalog_SOCCER.GetOrCreateBackground('images/bg_lines/soccer/money_total_spread/win-black2.jpg');
					question.Background = background;

					question = catalog_SOCCER.FindQuestionById({Convert.ToInt32(questionIds["question"][1]["MoneyDrawQuestion"])});
					catalog_SOCCER.LinkAliasToQuestion({ProvidersCollection.BetFair.Id}, 'MATCH_ODDS3', 'MATCH_ODDS', question);
					background = catalog_SOCCER.GetOrCreateBackground('images/bg_lines/soccer/money_total_spread/win-black3.jpg');
					question.Background = background;

					question = catalog_SOCCER.FindQuestionById({Convert.ToInt32(questionIds["question"][0]["MoneyQuestion"])});
					catalog_SOCCER.LinkAliasToQuestion({ProvidersCollection.BetFair.Id},'MATCH_BET',question);
					background = catalog_SOCCER.GetOrCreateBackground('images/bg_lines/soccer/money_total_spread/win-black7.jpg');
					question.Background = background;

					Eval('questionId = ' + catalog_SOCCER.NextQuestionId() + ';');
					question = catalog_SOCCER.CreateTierTwoMoneyQuestion(questionId, 'mn1');
					catalog_SOCCER.LinkAliasToQuestion({ProvidersCollection.BetFair.Id},'HALF_MATCH_ODDS',question);
					background = catalog_SOCCER.GetOrCreateBackground('images/bg_lines/soccer/money_total_spread/win-black1.jpg');
					question.Background = background;
					question.AllowPublished({{{"'Pregame', 'In play', 'First half', 'Second half', 'First half extra', 'Second half extra', 'Penalties'"}}});	

					question = catalog_SOCCER.FindQuestionById({Convert.ToInt32(questionIds["question"][3]["TotalPointsQuestion"])});
					catalog_SOCCER.LinkAliasToQuestion({ProvidersCollection.BetFair.Id},'TOTAL_MATCH_POINTS',question);
					background = catalog_SOCCER.GetOrCreateBackground('images/bg_lines/soccer/money_total_spread/win-black2.jpg');
					question.Background = background;

					Eval('questionId = ' + catalog_SOCCER.NextQuestionId() + ';');
					question = catalog_SOCCER.CreateTierTwoYesNoQuestion(questionId, 'BT01', 'Both Teams to Score');
					catalog_SOCCER.LinkAliasToQuestion({ProvidersCollection.BetFair.Id},'BOTH_TEAMS_TO_SCORE',question);
					background = catalog_SOCCER.GetOrCreateBackground('images/bg_lines/soccer/yes-no/yes-no1.jpg');
					question.Background = background;

					Eval('questionId = ' + catalog_SOCCER.NextQuestionId() + ';');
					question = catalog_SOCCER.CreateTierTwoYesNoQuestion(questionId, 'BT02', 'Hat-trick Scored');
					catalog_SOCCER.LinkAliasToQuestion({ProvidersCollection.BetFair.Id},'HAT_TRICKED_SCORED',question);
					background = catalog_SOCCER.GetOrCreateBackground('images/bg_lines/soccer/yes-no/yes-no2.jpg');
					question.Background = background;

					Eval('questionId = ' + catalog_SOCCER.NextQuestionId() + ';');
					question = catalog_SOCCER.CreateTierOneOverUnderQuestion(questionId, 'BT03', 'Over/Under 0.5 Goals');
					catalog_SOCCER.LinkAliasToQuestion({ProvidersCollection.BetFair.Id},'OVER_UNDER_05',question);
					background = catalog_SOCCER.GetOrCreateBackground('images/bg_lines/soccer/over-under/over-under1.jpg');
					question.Background = background;

					Eval('questionId = ' + catalog_SOCCER.NextQuestionId() + ';');
					question = catalog_SOCCER.CreateTierOneOverUnderQuestion(questionId, 'BT06', 'Over/Under 1.5 Goals');
					catalog_SOCCER.LinkAliasToQuestion({ProvidersCollection.BetFair.Id},'OVER_UNDER_15',question);
					background = catalog_SOCCER.GetOrCreateBackground('images/bg_lines/soccer/over-under/over-under4.jpg');
					question.Background = background;

					Eval('questionId = ' + catalog_SOCCER.NextQuestionId() + ';');
					question = catalog_SOCCER.CreateTierOneOverUnderQuestion(questionId, 'BT07', 'Over/Under 2.5 Goals');
					catalog_SOCCER.LinkAliasToQuestion({ProvidersCollection.BetFair.Id},'OVER_UNDER_25',question);
					background = catalog_SOCCER.GetOrCreateBackground('images/bg_lines/soccer/over-under/over-under5.jpg');
					question.Background = background;

					Eval('questionId = ' + catalog_SOCCER.NextQuestionId() + ';');
					question = catalog_SOCCER.CreateTierOneOverUnderQuestion(questionId, 'BT08', 'Over/Under 3.5 Goals');
					catalog_SOCCER.LinkAliasToQuestion({ProvidersCollection.BetFair.Id},'OVER_UNDER_35',question);
					background = catalog_SOCCER.GetOrCreateBackground('images/bg_lines/soccer/over-under/over-under6.jpg');
					question.Background = background;

					Eval('questionId = ' + catalog_SOCCER.NextQuestionId() + ';');
					question = catalog_SOCCER.CreateTierOneOverUnderQuestion(questionId, 'BT09', 'Over/Under 4.5 Goals');
					catalog_SOCCER.LinkAliasToQuestion({ProvidersCollection.BetFair.Id},'OVER_UNDER_45',question);
					background = catalog_SOCCER.GetOrCreateBackground('images/bg_lines/soccer/over-under/over-under1.jpg');
					question.Background = background;

					Eval('questionId = ' + catalog_SOCCER.NextQuestionId() + ';');
					question = catalog_SOCCER.CreateTierOneOverUnderQuestion(questionId, 'BT10', 'Over/Under 5.5 Goals');
					catalog_SOCCER.LinkAliasToQuestion({ProvidersCollection.BetFair.Id},'OVER_UNDER_55',question);
					background = catalog_SOCCER.GetOrCreateBackground('images/bg_lines/soccer/over-under/over-under2.jpg');
					question.Background = background;

					Eval('questionId = ' + catalog_SOCCER.NextQuestionId() + ';');
					question = catalog_SOCCER.CreateTierOneOverUnderQuestion(questionId, 'BT11', 'Over/Under 6.5 Goals');
					catalog_SOCCER.LinkAliasToQuestion({ProvidersCollection.BetFair.Id},'OVER_UNDER_65',question);
					background = catalog_SOCCER.GetOrCreateBackground('images/bg_lines/soccer/over-under/over-under3.jpg');
					question.Background = background;

					Eval('questionId = ' + catalog_SOCCER.NextQuestionId() + ';');
					question = catalog_SOCCER.CreateTierOneOverUnderQuestion(questionId, 'BT12', 'Over/Under 7.5 Goals');
					catalog_SOCCER.LinkAliasToQuestion({ProvidersCollection.BetFair.Id},'OVER_UNDER_75',question);
					background = catalog_SOCCER.GetOrCreateBackground('images/bg_lines/soccer/over-under/over-under4.jpg');
					question.Background = background;

					Eval('questionId = ' + catalog_SOCCER.NextQuestionId() + ';');
					question = catalog_SOCCER.CreateTierOneOverUnderQuestion(questionId, 'BT13', 'Over/Under 8.5 Goals');
					catalog_SOCCER.LinkAliasToQuestion({ProvidersCollection.BetFair.Id},'OVER_UNDER_85',question);
					background = catalog_SOCCER.GetOrCreateBackground('images/bg_lines/soccer/over-under/over-under5.jpg');
					question.Background = background;

					Eval('questionId = ' + catalog_SOCCER.NextQuestionId() + ';');
					question = catalog_SOCCER.CreateTierOneFixedQuestion(questionId, 'BT15', 'Double Chance');
					catalog_SOCCER.LinkAliasToQuestion({ProvidersCollection.BetFair.Id},'DOUBLE_CHANCE',question);
					background = catalog_SOCCER.GetOrCreateBackground('images/bg_lines/soccer/fixed/fixed1.jpg');
					question.Background = background;

					Eval('questionId = ' + catalog_SOCCER.NextQuestionId() + ';');
					question = catalog_SOCCER.CreateTierOneOverUnderQuestion(questionId, 'BT16', 'First Half Goals 1.5');
					catalog_SOCCER.LinkAliasToQuestion({ProvidersCollection.BetFair.Id},'FIRST_HALF_GOALS_15',question);
					background = catalog_SOCCER.GetOrCreateBackground('images/bg_lines/soccer/over-under/over-under1.jpg');
					question.Background = background;

					Eval('questionId = ' + catalog_SOCCER.NextQuestionId() + ';');
					question = catalog_SOCCER.CreateTierOneOverUnderQuestion(questionId, 'BT17', 'First Half Goals 0.5');
					catalog_SOCCER.LinkAliasToQuestion({ProvidersCollection.BetFair.Id},'FIRST_HALF_GOALS_05',question);
					background = catalog_SOCCER.GetOrCreateBackground('images/bg_lines/soccer/over-under/over-under2.jpg');
					question.Background = background;

					Eval('questionId = ' + catalog_SOCCER.NextQuestionId() + ';');
					question = catalog_SOCCER.CreateTierOneOverUnderQuestion(questionId, 'BT18', 'First Half Goals 2.5');
					catalog_SOCCER.LinkAliasToQuestion({ProvidersCollection.BetFair.Id},'FIRST_HALF_GOALS_25',question);
					background = catalog_SOCCER.GetOrCreateBackground('images/bg_lines/soccer/over-under/over-under3.jpg');
					question.Background = background;

					Eval('questionId = ' + catalog_SOCCER.NextQuestionId() + ';');
					question = catalog_SOCCER.CreateTierOneFixedQuestion(questionId, 'BT19', 'Correct Score');
					catalog_SOCCER.LinkAliasToQuestion({ProvidersCollection.BetFair.Id},'CORRECT_SCORE',question);
					background = catalog_SOCCER.GetOrCreateBackground('images/bg_lines/soccer/fixed/fixed2.jpg');
					question.Background = background;

					Eval('questionId = ' + catalog_SOCCER.NextQuestionId() + ';');
					question = catalog_SOCCER.CreateTierOneFixedQuestion(questionId, 'BT20', 'Half Time/Full Time');
					catalog_SOCCER.LinkAliasToQuestion({ProvidersCollection.BetFair.Id},'HALF_TIME_FULL_TIME',question);
					background = catalog_SOCCER.GetOrCreateBackground('images/bg_lines/soccer/fixed/fixed3.jpg');
					question.Background = background;

					Eval('questionId = ' + catalog_SOCCER.NextQuestionId() + ';');
					question = catalog_SOCCER.CreateTierOneFixedQuestion(questionId, 'BT21', 'Half Time Score');
					catalog_SOCCER.LinkAliasToQuestion({ProvidersCollection.BetFair.Id},'HALF_TIME_SCORE',question);
					background = catalog_SOCCER.GetOrCreateBackground('images/bg_lines/soccer/fixed/fixed4.jpg');
					question.Background = background;

					Eval('questionId = ' + catalog_SOCCER.NextQuestionId() + ';');
					question = catalog_SOCCER.CreateTierOneFixedQuestion(questionId, 'BT22', 'Total Goals Odd/Even');
					catalog_SOCCER.LinkAliasToQuestion({ProvidersCollection.BetFair.Id},'ODD_OR_EVEN',question);
					background = catalog_SOCCER.GetOrCreateBackground('images/bg_lines/soccer/fixed/fixed5.jpg');
					question.Background = background;

					Eval('questionId = ' + catalog_SOCCER.NextQuestionId() + ';');
					question = catalog_SOCCER.CreateTierOneFixedQuestion(questionId, 'BT23', 'Time Of First Goal');
					catalog_SOCCER.LinkAliasToQuestion({ProvidersCollection.BetFair.Id},'FIRST_GOAL_ODDS',question);
					background = catalog_SOCCER.GetOrCreateBackground('images/bg_lines/soccer/fixed/fixed6.jpg');
					question.Background = background;

					Eval('questionId = ' + catalog_SOCCER.NextQuestionId() + ';');
					question = catalog_SOCCER.CreateTierOneFixedQuestion(questionId, 'BT24', 'Correct Score 2 Home');
					catalog_SOCCER.LinkAliasToQuestion({ProvidersCollection.BetFair.Id},'CORRECT_SCORE2_2', 'CORRECT_SCORE', question);
					background = catalog_SOCCER.GetOrCreateBackground('images/bg_lines/soccer/fixed/fixed1.jpg');
					question.Background = background;

					Eval('questionId = ' + catalog_SOCCER.NextQuestionId() + ';');
					question = catalog_SOCCER.CreateTierOneFixedQuestion(questionId, 'BT25', 'Correct Score 2 Away');
					catalog_SOCCER.LinkAliasToQuestion({ProvidersCollection.BetFair.Id}, 'CORRECT_SCORE2_3', 'CORRECT_SCORE', question);
					background = catalog_SOCCER.GetOrCreateBackground('images/bg_lines/soccer/fixed/fixed2.jpg');
					question.Background = background;

					Eval('questionId = ' + catalog_SOCCER.NextQuestionId() + ';');
					question = catalog_SOCCER.CreateTierOneYesNoQuestion(questionId, 'BT26', 'Penalty Taken?');
					catalog_SOCCER.LinkAliasToQuestion({ProvidersCollection.BetFair.Id},'PENALTY_TAKEN',question);
					background = catalog_SOCCER.GetOrCreateBackground('images/bg_lines/soccer/yes-no/yes-no3.jpg');
					question.Background = background;

					Eval('questionId = ' + catalog_SOCCER.NextQuestionId() + ';');
					question = catalog_SOCCER.CreateTierOneYesNoQuestion(questionId, 'BT27', 'Sending Off?');
					catalog_SOCCER.LinkAliasToQuestion({ProvidersCollection.BetFair.Id},'SENDING_OFF',question);
					background = catalog_SOCCER.GetOrCreateBackground('images/bg_lines/soccer/yes-no/yes-no4.jpg');
					question.Background = background;

					Eval('questionId = ' + catalog_SOCCER.NextQuestionId() + ';');
					question = catalog_SOCCER.CreateTierOneFixedQuestion(questionId, 'BT28', 'To Qualify');
					catalog_SOCCER.LinkAliasToQuestion({ProvidersCollection.BetFair.Id},'TO_QUALIFY',question);
					background = catalog_SOCCER.GetOrCreateBackground('images/bg_lines/soccer/fixed/fixed3.jpg');
					question.Background = background;

					Eval('questionId = ' + catalog_SOCCER.NextQuestionId() + ';');
					question = catalog_SOCCER.CreateTierOneFixedQuestion(questionId, 'BT40', 'To Score');
					catalog_SOCCER.LinkAliasToQuestion({ProvidersCollection.BetFair.Id},'TO_SCORE',question);
					background = catalog_SOCCER.GetOrCreateBackground('images/bg_lines/soccer/fixed/fixed1.jpg');
					question.Background = background;

					Eval('questionId = ' + catalog_SOCCER.NextQuestionId() + ';');
					question = catalog_SOCCER.CreateTierOneFixedQuestion(questionId, 'BT41', 'Corners Match Bet');
					catalog_SOCCER.LinkAliasToQuestion({ProvidersCollection.BetFair.Id},'CORNER_MATCH_BET',question);
					background = catalog_SOCCER.GetOrCreateBackground('images/bg_lines/soccer/fixed/fixed2.jpg');
					question.Background = background;

					Eval('questionId = ' + catalog_SOCCER.NextQuestionId() + ';');
					question = catalog_SOCCER.CreateTierOneFixedQuestion(questionId, 'BT43', 'Bookings Odds');
					catalog_SOCCER.LinkAliasToQuestion({ProvidersCollection.BetFair.Id},'BOOKING_ODDS',question);
					background = catalog_SOCCER.GetOrCreateBackground('images/bg_lines/soccer/fixed/fixed4.jpg');
					question.Background = background;

					Eval('questionId = ' + catalog_SOCCER.NextQuestionId() + ';');
					question = catalog_SOCCER.CreateTierOneFixedQuestion(questionId, 'BT46', 'To Score a Hat-trick?');
					catalog_SOCCER.LinkAliasToQuestion({ProvidersCollection.BetFair.Id},'TO_SCORE_HATTRICK',question);
					background = catalog_SOCCER.GetOrCreateBackground('images/bg_lines/soccer/fixed/fixed7.jpg');
					question.Background = background;

					Eval('questionId = ' + catalog_SOCCER.NextQuestionId() + ';');
					question = catalog_SOCCER.CreateTierOneFixedQuestion(questionId, 'BT47', 'Shown a card?');
					catalog_SOCCER.LinkAliasToQuestion({ProvidersCollection.BetFair.Id},'SHOWN_A_CARD',question);
					background = catalog_SOCCER.GetOrCreateBackground('images/bg_lines/soccer/fixed/fixed1.jpg');
					question.Background = background;

					Eval('questionId = ' + catalog_SOCCER.NextQuestionId() + ';');
					question = catalog_SOCCER.CreateTierOneFixedQuestion(questionId, 'BT48', 'Scorecast');
					catalog_SOCCER.LinkAliasToQuestion({ProvidersCollection.BetFair.Id},'SCORE_CAST',question);
					background = catalog_SOCCER.GetOrCreateBackground('images/bg_lines/soccer/fixed/fixed2.jpg');
					question.Background = background;

					Eval('questionId = ' + catalog_SOCCER.NextQuestionId() + ';');
					question = catalog_SOCCER.CreateTierOneFixedQuestion(questionId, 'BT49', 'To Score 2 Goals or more');
					catalog_SOCCER.LinkAliasToQuestion({ProvidersCollection.BetFair.Id},'TO_SCORE_2_OR_MORE',question);
					background = catalog_SOCCER.GetOrCreateBackground('images/bg_lines/soccer/fixed/fixed3.jpg');
					question.Background = background;

					Eval('questionId = ' + catalog_SOCCER.NextQuestionId() + ';');
					question = catalog_SOCCER.CreateTierOneOverUnderQuestion(questionId, 'BT50', 'Corners Over/Under 4.5');
					catalog_SOCCER.LinkAliasToQuestion({ProvidersCollection.BetFair.Id},'OVER_UNDER_45_CORNR',question);
					background = catalog_SOCCER.GetOrCreateBackground('images/bg_lines/soccer/over-under/over-under1.jpg');
					question.Background = background;

					Eval('questionId = ' + catalog_SOCCER.NextQuestionId() + ';');
					question = catalog_SOCCER.CreateTierOneOverUnderQuestion(questionId, 'BT51', 'Corners Over/Under 5.5');
					catalog_SOCCER.LinkAliasToQuestion({ProvidersCollection.BetFair.Id},'OVER_UNDER_55_CORNR',question);
					background = catalog_SOCCER.GetOrCreateBackground('images/bg_lines/soccer/over-under/over-under2.jpg');
					question.Background = background;

					Eval('questionId = ' + catalog_SOCCER.NextQuestionId() + ';');
					question = catalog_SOCCER.CreateTierOneOverUnderQuestion(questionId, 'BT52', 'Corners Over/Under 6.5');
					catalog_SOCCER.LinkAliasToQuestion({ProvidersCollection.BetFair.Id},'OVER_UNDER_65_CORNR',question);
					background = catalog_SOCCER.GetOrCreateBackground('images/bg_lines/soccer/over-under/over-under3.jpg');
					question.Background = background;

					Eval('questionId = ' + catalog_SOCCER.NextQuestionId() + ';');
					question = catalog_SOCCER.CreateTierOneOverUnderQuestion(questionId, 'BT53', 'Corners Over/Under 7.5');
					catalog_SOCCER.LinkAliasToQuestion({ProvidersCollection.BetFair.Id},'OVER_UNDER_75_CORNR',question);
					background = catalog_SOCCER.GetOrCreateBackground('images/bg_lines/soccer/over-under/over-under4.jpg');
					question.Background = background;

					Eval('questionId = ' + catalog_SOCCER.NextQuestionId() + ';');
					question = catalog_SOCCER.CreateTierOneOverUnderQuestion(questionId, 'BT54', 'Corners Over/Under 8.5');
					catalog_SOCCER.LinkAliasToQuestion({ProvidersCollection.BetFair.Id},'OVER_UNDER_85_CORNR',question);
					background = catalog_SOCCER.GetOrCreateBackground('images/bg_lines/soccer/over-under/over-under5.jpg');
					question.Background = background;

					Eval('questionId = ' + catalog_SOCCER.NextQuestionId() + ';');
					question = catalog_SOCCER.CreateTierOneOverUnderQuestion(questionId, 'BT55', 'Corners Over/Under 9.5');
					catalog_SOCCER.LinkAliasToQuestion({ProvidersCollection.BetFair.Id},'OVER_UNDER_95_CORNR',question);
					background = catalog_SOCCER.GetOrCreateBackground('images/bg_lines/soccer/over-under/over-under1.jpg');
					question.Background = background;

					Eval('questionId = ' + catalog_SOCCER.NextQuestionId() + ';');
					question = catalog_SOCCER.CreateTierOneOverUnderQuestion(questionId, 'BT56', 'Corners Over/Under 10.5');
					catalog_SOCCER.LinkAliasToQuestion({ProvidersCollection.BetFair.Id},'OVER_UNDER_105_CORNR',question);
					background = catalog_SOCCER.GetOrCreateBackground('images/bg_lines/soccer/over-under/over-under2.jpg');
					question.Background = background;

					Eval('questionId = ' + catalog_SOCCER.NextQuestionId() + ';');
					question = catalog_SOCCER.CreateTierOneOverUnderQuestion(questionId, 'BT57', 'Corners Over/Under 11.5');
					catalog_SOCCER.LinkAliasToQuestion({ProvidersCollection.BetFair.Id},'OVER_UNDER_115_CORNR',question);
					background = catalog_SOCCER.GetOrCreateBackground('images/bg_lines/soccer/over-under/over-under3.jpg');
					question.Background = background;

						Eval('questionId = ' + catalog_SOCCER.NextQuestionId() + ';');
					question = catalog_SOCCER.CreateTierOneOverUnderQuestion(questionId, 'BT58', 'Corners Over/Under 12.5');
					catalog_SOCCER.LinkAliasToQuestion({ProvidersCollection.BetFair.Id},'OVER_UNDER_125_CORNR',question);
					background = catalog_SOCCER.GetOrCreateBackground('images/bg_lines/soccer/over-under/over-under4.jpg');
					question.Background = background;

					Eval('questionId = ' + catalog_SOCCER.NextQuestionId() + ';');
					question = catalog_SOCCER.CreateTierOneOverUnderQuestion(questionId, 'BT59', 'Corners Over/Under 13.5');
					catalog_SOCCER.LinkAliasToQuestion({ProvidersCollection.BetFair.Id},'OVER_UNDER_135_CORNR',question);
					background = catalog_SOCCER.GetOrCreateBackground('images/bg_lines/soccer/over-under/over-under5.jpg');
					question.Background = background;

					Eval('questionId = ' + catalog_SOCCER.NextQuestionId() + ';');
					question = catalog_SOCCER.CreateTierOneOverUnderQuestion(questionId, 'BT60', 'Corners Over/Under 14.5');
					catalog_SOCCER.LinkAliasToQuestion({ProvidersCollection.BetFair.Id},'OVER_UNDER_145_CORNR',question);
					background = catalog_SOCCER.GetOrCreateBackground('images/bg_lines/soccer/over-under/over-under1.jpg');
					question.Background = background;

					Eval('questionId = ' + catalog_SOCCER.NextQuestionId() + ';');
					question = catalog_SOCCER.CreateTierOneOverUnderQuestion(questionId, 'BT61', 'Corners Over/Under 15.5');
					catalog_SOCCER.LinkAliasToQuestion({ProvidersCollection.BetFair.Id},'OVER_UNDER_155_CORNR',question);
					background = catalog_SOCCER.GetOrCreateBackground('images/bg_lines/soccer/over-under/over-under2.jpg');
					question.Background = background;

					Eval('questionId = ' + catalog_SOCCER.NextQuestionId() + ';');
					question = catalog_SOCCER.CreateTierOneOverUnderQuestion(questionId, 'BT62', 'Corners Over/Under 16.5');
					catalog_SOCCER.LinkAliasToQuestion({ProvidersCollection.BetFair.Id},'OVER_UNDER_165_CORNR',question);
					background = catalog_SOCCER.GetOrCreateBackground('images/bg_lines/soccer/over-under/over-under3.jpg');
					question.Background = background;

					Eval('questionId = ' + catalog_SOCCER.NextQuestionId() + ';');
					question = catalog_SOCCER.CreateTierOneOverUnderQuestion(questionId, 'BT63', 'Corners Over/Under 17.5');
					catalog_SOCCER.LinkAliasToQuestion({ProvidersCollection.BetFair.Id},'OVER_UNDER_175_CORNR',question);
					background = catalog_SOCCER.GetOrCreateBackground('images/bg_lines/soccer/over-under/over-under4.jpg');
					question.Background = background;

					Eval('questionId = ' + catalog_SOCCER.NextQuestionId() + ';');
					question = catalog_SOCCER.CreateTierOneOverUnderQuestion(questionId, 'BT64', 'Corners Over/Under 18.5');
					catalog_SOCCER.LinkAliasToQuestion({ProvidersCollection.BetFair.Id},'OVER_UNDER_185_CORNR',question);
					background = catalog_SOCCER.GetOrCreateBackground('images/bg_lines/soccer/over-under/over-under5.jpg');
					question.Background = background;

					Eval('questionId = ' + catalog_SOCCER.NextQuestionId() + ';');
					question = catalog_SOCCER.CreateTierOneOverUnderQuestion(questionId, 'BT65', 'Corners Over/Under 19.5');
					catalog_SOCCER.LinkAliasToQuestion({ProvidersCollection.BetFair.Id},'OVER_UNDER_195_CORNR',question);
					background = catalog_SOCCER.GetOrCreateBackground('images/bg_lines/soccer/over-under/over-under1.jpg');
					question.Background = background;
					
					Eval('questionId = ' + catalog_SOCCER.NextQuestionId() + ';');
					question = catalog_SOCCER.CreateTierOneFixedQuestion(questionId, 'BT66', 'Corners Odds');
					catalog_SOCCER.LinkAliasToQuestion({ProvidersCollection.BetFair.Id},'CORNER_ODDS',question);
					background = catalog_SOCCER.GetOrCreateBackground('images/bg_lines/soccer/fixed/fixed4.jpg');
					question.Background = background;

					Eval('questionId = ' + catalog_SOCCER.NextQuestionId() + ';');
					question = catalog_SOCCER.CreateTierTwoMoneyDrawQuestion(questionId, 'md1');
					catalog_SOCCER.LinkAliasToQuestion({ProvidersCollection.BetFair.Id}, 'HALF_TIME', question);
					background = catalog_SOCCER.GetOrCreateBackground('images/bg_lines/soccer/money_total_spread/win-black4.jpg');
					question.Background = background;
					question.AllowPublished({{{"'Pregame', 'In play', 'First half', 'Second half', 'First half extra', 'Second half extra', 'Penalties'"}}});

				");
			}
			//Football
			else if (idSport == 12)
			{
				JObject questionIds = null;
				var scriptForFootballQuestionIds = $@"questions = catalog_FOOTBALL.Questions;
					for(question:questions)
					{{
						if(question.Text == 'Spread Line' && question.Tier.Name == 'T1')
						{{
							print question.Id SpreadQuestion;
						}}
						else if(question.Text == 'Money Line' && question.Tier.Name == 'T1')
						{{
							print question.Id MoneyQuestion;
						}}
						else if(question.Text == 'Total Points Line' && question.Tier.Name == 'T1')
						{{
							print question.Id TotalPointsQuestion;
						}}
					}}";
				if (!existT1QuestionsForFootball)
				{
					questionIds = JObject.Parse(Perform(@$"sport_FOTBALL = tournaments.Sports.FindById(12);
							   catalog_FOOTBALL = tournaments.Catalog(sport_FOTBALL);
							   questionId = catalog_FOOTBALL.NextQuestionId();
							   question = catalog_FOOTBALL.CreateTierOneMoneyQuestion(questionId, 'mm');
							   background = catalog_FOOTBALL.GetOrCreateBackground('images/bg_lines/basketball/money_total_spread/win-black1.jpg');
							   question.Background = background;
							   questionId = catalog_FOOTBALL.NextQuestionId();
							   question = catalog_FOOTBALL.CreateTierOneSpreadQuestion(questionId, 'ss');
							   background = catalog_FOOTBALL.GetOrCreateBackground('images/bg_lines/basketball/money_total_spread/win-black3.jpg');
							   question.Background = background;
							   questionId = catalog_FOOTBALL.NextQuestionId();
							   question = catalog_FOOTBALL.CreateTierOneTotalPointsQuestion(questionId, 'tt');
							   background = catalog_FOOTBALL.GetOrCreateBackground('images/bg_lines/basketball/money_total_spread/win-black4.jpg');
							   question.Background = background;
							   {scriptForFootballQuestionIds}"
					   ));

					existT1QuestionsForFootball = true;
				}
				else
				{
					questionIds = JObject.Parse(Perform($"{scriptForFootballQuestionIds}"
					   ));
				}

				Perform($@"
					question = catalog_FOOTBALL.FindQuestionById({Convert.ToInt32(questionIds["question"][1]["MoneyQuestion"])});
					catalog_FOOTBALL.LinkAliasToQuestion({ProvidersCollection.BetFair.Id},'MONEY_LINE',question);
					background = catalog_FOOTBALL.GetOrCreateBackground('images/bg_lines/football/money_total_spread/win-black1.jpg');
					question.Background = background;

					question = catalog_FOOTBALL.FindQuestionById({Convert.ToInt32(questionIds["question"][1]["MoneyQuestion"])});
					catalog_FOOTBALL.LinkAliasToQuestion({ProvidersCollection.BetFair.Id}, 'MATCH_ODDS2', 'MATCH_ODDS', question);
					background = catalog_FOOTBALL.GetOrCreateBackground('images/bg_lines/football/money_total_spread/win-black2.jpg');
					question.Background = background;

					question = catalog_FOOTBALL.FindQuestionById({Convert.ToInt32(questionIds["question"][1]["MoneyQuestion"])});
					catalog_FOOTBALL.LinkAliasToQuestion({ProvidersCollection.BetFair.Id}, 'MATCH_ODDS3', 'MATCH_ODDS', question);
					background = catalog_FOOTBALL.GetOrCreateBackground('images/bg_lines/football/money_total_spread/win-black3.jpg');
					question.Background = background;

					question = catalog_FOOTBALL.FindQuestionById({Convert.ToInt32(questionIds["question"][1]["MoneyQuestion"])});
					catalog_FOOTBALL.LinkAliasToQuestion({ProvidersCollection.BetFair.Id},'MATCH_BET',question);
					background = catalog_FOOTBALL.GetOrCreateBackground('images/bg_lines/football/money_total_spread/win-black6.jpg');
					question.Background = background;

					Eval('questionId = ' + catalog_FOOTBALL.NextQuestionId() + ';');
					question = catalog_FOOTBALL.CreateTierTwoMoneyQuestion(questionId, 'mq1');
					catalog_FOOTBALL.LinkAliasToQuestion({ProvidersCollection.BetFair.Id},'HALF_MATCH_ODDS',question);
					background = catalog_FOOTBALL.GetOrCreateBackground('images/bg_lines/football/money_total_spread/win-black7.jpg');
					question.Background = background;
					question.AllowPublished({{{"'Pregame', 'In play', 'First quarter', 'Second quarter', 'Third quarter', 'Fourth quarter', 'Overtime 1'"}}});

					question = catalog_FOOTBALL.FindQuestionById({Convert.ToInt32(questionIds["question"][2]["TotalPointsQuestion"])});
					catalog_FOOTBALL.LinkAliasToQuestion({ProvidersCollection.BetFair.Id},'TOTAL_MATCH_POINTS',question);
					background = catalog_FOOTBALL.GetOrCreateBackground('images/bg_lines/football/money_total_spread/win-black8.jpg');
					question.Background = background;

					Eval('questionId = ' + catalog_FOOTBALL.NextQuestionId() + ';');
					question = catalog_FOOTBALL.CreateTierTwoYesNoQuestion(questionId, 'BT01', 'Both Teams to Score');
					catalog_FOOTBALL.LinkAliasToQuestion({ProvidersCollection.BetFair.Id},'BOTH_TEAMS_TO_SCORE',question);
					background = catalog_FOOTBALL.GetOrCreateBackground('images/bg_lines/football/yes-no/yes-no1.jpg');
					question.Background = background;

					Eval('questionId = ' + catalog_FOOTBALL.NextQuestionId() + ';');
					question = catalog_FOOTBALL.CreateTierOneOverUnderQuestion(questionId, 'BT03', 'Over/Under 0.5 Goals');
					catalog_FOOTBALL.LinkAliasToQuestion({ProvidersCollection.BetFair.Id},'OVER_UNDER_05',question);
					background = catalog_FOOTBALL.GetOrCreateBackground('images/bg_lines/football/over-under/over-under1.jpg');
					question.Background = background;

					Eval('questionId = ' + catalog_FOOTBALL.NextQuestionId() + ';');
					question = catalog_FOOTBALL.CreateTierOneOverUnderQuestion(questionId, 'BT06', 'Over/Under 1.5 Goals');
					catalog_FOOTBALL.LinkAliasToQuestion({ProvidersCollection.BetFair.Id},'OVER_UNDER_15',question);
					background = catalog_FOOTBALL.GetOrCreateBackground('images/bg_lines/football/over-under/over-under2.jpg');
					question.Background = background;

					Eval('questionId = ' + catalog_FOOTBALL.NextQuestionId() + ';');
					question = catalog_FOOTBALL.CreateTierOneOverUnderQuestion(questionId, 'BT07', 'Over/Under 2.5 Goals');
					catalog_FOOTBALL.LinkAliasToQuestion({ProvidersCollection.BetFair.Id},'OVER_UNDER_25',question);
					background = catalog_FOOTBALL.GetOrCreateBackground('images/bg_lines/football/over-under/over-under3.jpg');
					question.Background = background;

					Eval('questionId = ' + catalog_FOOTBALL.NextQuestionId() + ';');
					question = catalog_FOOTBALL.CreateTierOneOverUnderQuestion(questionId, 'BT08', 'Over/Under 3.5 Goals');
					catalog_FOOTBALL.LinkAliasToQuestion({ProvidersCollection.BetFair.Id},'OVER_UNDER_35',question);
					background = catalog_FOOTBALL.GetOrCreateBackground('images/bg_lines/football/over-under/over-under4.jpg');
					question.Background = background;

					Eval('questionId = ' + catalog_FOOTBALL.NextQuestionId() + ';');
					question = catalog_FOOTBALL.CreateTierOneOverUnderQuestion(questionId, 'BT09', 'Over/Under 4.5 Goals');
					catalog_FOOTBALL.LinkAliasToQuestion({ProvidersCollection.BetFair.Id},'OVER_UNDER_45',question);
					background = catalog_FOOTBALL.GetOrCreateBackground('images/bg_lines/football/over-under/over-under5.jpg');
					question.Background = background;

					Eval('questionId = ' + catalog_FOOTBALL.NextQuestionId() + ';');
					question = catalog_FOOTBALL.CreateTierOneOverUnderQuestion(questionId, 'BT10', 'Over/Under 5.5 Goals');
					catalog_FOOTBALL.LinkAliasToQuestion({ProvidersCollection.BetFair.Id},'OVER_UNDER_55',question);
					background = catalog_FOOTBALL.GetOrCreateBackground('images/bg_lines/football/over-under/over-under6.jpg');
					question.Background = background;

					Eval('questionId = ' + catalog_FOOTBALL.NextQuestionId() + ';');
					question = catalog_FOOTBALL.CreateTierOneOverUnderQuestion(questionId, 'BT11', 'Over/Under 6.5 Goals');
					catalog_FOOTBALL.LinkAliasToQuestion({ProvidersCollection.BetFair.Id},'OVER_UNDER_65',question);
					background = catalog_FOOTBALL.GetOrCreateBackground('images/bg_lines/football/over-under/over-under1.jpg');
					question.Background = background;

					Eval('questionId = ' + catalog_FOOTBALL.NextQuestionId() + ';');
					question = catalog_FOOTBALL.CreateTierOneOverUnderQuestion(questionId, 'BT12', 'Over/Under 7.5 Goals');
					catalog_FOOTBALL.LinkAliasToQuestion({ProvidersCollection.BetFair.Id},'OVER_UNDER_75',question);
					background = catalog_FOOTBALL.GetOrCreateBackground('images/bg_lines/football/over-under/over-under2.jpg');
					question.Background = background;

					Eval('questionId = ' + catalog_FOOTBALL.NextQuestionId() + ';');
					question = catalog_FOOTBALL.CreateTierOneOverUnderQuestion(questionId, 'BT13', 'Over/Under 8.5 Goals');
					catalog_FOOTBALL.LinkAliasToQuestion({ProvidersCollection.BetFair.Id},'OVER_UNDER_85',question);
					background = catalog_FOOTBALL.GetOrCreateBackground('images/bg_lines/football/over-under/over-under3.jpg');
					question.Background = background;

					Eval('questionId = ' + catalog_FOOTBALL.NextQuestionId() + ';');
					question = catalog_FOOTBALL.CreateTierOneFixedQuestion(questionId, 'BT15', 'Double Chance');
					catalog_FOOTBALL.LinkAliasToQuestion({ProvidersCollection.BetFair.Id},'DOUBLE_CHANCE',question);
					background = catalog_FOOTBALL.GetOrCreateBackground('images/bg_lines/football/fixed/fixed1.jpg');
					question.Background = background;

					Eval('questionId = ' + catalog_FOOTBALL.NextQuestionId() + ';');
					question = catalog_FOOTBALL.CreateTierOneOverUnderQuestion(questionId, 'BT16', 'First Half Goals 1.5');
					catalog_FOOTBALL.LinkAliasToQuestion({ProvidersCollection.BetFair.Id},'FIRST_HALF_GOALS_15',question);
					background = catalog_FOOTBALL.GetOrCreateBackground('images/bg_lines/football/over-under/over-under4.jpg');
					question.Background = background;

					Eval('questionId = ' + catalog_FOOTBALL.NextQuestionId() + ';');
					question = catalog_FOOTBALL.CreateTierOneOverUnderQuestion(questionId, 'BT17', 'First Half Goals 0.5');
					catalog_FOOTBALL.LinkAliasToQuestion({ProvidersCollection.BetFair.Id},'FIRST_HALF_GOALS_05',question);
					background = catalog_FOOTBALL.GetOrCreateBackground('images/bg_lines/football/over-under/over-under5.jpg');
					question.Background = background;

					Eval('questionId = ' + catalog_FOOTBALL.NextQuestionId() + ';');
					question = catalog_FOOTBALL.CreateTierOneOverUnderQuestion(questionId, 'BT18', 'First Half Goals 2.5');
					catalog_FOOTBALL.LinkAliasToQuestion({ProvidersCollection.BetFair.Id},'FIRST_HALF_GOALS_25',question);
					background = catalog_FOOTBALL.GetOrCreateBackground('images/bg_lines/football/over-under/over-under6.jpg');
					question.Background = background;

					Eval('questionId = ' + catalog_FOOTBALL.NextQuestionId() + ';');
					question = catalog_FOOTBALL.CreateTierOneFixedQuestion(questionId, 'BT19', 'Correct Score');
					catalog_FOOTBALL.LinkAliasToQuestion({ProvidersCollection.BetFair.Id},'CORRECT_SCORE',question);
					background = catalog_FOOTBALL.GetOrCreateBackground('images/bg_lines/football/fixed/fixed2.jpg');
					question.Background = background;

					Eval('questionId = ' + catalog_FOOTBALL.NextQuestionId() + ';');
					question = catalog_FOOTBALL.CreateTierOneFixedQuestion(questionId, 'BT22', 'Total Goals Odd/Even');
					catalog_FOOTBALL.LinkAliasToQuestion({ProvidersCollection.BetFair.Id},'ODD_OR_EVEN',question);
					background = catalog_FOOTBALL.GetOrCreateBackground('images/bg_lines/football/fixed/fixed3.jpg');
					question.Background = background;

					Eval('questionId = ' + catalog_FOOTBALL.NextQuestionId() + ';');
					question = catalog_FOOTBALL.CreateTierOneFixedQuestion(questionId, 'BT23', 'Time Of First Goal');
					catalog_FOOTBALL.LinkAliasToQuestion({ProvidersCollection.BetFair.Id},'FIRST_GOAL_ODDS',question);
					background = catalog_FOOTBALL.GetOrCreateBackground('images/bg_lines/football/fixed/fixed4.jpg');
					question.Background = background;

					Eval('questionId = ' + catalog_FOOTBALL.NextQuestionId() + ';');
					question = catalog_FOOTBALL.CreateTierOneFixedQuestion(questionId, 'BT24', 'Correct Score 2 Home');
					catalog_FOOTBALL.LinkAliasToQuestion({ProvidersCollection.BetFair.Id},'CORRECT_SCORE2_2', 'CORRECT_SCORE', question);
					background = catalog_FOOTBALL.GetOrCreateBackground('images/bg_lines/football/fixed/fixed5.jpg');
					question.Background = background;

					Eval('questionId = ' + catalog_FOOTBALL.NextQuestionId() + ';');
					question = catalog_FOOTBALL.CreateTierOneFixedQuestion(questionId, 'BT25', 'Correct Score 2 Away');
					catalog_FOOTBALL.LinkAliasToQuestion({ProvidersCollection.BetFair.Id}, 'CORRECT_SCORE2_3', 'CORRECT_SCORE', question);
					background = catalog_FOOTBALL.GetOrCreateBackground('images/bg_lines/football/fixed/fixed6.jpg');
					question.Background = background;

					Eval('questionId = ' + catalog_FOOTBALL.NextQuestionId() + ';');
					question = catalog_FOOTBALL.CreateTierOneYesNoQuestion(questionId, 'BT26', 'Penalty Taken?');
					catalog_FOOTBALL.LinkAliasToQuestion({ProvidersCollection.BetFair.Id},'PENALTY_TAKEN',question);
					background = catalog_FOOTBALL.GetOrCreateBackground('images/bg_lines/football/yes-no/yes-no3.jpg');
					question.Background = background;

					Eval('questionId = ' + catalog_FOOTBALL.NextQuestionId() + ';');
					question = catalog_FOOTBALL.CreateTierOneYesNoQuestion(questionId, 'BT27', 'Sending Off?');
					catalog_FOOTBALL.LinkAliasToQuestion({ProvidersCollection.BetFair.Id},'SENDING_OFF',question);
					background = catalog_FOOTBALL.GetOrCreateBackground('images/bg_lines/football/yes-no/yes-no4.jpg');
					question.Background = background;

					Eval('questionId = ' + catalog_FOOTBALL.NextQuestionId() + ';');
					question = catalog_FOOTBALL.CreateTierOneFixedQuestion(questionId, 'BT28', 'To Qualify');
					catalog_FOOTBALL.LinkAliasToQuestion({ProvidersCollection.BetFair.Id},'TO_QUALIFY',question);
					background = catalog_FOOTBALL.GetOrCreateBackground('images/bg_lines/football/fixed/fixed3.jpg');
					question.Background = background;

					Eval('questionId = ' + catalog_FOOTBALL.NextQuestionId() + ';');
					question = catalog_FOOTBALL.CreateTierOneFixedQuestion(questionId, 'BT38', 'To Score');
					catalog_FOOTBALL.LinkAliasToQuestion({ProvidersCollection.BetFair.Id},'TO_SCORE',question);
					background = catalog_FOOTBALL.GetOrCreateBackground('images/bg_lines/football/fixed/fixed1.jpg');
					question.Background = background;

					Eval('questionId = ' + catalog_FOOTBALL.NextQuestionId() + ';');
					question = catalog_FOOTBALL.CreateTierOneFixedQuestion(questionId, 'BT39', 'Corners Match Bet');
					catalog_FOOTBALL.LinkAliasToQuestion({ProvidersCollection.BetFair.Id},'CORNER_MATCH_BET',question);
					background = catalog_FOOTBALL.GetOrCreateBackground('images/bg_lines/football/fixed/fixed2.jpg');
					question.Background = background;

					Eval('questionId = ' + catalog_FOOTBALL.NextQuestionId() + ';');
					question = catalog_FOOTBALL.CreateTierOneFixedQuestion(questionId, 'BT40', 'AFC East');
					catalog_FOOTBALL.LinkAliasToQuestion({ProvidersCollection.BetFair.Id}, 'SEASON_SPECIALS_AFC_E', 'SEASON_SPECIALS', question);
					background = catalog_FOOTBALL.GetOrCreateBackground('images/bg_lines/football/fixed/fixed4.jpg');
					question.Background = background;

					Eval('questionId = ' + catalog_FOOTBALL.NextQuestionId() + ';');
					question = catalog_FOOTBALL.CreateTierOneFixedQuestion(questionId, 'BT41', 'AFC North');
					catalog_FOOTBALL.LinkAliasToQuestion({ProvidersCollection.BetFair.Id}, 'SEASON_SPECIALS_AFC_N', 'SEASON_SPECIALS', question);
					background = catalog_FOOTBALL.GetOrCreateBackground('images/bg_lines/football/fixed/fixed5.jpg');
					question.Background = background;

					Eval('questionId = ' + catalog_FOOTBALL.NextQuestionId() + ';');
					question = catalog_FOOTBALL.CreateTierOneFixedQuestion(questionId, 'BT42', 'AFC South');
					catalog_FOOTBALL.LinkAliasToQuestion({ProvidersCollection.BetFair.Id}, 'SEASON_SPECIALS_AFC_S', 'SEASON_SPECIALS', question);
					background = catalog_FOOTBALL.GetOrCreateBackground('images/bg_lines/football/fixed/fixed6.jpg');
					question.Background = background;

					Eval('questionId = ' + catalog_FOOTBALL.NextQuestionId() + ';');
					question = catalog_FOOTBALL.CreateTierOneFixedQuestion(questionId, 'BT43', 'AFC West');
					catalog_FOOTBALL.LinkAliasToQuestion({ProvidersCollection.BetFair.Id}, 'SEASON_SPECIALS_AFC_W', 'SEASON_SPECIALS', question);
					background = catalog_FOOTBALL.GetOrCreateBackground('images/bg_lines/football/fixed/fixed7.jpg');
					question.Background = background;

					Eval('questionId = ' + catalog_FOOTBALL.NextQuestionId() + ';');
					question = catalog_FOOTBALL.CreateTierOneFixedQuestion(questionId, 'BT44', 'NFC East');
					catalog_FOOTBALL.LinkAliasToQuestion({ProvidersCollection.BetFair.Id}, 'SEASON_SPECIALS_NFC_E', 'SEASON_SPECIALS', question);
					background = catalog_FOOTBALL.GetOrCreateBackground('images/bg_lines/football/fixed/fixed1.jpg');
					question.Background = background;

					Eval('questionId = ' + catalog_FOOTBALL.NextQuestionId() + ';');
					question = catalog_FOOTBALL.CreateTierOneFixedQuestion(questionId, 'BT45', 'NFC North');
					catalog_FOOTBALL.LinkAliasToQuestion({ProvidersCollection.BetFair.Id}, 'SEASON_SPECIALS_NFC_N', 'SEASON_SPECIALS', question);
					background = catalog_FOOTBALL.GetOrCreateBackground('images/bg_lines/football/fixed/fixed2.jpg');
					question.Background = background;

					Eval('questionId = ' + catalog_FOOTBALL.NextQuestionId() + ';');
					question = catalog_FOOTBALL.CreateTierOneFixedQuestion(questionId, 'BT46', 'NFC South');
					catalog_FOOTBALL.LinkAliasToQuestion({ProvidersCollection.BetFair.Id}, 'SEASON_SPECIALS_NFC_S', 'SEASON_SPECIALS', question);
					background = catalog_FOOTBALL.GetOrCreateBackground('images/bg_lines/football/fixed/fixed3.jpg');
					question.Background = background;

					Eval('questionId = ' + catalog_FOOTBALL.NextQuestionId() + ';');
					question = catalog_FOOTBALL.CreateTierOneFixedQuestion(questionId, 'BT47', 'NFC West');
					catalog_FOOTBALL.LinkAliasToQuestion({ProvidersCollection.BetFair.Id}, 'SEASON_SPECIALS_NFC_W', 'SEASON_SPECIALS', question);
					background = catalog_FOOTBALL.GetOrCreateBackground('images/bg_lines/football/fixed/fixed4.jpg');
					question.Background = background;

					Eval('questionId = ' + catalog_FOOTBALL.NextQuestionId() + ';');
					question = catalog_FOOTBALL.CreateTierOneFixedQuestion(questionId, 'BT48', 'AFC Conference Winner');
					catalog_FOOTBALL.LinkAliasToQuestion({ProvidersCollection.BetFair.Id}, 'TOURNAMENT_WINNER_A', 'TOURNAMENT_WINNER', question);
					background = catalog_FOOTBALL.GetOrCreateBackground('images/bg_lines/football/fixed/fixed5.jpg');
					question.Background = background;

					Eval('questionId = ' + catalog_FOOTBALL.NextQuestionId() + ';');
					question = catalog_FOOTBALL.CreateTierOneFixedQuestion(questionId, 'BT49', 'Super Bowl Winner');
					catalog_FOOTBALL.LinkAliasToQuestion({ProvidersCollection.BetFair.Id}, 'TOURNAMENT_WINNER_B', 'TOURNAMENT_WINNER', question);
					background = catalog_FOOTBALL.GetOrCreateBackground('images/bg_lines/football/fixed/fixed6.jpg');
					question.Background = background;

					Eval('questionId = ' + catalog_FOOTBALL.NextQuestionId() + ';');
					question = catalog_FOOTBALL.CreateTierOneFixedQuestion(questionId, 'BT50', 'NFC Conference Winner');
					catalog_FOOTBALL.LinkAliasToQuestion({ProvidersCollection.BetFair.Id}, 'TOURNAMENT_WINNER_C', 'TOURNAMENT_WINNER', question);
					background = catalog_FOOTBALL.GetOrCreateBackground('images/bg_lines/football/fixed/fixed4.jpg');
					question.Background = background;

				");
			}
			//Baseball
			else if (idSport == 16)
			{
				JObject questionIds = null;
				var scriptForBaseballQuestionIds = $@"questions = catalog_BASEBALL.Questions;
					for(question:questions)
					{{
						if(question.Text == 'Spread Line' && question.Tier.Name == 'T1')
						{{
							print question.Id SpreadQuestion;
						}}
						else if(question.Text == 'Money Line' && question.Tier.Name == 'T1')
						{{
							print question.Id MoneyQuestion;
						}}
						else if(question.Text == 'Total Points Line' && question.Tier.Name == 'T1')
						{{
							print question.Id TotalPointsQuestion;
						}}
					}}";
				if (!existT1QuestionsForBaseball)
				{
					questionIds = JObject.Parse(Perform(@$"sport_BASEBALL = tournaments.Sports.FindById(16);
							catalog_BASEBALL = tournaments.Catalog(sport_BASEBALL);
							questionId = catalog_BASEBALL.NextQuestionId();
							question = catalog_BASEBALL.CreateTierOneMoneyQuestion(questionId, 'mm');
							background = catalog_BASEBALL.GetOrCreateBackground('images/bg_lines/basketball/money_total_spread/win-black1.jpg');
							question.Background = background;
							questionId = catalog_BASEBALL.NextQuestionId();
							question = catalog_BASEBALL.CreateTierOneSpreadQuestion(questionId, 'ss');
							background = catalog_BASEBALL.GetOrCreateBackground('images/bg_lines/basketball/money_total_spread/win-black3.jpg');
							question.Background = background;
							questionId = catalog_BASEBALL.NextQuestionId();
							question = catalog_BASEBALL.CreateTierOneTotalPointsQuestion(questionId, 'tt');
							background = catalog_BASEBALL.GetOrCreateBackground('images/bg_lines/basketball/money_total_spread/win-black4.jpg');
							question.Background = background;
							{scriptForBaseballQuestionIds}"
					));

					existT1QuestionsForBaseball = true;
				}
				else
				{
					questionIds = JObject.Parse(Perform($"{scriptForBaseballQuestionIds}"));
				}

				Perform($@"
					question = catalog_BASEBALL.FindQuestionById({Convert.ToInt32(questionIds["question"][0]["MoneyQuestion"])});
					catalog_BASEBALL.LinkAliasToQuestion({ProvidersCollection.BetFair.Id},'MONEY_LINE',question);
					background = catalog_BASEBALL.GetOrCreateBackground('images/bg_lines/default/money_total_spread/spread1.jpg');
					question.Background = background;

					question = catalog_BASEBALL.FindQuestionById({Convert.ToInt32(questionIds["question"][0]["MoneyQuestion"])});
					catalog_BASEBALL.LinkAliasToQuestion({ProvidersCollection.BetFair.Id}, 'MATCH_ODDS2', 'MATCH_ODDS', question);
					background = catalog_BASEBALL.GetOrCreateBackground('images/bg_lines/default/money_total_spread/win-black1.jpg');
					question.Background = background;

					question = catalog_BASEBALL.FindQuestionById({Convert.ToInt32(questionIds["question"][0]["MoneyQuestion"])});
					catalog_BASEBALL.LinkAliasToQuestion({ProvidersCollection.BetFair.Id}, 'MATCH_ODDS3', 'MATCH_ODDS', question);
					background = catalog_BASEBALL.GetOrCreateBackground('images/bg_lines/default/money_total_spread/spread1.jpg');
					question.Background = background;

					question = catalog_BASEBALL.FindQuestionById({Convert.ToInt32(questionIds["question"][0]["MoneyQuestion"])});
					catalog_BASEBALL.LinkAliasToQuestion({ProvidersCollection.BetFair.Id},'MATCH_BET',question);
					background = catalog_BASEBALL.GetOrCreateBackground('images/bg_lines/default/money_total_spread/win-black1.jpg');
					question.Background = background;

					Eval('questionId = ' + catalog_BASEBALL.NextQuestionId() + ';');
					question = catalog_BASEBALL.CreateTierTwoMoneyQuestion(questionId, 'mq1');
					catalog_BASEBALL.LinkAliasToQuestion({ProvidersCollection.BetFair.Id},'HALF_MATCH_ODDS',question);
					background = catalog_BASEBALL.GetOrCreateBackground('images/bg_lines/default/money_total_spread/spread1.jpg');
					question.Background = background;
					question.AllowPublished({{{"'Pregame','In play','First Inning','Second Inning','Third Inning','Fourth Inning','Fiveth Inning','Sixth Inning','Seventh Inning','Eighth Inning','Nineth inning','Extra inning 1','Game Over'"}}});

					question = catalog_BASEBALL.FindQuestionById({Convert.ToInt32(questionIds["question"][2]["TotalPointsQuestion"])});
					catalog_BASEBALL.LinkAliasToQuestion({ProvidersCollection.BetFair.Id},'TOTAL_MATCH_POINTS',question);
					background = catalog_BASEBALL.GetOrCreateBackground('images/bg_lines/default/money_total_spread/win-black1.jpg');
					question.Background = background;

					Eval('questionId = ' + catalog_BASEBALL.NextQuestionId() + ';');
					question = catalog_BASEBALL.CreateTierTwoYesNoQuestion(questionId, 'BT01', 'Both Teams to Score');
					catalog_BASEBALL.LinkAliasToQuestion({ProvidersCollection.BetFair.Id},'BOTH_TEAMS_TO_SCORE',question);
					background = catalog_BASEBALL.GetOrCreateBackground('images/bg_lines/default/yes-no/yes-no1.jpg');
					question.Background = background;

					Eval('questionId = ' + catalog_BASEBALL.NextQuestionId() + ';');
					question = catalog_BASEBALL.CreateTierTwoYesNoQuestion(questionId, 'BT02', 'Hat-trick Scored');
					catalog_BASEBALL.LinkAliasToQuestion({ProvidersCollection.BetFair.Id},'HAT_TRICKED_SCORED',question);
					background = catalog_BASEBALL.GetOrCreateBackground('images/bg_lines/default/yes-no/yes-no1.jpg');
					question.Background = background;

					Eval('questionId = ' + catalog_BASEBALL.NextQuestionId() + ';');
					question = catalog_BASEBALL.CreateTierOneOverUnderQuestion(questionId, 'BT09', 'Over/Under 4.5 Goals');
					catalog_BASEBALL.LinkAliasToQuestion({ProvidersCollection.BetFair.Id},'OVER_UNDER_45',question);
					background = catalog_BASEBALL.GetOrCreateBackground('images/bg_lines/default/over-under/over-under1.jpg');
					question.Background = background;

					Eval('questionId = ' + catalog_BASEBALL.NextQuestionId() + ';');
					question = catalog_BASEBALL.CreateTierOneOverUnderQuestion(questionId, 'BT10', 'Over/Under 5.5 Goals');
					catalog_BASEBALL.LinkAliasToQuestion({ProvidersCollection.BetFair.Id},'OVER_UNDER_55',question);
					background = catalog_BASEBALL.GetOrCreateBackground('images/bg_lines/default/over-under/over-under1.jpg');
					question.Background = background;

					Eval('questionId = ' + catalog_BASEBALL.NextQuestionId() + ';');
					question = catalog_BASEBALL.CreateTierOneOverUnderQuestion(questionId, 'BT11', 'Over/Under 6.5 Goals');
					catalog_BASEBALL.LinkAliasToQuestion({ProvidersCollection.BetFair.Id},'OVER_UNDER_65',question);
					background = catalog_BASEBALL.GetOrCreateBackground('images/bg_lines/default/over-under/over-under1.jpg');
					question.Background = background;

					Eval('questionId = ' + catalog_BASEBALL.NextQuestionId() + ';');
					question = catalog_BASEBALL.CreateTierOneOverUnderQuestion(questionId, 'BT12', 'Over/Under 7.5 Goals');
					catalog_BASEBALL.LinkAliasToQuestion({ProvidersCollection.BetFair.Id},'OVER_UNDER_75',question);
					background = catalog_BASEBALL.GetOrCreateBackground('images/bg_lines/default/over-under/over-under1.jpg');
					question.Background = background;

					Eval('questionId = ' + catalog_BASEBALL.NextQuestionId() + ';');
					question = catalog_BASEBALL.CreateTierOneOverUnderQuestion(questionId, 'BT13', 'Over/Under 8.5 Goals');
					catalog_BASEBALL.LinkAliasToQuestion({ProvidersCollection.BetFair.Id},'OVER_UNDER_85',question);
					background = catalog_BASEBALL.GetOrCreateBackground('images/bg_lines/default/over-under/over-under1.jpg');
					question.Background = background;

					Eval('questionId = ' + catalog_BASEBALL.NextQuestionId() + ';');
					question = catalog_BASEBALL.CreateTierOneFixedQuestion(questionId, 'BT15', 'Double Chance');
					catalog_BASEBALL.LinkAliasToQuestion({ProvidersCollection.BetFair.Id},'DOUBLE_CHANCE',question);
					background = catalog_BASEBALL.GetOrCreateBackground('images/bg_lines/default/fixed/fixed1.jpg');
					question.Background = background;

					Eval('questionId = ' + catalog_BASEBALL.NextQuestionId() + ';');
					question = catalog_BASEBALL.CreateTierOneFixedQuestion(questionId, 'BT19', 'Correct Score');
					catalog_BASEBALL.LinkAliasToQuestion({ProvidersCollection.BetFair.Id},'CORRECT_SCORE',question);
					background = catalog_BASEBALL.GetOrCreateBackground('images/bg_lines/default/fixed/fixed1.jpg');
					question.Background = background;

					Eval('questionId = ' + catalog_BASEBALL.NextQuestionId() + ';');
					question = catalog_BASEBALL.CreateTierOneFixedQuestion(questionId, 'BT22', 'Total Goals Odd/Even');
					catalog_BASEBALL.LinkAliasToQuestion({ProvidersCollection.BetFair.Id},'ODD_OR_EVEN',question);
					background = catalog_BASEBALL.GetOrCreateBackground('images/bg_lines/default/fixed/fixed1.jpg');
					question.Background = background;

					Eval('questionId = ' + catalog_BASEBALL.NextQuestionId() + ';');
					question = catalog_BASEBALL.CreateTierOneFixedQuestion(questionId, 'BT24', 'Correct Score 2 Home');
					catalog_BASEBALL.LinkAliasToQuestion({ProvidersCollection.BetFair.Id},'CORRECT_SCORE2_2', 'CORRECT_SCORE', question);
					background = catalog_BASEBALL.GetOrCreateBackground('images/bg_lines/default/fixed/fixed1.jpg');
					question.Background = background;

					Eval('questionId = ' + catalog_BASEBALL.NextQuestionId() + ';');
					question = catalog_BASEBALL.CreateTierOneFixedQuestion(questionId, 'BT25', 'Correct Score 2 Away');
					catalog_BASEBALL.LinkAliasToQuestion({ProvidersCollection.BetFair.Id}, 'CORRECT_SCORE2_3', 'CORRECT_SCORE', question);
					background = catalog_BASEBALL.GetOrCreateBackground('images/bg_lines/default/fixed/fixed1.jpg');
					question.Background = background;

				");
			}
			//BASKETBALL
			else if (idSport == 18)
			{
				JObject questionIds = null;
				if (!existT1QuestionsForBasket)
				{
					questionIds = JObject.Parse(Perform(@$"sport_BASKETBALL = tournaments.Sports.FindById(18);
							catalog_BASKETBALL = tournaments.Catalog(sport_BASKETBALL);
							questionId = catalog_BASKETBALL.NextQuestionId();
							question = catalog_BASKETBALL.CreateTierOneMoneyQuestion(questionId, 'mm');
							background = catalog_BASKETBALL.GetOrCreateBackground('images/bg_lines/basketball/money_total_spread/win-black1.jpg');
							question.Background = background;
							questionId = catalog_BASKETBALL.NextQuestionId();
							question = catalog_BASKETBALL.CreateTierOneSpreadQuestion(questionId, 'ss');
							background = catalog_BASKETBALL.GetOrCreateBackground('images/bg_lines/basketball/money_total_spread/win-black3.jpg');
							question.Background = background;
							questionId = catalog_BASKETBALL.NextQuestionId();
							question = catalog_BASKETBALL.CreateTierOneTotalPointsQuestion(questionId, 'tt');
							background = catalog_BASKETBALL.GetOrCreateBackground('images/bg_lines/basketball/money_total_spread/win-black4.jpg');
							question.Background = background;
							{scriptForPrintQuestionIds}"
					));

					existT1QuestionsForBasket = true;
				}
				else
				{
					questionIds = JObject.Parse(Perform($"{scriptForPrintQuestionIds}"));
				}

				Perform($@"
					question = catalog_BASKETBALL.FindQuestionById({Convert.ToInt32(questionIds["question"][0]["MoneyQuestion"])});
					catalog_BASKETBALL.LinkAliasToQuestion({ProvidersCollection.BetFair.Id},'MONEY_LINE',question);
					background = catalog_BASKETBALL.GetOrCreateBackground('images/bg_lines/basketball/money_total_spread/win-black1.jpg');
					question.Background = background;

					question = catalog_BASKETBALL.FindQuestionById({Convert.ToInt32(questionIds["question"][0]["MoneyQuestion"])});
					catalog_BASKETBALL.LinkAliasToQuestion({ProvidersCollection.BetFair.Id},'MATCH_BET',question);
					background = catalog_BASKETBALL.GetOrCreateBackground('images/bg_lines/basketball/money_total_spread/win-black3.jpg');
					question.Background = background;

					Eval('questionId = ' + catalog_BASKETBALL.NextQuestionId() + ';');
					question = catalog_BASKETBALL.CreateTierTwoMoneyQuestion(questionId, 'mq1');
					catalog_BASKETBALL.LinkAliasToQuestion({ProvidersCollection.BetFair.Id},'HALF_MATCH_ODDS',question);
					background = catalog_BASKETBALL.GetOrCreateBackground('images/bg_lines/basketball/money_total_spread/win-black4.jpg');
					question.Background = background;
					question.AllowPublished({{{"'Pregame', 'In play', 'First quarter', 'Second quarter', 'Third quarter','Fourth quarter', 'Overtime 1'"}}});

					question = catalog_BASKETBALL.FindQuestionById({Convert.ToInt32(questionIds["question"][2]["TotalPointsQuestion"])});
					catalog_BASKETBALL.LinkAliasToQuestion({ProvidersCollection.BetFair.Id},'TOTAL_MATCH_POINTS',question);
					background = catalog_BASKETBALL.GetOrCreateBackground('images/bg_lines/basketball/money_total_spread/win-black5.jpg');
					question.Background = background;

					Eval('questionId = ' + catalog_BASKETBALL.NextQuestionId() + ';');
					question = catalog_BASKETBALL.CreateTierTwoYesNoQuestion(questionId, 'BT01', 'Both Teams to Score');
					catalog_BASKETBALL.LinkAliasToQuestion({ProvidersCollection.BetFair.Id},'BOTH_TEAMS_TO_SCORE',question);
					background = catalog_BASKETBALL.GetOrCreateBackground('images/bg_lines/basketball/yes-no/yes-no1.jpg');
					question.Background = background;

					Eval('questionId = ' + catalog_BASKETBALL.NextQuestionId() + ';');
					question = catalog_BASKETBALL.CreateTierTwoYesNoQuestion(questionId, 'BT02', 'Hat-trick Scored');
					catalog_BASKETBALL.LinkAliasToQuestion({ProvidersCollection.BetFair.Id},'HAT_TRICKED_SCORED',question);
					background = catalog_BASKETBALL.GetOrCreateBackground('images/bg_lines/basketball/yes-no/yes-no2.jpg');
					question.Background = background;

					Eval('questionId = ' + catalog_BASKETBALL.NextQuestionId() + ';');
					question = catalog_BASKETBALL.CreateTierOneOverUnderQuestion(questionId, 'BT03', 'Over/Under 0.5 Goals');
					catalog_BASKETBALL.LinkAliasToQuestion({ProvidersCollection.BetFair.Id},'OVER_UNDER_05',question);
					background = catalog_BASKETBALL.GetOrCreateBackground('images/bg_lines/basketball/over-under/over-under1.jpg');
					question.Background = background;

					Eval('questionId = ' + catalog_BASKETBALL.NextQuestionId() + ';');
					question = catalog_BASKETBALL.CreateTierOneOverUnderQuestion(questionId, 'BT06', 'Over/Under 1.5 Goals');
					catalog_BASKETBALL.LinkAliasToQuestion({ProvidersCollection.BetFair.Id},'OVER_UNDER_15',question);
					background = catalog_BASKETBALL.GetOrCreateBackground('images/bg_lines/basketball/over-under/over-under4.jpg');
					question.Background = background;

					Eval('questionId = ' + catalog_BASKETBALL.NextQuestionId() + ';');
					question = catalog_BASKETBALL.CreateTierOneOverUnderQuestion(questionId, 'BT07', 'Over/Under 2.5 Goals');
					catalog_BASKETBALL.LinkAliasToQuestion({ProvidersCollection.BetFair.Id},'OVER_UNDER_25',question);
					background = catalog_BASKETBALL.GetOrCreateBackground('images/bg_lines/basketball/over-under/over-under5.jpg');
					question.Background = background;

					Eval('questionId = ' + catalog_BASKETBALL.NextQuestionId() + ';');
					question = catalog_BASKETBALL.CreateTierOneOverUnderQuestion(questionId, 'BT08', 'Over/Under 3.5 Goals');
					catalog_BASKETBALL.LinkAliasToQuestion({ProvidersCollection.BetFair.Id},'OVER_UNDER_35',question);
					background = catalog_BASKETBALL.GetOrCreateBackground('images/bg_lines/basketball/over-under/over-under6.jpg');
					question.Background = background;

					Eval('questionId = ' + catalog_BASKETBALL.NextQuestionId() + ';');
					question = catalog_BASKETBALL.CreateTierOneOverUnderQuestion(questionId, 'BT09', 'Over/Under 4.5 Goals');
					catalog_BASKETBALL.LinkAliasToQuestion({ProvidersCollection.BetFair.Id},'OVER_UNDER_45',question);
					background = catalog_BASKETBALL.GetOrCreateBackground('images/bg_lines/basketball/over-under/over-under1.jpg');
					question.Background = background;

					Eval('questionId = ' + catalog_BASKETBALL.NextQuestionId() + ';');
					question = catalog_BASKETBALL.CreateTierOneOverUnderQuestion(questionId, 'BT10', 'Over/Under 5.5 Goals');
					catalog_BASKETBALL.LinkAliasToQuestion({ProvidersCollection.BetFair.Id},'OVER_UNDER_55',question);
					background = catalog_BASKETBALL.GetOrCreateBackground('images/bg_lines/basketball/over-under/over-under2.jpg');
					question.Background = background;

					Eval('questionId = ' + catalog_BASKETBALL.NextQuestionId() + ';');
					question = catalog_BASKETBALL.CreateTierOneOverUnderQuestion(questionId, 'BT11', 'Over/Under 6.5 Goals');
					catalog_BASKETBALL.LinkAliasToQuestion({ProvidersCollection.BetFair.Id},'OVER_UNDER_65',question);
					background = catalog_BASKETBALL.GetOrCreateBackground('images/bg_lines/basketball/over-under/over-under3.jpg');
					question.Background = background;

					Eval('questionId = ' + catalog_BASKETBALL.NextQuestionId() + ';');
					question = catalog_BASKETBALL.CreateTierOneOverUnderQuestion(questionId, 'BT12', 'Over/Under 7.5 Goals');
					catalog_BASKETBALL.LinkAliasToQuestion({ProvidersCollection.BetFair.Id},'OVER_UNDER_75',question);
					background = catalog_BASKETBALL.GetOrCreateBackground('images/bg_lines/basketball/over-under/over-under4.jpg');
					question.Background = background;

					Eval('questionId = ' + catalog_BASKETBALL.NextQuestionId() + ';');
					question = catalog_BASKETBALL.CreateTierOneOverUnderQuestion(questionId, 'BT13', 'Over/Under 8.5 Goals');
					catalog_BASKETBALL.LinkAliasToQuestion({ProvidersCollection.BetFair.Id},'OVER_UNDER_85',question);
					background = catalog_BASKETBALL.GetOrCreateBackground('images/bg_lines/basketball/over-under/over-under5.jpg');
					question.Background = background;

					Eval('questionId = ' + catalog_BASKETBALL.NextQuestionId() + ';');
					question = catalog_BASKETBALL.CreateTierTwoMoneyQuestion(questionId, 'mq2');
					catalog_BASKETBALL.LinkAliasToQuestion({ProvidersCollection.BetFair.Id},'QUARTER_MATCH_ODDS',question);
					background = catalog_BASKETBALL.GetOrCreateBackground('images/bg_lines/basketball/money_total_spread/win-black4.jpg');
					question.Background = background;
					question.AllowPublished({{{"'Pregame', 'In play', 'First quarter', 'Second quarter', 'Third quarter','Fourth quarter', 'Overtime 1'"}}});

					Eval('questionId = ' + catalog_BASKETBALL.NextQuestionId() + ';');
					question = catalog_BASKETBALL.CreateTierOneFixedQuestion(questionId, 'BT15', 'Women\'s Team Basketball - To Win a Medal');
					catalog_BASKETBALL.LinkAliasToQuestion({ProvidersCollection.BetFair.Id}, 'UNUSED_1', 'UNUSED', question);
					background = catalog_BASKETBALL.GetOrCreateBackground('images/bg_lines/basketball/fixed/fixed1.jpg');
					question.Background = background;

					Eval('questionId = ' + catalog_BASKETBALL.NextQuestionId() + ';');
					question = catalog_BASKETBALL.CreateTierOneFixedQuestion(questionId, 'BT16', 'Men\'s Team Basketball - To Win a Medal');
					catalog_BASKETBALL.LinkAliasToQuestion({ProvidersCollection.BetFair.Id}, 'UNUSED_2', 'UNUSED', question);
					background = catalog_BASKETBALL.GetOrCreateBackground('images/bg_lines/basketball/fixed/fixed2.jpg');
					question.Background = background;

				");
			}
			//HOCKEY
			else if (idSport == 17)
			{
				JObject questionIds = null;
				if (!existT1QuestionsForHockey)
				{
					questionIds = JObject.Parse(Perform($"sport_HOCKEY = tournaments.Sports.FindById(17);" +
							$"catalog_HOCKEY = tournaments.Catalog(sport_HOCKEY);" +
							$"questionId = sport_HOCKEY.NextQuestionId();" +
							$"print questionId MoneyQuestion;" +
							$"question = sport_HOCKEY.CreateTierOneMoneyQuestion(questionId, 'mm');" +
							"background = sport_HOCKEY.GetOrCreateBackground('images/bg_lines/basketball/money_total_spread/win-black1.jpg');" +
							"question.Background = background;" +
							$"questionId = sport_HOCKEY.NextQuestionId();" +
							$"print questionId SpreadQuestion;" +
							$"question = sport_HOCKEY.CreateTierOneSpreadQuestion(questionId, 'ss');" +
							"background = sport_HOCKEY.GetOrCreateBackground('images/bg_lines/basketball/money_total_spread/win-black3.jpg');" +
							"question.Background = background;" +
							$"questionId = sport_HOCKEY.NextQuestionId();" +
							$"print questionId TotalPointsQuestion;" +
							$"question = sport_HOCKEY.CreateTierOneTotalPointsQuestion(questionId, 'tt');" +
							"background = sport_HOCKEY.GetOrCreateBackground('images/bg_lines/basketball/money_total_spread/win-black4.jpg');" +
							"question.Background = background;"
					));

					existT1QuestionsForHockey = true;
				}

				Perform($@"
					question = catalog_HOCKEY.FindQuestionById({Convert.ToInt32(questionIds["MoneyQuestion"])});
					catalog_HOCKEY.LinkAliasToQuestion({ProvidersCollection.BetFair.Id},'MONEY_LINE',question);
					background = catalog_HOCKEY.GetOrCreateBackground('images/bg_lines/hockey/money_total_spread/spread1.jpg');
					question.Background = background;

				");
			}

			return this;
		}

		int identificationDocumentNumber = 0;

		internal LinesMockGenerator CreatePlayers(Dictionary<string, string> accountsById)
		{
			StringBuilder linesToCreatePlayer = new StringBuilder();
			int amountOfUsers = 0;
			foreach (var accountById in accountsById)
			{
				identificationDocumentNumber += 1;
				amountOfUsers++;
				string accountNumber = accountById.Key;
				string nameOfVariable = accountById.Value;
				string defaultNickNameIfNotExists = "TesterUser " + amountOfUsers;
				string defaultAvatarIfNotExists = "https://intranet.ncubo.com/ResourcesQA5/api/file/IDDefault/Lotto/Lotto/avatars/avatar2.png";
				linesToCreatePlayer.Append($@"
					custNO{accountNumber} = company.GetOrCreateCustomerById('NO{accountNumber}');
					custNO{accountNumber}.Identifier='10{identificationDocumentNumber}';
					player = custNO{accountNumber}.Player;
					Eval ('playerNO' + {accountNumber} + ' = player;');
					{{
						thePlayerItsNew = custNO{accountNumber}.ItsNewOne();
						If (thePlayerItsNew)
						{{
							custNO{accountNumber}.ReplicateInOtherNodes(itIsThePresent, true, Now, '{defaultNickNameIfNotExists}', '{defaultAvatarIfNotExists}');
						}}
						Else
						{{
							custNO{accountNumber}.AddVisit(itIsThePresent, now);
						}}
					}}

				");
				if (!string.IsNullOrEmpty(nameOfVariable)) linesToCreatePlayer.Append($@"playerNO{accountNumber} = player;");
			}

			string linesToAssignTag = @"tag = book.RiskTags.CreateTag('Red', 'http://www.tags.com/2D9CE2');
										player = company.GetOrCreateCustomerById('NO{accountNumber}').Player;
										book.RiskTags.Tag(player, tag);";

			var result = Perform(linesToCreatePlayer.ToString());
			Perform(linesToAssignTag);
			return this;
		}

		internal LinesMockGenerator CreateLeagueMappings(int sportId, string localAlias, string shortName, int providerId, string foreingId, string foreingAlias)
		{
			var result = Perform($@"
				sport = company.Tournaments.Sports.FindById({sportId});
				Eval('id = ' + company.Tournaments.Leagues.NextLeagueId + ';');
				league = company.Tournaments.Leagues.Create(sport, '{localAlias}', '{shortName}', id);
				league.Aliases.Add(ForeingAlias({providerId}, '{foreingId}', '{foreingAlias}'));
				
				print league.Id leagueId;
			");
			return this;
		}

		internal LinesMockGenerator CreateLeague(int sportId, string localAlias, string shortName, int providerId, string foreingId, string foreingAlias)
		{
			var result = Perform($@"
				sport = company.Tournaments.Sports.FindById({sportId});
				Eval('id = ' + company.Tournaments.Leagues.NextLeagueId + ';');
				league = company.Tournaments.Leagues.Create(sport, '{localAlias}', '{shortName}', id);
				league.Aliases.Add(ForeingAlias({providerId}, '{foreingId}', '{foreingAlias}'));
				
				print league.Id leagueId;
			");
			return this;
		}


		internal LinesMockGenerator CreateSeasonMappings(int premierLeagueId, string seasonName)
		{
			var result = Perform($@"
				league = company.Tournaments.Leagues.FindById({premierLeagueId});
					
				Eval('id = ' + company.Tournaments.NextTournamentId + ';');
				genericTournament = company.Tournaments.CreateTournament(league, id, '{seasonName}');
				print genericTournament.Id tournamentId;
			");
			return this;
		}
		internal LinesMockGenerator CreateLineMapping()
		{
			var result = Perform($@"
				questionId = catalog.NextQuestionId();
				overUnderQuestion = catalog.CreateTierOneOverUnderQuestion(questionId, 'OVER_UNDER_75', 'Over/Under 7.5 Goals');
				overUnderQuestion.SetParameters(2, -101.0, 50.0);
				lineId = betBoard.NextLineId();
				shelve.CreateLine(lineId, overUnderQuestion, 'N/A', Now);
			");
			return this;
		}

		internal LinesMockGenerator CreateTeamsMappings(int leagueId)
		{

			string json;
			using (StreamReader r = new StreamReader("Jsons/team.json"))
			{
				json = r.ReadToEnd();
			}

			dynamic teamsResponse = Newtonsoft.Json.JsonConvert.DeserializeObject(json);
			dynamic results = teamsResponse["results"];

			foreach (dynamic team in results)
			{
				var result = Perform($@"
				league = company.Tournaments.Leagues.FindById({leagueId});
				Eval('teamId =' + company.Tournaments.NextTeamId() + ';');
				team = company.Tournaments.CreateTeam(league, teamId, '{team["name"]}');
				team.Aliases.Add(ForeingAlias(0, '{team["id"]}', '{team["name"]}'));
			");
			}

			return this;
		}

		private const string scriptForPrintQuestionIds = @"questions = catalog_BASKETBALL.Questions;
					for(question:questions)
					{{
						if(question.Text == 'Spread Line' && question.Tier.Name == 'T1')
						{{
							print question.Id SpreadQuestion;
						}}
						else if(question.Text == 'Money Line' && question.Tier.Name == 'T1')
						{{
							print question.Id MoneyQuestion;
						}}
						else if(question.Text == 'Total Points Line' && question.Tier.Name == 'T1')
						{{
							print question.Id TotalPointsQuestion;
						}}
					}}";

		internal LinesMockGenerator CreateLine(int tournamentId, int gameNumber, QuestionType lineType, bool publish)
		{
			bool itIsThePresent = false;
			Random rnd = new Random();
			var now = DateTime.Now;
			int teamAReward = rnd.Next(101, 199);
			int teamBReward = rnd.Next(-160, -101);
			int tieReward = rnd.Next(101, 150);
			int overReward = rnd.Next(101, 160);
			int underReward = rnd.Next(-180, -101);
			int spread = rnd.Next(1, 9999);
			decimal score = 2.00m;
			int noReward = rnd.Next(-140, -101);
			int yesReward = rnd.Next(101, 130);
			string scriptForLineCreation = string.Empty;
			var dayAsText = $"{now.Month}/{now.Day}/{now.Year}";
			int fila1 = 0;

			JObject questionIds = null;

			if (!existT1QuestionsForBasket)
			{
				questionIds = JObject.Parse(Perform($@"questionId = catalog_BASKETBALL.NextQuestionId();
						question = catalog_BASKETBALL.CreateTierOneMoneyQuestion(questionId, 'mm');
						background = catalog_BASKETBALL.GetOrCreateBackground('images/bg_lines/basketball/money_total_spread/win-black1.jpg');
						question.Background = background;
						questionId = catalog_BASKETBALL.NextQuestionId();
						question = catalog_BASKETBALL.CreateTierOneMoneyDrawQuestion(questionId, 'dd');
						background = catalog_BASKETBALL.GetOrCreateBackground('images/bg_lines/basketball/money_total_spread/win-black2.jpg');
						question.Background = background;
						questionId = catalog_BASKETBALL.NextQuestionId();
						question = catalog_BASKETBALL.CreateTierOneSpreadQuestion(questionId, 'ss');
						background = catalog_BASKETBALL.GetOrCreateBackground('images/bg_lines/basketball/money_total_spread/win-black3.jpg');
						question.Background = background;
						questionId = catalog_BASKETBALL.NextQuestionId();
						question = catalog_BASKETBALL.CreateTierOneTotalPointsQuestion(questionId, 'tt');
						background = catalog_BASKETBALL.GetOrCreateBackground('images/bg_lines/basketball/money_total_spread/win-black4.jpg');
						question.Background = background;
						{scriptForPrintQuestionIds}
						"
				));

				existT1QuestionsForBasket = true;
			}
			else
			{
				questionIds = JObject.Parse(Perform($"{scriptForPrintQuestionIds}"
				));
			}

			switch (lineType)
			{
				case QuestionType.MoneyQuestion:
					scriptForLineCreation =
						$"question = catalog_BASKETBALL.FindQuestionById({Convert.ToInt32(questionIds["question"][0]["MoneyQuestion"])});" +
						$"question.SetParameters({teamAReward}, {teamBReward});" +
						$"newLine = shelve.CreateLine(lineId, question, 'c' +questionId , Now);" +
						$"if ({publish})" +
						$"{{ " +
							$"newLine.Publish({itIsThePresent}, Now);" +
						$"}}";
					break;
				case QuestionType.SpreadQuestion:
					scriptForLineCreation =
						$"question = catalog_BASKETBALL.FindQuestionById({Convert.ToInt32(questionIds["question"][1]["SpreadQuestion"])});" +
						$"question.SetParameters({spread}, {teamAReward}, {teamBReward});" +
						$"newLine = shelve.CreateLine(lineId, question, 'c' +questionId, Now);" +
						$"if ({publish})" +
						$"{{ " +
							$"newLine.Publish({itIsThePresent},Now);" +
						$"}}";
					break;
				case QuestionType.TotalPointsQuestion:
					scriptForLineCreation =
						$"question = catalog_BASKETBALL.FindQuestionById({Convert.ToInt32(questionIds["question"][2]["TotalPointsQuestion"])});" +
						$"question.SetParameters({score}, {overReward}, {underReward});" +
						$"newLine = shelve.CreateLine(lineId, question, 'c' +questionId, Now);" +
						$"if ({publish})" +
						$"{{ " +
							$"newLine.Publish({itIsThePresent},Now);" +
						$"}}";
					break;
				case QuestionType.YesNoQuestion:
					scriptForLineCreation = $"questionId = catalog_BASKETBALL.NextQuestionId();" +
						$"question = catalog_BASKETBALL.CreateTierTwoYesNoQuestion(questionId, 'WW'+questionId, 'Team A will score in the first minute {rnd.Next(1, 130)}');" +
						"background = catalog_BASKETBALL.GetOrCreateBackground('images/bg_lines/basketball/yes-no/yes-no1.jpg');" +
						"question.Background = background;" +
						$"question.SetParameters({yesReward}, {noReward});" +
						$"newLine = shelve.CreateLine(lineId, question, 'ww' +questionId, Now);" +
						$"if ({publish})" +
						$"{{ " +
							$"newLine.AllowToPublishOn(game.CurrentPeriod);" +
							$"newLine.Publish({itIsThePresent},Now);" +
						$"}}";
					break;
				case QuestionType.OverUnderQuestion:
					scriptForLineCreation = $"questionId = catalog_BASKETBALL.NextQuestionId();" +
						$"question = catalog_BASKETBALL.CreateTierTwoOverUnderQuestion(questionId, 'CF'+questionId, 'Game Total {rnd.Next(1, 130)}');" +
						"background = catalog_BASKETBALL.GetOrCreateBackground('images/bg_lines/basketball/over-under/over-under1.jpg');" +
						"question.Background = background;" +
						$"question.SetParameters({score}, {overReward}, {underReward});" +
						$"newLine = shelve.CreateLine(lineId, question, 'cf' +questionId, Now);" +
						$"if ({publish})" +
						$"{{ " +
							$"newLine.AllowToPublishOn(game.CurrentPeriod);" +
							$"newLine.Publish({itIsThePresent},Now);" +
						$"}}";
					break;
				case QuestionType.FixedQuestion:
					var optionsandRewards = new List<OptionBody> { new OptionBody { Option = "John", Reward = -125 }, new OptionBody { Option = "Peter", Reward = 114 }, new OptionBody { Option = "Carl", Reward = 110 } };
					var options = string.Join(",", optionsandRewards.Select(option => $"'{option.Option}'").ToArray());
					var rewards = string.Join(",", optionsandRewards.Select(option => $"{option.Reward}").ToArray());
					scriptForLineCreation = $"questionId = catalog_BASKETBALL.NextQuestionId();" +
					$"question = catalog_BASKETBALL.CreateTierTwoFixedQuestion(questionId, 'fx+questionId', 'Which player scores the most goals');" +
					"background = catalog_BASKETBALL.GetOrCreateBackground('images/bg_lines/basketball/fixed/fixed1.jpg');" +
					"question.Background = background;" +
					$"question.SetParameters({{{options}}}, {{{rewards}}});" +
					$"newLine = shelve.CreateLine(lineId, question, 'fx' +questionId, Now);" +
					$"if ({publish})" +
					$"{{ " +
						$"newLine.AllowToPublishOn(game.CurrentPeriod);" +
						$"newLine.Publish({itIsThePresent},Now);" +
					$"}}";
					break;
				default:
					throw new GameEngineException("case not supported");
			}

			var scriptForDraftOrPublish = publish ? "newLine.AllowToPublishOn(game.CurrentPeriod);" +
				"newLine.Publish(Now);" : string.Empty;

			Perform($@"
				tournament = company.Tournaments.FindById({tournamentId});
				game = tournament.GetGameNumber({gameNumber});
				betBoard = company.Betboard(tournament);
				catalog_BASKETBALL = betBoard.Catalog;
				lineId = betBoard.NextLineId();
				matchDay = betBoard.Matchday({dayAsText});
				shelve = matchDay.GetShowcase(game).InsertShelve({fila1});
				{scriptForLineCreation}
			");

			return this;
		}

		internal LinesMockGenerator CreateQuestion(int tournamentId, int gameNumber, QuestionType lineType)
		{
			bool itIsThePresent = false;
			Random rnd = new Random();
			var now = DateTime.Now;
			int teamAReward = rnd.Next(101, 199);
			int teamBReward = rnd.Next(-160, -101);
			int tieReward = rnd.Next(101, 150);
			int overReward = rnd.Next(101, 160);
			int underReward = rnd.Next(-180, -101);
			int spread = rnd.Next(1, 9999);
			decimal score = 2.00m;
			int noReward = rnd.Next(-140, -101);
			int yesReward = rnd.Next(101, 130);
			string scriptForLineCreation = string.Empty;
			var dayAsText = $"{now.Month}/{now.Day}/{now.Year}";
			int fila1 = 0;

			JObject questionIds = null;
			if (!existT1QuestionsForBasket)
			{
				questionIds = JObject.Parse(Perform($@"questionId = catalog_BASKETBALL.NextQuestionId();
						question = catalog_BASKETBALL.CreateTierOneMoneyQuestion(questionId, 'mm');
						background = catalog_BASKETBALL.GetOrCreateBackground('images/bg_lines/basketball/money_total_spread/win-black1.jpg');
						question.Background = background;
						questionId = catalog_BASKETBALL.NextQuestionId();
						question = catalog_BASKETBALL.CreateTierOneMoneyDrawQuestion(questionId, 'dd');
						background = catalog_BASKETBALL.GetOrCreateBackground('images/bg_lines/basketball/money_total_spread/win-black2.jpg');
						question.Background = background;
						questionId = catalog_BASKETBALL.NextQuestionId();
						question = catalog_BASKETBALL.CreateTierOneSpreadQuestion(questionId, 'ss');
						background = catalog_BASKETBALL.GetOrCreateBackground('images/bg_lines/basketball/money_total_spread/win-black3.jpg');
						question.Background = background;
						questionId = catalog_BASKETBALL.NextQuestionId();
						question = catalog_BASKETBALL.CreateTierOneTotalPointsQuestion(questionId, 'tt');
						background = catalog_BASKETBALL.GetOrCreateBackground('images/bg_lines/basketball/money_total_spread/win-black4.jpg');
						question.Background = background;
						{scriptForPrintQuestionIds}
						"
				));

				existT1QuestionsForBasket = true;
			}
			else
			{
				questionIds = JObject.Parse(Perform($"{scriptForPrintQuestionIds}"
				));
			}

			switch (lineType)
			{
				case QuestionType.MoneyQuestion:
					scriptForLineCreation = $"question = catalog_BASKETBALL.FindQuestionById({Convert.ToInt32(questionIds["question"][0]["MoneyQuestion"])});" +
						$"question.SetParameters({teamAReward}, {teamBReward});";
					break;
				case QuestionType.SpreadQuestion:
					scriptForLineCreation = $"question = catalog_BASKETBALL.FindQuestionById({Convert.ToInt32(questionIds["question"][1]["SpreadQuestion"])});" +
						$"question.SetParameters({spread}, {teamAReward}, {teamBReward});";
					break;
				case QuestionType.TotalPointsQuestion:
					scriptForLineCreation = $"question = catalog_BASKETBALL.FindQuestionById({Convert.ToInt32(questionIds["question"][2]["TotalPointsQuestion"])});" +
						$"question.SetParameters({score}, {overReward}, {underReward});";
					break;
				case QuestionType.YesNoQuestion:
					scriptForLineCreation = $"questionId = catalog_BASKETBALL.NextQuestionId();" +
						$"question = catalog_BASKETBALL.CreateTierTwoYesNoQuestion(questionId, 'WW'+questionId, 'Team A will score in the first minute {rnd.Next(1, 130)}');" +
						"background = catalog_BASKETBALL.GetOrCreateBackground('images/bg_lines/basketball/yes-no/yes-no1.jpg');" +
						"question.Background = background;" +
						$"question.SetParameters({yesReward}, {noReward});";
					break;
				case QuestionType.OverUnderQuestion:
					scriptForLineCreation = $"questionId = catalog_BASKETBALL.NextQuestionId();" +
						$"question = catalog_BASKETBALL.CreateTierTwoOverUnderQuestion(questionId, 'CF'+questionId, 'Game Total {rnd.Next(1, 130)}');" +
						"background = catalog_BASKETBALL.GetOrCreateBackground('images/bg_lines/basketball/over-under/over-under1.jpg');" +
						"question.Background = background;" +
						$"question.SetParameters({score}, {overReward}, {underReward});";
					break;
				case QuestionType.FixedQuestion:
					var optionsandRewards = new List<OptionBody> { new OptionBody { Option = "John", Reward = -125 }, new OptionBody { Option = "Peter", Reward = 114 }, new OptionBody { Option = "Carl", Reward = 110 } };
					var options = string.Join(",", optionsandRewards.Select(option => $"'{option.Option}'").ToArray());
					var rewards = string.Join(",", optionsandRewards.Select(option => $"{option.Reward}").ToArray());
					scriptForLineCreation = $"questionId = catalog_BASKETBALL.NextQuestionId();" +
					$"question = catalog_BASKETBALL.CreateTierTwoFixedQuestion(questionId, 'fx+questionId', 'Which player scores the most goals');" +
					"background = catalog_BASKETBALL.GetOrCreateBackground('images/bg_lines/basketball/fixed/fixed1.jpg');" +
					"question.Background = background;" +
					$"question.SetParameters({{{options}}}, {{{rewards}}});";
					break;
				default:
					throw new GameEngineException("case not supported");
			}

			Perform($@"
				tournament = company.Tournaments.FindById({tournamentId});
				game = tournament.GetGameNumber({gameNumber});
				betBoard = company.Betboard(tournament);
				catalog_BASKETBALL = betBoard.Catalog;
				{scriptForLineCreation}
			");

			return this;
		}

		[DataContract(Name = "OptionBody")]
		public class OptionBody
		{
			[DataMember(Name = "option")]
			public string Option { get; set; }
			[DataMember(Name = "reward")]
			public int Reward { get; set; }
		}

		internal LinesMockGenerator UpdateSpreadLine(int gameNumber, int lineId, int order, int teamAReward, int teamBReward, int tournamentId)
		{
			Perform($@"
            {{
                tournament = company.Tournaments.FindById({tournamentId});
                game = tournament.GetGameNumber({gameNumber});
                betBoard = company.Betboard(tournament);
                scheduledDate = game.ScheduledDate.Fecha();
                matchDay = betBoard.Matchday(scheduledDate);
                shelve = matchDay.GetShowcase(game).GetShelve({order});
                newLine = shelve.CreateNewVersionForSpreadLine({lineId}, {teamAReward}, {teamBReward}, 'Employee', Now);
                print newLine.Version newVersion;
                print newLine.LineId lineId;
            }}
            ");

			return this;
		}

		internal LinesMockGenerator UpdateMoneyLine(int gameNumber, int lineId, int order, int teamAReward, int teamBReward, int tournamentId)
		{
			Perform($@"
            {{
                tournament = company.Tournaments.FindById({tournamentId});
                game = tournament.GetGameNumber({gameNumber});
                betBoard = company.Betboard(tournament);
                scheduledDate = game.ScheduledDate.Fecha();
                matchDay = betBoard.Matchday(scheduledDate);
                shelve = matchDay.GetShowcase(game).GetShelve({order});
                newLine = shelve.CreateNewVersionForMoneyLine({lineId}, {teamAReward}, {teamBReward}, 'Employee', Now);
                print newLine.Version newVersion;
                print newLine.LineId lineId;
            }}
            ");

			return this;
		}

		internal LinesMockGenerator UpdateTotalPointsLine(int gameNumber, int lineId, int order, int overReward, int underReward, int tournamentId)
		{
			Perform($@"
            {{
                tournament = company.Tournaments.FindById({tournamentId});
                game = tournament.GetGameNumber({gameNumber});
                betBoard = company.Betboard(tournament);
                scheduledDate = game.ScheduledDate.Fecha();
                matchDay = betBoard.Matchday(scheduledDate);
                shelve = matchDay.GetShowcase(game).GetShelve({order});
                newLine = shelve.CreateNewVersionForTotalPointsLine({lineId}, {overReward}, {underReward}, 'Employee', Now);
                print newLine.Version newVersion;
                print newLine.LineId lineId;
            }}
            ");

			return this;
		}

		internal LinesMockGenerator UpdateYesNoLine(int gameNumber, int lineId, int order, int yesReward, int noReward, int tournamentId)
		{
			Perform($@"
            {{
                tournament = company.Tournaments.FindById({tournamentId});
                game = tournament.GetGameNumber({gameNumber});
                betBoard = company.Betboard(tournament);
                scheduledDate = game.ScheduledDate.Fecha();
                matchDay = betBoard.Matchday(scheduledDate);
                shelve = matchDay.GetShowcase(game).GetShelve({order});
                newLine = shelve.CreateNewVersionForYesNoLine({lineId}, {yesReward}, {noReward}, 'Employee', Now);
                print newLine.Version newVersion;
                print newLine.LineId lineId;
			}}
            ");

			return this;
		}

		internal LinesMockGenerator UpdateFixedLine(int gameNumber, int lineId, int order, int yesReward, int noReward, int tournamentId)
		{
			Random random = new Random();
			var optionsandRewards = new List<OptionBody> { new OptionBody { Option = "John", Reward = random.Next(-130, 130) }, new OptionBody { Option = "Peter", Reward = random.Next(-130, 130) }, new OptionBody { Option = "Carl", Reward = random.Next(-130, 130) } };
			var options = string.Join(",", optionsandRewards.Select(option => $"'{option.Option}'").ToArray());
			var rewards = string.Join(",", optionsandRewards.Select(option => $"{option.Reward}").ToArray());
			Perform($@"
             {{
                tournament = company.Tournaments.FindById({tournamentId});
                game = tournament.GetGameNumber({gameNumber});
                betBoard = company.Betboard(tournament);
                scheduledDate = game.ScheduledDate.Fecha();
                matchDay = betBoard.Matchday(scheduledDate);
                shelve = matchDay.GetShowcase(game).GetShelve({order});
                newLine = shelve.CreateNewVersionForFixedLine({lineId}, {{{rewards}}}, 'Jonathan', Now);
                print newLine.Version newVersion;
                print newLine.LineId lineId;
			}}
            ");

			return this;
		}

		internal LinesMockGenerator UpdateOverUnderLine(int gameNumber, int lineId, int order, int overReward, int underReward, int tournamentId)
		{
			Perform($@"
            {{
                tournament = company.Tournaments.FindById({tournamentId});
                game = tournament.GetGameNumber({gameNumber});
                betBoard = company.Betboard(tournament);
                scheduledDate = game.ScheduledDate.Fecha();
                matchDay = betBoard.Matchday(scheduledDate);
                shelve = matchDay.GetShowcase(game).GetShelve({order});
                newLine = shelve.CreateNewVersionForOverUnderLine({lineId}, {overReward}, {underReward}, 'Employee', Now);
                print newLine.Version newVersion;
                print newLine.LineId lineId;
			}}
            ");

			return this;
		}

		internal LinesMockGenerator GetSuspendedLines(int tournamentId, int gameNumber, out List<int> linesIds)
		{
			var result = Perform($@"
					 tournament = company.Tournaments.FindById({tournamentId});
                     betBoard = company.Betboard(tournament);
					 game = tournament.GetGameNumber({gameNumber});
                     scheduledDate = game.ScheduledDate.Fecha();
                     matchDay = betBoard.Matchday(scheduledDate);
                     showcase = matchDay.GetShowcase(game);
					 for(suspended: showcase.SuspendedLinesOffered())
					 {{
						print suspended.LineId lineId;
					 }}
					"
					);

			List<int> ids = new List<int>();

			if (!String.IsNullOrWhiteSpace(result))
			{
				foreach (var id in JObject.Parse(result)["suspended"])
				{
					ids.Add(Convert.ToInt32(id["lineId"]));
				}

			}

			linesIds = ids;
			return this;
		}

		internal LinesMockGenerator SuspendLine(int tournamentId, int gameNumber, int lineId, string msg, string employeeName)
		{
			Perform($@"
				{{
                    tournament = company.Tournaments.FindById({tournamentId});
                    betBoard = company.Betboard(tournament);
                    game = tournament.GetGameNumber({gameNumber});
                    scheduledDate = game.ScheduledDate.Fecha();
                    matchDay = betBoard.Matchday(scheduledDate);
                    showcase = matchDay.GetShowcase(game);
                    line = showcase.SuspendLine(itIsThePresent, {lineId}, Now);
                    line.AddAnnotation('{msg}', '{employeeName}', Now);
				}}
				");

			return this;
		}

		internal LinesMockGenerator ResumeLine(int tournamentId, int gameNumber, int lineId, string msg, string employeeName)
		{
			Perform($@"
				{{
                    tournament = company.Tournaments.FindById({tournamentId});
                    betBoard = company.Betboard(tournament);
                    game = tournament.GetGameNumber({gameNumber});
                    scheduledDate = game.ScheduledDate.Fecha();
                    matchDay = betBoard.Matchday(scheduledDate);
                    showcase = matchDay.GetShowcase(game);
                    line = showcase.ResumeLine(itIsThePresent, {lineId}, Now);
                    line.AddAnnotation('{msg}', '{employeeName}', Now);
				}}
				");

			return this;
		}

		internal LinesMockGenerator(Actor actor, DateTime dateWithTime)
		{
			this.actor = actor;
			var ids = JObject.Parse(Perform($@"
						company = Company();        
						companyIDf2401f84xa94ax4db3xaec4x83f5361730ae = company;        
						company.Accounting = MockAccounting();        
						store = company.Sales.CreateStore(5,'Ladybet Store');
						store.MakeCurrent();                                
						book = company.Book;        
						tournaments = company.Tournaments;     
   
						domain = company.Sales.CreateDomain(false, 1, '138.94.58.158', {Agents.INSIDER});   
						domain.AddAnnotation('Domain has been created', 'Jonathan', Now);
						company.Sales.CurrentStore.Add(domain);        
						domain.AddResourceUrl('RulesUrl', 'https://www.5dimes.eu/mm-brackets-how.html');        

						domain = company.Sales.CreateDomain(false, 2, 'nodo.qa5.ncubo.com', {Agents.INSIDER});       
						domain.AddAnnotation('Domain has been created', 'Jonathan', Now);
						company.Sales.CurrentStore.Add(domain);        
						domain.AddResourceUrl('RulesUrl', 'https://www.5dimes.eu/mm-brackets-how.html');      

						domain = company.Sales.CreateDomain(false, 3, 'nodo.qa3.ncubo.com', {Agents.INSIDER});       
						domain.AddAnnotation('Domain has been created', 'Jonathan', Now);
						company.Sales.CurrentStore.Add(domain);        
						domain.AddResourceUrl('RulesUrl', 'https://www.5dimes.eu/mm-brackets-how.html');      

						domain = company.Sales.CreateDomain(false, 4, 'localhost', {Agents.INSIDER});       
						domain.AddAnnotation('Domain has been created', 'Jonathan', Now);
						company.Sales.CurrentStore.Add(domain);        
						domain.AddResourceUrl('RulesUrl', 'https://www.5dimes.eu/mm-brackets-how.html'); 

						domain = company.Sales.CreateDomain(false, 5, 'nodo.qa7.ncubo.com', {Agents.INSIDER});       
						domain.AddAnnotation('Domain has been created', 'Jonathan', Now);
						company.Sales.CurrentStore.Add(domain);        
						domain.AddResourceUrl('RulesUrl', 'https://www.5dimes.eu/mm-brackets-how.html'); 

						domain = company.Sales.CreateDomain(false, 6, '127.0.0.1', {Agents.INSIDER});       
						domain.AddAnnotation('Domain has been created', 'Jonathan', Now);
						company.Sales.CurrentStore.Add(domain);        
						domain.AddResourceUrl('RulesUrl', 'https://www.5dimes.eu/mm-brackets-how.html'); 
 
						sport = tournaments.Sports.FindById(1);
						sport.Aliases.Add(ForeingAlias({ProvidersCollection.BetFair.Id}, '1', 'Soccer'));
						company.Sales.CurrentStore.AddDefaultSport(sport);
						sport.CreateAndReplaceDefaultBackground('soccer.png', 'images/bg_sbs/soccer.png');
						sport.CreateAndReplaceDefaultSideBySide('coming_soon_picture', 'images/side-by-side.png');

						sport = tournaments.Sports.FindById(18);
						sport.Aliases.Add(ForeingAlias({ProvidersCollection.BetFair.Id}, '7522', 'Basketball'));
						company.Sales.CurrentStore.AddDefaultSport(sport);
						sport.CreateAndReplaceDefaultBackground('basketball.png', 'images/bg_sbs/basketball.png');
						sport.CreateAndReplaceDefaultSideBySide('coming_soon_picture', 'images/side-by-side.png');

						sport = tournaments.Sports.FindById(16);
						sport.Aliases.Add(ForeingAlias({ProvidersCollection.BetFair.Id}, '7511', 'Baseball'));
						company.Sales.CurrentStore.AddDefaultSport(sport);
						sport.CreateAndReplaceDefaultBackground('baseball.png', 'images/bg_sbs/baseball.png');
						sport.CreateAndReplaceDefaultSideBySide('coming_soon_picture', 'images/side-by-side.png');

						sport = tournaments.Sports.FindById(12);
						sport.Aliases.Add(ForeingAlias({ProvidersCollection.BetFair.Id}, '6423', 'American Football'));
						company.Sales.CurrentStore.AddDefaultSport(sport);
						sport.CreateAndReplaceDefaultBackground('football.png', 'images/bg_sbs/football.png');
						sport.CreateAndReplaceDefaultSideBySide('coming_soon_picture', 'images/side-by-side.png');

						sport = tournaments.Sports.FindById(17);
						sport.Aliases.Add(ForeingAlias({ProvidersCollection.BetFair.Id}, '7524', 'Ice Hockey'));
						company.Sales.CurrentStore.AddDefaultSport(sport);
						sport.CreateAndReplaceDefaultBackground('hockey.png', 'images/bg_sbs/hockey.png');
						sport.CreateAndReplaceDefaultSideBySide('coming_soon_picture', 'images/side-by-side.png');

						leagueIdBasket = company.Tournaments.Leagues.NextLeagueId; 
						print leagueIdBasket BasketballLeagueId;
						sport = company.Tournaments.Sports.FindById(18);        
						league = company.Tournaments.Leagues.Create(sport, 'National Basketball Association', 'NBA', leagueIdBasket); 

						leagueIdSoccer = company.Tournaments.Leagues.NextLeagueId;  
						print leagueIdSoccer SoccerLeagueId;
						sport = company.Tournaments.Sports.FindById(1);        
						league = company.Tournaments.Leagues.Create(sport, 'Major League Soccer', 'MLS', leagueIdSoccer); 

						tournamentIdBasket = company.Tournaments.NextTournamentId;   
						print tournamentIdBasket BasketballTournamentId;
						league = company.Tournaments.Leagues.FindById(leagueIdBasket);        
						tournament = company.Tournaments.CreateTournament(league, tournamentIdBasket, 'NBA 2021');        
						tournament = company.Tournaments.FindById(tournamentIdBasket);        
						tournament.OpenRegistration();
						tournament.Start();

						tournamentIdSoccer = company.Tournaments.NextTournamentId;   
						print tournamentIdSoccer SoccerTournamentId;
						league = company.Tournaments.Leagues.FindById(leagueIdSoccer);        
						tournament1 = company.Tournaments.CreateTournament(league, tournamentIdSoccer, 'MLS 2021');        
						tournament1 = company.Tournaments.FindById(tournamentIdSoccer);        
						tournament1.OpenRegistration();    
						tournament1.Start();  
  
						product = company.GetOrCreateProductById(1);        
						product.Description = 'Wager for lines';        
						product.Price = 1;      


						preference = StorePreference(1, 'Show pregame lines');
						store.AddPreference(preference);
						preference = StorePreference(2, 'Ask password in betslip');
						store.AddPreference(preference);

						domain = company.Sales.DomainFrom(1);
						preferences = company.Sales.CurrentStore.Preferences;
						preference = preferences.FindPreference(1);
						activation = preferences.Disable(preference, domain);
						activation.AddAnnotation(domain.Id.ToString(), 'Disable', 'Jonathan', Now);

						domain = company.Sales.DomainFrom(1);
						preferences = company.Sales.CurrentStore.Preferences;
						preference = preferences.FindPreference(2);
						activation = preferences.Disable(preference, domain);
						activation.AddAnnotation(domain.Id.ToString(), 'Disable', 'Jonathan', Now);

						domain = company.Sales.DomainFrom(2);
						preferences = company.Sales.CurrentStore.Preferences;
						preference = preferences.FindPreference(1);
						activation = preferences.Disable(preference, domain);
						activation.AddAnnotation(domain.Id.ToString(), 'Disable', 'Jonathan', Now);

						domain = company.Sales.DomainFrom(2);
						preferences = company.Sales.CurrentStore.Preferences;
						preference = preferences.FindPreference(2);
						activation = preferences.Disable(preference, domain);
						activation.AddAnnotation(domain.Id.ToString(), 'Disable', 'Jonathan', Now);
  
						domain = company.Sales.DomainFrom(3);
						preferences = company.Sales.CurrentStore.Preferences;
						preference = preferences.FindPreference(1);
						activation = preferences.Disable(preference, domain);
						activation.AddAnnotation(domain.Id.ToString(), 'Disable', 'Jonathan', Now);

						domain = company.Sales.DomainFrom(3);
						preferences = company.Sales.CurrentStore.Preferences;
						preference = preferences.FindPreference(2);
						activation = preferences.Disable(preference, domain);
						activation.AddAnnotation(domain.Id.ToString(), 'Disable', 'Jonathan', Now);

						domain = company.Sales.DomainFrom(4);
						preferences = company.Sales.CurrentStore.Preferences;
						preference = preferences.FindPreference(1);
						activation = preferences.Disable(preference, domain);
						activation.AddAnnotation(domain.Id.ToString(), 'Disable', 'Jonathan', Now);

						domain = company.Sales.DomainFrom(4);
						preferences = company.Sales.CurrentStore.Preferences;
						preference = preferences.FindPreference(2);
						activation = preferences.Disable(preference, domain);
						activation.AddAnnotation(domain.Id.ToString(), 'Disable', 'Jonathan', Now);

						domain = company.Sales.DomainFrom(5);
						preferences = company.Sales.CurrentStore.Preferences;
						preference = preferences.FindPreference(1);
						activation = preferences.Disable(preference, domain);
						activation.AddAnnotation(domain.Id.ToString(), 'Disable', 'Jonathan', Now);

						domain = company.Sales.DomainFrom(5);
						preferences = company.Sales.CurrentStore.Preferences;
						preference = preferences.FindPreference(2);
						activation = preferences.Disable(preference, domain);
						activation.AddAnnotation(domain.Id.ToString(), 'Disable', 'Jonathan', Now);

						betBoard = company.Betboard(tournament);        
						betBoard.AcceptFrom(tournament);        
						betBoard.Catalog.IncludeDomainForActivations(domain);        
						catalog = betBoard.Catalog;

						betBoard1 = company.Betboard(tournament1);        
						betBoard1.AcceptFrom(tournament1);        
						betBoard1.Catalog.IncludeDomainForActivations(domain);        
						catalog1 = betBoard1.Catalog;
  
						domain = company.Sales.DomainFrom('138.94.58.158');        
						catalog.IncludeDomainForActivations(domain);        
						domain = company.Sales.DomainFrom('nodo.qa5.ncubo.com');        
						catalog.IncludeDomainForActivations(domain);   
						domain = company.Sales.DomainFrom('nodo.qa3.ncubo.com');        
						catalog.IncludeDomainForActivations(domain);  
						domain = company.Sales.DomainFrom('nodo.qa7.ncubo.com');        
						catalog.IncludeDomainForActivations(domain);  

						betBoard.PresetBetAmounts.Add(0.25, 'N/A', Now);        
						betBoard.PresetBetAmounts.Add(0.5, 'N/A', Now);        
						betBoard.PresetBetAmounts.Add(1, 'N/A', Now);        
						betBoard.BetRanges.Add(Now, domain, 1, 0.25, 'N/A');   
"));

			CreateDefaultBasketTeamsAndMatches(dateWithTime, Convert.ToInt32(ids["BasketballTournamentId"]), Convert.ToInt32(ids["BasketballLeagueId"]));
			CreateDefaultSoccerTeamsAndMatches(dateWithTime, Convert.ToInt32(ids["SoccerTournamentId"]), Convert.ToInt32(ids["SoccerLeagueId"]));

			Perform($@"
						custNO562430373 = company.GetOrCreateCustomerById('NO562430373');                    
						custNO562430373.Identifier='101';                    
						player = custNO562430373.Player;                    
						playerNO562430373 = player;                   
						{{                        
								thePlayerItsNew = custNO562430373.ItsNewOne();
								If (thePlayerItsNew)
								{{                    
        							custNO562430373.ReplicateInOtherNodes(itIsThePresent, true, Now, 'TesterUser 1', 'https://intranet.ncubo.com/ResourcesQA5/api/file/IDDefault/Lotto/Lotto/avatars/avatar2.png');
								}}               
								Else
								{{                    
        							custNO562430373.AddVisit(itIsThePresent, now);                    
								 }}                    
						}}
			");

			Perform($@"
						{{
							tag = book.RiskTags.CreateTag('Wise', 'http://www.tags.com/ECC600');
							tag.Description = 'Wise';
						}}
						{{
							tag = book.RiskTags.CreateTag('Steam Chase', 'http://www.tags.com/E77E22');
							tag.Description = 'Steam Chase';
						}}
						{{
							tag = book.RiskTags.CreateTag('Bad Lines', 'http://www.tags.com/C200AF');
							tag.Description = 'Bad Lines';
						}}
						{{
							player = company.GetOrCreateCustomerById('NO562430373').Player;
							riskTags = book.RiskTags;
							tag = riskTags.FindTag('Wise');
							riskTags.Tag(player, tag);
						}}
						{{
							player = company.GetOrCreateCustomerById('NO562430373').Player;
							riskTags = book.RiskTags;
							tag = riskTags.FindTag('Steam Chase');
							riskTags.Tag(player, tag);
						}}
						{{
							player = company.GetOrCreateCustomerById('NO562430373').Player;
							riskTags = book.RiskTags;
							tag = riskTags.FindTag('Bad Lines');
							riskTags.Tag(player, tag);
						}}

			");
		}

		private LinesMockGenerator CreateDefaultBasketTeamsAndMatches(DateTime dateWithTime, int tournamentId, int leagueId)
		{
			string matchInfo = Perform(@$"
						tournament = company.Tournaments.FindById({tournamentId});        
						league = company.Tournaments.Leagues.FindById({leagueId});   
						Eval('teamId =' + company.Tournaments.NextTeamId() + ';');
						teama = tournaments.CreateTeam(league, teamId, 'Denver Nuggets');
						teama.Logo = 'images/teams-logos/NBA/denver_nuggets.png';
						Eval('teamId =' + company.Tournaments.NextTeamId() + ';');
						teamb = tournaments.CreateTeam(league, teamId, 'Minnesota Timberwolves');        
						teamb.Logo = 'images/teams-logos/NBA/minnesota_timberwolves.png';
						tournament.Register(teama);        
						tournament.Register(teamb);        
						game = tournament.GetNewGame(1, teama, teamb);        
						game.Home = game.TeamA;        
						game.Visitor = game.TeamB;        
						game.SetFavorite(game.TeamA);        
						tournament = company.Tournaments.FindById({tournamentId});        
						game.Reschedule({dateWithTime.Month}/{dateWithTime.Day}/{dateWithTime.Year} 8:00:00);        
						matchDay = betBoard.Matchday({dateWithTime.Month}/{dateWithTime.Day}/{dateWithTime.Year});        
						shelve = matchDay.GetShowcase(game).InsertShelve(0);
						{GameInfoToSendToSearchEngine}");

			GameRelatedInformation document = JsonConvert.DeserializeObject<Synchronizator.GameRelatedInformation>(matchInfo);
			Synchronizator.CreateOrUpdateOfficialDocuments(document);

			matchInfo = Perform($@"
						tournament = company.Tournaments.FindById({tournamentId});        
						league = company.Tournaments.Leagues.FindById({leagueId});   
						Eval('teamId =' + company.Tournaments.NextTeamId() + ';');        
						teama = tournaments.CreateTeam(league, teamId, 'Oklahoma City Thunder'); 
						teama.Logo = 'images/teams-logos/NBA/oklahoma_city_thunder.png';
						Eval('teamId =' + company.Tournaments.NextTeamId() + ';');        
						teamb = tournaments.CreateTeam(league, teamId, 'Portland Trail Blazers');  
						teamb.Logo = 'images/teams-logos/NBA/portland_trail_blazers.png';
						tournament.Register(teama);        
						tournament.Register(teamb);        
						game = tournament.GetNewGame(2, teama, teamb);        
						game.Home = game.TeamA;        
						game.Visitor = game.TeamB;        
						game.SetFavorite(game.TeamA);        
						tournament = company.Tournaments.FindById({tournamentId});        
						game.Reschedule({dateWithTime.Month}/{dateWithTime.Day}/{dateWithTime.Year} 8:10:00);        
						matchDay2 = betBoard.Matchday({dateWithTime.Month}/{dateWithTime.Day}/{dateWithTime.Year});       
						shelve2 = matchDay2.GetShowcase(game).InsertShelve(1);
						{GameInfoToSendToSearchEngine}");

			document = JsonConvert.DeserializeObject<Synchronizator.GameRelatedInformation>(matchInfo);
			Synchronizator.CreateOrUpdateOfficialDocuments(document);

			matchInfo = Perform($@"  
						tournament = company.Tournaments.FindById({tournamentId});        
						league = company.Tournaments.Leagues.FindById({leagueId});   
						Eval('teamId =' + company.Tournaments.NextTeamId() + ';');        
						teama = tournaments.CreateTeam(league, teamId, 'Utah Jazz');
						teama.Logo = 'images/teams-logos/NBA/utah_jazz.png';
						Eval('teamId =' + company.Tournaments.NextTeamId() + ';');
						teamb = tournaments.CreateTeam(league, teamId, 'Dallas Mavericks'); 
						teamb.Logo = 'images/teams-logos/NBA/dallas_mavericks.png';
						tournament.Register(teama);        
						tournament.Register(teamb);        
						game = tournament.GetNewGame(3, teama, teamb);        
						game.Home = game.TeamA;        
						game.Visitor = game.TeamB;        
						game.SetFavorite(game.TeamA);        
						tournament = company.Tournaments.FindById({tournamentId});        
						game.Reschedule({dateWithTime.Month}/{dateWithTime.Day}/{dateWithTime.Year} 8:20:00);        
						matchDay2 = betBoard.Matchday({dateWithTime.Month}/{dateWithTime.Day}/{dateWithTime.Year});
						{GameInfoToSendToSearchEngine}");

			document = JsonConvert.DeserializeObject<Synchronizator.GameRelatedInformation>(matchInfo);
			Synchronizator.CreateOrUpdateOfficialDocuments(document);

			matchInfo = Perform($@" 
						tournament = company.Tournaments.FindById({tournamentId});        
						league = company.Tournaments.Leagues.FindById({leagueId});   
						Eval('teamId =' + company.Tournaments.NextTeamId() + ';');        
						teama = tournaments.CreateTeam(league, teamId, 'Houston Rockets');  
						teama.Logo = 'images/teams-logos/NBA/houston_rockets.png';
						Eval('teamId =' + company.Tournaments.NextTeamId() + ';');        
						teamb = tournaments.CreateTeam(league, teamId, 'Memphis Grizzlies');      
						teamb.Logo = 'images/teams-logos/NBA/memphis_grizzlies.png';
						tournament.Register(teama);        
						tournament.Register(teamb);        
						game = tournament.GetNewGame(4, teama, teamb);        
						game.Home = game.TeamA;        
						game.Visitor = game.TeamB;        
						game.SetFavorite(game.TeamA);        
						tournament = company.Tournaments.FindById({tournamentId});        
						game.Reschedule({dateWithTime.Month}/{dateWithTime.Day}/{dateWithTime.Year} 8:30:00);        
						matchDay2 = betBoard.Matchday({dateWithTime.Month}/{dateWithTime.Day}/{dateWithTime.Year});
						{GameInfoToSendToSearchEngine}");

			document = JsonConvert.DeserializeObject<Synchronizator.GameRelatedInformation>(matchInfo);
			Synchronizator.CreateOrUpdateOfficialDocuments(document);

			matchInfo = Perform($@"
						tournament = company.Tournaments.FindById({tournamentId});        
						league = company.Tournaments.Leagues.FindById({leagueId});   
						Eval('teamId =' + company.Tournaments.NextTeamId() + ';');        
						teama = tournaments.CreateTeam(league, teamId, 'New Orleans Pelicans');   
						teama.Logo = 'images/teams-logos/NBA/new_orleans_pelicans.png';
						Eval('teamId =' + company.Tournaments.NextTeamId() + ';');        
						teamb = tournaments.CreateTeam(league, teamId, 'San Antonio Spurs');  
						teamb.Logo = 'images/teams-logos/NBA/san_antonio_spurs.png';
						tournament.Register(teama);        
						tournament.Register(teamb);        
						game = tournament.GetNewGame(5, teama, teamb);        
						game.Home = game.TeamA;        
						game.Visitor = game.TeamB;        
						game.SetFavorite(game.TeamA);        
						tournament = company.Tournaments.FindById({tournamentId});        
						game.Reschedule({dateWithTime.Month}/{dateWithTime.Day}/{dateWithTime.Year} 8:40:00);        
						matchDay2 = betBoard.Matchday({dateWithTime.Month}/{dateWithTime.Day}/{dateWithTime.Year});
						{GameInfoToSendToSearchEngine}");

			document = JsonConvert.DeserializeObject<Synchronizator.GameRelatedInformation>(matchInfo);
			Synchronizator.CreateOrUpdateOfficialDocuments(document);

			matchInfo = Perform($@"
						tournament = company.Tournaments.FindById({tournamentId});        
						league = company.Tournaments.Leagues.FindById({leagueId});   
						Eval('teamId =' + company.Tournaments.NextTeamId() + ';');        
						teama = tournaments.CreateTeam(league, teamId, 'Golden State Warriors');   
						teama.Logo = 'images/teams-logos/NBA/golden_state_warriors.png';
						Eval('teamId =' + company.Tournaments.NextTeamId() + ';');        
						teamb = tournaments.CreateTeam(league, teamId, 'Los Angeles Clippers');   
						teamb.Logo = 'images/teams-logos/NBA/Los_angeles_clippers.png';
						tournament.Register(teama);        
						tournament.Register(teamb);        
						game = tournament.GetNewGame(6, teama, teamb);        
						game.Home = game.TeamA;        
						game.Visitor = game.TeamB;        
						game.SetFavorite(game.TeamA);        
						tournament = company.Tournaments.FindById({tournamentId});        
						game.Reschedule({dateWithTime.Month}/{dateWithTime.Day}/{dateWithTime.Year} 8:50:00);        
						matchDay2 = betBoard.Matchday({dateWithTime.Month}/{dateWithTime.Day}/{dateWithTime.Year});
						{GameInfoToSendToSearchEngine}");

			document = JsonConvert.DeserializeObject<Synchronizator.GameRelatedInformation>(matchInfo);
			Synchronizator.CreateOrUpdateOfficialDocuments(document);

			matchInfo = Perform($@"
						tournament = company.Tournaments.FindById({tournamentId});        
						league = company.Tournaments.Leagues.FindById({leagueId});   
						Eval('teamId =' + company.Tournaments.NextTeamId() + ';');        
						teama = tournaments.CreateTeam(league, teamId, 'Los Angeles Lakers');
						teama.Logo = 'images/teams-logos/NBA/los_angeles_lakers.png';
						Eval('teamId =' + company.Tournaments.NextTeamId() + ';');
						teamb = tournaments.CreateTeam(league, teamId, 'Phoenix Suns');  
						teamb.Logo = 'images/teams-logos/NBA/phoenix_suns.png';
						tournament.Register(teama);        
						tournament.Register(teamb);        
						game = tournament.GetNewGame(7, teama, teamb);        
						game.Home = game.TeamA;        
						game.Visitor = game.TeamB;        
						game.SetFavorite(game.TeamA);        
						tournament = company.Tournaments.FindById({tournamentId});        
						game.Reschedule({dateWithTime.Month}/{dateWithTime.Day}/{dateWithTime.Year} 9:00:00);        
						matchDay2 = betBoard.Matchday({dateWithTime.Month}/{dateWithTime.Day}/{dateWithTime.Year});
						{GameInfoToSendToSearchEngine}");

			document = JsonConvert.DeserializeObject<Synchronizator.GameRelatedInformation>(matchInfo);
			Synchronizator.CreateOrUpdateOfficialDocuments(document);

			matchInfo = Perform($@"
						tournament = company.Tournaments.FindById({tournamentId});        
						league = company.Tournaments.Leagues.FindById({leagueId});   
						Eval('teamId =' + company.Tournaments.NextTeamId() + ';');        
						teama = tournaments.CreateTeam(league, teamId, 'Boston Celtics');   
						teama.Logo = 'images/teams-logos/NBA/boston_celtics.png';
						Eval('teamId =' + company.Tournaments.NextTeamId() + ';');        
						teamb = tournaments.CreateTeam(league, teamId, 'Brooklyn Nets');   
						teamb.Logo = 'images/teams-logos/NBA/brooklyn_nets.png';
						tournament.Register(teama);        
						tournament.Register(teamb);        
						game = tournament.GetNewGame(8, teama, teamb);        
						game.Home = game.TeamA;        
						game.Visitor = game.TeamB;        
						game.SetFavorite(game.TeamA);        
						tournament = company.Tournaments.FindById({tournamentId});        
						game.Reschedule({dateWithTime.Month}/{dateWithTime.Day}/{dateWithTime.Year} 9:10:00);        
						matchDay2 = betBoard.Matchday({dateWithTime.Month}/{dateWithTime.Day}/{dateWithTime.Year});
						{GameInfoToSendToSearchEngine}");

			document = JsonConvert.DeserializeObject<Synchronizator.GameRelatedInformation>(matchInfo);
			Synchronizator.CreateOrUpdateOfficialDocuments(document);

			matchInfo = Perform($@"
						tournament = company.Tournaments.FindById({tournamentId});        
						league = company.Tournaments.Leagues.FindById({leagueId});   
						Eval('teamId =' + company.Tournaments.NextTeamId() + ';');        
						teama = tournaments.CreateTeam(league, teamId, 'New York Knicks');  
						teama.Logo = 'images/teams-logos/NBA/new_york_knicks.png';
						Eval('teamId =' + company.Tournaments.NextTeamId() + ';');        
						teamb = tournaments.CreateTeam(league, teamId, 'Philadelphia 76ers'); 
						teamb.Logo = 'images/teams-logos/NBA/philadelphia_76ers.png';
						tournament.Register(teama);        
						tournament.Register(teamb);        
						game = tournament.GetNewGame(9, teama, teamb);        
						game.Home = game.TeamA;        
						game.Visitor = game.TeamB;        
						game.SetFavorite(game.TeamA);        
						tournament = company.Tournaments.FindById({tournamentId});        
						game.Reschedule({dateWithTime.Month}/{dateWithTime.Day}/{dateWithTime.Year} 9:20:00);        
						matchDay2 = betBoard.Matchday({dateWithTime.Month}/{dateWithTime.Day}/{dateWithTime.Year});
						{GameInfoToSendToSearchEngine}");

			document = JsonConvert.DeserializeObject<Synchronizator.GameRelatedInformation>(matchInfo);
			Synchronizator.CreateOrUpdateOfficialDocuments(document);

			matchInfo = Perform($@"
						tournament = company.Tournaments.FindById({tournamentId});        
						league = company.Tournaments.Leagues.FindById({leagueId});   
						Eval('teamId =' + company.Tournaments.NextTeamId() + ';');        
						teama = tournaments.CreateTeam(league, teamId, 'Toronto Raptors');
						teama.Logo = 'images/teams-logos/NBA/toronto_raptors.png';
						Eval('teamId =' + company.Tournaments.NextTeamId() + ';');        
						teamb = tournaments.CreateTeam(league, teamId, 'Chicago Bulls'); 
						teamb.Logo = 'images/teams-logos/NBA/chicago_bulls.png';
						tournament.Register(teama);        
						tournament.Register(teamb);        
						game0 = tournament.GetNewGame(10, teama, teamb);        
						game0.Home = game0.TeamA;        
						game0.Visitor = game0.TeamB;        
						game0.SetFavorite(game0.TeamA);        
						tournament = company.Tournaments.FindById({tournamentId});        
						game0.Reschedule({dateWithTime.Month}/{dateWithTime.Day}/{dateWithTime.Year} 9:30:00);        
						matchDay2 = betBoard.Matchday({dateWithTime.Month}/{dateWithTime.Day}/{dateWithTime.Year});
						{GameInfoToSendToSearchEngine}");

			document = JsonConvert.DeserializeObject<Synchronizator.GameRelatedInformation>(matchInfo);
			Synchronizator.CreateOrUpdateOfficialDocuments(document);

			matchInfo = Perform($@"
						tournament = company.Tournaments.FindById({tournamentId});        
						league = company.Tournaments.Leagues.FindById({leagueId});   
						Eval('teamId =' + company.Tournaments.NextTeamId() + ';');        
						teama = tournaments.CreateTeam(league, teamId, 'Cleveland Cavaliers');   
						teama.Logo = 'images/teams-logos/NBA/cleveland_cavaliers.png';
						Eval('teamId =' + company.Tournaments.NextTeamId() + ';');        
						teamb = tournaments.CreateTeam(league, teamId, 'Detroit Pistons');   
						teamb.Logo = 'images/teams-logos/NBA/detroit_pistons.png';
						tournament.Register(teama);        
						tournament.Register(teamb);        
						game1 = tournament.GetNewGame(11, teama, teamb);        
						game1.Home = game1.TeamA;        
						game1.Visitor = game1.TeamB;        
						game1.SetFavorite(game1.TeamA);        
						tournament = company.Tournaments.FindById({tournamentId});        
						game1.Reschedule({dateWithTime.Month}/{dateWithTime.Day}/{dateWithTime.Year} 9:40:00);        
						matchDay2 = betBoard.Matchday({dateWithTime.Month}/{dateWithTime.Day}/{dateWithTime.Year});
						{GameInfoToSendToSearchEngine}");

			document = JsonConvert.DeserializeObject<Synchronizator.GameRelatedInformation>(matchInfo);
			Synchronizator.CreateOrUpdateOfficialDocuments(document);

			matchInfo = Perform($@" 
						tournament = company.Tournaments.FindById({tournamentId});        
						league = company.Tournaments.Leagues.FindById({leagueId});   
						Eval('teamId =' + company.Tournaments.NextTeamId() + ';');        
						teama = tournaments.CreateTeam(league, teamId, 'Indiana Pacers');
						teama.Logo = 'images/teams-logos/NBA/indiana_pacers.png';
						Eval('teamId =' + company.Tournaments.NextTeamId() + ';');
						teamb = tournaments.CreateTeam(league, teamId, 'Milwaukee Bucks');   
						teamb.Logo = 'images/teams-logos/NBA/milwaukee_bucks.png';
						tournament.Register(teama);        
						tournament.Register(teamb);        
						game2 = tournament.GetNewGame(12, teama, teamb);        
						game2.Home = game2.TeamA;        
						game2.Visitor = game2.TeamB;        
						game2.SetFavorite(game2.TeamA);        
						tournament = company.Tournaments.FindById({tournamentId});        
						game2.Reschedule({dateWithTime.Month}/{dateWithTime.Day}/{dateWithTime.Year} 9:50:00);        
						matchDay2 = betBoard.Matchday({dateWithTime.Month}/{dateWithTime.Day}/{dateWithTime.Year});
						{GameInfoToSendToSearchEngine}");

			document = JsonConvert.DeserializeObject<Synchronizator.GameRelatedInformation>(matchInfo);
			Synchronizator.CreateOrUpdateOfficialDocuments(document);

			matchInfo = Perform($@"
						tournament = company.Tournaments.FindById({tournamentId});        
						league = company.Tournaments.Leagues.FindById({leagueId});   
						Eval('teamId =' + company.Tournaments.NextTeamId() + ';');        
						teama = tournaments.CreateTeam(league, teamId, 'Atlanta Hawks');  
						teama.Logo = 'images/teams-logos/NBA/atlanta_hawks.png';
						Eval('teamId =' + company.Tournaments.NextTeamId() + ';');        
						teamb = tournaments.CreateTeam(league, teamId, 'Charlotte Hornets'); 
						teamb.Logo = 'images/teams-logos/NBA/charlotte_hornets.png';
						tournament.Register(teama);        
						tournament.Register(teamb);        
						game3 = tournament.GetNewGame(13, teama, teamb);        
						game3.Home = game3.TeamA;        
						game3.Visitor = game3.TeamB;        
						game3.SetFavorite(game3.TeamA);        
						tournament = company.Tournaments.FindById({tournamentId});        
						game3.Reschedule({dateWithTime.Month}/{dateWithTime.Day}/{dateWithTime.Year} 10:00:00);        
						matchDay2 = betBoard.Matchday({dateWithTime.Month}/{dateWithTime.Day}/{dateWithTime.Year});
						{GameInfoToSendToSearchEngine}");

			document = JsonConvert.DeserializeObject<Synchronizator.GameRelatedInformation>(matchInfo);
			Synchronizator.CreateOrUpdateOfficialDocuments(document);

			matchInfo = Perform($@"

						tournament = company.Tournaments.FindById({tournamentId});        
						league = company.Tournaments.Leagues.FindById({leagueId});   
						Eval('teamId =' + company.Tournaments.NextTeamId() + ';');        
						teama = tournaments.CreateTeam(league, teamId, 'Miami Heat');  
						teama.Logo = 'images/teams-logos/NBA/miami_heat.png';
						Eval('teamId =' + company.Tournaments.NextTeamId() + ';');        
						teamb = tournaments.CreateTeam(league, teamId, 'Orlando Magic');    
						teamb.Logo = 'images/teams-logos/NBA/orlando_magic.png';
						tournament.Register(teama);        
						tournament.Register(teamb);        
						game4 = tournament.GetNewGame(14, teama, teamb);        
						game4.Home = game4.TeamA;        
						game4.Visitor = game4.TeamB;        
						game4.SetFavorite(game4.TeamA);        
						tournament = company.Tournaments.FindById({tournamentId});        
						game4.Reschedule({dateWithTime.Month}/{dateWithTime.Day}/{dateWithTime.Year} 10:10:00);        
						matchDay2 = betBoard.Matchday({dateWithTime.Month}/{dateWithTime.Day}/{dateWithTime.Year});
						{GameInfoToSendToSearchEngine}");

			document = JsonConvert.DeserializeObject<Synchronizator.GameRelatedInformation>(matchInfo);
			Synchronizator.CreateOrUpdateOfficialDocuments(document);

			matchInfo = Perform($@"
						tournament = company.Tournaments.FindById({tournamentId});        
						league = company.Tournaments.Leagues.FindById({leagueId});   
						Eval('teamId =' + company.Tournaments.NextTeamId() + ';');        
						teama = tournaments.CreateTeam(league, teamId, 'Sacramento Kings');   
						teama.Logo = 'images/teams-logos/NBA/sacramento_kings.png';
						Eval('teamId =' + company.Tournaments.NextTeamId() + ';');        
						teamb = tournaments.CreateTeam(league, teamId, 'Washington Wizards');  
						teamb.Logo = 'images/teams-logos/NBA/washington_wizards.png';
						tournament.Register(teama);        
						tournament.Register(teamb);        
						game5 = tournament.GetNewGame(15, teama, teamb);        
						game5.Home = game5.TeamA;        
						game5.Visitor = game5.TeamB;        
						game5.SetFavorite(game5.TeamA);        
						tournament = company.Tournaments.FindById({tournamentId});        
						game5.Reschedule({dateWithTime.Month}/{dateWithTime.Day}/{dateWithTime.Year} 10:20:00);        
						matchDay2 = betBoard.Matchday({dateWithTime.Month}/{dateWithTime.Day}/{dateWithTime.Year});
						{GameInfoToSendToSearchEngine}");

			document = JsonConvert.DeserializeObject<Synchronizator.GameRelatedInformation>(matchInfo);
			Synchronizator.CreateOrUpdateOfficialDocuments(document);

			return this;
		}

		private LinesMockGenerator CreateDefaultSoccerTeamsAndMatches(DateTime dateWithTime, int tournamentId, int leagueId)
		{
			string matchInfo = Perform(@$"
						tournament = company.Tournaments.FindById({tournamentId});        
						league = company.Tournaments.Leagues.FindById({leagueId});   
						Eval('teamId =' + company.Tournaments.NextTeamId() + ';');
						teama = tournaments.CreateTeam(league, teamId, 'Atlanta United');
						teama.Logo = 'images/teams-logos/MLS/atlanta_united.png';
						Eval('teamId =' + company.Tournaments.NextTeamId() + ';');
						teamb = tournaments.CreateTeam(league, teamId, 'New England Revolution');        
						teamb.Logo = 'images/teams-logos/MLS/new_england_revolution.png';
						tournament.Register(teama);        
						tournament.Register(teamb);        
						game = tournament.GetNewGame(1, teama, teamb);        
						game.Home = game.TeamA;        
						game.Visitor = game.TeamB;        
						game.SetFavorite(game.TeamA);        
						tournament = company.Tournaments.FindById({tournamentId});        
						game.Reschedule({dateWithTime.Month}/{dateWithTime.Day}/{dateWithTime.Year} 8:00:00);        
						matchDay = betBoard1.Matchday({dateWithTime.Month}/{dateWithTime.Day}/{dateWithTime.Year});        
						shelve = matchDay.GetShowcase(game).InsertShelve(0);
						{GameInfoToSendToSearchEngine}");

			GameRelatedInformation document = JsonConvert.DeserializeObject<Synchronizator.GameRelatedInformation>(matchInfo);
			Synchronizator.CreateOrUpdateOfficialDocuments(document);

			matchInfo = Perform($@"
						tournament = company.Tournaments.FindById({tournamentId});        
						league = company.Tournaments.Leagues.FindById({leagueId});   
						Eval('teamId =' + company.Tournaments.NextTeamId() + ';');        
						teama = tournaments.CreateTeam(league, teamId, 'Austion FC'); 
						teama.Logo = 'images/teams-logos/MLS/austin_fc.png';
						Eval('teamId =' + company.Tournaments.NextTeamId() + ';');        
						teamb = tournaments.CreateTeam(league, teamId, 'Nashville SC');  
						teamb.Logo = 'images/teams-logos/MLS/nashville_sc.png';
						tournament.Register(teama);        
						tournament.Register(teamb);        
						game = tournament.GetNewGame(2, teama, teamb);        
						game.Home = game.TeamA;        
						game.Visitor = game.TeamB;        
						game.SetFavorite(game.TeamA);        
						tournament = company.Tournaments.FindById({tournamentId});        
						game.Reschedule({dateWithTime.Month}/{dateWithTime.Day}/{dateWithTime.Year} 8:10:00);        
						matchDay2 = betBoard1.Matchday({dateWithTime.Month}/{dateWithTime.Day}/{dateWithTime.Year});       
						shelve2 = matchDay2.GetShowcase(game).InsertShelve(1);
						{GameInfoToSendToSearchEngine}");

			document = JsonConvert.DeserializeObject<Synchronizator.GameRelatedInformation>(matchInfo);
			Synchronizator.CreateOrUpdateOfficialDocuments(document);

			matchInfo = Perform($@"  
						tournament = company.Tournaments.FindById({tournamentId});        
						league = company.Tournaments.Leagues.FindById({leagueId});   
						Eval('teamId =' + company.Tournaments.NextTeamId() + ';');        
						teama = tournaments.CreateTeam(league, teamId, 'CF Montreal');
						teama.Logo = 'images/teams-logos/MLS/cf_montreal.png';
						Eval('teamId =' + company.Tournaments.NextTeamId() + ';');
						teamb = tournaments.CreateTeam(league, teamId, 'Minnesota United FC'); 
						teamb.Logo = 'images/teams-logos/MLS/minnesota_united_fc.png';
						tournament.Register(teama);        
						tournament.Register(teamb);        
						game = tournament.GetNewGame(3, teama, teamb);        
						game.Home = game.TeamA;        
						game.Visitor = game.TeamB;        
						game.SetFavorite(game.TeamA);        
						tournament = company.Tournaments.FindById({tournamentId});        
						game.Reschedule({dateWithTime.Month}/{dateWithTime.Day}/{dateWithTime.Year} 8:20:00);        
						matchDay2 = betBoard1.Matchday({dateWithTime.Month}/{dateWithTime.Day}/{dateWithTime.Year});
						{GameInfoToSendToSearchEngine}");

			document = JsonConvert.DeserializeObject<Synchronizator.GameRelatedInformation>(matchInfo);
			Synchronizator.CreateOrUpdateOfficialDocuments(document);

			matchInfo = Perform($@" 
						tournament = company.Tournaments.FindById({tournamentId});        
						league = company.Tournaments.Leagues.FindById({leagueId});   
						Eval('teamId =' + company.Tournaments.NextTeamId() + ';');        
						teama = tournaments.CreateTeam(league, teamId, 'Chicago Fire FC');  
						teama.Logo = 'images/teams-logos/MLS/chicago_fire_fc.png';
						Eval('teamId =' + company.Tournaments.NextTeamId() + ';');        
						teamb = tournaments.CreateTeam(league, teamId, 'Los Angeles Football Club');      
						teamb.Logo = 'images/teams-logos/MLS/los_angeles_football_club.png';
						tournament.Register(teama);        
						tournament.Register(teamb);        
						game = tournament.GetNewGame(4, teama, teamb);        
						game.Home = game.TeamA;        
						game.Visitor = game.TeamB;        
						game.SetFavorite(game.TeamA);        
						tournament = company.Tournaments.FindById({tournamentId});        
						game.Reschedule({dateWithTime.Month}/{dateWithTime.Day}/{dateWithTime.Year} 8:30:00);        
						matchDay2 = betBoard1.Matchday({dateWithTime.Month}/{dateWithTime.Day}/{dateWithTime.Year});
						{GameInfoToSendToSearchEngine}");

			document = JsonConvert.DeserializeObject<Synchronizator.GameRelatedInformation>(matchInfo);
			Synchronizator.CreateOrUpdateOfficialDocuments(document);

			matchInfo = Perform($@"
						tournament = company.Tournaments.FindById({tournamentId});        
						league = company.Tournaments.Leagues.FindById({leagueId});   
						Eval('teamId =' + company.Tournaments.NextTeamId() + ';');        
						teama = tournaments.CreateTeam(league, teamId, 'Colorado Rapids');   
						teama.Logo = 'images/teams-logos/MLS/colorado_rapids.png';
						Eval('teamId =' + company.Tournaments.NextTeamId() + ';');        
						teamb = tournaments.CreateTeam(league, teamId, 'LA Galaxy');  
						teamb.Logo = 'images/teams-logos/MLS/la_galaxy.png';
						tournament.Register(teama);        
						tournament.Register(teamb);        
						game = tournament.GetNewGame(5, teama, teamb);        
						game.Home = game.TeamA;        
						game.Visitor = game.TeamB;        
						game.SetFavorite(game.TeamA);        
						tournament = company.Tournaments.FindById({tournamentId});        
						game.Reschedule({dateWithTime.Month}/{dateWithTime.Day}/{dateWithTime.Year} 8:40:00);        
						matchDay2 = betBoard1.Matchday({dateWithTime.Month}/{dateWithTime.Day}/{dateWithTime.Year});
						{GameInfoToSendToSearchEngine}");

			document = JsonConvert.DeserializeObject<Synchronizator.GameRelatedInformation>(matchInfo);
			Synchronizator.CreateOrUpdateOfficialDocuments(document);

			matchInfo = Perform($@"
						tournament = company.Tournaments.FindById({tournamentId});        
						league = company.Tournaments.Leagues.FindById({leagueId});   
						Eval('teamId =' + company.Tournaments.NextTeamId() + ';');        
						teama = tournaments.CreateTeam(league, teamId, 'Columbus Crew');   
						teama.Logo = 'images/teams-logos/MLS/columbus_crew.png';
						Eval('teamId =' + company.Tournaments.NextTeamId() + ';');        
						teamb = tournaments.CreateTeam(league, teamId, 'Inter Miami');   
						teamb.Logo = 'images/teams-logos/MLS/inter_miami.png';
						tournament.Register(teama);        
						tournament.Register(teamb);        
						game = tournament.GetNewGame(6, teama, teamb);        
						game.Home = game.TeamA;        
						game.Visitor = game.TeamB;        
						game.SetFavorite(game.TeamA);        
						tournament = company.Tournaments.FindById({tournamentId});        
						game.Reschedule({dateWithTime.Month}/{dateWithTime.Day}/{dateWithTime.Year} 8:50:00);        
						matchDay2 = betBoard1.Matchday({dateWithTime.Month}/{dateWithTime.Day}/{dateWithTime.Year});
						{GameInfoToSendToSearchEngine}");

			document = JsonConvert.DeserializeObject<Synchronizator.GameRelatedInformation>(matchInfo);
			Synchronizator.CreateOrUpdateOfficialDocuments(document);

			matchInfo = Perform($@"
						tournament = company.Tournaments.FindById({tournamentId});        
						league = company.Tournaments.Leagues.FindById({leagueId});   
						Eval('teamId =' + company.Tournaments.NextTeamId() + ';');        
						teama = tournaments.CreateTeam(league, teamId, 'DC United');   
						teama.Logo = 'images/teams-logos/MLS/dc_united.png';
						Eval('teamId =' + company.Tournaments.NextTeamId() + ';');        
						teamb = tournaments.CreateTeam(league, teamId, 'Houston Dynamo FC');   
						teamb.Logo = 'images/teams-logos/MLS/houston_dynamo_fc.png';
						tournament.Register(teama);        
						tournament.Register(teamb);        
						game = tournament.GetNewGame(7, teama, teamb);        
						game.Home = game.TeamA;        
						game.Visitor = game.TeamB;        
						game.SetFavorite(game.TeamA);        
						tournament = company.Tournaments.FindById({tournamentId});        
						game.Reschedule({dateWithTime.Month}/{dateWithTime.Day}/{dateWithTime.Year} 8:50:00);        
						matchDay2 = betBoard1.Matchday({dateWithTime.Month}/{dateWithTime.Day}/{dateWithTime.Year});
						{GameInfoToSendToSearchEngine}");

			document = JsonConvert.DeserializeObject<Synchronizator.GameRelatedInformation>(matchInfo);
			Synchronizator.CreateOrUpdateOfficialDocuments(document);

			matchInfo = Perform($@"
						tournament = company.Tournaments.FindById({tournamentId});        
						league = company.Tournaments.Leagues.FindById({leagueId});   
						Eval('teamId =' + company.Tournaments.NextTeamId() + ';');        
						teama = tournaments.CreateTeam(league, teamId, 'FC Cincinnati');   
						teama.Logo = 'images/teams-logos/MLS/fc_cincinnati.png';
						Eval('teamId =' + company.Tournaments.NextTeamId() + ';');        
						teamb = tournaments.CreateTeam(league, teamId, 'FC Dallas');   
						teamb.Logo = 'images/teams-logos/MLS/fc_dallas.png';
						tournament.Register(teama);        
						tournament.Register(teamb);        
						game = tournament.GetNewGame(8, teama, teamb);        
						game.Home = game.TeamA;        
						game.Visitor = game.TeamB;        
						game.SetFavorite(game.TeamA);        
						tournament = company.Tournaments.FindById({tournamentId});        
						game.Reschedule({dateWithTime.Month}/{dateWithTime.Day}/{dateWithTime.Year} 8:50:00);        
						matchDay2 = betBoard1.Matchday({dateWithTime.Month}/{dateWithTime.Day}/{dateWithTime.Year});
						{GameInfoToSendToSearchEngine}");

			document = JsonConvert.DeserializeObject<Synchronizator.GameRelatedInformation>(matchInfo);
			Synchronizator.CreateOrUpdateOfficialDocuments(document);

			return this;
		}

		internal LinesMockGenerator DisableSportsByDomain(int domainId)
		{
			Perform($@"
						{{
							domain = company.Sales.DomainFrom({domainId});
							sport = company.Tournaments.Sports.FindById(13);
							company.Sales.CurrentStore.DisableDomain(sport, domain, 'N/A', Now);
						}}

						{{
							domain = company.Sales.DomainFrom({domainId});
							sport = company.Tournaments.Sports.FindById(78);
							company.Sales.CurrentStore.DisableDomain(sport, domain, 'N/A', Now);
						}}

						{{
							domain = company.Sales.DomainFrom({domainId});
							sport = company.Tournaments.Sports.FindById(83);
							company.Sales.CurrentStore.DisableDomain(sport, domain, 'N/A', Now);
						}}

						{{
							domain = company.Sales.DomainFrom({domainId});
							sport = company.Tournaments.Sports.FindById(92);
							company.Sales.CurrentStore.DisableDomain(sport, domain, 'N/A', Now);
						}}


						{{
							domain = company.Sales.DomainFrom({domainId});
							sport = company.Tournaments.Sports.FindById(8);
							company.Sales.CurrentStore.DisableDomain(sport, domain, 'N/A', Now);
						}}

						{{
							domain = company.Sales.DomainFrom({domainId});
							sport = company.Tournaments.Sports.FindById(9);
							company.Sales.CurrentStore.DisableDomain(sport, domain, 'N/A', Now);
						}}

						{{
							domain = company.Sales.DomainFrom({domainId});
							sport = company.Tournaments.Sports.FindById(36);
							company.Sales.CurrentStore.DisableDomain(sport, domain, 'N/A', Now);
						}}

						{{
							domain = company.Sales.DomainFrom({domainId});
							sport = company.Tournaments.Sports.FindById(90);
							company.Sales.CurrentStore.DisableDomain(sport, domain, 'N/A', Now);
						}}


						{{
							domain = company.Sales.DomainFrom({domainId});
							sport = company.Tournaments.Sports.FindById(110);
							company.Sales.CurrentStore.DisableDomain(sport, domain, 'N/A', Now);
						}}

						{{
							domain = company.Sales.DomainFrom({domainId});
							sport = company.Tournaments.Sports.FindById(151);
							company.Sales.CurrentStore.DisableDomain(sport, domain, 'N/A', Now);
						}}

						{{
							domain = company.Sales.DomainFrom({domainId});
							sport = company.Tournaments.Sports.FindById(91);
							company.Sales.CurrentStore.DisableDomain(sport, domain, 'N/A', Now);
						}}

						{{
							domain = company.Sales.DomainFrom({domainId});
							sport = company.Tournaments.Sports.FindById(14);
							company.Sales.CurrentStore.DisableDomain(sport, domain, 'N/A', Now);
						}}


						{{
							domain = company.Sales.DomainFrom({domainId});
							sport = company.Tournaments.Sports.FindById(3);
							company.Sales.CurrentStore.DisableDomain(sport, domain, 'N/A', Now);
						}}

						{{
							domain = company.Sales.DomainFrom({domainId});
							sport = company.Tournaments.Sports.FindById(15);
							company.Sales.CurrentStore.DisableDomain(sport, domain, 'N/A', Now);
						}}

						{{
							domain = company.Sales.DomainFrom({domainId});
							sport = company.Tournaments.Sports.FindById(19);
							company.Sales.CurrentStore.DisableDomain(sport, domain, 'N/A', Now);
						}}

						{{
							domain = company.Sales.DomainFrom({domainId});
							sport = company.Tournaments.Sports.FindById(94);
							company.Sales.CurrentStore.DisableDomain(sport, domain, 'N/A', Now);
						}}


						{{
							domain = company.Sales.DomainFrom({domainId});
							sport = company.Tournaments.Sports.FindById(75);
							company.Sales.CurrentStore.DisableDomain(sport, domain, 'N/A', Now);
						}}

						{{
							domain = company.Sales.DomainFrom({domainId});
							sport = company.Tournaments.Sports.FindById(95);
							company.Sales.CurrentStore.DisableDomain(sport, domain, 'N/A', Now);
						}}

						{{
							domain = company.Sales.DomainFrom({domainId});
							sport = company.Tournaments.Sports.FindById(107);
							company.Sales.CurrentStore.DisableDomain(sport, domain, 'N/A', Now);
						}}

						{{
							domain = company.Sales.DomainFrom({domainId});
							sport = company.Tournaments.Sports.FindById(66);
							company.Sales.CurrentStore.DisableDomain(sport, domain, 'N/A', Now);
						}}
							");

			return this;
		}

		internal LinesMockGenerator CreateQuestionsForSoccerGames(int sportId) 
		{
			Perform($@"
				{{ sport = company.Tournaments.Sports.FindById({sportId}); catalog = company.Tournaments.Catalog(sport); questionId = 1; question = catalog.CreateTierTwoOverUnderQuestion(questionId, 'ou05', 'Over/Under 0.5 Goals Second Half'); background = catalog.GetOrCreateBackground('images/bg_lines/soccer/over-under/over-under1.jpg'); question.Background = background; If (question.Tier.Name != 'T1') {{ question.AllowPublished({{{"'In play'"}}}); }} }} 
				{{ sport = company.Tournaments.Sports.FindById({sportId}); catalog = company.Tournaments.Catalog(sport); questionId = 2; question = catalog.CreateTierTwoOverUnderQuestion(questionId, 'ou15', 'Over/Under 1.5 Goals Second Half'); background = catalog.GetOrCreateBackground('images/bg_lines/soccer/over-under/over-under2.jpg'); question.Background = background; If (question.Tier.Name != 'T1') {{ question.AllowPublished({{{"'In play'"}}}); }} }} 
				{{ sport = company.Tournaments.Sports.FindById({sportId}); catalog = company.Tournaments.Catalog(sport); questionId = 3; question = catalog.CreateTierTwoOverUnderQuestion(questionId, 'ou25', 'Over/Under 2.5 Goals Second Half'); background = catalog.GetOrCreateBackground('images/bg_lines/soccer/over-under/over-under3.jpg'); question.Background = background; If (question.Tier.Name != 'T1') {{ question.AllowPublished({{{"'In play'"}}}); }} }} 
				{{ sport = company.Tournaments.Sports.FindById({sportId}); catalog = company.Tournaments.Catalog(sport); questionId = 4; question = catalog.CreateTierTwoOverUnderQuestion(questionId, 'ou35', 'Over/Under 3.5 Goals Second Half'); background = catalog.GetOrCreateBackground('images/bg_lines/soccer/over-under/over-under4.jpg'); question.Background = background; If (question.Tier.Name != 'T1') {{ question.AllowPublished({{{"'In play', 'First half'"}}}); }} }} 
				{{ sport = company.Tournaments.Sports.FindById({sportId}); catalog = company.Tournaments.Catalog(sport); questionId = 5; question = catalog.CreateTierTwoOverUnderQuestion(questionId, 'ou45', 'Over/Under 4.5 Goals Second Half '); background = catalog.GetOrCreateBackground('images/bg_lines/soccer/over-under/over-under5.jpg'); question.Background = background; If (question.Tier.Name != 'T1') {{ question.AllowPublished({{{"'In play', 'First half'"}}}); }} }} 
				{{ sport = company.Tournaments.Sports.FindById({sportId}); catalog = company.Tournaments.Catalog(sport); questionId = 6; question = catalog.CreateTierTwoOverUnderQuestion(questionId, 'ou55', 'Over/Under 5.5 Goals Second Half '); background = catalog.GetOrCreateBackground('images/bg_lines/soccer/over-under/over-under6.jpg'); question.Background = background; If (question.Tier.Name != 'T1') {{ question.AllowPublished({{{"'In play', 'First half'"}}}); }} }} 
				{{ sport = company.Tournaments.Sports.FindById({sportId}); catalog = company.Tournaments.Catalog(sport); questionId = 7; question = catalog.CreateTierTwoYesNoQuestion(questionId, 'sfh', 'Both Teams to Score First Half'); background = catalog.GetOrCreateBackground('images/bg_lines/soccer/yes-no/yes-no1.jpg'); question.Background = background; If (question.Tier.Name != 'T1') {{ question.AllowPublished({{{"'In play'"}}}); }} }} 
				{{ sport = company.Tournaments.Sports.FindById({sportId}); catalog = company.Tournaments.Catalog(sport); questionId = 8; question = catalog.CreateTierTwoYesNoQuestion(questionId, 'ssh', 'Both Teams to Score in Second Half'); background = catalog.GetOrCreateBackground('images/bg_lines/soccer/yes-no/yes-no2.jpg'); question.Background = background; If (question.Tier.Name != 'T1') {{ question.AllowPublished({{{"'In play', 'First half'"}}}); }} }} 
				{{ sport = company.Tournaments.Sports.FindById({sportId}); catalog = company.Tournaments.Catalog(sport); questionId = 9; question = catalog.CreateTierTwoYesNoQuestion(questionId, 'sbh', 'Both Teams to Score in Both Halves'); background = catalog.GetOrCreateBackground('images/bg_lines/soccer/yes-no/yes-no3.jpg'); question.Background = background; If (question.Tier.Name != 'T1') {{ question.AllowPublished({{{"'In play'"}}}); }} }} 
				{{ sport = company.Tournaments.Sports.FindById({sportId}); catalog = company.Tournaments.Catalog(sport); questionId = 10; question = catalog.CreateTierTwoYesNoQuestion(questionId, 'stmg', 'Both Teams to Score Two or More Goals'); background = catalog.GetOrCreateBackground('images/bg_lines/soccer/yes-no/yes-no4.jpg'); question.Background = background; If (question.Tier.Name != 'T1') {{ question.AllowPublished({{{"'In play'"}}}); }} }} 
				{{ sport = company.Tournaments.Sports.FindById({sportId}); catalog = company.Tournaments.Catalog(sport); questionId = 11; question = catalog.CreateTierOneMoneyDrawQuestion(questionId, 'mdq'); background = catalog.GetOrCreateBackground('images/bg_lines/soccer/money_total_spread/spread1.jpg'); question.Background = background; }} 
				{{ sport = company.Tournaments.Sports.FindById({sportId}); catalog = company.Tournaments.Catalog(sport); questionId = 12; question = catalog.CreateTierTwoMoneyDrawQuestion(questionId, 'mdqq'); background = catalog.GetOrCreateBackground('images/bg_lines/soccer/money_total_spread/spread2.jpg'); question.Background = background; If (question.Tier.Name != 'T1') {{ question.AllowPublished({{{"'Pregame', 'In play', 'First half', 'Second half'"}}}); }} }} 
			");

			return this;
		}

		internal LinesMockGenerator CreateLinesForInPlaySoccerGames(int tournamentId, int gameNumber, DateTime dateWithTime)
		{
			Random random = new Random();
			Perform($@"
				{{ tournament = company.Tournaments.FindById({tournamentId}); game = tournament.GetGameNumber({gameNumber}); betBoard1 = company.Betboard(tournament); catalog = betBoard1.Catalog; question = catalog.FindQuestionById(1); lineId = betBoard1.NextLineId(); matchDay = betBoard1.Matchday({dateWithTime.Month}/{dateWithTime.Day}/{dateWithTime.Year}); shelve = matchDay.GetShowcase(game).InsertShelve(1); question.SetParameters(0.5, {random.Next(101, 500)}, {random.Next(-500, -101)}); newLine = shelve.CreateLine(lineId, question, 'N/A', Now); newLine.Publish(itIsThePresent, Now); }} 
				{{ tournament = company.Tournaments.FindById({tournamentId}); game = tournament.GetGameNumber({gameNumber}); betBoard1 = company.Betboard(tournament); catalog = betBoard1.Catalog; question = catalog.FindQuestionById(2); lineId = betBoard1.NextLineId(); matchDay = betBoard1.Matchday({dateWithTime.Month}/{dateWithTime.Day}/{dateWithTime.Year}); shelve = matchDay.GetShowcase(game).InsertShelve(1); question.SetParameters(1.5, {random.Next(101, 500)}, {random.Next(-500, -101)}); newLine = shelve.CreateLine(lineId, question, 'N/A', Now); newLine.Publish(itIsThePresent, Now); }} 
				{{ tournament = company.Tournaments.FindById({tournamentId}); game = tournament.GetGameNumber({gameNumber}); betBoard1 = company.Betboard(tournament); catalog = betBoard1.Catalog; question = catalog.FindQuestionById(3); lineId = betBoard1.NextLineId(); matchDay = betBoard1.Matchday({dateWithTime.Month}/{dateWithTime.Day}/{dateWithTime.Year}); shelve = matchDay.GetShowcase(game).InsertShelve(1); question.SetParameters(2.5, {random.Next(101, 500)}, {random.Next(-500, -101)}); newLine = shelve.CreateLine(lineId, question, 'N/A', Now); newLine.Publish(itIsThePresent, Now); }} 
				{{ tournament = company.Tournaments.FindById({tournamentId}); game = tournament.GetGameNumber({gameNumber}); betBoard1 = company.Betboard(tournament); catalog = betBoard1.Catalog; question = catalog.FindQuestionById(4); lineId = betBoard1.NextLineId(); matchDay = betBoard1.Matchday({dateWithTime.Month}/{dateWithTime.Day}/{dateWithTime.Year}); shelve = matchDay.GetShowcase(game).InsertShelve(1); question.SetParameters(3.5, {random.Next(101, 500)}, {random.Next(-500, -101)}); newLine = shelve.CreateLine(lineId, question, 'N/A', Now); newLine.Publish(itIsThePresent, Now); }} 
				{{ tournament = company.Tournaments.FindById({tournamentId}); game = tournament.GetGameNumber({gameNumber}); betBoard1 = company.Betboard(tournament); catalog = betBoard1.Catalog; question = catalog.FindQuestionById(5); lineId = betBoard1.NextLineId(); matchDay = betBoard1.Matchday({dateWithTime.Month}/{dateWithTime.Day}/{dateWithTime.Year}); shelve = matchDay.GetShowcase(game).InsertShelve(1); question.SetParameters(4.5, {random.Next(101, 500)}, {random.Next(-500, -101)}); newLine = shelve.CreateLine(lineId, question, 'N/A', Now); newLine.Publish(itIsThePresent, Now); }} 
				{{ tournament = company.Tournaments.FindById({tournamentId}); game = tournament.GetGameNumber({gameNumber}); betBoard1 = company.Betboard(tournament); catalog = betBoard1.Catalog; question = catalog.FindQuestionById(6); lineId = betBoard1.NextLineId(); matchDay = betBoard1.Matchday({dateWithTime.Month}/{dateWithTime.Day}/{dateWithTime.Year}); shelve = matchDay.GetShowcase(game).InsertShelve(1); question.SetParameters(5.5, {random.Next(101, 500)}, {random.Next(-500, -101)}); newLine = shelve.CreateLine(lineId, question, 'N/A', Now); newLine.Publish(itIsThePresent, Now); }} 
				{{ tournament = company.Tournaments.FindById({tournamentId}); game = tournament.GetGameNumber({gameNumber}); betBoard1 = company.Betboard(tournament); catalog = betBoard1.Catalog; question = catalog.FindQuestionById(7); lineId = betBoard1.NextLineId(); matchDay = betBoard1.Matchday({dateWithTime.Month}/{dateWithTime.Day}/{dateWithTime.Year}); shelve = matchDay.GetShowcase(game).InsertShelve(1); question.SetParameters({random.Next(101, 500)}, {random.Next(-500, -101)}); newLine = shelve.CreateLine(lineId, question, 'N/A', Now); newLine.Publish(itIsThePresent, Now); }} 
				{{ tournament = company.Tournaments.FindById({tournamentId}); game = tournament.GetGameNumber({gameNumber}); betBoard1 = company.Betboard(tournament); catalog = betBoard1.Catalog; question = catalog.FindQuestionById(8); lineId = betBoard1.NextLineId(); matchDay = betBoard1.Matchday({dateWithTime.Month}/{dateWithTime.Day}/{dateWithTime.Year}); shelve = matchDay.GetShowcase(game).InsertShelve(1); question.SetParameters({random.Next(101, 500)}, {random.Next(-500, -101)}); newLine = shelve.CreateLine(lineId, question, 'N/A', Now); newLine.Publish(itIsThePresent, Now); }} 
				{{ tournament = company.Tournaments.FindById({tournamentId}); game = tournament.GetGameNumber({gameNumber}); betBoard1 = company.Betboard(tournament); catalog = betBoard1.Catalog; question = catalog.FindQuestionById(9); lineId = betBoard1.NextLineId(); matchDay = betBoard1.Matchday({dateWithTime.Month}/{dateWithTime.Day}/{dateWithTime.Year}); shelve = matchDay.GetShowcase(game).InsertShelve(1); question.SetParameters({random.Next(101, 500)}, {random.Next(-500, -101)}); newLine = shelve.CreateLine(lineId, question, 'N/A', Now); newLine.Publish(itIsThePresent, Now); }} 
				{{ tournament = company.Tournaments.FindById({tournamentId}); game = tournament.GetGameNumber({gameNumber}); betBoard1 = company.Betboard(tournament); catalog = betBoard1.Catalog; question = catalog.FindQuestionById(10); lineId = betBoard1.NextLineId(); matchDay = betBoard1.Matchday({dateWithTime.Month}/{dateWithTime.Day}/{dateWithTime.Year}); shelve = matchDay.GetShowcase(game).InsertShelve(1); question.SetParameters({random.Next(101, 500)}, {random.Next(-500, -101)}); newLine = shelve.CreateLine(lineId, question, 'N/A', Now); newLine.Publish(itIsThePresent, Now); }} 
			");

			return this;
		}

		internal LinesMockGenerator CreateLinesForPregameSoccerGames(int tournamentId, int gameNumber, DateTime dateWithTime)
		{
			Random random = new Random();
			Perform($@"
				{{ tournament = company.Tournaments.FindById({tournamentId}); game = tournament.GetGameNumber({gameNumber}); betBoard1 = company.Betboard(tournament); catalog = betBoard1.Catalog; question = catalog.FindQuestionById(11); lineId = betBoard1.NextLineId(); matchDay = betBoard1.Matchday({dateWithTime.Month}/{dateWithTime.Day}/{dateWithTime.Year}); shelve = matchDay.GetShowcase(game).InsertShelve(1); question.SetParameters({random.Next(101, 500)}, {random.Next(-500, -101)}, {random.Next(101, 500)}); newLine = shelve.CreateLine(lineId, question, 'N/A', Now); newLine.Publish(itIsThePresent, Now); }} 
			");

			return this;
		}

		internal LinesMockGenerator CreateQuestionsForBasketballGames(int sportId)
		{
			Perform($@"
				{{ sport = company.Tournaments.Sports.FindById({sportId}); catalog = company.Tournaments.Catalog(sport); questionId = 13; question = catalog.CreateTierTwoYesNoQuestion(questionId, 'go', 'Will The Game Go To Overtime'); background = catalog.GetOrCreateBackground('images/bg_lines/basketball/yes-no/yes-no1.jpg'); question.Background = background; If (question.Tier.Name != 'T1') {{ question.AllowPublished({{{"'In play', 'First quarter', 'Second quarter', 'Third quarter'"}}}); }} }} 
				{{ sport = company.Tournaments.Sports.FindById({sportId}); catalog = company.Tournaments.Catalog(sport); questionId = 14; question = catalog.CreateTierTwoYesNoQuestion(questionId, 'sfq', 'Both Teams to Score First Quarter'); background = catalog.GetOrCreateBackground('images/bg_lines/basketball/yes-no/yes-no1.jpg'); question.Background = background; If (question.Tier.Name != 'T1') {{ question.AllowPublished({{{"'In play'"}}}); }} }} 
				{{ sport = company.Tournaments.Sports.FindById({sportId}); catalog = company.Tournaments.Catalog(sport); questionId = 15; question = catalog.CreateTierTwoYesNoQuestion(questionId, 'ssq', 'Both Teams to Score Second Quarter'); background = catalog.GetOrCreateBackground('images/bg_lines/basketball/yes-no/yes-no2.jpg'); question.Background = background; If (question.Tier.Name != 'T1') {{ question.AllowPublished({{{"'In play','First quarter'"}}}); }} }} 
				{{ sport = company.Tournaments.Sports.FindById({sportId}); catalog = company.Tournaments.Catalog(sport); questionId = 16; question = catalog.CreateTierTwoYesNoQuestion(questionId, 'stq', 'Both Teams to Score ThridQuarter'); background = catalog.GetOrCreateBackground('images/bg_lines/basketball/yes-no/yes-no3.jpg'); question.Background = background; If (question.Tier.Name != 'T1') {{ question.AllowPublished({{{"'In play', 'First quarter', 'Second quarter'"}}}); }} }} 
				{{ sport = company.Tournaments.Sports.FindById({sportId}); catalog = company.Tournaments.Catalog(sport); questionId = 17; question = catalog.CreateTierTwoYesNoQuestion(questionId, 'spfq', 'Both Teams to Score Fourth Quarter'); background = catalog.GetOrCreateBackground('images/bg_lines/basketball/yes-no/yes-no4.jpg'); question.Background = background; If (question.Tier.Name != 'T1') {{ question.AllowPublished({{{"'In play', 'First quarter', 'Second quarter', 'Third quarter'"}}}); }} }} 
				{{ sport = company.Tournaments.Sports.FindById({sportId}); catalog = company.Tournaments.Catalog(sport); questionId = 18; question = catalog.CreateTierTwoOverUnderQuestion(questionId, 'ou35', 'Over/Under 35 Points First Quarter'); background = catalog.GetOrCreateBackground('images/bg_lines/soccer/over-under/over-under1.jpg'); question.Background = background; If (question.Tier.Name != 'T1') {{ question.AllowPublished({{{"'In play'"}}}); }} }} 
				{{ sport = company.Tournaments.Sports.FindById({sportId}); catalog = company.Tournaments.Catalog(sport); questionId = 19; question = catalog.CreateTierTwoOverUnderQuestion(questionId, 'ou55', 'Over/Under 55 Points First Quarter'); background = catalog.GetOrCreateBackground('images/bg_lines/soccer/over-under/over-under1.jpg'); question.Background = background; If (question.Tier.Name != 'T1') {{ question.AllowPublished({{{"'In play'"}}}); }} }} 
				{{ sport = company.Tournaments.Sports.FindById({sportId}); catalog = company.Tournaments.Catalog(sport); questionId = 20; question = catalog.CreateTierTwoOverUnderQuestion(questionId, 'ou75', 'Over/Under 75 Points Second Quarter'); background = catalog.GetOrCreateBackground('images/bg_lines/soccer/over-under/over-under2.jpg'); question.Background = background; If (question.Tier.Name != 'T1') {{ question.AllowPublished({{{"'In play','First quarter'"}}}); }} }} 
				{{ sport = company.Tournaments.Sports.FindById({sportId}); catalog = company.Tournaments.Catalog(sport); questionId = 21; question = catalog.CreateTierTwoOverUnderQuestion(questionId, 'ou85', 'Over/Under 85 Points Second Quarter'); background = catalog.GetOrCreateBackground('images/bg_lines/soccer/over-under/over-under2.jpg'); question.Background = background; If (question.Tier.Name != 'T1') {{ question.AllowPublished({{{"'In play','First quarter'"}}}); }} }} 
				{{ sport = company.Tournaments.Sports.FindById({sportId}); catalog = company.Tournaments.Catalog(sport); questionId = 22; question = catalog.CreateTierTwoOverUnderQuestion(questionId, 'ou105', 'Over/Under 105 Points Third Quarter'); background = catalog.GetOrCreateBackground('images/bg_lines/soccer/over-under/over-under3.jpg'); question.Background = background; If (question.Tier.Name != 'T1') {{ question.AllowPublished({{{"'In play', 'First quarter', 'Second quarter'"}}}); }} }} 
				{{ sport = company.Tournaments.Sports.FindById({sportId}); catalog = company.Tournaments.Catalog(sport); questionId = 23; question = catalog.CreateTierTwoOverUnderQuestion(questionId, 'ou115', 'Over/Under 115 Points Third Quarter'); background = catalog.GetOrCreateBackground('images/bg_lines/soccer/over-under/over-under3.jpg'); question.Background = background; If (question.Tier.Name != 'T1') {{ question.AllowPublished({{{"'In play', 'First quarter', 'Second quarter'"}}}); }} }} 
				{{ sport = company.Tournaments.Sports.FindById({sportId}); catalog = company.Tournaments.Catalog(sport); questionId = 24; question = catalog.CreateTierTwoOverUnderQuestion(questionId, 'ou135', 'Over/Under 135 Points Fourth Quarter'); background = catalog.GetOrCreateBackground('images/bg_lines/soccer/over-under/over-under4.jpg'); question.Background = background; If (question.Tier.Name != 'T1') {{ question.AllowPublished({{{"'In play', 'First quarter', 'Second quarter', 'Third quarter'"}}}); }} }} 
				{{ sport = company.Tournaments.Sports.FindById({sportId}); catalog = company.Tournaments.Catalog(sport); questionId = 25; question = catalog.CreateTierTwoOverUnderQuestion(questionId, 'ou145', 'Over/Under 145 Points Fourth Quarter'); background = catalog.GetOrCreateBackground('images/bg_lines/soccer/over-under/over-under4.jpg'); question.Background = background; If (question.Tier.Name != 'T1') {{ question.AllowPublished({{{"'In play', 'First quarter', 'Second quarter', 'Third quarter'"}}}); }} }} 
		");

			return this;
		}

		internal LinesMockGenerator CreateLinesForBasketballGames(int tournamentId, int gameNumber, DateTime dateWithTime)
		{
			Random random = new Random();
			Perform($@" 
				{{ tournament = company.Tournaments.FindById({tournamentId}); game = tournament.GetGameNumber({gameNumber}); betBoard = company.Betboard(tournament); catalog = betBoard.Catalog; question = catalog.FindQuestionById(13); lineId = betBoard.NextLineId(); matchDay = betBoard.Matchday({dateWithTime.Month}/{dateWithTime.Day}/{dateWithTime.Year}); shelve = matchDay.GetShowcase(game).InsertShelve(1); question.SetParameters({random.Next(101, 500)}, {random.Next(-500, -101)}); newLine = shelve.CreateLine(lineId, question, 'N/A', Now); newLine.Publish(itIsThePresent, Now); }} 
				{{ tournament = company.Tournaments.FindById({tournamentId}); game = tournament.GetGameNumber({gameNumber}); betBoard = company.Betboard(tournament); catalog = betBoard.Catalog; question = catalog.FindQuestionById(14); lineId = betBoard.NextLineId(); matchDay = betBoard.Matchday({dateWithTime.Month}/{dateWithTime.Day}/{dateWithTime.Year}); shelve = matchDay.GetShowcase(game).InsertShelve(1); question.SetParameters({random.Next(101, 500)}, {random.Next(-500, -101)}); newLine = shelve.CreateLine(lineId, question, 'N/A', Now); newLine.Publish(itIsThePresent, Now); }} 
				{{ tournament = company.Tournaments.FindById({tournamentId}); game = tournament.GetGameNumber({gameNumber}); betBoard = company.Betboard(tournament); catalog = betBoard.Catalog; question = catalog.FindQuestionById(15); lineId = betBoard.NextLineId(); matchDay = betBoard.Matchday({dateWithTime.Month}/{dateWithTime.Day}/{dateWithTime.Year}); shelve = matchDay.GetShowcase(game).InsertShelve(1); question.SetParameters({random.Next(101, 500)}, {random.Next(-500, -101)}); newLine = shelve.CreateLine(lineId, question, 'N/A', Now); newLine.Publish(itIsThePresent, Now); }} 
				{{ tournament = company.Tournaments.FindById({tournamentId}); game = tournament.GetGameNumber({gameNumber}); betBoard = company.Betboard(tournament); catalog = betBoard.Catalog; question = catalog.FindQuestionById(16); lineId = betBoard.NextLineId(); matchDay = betBoard.Matchday({dateWithTime.Month}/{dateWithTime.Day}/{dateWithTime.Year}); shelve = matchDay.GetShowcase(game).InsertShelve(1); question.SetParameters({random.Next(101, 500)}, {random.Next(-500, -101)}); newLine = shelve.CreateLine(lineId, question, 'N/A', Now); newLine.Publish(itIsThePresent, Now); }} 
				{{ tournament = company.Tournaments.FindById({tournamentId}); game = tournament.GetGameNumber({gameNumber}); betBoard = company.Betboard(tournament); catalog = betBoard.Catalog; question = catalog.FindQuestionById(17); lineId = betBoard.NextLineId(); matchDay = betBoard.Matchday({dateWithTime.Month}/{dateWithTime.Day}/{dateWithTime.Year}); shelve = matchDay.GetShowcase(game).InsertShelve(1); question.SetParameters({random.Next(101, 500)}, {random.Next(-500, -101)}); newLine = shelve.CreateLine(lineId, question, 'N/A', Now); newLine.Publish(itIsThePresent, Now); }} 
				{{ tournament = company.Tournaments.FindById({tournamentId}); game = tournament.GetGameNumber({gameNumber}); betBoard = company.Betboard(tournament); catalog = betBoard.Catalog; question = catalog.FindQuestionById(18); lineId = betBoard.NextLineId(); matchDay = betBoard.Matchday({dateWithTime.Month}/{dateWithTime.Day}/{dateWithTime.Year}); shelve = matchDay.GetShowcase(game).InsertShelve(1); question.SetParameters(35, {random.Next(101, 500)}, {random.Next(-500, -101)}); newLine = shelve.CreateLine(lineId, question, 'N/A', Now); newLine.Publish(itIsThePresent, Now); }} 
				{{ tournament = company.Tournaments.FindById({tournamentId}); game = tournament.GetGameNumber({gameNumber}); betBoard = company.Betboard(tournament); catalog = betBoard.Catalog; question = catalog.FindQuestionById(19); lineId = betBoard.NextLineId(); matchDay = betBoard.Matchday({dateWithTime.Month}/{dateWithTime.Day}/{dateWithTime.Year}); shelve = matchDay.GetShowcase(game).InsertShelve(1); question.SetParameters(55, {random.Next(101, 500)}, {random.Next(-500, -101)}); newLine = shelve.CreateLine(lineId, question, 'N/A', Now); newLine.Publish(itIsThePresent, Now); }} 
				{{ tournament = company.Tournaments.FindById({tournamentId}); game = tournament.GetGameNumber({gameNumber}); betBoard = company.Betboard(tournament); catalog = betBoard.Catalog; question = catalog.FindQuestionById(20); lineId = betBoard.NextLineId(); matchDay = betBoard.Matchday({dateWithTime.Month}/{dateWithTime.Day}/{dateWithTime.Year}); shelve = matchDay.GetShowcase(game).InsertShelve(1); question.SetParameters(75, {random.Next(101, 500)}, {random.Next(-500, -101)}); newLine = shelve.CreateLine(lineId, question, 'N/A', Now); newLine.Publish(itIsThePresent, Now); }} 
				{{ tournament = company.Tournaments.FindById({tournamentId}); game = tournament.GetGameNumber({gameNumber}); betBoard = company.Betboard(tournament); catalog = betBoard.Catalog; question = catalog.FindQuestionById(21); lineId = betBoard.NextLineId(); matchDay = betBoard.Matchday({dateWithTime.Month}/{dateWithTime.Day}/{dateWithTime.Year}); shelve = matchDay.GetShowcase(game).InsertShelve(1); question.SetParameters(85, {random.Next(101, 500)}, {random.Next(-500, -101)}); newLine = shelve.CreateLine(lineId, question, 'N/A', Now); newLine.Publish(itIsThePresent, Now); }} 
				{{ tournament = company.Tournaments.FindById({tournamentId}); game = tournament.GetGameNumber({gameNumber}); betBoard = company.Betboard(tournament); catalog = betBoard.Catalog; question = catalog.FindQuestionById(22); lineId = betBoard.NextLineId(); matchDay = betBoard.Matchday({dateWithTime.Month}/{dateWithTime.Day}/{dateWithTime.Year}); shelve = matchDay.GetShowcase(game).InsertShelve(1); question.SetParameters(105, {random.Next(101, 500)}, {random.Next(-500, -101)}); newLine = shelve.CreateLine(lineId, question, 'N/A', Now); newLine.Publish(itIsThePresent, Now); }}
				{{ tournament = company.Tournaments.FindById({tournamentId}); game = tournament.GetGameNumber({gameNumber}); betBoard = company.Betboard(tournament); catalog = betBoard.Catalog; question = catalog.FindQuestionById(23); lineId = betBoard.NextLineId(); matchDay = betBoard.Matchday({dateWithTime.Month}/{dateWithTime.Day}/{dateWithTime.Year}); shelve = matchDay.GetShowcase(game).InsertShelve(1); question.SetParameters(115, {random.Next(101, 500)}, {random.Next(-500, -101)}); newLine = shelve.CreateLine(lineId, question, 'N/A', Now); newLine.Publish(itIsThePresent, Now); }} 
				{{ tournament = company.Tournaments.FindById({tournamentId}); game = tournament.GetGameNumber({gameNumber}); betBoard = company.Betboard(tournament); catalog = betBoard.Catalog; question = catalog.FindQuestionById(24); lineId = betBoard.NextLineId(); matchDay = betBoard.Matchday({dateWithTime.Month}/{dateWithTime.Day}/{dateWithTime.Year}); shelve = matchDay.GetShowcase(game).InsertShelve(1); question.SetParameters(135, {random.Next(101, 500)}, {random.Next(-500, -101)}); newLine = shelve.CreateLine(lineId, question, 'N/A', Now); newLine.Publish(itIsThePresent, Now); }} 
				{{ tournament = company.Tournaments.FindById({tournamentId}); game = tournament.GetGameNumber({gameNumber}); betBoard = company.Betboard(tournament); catalog = betBoard.Catalog; question = catalog.FindQuestionById(25); lineId = betBoard.NextLineId(); matchDay = betBoard.Matchday({dateWithTime.Month}/{dateWithTime.Day}/{dateWithTime.Year}); shelve = matchDay.GetShowcase(game).InsertShelve(1); question.SetParameters(145, {random.Next(101, 500)}, {random.Next(-500, -101)}); newLine = shelve.CreateLine(lineId, question, 'N/A', Now); newLine.Publish(itIsThePresent, Now); }} 
			");

			return this;
		}

		internal LinesMockGenerator CreateLine(int tournamentId, int gameNumber, QuestionType lineType, string question, bool publish)
		{
			bool itIsThePresent = false;
			Random rnd = new Random();
			var now = DateTime.Now;
			int teamAReward = rnd.Next(101, 199);
			int teamBReward = rnd.Next(-160, -101);
			int tieReward = rnd.Next(101, 150);
			int overReward = rnd.Next(101, 160);
			int underReward = rnd.Next(-180, -101);
			int spread = rnd.Next(1, 9999);
			decimal score = 2.00m;
			int noReward = rnd.Next(-140, -101);
			int yesReward = rnd.Next(101, 130);
			string scriptForLineCreation = string.Empty;
			var dayAsText = $"{now.Month}/{now.Day}/{now.Year}";
			int fila1 = 0;

			string periods = "'Pregame','In play','First quarter','Second quarter','Third quarter','Fourth quarter'";

			switch (lineType)
			{
				case QuestionType.MoneyQuestion:
					scriptForLineCreation = $"questionId = catalog.NextQuestionId();" +
						$"question = catalog.CreateTierOneMoneyQuestion(questionId, 'mm');" +
						"background = catalog.GetOrCreateBackground('images/bg_lines/basketball/money_total_spread/win-black1.jpg');" +
						"question.Background = background;" +
						$"question.SetParameters({teamAReward}, {teamBReward});" +
						$"newLine = shelve.CreateLine(lineId, question, 'c' +questionId , Now);" +
						$"if ({publish})" +
						$"{{ " +
							$"newLine.Publish({itIsThePresent}, Now);" +
						$"}}";
					break;
				case QuestionType.MoneyDrawQuestion:
					scriptForLineCreation = $"questionId = catalog.NextQuestionId();" +
						$"question = catalog.CreateTierOneMoneyDrawQuestion(questionId, 'dd');" +
						"background = catalog.GetOrCreateBackground('images/bg_lines/basketball/money_total_spread/win-black2.jpg');" +
						"question.Background = background;" +
						$"question.SetParameters({teamAReward}, {tieReward}, {teamBReward});" +
						$"newLine = shelve.CreateLine(lineId, question, 'c' +questionId , Now);" +
						$"if ({publish})" +
						$"{{ " +
							$"newLine.Publish({itIsThePresent},Now);" +
						$"}}";
					break;
				case QuestionType.SpreadQuestion:
					scriptForLineCreation = $"questionId = catalog.NextQuestionId();" +
						$"question = catalog.CreateTierOneSpreadQuestion(questionId, 'ss');" +
						"background = catalog.GetOrCreateBackground('images/bg_lines/basketball/money_total_spread/win-black3.jpg');" +
						"question.Background = background;" +
						$"question.SetParameters({spread}, {teamAReward}, {teamBReward});" +
						$"newLine = shelve.CreateLine(lineId, question, 'c' +questionId, Now);" +
						$"if ({publish})" +
						$"{{ " +
							$"newLine.Publish({itIsThePresent},Now);" +
						$"}}";
					break;
				case QuestionType.TotalPointsQuestion:
					scriptForLineCreation = $"questionId = catalog.NextQuestionId();" +
						$"question = catalog.CreateTierOneTotalPointsQuestion(questionId, 'tt');" +
						"background = catalog.GetOrCreateBackground('images/bg_lines/basketball/money_total_spread/win-black4.jpg');" +
						"question.Background = background;" +
						$"question.SetParameters({score}, {overReward}, {underReward});" +
						$"newLine = shelve.CreateLine(lineId, question, 'c' +questionId, Now);" +
						$"if ({publish})" +
						$"{{ " +
							$"newLine.Publish({itIsThePresent},Now);" +
						$"}}";
					break;
				case QuestionType.YesNoQuestion:
					scriptForLineCreation = $"questionId = catalog.NextQuestionId();" +
						$"question = catalog.CreateTierTwoYesNoQuestion(questionId, 'WW'+questionId, '{question}');" +
						"background = catalog.GetOrCreateBackground('images/bg_lines/basketball/yes-no/yes-no1.jpg');" +
						"question.Background = background;" +
						$"question.SetParameters({yesReward}, {noReward});" +
						$"question.AllowPublished({{{periods}}});" +
						$"newLine = shelve.CreateLine(lineId, question, 'ww' +questionId, Now);" +
						$"if ({publish})" +
						$"{{ " +
							$"newLine.Publish({itIsThePresent},Now);" +
						$"}}";
					break;
				case QuestionType.OverUnderQuestion:
					scriptForLineCreation = $"questionId = catalog.NextQuestionId();" +
						$"question = catalog.CreateTierTwoOverUnderQuestion(questionId, 'CF'+questionId, '{question}');" +
						"background = catalog.GetOrCreateBackground('images/bg_lines/basketball/over-under/over-under1.jpg');" +
						"question.Background = background;" +
						$"question.SetParameters({score}, {overReward}, {underReward});" +
						$"question.AllowPublished({{{periods}}});" +
						$"newLine = shelve.CreateLine(lineId, question, 'cf' +questionId, Now);" +
						$"if ({publish})" +
						$"{{ " +
							$"newLine.Publish({itIsThePresent},Now);" +
						$"}}";
					break;
				case QuestionType.FixedQuestion:
					var optionsandRewards = new List<OptionBody> { new OptionBody { Option = "John", Reward = -125 }, new OptionBody { Option = "Peter", Reward = 114 }, new OptionBody { Option = "Carl", Reward = 110 } };
					var options = string.Join(",", optionsandRewards.Select(option => $"'{option.Option}'").ToArray());
					var rewards = string.Join(",", optionsandRewards.Select(option => $"{option.Reward}").ToArray());
					scriptForLineCreation = $"questionId = catalog.NextQuestionId();" +
					$"question = catalog.CreateTierTwoFixedQuestion(questionId, 'fx'+questionId, '{question}');" +
					"background = catalog.GetOrCreateBackground('images/bg_lines/basketball/fixed/fixed1.jpg');" +
					"question.Background = background;" +
					$"question.SetParameters({{{options}}}, {{{rewards}}});" +
					$"question.AllowPublished({{{periods}}});" +
					$"newLine = shelve.CreateLine(lineId, question, 'fx' +questionId, Now);" +
					$"if ({publish})" +
					$"{{ " +
						$"newLine.Publish({itIsThePresent},Now);" +
					$"}}";
					break;
				default:
					throw new GameEngineException("case not supported");
			}

			var scriptForDraftOrPublish = publish ? "newLine.AllowToPublishOn(game.CurrentPeriod);" +
				"newLine.Publish(Now);" : string.Empty;

			Perform($@"
				tournament = company.Tournaments.FindById({tournamentId});
				game = tournament.GetGameNumber({gameNumber});
				betBoard = company.Betboard(tournament);
				catalog = betBoard.Catalog;
				lineId = betBoard.NextLineId();
				matchDay = betBoard.Matchday({dayAsText});
				shelve = matchDay.GetShowcase(game).InsertShelve({fila1});
				{scriptForLineCreation}
			");

			return this;
		}

		internal LinesMockGenerator StartSpecificManyGame(int tournamentId, int gameNumber)
		{
			Perform($@"
					{{
						tournament = company.Tournaments.FindById({tournamentId});
						game = tournament.GetGameNumber({gameNumber});
						game.MoveToTheNextPeriod(itIsThePresent, 'Begin', Now);
					}}
			");

			return this;
		}

		internal LinesMockGenerator StartManyGames()
		{
			Perform($@"
					{{
						tournament = company.Tournaments.FindById(1);
						game = tournament.GetGameNumber(11);
						game.MoveToTheNextPeriod(itIsThePresent, 'Begin', Now);
					}}

					{{
						tournament = company.Tournaments.FindById(1);
						game = tournament.GetGameNumber(12);
						game.MoveToTheNextPeriod(itIsThePresent, 'Begin', Now);
					}}

					{{
						tournament = company.Tournaments.FindById(1);
						game = tournament.GetGameNumber(13);
						game.MoveToTheNextPeriod(itIsThePresent, 'Begin', Now);
					}}

					{{
						tournament = company.Tournaments.FindById(1);
						game = tournament.GetGameNumber(14);
						game.MoveToTheNextPeriod(itIsThePresent, 'Begin', Now);
					}}

					{{
						tournament = company.Tournaments.FindById(1);
						game = tournament.GetGameNumber(15);
						game.MoveToTheNextPeriod(itIsThePresent, 'Begin', Now);
					}}
			");

			return this;
		}


	}
}

