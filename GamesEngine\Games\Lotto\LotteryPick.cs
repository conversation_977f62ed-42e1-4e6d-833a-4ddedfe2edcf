﻿using GamesEngine.Location;
using GamesEngine.Time;
using System;
using System.Linq;
using System.Collections.Generic;
using System.Text;
using GamesEngine.Bets;
using GamesEngine.Business;
using static GamesEngine.Gameboards.Lotto.Ticket;
using GamesEngine.Gameboards.Lotto;
using static GamesEngine.Games.Lotto.TicketsOfDraw;
using GamesEngine.RealTime.Events;
using GamesEngine.RealTime;
using GamesEngine.Settings;

namespace GamesEngine.Games.Lotto
{
    internal class LotteryPick<TPick> : LotteryForPicks where TPick:IPick
    {
        internal LotteryPick(LotteryGame lotteries, State state) : base(lotteries, state) { }

        private LotteryPickWithFireBall<TPick> _fireBallInstance;
        private LotteryPickWithFireBall<TPick> _fireBallInstanceForPendingDraws;

        private LotteryPickWithFireBall<TPick> ActiveFireBallInstance()
        {
            if (_fireBallInstance != null) return _fireBallInstance;
            if (_fireBallInstanceForPendingDraws != null) return _fireBallInstanceForPendingDraws;
            return null;
        }

        internal LotteryPickWithFireBall<TPick> FireBallLottery
        {
            get
            {
                var result = ActiveFireBallInstance();
                if (result == null) throw new GameEngineException($"Lottery in State {State.Name} and Pick {IdOfLottery} does not have Fireball created.");
                return result;
            }
        }

        internal override bool IsLotteryPickWithFireBall => false;

        internal override bool IsFireBallTurnedOn => _fireBallInstance != null;

        internal override bool HasFireBall => ActiveFireBallInstance() != null;

        internal override List<PayFragmentsMessage> ResendToExternalAccounting(DateTime drawDate, DateTime now, bool itIsThePresent, string employeeName)
        {
            List<PayFragmentsMessage> result = base.ResendToExternalAccounting(drawDate, now, itIsThePresent, employeeName);

            if (!IsLotteryPickWithFireBall)
            {
                if (HasFireBall)
                {
                    List<PayFragmentsMessage> resultWithFireBall = ActiveFireBallInstance().ResendToExternalAccounting(drawDate, now, itIsThePresent, employeeName);
                    result.AddRange(resultWithFireBall);
                }
            }

            return result;
        }

        internal override List<PayFragmentsMessage> CreateMessagesToResendDraw(DateTime date, DateTime now)
        {
            List<PayFragmentsMessage> result = base.CreateMessagesToResendDraw(date, now);

            if (!IsLotteryPickWithFireBall)
            {
                if (HasFireBall)
                {
                    List<PayFragmentsMessage> resultWithFireBall = ActiveFireBallInstance().CreateMessagesToResendDraw(date, now);
                    result.AddRange(resultWithFireBall);
                }
            }

            return result;
        }

        internal override bool ExistsPostedDrawInMemory(DateTime drawDate)
        {
            bool result = base.ExistsPostedDrawInMemory(drawDate);

            if (!IsLotteryPickWithFireBall)
            {
                if (HasFireBall)
                {
                    bool resultWithFireBall = ActiveFireBallInstance().ExistsPostedDrawInMemory(drawDate);
                    result = result || resultWithFireBall;
                }
            }

            return result;
        }

        internal override bool WasAlreadyConfirmed(DateTime drawDate)
        {
            bool result = base.WasAlreadyConfirmed(drawDate);

            if (!IsLotteryPickWithFireBall)
            {
                if (HasFireBall)
                {
                    bool resultWithFireBall = ActiveFireBallInstance().WasAlreadyConfirmed(drawDate);
                    result = result || resultWithFireBall;
                }
            }

            return result;
        }

        internal override IEnumerable<LotteryDraw> DrawnLotteriesBetween(IEnumerable<DateTime> dates)
        {
            IEnumerable<LotteryDraw> result = base.DrawnLotteriesBetween(dates);

            if (!IsLotteryPickWithFireBall)
            {
                if (HasFireBall)
                {
                    IEnumerable<LotteryDraw> resultWithFireBall = ActiveFireBallInstance().DrawnLotteriesBetween(dates);
                    result = result.Concat(resultWithFireBall);
                }
            }

            return result;
        }

        internal override IEnumerable<LotteryDraw> DrawnLotteriesBetween(IEnumerable<DateTime> dates, int hour, int minute)
        {
            IEnumerable<LotteryDraw> result = base.DrawnLotteriesBetween(dates, hour, minute);

            if (!IsLotteryPickWithFireBall)
            {
                if (HasFireBall)
                {
                    IEnumerable<LotteryDraw> resultWithFireBall = ActiveFireBallInstance().DrawnLotteriesBetween(dates, hour, minute);
                    result = result.Concat(resultWithFireBall);
                }
            }

            return result;
        }

        internal override IEnumerable<Ticket> TicketsOfDrawAt(DateTime drawDate)
        {
            IEnumerable<Ticket> result = base.TicketsOfDrawAt(drawDate);

            if (!IsLotteryPickWithFireBall)
            {
                if (HasFireBall)
                {
                    IEnumerable<Ticket> resultWithFireBall = ActiveFireBallInstance().TicketsOfDrawAt(drawDate);
                    result = result.Concat(resultWithFireBall);
                }
            }

            return result;
        }

        internal override TicketsOfDraw TicketInMemory(DateTime date)
        {
            TicketsOfDraw result = new TicketsOfDraw(this, date);

            TicketsOfDraw ticketsOfDraw = base.TicketInMemory(date);

            if (ticketsOfDraw != null) foreach (var ticket in ticketsOfDraw.Tickets) result.AddTicket(ticket);

            if (!IsLotteryPickWithFireBall)
            {
                if (HasFireBall)
                {
                    ticketsOfDraw = ActiveFireBallInstance().TicketInMemory(date);
                    if (ticketsOfDraw != null) foreach (var ticket in ticketsOfDraw.Tickets) result.AddTicket(ticket);
                }
            }
            return result;
        }

        internal IEnumerable<PendingDraw> PendingDrawsBy(string domainIds)
        {
            var result = Tickets.PendingDrawsBy(domainIds);
            if (!IsLotteryPickWithFireBall)
            {
                if (HasFireBall)
                {
                    result = result.Concat(ActiveFireBallInstance().PendingDrawsBy(domainIds));
                }
            }
            return result;
        }

        internal override IEnumerable<Ticket> FindTicketsMatchingWith(IEnumerable<int> ticketNumbers)
        {
            var result = base.FindTicketsMatchingWith(ticketNumbers);
            if (!IsLotteryPickWithFireBall)
            {
                if (HasFireBall)
                {
                    var resultWithFireBall = ActiveFireBallInstance().FindTicketsMatchingWith(ticketNumbers);
                    result = result.Concat(resultWithFireBall);
                }
            }
            return result;
        }

        internal bool AreAllPendingTickets(int ticketNumber)
        {
            if (ticketNumber <= 0) throw new GameEngineException($"{nameof(ticketNumber)} {ticketNumber} must be greater than 0");

            var ticketsFound = Tickets.SearchTicketByNumber(ticketNumber);

            if (!IsLotteryPickWithFireBall)
            {
                if (HasFireBall)
                {
                    var ticketsFoundWithFireBall = ActiveFireBallInstance().Tickets.SearchTicketByNumber(ticketNumber);
                    ticketsFound = ticketsFound.Concat(ticketsFoundWithFireBall);
                }
            }
            var result = ticketsFound.Count() > 0 && ticketsFound.All(ticket => ticket.IsPending());
            return result;
        }

        internal override IEnumerable<Ticket> SearchTicketByNumber(int ticketNumber)
        {
            var result = base.SearchTicketByNumber(ticketNumber);
            if (!IsLotteryPickWithFireBall)
            {
                if (HasFireBall)
                {
                    var resultWithFireBall = ActiveFireBallInstance().SearchTicketByNumber(ticketNumber);
                    result = result.Concat(resultWithFireBall);
                }
            }
            return result;
        }

        internal override NoActionReport SetNoAction(bool itIsThePresent, DateTime date, DateTime now, string employeeName, bool notifyEvent)
        {
            NoActionReport noActionReport = base.SetNoAction(itIsThePresent, date, now, employeeName, notifyEvent);
            if (!IsLotteryPickWithFireBall)
            {
                if (HasFireBall)
                {
                    NoActionReport noActionReportWithFireBall = ActiveFireBallInstance().SetNoAction(itIsThePresent, date, now, employeeName, notifyEvent);
                    noActionReport.JoinReport(noActionReportWithFireBall);
                }
            }
            return noActionReport;
        }

        internal override DrawingsSummaryReport ConfirmDraw(DateTime drawDate, DateTime now, string employeeName, bool itIsThePresent, bool notifyEvent)
        {
            DrawingsSummaryReport reportPick = base.ConfirmDraw(drawDate, now, employeeName, itIsThePresent, notifyEvent);
            if (!IsLotteryPickWithFireBall)
            {
                if (HasFireBall)
                {
                    LotteryPickWithFireBall<TPick> lotteryPickWithFireBall = ActiveFireBallInstance();
                    bool canBeConfirmed = lotteryPickWithFireBall.CanBeConfirmed(drawDate);
                    if (canBeConfirmed)
                    {
                        DrawingsSummaryReport reportPickWithFireBall = ActiveFireBallInstance().ConfirmDraw(drawDate, now, employeeName, itIsThePresent, notifyEvent);
                        reportPick.JoinReport(reportPickWithFireBall);
                    }
                }
            }
            return reportPick;
        }

        internal override DrawingsSummaryReport Regrade(bool itIsThePresent, DateTime date, DateTime now, string employeeName, bool notifyEvent)
        {
            DrawingsSummaryReport reportPick = base.Regrade(itIsThePresent, date, now, employeeName, notifyEvent);
            if (!IsLotteryPickWithFireBall)
            {
                if (HasFireBall)
                {
                    LotteryPickWithFireBall<TPick> lotteryPickWithFireBall = ActiveFireBallInstance();
                    bool canBeRegraded = lotteryPickWithFireBall.LotteryDraws.IsGraded(date);
                    if (canBeRegraded)
                    {
                        DrawingsSummaryReport reportPickWithFireBall = lotteryPickWithFireBall.Regrade(itIsThePresent, date, now, employeeName, notifyEvent);
                        reportPick.JoinReport(reportPickWithFireBall);
                    }
                }
            }
            return reportPick;
        }

        internal void TurnOnFireBall(bool itIsThePresent, DateTime now, string employeeName)
        {
            if (this is LotteryPickWithFireBall<TPick>) throw new GameEngineException("Cannot make Fireball from a Fireball");
            if (_fireBallInstance != null) throw new GameEngineException("Fireball already created");

            bool isNew = false;
            if (_fireBallInstanceForPendingDraws != null)
            {
                isNew= false;
                _fireBallInstance = _fireBallInstanceForPendingDraws;
                _fireBallInstanceForPendingDraws = null;
            }
            else
            {
                isNew = true;
                _fireBallInstance = new LotteryPickWithFireBall<TPick>(this);
            }

            foreach (var schedule in schedules)
            {
                var lastKnownGrade = _fireBallInstance.LastKnownGradeOrDefault(schedule);
                ((WeeklySchedule)schedule).CreateFireballVersion(itIsThePresent, now, employeeName, lastKnownGrade);
                string previouslyCreated = isNew ? "activates" : "reactives";
                WriteLog(schedule, $"{employeeName} {previouslyCreated} fireball for {State.Abbreviation} at {now} in pick {IdOfLottery}<br>");
            }
        }


        internal void TurnOffFireBall(bool itIsThePresent, DateTime now, string employeeName)
        {
            if (this is LotteryPickWithFireBall<TPick>) throw new GameEngineException("Cannot remove Fireball from a Fireball");
            if (ActiveFireBallInstance() == null) throw new GameEngineException("Fireball does not exists");
            if (_fireBallInstance == null) throw new GameEngineException("Fireball is already Off.");

            _fireBallInstanceForPendingDraws = _fireBallInstance;
            _fireBallInstance = null;
            foreach (var schedule in schedules)
            {
                WriteLog(schedule, $"{employeeName} deactivates fireball for {State.Abbreviation} at {now}<br>");
            }
        }

        private LotteryDraw GradeForPicks(DateTime date, string sequenceOfNumbers, int fireBallNumber, DateTime now, string employeeName)
        {
            LotteryDraw currentLotteryDraw = lotteryDraws.Draw(date, sequenceOfNumbers, fireBallNumber, now, employeeName);
            return currentLotteryDraw;
        }

        internal DrawingsSummaryReport DrawPicks(DateTime date, string sequenceOfNumbers, DateTime now, bool itIsThePresent, string employeeName, bool notifyEvent)
        {
            if (IsFireBallTurnedOn) throw new GameEngineException($"This lottery in state {State.Name} has fireball in pick {IdOfLottery}, assign a fireball number.");

            return DrawPicks(date, sequenceOfNumbers, Lotto.LotteryDraw.WITHOUT_FIREBALL, now, itIsThePresent, employeeName, notifyEvent);
        }

        internal DrawingsSummaryReport DrawPicks(DateTime date, string sequenceOfNumbers, int fireBallNumber, DateTime now, bool itIsThePresent, string employeeName, bool notifyEvent)
        {
            if (String.IsNullOrWhiteSpace(sequenceOfNumbers)) throw new ArgumentNullException(nameof(sequenceOfNumbers));
            if (sequenceOfNumbers.Length != this.PickNumber) throw new GameEngineException($"Invalid {sequenceOfNumbers}.Triz winner must have {this.PickNumber} digits number.");
            if (!sequenceOfNumbers.All(char.IsDigit)) throw new GameEngineException($"Invalid {sequenceOfNumbers}.Triz winner must have {this.PickNumber} digits number.");
            if (string.IsNullOrWhiteSpace(employeeName)) throw new ArgumentNullException(nameof(employeeName));

            Func<LotteryDraw> gradeAction = () => this.GradeForPicks(date, sequenceOfNumbers, Lotto.LotteryDraw.WITHOUT_FIREBALL, now, employeeName);
            DrawingsSummaryReport drawingsSummaryReport = this.ReleaseDraw(gradeAction, date, sequenceOfNumbers, now, itIsThePresent, employeeName, false);

            bool withFireBall = fireBallNumber != Lotto.LotteryDraw.WITHOUT_FIREBALL;
            if (!IsLotteryPickWithFireBall)
            {
                if (withFireBall)
                {
                    if (fireBallNumber < 0 || fireBallNumber > 9) throw new GameEngineException("Invalid fireball number");

                    if (!HasFireBall) throw new GameEngineException($"This lottery in state {State.Name} does not have fireball in pick {IdOfLottery}");
                    Func<LotteryDraw> fireBallGradeAction = () => ActiveFireBallInstance().GradeForPicks(date, sequenceOfNumbers, fireBallNumber, now, employeeName);
                    DrawingsSummaryReport fireBalldrawingsSummaryReport = ActiveFireBallInstance().ReleaseDraw(fireBallGradeAction, date, sequenceOfNumbers, now, itIsThePresent, employeeName, false);
                    drawingsSummaryReport.JoinReport(fireBalldrawingsSummaryReport);
                }
            }

            if (itIsThePresent && Integration.UseKafka && notifyEvent)
            {
                var schedule = FindScheduleAt(date);
                if (withFireBall)
                {
                    Integration.Kafka.Send(itIsThePresent, $"{KafkaMessage.LOTTERY_DRAW_CONSUMER_PREFIX}{Integration.Kafka.TopicForAllGrades}",
                    new LotteryDrawGradeMessage(GetGameType(), state.Abbreviation, date, employeeName, schedule.UniqueId, fireBallNumber, sequenceOfNumbers));
                }
                else
                {
                    Integration.Kafka.Send(itIsThePresent, $"{KafkaMessage.LOTTERY_DRAW_CONSUMER_PREFIX}{Integration.Kafka.TopicForAllGrades}",
                        new LotteryDrawGradeMessage(GetGameType(), state.Abbreviation, date, employeeName, schedule.UniqueId, sequenceOfNumbers));
                }
            }

            return drawingsSummaryReport;
        }

        internal TicketPick<Pick2> StraightTicket(Player player, DateTime date, bool withFireBall, string number1, string number2, Selection selectionMode, DateTime creationDate, decimal ticketCost, int ticketNumber, Prizes prizes)
        {
            if (player == null) throw new ArgumentNullException(nameof(player));
            if (date == default(DateTime)) throw new GameEngineException("Date cannot have default value");
            if (String.IsNullOrEmpty(number1)) throw new ArgumentNullException(nameof(number1));
            if (String.IsNullOrEmpty(number2)) throw new ArgumentNullException(nameof(number2));

            if (IsLotteryPickWithFireBall) throw new GameEngineException($"Cannot create a ticket for {nameof(StraightTicket)}, make sure you are using {nameof(LotteryPick<Pick2>)} instance, instead of {nameof(LotteryPickWithFireBall<Pick2>)}");
            if (withFireBall && !this.IsFireBallTurnedOn) throw new GameEngineException($"Cannot create a ticket, make sure your Lottery has Fireball");

            var ticket = new TicketPick2Straight(player, this, date, number1, number2, selectionMode, creationDate, ticketCost, prizes);
            ticket.TicketNumber = ticketNumber;
            StoreTicket(withFireBall, date, ticket);
            return ticket;
        }

        internal TicketPick<Pick3> StraightTicket(Player player, DateTime date, bool withFireBall, string number1, string number2, string number3, Selection selectionMode, DateTime creationDate, decimal ticketCost, int ticketNumber, Prizes prizes)
        {
            if (player==null) throw new ArgumentNullException(nameof(player));
            if (date == default(DateTime)) throw new GameEngineException("Date cannot have default value");
            if (String.IsNullOrEmpty(number1)) throw new ArgumentNullException(nameof(number1));
            if (String.IsNullOrEmpty(number2)) throw new ArgumentNullException(nameof(number2));
            if (String.IsNullOrEmpty(number3)) throw new ArgumentNullException(nameof(number3));

            if (IsLotteryPickWithFireBall) throw new GameEngineException($"Cannot create a ticket for {nameof(StraightTicket)}, make sure you are using {nameof(LotteryPick<Pick3>)} instance, instead of {nameof(LotteryPickWithFireBall<Pick3>)}");
            if (withFireBall && !this.IsFireBallTurnedOn) throw new GameEngineException($"Cannot create a ticket, make sure your Lottery has Fireball");

            var ticket = new TicketPick3Straight(player, this, date, number1, number2, number3, selectionMode, creationDate, ticketCost, prizes);
            ticket.TicketNumber = ticketNumber;
            StoreTicket(withFireBall, date, ticket);
            return ticket;
        }

        internal TicketPick<Pick4> StraightTicket(Player player, DateTime date, bool withFireBall, string number1, string number2, string number3, string number4, Selection selectionMode, DateTime creationDate, decimal ticketCost, int ticketNumber, Prizes prizes)
        {
            if (player == null) throw new ArgumentNullException(nameof(player));
            if (date == default(DateTime)) throw new GameEngineException("Date cannot have default value");
            if (String.IsNullOrEmpty(number1)) throw new ArgumentNullException(nameof(number1));
            if (String.IsNullOrEmpty(number2)) throw new ArgumentNullException(nameof(number2));
            if (String.IsNullOrEmpty(number3)) throw new ArgumentNullException(nameof(number3));
            if (String.IsNullOrEmpty(number4)) throw new ArgumentNullException(nameof(number4));

            if (IsLotteryPickWithFireBall) throw new GameEngineException($"Cannot create a ticket for {nameof(StraightTicket)}, make sure you are using {nameof(LotteryPick<Pick4>)} instance, instead of {nameof(LotteryPickWithFireBall<Pick4>)}");
            if (withFireBall && !this.IsFireBallTurnedOn) throw new GameEngineException($"Cannot create a ticket, make sure your Lottery has Fireball");

            var ticket = new TicketPick4Straight(player, this, date, number1, number2, number3, number4, selectionMode, creationDate, ticketCost, prizes);
            ticket.TicketNumber = ticketNumber;
            StoreTicket(withFireBall, date, ticket);
            return ticket;
        }

        internal TicketPick<Pick5> StraightTicket(Player player, DateTime date, bool withFireBall, string number1, string number2, string number3, string number4, string number5, Selection selectionMode, DateTime creationDate, decimal ticketCost, int ticketNumber, Prizes prizes)
        {
            if (player == null) throw new ArgumentNullException(nameof(player));
            if (date == default(DateTime)) throw new GameEngineException("Date cannot have default value");
            if (String.IsNullOrEmpty(number1)) throw new ArgumentNullException(nameof(number1));
            if (String.IsNullOrEmpty(number2)) throw new ArgumentNullException(nameof(number2));
            if (String.IsNullOrEmpty(number3)) throw new ArgumentNullException(nameof(number3));
            if (String.IsNullOrEmpty(number4)) throw new ArgumentNullException(nameof(number4));
            if (String.IsNullOrEmpty(number5)) throw new ArgumentNullException(nameof(number5));

            if (withFireBall && !this.IsFireBallTurnedOn) throw new GameEngineException($"Cannot create a ticket, make sure your Lottery has Fireball");

            var ticket = new TicketPick5Straight(player, this, date, number1, number2, number3, number4, number5, selectionMode, creationDate, ticketCost, prizes);
            ticket.TicketNumber = ticketNumber;
            StoreTicket(withFireBall, date, ticket);
            return ticket;
        }

        internal TicketPick<TPick> StraightTicket(Player player, DateTime date, bool withFireBall, string [] numbers, Selection selectionMode, DateTime creationDate, decimal ticketCost, int ticketNumber, Prizes prizes)
		{
			if (player == null) throw new ArgumentNullException(nameof(player));
			if (date == default(DateTime)) throw new GameEngineException("Date cannot have default value");
            if (numbers == null) throw new ArgumentNullException(nameof(numbers));
			if (numbers.Any(x=>string.IsNullOrWhiteSpace(x))) throw new ArgumentNullException(nameof(numbers));

            if (withFireBall && !this.IsFireBallTurnedOn) throw new GameEngineException($"Cannot create a ticket, make sure you instance has Fireball");

            Ticket ticket;
            Lottery selectedLottery = withFireBall ? (this.ActiveFireBallInstance()) : this;
            switch (Length(numbers))
            {
                case 2:
                    ticket = new TicketPick2Straight(player, selectedLottery, date, numbers, selectionMode, creationDate, ticketCost, prizes);
                    break;
                case 3:
                    ticket = new TicketPick3Straight(player, selectedLottery, date, numbers, selectionMode, creationDate, ticketCost, prizes);
                    break;
                case 4:
                    ticket = new TicketPick4Straight(player, selectedLottery, date, numbers, selectionMode, creationDate, ticketCost, prizes);
                    break;
                case 5:
                    ticket = new TicketPick5Straight(player, selectedLottery, date, numbers, selectionMode, creationDate, ticketCost, prizes);
                    break;
                default:
                    throw new GameEngineException("Invalid sequence of numbers");
            }
            ticket.TicketNumber = ticketNumber;
            StoreTicket(withFireBall, date, ticket);
            return (TicketPick<TPick>) ticket;
		}

        internal TicketPick<Pick2> BoxedTicket(Player player, DateTime date, bool withFireBall, string number1, string number2, Selection selectionMode, DateTime creationDate, decimal ticketCost, int ticketNumber, Prizes prizes)
        {
            if (player == null) throw new ArgumentNullException(nameof(player));
            if (date == default(DateTime)) throw new GameEngineException("Date cannot have default value");
            if (String.IsNullOrEmpty(number1)) throw new ArgumentNullException(nameof(number1));
            if (String.IsNullOrEmpty(number2)) throw new ArgumentNullException(nameof(number2));

            if (withFireBall && !this.IsFireBallTurnedOn) throw new GameEngineException($"Cannot create a ticket, make sure your Lottery has Fireball");

            var ticket = new TicketPick2Boxed(player, this, date, number1, number2, selectionMode, creationDate, ticketCost, prizes);
            ticket.TicketNumber = ticketNumber;
            StoreTicket(withFireBall, date, ticket);
            return ticket;
        }

        internal TicketPick<Pick3> BoxedTicket(Player player, DateTime date, bool withFireBall, string number1, string number2, string number3, Selection selectionMode, DateTime creationDate, decimal ticketCost, int ticketNumber, Prizes prizes)
        {
            if (player == null) throw new ArgumentNullException(nameof(player));
            if (date == default(DateTime)) throw new GameEngineException("Date cannot have default value");
            if (String.IsNullOrEmpty(number1)) throw new ArgumentNullException(nameof(number1));
            if (String.IsNullOrEmpty(number2)) throw new ArgumentNullException(nameof(number2));
            if (String.IsNullOrEmpty(number3)) throw new ArgumentNullException(nameof(number3));

            if (withFireBall && !this.IsFireBallTurnedOn) throw new GameEngineException($"Cannot create a ticket, make sure your Lottery has Fireball");

            var ticket = new TicketPick3Boxed(player, this, date, number1, number2, number3, selectionMode, creationDate, ticketCost, prizes);
            ticket.TicketNumber = ticketNumber;
            StoreTicket(withFireBall, date, ticket);
            return ticket;
        }

        internal TicketPick<Pick4> BoxedTicket(Player player, DateTime date, bool withFireBall, string number1, string number2, string number3, string number4, Selection selectionMode, DateTime creationDate, decimal ticketCost, int ticketNumber, Prizes prizes)
        {
            if (player == null) throw new ArgumentNullException(nameof(player));
            if (date == default(DateTime)) throw new GameEngineException("Date cannot have default value");
            if (String.IsNullOrEmpty(number1)) throw new ArgumentNullException(nameof(number1));
            if (String.IsNullOrEmpty(number2)) throw new ArgumentNullException(nameof(number2));
            if (String.IsNullOrEmpty(number3)) throw new ArgumentNullException(nameof(number3));
            if (String.IsNullOrEmpty(number4)) throw new ArgumentNullException(nameof(number4));

            if (withFireBall && !this.IsFireBallTurnedOn) throw new GameEngineException($"Cannot create a ticket, make sure your Lottery has Fireball");

            var ticket = new TicketPick4Boxed(player, this, date, number1, number2, number3, number4, selectionMode, creationDate, ticketCost, prizes);
            ticket.TicketNumber = ticketNumber;
            StoreTicket(withFireBall, date, ticket);
            return ticket;
        }

        internal TicketPick<Pick5> BoxedTicket(Player player, DateTime date, bool withFireBall, string number1, string number2, string number3, string number4, string number5, Selection selectionMode, DateTime creationDate, decimal ticketCost, int ticketNumber, Prizes prizes)
        {
            if (player == null) throw new ArgumentNullException(nameof(player));
            if (date == default(DateTime)) throw new GameEngineException("Date cannot have default value");
            if (String.IsNullOrEmpty(number1)) throw new ArgumentNullException(nameof(number1));
            if (String.IsNullOrEmpty(number2)) throw new ArgumentNullException(nameof(number2));
            if (String.IsNullOrEmpty(number3)) throw new ArgumentNullException(nameof(number3));
            if (String.IsNullOrEmpty(number4)) throw new ArgumentNullException(nameof(number4));
            if (String.IsNullOrEmpty(number5)) throw new ArgumentNullException(nameof(number5));

            if (withFireBall && !this.IsFireBallTurnedOn) throw new GameEngineException($"Cannot create a ticket, make sure your Lottery has Fireball");

            var ticket = new TicketPick5Boxed(player, this, date, number1, number2, number3, number4, number5, selectionMode, creationDate, ticketCost, prizes);
            ticket.TicketNumber = ticketNumber;
            StoreTicket(withFireBall, date, ticket);
            return ticket;
        }

        private void StoreTicket(bool withFireBall, DateTime date, Ticket ticket)
        {
            if (withFireBall)
            {
                if (IsFireBallTurnedOn)
                {
                    ActiveFireBallInstance().AddTicket(date, ticket);
                }
                else
                {
                    throw new GameEngineException("Cannot store ticket with fireball, make sure your Lottery has Fireball");
                }
            }
            else
            {
                AddTicket(date, ticket);
            }
        }

        internal TicketPick<TPick> BoxedTicket(Player player, DateTime date, bool withFireBall, string [] numbers, Selection selectionMode, DateTime creationDate, decimal ticketCost, int ticketNumber, Prizes prizes)
		{
			if (player == null) throw new ArgumentNullException(nameof(player));
			if (date == default(DateTime)) throw new GameEngineException("Date cannot have default value");
			if (numbers == null) throw new ArgumentNullException(nameof(numbers));
			if (numbers.Any(x=>string.IsNullOrWhiteSpace(x))) throw new ArgumentNullException(nameof(numbers));

            if (IsLotteryPickWithFireBall) throw new GameEngineException($"Cannot create a ticket for {nameof(BoxedTicket)}, make sure you are using {nameof(LotteryPick<TPick>)} instance, instead of {nameof(LotteryPickWithFireBall<TPick>)}");
            if (withFireBall && !this.IsFireBallTurnedOn) throw new GameEngineException($"Cannot create a ticket, make sure your Lottery has Fireball");

            Ticket ticket;
            Lottery selectedLottery = withFireBall ? (this.ActiveFireBallInstance()) : this;
            switch (Length(numbers))
            {
                case 2:
                    ticket = new TicketPick2Boxed(player, selectedLottery, date, numbers, selectionMode, creationDate, ticketCost, prizes);
                    break;
                case 3:
                    ticket = new TicketPick3Boxed(player, selectedLottery, date, numbers, selectionMode, creationDate, ticketCost, prizes);
                    break;
                case 4:
                    ticket = new TicketPick4Boxed(player, selectedLottery, date, numbers, selectionMode, creationDate, ticketCost, prizes);
                    break;
                case 5:
                    ticket = new TicketPick5Boxed(player, selectedLottery, date, numbers, selectionMode, creationDate, ticketCost, prizes);
                    break;
                default:
                    throw new GameEngineException("Invalid sequence of numbers");
            }
            ticket.TicketNumber = ticketNumber;
            StoreTicket(withFireBall, date, ticket);
            return (TicketPick<TPick>) ticket;
		}

        private int Length(string [] sequenceOfNumbers)
        {
            int len = sequenceOfNumbers[0].Length;
            if (len < 2 || len > 5) throw new GameEngineException("Invalid sequence of numbers");

            return len;            
        }

        protected override void TryToCancelLottery()
        {
            if ((schedules.Count == 0)||(AreAllSchedulesDisabled()))
            {
                base.PicksLotteryGame.CancelLottery(this);
            }
        }

        internal override string GameType()
        {
            var type = this.GetType();
            var gameType = $"Lottery{type.GenericTypeArguments[0].Name}";
            return gameType;
        }

        internal override int PickNumber
        {
            get
            {
                var pickNumber = GameType().Last();
                if (!char.IsDigit(pickNumber)) throw new GameEngineException("Invalid pick number");
                return pickNumber - '0';
            }
        }
    }
}
