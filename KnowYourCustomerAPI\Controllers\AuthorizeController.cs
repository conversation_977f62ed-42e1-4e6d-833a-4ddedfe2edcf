﻿using GamesEngine.Settings;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;

namespace KnowYourCustomerAPI.Controllers
{
    [Authorize]
    [Filter]
    public class AuthorizeController : Controller
    { }

    public class Filter : Microsoft.AspNetCore.Mvc.Filters.ActionFilterAttribute
    {
        public override void OnActionExecuted(ActionExecutedContext filterContext)
        {
            if (filterContext.Exception != null)
            {
                ErrorsSender.Send(filterContext.Exception);
            }
            else if (filterContext.HttpContext.Response.StatusCode != 200 && Build.IsInDevelopmentMode)
            {
                ErrorsSender.Send(filterContext.Exception);
            }
        }
    }
}