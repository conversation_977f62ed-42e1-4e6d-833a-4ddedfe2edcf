﻿using GamesEngine;
using GamesEngine.Business;
using GamesEngine.Custodian.Operations;
using GamesEngine.Domains;
using GamesEngine.Exchange;
using GamesEngine.Exchange.Batch;
using GamesEngine.Finance;
using GamesEngine.PurchaseOrders;
using GamesEngine.Settings;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using System;
using System.Collections.Generic;
using System.Linq;
using Unit.Games.Tools;
using static GamesEngine.Exchange.FloatingExchangeRate;
using static GamesEngine.Exchange.CurrencyPair;
using static GamesEngine.Exchange.TransactionCompleted;
using GamesEngine.Custodian;
using GamesEngineTests.Unit_Tests.Games.Lotto;
using town.connectors.drivers;
using static town.connectors.drivers.Result;
using static GamesEngine.Business.WholePaymentProcessor;
using GamesEngineTests.Custodian;
using System.Reflection;

namespace GamesEngineTests.Exchange
{

	[TestClass]
	public class SaleTransactionsTest
	{
		[TestInitialize]
		public void Initialize()
		{
			typeof(WholePaymentProcessor).GetField("_wholePaymentProcessor", System.Reflection.BindingFlags.NonPublic | BindingFlags.Static).SetValue(null, null);
		}

		[TestMethod]
		public void Rate_Operations()
		{
			/*
			BTC/USD 40
			*/
			DateTime today = DateTime.Now;
			Company company = new Company();CurrenciesTest.AddCurrencies();
			company.Sales.CreateStore(1, "Test Store").MakeCurrent();
			string ced="abc123";string accountNumber = "B15461";Customer customer=company.GetOrCreateCustomerById(accountNumber);customer.Identifier= ced;
			Marketplace marketplace = new Marketplace(company, "CR");
			ExchangeRates exchangeRates = new ExchangeRates(marketplace);

			SaleRate rate = FloatingExchangeRate.CreateSaleRate<SaleRate>(exchangeRates, today, Coinage.Coin(Currencies.CODES.USD), Coinage.Coin(Currencies.CODES.BTC), 40m);
			Assert.AreEqual(new Btc(1), rate.Convert(new Dollar(40)));
			Assert.AreEqual(new Dollar(40m), rate.Convert(new Btc(1)));

			Assert.AreEqual(new Btc(5), rate.Convert(new Dollar(200)));
			Assert.AreEqual(new Dollar(200m), rate.Convert(new Btc(5)));

			Assert.AreEqual(new Dollar(117m), rate.Convert(new Btc(2.925m)));

			SaleRate rate2 = FloatingExchangeRate.CreateSaleRate<SaleRate>(exchangeRates, today, Coinage.Coin(Currencies.CODES.USD), Coinage.Coin(Currencies.CODES.BTC), 60m);
			Assert.AreEqual(new Dollar(60m), rate2.Convert(new Btc(1)));
			Assert.AreEqual(new Btc(0.01666667m), rate2.Convert(new Dollar(1)));
			Assert.AreEqual(new Dollar(1), rate2.Convert(new Btc(0.01666667m)));
		}
		[TestMethod]
		public void BTC_to_USD_Rates()
		{
			/*
				Aqui por ejemplo…  el mae llego como $1000 y quiere pasarlos a BTC...
				supongamos que la compra esta en $20 y la venta en $40... por eso profit dice $20
				El Gross es monto bruto en la moneda source... es decir $1000
				El Net es el Gross - El profit

				Exchange compra BTC/USD $20  * venta  USD/BTC 0.05
				Exchange venta  * BTC/USD $40  compra USD/BTC 0.025
					3BTC -> $

					profit = 3 * 0.025 = BTC 0.075 -> Calcular el profit (Venta - compra)de esta manera es error ya que solo muestra el delta de los precio, pero se debe poner porcentualmente(venta - compra)/venta para evitar negativos a la hora de calcular el profit.
					Neto 3BTC - profit = 2.925BTC

					Resulto 2.925BTC /  0.05BTC  = $58.5
					*Resulto 2.925BTC /  0.025BTC = $117

					Resulto 2.925BTC * 20BTC  = $58.5
					*Resulto 2.925BTC * 40BTC  = $117
	
	
					<<Correcto>>
					profit = 3 * 0.5 = BTC 1.5
					Neto 3BTC - profit = 1.5BTC

					Resulto  1.5BTC /  0.05BTC  = $30
					*Resulto 1.5BTC /  0.025BTC = $60

					Resulto  1.5BTC * 20BTC  = $30
					*Resulto 1.5BTC * 40BTC  = $60
			 */

			DateTime today = DateTime.Now;
			Company company = new Company();CurrenciesTest.AddCurrencies();
			company.Sales.CreateStore(1, "Test Store").MakeCurrent();
			string ced="abc123";string accountNumber = "B15461";Customer customer=company.GetOrCreateCustomerById(accountNumber);customer.Identifier= ced;
			Marketplace marketplace = new Marketplace(company, "CR");
			ExchangeRates exchangeRates = new ExchangeRates(marketplace);

			SaleRate saleRate = FloatingExchangeRate.CreateSaleRate<SaleRate>(exchangeRates, today, Coinage.Coin(Currencies.CODES.USD), Coinage.Coin(Currencies.CODES.BTC), 40m);
			PurchaseRate purchaseRate = FloatingExchangeRate.CreatePurchaseRate<PurchaseRate>(exchangeRates, today, Coinage.Coin(Currencies.CODES.USD), Coinage.Coin(Currencies.CODES.BTC), 20m);

			Assert.AreEqual("BTC/USD 40", saleRate.ToString());
			Assert.AreEqual("USD/BTC 0.025", saleRate.PurchaseRate().ToString());
			Assert.AreEqual("BTC/USD 20", purchaseRate.ToString());
			Assert.AreEqual("USD/BTC 0.05", purchaseRate.SaleRate().ToString());

			Btc threeBitcoins = new Btc(3);

			RateMarkup markup = saleRate.CalculateMarkupWith(purchaseRate);
			var profit = saleRate.Comission<Currency>(threeBitcoins, markup);
			var inverseprofit = purchaseRate.Comission<Currency>(threeBitcoins, markup);
			Btc gross = threeBitcoins;
			var net = Currency.Factory(Coinage.Coin(Currencies.CODES.BTC), gross.Value - profit.Value);

			Assert.AreEqual(0.5m, markup.Value);
			Assert.AreEqual(true, profit.Value == inverseprofit.Value);
			Assert.AreEqual(new Btc(1.5m), profit);
			Assert.AreEqual(new Btc(1.5m), net);
			Assert.AreEqual(threeBitcoins, gross);

			var finalResult = saleRate.Convert(net);
			Assert.AreEqual(new Dollar(60m), finalResult);
		}

		[TestMethod]
		public void USD_to_BTC_Rates()
		{
			DateTime today = DateTime.Now;
			Company company = new Company();CurrenciesTest.AddCurrencies();
			company.Sales.CreateStore(1, "Test Store").MakeCurrent();
			string ced="abc123";string accountNumber = "B15461";Customer customer=company.GetOrCreateCustomerById(accountNumber);customer.Identifier = ced;
			Marketplace marketplace = new Marketplace(company, "CR");
			ExchangeRates exchangeRates = new ExchangeRates(marketplace);

			SaleRate saleRate = FloatingExchangeRate.CreateSaleRate<SaleRate>(exchangeRates, today, Coinage.Coin(Currencies.CODES.USD), Coinage.Coin(Currencies.CODES.BTC), 40m);
			PurchaseRate purchaseRate = FloatingExchangeRate.CreatePurchaseRate<PurchaseRate>(exchangeRates, today, Coinage.Coin(Currencies.CODES.USD), Coinage.Coin(Currencies.CODES.BTC), 20m);

			Assert.AreEqual("BTC/USD 40", saleRate.ToString());
			Assert.AreEqual("USD/BTC 0.025", saleRate.PurchaseRate().ToString());
			Assert.AreEqual("BTC/USD 20", purchaseRate.ToString());
			Assert.AreEqual("USD/BTC 0.05", purchaseRate.SaleRate().ToString());

			var dollars = new Currency("USD", 60);

			RateMarkup markup = saleRate.CalculateMarkupWith(purchaseRate);
			var profit = saleRate.Comission(dollars, markup);
			var inverseprofit = purchaseRate.Comission(dollars, markup);
			var gross = dollars;
			var net = Currency.Factory(Coinage.Coin(Currencies.CODES.USD), gross.Value - profit.Value);

			Assert.AreEqual(0.5m, markup.Value);
			Assert.AreEqual(true, profit.Value == inverseprofit.Value);
			Assert.AreEqual(new Dollar(30m), profit);
			Assert.AreEqual(new Dollar(30m), net);
			Assert.AreEqual(dollars, gross);

			var finalResult = saleRate.Convert(net);
			Assert.AreEqual(new Btc(0.75m), finalResult);
		}

		[TestMethod]
		public void ConvertUSDtoBTC()
		{
			bool itIsThePresent = false;
			DateTime today1 = DateTime.Now;
			DateTime today2 = today1.AddMilliseconds(1);
			Company company = new Company();CurrenciesTest.AddCurrencies();
			company.Sales.CreateStore(1, "Test Store").MakeCurrent();
			string employeeName = "Juan";
			string ced = "abc123"; string accountNumber = "B15461"; Customer customer = company.GetOrCreateCustomerById(accountNumber); customer.Identifier= ced;
			Marketplace marketplace = new Marketplace(company, "CR");

			#region rate #1
			RegularConversionSpread rate1 = marketplace.CreateConversionSpread(1, today1, itIsThePresent, Coinage.Coin(Currencies.CODES.USD), Coinage.Coin(Currencies.CODES.BTC), 60m, 30m, employeeName);
			Assert.AreEqual(rate1, marketplace.ConversionSpreadAt(today1, Coinage.Coin(Currencies.CODES.USD), Coinage.Coin(Currencies.CODES.BTC)));
			Assert.AreEqual(1, marketplace.AmountOfRatesInMemory());
			#endregion

			#region rate #2
			RegularConversionSpread rate2 = marketplace.CreateConversionSpread(2, today2, itIsThePresent, Coinage.Coin(Currencies.CODES.BTC), Coinage.Coin(Currencies.CODES.USD), 0.033333333m, 0.016666666m, employeeName);
			Assert.AreEqual(rate2, marketplace.ConversionSpreadAt(today2, Coinage.Coin(Currencies.CODES.BTC), Coinage.Coin(Currencies.CODES.USD)));
			Assert.AreEqual(2, marketplace.AmountOfRatesInMemory());
			#endregion
			
			var result = marketplace.Convert(today1, Coinage.Coin(Currencies.CODES.USD), Coinage.Coin(Currencies.CODES.BTC), 100m);
			Assert.AreEqual(new Btc(0.83333333m), result);
			result = marketplace.Convert(today2, Coinage.Coin(Currencies.CODES.BTC), Coinage.Coin(Currencies.CODES.USD), 0.83333333m);
			Assert.AreEqual(new Dollar(12.5m), result);
		}

		[TestMethod]
		public void SaleAndPurchaseRatePrice()
		{
			bool itIsThePresent = false;
			DateTime today1 = DateTime.Now;
			Company company = new Company();CurrenciesTest.AddCurrencies();
			company.Sales.CreateStore(1, "Test Store").MakeCurrent();
			string employeeName = "Juan";
			string ced = "abc123"; string accountNumber = "B15461"; Customer customer = company.GetOrCreateCustomerById(accountNumber); customer.Identifier = ced;
			Marketplace marketplace = new Marketplace(company, "CR");

			#region rate #1
			RegularConversionSpread rate1 = marketplace.CreateConversionSpread(1, today1, itIsThePresent, Coinage.Coin(Currencies.CODES.USD), Coinage.Coin(Currencies.CODES.BTC), 60m, 30m, employeeName);
			Assert.AreEqual(rate1, marketplace.ConversionSpreadAt(today1, Coinage.Coin(Currencies.CODES.USD), Coinage.Coin(Currencies.CODES.BTC)));
			Assert.AreEqual(1, marketplace.AmountOfRatesInMemory());
			#endregion

			Assert.AreEqual(60m, rate1.SaleRatePrice());
			Assert.AreEqual(30m, rate1.PurchaseRate.Price);
			Assert.AreEqual(Coinage.Coin(Currencies.CODES.BTC), rate1.SaleRateBaseCurrency());
			Assert.AreEqual(Coinage.Coin(Currencies.CODES.USD), rate1.SaleRateQouteCurrency());
		}

		[TestMethod]
		public void SearchRate()
		{
			bool itIsThePresent = false;
			DateTime today1 = DateTime.Now;
			Company company = new Company();CurrenciesTest.AddCurrencies();
			company.Sales.CreateStore(1, "Test Store").MakeCurrent();
			string employeeName = "Juan";
			string ced = "abc123"; string accountNumber = "B15461"; Customer customer = company.GetOrCreateCustomerById(accountNumber); customer.Identifier = ced;
			Marketplace marketplace = new Marketplace(company, "CR");

			Assert.IsFalse(marketplace.ExistsRate(today1, "USD", "BTC"));
			#region rate #1
			RegularConversionSpread rate1 = marketplace.CreateConversionSpread(1, today1, itIsThePresent, Coinage.Coin(Currencies.CODES.USD), Coinage.Coin(Currencies.CODES.BTC), 60m, 30m, employeeName);
			Assert.AreEqual(rate1, marketplace.ConversionSpreadAt(today1, Coinage.Coin(Currencies.CODES.USD), Coinage.Coin(Currencies.CODES.BTC)));
			Assert.AreEqual(1, marketplace.AmountOfRatesInMemory());
			#endregion

			Assert.IsTrue(marketplace.ExistsRate(today1, "USD", "BTC"));
			var rate = marketplace.SearchRate(today1, Coinage.Coin(Currencies.CODES.USD), Coinage.Coin(Currencies.CODES.BTC));
			Assert.AreEqual("BTC/USD 60.00", rate.Rate);
			Assert.AreEqual(60m, rate.SaleRatePrice);
			Assert.AreEqual("BTC", rate.SaleRateBaseCurrency);
			Assert.AreEqual("USD", rate.SaleRateQouteCurrency);
		}

		[TestMethod]
		public void DropRatesFromMemory()
		{
			Integration.SavePreviousSettings(tempUseKafka: false, tempUseKafkaForAuto: false);
			bool itIsThePresent = false;
			DateTime today1 = DateTime.Now;
			DateTime today2 = today1.AddMilliseconds(1);
			DateTime today3 = today1.AddMilliseconds(2);
			Company company = new Company();CurrenciesTest.AddCurrencies();
			company.Sales.CreateStore(1, "Test Store").MakeCurrent();
			string ced="abc123";string accountNumber = "B15461";Customer customer=company.GetOrCreateCustomerById(accountNumber);customer.Identifier=ced;
			CustomerAccount bitcoinAccount = customer.CreateNewAccountFor(Coinage.Coin(Currencies.CODES.BTC));
			Marketplace marketplace = new Marketplace(company, "CR");
			var a = (GamesEngine.Exchange.Agent)marketplace.AddAgent("A");
			var b = a.AddCashier("Juan");
			string employeeName = "Juan";
			Domain domain = company.Sales.CreateDomain(false, 1, "localhost", PaymentChannels.Agents.INSIDER);
			company.Sales.CurrentStore.Add(domain);
			GuardianTest.LoadProcessors(company);

			#region rate #1
			RegularConversionSpread rate1 = marketplace.CreateConversionSpread(1, today1, itIsThePresent, Coinage.Coin(Currencies.CODES.USD), Coinage.Coin(Currencies.CODES.BTC), 60m, 30m, employeeName);
			Assert.AreEqual(rate1, marketplace.ConversionSpreadAt(today1, Coinage.Coin(Currencies.CODES.USD), Coinage.Coin(Currencies.CODES.BTC)));
			Assert.AreEqual(1, marketplace.AmountOfRatesInMemory());
			#endregion

			MarketplaceBatch marketPLaceBatch;
			AgentBatch agencyBatch;
			BatchTransactions juanTransactions = BatchTest.SetInitialSettings(marketplace, today1, new Domain[] { domain }, out marketPLaceBatch, out agencyBatch);
			BatchSet agentBatch = juanTransactions.Batch();
			string agentPath = a.FullName;

			int id = marketplace.NewTransationNumber();

			var transactionType = marketplace.Company.System.TransactionTypes.SearchByName<ProcessorTransaction>(TransactionType.Deposit.ToString());
			int authorizationNumber = PaymentChannels.Deposit(
				itIsThePresent,
				Coinage.Coin(Currencies.CODES.USD),
				domain,
				transactionType,
				new DespositBody(
					accountNumber, 
					ced,
					100,
					"deposit test",
					today1, 
					"",
					"cris",
					domain)
				).AuthorizationId;


			DepositTransaction transaction1 = (DepositTransaction)marketplace.From(id, bitcoinAccount, agentPath, employeeName, domain).Deposit(today1, itIsThePresent, new Dollar(100), employeeName, (PaymentProcessor)marketplace.Company.System.PaymentProcessor.First(), 1);
			DepositTransaction transaction = (DepositTransaction)marketplace.FindDraftTransaction(juanTransactions, transaction1.Id);

			#region rate #2
			RegularConversionSpread rate2 = marketplace.CreateConversionSpread(2, today2, itIsThePresent, Coinage.Coin(Currencies.CODES.USD), Coinage.Coin(Currencies.CODES.BTC), 70m, 40m, employeeName);
			Assert.AreEqual(rate2, marketplace.ConversionSpreadAt(today2, Coinage.Coin(Currencies.CODES.USD), Coinage.Coin(Currencies.CODES.BTC)));
			Assert.AreEqual(2, marketplace.AmountOfRatesInMemory());
			#endregion

			Assert.AreEqual(2, marketplace.AmountOfRatesInMemory());

			TransactionCompleted result1 = marketplace.Approve(today2, itIsThePresent, transaction1, 1, employeeName);
			transactionType = marketplace.Company.System.TransactionTypes.SearchByName<ProcessorTransaction>(TransactionType.Deposit.ToString());
			authorizationNumber = PaymentChannels.Deposit(
				itIsThePresent,
				Coinage.Coin(Currencies.CODES.USD),
				domain,
				transactionType,
				new DespositBody(
					accountNumber,
					ced,
					100,
					"deposit test",
					today1,
					"",
					"cris",
					domain)
				).AuthorizationId;

			DepositTransaction transaction2 = (DepositTransaction)marketplace.From(id, bitcoinAccount, agentPath, employeeName, domain).Deposit(today2, itIsThePresent, new Dollar(100), employeeName, (PaymentProcessor)marketplace.Company.System.PaymentProcessor.First(), 1);

			TransactionCompleted result2 = marketplace.Approve(today2, itIsThePresent, transaction2, 2, employeeName);

			IConversionSpread rate1StoredInMemory = marketplace.ConversionSpreadAt(today1, Coinage.Coin(Currencies.CODES.USD), Coinage.Coin(Currencies.CODES.BTC));
			IConversionSpread rate2StoredInMemory = marketplace.ConversionSpreadAt(today2, Coinage.Coin(Currencies.CODES.USD), Coinage.Coin(Currencies.CODES.BTC));

			Assert.AreEqual(rate1, rate1StoredInMemory);
			Assert.AreEqual(rate2, rate2StoredInMemory);
			Assert.AreEqual(rate1, transaction1.ConversionSpread);
			Assert.AreEqual(rate2, transaction2.ConversionSpread);
			Assert.AreEqual(2, marketplace.AmountOfRatesInMemory());

			#region rate #3
			RegularConversionSpread rate3 = marketplace.CreateConversionSpread(3, today2, itIsThePresent, Coinage.Coin(Currencies.CODES.USD), Coinage.Coin(Currencies.CODES.BTC), 70m, 40m, employeeName);
			Assert.AreEqual(rate3, marketplace.ConversionSpreadAt(today2, Coinage.Coin(Currencies.CODES.USD), Coinage.Coin(Currencies.CODES.BTC)));
			Assert.AreEqual(1, marketplace.AmountOfRatesInMemory());
			#endregion

			#region rate #4
			RegularConversionSpread rate4 = marketplace.CreateConversionSpread(4, today3, itIsThePresent, Coinage.Coin(Currencies.CODES.USD), Coinage.Coin(Currencies.CODES.BTC), 80m, 40m, employeeName);
			Assert.AreEqual(rate4, marketplace.ConversionSpreadAt(today3, Coinage.Coin(Currencies.CODES.USD), Coinage.Coin(Currencies.CODES.BTC)));
			Assert.AreEqual(1, marketplace.AmountOfRatesInMemory());
			#endregion

			transactionType = marketplace.Company.System.TransactionTypes.SearchByName<ProcessorTransaction>(TransactionType.Deposit.ToString());
			authorizationNumber = PaymentChannels.Deposit(
				itIsThePresent,
				Coinage.Coin(Currencies.CODES.USD),
				domain,
				transactionType,
				new DespositBody(
					accountNumber,
					ced,
					100,
					"deposit test",
					today1,
					"",
					"cris",
					domain)
				).AuthorizationId;

			DepositTransaction transaction3 = (DepositTransaction)marketplace.From(id, bitcoinAccount, agentPath, employeeName, domain).Deposit(today3, itIsThePresent, new Dollar(100), employeeName, (PaymentProcessor)marketplace.Company.System.PaymentProcessor.First(), 1);
			TransactionCompleted result4 = marketplace.Approve(today2, itIsThePresent, transaction3, 3, employeeName);

			Assert.AreEqual(rate1, rate1StoredInMemory);
			Assert.AreEqual(rate2, rate2StoredInMemory);
			Assert.AreEqual(rate1, transaction1.ConversionSpread);
			Assert.AreEqual(rate2, transaction2.ConversionSpread);
			Assert.AreEqual(rate4, transaction3.ConversionSpread);
			Assert.AreEqual(1, marketplace.AmountOfRatesInMemory());

			marketplace.SearchAgentBatch("CR/A/Juan").Close(itIsThePresent, today3, "N/A");
			marketplace.SearchAgentBatch("CR/A").Close(itIsThePresent, today3, "N/A");
			marketplace.SearchAgentBatch("CR").Close(itIsThePresent, today3, "N/A");

			Assert.AreEqual(1, marketplace.AmountOfRatesInMemory());

			marketplace.SearchAgentBatch("CR/A/Juan").Verify(itIsThePresent, today3, "N/A");
			marketplace.SearchAgentBatch("CR/A").Verify(itIsThePresent, today3, "N/A");
			marketplace.SearchAgentBatch("CR").Verify(itIsThePresent, today3, "N/A");

			Assert.AreEqual(1, marketplace.AmountOfRatesInMemory());
			Assert.AreEqual(rate4, marketplace.ConversionSpreadAt(today3, Coinage.Coin(Currencies.CODES.USD), Coinage.Coin(Currencies.CODES.BTC)));

			Integration.RestoreToDefaultSettings();
			//GamesEngine.Custodian.PaymentMethodsActions.ResetInstance();
		}

		[TestMethod]
		public void USD2BTC_New_Rate_Milisecond_of_difference()
		{
			bool itIsThePresent = false;
			DateTime today1 = DateTime.Now;
			DateTime today2 = today1.AddMilliseconds(1);
			Company company = new Company();CurrenciesTest.AddCurrencies();
			company.Sales.CreateStore(1, "Test Store").MakeCurrent();
			string ced="abc123";string accountNumber = "B15461";Customer customer=company.GetOrCreateCustomerById(accountNumber);customer.Identifier=ced;
			CustomerAccount bitcoinAccount = customer.CreateNewAccountFor(Coinage.Coin(Currencies.CODES.BTC));
			Marketplace marketplace = new Marketplace(company, "CR");
			var a = (GamesEngine.Exchange.Agent)marketplace.AddAgent("A");
			var b = a.AddCashier("Juan");
			string employeeName = "Juan";
			string description = "Decription";
			Domain domain = company.Sales.CreateDomain(false, 1, "localhost", PaymentChannels.Agents.INSIDER);
			company.Sales.CurrentStore.Add(domain);
			GuardianTest.LoadProcessors(company);

			#region rate #1
			RegularConversionSpread rate1 = marketplace.CreateConversionSpread(1, today1, itIsThePresent, Coinage.Coin(Currencies.CODES.USD), Coinage.Coin(Currencies.CODES.BTC), 60m, 30m, employeeName);
			Assert.AreEqual(rate1, marketplace.ConversionSpreadAt(today1, Coinage.Coin(Currencies.CODES.USD), Coinage.Coin(Currencies.CODES.BTC)));
			#endregion

			BatchTransactions juanTransactions = BatchTest.SetInitialSettings(marketplace, today1, new Domain[] { domain });
			BatchSet agentBatch = juanTransactions.Batch();
			string agentPath = a.FullName;

			int id = marketplace.NewTransationNumber();
			SaleTransaction transaction1 = (SaleTransaction)marketplace.From(id, bitcoinAccount, agentPath, employeeName, domain).Sale(today1, itIsThePresent, new Dollar(100), employeeName);

			#region rate #2
			RegularConversionSpread rate2 = marketplace.CreateConversionSpread(2, today2, itIsThePresent, Coinage.Coin(Currencies.CODES.USD), Coinage.Coin(Currencies.CODES.BTC), 60m, 20m, employeeName);
			Assert.AreEqual(rate1, marketplace.ConversionSpreadAt(today1, Coinage.Coin(Currencies.CODES.USD), Coinage.Coin(Currencies.CODES.BTC)));
			Assert.AreEqual(rate2, marketplace.ConversionSpreadAt(today2, Coinage.Coin(Currencies.CODES.USD), Coinage.Coin(Currencies.CODES.BTC)));
			Assert.AreEqual(rate2, marketplace.ConversionSpreadAt(today2.AddDays(1), Coinage.Coin(Currencies.CODES.USD), Coinage.Coin(Currencies.CODES.BTC)));
			#endregion

			id = marketplace.NewTransationNumber();
			
			SaleTransaction transaction2 = (SaleTransaction)marketplace.From(id, bitcoinAccount, agentPath, employeeName, domain).Sale(today1, itIsThePresent, new Dollar(100), employeeName);

			var journalEntryNumber = marketplace.NewJournalEntryNumber();
			TransactionCompleted result1 = transaction1.Approve(today1, itIsThePresent, employeeName, journalEntryNumber);
			TransactionCompleted result2 = transaction2.Approve(today1, itIsThePresent, employeeName, journalEntryNumber);
			TransactionResult resultStats1 = (TransactionResult)result1.Result;
			Assert.AreEqual(new Dollar(50m).Value, resultStats1.Comission.Value);
			Assert.AreEqual(new Dollar(100m).Value, resultStats1.Gross.Value);
			Assert.AreEqual(new Dollar(50m).Value, resultStats1.Net.Value);
			Assert.AreEqual(new Btc(0.83333333m).Value, resultStats1.Amount.Value);

			try
			{
				marketplace.ConversionSpreadAt(today1.AddMilliseconds(-1), Coinage.Coin(Currencies.CODES.USD), Coinage.Coin(Currencies.CODES.BTC));
				Assert.Fail($"There is no a exchange rate for dates before {today1}.");
			}
			catch (GameEngineException e)
			{
			}
		}

		[TestMethod]
		[Description("Tipo de cambio actual sin haber expirado el tiempo")]
		public void USD2BTC_New_Rate_BeforeRateTimeExpires()
		{
			bool itIsThePresent = false;
			DateTime today1 = DateTime.Now;
			DateTime saleToday = today1.AddSeconds(15);
			DateTime approveToday = today1.AddMinutes(30);
			Company company = new Company();CurrenciesTest.AddCurrencies();
			company.Sales.CreateStore(1, "Test Store").MakeCurrent();
			string ced="abc123";string accountNumber = "B15461";Customer customer=company.GetOrCreateCustomerById(accountNumber);customer.Identifier=ced;
			CustomerAccount bitcoinAccount = customer.CreateNewAccountFor(Coinage.Coin(Currencies.CODES.BTC));
			Marketplace marketplace = new Marketplace(company, "CR");
			var a = (GamesEngine.Exchange.Agent)marketplace.AddAgent("A");
			var b = a.AddCashier("Juan");
			string employeeName = "Juan";
			Domain domain = company.Sales.CreateDomain(false, 1, "localhost", PaymentChannels.Agents.INSIDER);
			company.Sales.CurrentStore.Add(domain);
			GuardianTest.LoadProcessors(company);

			#region rate #1
			RegularConversionSpread rate1 = marketplace.CreateConversionSpread(1, today1, itIsThePresent, Coinage.Coin(Currencies.CODES.USD), Coinage.Coin(Currencies.CODES.BTC), 60m, 30m, employeeName);
			Assert.AreEqual(rate1, marketplace.ConversionSpreadAt(today1, Coinage.Coin(Currencies.CODES.USD), Coinage.Coin(Currencies.CODES.BTC)));
			#endregion

			BatchTransactions juanTransactions = BatchTest.SetInitialSettings(marketplace, today1, new Domain[] { domain });
			BatchSet agentBatch = juanTransactions.Batch();
			string agentPath = a.FullName;

			//Necesita enviarse el id del rate a utilizar
			int id = marketplace.NewTransationNumber();
			SaleTransaction transaction1 = (SaleTransaction)marketplace.From(id, bitcoinAccount, agentPath, employeeName, domain).Sale(saleToday, itIsThePresent, new Dollar(100), employeeName);
			Assert.AreEqual(transaction1.Status, TransactionStatus.DRAFT);
			Assert.AreEqual(transaction1.TransactionDate, saleToday);

			var journalEntryNumber = marketplace.NewJournalEntryNumber();
			TransactionCompleted result1 = transaction1.Approve(approveToday, itIsThePresent, employeeName, journalEntryNumber);

			TransactionResult resultStats1 = (TransactionResult)result1.Result;
			Assert.AreEqual(new Dollar(50m).Value, resultStats1.Comission.Value);
			Assert.AreEqual(new Dollar(100m).Value, resultStats1.Gross.Value);
			Assert.AreEqual(new Dollar(50m).Value, resultStats1.Net.Value);
			Assert.AreEqual(new Btc(0.83333333m).Value, resultStats1.Amount.Value);

			try
			{
				marketplace.ConversionSpreadAt(today1.AddMilliseconds(-1), Coinage.Coin(Currencies.CODES.USD), Coinage.Coin(Currencies.CODES.BTC));
				Assert.Fail($"There is no a exchange rate for dates before {today1}.");
			}
			catch (GameEngineException e)
			{
			}
		}

		[TestMethod]
		[Description("Tipo de cambio actual sin haber expirado el tiempo, pero actualizando a un rate nuevo el último rate activo")]
		public void USD2BTC_New_Rate_BeforeRateTimeExpires_NewRateIn()
		{
			bool itIsThePresent = false;
			DateTime today1 = DateTime.Now;
			DateTime today2 = today1.AddSeconds(15);
			DateTime saleToday = today1.AddSeconds(30);
			DateTime approveToday = today1.AddMinutes(30);
			Company company = new Company();CurrenciesTest.AddCurrencies();
			company.Sales.CreateStore(1, "Test Store").MakeCurrent();
			string ced="abc123";string accountNumber = "B15461";Customer customer=company.GetOrCreateCustomerById(accountNumber);customer.Identifier=ced;
			CustomerAccount bitcoinAccount = customer.CreateNewAccountFor(Coinage.Coin(Currencies.CODES.BTC));
			Marketplace marketplace = new Marketplace(company, "CR");
			var a = (GamesEngine.Exchange.Agent)marketplace.AddAgent("A");
			var b = a.AddCashier("Juan");
			string employeeName = "Juan";
			Domain domain = company.Sales.CreateDomain(false, 1, "localhost", PaymentChannels.Agents.INSIDER);
			company.Sales.CurrentStore.Add(domain);
			GuardianTest.LoadProcessors(company);

			#region rate #1
			RegularConversionSpread rate1 = marketplace.CreateConversionSpread(1, today1, itIsThePresent, Coinage.Coin(Currencies.CODES.USD), Coinage.Coin(Currencies.CODES.BTC), 60m, 20m, employeeName);
			Assert.AreEqual(rate1, marketplace.ConversionSpreadAt(today1, Coinage.Coin(Currencies.CODES.USD), Coinage.Coin(Currencies.CODES.BTC)));
			#endregion

			#region rate #2
			Assert.AreEqual(rate1, marketplace.ConversionSpreadAt(today1, Coinage.Coin(Currencies.CODES.USD), Coinage.Coin(Currencies.CODES.BTC)));
			RegularConversionSpread rate2 = marketplace.CreateConversionSpread(2, today2, itIsThePresent, Coinage.Coin(Currencies.CODES.USD), Coinage.Coin(Currencies.CODES.BTC), 60m, 30m, employeeName);
			try
			{
				Assert.AreEqual(rate1, marketplace.ConversionSpreadAt(today1, Coinage.Coin(Currencies.CODES.USD), Coinage.Coin(Currencies.CODES.BTC)));
				Assert.Fail("Rate 1 should be dropped.");
			}
			catch (GameEngineException e) { }
			
			Assert.AreEqual(rate2, marketplace.ConversionSpreadAt(today2, Coinage.Coin(Currencies.CODES.USD), Coinage.Coin(Currencies.CODES.BTC)));
			Assert.AreEqual(rate2, marketplace.ConversionSpreadAt(today2.AddDays(1), Coinage.Coin(Currencies.CODES.USD), Coinage.Coin(Currencies.CODES.BTC)));
			#endregion

			BatchTransactions juanTransactions = BatchTest.SetInitialSettings(marketplace, today1, new Domain[] { domain });
			BatchSet agentBatch = juanTransactions.Batch();
			string agentPath = a.FullName;

			//Necesita enviarse el id del rate a utilizar
			var id = marketplace.NewTransationNumber();
			SaleTransaction transaction1 = (SaleTransaction)marketplace.From(id, bitcoinAccount, agentPath, employeeName, domain).Sale(saleToday, itIsThePresent, new Dollar(100), employeeName);
			Assert.AreEqual(transaction1.Status, TransactionStatus.DRAFT);
			Assert.AreEqual(transaction1.TransactionDate, saleToday);

			var journalEntryNumber = marketplace.NewJournalEntryNumber();
			TransactionCompleted result1 = transaction1.Approve(approveToday, itIsThePresent, employeeName, journalEntryNumber);

			TransactionResult resultStats1 = (TransactionResult)result1.Result;
			Assert.AreEqual(new Dollar(50m).Value, resultStats1.Comission.Value);
			Assert.AreEqual(new Dollar(100m).Value, resultStats1.Gross.Value);
			Assert.AreEqual(new Dollar(50m).Value, resultStats1.Net.Value);
			Assert.AreEqual(new Btc(0.83333333m).Value, resultStats1.Amount.Value);

			try
			{
				marketplace.ConversionSpreadAt(today1.AddMilliseconds(-1), Coinage.Coin(Currencies.CODES.USD), Coinage.Coin(Currencies.CODES.BTC));
				Assert.Fail($"There is no a exchange rate for dates before {today1}.");
			}
			catch (GameEngineException e)
			{
			}
		}


		[TestMethod]
		[Description("Tipo de cambio actual y vigente habiendo expirado el tiempo")]
		public void USD2BTC_New_Rate_AfterTimeExpires()
		{
			bool itIsThePresent = false;
			DateTime today1 = DateTime.Now;
			DateTime saleToday = today1.AddMinutes(30);
			DateTime approveToday = today1.AddMinutes(30);
			Company company = new Company();CurrenciesTest.AddCurrencies();
			company.Sales.CreateStore(1, "Test Store").MakeCurrent();
			string ced="abc123";string accountNumber = "B15461";Customer customer=company.GetOrCreateCustomerById(accountNumber);customer.Identifier=ced;
			CustomerAccount bitcoinAccount = customer.CreateNewAccountFor(Coinage.Coin(Currencies.CODES.BTC));
			Marketplace marketplace = new Marketplace(company, "CR");
			var a = (GamesEngine.Exchange.Agent)marketplace.AddAgent("A");
			var b = a.AddCashier("Juan");
			string employeeName = "Juan";
			Domain domain = company.Sales.CreateDomain(false, 1, "localhost", PaymentChannels.Agents.INSIDER);
			company.Sales.CurrentStore.Add(domain);
			GuardianTest.LoadProcessors(company);

			#region rate #1
			RegularConversionSpread rate1 = marketplace.CreateConversionSpread(1, today1, itIsThePresent, Coinage.Coin(Currencies.CODES.USD), Coinage.Coin(Currencies.CODES.BTC), 60m, 30m, employeeName);
			Assert.AreEqual(rate1, marketplace.ConversionSpreadAt(today1, Coinage.Coin(Currencies.CODES.USD), Coinage.Coin(Currencies.CODES.BTC)));
			#endregion

			BatchTransactions juanTransactions = BatchTest.SetInitialSettings(marketplace, today1, new Domain[] { domain });
			BatchSet agentBatch = juanTransactions.Batch();
			string agentPath = a.FullName;

			//Necesita enviarse el id del rate a utilizar
			int id = marketplace.NewTransationNumber();
			SaleTransaction transaction1 = (SaleTransaction)marketplace.From(id, bitcoinAccount, agentPath, employeeName, domain).Sale(saleToday, itIsThePresent, new Dollar(100), employeeName);
			Assert.AreEqual(transaction1.Status, TransactionStatus.DRAFT);
			Assert.AreEqual(transaction1.TransactionDate, saleToday);

			var journalEntryNumber = marketplace.NewJournalEntryNumber();
			TransactionCompleted result1 = transaction1.Approve(approveToday, itIsThePresent, employeeName, journalEntryNumber);

			TransactionResult resultStats1 = (TransactionResult)result1.Result;
			Assert.AreEqual(new Dollar(50m).Value, resultStats1.Comission.Value);
			Assert.AreEqual(new Dollar(100m).Value, resultStats1.Gross.Value);
			Assert.AreEqual(new Dollar(50m).Value, resultStats1.Net.Value);
			Assert.AreEqual(new Btc(0.83333333m).Value, resultStats1.Amount.Value);

			try
			{
				marketplace.ConversionSpreadAt(today1.AddMilliseconds(-1), Coinage.Coin(Currencies.CODES.USD), Coinage.Coin(Currencies.CODES.BTC));
				Assert.Fail($"There is no a exchange rate for dates before {today1}.");
			}
			catch (GameEngineException e)
			{
			}
		}

		[TestMethod]
		[Description("Tipo de cambio viejo, existió pero ya no está vigente ni activo")]
		public void USD2BTC_New_Rate_NonValidRate_TimeExpired()
		{
			bool itIsThePresent = false;
			DateTime today1 = DateTime.Now;
			DateTime today2 = today1.AddSeconds(49);
			DateTime saleToday = today1.AddHours(3);
			DateTime approveToday = today1.AddHours(5);
			Company company = new Company();CurrenciesTest.AddCurrencies();
			company.Sales.CreateStore(1, "Test Store").MakeCurrent();
			string ced="abc123";string accountNumber = "B15461";Customer customer=company.GetOrCreateCustomerById(accountNumber);customer.Identifier=ced;
			CustomerAccount bitcoinAccount = customer.CreateNewAccountFor(Coinage.Coin(Currencies.CODES.BTC));
			Marketplace marketplace = new Marketplace(company, "CR");
			var a = (GamesEngine.Exchange.Agent)marketplace.AddAgent("A");
			var b = a.AddCashier("Juan");
			string employeeName = "Juan";
			Domain domain = company.Sales.CreateDomain(false, 1, "localhost", PaymentChannels.Agents.INSIDER);
			company.Sales.CurrentStore.Add(domain);
			GuardianTest.LoadProcessors(company);

			#region rate #1
			RegularConversionSpread rate1 = marketplace.CreateConversionSpread(1, today1, itIsThePresent, Coinage.Coin(Currencies.CODES.USD), Coinage.Coin(Currencies.CODES.BTC), 60m, 30m, employeeName);
			Assert.AreEqual(rate1, marketplace.ConversionSpreadAt(today1, Coinage.Coin(Currencies.CODES.USD), Coinage.Coin(Currencies.CODES.BTC)));
			#endregion

			BatchTransactions juanTransactions = BatchTest.SetInitialSettings(marketplace, today1, new Domain[] { domain });
			BatchSet agentBatch = juanTransactions.Batch();
			string agentPath = a.FullName;

			#region rate #2
			RegularConversionSpread rate2 = marketplace.CreateConversionSpread(2, today2, itIsThePresent, Coinage.Coin(Currencies.CODES.USD), Coinage.Coin(Currencies.CODES.BTC), 60m, 20m, employeeName);
			Assert.AreEqual(rate2, marketplace.ConversionSpreadAt(today2, Coinage.Coin(Currencies.CODES.USD), Coinage.Coin(Currencies.CODES.BTC)));
			Assert.AreEqual(rate2, marketplace.ConversionSpreadAt(today2.AddDays(1), Coinage.Coin(Currencies.CODES.USD), Coinage.Coin(Currencies.CODES.BTC)));
			#endregion

			//Necesita enviarse el id del rate a utilizar
			var id = marketplace.NewTransationNumber();
			SaleTransaction transaction1 = (SaleTransaction)marketplace.From(id, bitcoinAccount, agentPath, employeeName, domain).Sale(saleToday, itIsThePresent, new Dollar(100), employeeName);

			var journalEntryNumber = marketplace.NewJournalEntryNumber();
			TransactionCompleted result1 = transaction1.Approve(approveToday, itIsThePresent, employeeName, journalEntryNumber);

			TransactionResult resultStats1 = (TransactionResult)result1.Result;
			Assert.AreEqual(new Dollar(67m).Value, resultStats1.Comission.Value);
			Assert.AreEqual(new Dollar(100m).Value, resultStats1.Gross.Value);
			Assert.AreEqual(new Dollar(33m).Value, resultStats1.Net.Value);
			Assert.AreEqual(new Btc(0.55m).Value, resultStats1.Amount.Value);

			try
			{
				marketplace.ConversionSpreadAt(today1.AddMilliseconds(-1), Coinage.Coin(Currencies.CODES.USD), Coinage.Coin(Currencies.CODES.BTC));
				Assert.Fail($"There is no a exchange rate for dates before {today1}.");
			}
			catch (GameEngineException e)
			{
			}
		}

		[TestMethod]
		public void BTC2USD()
		{
			//TODO tipos de transaccions
			//reglas de aprobaci[on
			// liberar memoria
			//generar el numero de transacci[on como el numero de orden
			bool itIsThePresent = false;
			DateTime today = DateTime.Now;
			Company company = new Company();CurrenciesTest.AddCurrencies();
			company.Sales.CreateStore(1, "Test Store").MakeCurrent();
			string ced="abc123";string accountNumber = "B15461";Customer customer=company.GetOrCreateCustomerById(accountNumber);customer.Identifier=ced;
			CustomerAccount dollarAccount = customer.CreateNewAccountFor(Coinage.Coin(Currencies.CODES.USD));
			Marketplace marketplace = new Marketplace(company, "CR");
			var a = (GamesEngine.Exchange.Agent)marketplace.AddAgent("A");
			var b = a.AddCashier("Juan");
			string employeeName = "Juan";
			string description = "Decription";
			Domain domain = company.Sales.CreateDomain(false, 1, "localhost", PaymentChannels.Agents.INSIDER);
			company.Sales.CurrentStore.Add(domain);
			GuardianTest.LoadProcessors(company);

			#region rate #1
			RegularConversionSpread rate1 = marketplace.CreateConversionSpread(1, today, itIsThePresent, Coinage.Coin(Currencies.CODES.USD), Coinage.Coin(Currencies.CODES.BTC), 60m, 30m, employeeName);
			Assert.AreEqual(rate1, marketplace.ConversionSpreadAt(today, Coinage.Coin(Currencies.CODES.USD), Coinage.Coin(Currencies.CODES.BTC)));

			RegularConversionSpread rate2 = marketplace.CreateConversionSpread(2, today, itIsThePresent, Coinage.Coin(Currencies.CODES.BTC), Coinage.Coin(Currencies.CODES.USD), 0.03333334m, 0.01666667m, employeeName);
			Assert.AreEqual(rate2, marketplace.ConversionSpreadAt(today, Coinage.Coin(Currencies.CODES.BTC), Coinage.Coin(Currencies.CODES.USD)));
			#endregion

			BatchTransactions juanTransactions = BatchTest.SetInitialSettings(marketplace, today, new Domain[] { domain });
			BatchSet agentBatch = juanTransactions.Batch();
			string agentPath = a.FullName;

			int id = marketplace.NewTransationNumber();

			SaleTransaction transaction1 = (SaleTransaction)marketplace.From(id, dollarAccount, agentPath, employeeName, domain).Sale(today, itIsThePresent, new Btc(100), employeeName);
			var journalEntryNumber = marketplace.NewJournalEntryNumber();
			TransactionCompleted result1 = transaction1.Approve(today, itIsThePresent, employeeName, journalEntryNumber);

			TransactionResult resultStats1 = (TransactionResult)result1.Result;
			Assert.AreEqual(new Btc(50m), resultStats1.Comission);
			Assert.AreEqual(new Btc(100m), resultStats1.Gross);
			Assert.AreEqual(new Btc(50m), resultStats1.Net);
			Assert.AreEqual(new Dollar(1500.00m), resultStats1.Amount);
		}

		[TestMethod]
		public void BTC2USDZeroProfit()
		{
			bool itIsThePresent = false;
			DateTime today = DateTime.Now;
			Company company = new Company();CurrenciesTest.AddCurrencies();
			company.Sales.CreateStore(1, "Test Store").MakeCurrent();
			string ced="abc123";string accountNumber = "B15461";Customer customer=company.GetOrCreateCustomerById(accountNumber);customer.Identifier=ced;
			CustomerAccount dollarAccount = customer.CreateNewAccountFor(Coinage.Coin(Currencies.CODES.USD));
			CustomerAccount bitcoinAccount = customer.CreateNewAccountFor(Coinage.Coin(Currencies.CODES.BTC));
			Marketplace marketplace = new Marketplace(company, "CR");
			string employeeName = "Juan";
			string description = "Decription";
			Domain domain = company.Sales.CreateDomain(false, 1, "localhost", PaymentChannels.Agents.INSIDER);
			company.Sales.CurrentStore.Add(domain);

			try
			{
				#region rate #1
				RegularConversionSpread rate1 = marketplace.CreateConversionSpread(1, today, itIsThePresent, Coinage.Coin(Currencies.CODES.USD), Coinage.Coin(Currencies.CODES.BTC), 60m, 60m, employeeName);
				Assert.AreEqual(rate1, marketplace.ConversionSpreadAt(today, Coinage.Coin(Currencies.CODES.USD), Coinage.Coin(Currencies.CODES.BTC)));

				RegularConversionSpread rate2 = marketplace.CreateConversionSpread(2, today, itIsThePresent, Coinage.Coin(Currencies.CODES.BTC), Coinage.Coin(Currencies.CODES.USD), 0.03333334m, 0.03333334m, employeeName);
				Assert.AreEqual(rate2, marketplace.ConversionSpreadAt(today, Coinage.Coin(Currencies.CODES.BTC), Coinage.Coin(Currencies.CODES.USD)));

				#endregion
				Assert.Fail("The markup must be higher than 0");
			}
			catch { }
		}

		[TestMethod]
		public void TransactionsIds()
		{
			bool itIsThePresent = false;
			DateTime today = DateTime.Now;
			Company company = new Company();CurrenciesTest.AddCurrencies();
			company.Sales.CreateStore(1, "Test Store").MakeCurrent();
			string ced="abc123";string accountNumber = "B15461";Customer customer=company.GetOrCreateCustomerById(accountNumber);customer.Identifier=ced;
			CustomerAccount dollarAccount = customer.CreateNewAccountFor(Coinage.Coin(Currencies.CODES.USD));
			CustomerAccount bitcoinAccount = customer.CreateNewAccountFor(Coinage.Coin(Currencies.CODES.BTC));
			Marketplace marketplace = new Marketplace(company, "CR");
			var a = (GamesEngine.Exchange.Agent)marketplace.AddAgent("A");
			var b = a.AddCashier("Juan");
			string employeeName = "Juan";
			Domain domain = company.Sales.CreateDomain(false, 1, "localhost", PaymentChannels.Agents.INSIDER);
			company.Sales.CurrentStore.Add(domain);
			GuardianTest.LoadProcessors(company);

			#region rate #1
			RegularConversionSpread rate1 = marketplace.CreateConversionSpread(1, today, itIsThePresent, Coinage.Coin(Currencies.CODES.USD), Coinage.Coin(Currencies.CODES.BTC), 60m, 30m, employeeName);
			Assert.AreEqual(rate1, marketplace.ConversionSpreadAt(today, Coinage.Coin(Currencies.CODES.USD), Coinage.Coin(Currencies.CODES.BTC)));

			RegularConversionSpread rate2 = marketplace.CreateConversionSpread(2, today, itIsThePresent, Coinage.Coin(Currencies.CODES.BTC), Coinage.Coin(Currencies.CODES.USD), 0.03333334m, 0.01666667m, employeeName);
			Assert.AreEqual(rate2, marketplace.ConversionSpreadAt(today, Coinage.Coin(Currencies.CODES.BTC), Coinage.Coin(Currencies.CODES.USD)));
			#endregion

			BatchTransactions juanTransactions = BatchTest.SetInitialSettings(marketplace, today, new Domain[] { domain });
			BatchSet agentBatch = juanTransactions.Batch();
			string agentPath = a.FullName;

			int id = marketplace.NewTransationNumber();
			int id1 = marketplace.NewTransationNumber();
			int id2 = marketplace.NewTransationNumber();

			SaleTransaction transaction1 = (SaleTransaction)marketplace.From(10, dollarAccount, agentPath, employeeName, domain).Sale(today, itIsThePresent, new Btc(100), employeeName);
			int id11 = marketplace.NewTransationNumber();

			Assert.AreEqual(1, id);
			Assert.AreEqual(2, id1);
			Assert.AreEqual(3, id2);
			Assert.AreEqual(11, id11);
		}

		[TestMethod]
		public void Deposit()
		{
			CurrenciesTest.AddCurrencies();
			Company company;
			Guardian guardian;
			QueueInMemory queue;
			DateTime today = DateTime.Now;
			GuardianTest.Init(today, out company, out guardian, out queue);

			bool itIsThePresent = false;
			DateTime today1 = DateTime.Now;
			DateTime today2 = today1.AddMilliseconds(1);
			//Company company = new Company();CurrenciesTest.AddCurrencies();
		//	company.Sales.CreateStore(1, "Test Store").MakeCurrent();
			string ced="abc123";string accountNumber = "B15461";Customer customer=company.GetOrCreateCustomerById(accountNumber);customer.Identifier=ced;
			CustomerAccount bitcoinAccount = customer.CreateNewAccountFor(Coinage.Coin(Currencies.CODES.BTC));
			Marketplace marketplace = new Marketplace(company, "CR");
			var a = (GamesEngine.Exchange.Agent)marketplace.AddAgent("A");
			var b = a.AddCashier("Juan");
			string employeeName = "Juan";
			Domain domain = company.Sales.CreateDomain(false, 1, "localhost", PaymentChannels.Agents.INSIDER);
			//company.Sales.CurrentStore.Add(domain);

			GuardianTest.LoadProcessors(company);
			PaymentProcessorsAndActionsByDomains processors = marketplace.PaymentProcessors();
			PaymentProcessor GuardianBTCDriver = processors.SearchById("TEST-Secrets-TestDepositBTC-BTC-1");

			#region rate #1
			RegularConversionSpread rate1 = marketplace.CreateConversionSpread(1, today1, itIsThePresent, Coinage.Coin(Currencies.CODES.USD), Coinage.Coin(Currencies.CODES.BTC), 60m, 30m, employeeName);
			Assert.AreEqual(rate1, marketplace.ConversionSpreadAt(today1, Coinage.Coin(Currencies.CODES.USD), Coinage.Coin(Currencies.CODES.BTC)));
			#endregion

			BatchTransactions juanTransactions = BatchTest.SetInitialSettings(marketplace, today1, new Domain[] { domain });
			BatchSet agentBatch = juanTransactions.Batch();
			string agentPath = a.FullName;

			int id = marketplace.NewTransationNumber();

			var transactionType = marketplace.Company.System.TransactionTypes.SearchByName<ProcessorTransaction>(TransactionType.Deposit.ToString());
			int authorizationNumber = PaymentChannels.Deposit(
				itIsThePresent,
				Coinage.Coin(Currencies.CODES.USD),
				domain,
				transactionType,
				new DespositBody(
					accountNumber,
					ced,
					100,
					"deposit test",
					today1,
					"",
					"cris",
					domain)
				).AuthorizationId;

			DepositTransaction transaction1 = (DepositTransaction)marketplace.From(id, bitcoinAccount, agentPath, employeeName, domain).Deposit(today1, itIsThePresent, new Dollar(100), employeeName, (PaymentProcessor)marketplace.Company.System.PaymentProcessor.First(), 1);
			DepositTransaction transaction = (DepositTransaction)marketplace.FindDraftTransaction(transaction1.Id, agentPath, employeeName);

			Assert.AreEqual(transaction1, transaction);

			TransactionCompleted result1 = marketplace.Approve(today1, itIsThePresent, transaction1, 1, employeeName);
			TransactionResult resultStats = (TransactionResult)result1.Result;
			Assert.AreEqual(new Dollar(50m), resultStats.Comission);
			Assert.AreEqual(new Dollar(100m), resultStats.Gross);
			Assert.AreEqual(new Dollar(50m), resultStats.Net);
			Assert.AreEqual(new Btc(0.83333333m), resultStats.Amount);

			transactionType = marketplace.Company.System.TransactionTypes.SearchByName<ProcessorTransaction>(TransactionType.Deposit.ToString());
			authorizationNumber = PaymentChannels.Deposit(
				itIsThePresent,
				Coinage.Coin(Currencies.CODES.BTC),
				domain,
				transactionType,
				new DespositBody(
					accountNumber,
					ced,
					100,
					"deposit test",
					today1,
					"",
					"cris",
					domain)
				).AuthorizationId;

			DepositTransaction transaction2 = (DepositTransaction)marketplace.From(id, bitcoinAccount, agentPath, employeeName, domain).Deposit(today1, itIsThePresent, new Btc(100), employeeName, (PaymentProcessor)marketplace.Company.System.PaymentProcessor.First(), 1);
			Assert.AreEqual(true, transaction2.ConversionSpread is NoConversionSpread);
			//GamesEngine.Custodian.PaymentMethodsActions.ResetInstance();
		}

		[TestMethod]
		public void WithDraw()
		{
			DateTime today1 = DateTime.Now;
			bool itIsThePresent = false;
			DateTime today2 = today1.AddMilliseconds(1);
			Company company = new Company();CurrenciesTest.AddCurrencies();
			company.Sales.CreateStore(1, "Test Store").MakeCurrent();
			string ced="abc123";string accountNumber = "B15461";Customer customer=company.GetOrCreateCustomerById(accountNumber);customer.Identifier=ced;
			CustomerAccount dollarAccount = customer.CreateNewAccountFor(Coinage.Coin(Currencies.CODES.USD));
			CustomerAccount bitcoinAccount = customer.CreateNewAccountFor(Coinage.Coin(Currencies.CODES.BTC));
			Marketplace marketplace = new Marketplace(company, "CR");
			marketplace.RegisterNewAccount(1, dollarAccount);
			marketplace.RegisterNewAccount(2, bitcoinAccount);
			string employeeName = "Juan";
			Domain domain = company.Sales.CreateDomain(false, 1, "localhost", PaymentChannels.Agents.INSIDER);
			company.Sales.CurrentStore.Add(domain);

			#region rate #1
			RegularConversionSpread rate1 = marketplace.CreateConversionSpread(1, today1, itIsThePresent, Coinage.Coin(Currencies.CODES.USD), Coinage.Coin(Currencies.CODES.BTC), 60m, 30m, employeeName);
			Assert.AreEqual(rate1, marketplace.ConversionSpreadAt(today1, Coinage.Coin(Currencies.CODES.USD), Coinage.Coin(Currencies.CODES.BTC)));
			#endregion

			try
			{
				#region rate #2
				RegularConversionSpread rate2 = marketplace.CreateConversionSpread(2, today1, itIsThePresent, Coinage.Coin(Currencies.CODES.USD), Coinage.Coin(Currencies.CODES.USD), 1, 1, employeeName);
				Assert.AreEqual(rate2, marketplace.ConversionSpreadAt(today1, Coinage.Coin(Currencies.CODES.USD), Coinage.Coin(Currencies.CODES.USD)));
				#endregion
				Assert.Fail();
			}
			catch { }
		}

		[TestMethod]
		public void WithdrawalWithFee()
		{
			QueueInMemory queue = new QueueInMemory();
			Integration.Kafka = queue;

			DateTime today1 = DateTime.Now;
			bool itIsThePresent = true;
			DateTime today2 = today1.AddMilliseconds(1);
			Company company = new Company();CurrenciesTest.AddCurrencies();
			company.Sales.CreateStore(1, "Test Store").MakeCurrent();
			string ced="abc123";string accountNumber = "B15461";Customer customer=company.GetOrCreateCustomerById(accountNumber);customer.Identifier=ced;
			CustomerAccount dollarAccount = customer.CreateNewAccountFor(Coinage.Coin(Currencies.CODES.USD));
			CustomerAccount bitcoinAccount = customer.CreateNewAccountFor(Coinage.Coin(Currencies.CODES.BTC));
			Marketplace marketplace = new Marketplace(company, "CR");
			var a = (GamesEngine.Exchange.Agent)marketplace.AddAgent("A");
			var b = a.AddCashier("Juan");
			marketplace.RegisterNewAccount(1, dollarAccount);
			marketplace.RegisterNewAccount(2, bitcoinAccount);
			string employeeName = "Juan";
			Domain domain = company.Sales.CreateDomain(false, 1, "localhost", PaymentChannels.Agents.INSIDER);
			company.Sales.CurrentStore.Add(domain);
			GuardianTest.LoadProcessors(company);

			var outcomes = marketplace.TransactionOutcomes();
			outcomes.CreatesOutcomesFor(domain);

			#region rate #1
			RegularConversionSpread rate1 = marketplace.CreateConversionSpread(1, today1, itIsThePresent, Coinage.Coin(Currencies.CODES.USD), Coinage.Coin(Currencies.CODES.BTC), 60m, 30m, employeeName);
			Assert.AreEqual(rate1, marketplace.ConversionSpreadAt(today1, Coinage.Coin(Currencies.CODES.USD), Coinage.Coin(Currencies.CODES.BTC)));
			#endregion

			BatchTransactions juanTransactions = BatchTest.SetInitialSettings(marketplace, today1, new Domain[] { domain });
			BatchSet agentBatch = juanTransactions.Batch();
			string agentPath = a.FullName;

			int id = marketplace.NewTransationNumber();
			var feeAmount = Currency.Factory(Coinage.Coin(Currencies.CODES.USD), 0.1m);
			var minerFee = new MinerFee(feeAmount);
			var processor = marketplace.Company.System.PaymentProcessor.SearchWithdrawalProcessorBy("USD");
			WithdrawalTransaction transaction1 = (WithdrawalTransaction)marketplace.From(id, dollarAccount, agentPath, employeeName, domain).Withdraw(today1, itIsThePresent, new Dollar(100), 1, employeeName, "1-11", "", minerFee, processor, 1);
			var journalEntryNumber = marketplace.NewJournalEntryNumber();
			TransactionCompleted result1 = transaction1.Approve(today1, itIsThePresent, employeeName, journalEntryNumber);

			var resultStats = (WithdrawalTransactionResult)result1.Result;
			Assert.AreEqual(new Dollar(0m), resultStats.Comission);
			Assert.AreEqual(new Dollar(100m), resultStats.Gross);
			Assert.AreEqual(new Dollar(99.9m), resultStats.Net);
			Assert.AreEqual(new Dollar(100m), resultStats.Amount);
			Assert.AreEqual(new Dollar(99.9m), resultStats.Disbursement);
			Assert.AreEqual(new Dollar(0.1m), resultStats.TotalFee);


			string msn = queue.Dequeue(Integration.Kafka.TopicForGuardianOperations);
			OperationMessage operationMessage = new OperationMessage(msn);
			Assert.AreEqual( resultStats.Disbursement.Value, operationMessage.DisbursementAmount.Value);
			Assert.AreEqual( resultStats.TotalFee.Value, operationMessage.Fees.Total().Value);
			//GamesEngine.Custodian.PaymentMethodsActions.ResetInstance();
		}

		[TestMethod]
		public void WithdrawalWithFee_No_ExistingDomain()
		{
			QueueInMemory queue = new QueueInMemory();
			Integration.Kafka = queue;

			DateTime today1 = DateTime.Now;
			bool itIsThePresent = true;
			DateTime today2 = today1.AddMilliseconds(1);
			Company company = new Company();CurrenciesTest.AddCurrencies();
			company.Sales.CreateStore(1, "Test Store").MakeCurrent();
			string ced = "abc123"; string accountNumber = "B15461"; Customer customer = company.GetOrCreateCustomerById(accountNumber); customer.Identifier = ced;
			CustomerAccount dollarAccount = customer.CreateNewAccountFor(Coinage.Coin(Currencies.CODES.USD));
			CustomerAccount bitcoinAccount = customer.CreateNewAccountFor(Coinage.Coin(Currencies.CODES.BTC));
			Marketplace marketplace = new Marketplace(company, "CR");
			var a = (GamesEngine.Exchange.Agent)marketplace.AddAgent("A");
			var b = a.AddCashier("Juan");
			marketplace.RegisterNewAccount(1, dollarAccount);
			marketplace.RegisterNewAccount(2, bitcoinAccount);
			string employeeName = "Juan";
			Domain domain = company.Sales.CreateDomain(false, 1, "localhost", PaymentChannels.Agents.INSIDER);
			company.Sales.CurrentStore.Add(domain);
			Domain domain2 = company.Sales.CreateDomain(false, 2, "localhost2", PaymentChannels.Agents.INSIDER);
			company.Sales.CurrentStore.Add(domain2);
			GuardianTest.LoadProcessors(company);

			#region rate #1
			RegularConversionSpread rate1 = marketplace.CreateConversionSpread(1, today1, itIsThePresent, Coinage.Coin(Currencies.CODES.USD), Coinage.Coin(Currencies.CODES.BTC), 60m, 30m, employeeName);
			Assert.AreEqual(rate1, marketplace.ConversionSpreadAt(today1, Coinage.Coin(Currencies.CODES.USD), Coinage.Coin(Currencies.CODES.BTC)));
			#endregion

			BatchTransactions juanTransactions = BatchTest.SetInitialSettings(marketplace, today1, new Domain[] { domain });
			BatchSet agentBatch = juanTransactions.Batch();
			string agentPath = a.FullName;

			int id = marketplace.NewTransationNumber();
			var feeAmount = Currency.Factory(Coinage.Coin(Currencies.CODES.USD), 0.1m);
			var minerFee = new MinerFee(feeAmount);

			try
			{
				WithdrawalTransaction transaction1 = (WithdrawalTransaction)marketplace.From(id, dollarAccount, agentPath, employeeName, domain2).Withdraw(today1, itIsThePresent, new Dollar(100), 1, employeeName, "1-11", "", minerFee, (PaymentProcessor)marketplace.Company.System.PaymentProcessor.First(), 1);
			}
			catch (GameEngineException e)
			{ 
			}
			
		
		}

		[TestMethod]
		public void Transfer_with_different_target_currency()
		{
			DateTime today1 = DateTime.Now;
			bool itIsThePresent = false;
			DateTime today2 = today1.AddMilliseconds(1);
			Company company = new Company();CurrenciesTest.AddCurrencies();
			company.Sales.CreateStore(1, "Test Store").MakeCurrent();
			string ced="abc123";string accountNumber = "B15461";Customer customer=company.GetOrCreateCustomerById(accountNumber);customer.Identifier=ced;
			string ced2="abc1234";string accountNumber2 = "B15462";Customer toCustomer=company.GetOrCreateCustomerById(accountNumber2);toCustomer.Identifier=ced2;
			CustomerAccount dollarAccount = customer.CreateNewAccountFor(Coinage.Coin(Currencies.CODES.USD));
			CustomerAccount bitcoinAccount = toCustomer.CreateNewAccountFor(Coinage.Coin(Currencies.CODES.BTC));
			Marketplace marketplace = new Marketplace(company, "CR");
			var a = (GamesEngine.Exchange.Agent)marketplace.AddAgent("A");
			var b = a.AddCashier("Juan");
			marketplace.RegisterNewAccount(1, dollarAccount);
			marketplace.RegisterNewAccount(2, bitcoinAccount);
			string employeeName = "Juan";
			Domain domain = company.Sales.CreateDomain(false, 1, "localhost", PaymentChannels.Agents.INSIDER);
			company.Sales.CurrentStore.Add(domain);
			GuardianTest.LoadProcessors(company);

			#region rate #1
			RegularConversionSpread rate1 = marketplace.CreateConversionSpread(1, today1, itIsThePresent, Coinage.Coin(Currencies.CODES.USD), Coinage.Coin(Currencies.CODES.BTC), 60m, 30m, employeeName);
			Assert.AreEqual(rate1, marketplace.ConversionSpreadAt(today1, Coinage.Coin(Currencies.CODES.USD), Coinage.Coin(Currencies.CODES.BTC)));
			#endregion

			BatchTransactions juanTransactions = BatchTest.SetInitialSettings(marketplace, today1, new Domain[] { domain });
			BatchSet agentBatch = juanTransactions.Batch();
			string agentPath = a.FullName;

			int id = marketplace.NewTransationNumber();

			TransferTransaction transaction1 = (TransferTransaction)marketplace.From(id, dollarAccount, agentPath, employeeName, domain).TransferTo(today1, itIsThePresent, new Dollar(100), 1, bitcoinAccount, employeeName, (PaymentProcessor)marketplace.Company.System.PaymentProcessor.First(), 1);
			var journalEntryNumber = marketplace.NewJournalEntryNumber();
			TransactionCompleted result1 = transaction1.Approve(today1, itIsThePresent, employeeName, journalEntryNumber);
			TransactionResult resultStats = result1.Result;

			Assert.AreEqual(new Dollar(50m), resultStats.Comission);
			Assert.AreEqual(new Dollar(100m), resultStats.Gross);
			Assert.AreEqual(new Dollar(50m), resultStats.Net);
			Assert.AreEqual(new Btc(0.83333333m), resultStats.Amount);
		}

		[TestMethod]
		public void Transfer_with_same_default_target_currency()
		{
			DateTime today1 = DateTime.Now;
			bool itIsThePresent = false;
			DateTime today2 = today1.AddMilliseconds(1);
			Company company = new Company();CurrenciesTest.AddCurrencies();
			company.Sales.CreateStore(1, "Test Store").MakeCurrent();
			string ced="abc123";string accountNumber = "B15461";Customer customer=company.GetOrCreateCustomerById(accountNumber);customer.Identifier=ced;
			string ced2="abc1234";string accountNumber2 = "B15462";Customer toCustomer=company.GetOrCreateCustomerById(accountNumber2);toCustomer.Identifier=ced2;
			CustomerAccount dollarAccount = customer.CreateNewAccountFor(Coinage.Coin(Currencies.CODES.USD));
			CustomerAccount targetAccount = toCustomer.FindAccountByCurrency(Coinage.Coin(Currencies.CODES.USD));
			Marketplace marketplace = new Marketplace(company, "CR");
			var a = (GamesEngine.Exchange.Agent)marketplace.AddAgent("A");
			var b = a.AddCashier("Juan");
			marketplace.RegisterNewAccount(1, dollarAccount);
			marketplace.RegisterNewAccount(2, targetAccount);
			string employeeName = "Juan";
			Domain domain = company.Sales.CreateDomain(false, 1, "localhost", PaymentChannels.Agents.INSIDER);
			company.Sales.CurrentStore.Add(domain);
			GuardianTest.LoadProcessors(company);

			#region rate #1
			RegularConversionSpread rate1 = marketplace.CreateConversionSpread(1, today1, itIsThePresent, Coinage.Coin(Currencies.CODES.USD), Coinage.Coin(Currencies.CODES.BTC), 60m, 30m, employeeName);
			Assert.AreEqual(rate1, marketplace.ConversionSpreadAt(today1, Coinage.Coin(Currencies.CODES.USD), Coinage.Coin(Currencies.CODES.BTC)));
			#endregion

			BatchTransactions juanTransactions = BatchTest.SetInitialSettings(marketplace, today1, new Domain[] { domain });
			BatchSet agentBatch = juanTransactions.Batch();
			string agentPath = a.FullName;

			int id = marketplace.NewTransationNumber();

			TransferTransaction transaction1 = (TransferTransaction)marketplace.From(id, dollarAccount, agentPath, employeeName, domain).TransferTo(today1, itIsThePresent, new Dollar(100), 1, toCustomer, employeeName, (PaymentProcessor)marketplace.Company.System.PaymentProcessor.First(), 1);
			var journalEntryNumber = marketplace.NewJournalEntryNumber();
			TransactionCompleted result1 = transaction1.Approve(today1, itIsThePresent, employeeName, journalEntryNumber);
			TransactionResult resultStats = result1.Result;

			Assert.AreEqual(new Dollar(0m), resultStats.Comission);
			Assert.AreEqual(new Dollar(100m), resultStats.Gross);
			Assert.AreEqual(new Dollar(100m), resultStats.Net);
			Assert.AreEqual(new Dollar(100m), resultStats.Amount);

		}
		[TestMethod]
		public void Pay_lowest_convertion()
		{
			CurrenciesTest.AddCurrencies();
			Company company;
			Guardian guardian;
			QueueInMemory queue;
			DateTime today = DateTime.Now;
			GuardianTest.Init(today, out company, out guardian, out queue);


			/*
			Purchase BTC/USD 7000 USD/BTC 0.********
			Venta BTC/USD 7500	  USD/BTC  0.********

			BTC ->ventanilla
			markup -> 0.07

			20 BTC Lo que el cliente trae
			20 * 0.07 = 1.4BTC  -> profit del exchange


			20BTC -1.4BTC= 18.6BTC Neto
			18.6 BTC* 7500 = $139 5000 Plata para el cliente
			18.6 BTC / 0.******** = 135503.49 Plata para el cliente -> revisar
			 * 
			 */
			bool itIsThePresent = false;
			DateTime today1 = DateTime.Now;
			DateTime today2 = today1.AddMilliseconds(1);
			//Company company = new Company();CurrenciesTest.AddCurrencies();
//			company.Sales.CreateStore(1, "Test Store").MakeCurrent();
			string ced="abc123";string accountNumber = "B15461";Customer customer=company.GetOrCreateCustomerById(accountNumber);customer.Identifier=ced;
			CustomerAccount dollarAccount = customer.CreateNewAccountFor(Coinage.Coin(Currencies.CODES.USD));
			Marketplace marketplace = new Marketplace(company, "CR");
			var a = (GamesEngine.Exchange.Agent)marketplace.AddAgent("A");
			var b = a.AddCashier("Juan");
			string employeeName = "Juan";
			Domain domain = company.Sales.CreateDomain(false, 1, "localhost", PaymentChannels.Agents.INSIDER);
		//	company.Sales.CurrentStore.Add(domain);

			GuardianTest.LoadProcessors(company);
			PaymentProcessorsAndActionsByDomains processors = marketplace.PaymentProcessors();
			PaymentProcessor GuardianBTCDriver = processors.SearchById("TEST-Secrets-TestDepositBTC-BTC-1");

			#region rate #1
			RegularConversionSpread rate1 = marketplace.CreateConversionSpread(1, today1, itIsThePresent, Coinage.Coin(Currencies.CODES.USD), Coinage.Coin(Currencies.CODES.BTC), 7500m, 7000m, employeeName);
			Assert.AreEqual(rate1, marketplace.ConversionSpreadAt(today1, Coinage.Coin(Currencies.CODES.USD), Coinage.Coin(Currencies.CODES.BTC)));

			RegularConversionSpread rate2 = marketplace.CreateConversionSpread(2, today1, itIsThePresent, Coinage.Coin(Currencies.CODES.BTC), Coinage.Coin(Currencies.CODES.USD), 1.33333333m, 1.24444444m, employeeName);
			Assert.AreEqual(rate2, marketplace.ConversionSpreadAt(today1, Coinage.Coin(Currencies.CODES.BTC), Coinage.Coin(Currencies.CODES.USD)));

			#endregion

			BatchTransactions juanTransactions = BatchTest.SetInitialSettings(marketplace, today1, new Domain[] { domain });
			BatchSet agentBatch = juanTransactions.Batch();
			string agentPath = a.FullName;

			int id = marketplace.NewTransationNumber();

			var transactionType = marketplace.Company.System.TransactionTypes.SearchByName<ProcessorTransaction>(TransactionType.Deposit.ToString());
			int authorizationNumber = PaymentChannels.Deposit(
				itIsThePresent,
				Coinage.Coin(Currencies.CODES.BTC),
				domain,
				transactionType,
				new DespositBody(
					accountNumber,
					ced,
					20,
					"deposit test", 
					today1,
					"", 
					"cris",
					domain)
				).AuthorizationId;

			DepositTransaction transaction1 = (DepositTransaction)marketplace.From(id, dollarAccount, agentPath, employeeName, domain).Deposit(today1, itIsThePresent, new Btc(20), employeeName, (PaymentProcessor)marketplace.Company.System.PaymentProcessor.First(), 1);
			DepositTransaction transaction = (DepositTransaction)marketplace.FindDraftTransaction(transaction1.Id, agentPath, employeeName);

			Assert.AreEqual(transaction1, transaction);

			TransactionCompleted result1 = marketplace.Approve(today1, itIsThePresent, transaction1, 1, employeeName);
			TransactionResult resultStats = (TransactionResult)result1.Result;
			
			Assert.AreEqual(new Btc(1.33333340m), resultStats.Comission);
			Assert.AreEqual(new Btc(20m), resultStats.Gross);
			Assert.AreEqual(new Btc(18.6666666m), resultStats.Net);
			Assert.AreEqual(new Dollar(13.99999999m), resultStats.Amount);
			//GamesEngine.Custodian.PaymentMethodsActions.ResetInstance();
		}

		[TestMethod]
		public void Pay_lowest_convertion_v2()
		{
			CurrenciesTest.AddCurrencies();
			Company company;
			Guardian guardian;
			QueueInMemory queue;
			DateTime today = DateTime.Now;
			GuardianTest.Init(today, out company, out guardian, out queue);

			bool itIsThePresent = false;
			DateTime today1 = DateTime.Now;
			DateTime today2 = today1.AddMilliseconds(1);
			//Company company = new Company();CurrenciesTest.AddCurrencies();
			//company.Sales.CreateStore(1, "Test Store").MakeCurrent();
			string ced="abc123";string accountNumber = "B15461";Customer customer=company.GetOrCreateCustomerById(accountNumber);customer.Identifier=ced;
			CustomerAccount dollarAccount = customer.CreateNewAccountFor(Coinage.Coin(Currencies.CODES.USD));
			Marketplace marketplace = new Marketplace(company, "CR");
			var a = (GamesEngine.Exchange.Agent)marketplace.AddAgent("A");
			var b = a.AddCashier("Juan");
			string employeeName = "Juan";
			Domain domain = company.Sales.CreateDomain(false, 1, "localhost", PaymentChannels.Agents.INSIDER);
		//	company.Sales.CurrentStore.Add(domain);

			GuardianTest.LoadProcessors(company);
			PaymentProcessorsAndActionsByDomains processors = marketplace.PaymentProcessors();
			PaymentProcessor GuardianBTCDriver = processors.SearchById("TEST-Secrets-TestDepositBTC-BTC-1");

			#region rate #1
			RegularConversionSpread rate1 = marketplace.CreateConversionSpread(1, today1, itIsThePresent, Coinage.Coin(Currencies.CODES.USD), Coinage.Coin(Currencies.CODES.BTC), 7500m, 7000m, employeeName);
			Assert.AreEqual(rate1, marketplace.ConversionSpreadAt(today1, Coinage.Coin(Currencies.CODES.USD), Coinage.Coin(Currencies.CODES.BTC)));

			RegularConversionSpread rate2 = marketplace.CreateConversionSpread(2, today1, itIsThePresent, Coinage.Coin(Currencies.CODES.BTC), Coinage.Coin(Currencies.CODES.USD), 1.33333333m, 1.33333332m, employeeName);
			Assert.AreEqual(rate2, marketplace.ConversionSpreadAt(today1, Coinage.Coin(Currencies.CODES.BTC), Coinage.Coin(Currencies.CODES.USD)));

			#endregion

			BatchTransactions juanTransactions = BatchTest.SetInitialSettings(marketplace, today1, new Domain[] { domain });
			BatchSet agentBatch = juanTransactions.Batch();
			string agentPath = a.FullName;

			int id = marketplace.NewTransationNumber();

			var transactionType = marketplace.Company.System.TransactionTypes.SearchByName<ProcessorTransaction>(TransactionType.Deposit.ToString());
			int authorizationNumber = PaymentChannels.Deposit(
				itIsThePresent,
				Coinage.Coin(Currencies.CODES.BTC),
				domain,
				transactionType,
				new DespositBody(
					accountNumber,
					ced,
					20,
					"deposit test",
					today1,
					"",
					"cris",
					domain)).AuthorizationId;

			DepositTransaction transaction1 = (DepositTransaction)marketplace.From(id, dollarAccount, agentPath, employeeName, domain).Deposit(today1, itIsThePresent, new Btc(20), employeeName, (PaymentProcessor)marketplace.Company.System.PaymentProcessor.First(), 1);
			DepositTransaction transaction = (DepositTransaction)marketplace.FindDraftTransaction(transaction1.Id, agentPath, employeeName);

			Assert.AreEqual(transaction1, transaction);

			TransactionCompleted result1 = marketplace.Approve(today1, itIsThePresent, transaction1, 1, employeeName);
			TransactionResult resultStats = (TransactionResult)result1.Result;

			Assert.AreEqual(new Btc(0.0000002m), resultStats.Comission);
			Assert.AreEqual(new Btc(20m), resultStats.Gross);
			Assert.AreEqual(new Btc(19.9999998m), resultStats.Net);
			Assert.AreEqual(new Dollar(14.99999989m), resultStats.Amount);
			//GamesEngine.Custodian.PaymentMethodsActions.ResetInstance();
		}

		[TestMethod]
		public void BatchProfitUSD2BTC()
		{
			/*
			3 BTC me costaron 28 500
			1BTc me costo $9 500  -> BTC/USD 9 500


			BTC/USD 9 600.00
			BTC/USD 9 600.01
			Rate  0.***********

			$10 -> BTC
			Comsion ->0.*********** * 100 = %  $0.01
			Net = 9.********** -> $ 9.99

			Converted = BTC 0.********

			BTC/USD 9 500 - BTC/USD 9 600.01 =100.01
			 * 
			 */
			bool itIsThePresent = false;
			DateTime today1 = DateTime.Now;
			DateTime today2 = today1.AddMilliseconds(1);
			Company company = new Company();CurrenciesTest.AddCurrencies();
			company.Sales.CreateStore(1, "Test Store").MakeCurrent();
			string ced="abc123";string accountNumber = "B15461";Customer customer=company.GetOrCreateCustomerById(accountNumber);customer.Identifier=ced;
			CustomerAccount bitcoinAccount = customer.CreateNewAccountFor(Coinage.Coin(Currencies.CODES.BTC));
			CustomerAccount dollarAccount = customer.CreateNewAccountFor(Coinage.Coin(Currencies.CODES.USD));
			Marketplace marketplace = new Marketplace(company, "CR");
			var a = (GamesEngine.Exchange.Agent)marketplace.AddAgent("A");
			var b = a.AddCashier("Juan");
			string employeeName = "Juan";
			Domain domain = company.Sales.CreateDomain(false, 1, "localhost", PaymentChannels.Agents.INSIDER);
			company.Sales.CurrentStore.Add(domain);
			GuardianTest.LoadProcessors(company);

			#region rate #1
			RegularConversionSpread rate1 = marketplace.CreateConversionSpread(1, today1, itIsThePresent, Coinage.Coin(Currencies.CODES.USD), Coinage.Coin(Currencies.CODES.BTC), 9600.01m, 9600.00m, employeeName);
			Assert.AreEqual(rate1, marketplace.ConversionSpreadAt(today1, Coinage.Coin(Currencies.CODES.USD), Coinage.Coin(Currencies.CODES.BTC)));
			#endregion

			BatchTransactions juanTransactions = BatchTest.SetInitialSettings(marketplace, today1, new Domain[] { domain });
			BatchSet agentBatch = juanTransactions.Batch();
			string agentPath = a.FullName;

			int id2 = marketplace.NewTransationNumber();

			var transactionType = marketplace.Company.System.TransactionTypes.SearchByName<ProcessorTransaction>(TransactionType.Deposit.ToString());
			int authorizationNumber = PaymentChannels.Deposit(
				itIsThePresent,
				Coinage.Coin(Currencies.CODES.USD),
				domain,
				transactionType,
				new DespositBody(
					accountNumber,
					ced,
					10,
					"deposit test",
					today1,
					"",
					"cris",
					domain)).AuthorizationId;

			DepositTransaction transaction2 = (DepositTransaction)marketplace.From(id2, bitcoinAccount, agentPath, employeeName, domain).Deposit(today1, itIsThePresent, new Dollar(10), employeeName, (PaymentProcessor)marketplace.Company.System.PaymentProcessor.First(), 1);
			DepositTransaction transaction22 = (DepositTransaction)marketplace.FindDraftTransaction(transaction2.Id, agentPath, employeeName);

			Assert.AreEqual(transaction2, transaction22);

			TransactionCompleted result2 = marketplace.Approve(today1, itIsThePresent, transaction2, 2, employeeName);
			TransactionResult resultStats2 = (TransactionResult)result2.Result;
			Assert.AreEqual(new Dollar(0.01m), resultStats2.Comission);
			Assert.AreEqual(new Dollar(10m), resultStats2.Gross);
			Assert.AreEqual(new Dollar(9.99m), resultStats2.Net);
			Assert.AreEqual(new Btc(0.********m), resultStats2.Amount);
			Assert.AreEqual(new Dollar(0.10m), resultStats2.Profit);
			//GamesEngine.Custodian.PaymentMethodsActions.ResetInstance();

			//	$10->BTC
			//	Comsion->0.*********** * 100 = %  $0.01
			//	Net = 9.**********-> $ 9.99

			//	Converted = BTC 0.********

			//	BTC / USD 9 500 - BTC / USD 9 600.01 = 100.01
			// USD / BTC 0.00001053 - USD / BTC 9 600.01 = 0.00001042
		}


		[TestMethod]
		public void Bug5369_USD_BTC_notUsed_BTC_USD_Transaction()
		{
			CurrenciesTest.AddCurrencies();
			Company company;
			Guardian guardian;
			QueueInMemory queue;
			DateTime today = DateTime.Now;
			GuardianTest.Init(today, out company, out guardian, out queue);

			bool itIsThePresent = false;
			DateTime today1 = DateTime.Now;
			DateTime today2 = today1.AddMilliseconds(1);
			//Company company = new Company();CurrenciesTest.AddCurrencies();
			//company.Sales.CreateStore(1, "Test Store").MakeCurrent();
			string ced="abc123";string accountNumber = "B15461";Customer customer=company.GetOrCreateCustomerById(accountNumber);customer.Identifier=ced;
			CustomerAccount bitcoinAccount = customer.CreateNewAccountFor(Coinage.Coin(Currencies.CODES.BTC));
			CustomerAccount dollarAccount = customer.CreateNewAccountFor(Coinage.Coin(Currencies.CODES.USD));
			Marketplace marketplace = new Marketplace(company, "CR");
			var a = (GamesEngine.Exchange.Agent)marketplace.AddAgent("A");
			var b = a.AddCashier("Juan");
			string employeeName = "Juan";
			Domain domain = company.Sales.CreateDomain(false, 1, "localhost", PaymentChannels.Agents.INSIDER);
			//company.Sales.CurrentStore.Add(domain);

			GuardianTest.LoadProcessors(company);
			PaymentProcessorsAndActionsByDomains processors = marketplace.PaymentProcessors();
			PaymentProcessor GuardianBTCDriver = processors.SearchById("TEST-Secrets-TestDepositBTC-BTC-1");

			#region rate #1
			RegularConversionSpread rate1 = marketplace.CreateConversionSpread(1, today1, itIsThePresent, Coinage.Coin(Currencies.CODES.USD), Coinage.Coin(Currencies.CODES.BTC), 9600.01m, 9600.00m, employeeName);
			Assert.AreEqual(rate1, marketplace.ConversionSpreadAt(today1, Coinage.Coin(Currencies.CODES.USD), Coinage.Coin(Currencies.CODES.BTC)));
			#endregion

			BatchTransactions juanTransactions = BatchTest.SetInitialSettings(marketplace, today1, new Domain[] { domain });
			BatchSet agentBatch = juanTransactions.Batch();
			string agentPath = a.FullName;

			int id2 = marketplace.NewTransationNumber();

			try
			{
				var transactionType = marketplace.Company.System.TransactionTypes.SearchByName<ProcessorTransaction>(TransactionType.Deposit.ToString());
				int authorizationNumber = PaymentChannels.Deposit(
					itIsThePresent,
					Coinage.Coin(Currencies.CODES.BTC),
					domain,
					transactionType,
					new DespositBody(
						accountNumber,
						ced,
						0.00000001m,
						"deposit test",
						today1,
						"",
						"cris",
						domain)).AuthorizationId;

				DepositTransaction transaction2 = (DepositTransaction)marketplace.From(id2, dollarAccount, agentPath, employeeName, domain).Deposit(today1, itIsThePresent, new Btc(0.00000001m), employeeName, (PaymentProcessor)marketplace.Company.System.PaymentProcessor.First(), 1);
				Assert.Fail("No rate already defined exception was expected. ");
			}
			catch (GameEngineException e)
			{
				Assert.AreEqual("There is no a Rate defined for USD/BTC ", e.Message);
			}
		}


		[TestMethod]
		public void BatchNegativeProfitUSD2BTC()
		{
			/*
			3 BTC me costaron 28 500
			1BTc me costo $9 700 


			BTC/USD 9 600.00
			BTC/USD 9 600.01
			Rate  0.***********

			$10 -> BTC
			Comsion ->0.*********** * 100 = %  $0.01
			Net = 9.********** -> $ 9.99

			Converted = BTC 0.********

			BTC/USD 9 700 - BTC/USD 9 600.01 = -99.99  por cada BTC vendido.
			 *  0.******** * -99.99 =  -0.********** -> -0.10
			 *  
			 */
			bool itIsThePresent = false;
			DateTime today1 = DateTime.Now;
			DateTime today2 = today1.AddMilliseconds(1);
			Company company = new Company();CurrenciesTest.AddCurrencies();
			company.Sales.CreateStore(1, "Test Store").MakeCurrent();
			string ced="abc123";string accountNumber = "B15461";Customer customer=company.GetOrCreateCustomerById(accountNumber);customer.Identifier=ced;
			CustomerAccount bitcoinAccount = customer.CreateNewAccountFor(Coinage.Coin(Currencies.CODES.BTC));
			CustomerAccount dollarAccount = customer.CreateNewAccountFor(Coinage.Coin(Currencies.CODES.USD));
			Marketplace marketplace = new Marketplace(company, "CR");
			var a = (GamesEngine.Exchange.Agent)marketplace.AddAgent("A");
			var b = a.AddCashier("Juan");
			string employeeName = "Juan";
			Domain domain = company.Sales.CreateDomain(false, 1, "localhost", PaymentChannels.Agents.INSIDER);
			company.Sales.CurrentStore.Add(domain);
			GuardianTest.LoadProcessors(company);

			#region rate #1
			RegularConversionSpread rate1 = marketplace.CreateConversionSpread(1, today1, itIsThePresent, Coinage.Coin(Currencies.CODES.USD), Coinage.Coin(Currencies.CODES.BTC), 9600.01m, 9600.00m, employeeName);
			Assert.AreEqual(rate1, marketplace.ConversionSpreadAt(today1, Coinage.Coin(Currencies.CODES.USD), Coinage.Coin(Currencies.CODES.BTC)));
			#endregion

			MarketplaceBatch viernes;
			AgentBatch agency;
			BatchTransactions juanTransactions = BatchTest.SetInitialSettings(marketplace, today1, 2910000m, new Domain[] { domain }, out viernes, out agency) ;
			BatchSet agentBatch = juanTransactions.Batch();
			string agentPath = a.FullName;

			int id2 = marketplace.NewTransationNumber();

			var transactionType = marketplace.Company.System.TransactionTypes.SearchByName<ProcessorTransaction>(TransactionType.Deposit.ToString());
			int authorizationNumber = PaymentChannels.Deposit(
				itIsThePresent,
				Coinage.Coin(Currencies.CODES.USD),
				domain,
				transactionType,
				new DespositBody(
					accountNumber,
					ced,
					10,
					"deposit test",
					today1,
					"",
					"cris",
					domain)).AuthorizationId;

			DepositTransaction transaction2 = (DepositTransaction)marketplace.From(id2, bitcoinAccount, agentPath, employeeName, domain).Deposit(today1, itIsThePresent, new Dollar(10), employeeName, (PaymentProcessor)marketplace.Company.System.PaymentProcessor.First(), 1);
			DepositTransaction transaction22 = (DepositTransaction)marketplace.FindDraftTransaction(transaction2.Id, agentPath, employeeName);

			Assert.AreEqual(transaction2, transaction22);

			TransactionCompleted result2 = marketplace.Approve(today1, itIsThePresent, transaction2, 2, employeeName);
			TransactionResult resultStats2 = (TransactionResult)result2.Result;
			Assert.AreEqual(new Dollar(0.01m), resultStats2.Comission);
			Assert.AreEqual(new Dollar(10m), resultStats2.Gross);
			Assert.AreEqual(new Dollar(9.99m), resultStats2.Net);
			Assert.AreEqual(new Btc(0.********m), resultStats2.Amount);
			Assert.AreEqual(new Dollar(-0.10m), resultStats2.Profit);
			//GamesEngine.Custodian.PaymentMethodsActions.ResetInstance();
		}

		[TestMethod]
		public void BatchMovements_Deposit_Accounts_and_amounts_with_same_currencies()
		{
			CurrenciesTest.AddCurrencies();
			Company company;
			Guardian guardian;
			QueueInMemory queue;
			DateTime today = DateTime.Now;
			GuardianTest.Init(today, out company, out guardian, out queue);

			//QueueInMemory queue = new QueueInMemory();
			Integration.Kafka = queue;
			Integration.SavePreviousSettings(tempUseKafka: true, tempUseKafkaForAuto: true);

			bool itIsThePresent = false;
			DateTime today1 = DateTime.Now;
			DateTime today2 = today1.AddMilliseconds(1);
			//Company company = new Company();CurrenciesTest.AddCurrencies();
			
			//company.Sales.CreateStore(1, "Test Store").MakeCurrent();
			string ced="abc123";string accountNumber = "B15461";Customer customer=company.GetOrCreateCustomerById(accountNumber);customer.Identifier=ced;
			CustomerAccount btcccount = customer.CreateNewAccountFor(Coinage.Coin(Currencies.CODES.BTC));
			Marketplace marketplace = new Marketplace(company, "CR");
			var a = (GamesEngine.Exchange.Agent)marketplace.AddAgent("A");
			var b = a.AddCashier("Juan");
			string employeeName = "Juan";
			Domain domain = company.Sales.CreateDomain(false, 1, "localhost", PaymentChannels.Agents.INSIDER);
			company.Sales.CurrentStore.Add(domain);

			GuardianTest.LoadProcessors(company);
			PaymentProcessorsAndActionsByDomains processors = marketplace.PaymentProcessors();
			PaymentProcessor GuardianBTCDriver = processors.SearchById("TEST-Secrets-TestDepositBTC-BTC-1");

			#region rate #1
			RegularConversionSpread rate1 = marketplace.CreateConversionSpread(1, today1, itIsThePresent, Coinage.Coin(Currencies.CODES.USD), Coinage.Coin(Currencies.CODES.BTC), 60m, 30m, employeeName);
			Assert.AreEqual(rate1, marketplace.ConversionSpreadAt(today1, Coinage.Coin(Currencies.CODES.USD), Coinage.Coin(Currencies.CODES.BTC)));

			RegularConversionSpread rate2 = marketplace.CreateConversionSpread(2, today1, itIsThePresent, Coinage.Coin(Currencies.CODES.BTC), Coinage.Coin(Currencies.CODES.USD), 1.33333333m, 1.24444444m, employeeName);
			Assert.AreEqual(rate2, marketplace.ConversionSpreadAt(today1, Coinage.Coin(Currencies.CODES.BTC), Coinage.Coin(Currencies.CODES.USD)));
			#endregion

			BatchTransactions juanTransactions = BatchTest.SetInitialSettings(marketplace, today1, new Domain[] { domain });
			BatchSet agentBatch = juanTransactions.Batch();
			string agentPath = a.FullName;

			int id = marketplace.NewTransationNumber();

			var transactionType = marketplace.Company.System.TransactionTypes.SearchByName<ProcessorTransaction>(TransactionType.Deposit.ToString());
			int authorizationNumber = PaymentChannels.Deposit(
				itIsThePresent,
				Coinage.Coin(Currencies.CODES.BTC),
				domain,
				transactionType,
				new DespositBody(
					accountNumber,
					ced,
					200,
					"deposit test",
					today1,
					"",
					"cris",
					domain)).AuthorizationId;

			DepositTransaction transaction1 = (DepositTransaction)marketplace.From(id, btcccount, agentPath, employeeName, domain).Deposit(today1, itIsThePresent, new Btc(200), employeeName, (PaymentProcessor)marketplace.Company.System.PaymentProcessor.First(), 1);
			DepositTransaction transaction = (DepositTransaction)marketplace.FindDraftTransaction(transaction1.Id, agentPath, employeeName);

			List<BatchMovement> movements = juanTransactions.Movements().ToList();
			Assert.AreEqual(2, movements.Count); 
			Assert.AreEqual(new Btc(200), movements[0].Amount, "This is the amount to accredit to the IN account when transaction will be approved.");
			Assert.AreEqual(new Btc(200), movements[1].Amount, "This is the blocked BTC to the IN account when transaction will be approved.");
			Assert.AreEqual(new Btc(0), juanTransactions.Locked(Coinage.Coin(Currencies.CODES.BTC)), "BTC 500 should be locked meanwhile transaction it's approved.");
			Assert.AreEqual(new Btc(200), juanTransactions.LockedReception(Coinage.Coin(Currencies.CODES.BTC)), "BTC 200 should be locked meanwhile transaction it's approved.");
			Assert.AreEqual(new Btc(200m), juanTransactions.Available(Coinage.Coin(Currencies.CODES.BTC)), "No available modification are expected.");
			Assert.AreEqual(new Btc(0), juanTransactions.AvailableReception(Coinage.Coin(Currencies.CODES.BTC)), "No available modification are expected.");
			Assert.AreEqual(new Dollar(5000000m), juanTransactions.Available(Coinage.Coin(Currencies.CODES.USD)), "No available modification are expected.");
			Assert.AreEqual(new Eth(5m), juanTransactions.Available(Coinage.Coin(Currencies.CODES.ETH)), "No available modification are expected.");
			
			TransactionCompleted result1 = marketplace.Approve(today1, itIsThePresent, transaction1, 1, employeeName);
			movements = juanTransactions.Movements().ToList();
			Assert.AreEqual(4, movements.Count);
			Assert.AreEqual(new Btc(200), movements[2].Amount, "This is the unlocked amount to the IN account because transaction was approved.");
			Assert.AreEqual(new Btc(200), movements[3].Amount, "This is the amount to accredit to the IN account because transaction was approved.");
			Assert.AreEqual(new Btc(0), juanTransactions.LockedReception(Coinage.Coin(Currencies.CODES.BTC)), "BTC 500 should be unlocked when transaction it's approved.");
			Assert.AreEqual(new Btc(200), juanTransactions.AvailableReception(Coinage.Coin(Currencies.CODES.BTC)), "Available reception should increase.");

			Assert.AreEqual(new Btc(0), juanTransactions.Locked(Coinage.Coin(Currencies.CODES.BTC)), "BTC $500 should be unocked when transaction it's approved.");
			Assert.AreEqual(new Btc(200m), juanTransactions.Available(Coinage.Coin(Currencies.CODES.BTC)), "Available should not increase.");
			Assert.AreEqual(new Dollar(5000000m), juanTransactions.Available(Coinage.Coin(Currencies.CODES.USD)), "No available modification are expected.");
			Assert.AreEqual(new Eth(5m), juanTransactions.Available(Coinage.Coin(Currencies.CODES.ETH)), "No available modification are expected.");
			//GamesEngine.Custodian.PaymentMethodsActions.ResetInstance();
		}

		[TestMethod]
		public void BatchMovements_Deposit_Accounts_and_amounts_with_different_currencies()
		{
			bool itIsThePresent = false;
			DateTime today1 = DateTime.Now;
			DateTime today2 = today1.AddMilliseconds(1);
			Company company = new Company();CurrenciesTest.AddCurrencies();
			
			company.Sales.CreateStore(1, "Test Store").MakeCurrent();
			string ced="abc123";string accountNumber = "B15461";Customer customer=company.GetOrCreateCustomerById(accountNumber);customer.Identifier=ced;
			CustomerAccount usdccount = customer.CreateNewAccountFor(Coinage.Coin(Currencies.CODES.USD));
			CustomerAccount btcccount = customer.CreateNewAccountFor(Coinage.Coin(Currencies.CODES.BTC));
			Marketplace marketplace = new Marketplace(company, "CR");
			var a = (GamesEngine.Exchange.Agent)marketplace.AddAgent("A");
			var b = a.AddCashier("Juan");
			string employeeName = "Juan";
			Domain domain = company.Sales.CreateDomain(false, 1, "localhost", PaymentChannels.Agents.INSIDER);
			company.Sales.CurrentStore.Add(domain);
			GuardianTest.LoadProcessors(company);

			#region rate #1
			RegularConversionSpread rate1 = marketplace.CreateConversionSpread(1, today1, itIsThePresent, Coinage.Coin(Currencies.CODES.USD), Coinage.Coin(Currencies.CODES.BTC), 9500.01m, 9500m, employeeName);
			Assert.AreEqual(rate1, marketplace.ConversionSpreadAt(today1, Coinage.Coin(Currencies.CODES.USD), Coinage.Coin(Currencies.CODES.BTC)));
			#endregion

			BatchTransactions juanTransactions = BatchTest.SetInitialSettings(marketplace, today1, new Domain[] { domain });
			BatchSet agentBatch = juanTransactions.Batch();
			string agentPath = a.FullName;

			int id = marketplace.NewTransationNumber();

			var transactionType = marketplace.Company.System.TransactionTypes.SearchByName<ProcessorTransaction>(TransactionType.Deposit.ToString());
			int authorizationNumber = PaymentChannels.Deposit(
				itIsThePresent,
				Coinage.Coin(Currencies.CODES.USD),
				domain,
				transactionType,
				new DespositBody(
					accountNumber,
					ced,
					100,
					"deposit test",
					today1,
					"",
					"cris",
					domain)).AuthorizationId;

			DepositTransaction transaction1 = (DepositTransaction)marketplace.From(id, btcccount, agentPath, employeeName, domain).Deposit(today1, itIsThePresent, new Dollar(100), employeeName, (PaymentProcessor)marketplace.Company.System.PaymentProcessor.First(), 1);
			DepositTransaction transaction = (DepositTransaction)marketplace.FindDraftTransaction(transaction1.Id, agentPath, employeeName);

			Btc expectedAmounttoCustomer= new Btc(0.01052525m);
			Btc expectedAvailableInBatch= new Btc(199.98947475m);

			List<BatchMovement> movements = juanTransactions.Movements().ToList();
			Assert.AreEqual(3, movements.Count);
			Assert.AreEqual(expectedAmounttoCustomer, movements[0].Amount, "This is the blocked BTC to the OUT account when transaction will be approved.");
			Assert.AreEqual(new Dollar(100), movements[1].Amount, "This is the amount to accredit to the IN account when transaction will be approved.");
			Assert.AreEqual(new Dollar(100), movements[2].Amount, "This is the blocked amount to the IN account when transaction will be approved.");
			Assert.AreEqual(expectedAmounttoCustomer, juanTransactions.Locked(Coinage.Coin(Currencies.CODES.BTC)), $"BTC {expectedAmounttoCustomer} should be locked meanwhile transaction it's approved.");
			Assert.AreEqual(new Dollar(100), juanTransactions.LockedReception(Coinage.Coin(Currencies.CODES.USD)), $"USD {new Dollar(100)} should be locked meanwhile transaction it's approved.");
			Assert.AreEqual(expectedAvailableInBatch, juanTransactions.Available(Coinage.Coin(Currencies.CODES.BTC)), "No available modification are expected.");
			Assert.AreEqual(new Dollar(0), juanTransactions.AvailableReception(Coinage.Coin(Currencies.CODES.USD)), "No available modification are expected.");
			Assert.AreEqual(new Dollar(5000000m), juanTransactions.Available(Coinage.Coin(Currencies.CODES.USD)), "No available modification are expected.");
			Assert.AreEqual(new Eth(5m), juanTransactions.Available(Coinage.Coin(Currencies.CODES.ETH)), "No available modification are expected.");

			TransactionCompleted result1 = marketplace.Approve(today1, itIsThePresent, transaction1, 1, employeeName);
			movements = juanTransactions.Movements().ToList();
			Assert.AreEqual(6, movements.Count);
			Assert.AreEqual(expectedAmounttoCustomer, movements[3].Amount, "This is the unlocked BTC to the OUT account because transaction was approved.");
			Assert.AreEqual(new Dollar(100), movements[4].Amount, "This is the unlocked amount to the IN account because transaction was approved.");
			Assert.AreEqual(new Dollar(100), movements[5].Amount, "This is the amount to accredit to the IN account because transaction was approved.");
			Assert.AreEqual(new Btc(0), juanTransactions.Locked(Coinage.Coin(Currencies.CODES.BTC)), "BTC $500 should be unlocked when transaction it's approved.");
			Assert.AreEqual(new Dollar(0), juanTransactions.LockedReception(Coinage.Coin(Currencies.CODES.USD)), "USD $100 should be unlocked when transaction it's approved.");
			Assert.AreEqual(expectedAvailableInBatch, juanTransactions.Available(Coinage.Coin(Currencies.CODES.BTC)), "Available should increase.");
			Assert.AreEqual(new Dollar(100), juanTransactions.AvailableReception(Coinage.Coin(Currencies.CODES.USD)), "Available should increase.");
			Assert.AreEqual(new Dollar(5000000m), juanTransactions.Available(Coinage.Coin(Currencies.CODES.USD)), "No available modification are expected.");
			Assert.AreEqual(new Eth(5m), juanTransactions.Available(Coinage.Coin(Currencies.CODES.ETH)), "No available modification are expected.");


			Assert.AreEqual(agentBatch.InitialAccount.InitialAmountFor(Coinage.Coin(Currencies.CODES.USD)) + 100, agentBatch.InitialAccount.CurrentBalance(Coinage.Coin(Currencies.CODES.USD)));
			Assert.AreEqual(agentBatch.InitialAccount.InitialAmountFor(Coinage.Coin(Currencies.CODES.BTC))- result1.Result.Amount.Value, agentBatch.InitialAccount.CurrentBalance(Coinage.Coin(Currencies.CODES.BTC)));
			Assert.AreEqual(0, marketplace.RealAccounts.Movements(Coinage.Coin(Currencies.CODES.USD)));
			Assert.AreEqual(0, marketplace.RealAccounts.Movements(Coinage.Coin(Currencies.CODES.BTC)));

			agentBatch.Close(false, today1, "N/A");
			Assert.AreEqual(agentBatch.InitialAccount.InitialAmountFor(Coinage.Coin(Currencies.CODES.USD)) + 100, agentBatch.InitialAccount.CurrentBalance(Coinage.Coin(Currencies.CODES.USD)));
			Assert.AreEqual(agentBatch.InitialAccount.InitialAmountFor(Coinage.Coin(Currencies.CODES.BTC)) - result1.Result.Amount.Value, agentBatch.InitialAccount.CurrentBalance(Coinage.Coin(Currencies.CODES.BTC)));
			//GamesEngine.Custodian.PaymentMethodsActions.ResetInstance();
		}

		[TestMethod]
		public void BatchMovements_Withdrawal_Accounts_and_amounts_with_same_currencies()
		{
			bool itIsThePresent = false;
			DateTime today1 = DateTime.Now;
			DateTime today2 = today1.AddMilliseconds(1);
			Company company = new Company();CurrenciesTest.AddCurrencies();
			company.Sales.CreateStore(1, "Test Store").MakeCurrent();
			string ced="abc123";string accountNumber = "B15461";Customer customer=company.GetOrCreateCustomerById(accountNumber);customer.Identifier=ced;
			CustomerAccount usdccount = customer.CreateNewAccountFor(Coinage.Coin(Currencies.CODES.USD));
			CustomerAccount btcccount = customer.CreateNewAccountFor(Coinage.Coin(Currencies.CODES.BTC));
			Marketplace marketplace = new Marketplace(company, "CR");
			var a = (GamesEngine.Exchange.Agent)marketplace.AddAgent("A");
			var b = a.AddCashier("Juan");
			marketplace.RegisterNewAccount(1, usdccount);
			string employeeName = "Juan";
			Domain domain = company.Sales.CreateDomain(false, 1, "localhost", PaymentChannels.Agents.INSIDER);
			company.Sales.CurrentStore.Add(domain);
			GuardianTest.LoadProcessors(company);

			#region rate #1
			RegularConversionSpread rate1 = marketplace.CreateConversionSpread(1, today1, itIsThePresent, Coinage.Coin(Currencies.CODES.USD), Coinage.Coin(Currencies.CODES.BTC), 9500.01m, 9500m, employeeName);
			Assert.AreEqual(rate1, marketplace.ConversionSpreadAt(today1, Coinage.Coin(Currencies.CODES.USD), Coinage.Coin(Currencies.CODES.BTC)));
			#endregion

			BatchTransactions juanTransactions = BatchTest.SetInitialSettings(marketplace, today1, new Domain[] { domain });
			BatchSet agentBatch = juanTransactions.Batch();
			string agentPath = a.FullName;

			int id = marketplace.NewTransationNumber();
			var processor = marketplace.Company.System.PaymentProcessor.SearchWithdrawalProcessorBy("USD");
			WithdrawalTransaction transaction1 = (WithdrawalTransaction)marketplace.From(id, usdccount, agentPath, employeeName, domain).Withdraw(today1, itIsThePresent, new Dollar(100), 1, employeeName, "1-11", "", new NoFeeUSD(), processor, 1);
			WithdrawalTransaction transaction = (WithdrawalTransaction)marketplace.FindDraftTransaction(transaction1.Id, agentPath, employeeName);

			List<BatchMovement> movements = juanTransactions.Movements().ToList();
			Assert.AreEqual(1, movements.Count);
			Assert.AreEqual(new Dollar(100), movements[0].Amount, "This is the blocked USD to the OUT account when transaction will be approved.");
			Assert.AreEqual(new Dollar(100), juanTransactions.Locked(Coinage.Coin(Currencies.CODES.USD)), $"USD {new Dollar(100)} should be locked meanwhile transaction it's approved.");
			Assert.AreEqual(new Dollar(4999900), juanTransactions.Available(Coinage.Coin(Currencies.CODES.USD)), "Available should decrease.");
			Assert.AreEqual(new Btc(200), juanTransactions.Available(Coinage.Coin(Currencies.CODES.BTC)), "No available modification are expected.");
			Assert.AreEqual(new Eth(5m), juanTransactions.Available(Coinage.Coin(Currencies.CODES.ETH)), "No available modification are expected.");

			Assert.AreEqual(new Dollar(0), juanTransactions.LockedReception(Coinage.Coin(Currencies.CODES.USD)), "No available modification are expected.");
			Assert.AreEqual(new Dollar(0), juanTransactions.AvailableReception(Coinage.Coin(Currencies.CODES.USD)), "No available modification are expected.");

			TransactionCompleted result1 = marketplace.Approve(today1, itIsThePresent, transaction1, 1, employeeName);
			movements = juanTransactions.Movements().ToList();
			Assert.AreEqual(2, movements.Count);
			Assert.AreEqual(new Dollar(100), movements[1].Amount, "This is the unlocked USD to the OUT account because transaction was approved.");
			Assert.AreEqual(new Dollar(0), juanTransactions.Locked(Coinage.Coin(Currencies.CODES.USD)), $"USD {new Dollar(0)} should be locked when transaction it's approved.");
			Assert.AreEqual(new Dollar(4999900), juanTransactions.Available(Coinage.Coin(Currencies.CODES.USD)), "No available modification are expected.");
			Assert.AreEqual(new Btc(200), juanTransactions.Available(Coinage.Coin(Currencies.CODES.BTC)), "No available modification are expected.");
			Assert.AreEqual(new Eth(5m), juanTransactions.Available(Coinage.Coin(Currencies.CODES.ETH)), "No available modification are expected.");

			Assert.AreEqual(new Dollar(0), juanTransactions.LockedReception(Coinage.Coin(Currencies.CODES.USD)), "No available modification are expected.");
			Assert.AreEqual(new Dollar(0), juanTransactions.AvailableReception(Coinage.Coin(Currencies.CODES.USD)), "No available modification are expected.");

			Assert.AreEqual(agentBatch.InitialAccount.InitialAmountFor(Coinage.Coin(Currencies.CODES.USD)) - 100, agentBatch.InitialAccount.CurrentBalance(Coinage.Coin(Currencies.CODES.USD)));
			Assert.AreEqual(agentBatch.InitialAccount.InitialAmountFor(Coinage.Coin(Currencies.CODES.BTC)) , agentBatch.InitialAccount.CurrentBalance(Coinage.Coin(Currencies.CODES.BTC)));
			Assert.AreEqual(0, marketplace.RealAccounts.Movements(Coinage.Coin(Currencies.CODES.USD)));
			Assert.AreEqual(0, marketplace.RealAccounts.Movements(Coinage.Coin(Currencies.CODES.BTC)));

			agentBatch.Close(false, today1, "N/A");
			Assert.AreEqual(agentBatch.InitialAccount.InitialAmountFor(Coinage.Coin(Currencies.CODES.USD)) - 100, agentBatch.InitialAccount.CurrentBalance(Coinage.Coin(Currencies.CODES.USD)));
			Assert.AreEqual(agentBatch.InitialAccount.InitialAmountFor(Coinage.Coin(Currencies.CODES.BTC)), agentBatch.InitialAccount.CurrentBalance(Coinage.Coin(Currencies.CODES.BTC)));
			//GamesEngine.Custodian.PaymentMethodsActions.ResetInstance();
		}
		[TestMethod]
		public void BatchMovements_Withdrawal_Accounts_and_amounts_with_different_currencies()
		{
			bool itIsThePresent = false;
			DateTime today1 = DateTime.Now;
			DateTime today2 = today1.AddMilliseconds(1);
			Company company = new Company();CurrenciesTest.AddCurrencies();
			company.Sales.CreateStore(1, "Test Store").MakeCurrent();
			string ced="abc123";string accountNumber = "B15461";Customer customer=company.GetOrCreateCustomerById(accountNumber);customer.Identifier=ced;
			CustomerAccount usdccount = customer.CreateNewAccountFor(Coinage.Coin(Currencies.CODES.USD));
			CustomerAccount btcccount = customer.CreateNewAccountFor(Coinage.Coin(Currencies.CODES.BTC));
			Marketplace marketplace = new Marketplace(company, "CR");
			var a = (GamesEngine.Exchange.Agent)marketplace.AddAgent("A");
			var b = a.AddCashier("Juan");
			marketplace.RegisterNewAccount(1, usdccount);
			marketplace.RegisterNewAccount(2, btcccount);
			string employeeName = "Juan";
			Domain domain = company.Sales.CreateDomain(false, 1, "localhost", PaymentChannels.Agents.INSIDER);
			company.Sales.CurrentStore.Add(domain);
			GuardianTest.LoadProcessors(company);

			#region rate #1
			RegularConversionSpread rate1 = marketplace.CreateConversionSpread(1, today1, itIsThePresent, Coinage.Coin(Currencies.CODES.USD), Coinage.Coin(Currencies.CODES.BTC), 9500.01m, 9500m, employeeName);
			Assert.AreEqual(rate1, marketplace.ConversionSpreadAt(today1, Coinage.Coin(Currencies.CODES.USD), Coinage.Coin(Currencies.CODES.BTC)));
			#endregion

			BatchTransactions juanTransactions = BatchTest.SetInitialSettings(marketplace, today1, new Domain[] { domain });
			BatchSet agentBatch = juanTransactions.Batch();
			string agentPath = a.FullName;

			int id = marketplace.NewTransationNumber();
			var processor = marketplace.Company.System.PaymentProcessor.SearchWithdrawalProcessorBy("USD");
			WithdrawalTransaction transaction1 = (WithdrawalTransaction)marketplace.From(id, btcccount, agentPath, employeeName, domain).Withdraw(today1, itIsThePresent, new Dollar(100), 1, employeeName, "1-11", "", new NoFeeUSD(), processor, 1);
			WithdrawalTransaction transaction = (WithdrawalTransaction)marketplace.FindDraftTransaction(transaction1.Id, agentPath, employeeName);

			Btc expectedAmounttoCustomer = new Btc(0.01052525m);
			Btc expectedAvailableInBatch = new Btc(199.98947475m);

			List<BatchMovement> movements = juanTransactions.Movements().ToList();
			Assert.AreEqual(1, movements.Count);
			Assert.AreEqual(expectedAmounttoCustomer, movements[0].Amount, "This is the blocked BTC to the OUT account when transaction will be approved.");
			Assert.AreEqual(expectedAmounttoCustomer, juanTransactions.Locked(Coinage.Coin(Currencies.CODES.BTC)), $"BTC {expectedAmounttoCustomer} should be locked meanwhile transaction it's approved.");
			Assert.AreEqual(expectedAvailableInBatch, juanTransactions.Available(Coinage.Coin(Currencies.CODES.BTC)), "Available should decrease.");
			Assert.AreEqual(new Eth(5m), juanTransactions.Available(Coinage.Coin(Currencies.CODES.ETH)), "No available modification are expected.");

			Assert.AreEqual(new Dollar(0), juanTransactions.LockedReception(Coinage.Coin(Currencies.CODES.USD)), "No available modification are expected.");
			Assert.AreEqual(new Dollar(0), juanTransactions.AvailableReception(Coinage.Coin(Currencies.CODES.USD)), "No available modification are expected.");

			TransactionCompleted result1 = marketplace.Approve(today1, itIsThePresent, transaction1, 1, employeeName);
			movements = juanTransactions.Movements().ToList();
			Assert.AreEqual(2, movements.Count);
			Assert.AreEqual(expectedAmounttoCustomer, movements[1].Amount, "This is the unlocked BTC to the OUT account because transaction was approved.");
			Assert.AreEqual(new Btc(0), juanTransactions.Locked(Coinage.Coin(Currencies.CODES.BTC)), $"BTC {new Btc(0)} should be locked when transaction it's approved.");
			Assert.AreEqual(expectedAvailableInBatch, juanTransactions.Available(Coinage.Coin(Currencies.CODES.BTC)), "No available modification are expected.");
			Assert.AreEqual(new Eth(5m), juanTransactions.Available(Coinage.Coin(Currencies.CODES.ETH)), "No available modification are expected.");

			Assert.AreEqual(new Dollar(0), juanTransactions.LockedReception(Coinage.Coin(Currencies.CODES.USD)), "No available modification are expected.");
			Assert.AreEqual(new Dollar(0), juanTransactions.AvailableReception(Coinage.Coin(Currencies.CODES.USD)), "No available modification are expected.");
			//GamesEngine.Custodian.PaymentMethodsActions.ResetInstance();
		}

		[TestMethod]
		public void BatchMovements_Transfer_Accounts_and_amounts_with_same_currencies()
		{
			bool itIsThePresent = false;
			DateTime today1 = DateTime.Now;
			DateTime today2 = today1.AddMilliseconds(1);
			Company company = new Company();CurrenciesTest.AddCurrencies();
			
			company.Sales.CreateStore(1, "Test Store").MakeCurrent();
			string ced="abc123";string accountNumber = "B15461";Customer customer=company.GetOrCreateCustomerById(accountNumber);customer.Identifier=ced;
			string ced2="abc1234";string accountNumber2 = "B15462";Customer toCustomer=company.GetOrCreateCustomerById(accountNumber2);toCustomer.Identifier=ced2;
			CustomerAccount dollarAccount = customer.CreateNewAccountFor(Coinage.Coin(Currencies.CODES.USD));
			CustomerAccount dollarAccount2 = toCustomer.CreateNewAccountFor(Coinage.Coin(Currencies.CODES.USD));
			Marketplace marketplace = new Marketplace(company, "CR");
			var a = (GamesEngine.Exchange.Agent)marketplace.AddAgent("A");
			var b = a.AddCashier("Juan");
			marketplace.RegisterNewAccount(1, dollarAccount);
			marketplace.RegisterNewAccount(2, dollarAccount2);
			string employeeName = "Juan";
			Domain domain = company.Sales.CreateDomain(false, 1, "localhost", PaymentChannels.Agents.INSIDER);
			company.Sales.CurrentStore.Add(domain);
			GuardianTest.LoadProcessors(company);

			#region rate #1
			RegularConversionSpread rate1 = marketplace.CreateConversionSpread(1, today1, itIsThePresent, Coinage.Coin(Currencies.CODES.USD), Coinage.Coin(Currencies.CODES.BTC), 9500.01m, 9500m, employeeName);
			Assert.AreEqual(rate1, marketplace.ConversionSpreadAt(today1, Coinage.Coin(Currencies.CODES.USD), Coinage.Coin(Currencies.CODES.BTC)));
			#endregion

			BatchTransactions juanTransactions = BatchTest.SetInitialSettings(marketplace, today1, new Domain[] { domain });
			BatchSet agentBatch = juanTransactions.Batch();
			string agentPath = a.FullName;

			int id = marketplace.NewTransationNumber();

			TransferTransaction transaction1 = (TransferTransaction)marketplace.From(id, dollarAccount, agentPath, employeeName, domain).TransferTo(today1, itIsThePresent, new Dollar(100), 1, dollarAccount2, employeeName, (PaymentProcessor)marketplace.Company.System.PaymentProcessor.First(), 1);
			TransferTransaction transaction = (TransferTransaction)marketplace.FindDraftTransaction(transaction1.Id, agentPath, employeeName);

			Btc expectedAmounttoCustomer = new Btc(0.01052525m);

			List<BatchMovement> movements = juanTransactions.Movements().ToList();
			Assert.AreEqual(1, movements.Count);
			Assert.AreEqual(new Dollar(100), movements[0].Amount, "This is the blocked USD amount to the customer when transaction will be approved.");
			Assert.AreEqual(new Btc(0), juanTransactions.Locked(Coinage.Coin(Currencies.CODES.BTC)), "No available modification are expected.");
			Assert.AreEqual(new Dollar(5000000), juanTransactions.Available(Coinage.Coin(Currencies.CODES.USD)), "No available modification are expected.");
			Assert.AreEqual(new Btc(200), juanTransactions.Available(Coinage.Coin(Currencies.CODES.BTC)), "No available modification are expected.");
			Assert.AreEqual(new Eth(5m), juanTransactions.Available(Coinage.Coin(Currencies.CODES.ETH)), "No available modification are expected.");

			Assert.AreEqual(new Dollar(0), juanTransactions.LockedReception(Coinage.Coin(Currencies.CODES.USD)), "No available modification are expected.");
			Assert.AreEqual(new Dollar(0), juanTransactions.AvailableReception(Coinage.Coin(Currencies.CODES.USD)), "No available modification are expected.");

			TransactionCompleted result1 = marketplace.Approve(today1, itIsThePresent, transaction1, 1, employeeName);
			movements = juanTransactions.Movements().ToList();
			Assert.AreEqual(2, movements.Count);
			Assert.AreEqual(new Dollar(100), movements[1].Amount, "This is the unlocked USD amount to the customer when transaction will be approved.");
			Assert.AreEqual(new Btc(0), juanTransactions.Locked(Coinage.Coin(Currencies.CODES.BTC)), "No available modification are expected.");
			Assert.AreEqual(new Dollar(5000000), juanTransactions.Available(Coinage.Coin(Currencies.CODES.USD)), "No available modification are expected.");
			Assert.AreEqual(new Btc(200), juanTransactions.Available(Coinage.Coin(Currencies.CODES.BTC)), "No available modification are expected.");
			Assert.AreEqual(new Eth(5m), juanTransactions.Available(Coinage.Coin(Currencies.CODES.ETH)), "No available modification are expected.");

			Assert.AreEqual(new Dollar(0), juanTransactions.LockedReception(Coinage.Coin(Currencies.CODES.USD)), "No available modification are expected.");
			Assert.AreEqual(new Dollar(0), juanTransactions.AvailableReception(Coinage.Coin(Currencies.CODES.USD)), "No available modification are expected.");
		}

		[TestMethod]
		public void BatchMovements_Transfer_Accounts_and_amounts_with_different_currencies()
		{
			bool itIsThePresent = false;
			DateTime today1 = DateTime.Now;
			DateTime today2 = today1.AddMilliseconds(1);
			Company company = new Company();CurrenciesTest.AddCurrencies();
			
			company.Sales.CreateStore(1, "Test Store").MakeCurrent();
			string ced="abc123";string accountNumber = "B15461";Customer customer=company.GetOrCreateCustomerById(accountNumber);customer.Identifier=ced;
			string ced2="abc1234";string accountNumber2 = "B15462";Customer toCustomer=company.GetOrCreateCustomerById(accountNumber2);toCustomer.Identifier=ced2;
			CustomerAccount dollarAccount = customer.CreateNewAccountFor(Coinage.Coin(Currencies.CODES.USD));
			CustomerAccount bitcoinAccount = toCustomer.CreateNewAccountFor(Coinage.Coin(Currencies.CODES.BTC));
			Marketplace marketplace = new Marketplace(company, "CR");
			var a = (GamesEngine.Exchange.Agent)marketplace.AddAgent("A");
			var b = a.AddCashier("Juan");
			marketplace.RegisterNewAccount(1, dollarAccount);
			marketplace.RegisterNewAccount(2, bitcoinAccount);
			string employeeName = "Juan";
			Domain domain = company.Sales.CreateDomain(false, 1, "localhost", PaymentChannels.Agents.INSIDER);
			company.Sales.CurrentStore.Add(domain);
			GuardianTest.LoadProcessors(company);

			#region rate #1
			RegularConversionSpread rate1 = marketplace.CreateConversionSpread(1, today1, itIsThePresent, Coinage.Coin(Currencies.CODES.USD), Coinage.Coin(Currencies.CODES.BTC), 9500.01m, 9500m, employeeName);
			Assert.AreEqual(rate1, marketplace.ConversionSpreadAt(today1, Coinage.Coin(Currencies.CODES.USD), Coinage.Coin(Currencies.CODES.BTC)));
			#endregion

			BatchTransactions juanTransactions = BatchTest.SetInitialSettings(marketplace, today1, new Domain[] { domain });
			BatchSet agentBatch = juanTransactions.Batch();
			string agentPath = a.FullName;

			int id = marketplace.NewTransationNumber();

			TransferTransaction transaction1 = (TransferTransaction)marketplace.From(id, dollarAccount, agentPath, employeeName, domain).TransferTo(today1, itIsThePresent, new Dollar(100), 1, bitcoinAccount, employeeName, (PaymentProcessor)marketplace.Company.System.PaymentProcessor.First(), 1);
			TransferTransaction transaction = (TransferTransaction)marketplace.FindDraftTransaction(transaction1.Id, agentPath, employeeName);

			Btc expectedAmounttoCustomer = new Btc(0.01052525m);

			List<BatchMovement> movements = juanTransactions.Movements().ToList();
			Assert.AreEqual(3, movements.Count);
			Assert.AreEqual(expectedAmounttoCustomer, movements[0].Amount, "This is the blocked BTC amount to the customer when transaction will be approved.");
			Assert.AreEqual(new Btc(0.01052525m), juanTransactions.Locked(Coinage.Coin(Currencies.CODES.BTC)), "No available modification are expected.");
			Assert.AreEqual(new Btc(199.98947475m), juanTransactions.Available(Coinage.Coin(Currencies.CODES.BTC)), "No available modification are expected.");
			Assert.AreEqual(new Eth(5m), juanTransactions.Available(Coinage.Coin(Currencies.CODES.ETH)), "No available modification are expected.");

			Assert.AreEqual(new Dollar(100m), juanTransactions.LockedReception(Coinage.Coin(Currencies.CODES.USD)), "No available modification are expected.");
			Assert.AreEqual(new Dollar(0), juanTransactions.AvailableReception(Coinage.Coin(Currencies.CODES.USD)), "No available modification are expected.");

			TransactionCompleted result1 = marketplace.Approve(today1, itIsThePresent, transaction1, 1, employeeName);
			movements = juanTransactions.Movements().ToList();
			Assert.AreEqual(6, movements.Count);
			Assert.AreEqual(new Dollar(100), movements[1].Amount, "This is the unlocked BTC amount to the customer when transaction will be approved.");
			Assert.AreEqual(new Btc(0), juanTransactions.Locked(Coinage.Coin(Currencies.CODES.BTC)), "No available modification are expected.");
			Assert.AreEqual(new Btc(199.98947475m), juanTransactions.Available(Coinage.Coin(Currencies.CODES.BTC)), "No available modification are expected.");
			Assert.AreEqual(new Eth(5m), juanTransactions.Available(Coinage.Coin(Currencies.CODES.ETH)), "No available modification are expected.");

			Assert.AreEqual(new Dollar(0), juanTransactions.LockedReception(Coinage.Coin(Currencies.CODES.USD)), "No available modification are expected.");
			Assert.AreEqual(new Dollar(100m), juanTransactions.AvailableReception(Coinage.Coin(Currencies.CODES.USD)), "No available modification are expected.");

			Assert.AreEqual(marketplace.RealAccounts.InitialAmountFor(Coinage.Coin(Currencies.CODES.USD)) + 100, agentBatch.InitialAccount.CurrentBalance(Coinage.Coin(Currencies.CODES.USD)));
			Assert.AreEqual(marketplace.RealAccounts.InitialAmountFor(Coinage.Coin(Currencies.CODES.BTC))-result1.Result.Amount.Value, agentBatch.InitialAccount.CurrentBalance(Coinage.Coin(Currencies.CODES.BTC)));
			Assert.AreEqual(0, marketplace.RealAccounts.Movements(Coinage.Coin(Currencies.CODES.USD)));
			Assert.AreEqual(0, marketplace.RealAccounts.Movements(Coinage.Coin(Currencies.CODES.BTC)));
			agentBatch.Close(false, today1, "N/A");
			Assert.AreEqual(marketplace.RealAccounts.InitialAmountFor(Coinage.Coin(Currencies.CODES.USD)) + 100, agentBatch.InitialAccount.CurrentBalance(Coinage.Coin(Currencies.CODES.USD)));
			Assert.AreEqual(marketplace.RealAccounts.InitialAmountFor(Coinage.Coin(Currencies.CODES.BTC)) - result1.Result.Amount.Value, agentBatch.InitialAccount.CurrentBalance(Coinage.Coin(Currencies.CODES.BTC)));

		}

		[TestMethod]
		public void DenyTransactions_AreAffectingTotalSpent_Bug5508()
		{
			bool itIsThePresent = false;
			DateTime today1 = DateTime.Now;
			DateTime today2 = today1.AddMilliseconds(1);
			Company company = new Company();CurrenciesTest.AddCurrencies();
			company.Sales.CreateStore(1, "Test Store").MakeCurrent();
			string ced="abc123";string accountNumber = "B15461";Customer customer=company.GetOrCreateCustomerById(accountNumber);customer.Identifier=ced;
			string ced2="abc1234";string accountNumber2 = "B15462";Customer toCustomer=company.GetOrCreateCustomerById(accountNumber2);toCustomer.Identifier=ced2;
			CustomerAccount dollarAccount = customer.CreateNewAccountFor(Coinage.Coin(Currencies.CODES.USD));
			CustomerAccount bitcoinAccount = toCustomer.CreateNewAccountFor(Coinage.Coin(Currencies.CODES.BTC));
			Marketplace marketplace = new Marketplace(company, "CR");
			var a = (GamesEngine.Exchange.Agent)marketplace.AddAgent("A");
			var b = a.AddCashier("Juan");
			marketplace.RegisterNewAccount(1, dollarAccount);
			marketplace.RegisterNewAccount(2, bitcoinAccount);
			string employeeName = "Juan";
			Domain domain = company.Sales.CreateDomain(false, 1, "localhost", PaymentChannels.Agents.INSIDER);
			company.Sales.CurrentStore.Add(domain);
			GuardianTest.LoadProcessors(company);

			#region rate #1
			RegularConversionSpread rate1 = marketplace.CreateConversionSpread(1, today1, itIsThePresent, Coinage.Coin(Currencies.CODES.USD), Coinage.Coin(Currencies.CODES.BTC), 9500.01m, 9500m, employeeName);
			Assert.AreEqual(rate1, marketplace.ConversionSpreadAt(today1, Coinage.Coin(Currencies.CODES.USD), Coinage.Coin(Currencies.CODES.BTC)));
			#endregion

			MarketplaceBatch marketPLaceBatch;
			AgentBatch agencyBatch;
			BatchTransactions juanTransactions = BatchTest.SetInitialSettings(marketplace, today1, 2910000m, new Domain[] { domain }, out marketPLaceBatch, out agencyBatch);
			BatchSet agentBatch = juanTransactions.Batch();
			string agentPath = a.FullName;

			BatchAccount marketplaceInBlancesResults = marketPLaceBatch.SelfReceptionAccounts(Coinage.Coin(Currencies.CODES.USD));
			BatchAccount agencyInBalances = agencyBatch.SelfReceptionAccounts(Coinage.Coin(Currencies.CODES.USD));
			BatchAccount marketplaceOutBlancesResults = marketPLaceBatch.SaleAccount(Coinage.Coin(Currencies.CODES.BTC));
			BatchAccount agencyOutBalances = agencyBatch.SaleAccount(Coinage.Coin(Currencies.CODES.BTC));

			int id = marketplace.NewTransationNumber();

			TransferTransaction transaction1 = (TransferTransaction)marketplace.From(id, dollarAccount, agentPath, employeeName, domain).TransferTo(today1, itIsThePresent, new Dollar(55.99m), 1, bitcoinAccount, employeeName, (PaymentProcessor)marketplace.Company.System.PaymentProcessor.First(), 1);
			TransferTransaction transaction = (TransferTransaction)marketplace.FindDraftTransaction(transaction1.Id, agentPath, employeeName);

			Btc expectedAmounttoCustomer = new Btc(0.00589263m);

			List<BatchMovement> movements = juanTransactions.Movements().ToList();
			Assert.AreEqual(3, movements.Count);
			Assert.AreEqual(expectedAmounttoCustomer, movements[0].Amount, "This is the blocked BTC amount in sale account batch.");
			Assert.AreEqual(new Dollar(55.99m), movements[1].Amount, "This is the accredit USD amount in receipt account batch.");
			Assert.AreEqual(new Dollar(55.99m), movements[2].Amount, "This is the blocked USD amount in receipt account batch.");
			Assert.AreEqual(new Btc(0.00589263m), juanTransactions.Locked(Coinage.Coin(Currencies.CODES.BTC)), "This is the blocked BTC amount in the batch until transaction will be approved.");
			Assert.AreEqual(new Btc(199.99410737m), juanTransactions.Available(Coinage.Coin(Currencies.CODES.BTC)), "This is the available BTC amount in the batch until transaction will be approved.");
			Assert.AreEqual(new Dollar(0m), agencyInBalances.SpendAccumulated);
			Assert.AreEqual(new Btc(0m), agencyOutBalances.SpendAccumulated);
			Assert.AreEqual(marketplaceInBlancesResults.SpendAccumulated, agencyInBalances.SpendAccumulated);
			Assert.AreEqual(new Eth(5m), juanTransactions.Available(Coinage.Coin(Currencies.CODES.ETH)), "No available modification are expected.");

			Assert.AreEqual(new Dollar(55.99m), juanTransactions.LockedReception(Coinage.Coin(Currencies.CODES.USD)), "No available modification are expected.");
			Assert.AreEqual(new Dollar(0), juanTransactions.AvailableReception(Coinage.Coin(Currencies.CODES.USD)), "No available modification are expected.");

			transaction.Deny(today1, itIsThePresent, employeeName, "incomplete data");
			Assert.IsFalse(marketplace.ExistsDraftTransaction(juanTransactions, id));

			movements = juanTransactions.Movements().ToList();
			Assert.AreEqual(6, movements.Count, "Unlock and credit movement was expected.");
			Assert.AreEqual(expectedAmounttoCustomer, movements[3].Amount, "This is the unlocked BTC amount in sale account batch.");
			Assert.AreEqual(expectedAmounttoCustomer, movements[4].Amount, "This is the accredit BTC amount in sale account batch.");
			Assert.AreEqual(new Dollar(55.99m), movements[5].Amount, "This is the unlocked USD amount in receipt account batch.");
			Assert.AreEqual(new Btc(0), juanTransactions.Locked(Coinage.Coin(Currencies.CODES.BTC)), "This is the blocked BTC amount in the batch until transaction will be approved.");
			Assert.AreEqual(new Btc(200), juanTransactions.Available(Coinage.Coin(Currencies.CODES.BTC)), "This is the available BTC amount in the batch until transaction will be approved.");
			Assert.AreEqual(new Dollar(0m), agencyInBalances.SpendAccumulated);
			Assert.AreEqual(new Btc(0), agencyOutBalances.SpendAccumulated);
			Assert.AreEqual(marketplaceInBlancesResults.SpendAccumulated, agencyInBalances.SpendAccumulated);
			Assert.AreEqual(new Eth(5), juanTransactions.Available(Coinage.Coin(Currencies.CODES.ETH)), "No available modification are expected.");

			Assert.AreEqual(new Dollar(5000000m), juanTransactions.Available(Coinage.Coin(Currencies.CODES.USD)), "No available modification are expected.");
			Assert.AreEqual(new Dollar(0m), juanTransactions.Locked(Coinage.Coin(Currencies.CODES.USD)), "No available modification are expected.");

			Assert.AreEqual(marketplace.RealAccounts.InitialAmountFor(Coinage.Coin(Currencies.CODES.USD)), marketplace.RealAccounts.CurrentBalance(Coinage.Coin(Currencies.CODES.USD)));
			Assert.AreEqual(marketplace.RealAccounts.InitialAmountFor(Coinage.Coin(Currencies.CODES.BTC)), marketplace.RealAccounts.CurrentBalance(Coinage.Coin(Currencies.CODES.BTC)));
			Assert.AreEqual(0, marketplace.RealAccounts.Movements(Coinage.Coin(Currencies.CODES.USD)));
			Assert.AreEqual(0, marketplace.RealAccounts.Movements(Coinage.Coin(Currencies.CODES.BTC)));
			
			agentBatch.Close(false, today1, "N/A");
			Assert.AreEqual(marketplace.RealAccounts.InitialAmountFor(Coinage.Coin(Currencies.CODES.USD)), marketplace.RealAccounts.CurrentBalance(Coinage.Coin(Currencies.CODES.USD)));
			Assert.AreEqual(marketplace.RealAccounts.InitialAmountFor(Coinage.Coin(Currencies.CODES.BTC)), marketplace.RealAccounts.CurrentBalance(Coinage.Coin(Currencies.CODES.BTC)));
		}


		[TestMethod]
		public void BatchMovements()
		{
			/*
			3 BTC me costaron 28 500
			1BTc me costo $9 500  -> BTC/USD 9 500


			BTC/USD 9 600.00
			BTC/USD 9 600.01
			Rate  0.***********

			$10 -> BTC
			Comsion ->0.*********** * 100 = %  $0.01
			Net = 9.********** -> $ 9.99

			Converted = BTC 0.********

			BTC/USD 9 500 - BTC/USD 9 600.01 =100.01
			 * 
			 */
			bool itIsThePresent = false;
			DateTime today1 = DateTime.Now;
			DateTime today2 = today1.AddMilliseconds(1);
			Company company = new Company();CurrenciesTest.AddCurrencies();
			
			company.Sales.CreateStore(1, "Test Store").MakeCurrent();
			string ced="abc123";string accountNumber = "B15461";Customer customer=company.GetOrCreateCustomerById(accountNumber);customer.Identifier=ced;
			CustomerAccount bitcoinAccount = customer.CreateNewAccountFor(Coinage.Coin(Currencies.CODES.BTC));
			CustomerAccount dollarAccount = customer.CreateNewAccountFor(Coinage.Coin(Currencies.CODES.USD));
			Marketplace marketplace = new Marketplace(company, "CR");
			var a = (GamesEngine.Exchange.Agent)marketplace.AddAgent("A");
			var b = a.AddCashier("Juan");
			string employeeName = "Juan";
			Domain domain = company.Sales.CreateDomain(false, 1, "localhost", PaymentChannels.Agents.INSIDER);
			company.Sales.CurrentStore.Add(domain);
			GuardianTest.LoadProcessors(company);

			#region rate #1
			RegularConversionSpread rate1 = marketplace.CreateConversionSpread(1, today1, itIsThePresent, Coinage.Coin(Currencies.CODES.USD), Coinage.Coin(Currencies.CODES.BTC), 9600.01m, 9600.00m, employeeName);
			Assert.AreEqual(rate1, marketplace.ConversionSpreadAt(today1, Coinage.Coin(Currencies.CODES.USD), Coinage.Coin(Currencies.CODES.BTC)));
			#endregion

			BatchTransactions juanTransactions = BatchTest.SetInitialSettings(marketplace, today1, new Domain[] { domain });
			BatchSet agentBatch = juanTransactions.Batch();
			string agentPath = a.FullName;

			int id2 = marketplace.NewTransationNumber();

			var transactionType = marketplace.Company.System.TransactionTypes.SearchByName<ProcessorTransaction>(TransactionType.Deposit.ToString());
			int authorizationNumber = PaymentChannels.Deposit(
				itIsThePresent,
				Coinage.Coin(Currencies.CODES.USD),
				domain,
				transactionType,
				new DespositBody(
					accountNumber,
					ced,
					10,
					"deposit test",
					today1,
					"",
					"cris",
					domain)).AuthorizationId;

			DepositTransaction transaction2 = (DepositTransaction)marketplace.From(id2, bitcoinAccount, agentPath, employeeName, domain).Deposit(today1, itIsThePresent,new Dollar(10), employeeName, (PaymentProcessor)marketplace.Company.System.PaymentProcessor.First(), 1);
			DepositTransaction transaction22 = (DepositTransaction)marketplace.FindDraftTransaction(transaction2.Id, agentPath, employeeName);

			Assert.AreEqual(transaction2, transaction22);
			Btc expectedResult = new Btc(0.********m);
			Btc expectedAvailableInBatch = new Btc(199.99895938m);

			List<BatchMovement> movements = juanTransactions.Movements().ToList();
			Assert.AreEqual(3, movements.Count);
			Assert.AreEqual(expectedResult, movements[0].Amount, "This is the blocked BTC to the OUT account when transaction will be approved.");
			Assert.AreEqual(new Dollar(10), movements[1].Amount, "This is the amount to accredit to the IN account when transaction will be approved.");
			Assert.AreEqual(new Dollar(10), movements[2].Amount, "This is the blocked amount to the IN account when transaction will be approved.");
			Assert.AreEqual(expectedResult, juanTransactions.Locked(Coinage.Coin(Currencies.CODES.BTC)), $"BTC {expectedResult} should be locked meanwhile transaction it's approved.");
			Assert.AreEqual(new Dollar(10), juanTransactions.LockedReception(Coinage.Coin(Currencies.CODES.USD)), $"USD {new Dollar(10)} should be locked meanwhile transaction it's approved.");
			Assert.AreEqual(expectedAvailableInBatch, juanTransactions.Available(Coinage.Coin(Currencies.CODES.BTC)), "No available modification are expected.");
			Assert.AreEqual(new Dollar(0), juanTransactions.AvailableReception(Coinage.Coin(Currencies.CODES.USD)), "No available modification are expected.");
			Assert.AreEqual(new Dollar(5000000m), juanTransactions.Available(Coinage.Coin(Currencies.CODES.USD)), "No available modification are expected.");
			Assert.AreEqual(new Eth(5m), juanTransactions.Available(Coinage.Coin(Currencies.CODES.ETH)), "No available modification are expected.");

			TransactionCompleted result1 = marketplace.Approve(today1, itIsThePresent, transaction2, 1, employeeName);
			movements = juanTransactions.Movements().ToList();
			Assert.AreEqual(6, movements.Count);
			Assert.AreEqual(expectedResult, movements[3].Amount, "This is the unlocked BTC to the OUT account because transaction was approved.");
			Assert.AreEqual(new Dollar(10), movements[4].Amount, "This is the unlocked amount to the IN account because transaction was approved.");
			Assert.AreEqual(new Dollar(10), movements[5].Amount, "This is the amount to accredit to the IN account because transaction was approved.");
			Assert.AreEqual(new Btc(0), juanTransactions.Locked(Coinage.Coin(Currencies.CODES.BTC)), "BTC $500 should be unlocked when transaction it's approved.");
			Assert.AreEqual(new Dollar(0), juanTransactions.LockedReception(Coinage.Coin(Currencies.CODES.USD)), "USD $100 should be unlocked when transaction it's approved.");
			Assert.AreEqual(expectedAvailableInBatch, juanTransactions.Available(Coinage.Coin(Currencies.CODES.BTC)), "Available should increase.");
			Assert.AreEqual(new Dollar(10), juanTransactions.AvailableReception(Coinage.Coin(Currencies.CODES.USD)), "Available should increase.");
			Assert.AreEqual(new Dollar(5000000m), juanTransactions.Available(Coinage.Coin(Currencies.CODES.USD)), "No available modification are expected.");
			Assert.AreEqual(new Eth(5m), juanTransactions.Available(Coinage.Coin(Currencies.CODES.ETH)), "No available modification are expected.");
			//GamesEngine.Custodian.PaymentMethodsActions.ResetInstance();
		}

		[TestMethod]
		public void BatchMovements_RejectedTransaction()
		{
			/*
			3 BTC me costaron 28 500
			1BTc me costo $9 500  -> BTC/USD 9 500


			BTC/USD 9 600.00
			BTC/USD 9 600.01
			Rate  0.***********

			$10 -> BTC
			Comsion ->0.*********** * 100 = %  $0.01
			Net = 9.********** -> $ 9.99

			Converted = BTC 0.********

			BTC/USD 9 500 - BTC/USD 9 600.01 =100.01
			 * 
			 */
			bool itIsThePresent = false;
			DateTime today1 = DateTime.Now;
			DateTime today2 = today1.AddMilliseconds(1);
			Company company = new Company();CurrenciesTest.AddCurrencies();
			
			company.Sales.CreateStore(1, "Test Store").MakeCurrent();
			string ced="abc123";string accountNumber = "B15461";Customer customer=company.GetOrCreateCustomerById(accountNumber);customer.Identifier=ced;
			CustomerAccount bitcoinAccount = customer.CreateNewAccountFor(Coinage.Coin(Currencies.CODES.BTC));
			CustomerAccount dollarAccount = customer.CreateNewAccountFor(Coinage.Coin(Currencies.CODES.USD));
			Marketplace marketplace = new Marketplace(company, "CR");
			var a = (GamesEngine.Exchange.Agent)marketplace.AddAgent("A");
			var b = a.AddCashier("Juan");
			string employeeName = "Juan";
			Domain domain = company.Sales.CreateDomain(false, 1, "localhost", PaymentChannels.Agents.INSIDER);
			company.Sales.CurrentStore.Add(domain);
			GuardianTest.LoadProcessors(company);

			#region rate #1
			RegularConversionSpread rate1 = marketplace.CreateConversionSpread(1, today1, itIsThePresent, Coinage.Coin(Currencies.CODES.USD), Coinage.Coin(Currencies.CODES.BTC), 9600.01m, 9600.00m, employeeName);
			Assert.AreEqual(rate1, marketplace.ConversionSpreadAt(today1, Coinage.Coin(Currencies.CODES.USD), Coinage.Coin(Currencies.CODES.BTC)));
			#endregion

			BatchTransactions juanTransactions = BatchTest.SetInitialSettings(marketplace, today1, new Domain[] { domain });
			BatchSet agentBatch = juanTransactions.Batch();
			string agentPath = a.FullName;

			int id2 = marketplace.NewTransationNumber();

			var transactionType = marketplace.Company.System.TransactionTypes.SearchByName<ProcessorTransaction>(TransactionType.Deposit.ToString());
			int authorizationNumber = PaymentChannels.Deposit(
				itIsThePresent,
				Coinage.Coin(Currencies.CODES.USD),
				domain,
				transactionType,
				new DespositBody(
					accountNumber,
					ced,
					10,
					"deposit test",
					today1,
					"",
					"cris",
					domain)).AuthorizationId;

			DepositTransaction transaction2 = (DepositTransaction)marketplace.From(id2, bitcoinAccount, agentPath, employeeName, domain).Deposit(today1, itIsThePresent, new Dollar(10), employeeName, (PaymentProcessor)marketplace.Company.System.PaymentProcessor.First(), 1);
			DepositTransaction transaction22 = (DepositTransaction)marketplace.FindDraftTransaction(transaction2.Id, agentPath, employeeName);

			Assert.AreEqual(transaction2, transaction22);
			Btc expectedResult = new Btc(0.********m);

			List<BatchMovement> movements = juanTransactions.Movements().ToList();

			Assert.AreEqual(3, movements.Count);
			Assert.AreEqual(expectedResult, movements[0].Amount, "This is the blocked BTC to the OUT account when transaction will be approved.");
			Assert.AreEqual(new Dollar(10), movements[1].Amount, "This is the amount to accredit to the IN account when transaction will be approved.");
			Assert.AreEqual(new Dollar(10), movements[2].Amount, "This is the blocked amount to the IN account when transaction will be approved.");
			Assert.AreEqual(expectedResult, juanTransactions.Locked(Coinage.Coin(Currencies.CODES.BTC)), $"BTC {expectedResult} should be locked meanwhile transaction it's approved.");
			Assert.AreEqual(new Dollar(10), juanTransactions.LockedReception(Coinage.Coin(Currencies.CODES.USD)), $"USD {new Dollar(10)} should be locked meanwhile transaction it's approved.");
			Assert.AreEqual(new Btc(200 - expectedResult.Value), juanTransactions.Available(Coinage.Coin(Currencies.CODES.BTC)), "Available should decrease.");
			Assert.AreEqual(new Dollar(0), juanTransactions.AvailableReception(Coinage.Coin(Currencies.CODES.USD)), "No available modification are expected.");
			Assert.AreEqual(new Dollar(5000000m), juanTransactions.Available(Coinage.Coin(Currencies.CODES.USD)), "No available modification are expected.");
			Assert.AreEqual(new Eth(5m), juanTransactions.Available(Coinage.Coin(Currencies.CODES.ETH)), "No available modification are expected.");

			transaction2.Deny(today1, itIsThePresent, employeeName, "He is so ugly.");
			movements = juanTransactions.Movements().ToList();

			Assert.AreEqual(6, movements.Count, "Unlock and credit movments were expected.");
			Assert.AreEqual(expectedResult, movements[3].Amount, "This is the unlocked BTC to the OUT account when transaction will be approved.");
			Assert.AreEqual(expectedResult, movements[4].Amount, "This is the accredit BTC to the OUT account when transaction will be approved.");
			Assert.AreEqual(new Dollar(10), movements[5].Amount, "This is the unlocked amount to the IN account when transaction will be approved.");

			Assert.AreEqual(new Btc(200), juanTransactions.Available(Coinage.Coin(Currencies.CODES.BTC)), "200 Btc it's the original available in agent batch.");
			Assert.AreEqual(new Dollar(5000000m), juanTransactions.Available(Coinage.Coin(Currencies.CODES.USD)), "No available modification are expected.");
			Assert.AreEqual(new Eth(5m), juanTransactions.Available(Coinage.Coin(Currencies.CODES.ETH)), "No available modification are expected.");
			Assert.AreEqual(new Btc(0), juanTransactions.Locked(Coinage.Coin(Currencies.CODES.BTC)), "No lock is expected after approve the transaction");
			Assert.AreEqual(new Dollar(0m), juanTransactions.Locked(Coinage.Coin(Currencies.CODES.USD)), "No available modification are expected.");
			Assert.AreEqual(new Eth(0m), juanTransactions.Locked(Coinage.Coin(Currencies.CODES.ETH)), "No available modification are expected.");

			Assert.AreEqual(new Btc(0), juanTransactions.AvailableReception(Coinage.Coin(Currencies.CODES.BTC)), "No available modification are expected.");
			Assert.AreEqual(new Dollar(0), juanTransactions.AvailableReception(Coinage.Coin(Currencies.CODES.USD)), "No available modification are expected.");
			Assert.AreEqual(new Eth(0), juanTransactions.AvailableReception(Coinage.Coin(Currencies.CODES.ETH)), "No available modification are expected.");
			Assert.AreEqual(new Btc(0), juanTransactions.LockedReception(Coinage.Coin(Currencies.CODES.BTC)), "No available modification are expected.");
			Assert.AreEqual(new Dollar(0m), juanTransactions.LockedReception(Coinage.Coin(Currencies.CODES.USD)), "No available modification are expected.");
			Assert.AreEqual(new Eth(0m), juanTransactions.LockedReception(Coinage.Coin(Currencies.CODES.ETH)), "No available modification are expected.");
			//GamesEngine.Custodian.PaymentMethodsActions.ResetInstance();
		}

		[TestMethod]
		public void ValidateTransactionCreationIfHasNotAvailableInBatch()
		{
			CurrenciesTest.AddCurrencies();
			Company company;
			Guardian guardian;
			QueueInMemory queue;
			DateTime today = DateTime.Now;
			GuardianTest.Init(today, out company, out guardian, out queue);

			bool itIsThePresent = false;
			DateTime today1 = DateTime.Now;
			DateTime today2 = today1.AddMilliseconds(1);
			//Company company = new Company();CurrenciesTest.AddCurrencies();
			//company.Sales.CreateStore(1, "Test Store").MakeCurrent();
			string ced="abc123";string accountNumber = "B15461";Customer customer=company.GetOrCreateCustomerById(accountNumber);customer.Identifier=ced;
			CustomerAccount bitcoinAccount = customer.CreateNewAccountFor(Coinage.Coin(Currencies.CODES.BTC));
			CustomerAccount dollarAccount = customer.CreateNewAccountFor(Coinage.Coin(Currencies.CODES.USD));
			CustomerAccount ethereumAccount = customer.CreateNewAccountFor(Coinage.Coin(Currencies.CODES.ETH));
			Marketplace marketplace = new Marketplace(company, "CR");
			marketplace.RegisterNewAccount(1, bitcoinAccount);
			marketplace.RegisterNewAccount(2, dollarAccount);
			marketplace.RegisterNewAccount(3, ethereumAccount);
			var a = (GamesEngine.Exchange.Agent)marketplace.AddAgent("A");
			var b = a.AddCashier("Juan");
			string employeeName = "Juan";
			Domain domain = company.Sales.CreateDomain(false, 1, "localhost", PaymentChannels.Agents.INSIDER);
			//company.Sales.CurrentStore.Add(domain);

			GuardianTest.LoadProcessors(company);
			PaymentProcessorsAndActionsByDomains processors = marketplace.PaymentProcessors();
			PaymentProcessor GuardianBTCDriver = processors.SearchById("TEST-Secrets-TestDepositBTC-BTC-1");
			GuardianBTCDriver = processors.SearchById("TEST-Secrets-TestWithdrawalBTC-BTC-1");

			#region rate #1
			RegularConversionSpread rate1 = marketplace.CreateConversionSpread(1, today1, itIsThePresent, Coinage.Coin(Currencies.CODES.USD), Coinage.Coin(Currencies.CODES.BTC), 9600.01m, 9600.00m, employeeName);
			Assert.AreEqual(rate1, marketplace.ConversionSpreadAt(today1, Coinage.Coin(Currencies.CODES.USD), Coinage.Coin(Currencies.CODES.BTC)));
			#endregion

			BatchTransactions juanTransactions = BatchTest.SetInitialSettings(marketplace, today1, new Domain[] { domain });
			BatchSet agentBatch = juanTransactions.Batch();
			string agentPath = a.FullName;


			try
			{
				int id2 = marketplace.NewTransationNumber();

				var transactionType = marketplace.Company.System.TransactionTypes.SearchByName<ProcessorTransaction>(TransactionType.Deposit.ToString());
				int authorizationNumber = PaymentChannels.Deposit(
					itIsThePresent,
					Coinage.Coin(Currencies.CODES.BTC),
					domain,
					transactionType,
					new DespositBody(
						accountNumber,
						ced,
						300,
						"deposit test",
						today1,
						"",
						"cris",
						domain)).AuthorizationId;

				DepositTransaction transaction2 = (DepositTransaction)marketplace.From(id2, bitcoinAccount, agentPath, employeeName, domain).Deposit(today1, itIsThePresent, new Btc(300), employeeName, (PaymentProcessor)marketplace.Company.System.PaymentProcessor.First(), 1);
			}
			catch (GameEngineException e)
			{
				Assert.AreEqual("It's not possible to make a deposit, because there's no exist funds in BTC", e.Message);
			}

			try
			{
				int id2 = marketplace.NewTransationNumber();
				TransferTransaction transaction1 = (TransferTransaction)marketplace.From(id2, ethereumAccount, agentPath, employeeName, domain).TransferTo(today1, itIsThePresent, new Eth(10), 1, bitcoinAccount, employeeName, (PaymentProcessor)marketplace.Company.System.PaymentProcessor.First(), 1);
			}
			catch(Exception e)
			{
				Assert.AreEqual("It's not possible to make a transfer, because there's no exist funds in ETH", e.Message);
			}

			try
			{
				int id2 = marketplace.NewTransationNumber();
				var processor = marketplace.Company.System.PaymentProcessor.SearchWithdrawalProcessorBy("BTC");
				WithdrawalTransaction transaction1 = (WithdrawalTransaction)marketplace.From(id2, bitcoinAccount, agentPath, employeeName, domain).Withdraw(today1, itIsThePresent, new Btc(250), 1, employeeName, "1-11", "", new NoFeeUSD(), processor, 1);
			}
			catch (GameEngineException e)
			{
				Assert.AreEqual("It's not possible to make a withdraw, because there's no exist funds in BTC", e.Message);
			}

			//GamesEngine.Custodian.PaymentMethodsActions.ResetInstance();
		}

		[TestMethod]
		public void Blocks_Deposit_BTC_to_BTCAccount()
		{
			CurrenciesTest.AddCurrencies();
			Company company;
			Guardian guardian;
			QueueInMemory queue;
			DateTime today = DateTime.Now;
			GuardianTest.Init(today, out company, out guardian, out queue);

			//QueueInMemory queue = new QueueInMemory();
			Integration.Kafka = queue;
			Integration.SavePreviousSettings(tempUseKafka: true, tempUseKafkaForAuto: false);

			bool itIsThePresent = false;
			DateTime today1 = DateTime.Now;
			DateTime today2 = today1.AddMilliseconds(1);
			//Company company = new Company();CurrenciesTest.AddCurrencies();
			//company.Sales.CreateStore(1, "Test Store").MakeCurrent();
			string ced="abc123";string accountNumber = "B15461";Customer customer=company.GetOrCreateCustomerById(accountNumber);customer.Identifier=ced;
			CustomerAccount btcccount = customer.CreateNewAccountFor(Coinage.Coin(Currencies.CODES.BTC));
			Marketplace marketplace = new Marketplace(company, "CR");
			var a = (GamesEngine.Exchange.Agent)marketplace.AddAgent("A");
			var b = a.AddCashier("Juan");
			var c = (GamesEngine.Exchange.Agent)marketplace.AddAgent("blocks");
			c.AddAgent("blocks");
			string employeeName = "Juan";
			Domain domain = company.Sales.CreateDomain(false, 1, "localhost", PaymentChannels.Agents.INSIDER);
//			company.Sales.CurrentStore.Add(domain);

			//GuardianTest.LoadProcessors(company);
			PaymentProcessorsAndActionsByDomains processors = marketplace.PaymentProcessors();
			PaymentProcessor GuardianBTCDriver = processors.SearchById("TEST-Secrets-TestDepositBTC-BTC-1");

			#region rate #1
			RegularConversionSpread rate1 = marketplace.CreateConversionSpread(1, today1, itIsThePresent, Coinage.Coin(Currencies.CODES.USD), Coinage.Coin(Currencies.CODES.BTC), 60m, 30m, employeeName);
			Assert.AreEqual(rate1, marketplace.ConversionSpreadAt(today1, Coinage.Coin(Currencies.CODES.USD), Coinage.Coin(Currencies.CODES.BTC)));

			RegularConversionSpread rate2 = marketplace.CreateConversionSpread(2, today1, itIsThePresent, Coinage.Coin(Currencies.CODES.BTC), Coinage.Coin(Currencies.CODES.USD), 1.33333333m, 1.24444444m, employeeName);
			Assert.AreEqual(rate2, marketplace.ConversionSpreadAt(today1, Coinage.Coin(Currencies.CODES.BTC), Coinage.Coin(Currencies.CODES.USD)));
			#endregion

			BatchTransactions juanTransactions = BatchTest.SetInitialSettings(marketplace, today1, new Domain[] { domain });
			BatchSet agentBatch = juanTransactions.Batch();
			string agentPath = a.FullName;

			int id = marketplace.NewTransationNumber();

			var transactionType = marketplace.Company.System.TransactionTypes.SearchByName<ProcessorTransaction>(TransactionType.Deposit.ToString());
			int authorizationNumber = PaymentChannels.Deposit(
				itIsThePresent,
				Coinage.Coin(Currencies.CODES.USD),
				domain,
				transactionType,
				new DespositBody(
					accountNumber,
					ced,
					200,
					"deposit test",
					today1,
					"",
					"cris",
					domain)).AuthorizationId;

			AgentBatch batch = (AgentBatch)marketplace.SearchAgentBatch("CR/blocks/blocks");
			DepositTransaction transaction1 = (DepositTransaction)marketplace.From(id, btcccount, batch, "CR/blocks/blocks", domain).Deposit(today1, itIsThePresent, new Btc(200), employeeName, (PaymentProcessor)marketplace.Company.System.PaymentProcessor.First(), 1);
			//DepositTransaction transaction = (DepositTransaction)marketplace.FindDraftTransaction(transaction1.Id, "CR/blocks/blocks");

			transaction1.SaveTrackingInformation("BG87RFUY", "AI12TFUY");

			BatchTransactions transactions = marketplace.SearchAgentBatch("CR/blocks/blocks").BatchTransactions;

			DepositTransaction transactionsearched = (DepositTransaction)batch.SearchTransactionByTrackinginformation("BG87RFUY", "AI12TFUY");
			Assert.AreEqual(transaction1, transactionsearched);

			List<BatchMovement> movements = transactions.Movements().ToList();
			Assert.AreEqual(2, movements.Count);
			Assert.AreEqual(new Btc(200), movements[0].Amount, "This is the amount to accredit to the IN account when transaction will be approved.");
			Assert.AreEqual(new Btc(200), movements[1].Amount, "This is the blocked BTC to the IN account when transaction will be approved.");
			Assert.AreEqual(new Btc(0), transactions.Locked(Coinage.Coin(Currencies.CODES.BTC)), "BTC 500 should be locked meanwhile transaction it's approved.");
			Assert.AreEqual(new Btc(200), transactions.LockedReception(Coinage.Coin(Currencies.CODES.BTC)), "BTC 200 should be locked meanwhile transaction it's approved.");
			Assert.AreEqual(new Btc(10000m), transactions.Available(Coinage.Coin(Currencies.CODES.BTC)), "No available modification are expected.");
			Assert.AreEqual(new Btc(0), transactions.AvailableReception(Coinage.Coin(Currencies.CODES.BTC)), "No available modification are expected.");

			TransactionCompleted result1 = marketplace.Approve(today1, itIsThePresent, transaction1, 1, employeeName);
			movements = transactions.Movements().ToList();
			Assert.AreEqual(4, movements.Count);
			Assert.AreEqual(new Btc(200), movements[2].Amount, "This is the unlocked amount to the IN account because transaction was approved.");
			Assert.AreEqual(new Btc(200), movements[3].Amount, "This is the amount to accredit to the IN account because transaction was approved.");
			Assert.AreEqual(new Btc(0), transactions.LockedReception(Coinage.Coin(Currencies.CODES.BTC)), "BTC 500 should be unlocked when transaction it's approved.");
			Assert.AreEqual(new Btc(200), transactions.AvailableReception(Coinage.Coin(Currencies.CODES.BTC)), "Available reception should increase.");

			Assert.AreEqual(new Btc(0), transactions.Locked(Coinage.Coin(Currencies.CODES.BTC)), "BTC $500 should be unocked when transaction it's approved.");
			Assert.AreEqual(new Btc(10000m), transactions.Available(Coinage.Coin(Currencies.CODES.BTC)), "Available should not increase.");

			try
			{
				transactions.SearchTransactionByTrackinginformation("BG87RFUY", "AI12TFUY");
				Assert.Fail("Transaction should be deleted from memory.");
			}
			catch (GameEngineException e)
			{
				Assert.AreEqual(transaction1, transactionsearched);
			}

			Assert.AreEqual(batch.InitialAccount.InitialAmountFor(Coinage.Coin(Currencies.CODES.USD)), batch.InitialAccount.CurrentBalance(Coinage.Coin(Currencies.CODES.USD)));
			Assert.AreEqual(batch.InitialAccount.InitialAmountFor(Coinage.Coin(Currencies.CODES.BTC)) + 200, batch.InitialAccount.CurrentBalance(Coinage.Coin(Currencies.CODES.BTC)));
			Assert.AreEqual(0, marketplace.RealAccounts.Movements(Coinage.Coin(Currencies.CODES.USD)));
			Assert.AreEqual(0, marketplace.RealAccounts.Movements(Coinage.Coin(Currencies.CODES.BTC)));

			batch.Close(false, today1, "N/A");
			Assert.AreEqual(batch.InitialAccount.InitialAmountFor(Coinage.Coin(Currencies.CODES.USD)), batch.InitialAccount.CurrentBalance(Coinage.Coin(Currencies.CODES.USD)));
			Assert.AreEqual(batch.InitialAccount.InitialAmountFor(Coinage.Coin(Currencies.CODES.BTC)) + 200, batch.InitialAccount.CurrentBalance(Coinage.Coin(Currencies.CODES.BTC)));
			//GamesEngine.Custodian.PaymentMethodsActions.ResetInstance();
		}

		[TestMethod]
		public void Blocks_Deposit_BTC_to_USDAccount()
		{
			CurrenciesTest.AddCurrencies();
			Company company;
			Guardian guardian;
			QueueInMemory queue;
			DateTime today = DateTime.Now;
			GuardianTest.Init(today, out company, out guardian, out queue);

			bool itIsThePresent = false;
			DateTime today1 = DateTime.Now;
			DateTime today2 = today1.AddMilliseconds(1);
			//Company company = new Company();CurrenciesTest.AddCurrencies();
			//company.Sales.CreateStore(1, "Test Store").MakeCurrent();
			string ced="abc123";string accountNumber = "B15461";Customer customer=company.GetOrCreateCustomerById(accountNumber);customer.Identifier=ced;
			CustomerAccount usdccount = customer.CreateNewAccountFor(Coinage.Coin(Currencies.CODES.USD));
			Marketplace marketplace = new Marketplace(company, "CR");
			var a = (GamesEngine.Exchange.Agent)marketplace.AddAgent("A");
			var b = a.AddCashier("Juan");
			var c = (GamesEngine.Exchange.Agent)marketplace.AddAgent("blocks");
			c.AddAgent("blocks");
			string employeeName = "Juan";
			Domain domain = company.Sales.CreateDomain(false, 1, "localhost", PaymentChannels.Agents.INSIDER);
			//company.Sales.CurrentStore.Add(domain);

			GuardianTest.LoadProcessors(company);
			PaymentProcessorsAndActionsByDomains processors = marketplace.PaymentProcessors();
			PaymentProcessor GuardianBTCDriver = processors.SearchById("TEST-Secrets-TestDepositBTC-BTC-1");

			#region rate #1
			RegularConversionSpread rate1 = marketplace.CreateANewConversionSpread(today1, Coinage.Coin(Currencies.CODES.USD), Coinage.Coin(Currencies.CODES.BTC), 60m, 30m);
			#endregion

			BatchTransactions juanTransactions = BatchTest.SetInitialSettings(marketplace, today1, new Domain[] { domain });
			BatchSet agentBatch = juanTransactions.Batch();
			string agentPath = a.FullName;

			int id = marketplace.NewTransationNumber();

			var transactionType = marketplace.Company.System.TransactionTypes.SearchByName<ProcessorTransaction>(TransactionType.Deposit.ToString());
			int authorizationNumber = PaymentChannels.Deposit(
				itIsThePresent,
				Coinage.Coin(Currencies.CODES.BTC),
				domain,
				transactionType,
				new DespositBody(
					accountNumber,
					ced,
					200,
					"deposit test",
					today1,
					"",
					"cris",
					domain)).AuthorizationId;

			AgentBatch batch = (AgentBatch)marketplace.SearchAgentBatch("CR/blocks/blocks");
			DepositTransaction transaction1 = (DepositTransaction)marketplace.From(id, usdccount, batch, "CR/blocks/blocks", domain).Deposit(today1, itIsThePresent, rate1, new Btc(200), employeeName, (PaymentProcessor)marketplace.Company.System.PaymentProcessor.First(), 1);
			//DepositTransaction transaction = (DepositTransaction)marketplace.FindDraftTransaction(transaction1.Id, "CR/blocks/blocks");
			transaction1.SaveTrackingInformation("BG87RFUY", "AI12TFUY");

			BatchTransactions transactions = marketplace.SearchAgentBatch("CR/blocks/blocks").BatchTransactions;

			DepositTransaction transactionsearched = (DepositTransaction)batch.SearchTransactionByTrackinginformation("BG87RFUY", "AI12TFUY");
			Assert.AreEqual(transaction1, transactionsearched);

			List<BatchMovement> movements = transactions.Movements().ToList();
			Assert.AreEqual(3, movements.Count);

			TransactionCompleted result1 = marketplace.Approve(today1, itIsThePresent, transaction1, 1, employeeName);
			movements = movements = transactions.Movements().ToList();
			Assert.AreEqual(6, movements.Count);

			try
			{
				transactions.SearchTransactionByTrackinginformation("BG87RFUY", "AI12TFUY");
				Assert.Fail("Transaction should be deleted from memory.");
			}
			catch (GameEngineException e)
			{
				Assert.AreEqual(transaction1, transactionsearched);
			}

			Assert.AreEqual(batch.InitialAccount.InitialAmountFor(Coinage.Coin(Currencies.CODES.USD)) - result1.Result.Amount.Value, batch.InitialAccount.CurrentBalance(Coinage.Coin(Currencies.CODES.USD)));
			Assert.AreEqual(batch.InitialAccount.InitialAmountFor(Coinage.Coin(Currencies.CODES.BTC)) + 200, batch.InitialAccount.CurrentBalance(Coinage.Coin(Currencies.CODES.BTC)));
			Assert.AreEqual(0, marketplace.RealAccounts.Movements(Coinage.Coin(Currencies.CODES.USD)));
			Assert.AreEqual(0, marketplace.RealAccounts.Movements(Coinage.Coin(Currencies.CODES.BTC)));

			batch.Close(false, today1, "N/A");
			Assert.AreEqual(batch.InitialAccount.InitialAmountFor(Coinage.Coin(Currencies.CODES.USD)) - result1.Result.Amount.Value, batch.InitialAccount.CurrentBalance(Coinage.Coin(Currencies.CODES.USD)));
			Assert.AreEqual(batch.InitialAccount.InitialAmountFor(Coinage.Coin(Currencies.CODES.BTC)) + 200, batch.InitialAccount.CurrentBalance(Coinage.Coin(Currencies.CODES.BTC)));

			Assert.AreEqual(transaction1, transactionsearched);
			//GamesEngine.Custodian.PaymentMethodsActions.ResetInstance();
		}

		[TestMethod]
		public void Bug_Wrong_Batch_Amount_After_RejectedTransaction()
		{

			bool itIsThePresent = false;
			DateTime today1 = DateTime.Now;
			DateTime today2 = today1.AddMilliseconds(1);
			Company company = new Company();CurrenciesTest.AddCurrencies();
			company.Sales.CreateStore(1, "Test Store").MakeCurrent();
			string ced="abc123";string accountNumber = "B15461";Customer customer=company.GetOrCreateCustomerById(accountNumber);customer.Identifier=ced;
			CustomerAccount bitcoinAccount = customer.CreateNewAccountFor(Coinage.Coin(Currencies.CODES.BTC));
			CustomerAccount dollarAccount = customer.CreateNewAccountFor(Coinage.Coin(Currencies.CODES.USD));
			Marketplace marketplace = new Marketplace(company, "CR");
			var a = (GamesEngine.Exchange.Agent)marketplace.AddAgent("A");
			var b = a.AddCashier("Juan");
			string employeeName = "Juan";
			Domain domain = company.Sales.CreateDomain(false, 1, "localhost", PaymentChannels.Agents.INSIDER);
			company.Sales.CurrentStore.Add(domain);
			GuardianTest.LoadProcessors(company);

			#region rate #1
			RegularConversionSpread rate1 = marketplace.CreateConversionSpread(1, today1, itIsThePresent, Coinage.Coin(Currencies.CODES.USD), Coinage.Coin(Currencies.CODES.BTC), 9600.01m, 9600.00m, employeeName);
			Assert.AreEqual(rate1, marketplace.ConversionSpreadAt(today1, Coinage.Coin(Currencies.CODES.USD), Coinage.Coin(Currencies.CODES.BTC)));
			#endregion

			MarketplaceBatch marketPLaceBatch;
			AgentBatch agencyBatch;
			BatchTransactions juanTransactions = BatchTest.SetInitialSettings(marketplace, today1, new Domain[] { domain }, out marketPLaceBatch, out agencyBatch);
			BatchSet agentBatch = juanTransactions.Batch();
			string agentPath = a.FullName;

			int id = marketplace.NewTransationNumber();

			var transactionType = marketplace.Company.System.TransactionTypes.SearchByName<ProcessorTransaction>(TransactionType.Deposit.ToString());
			int authorizationNumber = PaymentChannels.Deposit(
				itIsThePresent,
				Coinage.Coin(Currencies.CODES.USD),
				domain,
				transactionType,
				new DespositBody(
					accountNumber,
					ced,
					9.9m,
					"deposit test",
					today1,
					"",
					"cris",
					domain)).AuthorizationId;

			DepositTransaction transactionA = (DepositTransaction)marketplace.From(id, bitcoinAccount, agentPath, employeeName, domain).Deposit(today1, itIsThePresent, new Dollar(9.9m), employeeName, (PaymentProcessor)marketplace.Company.System.PaymentProcessor.First(), 1);
			var journalEntryNumber = marketplace.NewJournalEntryNumber();
			transactionA.Approve(today1, itIsThePresent, employeeName, journalEntryNumber);

			id = marketplace.NewTransationNumber();

			transactionType = marketplace.Company.System.TransactionTypes.SearchByName<ProcessorTransaction>(TransactionType.Deposit.ToString());
			authorizationNumber = PaymentChannels.Deposit(
				itIsThePresent,
				Coinage.Coin(Currencies.CODES.USD),
				domain,
				transactionType,
				new DespositBody(
					accountNumber,
					ced,
					10m,
					"deposit test",
					today1,
					"",
					"cris",
					domain)
				).AuthorizationId;

			DepositTransaction transactionB = (DepositTransaction)marketplace.From(id, bitcoinAccount, agentPath, employeeName, domain).Deposit(today1, itIsThePresent, new Dollar(10m), employeeName, (PaymentProcessor)marketplace.Company.System.PaymentProcessor.First(), 1);
			transactionB.Deny(today1, itIsThePresent, employeeName, "He is so ugly.");
			
			BatchAccount marketplaceInBlancesResults = marketPLaceBatch.SelfReceptionAccounts(Coinage.Coin(Currencies.CODES.USD));
			BatchAccount agencyInBalances = agencyBatch.SelfReceptionAccounts(Coinage.Coin(Currencies.CODES.USD));
			BatchAccount agentInBalances = agentBatch.SelfReceptionAccounts(Coinage.Coin(Currencies.CODES.USD));
			BatchAccount marketplaceOutBlancesResults = marketPLaceBatch.SaleAccount(Coinage.Coin(Currencies.CODES.USD));
			BatchAccount agencyOutBalances = agencyBatch.SaleAccount(Coinage.Coin(Currencies.CODES.USD));
			BatchAccount agentOutBalances = agentBatch.SaleAccount(Coinage.Coin(Currencies.CODES.USD));

			Assert.AreEqual(new Dollar(0m), agentInBalances.Initial);
			Assert.AreEqual(new Dollar(9.9m), agentInBalances.Available);
			Assert.AreEqual(new Dollar(0m), agentInBalances.Locked);
			Assert.AreEqual(new Dollar(0m), agentInBalances.LockedAccumulated);
			Assert.AreEqual(new Dollar(0m), agentInBalances.SpendAccumulated);
			Assert.AreEqual(new Dollar(5000000m), agentOutBalances.Initial);
			Assert.AreEqual(new Dollar(5000000m), agentOutBalances.Available);
			Assert.AreEqual(new Dollar(0m), agentOutBalances.Locked);
			Assert.AreEqual(new Dollar(0m), agentOutBalances.LockedAccumulated);
			Assert.AreEqual(new Dollar(0m), agentOutBalances.SpendAccumulated);
			//GamesEngine.Custodian.PaymentMethodsActions.ResetInstance();
		}
	}
}
