﻿using GamesEngine.Business;
using GamesEngine.Custodian;
using GamesEngine.Exchange;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using static Nest.JoinField;

namespace GamesEngineTests.Exchange
{
	[TestClass]
	public class AgentTests
    {
		[TestMethod]
		public void HasCashiers()
		{
			Company company = new Company();
			Marketplace marketplace = new Marketplace(company, "CR");

			var agent = (GamesEngine.Exchange.Agent)marketplace.AddAgent("Cartago");
			Assert.IsFalse(agent.HasCashiers);
			Assert.AreEqual(0, agent.CashiersCount);

			Assert.IsFalse(agent.ExistsCashier("123"));
			agent.AddCashier("123");
			Assert.IsTrue(agent.ExistsCashier("123"));
			Assert.IsTrue(agent.HasCashiers);
			Assert.AreEqual(1, agent.CashiersCount);

			agent.AddUser("Cashier", "cashier1");
			Assert.AreEqual(2, agent.CashiersCount);
		}

		[TestMethod]
		public void HasManagers()
		{
			Company company = new Company();
			Marketplace marketplace = new Marketplace(company, "CR");

			var agent = (GamesEngine.Exchange.Agent)marketplace.AddAgent("Cartago");
			Assert.IsFalse(agent.HasManagers);
			Assert.AreEqual(0, agent.ManagersCount);

			Assert.IsFalse(agent.ExistsManager("admin"));
			agent.AddManager("admin");
			Assert.IsTrue(agent.ExistsManager("admin"));
			Assert.IsTrue(agent.HasManagers);
			Assert.AreEqual(1, agent.ManagersCount);

			agent.AddUser("Manager", "managerA");
			Assert.AreEqual(2, agent.ManagersCount);
		}

		[TestMethod]
		public void SearchCashier()
		{
			Company company = new Company();
			Marketplace marketplace = new Marketplace(company, "CR");

			var agent = (GamesEngine.Exchange.Agent)marketplace.AddAgent("Cartago");
			var user = agent.AddCashier("cashier1");
			Assert.AreEqual(user, agent.SearchCashier("cashier1"));
		}

		[TestMethod]
		public void SearchManager()
		{
			Company company = new Company();
			Marketplace marketplace = new Marketplace(company, "CR");

			var agent = (GamesEngine.Exchange.Agent)marketplace.AddAgent("Cartago");
			var user = agent.AddManager("admin");
			Assert.AreEqual(user, agent.SearchManager("admin"));
		}

		[TestMethod]
		public void ChangeUserType()
		{
			Company company = new Company();
			Marketplace marketplace = new Marketplace(company, "CR");

			var agent = (GamesEngine.Exchange.Agent)marketplace.AddAgent("Cartago");
			var user = agent.AddUser("Cashier", "cashier1");
			agent.ChangeUserType("Manager", user);
			Assert.AreEqual(0, agent.CashiersCount);
			Assert.AreEqual(1, agent.ManagersCount);
		}

        [TestMethod]
        public void bug7964()
        {
            Company company = new Company();
            var guardian = new Guardian(company);
            Marketplace marketplace = new Marketplace(company, "CR");
            var profiles = guardian.Profiles();
            var profile1 = new Profile(1, "profile 1");
            profiles.Add(profile1);
			var profile2 = new Profile(2, "profile 2");
			profiles.Add(profile2);
			var profile3 = new Profile(3, "profile 3");
			profiles.Add(profile3);
            var profile4 = new Profile(4, "profile 4");
            profiles.Add(profile4);

            var agent = (Agent)marketplace.AddAgent("Cartago");
            var child = agent.AddUser("Manager", "cashierL");
            agent.AssignProfile(child, profile1);
			Assert.AreEqual(profile1, child.Profile);
            var count = agent.UserProfileCounts.Count(profile1);
            Assert.AreEqual(1, count);

            agent.ReassignProfile(child, profile4);
			Assert.AreEqual(profile4, child.Profile);
            count = agent.UserProfileCounts.Count(profile4);
            Assert.AreEqual(1, count);

            child = agent.SearchUser("cashierl");
            Assert.AreEqual(profile4, child.Profile);
            agent.ReassignProfile(child, profile3);
            count = agent.UserProfileCounts.Count(profile3);
            Assert.AreEqual(1, count);
            count = agent.UserProfileCounts.Count(profile1);
            Assert.AreEqual(0, count);
            count = agent.UserProfileCounts.Count(profile4);
            Assert.AreEqual(0, count);
        }
    }
}
