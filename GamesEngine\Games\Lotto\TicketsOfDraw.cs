﻿using GamesEngine.Bets;
using GamesEngine.Business;
using GamesEngine.Domains;
using GamesEngine.Finance;
using GamesEngine.Gameboards;
using GamesEngine.Gameboards.Lotto;
using GamesEngine.Time;
using Puppeteer.EventSourcing.Libraries;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using static GamesEngine.Finance.PaymentChannels;
using static GamesEngine.Games.Lotto.Lottery;
using static GamesEngine.Games.Lotto.LotteryTriz;

namespace GamesEngine.Games.Lotto
{

	class GradeStatusOfCollection
	{
		private bool allHaveTheSameGradeStatus;
		private GameboardStatus gameboardStatus;

		internal GradeStatusOfCollection()
		{
			allHaveTheSameGradeStatus = false;
		}

		internal GradeStatusOfCollection(GameboardStatus gameboardStatus)
		{
			allHaveTheSameGradeStatus = true;
			this.gameboardStatus = gameboardStatus;
		}

		internal GameboardStatus GameboardStatus
		{
			get
			{
				if (!allHaveTheSameGradeStatus) throw new GameEngineException("There are many different Ticket Status in the collection");
				return gameboardStatus;
			}
		}

		internal bool AllHaveTheSameGradeStatus
		{
			get
			{
				return allHaveTheSameGradeStatus;
			}
		}
	}

	class TicketsOfDraw
	{
		private DateTime date;
		private readonly Lottery lottery;
		private readonly Dictionary<int, Ticket> tickets = new Dictionary<int, Ticket>();
		private GradeStatusOfCollection gradeStatusOfCollection = new GradeStatusOfCollection();
		private bool WinnersAndLosersAreDeleted = false;
		private bool NoActionAreDeleted = false;
		private bool isTheSetReadyForAccounting = false;

		internal IEnumerable<Ticket> Tickets
		{
			get
			{
				return tickets.Values;
			}
		}

		internal TicketsOfDraw(Lottery lottery, DateTime date)
		{
			if (lottery == null) throw new ArgumentNullException(nameof(lottery));
			this.lottery = lottery;
			this.date = date;
		}

		internal DateTime DateOfSet
		{
			get
			{
				return date;
			}
		}

		internal Lottery Lottery
		{
			get
			{
				return lottery;
			}
		}

		internal void ChangeTicketsHour(DateTime newDate)
		{
			if (lottery.IsAlreadyGraded(date)) throw new GameEngineException($"{this.lottery.State.Abbreviation} of {date} was already drawn. Drawing hour cannot be changed.");
			this.date = newDate;
			foreach (Ticket t in tickets.Values)
			{
				t.ChangeTicketHour(newDate);
			}
		}

		internal void AddTicket(Ticket ticket)
		{
			if (tickets.ContainsKey(ticket.TicketNumber)) throw new GameEngineException($"Ticket number {ticket.TicketNumber} already exists in the collection");
			if (tickets.Count == 0)
			{
				gradeStatusOfCollection = new GradeStatusOfCollection(ticket.Grading);
			}
			else if (gradeStatusOfCollection.AllHaveTheSameGradeStatus && gradeStatusOfCollection.GameboardStatus != ticket.Grading)
			{
				gradeStatusOfCollection = new GradeStatusOfCollection();
			}
			else
			{
				//TODO: Erick ASSERT en CdL de que todos deberian tener el mismo status 
			}
			tickets.Add(ticket.TicketNumber, ticket);
		}

		private void UpdateGradingStatusOfTheWholeCollection()
		{
			if (tickets.Count == 0)
			{
				gradeStatusOfCollection = new GradeStatusOfCollection();
			}
			else
			{
				var firstTicketStatus = FirstTicketStatusWithoutNoAction();
				var allHaveSameStatus = tickets.Values.All(ticket => ticket.IsNoAction() || ticket.Grading == firstTicketStatus);
				if (allHaveSameStatus)
				{
					if (!(gradeStatusOfCollection.AllHaveTheSameGradeStatus && gradeStatusOfCollection.GameboardStatus == firstTicketStatus))
					{
						gradeStatusOfCollection = new GradeStatusOfCollection(firstTicketStatus);
					}
				}
				else
				{
					if (gradeStatusOfCollection.AllHaveTheSameGradeStatus)
					{
						gradeStatusOfCollection = new GradeStatusOfCollection();
					}
				}
			}
		}

		private GameboardStatus FirstTicketStatusWithoutNoAction()
		{
			foreach (var ticket in tickets.Values)
			{
				if (!ticket.IsNoAction()) return ticket.Grading;
			}

			return GameboardStatus.NOACTION;
		}

		internal IEnumerable<Ticket> TicketsWithoutNoAction()
		{
			var result = tickets.Values.Where(ticket => !ticket.IsNoAction() && !ticket.IsRefunded());
			return result;
		}

		internal IEnumerable<Ticket> PendingOrRegradedTickets()
		{
			var pendingOrRegradedTickets = new List<Ticket>();
			foreach (var ticket in tickets.Values)
			{
				if (ticket.IsPending() || ticket.IsRegraded())
				{
					pendingOrRegradedTickets.Add(ticket);
				}
			}

			return pendingOrRegradedTickets;
		}
		//internal IEnumerable<Ticket> PendingOrRegradedTickets(int scheduleId)
		//{
		//	var pendingOrRegradedTickets = new List<Ticket>();
		//	foreach (var ticket in tickets)
		//	{
		//		var isPending = ticket.IsPending() || ticket.IsRegraded();
				
		//		if ((ticket as TicketKeno).DrawId == scheduleId && isPending)
		//		{
		//			pendingOrRegradedTickets.Add(ticket);
		//		}
		//	}

		//	return pendingOrRegradedTickets;
		//}
		internal IEnumerable<Ticket> TicketsWithDrawDateBetween(DateTime startDrawDay, DateTime endDrawDay)
		{
			if (startDrawDay.Hour != 0 || startDrawDay.Minute != 0 || startDrawDay.Second != 0 || startDrawDay.Millisecond != 0) throw new GameEngineException($"{nameof(startDrawDay)} '{startDrawDay}' must be an exact day");
			if (endDrawDay.Hour != 0 || endDrawDay.Minute != 0 || endDrawDay.Second != 0 || endDrawDay.Millisecond != 0) throw new GameEngineException($"{nameof(endDrawDay)} '{endDrawDay}' must be an exact day");

			var result = tickets.Values.Where(ticket => ticket.HasDrawDateBetween(startDrawDay, endDrawDay));
			return result;
		}

		private decimal totalPrize = 0;

		internal decimal TotalPrize
		{
			get
			{
				return this.totalPrize;
			}
		}
		private IEnumerable<Ticket> ticketsAliveWithoutNoAction;
		WinnerTicketNotifications winnerTicketNotifications = null;

		internal void LotteryDraw(LotteryDraw winner, bool itIsThePresent, DateTime now, string employeeName)
		{
			this.ticketsAliveWithoutNoAction = tickets.Values.Where(ticket => !ticket.IsRefunded());

			var commonLoserInfo = CommonLoserInfo(winner);
			this.totalPrize = 0m;
			
			using (SenderOfHistoricalPicks historical = new SenderOfHistoricalPicks(this.lottery, itIsThePresent))
			{
				var belongsToFireBallDraw = ticketsAliveWithoutNoAction.Any() ? ticketsAliveWithoutNoAction.First().BelongsToFireBallDraw : false;
                this.StartSendingLotteryDraw(historical, employeeName, belongsToFireBallDraw);
				foreach (Ticket ticket in ticketsAliveWithoutNoAction)
				{
					ticket.LotteryDraw(winner);
					switch (ticket.Prizing)
					{
						case GameboardStatus.LOSER:
							historical.WriteLoserData(commonLoserInfo, ticket);
							break;
						case GameboardStatus.WINNER:
							historical.WriteWinnerData(date, ticket);
							totalPrize += ticket.CalculatedPrize();

							if (winnerTicketNotifications == null) winnerTicketNotifications = new WinnerTicketLottoNotifications();
							winnerTicketNotifications.Add(itIsThePresent, now, ticket);
							break;
						default:
							throw new GameEngineException("At this point ticket must be winner or loser");
					}
				}
				historical.EndSending(lottery.State.Abbreviation, date, lottery.IdOfLottery);
			}

			if (winnerTicketNotifications != null && !winnerTicketNotifications.IsEmpty())
            {
				winnerTicketNotifications.Send(itIsThePresent, now);
			}
		}

		internal void ResendGradeToHistorical(bool itIsThePresent)
		{
			this.ticketsAliveWithoutNoAction = tickets.Values.Where(ticket => !ticket.IsRefunded());
			LotteryDraw winner = tickets.Values.First().Draw;
			var commonLoserInfo = CommonLoserInfo(winner);
			using (var historical = new SenderOfHistoricalPicks(this.lottery, itIsThePresent))
			{
                var belongsToFireBallDraw = ticketsAliveWithoutNoAction.Any() ? ticketsAliveWithoutNoAction.First().BelongsToFireBallDraw : false;
                this.StartSendingLotteryDraw(historical, winner.WhoGraded, belongsToFireBallDraw);
				foreach (Ticket ticket in ticketsAliveWithoutNoAction)
				{
					commonLoserInfo.DomainId = ticket.DomainId;
					switch (ticket.Prizing)
					{
						case GameboardStatus.LOSER:
							historical.WriteLoserData(commonLoserInfo, ticket);
							break;
						case GameboardStatus.WINNER:
							historical.WriteWinnerData(date, ticket);
							break;
						default:
							throw new GameEngineException("At this point ticket must be winner or loser");
					}
				}
				historical.EndSending(lottery.State.Abbreviation, date, lottery.IdOfLottery);
			}
		}

		internal void LotteryDrawKeno(LotteryDrawKeno winner, bool itIsThePresent, DateTime now, string employeeName)
		{
			this.ticketsAliveWithoutNoAction = tickets.Values.Where(ticket => !ticket.IsRefunded());

			var commonLoserInfo = CommonLoserKenoInfo(winner);
			this.totalPrize = 0m;

			using (var historical = new SenderOfHistoricalKeno((LotteryKeno)lottery, itIsThePresent))
			{
				this.StartSendingLotteryDraw(historical, employeeName);
				foreach (TicketKeno ticket in ticketsAliveWithoutNoAction)
				{
					ticket.LotteryDraw(winner);
					switch (ticket.Prizing)
					{
						case GameboardStatus.LOSER:
							historical.WriteLoserData(commonLoserInfo, ticket);
							break;
						case GameboardStatus.WINNER:
							historical.WriteWinnerData(date, ticket);
							totalPrize = totalPrize + ticket.CalculatedPrize();

							if (winnerTicketNotifications == null) winnerTicketNotifications = new WinnerTicketKenoNotifications();
							winnerTicketNotifications.Add(itIsThePresent, now, ticket);
							break;
						default:
							throw new GameEngineException("At this point ticket must be winner or loser");
					}
				}
				historical.EndSending(winner.Date);
			}

			if (winnerTicketNotifications != null && !winnerTicketNotifications.IsEmpty())
			{
				winnerTicketNotifications.Send(itIsThePresent, now);
			}
		}

		internal List<PayFragmentsMessage> CreateMessagesToResendGradedTickets(DateTime now)
		{
			var gradeFreeFormWagers = new List<PayFragmentsMessage>();
			foreach (var ticket in tickets.Values)
			{
				if (ticket.WasPurchasedForFree) continue;

				foreach (var wager in ticket.Wagers)
				{
					var isTheSamePrizeAndRisk = wager.AdjustedWinAmount == 0 && wager.AdjustedLossAmount == 0 && wager.IsWinner();
					var isAdjustWinAmountRequired = wager.AdjustedWinAmount != 0 && wager.IsWinner();
					var isAdjustLossAmountRequired = isTheSamePrizeAndRisk || (wager.AdjustedLossAmount != 0 && !wager.IsWinner());

					var wagerStatus = isTheSamePrizeAndRisk ? WagerStatus.L : wager.IsWinner() ? WagerStatus.W : WagerStatus.L;

					var gradeFreeFormWager = new PayFragmentsMessage()
					{
						AdjustedLossAmount = isAdjustLossAmountRequired ? wager.AdjustedLossAmount.ToString() : string.Empty,
						AdjustedWinAmount = isAdjustWinAmountRequired ? wager.AdjustedWinAmount.ToString() : string.Empty,
						DailyFigureDate_YYYYMMDD = now.ToString("yyyyMMdd"),
						IsValidTicketNumber = true,
						Outcome = wagerStatus.ToString(),
						TicketNumber = wager.TicketNumber.ToString(),
						WagerNumber = wager.WagerNumber.ToString(),
						AgentId = wager.Ticket.Player.AgentNumber
					};

					if (gradeFreeFormWager.WagerNumber != "0")//Arregla el Id 463395 de la BD pues fue una compra sin wagers
					{
						gradeFreeFormWagers.Add(gradeFreeFormWager);
					}
				}
			}
			return gradeFreeFormWagers;
		}

		internal List<PayFragmentsMessage> CreateMessagesToResendRegradedTickets(DateTime now)
		{
			var gradeFreeFormWagers = new List<PayFragmentsMessage>();
			foreach (var ticket in tickets.Values)
			{
				if (ticket.WasPurchasedForFree) continue;
				WagerStatus wagerStatus = WagerStatus.L;

				foreach (var wager in ticket.Wagers)
				{
					var isAdjustLossAmountRequired = wager.AdjustedLossAmount != 0;

					var gradeFreeFormWager = new PayFragmentsMessage()
					{
						AdjustedLossAmount = isAdjustLossAmountRequired ? wager.AdjustedLossAmount.ToString() : string.Empty,
						AdjustedWinAmount = string.Empty,
						DailyFigureDate_YYYYMMDD = now.ToString("yyyyMMdd"),
						IsValidTicketNumber = true,
						Outcome = wagerStatus.ToString(),
						TicketNumber = wager.TicketNumber.ToString(),
						WagerNumber = wager.WagerNumber.ToString(),
						AgentId = wager.Ticket.Player.AgentNumber
					};

					if (gradeFreeFormWager.WagerNumber != "0")//Arregla el Id 463395 de la BD pues fue una compra sin wagers
					{
						gradeFreeFormWagers.Add(gradeFreeFormWager);
					}
				}
			}
			return gradeFreeFormWagers;
		}

		internal List<PayFragmentsMessage> CreateMessagesToResendNoActionTickets(DateTime now)
		{
			var gradeFreeFormWagers = new List<PayFragmentsMessage>();
			foreach (var ticket in tickets.Values)
			{
				if (ticket.WasPurchasedForFree) continue;
				WagerStatus wagerStatus = WagerStatus.X;

				foreach (var wager in ticket.Wagers)
				{
					var isAdjustLossAmountRequired = wager.AdjustedLossAmount != 0 && !wager.IsWinner();

					var gradeFreeFormWager = new PayFragmentsMessage()
					{
						AdjustedLossAmount = isAdjustLossAmountRequired ? wager.AdjustedLossAmount.ToString() : string.Empty,
						AdjustedWinAmount = string.Empty,
						DailyFigureDate_YYYYMMDD = now.ToString("yyyyMMdd"),
						IsValidTicketNumber = true,
						Outcome = wagerStatus.ToString(),
						TicketNumber = wager.TicketNumber.ToString(),
						WagerNumber = wager.WagerNumber.ToString(),
						AgentId = wager.Ticket.Player.AgentNumber
					};

					if (gradeFreeFormWager.WagerNumber != "0")//Arregla el Id 463395 de la BD pues fue una compra sin wagers
					{
						gradeFreeFormWagers.Add(gradeFreeFormWager);
					}
				}
			}
			return gradeFreeFormWagers;
		}

		public class PayFragmentsMessageClone : Objeto
		{
			public string TicketNumber { get; set; }
			public string WagerNumber { get; set; }
			public string Outcome { get; set; }
			public string DailyFigureDate_YYYYMMDD { get; set; }
			public string AdjustedWinAmount { get; set; }
			public string AdjustedLossAmount { get; set; }
			public bool IsValidTicketNumber { get; set; }
		}

        [DataContract(Name = "payFragmentsMessage")]
        public class PayFragmentsMessage : Objeto
        {
            [DataMember(Name = "ticketNumber")]
            public string TicketNumber { get; set; }
            [DataMember(Name = "wagerNumber")]
            public string WagerNumber { get; set; }
            [DataMember(Name = "outcome")]
            public string Outcome { get; set; }
            [DataMember(Name = "dailyFigureDate_YYYYMMDD")]
            public string DailyFigureDate_YYYYMMDD { get; set; }
            [DataMember(Name = "adjustedWinAmount")]
            public string AdjustedWinAmount { get; set; }
            [DataMember(Name = "adjustedLossAmount")]
            public string AdjustedLossAmount { get; set; }
            [DataMember(Name = "isValidTicketNumber")]
            public bool IsValidTicketNumber { get; set; }
            [DataMember(Name = "agentId")]
            public int AgentId { get; set; }
        }

        internal void CommitGrade(bool itIsThePresent)
		{
			UpdateGradingStatusOfTheWholeCollection();
			this.ticketsAliveWithoutNoAction = null;
		}

		internal void RollbackFromGradeToPending()
		{
			if (this.ticketsAliveWithoutNoAction != null)
			{
				foreach (Ticket ticket in this.ticketsAliveWithoutNoAction)
				{
					if (ticket.IsWinner() || ticket.IsLoser())
					{
						ticket.RestorePreviousGameboardStatus();
						ticket.SetDrawAsNull();
					}
				}
			cancelRollback:
				ticketsAliveWithoutNoAction = null;
			}
		}

		private LoserInfo CommonLoserInfo(LotteryDraw winner)
		{
			var schedule = lottery.FindScheduleAt(date);
			var scheduleId = winner.IsLotteryPickWithFireBall ? schedule.FireballId : schedule.Id;
            var fireballNumber = winner.IsLotteryPickWithFireBall ? winner.FireBallNumber : Lotto.LotteryDraw.WITHOUT_FIREBALL;
            LoserInfo info = new LoserInfo(hour: date.Hour, minute: date.Minute, year: date.Year, month: date.Month, day: date.Day, draw: winner.SequenceOfNumbers, gradedBy: winner.WhoGraded, drawingId: scheduleId, position: lottery.GetTypeNumberSequence(), fireball: fireballNumber);
			info.StateAbb = winner.State.Abbreviation;
			return info;
		}

		LoserKenoInfo CommonLoserKenoInfo(LotteryDrawKeno winner)
		{
			var drawId = ((TicketKeno)tickets.Values.First()).DrawIdPrefix;
			var info = new LoserKenoInfo(hour: date.Hour, minute: date.Minute, year: date.Year, month: date.Month, day: date.Day, draw: winner.GetNumbersWithMultiplierAndBulleye(), gradedBy: winner.WhoGraded, drawingId: int.Parse(drawId));
			return info;
		}

		//status
		private class TicketsDataForRollback
		{
			private TicketData[] collection;
			private int appendIndex = 0;

			internal struct TicketData
			{
				public Type betType;
				public decimal payout;
			}

			internal TicketsDataForRollback(int size)
			{
				this.collection = new TicketData[size];
			}

			internal void RecordTypeOf(Ticket ticket)
			{
				this.collection[appendIndex].payout = ticket.Payout;
				appendIndex++;
			}

			internal TicketData this[int index]
			{
				get
				{
					return collection[index];
				}
			}

			internal int Count
			{
				get
				{
					return this.collection.Length;
				}
			}

		}

		private TicketsDataForRollback previousTicketsDataForRollback;

		internal void GradeAGraded(LotteryDraw winner, bool itIsThePresent, DateTime now, string previousSequenceOfNumbers, string employeeName, int version)
		{
			this.previousTicketsDataForRollback = new TicketsDataForRollback(tickets.Count);

			using (SenderOfHistoricalPicks historical = new SenderOfHistoricalPicks(this.lottery, itIsThePresent))
			{
				totalPrize = 0m;
                var belongsToFireBallDraw = tickets.Values.Any() ? tickets.Values.First().BelongsToFireBallDraw : false;
                this.StartSendingLotteryDraw(historical, employeeName, belongsToFireBallDraw);
				foreach (Ticket ticket in tickets.Values.Where(x => !x.IsRefunded()))
				{
					this.previousTicketsDataForRollback.RecordTypeOf(ticket);
					ticket.LotteryDraw(winner);

					if (ticket.IsWinner())
					{
						if (ticket.PreviouslyWasLoser())
						{
							if (winnerTicketNotifications == null) winnerTicketNotifications = new WinnerTicketLottoNotifications();
							winnerTicketNotifications.Add(itIsThePresent, now, ticket);
						}
						else if(ticket.PreviouslyWasWinner())
						{
						}
						else if (ticket.PreviouslyWasNoAction())
						{
						}
						else
						{
							if (winnerTicketNotifications == null) winnerTicketNotifications = new WinnerTicketLottoNotifications();
							winnerTicketNotifications.Add(itIsThePresent, now, ticket);
						}

						totalPrize += ticket.CalculatedPrize();

						historical.WriteWinnerData(date, ticket);
					}
					else if(ticket.IsLoser())
					{
						historical.WriteLoserData(date, ticket);
					}
				}
				historical.EndSending(lottery.State.Abbreviation, date, lottery.IdOfLottery);
			}
		}

		internal void ResendGradeAGradedToHisttorical(bool itIsThePresent)
		{
			LotteryDraw winner = tickets.Values.First().Draw;
			using (var historical = new SenderOfHistoricalPicks(this.lottery, itIsThePresent))
			{
                var belongsToFireBallDraw = tickets.Values.Any() ? tickets.Values.First().BelongsToFireBallDraw : false;
                this.StartSendingLotteryDraw(historical, winner.WhoGraded, belongsToFireBallDraw);
				foreach (Ticket ticket in tickets.Values.Where(x => !x.IsRefunded()))
				{
					if (ticket.IsWinner())
					{
						historical.WriteWinnerData(date, ticket);
					}
					else if (ticket.IsLoser())
					{
						historical.WriteLoserData(date, ticket);
					}
				}
				historical.EndSending(lottery.State.Abbreviation, date, lottery.IdOfLottery);
			}
		}

		internal void GradeAGradedKeno(LotteryDrawKeno winner, bool itIsThePresent, DateTime now, string employeeName)
		{
			this.previousTicketsDataForRollback = new TicketsDataForRollback(tickets.Count);

			using (var historical = new SenderOfHistoricalKeno((LotteryKeno)lottery, itIsThePresent))
			{
				totalPrize = 0m;
				this.StartSendingLotteryDraw(historical, employeeName);
				foreach (Ticket ticket in tickets.Values.Where(x => !x.IsRefunded()))
				{
					this.previousTicketsDataForRollback.RecordTypeOf(ticket);

					ticket.LotteryDraw(winner);

					if (ticket.IsWinner())
					{
						if (ticket.PreviouslyWasLoser())
						{
							if (winnerTicketNotifications == null) winnerTicketNotifications = new WinnerTicketKenoNotifications();
							winnerTicketNotifications.Add(itIsThePresent, now, ticket);
						}
						else
						{
							if (winnerTicketNotifications == null) winnerTicketNotifications = new WinnerTicketKenoNotifications();
							winnerTicketNotifications.Add(itIsThePresent, now, ticket);
						}
						totalPrize = totalPrize + ticket.CalculatedPrize();
						historical.WriteWinnerData(date, ticket);
					}
					else if (ticket.IsLoser())
					{
						historical.WriteLoserData(date, ticket);
					}
				}
				historical.EndSending(date);
			}
		}

		internal void CancelWinnerNotifications(bool itIsThePresent)
        {
			winnerTicketNotifications?.RemoveAll(itIsThePresent);
		}

		internal void CommitRegrade(bool itIsThePresent)
		{
			UpdateGradingStatusOfTheWholeCollection();
			this.previousTicketsDataForRollback = null;
		}

		internal void RollbackFromGradeToPending(LotteryDraw winner, DateTime now, string previousSequenceOfNumbers)
		{
			if (this.previousTicketsDataForRollback != null)
			{
				if (winner.SequenceOfNumbers != previousSequenceOfNumbers)
				{
					var wasRegraded = previousSequenceOfNumbers[0] == '-';
					if (wasRegraded)
					{
						winner.UpdateToRegrade(winner.WhoRegraded, now);
					}
					else
					{
						winner.Update(previousSequenceOfNumbers, winner.WhoGraded, now);
					}
				}
				for (int index = 0; index < tickets.Count; index++)
				{
					Ticket ticket = tickets[index];
					if (ticket.IsWinner() || ticket.IsLoser())
					{
						ticket.RestorePreviousGameboardStatus();
						ticket.RevertPayout(previousTicketsDataForRollback[index].payout);
					}
				}

				this.previousTicketsDataForRollback = null;
			}
		}

		internal void Regrade(LotteryDraw winner, bool itIsThePresent, DateTime now, string previousSequenceOfNumbers, string employeeName, int version)
		{
			this.previousTicketsDataForRollback = new TicketsDataForRollback(tickets.Count);

			using (SenderOfHistoricalPicks historical = new SenderOfHistoricalPicks(this.lottery, itIsThePresent))
			{
				this.totalPrize = 0m;
                var belongsToFireBallDraw = tickets.Values.Any() ? tickets.Values.First().BelongsToFireBallDraw : false;
                this.StartSendingDrawingWithoutResult(historical, employeeName, belongsToFireBallDraw);
				foreach (Ticket ticket in tickets.Values.Where(x=>! x.IsRefunded()))
				{
					this.previousTicketsDataForRollback.RecordTypeOf(ticket);

					ticket.RegradeAsLoser(winner);

					if (ticket.PreviouslyWasWinner())
					{
						winnerTicketNotifications?.Remove(itIsThePresent, ticket);
						historical.WriteLoserData(date, ticket);
					}
				}
				historical.EndSending(lottery.State.Abbreviation, date, lottery.IdOfLottery);
			}
		}

		internal void ResendRegradeAsLosersToHistorical(bool itIsThePresent)
		{
			LotteryDraw winner = tickets.Values.First().Draw;
			using (var historical = new SenderOfHistoricalPicks(this.lottery, itIsThePresent))
			{
                var belongsToFireBallDraw = tickets.Values.Any() ? tickets.Values.First().BelongsToFireBallDraw : false;
                this.StartSendingDrawingWithoutResult(historical, winner.WhoRegraded, belongsToFireBallDraw);
				foreach (Ticket ticket in tickets.Values.Where(x => !x.IsRefunded()))
				{
					if (ticket.PreviouslyWasWinner())
					{
						historical.WriteLoserData(date, ticket);
					}
				}
				historical.EndSending(lottery.State.Abbreviation, date, lottery.IdOfLottery);
			}
		}

		internal void RegradeKeno(LotteryDrawKeno winner, bool itIsThePresent, DateTime now, string previousSequenceOfNumbers, string employeeName, int version)
		{
			this.previousTicketsDataForRollback = new TicketsDataForRollback(tickets.Count);

			using (var historical = new SenderOfHistoricalKeno((LotteryKeno)lottery, itIsThePresent))
			{
				this.totalPrize = 0m;
				this.StartSendingDrawingWithoutResult(historical, employeeName);
				foreach (Ticket ticket in tickets.Values.Where(x => !x.IsRefunded()))
				{
					this.previousTicketsDataForRollback.RecordTypeOf(ticket);

					ticket.RegradeAsLoser(winner);

					if (ticket.PreviouslyWasWinner())
					{
						winnerTicketNotifications?.Remove(itIsThePresent, ticket);
						historical.WriteLoserData(date, ticket);
					}
				}
				historical.EndSending(date);
			}
		}

		internal void RollbackFromRegradeToPending(LotteryDraw winner, DateTime now, string previousSequenceOfNumbers)
		{
			if (this.previousTicketsDataForRollback != null)
			{
				if (winner.SequenceOfNumbers != previousSequenceOfNumbers)
				{
					winner.Update(previousSequenceOfNumbers, winner.WhoGraded, now);
				}

				for (int index = 0; index < tickets.Count; index++)
				{
					var ticket = tickets[index];
					if (ticket.IsWinner() || ticket.IsLoser())
					{
						ticket.RestorePreviousGameboardStatus();
						ticket.RevertPayout(previousTicketsDataForRollback[index].payout);
					}
				}

				this.previousTicketsDataForRollback = null;
			}
		}

		internal void SetNoAction(bool itIsThePresent, string employeeName, DateTime now, LotteryNoAction lotteryNoAction)
		{
			if (string.IsNullOrWhiteSpace(employeeName)) throw new ArgumentNullException(nameof(employeeName));

			using (SenderOfHistoricalPicks historical = new SenderOfHistoricalPicks(this.lottery, itIsThePresent))
			{
				 var status = this.GeneralGradeStatus(); //TODO xxxx
				var gameboardStatus = status.GameboardStatus;
				if (status.AllHaveTheSameGradeStatus && gameboardStatus.IsGraded())
				{
					CancelGrade(historical, itIsThePresent, employeeName, now, lotteryNoAction);
				}
				else if (status.AllHaveTheSameGradeStatus && (gameboardStatus.IsPending() || gameboardStatus.IsRegraded()))
				{
                    var belongsToFireBallDraw = tickets.Values.Any() ? tickets.Values.First().BelongsToFireBallDraw : false;
                    this.StartSendingDrawingWithoutResult(historical, employeeName, belongsToFireBallDraw);

					var ticketsWithoutNoAction = TicketsWithoutNoAction();
					foreach (Ticket ticket in ticketsWithoutNoAction)
					{
						ticket.NoActionDraw = lotteryNoAction;
						ticket.ChangeToNoAction();
						historical.WriteNoActionData(date, ticket);
					}
					historical.EndSending(lottery.State.Abbreviation, date, lottery.IdOfLottery);
				}
				
				UpdateGradingStatusOfTheWholeCollection();
			}
		}

		internal void SetNoActionKeno(bool itIsThePresent, string employeeName, DateTime now, LotteryNoAction lotteryNoAction)
		{
			if (string.IsNullOrWhiteSpace(employeeName)) throw new ArgumentNullException(nameof(employeeName));

			using (var historical = new SenderOfHistoricalKeno((LotteryKeno)lottery, itIsThePresent))
			{
				var status = this.GeneralGradeStatus(); //TODO xxxx
				var gameboardStatus = status.GameboardStatus;
				if (status.AllHaveTheSameGradeStatus && gameboardStatus.IsGraded())
				{
					StartSendingDrawingWithoutResult(historical, employeeName);
					CancelGrade(historical, itIsThePresent, employeeName, now, lotteryNoAction);
				}
				else if (status.AllHaveTheSameGradeStatus && (gameboardStatus.IsPending() || gameboardStatus.IsRegraded()))
				{
					this.StartSendingDrawingWithoutResult(historical, employeeName);

					var ticketsWithoutNoAction = TicketsWithoutNoAction();
					foreach (Ticket ticket in ticketsWithoutNoAction)
					{
						ticket.NoActionDraw = lotteryNoAction;
						ticket.ChangeToNoAction();
						historical.WriteNoActionData(date, ticket);
					}
					historical.EndSending(date);
				}

				UpdateGradingStatusOfTheWholeCollection();
			}
		}

		private void CancelGrade(SenderOfHistoricalPicks historical, bool itIsThePresent, string employeeName, DateTime now, LotteryNoAction lotteryNoAction)
		{
			var status = this.GeneralGradeStatus(); //TODO xxxx
			if (status.AllHaveTheSameGradeStatus && ! status.GameboardStatus.IsGraded()) throw new GameEngineException("Draw was not graded, it can not be rolled back");
            var belongsToFireBallDraw = tickets.Values.Any() ? tickets.Values.First().BelongsToFireBallDraw : false;
            this.StartSendingDrawingWithoutResult(historical, employeeName, belongsToFireBallDraw);

			bool wasWinner = false;
			decimal previousPrize = 0m;
			foreach (Ticket ticket in tickets.Values.Where(x => !x.IsRefunded()))
			{
				wasWinner = ticket.IsWinner();
				previousPrize = ticket.WonAmount();
				ticket.ChangeToNoAction();
				ticket.NoActionDraw = lotteryNoAction;
				if (wasWinner)
				{
					winnerTicketNotifications?.Remove(itIsThePresent, ticket);
				}
				historical.WriteNoActionData(date, ticket);
			}
			UpdateGradingStatusOfTheWholeCollection();
			historical.EndSending(lottery.State.Abbreviation, date, lottery.IdOfLottery);
		}
		void CancelGrade(SenderOfHistoricalKeno historical, bool itIsThePresent, string employeeName, DateTime now, LotteryNoAction lotteryNoAction)
		{
			var status = this.GeneralGradeStatus(); //TODO xxxx
			if (status.AllHaveTheSameGradeStatus && !status.GameboardStatus.IsGraded()) throw new GameEngineException("Draw was not graded, it can not be rolled back");

			this.StartSendingDrawingWithoutResult(historical, employeeName);

			bool wasWinner = false;
			decimal previousPrize = 0m;
			foreach (Ticket ticket in tickets.Values.Where(x => !x.IsRefunded()))
			{
				wasWinner = ticket.IsWinner();
				previousPrize = ticket.WonAmount();
				ticket.ChangeToNoAction();
				ticket.NoActionDraw = lotteryNoAction;
				if (wasWinner)
				{
					winnerTicketNotifications?.Remove(itIsThePresent, ticket);
				}
				historical.WriteNoActionData(date, ticket);
			}
			UpdateGradingStatusOfTheWholeCollection();
			historical.EndSending(date);
		}

		internal void ResendNoActionToHistorical(bool itIsThePresent)
		{
			LotteryNoAction lotteryNoAction = tickets.Values.First().NoActionDraw;
			using (var historical = new SenderOfHistoricalPicks(this.lottery, itIsThePresent))
			{
                var belongsToFireBallDraw = tickets.Values.Any() ? tickets.Values.First().BelongsToFireBallDraw : false;
                this.StartSendingDrawingWithoutResult(historical, lotteryNoAction.WhoSetNoAction, belongsToFireBallDraw);

				foreach (Ticket ticket in tickets.Values.Where(x => x.IsRefunded()))
				{
					historical.WriteNoActionData(date, ticket);
				}
				historical.EndSending(lottery.State.Abbreviation, date, lottery.IdOfLottery);
			}
		}

		internal void SendToHistoricalLateGradedTickets(bool itIsThePresent, string employeeName)
		{
			if (itIsThePresent)
			{
				using (SenderOfHistoricalPicks historical = new SenderOfHistoricalPicks(this.lottery, itIsThePresent))
				{
                    var belongsToFireBallDraw = tickets.Values.Any() ? tickets.Values.First().BelongsToFireBallDraw : false;
                    this.StartSendingLotteryDraw(historical, employeeName, belongsToFireBallDraw);

					foreach (Ticket ticket in tickets.Values.Where(x => !x.IsRefunded()))
					{
						if (!ticket.IsMarkedAsSent)
						{
							switch (ticket.Grading)
							{
								case GameboardStatus.GRADED:
								case GameboardStatus.REGRADED:
									switch (ticket.Prizing)
									{
										case GameboardStatus.LOSER:
											historical.WriteLoserData(date, ticket);
											break;
										case GameboardStatus.WINNER:
											historical.WriteWinnerData(date, ticket);
											break;
										default:
											throw new GameEngineException("At this point ticket must be winner or loser");
									}
									break;
								default:
									throw new GameEngineException($"At this point ticket must be {nameof(GameboardStatus.GRADED)} or {nameof(GameboardStatus.REGRADED)}");
							}
						}
					}
					historical.EndSending(lottery.State.Abbreviation, date, lottery.IdOfLottery);
				}
			}
		}

		internal void SendToHistoricalLateNoActionTickets(bool itIsThePresent, string employeeName)
		{
			if (itIsThePresent)
			{
				using (SenderOfHistoricalPicks historical = new SenderOfHistoricalPicks(this.lottery, itIsThePresent))
				{
                    var belongsToFireBallDraw = tickets.Values.Any() ? tickets.Values.First().BelongsToFireBallDraw : false;
                    this.StartSendingDrawingWithoutResult(historical, employeeName, belongsToFireBallDraw);

					foreach (Ticket ticket in tickets.Values)
					{
						if (!ticket.IsMarkedAsSent)
						{
							switch (ticket.Grading)
							{
								case GameboardStatus.NOACTION:
									historical.WriteNoActionData(date, ticket);
									break;
								default:
									throw new GameEngineException($"At this point ticket must be {nameof(GameboardStatus.NOACTION)}");
							}
						}
					}
					historical.EndSending(lottery.State.Abbreviation, date, lottery.IdOfLottery);
				}
			}
		}

		private void StartSendingDrawingWithoutResult(SenderOfHistoricalPicks historical, string gradedBy, bool belongToFireball)
		{
			bool hasDrawingResult = false;
			StartSending(historical, gradedBy, hasDrawingResult, belongToFireball);
		}

		void StartSendingDrawingWithoutResult(SenderOfHistoricalKeno historical, string gradedBy)
		{
			bool hasDrawingResult = false;
			StartSending(historical, gradedBy, hasDrawingResult);
		}

		private void StartSendingLotteryDraw(SenderOfHistoricalPicks historical, string gradedBy, bool belongToFireball)
		{
			bool hasDrawingResult = true;
            StartSending(historical, gradedBy, hasDrawingResult, belongToFireball);
		}

		void StartSendingLotteryDraw(SenderOfHistoricalKeno historical, string gradedBy)
		{
			bool hasDrawingResult = true;
			StartSending(historical, gradedBy, hasDrawingResult);
		}

		private void StartSending(SenderOfHistoricalPicks historical, string gradedBy, bool hasDrawingResult, bool belongToFireball)
		{
			string sequenceOfNumbers;
			int fireball = Lotto.LotteryDraw.WITHOUT_FIREBALL;
            IdOfLottery idOfLottery = this.lottery.IdOfLottery;
            var schedule = (WeeklySchedule)this.lottery.FindScheduleAt(date);
			int scheduleId = schedule.Id;
            int uniqueId = schedule.UniqueId;
            string scheduleDescription = schedule.GetDescription();
            if (hasDrawingResult)
			{
                var lotteryDraw = lottery.RootLottery.LotteryDraws.GetLotteryDraw(date);
                sequenceOfNumbers = lotteryDraw.SequenceOfNumbers;
				if (belongToFireball)
				{
					fireball = lotteryDraw.FireBallNumber;
					scheduleId = schedule.FireballId;
					uniqueId = schedule.UniqueId;
                    scheduleDescription = schedule.FireballDescription();
                }
            }
			else
			{
				sequenceOfNumbers = this.lottery.DefaultValueForEmptyWinnerNumber();
			}

			var domains = tickets.Values.Where(x => !x.IsRefunded()).Select(x => x.Order.Domain).Distinct();
			historical.WriteGeneralTicketData(uniqueId, scheduleId, date, sequenceOfNumbers, fireball, scheduleDescription, idOfLottery, lottery.GetTypeNumberSequence(), domains);
			historical.StartSending(lottery.State.Abbreviation, date, idOfLottery, scheduleId, gradedBy, sequenceOfNumbers, fireball);
		}

		void StartSending(SenderOfHistoricalKeno historical, string gradedBy, bool hasDrawingResult)
		{
			string sequenceOfNumbers;
			if (hasDrawingResult)
			{
				var lotteryDraw = this.lottery.LotteryDraws.GetLotteryDraw(date);
				sequenceOfNumbers = lotteryDraw.SequenceOfNumbers;
			}
			else
			{
				sequenceOfNumbers = this.lottery.DefaultValueForEmptyWinnerNumber();
			}

			var schedule = this.lottery.FindScheduleAt(date);
			var domains = tickets.Values.Where(ticket => !ticket.IsRefunded()).Select(ticket => ticket.Order.Domain).Distinct();
			var idPrefix = ((TicketKeno)tickets.Values.First()).DrawIdPrefix;
			var drawId = int.Parse(idPrefix);
			historical.WriteGeneralTicketData(drawId, date, sequenceOfNumbers, schedule.GetDescription(), domains);
			historical.StartSending(date, drawId, gradedBy, sequenceOfNumbers);
		}

		internal void DeleteWinnersAndLosers()
		{
			if (WinnersAndLosersAreDeleted) throw new GameEngineException("Winners were already deleted");
			WinnersAndLosersAreDeleted = true;
			var toDelete = tickets.Values.Where(
					ticket => (ticket.IsGraded() || ticket.IsNoAction())
			);
			foreach (Ticket ticket in toDelete)
			{
				ticket.Player.Remove(ticket);
				ticket.Dispose();
			}
			UpdateGradingStatusOfTheWholeCollection();
		}

		internal void DeleteNoAction()
		{
			if (NoActionAreDeleted) throw new GameEngineException("Losers were already deleted");
			NoActionAreDeleted = true;
			var toDelete = tickets.Values.Where(
					ticket => ticket.IsNoAction()
			);
			foreach (Ticket ticket in toDelete)
			{
				ticket.Player.Remove(ticket);
				ticket.Dispose();
			}
			UpdateGradingStatusOfTheWholeCollection();
		}

		internal bool IsDeleted
		{
			get
			{
				return WinnersAndLosersAreDeleted || NoActionAreDeleted;
			}
		}

		internal bool Contains(Ticket ticket)
		{
			if (ticket == null) throw new ArgumentNullException(nameof(ticket));

			var result = tickets.Values.Contains(ticket);
			return result;
		}

		internal GradeStatusOfCollection GeneralGradeStatus()
		{
			return gradeStatusOfCollection;
		}

		internal bool IsOnlyOneGradedTicketWithoutNumber()
		{
			var result = tickets.Values.Count(x => x.TicketNumber == Ticket.DEFAULT_UNASSIGNED_TICKET_NUMBER && (x.IsGraded() || x.IsRegraded())) == 1;
			return result;
		}

		internal bool IsOnlyOneNoActionTicketWithoutNumber()
		{
			var result = tickets.Values.Count(x => x.TicketNumber == Ticket.DEFAULT_UNASSIGNED_TICKET_NUMBER && x.IsNoAction()) == 1;
			return result;
		}

		internal bool AnyFakeTicketNumber()
		{
			foreach (var ticket in tickets.Values)
			{
				if (ticket.TicketNumber == Ticket.FAKE_TICKET_NUMBER) return true;
			}
			return false;
		}

		internal int Count
		{
			get
			{
				return tickets.Count;
			}
		}

		internal IEnumerable<Ticket> SearchTicketByNumber(int ticketNumber)
		{
			var ticketsToRemove = tickets.Values.Where(x => x.TicketNumber == ticketNumber);
			return ticketsToRemove;
		}

        internal IEnumerable<Ticket> FindTicketsMatchingWith(IEnumerable<int> ticketNumbers)
        {
            return ticketNumbers.Where(tickets.ContainsKey).Select(ticketNumber => tickets[ticketNumber]);
        }

        internal Ticket FindTicketMatchingWith(int ticketNumber)
        {
            tickets.TryGetValue(ticketNumber, out var ticket);
            return ticket;
        }

        internal IEnumerable<Ticket> FindTicketsInRange(int theLowestBetId, int theHighestBetId)
		{
			var ticketsFound = tickets.Values.Where(x => x.BetNumber() <= theHighestBetId && x.BetNumber() >= theLowestBetId);
			return ticketsFound;
		}

		internal TicketWager WagerToRemoveFor(int ticketNumber, int wagerNumber)
		{
			var wagerToRemove = tickets.Values.Where(x => x.TicketNumber == ticketNumber).
				SelectMany(x => x.Wagers).
				SingleOrDefault(x => x.WagerNumber == wagerNumber);

			return wagerToRemove;
		}

		internal IEnumerable<Domain> DomainsWhereBelongThisSet()
        {
			var result = tickets.Values.Select(ticket => ticket.Order.Domain);
			return result;
        }
    }
}
