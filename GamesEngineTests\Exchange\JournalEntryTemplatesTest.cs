﻿using GamesEngine.Accounting.JournalTemplates;
using GamesEngine.Business;
using GamesEngine.Custodian;
using GamesEngine.Domains;
using GamesEngine.Exchange;
using GamesEngine.Exchange.Batch;
using GamesEngine.Finance;
using GamesEngine.PurchaseOrders;
using GamesEngine.Settings;
using GamesEngineTests.Custodian;
using GamesEngineTests.Unit_Tests.Games.Lotto;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using System;
using System.Linq;
using System.Reflection;
using town.connectors.drivers;
using Unit.Games.Tools;
using static GamesEngine.Business.WholePaymentProcessor;
using static GamesEngine.Exchange.TransactionCompleted;

namespace GamesEngineTests.Exchange
{
	[TestClass]
	public class JournalEntryTemplatesTest
	{
		[TestInitialize]
		public void Initialize()
		{
			typeof(WholePaymentProcessor).GetField("_wholePaymentProcessor", System.Reflection.BindingFlags.NonPublic | BindingFlags.Static).SetValue(null, null);
		}

		[TestMethod]
		public void Define_Different_Templates_Test()
		{
			Company company = new Company();
			company.Sales.CreateStore(1, "Test Store").MakeCurrent();
			Marketplace marketplace = new Marketplace(company, "CR");

			JournalEntryTemplates templates = marketplace.JournalEntryTemplates;

			int aNewTemplateId = 999;
			JournalEntryTemplate templateForDeposits = templates.CreateTemplate(aNewTemplateId, "Template for deposits");
			templateForDeposits = templates[aNewTemplateId];

			//Fase de Definicion: Parametros que van a necesitar las plantillas
			templateForDeposits.Parameters.AddTextParameter("pick");
			templateForDeposits.Parameters.AddTextParameter("state");
			templateForDeposits.Parameters.AddTextParameter("amount");
			templateForDeposits.Parameters.AddTextParameter("referencia");
			templateForDeposits.Parameters.AddAmountParameter("debito100-01");
			templateForDeposits.Parameters.AddAmountParameter("credito100-02");

			//Fase de Definicion: Lineas de la plantilla, deben haberse definido los parametros primero
			templateForDeposits.AddDebit("100-01", "Purchase {pick} on {state}", "debito100-01");
			templateForDeposits.AddCredit("200-01", "Receive {amount} because selling a ticket", "credito100-02");

			templateForDeposits.AddDebit("100-02", "{pick} on {state}", "debito100-01");
			templateForDeposits.AddCredit("200-02", "Receive because selling a ticket", "credito100-02");

			templateForDeposits.AddDebit("100-03", "{pick}", "debito100-01");
			templateForDeposits.AddCredit("200-03", "{pick}{state}", "credito100-02");

			//Fase de Uso: Solo para que sea facil 
			Argument pick = templateForDeposits.Parameters["pick"].Argument;
			Argument state = templateForDeposits.Parameters["state"].Argument;
			Argument amount = templateForDeposits.Parameters["amount"].Argument;
			Argument reference = templateForDeposits.Parameters["referencia"].Argument;
			Argument montoDebe = templateForDeposits.Parameters["debito100-01"].Argument;
			Argument montoHaber = templateForDeposits.Parameters["credito100-02"].Argument;

			//Fase de Uso: A cada paramtero se le setean todos los valores probablemente en un ciclo seteandolos para cada transaccion
			string journalEntryId = "AD-0001";
			string ticketNumber = "12345678";
			pick.SetValue("pick3");
			state.SetValue("TX");
			amount.SetValue("123.45");
			reference.SetValue(ticketNumber);
			montoDebe.SetValue(100);
			montoHaber.SetValue(100);

			JournalEntry journalEntry = templateForDeposits.Calculate(journalEntryId, DateTime.Now, "referencia", "Exchange USD to EUR");
            //GamesEngine.Custodian.PaymentMethodsActions.ResetInstance();
		}

		[TestMethod]
		public void EnqueueJournalEntryForDeposit()
		{
			Company company = new Company();
			CurrenciesTest.AddCurrencies();

			Movements.Storage = new MovementStorageMemory();
			//Integration.UseKafka = true;
			QueueInMemory queue = new QueueInMemory();
			Integration.Kafka = queue;
			Integration.SavePreviousSettings(tempUseKafka: true, tempUseKafkaForAuto: false);

			bool itIsThePresent = true;
			DateTime today = DateTime.Now;

			company.Sales.CreateStore(1, "Test Store").MakeCurrent();
			string ced="abc123";string accountNumber = "B15461";Customer customer=company.GetOrCreateCustomerById(accountNumber);customer.Identifier=ced;
			CustomerAccount dollarAccount = customer.CreateNewAccountFor(Coinage.Coin(Currencies.CODES.USD));
			Marketplace marketplace = new Marketplace(company, "CR");
			var a = (GamesEngine.Exchange.Agent)marketplace.AddAgent("A");
			var b = a.AddCashier("Juan");
			marketplace.RegisterNewAccount(1, dollarAccount);
			string employeeName = "Juan";
			string concept = "concept";
			Domain domain = company.Sales.CreateDomain(false, 1, "localhost", PaymentChannels.Agents.INSIDER);
			company.Sales.CurrentStore.Add(domain);
			GuardianTest.LoadProcessors(company);

			var outcomes = marketplace.TransactionOutcomes();
			outcomes.CreatesOutcomesFor(domain);

			#region rate #1
			RegularConversionSpread rate1 = marketplace.CreateConversionSpread(1, today, itIsThePresent, Coinage.Coin(Currencies.CODES.USD), Coinage.Coin(Currencies.CODES.BTC), 9600.01m, 9600.00m, employeeName);
			Assert.AreEqual(rate1, marketplace.ConversionSpreadAt(today, Coinage.Coin(Currencies.CODES.USD), Coinage.Coin(Currencies.CODES.BTC)));
			#endregion

			BatchTransactions juanTransactions = BatchTest.SetInitialSettings(marketplace, today, new Domain[] { domain });
			BatchSet agentBatch = juanTransactions.Batch();
			string agentPath = a.FullName;

			int id = marketplace.NewTransationNumber();
			var expectedResult = new Dollar(10m);

			var transactionType = marketplace.Company.System.TransactionTypes.SearchByName<ProcessorTransaction>(TransactionType.Deposit.ToString());
			int authorizationNumber = PaymentChannels.Deposit(
				itIsThePresent,
				Coinage.Coin(expectedResult.CurrencyCode),
				domain,
				transactionType,
				new DespositBody(
					accountNumber, 
					ced,
					expectedResult.Value,
					"deposit test",
					today, 
					"", 
					"cris", 
					domain)
				).AuthorizationId;


			var transaction1 = marketplace.From(id, dollarAccount, agentPath, "Juan", domain).Deposit(today, itIsThePresent, expectedResult, employeeName, string.Empty, string.Empty, string.Empty, concept, (PaymentProcessor)marketplace.Company.System.PaymentProcessor.First(), 1);

			var journalEntryNumber = marketplace.NewJournalEntryNumber();
			TransactionCompleted result1 = transaction1.Approve(today, itIsThePresent, authorizationNumber, employeeName, journalEntryNumber);
			Assert.IsFalse(marketplace.ExistsDraftTransaction(juanTransactions, id));

			TransactionResult resultStats1 = result1.Result;
			Assert.AreEqual(new Dollar(0m).Value, resultStats1.Comission.Value);
			Assert.AreEqual(expectedResult.Value, resultStats1.Gross.Value);
			Assert.AreEqual(expectedResult.Value, resultStats1.Net.Value);
			Assert.AreEqual(expectedResult.Value, resultStats1.Amount.Value);

			var count = queue.Count($"{KafkaMessage.TRANSACTION_EXCHANGE_CONSUMER_PREFIX}_{queue.TopicForTransacctions}");
			Assert.AreEqual(3, count);
			string msg1 = queue.Dequeue($"{KafkaMessage.TRANSACTION_EXCHANGE_CONSUMER_PREFIX}_{queue.TopicForTransacctions}").ToString();
			string msg2 = queue.Dequeue($"{KafkaMessage.TRANSACTION_EXCHANGE_CONSUMER_PREFIX}_{queue.TopicForTransacctions}").ToString();
			string msg3 = queue.Dequeue($"{KafkaMessage.TRANSACTION_EXCHANGE_CONSUMER_PREFIX}_{queue.TopicForTransacctions}").ToString();

			var message = new JournalEntryForDepositMessage(msg2);
			Assert.AreEqual(id, message.TransactionId);
			Assert.AreEqual(today, message.ApprovalDate);
			Assert.AreEqual(expectedResult.CurrencyCode, message.CurrencyCode);
			Assert.AreEqual(expectedResult.Value, message.TotalAmount);
           // GamesEngine.Custodian.PaymentMethodsActionsResetInstance();
		}

		[TestMethod]
		public void EnqueueJournalEntryForWithdraw()
		{
			Company company = new Company();
			CurrenciesTest.AddCurrencies();

			Movements.Storage = new MovementStorageMemory();
			//Integration.UseKafka = true;
			QueueInMemory queue = new QueueInMemory();
			Integration.Kafka = queue;
			Integration.SavePreviousSettings(tempUseKafka: true, tempUseKafkaForAuto: false);

			bool itIsThePresent = true;
			DateTime today = DateTime.Now;

			company.Sales.CreateStore(1, "Test Store").MakeCurrent();
			string ced="abc123";string accountNumber = "B15461";Customer customer=company.GetOrCreateCustomerById(accountNumber);customer.Identifier=ced;
			CustomerAccount dollarAccount = customer.CreateNewAccountFor(Coinage.Coin(Currencies.CODES.USD));
			Marketplace marketplace = new Marketplace(company, "CR");
			var a = (GamesEngine.Exchange.Agent)marketplace.AddAgent("A");
			var b = a.AddCashier("Juan");
			marketplace.RegisterNewAccount(1, dollarAccount);
			string employeeName = "Juan";
			string concept = "concept";
			Domain domain = company.Sales.CreateDomain(false, 1, "localhost", PaymentChannels.Agents.INSIDER);
			company.Sales.CurrentStore.Add(domain);
			GuardianTest.LoadProcessors(company);

			var outcomes = marketplace.TransactionOutcomes();
			outcomes.CreatesOutcomesFor(domain);

			#region rate #1
			RegularConversionSpread rate1 = marketplace.CreateConversionSpread(1, today, itIsThePresent, Coinage.Coin(Currencies.CODES.USD), Coinage.Coin(Currencies.CODES.BTC), 9600.01m, 9600.00m, employeeName);
			Assert.AreEqual(rate1, marketplace.ConversionSpreadAt(today, Coinage.Coin(Currencies.CODES.USD), Coinage.Coin(Currencies.CODES.BTC)));
			#endregion

			BatchTransactions juanTransactions = BatchTest.SetInitialSettings(marketplace, today, new Domain[] { domain });
			BatchSet agentBatch = juanTransactions.Batch();
			string agentPath = a.FullName;

			int id = marketplace.NewTransationNumber();
			var expectedResult = new Dollar(10m);
			var processor = marketplace.Company.System.PaymentProcessor.SearchWithdrawalProcessorBy("USD");
			var transaction1 = marketplace.From(id, dollarAccount, agentPath, "Juan", domain).Withdraw(today, itIsThePresent, expectedResult, 1, employeeName, "1-11", concept, new NoFeeUSD(), processor, 1);

			var journalEntryNumber = marketplace.NewJournalEntryNumber();
			TransactionCompleted result1 = transaction1.Approve(today, itIsThePresent, employeeName, journalEntryNumber);
			Assert.IsFalse(marketplace.ExistsDraftTransaction(juanTransactions, id));

			TransactionResult resultStats1 = result1.Result;
			Assert.AreEqual(new Dollar(0m).Value, resultStats1.Comission.Value);
			Assert.AreEqual(expectedResult.Value, resultStats1.Gross.Value);
			Assert.AreEqual(expectedResult.Value, resultStats1.Net.Value);
			Assert.AreEqual(expectedResult.Value, resultStats1.Amount.Value);

			var count = queue.Count($"{KafkaMessage.TRANSACTION_EXCHANGE_CONSUMER_PREFIX}_{queue.TopicForTransacctions}");
			Assert.AreEqual(3, count);
			string msg1 = queue.Dequeue($"{KafkaMessage.TRANSACTION_EXCHANGE_CONSUMER_PREFIX}_{queue.TopicForTransacctions}").ToString();
			string msg2 = queue.Dequeue($"{KafkaMessage.TRANSACTION_EXCHANGE_CONSUMER_PREFIX}_{queue.TopicForTransacctions}").ToString();
			string msg3 = queue.Dequeue($"{KafkaMessage.TRANSACTION_EXCHANGE_CONSUMER_PREFIX}_{queue.TopicForTransacctions}").ToString();

			var message = new JournalEntryForWithdrawalMessage(msg2);
			Assert.AreEqual(id, message.TransactionId);
			Assert.AreEqual(today, message.ApprovalDate);
			Assert.AreEqual(expectedResult.CurrencyCode, message.CurrencyCode);
			Assert.AreEqual(expectedResult.Value, message.TotalAmount);
			Integration.RestoreToDefaultSettings();
            //GamesEngine.Custodian.PaymentMethodsActions.ResetInstance();
		}

		[TestMethod]
		public void EnqueueJournalEntryForTransfer()
		{
			Company company = new Company();
			CurrenciesTest.AddCurrencies();

			Movements.Storage = new MovementStorageMemory();
			//Integration.UseKafka = true;
			QueueInMemory queue = new QueueInMemory();
			Integration.Kafka = queue;
			Integration.SavePreviousSettings(tempUseKafka: true, tempUseKafkaForAuto: false);

			bool itIsThePresent = true;
			DateTime today = DateTime.Now;
			company.Sales.CreateStore(1, "Test Store").MakeCurrent();
			string ced="abc123";string accountNumber = "B15461";Customer customer=company.GetOrCreateCustomerById(accountNumber);customer.Identifier=ced;
			string ced2="abc1234";string accountNumber2 = "B15462";Customer toCustomer=company.GetOrCreateCustomerById(accountNumber2);toCustomer.Identifier=ced2;
			CustomerAccount dollarAccount = customer.CreateNewAccountFor(Coinage.Coin(Currencies.CODES.USD));
			CustomerAccount dollarAccount2 = toCustomer.CreateNewAccountFor(Coinage.Coin(Currencies.CODES.USD));
			Marketplace marketplace = new Marketplace(company, "CR");
			var a = (GamesEngine.Exchange.Agent)marketplace.AddAgent("A");
			var b = a.AddCashier("Juan");
			marketplace.RegisterNewAccount(1, dollarAccount);
			marketplace.RegisterNewAccount(2, dollarAccount2);
			string employeeName = "Juan";
			string concept = "concept";
			Domain domain = company.Sales.CreateDomain(false, 1, "localhost", PaymentChannels.Agents.INSIDER);
			company.Sales.CurrentStore.Add(domain);
			GuardianTest.LoadProcessors(company);

			#region rate #1
			RegularConversionSpread rate1 = marketplace.CreateConversionSpread(1, today, itIsThePresent, Coinage.Coin(Currencies.CODES.USD), Coinage.Coin(Currencies.CODES.BTC), 9600.01m, 9600.00m, employeeName);
			Assert.AreEqual(rate1, marketplace.ConversionSpreadAt(today, Coinage.Coin(Currencies.CODES.USD), Coinage.Coin(Currencies.CODES.BTC)));
			#endregion

			BatchTransactions juanTransactions = BatchTest.SetInitialSettings(marketplace, today, new Domain[] { domain });
			BatchSet agentBatch = juanTransactions.Batch();
			string agentPath = a.FullName;

			int id = marketplace.NewTransationNumber();
			var expectedResult = new Dollar(10m);
			var transaction1 = marketplace.From(id, dollarAccount, agentPath, "Juan", domain).TransferTo(today, itIsThePresent, expectedResult, 1, dollarAccount2, employeeName, "1-11", concept, (PaymentProcessor)marketplace.Company.System.PaymentProcessor.First(), 1);
			var journalEntryNumber = marketplace.NewJournalEntryNumber();
			TransactionCompleted result1 = transaction1.Approve(today, itIsThePresent, employeeName, journalEntryNumber);
			Assert.IsFalse(marketplace.ExistsDraftTransaction(juanTransactions, id));

			TransactionResult resultStats1 = result1.Result;
			Assert.AreEqual(new Dollar(0m).Value, resultStats1.Comission.Value);
			Assert.AreEqual(expectedResult.Value, resultStats1.Gross.Value);
			Assert.AreEqual(expectedResult.Value, resultStats1.Net.Value);
			Assert.AreEqual(expectedResult.Value, resultStats1.Amount.Value);

			var count = queue.Count($"{KafkaMessage.TRANSACTION_EXCHANGE_CONSUMER_PREFIX}_{queue.TopicForTransacctions}");
			Assert.AreEqual(3, count);
			string msg1 = queue.Dequeue($"{KafkaMessage.TRANSACTION_EXCHANGE_CONSUMER_PREFIX}_{queue.TopicForTransacctions}").ToString();
			string msg2 = queue.Dequeue($"{KafkaMessage.TRANSACTION_EXCHANGE_CONSUMER_PREFIX}_{queue.TopicForTransacctions}").ToString();
			string msg3 = queue.Dequeue($"{KafkaMessage.TRANSACTION_EXCHANGE_CONSUMER_PREFIX}_{queue.TopicForTransacctions}").ToString();

			var message = new JournalEntryForTransferMessage(msg2);
			Assert.AreEqual(id, message.TransactionId);
			Assert.AreEqual(today, message.ApprovalDate);
			Assert.AreEqual(expectedResult.CurrencyCode, message.CurrencyCode.Iso4217Code);
			Assert.AreEqual(expectedResult.Value, message.TotalAmount);
			Integration.RestoreToDefaultSettings();
            //GamesEngine.Custodian.PaymentMethodsActions.ResetInstance();
		}

		[TestMethod]
		public void EnqueueJournalEntryForCreditNote()
		{
			Company company = new Company();
			CurrenciesTest.AddCurrencies();

			Movements.Storage = new MovementStorageMemory();
			//Integration.UseKafka = true;
			QueueInMemory queue = new QueueInMemory();
			Integration.Kafka = queue;
			Integration.SavePreviousSettings(tempUseKafka: true, tempUseKafkaForAuto: false);

			bool itIsThePresent = true;
			DateTime today = DateTime.Now;
			
			company.Sales.CreateStore(1, "Test Store").MakeCurrent();
			string ced="abc123";string accountNumber = "B15461";Customer customer=company.GetOrCreateCustomerById(accountNumber);customer.Identifier=ced;
			CustomerAccount dollarAccount = customer.CreateNewAccountFor(Coinage.Coin(Currencies.CODES.USD));
			Marketplace marketplace = new Marketplace(company, "CR");
			var a = (GamesEngine.Exchange.Agent)marketplace.AddAgent("A");
			var b = a.AddCashier("Juan");
			marketplace.RegisterNewAccount(1, dollarAccount);
			string employeeName = "Juan";
			string concept = "concept";
			int referenceId = 1;
			int batchNumber = 1;
			Domain domain = company.Sales.CreateDomain(false, 1, "localhost", PaymentChannels.Agents.INSIDER);
			company.Sales.CurrentStore.Add(domain);
			GuardianTest.LoadProcessors(company);

			#region rate #1
			RegularConversionSpread rate1 = marketplace.CreateConversionSpread(1, today, itIsThePresent, Coinage.Coin(Currencies.CODES.USD), Coinage.Coin(Currencies.CODES.BTC), 9600.01m, 9600.00m, employeeName);
			Assert.AreEqual(rate1, marketplace.ConversionSpreadAt(today, Coinage.Coin(Currencies.CODES.USD), Coinage.Coin(Currencies.CODES.BTC)));
			#endregion

			BatchTransactions juanTransactions = BatchTest.SetInitialSettings(marketplace, today, new Domain[] { domain });
			BatchSet agentBatch = juanTransactions.Batch();
			string agentPath = a.FullName;

			int id = marketplace.NewTransationNumber();
			var expectedResult = new Dollar(10m);
			var transaction1 = marketplace.From(id, dollarAccount, agentPath, "Juan", domain).CreditNote(today, itIsThePresent, expectedResult, employeeName, concept, referenceId, batchNumber, (PaymentProcessor)marketplace.Company.System.PaymentProcessor.First(), 1);
			var journalEntryNumber = marketplace.NewJournalEntryNumber();
			TransactionCompleted result1 = transaction1.Approve(today, itIsThePresent, employeeName, journalEntryNumber);
			Assert.IsFalse(marketplace.ExistsDraftTransaction(juanTransactions, id));

			TransactionResult resultStats1 = result1.Result;
			Assert.AreEqual(new Dollar(0m).Value, resultStats1.Comission.Value);
			Assert.AreEqual(expectedResult.Value, resultStats1.Gross.Value);
			Assert.AreEqual(expectedResult.Value, resultStats1.Net.Value);
			Assert.AreEqual(expectedResult.Value, resultStats1.Amount.Value);

			var count = queue.Count($"{KafkaMessage.TRANSACTION_EXCHANGE_CONSUMER_PREFIX}_{queue.TopicForTransacctions}");
			Assert.AreEqual(3, count);
			string msg1 = queue.Dequeue($"{KafkaMessage.TRANSACTION_EXCHANGE_CONSUMER_PREFIX}_{queue.TopicForTransacctions}").ToString();
			string msg2 = queue.Dequeue($"{KafkaMessage.TRANSACTION_EXCHANGE_CONSUMER_PREFIX}_{queue.TopicForTransacctions}").ToString();
			string msg3 = queue.Dequeue($"{KafkaMessage.TRANSACTION_EXCHANGE_CONSUMER_PREFIX}_{queue.TopicForTransacctions}").ToString();

			var message = new JournalEntryForCreditNoteMessage(msg2);
			Assert.AreEqual(id, message.TransactionId);
			Assert.AreEqual(today, message.ApprovalDate);
			Assert.AreEqual(expectedResult.CurrencyCode, message.CurrencyCode);
			Assert.AreEqual(expectedResult.Value, message.TotalAmount);
			Integration.RestoreToDefaultSettings();
            //GamesEngine.Custodian.PaymentMethodsActions.ResetInstance();
		}

		[TestMethod]
		public void EnqueueJournalEntryForDebitNote()
		{
			Company company = new Company();
			CurrenciesTest.AddCurrencies();

			Movements.Storage = new MovementStorageMemory();
			//Integration.UseKafka = true;
			QueueInMemory queue = new QueueInMemory();
			Integration.Kafka = queue;
			Integration.SavePreviousSettings(tempUseKafka: true, tempUseKafkaForAuto: false);

			bool itIsThePresent = true;
			DateTime today = DateTime.Now;

			company.Sales.CreateStore(1, "Test Store").MakeCurrent();
			string ced="abc123";string accountNumber = "B15461";Customer customer=company.GetOrCreateCustomerById(accountNumber);customer.Identifier=ced;
			CustomerAccount dollarAccount = customer.CreateNewAccountFor(Coinage.Coin(Currencies.CODES.USD));
			Marketplace marketplace = new Marketplace(company, "CR");
			var a = (GamesEngine.Exchange.Agent)marketplace.AddAgent("A");
			var b = a.AddCashier("Juan");
			marketplace.RegisterNewAccount(1, dollarAccount);
			string employeeName = "Juan";
			string concept = "concept";
			int referenceId = 1;
			int batchNumber = 1;
			Domain domain = company.Sales.CreateDomain(false, 1, "localhost", PaymentChannels.Agents.INSIDER);
			company.Sales.CurrentStore.Add(domain);
			GuardianTest.LoadProcessors(company);

			#region rate #1
			RegularConversionSpread rate1 = marketplace.CreateConversionSpread(1, today, itIsThePresent, Coinage.Coin(Currencies.CODES.USD), Coinage.Coin(Currencies.CODES.BTC), 9600.01m, 9600.00m, employeeName);
			Assert.AreEqual(rate1, marketplace.ConversionSpreadAt(today, Coinage.Coin(Currencies.CODES.USD), Coinage.Coin(Currencies.CODES.BTC)));
			#endregion

			BatchTransactions juanTransactions = BatchTest.SetInitialSettings(marketplace, today, new Domain[] { domain });
			BatchSet agentBatch = juanTransactions.Batch();
			string agentPath = a.FullName;

			int id = marketplace.NewTransationNumber();
			var expectedResult = new Dollar(10m);
			var transaction1 = marketplace.From(id, dollarAccount, agentPath, "Juan", domain).DebitNote(today, itIsThePresent, expectedResult, employeeName, concept, referenceId, batchNumber, (PaymentProcessor)marketplace.Company.System.PaymentProcessor.First(), 1);
			var journalEntryNumber = marketplace.NewJournalEntryNumber();
			TransactionCompleted result1 = transaction1.Approve(today, itIsThePresent, employeeName, journalEntryNumber);
			Assert.IsFalse(marketplace.ExistsDraftTransaction(juanTransactions, id));

			TransactionResult resultStats1 = result1.Result;
			Assert.AreEqual(new Dollar(0m).Value, resultStats1.Comission.Value);
			Assert.AreEqual(expectedResult.Value, resultStats1.Gross.Value);
			Assert.AreEqual(expectedResult.Value, resultStats1.Net.Value);
			Assert.AreEqual(expectedResult.Value, resultStats1.Amount.Value);

			var count = queue.Count($"{KafkaMessage.TRANSACTION_EXCHANGE_CONSUMER_PREFIX}_{queue.TopicForTransacctions}");
			Assert.AreEqual(3, count);
			string msg1 = queue.Dequeue($"{KafkaMessage.TRANSACTION_EXCHANGE_CONSUMER_PREFIX}_{queue.TopicForTransacctions}").ToString();
			string msg2 = queue.Dequeue($"{KafkaMessage.TRANSACTION_EXCHANGE_CONSUMER_PREFIX}_{queue.TopicForTransacctions}").ToString();
			string msg3 = queue.Dequeue($"{KafkaMessage.TRANSACTION_EXCHANGE_CONSUMER_PREFIX}_{queue.TopicForTransacctions}").ToString();

			var message = new JournalEntryForDebitNoteMessage(msg2);
			Assert.AreEqual(id, message.TransactionId);
			Assert.AreEqual(today, message.ApprovalDate);
			Assert.AreEqual(expectedResult.CurrencyCode, message.CurrencyCode);
			Assert.AreEqual(expectedResult.Value, message.TotalAmount);
			Integration.RestoreToDefaultSettings();
            //GamesEngine.Custodian.PaymentMethodsActions.ResetInstance();
		}
	}
}
