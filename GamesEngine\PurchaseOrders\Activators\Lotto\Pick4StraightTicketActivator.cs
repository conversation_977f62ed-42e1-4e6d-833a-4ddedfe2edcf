﻿using GamesEngine.Bets;
using GamesEngine.Business;
using GamesEngine.Games.Lotto;
using GamesEngine.Gameboards.Lotto;
using Puppeteer.EventSourcing;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Text;
using static GamesEngine.Gameboards.Lotto.Ticket;
using System.Linq;

namespace GamesEngine.PurchaseOrders.Activators.Lotto
{
    [Puppet]
    internal class Pick4StraightTicketActivator : TicketPickActivator
    {
        private string[] number = new string[4];

        internal override string this[int i]
        {
            get
            {
                if (i < 1 || i > 4) throw new GameEngineException($"{nameof(Pick4StraightTicketActivator)} does not have {i} number");
                if (String.IsNullOrWhiteSpace(number[i - 1])) throw new GameEngineException($"Number{i} has not been set on {nameof(Pick4StraightTicketActivator)}");
                if (SelectionMode != "balls") throw new GameEngineException("Numbers is only a valid property for balls selection.");
                return number[i - 1];
            }
            set
            {
                if (String.IsNullOrWhiteSpace(value)) throw new ArgumentNullException($"Number{i}");
                if (i < 1 || i > 4) throw new GameEngineException($"{nameof(Pick4StraightTicketActivator)} does not have {i} number");
                number[i - 1] = value;
            }

        }

        internal string Number1 { get { return this[1]; } set { this[1] = value; } }
        internal string Number2 { get { return this[2]; } set { this[2] = value; } }
        internal string Number3 { get { return this[3]; } set { this[3] = value; } }
        internal string Number4 { get { return this[4]; } set { this[4] = value; } }

        internal Pick4StraightTicketActivator(PicksLotteryGame lotteryGame) : base(lotteryGame)
        {
        }

		internal override int Activate(bool itIsThePresent, DateTime now, OrderCompleted order)
		{
			LotteryPick<Pick4> lottery = (LotteryPick<Pick4>)base.PicksLotteryGame.GetLottery(4, State);
			TicketPick<Pick4> ticket;
            int itemNumber = 0;
            var ticketNumber = order.GetAuthorizationId(IndexInOrder);
            Prizes prizes = order.HasSeller ? order.Agent.CurrentPrizes : base.PicksLotteryGame.Prizes;
            switch (SelectionMode)
            {
                case "balls":
                    ticket = lottery.StraightTicket(Player, DrawDate, WithFireBall, this[1], this[2], this[3], this[4], Selection.BALLS, now, TicketCost, ticketNumber, prizes);
                    break;
                case "SingleInputMultipleAmount":
                    ticket = lottery.StraightTicket(Player, DrawDate, WithFireBall, Numbers, Selection.SingleInputMultipleAmount, now, TicketCost, ticketNumber, prizes);
                    break;
                case "MultipleInputSingleAmount":
                    ticket = lottery.StraightTicket(Player, DrawDate, WithFireBall, Numbers, Selection.MultipleInputSingleAmount, now, TicketCost, ticketNumber, prizes);
                    break;
                default:
                    throw new GameEngineException("Selection mode is invalid.");
            }
            ticket.ExcludeGroupOfNumbers(ExcludedNumbers);

            if (!order.IsMultipleAuthorization)
            {
                BetAlive bet;
                if (order.NextBetNumber != 0)
                {
                    bet = (BetAlive)Company.CreateNewBet(Player, ticket, lottery.Pool, now, order.NextBetNumber);
                    order.NextBetNumber++;
                }
                else
                {
                    bet = (BetAlive)Company.CreateNewBet(Player, ticket, lottery.Pool, now);
                }

                bet.Contribute(TicketCost);
                Company.Lock(bet);
                itemNumber = bet.Number;
                ticket.AssignBetNumber(bet.Number);
            }
            else
            {
                itemNumber = order.NextBetNumber;
                ticket.AssignBetNumber(order.NextBetNumber);
                order.NextBetNumber++;
            }

            ticket.Order = order;
            if (SelectionMode.Equals("MultipleInputSingleAmount") && Numbers.Length == 1) ticket.GenerateWagers(TicketCost);
            else ticket.GenerateWagers();

            base.Gameboard = ticket;

            order.ActiveGameboards++;
            var risk = base.PicksLotteryGame.RiskProfiles.GetRiskProfile(order.Domain).Risks.Risk;
            risk.GetRiskPerLottery(4, lottery).AddToWin(DrawDate, ticket, now);

            Player.SaveLastPurchase(now);
            Player.Add(ticket);
            return itemNumber;
        }
    }
}
