﻿using GamesEngine;
using GamesEngine.Finance;
using GamesEngine.Marketing.Campaigns;
using Puppeteer.EventSourcing;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Text;
using town.connectors.drivers;
using static GamesEngine.Finance.PaymentChannels;

namespace GamesEngineMocks
{
    public class LoyaltyMockGenerator : Mock
    {
        Actor actor;
        public const string TOKEN = "w38932dkbdewkjdh";

        public String Perform(String script)
        {
			string result;
			try
			{
#pragma warning disable VSTHRD002 // Avoid problematic synchronous waits
				result = actor.PerformCmdAsync(script, IpAddress.DEFAULT, UserInLog.ANONYMOUS).Result;
#pragma warning restore VSTHRD002 // Avoid problematic synchronous waits
				result = CheckForAssert(result);
			}
			catch (Exception e)
			{
				throw e.InnerException;
			}
            return result;
        }
        public LoyaltyMockGenerator(Actor actor)
        {
			this.actor = actor;
			Perform($@"
                company = Company();
                companyIDf2401f84xa94ax4db3xaec4x83f5361730ae = company;
                if (!company.System.Coins.ExistsIsoCode('FP'))
				{{
					coin = company.System.Coins.Add(0, 'FP', 'FP', 2, 'F', 'free play', {CoinType.Digital});
					coin.Visible = true;
					coin.Enabled = true;
				}}
				if (!company.System.Coins.ExistsIsoCode('USD'))
				{{
					coin = company.System.Coins.Add(2, 'USD', '$', 2, '$', 'dollar', {CoinType.Fiat});
					coin.Visible = true;
					coin.Enabled = true;
				}}
				if (!company.System.Coins.ExistsIsoCode('BTC'))
				{{
					company.System.Coins.Add(3, 'BTC', 'BTC', 8, '₿', 'bitcoin', {CoinType.Crypt});
				}}
				if (!company.System.Coins.ExistsIsoCode('ETH'))
				{{
					company.System.Coins.Add(7, 'ETH', 'ETH', 8, 'Ξ', 'ethereum', {CoinType.Crypt});
				}}

				marketplace = Marketplace(company, 'CR');
				cartagoAgent = marketplace.AddAgent('Cartago');
				agent1 = marketplace.AddAgent('1');
				marketplace.AddAgent('San José');
				marketplace.AddAgent('Heredia');

				store = company.Sales.CreateStore(1,'Picks Store');
                Eval('suggestionBoxFeedbackCampaign'+store.Id+' = SuggestionBox(company, store);');
                Eval('announcementsCampaign'+store.Id+' = Announcements(company, store);');

                store = company.Sales.CreateStore(2,'Brackets Store 2019');
                Eval('suggestionBoxFeedbackCampaign'+store.Id+' = SuggestionBox(company, store);');
                Eval('announcementsCampaign'+store.Id+' = Announcements(company, store);');

                store = company.Sales.CreateStore(3,'Brackets Store 2020');
                Eval('suggestionBoxFeedbackCampaign'+store.Id+' = SuggestionBox(company, store);');
                Eval('announcementsCampaign'+store.Id+' = Announcements(company, store);');

                store = company.Sales.CreateStore(4,'Fiero Wallet');
                Eval('suggestionBoxFeedbackCampaign'+store.Id+' = SuggestionBox(company, store);');
                Eval('announcementsCampaign'+store.Id+' = Announcements(company, store);');

                store = company.Sales.CreateStore(5,'Ladybet Store');
                Eval('suggestionBoxFeedbackCampaign'+store.Id+' = SuggestionBox(company, store);');
                Eval('announcementsCampaign'+store.Id+' = Announcements(company, store);');

                store = company.Sales.CreateStore(6,'Brackets Store 2021');
                Eval('suggestionBoxFeedbackCampaign'+store.Id+' = SuggestionBox(company, store);');
                Eval('announcementsCampaign'+store.Id+' = Announcements(company, store);');

                store = company.Sales.CreateStore(7,'Keno Store');
                Eval('suggestionBoxFeedbackCampaign'+store.Id+' = SuggestionBox(company, store);');
                Eval('announcementsCampaign'+store.Id+' = Announcements(company, store);');

                store = company.Sales.StoreById(5);
                homeFirstBillboard = company.Billboards.CreateBillboard(store, 'lines.home.first', 80, 440);
                homeSecondBillboard = company.Billboards.CreateBillboard(store, 'lines.home.second', 90, 440);
                homeThirdBillboard = company.Billboards.CreateBillboard(store, 'lines.home.third', 80, 440);
                homeFirstBillboard.Description = 'Ladybet Home Page First Billboard at the top';
                homeSecondBillboard.Description = 'Ladybet Home Page Second Billboard at the middle';
                homeThirdBillboard.Description = 'Ladybet Home Page Third Billboard at the bottom';

                store = company.Sales.StoreById(1);
                homeFirstBillboard = company.Billboards.CreateBillboard(store, 'lotto.home.first', 80, 440);
                homeSecondBillboard = company.Billboards.CreateBillboard(store, 'lotto.home.second', 90, 440);
                homeThirdBillboard = company.Billboards.CreateBillboard(store, 'lotto.home.third', 80, 440);
                homeFirstBillboard.Description = 'Lotto Home Page First Billboard at the top';
                homeSecondBillboard.Description = 'Lotto Home Page Second Billboard at the middle';
                homeThirdBillboard.Description = 'Lotto Home Page Third Billboard at the bottom';

				customSettings = CustomSettingsCollection(company);
				{{
					company.System.Entities.Add(1, 'Apolo');
					artemisEntity = company.System.Entities.Add(2, 'Artemis');
					artemisEntity.Visible = true;
					artemisEntity.Enabled = true;
					zeusEntity = company.System.Entities.Add(3, 'Zeus');
					fieroEntity = company.System.Entities.Add(4, 'Fiero');
					fieroEntity.Visible = true;
					fieroEntity.Enabled = true;
					hadesEntity = company.System.Entities.Add(5, 'Hades');
					hadesEntity.Visible = true;
					hadesEntity.Enabled = true;
					consignmentEntity = company.System.Entities.Add(6, 'Consignment');
					consignmentEntity.Visible = true;
					consignmentEntity.Enabled = true;

					company.System.Tenants.Add(1, 'Apolo');
					artemisTenant = company.System.Tenants.Add(2, 'Artemis');
					zeusTenant = company.System.Tenants.Add(3, 'Zeus');
					insiderTenant = company.System.Tenants.Add(4, 'Insider');
					hadesTenant = company.System.Tenants.Add(5, 'Hades');
					hadesTenant.MakeCurrent();

					asiPM = company.System.PaymentMethods.Add(1, 'A.S.I.');
					dgsPM = company.System.PaymentMethods.Add(2, 'D.G.S.');
					dgsV2PM = company.System.PaymentMethods.Add(3, 'D.G.S. v2');
					credirCardPM = company.System.PaymentMethods.Add(4, 'Credit card');
					blockChainPM = company.System.PaymentMethods.Add(5, 'Blockchain');
					moneyTransferPM = company.System.PaymentMethods.Add(6, 'Money transfer');
					asiSimulatorPM = company.System.PaymentMethods.Add(7, 'A.S.I. Simulator');
					dsgSimulatorPM = company.System.PaymentMethods.Add(8, 'D.G.S. Simulator');
					pm = company.System.PaymentMethods.Add(9, '{PaymentMethod.Cash.ToString()}');
					pm.Visible = true;
					pm.Enabled = true;
					pm = company.System.PaymentMethods.Add(10, '{PaymentMethod.Bank.ToString()}');
					pm.Visible = true;
					pm.Enabled = true;
					pm = company.System.PaymentMethods.Add(11, '{PaymentMethod.Creditcard.ToString()}');
					pm.Visible = true;
					pm.Enabled = true;
					pm = company.System.PaymentMethods.Add(12, '{PaymentMethod.FinancialServices.ToString()}');
					pm.Visible = true;
					pm.Enabled = true;
					pm = company.System.PaymentMethods.Add(13, '{PaymentMethod.ThirdParty.ToString()}');
					pm.Visible = true;
					pm.Enabled = true;
					pm = company.System.PaymentMethods.Add(14, '{PaymentMethod.Secrets.ToString()}');
					pm.Visible = true;
					pm.Enabled = true;
					pm = company.System.PaymentMethods.Add(15, '{PaymentMethod.P2PTransfer.ToString()}');
					pm.Visible = true;
					pm.Enabled = true;
					pm.AddProvider(1,'MoneyGram');
					pm.AddProvider(2,'Rio Money Transfer');
					pm.AddProvider(3,'Sigue');
					pm.AddProvider(4,'Boss Revolution Money Transfer');
					pm.AddProvider(5,'Delgado Travel');
					pm.AddProvider(6,'Intermex International Money Transfer');
					pm.AddProvider(7,'Maxi Money Services');
					pm.AddProvider(8,'Barri Financial Group');
					pm.AddProvider(9,'La Nacional');
					pm.AddProvider(10,'Dinex');
					pm.AddProvider(11,'Remitly');

					depositTransactionType = company.System.TransactionTypes.Add(1, '{TransactionType.Deposit.ToString()}');
					depositTransactionType.Description = 'A sum of money placed in an account';
					withDrawalTransactionType = company.System.TransactionTypes.Add(2, '{TransactionType.Withdrawal.ToString()}');
					withDrawalTransactionType.Description = 'A sum of money take out of an account';
					transferTransactionType = company.System.TransactionTypes.Add(3, '{TransactionType.Transfer.ToString()}');
					transferTransactionType.Description = 'A sum of money is sent from one account to another';
					creditNoteTransactionType = company.System.TransactionTypes.Add(4, '{TransactionType.CreditNote.ToString()}');
					creditNoteTransactionType.Description = 'It is a document used by a vendor to inform the buyer of mistake on an order';
					debitNoteTransactionType = company.System.TransactionTypes.Add(5, '{TransactionType.DebitNote.ToString()}');
					debitNoteTransactionType.Description = 'It is a document used by a vendor to inform the buyer of current debt obligations';
					saleTransactionType = company.System.TransactionTypes.Add(6, '{TransactionType.Sale.ToString()}');
					saleTransactionType.Description = 'The exchange of a commodity for money';
					depositAndLockTransactionType = company.System.TransactionTypes.Add(7, '{TransactionType.Deposit.ToString()} then Lock');
					depositAndLockTransactionType.Description = 'Add and Lock money placed in an account';

					depositTransactionType.Visible = true;
					depositTransactionType.Enabled = true;
					withDrawalTransactionType.Visible = true;
					withDrawalTransactionType.Enabled = true;
					transferTransactionType.Visible = true;
					transferTransactionType.Enabled = true;
					debitNoteTransactionType.Visible = true;
					debitNoteTransactionType.Enabled = true;
					creditNoteTransactionType.Visible = true;
					creditNoteTransactionType.Enabled = true;
					saleTransactionType.Visible = true;
					saleTransactionType.Enabled = true;
					depositAndLockTransactionType.Visible = true;
					depositAndLockTransactionType.Enabled = true;

					cs = customSettings.AddFixedParameter(Now, 'CashierDriver.url', 'http://cashierapi:5000/'); 
					cs.Description = 'Url Cashier';
					cs = customSettings.AddFixedParameter(Now, 'CompanyBaseUrlServices', 'http://cashierapi:5000/'); 
					cs.Description = 'It is the base production url';
					cs = customSettings.AddFixedParameter(Now, 'TokenSystemId', '3aab83fb'); 
					cs.Description = 'App\'s token id';
					cs = customSettings.AddSecretParameter(Now, 'TokenSystemPassword', '38f845e977af7f2cd6313c5a9d35e4de7f353405100e7748ab15b410eb53d886');
					cs.Description = 'Password for token';
					cs = customSettings.AddFixedParameter(Now, 'SendWagers', true); 
					cs.Description = 'To send wagers to third party';
					cs = customSettings.AddFixedParameter(Now, 'CompanySystemId', 'N/A'); 
					cs.Description = 'company id';
					cs = customSettings.AddSecretParameter(Now, 'CompanySystemPassword', 'N/A'); 
					cs.Description = 'password';
					cs = customSettings.AddFixedParameter(Now, 'NeedsUniqueIdentifierForPaymentHub', 'PRODUCTION_DOES_NOT_NEED_IT'); 
					cs.Description = 'Setting';
					cs = customSettings.AddFixedParameter(Now, 'CompanyClerkId', 'Administrator'); 
					cs.Description = 'Clerk id';

					cs = customSettings.AddVariableParameter('Input$Provider'); 
					cs.Description = 'Consignment provider';
					cs = customSettings.AddVariableParameter('KYC$DOB'); 
					cs.Description = 'Birthdate from KYC';
					cs = customSettings.AddSecretParameter(Now, 'KYCUsername', 'testUser1');
					cs.Description = 'KYC Username for fields';
					cs = customSettings.AddSecretParameter(Now, 'KYCPassword', '12345');
					cs.Description = 'KYC Password for fields';
				}}
                ");
        }

		int amountOfUsers = 0;
		public LoyaltyMockGenerator CreatePlayers(Dictionary<string, string> accountsById)
        {
            string linesToCreatePlayer = "";
            
            foreach (var accountById in accountsById)
            {
                amountOfUsers++;
                string accountNumber = accountById.Key;
                string nameOfVariable = accountById.Value;
                string defaultNickNameIfNotExists = "TesterUser " + amountOfUsers;
                string defaultAvatarIfNotExists = "https://intranet.ncubo.com/ResourcesQA5/api/file/IDDefault/Lotto/Lotto/avatars/avatar2.png";
                linesToCreatePlayer += $@"
                    custNO{accountNumber} = company.GetOrCreateCustomerById('NO{accountNumber}');
                    player = custNO{accountNumber}.Player;
                    Eval ('playerNO' + {accountNumber} + ' = player;');
                    player.NickName = '{defaultNickNameIfNotExists}';
                    player.UpdateAvatar('{defaultAvatarIfNotExists}', Now);
                    player.ApproveAvatar(Now);
                ";
            }
            var result = Perform(linesToCreatePlayer);
            return this;
        }

		public LoyaltyMockGenerator CreatePlayer(string accountNumber, Agents agent)
		{
			amountOfUsers += 1;
			string defaultNickNameIfNotExists = "TesterUser " + amountOfUsers;
			string defaultAvatarIfNotExists = "https://intranet.ncubo.com/ResourcesQA5/api/file/IDDefault/Lotto/Lotto/avatars/avatar2.png";
			string linesToCreatePlayer = $@"
                cust{accountNumber} = company.CreateCustomer('{accountNumber}',{agent});
                cust{accountNumber}.Name = 'Tester User {amountOfUsers}';
				cust{accountNumber}.Identifier='10{amountOfUsers}';
                player = cust{accountNumber}.Player;
				player.NickName = '{defaultNickNameIfNotExists}';
                player.UpdateAvatar('{defaultAvatarIfNotExists}', Now);
                player.ApproveAvatar(Now);
            ";
			var result = Perform(linesToCreatePlayer.ToString());

			return this;
		}

		public LoyaltyMockGenerator UpdatePlayer(string accountNumber, string nickname, string avatarPath)
		{
			var result = Perform($@"
                    cust{accountNumber} = company.GetOrCreateCustomerById('{accountNumber}');
                    player = cust{accountNumber}.Player;
                    player.NickName = '{nickname}';
                    player.UpdateAvatar('{avatarPath}', Now);
                    player.ApproveAvatar(Now);
                ");
			return this;
		}

		public LoyaltyMockGenerator CreateThumbsUpFeedback(string suggestionVariable, string playerAccount, string message)
		{
			SuggestionBox suggestion = (SuggestionBox)this.actor.Value(suggestionVariable);
			StringBuilder script = new StringBuilder();
			script.Append($"cust{playerAccount} = company.GetOrCreateCustomerById('{playerAccount}');");
			script.Append($"player = cust{playerAccount}.Player;");
			script.Append($"player.SendPositiveFeedback(itIsThePresent, {suggestionVariable}, '{message}', Now);");

			Perform(script.ToString());
			return this;
		}

		public LoyaltyMockGenerator CreateThumbsDownFeedback(string suggestionVariable, string playerAccount, string message, string deviceName, string browserName)
		{
			SuggestionBox suggestion = (SuggestionBox)this.actor.Value(suggestionVariable);
			StringBuilder script = new StringBuilder();
			script.Append($"cust{playerAccount} = company.GetOrCreateCustomerById('{playerAccount}');");
			script.Append($"player = cust{playerAccount}.Player;");
			script.Append($"player.SendNegativeFeedback(itIsThePresent, {suggestionVariable}, '{message}', Now, '{deviceName}', '{browserName}');");

			Perform(script.ToString());
			return this;
		}

		//SendAnnouncementTo(bool itIsThePresent, Announcement campaign, string criteria, DateTime now, string employeeName)
		public LoyaltyMockGenerator CreateNewLottoAnnouncementTo(string subject, string message, string criteria, string employeeName)
		{
			StringBuilder script = new StringBuilder();
			script.Append($"announcement = lotto900.CreateAnnouncementCampaign(itIsThePresent, company, Now, '{subject}', '{message}');");
			criteria = Validator.StringEscape(criteria);
			script.Append($"company.SendAnnouncementTo(itIsThePresent, announcement, '{criteria}', Now, '{employeeName}');");
			Perform(script.ToString());
			return this;
		}

		public LoyaltyMockGenerator CreateNewTournamentAnnouncementTo(int year, string subject, string message, string criteria, string employeeName)
		{
			StringBuilder script = new StringBuilder();
			script.Append($"tournament = company.Tournaments.GetMarchMadnessTournamentOf({year});");
			script.Append($"announcement = tournament.CreateAnnouncementCampaign(itIsThePresent, company, Now, '{subject}', '{message}');");
			criteria = Validator.StringEscape(criteria);
			script.Append($"company.SendAnnouncementTo(itIsThePresent, announcement, '{criteria}', Now, '{employeeName}');");
			Perform(script.ToString());
			return this;
		}

		public LoyaltyMockGenerator CreateGiftCardCampaign(string campaignName, string currencyCode, int budget, Dictionary<string, string> resources)
		{
			var names = string.Join(',', resources.Keys.Select(x=>$"'{x}'"));
			var urls = string.Join(',', resources.Values.Select(x => $"'{x}'"));
			var result = Perform($@"
				Eval('campaignId =' + company.Campaigns.NextCampaignConsecutive() + ';');
				campaign = company.Campaigns.CreateGiftCardCampaign(campaignId, Now, '{campaignName}', '{currencyCode}', {budget});
				campaign.AddResources({{{names}}}, {{{urls}}});
				campaign.ChoosePreferredResource('{resources.Keys.First()}');
				campaign.Description = 'test campaign description';
                ");
			return this;
		}

		public LoyaltyMockGenerator CreateGiftCardCampaignUnlimited(string campaignName, string currencyCode)
		{
			var result = Perform($@"
				Eval('campaignId =' + company.Campaigns.NextCampaignConsecutive() + ';');
				campaign = company.Campaigns.CreateGiftCardCampaign(campaignId, Now, '{campaignName}', '{currencyCode}');
				campaign.Description = 'test campaign description';
                ");
			return this;
		}

		public LoyaltyMockGenerator CreateStreakCampaignMock(DateTime creationDate, string name, string currencyCode, DateTime startDateCampaign, DateTime endDateCampaign, int maxLevels, string description, DateTime lastDayToChangePrizes, DateTime expectedStartDate, string rewardTypeAsText, bool isEnable)
		{
			var result = Perform($@"
				{{
					creationDate = {creationDate.ToString("M/d/yyyy H:m:s")};
					startDateCampaign = {startDateCampaign.ToString("M/d/yyyy H:m:s")};
					endDateCampaign = {endDateCampaign.ToString("M/d/yyyy H:m:s")};
					lastDayToChangePrizes = {lastDayToChangePrizes.ToString("M/d/yyyy H:m:s")};
					expectedStartDate = {expectedStartDate.ToString("M/d/yyyy H:m:s")};
					domain = company.Sales.CreateDomain(false, 1, 'localhost', {Agents.INSIDER});

					Eval('campaignId =' + company.Campaigns.NextCampaignConsecutive() + ';');
					streakCampaign = company.Campaigns.CreateStreakCampaign(itIsThePresent, campaignId, Now, '{name}', '{currencyCode}', startDateCampaign, endDateCampaign, {maxLevels}, '{description}', lastDayToChangePrizes, expectedStartDate, '{rewardTypeAsText}', {isEnable});

					streakCampaign.ModifySpinWheel({{'$0.25','$0.25','$0.50','$0.50','$1','$1','$2','$5'}}, {{0.25,0.25,0.50,0.50,1.0,1.0,2.0,5.0}},'+$5',0,1);
					streakCampaign.ModifySpinWheel({{'$0.50','$0.50','$0.75','$0.75','$2','$2','$5','$10'}}, {{0.50,0.50,0.75,0.75,2.0,2.0,5.0,10.0}},'+$10',1,5);
					streakCampaign.ModifySpinWheel({{'$0.50','$0.50','$1','$1','$2','$2','$10','$20'}}, {{0.50,0.50,1.0,1.0,2.0,2.0,10.0,20.0}},'+$20',2,5);
					streakCampaign.ModifySpinWheel({{'$1','$1','$2','$2','$5','$5','$10','$20'}}, {{1.0,1.0,2.0,2.0,5.0,5.0,10.0,20.0}},'+$20',3,5);
					streakCampaign.ModifySpinWheel({{'$1','$1','$2','$2','$5','$5','$15','$30'}}, {{1.0,1.0,2.0,2.0,5.0,5.0,15.0,30.0}},'+$30',4,5);
					streakCampaign.ModifySpinWheel({{'$2','$2','$5','$5','$10','$10','$15','$30'}}, {{2.0,2.0,5.0,5.0,10.0,10.0,15.0,30.0}},'+$30',5,5);
					streakCampaign.ModifySpinWheel({{'$4','$4','$8','$8','$10','$10','$15','$30'}}, {{4.0,4.0,8.0,8.0,10.0,10.0,15.0,30.0}},'+$30',6,5);
					streakCampaign.ModifySpinWheel({{'$5','$5','$8','$8','$10','$10','$20','$50'}}, {{5.0,5.0,8.0,8.0,10.0,10.0,20.0,50.0}},'+$50',7,5);
					streakCampaign.ModifySpinWheel({{'$5','$5','$8','$8','$10','$10','$25','$50'}}, {{5.0,5.0,8.0,8.0,10.0,10.0,25.0,50.0}},'+$50',8,5);
					streakCampaign.ModifySpinWheel({{'$5','$5','$10','$10','$15','$15','$25','$50'}}, {{5.0,5.0,10.0,10.0,15.0,15.0,25.0,50.0}},'+$50',9,5);
					streakCampaign.ModifySpinWheel({{'$5','$5','$10','$10','$20','$20','$30','$100'}}, {{5.0,5.0,10.0,10.0,20.0,20.0,30.0,100.0}},'+$100',10,5);
					
					streakCampaign.ApplyDomains({{domain.Id}});
				}}
                ");
			return this;
		}

		public LoyaltyMockGenerator CreateCustomerRegisterPurchaseMock()
		{
			var result = Perform($@"
				testcustomer = customer(company, '1234567', {Agents.TEST_BOOK});
				{{
					player = company.Players.SearchPlayer(testcustomer.Player.Id);
					streakCampaigns = company.Campaigns.ActiveStreakCampaigns(Now, 1);
					for (streakCampaign : streakCampaigns)
					{{
						streakCampaign.RegisterPurchase(itIsThePresent, player, Now, 1, 1, 1);
					}}
				}}
            ");
			return this;
		}
	}
}
