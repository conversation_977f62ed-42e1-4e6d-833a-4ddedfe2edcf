﻿using GamesEngine.Custodian;
using GamesEngine.Custodian.Operations;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Text;
using System.Threading.Tasks;

namespace GuardianAPI.Controllers
{
	public class PaymentCommitmentsController : AuthorizeController
	{
		[HttpGet("api/paymentcommitments")]
		[Authorize(Roles = "h13")]
		public async Task<IActionResult> ListPaymentCommitmentsAsync(
			DateTime initDateToSearch,
			DateTime finalDateToSearch,
			string processorId, 
			string accountNumber, 
			string currencuCode, 
			int inicialIndex, 
			int amountOfRows,
			decimal minAmount, 
			decimal maxAmount, 
			string transactionType)
		{
			if (inicialIndex < 0) return BadRequest($"{nameof(inicialIndex)} must be greater than 0");
			if (initDateToSearch == default(DateTime) || finalDateToSearch == default(DateTime)) return BadRequest($"{nameof(initDateToSearch)} and {nameof(finalDateToSearch)} are required.");
			if (amountOfRows == 0) amountOfRows = 50;
			if (string.IsNullOrEmpty(processorId)) processorId = "";
			if (string.IsNullOrEmpty(accountNumber)) accountNumber = "";
			if (string.IsNullOrEmpty(currencuCode)) currencuCode = "";
			if (string.IsNullOrEmpty(transactionType)) transactionType = "";

			var normalizedStartDate = $"{initDateToSearch.Month}/{initDateToSearch.Day}/{initDateToSearch.Year}";
			var normalizedEndDate = $"{finalDateToSearch.Month}/{finalDateToSearch.Day}/{finalDateToSearch.Year}";

			IActionResult result = await GuardianManagerAPI.GuardianManager.PerformQryAsync(HttpContext, $@"
				{{
					accountsStored = guardian.Accounts();
					accountsFiltered = accountsStored.ListAccumulates({normalizedStartDate}, {normalizedEndDate}, {inicialIndex}, {amountOfRows}, '{processorId}', '{accountNumber}', '{currencuCode}', {minAmount}, {maxAmount}, '{transactionType}');
					for( accounts : accountsFiltered.List())
					{{
						print accounts.Account.Id processorAccountId;
						print accounts.Account.Number accountNumber;
						print accounts.Account.CurrencyCodeAsText accountCurrency;
						print accounts.Accumulate.ToDisplayFormat() accumulate;
						print accounts.AccumulateCurrencyCodeAsText accumulateCurrencyCodeAsText;
						print accounts.ScheduledDate ScheduledDate;
					}}
					processorsWithDistinctKey = company.System.PaymentProcessor.SearchProcessorsWithDistinctKey();
					for (processors:processorsWithDistinctKey)
					{{
						processor = processors;
						print processor.Alias alias;
						print processor.Entity.Name entity;
						print processor.PaymentMethodAsText paymentMethod;
						print processor.Transactions.TransactionsAsText transactionType;
						print processor.CurrencyIso4217Code currencyCode;

						account = guardian.Accounts().SearchByProcessor(processor.ProcessorKey);
						print account.Id accountId;
					}}
				}}
			");
			return result;

		}

		[HttpGet("api/paymentcommitments/details")]
		[Authorize(Roles = "h14")]
		public async Task<IActionResult> ListPaymentCommitmentsDetailsAsync(string accountNumber, DateTime dateToSearch, int inicialIndex, int amountOfRows, string currencyCode)
		{
			if (inicialIndex < 0) return BadRequest($"{nameof(inicialIndex)} must be greater than 0");
			if (string.IsNullOrEmpty(currencyCode)) currencyCode = "";
			if (string.IsNullOrWhiteSpace(accountNumber)) return BadRequest($"{nameof(accountNumber)} is required.");
			if (dateToSearch == default(DateTime)) dateToSearch = DateTime.Now;
			if (amountOfRows == 0) amountOfRows = 50;

			Operation.StatusCodes transactionStatus = Operation.StatusCodes.IN_PROCESS;
			var normalizedDateToSearch = $"{dateToSearch.Month}/{dateToSearch.Day}/{dateToSearch.Year}";
			IActionResult result = await GuardianManagerAPI.GuardianManager.PerformQryAsync(HttpContext, $@"
			{{
				accountsStored = guardian.Accounts();
				account = accountsStored.Search('{accountNumber}');

				print account.Number accountNumber;
				print account.CurrencyCodeAsText accountCurrency;

				disbursmentsByDateList = guardian.DisbursmentsByDate();
				disbursementsGroup = disbursmentsByDateList.List({normalizedDateToSearch});
				disbursementsGroupFilteredByAccount = disbursementsGroup.Filter('{accountNumber}', '{currencyCode}', {transactionStatus});
				disbursementsGroupFiltered = disbursementsGroupFilteredByAccount.Filter({inicialIndex}, {amountOfRows});
				
				for(disbursments : disbursementsGroupFiltered.List() )
				{{
					print disbursments.TransacionId transactionId;
					print disbursments.FormatedAmount formatedAmount;
					print disbursments.Id disbursmentId;
					print disbursments.Amount amount;
					print disbursments.CurrencyCodeAsText CurrencyCodeAsText;
					print disbursments.ScheduledDate scheduledDate;
					print disbursments.ItsApproved itsApproved;
					print disbursments.Description description;
					print disbursments.HasExecutions() hasExecutions;
					if(disbursments.HasExecutions())
					{{
						print disbursments.LastExecution.Date lastExecutionDate;
						print disbursments.LastExecution.StatusTxt lastExecutionStatus;
						print disbursments.LastExecution.Message lastExecutionMessage;
					}}
				}}
				print disbursementsGroupFilteredByAccount.Count() totalOfRows;
			}}
			");
			return result;
		}
		[HttpPost("api/paymentcommitments/execution")]
		[Authorize(Roles = "h15")]
		public async Task<IActionResult> PayCommitmentsAsync([FromBody] PaymentsToBeExecutedModel payload)
		{
			if (payload == null) return BadRequest($"{nameof(payload)} is required");
			if (!payload.HasAnItem()) return BadRequest($"{nameof(payload)} can not be empty");

			string path = Security.UserPath(HttpContext);
			String user = Security.UserName(HttpContext);
			const int storeId = 4;
			StringBuilder program = new StringBuilder();
			foreach (PaymentsToBeExecutedModel.Operation operation in payload.Operations)
			{
				program.AppendLine($"payments.Add({operation.Id}, {{{string.Join(",", operation.DisbusmentIds)}}} );");
			}
			IActionResult result = await GuardianManagerAPI.GuardianManager.PerformCmdAsync(HttpContext, $@"
				{{
					payments = PaymentsToBeExecuted(guardian);
					{program}
					paymentResult = payments.Execute(itIsThePresent, Now, '{user}', {storeId}, '{path}');
					

					print paymentResult.Date date;
					print paymentResult.ExecutedBy executedBy;
					print paymentResult.Transactions transactions;
					print paymentResult.TransactionWithErrors transactionWithErrors;
					for(disbursementWithErrors : paymentResult.DisbursementsExecutedWithError)
					{{
						print disbursementWithErrors.TransacionId transacionId;
					}}
					for(totals : paymentResult.TotalAmounts)
					{{
						print totals.ToDisplayFormat() amount;
					}}
				}}
			");

			return result;
		}
	}
}
