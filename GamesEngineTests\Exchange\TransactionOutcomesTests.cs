﻿using GamesEngine.Business;
using GamesEngine.Exchange;
using GamesEngine.Finance;
using GamesEngineTests.Unit_Tests.Games.Lotto;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using town.connectors.drivers;

namespace GamesEngineTests.Exchange
{
    [TestClass]
    public class TransactionOutcomesTests
    {
        [TestMethod]
        public void ListDefaultParameters()
        {
            Company company = new Company();
            company.Sales.CreateStore(1, "Test Store").MakeCurrent();
            Marketplace marketplace = new Marketplace(company, "CR");
            var transactionOutcomes = marketplace.TransactionOutcomes();
            var parameters = transactionOutcomes.ListDefaultParametersFor(TransactionType.Deposit).GetAll;
            Assert.AreEqual(8, parameters.Count());

            var parameter = parameters.ElementAt(0);
            Assert.AreEqual("TransactionId", parameter.Identifier);
            parameter = parameters.ElementAt(1);
            Assert.AreEqual("Date", parameter.Identifier);
            parameter = parameters.ElementAt(2);
            Assert.AreEqual("CustomerNumber", parameter.Identifier);
            parameter = parameters.ElementAt(3);
            Assert.AreEqual("AccountNumber", parameter.Identifier);
            parameter = parameters.ElementAt(4);
            Assert.AreEqual("Currency", parameter.Identifier);

            parameters = transactionOutcomes.ListDefaultParametersFor(TransactionType.Withdrawal).GetAll;
            Assert.AreEqual(10, parameters.Count());
            parameter = parameters.ElementAt(0);
            Assert.AreEqual("TransactionId", parameter.Identifier);
            parameter = parameters.ElementAt(1);
            Assert.AreEqual("AuthorizationId", parameter.Identifier);
            parameter = parameters.ElementAt(2);
            Assert.AreEqual("Date", parameter.Identifier);
            parameter = parameters.ElementAt(3);
            Assert.AreEqual("CustomerNumber", parameter.Identifier);
            parameter = parameters.ElementAt(4);
            Assert.AreEqual("AccountNumber", parameter.Identifier);
            parameter = parameters.ElementAt(5);
            Assert.AreEqual("Currency", parameter.Identifier);
        }

        [TestMethod]
        public void DefaultTemplate()
        {
            Company company = new Company();
            company.Sales.CreateStore(1, "Test Store").MakeCurrent();
            var localhostDomain = company.Sales.CreateDomain(false, 4, "localhost", PaymentChannels.Agents.INSIDER);
            Marketplace marketplace = new Marketplace(company, "CR");

            var transactionOutcomes = marketplace.TransactionOutcomes();
            transactionOutcomes.CreatesOutcomesFor(localhostDomain);
            var template = transactionOutcomes.TemplateFor(localhostDomain, TransactionType.Deposit, Coinage.Coin(Currencies.CODES.USD));
            Assert.IsTrue(template.IsActive);
            var lines = template.Lines;
            Assert.AreEqual(2, lines.Count());

            var line = lines.ElementAt(0);
            Assert.AreEqual(1, line.LineNumber);
            Assert.AreEqual("100-01", line.AccountNumber);
            Assert.AreEqual("TotalAmount", line.ParameterName);
            Assert.AreEqual("Dummy Deposit Transaction {TransactionId}", line.Description);
            Assert.IsTrue(line.IsDebit);
            line = lines.ElementAt(1);
            Assert.AreEqual(2, line.LineNumber);
            Assert.AreEqual("200-01", line.AccountNumber);
            Assert.AreEqual("TotalAmount", line.ParameterName);
            Assert.AreEqual("Dummy Deposit Transaction {TransactionId}", line.Description);
            Assert.IsTrue(line.IsCredit);

            template = transactionOutcomes.TemplateFor(localhostDomain, TransactionType.Withdrawal, Coinage.Coin(Currencies.CODES.USD));
            Assert.IsTrue(template.IsActive);
            lines = template.Lines;
            Assert.AreEqual(2, lines.Count());
            line = lines.ElementAt(0);
            Assert.AreEqual(1, line.LineNumber);
            Assert.AreEqual("100-01", line.AccountNumber);
            Assert.AreEqual("TotalAmount", line.ParameterName);
            Assert.AreEqual("Dummy Withdrawal Transaction {TransactionId}", line.Description);
            Assert.IsTrue(line.IsDebit);
            line = lines.ElementAt(1);
            Assert.AreEqual(2, line.LineNumber);
            Assert.AreEqual("200-01", line.AccountNumber);
            Assert.AreEqual("TotalAmount", line.ParameterName);
            Assert.AreEqual("Dummy Withdrawal Transaction {TransactionId}", line.Description);
            Assert.IsTrue(line.IsCredit);
        }

        [TestMethod]
        public void AddCustomParameter()
        {
            CurrenciesTest.AddCurrencies();
            Company company = new Company();
            company.Sales.CreateStore(1, "Test Store").MakeCurrent();
            var localhostDomain = company.Sales.CreateDomain(false, 4, "localhost", PaymentChannels.Agents.INSIDER);
            Marketplace marketplace = new Marketplace(company, "CR");
            var transactionType = TransactionType.Deposit;

            var transactionOutcomes = marketplace.TransactionOutcomes();
            transactionOutcomes.CreatesOutcomesFor(localhostDomain);
            var parameters = transactionOutcomes.CustomParametersFor(localhostDomain, transactionType, Coinage.Coin(Currencies.CODES.USD)).GetAll;
            Assert.AreEqual(0, parameters.Count());
            transactionOutcomes.AddCustomAmountParameter(localhostDomain, transactionType, Coinage.Coin(Currencies.CODES.USD), "TestAmountParam", "test parameter", "x+y");
            Assert.AreEqual(1, parameters.Count());
            transactionOutcomes.AddCustomTextParameter(localhostDomain, transactionType, Coinage.Coin(Currencies.CODES.USD), "TestTextParam", "test parameter");
            Assert.AreEqual(2, parameters.Count());
        }

        [TestMethod]
        public void UpdateCustomParameter()
        {
            Company company = new Company();
            company.Sales.CreateStore(1, "Test Store").MakeCurrent();
            var localhostDomain = company.Sales.CreateDomain(false, 4, "localhost", PaymentChannels.Agents.INSIDER);
            Marketplace marketplace = new Marketplace(company, "CR");
            var transactionType = TransactionType.Deposit;

            var transactionOutcomes = marketplace.TransactionOutcomes();
            transactionOutcomes.CreatesOutcomesFor(localhostDomain);
            var parameters = transactionOutcomes.CustomParametersFor(localhostDomain, transactionType, Coinage.Coin(Currencies.CODES.USD)).GetAll;
            Assert.AreEqual(0, parameters.Count());
            transactionOutcomes.AddCustomAmountParameter(localhostDomain, transactionType, Coinage.Coin(Currencies.CODES.USD), "TestAmountParam", "test parameter", "x+y");
            Assert.AreEqual(1, parameters.Count());
            transactionOutcomes.AddCustomTextParameter(localhostDomain, transactionType, Coinage.Coin(Currencies.CODES.USD), "TestTextParam", "test parameter");
            Assert.AreEqual(2, parameters.Count());

            var newDescription1 = "test parameter1";
            transactionOutcomes.UpdateCustomAmountParameter(localhostDomain, transactionType, Coinage.Coin(Currencies.CODES.USD), "TestAmountParam", newDescription1, "x+y+z");
            var newDescription2 = "test parameter2";
            transactionOutcomes.UpdateCustomTextParameter(localhostDomain, transactionType, Coinage.Coin(Currencies.CODES.USD), "TestTextParam", newDescription2);
            var parameter = parameters.ElementAt(0);
            Assert.AreEqual(newDescription1, parameter.Description);
            parameter = parameters.ElementAt(1);
            Assert.AreEqual(newDescription2, parameter.Description);
        }

        [TestMethod]
        public void ExistsCustomParameter()
        {
            Company company = new Company();
            company.Sales.CreateStore(1, "Test Store").MakeCurrent();
            var localhostDomain = company.Sales.CreateDomain(false, 4, "localhost", PaymentChannels.Agents.INSIDER);
            Marketplace marketplace = new Marketplace(company, "CR");
            var transactionType = TransactionType.Deposit;

            var transactionOutcomes = marketplace.TransactionOutcomes();
            transactionOutcomes.CreatesOutcomesFor(localhostDomain);
            var parameters = transactionOutcomes.CustomParametersFor(localhostDomain, transactionType, Coinage.Coin(Currencies.CODES.USD)).GetAll;
            Assert.AreEqual(0, parameters.Count());
            transactionOutcomes.AddCustomAmountParameter(localhostDomain, transactionType, Coinage.Coin(Currencies.CODES.USD), "TestAmountParam", "test parameter", "x+y");
            Assert.AreEqual(1, parameters.Count());
            transactionOutcomes.AddCustomTextParameter(localhostDomain, transactionType, Coinage.Coin(Currencies.CODES.USD), "TestTextParam", "test parameter");
            Assert.AreEqual(2, parameters.Count());

            var exists = transactionOutcomes.ExistsCustomParameter(localhostDomain, transactionType, Coinage.Coin(Currencies.CODES.USD), "TestAmountParam");
            Assert.IsTrue(exists);
            exists = transactionOutcomes.ExistsCustomParameter(localhostDomain, transactionType, Coinage.Coin(Currencies.CODES.USD), "TestTextParam");
            Assert.IsTrue(exists);
            exists = transactionOutcomes.ExistsCustomParameter(localhostDomain, transactionType, Coinage.Coin(Currencies.CODES.BTC), "TestTextParam");
            Assert.IsFalse(exists);
            exists = transactionOutcomes.ExistsCustomParameter(localhostDomain, transactionType, Coinage.Coin(Currencies.CODES.USD), "TestTextParam1");
            Assert.IsFalse(exists);
        }

        [TestMethod]
        public void AddCustomParameterAsFee()
        {
            Company company = new Company();
            company.Sales.CreateStore(1, "Test Store").MakeCurrent();
            var localhostDomain = company.Sales.CreateDomain(false, 4, "localhost", PaymentChannels.Agents.INSIDER);
            Marketplace marketplace = new Marketplace(company, "CR");
            var transactionType = TransactionType.Deposit;

            var transactionOutcomes = marketplace.TransactionOutcomes();
            transactionOutcomes.CreatesOutcomesFor(localhostDomain);
            var parameters = transactionOutcomes.CustomParametersFor(localhostDomain, transactionType, Coinage.Coin(Currencies.CODES.USD)).GetAll;
            Assert.AreEqual(0, parameters.Count());
            transactionOutcomes.AddCustomAmountParameter(localhostDomain, transactionType, Coinage.Coin(Currencies.CODES.USD), "TestAmountParam", "test parameter", "x+y");
            Assert.AreEqual(1, parameters.Count());
            transactionOutcomes.AddCustomTextParameter(localhostDomain, transactionType, Coinage.Coin(Currencies.CODES.USD), "TestTextParam", "test parameter");
            Assert.AreEqual(2, parameters.Count());

            transactionOutcomes.AddCustomParameterAsFee(localhostDomain, transactionType, Coinage.Coin(Currencies.CODES.USD), "TestAmountParam");
            var exists = transactionOutcomes.IsParameterAddedAsFee(localhostDomain, transactionType, Coinage.Coin(Currencies.CODES.USD), "TestAmountParam");
            Assert.IsTrue(exists);
            exists = transactionOutcomes.IsParameterAddedAsFee(localhostDomain, transactionType, Coinage.Coin(Currencies.CODES.BTC), "TestAmountParam");
            Assert.IsFalse(exists);
            exists = transactionOutcomes.IsParameterAddedAsFee(localhostDomain, transactionType, Coinage.Coin(Currencies.CODES.USD), "TestTextParam");
            Assert.IsFalse(exists);
        }

        [TestMethod]
        public void RemoveCustomParameterAsFee()
        {
            Company company = new Company();
            company.Sales.CreateStore(1, "Test Store").MakeCurrent();
            var localhostDomain = company.Sales.CreateDomain(false, 4, "localhost", PaymentChannels.Agents.INSIDER);
            Marketplace marketplace = new Marketplace(company, "CR");
            var transactionType = TransactionType.Deposit;

            var transactionOutcomes = marketplace.TransactionOutcomes();
            transactionOutcomes.CreatesOutcomesFor(localhostDomain);
            var parameters = transactionOutcomes.CustomParametersFor(localhostDomain, transactionType, Coinage.Coin(Currencies.CODES.USD)).GetAll;
            Assert.AreEqual(0, parameters.Count());
            transactionOutcomes.AddCustomAmountParameter(localhostDomain, transactionType, Coinage.Coin(Currencies.CODES.USD), "TestAmountParam", "test parameter", "x+y");
            Assert.AreEqual(1, parameters.Count());
            transactionOutcomes.AddCustomTextParameter(localhostDomain, transactionType, Coinage.Coin(Currencies.CODES.USD), "TestTextParam", "test parameter");
            Assert.AreEqual(2, parameters.Count());

            transactionOutcomes.AddCustomParameterAsFee(localhostDomain, transactionType, Coinage.Coin(Currencies.CODES.USD), "TestAmountParam");
            var exists = transactionOutcomes.IsParameterAddedAsFee(localhostDomain, transactionType, Coinage.Coin(Currencies.CODES.USD), "TestAmountParam");
            Assert.IsTrue(exists);
            transactionOutcomes.RemoveCustomParameterAsFee(localhostDomain, transactionType, Coinage.Coin(Currencies.CODES.USD), "TestAmountParam");
            exists = transactionOutcomes.IsParameterAddedAsFee(localhostDomain, transactionType, Coinage.Coin(Currencies.CODES.USD), "TestAmountParam");
            Assert.IsFalse(exists);
        }

        [TestMethod]
        public void IsParameterAddedAsFee()
        {
            Company company = new Company();
            company.Sales.CreateStore(1, "Test Store").MakeCurrent();
            var localhostDomain = company.Sales.CreateDomain(false, 4, "localhost", PaymentChannels.Agents.INSIDER);
            Marketplace marketplace = new Marketplace(company, "CR");
            var transactionType = TransactionType.Deposit;

            var transactionOutcomes = marketplace.TransactionOutcomes();
            transactionOutcomes.CreatesOutcomesFor(localhostDomain);
            var parameters = transactionOutcomes.CustomParametersFor(localhostDomain, transactionType, Coinage.Coin(Currencies.CODES.USD)).GetAll;
            Assert.AreEqual(0, parameters.Count());
            transactionOutcomes.AddCustomAmountParameter(localhostDomain, transactionType, Coinage.Coin(Currencies.CODES.USD), "TestAmountParam", "test parameter", "x+y");
            Assert.AreEqual(1, parameters.Count());
            transactionOutcomes.AddCustomTextParameter(localhostDomain, transactionType, Coinage.Coin(Currencies.CODES.USD), "TestTextParam", "test parameter");
            Assert.AreEqual(2, parameters.Count());

            transactionOutcomes.AddCustomParameterAsFee(localhostDomain, transactionType, Coinage.Coin(Currencies.CODES.USD), "TestAmountParam");
            var parameter = parameters.ElementAt(0);
            var exists = transactionOutcomes.IsParameterAddedAsFee(localhostDomain, transactionType, Coinage.Coin(Currencies.CODES.USD), parameter);
            Assert.IsTrue(exists);
            parameter = parameters.ElementAt(1);
            exists = transactionOutcomes.IsParameterAddedAsFee(localhostDomain, transactionType, Coinage.Coin(Currencies.CODES.USD), parameter);
            Assert.IsFalse(exists);
        }

        [TestMethod]
        public void EnableOrDisableTemplate()
        {
            Company company = new Company();
            company.Sales.CreateStore(1, "Test Store").MakeCurrent();
            var localhostDomain = company.Sales.CreateDomain(false, 4, "localhost", PaymentChannels.Agents.INSIDER);
            Marketplace marketplace = new Marketplace(company, "CR");
            var transactionType = TransactionType.Deposit;

            var transactionOutcomes = marketplace.TransactionOutcomes();
            transactionOutcomes.CreatesOutcomesFor(localhostDomain);
            var template = transactionOutcomes.TemplateFor(localhostDomain, transactionType, Coinage.Coin(Currencies.CODES.USD));
            Assert.IsTrue(template.IsActive);
            transactionOutcomes.DisableTemplate(localhostDomain, transactionType, Coinage.Coin(Currencies.CODES.USD));
            Assert.IsFalse(template.IsActive);
            transactionOutcomes.EnableTemplate(localhostDomain, transactionType, Coinage.Coin(Currencies.CODES.USD));
            Assert.IsTrue(template.IsActive);
        }

        [TestMethod]
        public void EnableOrDisableFee()
        {
            Company company = new Company();
            company.Sales.CreateStore(1, "Test Store").MakeCurrent();
            var localhostDomain = company.Sales.CreateDomain(false, 4, "localhost", PaymentChannels.Agents.INSIDER);
            Marketplace marketplace = new Marketplace(company, "CR");
            var transactionType = TransactionType.Deposit;

            var transactionOutcomes = marketplace.TransactionOutcomes();
            transactionOutcomes.CreatesOutcomesFor(localhostDomain);
            var areFeesEnabled = transactionOutcomes.AreFeesEnabled(localhostDomain, transactionType, Coinage.Coin(Currencies.CODES.USD));
            Assert.IsTrue(areFeesEnabled);
            transactionOutcomes.DisableFee(localhostDomain, transactionType, Coinage.Coin(Currencies.CODES.USD));
            areFeesEnabled = transactionOutcomes.AreFeesEnabled(localhostDomain, transactionType, Coinage.Coin(Currencies.CODES.USD));
            Assert.IsFalse(areFeesEnabled);
            transactionOutcomes.EnableFee(localhostDomain, transactionType, Coinage.Coin(Currencies.CODES.USD));
            areFeesEnabled = transactionOutcomes.AreFeesEnabled(localhostDomain, transactionType, Coinage.Coin(Currencies.CODES.USD));
            Assert.IsTrue(areFeesEnabled);
        }
    }
}
