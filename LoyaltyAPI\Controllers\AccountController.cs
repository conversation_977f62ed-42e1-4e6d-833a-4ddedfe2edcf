﻿using Connectors.town.connectors.drivers.artemis;
using GamesEngine.Business;
using GamesEngine.Exchange.town.connectors.drivers.artemis.tenant;
using GamesEngine.Settings;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using System.Threading.Tasks;
using town.connectors.drivers.artemis;
using static GamesEngine.Exchange.town.connectors.drivers.artemis.tenant.AccountInfo;
using static town.connectors.CustomSettings;

namespace LoyaltyAPI.Controllers
{
    public class AccountController : Controller
    {
        [HttpGet("api/accounts/{accountName}")]
        public async Task<IActionResult> GetAccountNumberAsync(string accountName)
        {
            if (string.IsNullOrWhiteSpace(accountName)) return BadRequest($"{nameof(accountName)} is required");

            AccountInfoBody accountInfo;
            var paymentProcessor = WholePaymentProcessor.Instance().SearchOtherProcessorBy(typeof(AccountInfo));
            using (RecordSet recordSet = paymentProcessor.GetRecordSet())
            {
                recordSet.SetParameter("accountName", accountName);

                accountInfo = await paymentProcessor.ExecuteAsync<AccountInfoBody>(DateTime.Now, recordSet);
            }

            var result = new AccountNumberResponse() { AccountNumber = accountInfo.idPlayer.ToString() };
            return Ok(result);
        }

        [HttpGet("api/externalAccounts/{accountNumber}")]
        public async Task<IActionResult> GetExternalAccountAsync(string accountNumber)
        {
            if (string.IsNullOrWhiteSpace(accountNumber)) return BadRequest($"{nameof(accountNumber)} is required");

            PlayerBalanceBody infoBalance;
            var paymentProcessor = WholePaymentProcessor.Instance().SearchBalanceProcesorBy(typeof(BalanceInfo));
            using (RecordSet recordSet = paymentProcessor.GetRecordSet())
            {
                recordSet.SetParameter("customerId", accountNumber);

                infoBalance = await paymentProcessor.ExecuteAsync<PlayerBalanceBody>(DateTime.Now, recordSet);
            }

            var result = new AccountNameResponse() { AccountName = infoBalance.player ?? string.Empty };
            return Ok(result);
        }

        [DataContract(Name = "AccountNumberResponse")]
        public class AccountNumberResponse
        {
            [DataMember(Name = "accountNumber")]
            public string AccountNumber { get; set; }
        }

        [DataContract(Name = "AccountNameResponse")]
        public class AccountNameResponse
        {
            [DataMember(Name = "accountName")]
            public string AccountName { get; set; }
        }
    }
}
