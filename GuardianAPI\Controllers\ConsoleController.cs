﻿using GamesEngine.RealTime;
using GamesEngine.Settings;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GuardianAPI.Controllers
{
    public class ConsoleController : AuthorizeController
    {
        [HttpPost("console/command")]
        [Authorize(Roles = "devops")]
        public async Task<IActionResult> processCommandAsync()
        {
            string body = "";

            using (StreamReader reader = new StreamReader(Request.Body, Encoding.UTF8))
            {
                body = await reader.ReadToEndAsync();
            }

            if (body == null) return NotFound("Body is required");
            if (String.IsNullOrWhiteSpace(body)) return NotFound($"Parameter {nameof(body)} is required");

            var result = await GuardianManagerAPI.GuardianManager.PerformCmdAsync(HttpContext, body);
            return result;
        }

        [HttpPost("console/query")]
        [Authorize(Roles = "devops")]
        public async Task<IActionResult> processQueryAsync()
        {
            string body = "";

            using (StreamReader reader = new StreamReader(Request.Body, Encoding.UTF8))
            {
                body = await reader.ReadToEndAsync();
            }

            if (body == null) return NotFound("Body is required");
            if (String.IsNullOrWhiteSpace(body)) return NotFound($"Parameter {nameof(body)} is required");

            var result = await GuardianManagerAPI.GuardianManager.PerformQryAsync(HttpContext, body);
            return result;
        }

        [HttpGet("console/scriptEnEjecucion")]
        [AllowAnonymous]
        public IActionResult ScriptEnEjecucion()
        {
            IActionResult result;
            try
            {
                result = Ok("Script executed: " + GuardianManagerAPI.GuardianManager.ScriptEnEjecucion);
            }
            catch
            {
                result = Ok("Vuelva a ejecutar el request");
            }
            return result;
        }

        [HttpGet("producer/stop")]
        [AllowAnonymous]
        public async Task<IActionResult> StopProducerAsync()
        {
            await Consumer.StopAllConsumerActiveInMemoryAsync();

            IProducer kafka = Integration.Kafka;
            if (kafka != null)
            {
                await Integration.Kafka.StopProducerAsync();
                return Ok();
            }
            return BadRequest("Kafka is not configured.");
        }

        [HttpGet("consumer/stop")]
        [AllowAnonymous]
        public async Task<IActionResult> StopConsumerAsync()
        {
            await Consumer.StopAllConsumerActiveInMemoryAsync();
            return Ok();
        }

        [HttpPost("consumer/reset")]
        [Authorize(Roles = "devops")]
        public async Task<IEnumerable<int>> ResetAsync(int[] consumerIds)
        {
            return await Consumer.ResetAsync(consumerIds);
        }

        [HttpGet("console/ping")]
        [AllowAnonymous]
        public IActionResult Ping()
        {
            return Ok("pong");
        }

        [HttpGet("consumers")]
        [Authorize(Roles = "devops")]
        public List<string> List()
        {
            return Consumer.ConsumersInfo();
        }

        [HttpPost("api/listener")]
        [AllowAnonymous]
        public async Task<IActionResult> ConnectSignalRAsync([FromBody] SignalRRegisterBody body)
        {
            ProxyListener listener = new ProxyListener(body.IpAddress, body.Port, body.Protocol);
            await listener.ConnectAsync();
            PlatformMonitor.GetInstance().Register(listener);
            return Ok();
        }

        [HttpPost("api/listeners")]
        [AllowAnonymous]
        public async Task<IActionResult> ConnectListenersAsync([FromBody] ListenerRegistrationBody body)
        {
            foreach (var subset in body.subsets)
            {
                if (subset.ports.Length > 1) return BadRequest($@"Only one port is allowed for all connections.");
                var port = subset.ports.First().port;
                var protocol = subset.ports.First().name;
                foreach (var address in subset.addresses)
                {
                    ProxyListener listener = new ProxyListener(address.ip, port, protocol);
                    await listener.ConnectAsync();
                    PlatformMonitor.GetInstance().Register(listener);
                }
            }
            return Ok();
        }

        [HttpGet("api/listeners")]
        [AllowAnonymous]
        public IActionResult GetListeners()
        {
            var result = PlatformMonitor.GetInstance().Listeners;
            return Ok(result);
        }
    }
}
