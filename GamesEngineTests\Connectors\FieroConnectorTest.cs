﻿using Connectors.town.connectors.driver.transactions;
using GamesEngine.Business;
using GamesEngine.Exchange;
using GamesEngine.Finance;
using GamesEngine.Settings;
using GamesEngineMocks;
using GamesEngineTests.Unit_Tests.Games.Lotto;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Http.Features;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Primitives;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Security.Claims;
using System.Threading;
using System.Threading.Tasks;
using town.connectors;
using town.connectors.commons;
using town.connectors.drivers;
using town.connectors.drivers.fiero;
using Unit.Games.Tools;
using static GamesEngine.Finance.PaymentChannels;
using static town.connectors.CustomSettings;
using static town.connectors.CustomSettings.CustomSetting;
using Authorization = town.connectors.drivers.fiero.Authorization;
using Balance = town.connectors.drivers.fiero.Balance;
using DepositTransaction = Connectors.town.connectors.driver.transactions.DepositTransaction;
using WithdrawalTransaction = Connectors.town.connectors.driver.transactions.WithdrawalTransaction;

namespace GamesEngineTests.Unit_Tests.Connectors
{

    [TestClass]
    public class FieroConnectorTest
    {
        [TestMethod]
        public async Task AuthorizationDeclarationAsync()
        {
            Coinage.Add(2, "USD", "$", 2, '$', "dollar", CoinType.Fiat);
            //TODO disparar el docker-compose  cashier asi stub
            Variables variables = new Variables();
            var obj = new { Name = "tester" };
            DateTime now = DateTime.Now;
            CustomSettings cs = new CustomSettings(variables);
            cs.AddFixedParameter(now, "CashierDriver.url", "http://www.site.api.com/rest");

            Authorization authorization = new Authorization();

            authorization.RegisterANotifyErrorsBehavior((string message, Exception e, string[] details) =>
            {

            });
            authorization.RegisterANotifyWarningsBehavior((string message, Exception e, string[] details) =>
            {

            });

            AuthorizationTransaction result;

            authorization.ConfigureThenPrepare(now, cs);
            using (RecordSet recordSet = authorization.CustomSettings.GetRecordSet())
            {
                TestContext context = new TestContext();
                context.Request.Headers["Authorization"] = "Bearer 2434535";

                recordSet.SetParameter("atAddress", "123");
                recordSet.SetParameter("accountNumber", "USD");
                recordSet.SetParameter("purchaseTotal.Value", 123m);
                recordSet.SetParameter("purchaseTotal.CurrencyCode", Coinage.Coin(Currencies.CODES.USD));
                recordSet.SetParameter("storeId", 1);
                recordSet.SetParameter("concept", "123");
                recordSet.SetParameter("fragmentInformation", new FragmentInformation());
                recordSet.SetParameter("referenceNumber", "123");
                recordSet.SetParameter("useless", DateTime.Now);
                recordSet.SetParameter("context", context);

                result = (AuthorizationTransaction)await authorization.ExecuteAsync<AuthorizationTransaction>(now, recordSet);
            }
        }

        [TestMethod]
        public async Task AuthorizationInternalAsync()
        {
            CurrenciesTest.AddCurrencies();
            Movements.Storage = new MovementStorageMemory();

            //TODO disparar el docker-compose  cashier asi stub
            Variables variables = new Variables();
            DateTime now = DateTime.Now;
            CustomSettings cs = new CustomSettings(variables);

            var driver = new AuthorizationInternal();
            driver.ConfigureThenPrepare(now, cs);
            using (RecordSet recordSet = driver.CustomSettings.GetRecordSet())
            {
                TestContext context = new TestContext();
                context.Request.Headers["Authorization"] = "Bearer 2434535";
                const string mock = "Mocks";
                var actor = new RestAPISpawnerActor(new CashierMocks(), string.Empty, mock, mock);

                recordSet.SetParameter("actor", actor);
                recordSet.SetParameter("atAddress", "123");
                recordSet.SetParameter("accountNumber", "USD");
                recordSet.SetParameter("purchaseTotal", new Currency(Coinage.Coin(Currencies.CODES.USD), 123m));
                recordSet.SetParameter("storeId", 1);
                recordSet.SetParameter("concept", "123");
                recordSet.SetParameter("useless", DateTime.Now);
                recordSet.SetParameter("processorId", WholePaymentProcessor.NoPaymentProcessor);
                recordSet.SetParameter("fragmentInformation", new FragmentInformation());
                recordSet.SetParameter("referenceNumber", "123");
                recordSet.SetParameter("context", context);

                var result = await driver.ExecuteAsync<AuthorizationTransaction>(now, recordSet);
            }
        }

        [TestMethod]
        public void CreditNoteCash()
        {
            CurrenciesTest.AddCurrencies();
            Movements.Storage = new MovementStorageMemory();
            //Integration.UseKafka = true;
            QueueInMemory queue = new QueueInMemory();
            Integration.Kafka = queue;
            Integration.SavePreviousSettings(tempUseKafka: false, tempUseKafkaForAuto: false);

            Variables variables = new Variables();
            DateTime now = DateTime.Now;
            CustomSettings cs = new CustomSettings(variables);
            cs.AddFixedParameter(now, "CompanySystemId", "test");

            var driver = new CreditNoteCash();
            driver.ConfigureThenPrepare(now, cs);
            using (RecordSet recordSet = driver.CustomSettings.GetRecordSet())
            {
                var result = driver.Execute<DepositTransaction>(now, recordSet);
            }
        }

        [TestMethod]
        public void DebitNoteCash()
        {
            CurrenciesTest.AddCurrencies();
            Movements.Storage = new MovementStorageMemory();
            //Integration.UseKafka = true;
            QueueInMemory queue = new QueueInMemory();
            Integration.Kafka = queue;
            Integration.SavePreviousSettings(tempUseKafka: false, tempUseKafkaForAuto: false);

            Variables variables = new Variables();
            DateTime now = DateTime.Now;
            CustomSettings cs = new CustomSettings(variables);
            cs.AddFixedParameter(now, "CompanySystemId", "test");

            var driver = new DebitNoteCash();
            driver.ConfigureThenPrepare(now, cs);
            using (RecordSet recordSet = driver.CustomSettings.GetRecordSet())
            {
                var result = driver.Execute<WithdrawalTransaction>(now, recordSet);
            }
        }

        [TestMethod]
        public void Deposit()
        {
            CurrenciesTest.AddCurrencies();
            Movements.Storage = new MovementStorageMemory();
            //Integration.UseKafka = true;
            QueueInMemory queue = new QueueInMemory();
            Integration.Kafka = queue;
            Integration.SavePreviousSettings(tempUseKafka: false, tempUseKafkaForAuto: false);

            //TODO disparar el docker-compose  cashier asi stub
            Variables variables = new Variables();
            DateTime now = DateTime.Now;
            CustomSettings cs = new CustomSettings(variables);

            var driver = new Deposit();
            driver.ConfigureThenPrepare(now, cs);
            using (RecordSet recordSet = driver.CustomSettings.GetRecordSet())
            {
                const string mock = "Mocks";
                var actor = new RestAPISpawnerActor(new CashierMocks(), string.Empty, mock, mock);
                var msg = new DepositMessage("*********￺0￺LR￺2￺225.00￺Game Admin￺Lotto Win for TN#*********-1 - Posted date 10/15/2019 10:00 AM￺*********-1000001-1-1￺LR￺0￺-￺0￺0");

                recordSet.SetParameter("message", msg);
                recordSet.SetParameter("actor", actor);
                var result = driver.Execute<DepositTransaction>(now, recordSet);
            }
        }

        [TestMethod]
        public void DepositThenLock()
        {
            CurrenciesTest.AddCurrencies();
            Movements.Storage = new MovementStorageMemory();
            //Integration.UseKafka = true;
            QueueInMemory queue = new QueueInMemory();
            Integration.Kafka = queue;
            Integration.SavePreviousSettings(tempUseKafka: false, tempUseKafkaForAuto: false);

            //TODO disparar el docker-compose  cashier asi stub
            Variables variables = new Variables();
            DateTime now = DateTime.Now;
            CustomSettings cs = new CustomSettings(variables);

            var driver = new DepositThenLock();
            driver.ConfigureThenPrepare(now, cs);
            using (RecordSet recordSet = driver.CustomSettings.GetRecordSet())
            {
                const string mock = "Mocks";
                var actor = new RestAPISpawnerActor(new CashierMocks(), string.Empty, mock, mock);
                var msg = new FragmentsCreationBody()
                {
                    AgentId = (int)Agents.INSIDER,
                    AtAddress = "123",
                    AuthorizationNumber = 123,
                    CurrencyCode = "USD",
                    OrderNumber = 1,
                    StoreId = 1,
                    TheHighestBetId = 1,
                    TheLowestBetId = 1,
                    Total = 3
                };
                recordSet.SetParameter("fragmentMessage", msg);
                recordSet.SetParameter("actor", actor);
                var result = driver.Execute<AuthorizationTransaction>(now, recordSet);
            }
        }

        [TestMethod]
        public void Withdrawal()
        {
            CurrenciesTest.AddCurrencies();
            Movements.Storage = new MovementStorageMemory();
            //Integration.UseKafka = true;
            QueueInMemory queue = new QueueInMemory();
            Integration.Kafka = queue;
            Integration.SavePreviousSettings(tempUseKafka: false, tempUseKafkaForAuto: false);

            //TODO disparar el docker-compose  cashier asi stub
            Variables variables = new Variables();
            DateTime now = DateTime.Now;
            CustomSettings cs = new CustomSettings(variables);

            var driver = new Withdrawal();
            driver.ConfigureThenPrepare(now, cs);
            using (RecordSet recordSet = driver.CustomSettings.GetRecordSet())
            {
                var storeId = 1;
                var account = "*********";
                var ticketNumber = 1001;
                const string mock = "Mocks";
                var actor = new RestAPISpawnerActor(new CashierMocks(), string.Empty, mock, mock);
                actor.PerformCmd(account, @$"{{
                    balance = atAddress.CreateAccountIfNotExists('{Currencies.CODES.LR}', '{Currencies.CODES.LR}').Balance;
	                store = company.Sales.StoreById({storeId});
	                balance.Accredit(itIsThePresent, Now, Currency('{Currencies.CODES.LR}', 1000), 'ME', '{ticketNumber}', store, '', '');
                }}
                ");
                var msg = new WithdrawMessage("*********￺0￺LR￺2￺225.00￺Game Admin￺Lotto Win for TN#*********-1 - Posted date 10/15/2019 10:00 AM￺*********-1000001-1-1￺LR￺0￺-￺0￺0");

                recordSet.SetParameter("message", msg);
                recordSet.SetParameter("actor", actor);
                var result = driver.Execute<WithdrawalTransaction>(now, recordSet);
            }
        }

        [TestMethod]
        public async Task BalanceAsync()
        {
            Integration.UseKafka = false;
            CurrenciesTest.AddCurrencies();
            Movements.Storage = new MovementStorageMemory();

            //TODO disparar el docker-compose  cashier asi stub
            Variables variables = new Variables();
            DateTime now = DateTime.Now;
            CustomSettings cs = new CustomSettings(variables);

            var driver = new Balance();
            driver.ConfigureThenPrepare(now, cs);
            using (RecordSet recordSet = driver.CustomSettings.GetRecordSet())
            {
                TestContext context = new TestContext();
                context.Request.Headers["Authorization"] = "Bearer 2434535";
                const string mock = "Mocks";
                var actor = new RestAPISpawnerActor(new CashierMocks(), string.Empty, mock, mock);

                recordSet.SetParameter("atAddress", "123");
                recordSet.SetParameter("HttpContext", context);
                recordSet.SetParameter("currencyCode", "USD");
                recordSet.SetParameter("actor", actor);
                var result = await driver.ExecuteAsync<decimal>(DateTime.Now, recordSet);
            }
        }

        [TestMethod]
        public async Task BalanceWithAccountsAsync()
        {
            Integration.UseKafka = false;
            CurrenciesTest.AddCurrencies();
            Movements.Storage = new MovementStorageMemory();

            //TODO disparar el docker-compose  cashier asi stub
            Variables variables = new Variables();
            DateTime now = DateTime.Now;
            CustomSettings cs = new CustomSettings(variables);

            var driver = new BalanceWithAccounts();
            driver.ConfigureThenPrepare(now, cs);
            using (RecordSet recordSet = driver.CustomSettings.GetRecordSet())
            {
                TestContext context = new TestContext();
                context.Request.Headers["Authorization"] = "Bearer 2434535";
                const string mock = "Mocks";
                var actor = new RestAPISpawnerActor(new CashierMocks(), string.Empty, mock, mock);

                recordSet.SetParameter("atAddress", "123");
                recordSet.SetParameter("HttpContext", context);
                recordSet.SetParameter("currencyCode", "USD");
                recordSet.SetParameter("actor", actor);
                var result = await driver.ExecuteAsync<BalanceResponse>(DateTime.Now, recordSet);
            }
        }

        [TestMethod]
        public void Fragment_an_Authorization()
        {
            CurrenciesTest.AddCurrencies();
            Movements.Storage = new MovementStorageMemory();
            Integration.UseKafka = true;
            QueueInMemory queue = new QueueInMemory();
            Integration.Kafka = queue;
            Integration.SavePreviousSettings(tempUseKafka: false, tempUseKafkaForAuto: false);

            var storeId = 1;
            var account = "**********";
            var ticketNumber = 1001;
            const string mock = "Mocks";
            var actor = new RestAPISpawnerActor(new CashierMocks(), string.Empty, mock, mock);
            actor.PerformCmd(account, @$"{{
                balance = atAddress.CreateAccountIfNotExists('{Currencies.CODES.USD}', '{Currencies.CODES.USD}').Balance;
	            store = company.Sales.StoreById({storeId});
	            balance.Accredit(itIsThePresent, Now, Currency('{Currencies.CODES.USD}', 1), 'ME', '{ticketNumber}', store, '', '');
	            authorization = atAddress.CreateAuthorization(itIsThePresent, Now, Currency('{Currencies.CODES.USD}', 1), {ticketNumber}, store, '', '', '{Currencies.CODES.USD}');
            }}
            ");

            //TODO disparar el docker-compose  cashier asi stub
            Variables variables = new Variables();
            var obj = new { Name = "tester" };
            DateTime now = DateTime.Now;
            CustomSettings cs = new CustomSettings(variables);

            Fragment_an_Authorization driver = new Fragment_an_Authorization();
            driver.ConfigureThenPrepare(now, cs);
            using (RecordSet recordSet = driver.CustomSettings.GetRecordSet())
            {
                recordSet.SetParameter("actor", actor);
                recordSet.SetParameter("ticketNumber", ticketNumber);
                recordSet.SetParameter("customerId", account);
                var wagers = new PostFreeFormWager[] {
                    new PostFreeFormWager(){
                        BetDescription = "lotto bet description 1",
                        ReferenceNumber = "1-1",
                        Risk = "1",
                        Status = "W",
                        ToWin = "900",
                        WagerNumber = "1"
                    }
                };
                recordSet.SetParameter("wagers", wagers);

                PostFreeFormWagerCollectionSuccessResponse result = driver.Execute<PostFreeFormWagerCollectionSuccessResponse>(now, recordSet);
            }
        }

        [TestMethod]
        public void FragmentAnAuthorizationAppended()
        {
            CurrenciesTest.AddCurrencies();
            Movements.Storage = new MovementStorageMemory();
            //Integration.UseKafka = true;
            QueueInMemory queue = new QueueInMemory();
            Integration.Kafka = queue;
            Integration.SavePreviousSettings(tempUseKafka: false, tempUseKafkaForAuto: false);

            var storeId = 1;
            var account = "*********";
            var ticketNumber = 1001;
            const string mock = "Mocks";
            var actor = new RestAPISpawnerActor(new CashierMocks(), string.Empty, mock, mock);
            actor.PerformCmd(account, @$"{{
                balance = atAddress.CreateAccountIfNotExists('{Currencies.CODES.USD}', '{Currencies.CODES.USD}').Balance;
	            store = company.Sales.StoreById({storeId});
	            balance.Accredit(itIsThePresent, Now, Currency('{Currencies.CODES.USD}', 1), 'ME', '{ticketNumber}', store, '', '');
	            authorization = atAddress.CreateAuthorization(itIsThePresent, Now, Currency('{Currencies.CODES.USD}', 1), {ticketNumber}, store, '', '', '{Currencies.CODES.USD}');
            }}
            ");

            //TODO disparar el docker-compose  cashier asi stub
            Variables variables = new Variables();
            var obj = new { Name = "tester" };
            DateTime now = DateTime.Now;
            CustomSettings cs = new CustomSettings(variables);

            var driver = new FragmentAnAuthorizationAppended();
            driver.ConfigureThenPrepare(now, cs);
            using (RecordSet recordSet = driver.CustomSettings.GetRecordSet())
            {
                recordSet.SetParameter("actor", actor);
                recordSet.SetParameter("authorizationNumber", ticketNumber);
                recordSet.SetParameter("atAddress", account);
                recordSet.SetParameter("theLowestFragmentNumber", 1);
                recordSet.SetParameter("theHighestFragmentNumber", 1);
                var fragmentsChunks = new FragmentsChunks();
                Fragment fragment = new Fragment()
                {
                    BetDescription = "lotto bet description 1",
                    Risk = "1",
                    ReferenceNumber = "1-1",
                    ToWin = "900",
                    Number = "1"
                };

                fragmentsChunks.Add(fragment);
                recordSet.SetParameter("fragmentsChunks", fragmentsChunks);

                driver.Execute<object>(now, recordSet);
            }
        }

        [TestMethod]
        public void Grade()
        {
            CurrenciesTest.AddCurrencies();
            Movements.Storage = new MovementStorageMemory();
            //Integration.UseKafka = true;
            QueueInMemory queue = new QueueInMemory();
            Integration.Kafka = queue;
            Integration.SavePreviousSettings(tempUseKafka: false, tempUseKafkaForAuto: false);

            var storeId = 1;
            var account = "**********";
            var ticketNumber = 1001;
            const string mock = "Mocks";
            var actor = new RestAPISpawnerActor(new CashierMocks(), string.Empty, mock, mock);
            actor.PerformCmd("general", @$"{{
                accounts = guardian.Accounts();
                accounts.Create(false, 1, 'Consignment_P2PTransfer_Deposit_USD', 'C_P_D_USD_1', 'USD');
                accounts.Create(false, 2, 'Consignment_P2PTransfer_Withdrawal_USD', 'C_P_W_USD_2', 'USD');
                accounts.Create(false, 3, 'Artemis_ThirdParty_Deposit_USD', 'A_T_D_USD_3', 'USD');
            }}
            ");

            actor.PerformCmd(account, @$"{{
                balance = atAddress.CreateAccountIfNotExists('{Currencies.CODES.USD}', '{Currencies.CODES.USD}').Balance;
	            store = company.Sales.StoreById({storeId});
	            balance.Accredit(itIsThePresent, Now, Currency('{Currencies.CODES.USD}', 1), 'ME', '{ticketNumber}', store, '', '');
	            authorization = atAddress.CreateAuthorization(itIsThePresent, Now, Currency('{Currencies.CODES.USD}', 1), {ticketNumber}, store, '', '', '{Currencies.CODES.USD}');
            }}
            ");
            actor.PerformCmd(account, @$"{{
                authorization = atAddress.GetAuthorization({ticketNumber});
	            authorization.CreateFragments(1, 1);
	            authorization.AddFragments(1, 1, {{'1000001-1'}}, {{'Lotto OH Midday 12:14PM Pick 3 07/06/2022 (1-1-1) BOX $1.00 ticket for $900.00 prize'}}, 1, 899);
            }}
            ");

            //TODO disparar el docker-compose  cashier asi stub
            Variables variables = new Variables();
            DateTime now = DateTime.Now;
            CustomSettings cs = new CustomSettings(variables);
            
            var driver = new Grade();
            driver.ConfigureThenPrepare(now, cs);
            var msg = "￼￻1￻Bart￻1￻1￻False￻2019￻4￻17￻￼￺**********￺1001￺USD￺1￺1￺149.5￺0￺L";
            FragmentPaymentMessages payFragments = new FragmentPaymentMessages(msg);
            using (RecordSet recordSet = driver.CustomSettings.GetRecordSet())
            {
                recordSet.SetParameter("actor", actor);
                recordSet.SetParameter("who", "Cris5");
                recordSet.SetParameter("payFragments", payFragments);
                recordSet.SetParameter("gradeFreeFormWagers", payFragments.MessagesBy(Coinage.Coin(Currencies.CODES.USD)));
                recordSet.SetParameter("processorKey", "Artemis_ThirdParty_Deposit_USD");
                var result = driver.Execute<GradeFreeFormWagersResponse>(now, recordSet);
            }
        }


        class TestHeaderDictionary : IHeaderDictionary
        {
            private Dictionary<string, StringValues> dic = new Dictionary<string, StringValues>();
            public StringValues this[string key] { get => dic[key]; set => dic[key]=value; }

            public long? ContentLength { get => throw new NotImplementedException(); set => throw new NotImplementedException(); }

            public ICollection<string> Keys => throw new NotImplementedException();

            public ICollection<StringValues> Values => throw new NotImplementedException();

            public int Count => throw new NotImplementedException();

            public bool IsReadOnly => throw new NotImplementedException();

            public void Add(string key, StringValues value)
            {
                dic.Add(key, value);
            }

            public void Add(KeyValuePair<string, StringValues> item)
            {
                throw new NotImplementedException();
            }

            public void Clear()
            {
                throw new NotImplementedException();
            }

            public bool Contains(KeyValuePair<string, StringValues> item)
            {
                throw new NotImplementedException();
            }

            public bool ContainsKey(string key)
            {
                throw new NotImplementedException();
            }

            public void CopyTo(KeyValuePair<string, StringValues>[] array, int arrayIndex)
            {
                throw new NotImplementedException();
            }

            public IEnumerator<KeyValuePair<string, StringValues>> GetEnumerator()
            {
                throw new NotImplementedException();
            }

            public bool Remove(string key)
            {
                throw new NotImplementedException();
            }

            public bool Remove(KeyValuePair<string, StringValues> item)
            {
                throw new NotImplementedException();
            }

            public bool TryGetValue(string key, [MaybeNullWhen(false)] out StringValues value)
            {
               return dic.TryGetValue(key, out value);
            }

            IEnumerator IEnumerable.GetEnumerator()
            {
                throw new NotImplementedException();
            }
        }
        internal class TestHttpRequest : HttpRequest
        {
            private TestHeaderDictionary testHeaderDictionary = new TestHeaderDictionary();
            public override HttpContext HttpContext => throw new NotImplementedException();

            public override string Method { get => throw new NotImplementedException(); set => throw new NotImplementedException(); }
            public override string Scheme { get => throw new NotImplementedException(); set => throw new NotImplementedException(); }
            public override bool IsHttps { get => throw new NotImplementedException(); set => throw new NotImplementedException(); }
            public override HostString Host { get => throw new NotImplementedException(); set => throw new NotImplementedException(); }
            public override PathString PathBase { get => throw new NotImplementedException(); set => throw new NotImplementedException(); }
            public override PathString Path { get => throw new NotImplementedException(); set => throw new NotImplementedException(); }
            public override QueryString QueryString { get => throw new NotImplementedException(); set => throw new NotImplementedException(); }
            public override IQueryCollection Query { get => throw new NotImplementedException(); set => throw new NotImplementedException(); }
            public override string Protocol { get => throw new NotImplementedException(); set => throw new NotImplementedException(); }

            public override IHeaderDictionary Headers => testHeaderDictionary;

            public override IRequestCookieCollection Cookies { get => throw new NotImplementedException(); set => throw new NotImplementedException(); }
            public override long? ContentLength { get => throw new NotImplementedException(); set => throw new NotImplementedException(); }
            public override string ContentType { get => throw new NotImplementedException(); set => throw new NotImplementedException(); }
            public override Stream Body { get => throw new NotImplementedException(); set => throw new NotImplementedException(); }

            public override bool HasFormContentType => throw new NotImplementedException();

            public override IFormCollection Form { get => throw new NotImplementedException(); set => throw new NotImplementedException(); }

            public override Task<IFormCollection> ReadFormAsync(CancellationToken cancellationToken = default)
            {
                throw new NotImplementedException();
            }
        }

        internal class TestContext : HttpContext
        {
            private TestHttpRequest testHttpRequest = new TestHttpRequest();
            public override IFeatureCollection Features => throw new NotImplementedException();

            public override HttpRequest Request => testHttpRequest;

            public override HttpResponse Response => throw new NotImplementedException();

            public override ConnectionInfo Connection => throw new NotImplementedException();

            public override WebSocketManager WebSockets => throw new NotImplementedException();

            public override ClaimsPrincipal User { get => throw new NotImplementedException(); set => throw new NotImplementedException(); }
            public override IDictionary<object, object> Items { get => throw new NotImplementedException(); set => throw new NotImplementedException(); }
            public override IServiceProvider RequestServices { get => throw new NotImplementedException(); set => throw new NotImplementedException(); }
            public override CancellationToken RequestAborted { get => throw new NotImplementedException(); set => throw new NotImplementedException(); }
            public override string TraceIdentifier { get => throw new NotImplementedException(); set => throw new NotImplementedException(); }
            public override ISession Session { get => throw new NotImplementedException(); set => throw new NotImplementedException(); }

            public override void Abort()
            {
                throw new NotImplementedException();
            }
        }
    }
}
