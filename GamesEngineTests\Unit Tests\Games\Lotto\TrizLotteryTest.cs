﻿using Betfair.ESASwagger.Model;
using GamesEngine;
using GamesEngine.Bets;
using GamesEngine.Business;
using GamesEngine.Domains;
using GamesEngine.Finance;
using GamesEngine.Games.Lotto;
using GamesEngine.Location;
using GamesEngine.PurchaseOrders;
using GamesEngine.Settings;
using GamesEngine.Time;
using GamesEngine.Tools;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using Nest;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Unit.Games.Tools;
using static GamesEngine.Games.Lotto.Disincentives;
using static GamesEngine.Games.Lotto.Lottery;
using static GamesEngine.Games.Lotto.LotteryTriz;
using static LottoAPI.Controllers.DrawController;

namespace GamesEngineTests.Unit_Tests.Games.Lotto
{
    [TestClass]
    public class TrizLotteryTest
    {
        [TestMethod]
        public void TrizLottery_SetTime_Schedule_Test()
        {
            var now = DateTime.Now;
            Company company = new Company();
            CurrenciesTest.AddCurrencies();
            company.Sales.CreateStore(1, "Test Store").MakeCurrent();

            var trizLottery = company.LotteryGamesPool.TrizLotteryGame.GetLottery();

            trizLottery.Every(hour: 12, minute: 15, dayOfWeek: 0, now, "Admin");
            trizLottery.Every(hour: 12, minute: 15, dayOfWeek: 1, now, "Admin");
            trizLottery.Every(hour: 12, minute: 15, dayOfWeek: 2, now, "Admin");
            trizLottery.Every(hour: 12, minute: 15, dayOfWeek: 3, now, "Admin");
            trizLottery.Every(hour: 12, minute: 15, dayOfWeek: 4, now, "Admin");
            trizLottery.Every(hour: 12, minute: 15, dayOfWeek: 5, now, "Admin");
            trizLottery.Every(hour: 12, minute: 15, dayOfWeek: 6, now, "Admin");

            GamesEngine.Time.Schedule schedule = trizLottery.GetSchedule(hour: 12, minute: 15);
            schedule.UpdateDescription(itIsThePresent: false, "Triz Midday 12:15 PM");
        }

        [TestMethod]
        public void TrizLottery_And_PickLottery_InternalSchedule_Test()
        {
            var now = DateTime.Now;
            Company company = new Company();
            CurrenciesTest.AddCurrencies();
            company.Sales.CreateStore(1, "Test Store").MakeCurrent();

            var trizLottery = company.LotteryGamesPool.TrizLotteryGame.GetLottery();
            trizLottery.Every(hour: 10, minute: 00, dayOfWeek: 0, now, "Admin");
            trizLottery.Every(hour: 10, minute: 00, dayOfWeek: 1, now, "Admin");
            trizLottery.Every(hour: 20, minute: 00, dayOfWeek: 0, now, "Admin");
            trizLottery.Every(hour: 20, minute: 00, dayOfWeek: 1, now, "Admin");

            State s = company.LotteryGamesPool.PicksLotteryGame.GetOrCreateState("GA", "Georgia", now, "Bart");
            LotteryPick<Pick2> lotteryPick2 = (LotteryPick<Pick2>)company.LotteryGamesPool.PicksLotteryGame.GetOrCreateLottery(2, s);
            lotteryPick2.Every(hour: 10, minute: 00, dayOfWeek: 0, now, "Admin");
            lotteryPick2.Every(hour: 10, minute: 00, dayOfWeek: 1, now, "Admin");
            lotteryPick2.Every(hour: 20, minute: 00, dayOfWeek: 0, now, "Admin");
            lotteryPick2.Every(hour: 20, minute: 00, dayOfWeek: 1, now, "Admin");

            DateTime drawDate = new DateTime(2024, 11, 18, 10, 00, 00);
            DateTime newDrawDate = new DateTime(2024, 11, 18, 15, 00, 00);
            trizLottery.AttachToDate(drawDate, newDrawDate, now, "N/A");
            lotteryPick2.AttachToDate(drawDate, newDrawDate, now, "N/A");

            drawDate = new DateTime(2024, 11, 18, 20, 00, 00);
            newDrawDate = new DateTime(2024, 11, 18, 15, 00, 00);
            trizLottery.AttachToDate(drawDate, newDrawDate, now, "N/A");
            lotteryPick2.AttachToDate(drawDate, newDrawDate, now, "N/A");

            DateTime lastVersionOfDate = new DateTime(2024, 11, 17, 23, 59, 59);
            var schedules = company.LotteryGamesPool.PendingAndNoRegradedSchedulesAt(lastVersionOfDate);

            var schedule = schedules.ElementAt(0);
            DateTime scheduleDrawDate = schedule.ToDateTime(lastVersionOfDate);
            Assert.AreEqual(new DateTime(2024, 11, 17, 10, 00, 00), scheduleDrawDate);

            schedule = schedules.ElementAt(1);
            scheduleDrawDate = schedule.ToDateTime(lastVersionOfDate);
            Assert.AreEqual(new DateTime(2024, 11, 17, 10, 00, 00), scheduleDrawDate);

            schedule = schedules.ElementAt(2);
            scheduleDrawDate = schedule.ToDateTime(lastVersionOfDate);
            Assert.AreEqual(new DateTime(2024, 11, 17, 20, 00, 00), scheduleDrawDate);

            schedule = schedules.ElementAt(3);
            scheduleDrawDate = schedule.ToDateTime(lastVersionOfDate);
            Assert.AreEqual(new DateTime(2024, 11, 17, 20, 00, 00), scheduleDrawDate);

            lastVersionOfDate = new DateTime(2024, 11, 18, 23, 59, 59);
            schedules = company.LotteryGamesPool.PendingAndNoRegradedSchedulesAt(lastVersionOfDate);

            schedule = schedules.ElementAt(0);
            scheduleDrawDate = schedule.ToDateTime(lastVersionOfDate);
            Assert.AreEqual(new DateTime(2024, 11, 18, 15, 00, 00), scheduleDrawDate);

            schedule = schedules.ElementAt(1);
            scheduleDrawDate = schedule.ToDateTime(lastVersionOfDate);
            Assert.AreEqual(new DateTime(2024, 11, 18, 15, 00, 00), scheduleDrawDate);

            schedule = schedules.ElementAt(2);
            scheduleDrawDate = schedule.ToDateTime(lastVersionOfDate);
            Assert.AreEqual(new DateTime(2024, 11, 18, 15, 00, 00), scheduleDrawDate);

            schedule = schedules.ElementAt(3);
            scheduleDrawDate = schedule.ToDateTime(lastVersionOfDate);
            Assert.AreEqual(new DateTime(2024, 11, 18, 15, 00, 00), scheduleDrawDate);

            schedule = trizLottery.FindScheduleAt(new DateTime(2024, 11, 18, 15, 00, 00));
            bool hasScheduleAtDate = trizLottery.AnyScheduleAt(new DateTime(2024, 11, 18, 15, 00, 00));
            Assert.IsTrue(hasScheduleAtDate);
        }

        [TestMethod]
        public void TrizLottery_And_PickLottery_Test()
        {
            var now = DateTime.Now;
            Company company = new Company();
            CurrenciesTest.AddCurrencies();
            company.Sales.CreateStore(1, "Test Store").MakeCurrent();

            var trizLottery = company.LotteryGamesPool.TrizLotteryGame.GetLottery();

            var lotteries = company.Lotto900();
            State s = lotteries.GetOrCreateState("GA", "Georgia", now, "Bart");
            LotteryPick<Pick2> lotteryPick2 = (LotteryPick<Pick2>)lotteries.GetOrCreateLottery(2, s);

            Assert.IsTrue(lotteryPick2 == lotteryPick2.RootLottery);
            Assert.IsTrue(trizLottery == trizLottery.RootLottery);

            LotteryForPicks trizPick2 = trizLottery.GetLotteryPick(2, ending: false);
            Assert.IsTrue(trizLottery == trizPick2.RootLottery);

            LotteryForPicks trizPick3 = trizLottery.GetLotteryPick(3);
            Assert.IsTrue(trizLottery == trizPick3.RootLottery);

            LotteryForPicks trizPick4 = trizLottery.GetLotteryPick(4);
            Assert.IsTrue(trizLottery == trizPick4.RootLottery);

            LotteryForPicks trizPick5 = trizLottery.GetLotteryPick(5);
            Assert.IsTrue(trizLottery == trizPick5.RootLottery);

            Assert.IsTrue(TypeNumberSequence.BEGINNING == trizPick2.GetTypeNumberSequence());

            Assert.IsTrue(TypeNumberSequence.ENDING == trizPick3.GetTypeNumberSequence());
        }

        [TestMethod]
        public void TrizLottery_OfficialLinks_Test()
        {
            Company company = new Company();
            CurrenciesTest.AddCurrencies();
            company.Sales.CreateStore(1, "Test Store").MakeCurrent();

            TrizLotteryGame trizLotteryGame = company.LotteryGamesPool.TrizLotteryGame;
            var trizLottery = trizLotteryGame.GetLottery();

            trizLotteryGame.OfficialLinks.Update(trizLottery.State, "https://www.triz.com.mx");

            var url = trizLotteryGame.OfficialLinks.Url(trizLottery.State);
            Assert.AreEqual("https://www.triz.com.mx", url);
        }

        [TestMethod]
        public void TrizLottery_PendingDraws_Test()
        {
            Company company = new Company();
            CurrenciesTest.AddCurrencies();
            company.Sales.CreateStore(1, "Test Store").MakeCurrent();

            TrizLotteryGame trizLotteryGame = company.LotteryGamesPool.TrizLotteryGame;

            DateTime lastVersionOfDate = new DateTime(2024, 10, 01, 23, 59, 59);
            var schedules = trizLotteryGame.PendingAndNoRegradedSchedulesAt(lastVersionOfDate);

            foreach (var scheduledLotteries in schedules)
            {
                var lottery = scheduledLotteries.Lottery;
                var gameType = lottery.GameType();
                bool result = (gameType == "LotteryKeno" || gameType == "LotteryPowerball" || gameType == "LotteryTriz");
                Assert.IsTrue(result);
            }
        }

        [TestMethod]
        public void TrizLottery_Purchase_TicketPick2_Test()
        {
            Company company = new Company();
            CurrenciesTest.AddCurrencies();
            company.Sales.CreateStore(1, "Test Store").MakeCurrent();

            Product ticketPickProduct = company.LotteryGamesPool.GetOrCreateProductById(5);
            ticketPickProduct.Description = "Ticket pick 2 straight";
            ticketPickProduct.Price = 1;

            DateTime now = new DateTime(2024, 10, 04, 00, 00, 00);
            company.LotteryGamesPool.TrizLotteryGame.GetLottery().Every(hour: 12, minute: 15, dayOfWeek: 5, now, "Admin");

            Domain domain = company.Sales.CreateDomain(false, 1, "localhost", PaymentChannels.Agents.INSIDER);
            Player player = company.GetOrCreateCustomerById("*********").Player;
            Customer customer = company.CustomerByPlayer(player);
            customer.Identifier = "*********";

            int orderNumber = 301952;
            var myOrder = company.NewOrder(customer, orderNumber, Currencies.CODES.USD);

            string gameType = "triz";
            string dates = "10/04/2024";
            string drawHours = "12:15 PM";
            string strIsPresentPlayerBeforeToCloseStore = "False";
            string strUseNextDate = "False";
            string states = "MX";
            string withFireBalls = "False";
            DateTime purchaseDate = new DateTime(2024, 10, 04, 13, 24, 54);
            company.LotteryGamesPool.TrizLotteryGame.NextDatesAccumulator.Calculate(domain, purchaseDate, gameType, dates, states, drawHours, withFireBalls, strIsPresentPlayerBeforeToCloseStore, strUseNextDate);

            var nextDatesAccumulator = company.LotteryGamesPool.TrizLotteryGame.NextDatesAccumulator;
            var numbers = new List<string> { "88" };
            myOrder = company.PrepareTicketOrderWithMultipleAuthorization(player, "MultipleInputSingleAmount", "S", (OrderCart)myOrder, nextDatesAccumulator, domain).Add(numbers, states, false, 1).Commit();
            company.AddOrders((OrderCart)myOrder);

            int lowReference = company.IdentitytBetNumber;
            Assert.AreEqual(1000001, lowReference);

            int highReference = (lowReference + myOrder.CountOfItems - 1);
            Assert.AreEqual(1000001, highReference);

            var auths = new AuthorizationsNumbers(*********);
            company.PurchaseTickets(itIsThePresent: false, (OrderCart)myOrder, purchaseDate, auths, domain, lowReference, orderNumber, "Artemis_ThirdParty_Deposit_USD");
        }

        [TestMethod]
        public void TrizLottery_Purchase_TicketPick3_Test()
        {
            Company company = new Company();
            CurrenciesTest.AddCurrencies();
            company.Sales.CreateStore(1, "Test Store").MakeCurrent();

            Product ticketPickProduct = company.LotteryGamesPool.GetOrCreateProductById(1);
            ticketPickProduct.Description = "Ticket pick 3 straight";
            ticketPickProduct.Price = 1;

            DateTime now = new DateTime(2024, 10, 04, 00, 00, 00);
            company.LotteryGamesPool.TrizLotteryGame.GetLottery().Every(hour: 12, minute: 15, dayOfWeek: 5, now, "Admin");

            Domain domain = company.Sales.CreateDomain(false, 1, "localhost", PaymentChannels.Agents.INSIDER);
            Player player = company.GetOrCreateCustomerById("*********").Player;
            Customer customer = company.CustomerByPlayer(player);
            customer.Identifier = "*********";

            //    orderNumber = 301952;
            int orderNumber = 301952;

            //    myOrder = company.NewOrder(customer, orderNumber, USD);
            var myOrder = company.NewOrder(customer, orderNumber, Currencies.CODES.USD);

            //    lotto900.NextDatesAccumulator.Calculate(domain, 10 / 04 / 2024 13:24:54, '3', '10/04/2024', 'CA', '3:45 PM', 'False');
            string gameType = "triz";
            string dates = "10/04/2024";
            string drawHours = "12:15 PM";
            string strIsPresentPlayerBeforeToCloseStore = "False";
            string strUseNextDate = "False";
            string states = "MX";
            string withFireBalls = "False";
            DateTime purchaseDate = new DateTime(2024, 10, 04, 13, 24, 54);
            company.LotteryGamesPool.TrizLotteryGame.NextDatesAccumulator.Calculate(domain, purchaseDate, gameType, dates, states, drawHours, withFireBalls, strIsPresentPlayerBeforeToCloseStore, strUseNextDate);

            //    myOrder = company.PrepareTicketOrderWithMultipleAuthorization(player, 'MultipleInputSingleAmount', 'S', myOrder, lotto900.NextDatesAccumulator, domain).Add({ '888'}, 'CA', false, 1).Commit();
            var nextDatesAccumulator = company.LotteryGamesPool.TrizLotteryGame.NextDatesAccumulator;
            var numbers = new List<string> { "888" };
            myOrder = company.PrepareTicketOrderWithMultipleAuthorization(player, "MultipleInputSingleAmount", "S", (OrderCart)myOrder, nextDatesAccumulator, domain).Add(numbers, states, false, 1).Commit();

            //    company.AddOrders(myOrder);
            company.AddOrders((OrderCart)myOrder);

            //    lowReference = company.IdentitytBetNumber;
            int lowReference = company.IdentitytBetNumber;
            Assert.AreEqual(1000001, lowReference);

            //    highReference = (lowReference + myOrder.CountOfItems - 1);
            int highReference = (lowReference + myOrder.CountOfItems - 1);
            Assert.AreEqual(1000001, highReference);

            //    auths = AuthorizationsNumbers(*********);
            var auths = new AuthorizationsNumbers(*********);

            //    company.PurchaseTickets(itIsThePresent, myOrder, 10 / 04 / 2024 13:24:54, auths, domain, lowReference, orderNumber, 'Artemis_ThirdParty_Deposit_USD');
            company.PurchaseTickets(itIsThePresent: false, (OrderCart)myOrder, purchaseDate, auths, domain, lowReference, orderNumber, "Artemis_ThirdParty_Deposit_USD");
        }

        [TestMethod]
        public void TrizLottery_Purchase_TicketPick4_Test()
        {
            Company company = new Company();
            CurrenciesTest.AddCurrencies();
            company.Sales.CreateStore(1, "Test Store").MakeCurrent();

            Product ticketPickProduct = company.LotteryGamesPool.GetOrCreateProductById(2);
            ticketPickProduct.Description = "Ticket pick 4 straight";
            ticketPickProduct.Price = 1;

            DateTime now = new DateTime(2024, 10, 04, 00, 00, 00);
            company.LotteryGamesPool.TrizLotteryGame.GetLottery().Every(hour: 12, minute: 15, dayOfWeek: 5, now, "Admin");

            Domain domain = company.Sales.CreateDomain(false, 1, "localhost", PaymentChannels.Agents.INSIDER);
            Player player = company.GetOrCreateCustomerById("*********").Player;
            Customer customer = company.CustomerByPlayer(player);
            customer.Identifier = "*********";

            int orderNumber = 301952;
            var myOrder = company.NewOrder(customer, orderNumber, Currencies.CODES.USD);

            string gameType = "triz";
            string dates = "10/04/2024";
            string drawHours = "12:15 PM";
            string strIsPresentPlayerBeforeToCloseStore = "False";
            string strUseNextDate = "False";
            string states = "MX";
            string withFireBalls = "False";
            DateTime purchaseDate = new DateTime(2024, 10, 04, 13, 24, 54);
            company.LotteryGamesPool.TrizLotteryGame.NextDatesAccumulator.Calculate(domain, purchaseDate, gameType, dates, states, drawHours, withFireBalls, strIsPresentPlayerBeforeToCloseStore, strUseNextDate);

            var nextDatesAccumulator = company.LotteryGamesPool.TrizLotteryGame.NextDatesAccumulator;
            var numbers = new List<string> { "8888" };
            myOrder = company.PrepareTicketOrderWithMultipleAuthorization(player, "MultipleInputSingleAmount", "S", (OrderCart)myOrder, nextDatesAccumulator, domain).Add(numbers, states, false, 1).Commit();
            company.AddOrders((OrderCart)myOrder);

            int lowReference = company.IdentitytBetNumber;
            Assert.AreEqual(1000001, lowReference);

            int highReference = (lowReference + myOrder.CountOfItems - 1);
            Assert.AreEqual(1000001, highReference);

            var auths = new AuthorizationsNumbers(*********);
            company.PurchaseTickets(itIsThePresent: false, (OrderCart)myOrder, purchaseDate, auths, domain, lowReference, orderNumber, "Artemis_ThirdParty_Deposit_USD");
        }

        [TestMethod]
        public void TrizLottery_Purchase_TicketPick5_Test()
        {
            Company company = new Company();
            CurrenciesTest.AddCurrencies();
            company.Sales.CreateStore(1, "Test Store").MakeCurrent();

            Product ticketPickProduct = company.LotteryGamesPool.GetOrCreateProductById(7);
            ticketPickProduct.Description = "Ticket pick 5 straight";
            ticketPickProduct.Price = 1;

            DateTime now = new DateTime(2024, 10, 04, 00, 00, 00);
            company.LotteryGamesPool.TrizLotteryGame.GetLottery().Every(hour: 12, minute: 15, dayOfWeek: 5, now, "Admin");

            Domain domain = company.Sales.CreateDomain(false, 1, "localhost", PaymentChannels.Agents.INSIDER);
            Player player = company.GetOrCreateCustomerById("*********").Player;
            Customer customer = company.CustomerByPlayer(player);
            customer.Identifier = "*********";

            int orderNumber = 301952;
            var myOrder = company.NewOrder(customer, orderNumber, Currencies.CODES.USD);

            string gameType = "triz";
            string dates = "10/04/2024";
            string drawHours = "12:15 PM";
            string strIsPresentPlayerBeforeToCloseStore = "False";
            string strUseNextDate = "False";
            string states = "MX";
            string withFireBalls = "False";
            DateTime purchaseDate = new DateTime(2024, 10, 04, 13, 24, 54);
            company.LotteryGamesPool.TrizLotteryGame.NextDatesAccumulator.Calculate(domain, purchaseDate, gameType, dates, states, drawHours, withFireBalls, strIsPresentPlayerBeforeToCloseStore, strUseNextDate);

            var nextDatesAccumulator = company.LotteryGamesPool.TrizLotteryGame.NextDatesAccumulator;
            var numbers = new List<string> { "88888" };
            myOrder = company.PrepareTicketOrderWithMultipleAuthorization(player, "MultipleInputSingleAmount", "S", (OrderCart)myOrder, nextDatesAccumulator, domain).Add(numbers, states, false, 1).Commit();
            company.AddOrders((OrderCart)myOrder);

            int lowReference = company.IdentitytBetNumber;
            Assert.AreEqual(1000001, lowReference);

            int highReference = (lowReference + myOrder.CountOfItems - 1);
            Assert.AreEqual(1000001, highReference);

            var auths = new AuthorizationsNumbers(*********);
            company.PurchaseTickets(itIsThePresent: false, (OrderCart)myOrder, purchaseDate, auths, domain, lowReference, orderNumber, "Artemis_ThirdParty_Deposit_USD");
        }

        [TestMethod]
        public void TrizLottery_Purchase_TicketPick2_Boxed_Test()
        {
            Company company = new Company();
            CurrenciesTest.AddCurrencies();
            company.Sales.CreateStore(1, "Test Store").MakeCurrent();

            Product ticketPickProduct = company.LotteryGamesPool.GetOrCreateProductById(6);
            ticketPickProduct.Description = "Ticket pick 2 boxed";
            ticketPickProduct.Price = 1;

            DateTime now = new DateTime(2024, 10, 04, 00, 00, 00);
            company.LotteryGamesPool.TrizLotteryGame.GetLottery().Every(hour: 12, minute: 15, dayOfWeek: 5, now, "Admin");

            Domain domain = company.Sales.CreateDomain(false, 1, "localhost", PaymentChannels.Agents.INSIDER);
            Player player = company.GetOrCreateCustomerById("*********").Player;
            Customer customer = company.CustomerByPlayer(player);
            customer.Identifier = "*********";

            int orderNumber = 301952;
            var myOrder = company.NewOrder(customer, orderNumber, Currencies.CODES.USD);

            string gameType = "triz";
            string dates = "10/04/2024";
            string drawHours = "12:15 PM";
            string strIsPresentPlayerBeforeToCloseStore = "False";
            string strUseNextDate = "False";
            string states = "MX";
            string withFireBalls = "False";
            DateTime purchaseDate = new DateTime(2024, 10, 04, 13, 24, 54);
            company.LotteryGamesPool.TrizLotteryGame.NextDatesAccumulator.Calculate(domain, purchaseDate, gameType, dates, states, drawHours, withFireBalls, strIsPresentPlayerBeforeToCloseStore, strUseNextDate);

            var nextDatesAccumulator = company.LotteryGamesPool.TrizLotteryGame.NextDatesAccumulator;
            var numbers = new List<string> { "88" };
            myOrder = company.PrepareTicketOrderWithMultipleAuthorization(player, "MultipleInputSingleAmount", "B", (OrderCart)myOrder, nextDatesAccumulator, domain).Add(numbers, states, false, 1).Commit();
            company.AddOrders((OrderCart)myOrder);

            int lowReference = company.IdentitytBetNumber;
            Assert.AreEqual(1000001, lowReference);

            int highReference = (lowReference + myOrder.CountOfItems - 1);
            Assert.AreEqual(1000001, highReference);

            var auths = new AuthorizationsNumbers(*********);
            company.PurchaseTickets(itIsThePresent: false, (OrderCart)myOrder, purchaseDate, auths, domain, lowReference, orderNumber, "Artemis_ThirdParty_Deposit_USD");
        }

        [TestMethod]
        public void TrizLottery_Purchase_TicketPick3_Boxed_Test()
        {
            Company company = new Company();
            CurrenciesTest.AddCurrencies();
            company.Sales.CreateStore(1, "Test Store").MakeCurrent();

            Product ticketPickProduct = company.LotteryGamesPool.GetOrCreateProductById(3);
            ticketPickProduct.Description = "Ticket pick 3 boxed";
            ticketPickProduct.Price = 1;

            DateTime now = new DateTime(2024, 10, 04, 00, 00, 00);
            company.LotteryGamesPool.TrizLotteryGame.GetLottery().Every(hour: 12, minute: 15, dayOfWeek: 5, now, "Admin");

            Domain domain = company.Sales.CreateDomain(false, 1, "localhost", PaymentChannels.Agents.INSIDER);
            Player player = company.GetOrCreateCustomerById("*********").Player;
            Customer customer = company.CustomerByPlayer(player);
            customer.Identifier = "*********";

            int orderNumber = 301952;
            var myOrder = company.NewOrder(customer, orderNumber, Currencies.CODES.USD);

            string gameType = "triz";
            string dates = "10/04/2024";
            string drawHours = "12:15 PM";
            string strIsPresentPlayerBeforeToCloseStore = "False";
            string strUseNextDate = "False";
            string states = "MX";
            string withFireBalls = "False";
            DateTime purchaseDate = new DateTime(2024, 10, 04, 13, 24, 54);
            company.LotteryGamesPool.TrizLotteryGame.NextDatesAccumulator.Calculate(domain, purchaseDate, gameType, dates, states, drawHours, withFireBalls, strIsPresentPlayerBeforeToCloseStore, strUseNextDate);

            var nextDatesAccumulator = company.LotteryGamesPool.TrizLotteryGame.NextDatesAccumulator;
            var numbers = new List<string> { "888" };
            myOrder = company.PrepareTicketOrderWithMultipleAuthorization(player, "MultipleInputSingleAmount", "B", (OrderCart)myOrder, nextDatesAccumulator, domain).Add(numbers, states, false, 1).Commit();
            company.AddOrders((OrderCart)myOrder);

            int lowReference = company.IdentitytBetNumber;
            Assert.AreEqual(1000001, lowReference);

            int highReference = (lowReference + myOrder.CountOfItems - 1);
            Assert.AreEqual(1000001, highReference);

            var auths = new AuthorizationsNumbers(*********);
            company.PurchaseTickets(itIsThePresent: false, (OrderCart)myOrder, purchaseDate, auths, domain, lowReference, orderNumber, "Artemis_ThirdParty_Deposit_USD");
        }

        [TestMethod]
        public void TrizLottery_Purchase_TicketPick4_Boxed_Test()
        {
            Company company = new Company();
            CurrenciesTest.AddCurrencies();
            company.Sales.CreateStore(1, "Test Store").MakeCurrent();

            Product ticketPickProduct = company.LotteryGamesPool.GetOrCreateProductById(4);
            ticketPickProduct.Description = "Ticket pick 4 boxed";
            ticketPickProduct.Price = 1;

            DateTime now = new DateTime(2024, 10, 04, 00, 00, 00);
            company.LotteryGamesPool.TrizLotteryGame.GetLottery().Every(hour: 12, minute: 15, dayOfWeek: 5, now, "Admin");

            Domain domain = company.Sales.CreateDomain(false, 1, "localhost", PaymentChannels.Agents.INSIDER);
            Player player = company.GetOrCreateCustomerById("*********").Player;
            Customer customer = company.CustomerByPlayer(player);
            customer.Identifier = "*********";

            int orderNumber = 301952;
            var myOrder = company.NewOrder(customer, orderNumber, Currencies.CODES.USD);

            string gameType = "triz";
            string dates = "10/04/2024";
            string drawHours = "12:15 PM";
            string strIsPresentPlayerBeforeToCloseStore = "False";
            string strUseNextDate = "False";
            string states = "MX";
            string withFireBalls = "False";
            DateTime purchaseDate = new DateTime(2024, 10, 04, 13, 24, 54);
            company.LotteryGamesPool.TrizLotteryGame.NextDatesAccumulator.Calculate(domain, purchaseDate, gameType, dates, states, drawHours, withFireBalls, strIsPresentPlayerBeforeToCloseStore, strUseNextDate);

            var nextDatesAccumulator = company.LotteryGamesPool.TrizLotteryGame.NextDatesAccumulator;
            var numbers = new List<string> { "8888" };
            myOrder = company.PrepareTicketOrderWithMultipleAuthorization(player, "MultipleInputSingleAmount", "B", (OrderCart)myOrder, nextDatesAccumulator, domain).Add(numbers, states, false, 1).Commit();
            company.AddOrders((OrderCart)myOrder);

            int lowReference = company.IdentitytBetNumber;
            Assert.AreEqual(1000001, lowReference);

            int highReference = (lowReference + myOrder.CountOfItems - 1);
            Assert.AreEqual(1000001, highReference);

            var auths = new AuthorizationsNumbers(*********);
            company.PurchaseTickets(itIsThePresent: false, (OrderCart)myOrder, purchaseDate, auths, domain, lowReference, orderNumber, "Artemis_ThirdParty_Deposit_USD");
        }

        [TestMethod]
        public void TrizLottery_Purchase_TicketPick5_Boxed_Test()
        {
            Company company = new Company();
            CurrenciesTest.AddCurrencies();
            company.Sales.CreateStore(1, "Test Store").MakeCurrent();

            Product ticketPickProduct = company.LotteryGamesPool.GetOrCreateProductById(8);
            ticketPickProduct.Description = "Ticket pick 5 boxed";
            ticketPickProduct.Price = 1;

            DateTime now = new DateTime(2024, 10, 04, 00, 00, 00);
            company.LotteryGamesPool.TrizLotteryGame.GetLottery().Every(hour: 12, minute: 15, dayOfWeek: 5, now, "Admin");

            Domain domain = company.Sales.CreateDomain(false, 1, "localhost", PaymentChannels.Agents.INSIDER);
            Player player = company.GetOrCreateCustomerById("*********").Player;
            Customer customer = company.CustomerByPlayer(player);
            customer.Identifier = "*********";

            int orderNumber = 301952;
            var myOrder = company.NewOrder(customer, orderNumber, Currencies.CODES.USD);

            string gameType = "triz";
            string dates = "10/04/2024";
            string drawHours = "12:15 PM";
            string strIsPresentPlayerBeforeToCloseStore = "False";
            string strUseNextDate = "False";
            string states = "MX";
            string withFireBalls = "False";
            DateTime purchaseDate = new DateTime(2024, 10, 04, 13, 24, 54);
            company.LotteryGamesPool.TrizLotteryGame.NextDatesAccumulator.Calculate(domain, purchaseDate, gameType, dates, states, drawHours, withFireBalls, strIsPresentPlayerBeforeToCloseStore, strUseNextDate);

            var nextDatesAccumulator = company.LotteryGamesPool.TrizLotteryGame.NextDatesAccumulator;
            var numbers = new List<string> { "88888" };
            myOrder = company.PrepareTicketOrderWithMultipleAuthorization(player, "MultipleInputSingleAmount", "B", (OrderCart)myOrder, nextDatesAccumulator, domain).Add(numbers, states, false, 1).Commit();
            company.AddOrders((OrderCart)myOrder);

            int lowReference = company.IdentitytBetNumber;
            Assert.AreEqual(1000001, lowReference);

            int highReference = (lowReference + myOrder.CountOfItems - 1);
            Assert.AreEqual(1000001, highReference);

            var auths = new AuthorizationsNumbers(*********);
            company.PurchaseTickets(itIsThePresent: false, (OrderCart)myOrder, purchaseDate, auths, domain, lowReference, orderNumber, "Artemis_ThirdParty_Deposit_USD");
        }

        [TestMethod]
        public void TrizLottery_Purchase_2Ticket_Test()
        {
            Company company = new Company();
            CurrenciesTest.AddCurrencies();
            company.Sales.CreateStore(1, "Test Store").MakeCurrent();

            Product ticketPick3S = company.LotteryGamesPool.GetOrCreateProductById(1);
            ticketPick3S.Description = "Ticket pick 3 boxed";
            ticketPick3S.Price = 1;

            DateTime now = new DateTime(2024, 10, 04, 00, 00, 00);
            company.LotteryGamesPool.TrizLotteryGame.GetLottery().Every(hour: 12, minute: 15, dayOfWeek: 5, now, "Admin");

            Domain domain = company.Sales.CreateDomain(false, 1, "localhost", PaymentChannels.Agents.INSIDER);
            Player player = company.GetOrCreateCustomerById("*********").Player;
            Customer customer = company.CustomerByPlayer(player);
            customer.Identifier = "*********";

            int orderNumber = 301952;
            var myOrder = company.NewOrder(customer, orderNumber, Currencies.CODES.USD);

            string gameType = "triz";
            string dates = "10/04/2024";
            string drawHours = "12:15 PM,12:15 PM";
            string strIsPresentPlayerBeforeToCloseStore = "False,False";
            string strUseNextDate = "False,False";
            string states = "MX,MX";
            string withFireBalls = "False,False";
            DateTime purchaseDate = new DateTime(2024, 10, 04, 13, 24, 54);
            company.LotteryGamesPool.TrizLotteryGame.NextDatesAccumulator.Calculate(domain, purchaseDate, gameType, dates, states, drawHours, withFireBalls, strIsPresentPlayerBeforeToCloseStore, strUseNextDate);

            var nextDatesAccumulator = company.LotteryGamesPool.TrizLotteryGame.NextDatesAccumulator;
            var numbers = new List<string> { "888", "999" };
            myOrder = company.PrepareTicketOrderWithMultipleAuthorization(player, "MultipleInputSingleAmount", "S", (OrderCart)myOrder, nextDatesAccumulator, domain).Add(numbers, "MX", false, 1).Commit();
            company.AddOrders((OrderCart)myOrder);

            int lowReference = company.IdentitytBetNumber;
            Assert.AreEqual(1000001, lowReference);

            int highReference = (lowReference + myOrder.CountOfItems - 1);
            Assert.AreEqual(1000002, highReference);

            var auths = new AuthorizationsNumbers(*********, 2);
            company.PurchaseTickets(itIsThePresent: false, (OrderCart)myOrder, purchaseDate, auths, domain, lowReference, orderNumber, "Artemis_ThirdParty_Deposit_USD");
        }

        [TestMethod]
        public void TrizLottery_Purchase_Ticket_And_Grade_Test()
        {
            QueueInMemory queue = new QueueInMemory();
            Integration.Kafka = queue;

            Company company = new Company();
            CurrenciesTest.AddCurrencies();
            company.Sales.CreateStore(1, "Test Store").MakeCurrent();

            Product ticketPick3S = company.LotteryGamesPool.GetOrCreateProductById(1);
            ticketPick3S.Description = "Ticket pick 3 boxed";
            ticketPick3S.Price = 1;

            DateTime now = new DateTime(2024, 10, 04, 00, 00, 00);
            company.LotteryGamesPool.TrizLotteryGame.GetLottery().Every(hour: 12, minute: 15, dayOfWeek: 5, now, "Admin");

            Domain domain = company.Sales.CreateDomain(false, 1, "localhost", PaymentChannels.Agents.INSIDER);
            Player player = company.GetOrCreateCustomerById("*********").Player;
            Customer customer = company.CustomerByPlayer(player);
            customer.Identifier = "*********";

            int orderNumber = 301952;
            var myOrder = company.NewOrder(customer, orderNumber, Currencies.CODES.USD);

            string gameType = "triz";
            string dates = "10/04/2024";
            string drawHours = "12:15 PM";
            string strIsPresentPlayerBeforeToCloseStore = "False";
            string strUseNextDate = "False";
            string states = "MX";
            string withFireBalls = "False";
            DateTime purchaseDate = new DateTime(2024, 10, 04, 10, 00, 00);
            company.LotteryGamesPool.TrizLotteryGame.NextDatesAccumulator.Calculate(domain, purchaseDate, gameType, dates, states, drawHours, withFireBalls, strIsPresentPlayerBeforeToCloseStore, strUseNextDate);

            var nextDatesAccumulator = company.LotteryGamesPool.TrizLotteryGame.NextDatesAccumulator;
            var numbers = new List<string> { "345" };
            myOrder = company.PrepareTicketOrderWithMultipleAuthorization(player, "MultipleInputSingleAmount", "S", (OrderCart)myOrder, nextDatesAccumulator, domain).Add(numbers, "MX", false, 1).Commit();
            company.AddOrders((OrderCart)myOrder);

            int lowReference = company.IdentitytBetNumber;
            Assert.AreEqual(1000001, lowReference);

            int highReference = (lowReference + myOrder.CountOfItems - 1);
            Assert.AreEqual(1000001, highReference);

            var auths = new AuthorizationsNumbers(*********);
            company.PurchaseTickets(itIsThePresent: false, (OrderCart)myOrder, purchaseDate, auths, domain, lowReference, orderNumber, "Artemis_ThirdParty_Deposit_USD");

            // Gradear lottery
            DateTime gradeDate = new DateTime(2024, 10, 04, 12, 15, 00);
            LotteryTriz lotteryTriz = company.LotteryGamesPool.TrizLotteryGame.GetLottery();
            string winnerNumber = "12345";
            DrawingsSummaryReport drawingsSummaryReport = lotteryTriz.DrawTriz(gradeDate, winnerNumber, now, false, "Erick", false);
            Assert.AreEqual(1, drawingsSummaryReport.TotalWinners);
            Assert.AreEqual(1, drawingsSummaryReport.TotalPlayers);
            Assert.AreEqual(1, drawingsSummaryReport.TotalTickets);
            Assert.AreEqual(1, drawingsSummaryReport.TotalTicketAmount);
            Assert.AreEqual(900, drawingsSummaryReport.TotalPrize);
        }

        [TestMethod]
        public void SequenceOfNumbersOfDrawAt_bug9219()
        {
            QueueInMemory queue = new QueueInMemory();
            Integration.Kafka = queue;

            Company company = new Company();
            CurrenciesTest.AddCurrencies();
            company.Sales.CreateStore(1, "Test Store").MakeCurrent();

            Product ticketPick3S = company.LotteryGamesPool.GetOrCreateProductById(1);
            ticketPick3S.Description = "Ticket pick 3 boxed";
            ticketPick3S.Price = 1;

            DateTime now = new DateTime(2024, 10, 04, 00, 00, 00);
            company.LotteryGamesPool.TrizLotteryGame.GetLottery().Every(hour: 12, minute: 15, dayOfWeek: 5, now, "Admin");

            Domain domain = company.Sales.CreateDomain(false, 1, "localhost", PaymentChannels.Agents.INSIDER);
            Player player = company.GetOrCreateCustomerById("*********").Player;
            Customer customer = company.CustomerByPlayer(player);
            customer.Identifier = "*********";

            var lottery = company.LotteryGamesPool.TrizLotteryGame.GetLottery(3, company.LotteryGamesPool.TrizLotteryGame.States().First());
            var riskProfile = company.LotteryGamesPool.TrizLotteryGame.RiskProfiles.GetRiskProfile(domain);
            var risk = riskProfile.Risks.Risk;
            var riskPerLottery = risk.GetRiskPerLottery(3, lottery);

            int orderNumber = 301952;
            var myOrder = company.NewOrder(customer, orderNumber, Currencies.CODES.USD);

            string gameType = "triz";
            string dates = "10/04/2024";
            string drawHours = "12:15 PM";
            string strIsPresentPlayerBeforeToCloseStore = "False";
            string strUseNextDate = "False";
            string states = "MX";
            string withFireBalls = "False";
            DateTime purchaseDate = new DateTime(2024, 10, 04, 10, 00, 00);
            company.LotteryGamesPool.TrizLotteryGame.NextDatesAccumulator.Calculate(domain, purchaseDate, gameType, dates, states, drawHours, withFireBalls, strIsPresentPlayerBeforeToCloseStore, strUseNextDate);

            var nextDatesAccumulator = company.LotteryGamesPool.TrizLotteryGame.NextDatesAccumulator;
            var numbers = new List<string> { "345" };
            myOrder = company.PrepareTicketOrderWithMultipleAuthorization(player, "MultipleInputSingleAmount", "S", (OrderCart)myOrder, nextDatesAccumulator, domain).Add(numbers, "MX", false, 1).Commit();
            company.AddOrders((OrderCart)myOrder);

            int lowReference = company.IdentitytBetNumber;
            Assert.AreEqual(1000001, lowReference);

            int highReference = (lowReference + myOrder.CountOfItems - 1);
            Assert.AreEqual(1000001, highReference);

            var auths = new AuthorizationsNumbers(*********);
            company.PurchaseTickets(itIsThePresent: false, (OrderCart)myOrder, purchaseDate, auths, domain, lowReference, orderNumber, "Artemis_ThirdParty_Deposit_USD");

            // Grade lottery
            DateTime gradeDate = new DateTime(2024, 10, 04, 12, 15, 00);
            LotteryTriz lotteryTriz = company.LotteryGamesPool.TrizLotteryGame.GetLottery();
            string winnerNumber = "12345";
            DrawingsSummaryReport drawingsSummaryReport = lotteryTriz.DrawTriz(gradeDate, winnerNumber, now, false, "Admin", false);

            var schedule = lotteryTriz.FindScheduleAt(gradeDate);
            var sequenceOfNumbers = lotteryTriz.SequenceOfNumbersOfDrawAt(gradeDate, schedule);
            Assert.AreEqual(winnerNumber, sequenceOfNumbers);

            var pick3Ending = lotteryTriz.GetLotteryPick(3);
            var schedulePick3Ending = pick3Ending.FindScheduleAt(gradeDate);
            var pick3EndingNumbers = pick3Ending.SequenceOfNumbersOfDrawAt(gradeDate, schedule);
            Assert.AreEqual(winnerNumber[2..], pick3EndingNumbers);
        }

        [TestMethod]
        public void Triz_NewDisincentive()
        {
            QueueInMemory queue = new QueueInMemory();
            Integration.Kafka = queue;

            Company company = new Company();
            CurrenciesTest.AddCurrencies();
            company.Sales.CreateStore(1, "Test Store").MakeCurrent();

            DateTime now = new DateTime(2024, 10, 04, 00, 00, 00);
            var trizLotteryGame = company.LotteryGamesPool.TrizLotteryGame;
            trizLotteryGame.GetLottery().Every(hour: 12, minute: 15, dayOfWeek: 5, now, "Admin");

            var disincentives = company.LotteryGamesPool.RiskProfiles.GetRiskProfile("Preset Risk Profile").Risks.Disincentives;
            var allNumbers = disincentives.AllDisincentives(3);
            Assert.IsTrue(allNumbers.Count() == 0);

            var trizLottery = trizLotteryGame.GetLottery();
            var pick3Ending = trizLottery.GetLotteryPick(3);
            var schedule = pick3Ending.GetSchedule(hour: 12, minute: 15);
            Assert.AreEqual(1, pick3Ending.Schedules.Count());

            var schedulePick3Ending = company.LotteryGamesPool.GetInternalSchedule(schedule.UniqueId);
            Assert.AreEqual(schedule, schedulePick3Ending);
            disincentives.NewDisincentive(schedulePick3Ending, PrizeCriteriaIds.PRIZE_CRITERIA_3_WAY, RuleType.Straight, 80);

            allNumbers = disincentives.AllDisincentives(3);
            Assert.IsTrue(allNumbers.Count() == 1);
            var disincentive = allNumbers.First();
            Assert.AreEqual(0.8m, disincentive.DisincentivePercentage);
            Assert.AreEqual(PrizeCriteriaIds.PRIZE_CRITERIA_3_WAY, disincentive.PrizeCriteriaId);
            Assert.AreEqual(RuleType.Straight, disincentive.RuleType);
            Assert.AreEqual(trizLottery.State, disincentive.State);
            Assert.AreEqual(schedulePick3Ending, disincentive.Schedule);
        }

        [TestMethod]
        public void Picks_NewDisincentive()
        {
            QueueInMemory queue = new QueueInMemory();
            Integration.Kafka = queue;

            Company company = new Company();
            CurrenciesTest.AddCurrencies();
            company.Sales.CreateStore(1, "Test Store").MakeCurrent();

            DateTime now = new DateTime(2024, 10, 04, 00, 00, 00);
            var picksLotteryGame = company.LotteryGamesPool.PicksLotteryGame;
            var state = new State("TX", "Texas");
            var picksLottery = picksLotteryGame.GetOrCreateLottery(3, state);
            for (int i = 0; i <= 6; i++) picksLottery.Every(9, 30, i, now, "Game Admin");

            var disincentives = company.LotteryGamesPool.RiskProfiles.GetRiskProfile("Preset Risk Profile").Risks.Disincentives;
            var allNumbers = disincentives.AllDisincentives(3);
            Assert.IsTrue(allNumbers.Count() == 0);
            var schedule = picksLottery.GetSchedule(hour: 9, minute: 30);
            Assert.AreEqual(1, picksLottery.Schedules.Count());

            Assert.IsTrue(picksLotteryGame.ExistsInternalSchedule(schedule.UniqueId));
            var scheduleInternal = company.LotteryGamesPool.GetInternalSchedule(schedule.UniqueId);
            Assert.AreEqual(schedule, scheduleInternal);
            disincentives.NewDisincentive(scheduleInternal, PrizeCriteriaIds.PRIZE_CRITERIA_3_WAY, RuleType.Straight, 80);

            allNumbers = disincentives.AllDisincentives(3);
            Assert.IsTrue(allNumbers.Count() == 1);
            var disincentive = allNumbers.First();
            Assert.AreEqual(0.8m, disincentive.DisincentivePercentage);
            Assert.AreEqual(PrizeCriteriaIds.PRIZE_CRITERIA_3_WAY, disincentive.PrizeCriteriaId);
            Assert.AreEqual(RuleType.Straight, disincentive.RuleType);
            Assert.AreEqual(picksLottery.State, disincentive.State);
            Assert.AreEqual(scheduleInternal, disincentive.Schedule);
        }

        [TestMethod]
        public void TrizLottery_Purchase_Ticket_And_Grade_And_Regrade_Test()
        {
            QueueInMemory queue = new QueueInMemory();
            Integration.Kafka = queue;

            Company company = new Company();
            CurrenciesTest.AddCurrencies();
            company.Sales.CreateStore(1, "Test Store").MakeCurrent();

            Product ticketPick3S = company.LotteryGamesPool.GetOrCreateProductById(1);
            ticketPick3S.Description = "Ticket pick 3 boxed";
            ticketPick3S.Price = 1;

            DateTime now = new DateTime(2024, 10, 04, 00, 00, 00);
            company.LotteryGamesPool.TrizLotteryGame.GetLottery().Every(hour: 12, minute: 15, dayOfWeek: 5, now, "Admin");

            Domain domain = company.Sales.CreateDomain(false, 1, "localhost", PaymentChannels.Agents.INSIDER);
            Player player = company.GetOrCreateCustomerById("*********").Player;
            Customer customer = company.CustomerByPlayer(player);
            customer.Identifier = "*********";

            int orderNumber = 301952;
            var myOrder = company.NewOrder(customer, orderNumber, Currencies.CODES.USD);

            string gameType = "triz";
            string dates = "10/04/2024";
            string drawHours = "12:15 PM";
            string strIsPresentPlayerBeforeToCloseStore = "False";
            string strUseNextDate = "False";
            string states = "MX";
            string withFireBalls = "False";
            DateTime purchaseDate = new DateTime(2024, 10, 04, 10, 00, 00);
            company.LotteryGamesPool.TrizLotteryGame.NextDatesAccumulator.Calculate(domain, purchaseDate, gameType, dates, states, drawHours, withFireBalls, strIsPresentPlayerBeforeToCloseStore, strUseNextDate);

            var nextDatesAccumulator = company.LotteryGamesPool.TrizLotteryGame.NextDatesAccumulator;
            var numbers = new List<string> { "345" };
            myOrder = company.PrepareTicketOrderWithMultipleAuthorization(player, "MultipleInputSingleAmount", "S", (OrderCart)myOrder, nextDatesAccumulator, domain).Add(numbers, "MX", false, 1).Commit();
            company.AddOrders((OrderCart)myOrder);

            int lowReference = company.IdentitytBetNumber;
            Assert.AreEqual(1000001, lowReference);

            int highReference = (lowReference + myOrder.CountOfItems - 1);
            Assert.AreEqual(1000001, highReference);

            var auths = new AuthorizationsNumbers(*********);
            company.PurchaseTickets(itIsThePresent: false, (OrderCart)myOrder, purchaseDate, auths, domain, lowReference, orderNumber, "Artemis_ThirdParty_Deposit_USD");

            DateTime lastVersionOfDate = new DateTime(2024, 10, 04, 23, 59, 59);
            var schedules = company.LotteryGamesPool.TrizLotteryGame.PendingAndNoRegradedSchedulesAt(lastVersionOfDate);
            Assert.IsTrue(schedules.Count() == 1);

            // Gradear lottery
            DateTime gradeDate = new DateTime(2024, 10, 04, 12, 15, 00);
            LotteryTriz lotteryTriz = company.LotteryGamesPool.TrizLotteryGame.GetLottery();
            string winnerNumber = "12345";
            DrawingsSummaryReport drawingsSummaryReport = lotteryTriz.DrawTriz(gradeDate, winnerNumber, now, false, "N/A", false);
            Assert.AreEqual(1, drawingsSummaryReport.TotalWinners);
            Assert.AreEqual(1, drawingsSummaryReport.TotalPlayers);
            Assert.AreEqual(1, drawingsSummaryReport.TotalTickets);
            Assert.AreEqual(1, drawingsSummaryReport.TotalTicketAmount);
            Assert.AreEqual(900, drawingsSummaryReport.TotalPrize);

            lastVersionOfDate = new DateTime(2024, 10, 04, 23, 59, 59);
            schedules = company.LotteryGamesPool.TrizLotteryGame.PendingAndNoRegradedSchedulesAt(lastVersionOfDate);
            Assert.IsTrue(schedules.Count() == 0);

            schedules = company.LotteryGamesPool.TrizLotteryGame.FinishedAndRegradedSchedulesOf(lastVersionOfDate);
            Assert.IsTrue(schedules.Count() == 1);

            schedules = company.LotteryGamesPool.FinishedAndRegradedSchedulesOf(lastVersionOfDate);
            Assert.IsTrue(schedules.Count() == 1);

            bool canBeConfirmed = lotteryTriz.CanBeConfirmed(gradeDate);
            Assert.IsTrue(canBeConfirmed);

            // Re-gradear lottery
            DateTime regradeDate = new DateTime(2024, 10, 04, 12, 15, 00);
            drawingsSummaryReport = lotteryTriz.Regrade(itIsThePresent: false, regradeDate, now, "N/A", false);
            Assert.AreEqual(0, drawingsSummaryReport.TotalWinners);
            Assert.AreEqual(1, drawingsSummaryReport.TotalPlayers);
            Assert.AreEqual(1, drawingsSummaryReport.TotalTickets);
            Assert.AreEqual(1, drawingsSummaryReport.TotalTicketAmount);
            Assert.AreEqual(0, drawingsSummaryReport.TotalPrize);

            // check if the lottery is regreaded
            bool isRegraded = lotteryTriz.IsRegraded(regradeDate);
            Assert.IsTrue(isRegraded);
        }

        [TestMethod]
        public void TrizLottery_Purchase_Ticket_And_Grade_And_NoAction_Test()
        {
            QueueInMemory queue = new QueueInMemory();
            Integration.Kafka = queue;

            Company company = new Company();
            CurrenciesTest.AddCurrencies();
            company.Sales.CreateStore(1, "Test Store").MakeCurrent();

            Product ticketPick3S = company.LotteryGamesPool.GetOrCreateProductById(1);
            ticketPick3S.Description = "Ticket pick 3 boxed";
            ticketPick3S.Price = 1;

            DateTime now = new DateTime(2024, 10, 04, 00, 00, 00);
            company.LotteryGamesPool.TrizLotteryGame.GetLottery().Every(hour: 12, minute: 15, dayOfWeek: 5, now, "Admin");

            Domain domain = company.Sales.CreateDomain(false, 1, "localhost", PaymentChannels.Agents.INSIDER);
            Player player = company.GetOrCreateCustomerById("*********").Player;
            Customer customer = company.CustomerByPlayer(player);
            customer.Identifier = "*********";

            int orderNumber = 301952;
            var myOrder = company.NewOrder(customer, orderNumber, Currencies.CODES.USD);

            string gameType = "triz";
            string dates = "10/04/2024";
            string drawHours = "12:15 PM";
            string strIsPresentPlayerBeforeToCloseStore = "False";
            string strUseNextDate = "False";
            string states = "MX";
            string withFireBalls = "False";
            DateTime purchaseDate = new DateTime(2024, 10, 04, 10, 00, 00);
            company.LotteryGamesPool.TrizLotteryGame.NextDatesAccumulator.Calculate(domain, purchaseDate, gameType, dates, states, drawHours, withFireBalls, strIsPresentPlayerBeforeToCloseStore, strUseNextDate);

            var nextDatesAccumulator = company.LotteryGamesPool.TrizLotteryGame.NextDatesAccumulator;
            var numbers = new List<string> { "345" };
            myOrder = company.PrepareTicketOrderWithMultipleAuthorization(player, "MultipleInputSingleAmount", "S", (OrderCart)myOrder, nextDatesAccumulator, domain).Add(numbers, "MX", false, 1).Commit();
            company.AddOrders((OrderCart)myOrder);

            int lowReference = company.IdentitytBetNumber;
            Assert.AreEqual(1000001, lowReference);

            int highReference = (lowReference + myOrder.CountOfItems - 1);
            Assert.AreEqual(1000001, highReference);

            var auths = new AuthorizationsNumbers(*********);
            company.PurchaseTickets(itIsThePresent: false, (OrderCart)myOrder, purchaseDate, auths, domain, lowReference, orderNumber, "Artemis_ThirdParty_Deposit_USD");

            // Gradear lottery
            DateTime gradeDate = new DateTime(2024, 10, 04, 12, 15, 00);
            LotteryTriz lotteryTriz = company.LotteryGamesPool.TrizLotteryGame.GetLottery();
            string winnerNumber = "12345";
            DrawingsSummaryReport drawingsSummaryReport = lotteryTriz.DrawTriz(gradeDate, winnerNumber, now, false, "N/A", false);
            Assert.AreEqual(1, drawingsSummaryReport.TotalWinners);
            Assert.AreEqual(1, drawingsSummaryReport.TotalPlayers);
            Assert.AreEqual(1, drawingsSummaryReport.TotalTickets);
            Assert.AreEqual(1, drawingsSummaryReport.TotalTicketAmount);
            Assert.AreEqual(900, drawingsSummaryReport.TotalPrize);

            // No action lottery
            DateTime noActionDate = new DateTime(2024, 10, 04, 12, 15, 00);
            NoActionReport noActionReport = lotteryTriz.SetNoAction(itIsThePresent: false, noActionDate, now, "N/A", false);
            Assert.AreEqual(1, noActionReport.TotalTickets);

            // check if the lottery is no action
            bool isNoAction = lotteryTriz.IsNoActionAt(noActionDate);
            Assert.IsTrue(isNoAction);
        }

        [TestMethod]
        public void TrizLottery_Purchase_Ticket_Resend_Test()
        {
            QueueInMemory queue = new QueueInMemory();
            Integration.Kafka = queue;

            Company company = new Company();
            CurrenciesTest.AddCurrencies();
            company.Sales.CreateStore(1, "Test Store").MakeCurrent();

            Product ticketPick3S = company.LotteryGamesPool.GetOrCreateProductById(1);
            ticketPick3S.Description = "Ticket pick 3 boxed";
            ticketPick3S.Price = 1;

            DateTime now = new DateTime(2024, 10, 04, 00, 00, 00);
            company.LotteryGamesPool.TrizLotteryGame.GetLottery().Every(hour: 12, minute: 15, dayOfWeek: 5, now, "Admin");

            Domain domain = company.Sales.CreateDomain(false, 1, "localhost", PaymentChannels.Agents.INSIDER);
            Player player = company.GetOrCreateCustomerById("*********").Player;
            Customer customer = company.CustomerByPlayer(player);
            customer.Identifier = "*********";

            int orderNumber = 301952;
            var myOrder = company.NewOrder(customer, orderNumber, Currencies.CODES.USD);

            string gameType = "triz";
            string dates = "10/04/2024";
            string drawHours = "12:15 PM";
            string strIsPresentPlayerBeforeToCloseStore = "False";
            string strUseNextDate = "False";
            string states = "MX";
            string withFireBalls = "False";
            DateTime purchaseDate = new DateTime(2024, 10, 04, 10, 00, 00);
            company.LotteryGamesPool.TrizLotteryGame.NextDatesAccumulator.Calculate(domain, purchaseDate, gameType, dates, states, drawHours, withFireBalls, strIsPresentPlayerBeforeToCloseStore, strUseNextDate);

            var nextDatesAccumulator = company.LotteryGamesPool.TrizLotteryGame.NextDatesAccumulator;
            var numbers = new List<string> { "345" };
            myOrder = company.PrepareTicketOrderWithMultipleAuthorization(player, "MultipleInputSingleAmount", "S", (OrderCart)myOrder, nextDatesAccumulator, domain).Add(numbers, "MX", false, 1).Commit();
            company.AddOrders((OrderCart)myOrder);

            int lowReference = company.IdentitytBetNumber;
            Assert.AreEqual(1000001, lowReference);

            int highReference = (lowReference + myOrder.CountOfItems - 1);
            Assert.AreEqual(1000001, highReference);

            var auths = new AuthorizationsNumbers(*********);
            company.PurchaseTickets(itIsThePresent: false, (OrderCart)myOrder, purchaseDate, auths, domain, lowReference, orderNumber, "Artemis_ThirdParty_Deposit_USD");

            // Gradear lottery
            DateTime gradeDate = new DateTime(2024, 10, 04, 12, 15, 00);
            LotteryTriz lotteryTriz = company.LotteryGamesPool.TrizLotteryGame.GetLottery();
            string winnerNumber = "12345";
            DrawingsSummaryReport drawingsSummaryReport = lotteryTriz.DrawTriz(gradeDate, winnerNumber, now, false, "N/A", false);
            Assert.AreEqual(1, drawingsSummaryReport.TotalWinners);
            Assert.AreEqual(1, drawingsSummaryReport.TotalPlayers);
            Assert.AreEqual(1, drawingsSummaryReport.TotalTickets);
            Assert.AreEqual(1, drawingsSummaryReport.TotalTicketAmount);
            Assert.AreEqual(900, drawingsSummaryReport.TotalPrize);

            // ExistsPostedDrawInMemory
            bool existsDraw = lotteryTriz.ExistsPostedDrawInMemory(gradeDate);
            Assert.IsTrue(existsDraw);

            // WasAlreadyConfirmed
            bool wasAlreadyConfirmed = lotteryTriz.WasAlreadyConfirmed(gradeDate);
            Assert.IsFalse(wasAlreadyConfirmed);

            // Resend
            var gradeFreeFormWagers = lotteryTriz.CreateMessagesToResendDraw(gradeDate, now);
            Assert.AreEqual(1, gradeFreeFormWagers.Count());
        }

        [TestMethod]
        public void TrizLottery_Purchase_Ticket_Resend_ARTEMIS_Test()
        {
            QueueInMemory queue = new QueueInMemory();
            Integration.Kafka = queue;

            Company company = new Company();
            CurrenciesTest.AddCurrencies();
            company.Sales.CreateStore(1, "Test Store").MakeCurrent();

            Product ticketPick3S = company.LotteryGamesPool.GetOrCreateProductById(1);
            ticketPick3S.Description = "Ticket pick 3 boxed";
            ticketPick3S.Price = 1;

            DateTime now = new DateTime(2024, 10, 04, 00, 00, 00);
            company.LotteryGamesPool.TrizLotteryGame.GetLottery().Every(hour: 12, minute: 15, dayOfWeek: 5, now, "Admin");

            Domain domain = company.Sales.CreateDomain(false, 1, "localhost", PaymentChannels.Agents.INSIDER);
            Player player = company.GetOrCreateCustomerById("*********").Player;
            Customer customer = company.CustomerByPlayer(player);
            customer.Identifier = "*********";

            int orderNumber = 301952;
            var myOrder = company.NewOrder(customer, orderNumber, Currencies.CODES.USD);

            string gameType = "triz";
            string dates = "10/04/2024";
            string drawHours = "12:15 PM";
            string strIsPresentPlayerBeforeToCloseStore = "False";
            string strUseNextDate = "False";
            string states = "MX";
            string withFireBalls = "False";
            DateTime purchaseDate = new DateTime(2024, 10, 04, 10, 00, 00);
            company.LotteryGamesPool.TrizLotteryGame.NextDatesAccumulator.Calculate(domain, purchaseDate, gameType, dates, states, drawHours, withFireBalls, strIsPresentPlayerBeforeToCloseStore, strUseNextDate);

            var nextDatesAccumulator = company.LotteryGamesPool.TrizLotteryGame.NextDatesAccumulator;
            var numbers = new List<string> { "345" };
            myOrder = company.PrepareTicketOrderWithMultipleAuthorization(player, "MultipleInputSingleAmount", "S", (OrderCart)myOrder, nextDatesAccumulator, domain).Add(numbers, "MX", false, 1).Commit();
            company.AddOrders((OrderCart)myOrder);

            int lowReference = company.IdentitytBetNumber;
            Assert.AreEqual(1000001, lowReference);

            int highReference = (lowReference + myOrder.CountOfItems - 1);
            Assert.AreEqual(1000001, highReference);

            var auths = new AuthorizationsNumbers(*********);
            company.PurchaseTickets(itIsThePresent: false, (OrderCart)myOrder, purchaseDate, auths, domain, lowReference, orderNumber, "Artemis_ThirdParty_Deposit_USD");

            // Gradear lottery
            DateTime gradeDate = new DateTime(2024, 10, 04, 12, 15, 00);
            LotteryTriz lotteryTriz = company.LotteryGamesPool.TrizLotteryGame.GetLottery();
            string winnerNumber = "12345";
            DrawingsSummaryReport drawingsSummaryReport = lotteryTriz.DrawTriz(gradeDate, winnerNumber, now, false, "N/A", false);
            Assert.AreEqual(1, drawingsSummaryReport.TotalWinners);
            Assert.AreEqual(1, drawingsSummaryReport.TotalPlayers);
            Assert.AreEqual(1, drawingsSummaryReport.TotalTickets);
            Assert.AreEqual(1, drawingsSummaryReport.TotalTicketAmount);
            Assert.AreEqual(900, drawingsSummaryReport.TotalPrize);

            // ExistsPostedDrawInMemory
            bool existsDraw = lotteryTriz.ExistsPostedDrawInMemory(gradeDate);
            Assert.IsTrue(existsDraw);

            // WasAlreadyConfirmed
            bool wasAlreadyConfirmed = lotteryTriz.WasAlreadyConfirmed(gradeDate);
            Assert.IsFalse(wasAlreadyConfirmed);

            // Resend
            var gradeFreeFormWagers = lotteryTriz.ResendToExternalAccounting(gradeDate, now, itIsThePresent: false, "N/A");
            Assert.AreEqual(1, gradeFreeFormWagers.Count());
        }

        [TestMethod]
        public void TrizLottery_PurchaseOtherMethod_Ticket_Resend_ARTEMIS_Test()
        {
            QueueInMemory queue = new QueueInMemory();
            Integration.Kafka = queue;

            Company company = new Company();
            CurrenciesTest.AddCurrencies();
            company.Sales.CreateStore(1, "Test Store").MakeCurrent();

            Product ticketPick3S = company.LotteryGamesPool.GetOrCreateProductById(1);
            ticketPick3S.Description = "Ticket pick 3 boxed";
            ticketPick3S.Price = 1;

            DateTime now = new DateTime(2024, 10, 04, 00, 00, 00);
            company.LotteryGamesPool.TrizLotteryGame.GetLottery().Every(hour: 12, minute: 15, dayOfWeek: 5, now, "Admin");

            Domain domain = company.Sales.CreateDomain(false, 1, "localhost", PaymentChannels.Agents.INSIDER);
            Player player = company.GetOrCreateCustomerById("*********").Player;
            Customer customer = company.CustomerByPlayer(player);
            customer.Identifier = "*********";

            int orderNumber = 301952;
            var myOrder = company.NewOrder(customer, orderNumber, Currencies.CODES.USD);

            string gameType = "triz";
            string dates = "10/04/2024";
            string drawHours = "12:15 PM";
            string strIsPresentPlayerBeforeToCloseStore = "False";
            string strUseNextDate = "False";
            string states = "MX";
            string withFireBalls = "False";
            DateTime purchaseDate = new DateTime(2024, 10, 04, 10, 00, 00);
            company.LotteryGamesPool.TrizLotteryGame.NextDatesAccumulator.Calculate(domain, purchaseDate, gameType, dates, states, drawHours, withFireBalls, strIsPresentPlayerBeforeToCloseStore, strUseNextDate);

            var nextDatesAccumulator = company.LotteryGamesPool.TrizLotteryGame.NextDatesAccumulator;
            var numbers = new List<string> { "345" };
            var statesList = new List<string> { "MX" };
            var fireBallList = new List<bool> { false };
            myOrder = company.PrepareTicketOrderWithMultipleAuthorization(player, "MultipleInputSingleAmount", "S", (OrderCart)myOrder, nextDatesAccumulator, domain).Add(numbers, statesList, fireBallList, 1).Commit();
            company.AddOrders((OrderCart)myOrder);

            int lowReference = company.IdentitytBetNumber;
            Assert.AreEqual(1000001, lowReference);

            int highReference = (lowReference + myOrder.CountOfItems - 1);
            Assert.AreEqual(1000001, highReference);

            var auths = new AuthorizationsNumbers(*********);
            company.PurchaseTickets(itIsThePresent: false, (OrderCart)myOrder, purchaseDate, auths, domain, lowReference, orderNumber, "Artemis_ThirdParty_Deposit_USD");

            // Gradear lottery
            DateTime gradeDate = new DateTime(2024, 10, 04, 12, 15, 00);
            LotteryTriz lotteryTriz = company.LotteryGamesPool.TrizLotteryGame.GetLottery();
            string winnerNumber = "12345";
            DrawingsSummaryReport drawingsSummaryReport = lotteryTriz.DrawTriz(gradeDate, winnerNumber, now, false, "N/A", false);
            Assert.AreEqual(1, drawingsSummaryReport.TotalWinners);
            Assert.AreEqual(1, drawingsSummaryReport.TotalPlayers);
            Assert.AreEqual(1, drawingsSummaryReport.TotalTickets);
            Assert.AreEqual(1, drawingsSummaryReport.TotalTicketAmount);
            Assert.AreEqual(900, drawingsSummaryReport.TotalPrize);

            // ExistsPostedDrawInMemory
            bool existsDraw = lotteryTriz.ExistsPostedDrawInMemory(gradeDate);
            Assert.IsTrue(existsDraw);

            // WasAlreadyConfirmed
            bool wasAlreadyConfirmed = lotteryTriz.WasAlreadyConfirmed(gradeDate);
            Assert.IsFalse(wasAlreadyConfirmed);

            // Resend
            var gradeFreeFormWagers = lotteryTriz.ResendToExternalAccounting(gradeDate, now, itIsThePresent: false, "N/A");
            Assert.AreEqual(1, gradeFreeFormWagers.Count());
        }

        [TestMethod]
        public void TrizLottery_Purchase_Ticket_And_Grade_And_Preview_NoAction_Test()
        {
            QueueInMemory queue = new QueueInMemory();
            Integration.Kafka = queue;

            Company company = new Company();
            CurrenciesTest.AddCurrencies();
            company.Sales.CreateStore(1, "Test Store").MakeCurrent();

            Product ticketPick3S = company.LotteryGamesPool.GetOrCreateProductById(1);
            ticketPick3S.Description = "Ticket pick 3 boxed";
            ticketPick3S.Price = 1;

            DateTime now = new DateTime(2024, 10, 04, 00, 00, 00);
            company.LotteryGamesPool.TrizLotteryGame.GetLottery().Every(hour: 12, minute: 15, dayOfWeek: 5, now, "Admin");

            Domain domain = company.Sales.CreateDomain(false, 1, "localhost", PaymentChannels.Agents.INSIDER);
            Player player = company.GetOrCreateCustomerById("*********").Player;
            Customer customer = company.CustomerByPlayer(player);
            customer.Identifier = "*********";

            int orderNumber = 301952;
            var myOrder = company.NewOrder(customer, orderNumber, Currencies.CODES.USD);

            string gameType = "triz";
            string dates = "10/04/2024";
            string drawHours = "12:15 PM";
            string strIsPresentPlayerBeforeToCloseStore = "False";
            string strUseNextDate = "False";
            string states = "MX";
            string withFireBalls = "False";
            DateTime purchaseDate = new DateTime(2024, 10, 04, 10, 00, 00);
            company.LotteryGamesPool.TrizLotteryGame.NextDatesAccumulator.Calculate(domain, purchaseDate, gameType, dates, states, drawHours, withFireBalls, strIsPresentPlayerBeforeToCloseStore, strUseNextDate);

            var nextDatesAccumulator = company.LotteryGamesPool.TrizLotteryGame.NextDatesAccumulator;
            var numbers = new List<string> { "345" };
            myOrder = company.PrepareTicketOrderWithMultipleAuthorization(player, "MultipleInputSingleAmount", "S", (OrderCart)myOrder, nextDatesAccumulator, domain).Add(numbers, "MX", false, 1).Commit();
            company.AddOrders((OrderCart)myOrder);

            int lowReference = company.IdentitytBetNumber;
            Assert.AreEqual(1000001, lowReference);

            int highReference = (lowReference + myOrder.CountOfItems - 1);
            Assert.AreEqual(1000001, highReference);

            var auths = new AuthorizationsNumbers(*********);
            company.PurchaseTickets(itIsThePresent: false, (OrderCart)myOrder, purchaseDate, auths, domain, lowReference, orderNumber, "Artemis_ThirdParty_Deposit_USD");

            // Gradear lottery
            DateTime gradeDate = new DateTime(2024, 10, 04, 12, 15, 00);
            LotteryTriz lotteryTriz = company.LotteryGamesPool.TrizLotteryGame.GetLottery();
            string winnerNumber = "12345";
            DrawingsSummaryReport drawingsSummaryReport = lotteryTriz.DrawTriz(gradeDate, winnerNumber, now, false, "N/A", false);
            Assert.AreEqual(1, drawingsSummaryReport.TotalWinners);
            Assert.AreEqual(1, drawingsSummaryReport.TotalPlayers);
            Assert.AreEqual(1, drawingsSummaryReport.TotalTickets);
            Assert.AreEqual(1, drawingsSummaryReport.TotalTicketAmount);
            Assert.AreEqual(900, drawingsSummaryReport.TotalPrize);

            // No action lottery
            DateTime noActionDate = new DateTime(2024, 10, 04, 12, 15, 00);
            NoActionReport noActionReport = lotteryTriz.PreviewNoAction(noActionDate);
            Assert.AreEqual(1, noActionReport.TotalTickets);
        }

        [TestMethod]
        public void TrizLottery_1Check_NextDatesAccumulator_Test()
        {
            Company company = new Company();
            CurrenciesTest.AddCurrencies();
            company.Sales.CreateStore(1, "Test Store").MakeCurrent();

            Product ticketPickProduct = company.LotteryGamesPool.GetOrCreateProductById(1);
            ticketPickProduct.Description = "Ticket pick 3 boxed";
            ticketPickProduct.Price = 1;

            DateTime now = new DateTime(2024, 10, 25, 00, 00, 00);
            company.LotteryGamesPool.TrizLotteryGame.GetLottery().Every(hour: 12, minute: 15, dayOfWeek: 1, now, "Admin");

            Domain domain = company.Sales.CreateDomain(false, 1, "localhost", PaymentChannels.Agents.INSIDER);
            Player player = company.GetOrCreateCustomerById("*********").Player;
            Customer customer = company.CustomerByPlayer(player);
            customer.Identifier = "*********";

            var lotteryGame = company.LotteryGamesPool.GetLotteryGame(IdOfLotteryGame.Triz);

            var dateFormatted = "10/21/2024";
            var picksBuilder = new List<int> { 2 };
            var statesBuilder = new List<string> { "MX" };
            var hoursBuilder = new List<string> { "12:15 PM" };
            var withFireBallBuilder = new List<string> { "False" };
            var isPresentPlayerBeforeToCloseStoreBuilder = new List<string> { "False" };
            var useNextDateBuilder = new List<string> { "False" };
            var anyScheduleHasChanged = lotteryGame.AnyScheduleHasChanged(picksBuilder, statesBuilder, hoursBuilder, withFireBallBuilder);
            Assert.IsFalse(anyScheduleHasChanged);

            var nextDatesAccumulator = new NextDatesAccumulator(lotteryGame, domain, now, picksBuilder, dateFormatted, statesBuilder, hoursBuilder, withFireBallBuilder, isPresentPlayerBeforeToCloseStoreBuilder, useNextDateBuilder);
            Assert.IsTrue(nextDatesAccumulator.GetAll.Count() == 1);

            var nextDate = nextDatesAccumulator.GetAll.First().Date;
            Assert.AreEqual(new DateTime(2024, 10, 28, 12, 15, 00), nextDate);

            var hour = nextDatesAccumulator.GetAll.First().Date.HHmmAMPM();
            Assert.AreEqual("12:15 PM", hour);
        }

        [TestMethod]
        public void TrizLottery_2Check_NextDatesAccumulator_Test()
        {
            Company company = new Company();
            CurrenciesTest.AddCurrencies();
            company.Sales.CreateStore(1, "Test Store").MakeCurrent();

            Product ticketPickProduct = company.LotteryGamesPool.GetOrCreateProductById(1);
            ticketPickProduct.Description = "Ticket pick 3 boxed";
            ticketPickProduct.Price = 1;

            DateTime now = new DateTime(2024, 10, 25, 00, 00, 00);
            company.LotteryGamesPool.TrizLotteryGame.GetLottery().Every(hour: 12, minute: 15, dayOfWeek: 1, now, "Admin");

            Domain domain = company.Sales.CreateDomain(false, 1, "localhost", PaymentChannels.Agents.INSIDER);
            Player player = company.GetOrCreateCustomerById("*********").Player;
            Customer customer = company.CustomerByPlayer(player);
            customer.Identifier = "*********";

            var lotteryGame = company.LotteryGamesPool.GetLotteryGame(IdOfLotteryGame.Triz);

            var dateFormatted = "10/21/2024";
            var picksBuilder = new List<int> { 2, 3 };
            var statesBuilder = new List<string> { "MX", "MX" };
            var hoursBuilder = new List<string> { "12:15 PM", "12:15 PM" };
            var withFireBallBuilder = new List<string> { "False", "false" };
            var isPresentPlayerBeforeToCloseStoreBuilder = new List<string> { "False", "false" };
            var useNextDateBuilder = new List<string> { "False", "false" };
            var anyScheduleHasChanged = lotteryGame.AnyScheduleHasChanged(picksBuilder, statesBuilder, hoursBuilder, withFireBallBuilder);
            Assert.IsFalse(anyScheduleHasChanged);

            var nextDatesAccumulator = new NextDatesAccumulator(lotteryGame, domain, now, picksBuilder, dateFormatted, statesBuilder, hoursBuilder, withFireBallBuilder, isPresentPlayerBeforeToCloseStoreBuilder, useNextDateBuilder);
            Assert.IsTrue(nextDatesAccumulator.GetAll.Count() == 2);

            var nextDate = nextDatesAccumulator.GetAll.First().Date;
            Assert.AreEqual(new DateTime(2024, 10, 28, 12, 15, 00), nextDate);

            var hour = nextDatesAccumulator.GetAll.First().Date.HHmmAMPM();
            Assert.AreEqual("12:15 PM", hour);
        }

        [TestMethod]
        public void GameTypes_EditMainURL_EnableDisable_Test()
        {
            Company company = new Company();
            CurrenciesTest.AddCurrencies();
            company.Sales.CreateStore(1, "Test Store").MakeCurrent();

            Domain domain = company.Sales.CreateDomain(false, 1, "localhost", PaymentChannels.Agents.INSIDER);
            Assert.ThrowsException<GameEngineException>(() =>
            {
                company.Sales.UpdateMainUrl(false, domain, "localhost");
            });
            Assert.IsTrue(company.Sales.AllDomains.Count() == 1);

            company.Sales.UpdateMainUrl(false, domain, "localhost2");
            Assert.IsTrue(company.Sales.AllDomains.Count() == 1);
        }

        [TestMethod]
        public void TrizLottery_NoTickets_Test()
        {
            Company company = new Company();
            CurrenciesTest.AddCurrencies();
            company.Sales.CreateStore(1, "Test Store").MakeCurrent();

            Product ticketPickProduct = company.LotteryGamesPool.GetOrCreateProductById(1);
            ticketPickProduct.Description = "Ticket pick 3 boxed";
            ticketPickProduct.Price = 1;

            DateTime now = new DateTime(2024, 10, 25, 00, 00, 00);
            company.LotteryGamesPool.TrizLotteryGame.GetLottery().Every(hour: 12, minute: 15, dayOfWeek: 1, now, "Admin");

            Domain domain = company.Sales.CreateDomain(false, 1, "localhost", PaymentChannels.Agents.INSIDER);
            Player player = company.GetOrCreateCustomerById("*********").Player;
            Customer customer = company.CustomerByPlayer(player);
            customer.Identifier = "*********";

            // gradear lottery with no tickets
            DateTime gradeDate = new DateTime(2024, 10, 25, 12, 15, 00);
            LotteryTriz lotteryTriz = company.LotteryGamesPool.TrizLotteryGame.GetLottery();
            string winnerNumber = "12345";
            DrawingsSummaryReport drawingsSummaryReport = lotteryTriz.DrawTriz(gradeDate, winnerNumber, now, false, "N/A", false);
            Assert.AreEqual(0, drawingsSummaryReport.TotalWinners);
            Assert.AreEqual(0, drawingsSummaryReport.TotalPlayers);
            Assert.AreEqual(0, drawingsSummaryReport.TotalTickets);
            Assert.AreEqual(0, drawingsSummaryReport.TotalTicketAmount);
            Assert.AreEqual(0, drawingsSummaryReport.TotalPrize);
        }

        [TestMethod]
        public void TrizLottery_Purchase_Ticket_And_Grade_And_DrawsToBeConfirmed_Confirm_Test()
        {
            QueueInMemory queue = new QueueInMemory();
            Integration.Kafka = queue;

            Company company = new Company();
            CurrenciesTest.AddCurrencies();
            company.Sales.CreateStore(1, "Test Store").MakeCurrent();

            Product ticketPick = company.LotteryGamesPool.GetOrCreateProductById(1);
            ticketPick.Description = "Ticket pick 3 straight";
            ticketPick.Price = 1;

            DateTime now = new DateTime(2024, 10, 04, 00, 00, 00);
            company.LotteryGamesPool.TrizLotteryGame.GetLottery().Every(hour: 12, minute: 15, dayOfWeek: 5, now, "Admin");

            Domain domain = company.Sales.CreateDomain(false, 1, "localhost", PaymentChannels.Agents.INSIDER);
            Player player = company.GetOrCreateCustomerById("*********").Player;
            Customer customer = company.CustomerByPlayer(player);
            customer.Identifier = "*********";

            int orderNumber = 301952;
            var myOrder = company.NewOrder(customer, orderNumber, Currencies.CODES.USD);

            string gameType = "triz";
            string dates = "10/04/2024";
            string drawHours = "12:15 PM";
            string strIsPresentPlayerBeforeToCloseStore = "False";
            string strUseNextDate = "False";
            string states = "MX";
            string withFireBalls = "False";
            DateTime purchaseDate = new DateTime(2024, 10, 04, 10, 00, 00);
            company.LotteryGamesPool.TrizLotteryGame.NextDatesAccumulator.Calculate(domain, purchaseDate, gameType, dates, states, drawHours, withFireBalls, strIsPresentPlayerBeforeToCloseStore, strUseNextDate);

            var nextDatesAccumulator = company.LotteryGamesPool.TrizLotteryGame.NextDatesAccumulator;
            var numbers = new List<string> { "345" };
            myOrder = company.PrepareTicketOrderWithMultipleAuthorization(player, "MultipleInputSingleAmount", "S", (OrderCart)myOrder, nextDatesAccumulator, domain).Add(numbers, "MX", false, 1).Commit();
            company.AddOrders((OrderCart)myOrder);

            int lowReference = company.IdentitytBetNumber;
            Assert.AreEqual(1000001, lowReference);

            int highReference = (lowReference + myOrder.CountOfItems - 1);
            Assert.AreEqual(1000001, highReference);

            var auths = new AuthorizationsNumbers(*********);
            company.PurchaseTickets(itIsThePresent: false, (OrderCart)myOrder, purchaseDate, auths, domain, lowReference, orderNumber, "Artemis_ThirdParty_Deposit_USD");

            DateTime lastVersionOfDate = new DateTime(2024, 10, 04, 23, 59, 59);
            var schedules = company.LotteryGamesPool.TrizLotteryGame.PendingAndNoRegradedSchedulesAt(lastVersionOfDate);
            Assert.IsTrue(schedules.Count() == 1);

            var drawsToBeConfirmed = company.LotteryGamesPool.TrizLotteryGame.DrawsToBeConfirmed();
            Assert.IsTrue(drawsToBeConfirmed.Count() == 0);

            drawsToBeConfirmed = company.LotteryGamesPool.DrawsToBeConfirmed();
            Assert.IsTrue(drawsToBeConfirmed.Count() == 0);

            // Gradear lottery
            DateTime gradeDate = new DateTime(2024, 10, 04, 12, 15, 00);
            LotteryTriz lotteryTriz = company.LotteryGamesPool.TrizLotteryGame.GetLottery();
            string winnerNumber = "12345";
            DrawingsSummaryReport drawingsSummaryReport = lotteryTriz.DrawTriz(gradeDate, winnerNumber, now, false, "N/A", false);
            Assert.AreEqual(1, drawingsSummaryReport.TotalWinners);
            Assert.AreEqual(1, drawingsSummaryReport.TotalPlayers);
            Assert.AreEqual(1, drawingsSummaryReport.TotalTickets);
            Assert.AreEqual(1, drawingsSummaryReport.TotalTicketAmount);
            Assert.AreEqual(900, drawingsSummaryReport.TotalPrize);

            lastVersionOfDate = new DateTime(2024, 10, 04, 23, 59, 59);
            schedules = company.LotteryGamesPool.TrizLotteryGame.PendingAndNoRegradedSchedulesAt(lastVersionOfDate);
            Assert.IsTrue(schedules.Count() == 0);

            schedules = company.LotteryGamesPool.TrizLotteryGame.FinishedAndRegradedSchedulesOf(lastVersionOfDate);
            Assert.IsTrue(schedules.Count() == 1);

            schedules = company.LotteryGamesPool.FinishedAndRegradedSchedulesOf(lastVersionOfDate);
            Assert.IsTrue(schedules.Count() == 1);

            bool canBeConfirmed = lotteryTriz.CanBeConfirmed(gradeDate);
            Assert.IsTrue(canBeConfirmed);

            drawsToBeConfirmed = company.LotteryGamesPool.TrizLotteryGame.DrawsToBeConfirmed();
            Assert.IsTrue(drawsToBeConfirmed.Count() == 1);

            drawsToBeConfirmed = company.LotteryGamesPool.DrawsToBeConfirmed();
            Assert.IsTrue(drawsToBeConfirmed.Count() == 1);

            // Confirm
            var confirmReport = lotteryTriz.ConfirmDraw(gradeDate, now, "N/A", false);
            Assert.AreEqual(1, confirmReport.TotalTickets);
            Assert.AreEqual(1, confirmReport.TotalPlayers);
            Assert.AreEqual(1, confirmReport.TotalWinners);
            Assert.AreEqual(900, confirmReport.TotalPrize);
        }

        [TestMethod]
        public void TrizLottery_Purchase_Ticket_Beginingn_Ending_And_Grade_And_DrawsToBeConfirmed_Confirm_Test()
        {
            QueueInMemory queue = new QueueInMemory();
            Integration.Kafka = queue;

            Company company = new Company();
            CurrenciesTest.AddCurrencies();
            company.Sales.CreateStore(1, "Test Store").MakeCurrent();

            Product ticketPick = company.LotteryGamesPool.GetOrCreateProductById(5);
            ticketPick.Description = "Ticket pick 2 straight";
            ticketPick.Price = 1;

            DateTime now = new DateTime(2024, 10, 04, 00, 00, 00);
            company.LotteryGamesPool.TrizLotteryGame.GetLottery().Every(hour: 12, minute: 15, dayOfWeek: 5, now, "Admin");

            Domain domain = company.Sales.CreateDomain(false, 1, "localhost", PaymentChannels.Agents.INSIDER);
            Player player = company.GetOrCreateCustomerById("*********").Player;
            Customer customer = company.CustomerByPlayer(player);
            customer.Identifier = "*********";

            int orderNumber = 301952;
            var myOrder = company.NewOrder(customer, orderNumber, Currencies.CODES.USD);

            string gameType = "triz";
            string dates = "10/04/2024";
            string drawHours = "12:15 PM";
            string strIsPresentPlayerBeforeToCloseStore = "False";
            string strUseNextDate = "False";
            string states = "MX";
            string withFireBalls = "False";
            DateTime purchaseDate = new DateTime(2024, 10, 04, 10, 00, 00);
            company.LotteryGamesPool.TrizLotteryGame.NextDatesAccumulator.Calculate(domain, purchaseDate, gameType, dates, states, drawHours, withFireBalls, strIsPresentPlayerBeforeToCloseStore, strUseNextDate);

            var nextDatesAccumulator = company.LotteryGamesPool.TrizLotteryGame.NextDatesAccumulator;
            var numbers = new List<string> { "12" };
            myOrder = company.PrepareTicketOrderWithMultipleAuthorization(player, "MultipleInputSingleAmount", "S", (OrderCart)myOrder, nextDatesAccumulator, domain).AddLeft(numbers, "MX", false, 1).Commit();
            company.AddOrders((OrderCart)myOrder);

            int lowReference = company.IdentitytBetNumber;
            Assert.AreEqual(1000001, lowReference);

            int highReference = (lowReference + myOrder.CountOfItems - 1);
            Assert.AreEqual(1000001, highReference);

            var auths = new AuthorizationsNumbers(*********);
            company.PurchaseTickets(itIsThePresent: false, (OrderCart)myOrder, purchaseDate, auths, domain, lowReference, orderNumber, "Artemis_ThirdParty_Deposit_USD");

            DateTime lastVersionOfDate = new DateTime(2024, 10, 04, 23, 59, 59);
            var schedules = company.LotteryGamesPool.TrizLotteryGame.PendingAndNoRegradedSchedulesAt(lastVersionOfDate);
            Assert.IsTrue(schedules.Count() == 1);

            var drawsToBeConfirmed = company.LotteryGamesPool.TrizLotteryGame.DrawsToBeConfirmed();
            Assert.IsTrue(drawsToBeConfirmed.Count() == 0);

            drawsToBeConfirmed = company.LotteryGamesPool.DrawsToBeConfirmed();
            Assert.IsTrue(drawsToBeConfirmed.Count() == 0);

            // Gradear lottery
            DateTime gradeDate = new DateTime(2024, 10, 04, 12, 15, 00);
            LotteryTriz lotteryTriz = company.LotteryGamesPool.TrizLotteryGame.GetLottery();
            string winnerNumber = "12345";
            DrawingsSummaryReport drawingsSummaryReport = lotteryTriz.DrawTriz(gradeDate, winnerNumber, now, false, "N/A", false);
            Assert.AreEqual(1, drawingsSummaryReport.TotalWinners);
            Assert.AreEqual(1, drawingsSummaryReport.TotalPlayers);
            Assert.AreEqual(1, drawingsSummaryReport.TotalTickets);
            Assert.AreEqual(1, drawingsSummaryReport.TotalTicketAmount);
            Assert.AreEqual(90, drawingsSummaryReport.TotalPrize);

            lastVersionOfDate = new DateTime(2024, 10, 04, 23, 59, 59);
            schedules = company.LotteryGamesPool.TrizLotteryGame.PendingAndNoRegradedSchedulesAt(lastVersionOfDate);
            Assert.IsTrue(schedules.Count() == 0);

            schedules = company.LotteryGamesPool.TrizLotteryGame.FinishedAndRegradedSchedulesOf(lastVersionOfDate);
            Assert.IsTrue(schedules.Count() == 1);

            schedules = company.LotteryGamesPool.FinishedAndRegradedSchedulesOf(lastVersionOfDate);
            Assert.IsTrue(schedules.Count() == 1);

            bool canBeConfirmed = lotteryTriz.CanBeConfirmed(gradeDate);
            Assert.IsTrue(canBeConfirmed);

            drawsToBeConfirmed = company.LotteryGamesPool.TrizLotteryGame.DrawsToBeConfirmed();
            Assert.IsTrue(drawsToBeConfirmed.Count() == 1);

            drawsToBeConfirmed = company.LotteryGamesPool.DrawsToBeConfirmed();
            Assert.IsTrue(drawsToBeConfirmed.Count() == 1);

            // Confirm
            var confirmReport = lotteryTriz.ConfirmDraw(gradeDate, now, "N/A", false);
            Assert.AreEqual(1, confirmReport.TotalTickets);
            Assert.AreEqual(1, confirmReport.TotalPlayers);
            Assert.AreEqual(1, confirmReport.TotalWinners);
            Assert.AreEqual(90, confirmReport.TotalPrize);
        }

        [TestMethod]
        public void TrizLottery_Purchase_2Same_Ticket_Beginingn_Ending_And_Grade_And_DrawsToBeConfirmed_Confirm_Test()
        {
            QueueInMemory queue = new QueueInMemory();
            Integration.Kafka = queue;

            Company company = new Company();
            CurrenciesTest.AddCurrencies();
            company.Sales.CreateStore(1, "Test Store").MakeCurrent();

            Product ticketPick = company.LotteryGamesPool.GetOrCreateProductById(5);
            ticketPick.Description = "Ticket pick 2 straight";
            ticketPick.Price = 1;

            DateTime now = new DateTime(2024, 10, 04, 00, 00, 00);
            company.LotteryGamesPool.TrizLotteryGame.GetLottery().Every(hour: 12, minute: 15, dayOfWeek: 5, now, "Admin");
            company.LotteryGamesPool.TrizLotteryGame.GetLottery().Every(hour: 15, minute: 00, dayOfWeek: 5, now, "Admin");

            Domain domain = company.Sales.CreateDomain(false, 1, "localhost", PaymentChannels.Agents.INSIDER);
            Player player = company.GetOrCreateCustomerById("*********").Player;
            Customer customer = company.CustomerByPlayer(player);
            customer.Identifier = "*********";

            int orderNumber = 301952;
            var myOrder = company.NewOrder(customer, orderNumber, Currencies.CODES.USD);

            string gameType = "triz";
            string dates = "10/04/2024";
            string drawHours = "12:15 PM,3:00 PM";
            string strIsPresentPlayerBeforeToCloseStore = "False,False";
            string strUseNextDate = "False,False";
            string states = "MX,MX";
            string withFireBalls = "False,False";
            DateTime purchaseDate = new DateTime(2024, 10, 04, 10, 00, 00);
            company.LotteryGamesPool.TrizLotteryGame.NextDatesAccumulator.Calculate(domain, purchaseDate, gameType, dates, states, drawHours, withFireBalls, strIsPresentPlayerBeforeToCloseStore, strUseNextDate);

            var nextDatesAccumulator = company.LotteryGamesPool.TrizLotteryGame.NextDatesAccumulator;
            myOrder = company.PrepareTicketOrderWithMultipleAuthorization(player, "MultipleInputSingleAmount", "S", (OrderCart)myOrder, nextDatesAccumulator, domain).AddLeft(new List<string> { "12" }, new List<string> { "MX", "MX" }, new List<bool> { false, false }, 1).Add(new List<string> { "12" }, new List<string> { "MX", "MX" }, new List<bool> { false, false }, 1).Commit();
            company.AddOrders((OrderCart)myOrder);

            int lowReference = company.IdentitytBetNumber;
            Assert.AreEqual(1000001, lowReference);

            int highReference = (lowReference + myOrder.CountOfItems - 1);
            Assert.AreEqual(1000004, highReference);
        }

        [TestMethod]
        public void GetInternalSchedule()
        {
            Company company = new Company();
            CurrenciesTest.AddCurrencies();
            company.Sales.CreateStore(1, "Test Store").MakeCurrent();
            DateTime now = new DateTime(2024, 10, 04, 00, 00, 00);

            LotteryTriz trizLottery = company.LotteryGamesPool.TrizLotteryGame.GetLottery();
            Assert.AreEqual(company.LotteryGamesPool.TrizLotteryGame, trizLottery.LotteryGame);
            for (int i = 0; i <= 6; i++) trizLottery.Every(12, 15, i, now, "Game Admin");
            var schedule = trizLottery.GetSchedule(hour: 12, minute: 15);

            var pick3Ending = trizLottery.GetLotteryPick(3);
            var pick3EndingSchedule = pick3Ending.GetSchedule(12, 15);
            Assert.IsTrue(company.LotteryGamesPool.ExistsInternalSchedule(pick3EndingSchedule.UniqueId));
            var internalSchedule = company.LotteryGamesPool.GetInternalSchedule(pick3EndingSchedule.UniqueId);
            Assert.AreEqual(internalSchedule, pick3EndingSchedule);
        }

        [TestMethod]
        public void GetLotteryPick_bug9163()
        {
            Company company = new Company();
            CurrenciesTest.AddCurrencies();
            company.Sales.CreateStore(1, "Test Store").MakeCurrent();

            var lotteries = company.Lotto900();
            var state = new State("TX", "Texas");
            var lottery = lotteries.GetOrCreateLottery(3, state);
            DateTime now = new DateTime(2024, 10, 04, 00, 00, 00);
            for (int i = 0; i <= 6; i++) lottery.Every(9, 30, i, now, "Game Admin");
            var lotteryPick = company.LotteryGamesPool.PicksLotteryGame.GetLottery(3, state);
            Assert.AreEqual(lotteries, lotteryPick.PicksLotteryGame);
            Assert.AreEqual(lottery, lotteryPick);
            Assert.AreEqual(1, lotteryPick.Schedules.Count());
            var firstSchedule = lotteryPick.Schedules.First();
            Assert.AreEqual(1, firstSchedule.UniqueId);

            LotteryTriz trizLottery = company.LotteryGamesPool.TrizLotteryGame.GetLottery();
            Assert.AreEqual(company.LotteryGamesPool.TrizLotteryGame, trizLottery.LotteryGame);
            for (int i = 0; i <= 6; i++) trizLottery.Every(12, 15, i, now, "Game Admin");
            GamesEngine.Time.Schedule schedule = trizLottery.GetSchedule(hour: 12, minute: 15);

            var pick2Beginning = trizLottery.GetLotteryPick(2, false);
            Assert.AreEqual(1, pick2Beginning.Schedules.Count());
            firstSchedule = pick2Beginning.GetSchedule(12, 15);
            Assert.AreEqual(*********, firstSchedule.UniqueId);
            Assert.AreEqual(*********, firstSchedule.Id);

            var pick2Ending = trizLottery.GetLotteryPick(2);
            Assert.AreEqual(1, pick2Ending.Schedules.Count());
            firstSchedule = pick2Ending.GetSchedule(12, 15);
            Assert.AreEqual(*********, firstSchedule.UniqueId);
            Assert.AreEqual(*********, firstSchedule.Id);

            var pick3Ending = trizLottery.GetLotteryPick(3);
            Assert.AreEqual(1, pick3Ending.Schedules.Count());
            firstSchedule = pick3Ending.GetSchedule(12, 15);
            Assert.AreEqual(134217733, firstSchedule.UniqueId);
            Assert.AreEqual(134217733, firstSchedule.Id);

            var pick4Ending = trizLottery.GetLotteryPick(4);
            Assert.AreEqual(1, pick4Ending.Schedules.Count());
            firstSchedule = pick4Ending.GetSchedule(12, 15);
            Assert.AreEqual(*********, firstSchedule.UniqueId);
            Assert.AreEqual(*********, firstSchedule.Id);

            var pick5Ending = trizLottery.GetLotteryPick(5);
            Assert.AreEqual(1, pick5Ending.Schedules.Count());
            firstSchedule = pick5Ending.GetSchedule(12, 15);
            Assert.AreEqual(*********, firstSchedule.UniqueId);
            Assert.AreEqual(*********, firstSchedule.Id);
        }

        [TestMethod]
        public void UpdateDescription_bug9150()
        {
            Company company = new Company();
            CurrenciesTest.AddCurrencies();
            company.Sales.CreateStore(1, "Test Store").MakeCurrent();

            var lotteries = company.Lotto900();
            var state = new State("TX", "Texas");
            var lottery = lotteries.GetOrCreateLottery(3, state);
            DateTime now = new DateTime(2024, 10, 04, 00, 00, 00);
            for (int i = 0; i <= 6; i++) lottery.Every(9, 30, i, now, "Game Admin");
            var lotteryPick = company.LotteryGamesPool.PicksLotteryGame.GetLottery(3, state);
            Assert.AreEqual(1, lotteryPick.Schedules.Count());
            var firstSchedule = lotteryPick.Schedules.First();
            Assert.AreEqual(1, firstSchedule.UniqueId);

            LotteryTriz trizLottery = company.LotteryGamesPool.TrizLotteryGame.GetLottery();
            Assert.AreEqual(company.LotteryGamesPool.TrizLotteryGame, trizLottery.LotteryGame);
            for (int i = 0; i <= 6; i++) trizLottery.Every(12, 15, i, now, "Game Admin");
            for (int i = 0; i <= 6; i++) trizLottery.Every(15, 0, i, now, "Game Admin");
            GamesEngine.Time.Schedule trizSchedule = trizLottery.GetSchedule(hour: 12, minute: 15);

            var pick2Beginning = trizLottery.GetLotteryPick(2, false);
            Assert.AreEqual(2, pick2Beginning.Schedules.Count());
            var pick2BeginningSchedule = pick2Beginning.GetSchedule(12, 15);
            Assert.AreEqual(*********, pick2BeginningSchedule.UniqueId);

            var pick2Ending = trizLottery.GetLotteryPick(2);
            Assert.AreEqual(2, pick2Ending.Schedules.Count());
            var pick2EndingSchedule = pick2Ending.GetSchedule(12, 15);
            Assert.AreEqual(*********, pick2EndingSchedule.UniqueId);

            var pick3Ending = trizLottery.GetLotteryPick(3);
            Assert.AreEqual(2, pick3Ending.Schedules.Count());
            var pick3EndingSchedule = pick3Ending.GetSchedule(12, 15);
            Assert.AreEqual(134217733, pick3EndingSchedule.UniqueId);

            var pick4Ending = trizLottery.GetLotteryPick(4);
            Assert.AreEqual(2, pick4Ending.Schedules.Count());
            var pick4EndingSchedule = pick4Ending.GetSchedule(12, 15);
            Assert.AreEqual(*********, pick4EndingSchedule.UniqueId);

            var pick5Ending = trizLottery.GetLotteryPick(5);
            Assert.AreEqual(2, pick5Ending.Schedules.Count());
            var pick5EndingSchedule = pick5Ending.GetSchedule(12, 15);
            Assert.AreEqual(*********, pick5EndingSchedule.UniqueId);

            Assert.AreEqual("MX 12:15 PM", trizSchedule.GetDescription());
            trizSchedule.UpdateDescription(false, "Triz Midday 12:15 PM");
            Assert.AreEqual("Triz Midday 12:15 PM", trizSchedule.GetDescription());
            Assert.AreEqual("MX 12:15 PM", pick2BeginningSchedule.GetDescription());
            Assert.AreEqual("MX 12:15 PM", pick2EndingSchedule.GetDescription());
            Assert.AreEqual("MX 12:15 PM", pick3EndingSchedule.GetDescription());
            Assert.AreEqual("MX 12:15 PM", pick4EndingSchedule.GetDescription());
            Assert.AreEqual("MX 12:15 PM", pick5EndingSchedule.GetDescription());

            trizLottery.UpdateDescription(false, "Triz 12:15 PM", 12, 15);
            Assert.AreEqual("Triz 12:15 PM", trizSchedule.GetDescription());
            Assert.AreEqual("Triz 12:15 PM First Pair", pick2BeginningSchedule.GetDescription());
            Assert.AreEqual("Triz 12:15 PM Last Pair", pick2EndingSchedule.GetDescription());
            Assert.AreEqual("Triz 12:15 PM", pick3EndingSchedule.GetDescription());
            Assert.AreEqual("Triz 12:15 PM", pick4EndingSchedule.GetDescription());
            Assert.AreEqual("Triz 12:15 PM", pick5EndingSchedule.GetDescription());

            trizLottery.UpdateSchedule(false, "Triz Midday 12:15PM", "0123456", 12, 15, now, "Admin");
            Assert.AreEqual("Triz Midday 12:15PM", trizSchedule.GetDescription());
            Assert.AreEqual("Triz Midday 12:15PM First Pair", pick2BeginningSchedule.GetDescription());
            Assert.AreEqual("Triz Midday 12:15PM Last Pair", pick2EndingSchedule.GetDescription());
            Assert.AreEqual("Triz Midday 12:15PM", pick3EndingSchedule.GetDescription());
            Assert.AreEqual("Triz Midday 12:15PM", pick4EndingSchedule.GetDescription());
            Assert.AreEqual("Triz Midday 12:15PM", pick5EndingSchedule.GetDescription());
        }

        [TestMethod]
        public void TrizLottery_Purchase_In_AttachDate_Test()
        {
            QueueInMemory queue = new QueueInMemory();
            Integration.Kafka = queue;

            Company company = new Company();
            CurrenciesTest.AddCurrencies();
            company.Sales.CreateStore(1, "Test Store").MakeCurrent();

            Product ticketPick = company.LotteryGamesPool.GetOrCreateProductById(1);
            ticketPick.Description = "Ticket pick 3 straight";
            ticketPick.Price = 1;

            DateTime now = new DateTime(2024, 11, 19, 00, 00, 00);
            company.LotteryGamesPool.TrizLotteryGame.GetLottery().Every(hour: 12, minute: 15, dayOfWeek: 5, now, "Admin");

            DateTime drawDate = new DateTime(2024, 11, 22, 12, 15, 00);
            DateTime attachDrawDate = new DateTime(2024, 11, 22, 10, 00, 00);

            bool hasSchedule = company.LotteryGamesPool.TrizLotteryGame.GetLottery().AnyScheduleAt(attachDrawDate);
            Assert.IsFalse(hasSchedule);

            Assert.ThrowsException<GameEngineException>(() =>
            {
                DateTime attachDrawInvalidDate = new DateTime(2024, 11, 23, 10, 00, 00);
                company.LotteryGamesPool.TrizLotteryGame.GetLottery().AttachToDate(drawDate, attachDrawInvalidDate, now, "admin");
            });


            DateTime oldAttachDrawDate = new DateTime(2024, 11, 22, 16, 00, 00);
            company.LotteryGamesPool.TrizLotteryGame.GetLottery().AttachToDate(drawDate, oldAttachDrawDate, now, "admin");
            company.LotteryGamesPool.TrizLotteryGame.GetLottery().EditAttachToDate(oldAttachDrawDate, attachDrawDate, now, "admin");

            hasSchedule = company.LotteryGamesPool.TrizLotteryGame.GetLottery().AnyScheduleAt(attachDrawDate);
            Assert.IsTrue(hasSchedule);

            Assert.ThrowsException<GameEngineException>(() =>
            {
                company.LotteryGamesPool.TrizLotteryGame.GetLottery().AttachToDate(drawDate, attachDrawDate, now, "admin");
            });

            Assert.ThrowsException<GameEngineException>(() =>
            {
                company.LotteryGamesPool.TrizLotteryGame.GetLottery().AttachToDate(attachDrawDate, drawDate, now, "admin");
            });

            bool hasPendingTickets = company.LotteryGamesPool.TrizLotteryGame.GetLottery().ExistPendingTicketsAt(attachDrawDate);
            Assert.IsFalse(hasPendingTickets);

            Domain domain = company.Sales.CreateDomain(false, 1, "localhost", PaymentChannels.Agents.INSIDER);
            Player player = company.GetOrCreateCustomerById("*********").Player;
            Customer customer = company.CustomerByPlayer(player);
            customer.Identifier = "*********";

            int orderNumber = 301952;
            var myOrder = company.NewOrder(customer, orderNumber, Currencies.CODES.USD);

            string gameType = "triz";
            string dates = "11/22/2024";
            string drawHours = "10:00 AM";
            string strIsPresentPlayerBeforeToCloseStore = "False";
            string strUseNextDate = "False";
            string states = "MX";
            string withFireBalls = "False";
            DateTime purchaseDate = new DateTime(2024, 11, 19, 00, 00, 00);
            company.LotteryGamesPool.TrizLotteryGame.NextDatesAccumulator.Calculate(domain, purchaseDate, gameType, dates, states, drawHours, withFireBalls, strIsPresentPlayerBeforeToCloseStore, strUseNextDate);

            var nextDatesAccumulator = company.LotteryGamesPool.TrizLotteryGame.NextDatesAccumulator;
            var numbers = new List<string> { "345" };
            myOrder = company.PrepareTicketOrderWithMultipleAuthorization(player, "MultipleInputSingleAmount", "S", (OrderCart)myOrder, nextDatesAccumulator, domain).Add(numbers, "MX", false, 1).Commit();
            company.AddOrders((OrderCart)myOrder);

            int lowReference = company.IdentitytBetNumber;
            Assert.AreEqual(1000001, lowReference);

            int highReference = (lowReference + myOrder.CountOfItems - 1);
            Assert.AreEqual(1000001, highReference);

            var auths = new AuthorizationsNumbers(*********);
            company.PurchaseTickets(itIsThePresent: false, (OrderCart)myOrder, purchaseDate, auths, domain, lowReference, orderNumber, "Artemis_ThirdParty_Deposit_USD");

            hasPendingTickets = company.LotteryGamesPool.TrizLotteryGame.GetLottery().ExistPendingTicketsAt(attachDrawDate);
            Assert.IsTrue(hasPendingTickets);

            DateTime lastVersionOfDate = new DateTime(2024, 11, 22, 23, 59, 59);
            var schedules = company.LotteryGamesPool.TrizLotteryGame.PendingAndNoRegradedSchedulesAt(lastVersionOfDate);
            Assert.IsTrue(schedules.Count() == 1);

            var drawsToBeConfirmed = company.LotteryGamesPool.TrizLotteryGame.DrawsToBeConfirmed();
            Assert.IsTrue(drawsToBeConfirmed.Count() == 0);

            drawsToBeConfirmed = company.LotteryGamesPool.DrawsToBeConfirmed();
            Assert.IsTrue(drawsToBeConfirmed.Count() == 0);

            Assert.ThrowsException<GameEngineException>(() =>
            {
                company.LotteryGamesPool.TrizLotteryGame.GetLottery().AttachToDate(attachDrawDate, drawDate, now, "admin");
            });

            // Gradear lottery
            DateTime gradeDate = new DateTime(2024, 11, 22, 10, 00, 00);
            LotteryTriz lotteryTriz = company.LotteryGamesPool.TrizLotteryGame.GetLottery();
            string winnerNumber = "12345";
            DrawingsSummaryReport drawingsSummaryReport = lotteryTriz.DrawTriz(gradeDate, winnerNumber, now, false, "N/A", false);
            Assert.AreEqual(1, drawingsSummaryReport.TotalWinners);
            Assert.AreEqual(1, drawingsSummaryReport.TotalPlayers);
            Assert.AreEqual(1, drawingsSummaryReport.TotalTickets);
            Assert.AreEqual(1, drawingsSummaryReport.TotalTicketAmount);
            Assert.AreEqual(900, drawingsSummaryReport.TotalPrize);

            lastVersionOfDate = new DateTime(2024, 11, 22, 23, 59, 59);
            schedules = company.LotteryGamesPool.TrizLotteryGame.PendingAndNoRegradedSchedulesAt(lastVersionOfDate);
            Assert.IsTrue(schedules.Count() == 0);

            schedules = company.LotteryGamesPool.TrizLotteryGame.FinishedAndRegradedSchedulesOf(lastVersionOfDate);
            Assert.IsTrue(schedules.Count() == 1);

            schedules = company.LotteryGamesPool.FinishedAndRegradedSchedulesOf(lastVersionOfDate);
            Assert.IsTrue(schedules.Count() == 1);

            bool canBeConfirmed = lotteryTriz.CanBeConfirmed(gradeDate);
            Assert.IsTrue(canBeConfirmed);

            drawsToBeConfirmed = company.LotteryGamesPool.TrizLotteryGame.DrawsToBeConfirmed();
            Assert.IsTrue(drawsToBeConfirmed.Count() == 1);

            drawsToBeConfirmed = company.LotteryGamesPool.DrawsToBeConfirmed();
            Assert.IsTrue(drawsToBeConfirmed.Count() == 1);

            // Confirm
            var confirmReport = lotteryTriz.ConfirmDraw(gradeDate, now, "N/A", false);
            Assert.AreEqual(1, confirmReport.TotalTickets);
            Assert.AreEqual(1, confirmReport.TotalPlayers);
            Assert.AreEqual(1, confirmReport.TotalWinners);
            Assert.AreEqual(900, confirmReport.TotalPrize);
        }

        [TestMethod]
        public void TrizLottery_Purchase_In_AttachDate_Christmas_Test()
        {
            QueueInMemory queue = new QueueInMemory();
            Integration.Kafka = queue;

            Company company = new Company();
            CurrenciesTest.AddCurrencies();
            company.Sales.CreateStore(1, "Test Store").MakeCurrent();

            Product ticketPick = company.LotteryGamesPool.GetOrCreateProductById(1);
            ticketPick.Description = "Ticket pick 3 straight";
            ticketPick.Price = 1;

            DateTime now = new DateTime(2024, 11, 20, 00, 00, 00);
            LotteryTriz lotteryTriz = company.LotteryGamesPool.TrizLotteryGame.GetLottery();
            lotteryTriz.Every(hour: 17, minute: 00, dayOfWeek: 0, now, "Admin");
            lotteryTriz.Every(hour: 17, minute: 00, dayOfWeek: 1, now, "Admin");
            lotteryTriz.Every(hour: 17, minute: 00, dayOfWeek: 2, now, "Admin");
            lotteryTriz.Every(hour: 17, minute: 00, dayOfWeek: 3, now, "Admin");
            lotteryTriz.Every(hour: 17, minute: 00, dayOfWeek: 4, now, "Admin");
            lotteryTriz.Every(hour: 17, minute: 00, dayOfWeek: 5, now, "Admin");
            lotteryTriz.Every(hour: 17, minute: 00, dayOfWeek: 6, now, "Admin");

            DateTime drawDate = new DateTime(2024, 12, 25, 17, 00, 00);
            DateTime newDrawDate = new DateTime(2024, 12, 25, 7, 00, 00);

            bool hasSchedule = lotteryTriz.AnyScheduleAt(newDrawDate);
            Assert.IsFalse(hasSchedule);

            lotteryTriz.AttachToDate(drawDate, newDrawDate, now, "admin");

            hasSchedule = lotteryTriz.AnyScheduleAt(newDrawDate);
            Assert.IsTrue(hasSchedule);

            bool hasPendingTickets = lotteryTriz.ExistPendingTicketsAt(newDrawDate);
            Assert.IsFalse(hasPendingTickets);

            Domain domain = company.Sales.CreateDomain(false, 1, "localhost", PaymentChannels.Agents.INSIDER);
            Player player = company.GetOrCreateCustomerById("*********").Player;
            Customer customer = company.CustomerByPlayer(player);
            customer.Identifier = "*********";

            int orderNumber = 301952;
            var myOrder = company.NewOrder(customer, orderNumber, Currencies.CODES.USD);

            string gameType = "triz";
            string dates = "12/25/2024";
            string drawHours = "7:00 AM";
            string strIsPresentPlayerBeforeToCloseStore = "False";
            string strUseNextDate = "False";
            string states = "MX";
            string withFireBalls = "False";
            company.LotteryGamesPool.TrizLotteryGame.NextDatesAccumulator.Calculate(domain, now, gameType, dates, states, drawHours, withFireBalls, strIsPresentPlayerBeforeToCloseStore, strUseNextDate);

            var nextDatesAccumulator = company.LotteryGamesPool.TrizLotteryGame.NextDatesAccumulator;
            var numbers = new List<string> { "345" };
            myOrder = company.PrepareTicketOrderWithMultipleAuthorization(player, "MultipleInputSingleAmount", "S", (OrderCart)myOrder, nextDatesAccumulator, domain).Add(numbers, "MX", false, 1).Commit();
            company.AddOrders((OrderCart)myOrder);

            int lowReference = company.IdentitytBetNumber;
            Assert.AreEqual(1000001, lowReference);

            int highReference = (lowReference + myOrder.CountOfItems - 1);
            Assert.AreEqual(1000001, highReference);

            var auths = new AuthorizationsNumbers(*********);
            company.PurchaseTickets(itIsThePresent: false, (OrderCart)myOrder, now, auths, domain, lowReference, orderNumber, "Artemis_ThirdParty_Deposit_USD");

            hasPendingTickets = company.LotteryGamesPool.TrizLotteryGame.GetLottery().ExistPendingTicketsAt(newDrawDate);
            Assert.IsTrue(hasPendingTickets);

            DateTime lastVersionOfDate = new DateTime(2024, 11, 22, 23, 59, 59);
            var schedules = company.LotteryGamesPool.TrizLotteryGame.PendingAndNoRegradedSchedulesAt(lastVersionOfDate);
            Assert.IsTrue(schedules.Count() == 1);

            var drawsToBeConfirmed = company.LotteryGamesPool.TrizLotteryGame.DrawsToBeConfirmed();
            Assert.IsTrue(drawsToBeConfirmed.Count() == 0);

            drawsToBeConfirmed = company.LotteryGamesPool.DrawsToBeConfirmed();
            Assert.IsTrue(drawsToBeConfirmed.Count() == 0);

            Assert.ThrowsException<GameEngineException>(() =>
            {
                company.LotteryGamesPool.TrizLotteryGame.GetLottery().AttachToDate(newDrawDate, drawDate, now, "admin");
            });

            // Gradear lottery
            string winnerNumber = "12345";
            DrawingsSummaryReport drawingsSummaryReport = lotteryTriz.DrawTriz(newDrawDate, winnerNumber, now, false, "N/A", false);
            Assert.AreEqual(1, drawingsSummaryReport.TotalWinners);
            Assert.AreEqual(1, drawingsSummaryReport.TotalPlayers);
            Assert.AreEqual(1, drawingsSummaryReport.TotalTickets);
            Assert.AreEqual(1, drawingsSummaryReport.TotalTicketAmount);
            Assert.AreEqual(900, drawingsSummaryReport.TotalPrize);

            lastVersionOfDate = new DateTime(2024, 12, 25, 23, 59, 59);
            schedules = company.LotteryGamesPool.TrizLotteryGame.PendingAndNoRegradedSchedulesAt(lastVersionOfDate);
            Assert.IsTrue(schedules.Count() == 0);

            schedules = company.LotteryGamesPool.TrizLotteryGame.FinishedAndRegradedSchedulesOf(lastVersionOfDate);
            Assert.IsTrue(schedules.Count() == 1);

            schedules = company.LotteryGamesPool.FinishedAndRegradedSchedulesOf(lastVersionOfDate);
            Assert.IsTrue(schedules.Count() == 1);

            bool canBeConfirmed = lotteryTriz.CanBeConfirmed(newDrawDate);
            Assert.IsTrue(canBeConfirmed);

            drawsToBeConfirmed = company.LotteryGamesPool.TrizLotteryGame.DrawsToBeConfirmed();
            Assert.IsTrue(drawsToBeConfirmed.Count() == 1);

            drawsToBeConfirmed = company.LotteryGamesPool.DrawsToBeConfirmed();
            Assert.IsTrue(drawsToBeConfirmed.Count() == 1);

            // Confirm
            var confirmReport = lotteryTriz.ConfirmDraw(newDrawDate, now, "N/A", false);
            Assert.AreEqual(1, confirmReport.TotalTickets);
            Assert.AreEqual(1, confirmReport.TotalPlayers);
            Assert.AreEqual(1, confirmReport.TotalWinners);
            Assert.AreEqual(900, confirmReport.TotalPrize);
        }

        [TestMethod]
        public void TimeZone_Test_Convertions()
        {
            DateTime cstTime = new DateTime(2024, 10, 20, 0, 0, 0);

            // Definir las zonas horarias
            TimeZoneInfo cstZone = TimeZoneInfo.FindSystemTimeZoneById("Central Standard Time");  // CST
            TimeZoneInfo cestZone = TimeZoneInfo.FindSystemTimeZoneById("Central Europe Standard Time");  // CEST

            DateTime cestTime = TimeZoneInfo.ConvertTime(cstTime, cstZone, cestZone);
            Assert.AreEqual(new DateTime(2024, 10, 20, 7, 0, 0), cestTime);
        }

        [TestMethod]
        public void TrizLottery_TimeZone_Draws()
        {
            CountryTimeZone tzCST = new CountryTimeZone { CountryCode = "CST", Windows = "Central Standard Time", Linux = "America/Mexico_City" };
            CountryTimeZone tzTST = new CountryTimeZone { CountryCode = "TST", Windows = "Tokyo Standard Time", Linux = "Asia/Tokyo" };
            CountryTimeZone tzCET = new CountryTimeZone { CountryCode = "CET", Windows = "Central Europe Standard Time", Linux = "Europe/Madrid" };

            TimeZoneConverter.Instance.Configure(new List<CountryTimeZone> { tzCST, tzTST, tzCET });

            Company company = new Company();
            company.TimeZone = "CET";

            CurrenciesTest.AddCurrencies();
            company.Sales.CreateStore(1, "Test Store").MakeCurrent();
            Domain domain = company.Sales.CreateDomain(false, 1, "localhost", PaymentChannels.Agents.INSIDER);

            var trizLottery = company.LotteryGamesPool.TrizLotteryGame.GetLottery();

            // European is Server hour
            var serverNow = new DateTime(2024, 10, 21, 00, 00, 00);

            // Set Draw only Mondays at 3:00 PM Server Time Europe
            trizLottery.Every(hour: 15, minute: 00, dayOfWeek: 1, serverNow, "Admin");

            // Get Schedule
            var schedule = trizLottery.GetSchedule(hour: 15, minute: 00);

            DateTime drawDate = trizLottery.NextValidDrawDate(schedule, serverNow);
            Assert.AreEqual(new DateTime(2024, 10, 21, 15, 00, 00), drawDate);

            drawDate = trizLottery.NextValidDrawDate(schedule, serverNow, toCountryTimeZone: "CET");
            Assert.AreEqual(new DateTime(2024, 10, 21, 15, 00, 00), drawDate);

            drawDate = trizLottery.NextValidDrawDate(schedule, serverNow, toCountryTimeZone: "TST");
            Assert.AreEqual(new DateTime(2024, 10, 21, 22, 00, 00), drawDate);

            drawDate = trizLottery.NextValidDrawDate(schedule, serverNow, toCountryTimeZone: "CST");
            Assert.AreEqual(new DateTime(2024, 10, 21, 08, 00, 00), drawDate);
        }

        [TestMethod]
        public void TimeZone_AllDrawsPerDay()
        {
            CountryTimeZone tzCET = new CountryTimeZone { CountryCode = "CET", Windows = "Central Europe Standard Time", Linux = "Europe/Madrid" };
            CountryTimeZone tzCST = new CountryTimeZone { CountryCode = "CST", Windows = "Central Standard Time", Linux = "America/Mexico_City" };
            CountryTimeZone tzTST = new CountryTimeZone { CountryCode = "TST", Windows = "Tokyo Standard Time", Linux = "Asia/Tokyo" };

            TimeZoneConverter.Instance.Configure(new List<CountryTimeZone> { tzCET, tzCST, tzTST });

            Company company = new Company();
            company.TimeZone = "CET";

            CurrenciesTest.AddCurrencies();
            company.Sales.CreateStore(1, "Test Store").MakeCurrent();
            Domain domain = company.Sales.CreateDomain(false, 1, "localhost", PaymentChannels.Agents.INSIDER);

            var trizLottery = company.LotteryGamesPool.TrizLotteryGame.GetLottery();

            // serverNow is in European
            var serverNow = new DateTime(2024, 11, 25, 00, 00, 00);

            // Set Draw only Mondays at 3:00 PM. TimeZone from company
            trizLottery.Every(hour: 15, minute: 00, dayOfWeek: 1, serverNow, "Admin");

            DateTime lastVersionOfDate = new DateTime(2024, 11, 25, 23, 59, 59);
            var schedules = company.LotteryGamesPool.PendingAndNoRegradedSchedulesAt(lastVersionOfDate);

            var schedule = schedules.First();
            var nearestDrawDate = schedule.ToDateTime(serverNow);
            Assert.AreEqual(new DateTime(2024, 11, 25, 15, 00, 00), nearestDrawDate);

            nearestDrawDate = schedule.ToDateTime(serverNow, "CET");
            Assert.AreEqual(new DateTime(2024, 11, 25, 15, 00, 00), nearestDrawDate);

            nearestDrawDate = schedule.ToDateTime(serverNow, "TST");
            Assert.AreEqual(new DateTime(2024, 11, 25, 23, 00, 00), nearestDrawDate);

            nearestDrawDate = schedule.ToDateTime(serverNow, "CST");
            Assert.AreEqual(new DateTime(2024, 11, 25, 08, 00, 00), nearestDrawDate);
        }

        [TestMethod]
        public void TimeZone_Player_TrizDraws_ByTime()
        {
            CountryTimeZone tzCET = new CountryTimeZone { CountryCode = "CET", Windows = "Central Europe Standard Time", Linux = "Europe/Madrid" };
            CountryTimeZone tzCST = new CountryTimeZone { CountryCode = "CST", Windows = "Central Standard Time", Linux = "America/Mexico_City" };
            CountryTimeZone tzTST = new CountryTimeZone { CountryCode = "TST", Windows = "Tokyo Standard Time", Linux = "Asia/Tokyo" };

            TimeZoneConverter.Instance.Configure(new List<CountryTimeZone> { tzCET, tzCST, tzTST });

            Company company = new Company();
            company.TimeZone = "CET";

            CurrenciesTest.AddCurrencies();
            company.Sales.CreateStore(1, "Test Store").MakeCurrent();
            Domain domain = company.Sales.CreateDomain(false, 1, "localhost", PaymentChannels.Agents.INSIDER);

            var serverNow = new DateTime(2024, 10, 21, 00, 00, 00);

            var trizLottery = company.LotteryGamesPool.TrizLotteryGame.GetLottery();
            trizLottery.Every(hour: 15, minute: 00, dayOfWeek: 1, serverNow, "Admin");

            State s = company.LotteryGamesPool.PicksLotteryGame.GetOrCreateState("GA", "Georgia");
            LotteryPick<Pick5> lotteryPick5 = (LotteryPick<Pick5>)company.LotteryGamesPool.PicksLotteryGame.GetOrCreateLottery(5, s);
            lotteryPick5.Every(hour: 12, minute: 00, dayOfWeek: 1, serverNow, "Admin");

            var schedules = company.LotteryGamesPool.TrizLotteryGame.SchedulesByTimeOf(serverNow, domain);
            var schedule = schedules.First();
            Assert.IsTrue(schedules.Count() == 1);

            DateTime nextDateSchedule = schedule.ToDateTime(serverNow);
            Assert.AreEqual(new DateTime(2024, 10, 21, 15, 00, 00), nextDateSchedule);

            nextDateSchedule = schedule.ToDateTime(serverNow, "CET");
            Assert.AreEqual(new DateTime(2024, 10, 21, 15, 00, 00), nextDateSchedule);

            nextDateSchedule = schedule.ToDateTime(serverNow, "CST");
            Assert.AreEqual(new DateTime(2024, 10, 21, 08, 00, 00), nextDateSchedule);

            nextDateSchedule = schedule.ToDateTime(serverNow, "TST");
            Assert.AreEqual(new DateTime(2024, 10, 21, 22, 00, 00), nextDateSchedule);

            schedules = company.LotteryGamesPool.PicksLotteryGame.SchedulesByTimeOf(serverNow, domain);
            schedule = schedules.First();
            Assert.IsTrue(schedules.Count() == 1);

            nextDateSchedule = schedule.ToDateTime(serverNow);
            Assert.AreEqual(new DateTime(2024, 10, 21, 12, 00, 00), nextDateSchedule);

            nextDateSchedule = schedule.ToDateTime(serverNow, "CET");
            Assert.AreEqual(new DateTime(2024, 10, 21, 12, 00, 00), nextDateSchedule);

            nextDateSchedule = schedule.ToDateTime(serverNow, "CST");
            Assert.AreEqual(new DateTime(2024, 10, 21, 05, 00, 00), nextDateSchedule);

            nextDateSchedule = schedule.ToDateTime(serverNow, "TST");
            Assert.AreEqual(new DateTime(2024, 10, 21, 19, 00, 00), nextDateSchedule);
        }

        [TestMethod]
        public void GetRiskPerLotteries()
        {
            Company company = new Company();
            CurrenciesTest.AddCurrencies();
            company.Sales.CreateStore(1, "Test Store").MakeCurrent();

            DateTime now = new DateTime(2024, 10, 04, 00, 00, 00);
            LotteryTriz trizLottery = company.LotteryGamesPool.TrizLotteryGame.GetLottery();
            for (int i = 0; i <= 6; i++) trizLottery.Every(12, 15, i, now, "Game Admin");
            for (int i = 0; i <= 6; i++) trizLottery.Every(15, 0, i, now, "Game Admin");
            GamesEngine.Time.Schedule trizSchedule = trizLottery.GetSchedule(hour: 12, minute: 15);

            var riskPerLotteries = trizLottery.GetRiskPerLotteries(company.LotteryGamesPool.RiskProfiles.DefaultRiskProfile.Risks.Risk);
            Assert.AreEqual(5, riskPerLotteries.Count());
        }
    }
}
