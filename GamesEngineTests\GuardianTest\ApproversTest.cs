﻿using GamesEngine.Business;
using GamesEngine.Exchange;
using GamesEngine.Finance;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using System;
using System.Collections.Generic;
using System.Linq;
using GamesEngine;
using Unit.Games.Tools;
using GamesEngine.Settings;

using GamesEngine.Custodian;
using System.Threading.Tasks;
using GamesEngine.Custodian.Operations;
using GamesEngineTests.Custodian;
using GamesEngine.Custodian.Persistance;
using town.connectors.drivers;
using static GamesEngine.Business.WholePaymentProcessor;
using GamesEngineTests.Unit_Tests.Games.Lotto;

namespace GamesEngineTests.Exchange
{

	[TestClass]
	public class ApproversTest
	{
		[TestMethod]
		public void ApproversValidations()
		{
			try
			{
				new Approver(1, "");
				Assert.Fail($"The {nameof(Approver)} emails it´s not valid.");
			}
			catch (GameEngineException e) { }

			try
			{
				new Approver(1, "a");
				Assert.Fail($"The {nameof(Approver)} emails it´s not valid.");
			}
			catch (GameEngineException e) { }

			try
			{
				new Approver(1, "a@");
				Assert.Fail($"The {nameof(Approver)} emails it´s not valid.");
			}
			catch (GameEngineException e) { }

			try
			{
				new Approver(1, "a@a");
				Assert.Fail($"The {nameof(Approver)} emails it´s not valid.");
			}
			catch (GameEngineException e) { }
			try
			{
				new Approver(1, "a@a.");
				Assert.Fail($"The {nameof(Approver)} emails it´s not valid.");
			}
			catch (GameEngineException e) { }

			Approver a = new Approver(1, "a@a.c");
			Assert.AreEqual("a@a.c", a.EmailAddress);
		}

		[TestMethod]
		public async Task ProfilessValidationsAsync()
		{
			Company cia;
			CurrenciesTest.AddCurrencies();
			Guardian guardian;
			QueueInMemory queue;
			DateTime today = DateTime.Now;
			GuardianTest.Init(today, out cia, out guardian, out queue);
		
			AccountNumbers accounts = guardian.Accounts();
			bool itIsThePresent = true;
			Transactions transactions = Transactions.GetInstance();

			AccountNumber account = accounts.Search("3770-4512-5467-6352");

			PaymentProcessorsAndActionsByDomains processsors = guardian.PaymentProcessors();
			Profiles profiles = guardian.Profiles();
			Approvers approvers = guardian.Approvers();

			int profeileId = profiles.NextProfileId();
			Profile profiletest = profiles.CreateProfile(itIsThePresent, profeileId, "Test");

			profeileId = profiles.NextProfileId();
			Profile profiletest2 = profiles.CreateProfile(itIsThePresent, profeileId, "Test2");

			try
			{
				profeileId = profiles.NextProfileId();
				Profile profiletest3 = profiles.CreateProfile(itIsThePresent, profeileId, "Test2");
				Assert.Fail($"The {nameof(Profile)} already exists in {nameof(Profiles)}");
			}
			catch (GameEngineException e) { }

			Assert.AreEqual(6, profiles.Count());

			int approverId = guardian.Approvers().NextApproverId();
			Approver approver = approvers.Create(itIsThePresent, approverId, "<EMAIL>");
			profiletest.Add(approver);
			try
			{
				approverId = guardian.Approvers().NextApproverId();
				approver = approvers.Create(itIsThePresent, approverId, "<EMAIL>");
				profiletest.Add(approver);
				Assert.Fail("The email already exists");
			}
			catch (GameEngineException e) { }
			approverId = guardian.Approvers().NextApproverId();
			Assert.AreEqual(6, approverId);

			approver = approvers.Create(itIsThePresent, approverId, "<EMAIL>");
			profiletest.Add(approver);

			Assert.AreEqual(6, profiles.Count());


			PaymentProcessor processsor  = processsors.SearchById("TEST-Secrets-TestDepositBTC-BTC-1");

			string domainUrl = "localhost";
			int domainId = 1;

			Dollar withdrawalAmount = new Dollar(4);

			Operations operations = guardian.Operations();
			PendingWithDrawal withdrawal = operations.CreateWithdrawal(itIsThePresent, DateTime.Now, 1, 1, "5D2428442", withdrawalAmount, processsor.Group.Id, processsor, domainId, domainUrl, "Bart");

			DateTime scheculeddate1 = today.AddDays(1);
			DateTime scheculeddate2 = today.AddDays(3);
			DateTime scheculeddate3 = today.AddDays(5);
			DisbursementsChanges changes1 = new DisbursementsChanges(today, guardian);
			changes1.Add(itIsThePresent, 1, account.Number, scheculeddate1, 1);
			changes1.Add(itIsThePresent, 2, account.Number, scheculeddate2, 1);
			changes1.Add(itIsThePresent, 3, account.Number, scheculeddate3, 2);
			withdrawal.Apply(itIsThePresent, today, changes1);

			GuardianTest.ValidateAccountsCreations(queue);

			string msn = queue.Dequeue(Integration.Kafka.TopicForGuardianInternalOperations);
			ProfileCreationMessage profileCreationMessage = (ProfileCreationMessage)InternalOperationMessage.Factory(msn);
			Assert.AreEqual("Test", profileCreationMessage.Name);

			msn = queue.Dequeue(Integration.Kafka.TopicForGuardianInternalOperations);
			profileCreationMessage = (ProfileCreationMessage)InternalOperationMessage.Factory(msn);
			Assert.AreEqual("Test2", profileCreationMessage.Name);

			msn = queue.Dequeue(Integration.Kafka.TopicForGuardianInternalOperations);
			var accountCreationMessage = (ApproverCreationMessage)InternalOperationMessage.Factory(msn);
			Assert.AreEqual("<EMAIL>", accountCreationMessage.Email);

			msn = queue.Dequeue(Integration.Kafka.TopicForGuardianInternalOperations);
			accountCreationMessage = (ApproverCreationMessage)InternalOperationMessage.Factory(msn);
			Assert.AreEqual("<EMAIL>", accountCreationMessage.Email);

			msn = queue.Dequeue(Integration.Kafka.TopicForGuardianInternalOperations);
			InternalOperationUpdateMessage withdrawalFromKafka = (InternalOperationUpdateMessage)InternalOperationMessage.Factory(msn);
			Assert.AreEqual(withdrawal.Description, withdrawalFromKafka.Description);
			Assert.AreEqual(withdrawal.DisbursementAmount.Value, withdrawalFromKafka.DisbursementAmount.Value);
			Assert.AreEqual(withdrawal.DisbursementAmount.CurrencyCode, withdrawalFromKafka.DisbursementAmount.CurrencyCode);
			Assert.AreEqual(withdrawal.Group, withdrawalFromKafka.Group);
			Assert.AreEqual(withdrawal.Processor.Driver.Id, withdrawalFromKafka.ProcessorId);
			Assert.AreEqual(withdrawal.TransactionId, withdrawalFromKafka.TransactionId);

			transactions.Save(withdrawalFromKafka);

			IEnumerable<StoredOperation> storedPendingOperation = await transactions.ListPendingOperationsAsync(1, OperationSchedule.Both, today.AddDays(-10), today.AddDays(10), 100, 0);
			Assert.AreEqual(true, storedPendingOperation.Count() > 0);

			Integration.RestoreToDefaultSettings();
            //GamesEngine.Custodian.PaymentMethodsActions.ResetInstance();
		}

	}
}
