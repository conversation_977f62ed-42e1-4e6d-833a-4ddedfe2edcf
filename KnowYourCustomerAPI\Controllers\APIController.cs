﻿using System;
using System.Collections.Generic;
using System.Dynamic;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Runtime.Serialization;
using System.Text;
using System.Threading.Tasks;
using Elasticsearch.Net;
using GamesEngine;
using GamesEngine.Customers;
using GamesEngine.Logs;
using GamesEngine.PurchaseOrders;
using GamesEngine.Settings;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Puppeteer.EventSourcing;
using town.connectors.commons;
using static GamesEngine.Finance.PaymentChannels;
using static KnowYourCustomerAPI.Security;

namespace KnowYourCustomerAPI.Controllers
{
    public class APIController : AuthorizeController
    {
        public const string INDEX_CUSTOMER_NAME = "customers";
        public const string INDEX_CUSTOMER_LOG_NAME = "customerslog";

        private async Task<StringResponse> SearchBasicCustomerInfoAsync(string identificator)
        {
            var normalizedIdentificator = identificator.Trim().ToLower();
            string query = $@"
                {{
                    ""query"": {{
                        ""bool"": {{ 
                            ""minimum_should_match"": 1,
                            ""should"": [
                            {{ 
                                ""match"": {{ 
                                    ""{Fields.ID_IDENTIFICATION_NUMBER}"": ""{normalizedIdentificator}""
                                }}
                            }},
                            {{ 
                                ""match"": {{ 
                                    ""{Fields.ID_ACCOUNT_NUMBER}"": ""{normalizedIdentificator}""
                                }}
                            }},
                            {{ 
                                ""match_phrase_prefix"": {{ 
                                    ""{Fields.ID_EMAIL}"": ""{normalizedIdentificator}""
                                }}
                            }}
                            ]
                        }}
                    }},
                    ""_source"": [
                        ""{Fields.ID_IDENTIFICATION_NUMBER}"",
                        ""{Fields.ID_ACCOUNT_NUMBER}"",
                        ""{Fields.ID_EMAIL}"",
                        ""{Fields.ID_FIRST_NAME}"",
                        ""{Fields.ID_LAST_NAME}""
                    ]
                }}
            ";
            return await SearchCustomerByQueryAsync(query);
        }

        private async Task<StringResponse> SearchCustomerTypeAsync(string identificationNumber)
        {
            identificationNumber = identificationNumber.Trim().ToUpper();
            string query = $@"
                {{
                    ""query"": {{
                        ""match"": {{ 
                            ""{Fields.ID_IDENTIFICATION_NUMBER}"": ""{identificationNumber}""
                        }}
                    }},
                    ""_source"": [
                        ""{Fields.ID_IDENTIFICATION_NUMBER}"",
                        ""{CustomerForm.PROPERTY_CUSTOMER_TYPE_ID}""
                    ]
                }}
            ";
            return await SearchCustomerByQueryAsync(query);
        }

        string[] uniqueIdentificators = new string[] { Fields.ID_IDENTIFICATION_NUMBER, Fields.ID_ACCOUNT_NUMBER, Fields.ID_EMAIL };
        string[] uniqueIdentificatorsForQuery = new string[] { Fields.ID_IDENTIFICATION_NUMBER, Fields.ID_ACCOUNT_NUMBER, Fields.ID_EMAIL+ ".keyword" };
        private async Task<StringResponse> SearchCustomerTypesAsync(IEnumerable<string> identificators)
        {
            var identificatorsAsText = string.Join($"\", \"", identificators);
            var strMatchingIdentificators = new StringBuilder();
            var isFirstTime = true;
            foreach (var identificator in uniqueIdentificatorsForQuery)
            {
                if (isFirstTime) isFirstTime = !isFirstTime;
                else strMatchingIdentificators.Append(',');
                strMatchingIdentificators.Append('{').AppendLine()
                    .Append("\"terms\": { ").AppendLine()
                    .Append('"').Append(identificator).Append("\": [\"").Append(identificatorsAsText).Append("\"]").AppendLine()
                    .Append('}').AppendLine()
                    .Append('}').AppendLine();
            }
            string query = $@"
                {{
                    ""query"": {{
                        ""bool"": {{ 
                            ""minimum_should_match"": 1,
                            ""should"": [
                            {strMatchingIdentificators}
                            ]
                        }}
                    }},
                    ""_source"": [
                        ""{Fields.ID_IDENTIFICATION_NUMBER}"",
                        ""{Fields.ID_ACCOUNT_NUMBER}"",
                        ""{Fields.ID_EMAIL}"",
                        ""{Fields.ID_AVATAR}"",
                        ""{CustomerForm.PROPERTY_CUSTOMER_TYPE_ID}""
                    ]
                }}
            ";
            return await SearchCustomerByQueryAsync(query);
        }

        async Task<StringResponse> SearchFullCustomerInfoAsync(string id)
        {
            string query = $@"
                {{
                    ""query"": {{
                        ""term"": {{ 
                            ""_id"": ""{id}""
                        }}
                    }}
                }}
            ";
            return await SearchCustomerByQueryAsync(query);
        }

        private async Task<StringResponse> SearchFullCustomerInfoAsync(string identificationNumber, string accountNumber, string email)
        {
            string query = $@"
                {{
                    ""query"": {{
                        ""bool"": {{ 
                            ""minimum_should_match"": 1,
                            ""should"": [
                            {{ 
                                ""match"": {{ 
                                    ""{Fields.ID_IDENTIFICATION_NUMBER}"": ""{identificationNumber}""
                                }}
                            }},
                            {{ 
                                ""match"": {{ 
                                    ""{Fields.ID_ACCOUNT_NUMBER}"": ""{accountNumber}""
                                }}
                            }},
                            {{ 
                                ""match_phrase_prefix"": {{ 
                                    ""{Fields.ID_EMAIL}"": ""{email}""
                                }}
                            }}
                            ]
                        }}
                    }}
                }}
            ";
            return await SearchCustomerByQueryAsync(query);
        }

        private async Task<StringResponse> SearchCustomerByQueryAsync(string query)
        {
            HttpELKClient client = HttpELKClient.GetInstance();
            Loggers.GetIntance().SearchEngine.Debug($"index: customers query:{query}");
            var response = await client.SearchAsync(INDEX_CUSTOMER_NAME, query);
            Loggers.GetIntance().SearchEngine.Debug($"index: customers response:{response}");

            if (!response.Success)
            {
                string error = $"Body: {response.Body} \n OriginalException:{response.OriginalException}";
                ErrorsSender.Send(error, $"Error searching. ");
            }

            return response;
        }

        private const string ALL_SELECTED = "all";

        private async Task<StringResponse> ListApprovedCustomersAsync(DateTime startDate, DateTime endDate, string identificationNumber, int initialIndex, int amountOfRows)
        {
            var allIdentificationNumberSelected = identificationNumber.ToLower().Trim() == ALL_SELECTED;
            string strMatchingIdentificationNumber = string.Empty;
            if (!allIdentificationNumberSelected)
            {
                strMatchingIdentificationNumber = $@",{{
                                    ""match"": {{
                                        ""{Fields.ID_IDENTIFICATION_NUMBER}"": ""{identificationNumber}""
                                    }}
                                }}";
            }

            string query = $@"
                {{
                    ""from"": {initialIndex},
                    ""size"": {amountOfRows},
                    ""query"": {{
                        ""bool"": {{
                            ""must"": [
                                {{
                                    ""match"": {{
                                        ""{CustomerForm.PROPERTY_STATUS}"": ""{Customer.CustomerStatus.Approved.ToString()}""
                                    }}
                                }}
                                {strMatchingIdentificationNumber}
                            ],
                            ""filter"": [
                                {{
                                    ""range"": {{
                                        ""{CustomerForm.PROPERTY_CREATION_DATE}"": {{
                                            ""lte"": ""{endDate.AddDays(1).ToString("MM/dd/yyyy HH:mm:ss")}"",
                                            ""gte"": ""{startDate.ToString("MM/dd/yyyy HH:mm:ss")}"",
                                            ""format"": ""MM/dd/yyyy HH:mm:ss""
                                        }}
                                    }}
                                }}
                            ]
                        }}
                    }},
                    ""_source"": [
                        ""{Fields.ID_IDENTIFICATION_NUMBER}"",
                        ""{Fields.ID_ACCOUNT_NUMBER}"",
                        ""{Fields.ID_EMAIL}"",
                        ""{CustomerForm.PROPERTY_CREATION_DATE}"",
                        ""{CustomerForm.PROPERTY_APPROVAL_DATE}"",
                        ""{CustomerForm.PROPERTY_STATUS}""
                    ]
                }}
            ";

            var response = await SearchCustomerByQueryAsync(query);
            return response;
        }

        private async Task<StringResponse> ListDraftedCustomersAsync(DateTime startDate, DateTime endDate, string identificationNumber, int initialIndex, int amountOfRows)
        {
            var allIdentificationNumberSelected = identificationNumber.ToLower().Trim() == ALL_SELECTED;
            string strMatchingIdentificationNumber = string.Empty;
            if (!allIdentificationNumberSelected)
            {
                strMatchingIdentificationNumber = $@",{{
                                    ""match"": {{
                                        ""{Fields.ID_IDENTIFICATION_NUMBER}"": ""{identificationNumber}""
                                    }}
                                }}";
            }

            string query = $@"
                {{
                    ""from"": {initialIndex},
                    ""size"": {amountOfRows},
                    ""query"": {{
                        ""bool"": {{
                            ""must"": [
                                {{
                                    ""match"": {{
                                        ""{CustomerForm.PROPERTY_STATUS}"": ""{Customer.CustomerStatus.Drafted.ToString()}""
                                    }}
                                }}
                                {strMatchingIdentificationNumber}
                            ],
                            ""filter"": [
                                {{
                                    ""range"": {{
                                        ""{CustomerForm.PROPERTY_CREATION_DATE}"": {{
                                            ""lte"": ""{endDate.AddDays(1).ToString("MM/dd/yyyy HH:mm:ss")}"",
                                            ""gte"": ""{startDate.ToString("MM/dd/yyyy HH:mm:ss")}"",
                                            ""format"": ""MM/dd/yyyy HH:mm:ss""
                                        }}
                                    }}
                                }}
                            ]
                        }}
                    }}
                }}
            ";

            var response = await SearchCustomerByQueryAsync(query);
            return response;
        }

        private async Task<StringResponse> ListRejectedCustomersAsync(DateTime startDate, DateTime endDate, string identificationNumber, int initialIndex, int amountOfRows)
        {
            var allIdentificationNumberSelected = identificationNumber.ToLower().Trim() == ALL_SELECTED;
            string strMatchingIdentificationNumber = string.Empty;
            if (!allIdentificationNumberSelected)
            {
                strMatchingIdentificationNumber = $@",{{
                                    ""match"": {{
                                        ""{Fields.ID_IDENTIFICATION_NUMBER}"": ""{identificationNumber}""
                                    }}
                                }}";
            }

            string query = $@"
                {{
                    ""from"": {initialIndex},
                    ""size"": {amountOfRows},
                    ""query"": {{
                        ""bool"": {{
                            ""must"": [
                                {{
                                    ""match"": {{
                                        ""{CustomerForm.PROPERTY_STATUS}"": ""{Customer.CustomerStatus.Rejected.ToString()}""
                                    }}
                                }}
                                {strMatchingIdentificationNumber}
                            ],
                            ""filter"": [
                                {{
                                    ""range"": {{
                                        ""{CustomerForm.PROPERTY_CREATION_DATE}"": {{
                                            ""lte"": ""{endDate.AddDays(1).ToString("MM/dd/yyyy HH:mm:ss")}"",
                                            ""gte"": ""{startDate.ToString("MM/dd/yyyy HH:mm:ss")}"",
                                            ""format"": ""MM/dd/yyyy HH:mm:ss""
                                        }}
                                    }}
                                }}
                            ]
                        }}
                    }}
                }}
            ";

            var response = await SearchCustomerByQueryAsync(query);
            return response;
        }

        private async Task<StringResponse> SearchFullCustomerLogAsync(string identifier)
        {
            string query = $@"
                {{
                    ""query"": {{
                        ""match"": {{ 
                            ""log_id"": ""{identifier}""
                        }}
                    }}
                }}
            ";

            HttpELKClient client = HttpELKClient.GetInstance();
            Loggers.GetIntance().SearchEngine.Debug($"index: customers query:{query}");
            var response = await client.SearchAsync(INDEX_CUSTOMER_LOG_NAME, query);
            Loggers.GetIntance().SearchEngine.Debug($"index: customers response:{response}");

            if (!response.Success)
            {
                string error = $"Body: {response.Body} \n OriginalException:{response.OriginalException}";
                ErrorsSender.Send(error, $"Error searching. ");
            }

            return response;
        }

        private async Task<string> GetCustomerDataOrNullAsync(string identificationNumber, string accountNumber, string email)
        {
            var result = await SearchFullCustomerInfoAsync(identificationNumber, accountNumber, email);

            string jsonString = result.Body;
            if (string.IsNullOrWhiteSpace(jsonString)) return null;
            var reader = new JsonTextReader(new StringReader(jsonString));
            reader.FloatParseHandling = FloatParseHandling.Decimal;

            string resultInJson = string.Empty;
            JObject obj = JObject.Load(reader);
            decimal amountOfHits = decimal.Parse(obj["hits"]["total"]["value"].ToString());
            if (amountOfHits == 1)
            {
                resultInJson = obj["hits"]["hits"][0]["_source"].ToString();
            }
            else
            {
                return null;
            }

            return resultInJson;
        }

        private async Task<string> GetCustomerDataOrNullAsync(string id)
        {
            var result = await SearchFullCustomerInfoAsync(id);

            string jsonString = result.Body;
            if (string.IsNullOrWhiteSpace(jsonString)) return null;
            var reader = new JsonTextReader(new StringReader(jsonString));
            reader.FloatParseHandling = FloatParseHandling.Decimal;

            string resultInJson = string.Empty;
            JObject obj = JObject.Load(reader);
            decimal amountOfHits = decimal.Parse(obj["hits"]["total"]["value"].ToString());
            if (amountOfHits == 1)
            {
                resultInJson = obj["hits"]["hits"][0]["_source"].ToString();
            }
            else
            {
                return null;
            }

            return resultInJson;
        }

        private async Task<IActionResult> CreateCustomerIfNotExistsAsync(string domain, string accountNumber, string identificationNumber)
        {
            if (string.IsNullOrWhiteSpace(domain)) return BadRequest($"Parameter {nameof(domain)} is required");

            var existResult = await KnowYourCustomerAPI.KnowYourCustomer.PerformQryAsync(HttpContext, $@"
				{{
					existsCustomer = company.ExistsCustomerByIdentifier('{identificationNumber}');
					if(!existsCustomer)
					{{
						existsCustomer = company.ExistsCustomer('{accountNumber}');
						print ! existsCustomer theCustomerItsNewOne;
					}}
                    else 
                    {{
                        print ! existsCustomer theCustomerItsNewOne;
                    }}
				}}
			");

            if (!(existResult is OkObjectResult))
            {
                return BadRequest($@"Error:{((ObjectResult)existResult).Value.ToString()}");
            }

            var obj = (OkObjectResult)existResult;
            string jsonCustomer = obj.Value.ToString();
            var customerExistence = JsonConvert.DeserializeObject<CustomerExistence>(jsonCustomer);

            IActionResult result = null;
            if (customerExistence.TheCustomerItsNewOne)
            {
                var commandAssigningIdentifier = string.IsNullOrWhiteSpace(identificationNumber) ? string.Empty : $"customer.Identifier='{identificationNumber}';";
                result = await KnowYourCustomerAPI.KnowYourCustomer.PerformCmdAsync(HttpContext, $@"
					customer = company.GetOrCreateCustomerById('{accountNumber}');
					{commandAssigningIdentifier}
                    player = customer.Player;
                    print player.Id uuid;
                    print player.Id password;
                    print '{accountNumber}' accountNumber;
				");
            }
            else
            {
                result = await KnowYourCustomerAPI.KnowYourCustomer.PerformQryAsync(HttpContext, $@"
                {{
                    customer = company.CustomerByAccountNumber('{accountNumber}');
                    playerQry = customer.Player;
					print playerQry.Id uuid;
                    print playerQry.Id password;
                    print customer.AccountNumber accountNumber;
                }}
                ");
            }

            return result;
        }

        BytesResponse DeleteCustomer(string id)
        {
            string query = $@"
                {{
                    ""query"": {{
                        ""term"": {{ 
                            ""_id"": ""{id}""
                        }}
                    }}
                }}
            ";
            HttpELKClient client = HttpELKClient.GetInstance();
            Loggers.GetIntance().SearchEngine.Debug($"index: customers query:{query}");
            var response = client.DeleteDocumentByQuery(APIController.INDEX_CUSTOMER_NAME, query);
            Loggers.GetIntance().SearchEngine.Debug($"index: customers response:{response}");

            if (!response.Success)
            {
                string error = $"Body: {response.Body} \n OriginalException:{response.OriginalException}";
                ErrorsSender.Send(error, $"Error searching. ");
            }

            return response;
        }

        [DataContract(Name = "CustomerUUidResponse")]
        public class CustomerUUidResponse
        {
            [DataMember(Name = "uuid")]
            public string Uuid { get; set; }
            [DataMember(Name = "password")]
            public string Password { get; set; }
            [DataMember(Name = "accountNumber")]
            public string AccountNumber { get; set; }

        }

        [HttpPost("api/KnowYourCustomer/customers")]
        [Authorize(Roles = "e1,e23")]
        public async Task<IActionResult> CreateOrUpdateCustomerAsync()
        {
            string body = null;
            using (StreamReader reader = new StreamReader(Request.Body, Encoding.UTF8))
            {
                body = await reader.ReadToEndAsync();
            }
            if (body == null) return BadRequest("Body is required");

            string employeeName = Security.UserName(HttpContext);

            var fieldsJson = JsonConvert.DeserializeObject<IDictionary<string, object>>(body);
            if (fieldsJson.Count==0) return BadRequest("Body has not fields");
            if (!fieldsJson.ContainsKey(CustomerForm.PROPERTY_TEMPLATE_VERSION)) return BadRequest($"'{CustomerForm.PROPERTY_TEMPLATE_VERSION}' property is required.");

            object objTemplateId;
            fieldsJson.TryGetValue(CustomerForm.PROPERTY_TEMPLATE_ID, out objTemplateId);
            if (objTemplateId == null) return BadRequest($"'{CustomerForm.PROPERTY_TEMPLATE_ID}' property is required.");
            var templateId = objTemplateId.ToString();

            fieldsJson.TryGetValue(Fields.ID_IDENTIFICATION_NUMBER, out object objIdentificationNumber);
            var hasIdentificationNumber = objIdentificationNumber != null;
            var identificationNumber = !hasIdentificationNumber ? string.Empty : objIdentificationNumber.ToString();
;
            fieldsJson.TryGetValue(Fields.ID_ACCOUNT_NUMBER, out object objAccountNumber);
            var hasAccountNumber = objAccountNumber != null;
            var accountNumber = !hasAccountNumber ? string.Empty : objAccountNumber.ToString();

            object objEmail;
            fieldsJson.TryGetValue(Fields.ID_EMAIL, out objEmail);
            var hasEmail = objEmail != null;
            var email = !hasEmail ? string.Empty : objEmail.ToString();

            fieldsJson.TryGetValue(CustomerForm.PROPERTY_ID, out object objId);
            var hasOriginalId = objId != null;
            var originalId = !hasOriginalId ? string.Empty : objId.ToString();

            string domain = HttpContext.Request.Host.Host;
            CustomerUUidResponse customerUUidResponse = null;
            if (hasAccountNumber)
            {
                if (string.IsNullOrWhiteSpace(accountNumber))
                {
                    accountNumber = Customer.AccountNumberGenerator(domain, DateTime.Now);
                    fieldsJson[Fields.ID_ACCOUNT_NUMBER] = accountNumber;
                    body = JsonConvert.SerializeObject(fieldsJson);
                }
                    
                var resultCreateCustomer = await CreateCustomerIfNotExistsAsync(domain, accountNumber, identificationNumber);
                if (!(resultCreateCustomer is OkObjectResult))
                {
                    return BadRequest($@"Error:{((ObjectResult)resultCreateCustomer).Value.ToString()}");
                }
                var o = (OkObjectResult)resultCreateCustomer;
                var json = o.Value.ToString();
                customerUUidResponse = JsonConvert.DeserializeObject<CustomerUUidResponse>(json);
            }
            else if (!hasAccountNumber && !hasIdentificationNumber && hasEmail)
            {
                fieldsJson[Fields.ID_ACCOUNT_NUMBER] = email;
                body = JsonConvert.SerializeObject(fieldsJson);
            }
            string scriptForExistingCustomerForm = string.Empty;
            string scriptForCreatingOrUpdating;
            var bodyEscaped = Validator.StringEscape(body);

            var identifier = fieldsJson.ContainsKey(CustomerForm.PROPERTY_KEY) ? Consumers.SearchIdentifier(fieldsJson) : string.Empty;
            var customerChangedKey = hasOriginalId && originalId != identifier;
            if (customerChangedKey)
            {
                var response = DeleteCustomer(originalId);
                if (!response.Success) return BadRequest($"It cannot delete customer with id {originalId}. {response.OriginalException}");
            }
            var isNewCustomer = !hasOriginalId || customerChangedKey;
            string topicName;
            if (isNewCustomer)
            {
                
                scriptForCreatingOrUpdating = $"customerFormBuilder.AddFields('{bodyEscaped}').SetAsNew(Now, '{employeeName}', customerTypeStandard.Id)";
                topicName = $"{KafkaMessage.CUSTOMER_CREATION_CONSUMER_PREFIX}_{Integration.Kafka.TopicForCustomers}";
            }
            else
            {
                var resultInJson = hasOriginalId ? await GetCustomerDataOrNullAsync(originalId) : null;
                var currentCustomerData = resultInJson != null ? JsonConvert.DeserializeObject<IDictionary<string, object>>(resultInJson) : null;
                scriptForExistingCustomerForm = hasAccountNumber ? $"customerFormBuilder = CustomerFormBuilder(customer, template);" : $"customerFormBuilder = CustomerFormBuilder(template);";
                scriptForExistingCustomerForm += $"customerFormWithModifiedFields = customerFormBuilder.AddFields('{bodyEscaped}').CreateForm();";
                object documentsSubmitted;
                currentCustomerData.TryGetValue(Fields.ID_DOCUMENTS_SUBMITTED, out documentsSubmitted);
                if (documentsSubmitted != null)
                {
                    var strDocumentsSubmitted = documentsSubmitted.ToString().Replace("{", "").Replace("}", "").Replace("[", "").Replace("]", "").Replace("\"", "").Replace("\r\n", "").Replace(" ", "").Trim().Split(",");
                    currentCustomerData[Fields.ID_DOCUMENTS_SUBMITTED] = strDocumentsSubmitted;
                }

                var currentSavedBody = JsonConvert.SerializeObject(currentCustomerData);
                scriptForCreatingOrUpdating = $"customerFormBuilder.AddFields('{Validator.StringEscape(currentSavedBody)}').SetAsUpdate(Now, '{employeeName}', customerFormWithModifiedFields)";
                topicName = $"{KafkaMessage.CUSTOMER_UPDATE_CONSUMER_PREFIX}_{Integration.Kafka.TopicForCustomers}";
            }

            Agents agent = Security.Agent(User);
            SerializedFieldsBody serializedFieldsBody;
            string command;
            if (hasAccountNumber)
            {
                command = $@"
                    {{
                        customer = company.CustomerByAccountNumber('{customerUUidResponse.AccountNumber}');
                        print customer.IsApproved() isCustomerAlreadyApproved;
                        template = templates.GetTemplateById({templateId});
                        {scriptForExistingCustomerForm}
                        customerFormBuilder = CustomerFormBuilder(customer, template);
                        customerForm = {scriptForCreatingOrUpdating}.CreateForm();
                        print customerForm.Serialize() serializedFields;
                        print customerForm.Log log;
                        print customerForm.IsCustomerAutoApproved isCustomerAutoApproved;
                        print customerForm.GetUniqueValue() uniqueValue;
                        print Now now;
                        print {(int)agent} agent;
                        print '{customerUUidResponse.Uuid}' uuid;
					    print customer.Player.Id password;
                    }}
                ";
            }
            else
            {
                command = $@"
                    {{
                        print false isCustomerAlreadyApproved;
                        template = templates.GetTemplateById({templateId});
                        {scriptForExistingCustomerForm}
                        customerFormBuilder = CustomerFormBuilder(template);
                        customerForm = {scriptForCreatingOrUpdating}.CreateForm();
                        print customerForm.Serialize() serializedFields;
                        print customerForm.Log log;
                        print customerForm.IsCustomerAutoApproved isCustomerAutoApproved;
                        print customerForm.GetUniqueValue() uniqueValue;
                        print Now now;
                        print {(int)agent} agent;
                    }}
                ";
            }
            var result = await KnowYourCustomerAPI.KnowYourCustomer.PerformQryAsync(HttpContext, command);

            if (!(result is OkObjectResult))
            {
                return BadRequest($@"Error:{((ObjectResult)result).Value.ToString()}");
            }

            OkObjectResult obj = (OkObjectResult)result;
            var strJson = obj.Value.ToString();
            serializedFieldsBody = JsonConvert.DeserializeObject<SerializedFieldsBody>(strJson);

            if (Integration.UseKafka || Integration.UseKafkaForAuto)
            {
                Integration.Kafka.Send(
                    true,
                    topicName,
                    new DraftedCustomerMessage(serializedFieldsBody.Now, employeeName, serializedFieldsBody.SerializedFields, serializedFieldsBody.IsCustomerAlreadyApproved, serializedFieldsBody.IsCustomerAutoApproved, false)
                );
                Integration.Kafka.Send(
                    true,
                    $"{KafkaMessage.CUSTOMER_LOG_CONSUMER_PREFIX}_{Integration.Kafka.TopicForCustomers}",
                    new CustomerLogMessage(serializedFieldsBody.Now, employeeName, serializedFieldsBody.UniqueValue, serializedFieldsBody.Log)
                );
            }

            return result;
        }

        [HttpPost("api/KnowYourCustomer/customers/extraFields")]
        [Authorize(Roles = "e1,e23")]
        public async Task<IActionResult> AddExtraFieldsForCustomerAsync()
        {
            string body = null;
            using (StreamReader reader = new StreamReader(Request.Body, Encoding.UTF8))
            {
                body = await reader.ReadToEndAsync();
            }
            if (body == null) return BadRequest("Body is required");

            string employeeName = Security.UserName(HttpContext);

            var fieldsJson = JsonConvert.DeserializeObject<IDictionary<string, object>>(body);
            if (fieldsJson.Count == 0) return BadRequest("Body has not fields");
            if (!fieldsJson.ContainsKey(CustomerForm.PROPERTY_TEMPLATE_VERSION)) return BadRequest($"'{CustomerForm.PROPERTY_TEMPLATE_VERSION}' property is required.");

            object objTemplateId;
            fieldsJson.TryGetValue(CustomerForm.PROPERTY_TEMPLATE_ID, out objTemplateId);
            if (objTemplateId == null) return BadRequest($"'{CustomerForm.PROPERTY_TEMPLATE_ID}' property is required.");
            var templateId = objTemplateId.ToString();

            object objIdentificationNumber;
            fieldsJson.TryGetValue(Fields.ID_IDENTIFICATION_NUMBER, out objIdentificationNumber);
            var identificationNumber = objIdentificationNumber == null ? string.Empty : objIdentificationNumber.ToString();

            object objAccountNumber;
            fieldsJson.TryGetValue(Fields.ID_ACCOUNT_NUMBER, out objAccountNumber);
            var isCustomerWithAccountNumber = objAccountNumber != null;
            var accountNumber = objAccountNumber == null ? string.Empty : objAccountNumber.ToString();

            object objEmail;
            fieldsJson.TryGetValue(Fields.ID_EMAIL, out objEmail);
            var email = objEmail == null ? string.Empty : objEmail.ToString();

            string domain = HttpContext.Request.Host.Host;
            CustomerUUidResponse customerUUidResponse = null;
            if (isCustomerWithAccountNumber)
            {
                if (string.IsNullOrWhiteSpace(accountNumber))
                {
                    accountNumber = Customer.AccountNumberGenerator(domain, DateTime.Now);
                    fieldsJson[Fields.ID_ACCOUNT_NUMBER] = accountNumber;
                    body = JsonConvert.SerializeObject(fieldsJson);
                }

                var resultCreateCustomer = await CreateCustomerIfNotExistsAsync(domain, accountNumber, identificationNumber);
                if (!(resultCreateCustomer is OkObjectResult))
                {
                    return BadRequest($@"Error:{((ObjectResult)resultCreateCustomer).Value.ToString()}");
                }
                var o = (OkObjectResult)resultCreateCustomer;
                var json = o.Value.ToString();
                customerUUidResponse = JsonConvert.DeserializeObject<CustomerUUidResponse>(json);
            }

            string scriptForExistingCustomerForm = string.Empty;
            string scriptForCreatingOrUpdating;
            string topicName = string.Empty;
            var resultInJson = await GetCustomerDataOrNullAsync(identificationNumber, accountNumber, email);
            var currentCustomerData = resultInJson != null ? JsonConvert.DeserializeObject<IDictionary<string, object>>(resultInJson) : null;
            object objCustomerStatus = null;
            currentCustomerData?.TryGetValue(CustomerForm.PROPERTY_STATUS, out objCustomerStatus);
            var bodyEscaped = Validator.StringEscape(body);
            scriptForExistingCustomerForm = isCustomerWithAccountNumber ? $"customerFormBuilder = CustomerFormBuilder(customer, template);" : $"customerFormBuilder = CustomerFormBuilder(template);";
            scriptForExistingCustomerForm += $"customerFormWithModifiedFields = customerFormBuilder.AddExtraFields('{bodyEscaped}').CreateForm();";
            object documentsSubmitted;
            currentCustomerData.TryGetValue(Fields.ID_DOCUMENTS_SUBMITTED, out documentsSubmitted);
            if (documentsSubmitted != null)
            {
                var strDocumentsSubmitted = documentsSubmitted.ToString().Replace("{", "").Replace("}", "").Replace("[", "").Replace("]", "").Replace("\"", "").Replace("\r\n", "").Replace(" ", "").Trim().Split(",");
                currentCustomerData[Fields.ID_DOCUMENTS_SUBMITTED] = strDocumentsSubmitted;
            }

            var currentSavedBody = JsonConvert.SerializeObject(currentCustomerData);
            scriptForCreatingOrUpdating = $"customerFormBuilder.AddFields('{Validator.StringEscape(currentSavedBody)}').SetAsUpdate(Now, '{employeeName}', customerFormWithModifiedFields)";
            topicName = $"{KafkaMessage.CUSTOMER_UPDATE_CONSUMER_PREFIX}_{Integration.Kafka.TopicForCustomers}";

            SerializedFieldsBody serializedFieldsBody;
            string command;
            if (isCustomerWithAccountNumber)
            {
                command = $@"
                    {{
                        customer = company.CustomerByAccountNumber('{customerUUidResponse.AccountNumber}');
                        print customer.IsApproved() isCustomerAlreadyApproved;
                        template = templates.GetTemplateById({templateId});
                        {scriptForExistingCustomerForm}
                        customerFormBuilder = CustomerFormBuilder(customer, template);
                        customerForm = {scriptForCreatingOrUpdating}.CreateForm();
                        print customerForm.Serialize() serializedFields;
                        print customerForm.Log log;
                        print customerForm.IsCustomerAutoApproved isCustomerAutoApproved;
                        print customerForm.GetUniqueValue() uniqueValue;
                        print Now now;
                        print '{customerUUidResponse.Uuid}' uuid;
					    print customer.Player.Id password;
                    }}
                ";
            }
            else
            {
                command = $@"
                    {{
                        print false isCustomerAlreadyApproved;
                        template = templates.GetTemplateById({templateId});
                        {scriptForExistingCustomerForm}
                        customerFormBuilder = CustomerFormBuilder(template);
                        customerForm = {scriptForCreatingOrUpdating}.CreateForm();
                        print customerForm.Serialize() serializedFields;
                        print customerForm.Log log;
                        print customerForm.IsCustomerAutoApproved isCustomerAutoApproved;
                        print customerForm.GetUniqueValue() uniqueValue;
                        print Now now;
                    }}
                ";
            }
            var result = await KnowYourCustomerAPI.KnowYourCustomer.PerformQryAsync(HttpContext, command);

            if (!(result is OkObjectResult))
            {
                return BadRequest($@"Error:{((ObjectResult)result).Value.ToString()}");
            }

            OkObjectResult obj = (OkObjectResult)result;
            var strJson = obj.Value.ToString();
            serializedFieldsBody = JsonConvert.DeserializeObject<SerializedFieldsBody>(strJson);

            if (Integration.UseKafka || Integration.UseKafkaForAuto)
            {
                Integration.Kafka.Send(
                    true,
                    topicName,
                    new DraftedCustomerMessage(serializedFieldsBody.Now, employeeName, serializedFieldsBody.SerializedFields, serializedFieldsBody.IsCustomerAlreadyApproved, serializedFieldsBody.IsCustomerAutoApproved, false)
                );
                Integration.Kafka.Send(
                    true,
                    $"{KafkaMessage.CUSTOMER_LOG_CONSUMER_PREFIX}_{Integration.Kafka.TopicForCustomers}",
                    new CustomerLogMessage(serializedFieldsBody.Now, employeeName, serializedFieldsBody.UniqueValue, serializedFieldsBody.Log)
                );
            }

            return result;
        }

        private const int MAXIMUM_AMOUNT_OF_ROWS_ALLOWED = 100;

        [HttpGet("api/KnowYourCustomer/customers/approved")]
        [Authorize(Roles = "e9")]
        public async Task<IActionResult> ApprovedCustomersAsync(DateTime startDate, DateTime endDate, string identificationNumber, int initialIndex, int amountOfRows)
        {
            if (startDate == default(DateTime)) return BadRequest($"{nameof(startDate)} is required");
            if (endDate == default(DateTime)) return BadRequest($"{nameof(endDate)} is required");
            if (string.IsNullOrWhiteSpace(identificationNumber)) return BadRequest($"{nameof(identificationNumber)} is required");
            if (initialIndex < 0) return BadRequest($"{nameof(initialIndex)} must be greater than 0");
            if (amountOfRows <= 0) return BadRequest($"{nameof(amountOfRows)} must be greater than 0");

            if (amountOfRows > MAXIMUM_AMOUNT_OF_ROWS_ALLOWED)
            {
                amountOfRows = MAXIMUM_AMOUNT_OF_ROWS_ALLOWED;
            }
            var result = await ListApprovedCustomersAsync(startDate, endDate, identificationNumber, initialIndex, amountOfRows);
            string jsonString = result.Body;
            if (string.IsNullOrWhiteSpace(jsonString)) return BadRequest($"Response {nameof(jsonString)} is empty. {result.DebugInformation}");
            var reader = new JsonTextReader(new StringReader(jsonString));
            reader.FloatParseHandling = FloatParseHandling.Decimal;

            JObject obj = JObject.Load(reader);
            var objCustomers = obj["hits"]["hits"].Select(token => JObject.Parse(token["_source"].ToString()));
            return Ok(new JObject(new JProperty("customers", objCustomers)));
        }

        [HttpGet("api/KnowYourCustomer/customers/drafted")]
        [Authorize(Roles = "e13")]
        public async Task<IActionResult> DraftedCustomersAsync(DateTime startDate, DateTime endDate, string identificationNumber, int initialIndex, int amountOfRows)
        {
            if (startDate == default(DateTime)) return BadRequest($"{nameof(startDate)} is required");
            if (endDate == default(DateTime)) return BadRequest($"{nameof(endDate)} is required");
            if (string.IsNullOrWhiteSpace(identificationNumber)) return BadRequest($"{nameof(identificationNumber)} is required");
            if (initialIndex < 0) return BadRequest($"{nameof(initialIndex)} must be greater than 0");
            if (amountOfRows < 0) return BadRequest($"{nameof(amountOfRows)} must be greater than 0");

            if (amountOfRows > MAXIMUM_AMOUNT_OF_ROWS_ALLOWED)
            {
                amountOfRows = MAXIMUM_AMOUNT_OF_ROWS_ALLOWED;
            }
            var result = await ListDraftedCustomersAsync(startDate, endDate, identificationNumber, initialIndex, amountOfRows);
            string jsonString = result.Body;
            if (string.IsNullOrWhiteSpace(jsonString)) return BadRequest($"Response {nameof(jsonString)} is empty. {result.DebugInformation}");
            var reader = new JsonTextReader(new StringReader(jsonString));
            reader.FloatParseHandling = FloatParseHandling.Decimal;

            JObject obj = JObject.Load(reader);
            var objCustomers = obj["hits"]["hits"].Select(token => JObject.Parse(token["_source"].ToString()));
            return Ok(new JObject(new JProperty("customers", objCustomers)));
        }

        [HttpGet("api/KnowYourCustomer/customers/rejected")]
        [Authorize(Roles = "e18")]
        public async Task<IActionResult> RejectedCustomersAsync(DateTime startDate, DateTime endDate, string identificationNumber, int initialIndex, int amountOfRows)
        {
            if (startDate == default(DateTime)) return BadRequest($"{nameof(startDate)} is required");
            if (endDate == default(DateTime)) return BadRequest($"{nameof(endDate)} is required");
            if (string.IsNullOrWhiteSpace(identificationNumber)) return BadRequest($"{nameof(identificationNumber)} is required");
            if (initialIndex < 0) return BadRequest($"{nameof(initialIndex)} must be greater than 0");
            if (amountOfRows < 0) return BadRequest($"{nameof(amountOfRows)} must be greater than 0");

            if (amountOfRows > MAXIMUM_AMOUNT_OF_ROWS_ALLOWED)
            {
                amountOfRows = MAXIMUM_AMOUNT_OF_ROWS_ALLOWED;
            }
            var result = await ListRejectedCustomersAsync(startDate, endDate, identificationNumber, initialIndex, amountOfRows);
            string jsonString = result.Body;
            if (string.IsNullOrWhiteSpace(jsonString)) return BadRequest($"Response {nameof(jsonString)} is empty. {result.DebugInformation}");
            var reader = new JsonTextReader(new StringReader(jsonString));
            reader.FloatParseHandling = FloatParseHandling.Decimal;

            JObject obj = JObject.Load(reader);
            var objCustomers = obj["hits"]["hits"].Select(token => JObject.Parse(token["_source"].ToString()));
            return Ok(new JObject(new JProperty("customers", objCustomers)));
        }

        [HttpGet("api/KnowYourCustomer/customer")]
        [Authorize(Roles = "e2")]
        public async Task<IActionResult> BasicCustomerInfoByIdAsync(string identificator)
        {
            if (string.IsNullOrWhiteSpace(identificator)) return BadRequest($"{nameof(identificator)} is required");

            var result = await SearchBasicCustomerInfoAsync(identificator);

            string jsonString = result.Body;
            if (string.IsNullOrWhiteSpace(jsonString)) return BadRequest($"Response {nameof(jsonString)} is empty. {result.DebugInformation}");
            var reader = new JsonTextReader(new StringReader(jsonString));
            reader.FloatParseHandling = FloatParseHandling.Decimal;

            JObject resultInJson = new JObject();
            JObject obj = JObject.Load(reader);
            decimal amountOfHits = decimal.Parse(obj["hits"]["total"]["value"].ToString());
            if (amountOfHits == 0)
            {
                resultInJson["existsCustomer"] = false;
            }
            else if (amountOfHits == 1)
            {
                resultInJson = JObject.Parse(obj["hits"]["hits"][0]["_source"].ToString());
                resultInJson["existsCustomer"] = true;
            }
            else
            {
                throw new GameEngineException($"There is {amountOfHits} hits for identification number {identificator}.");
            }
            return Ok(resultInJson);
        }

        [HttpGet("api/KnowYourCustomer/customer/essentialData")]
        [Authorize(Roles = "player")]
        public async Task<IActionResult> EssentialCustomerInfoAsync()
        {
            string playerId = Validator.StringEscape(Security.PlayerId(User));

            var result = await KnowYourCustomerAPI.KnowYourCustomer.PerformQryAsync(HttpContext, $@"
                {{
                    customer = company.CustomerByPlayerId('{playerId}');
                    player = customer.Player;
                    print player.AccountNumber accountNumber;
                    print customer.Identifier identificationNumber;
                }}
            ");
            return result;
        }

        [HttpGet("api/KnowYourCustomer/customers/{identificationNumber}/type")]
        public async Task<IActionResult> GetCustomerTypeAsync(string identificationNumber)
        {
            if (string.IsNullOrWhiteSpace(identificationNumber)) return BadRequest($"{nameof(identificationNumber)} is required");

            var result = await SearchCustomerTypeAsync(identificationNumber);

            string jsonString = result.Body;
            if (string.IsNullOrWhiteSpace(jsonString)) return BadRequest($"Response {nameof(jsonString)} is empty. {result.DebugInformation}");
            var reader = new JsonTextReader(new StringReader(jsonString));
            reader.FloatParseHandling = FloatParseHandling.Decimal;

            JObject resultInJson = new JObject();
            JObject obj = JObject.Load(reader);
            decimal amountOfHits = decimal.Parse(obj["hits"]["total"]["value"].ToString());
            if (amountOfHits == 0)
            {
                resultInJson["existsCustomer"] = false;
            }
            else if (amountOfHits == 1)
            {
                resultInJson = JObject.Parse(obj["hits"]["hits"][0]["_source"].ToString());
                resultInJson["existsCustomer"] = true;
            }
            else
            {
                throw new GameEngineException($"There is {amountOfHits} hits for identification number {identificationNumber}.");
            }
            var customerTypeId = Convert.ToInt32(resultInJson[CustomerForm.PROPERTY_CUSTOMER_TYPE_ID]);
            var resultQry = await KnowYourCustomerAPI.KnowYourCustomer.PerformQryAsync(HttpContext, $@"
                {{
                    specificCustomerType = customerTypes.FindCustomerType({customerTypeId});
                    print specificCustomerType.Id id;
					print specificCustomerType.Name name;
					print specificCustomerType.Description description;
                    print specificCustomerType.BadgePath badgePath;
                }}
            ");
            return resultQry;
        }

        [HttpGet("api/KnowYourCustomer/customers/customerTypes")]
        [Authorize(Roles = "e19,f2,b1,b2")]
        public async Task<IActionResult> GetCustomerTypesAsync(string identificators)
        {
            if (string.IsNullOrWhiteSpace(identificators)) return BadRequest($"{nameof(identificators)} is required");

            var normalizedIdentificators = identificators.ToLower().Trim();
            var identificatorsAsArray = normalizedIdentificators.Split(',');
            var result = await SearchCustomerTypesAsync(identificatorsAsArray);

            string jsonString = result.Body;
            if (string.IsNullOrWhiteSpace(jsonString)) return BadRequest($"Response {nameof(jsonString)} is empty. {result.DebugInformation}");
            var reader = new JsonTextReader(new StringReader(jsonString));
            reader.FloatParseHandling = FloatParseHandling.Decimal;

            JObject resultInJson = new JObject();
            JObject obj = JObject.Load(reader);
            var objCustomerTypes = obj["hits"]["hits"].Select(token => JObject.Parse(token["_source"].ToString()));
            var customerTypeIds = new StringBuilder();
            var avatarPaths = new StringBuilder();
            var identificatorsAsText = new StringBuilder();
            bool isFirst = true;
            foreach (var identificatorByParam in identificatorsAsArray)
            {
                foreach (var objCustomerType in objCustomerTypes)
                {
                    JToken token;
                    foreach (var identificator in uniqueIdentificators)
                    {
                        objCustomerType.TryGetValue(identificator, out token);
                        var identificatorFound = token == null ? string.Empty : token.ToString();

                        if (identificatorByParam == identificatorFound.ToLower().Trim())
                        {
                            if (isFirst) isFirst = false;
                            else
                            {
                                customerTypeIds.Append(',');
                                avatarPaths.Append(',');
                                identificatorsAsText.Append(',');
                            }

                            var customerTypeId = Convert.ToInt32(objCustomerType[CustomerForm.PROPERTY_CUSTOMER_TYPE_ID]);
                            customerTypeIds.Append(customerTypeId);
                            var avatarPath = objCustomerType.ContainsKey(Fields.ID_AVATAR) ? objCustomerType[Fields.ID_AVATAR].ToString() : string.Empty;
                            avatarPaths.Append('\'').Append(avatarPath).Append('\'');
                            identificatorsAsText.Append('\'').Append(identificatorByParam).Append('\'');
                            break;
                        }
                    }
                }
            }
            if (customerTypeIds.Length == 0) return BadRequest($"{nameof(customerTypeIds)} is empty");
            if (avatarPaths.Length == 0) return BadRequest($"{nameof(avatarPaths)} is empty");
            if (identificatorsAsText.Length == 0) return BadRequest($"{nameof(identificatorsAsText)} is empty");

            var resultQry = await KnowYourCustomerAPI.KnowYourCustomer.PerformQryAsync(HttpContext, $@"
                {{
                    for(types : customerTypes.FindCustomerTypes({{{customerTypeIds}}},{{{avatarPaths}}},{{{identificatorsAsText}}}))
					{{
                        print types.AvatarPath avatarPath;
                        print types.IdentificationNumber identificator;
						specificCustomerType = types.Type;
                        print specificCustomerType.Id customerTypeId;
						print specificCustomerType.Name name;
						print specificCustomerType.Description description;
                        print specificCustomerType.BadgePath badgePath;
                    }}
                }}
            ");
            return resultQry;
        }

        [HttpGet("api/KnowYourCustomer/customers/{identificationNumber}/accountnumber")]
        [Authorize(Roles = "e11,devops")]
        public async Task<IActionResult> GetAccountNumberByAsync(string identificationNumber)
        {
            if (string.IsNullOrWhiteSpace(identificationNumber)) return BadRequest($"{nameof(identificationNumber)} is required");

            IActionResult result = await KnowYourCustomerAPI.KnowYourCustomer.PerformQryAsync(HttpContext, $@"
				{{
					existsCustomer = company.ExistsCustomerByIdentifier('{identificationNumber}');
					print existsCustomer existsCustomer;
					if(existsCustomer)
					{{
						customer = company.CustomerByIdentifier('{identificationNumber}');
						print customer.AccountNumber account_number;
					}}
				}}
			");

            if (!(result is OkObjectResult))
            {
                throw new GameEngineException($@"Error:{((ObjectResult)result).Value.ToString()}");
            }

            return result;
        }

        public async Task<IActionResult> GetTemplateByIdAsync(int templateId)
        {
            if (templateId <= 0) return BadRequest($"{nameof(templateId)} is required");

            var result = await KnowYourCustomerAPI.KnowYourCustomer.PerformQryAsync(HttpContext, $@"
                {{
                    template = templates.GetTemplateByIdOrNull({templateId});
                    existsTemplate = template != null;
                    print existsTemplate existsTemplate;
                    if (existsTemplate)
                    {{
                        {scriptTemplateData}
                    }}
                }}
            ");
            return result;
        }

        [HttpGet("api/KnowYourCustomer/customers/template")]
        [Authorize(Roles = "e12")]
        public async Task<IActionResult> FullCustomerInfoByIdAsync(string identificationNumber, string accountNumber, string email)
        {
            if (string.IsNullOrWhiteSpace(identificationNumber) && string.IsNullOrWhiteSpace(accountNumber) && string.IsNullOrWhiteSpace(email)) return BadRequest($"All parameters are empty. It must send at least one");
            var result = await SearchFullCustomerInfoAsync(identificationNumber, accountNumber, email);

            string jsonString = result.Body;
            if (string.IsNullOrWhiteSpace(jsonString)) return BadRequest($"Response {nameof(jsonString)} is empty. {result.DebugInformation}");
            var reader = new JsonTextReader(new StringReader(jsonString));
            reader.FloatParseHandling = FloatParseHandling.Decimal;

            JObject resultInJson = new JObject();
            JObject obj = JObject.Load(reader);
            decimal amountOfHits = decimal.Parse(obj["hits"]["total"]["value"].ToString());
            if (amountOfHits == 0)
            {
                resultInJson["existsCustomer"] = false;
            }
            else if (amountOfHits == 1)
            {
                resultInJson = JObject.Parse(obj["hits"]["hits"][0]["_source"].ToString());
                resultInJson["existsCustomer"] = true;
                resultInJson[CustomerForm.PROPERTY_ID] = obj["hits"]["hits"][0]["_id"].ToString();
            }
            else
            {
                throw new GameEngineException($"There is {amountOfHits} hits for {nameof(identificationNumber)}: {identificationNumber}, {nameof(accountNumber)}: {accountNumber} {nameof(email)}: {email}.");
            }

            if (!resultInJson.ContainsKey(CustomerForm.PROPERTY_TEMPLATE_ID)) return BadRequest($"'{CustomerForm.PROPERTY_TEMPLATE_ID}' property it is required to get template.");
            var templateId = Convert.ToInt32(resultInJson[CustomerForm.PROPERTY_TEMPLATE_ID]);
            var templateResponse = await GetTemplateByIdAsync(templateId);

            JToken documentsSubmitted;
            resultInJson.TryGetValue(Fields.ID_DOCUMENTS_SUBMITTED, out documentsSubmitted);
            if (documentsSubmitted != null)
            {
                var documentsArray = JsonConvert.DeserializeObject<IList<string>>(documentsSubmitted.ToString());
                resultInJson[Fields.ID_DOCUMENTS_SUBMITTED] = JArray.FromObject(documentsArray);
            }

            jsonString = ((OkObjectResult)templateResponse).Value.ToString();
            reader = new JsonTextReader(new StringReader(jsonString));
            obj = JObject.Load(reader);
            return Ok(new CustomerWithTemplateBody()
            {
                Customer = resultInJson,
                Template = obj
            }
            );
        }

        const string KYCUsername = "testUser1";
        const string KYCPassword = "12345";
        [HttpGet("api/KnowYourCustomer/customers/fields")]
        public async Task<IActionResult> SpecificCustomerFieldsInfoByIdAsync(string identificationNumber, string accountNumber, string email, string username, string password, string fields)
        {
            if (string.IsNullOrWhiteSpace(identificationNumber) && string.IsNullOrWhiteSpace(accountNumber) && string.IsNullOrWhiteSpace(email)) return BadRequest($"All parameters are empty. It must send at least one");
            if (string.IsNullOrWhiteSpace(username)) return BadRequest(nameof(username));
            if (string.IsNullOrWhiteSpace(password)) return BadRequest(nameof(password));
            if (string.IsNullOrWhiteSpace(fields)) return BadRequest(nameof(fields));

            var result = await SearchFullCustomerInfoAsync(identificationNumber, accountNumber, email);

            string jsonString = result.Body;
            if (string.IsNullOrWhiteSpace(jsonString)) return BadRequest($"Response {nameof(jsonString)} is empty. {result.DebugInformation}");
            var reader = new JsonTextReader(new StringReader(jsonString));
            reader.FloatParseHandling = FloatParseHandling.Decimal;

            JObject resultInJson = new JObject();
            JObject obj = JObject.Load(reader);
            decimal amountOfHits = decimal.Parse(obj["hits"]["total"]["value"].ToString());
            if (amountOfHits == 0)
            {
                resultInJson["existsCustomer"] = false;
            }
            else if (amountOfHits == 1)
            {
                resultInJson = JObject.Parse(obj["hits"]["hits"][0]["_source"].ToString());
                resultInJson["existsCustomer"] = true;
            }
            else
            {
                return BadRequest($"There is {amountOfHits} hits for {nameof(identificationNumber)}: {identificationNumber}, {nameof(accountNumber)}: {accountNumber} {nameof(email)}: {email}.");
            }

            var fieldsArray = fields.Split(',');
            var fieldsReponse = new Dictionary<string, string>();
            var matchesCredentials = username == KYCUsername && password == KYCPassword;
            if (matchesCredentials)
            {
                foreach (var fieldKey in fieldsArray)
                {
                    if (!resultInJson.ContainsKey(fieldKey)) return BadRequest($"Field {fieldKey} does not exist");
                    var fieldValue = resultInJson[fieldKey].ToString();
                    fieldsReponse.Add(fieldKey, fieldValue);
                }
            }
            
            return Ok(fieldsReponse);
        }

        [HttpPost("api/KnowYourCustomer/customers/approval")]
        [Authorize(Roles = "e14")]
        public async Task<IActionResult> ApproveCustomerAsync(string identificationNumber, string accountNumber, string email)
        {
            if (string.IsNullOrWhiteSpace(identificationNumber) && string.IsNullOrWhiteSpace(accountNumber) && string.IsNullOrWhiteSpace(email)) return BadRequest($"All parameters are empty. It must send at least one");
            var result = await SearchFullCustomerInfoAsync(identificationNumber, accountNumber, email);

            string jsonString = result.Body;
            if (string.IsNullOrWhiteSpace(jsonString)) return BadRequest($"Response {nameof(jsonString)} is empty. {result.DebugInformation}");
            var reader = new JsonTextReader(new StringReader(jsonString));
            reader.FloatParseHandling = FloatParseHandling.Decimal;

            JObject resultInJson = new JObject();
            JObject obj = JObject.Load(reader);
            decimal amountOfHits = decimal.Parse(obj["hits"]["total"]["value"].ToString());
            if (amountOfHits == 0)
            {
                return BadRequest($"Customer with {nameof(identificationNumber)}: {identificationNumber}, {nameof(accountNumber)}: {accountNumber} {nameof(email)}: {email} does not exist");
            }
            else if (amountOfHits == 1)
            {
                resultInJson = JObject.Parse(obj["hits"]["hits"][0]["_source"].ToString());
            }
            else
            {
                return BadRequest($"There are several customers with {nameof(identificationNumber)}: {identificationNumber}, {nameof(accountNumber)}: {accountNumber} {nameof(email)}: {email}");
            }

            JToken status;
            resultInJson.TryGetValue(CustomerForm.PROPERTY_STATUS, out status);
            if (status == null) return BadRequest($"'{CustomerForm.PROPERTY_STATUS}' property is required.");
            if (status.ToString() != Customer.CustomerStatus.Drafted.ToString()) return BadRequest($"Customer {CustomerForm.PROPERTY_STATUS} is {status.ToString()} and it should be {Customer.CustomerStatus.Drafted.ToString()}.");

            resultInJson.TryGetValue(Fields.ID_ACCOUNT_NUMBER, out JToken accountNumberToken);
            var hasAccountNumber = accountNumberToken != null;
            
            resultInJson.TryGetValue(Fields.ID_IDENTIFICATION_NUMBER, out JToken identificationNumberToken);
            var hasIdentificationNumber = identificationNumberToken != null;

            if (!hasAccountNumber && ! hasIdentificationNumber) return BadRequest($"'{Fields.ID_ACCOUNT_NUMBER}' and '{Fields.ID_IDENTIFICATION_NUMBER}' do not exist.");
            accountNumber = hasAccountNumber ? accountNumberToken.ToString() : identificationNumberToken.ToString();

            if (Integration.UseKafka || Integration.UseKafkaForAuto)
            {
                resultInJson.TryGetValue(Fields.ID_NICKNAME, out JToken objNickname);
                var nickName = objNickname == null ? string.Empty : objNickname.ToString();
                resultInJson.TryGetValue(Fields.ID_AVATAR, out JToken objAvatar);
                var avatar = objAvatar == null ? string.Empty : objAvatar.ToString();
                string employeeName = Security.UserName(HttpContext);
                Integration.Kafka.Send(
                    true,
                    $"{KafkaMessage.KYC_CUSTOMER_STATUS_CHANGE_CONSUMER_PREFIX}_{Integration.Kafka.TopicForCustomers}",
                    new CustomerStatusChangeMessage(DateTime.Now, employeeName, Customer.CustomerStatus.Approved, accountNumber, nickName, avatar, false)
                );
            }

            return Ok();
        }

        [HttpPost("api/KnowYourCustomer/customers/rejection")]
        [Authorize(Roles = "e15")]
        public async Task<IActionResult> RejectCustomerAsync(string identificationNumber, string accountNumber, string email, [FromBody] CustomerRejectionBody body)
        {
            if (string.IsNullOrWhiteSpace(identificationNumber) && string.IsNullOrWhiteSpace(accountNumber) && string.IsNullOrWhiteSpace(email)) return BadRequest($"All parameters are empty. It must send at least one");
            if (body == null) return BadRequest("Body is required");
            if (string.IsNullOrWhiteSpace(body.Reason)) return BadRequest($"{nameof(body.Reason)} is required");

            var result = await SearchFullCustomerInfoAsync(identificationNumber, accountNumber, email);

            string jsonString = result.Body;
            if (string.IsNullOrWhiteSpace(jsonString)) return BadRequest($"Response {nameof(jsonString)} is empty. {result.DebugInformation}");
            var reader = new JsonTextReader(new StringReader(jsonString));
            reader.FloatParseHandling = FloatParseHandling.Decimal;

            JObject resultInJson = new JObject();
            JObject obj = JObject.Load(reader);
            decimal amountOfHits = decimal.Parse(obj["hits"]["total"]["value"].ToString());
            if (amountOfHits == 0)
            {
                return BadRequest($"Customer with {nameof(identificationNumber)}: {identificationNumber}, {nameof(accountNumber)}: {accountNumber} {nameof(email)}: {email} does not exist");
            }
            else if (amountOfHits == 1)
            {
                resultInJson = JObject.Parse(obj["hits"]["hits"][0]["_source"].ToString());
            }
            else
            {
                return BadRequest($"There are several customers with {nameof(identificationNumber)}: {identificationNumber}, {nameof(accountNumber)}: {accountNumber} {nameof(email)}: {email}");
            }

            JToken status;
            resultInJson.TryGetValue(CustomerForm.PROPERTY_STATUS, out status);
            if (status == null) return BadRequest($"'{CustomerForm.PROPERTY_STATUS}' property is required.");
            if (status.ToString() != Customer.CustomerStatus.Drafted.ToString()) return BadRequest($"Customer {CustomerForm.PROPERTY_STATUS} is {status.ToString()} and it should be {Customer.CustomerStatus.Drafted.ToString()}.");

            resultInJson.TryGetValue(Fields.ID_ACCOUNT_NUMBER, out JToken accountNumberToken);
            var hasAccountNumber = accountNumberToken != null;

            resultInJson.TryGetValue(Fields.ID_IDENTIFICATION_NUMBER, out JToken identificationNumberToken);
            var hasIdentificationNumber = identificationNumberToken != null;

            if (!hasAccountNumber && !hasIdentificationNumber) return BadRequest($"'{Fields.ID_ACCOUNT_NUMBER}' and '{Fields.ID_IDENTIFICATION_NUMBER}' do not exist.");
            accountNumber = hasAccountNumber ? accountNumberToken.ToString() : identificationNumberToken.ToString();

            if (Integration.UseKafka || Integration.UseKafkaForAuto)
            {
                string employeeName = Security.UserName(HttpContext);
                Integration.Kafka.Send(
                    true,
                    $"{KafkaMessage.REJECTED_CUSTOMER_CONSUMER_PREFIX}_{Integration.Kafka.TopicForCustomers}",
                    new RejectedCustomerMessage(DateTime.Now, employeeName, accountNumber, body.Reason)
                );
            }

            return Ok();
        }

        [HttpPost("api/KnowYourCustomer/customers/log")]
        [Authorize(Roles = "e16")]
        public async Task<IActionResult> AddCustomerLogAsync(string identificationNumber, string accountNumber, string email, [FromBody] CustomerLogBody body)
        {
            if (string.IsNullOrWhiteSpace(identificationNumber) && string.IsNullOrWhiteSpace(accountNumber) && string.IsNullOrWhiteSpace(email)) return BadRequest($"All parameters are empty. It must send at least one");
            if (body == null) return BadRequest("Body is required");
            if (string.IsNullOrWhiteSpace(body.Log)) return BadRequest($"{nameof(body.Log)} is required");

            var result = await SearchFullCustomerInfoAsync(identificationNumber, accountNumber, email);

            string jsonString = result.Body;
            if (string.IsNullOrWhiteSpace(jsonString)) return BadRequest($"Response {nameof(jsonString)} is empty. {result.DebugInformation}");
            var reader = new JsonTextReader(new StringReader(jsonString));
            reader.FloatParseHandling = FloatParseHandling.Decimal;

            JObject resultInJson = new JObject();
            JObject obj = JObject.Load(reader);
            decimal amountOfHits = decimal.Parse(obj["hits"]["total"]["value"].ToString());
            if (amountOfHits == 0)
            {
                return BadRequest($"Customer with {nameof(identificationNumber)}: {identificationNumber}, {nameof(accountNumber)}: {accountNumber} {nameof(email)}: {email} does not exist");
            }
            else if (amountOfHits == 1)
            {
                resultInJson = JObject.Parse(obj["hits"]["hits"][0]["_source"].ToString());
            }
            else
            {
                return BadRequest($"There are several customers with {nameof(identificationNumber)}: {identificationNumber}, {nameof(accountNumber)}: {accountNumber} {nameof(email)}: {email}");
            }

            var resultLogIdentifier = SearchLogIdentifier(resultInJson);
            string identifier = ((OkObjectResult)resultLogIdentifier).Value.ToString();

            if (Integration.UseKafka || Integration.UseKafkaForAuto)
            {
                string employeeName = Security.UserName(HttpContext);
                Integration.Kafka.Send(
                    true,
                    $"{KafkaMessage.CUSTOMER_LOG_CONSUMER_PREFIX}_{Integration.Kafka.TopicForCustomers}",
                    new CustomerLogMessage(DateTime.Now, employeeName, identifier, body.Log)
                );
            }

            return Ok();
        }

        IActionResult SearchLogIdentifier(JObject resultInJson)
        {
            resultInJson.TryGetValue(CustomerForm.PROPERTY_KEY, out JToken keyToken);
            if (keyToken == null) return BadRequest($"'{CustomerForm.PROPERTY_KEY}' does not exist.");
            var keyValue = keyToken.ToString();
            var uniqueKeys = keyValue.Split(',');
            string identifier = null;
            foreach (var key in uniqueKeys)
            {
                resultInJson.TryGetValue(key, out JToken tempToken);
                if (tempToken == null) return BadRequest($"Key '{key}' does not exist.");
                if (identifier == null)
                    identifier = tempToken.ToString();
                else
                    identifier += $",{tempToken.ToString()}";
            }
            return Ok(identifier);
        }

        [HttpGet("api/KnowYourCustomer/customers/log")]
        [Authorize(Roles = "e17")]
        public async Task<IActionResult> FullCustomerLogAsync(string identificationNumber, string accountNumber, string email)
        {
            if (string.IsNullOrWhiteSpace(identificationNumber) && string.IsNullOrWhiteSpace(accountNumber) && string.IsNullOrWhiteSpace(email)) return BadRequest($"All parameters are empty. It must send at least one");

            var result = await SearchFullCustomerInfoAsync(identificationNumber, accountNumber, email);

            string jsonString = result.Body;
            if (string.IsNullOrWhiteSpace(jsonString)) return BadRequest($"Response {nameof(jsonString)} is empty. {result.DebugInformation}");
            var reader = new JsonTextReader(new StringReader(jsonString));
            reader.FloatParseHandling = FloatParseHandling.Decimal;

            JObject resultInJson = new JObject();
            JObject obj = JObject.Load(reader);
            decimal amountOfHits = decimal.Parse(obj["hits"]["total"]["value"].ToString());
            if (amountOfHits == 0)
            {
                return BadRequest($"Customer with {nameof(identificationNumber)}: {identificationNumber}, {nameof(accountNumber)}: {accountNumber} {nameof(email)}: {email} does not exist");
            }
            else if (amountOfHits == 1)
            {
                resultInJson = JObject.Parse(obj["hits"]["hits"][0]["_source"].ToString());
            }
            else
            {
                return BadRequest($"There are several customers with {nameof(identificationNumber)}: {identificationNumber}, {nameof(accountNumber)}: {accountNumber} {nameof(email)}: {email}");
            }

            var resultLogIdentifier = SearchLogIdentifier(resultInJson);
            string identifier = ((OkObjectResult)resultLogIdentifier).Value.ToString();

            var resultLog = await SearchFullCustomerLogAsync(identifier);

            jsonString = resultLog.Body;
            if (string.IsNullOrWhiteSpace(jsonString)) return BadRequest($"Response {nameof(jsonString)} is empty. {resultLog.DebugInformation}");
            reader = new JsonTextReader(new StringReader(jsonString));
            reader.FloatParseHandling = FloatParseHandling.Decimal;

            obj = JObject.Load(reader);
            var objCustomers = obj["hits"]["hits"].Select(token => JObject.Parse(token["_source"].ToString()));
            var jsonCustomers = string.Join(",", objCustomers.Select(o => o.ToString()));
            var logs = new Logs();
            var log = logs.CreateLog(identifier);
            log.AddEntries($"[{jsonCustomers}]");

            return Ok(log.Serialize());
        }

        [HttpGet("api/KnowYourCustomer/availableFields")]
        [Authorize(Roles = "e3")]
        public async Task<IActionResult> ListAvailableFieldsAsync()
        {
            var result = await KnowYourCustomerAPI.KnowYourCustomer.PerformQryAsync(HttpContext, $@"
                {{
                    fieldsList = fields.List();
                    print fields.Count() count;
                    for (availableFields : fieldsList)
                    {{
                        print availableFields.Id fieldId;
                        print availableFields.Label label;
                        print availableFields.Notes notes;
                        print availableFields.Verifiable verifiable;
                        print availableFields.Unique unique;
                        if (availableFields.HasMultipleInputs())
                        {{
                            fieldNSF = availableFields;
                            print 'multioptions' inputType;
                            print false mandatory;
                            print false attachment;
                            print true multioptions;
                            print true editable;
                            allSubFileds = fieldNSF.SubFields();
                            for (subFields : allSubFileds)
                            {{
                                subField = subFields;
                                print subField.Id subFieldId;
                                print subField.Label label;
                                inputNSF = subField.Input;
                                print inputNSF.Id inputId;
                                print inputNSF.Name inputName;
                                print inputNSF.TypeAsString() inputType;
                                for (attributes : inputNSF.ListAttributes())
                                {{
                                    attributeSF = attributes;
                                    print attributeSF.TypeAsString() type;
                                    print attributeSF.GetValue() value;
                                }}
                            }}
                        }}
                        else 
                        {{
                            fieldSF = availableFields;
                            inputSF = fieldSF.Input;
                            print inputSF.Id inputId;
                            print inputSF.Name inputName;
                            print inputSF.TypeAsString() inputType;
                            print inputSF.HasAttribute({AttributeType.Required}) mandatory;
                            print inputSF.IsType({InputType.File}) attachment;
                            print false multioptions;
                            print ! inputSF.HasAttribute({AttributeType.Readonly}) editable;
                            for (attributes : inputSF.ListAttributes())
                            {{
                                attributeNSF = attributes;
                                print attributeNSF.TypeAsString() type;
                                print attributeNSF.GetValue() value;
                            }}
                        }}
                    }}
                }}
            ");
            return result;
        }

        private string scriptTemplateData = $@"
            print template.Id templateId;
            print template.Name name;
            print template.Version version;
            for (sortedFields : template.Fields)
            {{
                sortedField = sortedFields;
                print sortedField.Position position;
                field = sortedField.Field;
                print field.Id fieldId;
                print field.Label label;
                print field.Notes notes;
                print field.Verifiable verifiable;
                print field.Unique unique;
                if (field.HasMultipleInputs())
                {{
                    print 'multioptions' inputType;
                    print false mandatory;
                    print false attachment;
                    print true multioptions;
                    print true editable;
                    for (subFields : field.SubFields())
                    {{
                        subField = subFields;
                        print subField.Id subFieldId;
                        print subField.Label label;
                        input = subField.Input;
                        print input.Id inputId;
                        print input.Name inputName;
                        print input.TypeAsString() inputType;
                        for (attributes : input.ListAttributes())
                        {{
                            attribute = attributes;
                            print attribute.TypeAsString() type;
                            print attribute.GetValue() value;
                        }}
                    }}
                }}
                else 
                {{
                    input = field.Input;
                    print input.Id inputId;
                    print input.Name inputName;
                    print input.TypeAsString() inputType;
                    print input.HasAttribute({AttributeType.Required}) mandatory;
                    print input.IsType({InputType.File}) attachment;
                    print false multioptions;
                    print ! input.HasAttribute({AttributeType.Readonly}) editable;
                    for (attributes : input.ListAttributes())
                    {{
                        attribute = attributes;
                        print attribute.TypeAsString() type;
                        print attribute.GetValue() value;
                    }}
                }}
            }}";

        [HttpGet("api/KnowYourCustomer/templates")]
        [Authorize(Roles = "e4")]
        public async Task<IActionResult> ListTemplatesAsync()
        {
            var result = await KnowYourCustomerAPI.KnowYourCustomer.PerformQryAsync(HttpContext, $@"
                {{
                    templatesKYC = templates.GetAll;
                    print templatesKYC.Count() count;
                    for (templatesList : templatesKYC)
                    {{
                        template = templatesList;
                        {scriptTemplateData}
                    }}
                }}
            ");
            return result;
        }

        [HttpGet("api/KnowYourCustomer/template")]
        [Authorize(Roles = "e10")]
        public async Task<IActionResult> GetTemplateAsync(string name)
        {
            if (string.IsNullOrWhiteSpace(name)) return BadRequest($"{nameof(name)} is required");

            var nameEscaped = Validator.StringEscape(name.Trim());
            var result = await KnowYourCustomerAPI.KnowYourCustomer.PerformQryAsync(HttpContext, $@"
                {{
                    template = templates.GetTemplateOrNull('{nameEscaped}');
                    existsTemplate = template != null;
                    print existsTemplate existsTemplate;
                    if (existsTemplate)
                    {{
                        {scriptTemplateData}
                    }}
                }}
            ");
            return result;
        }

        [HttpDelete("api/KnowYourCustomer/template")]
        [Authorize(Roles = "e5")]
        public async Task<IActionResult> RemoveTemplateAsync(string name)
        {
            if (string.IsNullOrWhiteSpace(name)) return BadRequest($"{nameof(name)} is required");

            var nameEscaped = Validator.StringEscape(name.Trim());
            var result = await KnowYourCustomerAPI.KnowYourCustomer.PerformQryAsync(HttpContext, $@"
                {{
                    existsTemplate = templates.Exists('{nameEscaped}');
                    print existsTemplate existsTemplate;
                }}
            ");
            string value = ((OkObjectResult)result).Value.ToString();
            var templateExistance = JsonConvert.DeserializeObject<TemplateExistence>(value);

            if (templateExistance.existsTemplate)
            {
                await KnowYourCustomerAPI.KnowYourCustomer.PerformCmdAsync(HttpContext, $@"
                    {{
					    templates.Remove('{nameEscaped}');
                    }}
                ");
            }
            return result;
        }

        [HttpPost("api/KnowYourCustomer/template")]
        [Authorize(Roles = "e6")]
        public async Task<IActionResult> CreateTemplateAsync([FromBody] TemplateCreationBody body)
        {
            if (body == null) return BadRequest("Body is required");
            if (string.IsNullOrWhiteSpace(body.Name)) return BadRequest($"{nameof(body.Name)} is required");
            if (body.Fields.Length == 0) return BadRequest($"{nameof(body.Fields)} are empty");

            var nameEscaped = Validator.StringEscape(body.Name.Trim());
            var result = await KnowYourCustomerAPI.KnowYourCustomer.PerformQryAsync(HttpContext, $@"
                {{
                    existsTemplate = templates.Exists('{nameEscaped}');
                    print existsTemplate existsTemplate;
                }}
            ");
            string value = ((OkObjectResult)result).Value.ToString();
            var templateExistence = JsonConvert.DeserializeObject<TemplateExistence>(value);

            if (!templateExistence.existsTemplate)
            {
                var fieldsScript = new StringBuilder();
                foreach (var field in body.Fields)
                {
                    if (string.IsNullOrWhiteSpace(field.FieldId)) return BadRequest($"{nameof(field.FieldId)} is required");
                    if (field.Position <= 0) return BadRequest($"{nameof(field.Position)} is required");

                    fieldsScript.AppendLine($"field = fields.SearchFieldById('{field.FieldId}');");
                    fieldsScript.AppendLine($"template.AddSortedField({field.Position}, field);");
                }

                await KnowYourCustomerAPI.KnowYourCustomer.PerformCmdAsync(HttpContext, $@"
                {{
                    Eval('templateId =' + templates.NextTemplateConsecutive() + ';');
                    template = templates.CreateTemplate('{nameEscaped}',templateId);
                    {fieldsScript.ToString()}
					templates.Add(template);
                }}
            ");
            }

            return result;
        }

        [HttpPut("api/KnowYourCustomer/template")]
        [Authorize(Roles = "e7")]
        public async Task<IActionResult> UpdateTemplateAsync([FromBody] TemplateUpdatingBody body)
        {
            if (body == null) return BadRequest("Body is required");
            if (string.IsNullOrWhiteSpace(body.Name)) return BadRequest($"{nameof(body.Name)} is required");
            if (body.Fields.Length == 0) return BadRequest($"{nameof(body.Fields)} are empty");

            var nameEscaped = Validator.StringEscape(body.Name.Trim());
            var result = await KnowYourCustomerAPI.KnowYourCustomer.PerformQryAsync(HttpContext, $@"
                {{
                    existsTemplate = templates.Exists('{nameEscaped}');
                    print existsTemplate existsTemplate;
                }}
            ");
            string value = ((OkObjectResult)result).Value.ToString();
            var templateExistence = JsonConvert.DeserializeObject<TemplateExistence>(value);

            if (templateExistence.existsTemplate)
            {
                var fieldsScript = new StringBuilder();
                foreach (var field in body.Fields)
                {
                    if (string.IsNullOrWhiteSpace(field.FieldId)) return BadRequest($"{nameof(field.FieldId)} is required");
                    if (field.Position <= 0) return BadRequest($"{nameof(field.Position)} is required");

                    fieldsScript.AppendLine($"field = fields.SearchFieldById('{field.FieldId}');");
                    fieldsScript.AppendLine($"template.AddSortedField({field.Position}, field);");
                }

                var newNameEscaped = Validator.StringEscape(body.NewName.Trim());
                await KnowYourCustomerAPI.KnowYourCustomer.PerformCmdAsync(HttpContext, $@"
                {{
                    template = templates.GetTemplate('{nameEscaped}');
                    template.Name = '{newNameEscaped}';
                    template.ClearFields();
                    template.IncreaseVersion();
                    {fieldsScript.ToString()}
                }}
            ");
            }

            return result;
        }

        struct TemplateExistence
        {
            public bool existsTemplate { get; set; }
        }

        [HttpPost("api/KnowYourCustomer/templates/{name}/copy")]
        [Authorize(Roles = "e8")]
        public async Task<IActionResult> CopyTemplateAsync(string name)
        {
            if (string.IsNullOrWhiteSpace(name)) return BadRequest($"{nameof(name)} is required");

            var nameEscaped = Validator.StringEscape(name.Trim());
            var result = await KnowYourCustomerAPI.KnowYourCustomer.PerformCmdAsync(HttpContext, $@"
                {{
                    Eval('templateId =' + templates.NextTemplateConsecutive() + ';');
                    templates.CopyFrom('{nameEscaped}',templateId);
                }}
            ");
            return result;
        }

        [HttpGet("api/KnowYourCustomer/customerTypes")]
        [Authorize(Roles = "e19,h18")]
        public async Task<IActionResult> ListCustomerTypesAsync()
        {
            var result = await KnowYourCustomerAPI.KnowYourCustomer.PerformQryAsync(HttpContext, $@"
                {{
					print customerTypes.Count count;
					for(types : customerTypes.Types)
					{{
						specificCustomerType = types;
                        print specificCustomerType.Id id;
						print specificCustomerType.Name name;
						print specificCustomerType.Description description;
                        print specificCustomerType.BadgePath badgePath;
						print specificCustomerType.Enabled enabled;
                        lastEntries = specificCustomerType.Log.Entries();
						for (log:lastEntries)
						{{
							print log.DateFormattedAsText date;
							print log.Who who;
							print log.Message message;
						}}
                    }}
                }}
            ");
            return result;
        }

        [HttpGet("api/KnowYourCustomer/customerTypes/enabled")]
        [Authorize(Roles = "h18")]
        public async Task<IActionResult> ListEnabledCustomerTypesAsync()
        {
            var result = await KnowYourCustomerAPI.KnowYourCustomer.PerformQryAsync(HttpContext, $@"
                {{
					for(types : customerTypes.ListEnabledCustomerTypes())
					{{
						specificCustomerType = types;
                        print specificCustomerType.Id id;
						print specificCustomerType.Name name;
						print specificCustomerType.Description description;
                        print specificCustomerType.BadgePath badgePath;
                    }}
                }}
            ");
            return result;
        }

        [HttpPost("api/KnowYourCustomer/customerType")]
        [Authorize(Roles = "e21")]
        public async Task<IActionResult> CreateCustomerTypeAsync([FromBody] CustomerTypeBody body)
        {
            if (body == null) return BadRequest("Body is required");
            if (string.IsNullOrWhiteSpace(body.Name)) return BadRequest($"{nameof(body.Name)} is required");
            if (string.IsNullOrWhiteSpace(body.BadgePath)) return BadRequest($"{nameof(body.BadgePath)} is required");

            var nameEscaped = Validator.StringEscape(body.Name.Trim());
            var result = await KnowYourCustomerAPI.KnowYourCustomer.PerformQryAsync(HttpContext, $@"
                {{
                    existsCustomerType = customerTypes.ExistsName('{nameEscaped}');
                    print existsCustomerType existsCustomerType;
                }}
            ");

            if (!(result is OkObjectResult))
            {
                throw new GameEngineException($@"Error:{((ObjectResult)result).Value.ToString()}");
            }

            OkObjectResult o = (OkObjectResult)result;
            string json = o.Value.ToString();
            var existence = JsonConvert.DeserializeObject<CustomerTypeExistence>(json);

            if (!existence.ExistsCustomerType)
            {
                string employeeName = Security.UserName(HttpContext);

                string commandToDisable = string.Empty;
                if (!body.Enabled) commandToDisable = "customerType.Disable();";

                string commandForDescription = string.Empty;
                if (!string.IsNullOrWhiteSpace(body.Description))
                {
                    var descriptionEscaped = Validator.StringEscape(body.Description.Trim());
                    commandForDescription = $"customerType.Description = '{descriptionEscaped}';";
                }

                var badgePathEscaped = Validator.StringEscape(body.BadgePath.Trim());
                var commandForBadgePath = $"customerType.BadgePath = '{badgePathEscaped}';";

                return await KnowYourCustomerAPI.KnowYourCustomer.PerformCmdAsync(HttpContext, $@"
					{{
                        Eval('customerTypeId =' + customerTypes.NextConsecutive() + ';');
						customerType = customerTypes.NewCustomerType(customerTypeId, '{nameEscaped}', '{employeeName}', Now);
                        {commandForDescription}
                        {commandForBadgePath}
						{commandToDisable}
					}}
				");
            }
            else
            {
                var errorMessage = new StringBuilder();
                if (existence.ExistsCustomerType)
                {
                    errorMessage.AppendLine($"Sorry, there is a customer type using the name '{nameEscaped}'. Please try again with other one.");
                }

                return PrefabBadRequest(errorMessage.ToString());
            }
        }

        [HttpPut("api/KnowYourCustomer/customerType")]
        [Authorize(Roles = "e22")]
        public async Task<IActionResult> UpdateCustomerTypeAsync([FromBody] CustomerTypeUpdaterBody body)
        {
            if (body == null) return BadRequest("Body is required");
            if (string.IsNullOrWhiteSpace(body.Name)) return BadRequest($"{nameof(body.Name)} is required");
            if (string.IsNullOrWhiteSpace(body.NewName)) return BadRequest($"{nameof(body.NewName)} is required");
            if (string.IsNullOrWhiteSpace(body.BadgePath)) return BadRequest($"{nameof(body.BadgePath)} is required");

            var nameEscaped = Validator.StringEscape(body.Name.Trim());
            var newNameEscaped = Validator.StringEscape(body.NewName.Trim());
            var scriptCheckingNewName = string.Empty;
            var hasChangedName = nameEscaped != newNameEscaped;
            if (hasChangedName)
            {
                scriptCheckingNewName = $@"isAlreadyUsedNewName = customerTypes.ExistsName('{newNameEscaped}');
                    print isAlreadyUsedNewName isAlreadyUsedNewName;";
            }
            var result = await KnowYourCustomerAPI.KnowYourCustomer.PerformQryAsync(HttpContext, $@"
                {{
                    existsCustomerType = customerTypes.ExistsName('{nameEscaped}');
                    print existsCustomerType existsCustomerType;
					{scriptCheckingNewName}
                }}
            ");

            if (!(result is OkObjectResult))
            {
                throw new GameEngineException($@"Error:{((ObjectResult)result).Value.ToString()}");
            }

            OkObjectResult o = (OkObjectResult)result;
            string json = o.Value.ToString();
            var existence = JsonConvert.DeserializeObject<CustomerTypeAndNewNameExistence>(json);

            if (existence.ExistsCustomerType && !existence.IsAlreadyUsedNewName)
            {
                string employeeName = Security.UserName(HttpContext);

                var descriptionEscaped = !string.IsNullOrWhiteSpace(body.Description) ? Validator.StringEscape(body.Description.Trim()) : string.Empty;
                var commandForDescription = $"customerType.Description = '{descriptionEscaped}';";

                var badgePathEscaped = Validator.StringEscape(body.BadgePath.Trim());
                var commandForBadgePath = $"customerType.BadgePath = '{badgePathEscaped}';";

                return await KnowYourCustomerAPI.KnowYourCustomer.PerformCmdAsync(HttpContext, $@"
					{{
						customerType = customerTypes.FindCustomerType('{nameEscaped}');
						customerType.Update('{newNameEscaped}', {body.Enabled}, '{employeeName}', Now);
                        {commandForDescription}
                        {commandForBadgePath}
					}}
				");
            }
            else
            {
                var errorMessage = new StringBuilder();
                if (!existence.ExistsCustomerType)
                {
                    errorMessage.AppendLine($"Sorry, no customer type with the name '{nameEscaped}'. Please try again with other one.");
                }
                if (existence.IsAlreadyUsedNewName)
                {
                    errorMessage.AppendLine($"Sorry, there is a customer type using the name '{newNameEscaped}'. Please try again with other one.");
                }

                return PrefabBadRequest(errorMessage.ToString());
            }
        }

        private ContentResult PrefabBadRequest(string errorMessage)
        {
            ApiError error = new ApiError(errorMessage,
                    "No stack.",
                    "No command error info.",
                    "No Error info.");

            var contentResult = new ContentResult
            {
                ContentType = "application/json",
                Content = JsonConvert.SerializeObject(error),
                StatusCode = 400
            };
            return contentResult;
        }

        [DataContract(Name = "CustomerTypeUpdaterBody")]
        public class CustomerTypeUpdaterBody
        {
            [DataMember(Name = "name")]
            public string Name { get; set; }
            [DataMember(Name = "newName")]
            public string NewName { get; set; }
            [DataMember(Name = "description")]
            public string Description { get; set; }
            [DataMember(Name = "badgePath")]
            public string BadgePath { get; set; }
            [DataMember(Name = "enabled")]
            public bool Enabled { get; set; }
        }

        [DataContract(Name = "CustomerTypeBody")]
        public class CustomerTypeBody
        {
            [DataMember(Name = "name")]
            public string Name { get; set; }
            [DataMember(Name = "description")]
            public string Description { get; set; }
            [DataMember(Name = "badgePath")]
            public string BadgePath { get; set; }
            [DataMember(Name = "enabled")]
            public bool Enabled { get; set; }
        }

        [DataContract(Name = "CustomerTypeExistence")]
        public class CustomerTypeExistence
        {
            [DataMember(Name = "existsCustomerType")]
            public bool ExistsCustomerType { get; set; }
        }

        [DataContract(Name = "CustomerTypeAndNewNameExistence")]
        public class CustomerTypeAndNewNameExistence
        {
            [DataMember(Name = "existsCustomerType")]
            public bool ExistsCustomerType { get; set; }
            [DataMember(Name = "isAlreadyUsedNewName")]
            public bool IsAlreadyUsedNewName { get; set; }
        }

        [DataContract(Name = "ExistCustomer")]
        public class CustomerExistence
        {
            [DataMember(Name = "theCustomerItsNewOne")]
            public bool TheCustomerItsNewOne { get; set; }
            [DataMember(Name = "accountNumber")]
            public string AccountNumber { get; set; }
        }

        [DataContract(Name = "CustomersIdsBody")]
        public class CustomersIdsBody
        {
            [DataMember(Name = "customerIds")]
            public string[] CustomerIds { get; set; }
        }

        [DataContract(Name = "CustomerUpdatingBody")]
        public class CustomerUpdatingBody
        {
            [DataMember(Name = "name")]
            public string Name { get; set; }
            [DataMember(Name = "lastName")]
            public string LastName { get; set; }
        }

        [DataContract(Name = "TemplateUpdatingBody")]
        public class TemplateUpdatingBody
        {
            [DataMember(Name = "name")]
            public string Name { get; set; }
            [DataMember(Name = "newName")]
            public string NewName { get; set; }
            [DataMember(Name = "fields")]
            public FieldBody[] Fields { get; set; }
        }

        [DataContract(Name = "TemplateCreationBody")]
        public class TemplateCreationBody
        {
            [DataMember(Name = "name")]
            public string Name { get; set; }
            [DataMember(Name = "fields")]
            public FieldBody[] Fields { get; set; }
        }

        [DataContract(Name = "FieldBody")]
        public class FieldBody
        {
            [DataMember(Name = "position")]
            public int Position { get; set; }
            [DataMember(Name = "fieldId")]
            public string FieldId { get; set; }
        }

        [DataContract(Name = "CustomerWithTemplateBody")]
        public class CustomerWithTemplateBody
        {
            [DataMember(Name = "customer")]
            public JObject Customer { get; set; }
            [DataMember(Name = "template")]
            public JObject Template { get; set; }
        }

        [DataContract(Name = "CustomerRejectionBody")]
        public class CustomerRejectionBody
        {
            [DataMember(Name = "reason")]
            public string Reason { get; set; }
        }

        [DataContract(Name = "CustomerLogBody")]
        public class CustomerLogBody
        {
            [DataMember(Name = "log")]
            public string Log { get; set; }
        }

        [DataContract(Name = "AccountBody")]
        public class AccountBody
        {
            [DataMember(Name = "accountNumber")]
            public string AccountNumber { get; set; }
        }

        [DataContract(Name = "SerializedFieldsBody")]
        public class SerializedFieldsBody
        {
            [DataMember(Name = "serializedFields")]
            public string SerializedFields { get; set; }
            [DataMember(Name = "log")]
            public string Log { get; set; }
            [DataMember(Name = "uniqueValue")]
            public string UniqueValue { get; set; }
            [DataMember(Name = "isCustomerAutoApproved")]
            public bool IsCustomerAutoApproved { get; set; }
            [DataMember(Name = "isCustomerAlreadyApproved")]
            public bool IsCustomerAlreadyApproved { get; set; }
            [DataMember(Name = "now")]
            public string NowAsText { get; set; }
            public DateTime Now
            {
                get
                {
                    return DateTime.ParseExact(NowAsText, "M/d/yyyy HH:mm:ss", Integration.CultureInfoEnUS);
                }
            }

        }

        
    }
}
