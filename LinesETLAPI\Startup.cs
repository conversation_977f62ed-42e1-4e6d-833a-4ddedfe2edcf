using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Elastic.Apm.AspNetCore;
using ExternalServices;
using GamesEngine.Games;
using GamesEngine.Middleware;
using GamesEngine.Middleware.Providers;
using GamesEngine.Middleware.Providers.Bet365;
using GamesEngine.Settings;
using GamesEngineMocks;
using LinesETLAPI.Controllers;
using LinesETLAPI;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.OpenApi.Models;
using Puppeteer.EventSourcing;
using Betfair.ESAClient.Auth;
using Betfair.ESAClient;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using System.Runtime.Serialization;
using static GamesEngine.Middleware.Providers.BetFair.BetFairProvider;
using GamesEngine.Time;
using GamesEngine.MessageQueuing;

namespace LinesETLAPI
{
	public class Startup
	{
		public Startup(IConfiguration configuration)
		{
			Configuration = configuration;
		}

		public IConfiguration Configuration { get; }

		// This method gets called by the runtime. Use this method to add services to the container.
		public void ConfigureServices(IServiceCollection services)
		{
			services.AddControllers();

			services.AddSwaggerGen(c =>
			{
				c.SwaggerDoc("v1", new OpenApiInfo { Title = "ETL Lines API", Version = "v1" });
			});

			var biIntegration = Configuration.GetSection("BIIntegration");

			Integration.Configure(KafkaMessage.Prefix.NoTransacction, biIntegration);
			Integration.ConfigureAPM(Configuration);

			var messageOriginId = Configuration.GetValue<string>("MessageOriginId");
			if (string.IsNullOrEmpty(messageOriginId) && !int.TryParse(messageOriginId, out _)) throw new Exception("MessageOriginId its requeried in the appsettings.");
			MessageCatalog.AssingMessageOriginId(Convert.ToInt32(messageOriginId));
		}

		// This method gets called by the runtime. Use this method to configure the HTTP request pipeline.
		public void Configure(IApplicationBuilder app, IWebHostEnvironment env)
		{
			if (Integration.UseAPM)
			{
				app.UseElasticApm(Configuration);
			}
			app.UseSwagger();
			app.UseSwaggerUI(c =>
			{
				c.SwaggerEndpoint("/swagger/v1/swagger.json", "ETL Lines API V1");
				//c.RoutePrefix = string.Empty;
			});
			app.UseDeveloperExceptionPage();

			if (env.IsDevelopment())
			{
				app.UseDeveloperExceptionPage();
			}

			app.UseRouting();

			var sectionDairy = Configuration.GetSection("DBDairy");
			var mySQL = sectionDairy.GetSection("ConnectionStrings").GetValue<string>("MySQL");
			var sqlServer = sectionDairy.GetSection("ConnectionStrings").GetValue<string>("SQLServer");
			var dbSelected = sectionDairy.GetValue<string>("DBSelected");
			var scriptBeforeRecovering = Configuration.GetValue<string>("ActionsBeforeRecovering");

			string AppKey = "foNWTNM98v2kQKf1";
			string UserName = "cguillen%40ncubo.com";
			string Password = "B%23St5i2RyXdSu%25A";
			BetFairUpdater.SetUpBetfairClient(AppKey, UserName, Password);

			DBDairy dbDairy = new DBDairy();
			dbDairy.DBSelected = dbSelected;
			Integration.DbDairy = dbDairy;

			if (dbSelected == DatabaseType.MySQL.ToString())
			{
				LinesETLAPI.LinesETL.EventSourcingStorage(DatabaseType.MySQL, mySQL, scriptBeforeRecovering, AccountingSettings.NeedsUniqueIdentifierForPaymentHub);
			}
			else if (dbSelected == DatabaseType.SQLServer.ToString())
			{
				LinesETLAPI.LinesETL.EventSourcingStorage(DatabaseType.SQLServer, sqlServer, scriptBeforeRecovering, AccountingSettings.NeedsUniqueIdentifierForPaymentHub);
			}
			else if (!String.IsNullOrWhiteSpace(dbSelected))
			{
				throw new Exception($"There is no connection for {dbSelected}");
			}
			else
			{
				//Integration.Kafka.OffSetResetToLatest();
				int numberOfTheMockConfigured = Convert.ToInt32(Configuration["MockToStart"]);

				var DOTNET_RUNNING_IN_CONTAINER = bool.Parse(System.Environment.GetEnvironmentVariable("DOTNET_RUNNING_IN_CONTAINER") ?? "false");
				if (DOTNET_RUNNING_IN_CONTAINER)
					LinesETLAPI.LinesETL.EventSourcingStorage(DatabaseType.MySQL, mySQL, scriptBeforeRecovering, AccountingSettings.NeedsUniqueIdentifierForPaymentHub);

				RunMock(LinesETLAPI.LinesETL.Actor, numberOfTheMockConfigured);
			}

			#region sync sports
			Jobs jobs = Jobs.Instance()
					.Resgiter(
						new BetFairUpdater());
			#endregion

			app.UseEndpoints(endpoints =>
			{
				endpoints.MapControllers();
			});

			ConsoleController.liveness = true;
			ConsoleController.readyness = true;

			Integration.ConfigureTimer();
			if (Integration.UseKafka)
			{
				new Consumers().CreateConsumerForTopics();
			}

			GamesEngine.Time.Job job = new GamesEngine.Time.Job(
						"Close marketid cleaner",
						DateTime.Now.AddHours(-1)
						);

			job.WhenHappens +=  (WhenHappensInfo info) =>
			{
				try
				{
					DateTime eventDate = job.TriggerDate;
					string date = $"{eventDate.Month}/{eventDate.Day}/{eventDate.Year} {eventDate.Hour}:{eventDate.Minute}:{eventDate.Second}";
					var result = LinesETLAPI.LinesETL.PerformQry($@"
						{{
							lineMappings = mapper.LineMappingsForProvider({ProvidersCollection.BetFair.Id}, {date});
							for(lineMapping : lineMappings)
							{{
								print lineMapping.IdInTheProvider marketId;
								print lineMapping.GameId gameId;
								print lineMapping.TournamentId tournamentId;
							}}
						}}
						");
					if (!(result is OkObjectResult))
					{
						throw new Exception($@"Error:{((ObjectResult)result).Value.ToString()}");
					}
					OkObjectResult o = (OkObjectResult)result;
					string json = o.Value.ToString();
					LineMappingContext context = JsonConvert.DeserializeObject<LineMappingContext>(json);

					Jobs jobs = Jobs.Instance();
					Provider provider = ProvidersCollection.BetFair;
					BetFairUpdater updater = (BetFairUpdater)jobs.SearchByProvider(provider);

					string linesToDelete = "";
					if (context!=null)
					{
						foreach (KeyValuePair<string, Contexts> pairs in context.GroupByGame())
						{
							//updater.UpdateLines(true, pairs.Value.MarketIds, pairs.Value.TournamentId, pairs.Value.GameId);
							PrizesByMarketId prizesByMarketId = updater.SearchAllThePricesAndStatus(pairs.Value.MarketIds);
							IEnumerable<StatusAndPrizesByRunnerId> statusPrizes = prizesByMarketId.SearchClosedOnly();
							foreach (StatusAndPrizesByRunnerId statusPrize in statusPrizes)
							{
								linesToDelete += $@"
								mapper.Remove(LineMappingToBeRemoved({ProvidersCollection.BetFair.Id}, '{statusPrize.MarketId}'));
							";
							}
						}
						if (!string.IsNullOrEmpty(linesToDelete))
						{
							LinesETLAPI.LinesETL.PerformCmd($@"
							{{
								{linesToDelete}
							}}
						");
						}
					}

				}
				catch (Exception e)
				{
					ErrorsSender.Send(e);
				}
			};
			job.WhenReschedule = (Timer timer, GamesEngine.Time.Job job) =>
			{
				job.ChangeDate(job.TriggerDate.AddDays(1));
				Integration.TimersLoaded.Add(job);
			};

			Integration.TimersLoaded.Add(job);

			AddBetfairLeagues();
		}
		[DataContract(Name = "LineMappingContext")]
		public class LineMappingContext
		{
			[DataMember(Name = "lineMapping")]
			public List<Context> LineMapping { get; set; }

			public Dictionary<string, Contexts> GroupByGame()
			{
				Dictionary<string, Contexts> result = new Dictionary<string, Contexts>();
				foreach (Context lineMappingItem in LineMapping)
				{
					string id = $"{lineMappingItem.TournamentId}{lineMappingItem.GameId}";
					Contexts contexts;
					if (!result.TryGetValue(id, out contexts))
					{
						contexts = new Contexts();
						result.Add(id, contexts);
					}
					contexts.Add(lineMappingItem);
				}
				return result;
			}
		}
		public class Contexts
		{
			private List<Context> contexts = new List<Context>();
			private List<string> marketids = new List<string>();

			public int TournamentId { get { return contexts[0].TournamentId; } }
			public int GameId { get { return contexts[0].GameId; } }

			public string[] MarketIds { get { return marketids.ToArray(); } }

			public void Add(Context lineMappingItem)
			{
				contexts.Add(lineMappingItem);
				marketids.Add(lineMappingItem.MarketId);
			}

		}
		
		[DataContract(Name = "Context")]
		public class Context
		{
			[DataMember(Name = "marketId")]
			public string MarketId { get; set; }
			[DataMember(Name = "gameId")]
			public int GameId { get; set; }
			[DataMember(Name = "tournamentId")]
			public int TournamentId { get; set; }
		}

		void AddBetfairLeagues()
        {
			var job2 = new GamesEngine.Time.Job(
				"leagues",
				DateTime.Now.AddHours(-1)
				);
			job2.WhenHappens += (WhenHappensInfo info) =>
			{
				const string name = "BetFair";
				Jobs jobs = Jobs.Instance();
				BetFairUpdater updater = (BetFairUpdater)jobs.SearchByName(name);
				updater.UpdateLeague(true, null);
			};
			job2.WhenReschedule = (Timer timer, GamesEngine.Time.Job job2) =>
			{
				job2.ChangeDate(job2.TriggerDate.AddDays(1));
				Integration.TimersLoaded.Add(job2);
			};
			Integration.TimersLoaded.Add(job2);
		}

		void RunMock(Puppeteer.EventSourcing.Actor actor, int index = -1)
		{
			switch (index)
			{
				case 0:
					bool itIsThePresent = true;
					LinesETLMocks.Init(actor);

					Country USA = LocalEnvironmentCountries.Instance().FindByCode("us");
					Country Spain = LocalEnvironmentCountries.Instance().FindByCode("es");
					Country Australia = LocalEnvironmentCountries.Instance().FindByCode("au");
					Country Belgium = LocalEnvironmentCountries.Instance().FindByCode("be");
					Country Brazil = LocalEnvironmentCountries.Instance().FindByCode("br");
					Country Canada = LocalEnvironmentCountries.Instance().FindByCode("ca");
					Country Germany = LocalEnvironmentCountries.Instance().FindByCode("de");
					Country France = LocalEnvironmentCountries.Instance().FindByCode("fr");
					Country Italy = LocalEnvironmentCountries.Instance().FindByCode("it");

					string marketId1 = "", marketId2 = "", marketId3="";
					string league1 = "", league2 = "";
					Jobs jobs = Jobs.Instance()
					.Resgiter(
						new Bet365Updater(),
						new BetFairUpdater())
					.SyncCountries();
					/*.UpdateLeagues(itIsThePresent, Sport.SOCCER, USA) //Bet365
					.UpdateLeagues(itIsThePresent, (NormalizedProviderResponse response) =>
					{
						dynamic arrayOfMarkets = response.EvalPayloadAsArray();
						league1 = arrayOfMarkets[1].competition.id.ToString();
						league2 = arrayOfMarkets[4].competition.id.ToString();

					}) //Betfair
					.UpdateSeason(itIsThePresent, 1) //Bet365
					.UpdateTeam(itIsThePresent, Sport.SOCCER, USA) //Bet365
					.UpdateTeam(itIsThePresent, league1)//Betfair 
					.UpdateTeam(itIsThePresent, league2);//Betfair 
					.UpdateLines(itIsThePresent, 1, 1, (NormalizedProviderResponse response) =>//TODO cris filtrar por competition  o liga
					{
						dynamic arrayOfMarkets = response.EvalPayloadAsArray();
						marketId1 = arrayOfMarkets[1].marketId;
						marketId2 = arrayOfMarkets[4].marketId;
						marketId3 = arrayOfMarkets[6].marketId;
					})//Betfair
					.SyncLine(itIsThePresent, marketId1, marketId2, marketId3) //Betfair
					.SubscribeMarkets(itIsThePresent,
					new string[]{ marketId1, marketId2, marketId3},1,1); //Betfair
					;*/
					break;
				default:
					throw new Exception($"The mock {index} its not implemented yet.");
			}
		}
	}
}
