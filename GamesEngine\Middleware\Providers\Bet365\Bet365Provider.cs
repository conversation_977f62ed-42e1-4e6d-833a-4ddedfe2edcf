﻿using GamesEngine.Games;
using GamesEngine.Middleware.Providers;
using GamesEngine.Settings;
using RestSharp;
using System;
using System.Collections.Generic;
using System.Text;
using static GamesEngine.Middleware.Providers.NormalizedProviderResponse;
using static GamesEngine.Middleware.Providers.Response;

namespace GamesEngine.Middleware.Providers.Bet365
{
	public class Bet365Provider : Provider
	{
		internal Bet365Provider(int id, string name) : base(id, name)
		{

		}
		internal NormalizedProviderResponse Leagues(Authentication authentication, Sport sport, int page, Action<LeaguesResponse> WhenAPIResponse)
		{
			NormalizedProviderResponse response = Leagues(authentication, sport);
			if (WhenAPIResponse != null)
			{
				LeaguesResponse leaguesInBetsAPi = Newtonsoft.Json.JsonConvert.DeserializeObject<LeaguesResponse>(response.Payload);
				WhenAPIResponse(leaguesInBetsAPi);
				response = new NormalizedProviderResponse(response.Provider, ResponseType.LEAGUE, Newtonsoft.Json.JsonConvert.SerializeObject(leaguesInBetsAPi));
			}
			return response;
		}
		internal NormalizedProviderResponse Leagues(Authentication authentication, Sport sport, Country cc)
		{
			string url = $"https://api.b365api.com/v1/league?token={((Bet365Authentication)authentication).Token}&sport_id={sport.Id}&cc={cc.Code}";


			RestClient client = new RestClient($"https://api.b365api.com/v1/league?token={((Bet365Authentication)authentication).Token}&sport_id={sport.Id}&cc={cc.Code}");
			client.Timeout = -1;
			var request = new RestRequest(Method.GET);
			IRestResponse response = client.Execute(request);

			if ((int)response.StatusCode == 200)
			{
				return new NormalizedProviderResponse(this, ResponseType.LEAGUE, response.Content);
			}

			ErrorsSender.Send($"No Leagues were retrieved from {this.GetType().Name} provider", response.Content);
			throw new GameEngineException($"No Leagues were retrieved from {this.GetType().Name} provider");
		}
		internal NormalizedProviderResponse Leagues(Authentication authentication, Sport sport, int page)
		{
			RestClient client = new RestClient($"https://api.b365api.com/v1/league?token={((Bet365Authentication)authentication).Token}&sport_id={sport.Id}&page={page}");
			client.Timeout = -1;
			var request = new RestRequest(Method.GET);
			IRestResponse response = client.Execute(request);

			if ((int)response.StatusCode == 200)
			{
				return new NormalizedProviderResponse(this, ResponseType.LEAGUE, response.Content);
			}

			ErrorsSender.Send($"No Leagues were retrieved from {this.GetType().Name} provider", response.Content);
			throw new GameEngineException($"No Leagues were retrieved from {this.GetType().Name} provider");
		}
		internal NormalizedProviderResponse Leagues(Authentication authentication, Sport sport, Country cc, int page)
		{
			RestClient client = new RestClient($"https://api.b365api.com/v1/league?token={((Bet365Authentication)authentication).Token}&sport_id={sport.Id}&cc={cc.Code}&page={page}");
			client.Timeout = -1;
			var request = new RestRequest(Method.GET);
			IRestResponse response = client.Execute(request);

			if ((int)response.StatusCode == 200)
			{
				return new NormalizedProviderResponse(this, ResponseType.LEAGUE, response.Content);
			}

			ErrorsSender.Send($"No Leagues were retrieved from {this.GetType().Name} provider", response.Content);
			throw new GameEngineException($"No Leagues were retrieved from {this.GetType().Name} provider");
		}
		internal static Bet365Authentication GenerateAuthentication()
		{
			return new Bet365Authentication("Fake");
		}
		internal NormalizedProviderResponse Leagues(Authentication authentication, Sport sport)
		{
			RestClient client = new RestClient($"https://api.b365api.com/v1/league?token={((Bet365Authentication)authentication).Token}&sport_id={sport.Id}");
			client.Timeout = -1;
			var request = new RestRequest(Method.GET);
			IRestResponse response = client.Execute(request);

			if ((int)response.StatusCode == 200)
			{
				return new NormalizedProviderResponse(this, ResponseType.LEAGUE, response.Content);
			}

			ErrorsSender.Send($"No Leagues were retrieved from {this.GetType().Name} provider", response.Content);
			throw new GameEngineException($"No Leagues were retrieved from {this.GetType().Name} provider");
		}
		internal NormalizedProviderResponse LeagueTable(Authentication authentication, int leagueId)
		{
			RestClient client = new RestClient($"https://api.b365api.com/v2/league/table?token={((Bet365Authentication)authentication).Token}&league_id={leagueId}");
			client.Timeout = -1;
			var request = new RestRequest(Method.GET);
			IRestResponse response = client.Execute(request);

			if ((int)response.StatusCode == 200)
			{
				return new NormalizedProviderResponse(this, ResponseType.SEASON, response.Content, new Attachments() { LeagueId = leagueId.ToString() });
			}

			ErrorsSender.Send($"No Season was retrieved from {this.GetType().Name} provider", response.Content);
			throw new GameEngineException($"No Season was retrieved from {this.GetType().Name} provider");
		}
		internal NormalizedProviderResponse Team(Authentication authentication, Sport sport)
		{
			RestClient client = new RestClient($"https://api.b365api.com/v2/league/table?token={((Bet365Authentication)authentication).Token}&sport_id={sport.Id}");
			client.Timeout = -1;
			var request = new RestRequest(Method.GET);
			IRestResponse response = client.Execute(request);

			if ((int)response.StatusCode == 200)
			{
				return new NormalizedProviderResponse(this, ResponseType.TEAM, response.Content);
			}

			ErrorsSender.Send($"No Teams were retrieved from {this.GetType().Name} provider", response.Content);
			throw new GameEngineException($"No Teams were retrieved from {this.GetType().Name} provider");
		}
		internal NormalizedProviderResponse Team(Authentication authentication, Sport sport, Country country)
		{
			RestClient client = new RestClient($"https://api.b365api.com/v2/league/table?token={((Bet365Authentication)authentication).Token}&sport_id={sport.Id}&cc={country.Code}");
			client.Timeout = -1;
			var request = new RestRequest(Method.GET);
			IRestResponse response = client.Execute(request);

			if ((int)response.StatusCode == 200)
			{
				return new NormalizedProviderResponse(this, ResponseType.TEAM, response.Content);
			}

			ErrorsSender.Send($"No Teams were retrieved from {this.GetType().Name} provider", response.Content);
			throw new GameEngineException($"No Teams were retrieved from {this.GetType().Name} provider");
		}
		internal NormalizedProviderResponse Team(Authentication authentication, Sport sport, Country country, int page)
		{
			RestClient client = new RestClient($"https://api.b365api.com/v2/league/table?token={((Bet365Authentication)authentication).Token}&sport_id={sport.Id}&cc={country.Code}&page={page}");
			client.Timeout = -1;
			var request = new RestRequest(Method.GET);
			IRestResponse response = client.Execute(request);

			TeamsResponse teamsResponse = Newtonsoft.Json.JsonConvert.DeserializeObject<TeamsResponse>(response.Content);

			if ((int)response.StatusCode == 200)
			{
				return new NormalizedProviderResponse(this, ResponseType.TEAM, response.Content);
			}

			ErrorsSender.Send($"No Teams were retrieved from {this.GetType().Name} provider", response.Content);
			throw new GameEngineException($"No Teams were retrieved from {this.GetType().Name} provider");
		}
		internal class Bet365Authentication : Authentication
		{
			internal Bet365Authentication(string token)
			{
				Token = token;
			}
			internal string Token { get; }
		}
	}
}
