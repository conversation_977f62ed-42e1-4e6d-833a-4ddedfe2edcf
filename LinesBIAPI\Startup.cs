﻿using Elastic.Apm.AspNetCore;
using Elastic.Apm.NetCoreAll;
using ExternalServices;
using GamesEngine.Games.Lines;
using GamesEngine.MessageQueuing;
using GamesEngine.Settings;
using GamesEngineMocks;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.HttpOverrides;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Puppeteer.EventSourcing;
using System;
using System.Threading;

namespace LinesBIAPI
{
	public class Startup
    {
        public Startup(IWebHostEnvironment env, IConfiguration configuration)
        {
            var builder = new ConfigurationBuilder()
                .SetBasePath(env.ContentRootPath)
                .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true)
                .AddJsonFile("customization/appsettings.accounting.json", optional: true)
                .AddJsonFile("customization/appsettings.security.json", optional: true)
                .AddJsonFile("customization/appsettings.kafka.json", optional: true)
                .AddJsonFile("customization/appsettings.error_emails.json", optional: true)
                .AddJsonFile("customization/appsettings.apm.json", optional: true)
                .AddJsonFile("secrets/appsettings.secrets.json", optional: true);
            Configuration = builder.Build();
            Environment = env;
        }

        public IConfiguration Configuration { get; }
        public IWebHostEnvironment Environment { get; }

        // This method gets called by the runtime. Use this method to add services to the container.
        public void ConfigureServices(IServiceCollection services)
        {
            int minWorker, minIOC;
            ThreadPool.GetMinThreads(out minWorker, out minIOC);
            int maxWorker, maxIOC;
            ThreadPool.GetMaxThreads(out maxWorker, out maxIOC);

            Console.WriteLine($@" minWorker:{minWorker} minIOC:{minIOC} maxWorker:{maxWorker} maxIOC:{maxIOC}");

            /*Security*/
            Security.Configure(services, Configuration);
            /*Security*/

            services.AddMvc(options => options.EnableEndpointRouting = false).AddNewtonsoftJson();

            if (string.IsNullOrEmpty(Configuration.GetValue<string>("CashierUrl"))) throw new Exception("CashierUrl its requeried in the appsettings.");
            Settings.CashierUrl = Configuration.GetValue<string>("CashierUrl");

            services.AddSingleton<IHttpContextAccessor, HttpContextAccessor>();
            services.Configure<IISServerOptions>(options =>
            {
                options.AllowSynchronousIO = true;
            });
            var biIntegration = Configuration.GetSection("BIIntegration");

            AccountingSettings.NeedsUniqueIdentifierForPaymentHub = "STAGE_NEED_IT";

            Integration.Configure(KafkaMessage.Prefix.withExchangePrefix, biIntegration);

            ErrorsSender.Configure(Configuration.GetSection("ErrorsSender"), Environment.IsProduction());
            Integration.ConfigureAPM(Configuration);

            var messageOriginId = Configuration.GetValue<string>("MessageOriginId");
            if (string.IsNullOrEmpty(messageOriginId) && !int.TryParse(messageOriginId, out _)) throw new Exception("MessageOriginId its requeried in the appsettings.");
            MessageCatalog.AssingMessageOriginId(Convert.ToInt32(messageOriginId));
        }

        // This method gets called by the runtime. Use this method to configure the HTTP request pipeline.
        public void Configure(IApplicationBuilder app, IWebHostEnvironment env)
        {
            if (Integration.UseAPM)
            {
                app.UseElasticApm(Configuration);
            }
            if (env.IsDevelopment())
            {
                app.UseDeveloperExceptionPage();
            }
            else
            {
                if (String.IsNullOrEmpty(Integration.Db.DBSelected))
                {
                    throw new Exception("Db configuration its required.");
                }
                if (!Integration.UseKafka)
                {
                    throw new Exception("This project must use kafka integration to show data.");
                }
            }
            var sectionDairy = Configuration.GetSection("DBDairy");
            var mySQL = sectionDairy.GetSection("ConnectionStrings").GetValue<string>("MySQL");
            var sqlServer = sectionDairy.GetSection("ConnectionStrings").GetValue<string>("SQLServer");
            var dbSelected = sectionDairy.GetValue<string>("DBSelected");
            var scriptBeforeRecovering = Configuration.GetValue<string>("ActionsBeforeRecovering");

            DBDairy dbDairy = new DBDairy();
            dbDairy.DBSelected = dbSelected;
            Integration.DbDairy = dbDairy;

            if (dbSelected == DatabaseType.MySQL.ToString())
            {
                 LinesBIAPI.LinesBI.EventSourcingStorage(DatabaseType.MySQL, mySQL, scriptBeforeRecovering, AccountingSettings.NeedsUniqueIdentifierForPaymentHub);
            }
            else if (dbSelected == DatabaseType.SQLServer.ToString())
            {
                 LinesBIAPI.LinesBI.EventSourcingStorage(DatabaseType.SQLServer, sqlServer, scriptBeforeRecovering, AccountingSettings.NeedsUniqueIdentifierForPaymentHub);
            }
            else if (!String.IsNullOrWhiteSpace(dbSelected))
            {
                throw new Exception($"There is no connection for {dbSelected}");
            }
            else
            {
                //Integration.Kafka.OffSetResetToLatest();
                int numberOfTheMockConfigured = Convert.ToInt32(Configuration["MockToStart"]);

                var DOTNET_RUNNING_IN_CONTAINER = bool.Parse(System.Environment.GetEnvironmentVariable("DOTNET_RUNNING_IN_CONTAINER") ?? "false");
                if (DOTNET_RUNNING_IN_CONTAINER)
                    LinesBIAPI.LinesBI.EventSourcingStorage(DatabaseType.MySQL, mySQL, scriptBeforeRecovering, AccountingSettings.NeedsUniqueIdentifierForPaymentHub);

                RunMock(LinesBIAPI.LinesBI.Actor, numberOfTheMockConfigured);
            }

            /*Security*/
            app.UseAuthentication();
            /*Security*/

            app.UseMvc();

            ReceiverOfHistorical receiverForLineScores = new ReceiverOfHistorical();

            if (Integration.Db.DBSelected == HistoricalDatabaseType.MySQL.ToString())
            {
                receiverForLineScores.InitHistorical(HistoricalDatabaseType.MySQL, Integration.Db.MySQL);
            }
            else if (Integration.Db.DBSelected == HistoricalDatabaseType.SQLServer.ToString())
            {
                receiverForLineScores.InitHistorical(HistoricalDatabaseType.SQLServer, Integration.Db.SQLServer);
            }
            else if (!String.IsNullOrWhiteSpace(Integration.Db.DBSelected))
            {
                throw new Exception($"There is no connection for {Integration.Db.DBSelected}");
            }

            if (Integration.UseKafka)
            {
                new Consumers().CreateConsumerForTopics(receiverForLineScores);
            }

            ForwardedHeadersOptions options = new ForwardedHeadersOptions();
            options.ForwardedHeaders = ForwardedHeaders.XForwardedFor;
            app.UseForwardedHeaders(options);
        }

        void RunMock(Puppeteer.EventSourcing.Actor actor, int index = -1)
        {
            switch (index)
            {
                case 0:
                    LinesBIMocks.Init(actor);
                    break;
                default:
                    throw new Exception($"The mock {index} its not implemented yet.");
            }
        }
    }

}
