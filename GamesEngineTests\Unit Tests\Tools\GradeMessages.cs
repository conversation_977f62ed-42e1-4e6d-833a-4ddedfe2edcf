﻿using GamesEngine;
using GamesEngine.Bets;
using GamesEngine.Business;
using GamesEngine.Domains;
using GamesEngine.Finance;
using GamesEngine.Games.Lotto;
using GamesEngine.Gameboards;
using GamesEngine.Gameboards.Lotto;
using GamesEngine.Location;
using GamesEngine.PurchaseOrders;
using GamesEngine.Settings;
using GamesEngine.Tools;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using Puppeteer.EventSourcing;
using System;
using System.Collections;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;
using GamesEngineTests.Unit_Tests.Games.Lotto;
using town.connectors.commons;
using GamesEngine.Exchange;

namespace Unit.Games.Tools
{
	[TestClass]
	public class GradeMessages
	{
		[TestMethod]
		public void PurchaseOneTN()
		{
			//Integration.UseKafka = true;
			Integration.SavePreviousSettings(tempUseKafka: true, tempUseKafkaForAuto: false);
			QueueInMemory queue = new QueueInMemory();
			Integration.Kafka = queue;
			bool itIsThePresent = true;

			#region Create 2 purchases

			string employeeName = "Bart";
			Company company = new Company();
			CurrenciesTest.AddCurrencies();
			company.Accounting = new MockAccounting();
			company.Sales.CreateStore(1, "Test Store").MakeCurrent();
			var lotteries = company.Lotto900();
			DateTime now = new DateTime(2019, 04, 17, 9, 00, 00);
			State state = lotteries.GetOrCreateState("VA", "Virginia", now, employeeName);
			LotteryPick<Pick3> lottery = (LotteryPick<Pick3>)lotteries.GetOrCreateLottery(3, state);
			for (int i = 0; i <= 6; i++) lottery.Every(10, 20, i, now, employeeName);
			var drawDate = new DateTime(2019, 04, 17, 10, 20, 00);
			var schedule = lottery.GetScheduleId(1);
			schedule.Update(true, "VA Morning", "0123456", 10, 20, now, "Game Admin");

			Player player = company.GetOrCreateCustomerById("*********").Player;
			string states = "VA";
			string hours = "10:20 AM";
			string strIsPresentPlayerBeforeToCloseStore = "true";
			string useNextDates = "true";
			string dates = "04/17/2019";
			string withFireBalls = "false";
			string numbers = "3,4,23";
			string selectionMode = "MultipleInputSingleAmount";
			string pickNumber = "3";
			decimal ticketAmount = 0.5m;
			string includedNumbersForInput = "343,342";
			string gameType = "Boxed";

			Product ticketPick3StraightProd = lotteries.GetOrCreateProductById(1);
			ticketPick3StraightProd.Description = "Ticket pick 3 straight";
			ticketPick3StraightProd.Price = 1;

			Domain domain = company.Sales.CreateDomain(false, 1, "localhost", PaymentChannels.Agents.INSIDER);
			ExcludeSubtickets excludedSubtickets = new ExcludeSubtickets();
			Customer customer = company.CustomerByPlayer(player);
			customer.Identifier = "*********";

			NextDatesAccumulator nextDatesAccumulator = new NextDatesAccumulator(lotteries, domain, now, pickNumber, dates, states, hours, strIsPresentPlayerBeforeToCloseStore, useNextDates);
			int orderNumber = 1;
			OrderCart myOrder = (OrderCart)company.NewOrder(customer, orderNumber, GamesEngine.Finance.Currencies.CODES.FP);
			Order order = company.CreateTicketOrder(lotteries, player, states, dates, withFireBalls, numbers, selectionMode, ticketAmount, includedNumbersForInput, excludedSubtickets, gameType, myOrder, ticketPick3StraightProd, nextDatesAccumulator, domain);
			company.AddOrders(myOrder);

			int lowestBetId = 1;
			int highestBetId = 1;
			int ticketNumber = *********;
			var marketplace = new Marketplace(company, "CR");
			var agent = marketplace.AddAgent("1");
			myOrder.Agent = (GamesEngine.Exchange.Agent)agent;
			company.PurchaseTickets(itIsThePresent, myOrder, now, ticketNumber, domain, lowestBetId, highestBetId, orderNumber);
			lotteries.CreateWagers(itIsThePresent, lowestBetId, highestBetId, 1, 2, ticketNumber, orderNumber, now);
			#endregion

			#region Grade Draw
			now = new DateTime(2019, 04, 17, 10, 30, 00);
            bool notifyEvent = false;
            var report = lottery.DrawPicks(drawDate, "340", now, Integration.UseKafka, "Bart", notifyEvent);
			Assert.AreEqual(0m, report.Profit);
			Assert.AreEqual(1, report.TotalPlayers);
			Assert.AreEqual(0, report.TotalPrize);
			Assert.AreEqual(1m, report.TotalTicketAmount);
			Assert.AreEqual(2, report.TotalTickets);
			Assert.AreEqual(0, report.TotalWinners);

			int amountOfMessages = queue.Count(Integration.Kafka.TopicForFragmentPaymentsForAll);
			string message1 = queue.Dequeue(Integration.Kafka.TopicForFragmentPaymentsForAll);
			string expectedMessage = "0\uFFFBBart\uFFFB1\uFFFB0\uFFFBFalse\uFFFB*********\uFFFA*********\uFFFA1\uFFFAL\uFFFA2019\uFFFA4\uFFFA17\uFFFA0\uFFFA0\uFFFAFP\uFFFA0\uFFFB*********\uFFFA*********\uFFFA2\uFFFAL\uFFFA2019\uFFFA4\uFFFA17\uFFFA0\uFFFA0\uFFFAFP\uFFFA0";

			#endregion

			#region Send to Kafka with normal Buffer
			Action<KafkaMessages> beforeSend = (KafkaMessages kafkaMessages) =>
			{
				kafkaMessages.Add(employeeName);
				kafkaMessages.Add(company.Sales.CurrentStore.Id);
			};

			using (KafkaMessagesBuffer bufferForAll = new KafkaMessagesBuffer(Integration.UseKafka, Integration.Kafka.TopicForFragmentPaymentsForAll))
			{
				bufferForAll.BeforeSendTheFirstMessage = beforeSend;

				FragmentPaymentMessage gradedWagerMessage;
				foreach (var ticket in lottery.TicketsOfDrawAt(drawDate))
				{
					switch (ticket.Prizing)
					{
						case GameboardStatus.LOSER:
							foreach (var wager in ticket.Wagers)
							{
								gradedWagerMessage = wager.FragmentPaymentMessage(now);

								bufferForAll.Send(gradedWagerMessage);
							}
							break;
						case GameboardStatus.WINNER:
							foreach (var wager in ticket.Wagers)
							{
								var isWinnerWager = wager.IsWinner();
								if (isWinnerWager)
								{
									continue;
								}
								else
								{
									gradedWagerMessage = wager.FragmentPaymentMessage(now);
									bufferForAll.Send(gradedWagerMessage);
								}
							}
							break;
						default:
							throw new GameEngineException("At this point ticket must be winner or loser");
					}
				}
			}
			#endregion


			#region Send to kafka with compressed version

			using (FragmentPaymentCompressor bufferForAll = new FragmentPaymentCompressor(Integration.UseKafka, Integration.Kafka.TopicForFragmentPaymentsForAll))
			{
				bufferForAll.BeforeSendTheFirstMessage = bufferForAll.GenerateHeader(employeeName, company.Sales.CurrentStore.Id, WholePaymentProcessor.NoPaymentProcessor, false, now);

				FragmentPaymentMessage gradedWagerMessage;
				foreach (var ticket in lottery.TicketsOfDrawAt(drawDate))
				{
					switch (ticket.Prizing)
					{
						case GameboardStatus.LOSER:
							foreach (var wager in ticket.Wagers)
							{
								gradedWagerMessage = wager.FragmentPaymentMessage(now);
								bufferForAll.Send(gradedWagerMessage);
							}
							break;
						case GameboardStatus.WINNER:
							foreach (var wager in ticket.Wagers)
							{
								var isWinnerWager = wager.IsWinner();
								if (isWinnerWager)
								{
									continue;
								}
								else
								{
									gradedWagerMessage = wager.FragmentPaymentMessage(now);
									bufferForAll.Send(gradedWagerMessage);
								}
							}
							break;
						default:
							throw new GameEngineException("At this point ticket must be winner or loser");
					}
				}
			}
			#endregion

			amountOfMessages = queue.Count(Integration.Kafka.TopicForFragmentPaymentsForAll);
			message1 = queue.Dequeue(Integration.Kafka.TopicForFragmentPaymentsForAll);
			string message2 = queue.Dequeue(Integration.Kafka.TopicForFragmentPaymentsForAll);

			string expandedMessage2 = FragmentPaymentCompressor.FragmentSplit(message2);

			Assert.AreEqual(expectedMessage, expandedMessage2);

			Integration.RestoreToDefaultSettings();
		}

		[TestMethod]
		public void PurchaseSeveralTN()
		{
			//Integration.UseKafka = true;
			Integration.SavePreviousSettings(tempUseKafka: true, tempUseKafkaForAuto: true);
			QueueInMemory queue = new QueueInMemory();
			Integration.Kafka = queue;
			bool itIsThePresent = true;
			CurrenciesTest.AddCurrencies();

			#region Create 2 purchases

			string employeeName = "Bart";
			Company company = new Company();
			company.Sales.CreateStore(1, "Test Store").MakeCurrent();
			company.Accounting = new MockAccounting();
			var lotteries = company.Lotto900();
			DateTime now = new DateTime(2019, 04, 17, 9, 00, 00);
			State state = lotteries.GetOrCreateState("VA", "Virginia", now, employeeName);
			LotteryPick<Pick3> lottery = (LotteryPick<Pick3>)lotteries.GetOrCreateLottery(3, state);
			for (int i = 0; i <= 6; i++) lottery.Every(10, 20, i, now, employeeName);
			var drawDate = new DateTime(2019, 04, 17, 10, 20, 00);
			var schedule = lottery.GetScheduleId(1);
			schedule.Update(true, "VA Morning", "0123456", 10, 20, now, "Game Admin");

			#region player declaration
			string account1 = "*********";
			Player player1 = company.GetOrCreateCustomerById(account1).Player;
			string account2 = "*********";
			Player player2 = company.GetOrCreateCustomerById(account1).Player;
			#endregion

			string states = "VA";
			string hours = "10:20 AM";
			string strIsPresentPlayerBeforeToCloseStore = "true";
			string useNextDates = "true";
			string dates = "04/17/2019";
			string withFireBalls = "false";
			string numbers = "3,4,23";
			string selectionMode = "MultipleInputSingleAmount";
			string pickNumber = "3";
			decimal ticketAmount = 0.5m;
			string gameType = "Boxed";

			#region randomize selected numbers
			StringBuilder includedNumbersForInput1 = new StringBuilder();
			StringBuilder includedNumbersForInput2 = new StringBuilder();
			StringBuilder includedNumbersForInput3 = new StringBuilder();
			for (int i = 200; i < 500; i++)
			{
				includedNumbersForInput1.Append($"{i},");
			}
			includedNumbersForInput1.Length--;
			for (int i = 400; i < 700; i++)
			{
				includedNumbersForInput2.Append($"{i},");
			}
			includedNumbersForInput2.Length--;
			for (int i = 700; i < 1000; i++)
			{
				includedNumbersForInput3.Append($"{i},");
			}
			includedNumbersForInput3.Length--;

			#endregion

			Product ticketPick3StraightProd = lotteries.GetOrCreateProductById(1);
			ticketPick3StraightProd.Description = "Ticket pick 3 straight";
			ticketPick3StraightProd.Price = 1;

			#region purchases

			Domain domain = company.Sales.CreateDomain(false, 1, "localhost", PaymentChannels.Agents.INSIDER);
			ExcludeSubtickets excludedSubtickets1 = new ExcludeSubtickets();
			Customer customer1 = company.CustomerByPlayer(player1);
			customer1.Identifier = "*********";

			NextDatesAccumulator nextDatesAccumulator1 = new NextDatesAccumulator(lotteries, domain, now, pickNumber, dates, states, hours, strIsPresentPlayerBeforeToCloseStore, useNextDates);
			int orderNumber1 = 1;
			OrderCart myOrder1 = (OrderCart)company.NewOrder(customer1, orderNumber1, GamesEngine.Finance.Currencies.CODES.FP);
			Order order1 = company.CreateTicketOrder(lotteries, player1, states, dates, withFireBalls, numbers, selectionMode, ticketAmount, includedNumbersForInput1.ToString(), excludedSubtickets1, gameType, myOrder1, ticketPick3StraightProd, nextDatesAccumulator1, domain);
			company.AddOrders(myOrder1);

			ExcludeSubtickets excludedSubtickets2 = new ExcludeSubtickets();
			Customer customer2 = company.CustomerByPlayer(player2);

			NextDatesAccumulator nextDatesAccumulator2 = new NextDatesAccumulator(lotteries, domain, now, pickNumber, dates, states, hours, strIsPresentPlayerBeforeToCloseStore, useNextDates);
			int orderNumber2 = 2;
			OrderCart myOrder2 = (OrderCart)company.NewOrder(customer2, orderNumber2, GamesEngine.Finance.Currencies.CODES.FP);
			Order order2 = company.CreateTicketOrder(lotteries, player2, states, dates, withFireBalls, numbers, selectionMode, ticketAmount, includedNumbersForInput2.ToString(), excludedSubtickets2, gameType, myOrder2, ticketPick3StraightProd, nextDatesAccumulator2, domain);
			company.AddOrders(myOrder2);

			ExcludeSubtickets excludedSubtickets3 = new ExcludeSubtickets();
			Customer customer3 = company.CustomerByPlayer(player1);

			NextDatesAccumulator nextDatesAccumulator3 = new NextDatesAccumulator(lotteries, domain, now, pickNumber, dates, states, hours, strIsPresentPlayerBeforeToCloseStore, useNextDates);
			int orderNumber3 = 3;
			OrderCart myOrder3 = (OrderCart)company.NewOrder(customer3, orderNumber3, GamesEngine.Finance.Currencies.CODES.FP);
			Order order3 = company.CreateTicketOrder(lotteries, player1, states, dates, withFireBalls, numbers, selectionMode, ticketAmount, includedNumbersForInput3.ToString(), excludedSubtickets3, gameType, myOrder3, ticketPick3StraightProd, nextDatesAccumulator3, domain);
			company.AddOrders(myOrder3);

			int lowestBetId1 = 1;
			int highestBetId1 = 1;
			int ticketNumber1 = *********;
			var marketplace = new Marketplace(company, "CR");
			var agent = marketplace.AddAgent("1");
			myOrder1.Agent = (GamesEngine.Exchange.Agent)agent;
			company.PurchaseTickets(itIsThePresent, myOrder1, now, ticketNumber1, domain, lowestBetId1, highestBetId1, orderNumber1);
			lotteries.CreateWagers(itIsThePresent, lowestBetId1, highestBetId1, 1, 300, ticketNumber1, orderNumber1, now);

			int lowestBetId2 = 2;
			int highestBetId2 = 2;
			int ticketNumber2 = *********;
			myOrder2.Agent = (GamesEngine.Exchange.Agent)agent;
			company.PurchaseTickets(itIsThePresent, myOrder2, now, ticketNumber2, domain, lowestBetId2, highestBetId2, orderNumber2);
			lotteries.CreateWagers(itIsThePresent, lowestBetId2, highestBetId2, 1, 300, ticketNumber2, orderNumber2, now);

			int lowestBetId3 = 3;
			int highestBetId3 = 3;
			int ticketNumber3 = *********;
			myOrder3.Agent = (GamesEngine.Exchange.Agent)agent;
			company.PurchaseTickets(itIsThePresent, myOrder3, now, ticketNumber3, domain, lowestBetId3, highestBetId3, orderNumber3);
			lotteries.CreateWagers(itIsThePresent, lowestBetId3, highestBetId3, 1, 300, ticketNumber3, orderNumber3, now);

			#endregion


			#endregion

			#region Grade Draw
			now = new DateTime(2019, 04, 17, 10, 30, 00);
            bool notifyEvent = false;
            var report = lottery.DrawPicks(drawDate, "340", now, Integration.UseKafka, "Bart", notifyEvent);
			lottery.ConfirmDraw(drawDate, now, "Bart", itIsThePresent);
			#endregion

			List<FragmentPaymentMessages> paymentMessages = new List<FragmentPaymentMessages>();
			while (queue.Count(Integration.Kafka.TopicForFragmentPaymentsForAll) > 0)
			{
				string comp = queue.Dequeue(Integration.Kafka.TopicForFragmentPaymentsForAll);

				#region FragmentPaymentsConsumer Body
				string msg = FragmentPaymentCompressor.FragmentSplit(comp);
				var gradeFreeFormWagers = new List<PayFragmentsMessage>();
				FragmentPaymentMessages payFragments = new FragmentPaymentMessages(msg);
				paymentMessages.Add(payFragments);

				#endregion
			}
			int totalAmountOfFragment = 0;
			foreach (FragmentPaymentMessages messages in paymentMessages)
			{
				foreach (var currency in messages.Currencies())
				{
					totalAmountOfFragment += messages.MessagesBy(currency).Count;
				}
			}

			Assert.AreEqual(300, totalAmountOfFragment);

			paymentMessages.Clear();
			while (queue.Count(Integration.Kafka.TopicForFragmentPaymentsForWinners) > 0)
			{
				string comp = queue.Dequeue(Integration.Kafka.TopicForFragmentPaymentsForWinners);

				#region FragmentPaymentsConsumer Body
				string msg = FragmentPaymentCompressor.FragmentSplit(comp);
				var gradeFreeFormWagers = new List<PayFragmentsMessage>();
				FragmentPaymentMessages payFragments = new FragmentPaymentMessages(msg);
				paymentMessages.Add(payFragments);

				#endregion
			}

			totalAmountOfFragment = 0;
			foreach (FragmentPaymentMessages messages in paymentMessages)
			{
				foreach (var currency in messages.Currencies())
				{
					totalAmountOfFragment += messages.MessagesBy(currency).Count;
				}
			}

			Assert.AreEqual(600, totalAmountOfFragment);

			Integration.RestoreToDefaultSettings();
		}

		[TestMethod]
		public void SendingLotteryDrawMessages()
		{
			//Integration.UseKafka = true;
			Integration.SavePreviousSettings(tempUseKafka: true, tempUseKafkaForAuto: true);
			QueueInMemory queue = new QueueInMemory();
			Integration.Kafka = queue;
			bool itIsThePresent = true;
			CurrenciesTest.AddCurrencies();

			string employeeName = "Bart";
			Company company = new Company();
			company.Sales.CreateStore(1, "Test Store").MakeCurrent();
			company.Accounting = new MockAccounting();
			var lotteries = company.Lotto900();
			DateTime now = new DateTime(2019, 04, 17, 9, 00, 00);
			State state = lotteries.GetOrCreateState("VA", "Virginia", now, employeeName);
			LotteryPick<Pick3> lottery = (LotteryPick<Pick3>)lotteries.GetOrCreateLottery(3, state);
			for (int i = 0; i <= 6; i++) lottery.Every(10, 20, i, now, employeeName);
			var drawDate = new DateTime(2019, 04, 17, 10, 20, 00);

			string account1 = "*********";
			Player player1 = company.GetOrCreateCustomerById(account1).Player;

			string states = "VA";
			string hours = "10:20 AM";
			string strIsPresentPlayerBeforeToCloseStore = "true";
			string useNextDates = "true";
			string dates = "04/17/2019";
			string withFireBalls = "false";
			string numbers = "";
			string includedNumbersForInput1 = "000,001,002";
			string selectionMode = "MultipleInputSingleAmount";
			string pickNumber = "3";
			decimal ticketAmount = 0.75m;
			string gameType = "Boxed";

			Product ticketPick3StraightProd = lotteries.GetOrCreateProductById(1);
			ticketPick3StraightProd.Description = "Ticket pick 3 straight";
			ticketPick3StraightProd.Price = 1;

			Domain domain = company.Sales.CreateDomain(itIsThePresent, 1, "localhost", PaymentChannels.Agents.INSIDER);
			ExcludeSubtickets excludedSubtickets1 = new ExcludeSubtickets();
			Customer customer1 = company.CustomerByPlayer(player1);
			customer1.Identifier = "*********";

			NextDatesAccumulator nextDatesAccumulator1 = new NextDatesAccumulator(lotteries, domain, now, pickNumber, dates, states, hours, strIsPresentPlayerBeforeToCloseStore, useNextDates);
			int orderNumber1 = 1;
			OrderCart myOrder1 = (OrderCart)company.NewOrder(customer1, orderNumber1, "USD");
			Order order1 = company.CreateTicketOrder(lotteries, player1, states, dates, withFireBalls, numbers, selectionMode, ticketAmount, includedNumbersForInput1.ToString(), excludedSubtickets1, gameType, myOrder1, ticketPick3StraightProd, nextDatesAccumulator1, domain);
			company.AddOrders(myOrder1);

			int lowestBetId1 = 1;
			int highestBetId1 = 1;
			int ticketNumber1 = *********;
			var marketplace = new Marketplace(company, "CR");
			var agent = marketplace.AddAgent("1");
			myOrder1.Agent = (GamesEngine.Exchange.Agent)agent;
			company.PurchaseTickets(itIsThePresent, myOrder1, now, ticketNumber1, domain, lowestBetId1, highestBetId1, orderNumber1);
			lotteries.CreateWagers(itIsThePresent, lowestBetId1, highestBetId1, 1, 3, ticketNumber1, orderNumber1, now);

			now = new DateTime(2019, 04, 17, 10, 30, 00);
			lottery.DrawPicks(drawDate, "000", now, itIsThePresent, "Bart", true);

			int amountOfMessages = queue.Count($"{KafkaMessage.LOTTERY_DRAW_CONSUMER_PREFIX}{Integration.Kafka.TopicForAllGrades}");
			Assert.AreEqual(1, amountOfMessages);

			string msg = queue.Dequeue($"{KafkaMessage.LOTTERY_DRAW_CONSUMER_PREFIX}{Integration.Kafka.TopicForAllGrades}");
			var gradeMessage = new LotteryDrawGradeMessage(msg);
			Assert.AreEqual(drawDate, gradeMessage.DrawDate);
			Assert.AreEqual(LotteryDrawType.GRADE, gradeMessage.Type);

			lottery.ConfirmDraw(drawDate, now, "Bart", itIsThePresent, true);
			amountOfMessages = queue.Count($"{KafkaMessage.LOTTERY_DRAW_CONSUMER_PREFIX}{Integration.Kafka.TopicForAllGrades}");
			Assert.AreEqual(1, amountOfMessages);

			msg = queue.Dequeue($"{KafkaMessage.LOTTERY_DRAW_CONSUMER_PREFIX}{Integration.Kafka.TopicForAllGrades}");
			var confirmationMessage = new LotteryDrawConfirmationMessage(msg);
			Assert.AreEqual(drawDate, confirmationMessage.DrawDate);
			Assert.AreEqual(LotteryDrawType.CONFIRMATION, confirmationMessage.Type);

			lottery.Regrade(itIsThePresent, drawDate, now, "Bart", true);
			amountOfMessages = queue.Count($"{KafkaMessage.LOTTERY_DRAW_CONSUMER_PREFIX}{Integration.Kafka.TopicForAllGrades}");
			Assert.AreEqual(1, amountOfMessages);

			msg = queue.Dequeue($"{KafkaMessage.LOTTERY_DRAW_CONSUMER_PREFIX}{Integration.Kafka.TopicForAllGrades}");
			var regradeMessage = new LotteryDrawRegradeMessage(msg);
			Assert.AreEqual(drawDate, regradeMessage.DrawDate);
			Assert.AreEqual(LotteryDrawType.REGRADE, regradeMessage.Type);

			lottery.ConfirmDraw(drawDate, now, "Bart", itIsThePresent, true);
			amountOfMessages = queue.Count($"{KafkaMessage.LOTTERY_DRAW_CONSUMER_PREFIX}{Integration.Kafka.TopicForAllGrades}");
			Assert.AreEqual(1, amountOfMessages);

			msg = queue.Dequeue($"{KafkaMessage.LOTTERY_DRAW_CONSUMER_PREFIX}{Integration.Kafka.TopicForAllGrades}");
			confirmationMessage = new LotteryDrawConfirmationMessage(msg);
			Assert.AreEqual(drawDate, confirmationMessage.DrawDate);
			Assert.AreEqual(LotteryDrawType.CONFIRMATION, confirmationMessage.Type);

			lottery.SetNoAction(itIsThePresent, drawDate, now, "Bart", true);
			amountOfMessages = queue.Count($"{KafkaMessage.LOTTERY_DRAW_CONSUMER_PREFIX}{Integration.Kafka.TopicForAllGrades}");
			Assert.AreEqual(1, amountOfMessages);

			msg = queue.Dequeue($"{KafkaMessage.LOTTERY_DRAW_CONSUMER_PREFIX}{Integration.Kafka.TopicForAllGrades}");
			var noactionMessage = new LotteryDrawNoActionMessage(msg);
			Assert.AreEqual(drawDate, noactionMessage.DrawDate);
			Assert.AreEqual(LotteryDrawType.NOACTION, noactionMessage.Type);
		}
	}
}
