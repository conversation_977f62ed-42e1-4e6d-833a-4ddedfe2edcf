﻿using Connectors.town.connectors.driver.transactions;
using Connectors.town.connectors.drivers.consignment;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using town.connectors;
using static Connectors.town.connectors.drivers.consignment.DepositInfo;
using static Connectors.town.connectors.drivers.consignment.PayoutDetail;
using static Connectors.town.connectors.drivers.consignment.PayoutInfo;
using static Connectors.town.connectors.drivers.consignment.Senders;
using static Connectors.town.connectors.drivers.consignment.TodayDeposits;
using static Connectors.town.connectors.drivers.consignment.TodayPayouts;
using static town.connectors.CustomSettings;
using static town.connectors.CustomSettings.CustomSetting;

namespace GamesEngineTests.Unit_Tests.Connectors
{
	[TestClass]
	public class ConsignmentTests
	{
		[TestMethod]
		public async Task DepositAsync()
		{
			Variables variables = new Variables();
			DateTime now = DateTime.Now;
			CustomSettings cs = new CustomSettings(variables);
			cs.AddFixedParameter(now, "CompanyBaseUrlServices", "http://multipaymentsonline.net/api1.3/payments.asmx");
			cs.AddFixedParameter(now, "CompanySystemId", "test");
			cs.AddFixedParameter(now, "CompanySystemPassword", new Secret("test123"));

			DepositTransaction result;
			Deposit driver = new Deposit();
			driver.ConfigureThenPrepare(now, cs);
			using (RecordSet recordSet = driver.CustomSettings.GetRecordSet())
			{
				recordSet.SetParameter("merchId", "999");
				recordSet.SetParameter("sendersName", "Test1");
				recordSet.SetParameter("country", "CR");
				recordSet.SetParameter("state", "SJ");
				recordSet.SetParameter("city", "SJ");
				recordSet.SetParameter("controlNum", "1110");
				recordSet.SetParameter("amount", "0.5");
				recordSet.SetParameter("readyForProcessing", "true");
				recordSet.SetParameter("Input$Provider", 1);
				result = await driver.ExecuteAsync<DepositTransaction>(now, recordSet);
			}
		}

		[TestMethod]
		public async Task DepositUpdateAsync()
		{
			Variables variables = new Variables();
			DateTime now = DateTime.Now;
			CustomSettings cs = new CustomSettings(variables);
			cs.AddFixedParameter(now, "CompanyBaseUrlServices", "http://multipaymentsonline.net/api1.3/payments.asmx");
			cs.AddFixedParameter(now, "CompanySystemId", "test");
			cs.AddFixedParameter(now, "CompanySystemPassword", new Secret("test123"));

			DepositTransaction result;
			var driver = new DepositUpdate();
			driver.ConfigureThenPrepare(now, cs);
			using (RecordSet recordSet = driver.CustomSettings.GetRecordSet())
			{
				recordSet.SetParameter("depositId", "999");
				recordSet.SetParameter("sendersName", "Test1");
				recordSet.SetParameter("country", "CR");
				recordSet.SetParameter("state", "SJ");
				recordSet.SetParameter("city", "SJ");
				recordSet.SetParameter("controlNum", "1110");
				result = await driver.ExecuteAsync<DepositTransaction>(now, recordSet);
			}
		}

		[TestMethod]
		public async Task PayoutAsync()
		{
			Variables variables = new Variables();
			DateTime now = DateTime.Now;
			CustomSettings cs = new CustomSettings(variables);
			cs.AddFixedParameter(now, "CompanyBaseUrlServices", "http://multipaymentsonline.net/api1.3/payments.asmx");
			cs.AddFixedParameter(now, "CompanySystemId", "test");
			cs.AddFixedParameter(now, "CompanySystemPassword", new Secret("test123"));
			cs.AddFixedParameter(now, "KYCUsername", new Secret("test123"));
			cs.AddFixedParameter(now, "KYCPassword", new Secret("test123"));

			WithdrawalTransaction result;
			var driver = new Payout();
			driver.ConfigureThenPrepare(now, cs);
			using (RecordSet recordSet = driver.CustomSettings.GetRecordSet())
			{
				recordSet.SetParameter("identificationNumber", "333");
				recordSet.SetParameter("merchId", "333");
				recordSet.SetParameter("receiversName", "Juana");
				recordSet.SetParameter("country", "CR");
				recordSet.SetParameter("state", "SJ");
				recordSet.SetParameter("city", "SJ");
				recordSet.SetParameter("amount", "0.5");
				recordSet.SetParameter("sendersAddress", "Juan");
				recordSet.SetParameter("readyForProcessing", "true");
				result = await driver.ExecuteAsync<WithdrawalTransaction>(now, recordSet);
			}
		}

		[TestMethod]
		public async Task PayoutUpdateAsync()
		{
			Variables variables = new Variables();
			DateTime now = DateTime.Now;
			CustomSettings cs = new CustomSettings(variables);
			cs.AddFixedParameter(now, "CompanyBaseUrlServices", "http://multipaymentsonline.net/api1.3/payments.asmx");
			cs.AddFixedParameter(now, "CompanySystemId", "test");
			cs.AddFixedParameter(now, "CompanySystemPassword", new Secret("test123"));

			WithdrawalTransaction result;
			var driver = new PayoutUpdate();
			driver.ConfigureThenPrepare(now, cs);
			using (RecordSet recordSet = driver.CustomSettings.GetRecordSet())
			{
				recordSet.SetParameter("merchId", "333");
				recordSet.SetParameter("payoutId", "120545");
				recordSet.SetParameter("receiversName", "Juana");
				recordSet.SetParameter("country", "CR");
				recordSet.SetParameter("state", "SJ");
				recordSet.SetParameter("city", "SJ");
				recordSet.SetParameter("amount", "0.5");
				recordSet.SetParameter("sendersAddress", "Juan");
				result = await driver.ExecuteAsync<WithdrawalTransaction>(now, recordSet);
			}
		}

		[TestMethod]
		public async Task TodayPayoutsAsync()
		{
			Variables variables = new Variables();
			DateTime now = DateTime.Now;
			CustomSettings cs = new CustomSettings(variables);
			cs.AddFixedParameter(now, "CompanyBaseUrlServices", "http://multipaymentsonline.net/api1.3/payments.asmx");
			cs.AddFixedParameter(now, "CompanySystemId", "test");
			cs.AddFixedParameter(now, "CompanySystemPassword", new Secret("test123"));

			PayoutsResponse result;
			var driver = new TodayPayouts();
			driver.ConfigureThenPrepare(now, cs);
			using (RecordSet recordSet = driver.CustomSettings.GetRecordSet())
			{
				recordSet.SetParameter("status", "1");
				result = await driver.ExecuteAsync<PayoutsResponse>(now, recordSet);
			}
		}

		[TestMethod]
		public async Task TodayDepositsAsync()
		{
			Variables variables = new Variables();
			DateTime now = DateTime.Now;
			CustomSettings cs = new CustomSettings(variables);
			cs.AddFixedParameter(now, "CompanyBaseUrlServices", "http://multipaymentsonline.net/api1.3/payments.asmx");
			cs.AddFixedParameter(now, "CompanySystemId", "test");
			cs.AddFixedParameter(now, "CompanySystemPassword", new Secret("test123"));

			DepositsResponse result;
			var driver = new TodayDeposits();
			driver.ConfigureThenPrepare(now, cs);
			using (RecordSet recordSet = driver.CustomSettings.GetRecordSet())
			{
				recordSet.SetParameter("status", "3");
				result = await driver.ExecuteAsync<DepositsResponse>(now, recordSet);
			}
		}

		[TestMethod]
		public async Task DepositInfoAsync()
		{
			Variables variables = new Variables();
			DateTime now = DateTime.Now;
			CustomSettings cs = new CustomSettings(variables);
			cs.AddFixedParameter(now, "CompanyBaseUrlServices", "http://multipaymentsonline.net/api1.3/payments.asmx");
			cs.AddFixedParameter(now, "CompanySystemId", "test");
			cs.AddFixedParameter(now, "CompanySystemPassword", new Secret("test123"));

			DepositInfoResponse result;
			var driver = new DepositInfo();
			driver.ConfigureThenPrepare(now, cs);
			using (RecordSet recordSet = driver.CustomSettings.GetRecordSet())
			{
				recordSet.SetParameter("id", "782875");
				result = await driver.ExecuteAsync<DepositInfoResponse>(now, recordSet);
			}
		}

		[TestMethod]
		public async Task PayoutInfoInfoAsync()
		{
			Variables variables = new Variables();
			DateTime now = DateTime.Now;
			CustomSettings cs = new CustomSettings(variables);
			cs.AddFixedParameter(now, "CompanyBaseUrlServices", "http://multipaymentsonline.net/api1.3/payments.asmx");
			cs.AddFixedParameter(now, "CompanySystemId", "test");
			cs.AddFixedParameter(now, "CompanySystemPassword", new Secret("test123"));

			PayoutInfoResponse result;
			var driver = new PayoutInfo();
			driver.ConfigureThenPrepare(now, cs);
			using (RecordSet recordSet = driver.CustomSettings.GetRecordSet())
			{
				recordSet.SetParameter("id", "120492");
				result = await driver.ExecuteAsync<PayoutInfoResponse>(now, recordSet);
			}
		}

		[TestMethod]
		public async Task PayoutDetailInfoAsync()
		{
			Variables variables = new Variables();
			DateTime now = DateTime.Now;
			CustomSettings cs = new CustomSettings(variables);
			cs.AddFixedParameter(now, "CompanyBaseUrlServices", "http://multipaymentsonline.net/api1.3/payments.asmx");
			cs.AddFixedParameter(now, "CompanySystemId", "test");
			cs.AddFixedParameter(now, "CompanySystemPassword", new Secret("test123"));

			PayoutsDetailResponse result;
			var driver = new PayoutDetail();
			driver.ConfigureThenPrepare(now, cs);
			using (RecordSet recordSet = driver.CustomSettings.GetRecordSet())
			{
				recordSet.SetParameter("id", "120492");
				result = await driver.ExecuteAsync<PayoutsDetailResponse>(now, recordSet);
			}
		}

		[TestMethod]
		public async Task SendersAsync()
		{
			Variables variables = new Variables();
			DateTime now = DateTime.Now;
			CustomSettings cs = new CustomSettings(variables);
			cs.AddFixedParameter(now, "CompanyBaseUrlServices", "http://multipaymentsonline.net/api1.3/payments.asmx");
			cs.AddFixedParameter(now, "CompanySystemId", "test");
			cs.AddFixedParameter(now, "CompanySystemPassword", new Secret("test123"));

			SendersResponse result;
			var driver = new Senders();
			driver.ConfigureThenPrepare(now, cs);
			using (RecordSet recordSet = driver.CustomSettings.GetRecordSet())
			{
				recordSet.SetParameter("provider", "1");
				result = await driver.ExecuteAsync<SendersResponse>(now, recordSet);
			}
		}

		[TestMethod]
		public async Task NameAsync()
		{
			Variables variables = new Variables();
			DateTime now = DateTime.Now;
			CustomSettings cs = new CustomSettings(variables);
			cs.AddFixedParameter(now, "CompanyBaseUrlServices", "http://multipaymentsonline.net/api1.3/payments.asmx");
			cs.AddFixedParameter(now, "CompanySystemId", "test");
			cs.AddFixedParameter(now, "CompanySystemPassword", new Secret("test123"));

			string result;
			var driver = new Name();
			driver.ConfigureThenPrepare(now, cs);
			using (RecordSet recordSet = driver.CustomSettings.GetRecordSet())
			{
				recordSet.SetParameter("amount", 1m);
				recordSet.SetParameter("merchantId", "999");
				recordSet.SetParameter("sendersName", "Test1");
				recordSet.SetParameter("reference", "ref-1");
				recordSet.SetParameter("provider", 1);
				result = await driver.ExecuteAsync<string>(now, recordSet);
			}
		}

		[TestMethod]
		public async Task CommittedNamesAsync()
		{
			Variables variables = new Variables();
			DateTime now = DateTime.Now;
			CustomSettings cs = new CustomSettings(variables);
			cs.AddFixedParameter(now, "CompanyBaseUrlServices", "http://multipaymentsonline.net/api1.3/payments.asmx");
			cs.AddFixedParameter(now, "CompanySystemId", "test");
			cs.AddFixedParameter(now, "CompanySystemPassword", new Secret("test123"));

			string result;
			var driver = new CommittedNames();
			driver.ConfigureThenPrepare(now, cs);
			using (RecordSet recordSet = driver.CustomSettings.GetRecordSet())
			{
				result = await driver.ExecuteAsync<string>(now, recordSet);
			}
		}
	}
}
