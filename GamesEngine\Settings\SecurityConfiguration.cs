﻿using GamesEngine.Exchange;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.IdentityModel.Tokens.Jwt;
using System.Runtime.Serialization;
using System.Security.Claims;
using System.Text;
using System.Threading.Tasks;
using static GamesEngine.Exchange.PaymentProcessorsAndActionsByDomains;
using static GamesEngine.Finance.PaymentChannels;

namespace GamesEngine.Settings
{
    public abstract class SecurityConfiguration
    {
        private static SecuritySection clerks;
        private static SecuritySection players;
        private static SecuritySection mm;
        private static SecuritySection exchangeFiero;
        private static SecuritySection exchangeOwner;
        private static SecuritySection exchangeAgents;
        private static SecuritySection linesApp;
        protected static SecurityStates itsSecuritySchemeConfigured = SecurityStates.None;
        public enum SecurityStates
        {
            None,
            Enable,
            Disable
        };
        private enum CUSTOM_FIELDS { AGENT = 0, PLAYERID = 1 };
        private static string FakePlayerId = "LM0Mz6pghATLiFYeIfXmeYwD2BdIb1ZxfcJcdR0ZbiagbGtLPW6l2yjHVa0gGxLl";
        private static string FakeTenantId = "2";
        private static string FakeTenantName = "Artemis";
        private static string FakePath = "CR/1";
        private static string FakeUsername = "ccajeroqa";
        private static string FakeEmail = "<EMAIL>";
        private static string FakeStoreIds = "1,5,6,7";
        public const string FakeToken = "-";

        internal static Agents Agent(ClaimsPrincipal user)
        {
            if (ItsSecuritySchemeConfigured())
            {
                var agent = user.FindFirst(CUSTOM_FIELDS.AGENT.ToString())?.Value;
                return (Agents)Enum.Parse(typeof(Agents), agent);
            }
            else
            {
                return Agents.INSIDER;
            }
        }

        public static string PlayerId(HttpContext context)
        {
            if (!ItsSecuritySchemeConfigured())
            {
                //TODO descomentar if DEBUG cuando esté listo security
                //#if DEBUG
                //               return fakePlayerId;
                //#else
                //		throw new GameEngineException($@"Security must be on");
                //#endif
                return FakePlayerId;
            }
            UserInformation userInfo = GetUserInformation(context);
            return userInfo.PlayerId;
        }

        public static string PlayerId(ClaimsPrincipal user)
        {
            var claim = user.FindFirst(CUSTOM_FIELDS.PLAYERID.ToString());
            string playerId = claim == null ? FakePlayerId : claim.Value;
            return playerId;
        }

        public static string UserName(HttpContext context)
        {
            if (!ItsSecuritySchemeConfigured())
            {
                return FakeUsername;
            }
            UserInformation userInfo = GetUserInformation(context);
           
            return userInfo.UserName;
        }

        public static string UserPath(HttpContext context)
        {
            if (!ItsSecuritySchemeConfigured())
            {
                return FakePath;
            }
            UserInformation userInfo = GetUserInformation(context);

            return userInfo.UserPath;
        }

        public static string Email(HttpContext context)
        {
            if (!ItsSecuritySchemeConfigured())
            {
                return FakeEmail;
            }
            UserInformation userInfo = GetUserInformation(context);

            return userInfo.Email;
        }

        public static UserInformation GetUserInformation(HttpContext context)
        {
            UserInformation userInformation;
            if (!ItsSecuritySchemeConfigured())
            {
                userInformation = new UserInformation()
                {
                    Name = "Bart Simpson",
                    UserName = FakeUsername,
                    Email = FakeEmail,
                    Roles = new string[] { },
                    UserPath = FakePath,
                    PlayerId = FakePlayerId,
                    TenantId = FakeTenantId,
                    TenantName = FakeTenantName,
                    StoreIds = FakeStoreIds
                };
            }
            else
            {
                try
                {
                    string header = context.Request.Headers["Authorization"];
                    string[] tokens = header.Split(' ');

                    var jwthandler = new JwtSecurityTokenHandler();
                    JwtSecurityToken jwttoken = jwthandler.ReadToken(tokens[1]) as JwtSecurityToken;
                    JwtCustom jwt = JsonConvert.DeserializeObject<JwtCustom>(jwttoken.Payload.SerializeToJson());
                    userInformation = new UserInformation()
                    {
                        Name = jwt.name,
                        UserName = jwt.preferred_username,
                        Email = jwt.email,
                        Roles = jwt.realm_access.roles,
                        UserPath = jwt.path,
                        PlayerId = jwt.playerid,
                        TenantId = jwt.tenantid,
                        TenantName = jwt.tenantname,
                        StoreIds = jwt.storeids,
                        AffiliateId = jwt.affiliateId,
                        AffiliateName = jwt.affiliateName
                    };
                }
                catch
                {
                    userInformation = new UserInformation()
                    {
                        Name = "",
                        UserName = "",
                        Roles = new string[] { },
                        UserPath = "",
                        PlayerId = ""
                    };
                }
            }
            
            return userInformation;
        }

        public static User GetFakeUser()
        {
            var user = new User()
            {
                Attributes = new Attributes()
                {
                    Agent = new[] { "" },
                    Domains = new[] { "1" },
                    Path = new[] { "CR/1" },
                    ProfileId = new[] { "1" },
                    TenantId = new[] { "" },
                    UserTypeName = new[] { "Cashier" }
                },
                Email = "bsimpson@a.a",
                Enabled = true,
                FirstName = "Bart",
                Id = "1",
                LastName = "Simpson",
                Username = "bsimpson"
            };
            return user;
        }

        protected static void Configure(IConfigurationSection settings)
        {
            itsSecuritySchemeConfigured = Convert.ToBoolean(settings["ForceAuthority"]) ? SecurityStates.Enable : SecurityStates.Disable;
            if (itsSecuritySchemeConfigured == SecurityStates.Enable)
            {
                ConfigureClerks(settings);
                ConfigurePlayers(settings);
            }
        }

        protected static void ConfigureClerks(IConfigurationSection settings)
        {
            var securitySettings = settings.GetSection("Clerks");
            if (string.IsNullOrEmpty(securitySettings["Api"])) throw new Exception("Add Api to the app settings.");
            if (string.IsNullOrEmpty(securitySettings["Authority"])) throw new Exception("Add Authority to the app settings.");
            if (securitySettings.GetSection("Clients") == null) throw new Exception("Add Clients to the app settings.");

            var clients = securitySettings.GetSection("Clients").Get<List<SecurityClient>>();
            SecurityConfiguration.clerks = new LottoSecuritySection(
                securitySettings["Api"],
                securitySettings["Authority"],
                clients
            );
        }

        protected static void ConfigurePlayers(IConfigurationSection settings)
        {
            var securitySettings = settings.GetSection("Players");
            if (string.IsNullOrEmpty(securitySettings["Api"])) throw new Exception("Add Api to the app settings.");
            if (string.IsNullOrEmpty(securitySettings["Authority"])) throw new Exception("Add Authority to the app settings.");
            if (securitySettings.GetSection("Clients") == null) throw new Exception("Add Clients to the app settings.");

            var clients = securitySettings.GetSection("Clients").Get<List<SecurityClient>>();
            SecurityConfiguration.players = new LottoSecuritySection(
                securitySettings["Api"],
                securitySettings["Authority"],
                clients
            );
        }

        public static bool ItsSecuritySchemeConfigured()
        {
            if (itsSecuritySchemeConfigured == SecurityStates.None) throw new Exception("Configure the ForceAuthority option in the app setting and call Security.Configure method before this method.");

            return itsSecuritySchemeConfigured == SecurityStates.Enable;
        }
        public static SecuritySection ClerksAPI()
        {
            if (SecurityConfiguration.clerks == null) throw new Exception("ClerksAPI is not configured. Use Configure method.");

            return SecurityConfiguration.clerks;
        }

        public static SecuritySection PlayersAPI()
        {
            if (SecurityConfiguration.players == null) throw new Exception("PlayersAPI is not configured. Use Configure method.");

            return SecurityConfiguration.players;
        }
    }

    public class SecurityClient
    {
        public string Id { get; set; }
        public string Name { get; set; }
        public string DefaultUser { get; set; }
        public string DefaultPassword { get; set; }
    }


    public abstract class SecuritySection
    {
        public string Api { get; set; }
        private string Authority { get; set; }
        readonly List<SecurityClient> clients;

        public SecuritySection(string api, string authority, List<SecurityClient> clients)
        {
            if (string.IsNullOrEmpty(api)) throw new Exception("Api url is required.");
            if (string.IsNullOrEmpty(authority)) throw new Exception("Authority url is required.");
            if (clients == null || clients.Count == 0) throw new Exception("Clients are required.");

            Api = api;
            Authority = authority;
            this.clients = clients;
        }

        SecurityClient GetClient(string clientName)
        {
            foreach (SecurityClient client in clients)
            {
                if (client.Name.Equals(clientName, StringComparison.OrdinalIgnoreCase)) return client;
            }
            throw new Exception($"Client {clientName} not found in {Api}");
        }

        public bool IsDefaultUser(string clientName, string userName)
        {
            SecurityClient client = GetClient(clientName);
            return client.DefaultUser.Equals(userName, StringComparison.OrdinalIgnoreCase);
        }

        public bool ExistsClient(string clientName)
        {
            foreach (SecurityClient client in clients)
            {
                if (client.Name.Equals(clientName, StringComparison.OrdinalIgnoreCase)) return true;
            }
            return false;
        }

        public async Task<Token> GetActorAdminTokenAsync(HttpRestClientConfiguration restClient, string clientName)
        {
            SecurityClient client = GetClient(clientName);
            IActionResult result = await restClient.SubmitAsync(Authority + "/protocol/openid-connect/token", $@"grant_type=password&username={client.DefaultUser}&password={client.DefaultPassword}&client_id={client.Name}");
            if (result is OkObjectResult)
            {
                string jsonResult = (result as ObjectResult).Value.ToString();
                Token tr = JsonConvert.DeserializeObject<Token>(jsonResult);
                return tr;
            }
            else
            {
                throw new Exception($@"It's not possible to retrive a token for user {client.DefaultUser} in {Api}");
            }
        }

        public async Task<IActionResult> DeleteMapRoleToUsersAsync(string clientName, string userID, RoleMapping[] roleMapping)
        {
            HttpRestClientConfiguration restClient = HttpRestClientConfiguration.GetInstance();
            SecurityClient client = GetClient(clientName);
            Token tr = await GetActorAdminTokenAsync(restClient, clientName);
            return await restClient.DeleteAsync(tr, $"{Api}/users/{userID}/role-mappings/clients/{client.Id}", JsonConvert.SerializeObject(roleMapping));
        }

        public async Task<IActionResult> DeleteUserAsync(string clientName, string userID)
        {
            HttpRestClientConfiguration client = HttpRestClientConfiguration.GetInstance();
            Token tr = await GetActorAdminTokenAsync(client, clientName);
            return await client.DeleteAsync(tr, Api + "/users/" + userID, "");
        }

        public async Task<IActionResult> LogOutAsync(string clientName, string userID)
        {
            HttpRestClientConfiguration client = HttpRestClientConfiguration.GetInstance();
            Token tr = await GetActorAdminTokenAsync(client, clientName);
            return await client.PostAsync(tr, Api + "/users/" + userID + "/logout", "");
        }

        public async Task<IActionResult> UpdatePasswordAsync(string clientName, string userID, Password pass)
        {
            HttpRestClientConfiguration client = HttpRestClientConfiguration.GetInstance();
            Token tr = await GetActorAdminTokenAsync(client, clientName);
            return await client.PutAsync(tr, Api + "/users/" + userID+ "/reset-password", JsonConvert.SerializeObject(pass));
        }

        public async Task<List<RoleMapping>> GetAvailableRealmRolesAsync(string clientName)
        {
            HttpRestClientConfiguration client = HttpRestClientConfiguration.GetInstance();
            Token tr = await GetActorAdminTokenAsync(client, clientName);
            IActionResult result = await client.GetAsync(tr, Api + "/roles");
            if ( !( result is OkObjectResult) )
            {
                throw new Exception("Its not possible to retrieve the roles.\n"+ ((ObjectResult)result).Value);
            }

            string jsonResult = (result as ObjectResult).Value.ToString();
			List<RoleMapping> rolesToShow = new List<RoleMapping>();

			if(jsonResult != "{}")
			{
				RoleMapping[] roles = JsonConvert.DeserializeObject<RoleMapping[]>(jsonResult);

				foreach (RoleMapping role in roles)
				{
					if (!role.ItsAnInternalRole()) rolesToShow.Add(role);
				}
			}

            return rolesToShow;
        }

        public async Task<List<RoleMapping>> AvailableClientRolesAsync(string clientName)
        {
            HttpRestClientConfiguration restClient = HttpRestClientConfiguration.GetInstance();
            Token tr = await GetActorAdminTokenAsync(restClient, clientName);
            SecurityClient client = GetClient(clientName);
            IActionResult result = await restClient.GetAsync(tr, $"{Api}/clients/{client.Id}/roles");
            if (!(result is OkObjectResult))
            {
                throw new Exception("Its not possible to retrieve the roles.\n" + ((ObjectResult)result).Value);
            }

            string jsonResult = (result as ObjectResult).Value.ToString();
            List<RoleMapping> rolesToShow = new List<RoleMapping>();

            if (jsonResult != "{}")
            {
                RoleMapping[] roles = JsonConvert.DeserializeObject<RoleMapping[]>(jsonResult);

                foreach (RoleMapping role in roles)
                {
                    if (!role.ItsAnInternalRole()) rolesToShow.Add(role);
                }
            }

            return rolesToShow;
        }

        public async Task<IActionResult> GetAgentsAsync(string clientName, string search, int first, int max)
        {
            string url = "/users?first="+ first;

            if (max != 0) url += "&max=" + max;
            if (!string.IsNullOrEmpty(search)) url += "&search="+ search;

            HttpRestClientConfiguration client = HttpRestClientConfiguration.GetInstance();
            Token tr = await GetActorAdminTokenAsync(client, clientName);
            return await client.GetAsync(tr, Api + url);
        }

        public async Task<IActionResult> CountUsersAsync(string clientName)
        {
            HttpRestClientConfiguration client = HttpRestClientConfiguration.GetInstance();
            Token tr = await GetActorAdminTokenAsync(client, clientName);
            return await client.GetAsync(tr, Api + "/users/count");
        }

        public async Task<IActionResult> CreateAgentsAsync(string clientName, User user)
        {
            HttpRestClientConfiguration client = HttpRestClientConfiguration.GetInstance();
            Token tr = await GetActorAdminTokenAsync(client, clientName);
            return await client.PostAsync(tr, Api + "/users", JsonConvert.SerializeObject(user));
        }

        public async Task<IActionResult> GetUsersAsync(string clientName, string search, int first, int max)
        {
            string url = "/users?first=" + first;

            if (max != 0) url += "&max=" + max;
            if (!string.IsNullOrEmpty(search)) url += "&search=" + search;

            HttpRestClientConfiguration client = HttpRestClientConfiguration.GetInstance();
            Token tr = await GetActorAdminTokenAsync(client, clientName);
            return await client.GetAsync(tr, Api + url);
        }

        public async Task<IActionResult> GetAllUsersAsync(string clientName)
        {
            HttpRestClientConfiguration client = HttpRestClientConfiguration.GetInstance();
            Token tr = await GetActorAdminTokenAsync(client, clientName);
            return await client.GetAsync(tr, Api + "/users");
        }
        public async Task<IActionResult> GetAgentsAsync(string clientName, string userID)
        {
            HttpRestClientConfiguration client = HttpRestClientConfiguration.GetInstance();
            Token tr = await GetActorAdminTokenAsync(client, clientName);
            return await client.GetAsync(tr, Api + "/users/" + userID);
        }

        public async Task<List<RoleMapping>> GetRolesOFUserAsync(string clientName, string userID)
        {
            HttpRestClientConfiguration restClient = HttpRestClientConfiguration.GetInstance();
            SecurityClient client = GetClient(clientName);
            Token tr = await GetActorAdminTokenAsync(restClient, clientName);
            IActionResult result =  await restClient.GetAsync(tr, $"{Api}/users/{userID}/role-mappings/clients/{client.Id}");

            if (!(result is OkObjectResult))
            {
                throw new Exception("Its not possible to retrieve the roles.\n" + ((ObjectResult)result).Value);
            }

            string jsonResult = (result as ObjectResult).Value.ToString();
            RoleMapping[] roles = JsonConvert.DeserializeObject<RoleMapping[]>(jsonResult);

            List<RoleMapping> rolesToShow = new List<RoleMapping>();
            foreach (RoleMapping role in roles)
            {
                if (!role.ItsAnInternalRole()) rolesToShow.Add(role);
            }

            return rolesToShow;
        }

        public async Task<IActionResult> MapRoleToUsersAsync(string clientName, string userID, RoleMapping[] roleMapping)
        {
            HttpRestClientConfiguration restClient = HttpRestClientConfiguration.GetInstance();
            SecurityClient client = GetClient(clientName);
            Token tr = await GetActorAdminTokenAsync(restClient, clientName);
            return await restClient.PostAsync(tr, $"{Api}/users/{userID}/role-mappings/clients/{client.Id}", JsonConvert.SerializeObject(roleMapping));
        }

        public async Task<IActionResult> UpdateUserAsync(string clientName, string userID, User user)
        {
            user.Id = userID;
            HttpRestClientConfiguration client = HttpRestClientConfiguration.GetInstance();
            Token tr = await GetActorAdminTokenAsync(client, clientName);
            return await client.PutAsync(tr, Api + "/users/" + userID , JsonConvert.SerializeObject(user));
        }

        public async Task<IActionResult> GetByUsernameAsync(string clientName, User user)
        {
            HttpRestClientConfiguration client = HttpRestClientConfiguration.GetInstance();
            Token tr = await GetActorAdminTokenAsync(client, clientName);
            return await client.GetAsync(tr, Api + $@"/users?username={user.Username}");
        }

        public async Task<IActionResult> GetByEmailAsync(string clientName, User user)
        {
            HttpRestClientConfiguration client = HttpRestClientConfiguration.GetInstance();
            Token tr = await GetActorAdminTokenAsync(client, clientName);
            return await client.GetAsync(tr, Api + $@"/users?email={user.Email}");
        }

        public async Task<IActionResult> GetAsync(string clientName, User user)
        {
            HttpRestClientConfiguration client = HttpRestClientConfiguration.GetInstance();
            Token tr = await GetActorAdminTokenAsync(client, clientName);
            return await client.GetAsync(tr, Api + $@"/users?username={user.Username}&email={user.Email}&firstName={user.FirstName}");
        }
    }

    internal class LottoSecuritySection : SecuritySection
    {
        public LottoSecuritySection(string api, string authority, List<SecurityClient> clients) : base(api, authority, clients)
        {
        }
    }

    internal class LinesSecutirySection : SecuritySection
    {
        public LinesSecutirySection(string api, string authority, List<SecurityClient> clients) : base(api, authority, clients)
        {
        }
    }

    internal class MMSecuritySection : SecuritySection
    {
        public MMSecuritySection(string api, string authority, List<SecurityClient> clients) : base(api, authority, clients)
        {
        }
    }

	internal class ExchangeOwnerSecuritySection : SecuritySection
	{
		public ExchangeOwnerSecuritySection(string api, string authority, List<SecurityClient> clients) : base(api, authority, clients)
		{
		}
	}

    internal class ExchangeAgentsSecuritySection : SecuritySection
    {
        public ExchangeAgentsSecuritySection(string api, string authority, List<SecurityClient> clients) : base(api, authority, clients)
        {
        }
    }

    internal class ExchangeFieroSecuritySection : SecuritySection
    {
        public ExchangeFieroSecuritySection(string api, string authority, List<SecurityClient> clients) : base(api, authority, clients)
        {
        }
    }

    public class Token
    {
        public string access_token { get; set; }
        public int expires_in { get; set; }
        public int refresh_expires_in { get; set; }
        public string refresh_token { get; set; }
        public string token_type { get; set; }
        public string scope { get; set; }
    }

    public class TokenResponse
    {
        public string access_token { get; set; }
        public int expires_in { get; set; }
        public int refresh_expires_in { get; set; }
        public string refresh_token { get; set; }
        public string token_type { get; set; }
        public string scope { get; set; }
    }

    [DataContract]
    public class AmountOfWagersPerChunkBody
    {
        [DataMember(Name = "amountOfWagersPerChunk")]
        public int AmountOfWagersPerChunk { get; set; }
    }

    [DataContract]
    public class UserInternal
    {
        [DataMember(Name = "id", EmitDefaultValue =false)]
        public string Id { get; set; }
        [DataMember(Name = "username", EmitDefaultValue = false)]
        public string Username { get; set; }
        [DataMember(Name = "email", EmitDefaultValue = false)]
        public string Email { get; set; }
        [DataMember(Name = "enabled")]
        public bool Enabled { get; set; }
        [DataMember(Name = "firstName", EmitDefaultValue = false)]
        public string FirstName { get; set; }
        [DataMember(Name = "lastName", EmitDefaultValue = false)]
        public string LastName { get; set; }
        [DataMember(Name = "credentials", EmitDefaultValue = false)]
        public Credentials[] Credentials { get; set; }
        public bool HasEmail { get { return !string.IsNullOrEmpty(Email); } }
		[DataMember(Name = "agencyName", EmitDefaultValue = false)]
		public string AgencyName { get; set; }
        [DataMember(Name = "agent", EmitDefaultValue = false)]
        public string Agent { get; set; }
        [DataMember(Name = "spaces", EmitDefaultValue = false)]
        public string Spaces { get; set; }
        [DataMember(Name = "profileId", EmitDefaultValue = false)]
        public int ProfileId { get; set; }
        [DataMember(Name = "userTypeName", EmitDefaultValue = false)]
        public string UserTypeName { get; set; }
    }

    [DataContract]
	public class ExistAgent
	{
		[DataMember(Name = "hasAgent")]
		public bool HasAgent { get; set; }
        [DataMember(Name = "tenantId")]
        public int TenantId { get; set; }
    }

    [DataContract]
    public class TenantData
    {
        [DataMember(Name = "tenantId")]
        public int TenantId { get; set; }
        [DataMember(Name = "tenantName")]
        public string TenantName { get; set; }
        [DataMember(Name = "storeId")]
        public int StoreId { get; set; }
    }

    [DataContract]
	public class Agent
	{
		[DataMember(Name = "path")]
		public string AgentPath { get; set; }
	}

	[DataContract]
	public class User
	{
		[DataMember(Name = "id", EmitDefaultValue = false)]
		public string Id { get; set; }
		[DataMember(Name = "username", EmitDefaultValue = false)]
		public string Username { get; set; }
		[DataMember(Name = "email", EmitDefaultValue = false)]
		public string Email { get; set; }
		[DataMember(Name = "enabled")]
		public bool Enabled { get; set; }
		[DataMember(Name = "firstName", EmitDefaultValue = false)]
		public string FirstName { get; set; }
		[DataMember(Name = "lastName", EmitDefaultValue = false)]
		public string LastName { get; set; }
        [DataMember(Name = "credentials", EmitDefaultValue = false)]
		public Credentials[] Credentials { get; set; }
		public bool HasEmail { get { return !string.IsNullOrEmpty(Email); } }
		[DataMember(Name = "attributes")]
		public Attributes Attributes { get; set; }
	}

    [DataContract]
    public class UserBody:User
    {
        [DataMember(Name = "userTypeName", EmitDefaultValue = false)]
        public string UserTypeName { get; set; }
    }

    [DataContract]
    public class Credentials
    {
        [DataMember(Name = "type", EmitDefaultValue = false)]
        public string Type { get; set; }
        [DataMember(Name = "value", EmitDefaultValue = false)]
        public string Value { get; set; }
        [DataMember(Name = "temporary", EmitDefaultValue = false)]
        public string Temporary { get; set; }
    }

	[DataContract]
	public class Attributes
	{
        [DataMember(Name = "domains")]
        public string[] Domains { get; set; }
        [DataMember(Name = "path")]
		public string[] Path { get; set; }
        [DataMember(Name = "agent")]
        public string[] Agent { get; set; }
        [DataMember(Name = "spaces")]
        public string[] Spaces { get; set; }
        [DataMember(Name = "tenantId")]
        public string[] TenantId { get; set; }
        [DataMember(Name = "profileId")]
        public string[] ProfileId { get; set; }
        [DataMember(Name = "userTypeName")]
        public string[] UserTypeName { get; set; }
    }

	[DataContract]
    public class LogOutResponse
    {
        [DataMember(Name = "hasEmail")]
        public bool HasEmail { get; set; }
    }
    [DataContract]
    public class RoleMapping
    {
        [DataMember(Name = "id", EmitDefaultValue = false)]
        public string Id { get; set; }
        [DataMember(Name = "name", EmitDefaultValue = false)]
        public string Name { get; set; }
        [DataMember(Name = "description", EmitDefaultValue = false)]
        public string Description { get; set; }

        public bool ItsAnInternalRole()
        {
            return Name.Trim().Equals("offline_access")
                || Name.Trim().Equals("uma_authorization")
                || Name.Trim().Equals("player");
        }
    }

    [DataContract]
    public class Password
    {
        [DataMember(Name = "type", EmitDefaultValue = false)]
        public string Type { get; set; }
        [DataMember(Name = "value", EmitDefaultValue = false)]
        public string Value { get; set; }
        [DataMember(Name = "temporary")]
        public bool Temporary { get; set; }
    }

    public class Jwt
    {
        public RolesJwt realm_access { get; set; }
        public RolesJwtResource resource_access { get; set; }
        public string sub { get; set; }
        public string aud { get; set; }
        public string name { get; set; }
        public string email { get; set; }
        public string preferred_username { get; set; }
        public string realm_ulr { get; set; }
        public string playerid { get; set; }
        public string tenantid { get; set; }
        public string tenantname { get; set; }
        public string storeids { get; set; }
        public int affiliateId { get; set; }
        public string affiliateName { get; set; }
        public string agent { get; set; }
    }

    public class JwtCustom : Jwt
	{
		public string path { get; set; }
    }

    public class JwtCustomAttribute : Jwt
    {
        public string domains { get; set; }
        public string spaces { get; set; }
    }

    public class RolesJwt
    {
        public string[] roles { get; set; }
    }

    public class RolesJwtResource
    {
        public RolesJwt internal_admin { get; set; }
        public RolesJwt lotto { get; set; }
        public RolesJwt keno { get; set; }
        public RolesJwt exchange { get; set; }
        public RolesJwt lines { get; set; }
        public RolesJwt brackets { get; set; }
    }

    public class UserInformation
    {
        public string UserPath { get; set; }
        public string PlayerId { get; set; }
        public string TenantId { get; set; }
        public string TenantName { get; set; }
        public string StoreIds { get; set; }
        public int AffiliateId { get; set; }
        public string AffiliateName { get; set; }
        public string Domains { get; set; }
        public string Spaces { get; set; }
        [JsonIgnore]
        public string Email { get; set; }
        public string RefreshToken { get; set; }
        [JsonIgnore]
        public string Name { get; set; }
        [JsonIgnore]
        public string UserName { get; set; }
        [JsonIgnore]
        public string[] Roles { get; internal set; }
		public Agents Agent { get; set; }

		public bool ItHasEmail()
        {
            return (String.IsNullOrWhiteSpace(Email));
        }

        public bool IsInRole(string roleToCheck)
        {
            foreach (string role in Roles)
            {
                if (role.Equals(roleToCheck)) return true;
            }
            return false;
        }
    }
}
