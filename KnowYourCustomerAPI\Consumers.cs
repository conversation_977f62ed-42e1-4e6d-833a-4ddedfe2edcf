﻿using Elasticsearch.Net;
using GamesEngine;
using GamesEngine.Customers;
using GamesEngine.Logs;
using GamesEngine.PurchaseOrders;
using GamesEngine.Settings;
using KnowYourCustomerAPI.Controllers;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Puppeteer.EventSourcing;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Runtime.Serialization;
using System.Text;
using System.Threading.Tasks;
using static GamesEngine.Exchange.PaymentProcessorsAndActionsByDomains;
using static GamesEngine.Finance.PaymentChannels;
using static GamesEngine.PurchaseOrders.Customer;
using static KnowYourCustomerAPI.Controllers.APIController;

namespace KnowYourCustomerAPI
{
    public class Consumers
    {
        internal void CreateConsumerForTopics()
        {
            new ExternalCustomersConsumer($"{KafkaMessage.KYC_CONSUMER_PREFIX} {Integration.Kafka.Group}", Integration.Kafka.TopicForCustomers).StartListening();
            new CustomerCreationConsumer(Integration.Kafka.Group, $"{KafkaMessage.CUSTOMER_CREATION_CONSUMER_PREFIX}_{Integration.Kafka.TopicForCustomers}").StartListening();
            new CustomerUpdateConsumer(Integration.Kafka.Group, $"{KafkaMessage.CUSTOMER_UPDATE_CONSUMER_PREFIX}_{Integration.Kafka.TopicForCustomers}").StartListening();
            new CustomerStatusChangesConsumer(Integration.Kafka.Group, $"{KafkaMessage.KYC_CUSTOMER_STATUS_CHANGE_CONSUMER_PREFIX}_{Integration.Kafka.TopicForCustomers}").StartListening();
            new RejectedCustomersConsumer(Integration.Kafka.Group, $"{KafkaMessage.REJECTED_CUSTOMER_CONSUMER_PREFIX}_{Integration.Kafka.TopicForCustomers}").StartListening();
            new CustomerLogConsumer(Integration.Kafka.Group, $"{KafkaMessage.CUSTOMER_LOG_CONSUMER_PREFIX}_{Integration.Kafka.TopicForCustomers}").StartListening();
            new CatalogConsumer(ValidateMessageOwnership, GetActor, $"{KafkaMessage.KYC_CONSUMER_PREFIX}{Integration.Kafka.Group}", Integration.Kafka.TopicForCatalog).StartListening();

        }

        bool ValidateMessageOwnership(string storeAlias)
        {
            return false;
        }
        GamesEngine.Settings.RestAPIActorAsync GetActor()
        {
            return KnowYourCustomerAPI.KnowYourCustomer;
        }

        private class ExternalCustomersConsumer : GamesEngine.Settings.Consumer
        {
            public ExternalCustomersConsumer(string group, string topic) : base(group, topic)
            {
            }

            public override void OnMessageBeforeCommit(string msg)
            {
                if (String.IsNullOrEmpty(msg)) throw new Exception(nameof(msg));

                CustomerMessage.CustomerMessageType messageType = CustomerMessage.GetType(msg);

                if (messageType == CustomerMessage.CustomerMessageType.NEW_CUSTOMER)
                {
                    NewCustomerMessage customerMsg = new NewCustomerMessage(msg);

                    var result = KnowYourCustomerAPI.KnowYourCustomer.PerformQry($@"
						{{
							exists = company.ExistsCustomer('{customerMsg.AccountNumber}');
							print exists playerAlreadyExists;
						}}
					");

					OkObjectResult o = (OkObjectResult)result;
					string json = o.Value.ToString();
					CustomerData customer = JsonConvert.DeserializeObject<CustomerData>(json);

					if (!customer.PlayerAlreadyExists)
					{
                        var bodyEscaped = $@"{{
                            ""{CustomerForm.PROPERTY_TEMPLATE_ID}"":""{(int)TEMPLATES_CONST.Player}"",
                            ""{CustomerForm.PROPERTY_TEMPLATE_VERSION}"":""1"",
                            ""{Fields.ID_ACCOUNT_NUMBER}"":""{customerMsg.AccountNumber}"",
                            ""{Fields.PREFIX_VERIFIABLE}{Fields.ID_ACCOUNT_NUMBER}"":true
                        }}";

                        result = KnowYourCustomerAPI.KnowYourCustomer.PerformCmd($@"
                    	    customer = company.CreateCustomer('{customerMsg.AccountNumber}',{((Agents)customerMsg.AgentId).ToString()});
                    	    player = customer.Player;
                        ");

                        if (!(result is OkObjectResult))
                        {
                            throw new GameEngineException($@"Error:{((ObjectResult)result).Value.ToString()}");
                        }

                        result = KnowYourCustomerAPI.KnowYourCustomer.PerformQry($@"
                            {{
                    	        customer = company.GetOrCreateCustomerById('{customerMsg.AccountNumber}');
                                player = customer.Player;
                                template = templates.GetTemplateById({(int)TEMPLATES_CONST.Player});
                                customerFormBuilder = CustomerFormBuilder(customer, template);
                                customerForm = customerFormBuilder.AddFields('{bodyEscaped}').SetAsNew(Now, '{GamesEngine.Finance.Users.ME}', customerTypeStandard.Id).CreateForm();
                                customerForm.VerifyIfCustomerIsAutoApproved();
                                print customerForm.Serialize() serializedFields;
                                print customerForm.Log log;
                                print customerForm.IsCustomerAutoApproved isCustomerAutoApproved;
                                print Now now;
                                print player.Id uuid;
                                print player.Id password;
                            }}
                        ");

                        string topicName = string.Empty;
                        topicName = $"{KafkaMessage.CUSTOMER_CREATION_CONSUMER_PREFIX}_{Integration.Kafka.TopicForCustomers}";

                        if (!(result is OkObjectResult))
                        {
                            throw new GameEngineException($@"Error:{((ObjectResult)result).Value.ToString()}");
                        }

                        OkObjectResult obj = (OkObjectResult)result;
                        var strJson = obj.Value.ToString();
                        var serializedFieldsBody = JsonConvert.DeserializeObject<SerializedFieldsBody>(strJson);

                        Integration.Kafka.Send(
                            true,
                            topicName,
                            new DraftedCustomerMessage(serializedFieldsBody.Now, GamesEngine.Finance.Users.ME.ToString(), serializedFieldsBody.SerializedFields, serializedFieldsBody.IsCustomerAlreadyApproved, serializedFieldsBody.IsCustomerAutoApproved, true)
                        );
                        //Integration.Kafka.Send(
                        //true,
                        //    $"{KafkaMessage.KYC_CUSTOMER_STATUS_CHANGE_CONSUMER_PREFIX}_{Integration.Kafka.TopicForCustomers}",
                        //    new CustomerStatusChangeMessage(await GetNowAsync(), employeeName, Customer.CustomerStatus.Approved, identificationNumber)
                        //);
                        //Integration.Kafka.Send(
                        //    true,
                        //    $"{KafkaMessage.CUSTOMER_LOG_CONSUMER_PREFIX}_{Integration.Kafka.TopicForCustomers}",
                        //    new CustomerLogMessage(serializedFieldsBody.Now, GamesEngine.Finance.Users.ME.ToString(), customerMsg.AccountNumber, serializedFieldsBody.Log)
                        //);
                    }
				}
            }
        }
        [DataContract(Name = "Customer")]
        public class CustomerData
        {
            [DataMember(Name = "playerAlreadyExists")]
            public bool PlayerAlreadyExists { get; set; }
        }
        private class CustomerCreationConsumer : Consumer
        {
            public CustomerCreationConsumer(string group, string topic) : base(group, topic)
            {
            }

            public override void OnMessageBeforeCommit(string msg)
            {
                if (String.IsNullOrEmpty(msg)) throw new ArgumentNullException(nameof(msg));

                var message = new DraftedCustomerMessage(msg);
                var json = JsonConvert.DeserializeObject<IDictionary<string, object>>(message.Document);

                json.TryGetValue(Fields.ID_ACCOUNT_NUMBER, out object objCustomerId);
                var accountNumber = objCustomerId == null ? string.Empty : objCustomerId.ToString();
                var isCustomerWithAccountNumber = ! string.IsNullOrWhiteSpace(accountNumber);

                json.TryGetValue(Fields.ID_IDENTIFICATION_NUMBER, out object objIdentificationNumber);
                var identificationNumber = objIdentificationNumber == null ? string.Empty : objIdentificationNumber.ToString();
                var isCustomerWithIdentificationNumber = !string.IsNullOrWhiteSpace(identificationNumber);

                if (isCustomerWithAccountNumber || isCustomerWithIdentificationNumber)
                {
                    if (!isCustomerWithAccountNumber) accountNumber = identificationNumber;
                    if (!isCustomerWithIdentificationNumber) identificationNumber = accountNumber;
                    CreateCustomerInActor(json, accountNumber, identificationNumber);
                }

                var identifier = SearchIdentifier(json);

                object documentsSubmitted;
                json.TryGetValue(Fields.ID_DOCUMENTS_SUBMITTED, out documentsSubmitted);
                if (documentsSubmitted != null)
                {
                    var strDocumentsSubmitted = documentsSubmitted.ToString().Replace("{", "").Replace("}", "").Replace("[", "").Replace("]", "").Replace("\"", "").Replace("\r\n", "").Replace(" ", "").Trim().Split(",");
                    json[Fields.ID_DOCUMENTS_SUBMITTED] = strDocumentsSubmitted;
                }

                Loggers.GetIntance().SearchEngine.Debug($"index: customers request:{json}");
                var postData = PostData.Serializable(json);
                HttpELKClient client = HttpELKClient.GetInstance();
                var response = client.CreateDocument(APIController.INDEX_CUSTOMER_NAME, identifier, postData);

                Loggers.GetIntance().SearchEngine.Debug($"index: customers response:{response}");

                if (!response.Success)
                {
                    string error = $"Body: {response.Body} \n OriginalException:{response.OriginalException}";
                    ErrorsSender.Send(error, $"Error creating document for customer '{identifier}'. ");
                }
                else if (isCustomerWithAccountNumber && ! message.IsCustomerAlreadyApproved && message.IsCustomerAutoApproved)
                {
                    json.TryGetValue(Fields.ID_NICKNAME, out object objNickname);
                    var nickName = objNickname == null ? string.Empty : objNickname.ToString();
                    json.TryGetValue(Fields.ID_AVATAR, out object objAvatar);
                    var avatar = objAvatar == null ? string.Empty : objAvatar.ToString();
                    Integration.Kafka.Send(
                        true,
                        $"{KafkaMessage.KYC_CUSTOMER_STATUS_CHANGE_CONSUMER_PREFIX}_{Integration.Kafka.TopicForCustomers}",
                        new CustomerStatusChangeMessage(message.Date, message.EmployeeName, CustomerStatus.Approved, accountNumber, nickName, avatar, message.IsReplicatedExternally)
                    );
                }
                else if (isCustomerWithAccountNumber && message.IsCustomerAlreadyApproved && ! message.IsCustomerAutoApproved)
                {
                    Integration.Kafka.Send(
                        true,
                        $"{KafkaMessage.KYC_CUSTOMER_STATUS_CHANGE_CONSUMER_PREFIX}_{Integration.Kafka.TopicForCustomers}",
                        new CustomerStatusChangeMessage(message.Date, message.EmployeeName, CustomerStatus.Drafted, accountNumber, message.IsReplicatedExternally)
                    );
                }
            }

            struct CustomerExistence
            {
                public bool existsCustomer { get; set; }
            }

            bool ExistsCustomerBy(string identificationNumber)
            {
                if (string.IsNullOrWhiteSpace(identificationNumber)) return false;
                var result = KnowYourCustomerAPI.KnowYourCustomer.PerformQry($@"
                    {{
                        existsCustomer = company.ExistsCustomerByIdentifier('{identificationNumber}');
                        print existsCustomer existsCustomer;
                    }}
                ");

                if (!(result is OkObjectResult))
                {
                    throw new GameEngineException($@"Error:{((ObjectResult)result).Value.ToString()}");
                }

                OkObjectResult o = (OkObjectResult)result;
                string json = o.Value.ToString();
                var customerExistence = JsonConvert.DeserializeObject<CustomerExistence>(json);

                return customerExistence.existsCustomer;
            }

            void CreateCustomerInActor(IDictionary<string, object> json, string accountNumber, string identificationNumber)
            {
                var existsCustomerByIdentifier = ExistsCustomerBy(identificationNumber);
                if (!existsCustomerByIdentifier)
                {
                    var scriptToAssignIdentifier = string.IsNullOrWhiteSpace(identificationNumber) ? string.Empty : $"customer.Identifier='{identificationNumber}';";
                    var result = KnowYourCustomerAPI.KnowYourCustomer.PerformCmd($@"
					    customer = company.GetOrCreateCustomerById('{accountNumber}');
                        {scriptToAssignIdentifier}
					    player =  customer.Player;
				    ");
                    if (!(result is OkObjectResult))
                    {
                        throw new GameEngineException($@"Error:{((ObjectResult)result).Value.ToString()}");
                    }
                }
            }
        }

        internal static string SearchIdentifier(IDictionary<string, object> resultInJson)
        {
            resultInJson.TryGetValue(CustomerForm.PROPERTY_KEY, out object keyToken);
            if (keyToken == null) throw new Exception($"'{CustomerForm.PROPERTY_KEY}' does not exist.");
            var keyValue = keyToken.ToString();
            var uniqueKeys = keyValue.Split(',');
            string identifier = null;
            foreach (var key in uniqueKeys)
            {
                resultInJson.TryGetValue(key, out object tempToken);
                if (tempToken == null) throw new Exception($"Key '{key}' does not exist.");
                if (identifier == null)
                    identifier = tempToken.ToString();
                else
                    identifier += $",{tempToken.ToString()}";
            }
            return identifier;
        }

        private class CustomerUpdateConsumer : Consumer
        {
            public CustomerUpdateConsumer(string group, string topic) : base(group, topic)
            {
            }

            public override void OnMessageBeforeCommit(string msg)
            {
                if (String.IsNullOrEmpty(msg)) throw new ArgumentNullException(nameof(msg));

                var message = new DraftedCustomerMessage(msg);
                var json = JsonConvert.DeserializeObject<IDictionary<string, object>>(message.Document);

                object objCustomerId;
                json.TryGetValue(Fields.ID_ACCOUNT_NUMBER, out objCustomerId);
                var accountNumber = objCustomerId == null ? string.Empty : objCustomerId.ToString();
                var isCustomerWithAccountNumber = !string.IsNullOrWhiteSpace(accountNumber);

                var identifier = SearchIdentifier(json);

                object documentsSubmitted;
                json.TryGetValue(Fields.ID_DOCUMENTS_SUBMITTED, out documentsSubmitted);
                if (documentsSubmitted != null)
                {
                    var strDocumentsSubmitted = documentsSubmitted.ToString().Replace("{", "").Replace("}", "").Replace("[", "").Replace("]", "").Replace("\"", "").Replace("\r\n", "").Replace(" ", "").Trim().Split(",");
                    json[Fields.ID_DOCUMENTS_SUBMITTED] = strDocumentsSubmitted;
                }

                var jsonToUpdate = new CustomerDocumentBody() { Document = json };
                Loggers.GetIntance().SearchEngine.Debug($"update index: customers request:{jsonToUpdate}");
                var postData = PostData.Serializable(jsonToUpdate);
                HttpELKClient client = HttpELKClient.GetInstance();
                var response = client.UpdateDocument(APIController.INDEX_CUSTOMER_NAME, identifier, postData);
                Loggers.GetIntance().SearchEngine.Debug($"index: customers response:{response}");

                if (!response.Success)
                {
                    string error = $"Body: {response.Body} \n OriginalException:{response.OriginalException}";
                    ErrorsSender.Send(error, $"Error creating document for customer {identifier}. ");
                }
                else if (isCustomerWithAccountNumber && ! message.IsCustomerAlreadyApproved && message.IsCustomerAutoApproved)
                {
                    json.TryGetValue(Fields.ID_NICKNAME, out object objNickname);
                    var nickName = objNickname == null ? string.Empty : objNickname.ToString();
                    json.TryGetValue(Fields.ID_AVATAR, out object objAvatar);
                    var avatar = objAvatar == null ? string.Empty : objAvatar.ToString();
                    Integration.Kafka.Send(
                        true,
                        $"{KafkaMessage.KYC_CUSTOMER_STATUS_CHANGE_CONSUMER_PREFIX}_{Integration.Kafka.TopicForCustomers}",
                        new CustomerStatusChangeMessage(message.Date, message.EmployeeName, CustomerStatus.Approved, accountNumber, nickName, avatar, false)
                    );
                }
                else if (isCustomerWithAccountNumber && message.IsCustomerAlreadyApproved && ! message.IsCustomerAutoApproved)
                {
                    Integration.Kafka.Send(
                        true,
                        $"{KafkaMessage.KYC_CUSTOMER_STATUS_CHANGE_CONSUMER_PREFIX}_{Integration.Kafka.TopicForCustomers}",
                        new CustomerStatusChangeMessage(message.Date, message.EmployeeName, CustomerStatus.Drafted, accountNumber, false)
                    );
                }
            }
        }

        internal static BytesResponse AddCustomerLog(CustomerBaseMessage message, string identificationNumber, string logMessage)
        {
            var logs = new Logs();
            var log = logs.CreateLog(identificationNumber);
            log.AddEntry(message.Date, message.EmployeeName, logMessage);
            var entry = log.LastEntry();
            var serializedEntry = entry.Serialize();
            var deserializedEntry = JsonConvert.DeserializeObject<LogEntry.LogEntryJson>(serializedEntry);

            Loggers.GetIntance().SearchEngine.Debug($"index: customers request:{deserializedEntry}");
            var postData = PostData.Serializable(deserializedEntry);
            HttpELKClient client = HttpELKClient.GetInstance();
            var response = client.CreateDocument(APIController.INDEX_CUSTOMER_LOG_NAME, postData);
            return response;
        }

        internal static JObject GetPayloadToChangeCustomerStatus(CustomerStatus status, string dateFormattedAsText, string employeeName, string accountNumber)
        {
            string dateFieldName;
            string whoFieldName;
            switch (status)
            {
                case CustomerStatus.Approved:
                    dateFieldName = CustomerForm.PROPERTY_APPROVAL_DATE;
                    whoFieldName = CustomerForm.PROPERTY_WHO_APPROVED;
                    break;
                case CustomerStatus.Rejected:
                    dateFieldName = CustomerForm.PROPERTY_REJECTION_DATE;
                    whoFieldName = CustomerForm.PROPERTY_WHO_REJECTED;
                    break;
                default:
                    throw new GameEngineException($"No implementation to get payload for status {status}");
            }

            var payload = new JObject
            {
                ["script"] = new JObject
                {
                    ["source"] = @$"
                            ctx._source.{CustomerForm.PROPERTY_STATUS} = params.{CustomerForm.PROPERTY_STATUS};
                            ctx._source.{dateFieldName} = params.{dateFieldName};
                            ctx._source.{whoFieldName} = params.{whoFieldName};",
                    ["params"] = new JObject
                    {
                        [$"{CustomerForm.PROPERTY_STATUS}"] = $"{status}",
                        [$"{dateFieldName}"] = $"{dateFormattedAsText}",
                        [$"{whoFieldName}"] = $"{employeeName}"
                    }
                },
                ["query"] = new JObject
                {
                    ["bool"] = new JObject
                    {
                        ["should"] = new JArray
                            {
                                new JObject
                                {
                                    ["term"] = new JObject
                                    {
                                        ["account_number"] = $"{accountNumber}"
                                    }
                                },
                                new JObject
                                {
                                    ["term"] = new JObject
                                    {
                                        ["identification_number"] = $"{accountNumber}"
                                    }
                                }
                            }
                    }
                }
            };
            return payload;
        }

        private class CustomerStatusChangesConsumer : Consumer
        {
            public CustomerStatusChangesConsumer(string group, string topic) : base(group, topic)
            {
            }

            public override void OnMessageBeforeCommit(string msg)
            {
                if (String.IsNullOrEmpty(msg)) throw new ArgumentNullException(nameof(msg));

                var message = new CustomerStatusChangeMessage(msg);
                ChangeCustomerStatus(message);
            }

            private StringResponse ChangeCustomerStatus(CustomerStatusChangeMessage message)
            {
                var payload = GetPayloadToChangeCustomerStatus(message.Status, message.DateFormattedAsText, message.EmployeeName, message.AccountNumber);

                Loggers.GetIntance().SearchEngine.Debug($"update index: customers request:{payload}");
                HttpELKClient client = HttpELKClient.GetInstance();
                var response = client.UpdateDocumentByQuery(APIController.INDEX_CUSTOMER_NAME, payload.ToString());

                if (response.Success)
                {
                    var log = $"{message.EmployeeName} approved customer with account number '{message.AccountNumber}'";
                    AddCustomerLog(message, message.AccountNumber, log);

                    ChangeCustomerStatusInThisActor(message);
                    ChangeCustomerStatusExternally(message);
                }
                return response;
            }

            private void ChangeCustomerStatusInThisActor(CustomerStatusChangeMessage message)
            {
                switch (message.Status)
                {
                    case CustomerStatus.Drafted:
                        var result = KnowYourCustomerAPI.KnowYourCustomer.PerformCmd($@"
                        {{                               
                            customer = company.CustomerByAccountNumber('{message.AccountNumber}');
					        customer.Disapprove();
                        }}"
                        );

                        if (!(result is OkObjectResult))
                        {
                            throw new GameEngineException($@"Error:{((ObjectResult)result).Value.ToString()}");
                        }
                        break;
                    case CustomerStatus.Approved:
                        var replicationCommand = message.IsReplicatedExternally ? string.Empty : 
                                                                                $@"thePlayerItsNew = customer.ItsNewOne();
                                                                                customer.ReplicateInOtherNodes(itIsThePresent, thePlayerItsNew, now, '{message.Nickname}', '{message.Avatar}');";
                        result = KnowYourCustomerAPI.KnowYourCustomer.PerformCmd($@"
                        {{                               
                            customer = company.CustomerByAccountNumber('{message.AccountNumber}');
					        customer.Approve();
                            {replicationCommand}
                        }}"
                        );

                        if (!(result is OkObjectResult))
                        {
                            throw new GameEngineException($@"Error:{((ObjectResult)result).Value.ToString()}");
                        }
                        break;
                    default:
                        throw new GameEngineException($"{nameof(message.Status)} '{message.Status.ToString()}' is not valid");
                }
            }

            private void ChangeCustomerStatusExternally(CustomerStatusChangeMessage message)
            {
                Integration.Kafka.Send(
                    true,
                    Integration.Kafka.TopicForCustomers,
                    new CustomerStatusSynchronizationMessage(message.Status, message.AccountNumber)
                );
            }
        }

        private class RejectedCustomersConsumer : Consumer
        {
            public RejectedCustomersConsumer(string group, string topic) : base(group, topic)
            {
            }

            public override void OnMessageBeforeCommit(string msg)
            {
                if (String.IsNullOrEmpty(msg)) throw new ArgumentNullException(nameof(msg));

                var message = new RejectedCustomerMessage(msg);

                RejectCustomer(message);

                var result = KnowYourCustomerAPI.KnowYourCustomer.PerformCmd($@"
                    {{                               
                        customer = company.CustomerByAccountNumber('{message.AccountNumber}');
					    customer.Reject();
                    }}"
                );
                if (!(result is OkObjectResult))
                {
                    throw new GameEngineException($@"Error:{((ObjectResult)result).Value.ToString()}");
                }
            }

            private StringResponse RejectCustomer(RejectedCustomerMessage message)
            {
                var payload = GetPayloadToChangeCustomerStatus(CustomerStatus.Rejected, message.DateFormattedAsText, message.EmployeeName, message.AccountNumber);

                Loggers.GetIntance().SearchEngine.Debug($"update index: customers request:{payload}");
                HttpELKClient client = HttpELKClient.GetInstance();
                var response = client.UpdateDocumentByQuery(APIController.INDEX_CUSTOMER_NAME, payload.ToString());

                if (response.Success)
                {
                    var log = $"{message.EmployeeName} rejected customer with account number '{message.AccountNumber}'. Reason '{message.Reason}'";
                    AddCustomerLog(message, message.AccountNumber, log);

                    /*if (Integration.UseKafka || Integration.UseKafkaForAuto)
                    {
                        Integration.Kafka.Send(
                            true,
                            Integration.Kafka.TopicForCustomers,
                            new CustomerStatusSynchronizationMessage(Customer.CustomerStatus.Rejected, message.AccountNumber)
                        );
                    }*/
                }
                return response;
            }
        }

        private class CustomerLogConsumer : Consumer
        {
            public CustomerLogConsumer(string group, string topic) : base(group, topic)
            {
            }

            public override void OnMessageBeforeCommit(string msg)
            {
                if (String.IsNullOrEmpty(msg)) throw new ArgumentNullException(nameof(msg));

                var message = new CustomerLogMessage(msg);
                var log = $"{message.EmployeeName} wrote down '{message.Log}'";
                AddCustomerLog(message, message.Identifier, log);
            }
        }
    }

    public abstract class CustomerBaseMessage : KafkaMessage
    {
        public DateTime Date
        {
            get
            {
                return new DateTime(DateTicks);
            }
        }
        public string DateFormattedAsText
        {
            get
            {
                return Date.ToString("MM/dd/yyyy HH:mm:ss");
            }
        }
        public long DateTicks { private set; get; }
        public string EmployeeName { private set; get;}

        internal CustomerBaseMessage(DateTime date, string employeeName)
        {
            DateTicks = date.Ticks;
            EmployeeName = employeeName;
        }

        public CustomerBaseMessage(string message) : base(message)
        {
            var serializedMessage = KafkaMessage.Split(message);
            DateTicks = long.Parse(serializedMessage[0]);
            EmployeeName = serializedMessage[1];
        }

        protected override void InternalSerialize()
        {
            base.InternalSerialize();
            AddProperty(DateTicks).
            AddProperty(EmployeeName);
        }

        protected override void Deserialize(string [] message, out int fieldOrder)
        {
            base.Deserialize(message, out fieldOrder);
            DateTicks = long.Parse(message[fieldOrder++]);
            EmployeeName = message[fieldOrder++];
        }
    }

    public sealed class DraftedCustomerMessage : CustomerBaseMessage
    {
        public string Document { private set; get; }
        public bool IsCustomerAlreadyApproved { private set; get;}
        public bool IsCustomerAutoApproved { private set; get;}
        public bool IsReplicatedExternally { private set; get; }
        public DraftedCustomerMessage(DateTime date, string employeeName, string document, bool isCustomerAlreadyApproved, bool isCustomerAutoApproved, bool isReplicatedExternally) : base(date, employeeName)
        {
            Document = document;
            IsCustomerAlreadyApproved = isCustomerAlreadyApproved;
            IsCustomerAutoApproved = isCustomerAutoApproved;
            IsReplicatedExternally = isReplicatedExternally;
        }

        public DraftedCustomerMessage(string message) : base(message)
        {
            if (string.IsNullOrEmpty(message)) throw new ArgumentNullException(nameof(message));

            var serializedMessage = KafkaMessage.Split(message);
            Document = serializedMessage[2];
            IsCustomerAlreadyApproved = bool.Parse(serializedMessage[3]);
            IsCustomerAutoApproved = bool.Parse(serializedMessage[4]);
            IsReplicatedExternally = bool.Parse(serializedMessage[5]);
        }

        protected override void InternalSerialize()
        {
            base.InternalSerialize();
            AddProperty(Document).
            AddProperty(IsCustomerAlreadyApproved).
            AddProperty(IsCustomerAutoApproved).
            AddProperty(IsReplicatedExternally);
        }

        protected override void Deserialize(string [] message, out int fieldOrder)
        {
            base.Deserialize(message, out fieldOrder);
            Document = message[fieldOrder++];
            IsCustomerAlreadyApproved = bool.Parse(message[fieldOrder++]);
            IsCustomerAutoApproved = bool.Parse(message[fieldOrder++]);
            IsReplicatedExternally = bool.Parse(message[fieldOrder++]);
        }
    }

    public sealed class CustomerStatusChangeMessage : CustomerBaseMessage
    {
        internal string AccountNumber { get; private set; }
        internal string Nickname { get; private set; }
        internal string Avatar { get; private set; }
        internal bool IsReplicatedExternally { private set; get; }
        internal CustomerStatus Status { get; private set; }
        const char Empty = '-';

        public CustomerStatusChangeMessage(DateTime date, string employeeName, CustomerStatus status, string accountNumber, bool isReplicatedExternally) : base(date, employeeName)
        {
            Status = status;
            AccountNumber = accountNumber;
            IsReplicatedExternally = isReplicatedExternally;
        }
        public CustomerStatusChangeMessage(DateTime date, string employeeName, CustomerStatus status, string accountNumber, string nickname, string avatar, bool isReplicatedExternally) : 
            this(date, employeeName, status, accountNumber, isReplicatedExternally)
        {
            Nickname = nickname;
            Avatar = avatar;
        }

        public CustomerStatusChangeMessage(string message) : base(message)
        {
            if (string.IsNullOrEmpty(message)) throw new ArgumentNullException(nameof(message));
        }

        protected override void InternalSerialize()
        {
            base.InternalSerialize();
            AddProperty((int)Status).
            AddProperty(AccountNumber).
            AddProperty(IsReplicatedExternally);
            if (string.IsNullOrWhiteSpace(Nickname))
                AddProperty(Empty);
            else
                AddProperty(Nickname);

            if (string.IsNullOrWhiteSpace(Avatar))
                AddProperty(Empty);
            else
                AddProperty(Avatar);
        }

        protected override void Deserialize(string [] message, out int fieldOrder)
        {
            base.Deserialize(message, out fieldOrder);
            Status = (CustomerStatus)int.Parse(message[fieldOrder++]);
            AccountNumber = message[fieldOrder++];
            IsReplicatedExternally = bool.Parse(message[fieldOrder++]);
            if (message[fieldOrder++][0] != Empty) Nickname = message[fieldOrder-1];
            if (message[fieldOrder++][0] != Empty) Avatar = message[fieldOrder-1];
        }
    }

    public sealed class RejectedCustomerMessage : CustomerBaseMessage
    {
        public string AccountNumber { private set; get; }
        public string Reason { private set; get;}

        public RejectedCustomerMessage(DateTime date, string employeeName, string accountNumber, string reason) : base(date, employeeName)
        {
            AccountNumber = accountNumber;
            Reason = reason;
        }

        public RejectedCustomerMessage(string message) : base(message)
        {
            if (string.IsNullOrEmpty(message)) throw new ArgumentNullException(nameof(message));

            var serializedMessage = KafkaMessage.Split(message);
            AccountNumber = serializedMessage[2];
            Reason = serializedMessage[3];
        }

        protected override void InternalSerialize()
        {
            base.InternalSerialize();
            AddProperty(AccountNumber).
            AddProperty(Reason);
        }

        protected override void Deserialize(string [] message, out int fieldOrder)
        {
            base.Deserialize(message, out fieldOrder);
            AccountNumber = message[fieldOrder++];
            Reason = message[fieldOrder++];
        }
    }

    public sealed class CustomerLogMessage : CustomerBaseMessage
    {
        public string Identifier { private set; get;}
        public string Log { private set; get;}

        public CustomerLogMessage(DateTime date, string employeeName, string identifier, string log) : base(date, employeeName)
        {
            Identifier = identifier;
            Log = log;
        }

        public CustomerLogMessage(string message) : base(message)
        {
            if (string.IsNullOrEmpty(message)) throw new ArgumentNullException(nameof(message));

            var serializedMessage = KafkaMessage.Split(message);
            Identifier = serializedMessage[2];
            Log = serializedMessage[3];
        }

        protected override void InternalSerialize()
        {
            base.InternalSerialize();
            AddProperty(Identifier).
            AddProperty(Log);
        }

        protected override void Deserialize(string [] message, out int fieldOrder)
        {
            base.Deserialize(message, out fieldOrder);
            Identifier = message[fieldOrder++];
            Log = message[fieldOrder++];
        }
    }

    [DataContract(Name = "CustomerDocumentBody")]
    public class CustomerDocumentBody
    {
        [DataMember(Name = "doc")]
        public IDictionary<string, object> Document { get; set; }
    }

    [DataContract(Name = "ScriptAndQueryBody")]
    public class ScriptAndQueryBody
    {
        [DataMember(Name = "script")]
        public ScriptBody Script { get; set; }
        [DataMember(Name = "query")]
        public QueryBody Query { get; set; }
    }

    [DataContract(Name = "ScriptBody")]
    public class ScriptBody
    {
        [DataMember(Name = "source")]
        public string Source { get; set; }
        [DataMember(Name = "lang")]
        public string Lang { get; set; }
        [DataMember(Name = "params")]
        public IDictionary<string, object> Params { get; set; } = new Dictionary<string, object>();
    }

    [DataContract(Name = "QueryBody")]
    public class QueryBody
    {
        [DataMember(Name = "match")]
        public IDictionary<string, object> Match { get; set; } = new Dictionary<string, object>();
    }
}
