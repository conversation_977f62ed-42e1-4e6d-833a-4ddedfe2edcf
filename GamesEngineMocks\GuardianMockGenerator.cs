﻿using GamesEngine;
using GamesEngine.Custodian;
using GamesEngine.Exchange;
using GamesEngine.Finance;
using Puppeteer.EventSourcing;
using System;
using town.connectors.drivers;
using static GamesEngine.Finance.PaymentChannels;
using static town.connectors.drivers.Result;

namespace GamesEngineMocks
{
	public class GuardianMockGenerator : Mock
	{
		Actor actor;
        readonly int storeId = 4;
		public String Perform(String script)
		{
			string result;
			try
			{
#pragma warning disable VSTHRD002 // Avoid problematic synchronous waits
				result = actor.PerformCmdAsync(script, IpAddress.DEFAULT, UserInLog.ANONYMOUS).Result;
#pragma warning restore VSTHRD002 // Avoid problematic synchronous waits
				result = CheckForAssert(result);
			}
			catch (Exception e)
			{
				if (e.InnerException == null)
				{
					throw e;
				}
				throw e.InnerException;

			}
			return result;
		}

		public GuardianMockGenerator(Actor actor)
		{
			this.actor = actor;
			var transactionTypesAsText = RiskAssignmentModel.TransactionTypesAsText();
			Perform($@"
				company = Company();
				if (!company.System.Coins.ExistsIsoCode('FP'))
				{{
					coin = company.System.Coins.Add(0, 'FP', 'FP', 2, 'F', 'free play', {CoinType.Digital});
					coin.Visible = true;
					coin.Enabled = true;
				}}
				if (!company.System.Coins.ExistsIsoCode('USD'))
				{{
					coin = company.System.Coins.Add(2, 'USD', '$', 2, '$', 'dollar', {CoinType.Fiat});
					coin.Visible = true;
					coin.Enabled = true;
				}}
				if (!company.System.Coins.ExistsIsoCode('BTC'))
				{{
					coin = company.System.Coins.Add(3, 'BTC', 'BTC', 8, '₿', 'bitcoin', {CoinType.Crypt});
					coin.Visible = true;
					coin.Enabled = true;
				}}
				if (!company.System.Coins.ExistsIsoCode('ETH'))
				{{
					company.System.Coins.Add(7, 'ETH', 'ETH', 8, 'Ξ', 'ethereum', {CoinType.Crypt});
				}}
				guardian = Guardian(company);
				processsors = guardian.PaymentProcessors();

				localhostDomain = company.Sales.CreateDomain(false, 1, 'localhost', {Agents.INSIDER});
				qaDomain = company.Sales.CreateDomain(false, 2, 'nodo.qatest.ncubo.com', {Agents.INSIDER});

				marketplace = Marketplace(company, 'CR');
				cartagoAgent = marketplace.AddAgent('Cartago');
				agent1 = marketplace.AddAgent('1');
				marketplace.AddAgent('San José');
				marketplace.AddAgent('Heredia');

				customSettings = CustomSettingsCollection(company);
				{{
					company.System.Entities.Add(1, 'Apolo');
					artemisEntity = company.System.Entities.Add(2, 'Artemis');
					zeusEntity = company.System.Entities.Add(3, 'Zeus');
					fieroEntity = company.System.Entities.Add(4, 'Fiero');
					fieroEntity.Visible = true;
					fieroEntity.Enabled = true;
					hadesEntity = company.System.Entities.Add(5, 'Hades');
					hadesEntity.Visible = true;
					hadesEntity.Enabled = true;
					consignmentEntity = company.System.Entities.Add(6, 'Consignment');
					consignmentEntity.Visible = true;
					consignmentEntity.Enabled = true;

					company.System.Tenants.Add(1, 'Apolo');
					artemisTenant = company.System.Tenants.Add(2, 'Artemis');
					zeusTenant = company.System.Tenants.Add(3, 'Zeus');
					insiderTenant = company.System.Tenants.Add(4, 'Insider');
					hadesTenant = company.System.Tenants.Add(5, 'Hades');
					hadesTenant.MakeCurrent();

					asiPM = company.System.PaymentMethods.Add(1, 'A.S.I.');
					dgsPM = company.System.PaymentMethods.Add(2, 'D.G.S.');
					dgsV2PM = company.System.PaymentMethods.Add(3, 'D.G.S. v2');
					credirCardPM = company.System.PaymentMethods.Add(4, 'Credit card');
					blockChainPM = company.System.PaymentMethods.Add(5, 'Blockchain');
					moneyTransferPM = company.System.PaymentMethods.Add(6, 'Money transfer');
					asiSimulatorPM = company.System.PaymentMethods.Add(7, 'A.S.I. Simulator');
					dsgSimulatorPM = company.System.PaymentMethods.Add(8, 'D.G.S. Simulator');
					pm = company.System.PaymentMethods.Add(9, '{PaymentMethod.Cash.ToString()}');
					pm.Visible = true;
					pm.Enabled = true;
					pm = company.System.PaymentMethods.Add(10, '{PaymentMethod.Bank.ToString()}');
					pm.Visible = true;
					pm.Enabled = true;
					pm = company.System.PaymentMethods.Add(11, '{PaymentMethod.Creditcard.ToString()}');
					pm.Visible = true;
					pm.Enabled = true;
					pm = company.System.PaymentMethods.Add(12, '{PaymentMethod.FinancialServices.ToString()}');
					pm.Visible = true;
					pm.Enabled = true;
					pm = company.System.PaymentMethods.Add(13, '{PaymentMethod.ThirdParty.ToString()}');
					pm.Visible = true;
					pm.Enabled = true;
					pm = company.System.PaymentMethods.Add(14, '{PaymentMethod.Secrets.ToString()}');
					pm.Visible = true;
					pm.Enabled = true;
					pm = company.System.PaymentMethods.Add(15, '{PaymentMethod.P2PTransfer.ToString()}');
					pm.Visible = true;
					pm.Enabled = true;
					pm.AddProvider(1,'MoneyGram');
					pm.AddProvider(2,'Rio Money Transfer');
					pm.AddProvider(3,'Sigue');
					pm.AddProvider(4,'Boss Revolution Money Transfer');
					pm.AddProvider(5,'Delgado Travel');
					pm.AddProvider(6,'Intermex International Money Transfer');
					pm.AddProvider(7,'Maxi Money Services');
					pm.AddProvider(8,'Barri Financial Group');
					pm.AddProvider(9,'La Nacional');
					pm.AddProvider(10,'Dinex');
					pm.AddProvider(11,'Remitly');

					depositTransactionType = company.System.TransactionTypes.Add(1, '{TransactionType.Deposit.ToString()}');
					depositTransactionType.Description = 'A sum of money placed in an account';
					withDrawalTransactionType = company.System.TransactionTypes.Add(2, '{TransactionType.Withdrawal.ToString()}');
					withDrawalTransactionType.Description = 'A sum of money take out of an account';
					transferTransactionType = company.System.TransactionTypes.Add(3, '{TransactionType.Transfer.ToString()}');
					transferTransactionType.Description = 'A sum of money is sent from one account to another';
					creditNoteTransactionType = company.System.TransactionTypes.Add(4, '{TransactionType.CreditNote.ToString()}');
					creditNoteTransactionType.Description = 'It is a document used by a vendor to inform the buyer of mistake on an order';
					debitNoteTransactionType = company.System.TransactionTypes.Add(5, '{TransactionType.DebitNote.ToString()}');
					debitNoteTransactionType.Description = 'It is a document used by a vendor to inform the buyer of current debt obligations';
					saleTransactionType = company.System.TransactionTypes.Add(6, '{TransactionType.Sale.ToString()}');
					saleTransactionType.Description = 'The exchange of a commodity for money';
					depositAndLockTransactionType = company.System.TransactionTypes.Add(7, '{TransactionType.Deposit.ToString()} then Lock');
					depositAndLockTransactionType.Description = 'Add and Lock money placed in an account';

					depositTransactionType.Visible = true;
					depositTransactionType.Enabled = true;
					withDrawalTransactionType.Visible = true;
					withDrawalTransactionType.Enabled = true;
					transferTransactionType.Visible = true;
					transferTransactionType.Enabled = true;
					debitNoteTransactionType.Visible = true;
					debitNoteTransactionType.Enabled = true;
					creditNoteTransactionType.Visible = true;
					creditNoteTransactionType.Enabled = true;
					saleTransactionType.Visible = true;
					saleTransactionType.Enabled = true;
					depositAndLockTransactionType.Visible = true;
					depositAndLockTransactionType.Enabled = true;

					cs = customSettings.AddFixedParameter(Now, 'CashierDriver.url', 'http://cashierapi:5000/'); 
					cs.Description = 'Url Cashier';
					cs = customSettings.AddFixedParameter(Now, 'CompanyBaseUrlServices', 'http://cashierapi:5000/'); 
					cs.Description = 'It is the base production url';
					cs = customSettings.AddFixedParameter(Now, 'TokenSystemId', '3aab83fb'); 
					cs.Description = 'App\'s token id';
					cs = customSettings.AddSecretParameter(Now, 'TokenSystemPassword', '38f845e977af7f2cd6313c5a9d35e4de7f353405100e7748ab15b410eb53d886');
					cs.Description = 'Password for token';
					cs = customSettings.AddFixedParameter(Now, 'SendWagers', true); 
					cs.Description = 'To send wagers to third party';
					cs = customSettings.AddFixedParameter(Now, 'CompanySystemId', 'N/A'); 
					cs.Description = 'company id';
					cs = customSettings.AddSecretParameter(Now, 'CompanySystemPassword', 'N/A'); 
					cs.Description = 'password';
					cs = customSettings.AddFixedParameter(Now, 'NeedsUniqueIdentifierForPaymentHub', 'PRODUCTION_DOES_NOT_NEED_IT'); 
					cs.Description = 'Setting';
					cs = customSettings.AddFixedParameter(Now, 'CompanyClerkId', 'Administrator'); 
					cs.Description = 'Clerk id';

					cs = customSettings.AddVariableParameter('Input$Provider'); 
					cs.Description = 'Consignment provider';
					cs = customSettings.AddVariableParameter('KYC$DOB'); 
					cs.Description = 'Birthdate from KYC';
					cs = customSettings.AddSecretParameter(Now, 'KYCUsername', 'testUser1');
					cs.Description = 'KYC Username for fields';
					cs = customSettings.AddSecretParameter(Now, 'KYCPassword', '12345');
					cs.Description = 'KYC Password for fields';
				}}

				{{
					profiles = guardian.Profiles();
					approvers = guardian.Approvers();

					Eval('profileId = '+profiles.NextProfileId()+';');
					generalManagerProfile = profiles.CreateProfile(itIsThePresent, profileId, 'General Manager');

					Eval('profileId = '+profiles.NextProfileId()+';');
					financialManagerProfile = profiles.CreateProfile(itIsThePresent, profileId, 'Financial Manager');

					Eval('profileId = '+profiles.NextProfileId()+';');
					ownerProfile = profiles.CreateOwnerProfile(itIsThePresent, profileId, 'Owner');

					Eval('profileId = '+profiles.NextProfileId()+';');
					agentProfile = profiles.CreateProfile(itIsThePresent, profileId, 'Agent/Cashier');

					Eval('approverId = ' + approvers.NextApproverId() + ';');
					approver = approvers.Create(itIsThePresent, approverId, '<EMAIL>');
					generalManagerProfile.Add(approver);
					Eval('approverId = ' + approvers.NextApproverId() + ';');
					approver = approvers.Create(itIsThePresent, approverId, '<EMAIL>');
					generalManagerProfile.Add(approver);
					approver = approvers.Get('<EMAIL>');
					agentProfile.Add(approver);
					approver = approvers.Get('<EMAIL>');
					agentProfile.Add(approver);
					Eval('approverId = ' + approvers.NextApproverId() + ';');
					approver = approvers.Create(itIsThePresent, approverId, '<EMAIL>');
					agentProfile.Add(approver);
					Eval('approverId = ' + approvers.NextApproverId() + ';');
					approver = approvers.Create(itIsThePresent, approverId, '<EMAIL>');
					agentProfile.Add(approver);
					Eval('approverId = ' + approvers.NextApproverId() + ';');
					approver = approvers.Create(itIsThePresent, approverId, '<EMAIL>');
					ownerProfile.Add(approver);
				}}

				{{
					riskRatingTypes = company.RiskRatingTypes();
					riskRatingType = riskRatingTypes.NewRiskRatingType('High');
					riskRatingType.Description = 'Transactions with a high risk rating require additional review and approval from a supervisor before being processed. This is due to the potential for significant financial loss or reputational damage if the transaction is fraudulent or unauthorized.';
					riskRatingType = riskRatingTypes.NewRiskRatingType('Medium');
					riskRatingType.Description = 'Transactions with a medium risk rating may be processed without supervisor approval, but additional due diligence is required. This may include verifying customer information, confirming the legitimacy of the transaction, and monitoring for suspicious activity.';
                    riskRatingType = riskRatingTypes.NewRiskRatingType('Low');
                    riskRatingType.Description = 'Transactions with a low risk rating can be processed without supervisor approval and with minimal due diligence. These are typically routine transactions with minimal risk of fraud or loss.';
				}}

				{{
					riskRatingTypes = company.RiskRatingTypes();
					riskRatingType = riskRatingTypes.FindRiskRatingType('High');

					riskRating = guardian.RiskRating();
					ranges = AmountRanges();
					amountRangeByCurrencies = ranges.CreateAmountRangeByCurrencies(riskRating, {{{transactionTypesAsText}}});
					amountRangeByCurrencies.SetAmountRange(Dollar(8000), Dollar(0.5), Now, 'N/A');
					amountRangeByCurrencies.SetAmountRange(Btc(6), Btc(0.00000523), Now, 'N/A');

					profiles = guardian.Profiles();
					profilesSelected = Profiles();
					profilesSelected.Add(profiles.SearchById(1));
					profilesSelected.Add(profiles.SearchById(2));

					riskAssignments = guardian.RiskAssignments();
					riskAssignments.StartupRiskAssignment(riskRatingType);
					riskAssignment = riskAssignments.NewRiskAssignment(riskRatingType, amountRangeByCurrencies, profilesSelected);
				}}

				{{
					riskRatingTypes = company.RiskRatingTypes();
					riskRatingType = riskRatingTypes.FindRiskRatingType('Medium');

					riskRating = guardian.RiskRating();
					ranges = AmountRanges();
					amountRangeByCurrencies = ranges.CreateAmountRangeByCurrencies(riskRating, {{{transactionTypesAsText}}});
					amountRangeByCurrencies.SetAmountRange(Dollar(1000), Dollar(0.5), Now, 'N/A');
					amountRangeByCurrencies.SetAmountRange(Btc(0.6), Btc(0.00000499), Now, 'N/A');

					profiles = guardian.Profiles();
					profilesSelected = Profiles();
					profilesSelected.Add(profiles.SearchById(1));
					profilesSelected.Add(profiles.SearchById(3));

					riskAssignments = guardian.RiskAssignments();
					riskAssignments.StartupRiskAssignment(riskRatingType);
					riskAssignment = riskAssignments.NewRiskAssignment(riskRatingType, amountRangeByCurrencies, profilesSelected);
				}}

				{{
					riskRatingTypes = company.RiskRatingTypes();
					riskRatingType = riskRatingTypes.FindRiskRatingType('Low');

					riskRating = guardian.RiskRating();
					ranges = AmountRanges();
					amountRangeByCurrencies = ranges.CreateAmountRangeByCurrencies(riskRating, {{{transactionTypesAsText}}});
					amountRangeByCurrencies.SetAmountRange(Dollar(100), Dollar(0.5), Now, 'N/A');
					amountRangeByCurrencies.SetAmountRange(Btc(0.05), Btc(0.00000499), Now, 'N/A');

					profiles = guardian.Profiles();
					profilesSelected = Profiles();
					profilesSelected.Add(profiles.SearchById(4));

					riskAssignments = guardian.RiskAssignments();
					riskAssignments.StartupRiskAssignment(riskRatingType);
					riskAssignment = riskAssignments.NewRiskAssignment(riskRatingType, amountRangeByCurrencies, profilesSelected);
				}}
			");
		}

		public GuardianMockGenerator GenerateSomeOperations()
        {
			Perform(@$"{{
					operations = guardian.Operations();
					processsor = processsors.SearchById('Hades-ThirdParty-Deposit-USD-1');
					Eval('operationNumber =' + operations.NewOperationNumber() + ';');
					operation = operations.CreateDeposit(itIsThePresent, Now, operationNumber, 1, '*********','H_T_D_USD_2', Dollar(100000), 'Fake deposit to cashier.', processsor.Group.Id, processsor, 1, 'localhost');
					operation.Execute(itIsThePresent, Now, {TransactionStatus.APPROVED.ToString()}, 'Fake deposit to cashier.', 'cris', {storeId});
				}}

				{{
					operations = guardian.Operations();
					processsor = processsors.SearchById('Hades-ThirdParty-DepositNoReferenceId-USD-1.1');
					Eval('operationNumber =' + operations.NewOperationNumber() + ';');
					operation = operations.CreateDeposit(itIsThePresent, Now, operationNumber, 2, '*********','F_T_D_BTC_13', Btc(100000), 'Fake deposit to cashier.', processsor.Group.Id, processsor, 1, 'localhost');
					operation.Execute(itIsThePresent, Now, {TransactionStatus.APPROVED.ToString()}, 'Fake deposit to cashier.', 'cris', {storeId});
				}}

				{{
					operations = guardian.Operations();
					processsor = processsors.SearchById('Hades-ThirdParty-Withdrawal-USD-1');
					Eval('operationNumber =' + operations.NewOperationNumber() + ';');
					operation = operations.CreateWithdrawal(itIsThePresent, Now, operationNumber, 3, '*********', Dollar(20), processsor.Group.Id, processsor, 1, 'localhost');
				}}

				{{
					operations = guardian.Operations();
					processsor = processsors.SearchById('Hades-ThirdParty-Withdrawal-USD-1');
					Eval('operationNumber =' + operations.NewOperationNumber() + ';');
					operation = operations.CreateWithdrawal(itIsThePresent, Now, operationNumber, 4, '*********', Btc(20), processsor.Group.Id, processsor, 1, 'localhost');
					fee = {nameof(MinerFee)}(Btc(0.00000110));operation.Add(fee);
				}}	

				{{
					operations = guardian.Operations();
					processsor = processsors.SearchById('Hades-ThirdParty-WithdrawalNoReferenceId-USD-1.1');
					Eval('operationNumber =' + operations.NewOperationNumber() + ';');
					operation = operations.CreateWithdrawal(itIsThePresent, Now, operationNumber, 5, '*********', Btc(0.00000500), processsor.Group.Id, processsor, 1, 'localhost');
					fee = {nameof(MinerFee)}(Btc(0.********));operation.Add(fee);
				}}	

				{{
					operations = guardian.Operations();
					processsor = processsors.SearchById('Hades-ThirdParty-WithdrawalNoReferenceId-USD-1.1');
					Eval('operationNumber =' + operations.NewOperationNumber() + ';');
					operation = operations.CreateWithdrawal(itIsThePresent, Now, operationNumber, 6, '*********', Btc(0.********), processsor.Group.Id, processsor, 1, 'localhost');
					fee = {nameof(MinerFee)}(Btc(0.********));operation.Add(fee);

					operations = guardian.Operations();
					processsor = processsors.SearchById('Hades-ThirdParty-WithdrawalNoReferenceId-USD-1.1');
					Eval('operationNumber =' + operations.NewOperationNumber() + ';');
					operation = operations.CreateWithdrawal(itIsThePresent, Now, operationNumber, 7, '*********', Btc(0.********), processsor.Group.Id, processsor, 1, 'localhost');
					fee = {nameof(MinerFee)}(Btc(0.********));operation.Add(fee);
				}}	

				{{
					accounts = guardian.Accounts();
					account = accounts.Search('H_T_D_USD_2');
					operations = guardian.Operations();
					changes = DisbursementsChanges(Now, guardian);
					changes.Add(itIsThePresent, 1, account.Number, Now, 5);
					changes.Add(itIsThePresent, 2, account.Number, Now, 5);
					changes.Add(itIsThePresent, 3, account.Number, Now, 5);
					changes.Add(itIsThePresent, 4, account.Number, Now, 5);
					withDrawal = operations.SearchWithdrawalByTransactionId(3);
					withdrawal.Apply(itIsThePresent, Now, changes);
				}}

				{{
					accounts = guardian.Accounts();
					account = accounts.Search('H_T_D_USD_2');
					operations = guardian.Operations();
					changes = DisbursementsChanges(Now, guardian);
					changes.Add(itIsThePresent, 1, account.Number, Now, 0.********);
					changes.Add(itIsThePresent, 2, account.Number, Now, 0.********);
					withDrawal = operations.SearchWithdrawalByTransactionId(5);
					withdrawal.Apply(itIsThePresent, Now, changes);
				}}

				{{
					accounts = guardian.Accounts();
					account = accounts.Search('F_T_D_BTC_13');
					operations = guardian.Operations();
					changes = DisbursementsChanges(Now, guardian);
					changes.Generate(itIsThePresent, account.Number, Now, 100, 0.********);
					withDrawal = operations.SearchWithdrawalByTransactionId(6);
					withdrawal.Apply(itIsThePresent, Now, changes);

					changes = DisbursementsChanges(Now, guardian);
					changes.Add(itIsThePresent, 1, account.Number, Now, 0.********);
					changes.Add(itIsThePresent, 2, account.Number, Now, 0.********);
					withDrawal = operations.SearchWithdrawalByTransactionId(7);
					withdrawal.Apply(itIsThePresent, Now, changes);
				}}

				{{
					operations = guardian.Operations();
					operations.Approve(itIsThePresent, Now, 5, '<EMAIL>');
					operations.Approve(itIsThePresent, Now, 5, '<EMAIL>');
				}}

				{{
					operations = guardian.Operations();
					operations.Approve(itIsThePresent, Now, 6, '<EMAIL>');
					operations.Approve(itIsThePresent, Now, 6, '<EMAIL>');

					profielsAuthorizedByTheOwner = Profiles();
					profielsAuthorizedByTheOwner.Add(guardian.Profiles().SearchByName('Financial Manager'));
					operations.Approve(itIsThePresent, Now, 6, '<EMAIL>', profielsAuthorizedByTheOwner);

					operations.Approve(itIsThePresent, Now, 7, '<EMAIL>');
					operations.Approve(itIsThePresent, Now, 7, '<EMAIL>');

					profielsAuthorizedByTheOwner = Profiles();
					profielsAuthorizedByTheOwner.Add(guardian.Profiles().SearchByName('Financial Manager'));
					operations.Approve(itIsThePresent, Now, 7, '<EMAIL>', profielsAuthorizedByTheOwner);
				}}

				{{
					operations = guardian.Operations();

					payments = PaymentsToBeExecuted(guardian);
					payments.Add(6, {{1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25,26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100}} );
					payments.Add(7, {{2}});
					payments.Execute(itIsThePresent, Now, 'cris', {storeId});
				}}


				{{
					operations = guardian.Operations();
					processsor = processsors.SearchById('Hades-ThirdParty-WithdrawalNoReferenceId-USD-1.1');
					Eval('operationNumber =' + operations.NewOperationNumber() + ';');
					operation = operations.CreateWithdrawal(itIsThePresent, Now, operationNumber, 8, '*********', Btc(0.********), processsor.Group.Id, processsor, 1, 'localhost');
					fee = {nameof(MinerFee)}(Btc(0.********));operation.Add(fee);

					accounts = guardian.Accounts();
					account = accounts.Search('F_T_D_BTC_13');
					operations = guardian.Operations();
					changes = DisbursementsChanges(Now, guardian);
					changes.Add(itIsThePresent, 1, account.Number, Now, 0.********);
					changes.Add(itIsThePresent, 2, account.Number, Now, 0.********);
					withDrawal = operations.SearchWithdrawalByTransactionId(8);
					withdrawal.Apply(itIsThePresent, Now, changes);

					operations = guardian.Operations();
					operations.Approve(itIsThePresent, Now, 8, '<EMAIL>');
					operations.Approve(itIsThePresent, Now, 8, '<EMAIL>');

					profielsAuthorizedByTheOwner = Profiles();
					profielsAuthorizedByTheOwner.Add(guardian.Profiles().SearchByName('Financial Manager'));
					operations.Approve(itIsThePresent, Now, 8, '<EMAIL>', profielsAuthorizedByTheOwner);

					payments = PaymentsToBeExecuted(guardian);
					payments.Add(8, {{1, 2}} );
					payments.Execute(itIsThePresent, Now, 'cris', {storeId});
				}}

				{{
					operations = guardian.Operations();
					processsor = processsors.SearchById('Hades-ThirdParty-WithdrawalNoReferenceId-USD-1.1');
					Eval('operationNumber =' + operations.NewOperationNumber() + ';');
					operation = operations.CreateWithdrawal(itIsThePresent, Now, operationNumber, 9, '*********', Btc(0.********), processsor.Group.Id, processsor, 1, 'localhost');
					fee = {nameof(MinerFee)}(Btc(0.********));operation.Add(fee);

					accounts = guardian.Accounts();
					account = accounts.Search('F_T_D_BTC_13');
					operations = guardian.Operations();
					changes = DisbursementsChanges(Now, guardian);
					changes.Add(itIsThePresent, 1, account.Number, Now, 0.********);
					changes.Add(itIsThePresent, 2, account.Number, Now, 0.********);
					withDrawal = operations.SearchWithdrawalByTransactionId(9);
					withdrawal.Apply(itIsThePresent, Now, changes);

					operations = guardian.Operations();
					operations.Approve(itIsThePresent, Now, 9, '<EMAIL>');
					operations.Approve(itIsThePresent, Now, 9, '<EMAIL>');
					
					profielsAuthorizedByTheOwner = Profiles();
					profielsAuthorizedByTheOwner.Add(guardian.Profiles().SearchByName('Financial Manager'));
					operations.Approve(itIsThePresent, Now, 9, '<EMAIL>', profielsAuthorizedByTheOwner);
				}}

				{{
					operations = guardian.Operations();
					processsor = processsors.SearchById('Hades-ThirdParty-WithdrawalNoReferenceId-USD-1.1');
					Eval('operationNumber =' + operations.NewOperationNumber() + ';');
					operation = operations.CreateWithdrawal(itIsThePresent, Now, operationNumber, 10, '*********', Btc(0.********), processsor.Group.Id, processsor, 1, 'localhost');
					fee = {nameof(MinerFee)}(Btc(0.********));operation.Add(fee);

					accounts = guardian.Accounts();
					account = accounts.Search('F_T_D_BTC_13');
					operations = guardian.Operations();
					changes = DisbursementsChanges(Now, guardian);
					changes.Add(itIsThePresent, 1, account.Number, Now, 0.********);
					changes.Add(itIsThePresent, 2, account.Number, Now, 0.********);
					changes.Add(itIsThePresent, 3, account.Number, Now, 0.********);
					changes.Add(itIsThePresent, 4, account.Number, Now, 0.********);
					changes.Add(itIsThePresent, 5, account.Number, Now, 0.********);
					changes.Add(itIsThePresent, 6, account.Number, Now, 0.********);
					changes.Add(itIsThePresent, 7, account.Number, Now, 0.********);
					changes.Add(itIsThePresent, 8, account.Number, Now, 0.********);
					changes.Add(itIsThePresent, 9, account.Number, Now, 0.********);
					changes.Add(itIsThePresent, 10, account.Number, Now, 0.********);
					changes.Add(itIsThePresent, 11, account.Number, Now, 0.********);
					withDrawal = operations.SearchWithdrawalByTransactionId(10);
					withdrawal.Apply(itIsThePresent, Now, changes);

					operations = guardian.Operations();
					operations.Approve(itIsThePresent, Now, 10, '<EMAIL>');
					operations.Approve(itIsThePresent, Now, 10, '<EMAIL>');
					
					profielsAuthorizedByTheOwner = Profiles();
					profielsAuthorizedByTheOwner.Add(guardian.Profiles().SearchByName('Financial Manager'));
					operations.Approve(itIsThePresent, Now, 10, '<EMAIL>', profielsAuthorizedByTheOwner);
					
					payments = PaymentsToBeExecuted(guardian);
					payments.Add(10, {{2, 4, 6, 1, 5}} );
					payments.Execute(itIsThePresent, Now, 'cris', {storeId});
				}}
			");
			return this;
		}
	}
}

