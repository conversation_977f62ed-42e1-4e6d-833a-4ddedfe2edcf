﻿using GamesEngine;
using GamesEngine.Settings;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Puppeteer.EventSourcing;
using System;
using System.IO;
using System.Runtime.Serialization;
using System.Security.Claims;
using System.Threading.Tasks;

namespace GamesEngine.Settings
{
	public abstract class RestAPIActor
	{
		protected readonly Actor actor;

		protected RestAPIActor(Actor actor)
		{
			if (actor == null) throw new ArgumentNullException(nameof(actor));
			this.actor = actor;
		}

		public string Name 
		{
			get 
			{
				return this.actor.Name;
			}
		}

		protected IActionResult DoQry(IpAddress ip, UserInLog user, HttpContext context, Func<Puppeteer.EventSourcing.Actor, Puppeteer.EventSourcing.IpAddress, Puppeteer.EventSourcing.UserInLog, System.Threading.ReaderWriterLockSlim, string> go)
		{
			try
			{
				string output = actor.DoQry(go);

				return new OkObjectResult(output);
			}
			catch (Exception e)
			{
				string message = (e.InnerException == null) ? e.Message : e.InnerException.Message;
				const string SERVER_ERROR_MESSAGE_TO_THE_END_USER = "An error has been detected. Our support team was notified and they are working on it.";
				try
				{
					string stack = (e.StackTrace != null) ? e.StackTrace : (e.InnerException != null) ? e.InnerException.StackTrace : "no stack";

					var httpMethod = "no http";
					var httpPath = "";
					var httpParameters = "";
					if (context != null)
					{
						httpMethod = context.Request.Method;
						httpPath = context.Request.Path;
						if (context.Request.Body.CanSeek)
						{
							context.Request.Body.Position = 0;
						}
						httpParameters = new StreamReader(context.Request.Body).ReadToEnd();
					}

					var errorInfo = $"Method:{httpMethod}, Path:{httpPath}, WithTheFollowingParameters:[{{{httpParameters}}}]";
					var comandoConError = actor.CommandLineError;
					ApiError error = new ApiError(message, stack, comandoConError, errorInfo);

					if (e.InnerException is GameEngineException || e.InnerException is ArgumentNullException)
					{
						var contentResult = new ContentResult
						{
							ContentType = "application/json",
							Content = JsonConvert.SerializeObject(error),
							StatusCode = 400
						};
						ErrorsSender.Send(actor.CurrentTimeStamp, e, true, ip, user, "GameEngineException: No script", contentResult.Content);
						return contentResult;
					}
					else
					{
						error.message = SERVER_ERROR_MESSAGE_TO_THE_END_USER;
						var contentResult = new ContentResult
						{
							ContentType = "application/json",
							Content = JsonConvert.SerializeObject(error),
							StatusCode = 500
						};
						ErrorsSender.Send(actor.CurrentTimeStamp, e, true, ip, user, "GameEngineException: No script", contentResult.Content);
						return contentResult;
					}
				}
				catch (Exception e2)
				{
					ErrorsSender.Send(actor.CurrentTimeStamp, e2, true, ip, user, "GameEngineException: No script", $"Exception processing an error in {actor.Name}");
					return new NotFoundErrorObjectResult();
				}
			}
		}
		
		protected IActionResult Perform(IpAddress ip, UserInLog user, bool isQuery, HttpContext context, string validation, string script)
		{
			if (String.IsNullOrEmpty(script)) return new NotFoundErrorObjectResult();

			try
			{
				if (!String.IsNullOrWhiteSpace(validation))
				{
					var checkOutput = actor.PerformChk(validation, ip, user);

					bool hayEWI = checkOutput.IndexOf("\"EWI\"") != -1;
					if (hayEWI)
					{
						ApiPrecondition error = new ApiPrecondition(checkOutput);

						var contentResult = new ContentResult
						{
							ContentType = "application/json",
							Content = JsonConvert.SerializeObject(error)
						};

						var preconditionResponse = new BadRequestObjectResult(contentResult);
						preconditionResponse.StatusCode = 428;

						return preconditionResponse;
					}
				}

				var output = isQuery ? actor.PerformQry(script, ip, user) : actor.PerformCmd(script, ip, user);

				return new OkObjectResult(output);
			}
			catch (Exception e)
			{
				string message = (e.InnerException == null) ? e.Message : e.InnerException.Message;
				const string SERVER_ERROR_MESSAGE_TO_THE_END_USER = "An error has been detected. Our support team was notified and they are working on it.";
				try
				{
					string stack = (e.StackTrace != null) ? e.StackTrace : (e.InnerException != null) ? e.InnerException.StackTrace : "no stack";

					var httpMethod = "no http";
					var httpPath = "";
					var httpParameters = "";
					if (context != null)
					{
						httpMethod = context.Request.Method;
						httpPath = context.Request.Path;
						if (context.Request.Body.CanSeek)
						{
							context.Request.Body.Position = 0;
						}
						httpParameters = new StreamReader(context.Request.Body).ReadToEnd();
					}

					var errorInfo = $"Method:{httpMethod}, Path:{httpPath}, WithTheFollowingParameters:[{{{httpParameters}}}]";
					var comandoConError = actor.CommandLineError;
					ApiError error = new ApiError(message, stack, comandoConError, errorInfo);

					if (e.InnerException is GameEngineException || e.InnerException is ArgumentNullException)
					{
						var contentResult = new ContentResult
						{
							ContentType = "application/json",
							Content = JsonConvert.SerializeObject(error),
							StatusCode = 400
						};
						ErrorsSender.SendWithActor(actor.CurrentTimeStamp, e, isQuery, ip, user, "GameEngineException: " + script, contentResult.Content, this.Name);
						return contentResult;
					}
					else
					{
						error.message = SERVER_ERROR_MESSAGE_TO_THE_END_USER;
						var contentResult = new ContentResult
						{
							ContentType = "application/json",
							Content = JsonConvert.SerializeObject(error),
							StatusCode = 500
						};
						ErrorsSender.SendWithActor(actor.CurrentTimeStamp, e, isQuery, ip, user, script, contentResult.Content, this.Name);
						return contentResult;
					}
				}
				catch (Exception e2)
				{
					ErrorsSender.SendWithActor(actor.CurrentTimeStamp, e2, isQuery, ip, user, script, "Exception processing an error in LinesBI", this.Name);
					return new NotFoundErrorObjectResult();
				}

			}
		}

		public string LockWhileNotSyncronized()
		{
			return actor.LockWhileNotSyncronized();
		}

		public void UnlockAndRunAlive()
		{
			actor.UnlockAndRunAlive();
		}

		public string ScriptEnEjecucion
		{
			get
			{
				return actor.ScriptEnEjecucion;
			}
		}

		protected void EventSourcingStorage(DatabaseType dbtype, string connectionString, string scriptBeforeRecovering, string needsUniqueIdentifierForPaymentHub)
		{
			Console.WriteLine("Starting to recover state from dairy");
			actor.EventSourcingStorage(dbtype, connectionString, scriptBeforeRecovering, needsUniqueIdentifierForPaymentHub);
			Console.WriteLine("State has been already recovered");
		}

		public static IpAddress SearchUserIp(HttpContext context)
		{
			if (context == null) return IpAddress.DEFAULT;

            string ip = context.Request.Headers["X-Forwarded-For"];
            if (!string.IsNullOrEmpty(ip))
            {
                try
                {
                    var extractedIp = IpAddress.GetIpFromHeader(ip);
                    return IpAddress.GenerateIpBasedOn(extractedIp);
                }
                catch (Exception e)
                {
                    ErrorsSender.Send($"Invalid IP: '{ip}'. {e.Message}", "Invalid IP Address");
                    return IpAddress.DEFAULT;
                }
            }

            ip = context.Request.Headers[IpAddress.HEADER_HTTP_INCAP_CLIENT_IP];
			if (!String.IsNullOrEmpty(ip))
			{
				try
				{
					var extractedIp = IpAddress.GetIpFromINCAPHedaer(ip);
					return IpAddress.GenerateIpBasedOn(extractedIp);
				}
				catch (Exception e)
				{
					ErrorsSender.Send($"Invalid ip: '{ip}'. {e.Message}", "Invalid IP Address");
					return IpAddress.DEFAULT;
				}
			}

			string xForwardedHeader = context.Request.Headers["X-Forwarded-For"];

			if (String.IsNullOrEmpty(xForwardedHeader) || xForwardedHeader.Equals("{}"))
			{
				ip = context.Connection.RemoteIpAddress.ToString();
			}
			else
			{
				ip = xForwardedHeader;

			}

			try
			{
				var extractedIp = IpAddress.GetIpFromForwarded(ip);
				return IpAddress.GenerateIpBasedOn(extractedIp);
			}
			catch (Exception e)
			{
				ErrorsSender.Send($"Invalid ip: '{ip}'. {e.Message}", "Invalid IP Address");
				return IpAddress.DEFAULT;
			}
		}

		public static UserInLog SearchUser(HttpContext context)
		{
			if (context == null) return UserInLog.ANONYMOUS;

			return UserInLog.GenerateUserBasedOn(
				(context.User.FindFirst(ClaimTypes.PrimarySid) != null) ?
				context.User.FindFirst(ClaimTypes.PrimarySid).Value : "");
		}

	}

	[DataContract]
	public class ApiError
	{
		public ApiError(string message, string stack, string comandoConError, string errorInfo)
		{
			this.message = message;
			this.stacktrace = stack;
			this.commandInfo = comandoConError;
			this.errorInfo = errorInfo;
		}

		public ApiError(string message)
		{
			this.message = message;
		}


		[DataMember]
		public string errorInfo { get; set; }
		[DataMember]
		public string commandInfo { get; set; }
		[DataMember]
		public string message { get; set; }
		[DataMember]
		public string type { get { return "error"; } }
		[DataMember]
		public string stacktrace { get; set; }
		[DataMember]
		public string requestInfo { get; set; }
	}

	[DataContract]
	public class ApiPrecondition
	{
		public ApiPrecondition(string message)
		{
			this.message = message;
		}

		[DataMember]
		public string message { get; set; }
	}
}
