using System;
using System.IO;
using System.Text;
using System.Threading.Tasks;
using GamesEngine.Settings;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;


namespace LottoBIAPI.Controllers
{
    public class ConsoleController : AuthorizeController
    {
        [HttpPost("console/command")]
        [Authorize(Roles = "devops")]
        public async Task<IActionResult> processCommandAsync()
        {
            string body = "";

            using (StreamReader reader = new StreamReader(Request.Body, Encoding.UTF8))
            {
                body = await reader.ReadToEndAsync();
            }

            if (body == null) return NotFound("Body is required");
            if (String.IsNullOrWhiteSpace(body)) return NotFound($"Parameter {nameof(body)} is required");

            var result = await LottoBIAPI.LottoBI.PerformCmdAsync(HttpContext, body);
            return result;
        }

        [HttpPost("console/query")]
        [Authorize(Roles = "devops")]
        public async Task<IActionResult> processQueryAsync()
        {
            string body = "";

            using (StreamReader reader = new StreamReader(Request.Body, Encoding.UTF8))
            {
                body = await reader.ReadToEndAsync();
            }

            if (body == null) return NotFound("Body is required");
            if (String.IsNullOrWhiteSpace(body)) return NotFound($"Parameter {nameof(body)} is required");

            var result = await LottoBIAPI.LottoBI.PerformQryAsync(HttpContext, body);
            return result;
        }

		[HttpGet("console/scriptEnEjecucion")]
		[AllowAnonymous]
		public IActionResult ScriptEnEjecucion()
		{
			IActionResult result;
			try
			{
				result = Ok("Script executed: " + LottoBIAPI.LottoBI.ScriptEnEjecucion);
			}
			catch
			{
				result = Ok("Vuelva a ejecutar el request");
			}
			return result;
		}

		[HttpGet("producer/stop")]
        [AllowAnonymous]
        public async Task<IActionResult> StopProducerAsync()
        {
			await Consumer.StopAllConsumerActiveInMemoryAsync();

			IProducer kafka = Integration.Kafka;
            if (kafka != null)
            {
                await Integration.Kafka.StopProducerAsync();
                return Ok();
            }
            return BadRequest("Kafka is not configured.");
        }

        [HttpGet("console/ping")]
        [AllowAnonymous]
        public IActionResult Ping()
        {
            return Ok("pong");
        }
    }
}

