﻿using Confluent.Kafka;
using ExternalServices;
using GamesEngine.Business;
using GamesEngine.Exchange;
using GamesEngine.Exchange.Persistance;
using GamesEngine.Finance;
using GamesEngine.Messaging;
using GamesEngine.PurchaseOrders;
using GamesEngine.RealTime;
using GamesEngine.SmartContracts;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Puppeteer.EventSourcing;
using Puppeteer.EventSourcing.Libraries;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Diagnostics;
using System.Globalization;
using System.Linq;
using System.Net;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using static GamesEngine.Settings.KafkaMessage;

namespace GamesEngine.Settings
{
	public sealed class Integration
	{
		public static readonly GamesEngine.Time.Timer TimersLoaded = new GamesEngine.Time.Timer();
		private static Task timerTask;

		public static IExchangeUsers ExchangeUsers;
		public static IExchangeRates ExchangeRates;
		public static IDeposits Deposits;
		public static IWithdrawals Withdrawals;
		public static ITransfers Transfers;
		public static IDomains Domains;

		public static Db Db { get; set; }
		public static DBDairy DbDairy { get; set; }
		public static IProducer Kafka { get; set; }
        public static Localization Localization { get; set; } = Localization.US;
        public static string MySQL { get; set; }
        public static string SqlServer { get; set; }
        public static string DbSelected { get; set; }
        public static int MockToStart { get; set; }
		public static bool UseDb { get; set; }
		public static bool UseAPM { get; set; }
		public static bool OnlyMovementsConsumersInitialized { get; set; }
		public static SmartContractsIntegration SmartContractsIntegration { get; set; }

#if DEBUG
		private static bool previousUseKafka;
		private static bool previousUseKafkaForAuto;

		public static void SavePreviousSettings(bool tempUseKafka, bool tempUseKafkaForAuto)
		{
			UseKafka = tempUseKafka;
			UseKafkaForAuto = tempUseKafkaForAuto;
		}

		public static void RestoreToDefaultSettings()
		{
			UseKafka = false;
			UseKafkaForAuto = false;
			Kafka = null;
			Db = null;
			UseDb = false;
			//PaymentProcessorsAndActionsByDomains.paymentProcessors = null;
			PlatformMonitor.monitor = null;
		}

		public static bool UseKafka { get; set; } = false;
#else
		public static bool UseKafka { get; set; } = true;
#endif
		public static bool UseKafkaForAuto { get; set; } = false;
		public static CultureInfo CultureInfoEnUS { get;} = new CultureInfo("en-US");


		private Integration() { }

		//public static CustomerServices CustomerServices = new CustomergServicesWithErrorManagement();

		public static void Configure(Prefix prefix, IConfigurationSection biIntegration)
		{
			ConfigureBI( prefix, biIntegration);
			ConfigureQueue(prefix, biIntegration);
		}

		public static void ConfigureQueue(Prefix prefix, IConfigurationSection biIntegration)
		{
			Kafka kafka = new Kafka();
			var kafkaConfiguration = biIntegration.GetSection("kafka");
			kafka.Server = kafkaConfiguration.GetValue<string>("server");
			kafka.TopicForTransacctions = kafkaConfiguration.GetValue<string>("topicForTransacctions");
			kafka.TopicForLottoGrading = kafkaConfiguration.GetValue<string>("topicForLottoGrading");
			kafka.TopicForDrawings = kafkaConfiguration.GetValue<string>("topicForDrawings");
			kafka.TopicForRecents = kafkaConfiguration.GetValue<string>("topicForRecents");
			kafka.TopicForDomains = kafkaConfiguration.GetValue<string>("topicForDomains");
			kafka.TopicForStoreRegistration = kafkaConfiguration.GetValue<string>("topicForStoreRegistration");
			kafka.TopicForCustomSettings = kafkaConfiguration.GetValue<string>("topicForCustomSettings");
			kafka.TopicForCatalog = kafkaConfiguration.GetValue<string>("topicForCatalog");
			string grades = kafkaConfiguration.GetValue<string>("topicForGrades");
			kafka.TopicForGradesWinners = grades + "Winners";
			kafka.TopicForAllGrades = grades;
			kafka.TopicForCustomers = kafkaConfiguration.GetValue<string>("topicForCustomers");
			kafka.TopicForNotifications = kafkaConfiguration.GetValue<string>("topicForNotifications");
			kafka.TopicForProfanity = kafkaConfiguration.GetValue<string>("topicForProfanity");
			kafka.TopicForScheduler = kafkaConfiguration.GetValue<string>("topicForScheduler");
			kafka.TopicForPrizes = kafkaConfiguration.GetValue<string>("topicForPrizes");
			kafka.TopicForMovements = kafkaConfiguration.GetValue<string>("topicForMovements");
			kafka.TopicForFragmentsCreation = kafkaConfiguration.GetValue<string>("topicForFragments");
			kafka.TopicForFragmentPaymentsForAll = kafkaConfiguration.GetValue<string>("topicForPayFragments");
			kafka.TopicForFragmentPaymentsForWinners = kafka.TopicForFragmentPaymentsForAll + "winners";
			kafka.TopicForDeposits = kafkaConfiguration.GetValue<string>("topicForDeposits");
			kafka.TopicForWithdrawals = kafkaConfiguration.GetValue<string>("topicForWithdrawals");
			kafka.TopicForChallenges = kafkaConfiguration.GetValue<string>("topicForChallenges");
			kafka.TopicForBalances = kafkaConfiguration.GetValue<string>("topicForBalances");
			kafka.TopicForIncomingOperations = kafkaConfiguration.GetValue<string>("topicForIncomingOperations");
			kafka.TopicForOutgoingOperations = kafkaConfiguration.GetValue<string>("topicForOutgoingOperations");
			kafka.TopicForGuardianOperations = kafkaConfiguration.GetValue<string>("topicForGuardian");
			kafka.TopicForKenoGrades = "KenoGrade";

			kafka.TopicForLinesScores = kafkaConfiguration.GetValue<string>("topicForLinesScores");
			kafka.TopicForLinesGrading = kafkaConfiguration.GetValue<string>("topicForLinesGrading");
			kafka.TopicForLinesETL = kafkaConfiguration.GetValue<string>("topicForLinesETL");

			kafka.Group = kafkaConfiguration.GetValue<string>("group");

			if (String.IsNullOrEmpty(kafka.Server)) throw new ArgumentNullException(nameof(kafka.Server));
			if (String.IsNullOrEmpty(kafka.TopicForLottoGrading)) throw new ArgumentNullException(nameof(kafka.TopicForLottoGrading));
			if (String.IsNullOrEmpty(kafka.TopicForDrawings)) throw new ArgumentNullException(nameof(kafka.TopicForDrawings));
			if (string.IsNullOrEmpty(kafka.TopicForRecents)) throw new ArgumentNullException(nameof(kafka.TopicForRecents));
			if (String.IsNullOrEmpty(kafka.TopicForDomains)) throw new ArgumentNullException(nameof(kafka.TopicForDomains));
			if (string.IsNullOrEmpty(kafka.TopicForStoreRegistration)) throw new ArgumentNullException(nameof(kafka.TopicForStoreRegistration));
			if (string.IsNullOrEmpty(kafka.TopicForCustomSettings)) throw new ArgumentNullException(nameof(kafka.TopicForCustomSettings));
			if (string.IsNullOrEmpty(kafka.TopicForCatalog)) throw new ArgumentNullException(nameof(kafka.TopicForCatalog));
			if (String.IsNullOrEmpty(grades)) throw new ArgumentNullException(nameof(grades));
			if (String.IsNullOrEmpty(kafka.TopicForCustomers)) throw new ArgumentNullException(nameof(kafka.TopicForCustomers));
			if (String.IsNullOrEmpty(kafka.TopicForNotifications)) throw new ArgumentNullException(nameof(kafka.TopicForNotifications));
			if (String.IsNullOrEmpty(kafka.TopicForProfanity)) throw new ArgumentNullException(nameof(kafka.TopicForProfanity));
			if (String.IsNullOrEmpty(kafka.TopicForScheduler)) throw new ArgumentNullException(nameof(kafka.TopicForScheduler));
			if (String.IsNullOrEmpty(kafka.TopicForPrizes)) throw new ArgumentNullException(nameof(kafka.TopicForPrizes));
			if (String.IsNullOrEmpty(kafka.TopicForMovements)) throw new ArgumentNullException(nameof(kafka.TopicForMovements));
			if (String.IsNullOrEmpty(kafka.TopicForFragmentsCreation)) throw new ArgumentNullException(nameof(kafka.TopicForFragmentsCreation));
			if (String.IsNullOrEmpty(kafka.TopicForFragmentPaymentsForAll)) throw new ArgumentNullException(nameof(kafka.TopicForFragmentPaymentsForAll));
			if (String.IsNullOrEmpty(kafka.TopicForDeposits)) throw new ArgumentNullException(nameof(kafka.TopicForDeposits));
			if (String.IsNullOrEmpty(kafka.TopicForWithdrawals)) throw new ArgumentNullException(nameof(kafka.TopicForWithdrawals));
			if (String.IsNullOrEmpty(kafka.TopicForChallenges)) throw new ArgumentNullException(nameof(kafka.TopicForChallenges));
			if (String.IsNullOrEmpty(kafka.TopicForBalances)) throw new ArgumentNullException(nameof(kafka.TopicForBalances));
			if (String.IsNullOrEmpty(kafka.TopicForIncomingOperations)) throw new ArgumentNullException(nameof(kafka.TopicForIncomingOperations));
			if (String.IsNullOrEmpty(kafka.TopicForOutgoingOperations)) throw new ArgumentNullException(nameof(kafka.TopicForOutgoingOperations));
			if (String.IsNullOrEmpty(kafka.TopicForGuardianOperations)) throw new ArgumentNullException(nameof(kafka.TopicForGuardianOperations));
			if (String.IsNullOrEmpty(kafka.TopicForGuardianInternalOperations)) throw new ArgumentNullException(nameof(kafka.TopicForGuardianInternalOperations));

			if (String.IsNullOrEmpty(kafka.TopicForLinesScores)) throw new ArgumentNullException(nameof(kafka.TopicForLinesScores));
			if (String.IsNullOrEmpty(kafka.TopicForLinesGrading)) throw new ArgumentNullException(nameof(kafka.TopicForLinesGrading));
			if (String.IsNullOrEmpty(kafka.TopicForLinesETL)) throw new ArgumentNullException(nameof(kafka.TopicForLinesETL));



			if (String.IsNullOrEmpty(kafka.Group)) throw new ArgumentNullException(nameof(kafka.Group));

			if (prefix != Prefix.NoTransacction)
			{
				string prefixNameForTopic;

				if (prefix == Prefix.withExchangePrefix)
				{
					prefixNameForTopic = KafkaMessage.EXCHANGE_CONSUMER_PREFIX;
				}
				else if (prefix == Prefix.withLottoPrefix)
				{
					prefixNameForTopic = KafkaMessage.TRANSACTION_LOTTO_PREFIX;
				}
				else if (prefix == Prefix.withMMPrefix)
				{
					prefixNameForTopic = KafkaMessage.TRANSACTION_MM_PREFIX;
				}
				else
				{
					throw new Exception("Prefix is not valid.");
				}

				kafka.TopicForTransacctions = prefixNameForTopic + kafkaConfiguration.GetValue<string>("topicForTransacctions");

				if (String.IsNullOrEmpty(kafka.TopicForTransacctions)) throw new ArgumentNullException(nameof(kafka.TopicForTransacctions));
			}

			if (String.IsNullOrEmpty(kafka.TopicForFragmentsCreation)) throw new ArgumentNullException(nameof(kafka.TopicForFragmentsCreation));

			var environment = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT");
			bool isDevelopmentEnvironment = environment == "Development";
			if (isDevelopmentEnvironment)
			{
				string hotsName = Dns.GetHostName();

				kafka.TopicForLottoGrading = hotsName + kafka.TopicForLottoGrading;
				kafka.TopicForTransacctions = hotsName + kafka.TopicForTransacctions;
				kafka.TopicForDrawings = hotsName + kafka.TopicForDrawings;
				kafka.TopicForRecents = hotsName + kafka.TopicForRecents;
				kafka.TopicForDomains = hotsName + kafka.TopicForDomains;
				kafka.TopicForStoreRegistration = hotsName + kafka.TopicForStoreRegistration;
				kafka.TopicForCustomSettings = hotsName + kafka.TopicForCustomSettings;
				kafka.TopicForCatalog = hotsName + kafka.TopicForCatalog;
				kafka.TopicForGradesWinners = hotsName + kafka.TopicForGradesWinners;
				kafka.TopicForAllGrades = hotsName + kafka.TopicForAllGrades;
				kafka.TopicForCustomers = hotsName + kafka.TopicForCustomers;
				kafka.TopicForNotifications = hotsName + kafka.TopicForNotifications;
				kafka.TopicForProfanity = hotsName + kafka.TopicForProfanity;
				kafka.TopicForScheduler = hotsName + kafka.TopicForScheduler;
				kafka.TopicForPrizes = hotsName + kafka.TopicForPrizes;
				kafka.TopicForMovements = hotsName + kafka.TopicForMovements;
				kafka.TopicForFragmentsCreation = hotsName + kafka.TopicForFragmentsCreation;
				kafka.TopicForFragmentPaymentsForAll = hotsName + kafka.TopicForFragmentPaymentsForAll;
				kafka.TopicForFragmentPaymentsForWinners = hotsName + kafka.TopicForFragmentPaymentsForWinners;
				kafka.TopicForDeposits = hotsName + kafka.TopicForDeposits;
				kafka.TopicForWithdrawals = hotsName + kafka.TopicForWithdrawals;
				kafka.TopicForChallenges = hotsName + kafka.TopicForChallenges;
				kafka.TopicForBalances = hotsName + kafka.TopicForBalances;
				kafka.TopicForIncomingOperations = hotsName + kafka.TopicForIncomingOperations;
				kafka.TopicForOutgoingOperations = hotsName + kafka.TopicForOutgoingOperations;
				kafka.TopicForGuardianOperations = hotsName + kafka.TopicForGuardianOperations;
				kafka.TopicForKenoGrades = hotsName + kafka.TopicForKenoGrades;

				kafka.TopicForLinesGrading = hotsName + kafka.TopicForLinesGrading;
				kafka.TopicForLinesScores = hotsName + kafka.TopicForLinesScores;
				kafka.TopicForLinesETL = hotsName + kafka.TopicForLinesETL;
			}

			Integration.Kafka = kafka;
		}

		public static void ConfigureBI(Prefix prefix, IConfigurationSection biIntegration)
		{
			CultureInfo.DefaultThreadCurrentCulture = CultureInfoEnUS;
			CultureInfo.DefaultThreadCurrentUICulture = CultureInfoEnUS;

			UseKafka = biIntegration.GetValue<bool>("useKafka");
			UseDb = biIntegration.GetValue<bool>("useDb");
            var timeZone = biIntegration.GetValue<string>("localization");
            Localization = string.IsNullOrWhiteSpace(timeZone) ? Localization.US : Enum.Parse<Localization>(timeZone, true);

            Debug.WriteLine($"Localization {Localization}");
            Debug.WriteLine($"UseKafka {UseKafka}");
			Debug.WriteLine($"UseDb {UseDb}");

			Db bd = new Db();
			var bdConfiguration = biIntegration.GetSection("DBHistorical");
			bd.MySQL = bdConfiguration.GetSection("ConnectionStrings").GetValue<string>("MySQL");
			bd.SQLServer = bdConfiguration.GetSection("ConnectionStrings").GetValue<string>("SQLServer");
			bd.DBSelected = bdConfiguration.GetValue<string>("DBSelected");

			if (String.IsNullOrEmpty(bd.MySQL)) throw new ArgumentNullException(nameof(bd.MySQL));
			if (String.IsNullOrEmpty(bd.SQLServer)) throw new ArgumentNullException(nameof(bd.SQLServer));
			if (String.IsNullOrEmpty(bd.DBSelected)) throw new ArgumentNullException(nameof(bd.DBSelected));

			Integration.Db = bd;
		}

		public static void ConfigureAPM(IConfiguration configuration)
		{
			var section = configuration.GetSection("ElasticApm");
			if (section == null) return;
			if (!bool.Parse(section["useApm"])) return;

			UseAPM = true;
		}
		public static async Task StopTimerAsync()
		{
			TimersLoaded.Stop();
			await Task.WhenAll(timerTask);
		}

		public static void ConfigureTimer()
		{
			timerTask = Task.Run(() => {
				TimersLoaded.Start();
			});
		}

		public static void ConfigurationForTest()
		{
			Db bd = new Db();
			Integration.Db = bd;
			Kafka kafka = new Kafka();
			Integration.Kafka = kafka;
			Integration.UseKafka = false;
			Integration.UseDb = false;
		}

		public static void ConfigurationForAutoTest(string connection, bool useKafkaForAuto = true)
		{
			Db bd = new Db();
			bd.DBSelected = "MySQL";
			bd.MySQL = connection;
			Integration.Db = bd;
			Kafka kafka = new Kafka();
			Integration.Kafka = kafka;
			Integration.UseKafka = false;
			Integration.UseDb = false;
			Integration.UseKafkaForAuto = useKafkaForAuto;

			kafka.Group = "games";
			kafka.TopicForLottoGrading = "LottoGrading";
			kafka.TopicForTransacctions = "Transaction";
			kafka.TopicForDrawings = "LottoDrawings";
			kafka.TopicForRecents = "Recents";
			kafka.TopicForProfanity = "Profanity";
			kafka.TopicForDomains = "LottoDomains";
			kafka.TopicForStoreRegistration = "StoreRegistration";
			kafka.TopicForCustomSettings = "CustomSettings";
			kafka.TopicForCatalog = "Catalog";
			kafka.TopicForAllGrades = "LottoGrades";
			kafka.TopicForCustomers = "Customers";
			kafka.TopicForNotifications = "Notifications";
			kafka.TopicForPrizes = "LottoPrizes";
			kafka.TopicForMovements = "Movements";
			kafka.TopicForFragmentsCreation = "Fragments";
			kafka.TopicForFragmentPaymentsForAll = "PayFragments";
			kafka.TopicForFragmentPaymentsForWinners = "PayFragments_Winners";
			kafka.TopicForDeposits = "Deposits";
			kafka.TopicForWithdrawals = "Withdrawals";
			kafka.TopicForChallenges = "Challenges";
			kafka.TopicForGuardianOperations = "GuardianOperations";

			kafka.TopicForLinesGrading = "LinesGrading";
			kafka.TopicForLinesScores = "LinesScores";
			kafka.TopicForLinesETL = "LinesETL";

			Consumer.DeleteConsumersDictionary();
		}

		private static int currentStoreId = DEFAULT_STORE_ID;
		private const int DEFAULT_STORE_ID = int.MinValue;
		public static int CurrentStoreId
		{
			get
			{
				if (currentStoreId == DEFAULT_STORE_ID) throw new GameEngineException("This actor does not have a current store");
				return currentStoreId;
			}
			set
			{
				if (value == DEFAULT_STORE_ID) throw new GameEngineException("Invalid store id");
				currentStoreId = value;
			}
		}

		static string currentStoreAlias;
		public static string CurrentStoreAlias
		{
			get
			{
				if (string.IsNullOrWhiteSpace(currentStoreAlias)) throw new GameEngineException("This actor does not have a current store");
				return currentStoreAlias;
			}
			set
			{
				if (string.IsNullOrWhiteSpace(value)) throw new GameEngineException("Invalid store alias");
				currentStoreAlias = value;
			}
		}

		static int currentTenantId = DefaultTenantId;
		const int DefaultTenantId = -1;
		public static int CurrentTenantId
		{
			get
			{
				if (currentTenantId == DefaultTenantId) throw new GameEngineException("This actor does not have a current tenant");
				return currentTenantId;
			}
			set
			{
				if (value == DefaultTenantId) throw new GameEngineException("Invalid tenant id");
				currentTenantId = value;
			}
		}

		static string currentTenantName;
		public static string CurrentTenantName
		{
			get
			{
				if (string.IsNullOrWhiteSpace(currentTenantName)) throw new GameEngineException("This actor does not have a current tenant");
				return currentTenantName;
			}
			set
			{
				if (string.IsNullOrWhiteSpace(value)) throw new GameEngineException("Invalid tenant name");
				currentTenantName = value;
			}
		}

		internal static HashSet<string> createdFragmentsTables = new HashSet<string>(StringComparer.InvariantCultureIgnoreCase);
		internal static HashSet<string> createdMovementsTables = new HashSet<string>(StringComparer.InvariantCultureIgnoreCase);
		internal static Dictionary<string, List<string>> createdMovementsByCurrenciesTables = new Dictionary<string, List<string>>(StringComparer.InvariantCultureIgnoreCase);
		public static void MarkFragmentsTableAsCreated(string atAddress)
		{
			if (string.IsNullOrWhiteSpace(atAddress)) throw new ArgumentNullException(nameof(atAddress));

			if (!createdFragmentsTables.Contains(atAddress))
			{
				Movements.Storage.CreateFragmentsTableIfNotExistsForThisAccount(atAddress);
				createdFragmentsTables.Add(atAddress);
			}
		}

		public static void MarkMovementsTableAsCreated(string atAddress, Coin currencyCode)
		{
			if (string.IsNullOrWhiteSpace(atAddress)) throw new ArgumentNullException(nameof(atAddress));
			if (currencyCode == null) throw new ArgumentNullException(nameof(currencyCode));

			List<string> currencies;
			createdMovementsByCurrenciesTables.TryGetValue(atAddress, out currencies);
			Movements.Storage.CreateMovementsTableIfNotExistsForThisAccountAndCurrency(atAddress, currencyCode);
			if (currencies == null || currencies.Count == 0)
			{
				createdMovementsByCurrenciesTables.Add(atAddress, new List<string>() { currencyCode.Iso4217Code });
			}
			else
			{
				currencies.Add(currencyCode.Iso4217Code);
			}
		}

		public static void MarkMovementsTableAsCreated(string currencyCode)
		{
			if (currencyCode == null) throw new ArgumentNullException(nameof(currencyCode));

			if (!createdMovementsTables.Contains(currencyCode))
			{
				Movements.Storage.CreateMovementsTableIfNotExistsForThisCurrency(currencyCode);
				createdMovementsTables.Add(currencyCode);
			}
		}

		public static TenantAndStoreInfo GetCurrentTenantAndStore(RestAPIActorAsync actor)
		{
			var result = actor.PerformQry(@"
                {{
					store = company.Sales.CurrentStore;
					print store.Name storeName;
					print store.Alias storeAlias;

					isAlreadyRegisteredTenantAndStore = company.IsAlreadyRegisteredTenantAndStore;
					print isAlreadyRegisteredTenantAndStore isAlreadyRegisteredTenantAndStore;
					if (isAlreadyRegisteredTenantAndStore)
					{{
						print company.System.Tenants.CurrentTenant.Id tenantId;
						print store.Id storeId;
					}}
                }}"
			);

			if (!(result is OkObjectResult)) throw new GameEngineException("Current store and tenant cannot be retrieved.");
			OkObjectResult o = (OkObjectResult)result;
			string json = o.Value.ToString();
			var tenantAndStoreInfo = JsonConvert.DeserializeObject<TenantAndStoreInfo>(json);
			CurrentStoreId = tenantAndStoreInfo.storeId;
			CurrentStoreAlias = tenantAndStoreInfo.storeAlias;
			CurrentTenantId = tenantAndStoreInfo.tenantId;
			return tenantAndStoreInfo;
		}

	}


	public sealed class Db
	{
		public Db() { }
		public string MySQL { get; set; }
		public string SQLServer { get; set; }
		public string DBSelected { get; set; }

	}

	public sealed class DBDairy
	{
		public DBDairy() { }
		public string MySQL { get; set; }
		public string SQLServer { get; set; }
		public string DBSelected { get; set; }

	}

    public enum Localization
    {
        US,
        KOR,
		MX
    };

    public abstract class KafkaMessage : Objeto
	{
		protected const char PROPERTIES_SEPARATOR = '\uFFFA';
		private readonly StringBuilder buffer = new StringBuilder();
		protected virtual void Deserialize(string[] message, out int fieldOrder)
		{
			fieldOrder = 0;
		}

		public const string TRANSACTION_LOTTO_PREFIX = "Lotto";
		public const string TRANSACTION_MM_PREFIX = "MM";
		public const string BI_CONSUMER_PREFIX = "Bi";
		public const string CASHIER_CONSUMER_PREFIX = "Cashier";
		public const string LOYALTY_CONSUMER_PREFIX = "Loyalty";
		public const string KYC_CONSUMER_PREFIX = "KyC";
		public const string EXCHANGE_CONSUMER_PREFIX = "Exchange";
		public const string GUARDIAN_CONSUMER_PREFIX = "Guardian";
		public const string LOTTO_CONSUMER_PREFIX = "Lotto";
        public const string WISHMAKER_CONSUMER_PREFIX = "Wishmaker";
        public const string LINES_CONSUMER_PREFIX = "Lines";
		public const string RESOURCES_CONSUMER_PREFIX = "Resources";
		public const string LINES_BI_CONSUMER_PREFIX = "LinesBI";
		public const string LINES_ETL_CONSUMER_PREFIX = "LinesETL";
		public const string LOTTO_BI_CONSUMER_PREFIX = "LottoBI";
		public const string BRACKETS_CONSUMER_PREFIX = "Brackets";
		public const string ACCUMULATOR_GRADING_LOTTO_PREFIX = "Accumulator_";
		public const string RESENDING_LOTTO_TICKETS_SUFFIX = "_ResendingTickets";

		public const string LOTTERY_DRAW_CONSUMER_PREFIX = "LotteryDraw";

		public const string CONVERSION_SPREAD_EXCHANGE_CONSUMER_PREFIX = "ConversionSpread";
		public const string TRANSACTION_EXCHANGE_CONSUMER_PREFIX = "TransactionExchange";
		public const string VOUCHER_URL_EXCHANGE_CONSUMER_PREFIX = "VoucherUrlExchange";

		public const string CUSTOMER_UPDATE_CONSUMER_PREFIX = "DraftedUpdatedCustomer";
		public const string CUSTOMER_CREATION_CONSUMER_PREFIX = "DraftedCreatedCustomer";
		public const string KYC_CUSTOMER_STATUS_CHANGE_CONSUMER_PREFIX = "KYC_CustomerStatusChanged";
		public const string REJECTED_CUSTOMER_CONSUMER_PREFIX = "RejectedCustomer";
		public const string CUSTOMER_LOG_CONSUMER_PREFIX = "CustomerLog";
		public const string EXCHANGE_CUSTOMER_STATUS_CONSUMER_PREFIX = "CustomerStatus";

		public const string SENDING_FRAGMENTS_CONSUMER_SUFFIX = "SendingFragmentsToAgent";
		public const string SENDING_UPDATED_FRAGMENTS_CONSUMER_SUFFIX = "SendingUpdatedFragmentsToAgent";
		public const string CATALOG_CALLBACK_CONSUMER_SUFFIX = "_Callback";

		public enum Prefix { withLottoPrefix, withMMPrefix, NoTransacction, withExchangePrefix };
        

        protected KafkaMessage()
		{
		}

		protected KafkaMessage(string message)
		{
			if (string.IsNullOrWhiteSpace(message)) throw new ArgumentNullException(nameof(message));
			
			string[] attributes = KafkaMessage.Split(message);

			Deserialize(attributes, out int _);
		}

		private bool internalSerializeWasExecuted = false;

		public string Serialize()
		{
			var wasPreviouslySerialized = buffer.Length == 0;
			if (wasPreviouslySerialized)
			{
				internalSerializeWasExecuted = false;
				InternalSerialize();
			}
			return Result();
		}

		public string SerializeBufferPreviouslyLoaded()
		{
			var wasPreviouslySerialized = buffer.Length == 0;
			if (wasPreviouslySerialized)
			{
				internalSerializeWasExecuted = false;
				InternalSerialize();
			}
			bool bufferPreviouslyLoaded = true;
			return Result(bufferPreviouslyLoaded);
		}

		protected virtual void InternalSerialize()
		{
			internalSerializeWasExecuted = true;
		}

		public KafkaMessage AddProperty(string message)
		{
			if (String.IsNullOrEmpty(message)) throw new ArgumentException(nameof(message));
			if (buffer.Length != 0)
			{
				buffer.Append(PROPERTIES_SEPARATOR);
			}
			buffer.Append(message);
			return this;
		}

		public KafkaMessage AddProperty(KafkaMessages messages)
		{
			if (messages == null) throw new ArgumentException(nameof(messages));

			if (buffer.Length != 0)
			{
				buffer.Append(PROPERTIES_SEPARATOR);
			}
			messages.SerializeInto(buffer);
			return this;
		}

		public KafkaMessage AddProperty(KafkaMessage message)
		{
			if (message == null) throw new ArgumentException(nameof(message));

			if (buffer.Length != 0)
			{
				buffer.Append(PROPERTIES_SEPARATOR);
			}
			buffer.Append(message.buffer);
			return this;
		}

		public KafkaMessage AddProperty(int message)
		{
			if (buffer.Length != 0)
			{
				buffer.Append(PROPERTIES_SEPARATOR);
			}
			buffer.Append(message);
			return this;
		}
		public KafkaMessage AddProperty(long message)
		{
			if (buffer.Length != 0)
			{
				buffer.Append(PROPERTIES_SEPARATOR);
			}
			buffer.Append(message);
			return this;
		}
		public KafkaMessage AddProperty(char message)
		{
			if (buffer.Length != 0)
			{
				buffer.Append(PROPERTIES_SEPARATOR);
			}
			buffer.Append(message);
			return this;
		}

		public KafkaMessage AddProperty(bool message)
		{
			if (buffer.Length != 0)
			{
				buffer.Append(PROPERTIES_SEPARATOR);
			}
			buffer.Append(message);
			return this;
		}

		public KafkaMessage AddProperty(decimal message)
		{
			if (buffer.Length != 0)
			{
				buffer.Append(PROPERTIES_SEPARATOR);
			}
			buffer.Append(message);
			return this;
		}

		public KafkaMessage AddProperty(double message)
		{
			if (buffer.Length != 0)
			{
				buffer.Append(PROPERTIES_SEPARATOR);
			}
			buffer.Append(message);
			return this;
		}

		public KafkaMessage AddProperty(DateTime message)
		{
			if (buffer.Length != 0)
			{
				buffer.Append(PROPERTIES_SEPARATOR);
			}
			buffer.Append(message.Year);
			buffer.Append(PROPERTIES_SEPARATOR);
			buffer.Append(message.Month);
			buffer.Append(PROPERTIES_SEPARATOR);
			buffer.Append(message.Day);
			buffer.Append(PROPERTIES_SEPARATOR);
			buffer.Append(message.Hour);
			buffer.Append(PROPERTIES_SEPARATOR);
			buffer.Append(message.Minute);
			buffer.Append(PROPERTIES_SEPARATOR);
			buffer.Append(message.Second);
			return this;
		}

		public static string[] Split(string value)
		{
			if (String.IsNullOrEmpty(value)) throw new ArgumentException(nameof(value));
			return value.Split(PROPERTIES_SEPARATOR, StringSplitOptions.RemoveEmptyEntries);
		}

		private string Result(bool bufferPreviouslyLoaded = false)
		{
			if (!bufferPreviouslyLoaded && !internalSerializeWasExecuted) throw new GameEngineException($"There are one or more base.InternalSerialize() pending to been invoke in this message.");
			return buffer.ToString();
		}

		public int Size()
		{
			return buffer.Length;
		}

		internal bool ItsEmpty()
		{
			return buffer.Length == 0;
		}

		internal KafkaMessage Clear()
		{
			buffer.Clear();
			return this;
		}

		protected class BisCompressor
		{
			private const char bisCharacter = (char)0xFFFD;
			private String previousCompressInvocation = "";
			private String previousDecompressInvocation = "";

			public string CompressBis(string currentText)
			{
				String result = CompressBis(currentText, this.previousCompressInvocation);
				this.previousCompressInvocation = currentText;
				return result;
			}

			private string CompressBis(string currentText, string previousText)
			{
				StringBuilder result = new StringBuilder();
				int len = Math.Min(currentText.Length, previousText.Length);
				int idx = 0;
				int repeated = 0;
				while (idx < len)
				{
					if (currentText[idx] == previousText[idx])
					{
						repeated++;
					}
					else
					{
						if (repeated != 0)
						{
							result.Append(bisCharacter);
							result.Append((char)repeated);
							repeated = 0;
						}
						result.Append(currentText[idx]);
					}
					idx++;
				}
				if (repeated != 0)
				{
					result.Append(bisCharacter);
					result.Append((char)repeated);
				}
				if (currentText.Length > previousText.Length)
				{
					result.Append(currentText.Substring(idx));
				}
				return result.ToString();
			}
		}
		protected class BisDeCompressor
		{
			private const char bisCharacter = (char)0xFFFD;
			private String previousCompressInvocation = "";
			private String previousDecompressInvocation = "";

			public string DecompressBis(string compressText)
			{
				String result = DecompressBis(compressText, this.previousDecompressInvocation);
				this.previousDecompressInvocation = result;
				return result;
			}

			private string DecompressBis(string compressText, string previousText)
			{
				StringBuilder result = new StringBuilder();
				int lenCompressed = compressText.Length;
				int lenPreviousMessage = previousText.Length;
				int idx = 0;
				while (idx < lenCompressed)
				{
					if (compressText[idx] == bisCharacter)
					{
						idx++;
						int count = compressText[idx];
						idx++;
						if (count > 0)
						{
							if (result.Length + count < lenPreviousMessage)
								result.Append(previousText.Substring(result.Length, count));
							else
								result.Append(previousText.Substring(result.Length));
						}
					}
					else
					{
						result.Append(compressText[idx]);
						idx++;
					}
				}
				return result.ToString();
			}

		}
	}

	public class KafkaMessagesBuffer : IDisposable
	{
		private KafkaMessages buffer;
		private bool itIsThePresent;
		private string topic;
		protected KafkaMessagesBuffer()
		{ 
		}
		internal Action<KafkaMessages> AfterDispose { get; set; }
		internal Action<KafkaMessages> BeforeDispose { get; set; }
		public Action<KafkaMessages> BeforeSendTheFirstMessage { get; set; }

		public KafkaMessagesBuffer(bool itIsThePresent, string topic)
		{
			if (!itIsThePresent) return;

			this.itIsThePresent = itIsThePresent;
			buffer = new KafkaMessages();
			this.topic = topic;
		}

		public KafkaMessagesBuffer(bool itIsThePresent, string topic, int max_size)
		{
			if (!itIsThePresent) return;

			this.itIsThePresent = itIsThePresent;
			buffer = new KafkaMessages(max_size);
			this.topic = topic;
		}

		public virtual void Send(KafkaMessage kafkamessage)
		{
			if (!itIsThePresent) return;
			if (buffer == null) throw new GameEngineException($"This {GetType().Name} its disposed.");
			
			string message = kafkamessage.SerializeBufferPreviouslyLoaded();

			if (string.IsNullOrEmpty(message)) throw new GameEngineException($"message {message} can not be empty or null.");

			if (buffer.ItsEnoughSpace(message))
			{
				if (!buffer.ItsNotEmpty() && BeforeSendTheFirstMessage != null) BeforeSendTheFirstMessage(buffer);
			}
			else
			{
				if (buffer.ItsNotEmpty()) Integration.Kafka.Send(this.itIsThePresent, topic, buffer);
				buffer.Clear();

				if (BeforeSendTheFirstMessage != null) BeforeSendTheFirstMessage(buffer);
			}

			buffer.Add(message);
		}

		public virtual void Dispose()
		{
			if (!itIsThePresent) return;
			if (buffer == null) throw new GameEngineException($"This {GetType().Name} its disposed.");

			if (BeforeDispose != null) BeforeDispose(buffer);

			if (buffer.ItsNotEmpty())
			{
				Integration.Kafka.Send(this.itIsThePresent, topic, buffer);
				buffer.Clear();
			}

			buffer = null;
			this.topic = null;

			if (AfterDispose != null) AfterDispose(buffer);
		}
	}
	public sealed class KafkaMessages
	{
		public const char MESSAGE_SEPARATOR = '\uFFFB';
		private StringBuilder buffer = new StringBuilder();
		private const int DEFAUL_MAX_SIZEE = 10240;
		private readonly int max_size;

		public KafkaMessages(int max_size)
		{
			this.max_size = max_size;
		}
		public KafkaMessages()
		{
			this.max_size = DEFAUL_MAX_SIZEE;
		}

		public static string[] Split(string value)
		{
			if (String.IsNullOrEmpty(value)) throw new ArgumentException(nameof(value));
			return value.Split(MESSAGE_SEPARATOR);
		}

		public static IEnumerable<string> Separate(string s)
		{
			if (String.IsNullOrEmpty(s)) throw new ArgumentException(nameof(s));

			int l = s.Length;
			int i = 0, j = s.IndexOf(MESSAGE_SEPARATOR, 0, l);
			if (j == -1) // No such substring
			{
				yield return s; // Return original and break
				yield break;
			}

			while (j != -1)
			{
				if (j - i > 0) // Non empty? 
				{
					yield return s.Substring(i, j - i); // Return non-empty match
				}
				i = j + 1;
				j = s.IndexOf(MESSAGE_SEPARATOR, i, l - i);
			}

			if (i < l) // Has remainder?
			{
				yield return s.Substring(i, l - i); // Return remaining trail
			}
		}

		public KafkaMessages Add(string message)
		{
			if (String.IsNullOrEmpty(message)) throw new ArgumentException(nameof(message));
			if (buffer.Length != 0)
			{
				buffer.Append(MESSAGE_SEPARATOR);
			}
			buffer.Append(message);
			return this;
		}

		public KafkaMessages Add(int message)
		{
			if (buffer.Length != 0)
			{
				buffer.Append(MESSAGE_SEPARATOR);
			}
			buffer.Append(message);
			return this;
		}

		public KafkaMessages Add(char message)
		{
			if (buffer.Length != 0)
			{
				buffer.Append(MESSAGE_SEPARATOR);
			}
			buffer.Append(message);
			return this;
		}

		public KafkaMessages Add(bool message)
		{
			if (buffer.Length != 0)
			{
				buffer.Append(MESSAGE_SEPARATOR);
			}
			buffer.Append(message);
			return this;
		}

		public bool ItsEnoughSpace(string message)
		{
			return message.Length + buffer.Length < max_size;
		}

		internal string Serialize()
		{
			return buffer.ToString();
		}

		public bool ItsNotEmpty()
		{
			return buffer.Length != 0;
		}

		public void Clear()
		{
			buffer.Clear();
		}

		internal void SerializeInto(StringBuilder externalBuffer)
        {
			if (externalBuffer == null) throw new ArgumentException(nameof(externalBuffer));

			externalBuffer.Append(buffer);
        }
	}

	public interface IProducer
	{
		string Server { get; set; }
		AutoOffsetReset OffSetReset { get; }
		string Group { get; }
		string TopicForMovements { get; }
		string TopicForDeposits { get; }
		string TopicForWithdrawals { get; }
		string TopicForCustomers { get; }
		string TopicForTransacctions { get; }
		string TopicForProfanity { get; }
		string TopicForGradesWinners { get; }
		string TopicForAllGrades { get; }
		string TopicForNotifications { get; }
		string TopicForScheduler { get; }
		string TopicForDrawings { get; }
        string TopicForRecents { get; }
        string TopicForDomains { get; }
		string TopicForStoreRegistration { get; }
		string TopicForCustomSettings { get; }
		string TopicForCatalog { get; }
		string TopicForPrizes { get; }
		string TopicForLottoGrading { get; }
        string TopicForChallenges { get; }

		void Send(bool itIsThePresent, string topic, KafkaMessage message, int partition = 0);
		void Send(bool itIsThePresent, string topic, KafkaMessages messages, int partition = 0);
		void Send(string topic, KafkaMessages messages);
		void Send(bool itIsThePresent, string topic, string message, int partition = 0);
		void Send(string topic, KafkaMessage message);
		void OffSetResetToLatest();
		Task StopProducerAsync();
		int GetPartition();
        int GetPartition(Localization localization);

        string TopicForFragmentsCreation { get; }
		string TopicForFragmentPaymentsForAll { get; }

		string TopicForFragmentPaymentsForWinners { get; }
		string TopicForBalances { get; }
		string TopicForIncomingOperations { get; }
		string TopicForOutgoingOperations { get; }
		string TopicForGuardianOperations { get; }
		string TopicForGuardianInternalOperations { get; }
		string TopicForKenoGrades { get; }
		
		string TopicForWithdrawalsDisbursementExecution { get { return TopicForWithdrawals + "Executions";  } }

		string TopicForFragmentsCreationCallback(int storeId);
		string TopicForFragmentPaymentsCallback(int storeId);
		string TopicForLinesGrading { get; }
		string TopicForLinesScores { get; }

		string TopicForLinesETL { get; }
		string TopicForLinesETLRseponse { get { return TopicForLinesETL + "Reponse";  } }
	}
	public sealed class Kafka : IProducer
	{
		private AutoOffsetReset offSetReset;
		private IProducer<Null, string> producerGlobal = null;
		private Action<DeliveryReport<Null, string>> handler = null;
		private bool producerIsStopped = false;

		public Kafka()
		{
			OffSetResetToEarliest();
		}
		public string Server { get; set; }
		public string TopicForLottoGrading { get; set; }
		public string Group { get; set; }
		public string TopicForTransacctions { get; set; }
		public string TopicForDrawings { get; set; }
        public string TopicForRecents { get; set; }
        public string TopicForDomains { get; set; }
		public string TopicForStoreRegistration { get; set; }
		public string TopicForCustomSettings { get; set; }
		public string TopicForCatalog { get; set; }
		public string TopicForGradesWinners { get; set; }
		public string TopicForAllGrades { get; set; }
		public string TopicForCustomers { get; set; }
		public string TopicForNotifications { get; set; }
		public string TopicForProfanity { get; set; }
		public string TopicForScheduler { get; set; }
		public string TopicForPrizes { get; set; }
		public string TopicForBalances { get; set; }
		public string TopicForIncomingOperations { get; set; }
		public string TopicForOutgoingOperations { get; set; }
		public AutoOffsetReset OffSetReset { get { return offSetReset == null ? throw new ArgumentException(nameof(offSetReset)) : offSetReset; } }
		public string TopicForFragmentsCreation { get; set; }
		public string TopicForFragmentPaymentsForAll { get; set; }
		public string TopicForFragmentPaymentsForWinners { get; set; }
		public string TopicForFragmentsCreationCallback(int storeId)
		{
			return TopicForFragmentsCreation + storeId.ToString();
		}
		public string TopicForFragmentPaymentsCallback(int storeId)
		{
			return TopicForFragmentPaymentsForAll + storeId.ToString();
		}
		public string TopicForMovements { get; set; }

		public string TopicForDeposits { get; set; }
		public string TopicForWithdrawals { get; set; }
		public string TopicForChallenges { get; set; }

		public string TopicForGuardianOperations { get; set; }
		public string TopicForKenoGrades{ get; set; }

		public string TopicForGuardianInternalOperations
		{
			get
			{
				return "internal" + TopicForGuardianOperations;
			}
		}
		public string TopicForLinesScores { get; set; }

		public string TopicForLinesGrading { get; set; }
		public string TopicForLinesETL { get; set; }
		

		public void OffSetResetToEarliest()
		{
			offSetReset = AutoOffsetReset.Earliest;
		}

		public void OffSetResetToLatest()
		{
			offSetReset = AutoOffsetReset.Latest;
		}

		public async Task StopProducerAsync()
		{
			var producer = GetOrCreateProducer();

			if (producerIsStopped) return;

			int pendingMessages = producer.Flush(TimeSpan.FromMilliseconds(1));
			Debug.WriteLine($"pendingMessages: { pendingMessages}");
			while (pendingMessages > 0)
			{
				Debug.WriteLine($"pendingMessages: { pendingMessages}");
				await Task.Delay(1000);
				pendingMessages = producer.Flush(TimeSpan.FromMilliseconds(1));
			}
			producer.Dispose();
			producerIsStopped = true;
		}

		public void Send(bool itIsThePresent, string topic, KafkaMessage message, int partition = 0)
		{
			if (Integration.UseKafkaForAuto) Send(topic, message);
			if (!itIsThePresent || !Integration.UseKafka) return;

			if (String.IsNullOrEmpty(topic)) throw new ArgumentException(nameof(topic));
			if (message == null) throw new ArgumentException(nameof(message));

			string messageSerializated = message.Serialize();

			if (String.IsNullOrEmpty(messageSerializated)) throw new ArgumentException("message.Serialize() can not be empty.");
			if (producerIsStopped)
			{
				var e = new Exception($"Error in producer. Message was not delivered. \nitIsThePresent:{itIsThePresent} \nproducerIsStopped:{producerIsStopped} \ntopic:{topic}  \nmessage:{messageSerializated}");
				ErrorsSender.Send(e, $"itIsThePresent:{itIsThePresent}", $"topic:{topic}", $"message:{messageSerializated}");
				throw e;
			}

			var producer = GetOrCreateProducer();
			try
			{
                var topicPart = new TopicPartition(topic, new Partition(partition));
                producer.Produce(topicPart, new Message<Null, string> { Value = messageSerializated }, handler);
            }
			catch (KafkaException e)
			{
				Debug.WriteLine($"failed to deliver message: {e.Message} [{e.Error.Code}]");
				ErrorsSender.Send(e, $"itIsThePresent:{itIsThePresent}", $"topic:{topic}", $"message:{messageSerializated}");

				throw;
			}

		}
		public void Send(bool itIsThePresent, string topic, string messageSerializated, int partition = 0)
		{
			if (!itIsThePresent || !Integration.UseKafka) return;

			if (String.IsNullOrEmpty(topic)) throw new ArgumentException(nameof(topic));
			if (String.IsNullOrEmpty(messageSerializated)) throw new ArgumentException("message.Serialize() can not be empty.");

			if (producerIsStopped)
			{
				var e = new Exception($"Error in producer. Message was not delivered. \nitIsThePresent:{itIsThePresent} \nproducerIsStopped:{producerIsStopped} \ntopic:{topic}  \nmessage:{messageSerializated}");
				ErrorsSender.Send(e, $"itIsThePresent:{itIsThePresent}", $"topic:{topic}", $"message:{messageSerializated}");
				throw e;
			}

			var producer = GetOrCreateProducer();
			try
			{
                var topicPart = new TopicPartition(topic, new Partition(partition));
                producer.Produce(topicPart, new Message<Null, string> { Value = messageSerializated }, handler);
            }
			catch (KafkaException e)
			{
				Debug.WriteLine($"failed to deliver message: {e.Message} [{e.Error.Code}]");
				ErrorsSender.Send(e, $"itIsThePresent:{itIsThePresent}", $"topic:{topic}", $"message:{messageSerializated}");

				throw;
			}

		}
		[Obsolete("USE THIS METHOD ONLY FOR AUTO")]
		public void Send(string topic, KafkaMessage message)
		{

			if (!Integration.UseKafkaForAuto) return;
			if (message == null) throw new ArgumentException(nameof(message));
			if (String.IsNullOrEmpty(topic)) throw new ArgumentException(nameof(topic));

			string messageSerializated = message.Serialize();

			var consumers = Consumer.ConsumersDictionary();
			consumers[topic].OnMessageBeforeCommit(messageSerializated);
		}

		[Obsolete("USE THIS METHOD ONLY FOR AUTO")]
		public void Send(string topic, KafkaMessages messages)
		{
			if (! Integration.UseKafkaForAuto) return;
			if (String.IsNullOrEmpty(topic)) throw new ArgumentException(nameof(topic));
			if (messages == null) throw new ArgumentException(nameof(messages));

			string messageSerializated = messages.Serialize();

			if (!Integration.UseKafkaForAuto) return;

			var consumers = Consumer.ConsumersDictionary();
			var t = consumers[topic];
			t.OnMessageBeforeCommit(messageSerializated);
		}

		private IProducer<Null, string> GetOrCreateProducer()
		{
			if (producerGlobal == null)
			{
				var config = new ProducerConfig { BootstrapServers = Integration.Kafka.Server };
				handler = r =>
				{
					if (r.Error.IsError)
					{
						ErrorsSender.Send($"topic:{r.Topic}\nKey:{r.Key}\nmessage:{r.Message}\nStatus:{r.Status}\nerrorCode:{r.Error.Code}\nisBrokerError:{r.Error.IsBrokerError}\nIsErrorFatal:{r.Error.IsFatal}\nIsLocalError:{r.Error.IsLocalError}",
							r.Error.Reason);
					}
				};
				producerGlobal = new ProducerBuilder<Null, string>(config).Build();
			}
			return producerGlobal;
		}

		public void Send(bool itIsThePresent, string topic, KafkaMessages messages, int partition = 0)
		{
			if (Integration.UseKafkaForAuto) Send(topic, messages);
			if (!itIsThePresent || !Integration.UseKafka) return;
			if (String.IsNullOrEmpty(topic)) throw new ArgumentException(nameof(topic));
			if (messages == null) throw new ArgumentException(nameof(messages));

			string messageSerializated = messages.Serialize();
			if (String.IsNullOrEmpty(messageSerializated)) throw new ArgumentException("message.Serialize() can not be empty.");
			if (producerIsStopped)
			{
				var e = new Exception($"Error in producer. Message was not delivered. \nitIsThePresent:{itIsThePresent} \nproducerIsStopped:{producerIsStopped} \ntopic:{topic}  \nmessage:{messageSerializated}");
				ErrorsSender.Send(e, $"itIsThePresent:{itIsThePresent}", $"topic:{topic}", $"message:{messageSerializated}");
				throw e;
			}

			var producer = GetOrCreateProducer();
			try
			{
                var topicPart = new TopicPartition(topic, new Partition(partition));
                producer.Produce(topicPart, new Message<Null, string> { Value = messageSerializated }, handler);
            }
			catch (KafkaException e)
			{
				Debug.WriteLine($"failed to deliver message: {e.Message} [{e.Error.Code}]");
				ErrorsSender.Send(e, $"itIsThePresent:{itIsThePresent}", $"topic:{topic}", $"message:{messageSerializated}");
				throw;
			}
		}

		public int GetPartition()
		{
			return (int)Integration.Localization;
		}

		public int GetPartition(Localization localization)
        {
            return (int)localization;
        }
	}

	public abstract class Consumer
	{
		private static int idCounter = 0;
		private int id;
		private string group;
		private string topic;
		private ConsumerConfig conf;
		private static readonly object myLock = new object();
		private static List<Consumer> activeConsumersInMemory = new List<Consumer>();
		private bool continuePollingTheTopic = true;
		private Task task;
		private static Dictionary<string, Consumer> consumers = new Dictionary<string, Consumer>();
		private CancellationTokenSource cts = new CancellationTokenSource();

		public Consumer(string group, string topic)
		{
			if (String.IsNullOrEmpty(group)) throw new Exception(nameof(group));
			if (String.IsNullOrEmpty(topic)) throw new Exception(nameof(topic));

			if (Integration.UseKafkaForAuto)
			{
				Consumer consumer;
				if (!consumers.TryGetValue(topic, out consumer))
				{
					consumers.Add(topic, this);
				}

				return;
			}

			this.group = group;
			this.topic = topic;

			this.conf = new ConsumerConfig
			{
				GroupId = group,
				BootstrapServers = Integration.Kafka.Server,
				// Note: The AutoOffsetReset property determines the start offset in the event
				// there are not yet any committed offsets for the consumer group for the
				// topic/partitions of interest. By default, offsets are committed
				// automatically, so in this example, consumption will only start from the
				// earliest message in the topic 'my-topic' the first time you run the program.
				// EnableAutoCommit = false,
				AutoOffsetReset = Integration.Kafka.OffSetReset
			};

			lock (myLock)
			{
				idCounter++;
				id = idCounter;
			}
		}
		public static Dictionary<string, Consumer> ConsumersDictionary()
		{
			if (consumers == null) throw new NullReferenceException("El diccionario de consumers no puede estar vacio");
			return consumers;
		}

		public static void DeleteConsumersDictionary()
		{
			if (consumers == null) throw new NullReferenceException("El diccionario de consumers no puede estar vacio");
			consumers.Clear();
		}

		public static async Task StopAllConsumerActiveInMemoryAsync()
		{
			List<Task> taskToWait = new List<Task>();
			foreach (Consumer consumer in activeConsumersInMemory)
			{
				consumer.Stop();
				taskToWait.Add(consumer.task);
			}
			await Task.WhenAll(taskToWait);
		}

		private void Stop()
		{
			continuePollingTheTopic = false;
			cts.Cancel();
		}

		public static async Task<IEnumerable<int>> ResetAsync(int[] consumerIds)
		{
			List<Consumer> consumersToBedeleted = new List<Consumer>();
			lock (myLock)
			{
				foreach (Consumer consumer in activeConsumersInMemory)
				{
					if (consumerIds.Contains(consumer.id))
					{
						consumersToBedeleted.Add(consumer);
					}
				}
			}

			List<Task> taskToWait = new List<Task>();
			List<int> result = new List<int>();
			foreach (Consumer consumer in consumersToBedeleted)
			{
				taskToWait.Add(consumer.ResetAsync());
				result.Add(consumer.id);
			}
			await Task.WhenAll(taskToWait);
			return result;
		}


		internal async Task ResetAsync()
		{
			continuePollingTheTopic = false;
			cts.Cancel();
			activeConsumersInMemory.Remove(this);
			if (task != null)
			{
				await Task.WhenAll(task);
				task = null;
			}
			continuePollingTheTopic = true;
			cts = new CancellationTokenSource();
			StartListening();
		}

		public static List<string> ConsumersInfo()
		{
			List<string> resukt = new List<string>();
			List<Consumer> consumersToBeSerialized = new List<Consumer>();
			lock (myLock)
			{
				consumersToBeSerialized = activeConsumersInMemory.ToList();
			}

			foreach (Consumer consumer in consumersToBeSerialized)
			{
				resukt.Add($"id:{consumer.id} class:{nameof(consumer)} topic:{consumer.topic}  group:{consumer.group}");
			}

			return resukt;
		}

		public void StartListening(int partitionNumber=0)
		{
			if (Integration.UseKafkaForAuto) return;

#pragma warning disable VSTHRD105 // Avoid method overloads that assume TaskScheduler.Current
			task = Task.Factory.StartNew(async () =>
#pragma warning restore VSTHRD105 // Avoid method overloads that assume TaskScheduler.Current
			{
				Console.WriteLine($"Consumer ID: {id} - {topic} its listening");
				ConsumerBuilder<Ignore, string> consumerBuilder = new ConsumerBuilder<Ignore, string>(conf)
						// Note: All handlers are called on the main .Consume thread.
						.SetErrorHandler((_, error) =>
						{
							ErrorsSender.Send($"Consumer ID: {id } \nTopic: {topic} \nErrorCode: {error.Code} \nErrorReason: { error.Reason} \nIsBrokerError: { error.IsBrokerError} \nIsLocalError: { error.IsLocalError}  \nError: { error}", "Kafka consumer.OnError");
							Debug.WriteLine($"Consumer ID: {id} OnError Topic: {topic} ErrorCode: {error.Code} ErrorReason: { error.Reason} IsBrokerError: { error.IsBrokerError} IsLocalError: { error.IsLocalError}  Error: { error}");
						})
						.SetStatisticsHandler((_, json) =>
						{
							Debug.WriteLine($"Consumer ID: {id} Statistics: {json}");
						})
						.SetPartitionsAssignedHandler((c, partitions) =>
						{
							Debug.WriteLine($"Consumer ID: {id} Assigned partitions: [{string.Join(", ", partitions)}], member id: {c.MemberId}");
                        })
						.SetPartitionsRevokedHandler((c, partitions) =>
						{
							Debug.WriteLine($"Consumer ID: {id} Revoked partitions: [{string.Join(", ", partitions)}]");
						});
				using (var consumer = consumerBuilder.Build())
				{
                    var topicPartition = new TopicPartition(topic, new Partition(partitionNumber));
                    consumer.Assign(topicPartition);

                    while (continuePollingTheTopic)
					{
						Message<Ignore, string> msg = null;
						ConsumeResult<Ignore, string> consumeResult = null;
						try
						{
							consumeResult = consumer.Consume(cts.Token);
							msg = consumeResult.Message;
							Debug.WriteLine($"Consumer ID: {id} offset:{consumeResult.TopicPartitionOffset}");
							OnMessageBeforeCommit(msg.Value);
							Debug.WriteLine($"After OnMessageBeforeCommitAsync Consumer ID: {id} offset:{consumeResult.TopicPartitionOffset}");

						}
						catch (OperationCanceledException e)
						{
							// nothing
							Debug.WriteLine($"Stoping Consumer ID: {id} for topi {topic}. ");
						}
						catch (Exception e)
						{
							StringBuilder errorDetails = new StringBuilder();
							if (consumeResult != null)
							{
								string offset = (consumeResult.Offset != null) ? "" + consumeResult.Offset.Value : "";
								string partition = (consumeResult.Partition != null) ? "" + consumeResult.Partition.Value : "";
								string topic = (consumeResult.TopicPartition != null) ? "" + consumeResult.TopicPartition.Topic : "";
								errorDetails.AppendLine($"Offset: {offset}");
								errorDetails.AppendLine($"Partition: {partition}");
								errorDetails.AppendLine($"TopicPartition: {topic}");
							}
							if (msg != null)
							{
								errorDetails.AppendLine($"Message: {msg.Value}");
							}
							string[] deatils = new string[]{
							$"Consumer ID: {id}",
							$"{errorDetails}",
							$"Topic: {topic}",
							$"Type: {this.GetType().FullName}"
						};

							ErrorsSender.Send(e,
								deatils);
							Debug.WriteLine($"Enviar error {e} \n {deatils}");
						}
					}
				}

			}, TaskCreationOptions.LongRunning);

			lock (myLock)
			{
				activeConsumersInMemory.Add(this);
			}
		}

		public abstract void OnMessageBeforeCommit(string msg);
	}

	public abstract class ProfanityConsumerValidator : Consumer
	{
		public ProfanityConsumerValidator(string group, string topic) : base(group, topic) { }

		public static string ValidateResponseFromWebProfanity(string message, string profanityUrl, ObjectResult result, string profanityResultText)
		{
			string jsonResult = (result as ObjectResult).Value.ToString();
			if (result is OkObjectResult)
			{
				JObject resultObject = JObject.Parse(jsonResult);
				bool resultTextIsEmpty = resultObject["rsp"] == null || resultObject["rsp"]["text"] == null || resultObject["rsp"]["err"] != null;
				if (!resultTextIsEmpty)
				{
					profanityResultText = resultObject["rsp"]["text"].ToString();
				}
				else
				{
					ErrorsSender.Send($"Message: {message} \n Url: {profanityUrl} \n Result : {jsonResult}", $"Profanity service return an empty result");
				}
			}
			else
			{
				ErrorsSender.Send($"Message: {message} \n Url: {profanityUrl} \n Result : {jsonResult}", $"Profanity service return an error");
			}

			return profanityResultText;
		}
	}

	public struct TenantAndStoreInfo
	{
		public bool isAlreadyRegisteredTenantAndStore { get; set; }
		public int tenantId { get; set; }
		public int storeId { get; set; }
		public string storeName { get; set; }
		public string storeAlias { get; set; }
	}

	public struct TenantInfo
	{
		public int id { get; set; }
		public string name { get; set; }
	}

	public struct TenantsAndStoreInfo
	{
		public List<TenantInfo> tenants { get; set; }
		public int storeId { get; set; }
		public string storeName { get; set; }
		public string storeAlias { get; set; }
	}
}
