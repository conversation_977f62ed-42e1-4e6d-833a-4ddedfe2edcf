﻿using GamesEngine.Bets;
using GamesEngine.Business;
using GamesEngine.Domains;
using GamesEngine.Finance;
using GamesEngine.Games.Lotto;
using GamesEngine.Location;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using System;
using System.Collections.Generic;
using System.Linq;
using static GamesEngine.PurchaseOrders.Stores;
using System.Text;
using static GamesEngine.Business.Company;
using GamesEngine.PurchaseOrders;
using GamesEngine.Gameboards.Lotto;
using GamesEngineTests.Unit_Tests.Games.Lotto;
using GamesEngine.Exchange;
using static GamesEngine.Finance.PaymentChannels;
using System.Diagnostics;
using GamesEngine.Settings;
using Unit.Games.Tools;
using GamesEngine.Custodian;
using System.Collections;
using static GamesEngine.Games.Lotto.Disincentives;

namespace GamesEngineTests.Unit_Tests.Business
{
	[TestClass]
	public class CompanyTest
	{
		[TestMethod]
		public void ExistsCustomer_Task3957()
		{
			Company company = new Company();
			company.Sales.CreateStore(1, "Test Store").MakeCurrent();
			CurrenciesTest.AddCurrencies();
			var accountNumber = "*********";
			Assert.IsFalse(company.ExistsCustomer(accountNumber));

			var customer = company.GetOrCreateCustomerById(accountNumber);
			Assert.IsTrue(company.ExistsCustomer(accountNumber));
		}

		[TestMethod]
		public void ExistsCustomerByIdentifier()
		{
			Company company = new Company();
			company.Sales.CreateStore(1, "Test Store").MakeCurrent();
			CurrenciesTest.AddCurrencies();
			var accountNumber = "*********";
			var id = "********";
			Assert.IsFalse(company.ExistsCustomerByIdentifier(id));

			var customer = company.GetOrCreateCustomerById(accountNumber);
			customer.Identifier = id;
			Assert.IsTrue(company.ExistsCustomerByIdentifier(id));
		}

		[TestMethod]
		public void PlayersWithAvatarUpdatedBetween_Task4271()
		{
			Company company = new Company();
			var store = company.Sales.CreateStore(1, "Test Store");
			CurrenciesTest.AddCurrencies();
			var player1 = company.GetOrCreateCustomerById("*********").Player;
			var player2 = company.GetOrCreateCustomerById("*********").Player;
			var player3 = company.GetOrCreateCustomerById("*********").Player;
			var kpis = company.MarketingKPIs();

			DateTime now = new DateTime(2019, 04, 17, 9, 10, 00);
			player1.UpdateAvatar("test.mobi/images/avatar.png", now);
			now = new DateTime(2019, 04, 18, 9, 10, 00);
			player1.UpdateAvatar("test.mobi/images/avatar1.png", now);
			player1.ApproveAvatar(now);
			now = new DateTime(2019, 04, 19, 9, 10, 00);
			player2.UpdateAvatar("test.mobi/images/avatar2.png", now);

			var report = kpis.PlayersWithAvatarUpdatedBetween("04-13-2019", "04-15-2019");
			Assert.AreEqual(3, report.TotalPlayers);
			Assert.AreEqual(0, report.Players.Count);
			Assert.AreEqual(0m, report.PercentageAvatarsUpdated);
			report = kpis.PlayersWithAvatarUpdatedBetween("04-13-2019", "04-19-2019");
			Assert.AreEqual(3, report.TotalPlayers);
			Assert.AreEqual(1, report.Players.Count);
			Assert.AreEqual(33.33m, report.PercentageAvatarsUpdated);

			var player = report.Players.ElementAt(0);
			Assert.AreEqual(player1, player);
			Assert.AreEqual("test.mobi/images/avatar1.png", player.Avatar.Path);

			var date = new DateTime(2019, 04, 18, 9, 10, 00);
			Assert.AreEqual(date, kpis.LastAvatarUpdateFor(player));
		}

		[TestMethod]
		public void PlayersWithoutAvatarAndWithAvatarUpdatedBetween_Task4271()
		{
			var company = new Company();
			var store = company.Sales.CreateStore(1, "Test Store");
			CurrenciesTest.AddCurrencies();
			var player1 = company.GetOrCreateCustomerById("*********").Player;
			var player2 = company.GetOrCreateCustomerById("*********").Player;
			var player3 = company.GetOrCreateCustomerById("*********").Player;
			var kpis = company.MarketingKPIs();

			var now = new DateTime(2019, 04, 17, 9, 10, 00);
			player1.UpdateAvatar("test.mobi/images/avatar.png", now);
			now = new DateTime(2019, 04, 18, 9, 10, 00);
			player1.UpdateAvatar("test.mobi/images/avatar1.png", now);
			player1.ApproveAvatar(now);
			now = new DateTime(2019, 04, 19, 9, 10, 00);
			player2.UpdateAvatar("test.mobi/images/avatar2.png", now);
			player2.ApproveAvatar(now);

			var report = kpis.PlayersWithoutAvatarAndWithAvatarUpdatedBetween("04-13-2019", "04-15-2019");
			Assert.AreEqual(3, report.TotalPlayers);
			Assert.AreEqual(1, report.Players.Count);
			Assert.AreEqual(33.33m, report.PercentageAvatarsUpdated);
			var player = report.Players.ElementAt(0);
			Assert.AreEqual(player3, player);

			report = kpis.PlayersWithoutAvatarAndWithAvatarUpdatedBetween("04-13-2019", "04-18-2019");
			Assert.AreEqual(3, report.TotalPlayers);
			Assert.AreEqual(2, report.Players.Count);
			Assert.AreEqual(66.67m, report.PercentageAvatarsUpdated);

			player = report.Players.ElementAt(0);
			Assert.AreEqual(player1, player);
			Assert.AreEqual("test.mobi/images/avatar1.png", player.Avatar.Path);

			var date = new DateTime(2019, 04, 18, 9, 10, 00);
			Assert.AreEqual(date, kpis.LastAvatarUpdateFor(player));
		}

		[TestMethod]
		public void FirstAddAcquisitionThenUpdateAvatar_Task4271()
		{
			var company = new Company();
			CurrenciesTest.AddCurrencies();
			var player = company.GetOrCreateCustomerById("*********").Player;
			var kpis = company.MarketingKPIs();

			var now = new DateTime(2019, 04, 17, 9, 00, 00);
			player.UpdateAvatar("test.mobi/images/avatar.png", now);
			Assert.IsFalse(kpis.HasRegisteredAvatarUpdateFor(player));

			now = new DateTime(2019, 04, 17, 9, 10, 00);
			now = new DateTime(2019, 04, 17, 9, 20, 00);
			player.UpdateAvatar("test.mobi/images/avatarN.png", now);
			player.ApproveAvatar(now);
			Assert.IsTrue(kpis.HasRegisteredAvatarUpdateFor(player));
			Assert.AreEqual(now, kpis.LastAvatarUpdateFor(player));
		}

		private const string MSG_COMBINATION_EXCEEDED = "Maximum amount per combination exceeded";

		[TestMethod]
		public void bug7257_description()
		{
			Company company = new Company();
			company.Sales.CreateStore(1, "Test Store").MakeCurrent();
			company.Accounting = new MockAccounting();
			var lotteries = company.Lotto900();

			DateTime now = new DateTime(2019, 04, 17, 9, 00, 00);
			State state = lotteries.GetOrCreateState("VA", "Virginia", now, "Bart");
			LotteryPick<Pick3> lottery = (LotteryPick<Pick3>)lotteries.GetOrCreateLottery(3, state);
			for (int i = 0; i <= 6; i++) lottery.Every(10, 20, i, now, "Bart");
			for (int i = 0; i <= 6; i++) lottery.Every(11, 20, i, now, "Bart");

			var state2 = lotteries.GetOrCreateState("GA", "Georgia", now, "Bart");
			var lottery2 = (LotteryPick<Pick3>)lotteries.GetOrCreateLottery(3, state2);
			for (int i = 0; i <= 6; i++) lottery2.Every(10, 20, i, now, "Bart");
			for (int i = 0; i <= 6; i++) lottery2.Every(11, 20, i, now, "Bart");

			Player player = company.GetOrCreateCustomerById("*********").Player;
			string states = "GA";
			string hours = "10:20 AM";
			string strIsPresentPlayerBeforeToCloseStore = "true";
			string strUseNextDate = "true";
			string dates = "04/17/2019";
			string withFireballs = "false";
			string numbers = "";
			string selectionMode = "MultipleInputSingleAmount";
			string pickNumber = "3";
			decimal ticketAmount = 0.5m;
			string includedNumbersForInput = "343,342";
			string gameType = "Boxed";

			Product ticketPick3StraightProd = lotteries.GetOrCreateProductById(1);
			ticketPick3StraightProd.Description = "Ticket pick 3 straight";
			ticketPick3StraightProd.Price = 1;
			Domain domain = company.Sales.CreateDomain(false, 1, "localhost", Agents.INSIDER);

			//#1
			NextDatesAccumulator nextDatesAccumulator = new NextDatesAccumulator(lotteries, domain, now, pickNumber, dates, states, hours, strIsPresentPlayerBeforeToCloseStore, strUseNextDate);
			ExcludeSubtickets excludedSubtickets = new ExcludeSubtickets();
			Customer customer = company.CustomerByPlayer(player);
			OrderCart myOrder = company.NewOrder(customer, 1, Coinage.Coin(Currencies.CODES.USD), 1);
			var toWinAccumulator = new ToWinAccumulator();
			var totalAndPossibleError = new TotalOrderByToWin();
			totalAndPossibleError = (TotalOrderByToWin)company.TotalTicketOrderAndExclusionsByToWin(domain, states, dates, numbers, selectionMode, ticketAmount, includedNumbersForInput, gameType, ticketPick3StraightProd, nextDatesAccumulator, toWinAccumulator, totalAndPossibleError, "USD", string.Empty);
			Assert.AreEqual(string.Empty, totalAndPossibleError.ErrorMessage);
			Assert.AreEqual(1.0m, totalAndPossibleError.TotalOrder);

			var firstToWinByDrawAndNumber = totalAndPossibleError.Tickets.First();
			Assert.AreEqual("Lotto GA 10:20:00 AM Pick 3 04/17/2019 Boxed", firstToWinByDrawAndNumber.description);

			//#2
			now = new DateTime(2019, 04, 17, 11, 00, 00);
			nextDatesAccumulator = new NextDatesAccumulator(lotteries, domain, now, pickNumber, dates, states, hours, strIsPresentPlayerBeforeToCloseStore, strUseNextDate);
			toWinAccumulator = new ToWinAccumulator();
			totalAndPossibleError = new TotalOrderByToWin();
			totalAndPossibleError = (TotalOrderByToWin)company.TotalTicketOrderAndExclusionsByToWin(domain, states, dates, numbers, selectionMode, ticketAmount, includedNumbersForInput, gameType, ticketPick3StraightProd, nextDatesAccumulator, toWinAccumulator, totalAndPossibleError, "USD", string.Empty);
			Assert.AreEqual(string.Empty, totalAndPossibleError.ErrorMessage);
			Assert.AreEqual(1.0m, totalAndPossibleError.TotalOrder);

			firstToWinByDrawAndNumber = totalAndPossibleError.Tickets.First();
			Assert.AreEqual("Lotto GA 10:20:00 AM Pick 3 04/18/2019 Boxed", firstToWinByDrawAndNumber.description);

			nextDatesAccumulator = new NextDatesAccumulator(lotteries, domain, now, pickNumber, dates, states, hours, strIsPresentPlayerBeforeToCloseStore, strUseNextDate);
			Order order = company.CreateTicketOrder(lotteries, player, states, dates, withFireballs, numbers, selectionMode, ticketAmount, includedNumbersForInput, excludedSubtickets, gameType, myOrder, ticketPick3StraightProd, nextDatesAccumulator, domain);
			company.AddOrders(myOrder);
			int orderNumber = order.Number;
			order = company.OrderByNumber(orderNumber);
			int theLowestBetId = 1;
			int theHighestBetId = 1;
			int ticketNumber = *********;
			company.PurchaseTickets(false, myOrder, now, ticketNumber, domain, theLowestBetId, theHighestBetId, order.Number);
			lotteries.CreateWagers(false, theLowestBetId, theHighestBetId, 1, 2, ticketNumber, order.Number, now);

		}

		[TestMethod]
		public void TotalTicketOrderAndExcludedByToWin_BuyingOneState()
		{
			Company company = new Company();
			company.Sales.CreateStore(1, "Test Store").MakeCurrent();
			company.Accounting = new MockAccounting();
			var lotteries = company.Lotto900();
			CurrenciesTest.AddCurrencies();

			DateTime now = new DateTime(2019, 04, 17, 9, 00, 00);
			State state = lotteries.GetOrCreateState("VA", "Virginia", now, "Bart");
			LotteryPick<Pick3> lottery = (LotteryPick<Pick3>)lotteries.GetOrCreateLottery(3, state);
			for (int i = 0; i <= 6; i++) lottery.Every(10, 20, i, now, "Bart");
			for (int i = 0; i <= 6; i++) lottery.Every(11, 20, i, now, "Bart");

			var state2 = lotteries.GetOrCreateState("GA", "Georgia", now, "Bart");
			var lottery2 = (LotteryPick<Pick3>)lotteries.GetOrCreateLottery(3, state2);
			for (int i = 0; i <= 6; i++) lottery2.Every(10, 20, i, now, "Bart");
			for (int i = 0; i <= 6; i++) lottery2.Every(11, 20, i, now, "Bart");

			Player player = company.GetOrCreateCustomerById("*********").Player;
			string states = "GA";
			string hours = "10:20 AM";
			string strIsPresentPlayerBeforeToCloseStore = "true";
			string strUseNextDate = "true";
			string dates = "04/17/2019";
			string withFireballs = "false";
			string numbers = "";
			string selectionMode = "MultipleInputSingleAmount";
			string pickNumber = "3";
			decimal ticketAmount = 100.75m;
			string includedNumbersForInput = "343,342";
			string gameType = "Boxed";

			Product ticketPick3StraightProd = lotteries.GetOrCreateProductById(1);
			ticketPick3StraightProd.Description = "Ticket pick 3 straight";
			ticketPick3StraightProd.Price = 1;
			Domain domain = company.Sales.CreateDomain(false, 1, "localhost", Agents.INSIDER);

			var drawDate = new DateTime(2019, 04, 17, 10, 20, 00);
			lotteries.Risks.Risk.UpdateToWin(int.Parse(pickNumber), 100m);

			//#1
			NextDatesAccumulator nextDatesAccumulator = new NextDatesAccumulator(lotteries, domain, now, pickNumber, dates, states, hours, strIsPresentPlayerBeforeToCloseStore, strUseNextDate);
			ExcludeSubtickets excludedSubtickets = new ExcludeSubtickets();
			Customer customer = company.CustomerByPlayer(player);
			OrderCart myOrder = company.NewOrder(customer, 1, Coinage.Coin(Currencies.CODES.USD), 1);
			var toWinAccumulator = new ToWinAccumulator();
			var totalAndPossibleError = new TotalOrderAndExclusionsByToWin(1);
			totalAndPossibleError = company.TotalTicketOrderAndExclusionsByToWin(domain, states, dates, numbers, selectionMode, ticketAmount, includedNumbersForInput, gameType, ticketPick3StraightProd, nextDatesAccumulator, toWinAccumulator, totalAndPossibleError, "USD", string.Empty);
			Assert.AreEqual(MSG_COMBINATION_EXCEEDED, totalAndPossibleError.ErrorMessage);
			Assert.AreEqual(0m, totalAndPossibleError.TotalOrder);
			Assert.IsFalse(totalAndPossibleError.IsValidToRetryPurchase);
			var excludeSubticketsInResponse = totalAndPossibleError.ExclusionsForSingleSelection.ToList();
			Assert.AreEqual(1, excludeSubticketsInResponse.Count);

			var excludeSubticket = excludeSubticketsInResponse.First();
			Assert.AreEqual("all", excludeSubticket.Subticket);
			Assert.AreEqual("GA", excludeSubticket.StateAbb);
			Assert.AreEqual("10:20 AM", excludeSubticket.HourFormattedAsText);
			Assert.AreEqual("4/17/2019", excludeSubticket.DateFormattedAsText);

			//#2
			lotteries.Risks.Risk.UpdateToWin(int.Parse(pickNumber), 100000m);
			ticketAmount = 100m;
			nextDatesAccumulator = new NextDatesAccumulator(lotteries, domain, now, pickNumber, dates, states, hours, strIsPresentPlayerBeforeToCloseStore, strUseNextDate);
			toWinAccumulator = new ToWinAccumulator();
			totalAndPossibleError = new TotalOrderAndExclusionsByToWin(1);
			totalAndPossibleError = company.TotalTicketOrderAndExclusionsByToWin(domain, states, dates, numbers, selectionMode, ticketAmount, includedNumbersForInput, gameType, ticketPick3StraightProd, nextDatesAccumulator, toWinAccumulator, totalAndPossibleError, "USD", string.Empty);
			Assert.AreEqual(string.Empty, totalAndPossibleError.ErrorMessage);
			Assert.AreEqual(200m, totalAndPossibleError.TotalOrder);

			nextDatesAccumulator = new NextDatesAccumulator(lotteries, domain, now, pickNumber, dates, states, hours, strIsPresentPlayerBeforeToCloseStore, strUseNextDate);
			Order order = company.CreateTicketOrder(lotteries, player, states, dates, withFireballs, numbers, selectionMode, ticketAmount, includedNumbersForInput, excludedSubtickets, gameType, myOrder, ticketPick3StraightProd, nextDatesAccumulator, domain);
			company.AddOrders(myOrder);
			int orderNumber = order.Number;
			order = company.OrderByNumber(orderNumber);
			int theLowestBetId = 1;
			int theHighestBetId = 1;
			int ticketNumber = *********;
			var marketplace = new Marketplace(company, "CR");
			var agent = marketplace.AddAgent("1");
			myOrder.Agent = (GamesEngine.Exchange.Agent)agent;
			company.PurchaseTickets(false, myOrder, now, ticketNumber, domain, theLowestBetId, theHighestBetId, order.Number);
			lotteries.CreateWagers(false, theLowestBetId, theHighestBetId, 1, 2, ticketNumber, order.Number, now);

			//#3
			lotteries.Risks.Risk.UpdateToWin(int.Parse(pickNumber), 100m);
			nextDatesAccumulator = new NextDatesAccumulator(lotteries, domain, now, pickNumber, dates, states, hours, strIsPresentPlayerBeforeToCloseStore, strUseNextDate);
			toWinAccumulator = new ToWinAccumulator();
			totalAndPossibleError = new TotalOrderAndExclusionsByToWin(1);
			totalAndPossibleError = company.TotalTicketOrderAndExclusionsByToWin(domain, states, dates, numbers, selectionMode, ticketAmount, includedNumbersForInput, gameType, ticketPick3StraightProd, nextDatesAccumulator, toWinAccumulator, totalAndPossibleError, "USD", string.Empty);
			Assert.AreEqual(MSG_COMBINATION_EXCEEDED, totalAndPossibleError.ErrorMessage);
			Assert.AreEqual(0m, totalAndPossibleError.TotalOrder);
			Assert.IsFalse(totalAndPossibleError.IsValidToRetryPurchase);
			excludeSubticketsInResponse = totalAndPossibleError.ExclusionsForSingleSelection.ToList();
			Assert.AreEqual(1, excludeSubticketsInResponse.Count);

			excludeSubticket = excludeSubticketsInResponse.First();
			Assert.AreEqual("all", excludeSubticket.Subticket);
			Assert.AreEqual("GA", excludeSubticket.StateAbb);
			Assert.AreEqual("10:20 AM", excludeSubticket.HourFormattedAsText);
			Assert.AreEqual("4/17/2019", excludeSubticket.DateFormattedAsText);

			//#4
			lotteries.Risks.Risk.UpdateToWin(int.Parse(pickNumber), 100000m);
			now = new DateTime(2019, 04, 17, 10, 00, 00);
			string hours2 = "11:20 AM";
			string dates2 = "04/17/2019";
			string withFireballs2 = "false";
			string numbers2 = "4,5,6";
			string selectionMode2 = "balls";
			ticketNumber = *********;

			NextDatesAccumulator nextDatesAccumulator2 = new NextDatesAccumulator(lotteries, domain, now, pickNumber, dates2, states, hours2, strIsPresentPlayerBeforeToCloseStore, strUseNextDate);
			OrderCart myOrder2 = company.NewOrder(customer, 2, Coinage.Coin(Currencies.CODES.USD), 2);
			toWinAccumulator = new ToWinAccumulator();
			totalAndPossibleError = new TotalOrderAndExclusionsByToWin(1);
			totalAndPossibleError = company.TotalTicketOrderAndExclusionsByToWin(domain, states, dates2, numbers2, selectionMode2, ticketAmount, includedNumbersForInput, gameType, ticketPick3StraightProd, nextDatesAccumulator2, toWinAccumulator, totalAndPossibleError, "USD", string.Empty);
			Assert.AreEqual(string.Empty, totalAndPossibleError.ErrorMessage);
			Assert.AreEqual(100m, totalAndPossibleError.TotalOrder);

			nextDatesAccumulator2 = new NextDatesAccumulator(lotteries, domain, now, pickNumber, dates2, states, hours2, strIsPresentPlayerBeforeToCloseStore, strUseNextDate);
			Order order2 = company.CreateTicketOrder(lotteries, player, states, dates2, withFireballs2, numbers2, selectionMode2, ticketAmount, string.Empty, excludedSubtickets, gameType, myOrder2, ticketPick3StraightProd, nextDatesAccumulator2, domain);
			company.AddOrders(myOrder2);
			int orderNumber2 = order2.Number;
			order2 = company.OrderByNumber(orderNumber2);
			myOrder2.Agent = (GamesEngine.Exchange.Agent)agent;
			company.PurchaseTickets(false, myOrder2, now, ticketNumber, domain, theLowestBetId, theHighestBetId, order.Number);
			lotteries.CreateWagers(false, theLowestBetId, theHighestBetId, 1, 2, ticketNumber, order.Number, now);
		}

		[TestMethod]
		public void TotalTicketOrderAndExcludedByToWin_BuyingTwoStatesWithExcludesNumbers()
		{
			Company company = new Company();
			company.Sales.CreateStore(1, "Test Store").MakeCurrent();
			company.Accounting = new MockAccounting();
			var lotteries = company.Lotto900();
			CurrenciesTest.AddCurrencies();

			DateTime now = new DateTime(2019, 04, 17, 9, 00, 00);
			State state1 = lotteries.GetOrCreateState("VA", "Virginia", now, "Bart");
			LotteryPick<Pick3> lottery = (LotteryPick<Pick3>)lotteries.GetOrCreateLottery(3, state1);
			for (int i = 0; i <= 6; i++) lottery.Every(10, 20, i, now, "Bart");
			for (int i = 0; i <= 6; i++) lottery.Every(11, 20, i, now, "Bart");

			var state2 = lotteries.GetOrCreateState("GA", "Georgia", now, "Bart");
			var lottery2 = (LotteryPick<Pick3>)lotteries.GetOrCreateLottery(3, state2);
			for (int i = 0; i <= 6; i++) lottery2.Every(10, 20, i, now, "Bart");
			for (int i = 0; i <= 6; i++) lottery2.Every(11, 20, i, now, "Bart");

			Player player = company.GetOrCreateCustomerById("*********").Player;
			string states = "VA,GA";
			string hours = "10:20 AM,10:20 AM";
			string strIsPresentPlayerBeforeToCloseStore = "true,true";
			string strUseNextDate = "true,true";
			string dates = "04/17/2019";
			string withFireballs = "false,false";
			string numbers = "";
			string selectionMode = "MultipleInputSingleAmount";
			string pickNumber = "3";
			decimal ticketAmount = 100.5m;
			string includedNumbersForInput = "343,342";
			string gameType = "Boxed";

			Product ticketPick3StraightProd = lotteries.GetOrCreateProductById(1);
			ticketPick3StraightProd.Description = "Ticket pick 3 straight";
			ticketPick3StraightProd.Price = 1;
			Domain domain = company.Sales.CreateDomain(false, 1, "localhost", Agents.INSIDER);

			var drawDate = new DateTime(2019, 04, 17, 10, 20, 00);
			lotteries.Risks.Risk.UpdateToWin(int.Parse(pickNumber), 100m);

			//#1
			NextDatesAccumulator nextDatesAccumulator = new NextDatesAccumulator(lotteries, domain, now, pickNumber, dates, states, hours, strIsPresentPlayerBeforeToCloseStore, strUseNextDate);
			var excludedSubtickets = new ExcludeSubtickets();
			excludedSubtickets.Add(state1, "10:20 AM", "04/17/2019", "343");
			excludedSubtickets.Add(state2, "10:20 AM", "04/17/2019", "342");

			Customer customer = company.CustomerByPlayer(player);
			OrderCart myOrder = company.NewOrder(customer, 1, Coinage.Coin(Currencies.CODES.USD), 1);
			var toWinAccumulator = new ToWinAccumulator();
			var totalAndPossibleError = new TotalOrderAndExclusionsByToWin(1);
			totalAndPossibleError = company.TotalTicketOrderAndExclusionsByToWin(domain, states, dates, numbers, selectionMode, ticketAmount, includedNumbersForInput, gameType, excludedSubtickets, ticketPick3StraightProd, nextDatesAccumulator, toWinAccumulator, totalAndPossibleError, "USD", string.Empty);
			Assert.AreEqual(MSG_COMBINATION_EXCEEDED, totalAndPossibleError.ErrorMessage);
			Assert.AreEqual(0m, totalAndPossibleError.TotalOrder);
			var excludeSubticketsInResponse = totalAndPossibleError.ExclusionsForSingleSelection.ToList();
			Assert.AreEqual(2, excludeSubticketsInResponse.Count);

			var excludeSubticket1 = excludeSubticketsInResponse.First();
			Assert.AreEqual("all", excludeSubticket1.Subticket);
			Assert.AreEqual("VA", excludeSubticket1.StateAbb);
			Assert.AreEqual("10:20 AM", excludeSubticket1.HourFormattedAsText);
			Assert.AreEqual("4/17/2019", excludeSubticket1.DateFormattedAsText);

			var excludeSubticket2 = excludeSubticketsInResponse.Last();
			Assert.AreEqual("all", excludeSubticket2.Subticket);
			Assert.AreEqual("GA", excludeSubticket2.StateAbb);
			Assert.AreEqual("10:20 AM", excludeSubticket2.HourFormattedAsText);
			Assert.AreEqual("4/17/2019", excludeSubticket2.DateFormattedAsText);

			//#2
			lotteries.Risks.Risk.UpdateToWin(int.Parse(pickNumber), 100000m);
			excludedSubtickets = new ExcludeSubtickets();
			excludedSubtickets.Add(state1, "10:20 AM", "04/17/2019", "343");
			excludedSubtickets.Add(state2, "10:20 AM", "04/17/2019", "342");
			ticketAmount = 60.5m;
			nextDatesAccumulator = new NextDatesAccumulator(lotteries, domain, now, pickNumber, dates, states, hours, strIsPresentPlayerBeforeToCloseStore, strUseNextDate);
			toWinAccumulator = new ToWinAccumulator();
			totalAndPossibleError = new TotalOrderAndExclusionsByToWin(1);
			totalAndPossibleError = company.TotalTicketOrderAndExclusionsByToWin(domain, states, dates, numbers, selectionMode, ticketAmount, includedNumbersForInput, gameType, excludedSubtickets, ticketPick3StraightProd, nextDatesAccumulator, toWinAccumulator, totalAndPossibleError, "USD", string.Empty);
			Assert.AreEqual(string.Empty, totalAndPossibleError.ErrorMessage);
			Assert.AreEqual(121m, totalAndPossibleError.TotalOrder);

			nextDatesAccumulator = new NextDatesAccumulator(lotteries, domain, now, pickNumber, dates, states, hours, strIsPresentPlayerBeforeToCloseStore, strUseNextDate);
			Order order = company.CreateTicketOrder(lotteries, player, states, dates, withFireballs, numbers, selectionMode, ticketAmount, includedNumbersForInput, excludedSubtickets, gameType, myOrder, ticketPick3StraightProd, nextDatesAccumulator, domain);
			company.AddOrders(myOrder);
			int orderNumber = order.Number;
			order = company.OrderByNumber(orderNumber);
			int theLowestBetId = 1;
			int theHighestBetId = 1;
			int ticketNumber = *********;
			var marketplace = new Marketplace(company, "CR");
			var agent = marketplace.AddAgent("1");
			myOrder.Agent = (GamesEngine.Exchange.Agent)agent;
			company.PurchaseTickets(false, myOrder, now, ticketNumber, domain, theLowestBetId, theHighestBetId, order.Number);
			lotteries.CreateWagers(false, theLowestBetId, theHighestBetId, 1, 2, ticketNumber, order.Number, now);

			//#3
			lotteries.Risks.Risk.UpdateToWin(int.Parse(pickNumber), 100m);
			nextDatesAccumulator = new NextDatesAccumulator(lotteries, domain, now, pickNumber, dates, states, hours, strIsPresentPlayerBeforeToCloseStore, strUseNextDate);
			toWinAccumulator = new ToWinAccumulator();
			totalAndPossibleError = new TotalOrderAndExclusionsByToWin(1);
			totalAndPossibleError = company.TotalTicketOrderAndExclusionsByToWin(domain, states, dates, numbers, selectionMode, ticketAmount, includedNumbersForInput, gameType, excludedSubtickets, ticketPick3StraightProd, nextDatesAccumulator, toWinAccumulator, totalAndPossibleError, "USD", string.Empty);
			Assert.AreEqual(MSG_COMBINATION_EXCEEDED, totalAndPossibleError.ErrorMessage);
			Assert.AreEqual(0m, totalAndPossibleError.TotalOrder);
			excludeSubticketsInResponse = totalAndPossibleError.ExclusionsForSingleSelection.ToList();
			Assert.AreEqual(2, excludeSubticketsInResponse.Count);

			excludeSubticket1 = excludeSubticketsInResponse.First();
			Assert.AreEqual("all", excludeSubticket1.Subticket);
			Assert.AreEqual("VA", excludeSubticket1.StateAbb);
			Assert.AreEqual("10:20 AM", excludeSubticket1.HourFormattedAsText);
			Assert.AreEqual("4/17/2019", excludeSubticket1.DateFormattedAsText);

			excludeSubticket2 = excludeSubticketsInResponse.Last();
			Assert.AreEqual("all", excludeSubticket2.Subticket);
			Assert.AreEqual("GA", excludeSubticket2.StateAbb);
			Assert.AreEqual("10:20 AM", excludeSubticket2.HourFormattedAsText);
			Assert.AreEqual("4/17/2019", excludeSubticket2.DateFormattedAsText);

			//#4
			lotteries.Risks.Risk.UpdateToWin(int.Parse(pickNumber), 100000m);
			now = new DateTime(2019, 04, 17, 10, 00, 00);
			string hours2 = "11:20 AM,11:20 AM";
			string dates2 = "04/17/2019";
			string withFireballs2 = "false,false";
			string numbers2 = "4,5,67";
			string selectionMode2 = "balls";
			ticketNumber = *********;

			NextDatesAccumulator nextDatesAccumulator2 = new NextDatesAccumulator(lotteries, domain, now, pickNumber, dates2, states, hours2, strIsPresentPlayerBeforeToCloseStore, strUseNextDate);
			OrderCart myOrder2 = company.NewOrder(customer, 2, Coinage.Coin(Currencies.CODES.USD), 2);
			excludedSubtickets = new ExcludeSubtickets();
			excludedSubtickets.Add(state1, "11:20 AM", "04/17/2019", "456");
			excludedSubtickets.Add(state2, "11:20 AM", "04/17/2019", "457");
			toWinAccumulator = new ToWinAccumulator();
			totalAndPossibleError = new TotalOrderAndExclusionsByToWin(1);
			totalAndPossibleError = company.TotalTicketOrderAndExclusionsByToWin(domain, states, dates2, numbers2, selectionMode2, ticketAmount, includedNumbersForInput, gameType, excludedSubtickets, ticketPick3StraightProd, nextDatesAccumulator2, toWinAccumulator, totalAndPossibleError, "USD", string.Empty);
			Assert.AreEqual(string.Empty, totalAndPossibleError.ErrorMessage);
			Assert.AreEqual(121m, totalAndPossibleError.TotalOrder);

			nextDatesAccumulator2 = new NextDatesAccumulator(lotteries, domain, now, pickNumber, dates2, states, hours2, strIsPresentPlayerBeforeToCloseStore, strUseNextDate);
			Order order2 = company.CreateTicketOrder(lotteries, player, states, dates2, withFireballs2, numbers2, selectionMode2, ticketAmount, string.Empty, excludedSubtickets, gameType, myOrder2, ticketPick3StraightProd, nextDatesAccumulator2, domain);
			company.AddOrders(myOrder2);
			int orderNumber2 = order2.Number;
			order2 = company.OrderByNumber(orderNumber2);

			myOrder2.Agent = (GamesEngine.Exchange.Agent)agent;
			company.PurchaseTickets(false, myOrder2, now, ticketNumber, domain, theLowestBetId, theHighestBetId, order.Number);
			lotteries.CreateWagers(false, theLowestBetId, theHighestBetId, 1, 2, ticketNumber, order.Number, now);
		}

		[TestMethod]
		public void TotalTicketOrderAndExcludedByToWin_BuyingTwoStatesWithExcludesAll()
		{
			Company company = new Company();
			company.Sales.CreateStore(1, "Test Store").MakeCurrent();
			company.Accounting = new MockAccounting();
			var lotteries = company.Lotto900();
			CurrenciesTest.AddCurrencies();

			DateTime now = new DateTime(2019, 04, 17, 9, 00, 00);
			State state1 = lotteries.GetOrCreateState("VA", "Virginia", now, "Bart");
			LotteryPick<Pick3> lottery = (LotteryPick<Pick3>)lotteries.GetOrCreateLottery(3, state1);
			for (int i = 0; i <= 6; i++) lottery.Every(10, 20, i, now, "Bart");
			for (int i = 0; i <= 6; i++) lottery.Every(11, 20, i, now, "Bart");

			var state2 = lotteries.GetOrCreateState("GA", "Georgia", now, "Bart");
			var lottery2 = (LotteryPick<Pick3>)lotteries.GetOrCreateLottery(3, state2);
			for (int i = 0; i <= 6; i++) lottery2.Every(10, 20, i, now, "Bart");
			for (int i = 0; i <= 6; i++) lottery2.Every(11, 20, i, now, "Bart");

			Player player = company.GetOrCreateCustomerById("*********").Player;
			string states = "VA,GA";
			string hours = "10:20 AM,10:20 AM";
			string strIsPresentPlayerBeforeToCloseStore = "true,true";
			string strUseNextDate = "true,true";
			string dates = "04/17/2019";
			string withFireballs = "false,false";
			string numbers = "";
			string selectionMode = "MultipleInputSingleAmount";
			string pickNumber = "3";
			decimal ticketAmount = 100.5m;
			string includedNumbersForInput = "343,342";
			string gameType = "Boxed";

			Product ticketPick3StraightProd = lotteries.GetOrCreateProductById(1);
			ticketPick3StraightProd.Description = "Ticket pick 3 straight";
			ticketPick3StraightProd.Price = 1;
			Domain domain = company.Sales.CreateDomain(false, 1, "localhost", Agents.INSIDER);

			var drawDate = new DateTime(2019, 04, 17, 10, 20, 00);
			lotteries.Risks.Risk.UpdateToWin(int.Parse(pickNumber), 100m);

			//#1
			NextDatesAccumulator nextDatesAccumulator = new NextDatesAccumulator(lotteries, domain, now, pickNumber, dates, states, hours, strIsPresentPlayerBeforeToCloseStore, strUseNextDate);
			var excludedSubtickets = new ExcludeSubtickets();
			excludedSubtickets.Add(state1, "10:20 AM", "04/17/2019", "all");

			Customer customer = company.CustomerByPlayer(player);
			OrderCart myOrder = company.NewOrder(customer, 1, Coinage.Coin(Currencies.CODES.USD), 1);
			var toWinAccumulator = new ToWinAccumulator();
			var totalAndPossibleError = new TotalOrderAndExclusionsByToWin(1);
			totalAndPossibleError = company.TotalTicketOrderAndExclusionsByToWin(domain, states, dates, numbers, selectionMode, ticketAmount, includedNumbersForInput, gameType, excludedSubtickets, ticketPick3StraightProd, nextDatesAccumulator, toWinAccumulator, totalAndPossibleError, "USD", string.Empty);
			Assert.AreEqual(MSG_COMBINATION_EXCEEDED, totalAndPossibleError.ErrorMessage);
			Assert.AreEqual(0m, totalAndPossibleError.TotalOrder);
			var excludeSubticketsInResponse = totalAndPossibleError.ExclusionsForSingleSelection.ToList();
			Assert.AreEqual(1, excludeSubticketsInResponse.Count);

			var excludeSubticket1 = excludeSubticketsInResponse.First();
			Assert.AreEqual("all", excludeSubticket1.Subticket);
			Assert.AreEqual("GA", excludeSubticket1.StateAbb);
			Assert.AreEqual("10:20 AM", excludeSubticket1.HourFormattedAsText);
			Assert.AreEqual("4/17/2019", excludeSubticket1.DateFormattedAsText);

			//#2
			lotteries.Risks.Risk.UpdateToWin(int.Parse(pickNumber), 100000m);
			excludedSubtickets = new ExcludeSubtickets();
			excludedSubtickets.Add(state1, "10:20 AM", "04/17/2019", "all");
			ticketAmount = 60.5m;
			nextDatesAccumulator = new NextDatesAccumulator(lotteries, domain, now, pickNumber, dates, states, hours, strIsPresentPlayerBeforeToCloseStore, strUseNextDate);
			toWinAccumulator = new ToWinAccumulator();
			totalAndPossibleError = new TotalOrderAndExclusionsByToWin(1);
			totalAndPossibleError = company.TotalTicketOrderAndExclusionsByToWin(domain, states, dates, numbers, selectionMode, ticketAmount, includedNumbersForInput, gameType, excludedSubtickets, ticketPick3StraightProd, nextDatesAccumulator, toWinAccumulator, totalAndPossibleError, "USD", string.Empty);
			Assert.AreEqual(string.Empty, totalAndPossibleError.ErrorMessage);
			Assert.AreEqual(121m, totalAndPossibleError.TotalOrder);

			nextDatesAccumulator = new NextDatesAccumulator(lotteries, domain, now, pickNumber, dates, states, hours, strIsPresentPlayerBeforeToCloseStore, strUseNextDate);
			Order order = company.CreateTicketOrder(lotteries, player, states, dates, withFireballs, numbers, selectionMode, ticketAmount, includedNumbersForInput, excludedSubtickets, gameType, myOrder, ticketPick3StraightProd, nextDatesAccumulator, domain);
			company.AddOrders(myOrder);
			int orderNumber = order.Number;
			order = company.OrderByNumber(orderNumber);
			int theLowestBetId = 1;
			int theHighestBetId = 1;
			int ticketNumber = *********;
			var marketplace = new Marketplace(company, "CR");
			var agent = marketplace.AddAgent("1");
			myOrder.Agent = (GamesEngine.Exchange.Agent)agent;
			company.PurchaseTickets(false, myOrder, now, ticketNumber, domain, theLowestBetId, theHighestBetId, order.Number);
			lotteries.CreateWagers(false, theLowestBetId, theHighestBetId, 1, 2, ticketNumber, order.Number, now);

			//#3
			lotteries.Risks.Risk.UpdateToWin(int.Parse(pickNumber), 100m);
			nextDatesAccumulator = new NextDatesAccumulator(lotteries, domain, now, pickNumber, dates, states, hours, strIsPresentPlayerBeforeToCloseStore, strUseNextDate);
			toWinAccumulator = new ToWinAccumulator();
			totalAndPossibleError = new TotalOrderAndExclusionsByToWin(1);
			totalAndPossibleError = company.TotalTicketOrderAndExclusionsByToWin(domain, states, dates, numbers, selectionMode, ticketAmount, includedNumbersForInput, gameType, excludedSubtickets, ticketPick3StraightProd, nextDatesAccumulator, toWinAccumulator, totalAndPossibleError, "USD", string.Empty);
			Assert.AreEqual(MSG_COMBINATION_EXCEEDED, totalAndPossibleError.ErrorMessage);
			Assert.AreEqual(0m, totalAndPossibleError.TotalOrder);
			excludeSubticketsInResponse = totalAndPossibleError.ExclusionsForSingleSelection.ToList();
			Assert.AreEqual(1, excludeSubticketsInResponse.Count);

			excludeSubticket1 = excludeSubticketsInResponse.First();
			Assert.AreEqual("all", excludeSubticket1.Subticket);
			Assert.AreEqual("GA", excludeSubticket1.StateAbb);
			Assert.AreEqual("10:20 AM", excludeSubticket1.HourFormattedAsText);
			Assert.AreEqual("4/17/2019", excludeSubticket1.DateFormattedAsText);

			//#4
			lotteries.Risks.Risk.UpdateToWin(int.Parse(pickNumber), 100000m);
			now = new DateTime(2019, 04, 17, 10, 00, 00);
			string hours2 = "11:20 AM,11:20 AM";
			string dates2 = "04/17/2019";
			string withFireballs2 = "false,false";
			string numbers2 = "4,5,67";
			string selectionMode2 = "balls";
			ticketNumber = *********;

			NextDatesAccumulator nextDatesAccumulator2 = new NextDatesAccumulator(lotteries, domain, now, pickNumber, dates2, states, hours2, strIsPresentPlayerBeforeToCloseStore, strUseNextDate);
			OrderCart myOrder2 = company.NewOrder(customer, 2, Coinage.Coin(Currencies.CODES.USD), 2);
			excludedSubtickets = new ExcludeSubtickets();
			excludedSubtickets.Add(state1, "11:20 AM", "04/17/2019", "all");
			toWinAccumulator = new ToWinAccumulator();
			totalAndPossibleError = new TotalOrderAndExclusionsByToWin(1);
			totalAndPossibleError = company.TotalTicketOrderAndExclusionsByToWin(domain, states, dates2, numbers2, selectionMode2, ticketAmount, includedNumbersForInput, gameType, excludedSubtickets, ticketPick3StraightProd, nextDatesAccumulator2, toWinAccumulator, totalAndPossibleError, "USD", string.Empty);
			Assert.AreEqual(string.Empty, totalAndPossibleError.ErrorMessage);
			Assert.AreEqual(121m, totalAndPossibleError.TotalOrder);

			nextDatesAccumulator2 = new NextDatesAccumulator(lotteries, domain, now, pickNumber, dates2, states, hours2, strIsPresentPlayerBeforeToCloseStore, strUseNextDate);
			Order order2 = company.CreateTicketOrder(lotteries, player, states, dates2, withFireballs2, numbers2, selectionMode2, ticketAmount, string.Empty, excludedSubtickets, gameType, myOrder2, ticketPick3StraightProd, nextDatesAccumulator2, domain);
			company.AddOrders(myOrder2);
			int orderNumber2 = order2.Number;
			order2 = company.OrderByNumber(orderNumber2);
			myOrder2.Agent = (GamesEngine.Exchange.Agent)agent;
			company.PurchaseTickets(false, myOrder2, now, ticketNumber, domain, theLowestBetId, theHighestBetId, order.Number);
			lotteries.CreateWagers(false, theLowestBetId, theHighestBetId, 1, 2, ticketNumber, order.Number, now);
		}

		[TestMethod]
		public void TotalTicketOrderAndExcludedByToWin_BuyingTwoStates()
		{
			Company company = new Company();
			company.Sales.CreateStore(1, "Test Store").MakeCurrent();
			company.Accounting = new MockAccounting();
			var lotteries = company.Lotto900();
			CurrenciesTest.AddCurrencies();

			DateTime now = new DateTime(2019, 04, 17, 9, 00, 00);
			State state1 = lotteries.GetOrCreateState("VA", "Virginia", now, "Bart");
			LotteryPick<Pick3> lottery = (LotteryPick<Pick3>)lotteries.GetOrCreateLottery(3, state1);
			for (int i = 0; i <= 6; i++) lottery.Every(10, 20, i, now, "Bart");
			for (int i = 0; i <= 6; i++) lottery.Every(11, 20, i, now, "Bart");

			var state2 = lotteries.GetOrCreateState("GA", "Georgia", now, "Bart");
			var lottery2 = (LotteryPick<Pick3>)lotteries.GetOrCreateLottery(3, state2);
			for (int i = 0; i <= 6; i++) lottery2.Every(10, 20, i, now, "Bart");
			for (int i = 0; i <= 6; i++) lottery2.Every(11, 20, i, now, "Bart");

			Player player = company.GetOrCreateCustomerById("*********").Player;
			string states = "VA,GA";
			string hours = "10:20 AM,10:20 AM";
			string strIsPresentPlayerBeforeToCloseStore = "true,true";
			string strUseNextDate = "true,true";
			string dates = "04/17/2019";
			string withFireballs = "false,false";
			string numbers = "";
			string selectionMode = "MultipleInputSingleAmount";
			string pickNumber = "3";
			decimal ticketAmount = 100.5m;
			string includedNumbersForInput = "343,342";
			string gameType = "Boxed";

			Product ticketPick3StraightProd = lotteries.GetOrCreateProductById(1);
			ticketPick3StraightProd.Description = "Ticket pick 3 straight";
			ticketPick3StraightProd.Price = 1;
			Domain domain = company.Sales.CreateDomain(false, 1, "localhost", Agents.INSIDER);
			Customer customer = company.CustomerByPlayer(player);

			var drawDate = new DateTime(2019, 04, 17, 10, 20, 00);
			lotteries.Risks.Risk.UpdateToWin(int.Parse(pickNumber), 100m);

			//#1
			NextDatesAccumulator nextDatesAccumulator = new NextDatesAccumulator(lotteries, domain, now, pickNumber, dates, states, hours, strIsPresentPlayerBeforeToCloseStore, strUseNextDate);
			var excludedSubtickets = new ExcludeSubtickets();
			OrderCart myOrder = company.NewOrder(customer, 1, Coinage.Coin(Currencies.CODES.USD), 1);
			var toWinAccumulator = new ToWinAccumulator();
			var totalAndPossibleError = new TotalOrderAndExclusionsByToWin(1);
			totalAndPossibleError = company.TotalTicketOrderAndExclusionsByToWin(domain, states, dates, numbers, selectionMode, ticketAmount, includedNumbersForInput, gameType, excludedSubtickets, ticketPick3StraightProd, nextDatesAccumulator, toWinAccumulator, totalAndPossibleError, "USD", string.Empty);
			Assert.AreEqual(MSG_COMBINATION_EXCEEDED, totalAndPossibleError.ErrorMessage);
			Assert.AreEqual(0m, totalAndPossibleError.TotalOrder);
			var excludeSubticketsInResponse = totalAndPossibleError.ExclusionsForSingleSelection.ToList();
			Assert.AreEqual(2, excludeSubticketsInResponse.Count);

			var excludeSubticket1 = excludeSubticketsInResponse.First();
			Assert.AreEqual("all", excludeSubticket1.Subticket);
			Assert.AreEqual("VA", excludeSubticket1.StateAbb);
			Assert.AreEqual("10:20 AM", excludeSubticket1.HourFormattedAsText);
			Assert.AreEqual("4/17/2019", excludeSubticket1.DateFormattedAsText);

			var excludeSubticket2 = excludeSubticketsInResponse.Last();
			Assert.AreEqual("all", excludeSubticket2.Subticket);
			Assert.AreEqual("GA", excludeSubticket2.StateAbb);
			Assert.AreEqual("10:20 AM", excludeSubticket2.HourFormattedAsText);
			Assert.AreEqual("4/17/2019", excludeSubticket2.DateFormattedAsText);

			//#2
			lotteries.Risks.Risk.UpdateToWin(int.Parse(pickNumber), 1000000m);
			ticketAmount = 60.5m;
			nextDatesAccumulator = new NextDatesAccumulator(lotteries, domain, now, pickNumber, dates, states, hours, strIsPresentPlayerBeforeToCloseStore, strUseNextDate);
			toWinAccumulator = new ToWinAccumulator();
			totalAndPossibleError = new TotalOrderAndExclusionsByToWin(1);
			totalAndPossibleError = company.TotalTicketOrderAndExclusionsByToWin(domain, states, dates, numbers, selectionMode, ticketAmount, includedNumbersForInput, gameType, excludedSubtickets, ticketPick3StraightProd, nextDatesAccumulator, toWinAccumulator, totalAndPossibleError, "USD", string.Empty);
			Assert.AreEqual(string.Empty, totalAndPossibleError.ErrorMessage);
			Assert.AreEqual(242m, totalAndPossibleError.TotalOrder);

			nextDatesAccumulator = new NextDatesAccumulator(lotteries, domain, now, pickNumber, dates, states, hours, strIsPresentPlayerBeforeToCloseStore, strUseNextDate);
			Order order = company.CreateTicketOrder(lotteries, player, states, dates, withFireballs, numbers, selectionMode, ticketAmount, includedNumbersForInput, excludedSubtickets, gameType, myOrder, ticketPick3StraightProd, nextDatesAccumulator, domain);
			company.AddOrders(myOrder);
			int orderNumber = order.Number;
			order = company.OrderByNumber(orderNumber);
			int theLowestBetId = 1;
			int theHighestBetId = 1;
			int ticketNumber = *********;
			var marketplace = new Marketplace(company, "CR");
			var agent = marketplace.AddAgent("1");
			myOrder.Agent = (GamesEngine.Exchange.Agent)agent;
			company.PurchaseTickets(false, myOrder, now, ticketNumber, domain, theLowestBetId, theHighestBetId, order.Number);
			lotteries.CreateWagers(false, theLowestBetId, theHighestBetId, 1, 2, ticketNumber, order.Number, now);

			//#3
			lotteries.Risks.Risk.UpdateToWin(int.Parse(pickNumber), 100m);
			nextDatesAccumulator = new NextDatesAccumulator(lotteries, domain, now, pickNumber, dates, states, hours, strIsPresentPlayerBeforeToCloseStore, strUseNextDate);
			toWinAccumulator = new ToWinAccumulator();
			totalAndPossibleError = new TotalOrderAndExclusionsByToWin(1);
			totalAndPossibleError = company.TotalTicketOrderAndExclusionsByToWin(domain, states, dates, numbers, selectionMode, ticketAmount, includedNumbersForInput, gameType, excludedSubtickets, ticketPick3StraightProd, nextDatesAccumulator, toWinAccumulator, totalAndPossibleError, "USD", string.Empty);
			Assert.AreEqual(MSG_COMBINATION_EXCEEDED, totalAndPossibleError.ErrorMessage);
			Assert.AreEqual(0m, totalAndPossibleError.TotalOrder);
			excludeSubticketsInResponse = totalAndPossibleError.ExclusionsForSingleSelection.ToList();
			Assert.AreEqual(2, excludeSubticketsInResponse.Count);

			excludeSubticket1 = excludeSubticketsInResponse.First();
			Assert.AreEqual("all", excludeSubticket1.Subticket);
			Assert.AreEqual("VA", excludeSubticket1.StateAbb);
			Assert.AreEqual("10:20 AM", excludeSubticket1.HourFormattedAsText);
			Assert.AreEqual("4/17/2019", excludeSubticket1.DateFormattedAsText);

			excludeSubticket2 = excludeSubticketsInResponse.Last();
			Assert.AreEqual("all", excludeSubticket2.Subticket);
			Assert.AreEqual("GA", excludeSubticket2.StateAbb);
			Assert.AreEqual("10:20 AM", excludeSubticket2.HourFormattedAsText);
			Assert.AreEqual("4/17/2019", excludeSubticket2.DateFormattedAsText);

		}

		[TestMethod]
		public void TotalTicketOrderAndExcludedByToWin_UpdateRisk()
		{
			Company company = new Company();
			company.Sales.CreateStore(1, "Test Store").MakeCurrent();
			company.Accounting = new MockAccounting();
			var lotteries = company.Lotto900();
			CurrenciesTest.AddCurrencies();

			DateTime now = new DateTime(2019, 04, 17, 9, 00, 00);
			State state = lotteries.GetOrCreateState("GA", "Georgia", now, "Bart");
			LotteryPick<Pick3> lottery = (LotteryPick<Pick3>)lotteries.GetOrCreateLottery(3, state);
			for (int i = 0; i <= 6; i++) lottery.Every(10, 20, i, now, "Bart");
			for (int i = 0; i <= 6; i++) lottery.Every(11, 20, i, now, "Bart");

			var drawDate = new DateTime(2019, 04, 17, 10, 20, 00);
			Player player = company.GetOrCreateCustomerById("*********").Player;
			string states = "GA";
			string hours = "10:20 AM";
			string strIsPresentPlayerBeforeToCloseStore = "true";
			string strUseNextDate = "true";
			string dates = "04/17/2019";
			string withFireballs = "false";
			string numbers = "";
			string selectionMode = "MultipleInputSingleAmount";
			string pickNumber = "3";
			decimal ticketAmount = 100.75m;
			string includedNumbersForInput = "343,342";
			string gameType = "Boxed";

			Product ticketPick3StraightProd = lotteries.GetOrCreateProductById(1);
			ticketPick3StraightProd.Description = "Ticket pick 3 straight";
			ticketPick3StraightProd.Price = 1;
			Domain domain = company.Sales.CreateDomain(false, 1, "localhost", Agents.INSIDER);
			lotteries.Risks.Risk.UpdateToWin(int.Parse(pickNumber), 50m);

			//#1
			NextDatesAccumulator nextDatesAccumulator = new NextDatesAccumulator(lotteries, domain, now, pickNumber, dates, states, hours, strIsPresentPlayerBeforeToCloseStore, strUseNextDate);
			ExcludeSubtickets excludedSubtickets = new ExcludeSubtickets();
			Customer customer = company.CustomerByPlayer(player);
			OrderCart myOrder = company.NewOrder(customer, 1, Coinage.Coin(Currencies.CODES.USD), 1);
			var toWinAccumulator = new ToWinAccumulator();
			var totalAndPossibleError = new TotalOrderAndExclusionsByToWin(1);
			totalAndPossibleError = company.TotalTicketOrderAndExclusionsByToWin(domain, states, dates, numbers, selectionMode, ticketAmount, includedNumbersForInput, gameType, ticketPick3StraightProd, nextDatesAccumulator, toWinAccumulator, totalAndPossibleError, "USD", string.Empty);
			Assert.AreEqual(MSG_COMBINATION_EXCEEDED, totalAndPossibleError.ErrorMessage);
			Assert.AreEqual(0m, totalAndPossibleError.TotalOrder);
			var excludeSubticketsInResponse = totalAndPossibleError.ExclusionsForSingleSelection.ToList();
			Assert.AreEqual(1, excludeSubticketsInResponse.Count);

			var excludeSubticket = excludeSubticketsInResponse.First();
			Assert.AreEqual("all", excludeSubticket.Subticket);
			Assert.AreEqual("GA", excludeSubticket.StateAbb);
			Assert.AreEqual("10:20 AM", excludeSubticket.HourFormattedAsText);
			Assert.AreEqual("4/17/2019", excludeSubticket.DateFormattedAsText);

			//#2
			lotteries.Risks.Risk.UpdateToWin(int.Parse(pickNumber), 100000m);
			ticketAmount = 49.75m;
			nextDatesAccumulator = new NextDatesAccumulator(lotteries, domain, now, pickNumber, dates, states, hours, strIsPresentPlayerBeforeToCloseStore, strUseNextDate);
			toWinAccumulator = new ToWinAccumulator();
			totalAndPossibleError = new TotalOrderAndExclusionsByToWin(1);
			totalAndPossibleError = company.TotalTicketOrderAndExclusionsByToWin(domain, states, dates, numbers, selectionMode, ticketAmount, includedNumbersForInput, gameType, ticketPick3StraightProd, nextDatesAccumulator, toWinAccumulator, totalAndPossibleError, "USD", string.Empty);
			Assert.AreEqual(string.Empty, totalAndPossibleError.ErrorMessage);
			Assert.AreEqual(99.5m, totalAndPossibleError.TotalOrder);

			nextDatesAccumulator = new NextDatesAccumulator(lotteries, domain, now, pickNumber, dates, states, hours, strIsPresentPlayerBeforeToCloseStore, strUseNextDate);
			Order order = company.CreateTicketOrder(lotteries, player, states, dates, withFireballs, numbers, selectionMode, ticketAmount, includedNumbersForInput, excludedSubtickets, gameType, myOrder, ticketPick3StraightProd, nextDatesAccumulator, domain);
			company.AddOrders(myOrder);
			int orderNumber = order.Number;
			order = company.OrderByNumber(orderNumber);
			int theLowestBetId = 1;
			int theHighestBetId = 1;
			int ticketNumber = *********;
			var marketplace = new Marketplace(company, "CR");
			var agent = marketplace.AddAgent("1");
			myOrder.Agent = (GamesEngine.Exchange.Agent)agent;
			company.PurchaseTickets(false, myOrder, now, ticketNumber, domain, theLowestBetId, theHighestBetId, order.Number);
			lotteries.CreateWagers(false, theLowestBetId, theHighestBetId, 1, 2, ticketNumber, order.Number, now);

			//#3
			lotteries.Risks.Risk.UpdateToWin(int.Parse(pickNumber), 100m);
			nextDatesAccumulator = new NextDatesAccumulator(lotteries, domain, now, pickNumber, dates, states, hours, strIsPresentPlayerBeforeToCloseStore, strUseNextDate);
			toWinAccumulator = new ToWinAccumulator();
			totalAndPossibleError = new TotalOrderAndExclusionsByToWin(1);
			totalAndPossibleError = company.TotalTicketOrderAndExclusionsByToWin(domain, states, dates, numbers, selectionMode, ticketAmount, includedNumbersForInput, gameType, ticketPick3StraightProd, nextDatesAccumulator, toWinAccumulator, totalAndPossibleError, "USD", string.Empty);
			Assert.AreEqual(MSG_COMBINATION_EXCEEDED, totalAndPossibleError.ErrorMessage);
			Assert.AreEqual(0m, totalAndPossibleError.TotalOrder);
			excludeSubticketsInResponse = totalAndPossibleError.ExclusionsForSingleSelection.ToList();
			Assert.AreEqual(1, excludeSubticketsInResponse.Count);

			excludeSubticket = excludeSubticketsInResponse.First();
			Assert.AreEqual("all", excludeSubticket.Subticket);
			Assert.AreEqual("GA", excludeSubticket.StateAbb);
			Assert.AreEqual("10:20 AM", excludeSubticket.HourFormattedAsText);
			Assert.AreEqual("4/17/2019", excludeSubticket.DateFormattedAsText);

			lotteries.Risks.Risk.UpdateToWin(int.Parse(pickNumber), 100000m);

			//#4
			NextDatesAccumulator nextDatesAccumulator2 = new NextDatesAccumulator(lotteries, domain, now, pickNumber, dates, states, hours, strIsPresentPlayerBeforeToCloseStore, strUseNextDate);
			OrderCart myOrder2 = company.NewOrder(customer, 2, Coinage.Coin(Currencies.CODES.USD), 2);
			toWinAccumulator = new ToWinAccumulator();
			totalAndPossibleError = new TotalOrderAndExclusionsByToWin(1);
			totalAndPossibleError = company.TotalTicketOrderAndExclusionsByToWin(domain, states, dates, numbers, selectionMode, ticketAmount, includedNumbersForInput, gameType, ticketPick3StraightProd, nextDatesAccumulator2, toWinAccumulator, totalAndPossibleError, "USD", string.Empty);
			Assert.AreEqual(string.Empty, totalAndPossibleError.ErrorMessage);
			Assert.AreEqual(99.5m, totalAndPossibleError.TotalOrder);

			nextDatesAccumulator2 = new NextDatesAccumulator(lotteries, domain, now, pickNumber, dates, states, hours, strIsPresentPlayerBeforeToCloseStore, strUseNextDate);
			Order order2 = company.CreateTicketOrder(lotteries, player, states, dates, withFireballs, numbers, selectionMode, ticketAmount, includedNumbersForInput, excludedSubtickets, gameType, myOrder2, ticketPick3StraightProd, nextDatesAccumulator2, domain);
			company.AddOrders(myOrder2);
			int orderNumber2 = order2.Number;
			order2 = company.OrderByNumber(orderNumber2);
			theLowestBetId = 2;
			theHighestBetId = 2;
			ticketNumber = *********;
			myOrder2.Agent = (GamesEngine.Exchange.Agent)agent;
			company.PurchaseTickets(false, myOrder2, now, ticketNumber, domain, theLowestBetId, theHighestBetId, order.Number);
			lotteries.CreateWagers(false, theLowestBetId, theHighestBetId, 1, 2, ticketNumber, order.Number, now);
		}

		[TestMethod]
		public void TotalTicketOrderAndExcludedByToWin_Buying1NumberThen2_Inputs()
		{
			Company company = new Company();
			company.Sales.CreateStore(1, "Test Store").MakeCurrent();
			company.Accounting = new MockAccounting();
			var lotteries = company.Lotto900();
			CurrenciesTest.AddCurrencies();

			DateTime now = new DateTime(2019, 04, 17, 9, 00, 00);
			State state = lotteries.GetOrCreateState("VA", "Virginia", now, "Bart");
			LotteryPick<Pick3> lottery = (LotteryPick<Pick3>)lotteries.GetOrCreateLottery(3, state);
			for (int i = 0; i <= 6; i++) lottery.Every(10, 20, i, now, "Bart");
			for (int i = 0; i <= 6; i++) lottery.Every(13, 20, i, now, "Bart");

			var state2 = lotteries.GetOrCreateState("GA", "Georgia", now, "Bart");
			var lottery2 = (LotteryPick<Pick3>)lotteries.GetOrCreateLottery(3, state2);
			for (int i = 0; i <= 6; i++) lottery2.Every(10, 20, i, now, "Bart");
			for (int i = 0; i <= 6; i++) lottery2.Every(13, 20, i, now, "Bart");

			Player player = company.GetOrCreateCustomerById("*********").Player;
			string states = "GA";
			string hours = "10:20 AM";
			string strIsPresentPlayerBeforeToCloseStore = "true";
			string strUseNextDate = "true";
			string dates = "04/17/2019";
			string withFireballs = "false";
			string numbers = "";
			string selectionMode = "MultipleInputSingleAmount";
			string pickNumber = "3";
			decimal ticketAmount = 99.75m;
			string includedNumbersForInput = "343";
			string gameType = "Boxed";

			Product ticketPick3StraightProd = lotteries.GetOrCreateProductById(1);
			ticketPick3StraightProd.Description = "Ticket pick 3 straight";
			ticketPick3StraightProd.Price = 1;
			Domain domain = company.Sales.CreateDomain(false, 1, "localhost", Agents.INSIDER);
			var risk = lotteries.Risks.Risk;

			//#1
			NextDatesAccumulator nextDatesAccumulator = new NextDatesAccumulator(lotteries, domain, now, pickNumber, dates, states, hours, strIsPresentPlayerBeforeToCloseStore, strUseNextDate);
			ExcludeSubtickets excludedSubtickets = new ExcludeSubtickets();
			Customer customer = company.CustomerByPlayer(player);
			OrderCart myOrder = company.NewOrder(customer, 1, Coinage.Coin(Currencies.CODES.USD), 1);
			var toWinAccumulator = new ToWinAccumulator();
			var totalAndPossibleError = new TotalOrderAndExclusionsByToWin(1);
			totalAndPossibleError = company.TotalTicketOrderAndExclusionsByToWin(domain, states, dates, numbers, selectionMode, ticketAmount, includedNumbersForInput, gameType, ticketPick3StraightProd, nextDatesAccumulator, toWinAccumulator, totalAndPossibleError, "USD", string.Empty);
			Assert.AreEqual(string.Empty, totalAndPossibleError.ErrorMessage);
			Assert.AreEqual(99.75m, totalAndPossibleError.TotalOrder);

			nextDatesAccumulator = new NextDatesAccumulator(lotteries, domain, now, pickNumber, dates, states, hours, strIsPresentPlayerBeforeToCloseStore, strUseNextDate);
			Order order = company.CreateTicketOrder(lotteries, player, states, dates, withFireballs, numbers, selectionMode, ticketAmount, includedNumbersForInput, excludedSubtickets, gameType, myOrder, ticketPick3StraightProd, nextDatesAccumulator, domain);
			company.AddOrders(myOrder);
			int orderNumber = order.Number;
			order = company.OrderByNumber(orderNumber);
			int theLowestBetId = 1;
			int theHighestBetId = 1;
			int ticketNumber = *********;
			var marketplace = new Marketplace(company, "CR");
			var agent = marketplace.AddAgent("1");
			myOrder.Agent = (GamesEngine.Exchange.Agent)agent;
			company.PurchaseTickets(false, myOrder, now, ticketNumber, domain, theLowestBetId, theHighestBetId, order.Number);
			lotteries.CreateWagers(false, theLowestBetId, theHighestBetId, 1, 2, ticketNumber, order.Number, now);

			var drawDate = new DateTime(2019, 04, 17, 10, 20, 00);
			var toWins = risk.GetRiskPerLottery(int.Parse(pickNumber), lottery2).ToWinPerSubticketsAt(drawDate);
			var toWin = toWins.First();
			Assert.AreEqual(29925m, toWin.Amount);
			risk.UpdateToWin(int.Parse(pickNumber), 30000m);

			//#2
			includedNumbersForInput = "343,344";
			nextDatesAccumulator = new NextDatesAccumulator(lotteries, domain, now, pickNumber, dates, states, hours, strIsPresentPlayerBeforeToCloseStore, strUseNextDate);
			toWinAccumulator = new ToWinAccumulator();
			totalAndPossibleError = new TotalOrderAndExclusionsByToWin(1);
			totalAndPossibleError = company.TotalTicketOrderAndExclusionsByToWin(domain, states, dates, numbers, selectionMode, ticketAmount, includedNumbersForInput, gameType, ticketPick3StraightProd, nextDatesAccumulator, toWinAccumulator, totalAndPossibleError, "USD", string.Empty);

			Assert.AreEqual(MSG_COMBINATION_EXCEEDED, totalAndPossibleError.ErrorMessage);
			var excludeSubticketsInResponse = totalAndPossibleError.ExclusionsForSingleSelection.ToList();
			Assert.AreEqual(1, excludeSubticketsInResponse.Count);

			var excludeSubticket = excludeSubticketsInResponse.First();
			Assert.AreEqual("343", excludeSubticket.Subticket);
			Assert.AreEqual("GA", excludeSubticket.StateAbb);
			Assert.AreEqual("10:20 AM", excludeSubticket.HourFormattedAsText);
			Assert.AreEqual("4/17/2019", excludeSubticket.DateFormattedAsText);
		}

		[TestMethod]
		public void TotalTicketOrderAndExcludedByToWin_Buying1NumberThen2_Balls()
		{
			Company company = new Company();
			company.Sales.CreateStore(1, "Test Store").MakeCurrent();
			company.Accounting = new MockAccounting();
			var lotteries = company.Lotto900();
			CurrenciesTest.AddCurrencies();
			DateTime now = new DateTime(2019, 04, 17, 9, 00, 00);
			State state = lotteries.GetOrCreateState("VA", "Virginia", now, "Bart");
			LotteryPick<Pick3> lottery = (LotteryPick<Pick3>)lotteries.GetOrCreateLottery(3, state);
			for (int i = 0; i <= 6; i++) lottery.Every(10, 20, i, now, "Bart");
			for (int i = 0; i <= 6; i++) lottery.Every(13, 20, i, now, "Bart");

			var state2 = lotteries.GetOrCreateState("GA", "Georgia", now, "Bart");
			var lottery2 = (LotteryPick<Pick3>)lotteries.GetOrCreateLottery(3, state2);
			for (int i = 0; i <= 6; i++) lottery2.Every(10, 20, i, now, "Bart");
			for (int i = 0; i <= 6; i++) lottery2.Every(13, 20, i, now, "Bart");

			Player player = company.GetOrCreateCustomerById("*********").Player;
			string states = "GA";
			string hours = "1:20 PM";
			string strIsPresentPlayerBeforeToCloseStore = "true";
			string strUseNextDate = "true";
			string dates = "04/17/2019";
			string withFireballs = "false";
			string numbers = "4,5,6";
			string selectionMode = "balls";
			string pickNumber = "3";
			decimal ticketAmount = 99.75m;
			string includedNumbersForInput = "";
			string gameType = "Boxed";

			Product ticketPick3StraightProd = lotteries.GetOrCreateProductById(1);
			ticketPick3StraightProd.Description = "Ticket pick 3 straight";
			ticketPick3StraightProd.Price = 1;
			Domain domain = company.Sales.CreateDomain(false, 1, "localhost", Agents.INSIDER);
			var risk = lotteries.Risks.Risk;
			Customer customer = company.CustomerByPlayer(player);

			//#1
			var excludedSubtickets = new ExcludeSubtickets();
			now = new DateTime(2019, 04, 17, 10, 00, 00);

			NextDatesAccumulator nextDatesAccumulator = new NextDatesAccumulator(lotteries, domain, now, pickNumber, dates, states, hours, strIsPresentPlayerBeforeToCloseStore, strUseNextDate);
			OrderCart myOrder = company.NewOrder(customer, 2, Coinage.Coin(Currencies.CODES.USD), 1);
			var toWinAccumulator = new ToWinAccumulator();
			var totalAndPossibleError = new TotalOrderAndExclusionsByToWin(1);
			totalAndPossibleError = company.TotalTicketOrderAndExclusionsByToWin(domain, states, dates, numbers, selectionMode, ticketAmount, includedNumbersForInput, gameType, ticketPick3StraightProd, nextDatesAccumulator, toWinAccumulator, totalAndPossibleError, "USD", string.Empty);
			Assert.AreEqual(string.Empty, totalAndPossibleError.ErrorMessage);
			Assert.AreEqual(99.75m, totalAndPossibleError.TotalOrder);

			nextDatesAccumulator = new NextDatesAccumulator(lotteries, domain, now, pickNumber, dates, states, hours, strIsPresentPlayerBeforeToCloseStore, strUseNextDate);
			Order order = company.CreateTicketOrder(lotteries, player, states, dates, withFireballs, numbers, selectionMode, ticketAmount, string.Empty, excludedSubtickets, gameType, myOrder, ticketPick3StraightProd, nextDatesAccumulator, domain);
			company.AddOrders(myOrder);
			int orderNumber = order.Number;
			order = company.OrderByNumber(orderNumber);
			int theLowestBetId = 1;
			int theHighestBetId = 1;
			var ticketNumber = *********;
			var marketplace = new Marketplace(company, "CR");
			var agent = marketplace.AddAgent("1");
			myOrder.Agent = (GamesEngine.Exchange.Agent)agent;
			company.PurchaseTickets(false, myOrder, now, ticketNumber, domain, theLowestBetId, theHighestBetId, order.Number);
			lotteries.CreateWagers(false, theLowestBetId, theHighestBetId, 1, 2, ticketNumber, order.Number, now);

			var drawDate = new DateTime(2019, 04, 17, 13, 20, 00);
			var toWins = risk.GetRiskPerLottery(int.Parse(pickNumber), lottery2).ToWinPerSubticketsAt(drawDate);
			var toWin = toWins.First();
			Assert.AreEqual(14962.50m, toWin.Amount);
			risk.UpdateToWin(int.Parse(pickNumber), 15000m);

			//#2
			numbers = "4,5,67";
			nextDatesAccumulator = new NextDatesAccumulator(lotteries, domain, now, pickNumber, dates, states, hours, strIsPresentPlayerBeforeToCloseStore, strUseNextDate);
			toWinAccumulator = new ToWinAccumulator();
			totalAndPossibleError = new TotalOrderAndExclusionsByToWin(1);
			totalAndPossibleError = company.TotalTicketOrderAndExclusionsByToWin(domain, states, dates, numbers, selectionMode, ticketAmount, includedNumbersForInput, gameType, ticketPick3StraightProd, nextDatesAccumulator, toWinAccumulator, totalAndPossibleError, "USD", string.Empty);

			Assert.AreEqual(MSG_COMBINATION_EXCEEDED, totalAndPossibleError.ErrorMessage);
			var excludeSubticketsInResponse = totalAndPossibleError.ExclusionsForSingleSelection.ToList();
			Assert.AreEqual(1, excludeSubticketsInResponse.Count);

			var excludeSubticket = excludeSubticketsInResponse.First();
			Assert.AreEqual("456", excludeSubticket.Subticket);
			Assert.AreEqual("GA", excludeSubticket.StateAbb);
			Assert.AreEqual("1:20 PM", excludeSubticket.HourFormattedAsText);
			Assert.AreEqual("4/17/2019", excludeSubticket.DateFormattedAsText);
		}

		[TestMethod]
		public void TotalTicketOrderAndExcludedByToWin_Buying2NumberThenAsterisk_Balls()
		{
			Company company = new Company();
			company.Sales.CreateStore(1, "Test Store").MakeCurrent();
			company.Accounting = new MockAccounting();
			var lotteries = company.Lotto900();
			CurrenciesTest.AddCurrencies();

			DateTime now = new DateTime(2019, 04, 17, 9, 00, 00);
			State state = lotteries.GetOrCreateState("VA", "Virginia", now, "Bart");
			LotteryPick<Pick3> lottery = (LotteryPick<Pick3>)lotteries.GetOrCreateLottery(3, state);
			for (int i = 0; i <= 6; i++) lottery.Every(10, 20, i, now, "Bart");
			for (int i = 0; i <= 6; i++) lottery.Every(13, 20, i, now, "Bart");

			var state2 = lotteries.GetOrCreateState("GA", "Georgia", now, "Bart");
			var lottery2 = (LotteryPick<Pick3>)lotteries.GetOrCreateLottery(3, state2);
			for (int i = 0; i <= 6; i++) lottery2.Every(10, 20, i, now, "Bart");
			for (int i = 0; i <= 6; i++) lottery2.Every(13, 20, i, now, "Bart");

			Player player = company.GetOrCreateCustomerById("*********").Player;
			string states = "GA";
			string hours = "1:20 PM";
			string strIsPresentPlayerBeforeToCloseStore = "true";
			string strUseNextDate = "true";
			string dates = "04/17/2019";
			string withFireballs = "false";
			string numbers = "4,5,67";
			string selectionMode = "balls";
			string pickNumber = "3";
			decimal ticketAmount = 99.75m;
			string includedNumbersForInput = "";
			string gameType = "Boxed";

			Product ticketPick3StraightProd = lotteries.GetOrCreateProductById(1);
			ticketPick3StraightProd.Description = "Ticket pick 3 straight";
			ticketPick3StraightProd.Price = 1;
			Domain domain = company.Sales.CreateDomain(false, 1, "localhost", Agents.INSIDER);
			var risk = lotteries.Risks.Risk;
			Customer customer = company.CustomerByPlayer(player);

			//#1
			var excludedSubtickets = new ExcludeSubtickets();
			now = new DateTime(2019, 04, 17, 10, 00, 00);

			NextDatesAccumulator nextDatesAccumulator = new NextDatesAccumulator(lotteries, domain, now, pickNumber, dates, states, hours, strIsPresentPlayerBeforeToCloseStore, strUseNextDate);
			OrderCart myOrder = company.NewOrder(customer, 2, Coinage.Coin(Currencies.CODES.USD), 1);
			var toWinAccumulator = new ToWinAccumulator();
			var totalAndPossibleError = new TotalOrderAndExclusionsByToWin(1);
			totalAndPossibleError = company.TotalTicketOrderAndExclusionsByToWin(domain, states, dates, numbers, selectionMode, ticketAmount, includedNumbersForInput, gameType, ticketPick3StraightProd, nextDatesAccumulator, toWinAccumulator, totalAndPossibleError, "USD", string.Empty);
			Assert.AreEqual(string.Empty, totalAndPossibleError.ErrorMessage);
			Assert.AreEqual(199.5m, totalAndPossibleError.TotalOrder);

			nextDatesAccumulator = new NextDatesAccumulator(lotteries, domain, now, pickNumber, dates, states, hours, strIsPresentPlayerBeforeToCloseStore, strUseNextDate);
			Order order = company.CreateTicketOrder(lotteries, player, states, dates, withFireballs, numbers, selectionMode, ticketAmount, string.Empty, excludedSubtickets, gameType, myOrder, ticketPick3StraightProd, nextDatesAccumulator, domain);
			company.AddOrders(myOrder);
			int orderNumber = order.Number;
			order = company.OrderByNumber(orderNumber);
			int theLowestBetId = 1;
			int theHighestBetId = 1;
			var ticketNumber = *********;
			var marketplace = new Marketplace(company, "CR");
			var agent = marketplace.AddAgent("1");
			myOrder.Agent = (GamesEngine.Exchange.Agent)agent;
			company.PurchaseTickets(false, myOrder, now, ticketNumber, domain, theLowestBetId, theHighestBetId, order.Number);
			lotteries.CreateWagers(false, theLowestBetId, theHighestBetId, 1, 2, ticketNumber, order.Number, now);

			var drawDate = new DateTime(2019, 04, 17, 13, 20, 00);
			var toWins = risk.GetRiskPerLottery(int.Parse(pickNumber), lottery2).ToWinPerSubticketsAt(drawDate);
			var toWin = toWins.First();
			Assert.AreEqual(14962.50m, toWin.Amount);
			lotteries.Risks.Risk.UpdateToWin(int.Parse(pickNumber), 15000m);

			//#2
			ticketAmount = 9.75m;
			numbers = "4,5,*";
			nextDatesAccumulator = new NextDatesAccumulator(lotteries, domain, now, pickNumber, dates, states, hours, strIsPresentPlayerBeforeToCloseStore, strUseNextDate);
			toWinAccumulator = new ToWinAccumulator();
			totalAndPossibleError = new TotalOrderAndExclusionsByToWin(1);
			totalAndPossibleError = company.TotalTicketOrderAndExclusionsByToWin(domain, states, dates, numbers, selectionMode, ticketAmount, includedNumbersForInput, gameType, ticketPick3StraightProd, nextDatesAccumulator, toWinAccumulator, totalAndPossibleError, "USD", string.Empty);

			Assert.AreEqual(MSG_COMBINATION_EXCEEDED, totalAndPossibleError.ErrorMessage);
			var excludeSubticketsInResponse = totalAndPossibleError.ExclusionsForSingleSelection.ToList();
			Assert.AreEqual(2, excludeSubticketsInResponse.Count);

			var excludeSubticket1 = excludeSubticketsInResponse.First();
			Assert.AreEqual("456", excludeSubticket1.Subticket);
			Assert.AreEqual("GA", excludeSubticket1.StateAbb);
			Assert.AreEqual("1:20 PM", excludeSubticket1.HourFormattedAsText);
			Assert.AreEqual("4/17/2019", excludeSubticket1.DateFormattedAsText);

			var excludeSubticket2 = excludeSubticketsInResponse.Last();
			Assert.AreEqual("457", excludeSubticket2.Subticket);
			Assert.AreEqual("GA", excludeSubticket2.StateAbb);
			Assert.AreEqual("1:20 PM", excludeSubticket2.HourFormattedAsText);
			Assert.AreEqual("4/17/2019", excludeSubticket2.DateFormattedAsText);
		}

		[TestMethod]
		public void TotalTicketOrderAndExcludedByToWin_BuyingInputsAndBallsWithDifferentNumbers()
		{
			Company company = new Company();
			company.Sales.CreateStore(1, "Test Store").MakeCurrent();
			company.Accounting = new MockAccounting();
			var lotteries = company.Lotto900();
			CurrenciesTest.AddCurrencies();

			DateTime now = new DateTime(2019, 04, 17, 9, 00, 00);
			State state = lotteries.GetOrCreateState("VA", "Virginia", now, "Bart");
			LotteryPick<Pick3> lottery = (LotteryPick<Pick3>)lotteries.GetOrCreateLottery(3, state);
			for (int i = 0; i <= 6; i++) lottery.Every(10, 20, i, now, "Bart");
			for (int i = 0; i <= 6; i++) lottery.Every(13, 20, i, now, "Bart");

			var state2 = lotteries.GetOrCreateState("GA", "Georgia", now, "Bart");
			var lottery2 = (LotteryPick<Pick3>)lotteries.GetOrCreateLottery(3, state2);
			for (int i = 0; i <= 6; i++) lottery2.Every(10, 20, i, now, "Bart");
			for (int i = 0; i <= 6; i++) lottery2.Every(13, 20, i, now, "Bart");

			Player player = company.GetOrCreateCustomerById("*********").Player;
			string states = "GA";
			string hours = "10:20 AM";
			string strIsPresentPlayerBeforeToCloseStore = "true";
			string strUseNextDate = "true";
			string dates = "04/17/2019";
			string withFireballs = "false";
			string numbers = "";
			string selectionMode = "MultipleInputSingleAmount";
			string pickNumber = "3";
			decimal ticketAmount = 99.75m;
			string includedNumbersForInput = "321,320";
			string gameType = "Boxed";

			Product ticketPick3StraightProd = lotteries.GetOrCreateProductById(1);
			ticketPick3StraightProd.Description = "Ticket pick 3 straight";
			ticketPick3StraightProd.Price = 1;
			Domain domain = company.Sales.CreateDomain(false, 1, "localhost", Agents.INSIDER);

			//#1.1
			Customer customer = company.CustomerByPlayer(player);
			int orderNumber = 1;
			var myOrder = company.NewOrder(customer, orderNumber, Coinage.Coin(Currencies.CODES.USD), 1);
			ExcludeSubtickets excludedSubtickets = new ExcludeSubtickets();

			NextDatesAccumulator nextDatesAccumulator = new NextDatesAccumulator(lotteries, domain, now, pickNumber, dates, states, hours, strIsPresentPlayerBeforeToCloseStore, strUseNextDate);
			var toWinAccumulator = new ToWinAccumulator();
			var totalAndPossibleError = new TotalOrderAndExclusionsByToWin(2);
			totalAndPossibleError = company.TotalTicketOrderAndExclusionsByToWin(domain, states, dates, numbers, selectionMode, ticketAmount, includedNumbersForInput, gameType, ticketPick3StraightProd, nextDatesAccumulator, toWinAccumulator, totalAndPossibleError, "USD", string.Empty);
			Assert.AreEqual(string.Empty, totalAndPossibleError.ErrorMessage);
			Assert.AreEqual(199.5m, totalAndPossibleError.TotalOrder);
			nextDatesAccumulator = new NextDatesAccumulator(lotteries, domain, now, pickNumber, dates, states, hours, strIsPresentPlayerBeforeToCloseStore, strUseNextDate);
			var order = company.CreateTicketOrder(lotteries, player, states, dates, withFireballs, numbers, selectionMode, ticketAmount, includedNumbersForInput, excludedSubtickets, gameType, myOrder, ticketPick3StraightProd, nextDatesAccumulator, domain);

			//#1.2
			states = "VA";
			selectionMode = "balls";
			numbers = "4,5,67";
			includedNumbersForInput = "";
			nextDatesAccumulator = new NextDatesAccumulator(lotteries, domain, now, pickNumber, dates, states, hours, strIsPresentPlayerBeforeToCloseStore, strUseNextDate);
			toWinAccumulator = new ToWinAccumulator();
			totalAndPossibleError = company.TotalTicketOrderAndExclusionsByToWin(domain, states, dates, numbers, selectionMode, ticketAmount, includedNumbersForInput, gameType, ticketPick3StraightProd, nextDatesAccumulator, toWinAccumulator, totalAndPossibleError, "USD", string.Empty);
			Assert.AreEqual(string.Empty, totalAndPossibleError.ErrorMessage);
			Assert.AreEqual(399m, totalAndPossibleError.TotalOrder);
			nextDatesAccumulator = new NextDatesAccumulator(lotteries, domain, now, pickNumber, dates, states, hours, strIsPresentPlayerBeforeToCloseStore, strUseNextDate);
			order = company.CreateTicketOrder(lotteries, player, states, dates, withFireballs, numbers, selectionMode, ticketAmount, includedNumbersForInput, excludedSubtickets, gameType, myOrder, ticketPick3StraightProd, nextDatesAccumulator, domain);

			company.AddOrders(myOrder);
			int theLowestBetId = 1;
			int theHighestBetId = 1;
			int ticketNumber = *********;
			var marketplace = new Marketplace(company, "CR");
			var agent = marketplace.AddAgent("1");
			myOrder.Agent = (GamesEngine.Exchange.Agent)agent;
			company.PurchaseTickets(false, myOrder, now, ticketNumber, domain, theLowestBetId, theHighestBetId, orderNumber);
			lotteries.CreateWagers(false, theLowestBetId, theHighestBetId, 1, 2, ticketNumber, orderNumber, now);
		}

		[TestMethod]
		public void TotalTicketOrderAndExcludedByToWin_BuyingInputsAndBallsWithTheSameNumbers()
		{
			Company company = new Company();
			company.Sales.CreateStore(1, "Test Store").MakeCurrent();
			company.Accounting = new MockAccounting();
			var lotteries = company.Lotto900();
			CurrenciesTest.AddCurrencies();

			DateTime now = new DateTime(2019, 04, 17, 9, 00, 00);
			State state = lotteries.GetOrCreateState("VA", "Virginia", now, "Bart");
			LotteryPick<Pick3> lottery = (LotteryPick<Pick3>)lotteries.GetOrCreateLottery(3, state);
			for (int i = 0; i <= 6; i++) lottery.Every(10, 20, i, now, "Bart");
			for (int i = 0; i <= 6; i++) lottery.Every(13, 20, i, now, "Bart");

			var state2 = lotteries.GetOrCreateState("GA", "Georgia", now, "Bart");
			var lottery2 = (LotteryPick<Pick3>)lotteries.GetOrCreateLottery(3, state2);
			for (int i = 0; i <= 6; i++) lottery2.Every(10, 20, i, now, "Bart");
			for (int i = 0; i <= 6; i++) lottery2.Every(13, 20, i, now, "Bart");

			Player player = company.GetOrCreateCustomerById("*********").Player;
			string states = "GA";
			string hours = "10:20 AM";
			string strIsPresentPlayerBeforeToCloseStore = "true";
			string strUseNextDate = "true";
			string dates = "04/17/2019";
			string withFireballs = "false";
			string numbers = "";
			string selectionMode = "MultipleInputSingleAmount";
			string pickNumber = "3";
			decimal ticketAmount = 99.75m;
			string includedNumbersForInput = "321,320";
			string gameType = "Boxed";

			Product ticketPick3StraightProd = lotteries.GetOrCreateProductById(1);
			ticketPick3StraightProd.Description = "Ticket pick 3 straight";
			ticketPick3StraightProd.Price = 1;
			Domain domain = company.Sales.CreateDomain(false, 1, "localhost", Agents.INSIDER);

			//#1.1
			Customer customer = company.CustomerByPlayer(player);
			int orderNumber = 1;
			var myOrder = company.NewOrder(customer, orderNumber, Coinage.Coin(Currencies.CODES.USD), 1);
			ExcludeSubtickets excludedSubtickets = new ExcludeSubtickets();

			NextDatesAccumulator nextDatesAccumulator = new NextDatesAccumulator(lotteries, domain, now, pickNumber, dates, states, hours, strIsPresentPlayerBeforeToCloseStore, strUseNextDate);
			var toWinAccumulator = new ToWinAccumulator();
			var totalAndPossibleError = new TotalOrderAndExclusionsByToWin(2);
			totalAndPossibleError = company.TotalTicketOrderAndExclusionsByToWin(domain, states, dates, numbers, selectionMode, ticketAmount, includedNumbersForInput, gameType, excludedSubtickets, ticketPick3StraightProd, nextDatesAccumulator, toWinAccumulator, totalAndPossibleError, "USD", string.Empty);
			Assert.AreEqual(string.Empty, totalAndPossibleError.ErrorMessage);
			Assert.AreEqual(199.5m, totalAndPossibleError.TotalOrder);
			var excludeSubticketsInResponse = totalAndPossibleError.ExclusionsForMultiSelectionInputs.ToList();
			Assert.AreEqual(0, excludeSubticketsInResponse.Count);

			var drawDate = new DateTime(2019, 04, 17, 10, 20, 00);
			lotteries.Risks.Risk.UpdateToWin(int.Parse(pickNumber), 100m);

			//#1.2
			selectionMode = "balls";
			numbers = "3,2,10";
			includedNumbersForInput = "";
			nextDatesAccumulator = new NextDatesAccumulator(lotteries, domain, now, pickNumber, dates, states, hours, strIsPresentPlayerBeforeToCloseStore, strUseNextDate);
			totalAndPossibleError = company.TotalTicketOrderAndExclusionsByToWin(domain, states, dates, numbers, selectionMode, ticketAmount, includedNumbersForInput, gameType, excludedSubtickets, ticketPick3StraightProd, nextDatesAccumulator, toWinAccumulator, totalAndPossibleError, "USD", string.Empty);
			Assert.AreEqual(MSG_COMBINATION_EXCEEDED, totalAndPossibleError.ErrorMessage);
			Assert.AreEqual(199.5m, totalAndPossibleError.TotalOrder);
			Assert.IsTrue(totalAndPossibleError.IsValidToRetryPurchase);
			excludeSubticketsInResponse = totalAndPossibleError.ExclusionsForMultiSelectionBalls.ToList();
			Assert.AreEqual(1, excludeSubticketsInResponse.Count);

			var excludeSubticket = excludeSubticketsInResponse.First();
			Assert.AreEqual("all", excludeSubticket.Subticket);
			Assert.AreEqual("GA", excludeSubticket.StateAbb);
			Assert.AreEqual("10:20 AM", excludeSubticket.HourFormattedAsText);
			Assert.AreEqual("4/17/2019", excludeSubticket.DateFormattedAsText);

			//#2.1
			selectionMode = "MultipleInputSingleAmount";
			numbers = "";
			includedNumbersForInput = "321,320";
			lotteries.Risks.Risk.UpdateToWin(int.Parse(pickNumber), 100000m);

			nextDatesAccumulator = new NextDatesAccumulator(lotteries, domain, now, pickNumber, dates, states, hours, strIsPresentPlayerBeforeToCloseStore, strUseNextDate);
			toWinAccumulator = new ToWinAccumulator();
			totalAndPossibleError = new TotalOrderAndExclusionsByToWin(2);
			totalAndPossibleError = company.TotalTicketOrderAndExclusionsByToWin(domain, states, dates, numbers, selectionMode, ticketAmount, includedNumbersForInput, gameType, excludedSubtickets, ticketPick3StraightProd, nextDatesAccumulator, toWinAccumulator, totalAndPossibleError, "USD", string.Empty);
			Assert.AreEqual(string.Empty, totalAndPossibleError.ErrorMessage);
			Assert.AreEqual(199.5m, totalAndPossibleError.TotalOrder);
			nextDatesAccumulator = new NextDatesAccumulator(lotteries, domain, now, pickNumber, dates, states, hours, strIsPresentPlayerBeforeToCloseStore, strUseNextDate);
			var order = company.CreateTicketOrder(lotteries, player, states, dates, withFireballs, numbers, selectionMode, ticketAmount, includedNumbersForInput, excludedSubtickets, gameType, myOrder, ticketPick3StraightProd, nextDatesAccumulator, domain);

			//#2.2
			selectionMode = "balls";
			numbers = "3,2,10";
			includedNumbersForInput = "";
			excludedSubtickets = new ExcludeSubtickets();
			excludedSubtickets.Add(state2, "10:20 AM", "04/17/2019", "all");
			nextDatesAccumulator = new NextDatesAccumulator(lotteries, domain, now, pickNumber, dates, states, hours, strIsPresentPlayerBeforeToCloseStore, strUseNextDate);
			totalAndPossibleError = company.TotalTicketOrderAndExclusionsByToWin(domain, states, dates, numbers, selectionMode, ticketAmount, includedNumbersForInput, gameType, excludedSubtickets, ticketPick3StraightProd, nextDatesAccumulator, toWinAccumulator, totalAndPossibleError, "USD", string.Empty);
			Assert.AreEqual(string.Empty, totalAndPossibleError.ErrorMessage);
			Assert.AreEqual(199.5m, totalAndPossibleError.TotalOrder);
			nextDatesAccumulator = new NextDatesAccumulator(lotteries, domain, now, pickNumber, dates, states, hours, strIsPresentPlayerBeforeToCloseStore, strUseNextDate);
			order = company.CreateTicketOrder(lotteries, player, states, dates, withFireballs, numbers, selectionMode, ticketAmount, includedNumbersForInput, excludedSubtickets, gameType, myOrder, ticketPick3StraightProd, nextDatesAccumulator, domain);

			company.AddOrders(myOrder);
			int theLowestBetId = 1;
			int theHighestBetId = 1;
			int ticketNumber = *********;
			var marketplace = new Marketplace(company, "CR");
			var agent = marketplace.AddAgent("1");
			myOrder.Agent = (GamesEngine.Exchange.Agent)agent;
			company.PurchaseTickets(false, myOrder, now, ticketNumber, domain, theLowestBetId, theHighestBetId, orderNumber);
			lotteries.CreateWagers(false, theLowestBetId, theHighestBetId, 1, 2, ticketNumber, orderNumber, now);
		}

		[TestMethod]
		public void TotalTicketOrderAndExcludedByToWin_BuyingTwiceInputsAndBallsWithTheSameNumbers()
		{
			Company company = new Company();
			company.Sales.CreateStore(1, "Test Store").MakeCurrent();
			company.Accounting = new MockAccounting();
			var lotteries = company.Lotto900();
			CurrenciesTest.AddCurrencies();

			DateTime now = new DateTime(2019, 04, 17, 9, 00, 00);
			State state = lotteries.GetOrCreateState("VA", "Virginia", now, "Bart");
			LotteryPick<Pick3> lottery = (LotteryPick<Pick3>)lotteries.GetOrCreateLottery(3, state);
			for (int i = 0; i <= 6; i++) lottery.Every(10, 20, i, now, "Bart");
			for (int i = 0; i <= 6; i++) lottery.Every(13, 20, i, now, "Bart");

			var state2 = lotteries.GetOrCreateState("GA", "Georgia", now, "Bart");
			var lottery2 = (LotteryPick<Pick3>)lotteries.GetOrCreateLottery(3, state2);
			for (int i = 0; i <= 6; i++) lottery2.Every(10, 20, i, now, "Bart");
			for (int i = 0; i <= 6; i++) lottery2.Every(13, 20, i, now, "Bart");

			Player player = company.GetOrCreateCustomerById("*********").Player;
			string states = "GA";
			string hours = "10:20 AM";
			string strIsPresentPlayerBeforeToCloseStore = "true";
			string strUseNextDate = "true";
			string dates = "04/17/2019";
			string withFireballs = "false";
			string numbers = "";
			string selectionMode = "MultipleInputSingleAmount";
			string pickNumber = "3";
			decimal ticketAmount = 49.75m;
			string includedNumbersForInput = "321,320";
			string gameType = "Boxed";

			Product ticketPick3StraightProd = lotteries.GetOrCreateProductById(1);
			ticketPick3StraightProd.Description = "Ticket pick 3 straight";
			ticketPick3StraightProd.Price = 1;
			Domain domain = company.Sales.CreateDomain(false, 1, "localhost", Agents.INSIDER);
			Customer customer = company.CustomerByPlayer(player);

			//#1.1
			int orderNumber = 1;
			var myOrder = company.NewOrder(customer, orderNumber, Coinage.Coin(Currencies.CODES.USD), 1);
			ExcludeSubtickets excludedSubtickets = new ExcludeSubtickets();

			NextDatesAccumulator nextDatesAccumulator = new NextDatesAccumulator(lotteries, domain, now, pickNumber, dates, states, hours, strIsPresentPlayerBeforeToCloseStore, strUseNextDate);
			var toWinAccumulator = new ToWinAccumulator();
			var totalAndPossibleError = new TotalOrderAndExclusionsByToWin(2);
			totalAndPossibleError = company.TotalTicketOrderAndExclusionsByToWin(domain, states, dates, numbers, selectionMode, ticketAmount, includedNumbersForInput, gameType, ticketPick3StraightProd, nextDatesAccumulator, toWinAccumulator, totalAndPossibleError, "USD", string.Empty);
			Assert.AreEqual(string.Empty, totalAndPossibleError.ErrorMessage);
			Assert.AreEqual(99.5m, totalAndPossibleError.TotalOrder);
			nextDatesAccumulator = new NextDatesAccumulator(lotteries, domain, now, pickNumber, dates, states, hours, strIsPresentPlayerBeforeToCloseStore, strUseNextDate);
			var order = company.CreateTicketOrder(lotteries, player, states, dates, withFireballs, numbers, selectionMode, ticketAmount, includedNumbersForInput, excludedSubtickets, gameType, myOrder, ticketPick3StraightProd, nextDatesAccumulator, domain);

			//#1.2
			states = "VA";
			selectionMode = "balls";
			numbers = "3,2,10";
			includedNumbersForInput = "";
			nextDatesAccumulator = new NextDatesAccumulator(lotteries, domain, now, pickNumber, dates, states, hours, strIsPresentPlayerBeforeToCloseStore, strUseNextDate);
			totalAndPossibleError = company.TotalTicketOrderAndExclusionsByToWin(domain, states, dates, numbers, selectionMode, ticketAmount, includedNumbersForInput, gameType, ticketPick3StraightProd, nextDatesAccumulator, toWinAccumulator, totalAndPossibleError, "USD", string.Empty);
			Assert.AreEqual(string.Empty, totalAndPossibleError.ErrorMessage);
			Assert.AreEqual(199m, totalAndPossibleError.TotalOrder);
			nextDatesAccumulator = new NextDatesAccumulator(lotteries, domain, now, pickNumber, dates, states, hours, strIsPresentPlayerBeforeToCloseStore, strUseNextDate);
			order = company.CreateTicketOrder(lotteries, player, states, dates, withFireballs, numbers, selectionMode, ticketAmount, includedNumbersForInput, excludedSubtickets, gameType, myOrder, ticketPick3StraightProd, nextDatesAccumulator, domain);

			company.AddOrders(myOrder);
			int theLowestBetId = 1;
			int theHighestBetId = 1;
			int ticketNumber = *********;
			var marketplace = new Marketplace(company, "CR");
			var agent = marketplace.AddAgent("1");
			myOrder.Agent = (GamesEngine.Exchange.Agent)agent;
			company.PurchaseTickets(false, myOrder, now, ticketNumber, domain, theLowestBetId, theHighestBetId, orderNumber);
			lotteries.CreateWagers(false, theLowestBetId, theHighestBetId, 1, 2, ticketNumber, orderNumber, now);

			var drawDate = new DateTime(2019, 04, 17, 10, 20, 00);
			lotteries.Risks.Risk.UpdateToWin(int.Parse(pickNumber), 1000m);

			//#2.1
			states = "GA";
			selectionMode = "MultipleInputSingleAmount";
			numbers = "";
			includedNumbersForInput = "321,320";

			nextDatesAccumulator = new NextDatesAccumulator(lotteries, domain, now, pickNumber, dates, states, hours, strIsPresentPlayerBeforeToCloseStore, strUseNextDate);
			toWinAccumulator = new ToWinAccumulator();
			totalAndPossibleError = new TotalOrderAndExclusionsByToWin(2);
			totalAndPossibleError = company.TotalTicketOrderAndExclusionsByToWin(domain, states, dates, numbers, selectionMode, ticketAmount, includedNumbersForInput, gameType, ticketPick3StraightProd, nextDatesAccumulator, toWinAccumulator, totalAndPossibleError, "USD", string.Empty);
			Assert.AreEqual(MSG_COMBINATION_EXCEEDED, totalAndPossibleError.ErrorMessage);
			Assert.AreEqual(0m, totalAndPossibleError.TotalOrder);
			Assert.IsFalse(totalAndPossibleError.IsValidToRetryPurchase);
			var excludeSubticketsInResponse = totalAndPossibleError.ExclusionsForMultiSelectionInputs.ToList();
			Assert.AreEqual(1, excludeSubticketsInResponse.Count);

			var excludeSubticket = excludeSubticketsInResponse.First();
			Assert.AreEqual("all", excludeSubticket.Subticket);
			Assert.AreEqual("GA", excludeSubticket.StateAbb);
			Assert.AreEqual("10:20 AM", excludeSubticket.HourFormattedAsText);
			Assert.AreEqual("4/17/2019", excludeSubticket.DateFormattedAsText);

			//#2.2
			selectionMode = "balls";
			numbers = "3,2,10";
			includedNumbersForInput = "";
			nextDatesAccumulator = new NextDatesAccumulator(lotteries, domain, now, pickNumber, dates, states, hours, strIsPresentPlayerBeforeToCloseStore, strUseNextDate);
			totalAndPossibleError = company.TotalTicketOrderAndExclusionsByToWin(domain, states, dates, numbers, selectionMode, ticketAmount, includedNumbersForInput, gameType, ticketPick3StraightProd, nextDatesAccumulator, toWinAccumulator, totalAndPossibleError, "USD", string.Empty);
			Assert.AreEqual(MSG_COMBINATION_EXCEEDED, totalAndPossibleError.ErrorMessage);
			Assert.AreEqual(0m, totalAndPossibleError.TotalOrder);
			Assert.IsFalse(totalAndPossibleError.IsValidToRetryPurchase);
			excludeSubticketsInResponse = totalAndPossibleError.ExclusionsForMultiSelectionBalls.ToList();
			Assert.AreEqual(1, excludeSubticketsInResponse.Count);

			excludeSubticket = excludeSubticketsInResponse.First();
			Assert.AreEqual("all", excludeSubticket.Subticket);
			Assert.AreEqual("GA", excludeSubticket.StateAbb);
			Assert.AreEqual("10:20 AM", excludeSubticket.HourFormattedAsText);
			Assert.AreEqual("4/17/2019", excludeSubticket.DateFormattedAsText);
		}

		[TestMethod]
		public void TotalTicketOrderAndExcludedByToWin_BuyingInputsAndBallsWithAnExtraNumber()
		{
			Company company = new Company();
			company.Sales.CreateStore(1, "Test Store").MakeCurrent();
			company.Accounting = new MockAccounting();
			var lotteries = company.Lotto900();
			CurrenciesTest.AddCurrencies();

			DateTime now = new DateTime(2019, 04, 17, 9, 00, 00);
			State state = lotteries.GetOrCreateState("VA", "Virginia", now, "Bart");
			LotteryPick<Pick3> lottery = (LotteryPick<Pick3>)lotteries.GetOrCreateLottery(3, state);
			for (int i = 0; i <= 6; i++) lottery.Every(10, 20, i, now, "Bart");
			for (int i = 0; i <= 6; i++) lottery.Every(13, 20, i, now, "Bart");

			var state2 = lotteries.GetOrCreateState("GA", "Georgia", now, "Bart");
			var lottery2 = (LotteryPick<Pick3>)lotteries.GetOrCreateLottery(3, state2);
			for (int i = 0; i <= 6; i++) lottery2.Every(10, 20, i, now, "Bart");
			for (int i = 0; i <= 6; i++) lottery2.Every(13, 20, i, now, "Bart");

			Player player = company.GetOrCreateCustomerById("*********").Player;
			string states = "GA";
			string hours = "10:20 AM";
			string strIsPresentPlayerBeforeToCloseStore = "true";
			string strUseNextDate = "true";
			string dates = "04/17/2019";
			string withFireballs = "false";
			string numbers = "";
			string selectionMode = "MultipleInputSingleAmount";
			string pickNumber = "3";
			decimal ticketAmount = 50m;
			string includedNumbersForInput = "321";
			string gameType = "Boxed";

			Product ticketPick3StraightProd = lotteries.GetOrCreateProductById(1);
			ticketPick3StraightProd.Description = "Ticket pick 3 straight";
			ticketPick3StraightProd.Price = 1;
			Domain domain = company.Sales.CreateDomain(false, 1, "localhost", Agents.INSIDER);
			Customer customer = company.CustomerByPlayer(player);

			//#1.1
			int orderNumber = 1;
			var myOrder = company.NewOrder(customer, orderNumber, Coinage.Coin(Currencies.CODES.USD), 1);
			ExcludeSubtickets excludedSubtickets = new ExcludeSubtickets();

			NextDatesAccumulator nextDatesAccumulator = new NextDatesAccumulator(lotteries, domain, now, pickNumber, dates, states, hours, strIsPresentPlayerBeforeToCloseStore, strUseNextDate);
			var toWinAccumulator = new ToWinAccumulator();
			var totalAndPossibleError = new TotalOrderAndExclusionsByToWin(2);
			totalAndPossibleError = company.TotalTicketOrderAndExclusionsByToWin(domain, states, dates, numbers, selectionMode, ticketAmount, includedNumbersForInput, gameType, excludedSubtickets, ticketPick3StraightProd, nextDatesAccumulator, toWinAccumulator, totalAndPossibleError, "USD", string.Empty);
			Assert.AreEqual(string.Empty, totalAndPossibleError.ErrorMessage);
			Assert.AreEqual(50m, totalAndPossibleError.TotalOrder);
			nextDatesAccumulator = new NextDatesAccumulator(lotteries, domain, now, pickNumber, dates, states, hours, strIsPresentPlayerBeforeToCloseStore, strUseNextDate);
			var order = company.CreateTicketOrder(lotteries, player, states, dates, withFireballs, numbers, selectionMode, ticketAmount, includedNumbersForInput, excludedSubtickets, gameType, myOrder, ticketPick3StraightProd, nextDatesAccumulator, domain);

			//#1.2
			states = "VA";
			selectionMode = "balls";
			numbers = "3,2,10";
			includedNumbersForInput = "";
			nextDatesAccumulator = new NextDatesAccumulator(lotteries, domain, now, pickNumber, dates, states, hours, strIsPresentPlayerBeforeToCloseStore, strUseNextDate);
			totalAndPossibleError = company.TotalTicketOrderAndExclusionsByToWin(domain, states, dates, numbers, selectionMode, ticketAmount, includedNumbersForInput, gameType, excludedSubtickets, ticketPick3StraightProd, nextDatesAccumulator, toWinAccumulator, totalAndPossibleError, "USD", string.Empty);
			Assert.AreEqual(string.Empty, totalAndPossibleError.ErrorMessage);
			Assert.AreEqual(150m, totalAndPossibleError.TotalOrder);
			nextDatesAccumulator = new NextDatesAccumulator(lotteries, domain, now, pickNumber, dates, states, hours, strIsPresentPlayerBeforeToCloseStore, strUseNextDate);
			order = company.CreateTicketOrder(lotteries, player, states, dates, withFireballs, numbers, selectionMode, ticketAmount, includedNumbersForInput, excludedSubtickets, gameType, myOrder, ticketPick3StraightProd, nextDatesAccumulator, domain);

			company.AddOrders(myOrder);
			int theLowestBetId = 1;
			int theHighestBetId = 1;
			int ticketNumber = *********;
			var marketplace = new Marketplace(company, "CR");
			var agent = marketplace.AddAgent("1");
			myOrder.Agent = (GamesEngine.Exchange.Agent)agent;
			company.PurchaseTickets(false, myOrder, now, ticketNumber, domain, theLowestBetId, theHighestBetId, orderNumber);
			lotteries.CreateWagers(false, theLowestBetId, theHighestBetId, 1, 2, ticketNumber, orderNumber, now);

			var drawDate = new DateTime(2019, 04, 17, 10, 20, 00);
			lotteries.Risks.Risk.UpdateToWin(int.Parse(pickNumber), 1000m);

			//#2.1
			states = "GA";
			selectionMode = "MultipleInputSingleAmount";
			numbers = "";
			includedNumbersForInput = "321";
			orderNumber = 2;
			myOrder = company.NewOrder(customer, orderNumber, Coinage.Coin(Currencies.CODES.USD), 2);
			excludedSubtickets = new ExcludeSubtickets();
			nextDatesAccumulator = new NextDatesAccumulator(lotteries, domain, now, pickNumber, dates, states, hours, strIsPresentPlayerBeforeToCloseStore, strUseNextDate);
			toWinAccumulator = new ToWinAccumulator();
			totalAndPossibleError = new TotalOrderAndExclusionsByToWin(2);
			totalAndPossibleError = company.TotalTicketOrderAndExclusionsByToWin(domain, states, dates, numbers, selectionMode, ticketAmount, includedNumbersForInput, gameType, excludedSubtickets, ticketPick3StraightProd, nextDatesAccumulator, toWinAccumulator, totalAndPossibleError, "USD", string.Empty);
			Assert.AreEqual(MSG_COMBINATION_EXCEEDED, totalAndPossibleError.ErrorMessage);
			Assert.AreEqual(0m, totalAndPossibleError.TotalOrder);
			Assert.IsFalse(totalAndPossibleError.IsValidToRetryPurchase);
			var excludeSubticketsInResponse = totalAndPossibleError.ExclusionsForMultiSelectionInputs.ToList();
			Assert.AreEqual(1, excludeSubticketsInResponse.Count);

			var excludeSubticket = excludeSubticketsInResponse.First();
			Assert.AreEqual("all", excludeSubticket.Subticket);
			Assert.AreEqual("GA", excludeSubticket.StateAbb);
			Assert.AreEqual("10:20 AM", excludeSubticket.HourFormattedAsText);
			Assert.AreEqual("4/17/2019", excludeSubticket.DateFormattedAsText);

			//#2.2
			selectionMode = "balls";
			numbers = "3,2,10";
			includedNumbersForInput = "";
			nextDatesAccumulator = new NextDatesAccumulator(lotteries, domain, now, pickNumber, dates, states, hours, strIsPresentPlayerBeforeToCloseStore, strUseNextDate);
			totalAndPossibleError = company.TotalTicketOrderAndExclusionsByToWin(domain, states, dates, numbers, selectionMode, ticketAmount, includedNumbersForInput, gameType, excludedSubtickets, ticketPick3StraightProd, nextDatesAccumulator, toWinAccumulator, totalAndPossibleError, "USD", string.Empty);
			Assert.AreEqual(MSG_COMBINATION_EXCEEDED, totalAndPossibleError.ErrorMessage);
			Assert.AreEqual(0m, totalAndPossibleError.TotalOrder);
			Assert.IsFalse(totalAndPossibleError.IsValidToRetryPurchase);
			excludeSubticketsInResponse = totalAndPossibleError.ExclusionsForMultiSelectionBalls.ToList();
			Assert.AreEqual(1, excludeSubticketsInResponse.Count);

			excludeSubticket = excludeSubticketsInResponse.First();
			Assert.AreEqual("all", excludeSubticket.Subticket);
			Assert.AreEqual("GA", excludeSubticket.StateAbb);
			Assert.AreEqual("10:20 AM", excludeSubticket.HourFormattedAsText);
			Assert.AreEqual("4/17/2019", excludeSubticket.DateFormattedAsText);

			//#3.1
			selectionMode = "MultipleInputSingleAmount";
			numbers = "";
			includedNumbersForInput = "321";
			orderNumber = 2;
			myOrder = company.NewOrder(customer, orderNumber, Coinage.Coin(Currencies.CODES.USD), 3);
			excludedSubtickets = new ExcludeSubtickets();
			excludedSubtickets.Add(state2, "10:20 AM", "04/17/2019", "321");
			nextDatesAccumulator = new NextDatesAccumulator(lotteries, domain, now, pickNumber, dates, states, hours, strIsPresentPlayerBeforeToCloseStore, strUseNextDate);
			toWinAccumulator = new ToWinAccumulator();
			totalAndPossibleError = new TotalOrderAndExclusionsByToWin(2);
			totalAndPossibleError = company.TotalTicketOrderAndExclusionsByToWin(domain, states, dates, numbers, selectionMode, ticketAmount, includedNumbersForInput, gameType, excludedSubtickets, ticketPick3StraightProd, nextDatesAccumulator, toWinAccumulator, totalAndPossibleError, "USD", string.Empty);
			Assert.AreEqual(MSG_COMBINATION_EXCEEDED, totalAndPossibleError.ErrorMessage);
			Assert.AreEqual(0m, totalAndPossibleError.TotalOrder);
			Assert.IsFalse(totalAndPossibleError.IsValidToRetryPurchase);
			excludeSubticketsInResponse = totalAndPossibleError.ExclusionsForMultiSelectionInputs.ToList();
			Assert.AreEqual(0, excludeSubticketsInResponse.Count);

			nextDatesAccumulator = new NextDatesAccumulator(lotteries, domain, now, pickNumber, dates, states, hours, strIsPresentPlayerBeforeToCloseStore, strUseNextDate);
			order = company.CreateTicketOrder(lotteries, player, states, dates, withFireballs, numbers, selectionMode, ticketAmount, includedNumbersForInput, excludedSubtickets, gameType, myOrder, ticketPick3StraightProd, nextDatesAccumulator, domain);

			drawDate = new DateTime(2019, 04, 17, 10, 20, 00);
			lotteries.Risks.Risk.UpdateToWin(int.Parse(pickNumber), 1000000m);

			//#3.2
			selectionMode = "balls";
			numbers = "3,2,10";
			includedNumbersForInput = "";
			nextDatesAccumulator = new NextDatesAccumulator(lotteries, domain, now, pickNumber, dates, states, hours, strIsPresentPlayerBeforeToCloseStore, strUseNextDate);
			totalAndPossibleError = company.TotalTicketOrderAndExclusionsByToWin(domain, states, dates, numbers, selectionMode, ticketAmount, includedNumbersForInput, gameType, excludedSubtickets, ticketPick3StraightProd, nextDatesAccumulator, toWinAccumulator, totalAndPossibleError, "USD", string.Empty);
			Assert.AreEqual(string.Empty, totalAndPossibleError.ErrorMessage);
			Assert.AreEqual(50m, totalAndPossibleError.TotalOrder);
			nextDatesAccumulator = new NextDatesAccumulator(lotteries, domain, now, pickNumber, dates, states, hours, strIsPresentPlayerBeforeToCloseStore, strUseNextDate);
			order = company.CreateTicketOrder(lotteries, player, states, dates, withFireballs, numbers, selectionMode, ticketAmount, includedNumbersForInput, excludedSubtickets, gameType, myOrder, ticketPick3StraightProd, nextDatesAccumulator, domain);

			company.AddOrders(myOrder);
			theLowestBetId = 1;
			theHighestBetId = 1;
			ticketNumber = *********;
			myOrder.Agent = (GamesEngine.Exchange.Agent)agent;
			company.PurchaseTickets(false, myOrder, now, ticketNumber, domain, theLowestBetId, theHighestBetId, orderNumber);
			lotteries.CreateWagers(false, theLowestBetId, theHighestBetId, 1, 2, ticketNumber, orderNumber, now);
		}

		[TestMethod]
		public void TotalTicketOrderAndExcludedByToWin_BuyingInputsAndBallsAfterSinglePurchase()
		{
			Company company = new Company();
			company.Sales.CreateStore(1, "Test Store").MakeCurrent();
			company.Accounting = new MockAccounting();
			var lotteries = company.Lotto900();
			CurrenciesTest.AddCurrencies();

			DateTime now = new DateTime(2019, 04, 17, 9, 00, 00);
			State state = lotteries.GetOrCreateState("VA", "Virginia", now, "Bart");
			var lottery = (LotteryPick<Pick4>)lotteries.GetOrCreateLottery(4, state);
			for (int i = 0; i <= 6; i++) lottery.Every(10, 20, i, now, "Bart");
			for (int i = 0; i <= 6; i++) lottery.Every(13, 20, i, now, "Bart");

			var state2 = lotteries.GetOrCreateState("GA", "Georgia", now, "Bart");
			var lottery2 = (LotteryPick<Pick4>)lotteries.GetOrCreateLottery(4, state2);
			for (int i = 0; i <= 6; i++) lottery2.Every(10, 20, i, now, "Bart");
			for (int i = 0; i <= 6; i++) lottery2.Every(13, 20, i, now, "Bart");

			Player player = company.GetOrCreateCustomerById("*********").Player;
			string states = "GA";
			string hours = "10:20 AM";
			string strIsPresentPlayerBeforeToCloseStore = "true";
			string strUseNextDate = "true";
			string dates = "04/17/2019";
			string withFireballs = "false";
			string numbers = "";
			string selectionMode = "MultipleInputSingleAmount";
			string pickNumber = "4";
			decimal ticketAmount = 20m;
			string includedNumbersForInput = "3210";
			string gameType = "Boxed";

			Product ticketPick3StraightProd = lotteries.GetOrCreateProductById(1);
			ticketPick3StraightProd.Description = "Ticket pick 3 straight";
			ticketPick3StraightProd.Price = 1;
			Domain domain = company.Sales.CreateDomain(false, 1, "localhost", Agents.INSIDER);
			var risk = lotteries.Risks.Risk;
			Customer customer = company.CustomerByPlayer(player);

			//#1
			int orderNumber = 1;
			var myOrder = company.NewOrder(customer, orderNumber, Coinage.Coin(Currencies.CODES.USD), 1);
			var excludedSubtickets = new ExcludeSubtickets();
			now = new DateTime(2019, 04, 17, 10, 00, 00);

			NextDatesAccumulator nextDatesAccumulator = new NextDatesAccumulator(lotteries, domain, now, pickNumber, dates, states, hours, strIsPresentPlayerBeforeToCloseStore, strUseNextDate);
			var toWinAccumulator = new ToWinAccumulator();
			var totalAndPossibleError = new TotalOrderAndExclusionsByToWin(1);
			totalAndPossibleError = company.TotalTicketOrderAndExclusionsByToWin(domain, states, dates, numbers, selectionMode, ticketAmount, includedNumbersForInput, gameType, ticketPick3StraightProd, nextDatesAccumulator, toWinAccumulator, totalAndPossibleError, "USD", string.Empty);
			Assert.AreEqual(string.Empty, totalAndPossibleError.ErrorMessage);
			Assert.AreEqual(20m, totalAndPossibleError.TotalOrder);

			nextDatesAccumulator = new NextDatesAccumulator(lotteries, domain, now, pickNumber, dates, states, hours, strIsPresentPlayerBeforeToCloseStore, strUseNextDate);
			var order = company.CreateTicketOrder(lotteries, player, states, dates, withFireballs, numbers, selectionMode, ticketAmount, includedNumbersForInput, excludedSubtickets, gameType, myOrder, ticketPick3StraightProd, nextDatesAccumulator, domain);
			company.AddOrders(myOrder);
			int theLowestBetId = 1;
			int theHighestBetId = 1;
			var ticketNumber = *********;
			var marketplace = new Marketplace(company, "CR");
			var agent = marketplace.AddAgent("1");
			myOrder.Agent = (GamesEngine.Exchange.Agent)agent;
			company.PurchaseTickets(false, myOrder, now, ticketNumber, domain, theLowestBetId, theHighestBetId, orderNumber);
			lotteries.CreateWagers(false, theLowestBetId, theHighestBetId, 1, 2, ticketNumber, orderNumber, now);

			var drawDate = new DateTime(2019, 04, 17, 10, 20, 00);
			var toWins = risk.GetRiskPerLottery(int.Parse(pickNumber), lottery2).ToWinPerSubticketsAt(drawDate);
			var toWin = toWins.First();
			Assert.AreEqual(7500m, toWin.Amount);
			risk.UpdateToWin(int.Parse(pickNumber), 8000m);

			//#2.1
			selectionMode = "MultipleInputSingleAmount";
			numbers = "";
			includedNumbersForInput = "3210";
			orderNumber = 2;
			myOrder = company.NewOrder(customer, orderNumber, Coinage.Coin(Currencies.CODES.USD), 2);
			excludedSubtickets = new ExcludeSubtickets();
			nextDatesAccumulator = new NextDatesAccumulator(lotteries, domain, now, pickNumber, dates, states, hours, strIsPresentPlayerBeforeToCloseStore, strUseNextDate);
			toWinAccumulator = new ToWinAccumulator();
			totalAndPossibleError = new TotalOrderAndExclusionsByToWin(2);
			totalAndPossibleError = company.TotalTicketOrderAndExclusionsByToWin(domain, states, dates, numbers, selectionMode, ticketAmount, includedNumbersForInput, gameType, excludedSubtickets, ticketPick3StraightProd, nextDatesAccumulator, toWinAccumulator, totalAndPossibleError, "USD", string.Empty);
			Assert.AreEqual(MSG_COMBINATION_EXCEEDED, totalAndPossibleError.ErrorMessage);
			Assert.AreEqual(0m, totalAndPossibleError.TotalOrder);
			Assert.IsFalse(totalAndPossibleError.IsValidToRetryPurchase);
			var excludeSubticketsInResponse = totalAndPossibleError.ExclusionsForMultiSelectionInputs.ToList();
			Assert.AreEqual(1, excludeSubticketsInResponse.Count);

			var excludeSubticket = excludeSubticketsInResponse.First();
			Assert.AreEqual("all", excludeSubticket.Subticket);
			Assert.AreEqual("GA", excludeSubticket.StateAbb);
			Assert.AreEqual("10:20 AM", excludeSubticket.HourFormattedAsText);
			Assert.AreEqual("4/17/2019", excludeSubticket.DateFormattedAsText);

			//#2.2
			ticketAmount = 2m;
			selectionMode = "balls";
			numbers = "3,2,1,1";
			includedNumbersForInput = "";
			nextDatesAccumulator = new NextDatesAccumulator(lotteries, domain, now, pickNumber, dates, states, hours, strIsPresentPlayerBeforeToCloseStore, strUseNextDate);
			totalAndPossibleError = company.TotalTicketOrderAndExclusionsByToWin(domain, states, dates, numbers, selectionMode, ticketAmount, includedNumbersForInput, gameType, excludedSubtickets, ticketPick3StraightProd, nextDatesAccumulator, toWinAccumulator, totalAndPossibleError, "USD", string.Empty);
			Assert.AreEqual(MSG_COMBINATION_EXCEEDED, totalAndPossibleError.ErrorMessage);
			Assert.AreEqual(2m, totalAndPossibleError.TotalOrder);
			Assert.IsTrue(totalAndPossibleError.IsValidToRetryPurchase);
			excludeSubticketsInResponse = totalAndPossibleError.ExclusionsForMultiSelectionBalls.ToList();
			Assert.AreEqual(0, excludeSubticketsInResponse.Count);

			//#3.1
			selectionMode = "MultipleInputSingleAmount";
			numbers = "";
			includedNumbersForInput = "3210";
			orderNumber = 2;
			myOrder = company.NewOrder(customer, orderNumber, Coinage.Coin(Currencies.CODES.USD), 3);
			excludedSubtickets = new ExcludeSubtickets();
			excludedSubtickets.Add(state2, "10:20 AM", "04/17/2019", "all");
			nextDatesAccumulator = new NextDatesAccumulator(lotteries, domain, now, pickNumber, dates, states, hours, strIsPresentPlayerBeforeToCloseStore, strUseNextDate);
			toWinAccumulator = new ToWinAccumulator();
			totalAndPossibleError = new TotalOrderAndExclusionsByToWin(2);
			totalAndPossibleError = company.TotalTicketOrderAndExclusionsByToWin(domain, states, dates, numbers, selectionMode, ticketAmount, includedNumbersForInput, gameType, excludedSubtickets, ticketPick3StraightProd, nextDatesAccumulator, toWinAccumulator, totalAndPossibleError, "USD", string.Empty);
			Assert.AreEqual(MSG_COMBINATION_EXCEEDED, totalAndPossibleError.ErrorMessage);
			Assert.AreEqual(0m, totalAndPossibleError.TotalOrder);
			Assert.IsFalse(totalAndPossibleError.IsValidToRetryPurchase);
			excludeSubticketsInResponse = totalAndPossibleError.ExclusionsForMultiSelectionInputs.ToList();
			Assert.AreEqual(0, excludeSubticketsInResponse.Count);

			nextDatesAccumulator = new NextDatesAccumulator(lotteries, domain, now, pickNumber, dates, states, hours, strIsPresentPlayerBeforeToCloseStore, strUseNextDate);
			order = company.CreateTicketOrder(lotteries, player, states, dates, withFireballs, numbers, selectionMode, ticketAmount, includedNumbersForInput, excludedSubtickets, gameType, myOrder, ticketPick3StraightProd, nextDatesAccumulator, domain);

			//#3.2
			selectionMode = "balls";
			numbers = "3,2,1,1";
			includedNumbersForInput = "";
			excludedSubtickets = new ExcludeSubtickets();
			nextDatesAccumulator = new NextDatesAccumulator(lotteries, domain, now, pickNumber, dates, states, hours, strIsPresentPlayerBeforeToCloseStore, strUseNextDate);
			totalAndPossibleError = company.TotalTicketOrderAndExclusionsByToWin(domain, states, dates, numbers, selectionMode, ticketAmount, includedNumbersForInput, gameType, excludedSubtickets, ticketPick3StraightProd, nextDatesAccumulator, toWinAccumulator, totalAndPossibleError, "USD", string.Empty);
			Assert.AreEqual(string.Empty, totalAndPossibleError.ErrorMessage);
			Assert.AreEqual(2m, totalAndPossibleError.TotalOrder);
			nextDatesAccumulator = new NextDatesAccumulator(lotteries, domain, now, pickNumber, dates, states, hours, strIsPresentPlayerBeforeToCloseStore, strUseNextDate);
			order = company.CreateTicketOrder(lotteries, player, states, dates, withFireballs, numbers, selectionMode, ticketAmount, includedNumbersForInput, excludedSubtickets, gameType, myOrder, ticketPick3StraightProd, nextDatesAccumulator, domain);

			company.AddOrders(myOrder);
			theLowestBetId = 1;
			theHighestBetId = 1;
			ticketNumber = *********;
			myOrder.Agent = (GamesEngine.Exchange.Agent)agent;
			company.PurchaseTickets(false, myOrder, now, ticketNumber, domain, theLowestBetId, theHighestBetId, orderNumber);
			lotteries.CreateWagers(false, theLowestBetId, theHighestBetId, 1, 2, ticketNumber, orderNumber, now);
		}

		[TestMethod]
		public void TotalTicketOrderAndExcludedByToWin_BuyingInputsAndBallsAfterSinglePurchaseInverted()
		{
			Company company = new Company();
			company.Sales.CreateStore(1, "Test Store").MakeCurrent();
			company.Accounting = new MockAccounting();
			var lotteries = company.Lotto900();
			CurrenciesTest.AddCurrencies();

			DateTime now = new DateTime(2019, 04, 17, 9, 00, 00);
			State state = lotteries.GetOrCreateState("VA", "Virginia", now, "Bart");
			var lottery = (LotteryPick<Pick4>)lotteries.GetOrCreateLottery(4, state);
			for (int i = 0; i <= 6; i++) lottery.Every(10, 20, i, now, "Bart");
			for (int i = 0; i <= 6; i++) lottery.Every(13, 20, i, now, "Bart");

			var state2 = lotteries.GetOrCreateState("GA", "Georgia", now, "Bart");
			var lottery2 = (LotteryPick<Pick4>)lotteries.GetOrCreateLottery(4, state2);
			for (int i = 0; i <= 6; i++) lottery2.Every(10, 20, i, now, "Bart");
			for (int i = 0; i <= 6; i++) lottery2.Every(13, 20, i, now, "Bart");

			Player player = company.GetOrCreateCustomerById("*********").Player;
			string states = "GA";
			string hours = "10:20 AM";
			string strIsPresentPlayerBeforeToCloseStore = "true";
			string strUseNextDate = "true";
			string dates = "04/17/2019";
			string withFireballs = "false";
			string numbers = "";
			string selectionMode = "MultipleInputSingleAmount";
			string pickNumber = "4";
			decimal ticketAmount = 20m;
			string includedNumbersForInput = "3210";
			string gameType = "Boxed";

			Product ticketPick3StraightProd = lotteries.GetOrCreateProductById(1);
			ticketPick3StraightProd.Description = "Ticket pick 3 straight";
			ticketPick3StraightProd.Price = 1;
			Domain domain = company.Sales.CreateDomain(false, 1, "localhost", Agents.INSIDER);
			var risk = lotteries.Risks.Risk;
			Customer customer = company.CustomerByPlayer(player);

			//#1
			int orderNumber = 1;
			var myOrder = company.NewOrder(customer, orderNumber, Coinage.Coin(Currencies.CODES.USD), 1);
			var excludedSubtickets = new ExcludeSubtickets();
			now = new DateTime(2019, 04, 17, 10, 00, 00);

			NextDatesAccumulator nextDatesAccumulator = new NextDatesAccumulator(lotteries, domain, now, pickNumber, dates, states, hours, strIsPresentPlayerBeforeToCloseStore, strUseNextDate);
			var toWinAccumulator = new ToWinAccumulator();
			var totalAndPossibleError = new TotalOrderAndExclusionsByToWin(1);
			totalAndPossibleError = company.TotalTicketOrderAndExclusionsByToWin(domain, states, dates, numbers, selectionMode, ticketAmount, includedNumbersForInput, gameType, ticketPick3StraightProd, nextDatesAccumulator, toWinAccumulator, totalAndPossibleError, "USD", string.Empty);
			Assert.AreEqual(string.Empty, totalAndPossibleError.ErrorMessage);
			Assert.AreEqual(20m, totalAndPossibleError.TotalOrder);

			nextDatesAccumulator = new NextDatesAccumulator(lotteries, domain, now, pickNumber, dates, states, hours, strIsPresentPlayerBeforeToCloseStore, strUseNextDate);
			var order = company.CreateTicketOrder(lotteries, player, states, dates, withFireballs, numbers, selectionMode, ticketAmount, includedNumbersForInput, excludedSubtickets, gameType, myOrder, ticketPick3StraightProd, nextDatesAccumulator, domain);
			company.AddOrders(myOrder);
			int theLowestBetId = 1;
			int theHighestBetId = 1;
			var ticketNumber = *********;
			var marketplace = new Marketplace(company, "CR");
			var agent = marketplace.AddAgent("1");
			myOrder.Agent = (GamesEngine.Exchange.Agent)agent;
			company.PurchaseTickets(false, myOrder, now, ticketNumber, domain, theLowestBetId, theHighestBetId, orderNumber);
			lotteries.CreateWagers(false, theLowestBetId, theHighestBetId, 1, 2, ticketNumber, orderNumber, now);

			//#2.1
			selectionMode = "MultipleInputSingleAmount";
			numbers = "";
			includedNumbersForInput = "3211";
			orderNumber = 2;
			myOrder = company.NewOrder(customer, orderNumber, Coinage.Coin(Currencies.CODES.USD), 2);
			excludedSubtickets = new ExcludeSubtickets();
			nextDatesAccumulator = new NextDatesAccumulator(lotteries, domain, now, pickNumber, dates, states, hours, strIsPresentPlayerBeforeToCloseStore, strUseNextDate);
			toWinAccumulator = new ToWinAccumulator();
			totalAndPossibleError = new TotalOrderAndExclusionsByToWin(2);
			totalAndPossibleError = company.TotalTicketOrderAndExclusionsByToWin(domain, states, dates, numbers, selectionMode, ticketAmount, includedNumbersForInput, gameType, excludedSubtickets, ticketPick3StraightProd, nextDatesAccumulator, toWinAccumulator, totalAndPossibleError, "USD", string.Empty);
			Assert.AreEqual(string.Empty, totalAndPossibleError.ErrorMessage);
			Assert.AreEqual(20m, totalAndPossibleError.TotalOrder);
			Assert.IsFalse(totalAndPossibleError.IsValidToRetryPurchase);
			var excludeSubticketsInResponse = totalAndPossibleError.ExclusionsForMultiSelectionInputs.ToList();
			Assert.AreEqual(0, excludeSubticketsInResponse.Count);

			var drawDate = new DateTime(2019, 04, 17, 10, 20, 00);
			var toWins = risk.GetRiskPerLottery(int.Parse(pickNumber), lottery2).ToWinPerSubticketsAt(drawDate);
			var toWin = toWins.First();
			Assert.AreEqual(7500m, toWin.Amount);
			risk.UpdateToWin(int.Parse(pickNumber), 8000m);

			//#2.2
			selectionMode = "balls";
			numbers = "3,2,1,0";
			includedNumbersForInput = "";
			nextDatesAccumulator = new NextDatesAccumulator(lotteries, domain, now, pickNumber, dates, states, hours, strIsPresentPlayerBeforeToCloseStore, strUseNextDate);
			totalAndPossibleError = company.TotalTicketOrderAndExclusionsByToWin(domain, states, dates, numbers, selectionMode, ticketAmount, includedNumbersForInput, gameType, excludedSubtickets, ticketPick3StraightProd, nextDatesAccumulator, toWinAccumulator, totalAndPossibleError, "USD", string.Empty);
			Assert.AreEqual(MSG_COMBINATION_EXCEEDED, totalAndPossibleError.ErrorMessage);
			Assert.AreEqual(20m, totalAndPossibleError.TotalOrder);
			Assert.IsTrue(totalAndPossibleError.IsValidToRetryPurchase);
			excludeSubticketsInResponse = totalAndPossibleError.ExclusionsForMultiSelectionBalls.ToList();
			Assert.AreEqual(1, excludeSubticketsInResponse.Count);

			var excludeSubticket = excludeSubticketsInResponse.First();
			Assert.AreEqual("all", excludeSubticket.Subticket);
			Assert.AreEqual("GA", excludeSubticket.StateAbb);
			Assert.AreEqual("10:20 AM", excludeSubticket.HourFormattedAsText);
			Assert.AreEqual("4/17/2019", excludeSubticket.DateFormattedAsText);

			//#3.1
			ticketAmount = 10m;
			selectionMode = "MultipleInputSingleAmount";
			numbers = "";
			includedNumbersForInput = "3211";
			orderNumber = 2;
			myOrder = company.NewOrder(customer, orderNumber, Coinage.Coin(Currencies.CODES.USD), 3);

			nextDatesAccumulator = new NextDatesAccumulator(lotteries, domain, now, pickNumber, dates, states, hours, strIsPresentPlayerBeforeToCloseStore, strUseNextDate);
			toWinAccumulator = new ToWinAccumulator();
			totalAndPossibleError = new TotalOrderAndExclusionsByToWin(2);
			totalAndPossibleError = company.TotalTicketOrderAndExclusionsByToWin(domain, states, dates, numbers, selectionMode, ticketAmount, includedNumbersForInput, gameType, excludedSubtickets, ticketPick3StraightProd, nextDatesAccumulator, toWinAccumulator, totalAndPossibleError, "USD", string.Empty);
			Assert.AreEqual(string.Empty, totalAndPossibleError.ErrorMessage);
			Assert.AreEqual(10m, totalAndPossibleError.TotalOrder);
			Assert.IsFalse(totalAndPossibleError.IsValidToRetryPurchase);
			excludeSubticketsInResponse = totalAndPossibleError.ExclusionsForMultiSelectionInputs.ToList();
			Assert.AreEqual(0, excludeSubticketsInResponse.Count);

			nextDatesAccumulator = new NextDatesAccumulator(lotteries, domain, now, pickNumber, dates, states, hours, strIsPresentPlayerBeforeToCloseStore, strUseNextDate);
			order = company.CreateTicketOrder(lotteries, player, states, dates, withFireballs, numbers, selectionMode, ticketAmount, includedNumbersForInput, excludedSubtickets, gameType, myOrder, ticketPick3StraightProd, nextDatesAccumulator, domain);

			//#3.2
			selectionMode = "balls";
			numbers = "3,2,1,0";
			includedNumbersForInput = "";
			excludedSubtickets = new ExcludeSubtickets();
			excludedSubtickets.Add(state2, "10:20 AM", "04/17/2019", "all");
			nextDatesAccumulator = new NextDatesAccumulator(lotteries, domain, now, pickNumber, dates, states, hours, strIsPresentPlayerBeforeToCloseStore, strUseNextDate);
			totalAndPossibleError = company.TotalTicketOrderAndExclusionsByToWin(domain, states, dates, numbers, selectionMode, ticketAmount, includedNumbersForInput, gameType, excludedSubtickets, ticketPick3StraightProd, nextDatesAccumulator, toWinAccumulator, totalAndPossibleError, "USD", string.Empty);
			Assert.AreEqual(string.Empty, totalAndPossibleError.ErrorMessage);
			Assert.AreEqual(10m, totalAndPossibleError.TotalOrder);
			nextDatesAccumulator = new NextDatesAccumulator(lotteries, domain, now, pickNumber, dates, states, hours, strIsPresentPlayerBeforeToCloseStore, strUseNextDate);
			order = company.CreateTicketOrder(lotteries, player, states, dates, withFireballs, numbers, selectionMode, ticketAmount, includedNumbersForInput, excludedSubtickets, gameType, myOrder, ticketPick3StraightProd, nextDatesAccumulator, domain);

			company.AddOrders(myOrder);
			theLowestBetId = 1;
			theHighestBetId = 1;
			ticketNumber = *********;
			myOrder.Agent = (GamesEngine.Exchange.Agent)agent;
			company.PurchaseTickets(false, myOrder, now, ticketNumber, domain, theLowestBetId, theHighestBetId, orderNumber);
			lotteries.CreateWagers(false, theLowestBetId, theHighestBetId, 1, 2, ticketNumber, orderNumber, now);
		}

		[TestMethod]
		public void TotalTicketOrderAndExclusionsByToWin_ToWinPerSubticketsAt()
		{
			Company company = new Company();
			company.Sales.CreateStore(1, "Test Store").MakeCurrent();
			company.Accounting = new MockAccounting();
			var lotteries = company.Lotto900();
			CurrenciesTest.AddCurrencies();

			DateTime now = new DateTime(2019, 04, 17, 9, 00, 00);
			State state = lotteries.GetOrCreateState("GA", "Georgia", now, "Bart");
			LotteryPick<Pick3> lottery = (LotteryPick<Pick3>)lotteries.GetOrCreateLottery(3, state);
			for (int i = 0; i <= 6; i++) lottery.Every(10, 20, i, now, "Bart");

			var drawDate = new DateTime(2019, 04, 17, 10, 20, 00);
			var schedule = lottery.GetScheduleOrNullFor(drawDate);
			var scheduleDescription = "Georgia Morning";
			schedule.UpdateDescription(false, scheduleDescription);

			Player player = company.GetOrCreateCustomerById("*********").Player;
			string states = "GA";
			string hours = "10:20 AM";
			string strIsPresentPlayerBeforeToCloseStore = "true";
			string strUseNextDate = "true";
			string dates = "04/17/2019";
			string numbers = "";
			string selectionMode = "MultipleInputSingleAmount";
			string pickNumber = "3";
			decimal ticketAmount = 1m;
			string includedNumbersForInput = "333";
			string gameType = "Boxed";

			Product ticketPick3StraightProd = lotteries.GetOrCreateProductById(1);
			ticketPick3StraightProd.Description = "Ticket pick 3 straight";
			ticketPick3StraightProd.Price = 1;
			Domain domain = company.Sales.CreateDomain(false, 1, "localhost", Agents.INSIDER);
			var risk = lotteries.Risks.Risk;
			risk.UpdateToWin(int.Parse(pickNumber), 300m);

			//#1
			ExcludeSubtickets excludedSubtickets = new ExcludeSubtickets();
			Customer customer = company.CustomerByPlayer(player);
			OrderCart myOrder = company.NewOrder(customer, 1, Coinage.Coin(Currencies.CODES.USD), 1);

			var nextDatesAccumulator = new NextDatesAccumulator(lotteries, domain, now, pickNumber, dates, states, hours, strIsPresentPlayerBeforeToCloseStore, strUseNextDate);
			var toWinAccumulator = new ToWinAccumulator();
			var totalAndPossibleError = new TotalOrderAndExclusionsByToWin(1);
			totalAndPossibleError = company.TotalTicketOrderAndExclusionsByToWin(domain, states, dates, numbers, selectionMode, ticketAmount, includedNumbersForInput, gameType, ticketPick3StraightProd, nextDatesAccumulator, toWinAccumulator, totalAndPossibleError, "USD", string.Empty);
			Assert.AreEqual(MSG_COMBINATION_EXCEEDED, totalAndPossibleError.ErrorMessage);
			Assert.AreEqual(0m, totalAndPossibleError.TotalOrder);

			var toWins = risk.GetRiskPerLottery(int.Parse(pickNumber), lottery).ToWinPerSubticketsAt(drawDate);
			Assert.AreEqual(0, toWins.Count());
		}

		[TestMethod]
		public void TotalTicketOrderAndExclusionsByToWin_ToWinPerSubticketsAt_Boxed()
		{
			Company company = new Company();
			company.Sales.CreateStore(1, "Test Store").MakeCurrent();
			company.Accounting = new MockAccounting();
			var lotteries = company.Lotto900();
			CurrenciesTest.AddCurrencies();
			DateTime now = new DateTime(2019, 04, 17, 9, 00, 00);
			State state = lotteries.GetOrCreateState("GA", "Georgia", now, "Bart");
			LotteryPick<Pick3> lottery = (LotteryPick<Pick3>)lotteries.GetOrCreateLottery(3, state);
			for (int i = 0; i <= 6; i++) lottery.Every(10, 20, i, now, "Bart");

			var drawDate = new DateTime(2019, 04, 17, 10, 20, 00);
			var schedule = lottery.GetScheduleOrNullFor(drawDate);
			var scheduleDescription = "Georgia Morning";
			schedule.UpdateDescription(false, scheduleDescription);

			Player player = company.GetOrCreateCustomerById("*********").Player;
			string states = "GA";
			string hours = "10:20 AM";
			string strIsPresentPlayerBeforeToCloseStore = "true";
			string strUseNextDate = "true";
			string dates = "04/17/2019";
			string withFireballs = "false";
			string numbers = "";
			string selectionMode = "MultipleInputSingleAmount";
			string pickNumber = "3";
			decimal ticketAmount = 1m;
			string includedNumbersForInput = "121";
			string gameType = "Boxed";

			Product ticketPick3StraightProd = lotteries.GetOrCreateProductById(1);
			ticketPick3StraightProd.Description = "Ticket pick 3 straight";
			ticketPick3StraightProd.Price = 1;
			Domain domain = company.Sales.CreateDomain(false, 1, "localhost", Agents.INSIDER);
			var risk = lotteries.Risks.Risk;
			risk.UpdateToWin(int.Parse(pickNumber), 300m);

			//#1
			ExcludeSubtickets excludedSubtickets = new ExcludeSubtickets();
			Customer customer = company.CustomerByPlayer(player);
			OrderCart myOrder = company.NewOrder(customer, 1, Coinage.Coin(Currencies.CODES.USD), 1);

			var nextDatesAccumulator = new NextDatesAccumulator(lotteries, domain, now, pickNumber, dates, states, hours, strIsPresentPlayerBeforeToCloseStore, strUseNextDate);
			var toWinAccumulator = new ToWinAccumulator();
			var totalAndPossibleError = new TotalOrderAndExclusionsByToWin(1);
			totalAndPossibleError = company.TotalTicketOrderAndExclusionsByToWin(domain, states, dates, numbers, selectionMode, ticketAmount, includedNumbersForInput, gameType, ticketPick3StraightProd, nextDatesAccumulator, toWinAccumulator, totalAndPossibleError, "USD", string.Empty);
			Assert.AreEqual(string.Empty, totalAndPossibleError.ErrorMessage);
			Assert.AreEqual(1m, totalAndPossibleError.TotalOrder);

			int orderNumber = 1;
			excludedSubtickets = new ExcludeSubtickets();
			nextDatesAccumulator = new NextDatesAccumulator(lotteries, domain, now, pickNumber, dates, states, hours, strIsPresentPlayerBeforeToCloseStore, strUseNextDate);
			var order = company.CreateTicketOrder(lotteries, player, states, dates, withFireballs, numbers, selectionMode, ticketAmount, includedNumbersForInput, excludedSubtickets, gameType, myOrder, ticketPick3StraightProd, nextDatesAccumulator, domain);
			company.AddOrders(myOrder);
			int theLowestBetId = 1;
			int theHighestBetId = 1;
			var ticketNumber = *********;
			var marketplace = new Marketplace(company, "CR");
			var agent = marketplace.AddAgent("1");
			myOrder.Agent = (GamesEngine.Exchange.Agent)agent;
			company.PurchaseTickets(false, myOrder, now, ticketNumber, domain, theLowestBetId, theHighestBetId, orderNumber);
			lotteries.CreateWagers(false, theLowestBetId, theHighestBetId, 1, 2, ticketNumber, orderNumber, now);

			var toWins = risk.GetRiskPerLottery(int.Parse(pickNumber), lottery).ToWinPerSubticketsAt(drawDate);
			Assert.AreEqual(3, toWins.Count());
			var toWin = toWins.ElementAt(0);
			Assert.AreEqual(300, toWin.Amount);
			Assert.AreEqual("121", toWin.Number);
			toWin = toWins.ElementAt(1);
			Assert.AreEqual(300, toWin.Amount);
			Assert.AreEqual("112", toWin.Number);
			toWin = toWins.ElementAt(2);
			Assert.AreEqual(300, toWin.Amount);
			Assert.AreEqual("211", toWin.Number);

			//#2
			includedNumbersForInput = "111";
			orderNumber = 2;
			myOrder = company.NewOrder(customer, orderNumber, Coinage.Coin(Currencies.CODES.USD), 2);
			excludedSubtickets = new ExcludeSubtickets();
			nextDatesAccumulator = new NextDatesAccumulator(lotteries, domain, now, pickNumber, dates, states, hours, strIsPresentPlayerBeforeToCloseStore, strUseNextDate);
			toWinAccumulator = new ToWinAccumulator();
			totalAndPossibleError = new TotalOrderAndExclusionsByToWin(2);
			totalAndPossibleError = company.TotalTicketOrderAndExclusionsByToWin(domain, states, dates, numbers, selectionMode, ticketAmount, includedNumbersForInput, gameType, excludedSubtickets, ticketPick3StraightProd, nextDatesAccumulator, toWinAccumulator, totalAndPossibleError, "USD", string.Empty);
			Assert.AreEqual(MSG_COMBINATION_EXCEEDED, totalAndPossibleError.ErrorMessage);
			Assert.AreEqual(0m, totalAndPossibleError.TotalOrder);
		}

		[TestMethod]
		public void TotalTicketOrderAndExclusionsByToWin_ToWinPerSubticketsAt_Boxed_FB()
		{
			Company company = new Company();
			company.Sales.CreateStore(1, "Test Store").MakeCurrent();
			company.Accounting = new MockAccounting();
			var lotteries = company.Lotto900();
			CurrenciesTest.AddCurrencies();
			DateTime now = new DateTime(2019, 04, 17, 9, 00, 00);
			State state = lotteries.GetOrCreateState("GA", "Georgia", now, "Bart");
			LotteryPick<Pick3> lottery = (LotteryPick<Pick3>)lotteries.GetOrCreateLottery(3, state);
			for (int i = 0; i <= 6; i++) lottery.Every(10, 20, i, now, "Bart");
			lottery.TurnOnFireBall(false, now, "N/A");

			var drawDate = new DateTime(2019, 04, 17, 10, 20, 00);
			var schedule = lottery.GetScheduleOrNullFor(drawDate);

			Player player = company.GetOrCreateCustomerById("*********").Player;
			string states = "GA";
			string hours = "10:20 AM";
			string strIsPresentPlayerBeforeToCloseStore = "true";
			string strUseNextDate = "true";
			string dates = "04/17/2019";
			string withFireballs = "true";
			string numbers = "";
			string selectionMode = "MultipleInputSingleAmount";
			string pickNumber = "3";
			decimal ticketAmount = 1m;
			string includedNumbersForInput = "121";
			string gameType = "Boxed";

			Product ticketPick3StraightProd = lotteries.GetOrCreateProductById(1);
			ticketPick3StraightProd.Description = "Ticket pick 3 straight";
			ticketPick3StraightProd.Price = 1;
			Domain domain = company.Sales.CreateDomain(false, 1, "localhost", Agents.INSIDER);
			var risk = lotteries.Risks.Risk;
			risk.UpdateToWin(domain, int.Parse(pickNumber), 200m);

			//#1
			ExcludeSubtickets excludedSubtickets = new ExcludeSubtickets();
			Customer customer = company.CustomerByPlayer(player);
			OrderCart myOrder = company.NewOrder(customer, 1, Coinage.Coin(Currencies.CODES.USD), 1);

			var nextDatesAccumulator = new NextDatesAccumulator(lotteries, domain, now, pickNumber, dates, states, hours, withFireballs, strIsPresentPlayerBeforeToCloseStore, strUseNextDate);
			var toWinAccumulator = new ToWinAccumulator();
			var totalAndPossibleError = new TotalOrderAndExclusionsByToWin(1);
			totalAndPossibleError = company.TotalTicketOrderAndExclusionsByToWin(domain, states, dates, withFireballs, numbers, selectionMode, ticketAmount, includedNumbersForInput, gameType, ticketPick3StraightProd, nextDatesAccumulator, toWinAccumulator, totalAndPossibleError, "USD", string.Empty);
			Assert.AreEqual(string.Empty, totalAndPossibleError.ErrorMessage);
			Assert.AreEqual(1m, totalAndPossibleError.TotalOrder);

			int orderNumber = 1;
			excludedSubtickets = new ExcludeSubtickets();
			nextDatesAccumulator = new NextDatesAccumulator(lotteries, domain, now, pickNumber, dates, states, hours, withFireballs, strIsPresentPlayerBeforeToCloseStore, strUseNextDate);
			var order = company.CreateTicketOrder(lotteries, player, states, dates, withFireballs, numbers, selectionMode, ticketAmount, includedNumbersForInput, excludedSubtickets, gameType, myOrder, ticketPick3StraightProd, nextDatesAccumulator, domain);
			company.AddOrders(myOrder);
			int theLowestBetId = 1;
			int theHighestBetId = 1;
			var ticketNumber = *********;
			var marketplace = new Marketplace(company, "CR");
			var agent = marketplace.AddAgent("1");
			myOrder.Agent = (GamesEngine.Exchange.Agent)agent;
			company.PurchaseTickets(false, myOrder, now, ticketNumber, domain, theLowestBetId, theHighestBetId, orderNumber);
			lotteries.CreateWagers(false, theLowestBetId, theHighestBetId, 1, 2, ticketNumber, orderNumber, now);

			var toWins = risk.GetRiskPerLottery(int.Parse(pickNumber), lottery).ToWinPerSubticketsAt(drawDate);
			Assert.AreEqual(3, toWins.Count());
			var toWin = toWins.ElementAt(0);
			Assert.AreEqual(150, toWin.Amount);
			Assert.AreEqual("121", toWin.Number);
			toWin = toWins.ElementAt(1);
			Assert.AreEqual(150, toWin.Amount);
			Assert.AreEqual("112", toWin.Number);
			toWin = toWins.ElementAt(2);
			Assert.AreEqual(150, toWin.Amount);
			Assert.AreEqual("211", toWin.Number);

			//#2
			includedNumbersForInput = "112";
			orderNumber = 2;
			myOrder = company.NewOrder(customer, orderNumber, Coinage.Coin(Currencies.CODES.USD), 2);
			excludedSubtickets = new ExcludeSubtickets();
			nextDatesAccumulator = new NextDatesAccumulator(lotteries, domain, now, pickNumber, dates, states, hours, withFireballs, strIsPresentPlayerBeforeToCloseStore, strUseNextDate);
			toWinAccumulator = new ToWinAccumulator();
			totalAndPossibleError = new TotalOrderAndExclusionsByToWin(2);
			totalAndPossibleError = company.TotalTicketOrderAndExclusionsByToWin(domain, states, dates, withFireballs, numbers, selectionMode, ticketAmount, includedNumbersForInput, gameType, excludedSubtickets, ticketPick3StraightProd, nextDatesAccumulator, toWinAccumulator, totalAndPossibleError, "USD", string.Empty, applyToleranceFactor: false, issued: string.Empty, now);
			Assert.AreEqual(MSG_COMBINATION_EXCEEDED, totalAndPossibleError.ErrorMessage);
			Assert.AreEqual(0m, totalAndPossibleError.TotalOrder);
		}

		[TestMethod]
		public void TotalTicketOrderAndExclusionsByToWin_ToWinPerSubticketsAt_Straight()
		{
			Company company = new Company();
			company.Sales.CreateStore(1, "Test Store").MakeCurrent();
			company.Accounting = new MockAccounting();
			var lotteries = company.Lotto900();
			CurrenciesTest.AddCurrencies();
			DateTime now = new DateTime(2019, 04, 17, 9, 00, 00);
			State state = lotteries.GetOrCreateState("GA", "Georgia", now, "Bart");
			LotteryPick<Pick3> lottery = (LotteryPick<Pick3>)lotteries.GetOrCreateLottery(3, state);
			for (int i = 0; i <= 6; i++) lottery.Every(10, 20, i, now, "Bart");

			var drawDate = new DateTime(2019, 04, 17, 10, 20, 00);
			var schedule = lottery.GetScheduleOrNullFor(drawDate);
			var scheduleDescription = "Georgia Morning";
			schedule.UpdateDescription(false, scheduleDescription);

			Player player = company.GetOrCreateCustomerById("*********").Player;
			string states = "GA";
			string hours = "10:20 AM";
			string strIsPresentPlayerBeforeToCloseStore = "true";
			string strUseNextDate = "true";
			string dates = "04/17/2019";
			string numbers = "";
			string selectionMode = "MultipleInputSingleAmount";
			string pickNumber = "3";
			decimal ticketAmount = 1m;
			string includedNumbersForInput = "121";
			string gameType = "Straight";

			Product ticketPick3StraightProd = lotteries.GetOrCreateProductById(1);
			ticketPick3StraightProd.Description = "Ticket pick 3 straight";
			ticketPick3StraightProd.Price = 1;
			Domain domain = company.Sales.CreateDomain(false, 1, "localhost", Agents.INSIDER);
			var risk = lotteries.Risks.Risk;
			risk.UpdateToWin(int.Parse(pickNumber), 900m);

			//#1
			ExcludeSubtickets excludedSubtickets = new ExcludeSubtickets();
			Customer customer = company.CustomerByPlayer(player);
			OrderCart myOrder = company.NewOrder(customer, 1, Coinage.Coin(Currencies.CODES.USD), 1);

			var nextDatesAccumulator = new NextDatesAccumulator(lotteries, domain, now, pickNumber, dates, states, hours, strIsPresentPlayerBeforeToCloseStore, strUseNextDate);
			var toWinAccumulator = new ToWinAccumulator();
			var totalAndPossibleError = new TotalOrderAndExclusionsByToWin(1);
			totalAndPossibleError = company.TotalTicketOrderAndExclusionsByToWin(domain, states, dates, numbers, selectionMode, ticketAmount, includedNumbersForInput, gameType, ticketPick3StraightProd, nextDatesAccumulator, toWinAccumulator, totalAndPossibleError, "USD", string.Empty);
			Assert.AreEqual(string.Empty, totalAndPossibleError.ErrorMessage);
			Assert.AreEqual(1m, totalAndPossibleError.TotalOrder);

			int orderNumber = 1;
			excludedSubtickets = new ExcludeSubtickets();
			nextDatesAccumulator = new NextDatesAccumulator(lotteries, domain, now, pickNumber, dates, states, hours, strIsPresentPlayerBeforeToCloseStore, strUseNextDate);
			var order = company.CreateTicketOrder(lotteries, player, states, dates, numbers, selectionMode, ticketAmount, includedNumbersForInput, excludedSubtickets, gameType, myOrder, ticketPick3StraightProd, nextDatesAccumulator, domain);
			company.AddOrders(myOrder);
			int theLowestBetId = 1;
			int theHighestBetId = 1;
			var ticketNumber = *********;
			company.PurchaseTickets(false, myOrder, now, ticketNumber, domain, theLowestBetId, theHighestBetId, orderNumber);
			lotteries.CreateWagers(false, theLowestBetId, theHighestBetId, 1, 2, ticketNumber, orderNumber, now);

			var toWins = risk.GetRiskPerLottery(int.Parse(pickNumber), lottery).ToWinPerSubticketsAt(drawDate);
			Assert.AreEqual(1, toWins.Count());
			var toWin = toWins.ElementAt(0);
			Assert.AreEqual(900, toWin.Amount);
			Assert.AreEqual("121", toWin.Number);

			//#2
			ticketAmount = 2m;
			includedNumbersForInput = "111";
			orderNumber = 2;
			myOrder = company.NewOrder(customer, orderNumber, Coinage.Coin(Currencies.CODES.USD), 2);
			excludedSubtickets = new ExcludeSubtickets();
			nextDatesAccumulator = new NextDatesAccumulator(lotteries, domain, now, pickNumber, dates, states, hours, strIsPresentPlayerBeforeToCloseStore, strUseNextDate);
			toWinAccumulator = new ToWinAccumulator();
			totalAndPossibleError = new TotalOrderAndExclusionsByToWin(2);
			totalAndPossibleError = company.TotalTicketOrderAndExclusionsByToWin(domain, states, dates, numbers, selectionMode, ticketAmount, includedNumbersForInput, gameType, excludedSubtickets, ticketPick3StraightProd, nextDatesAccumulator, toWinAccumulator, totalAndPossibleError, "USD", string.Empty);
			Assert.AreEqual(MSG_COMBINATION_EXCEEDED, totalAndPossibleError.ErrorMessage);
			Assert.AreEqual(0m, totalAndPossibleError.TotalOrder);
		}

		[TestMethod]
		public void TotalTicketOrderAndExclusionsByToWin_ToWinPerSubticketsAt_Straight_FB()
		{
			Company company = new Company();
			company.Sales.CreateStore(1, "Test Store").MakeCurrent();
			company.Accounting = new MockAccounting();
			var lotteries = company.Lotto900();
			CurrenciesTest.AddCurrencies();
			DateTime now = new DateTime(2019, 04, 17, 9, 00, 00);
			State state = lotteries.GetOrCreateState("GA", "Georgia", now, "Bart");
			LotteryPick<Pick3> lottery = (LotteryPick<Pick3>)lotteries.GetOrCreateLottery(3, state);
			for (int i = 0; i <= 6; i++) lottery.Every(10, 20, i, now, "Bart");
			lottery.TurnOnFireBall(false, now, "N/A");

			var drawDate = new DateTime(2019, 04, 17, 10, 20, 00);
			var schedule = lottery.GetScheduleOrNullFor(drawDate);

			Player player = company.GetOrCreateCustomerById("*********").Player;
			string states = "GA";
			string hours = "10:20 AM";
			string strIsPresentPlayerBeforeToCloseStore = "true";
			string strUseNextDate = "true";
			string dates = "04/17/2019";
			string withFireballs = "true";
			string numbers = "";
			string selectionMode = "MultipleInputSingleAmount";
			string pickNumber = "3";
			decimal ticketAmount = 1m;
			string includedNumbersForInput = "121";
			string gameType = "Straight";

			Product ticketPick3StraightProd = lotteries.GetOrCreateProductById(1);
			ticketPick3StraightProd.Description = "Ticket pick 3 straight";
			ticketPick3StraightProd.Price = 1;
			Domain domain = company.Sales.CreateDomain(false, 1, "localhost", Agents.INSIDER);
			var risk = lotteries.Risks.Risk;
			risk.UpdateToWin(domain, int.Parse(pickNumber), 900m);

			//#1
			ExcludeSubtickets excludedSubtickets = new ExcludeSubtickets();
			Customer customer = company.CustomerByPlayer(player);
			OrderCart myOrder = company.NewOrder(customer, 1, Coinage.Coin(Currencies.CODES.USD), 1);

			var nextDatesAccumulator = new NextDatesAccumulator(lotteries, domain, now, pickNumber, dates, states, hours, withFireballs, strIsPresentPlayerBeforeToCloseStore, strUseNextDate);
			var toWinAccumulator = new ToWinAccumulator();
			var totalAndPossibleError = new TotalOrderAndExclusionsByToWin(1);
			totalAndPossibleError = company.TotalTicketOrderAndExclusionsByToWin(domain, states, dates, withFireballs, numbers, selectionMode, ticketAmount, includedNumbersForInput, gameType, ticketPick3StraightProd, nextDatesAccumulator, toWinAccumulator, totalAndPossibleError, "USD", string.Empty);
			Assert.AreEqual(string.Empty, totalAndPossibleError.ErrorMessage);
			Assert.AreEqual(1m, totalAndPossibleError.TotalOrder);

			int orderNumber = 1;
			excludedSubtickets = new ExcludeSubtickets();
			nextDatesAccumulator = new NextDatesAccumulator(lotteries, domain, now, pickNumber, dates, states, hours, withFireballs, strIsPresentPlayerBeforeToCloseStore, strUseNextDate);
			var order = company.CreateTicketOrder(lotteries, player, states, dates, withFireballs, numbers, selectionMode, ticketAmount, includedNumbersForInput, excludedSubtickets, gameType, myOrder, ticketPick3StraightProd, nextDatesAccumulator, domain);
			company.AddOrders(myOrder);
			int theLowestBetId = 1;
			int theHighestBetId = 1;
			var ticketNumber = *********;
			company.PurchaseTickets(false, myOrder, now, ticketNumber, domain, theLowestBetId, theHighestBetId, orderNumber);
			lotteries.CreateWagers(false, theLowestBetId, theHighestBetId, 1, 2, ticketNumber, orderNumber, now);

			var toWins = risk.GetRiskPerLottery(int.Parse(pickNumber), lottery).ToWinPerSubticketsAt(drawDate);
			Assert.AreEqual(1, toWins.Count());
			var toWin = toWins.ElementAt(0);
			Assert.AreEqual(450, toWin.Amount);
			Assert.AreEqual("121", toWin.Number);

			//#2
			ticketAmount = 2m;
			includedNumbersForInput = "121";
			orderNumber = 2;
			myOrder = company.NewOrder(customer, orderNumber, Coinage.Coin(Currencies.CODES.USD), 2);
			excludedSubtickets = new ExcludeSubtickets();
			nextDatesAccumulator = new NextDatesAccumulator(lotteries, domain, now, pickNumber, dates, states, hours, withFireballs, strIsPresentPlayerBeforeToCloseStore, strUseNextDate);
			toWinAccumulator = new ToWinAccumulator();
			totalAndPossibleError = new TotalOrderAndExclusionsByToWin(2);
			totalAndPossibleError = company.TotalTicketOrderAndExclusionsByToWin(domain, states, dates, withFireballs, numbers, selectionMode, ticketAmount, includedNumbersForInput, gameType, excludedSubtickets, ticketPick3StraightProd, nextDatesAccumulator, toWinAccumulator, totalAndPossibleError, "USD", string.Empty, applyToleranceFactor: false, issued: string.Empty, now);
			Assert.AreEqual(MSG_COMBINATION_EXCEEDED, totalAndPossibleError.ErrorMessage);
			Assert.AreEqual(0m, totalAndPossibleError.TotalOrder);
		}

		[TestMethod]
		public void TotalTicketOrderAndExclusionsByToWin_ApplyDisincentives()
		{
			Company company = new Company();
			company.Sales.CreateStore(1, "Test Store").MakeCurrent();
			company.Accounting = new MockAccounting();
			var lotteries = company.Lotto900();
			CurrenciesTest.AddCurrencies();

			DateTime now = new DateTime(2019, 04, 17, 9, 00, 00);
			State state = lotteries.GetOrCreateState("GA", "Georgia", now, "Bart");
			LotteryPick<Pick3> lottery = (LotteryPick<Pick3>)lotteries.GetOrCreateLottery(3, state);
			for (int i = 0; i <= 6; i++) lottery.Every(10, 20, i, now, "Bart");

			var drawDate = new DateTime(2019, 04, 17, 10, 20, 00);
			var schedule = lottery.GetScheduleOrNullFor(drawDate);
			var scheduleDescription = "Georgia Morning";
			schedule.UpdateDescription(false, scheduleDescription);

			Player player = company.GetOrCreateCustomerById("*********").Player;
			string states = "GA";
			string hours = "10:20 AM";
			string strIsPresentPlayerBeforeToCloseStore = "true";
			string strUseNextDate = "true";
			string dates = "04/17/2019";
			string numbers = "";
			string selectionMode = "MultipleInputSingleAmount";
			string pickNumber = "3";
			decimal ticketAmount = 1m;
			string includedNumbersForInput = "121";
			string gameType = "Boxed";

			Product ticketPick3StraightProd = lotteries.GetOrCreateProductById(1);
			ticketPick3StraightProd.Description = "Ticket pick 3 straight";
			ticketPick3StraightProd.Price = 1;
			Domain domain = company.Sales.CreateDomain(false, 1, "localhost", Agents.INSIDER);
			var maxToWin = 300m;
			lotteries.Risks.Risk.UpdateToWin(int.Parse(pickNumber), maxToWin);

			//#1
			//without disincentive: no error
			ExcludeSubtickets excludedSubtickets = new ExcludeSubtickets();
			Customer customer = company.CustomerByPlayer(player);
			var orderNumber = 1;
			OrderCart myOrder = company.NewOrder(customer, orderNumber, Coinage.Coin(Currencies.CODES.USD), 1);

			var nextDatesAccumulator = new NextDatesAccumulator(lotteries, domain, now, pickNumber, dates, states, hours, strIsPresentPlayerBeforeToCloseStore, strUseNextDate);
			var toWinAccumulator = new ToWinAccumulator();
			var totalAndPossibleError = new TotalOrderAndExclusionsByToWin(1);
			totalAndPossibleError = company.TotalTicketOrderAndExclusionsByToWin(domain, states, dates, numbers, selectionMode, ticketAmount, includedNumbersForInput, gameType, ticketPick3StraightProd, nextDatesAccumulator, toWinAccumulator, totalAndPossibleError, "USD", string.Empty);
			Assert.AreEqual(string.Empty, totalAndPossibleError.ErrorMessage);
			Assert.AreEqual(1m, totalAndPossibleError.TotalOrder);

			//with disincentive: error
			var disincentives = lotteries.Risks.Disincentives;
			disincentives.NewDisincentive(schedule, PrizeCriteriaIds.PRIZE_CRITERIA_ALL_NUMBERS_ARE_THE_SAME, RuleType.Boxed, 66);
			disincentives.NewDisincentive(schedule, PrizeCriteriaIds.PRIZE_CRITERIA_3_WAY, RuleType.Boxed, 33);

			nextDatesAccumulator = new NextDatesAccumulator(lotteries, domain, now, pickNumber, dates, states, hours, strIsPresentPlayerBeforeToCloseStore, strUseNextDate);
			toWinAccumulator = new ToWinAccumulator();
			totalAndPossibleError = new TotalOrderAndExclusionsByToWin(1);
			totalAndPossibleError = company.TotalTicketOrderAndExclusionsByToWin(domain, states, dates, numbers, selectionMode, ticketAmount, includedNumbersForInput, gameType, excludedSubtickets, ticketPick3StraightProd, nextDatesAccumulator, toWinAccumulator, totalAndPossibleError, "USD", string.Empty);
			Assert.AreEqual(MSG_COMBINATION_EXCEEDED, totalAndPossibleError.ErrorMessage);
			Assert.AreEqual(0m, totalAndPossibleError.TotalOrder);

			var subticket = new SubTicket<IPick>(1, 2, 1);
			var disincentivatedRisk = disincentives.ApplyTo(schedule, PrizeCriteriaIds.PRIZE_CRITERIA_3_WAY, RuleType.Boxed, int.Parse(pickNumber), maxToWin);
			Assert.AreEqual(99.0m, Math.Round(disincentivatedRisk, 2));

			var prizes = lottery.PicksLotteryGame.Prizes;
			var prizeCriteria = prizes.WayOfSubticket(TicketType.P3B, subticket);
			decimal prize = prizes.Prize(TicketType.P3B, prizeCriteria);
			prize *= ticketAmount;
			Assert.AreEqual(300, prize);
		}

		[TestMethod]
		public void bug8707()
		{
			QueueInMemory queue = new QueueInMemory();
			Integration.Kafka = queue;
			Integration.SavePreviousSettings(tempUseKafka: false, tempUseKafkaForAuto: false);
			CurrenciesTest.AddCurrencies();
			Company company = new Company();
			company.Sales.CreateStore(1, "Test Store").MakeCurrent();
			company.Accounting = new MockAccounting();
			var lotteries = company.Lotto900();

			DateTime now = new DateTime(2023, 11, 23, 9, 00, 00);
			State state = lotteries.GetOrCreateState("OH", "Ohio", now, "Bart");
			var lottery2 = (LotteryPick<Pick2>)lotteries.GetOrCreateLottery(2, state);
			for (int i = 0; i <= 6; i++) lottery2.Every(12, 14, i, now, "Bart");

			var ticketPick3StraightProd = lotteries.GetOrCreateProductById(5);
			ticketPick3StraightProd.Description = "Ticket pick 3 straight";
			ticketPick3StraightProd.Price = 1;
			bool itIsThePresent = true;

			var domain = company.Sales.CreateDomain(false, 1, "localhost", Agents.INSIDER);
			var drawDate = new DateTime(2023, 11, 24, 12, 14, 00);
			var schedule = lottery2.GetScheduleOrNullFor(drawDate);
			var player = company.GetOrCreateCustomerById("406827").Player;

			string states = "OH";
			string hours = "12:14 PM";
			string strIsPresentPlayerBeforeToCloseStore = "true";
			string strUseNextDate = "true";
			string dates = "11/24/2023";
			string withFireballs = "false";
			string numbers = "";
			string selectionMode = "MultipleInputSingleAmount";
			string pickNumber = "2";
			decimal ticketAmount = 5.5m;
			string includedNumbersForInput = "00";
			string gameType = "Boxed";

			domain = company.Sales.DomainFrom("localhost");
			var riskProfileCloned = lotteries.RiskProfiles.CreateRiskProfile(domain, "RiskProfile test");
			var risks = lotteries.RiskProfiles.GetRiskProfile(domain).Risks.Risk;
			var risk2 = risks.GetRiskPerLottery(2, lottery2);
			var maxToWin = 900m;
			risks.UpdateToWin(domain, 2, maxToWin);

			var maxBetAmount = risk2.MaxAvailableBetAmount(schedule, PrizeCriteriaIds.PRIZE_CRITERIA_ALL_NUMBERS_ARE_THE_SAME, RuleType.Straight, includedNumbersForInput, drawDate, domain);
			Assert.AreEqual(10m, maxBetAmount);

			player = company.Players.SearchPlayer("zTlBNBvsOf1nLb20zg8SSw==");
			var customer = company.CustomerByPlayer(player);

			//1
			var orderNumber = 30001;
			OrderCart myOrder = (OrderCart)company.NewOrder(customer, orderNumber, Currencies.CODES.USD);

			lotteries.NextDatesAccumulator.Calculate(domain, now, pickNumber, dates, states, hours, withFireballs);
			var set = new PrizesSet(new List<string>() { "489.5" });
			myOrder = (OrderCart)company.CreateTicketOrderWithMultipleAuthorization(player, states, dates, withFireballs, selectionMode, 5.5m, new List<string> { includedNumbersForInput }, "B", myOrder, ticketPick3StraightProd, lotteries.NextDatesAccumulator, domain);
			company.AddOrders(myOrder);
			Assert.AreEqual(5.5m, myOrder.Total());

			var lowReference = 100001;
			company.PurchaseTickets(itIsThePresent, myOrder, now, new List<int> { ********* }, domain, lowReference, orderNumber, "Artemis_ThirdParty_Deposit_USD");

			maxBetAmount = risk2.MaxAvailableBetAmount(schedule, PrizeCriteriaIds.PRIZE_CRITERIA_ALL_NUMBERS_ARE_THE_SAME, RuleType.Straight, includedNumbersForInput, drawDate, domain);
			Assert.AreEqual(4.5m, maxBetAmount);

			//2
			//without disincentive: no error
			ExcludeSubtickets excludedSubtickets = new ExcludeSubtickets();
			orderNumber = 30002;
			myOrder = (OrderCart)company.NewOrder(customer, orderNumber, Currencies.CODES.USD);

			var nextDatesAccumulator = new NextDatesAccumulator(lotteries, domain, now, pickNumber, dates, states, hours, strIsPresentPlayerBeforeToCloseStore, strUseNextDate);
			var toWinAccumulator = new ToWinAccumulator();
			var totalAndPossibleError = new TotalOrderAndExclusionsByToWin(1);
			totalAndPossibleError = company.TotalTicketOrderAndExclusionsByToWin(domain, states, dates, numbers, selectionMode, ticketAmount, includedNumbersForInput, gameType, ticketPick3StraightProd, nextDatesAccumulator, toWinAccumulator, totalAndPossibleError, "USD", string.Empty);
			Assert.AreEqual("Maximum amount per combination exceeded", totalAndPossibleError.ErrorMessage);

			//previous validation must not leave to make this purchase
			orderNumber = 30002;
			myOrder = (OrderCart)company.NewOrder(customer, orderNumber, Currencies.CODES.USD);

			lotteries.NextDatesAccumulator.Calculate(domain, now, "2", "11/24/2023", "OH", "12:14 PM", "False");
			set = new PrizesSet(new List<string>() { "489.5" });
			myOrder = (OrderCart)company.CreateTicketOrderWithMultipleAuthorization(player, "OH", "11/24/2023", "false", "MultipleInputSingleAmount", 5.5m, new List<string> { includedNumbersForInput }, "B", myOrder, ticketPick3StraightProd, lotteries.NextDatesAccumulator, domain);
			company.AddOrders(myOrder);
			Assert.AreEqual(5.5m, myOrder.Total());

			lowReference = 100002;
			company.PurchaseTickets(itIsThePresent, myOrder, now, new List<int> { ********* }, domain, lowReference, orderNumber, "Artemis_ThirdParty_Deposit_USD");
			maxBetAmount = risk2.MaxAvailableBetAmount(schedule, PrizeCriteriaIds.PRIZE_CRITERIA_ALL_NUMBERS_ARE_THE_SAME, RuleType.Straight, includedNumbersForInput, drawDate, domain);
			Assert.AreEqual(-1m, maxBetAmount);
		}

		[TestMethod]
		public void PurchaseTickets_BuyingMultipleInputMultipleAmount()
		{
			QueueInMemory queue = new QueueInMemory();
			Integration.Kafka = queue;
			Integration.SavePreviousSettings(tempUseKafka: false, tempUseKafkaForAuto: false);

			Company company = new Company();
			company.Sales.CreateStore(1, "Test Store").MakeCurrent();
			company.Accounting = new MockAccounting();
			var lotteries = company.Lotto900();

			for (int index = 1; index < 9; index++)
			{
				Product ticketPick3StraightProd = lotteries.GetOrCreateProductById(index);
				ticketPick3StraightProd.Description = "Ticket pick 3 straight";
				ticketPick3StraightProd.Price = 1;
			}

			DateTime now = new DateTime(2023, 11, 23, 9, 00, 00);
			State state = lotteries.GetOrCreateState("OH", "Ohio", now, "Bart");
			var lottery3 = (LotteryPick<Pick3>)lotteries.GetOrCreateLottery(3, state);
			for (int i = 0; i <= 6; i++) lottery3.Every(12, 14, i, now, "Bart");
			var lottery4 = (LotteryPick<Pick4>)lotteries.GetOrCreateLottery(4, state);
			for (int i = 0; i <= 6; i++) lottery4.Every(12, 14, i, now, "Bart");

			state = lotteries.GetOrCreateState("GA", "Georgia", now, "Bart");
			lottery3 = (LotteryPick<Pick3>)lotteries.GetOrCreateLottery(3, state);
			for (int i = 0; i <= 6; i++) lottery3.Every(12, 14, i, now, "Bart");
			for (int i = 0; i <= 6; i++) lottery3.Every(18, 44, i, now, "Bart");
			lottery4 = (LotteryPick<Pick4>)lotteries.GetOrCreateLottery(4, state);
			for (int i = 0; i <= 6; i++) lottery4.Every(12, 14, i, now, "Bart");
			for (int i = 0; i <= 6; i++) lottery4.Every(18, 44, i, now, "Bart");

			var ticketPick4BoxedProd = lotteries.GetOrCreateProductById(1);
			ticketPick4BoxedProd.Description = "Ticket pick 3 straight";
			ticketPick4BoxedProd.Price = 1;
			bool itIsThePresent = true;
			var domain = company.Sales.CreateDomain(false, 1, "localhost", Agents.INSIDER);
			var player = company.GetOrCreateCustomerById("406827").Player;

			domain = company.Sales.DomainFrom("localhost");
			player = company.Players.SearchPlayer("zTlBNBvsOf1nLb20zg8SSw==");
			var customer = company.CustomerByPlayer(player);
			var orderNumber = 30001;
			OrderCart myOrder = (OrderCart)company.NewOrder(customer, orderNumber, Currencies.CODES.USD);

			lotteries.NextDatesAccumulator.Calculate(domain, now, "3", "11/24/2023", "GA,OH,GA,OH,GA,GA", "6:44 PM,12:14 PM,12:14 PM,12:14 PM,12:14 PM,6:44 PM", "False,False,False,False,False,False");
			var orderBuilder = company.PrepareTicketOrderWithMultipleAuthorization(player, "MultipleInputSingleAmount", "S", myOrder, lotteries.NextDatesAccumulator, domain);
			orderBuilder.Add(new List<string> { "000" }, "GA", false, 0.5m);//sustituir por otro linea de compra de TC.
			orderBuilder.Add(new List<string> { "000", "100" }, new List<string> { "OH", "GA" }, new List<bool> { false, false }, 1m);
			orderBuilder.Add(new List<string> { "100" }, new List<string> { "GA" }, new List<bool> { false }, 1m);
			myOrder = (OrderCart)orderBuilder.Commit();
			company.AddOrders(myOrder);
			Assert.AreEqual(5.5m, myOrder.Total());

			var lowReference = 100001;
			company.PurchaseTickets(itIsThePresent, myOrder, now, new List<int> { *********, *********, *********, *********, *********, ********* }, domain, lowReference, orderNumber, "Artemis_ThirdParty_Deposit_USD");

			var tickets = lotteries.FindTicketsMatchingWith(new List<int> { *********, ********* });
			Assert.AreEqual(2, tickets.Count());

			tickets = lotteries.FindTicketsInRange(100001, 100006);
			Assert.AreEqual(6, tickets.Count());
		}

		[TestMethod]
		public void PurchaseTickets_BuyingPickify()
		{
			QueueInMemory queue = new QueueInMemory();
			Integration.Kafka = queue;
			Integration.SavePreviousSettings(tempUseKafka: false, tempUseKafkaForAuto: false);

			Company company = new Company();
			company.Sales.CreateStore(1, "Test Store").MakeCurrent();
			company.Accounting = new MockAccounting();
			var lotteries = company.Lotto900();
			CurrenciesTest.AddCurrencies();

			for (int index = 1; index < 9; index++)
			{
				Product ticketPick3StraightProd = lotteries.GetOrCreateProductById(index);
				ticketPick3StraightProd.Description = "Ticket pick 3 straight";
				ticketPick3StraightProd.Price = 1;
			}

			DateTime now = new DateTime(2023, 11, 23, 9, 00, 00);
			State state = lotteries.GetOrCreateState("OH", "Ohio", now, "Bart");
			var lottery3 = (LotteryPick<Pick3>)lotteries.GetOrCreateLottery(3, state);
			for (int i = 0; i <= 6; i++) lottery3.Every(12, 14, i, now, "Bart");
			var lottery4 = (LotteryPick<Pick4>)lotteries.GetOrCreateLottery(4, state);
			for (int i = 0; i <= 6; i++) lottery4.Every(12, 14, i, now, "Bart");

			state = lotteries.GetOrCreateState("GA", "Georgia", now, "Bart");
			lottery3 = (LotteryPick<Pick3>)lotteries.GetOrCreateLottery(3, state);
			for (int i = 0; i <= 6; i++) lottery3.Every(12, 14, i, now, "Bart");
			lottery4 = (LotteryPick<Pick4>)lotteries.GetOrCreateLottery(4, state);
			for (int i = 0; i <= 6; i++) lottery4.Every(12, 14, i, now, "Bart");
			for (int i = 0; i <= 6; i++) lottery4.Every(18, 44, i, now, "Bart");

			var ticketPick4BoxedProd = lotteries.GetOrCreateProductById(1);
			ticketPick4BoxedProd.Description = "Ticket pick 3 straight";
			ticketPick4BoxedProd.Price = 1;
			bool itIsThePresent = true;
			var domain = company.Sales.CreateDomain(false, 1, "localhost", Agents.INSIDER);
			var player = company.GetOrCreateCustomerById("406827").Player;

			domain = company.Sales.DomainFrom("localhost");
			player = company.Players.SearchPlayer("zTlBNBvsOf1nLb20zg8SSw==");
			var customer = company.CustomerByPlayer(player);
			var orderNumber = 30001;
			OrderCart myOrder = (OrderCart)company.NewOrder(customer, orderNumber, Currencies.CODES.USD);

			lotteries.NextDatesAccumulator.Calculate(domain, now, new List<int> { 3, 3, 4, 4 }, "11/24/2023", new List<string> { "GA", "OH", "GA", "GA" }, new List<string> { "12:14 PM", "12:14 PM", "12:14 PM", "6:44 PM" }, new List<string> { "False", "False", "False", "False" }, new List<string> { "True", "True", "True", "True" }, new List<string> { "True", "True", "True", "True" });
			var orderBuilder = company.PrepareTicketOrderWithMultipleAuthorization(player, "MultipleInputSingleAmount", 0.25m, myOrder, lotteries.NextDatesAccumulator, domain);
			orderBuilder.AddStraight(new List<string> { "100", "101" });
			orderBuilder.AddStraight(new List<string> { "1000" });
			orderBuilder.AddBoxed(new List<string> { "2000", "2001" });
			myOrder = (OrderCart)orderBuilder.Commit();
			company.AddOrders(myOrder);
			Assert.AreEqual(2.5m, myOrder.Total());

			var lowReference = 100001;
			company.PurchaseTickets(itIsThePresent, myOrder, now, new List<int> { *********, *********, *********, *********, *********, *********, *********, *********, *********, ********* }, domain, lowReference, orderNumber, "Artemis_ThirdParty_Deposit_USD");

			var tickets = lotteries.FindTicketsMatchingWith(new List<int> { *********, ********* });
			Assert.AreEqual(2, tickets.Count());

			tickets = lotteries.FindTicketsInRange(100001, 100010);
			Assert.AreEqual(10, tickets.Count());
		}

		[TestMethod]
		public void PurchaseTickets_Performcmd_task8394()
		{
			Company company = new Company();
			company.Sales.CreateStore(1, "Test Store").MakeCurrent();
			company.Accounting = new MockAccounting();
			var lotteries = company.Lotto900();
			DateTime now = new DateTime(2023, 11, 23, 9, 00, 00);
			State state = lotteries.GetOrCreateState("OH", "Ohio", now, "Bart");
			var lottery = (LotteryPick<Pick3>)lotteries.GetOrCreateLottery(3, state);
			for (int i = 0; i <= 6; i++) lottery.Every(12, 14, i, now, "Bart");
			State state2 = lotteries.GetOrCreateState("GA", "Georgia", now, "Bart");
			var lottery2 = (LotteryPick<Pick3>)lotteries.GetOrCreateLottery(3, state2);
			for (int i = 0; i <= 6; i++) lottery2.Every(12, 14, i, now, "Bart");

			var ticketPick4BoxedProd = lotteries.GetOrCreateProductById(1);
			ticketPick4BoxedProd.Description = "Ticket pick 3 straight";
			ticketPick4BoxedProd.Price = 1;
			bool itIsThePresent = true;
			var domain = company.Sales.CreateDomain(false, 1, "localhost", Agents.INSIDER);
			var player = company.GetOrCreateCustomerById("406827").Player;

			domain = company.Sales.DomainFrom("localhost");
			player = company.Players.SearchPlayer("zTlBNBvsOf1nLb20zg8SSw==");
			var customer = company.CustomerByPlayer(player);
			var orderNumber = 30001;
			OrderCart myOrder = (OrderCart)company.NewOrder(customer, orderNumber, Currencies.CODES.USD);

			lotteries.NextDatesAccumulator.Calculate(domain, now, "3", "11/24/2023", "OH,GA", "12:14 PM,12:14 PM", "False, False");
			var set = new PrizesSet(new List<string>() { "37.25", "224.75", "74.75" });
			myOrder = (OrderCart)company.CreateTicketOrderWithMultipleAuthorization(player, "OH,GA", "11/24/2023", "false,false", "MultipleInputSingleAmount", 0.25m, new List<string> { "123", "111", "100", "110" }, "B", myOrder, ticketPick4BoxedProd, lotteries.NextDatesAccumulator, domain);
			company.AddOrders(myOrder);
			var lowReference = 100001;

			var authorizations = new AuthorizationsNumbers(*********, 8);
			company.PurchaseTickets(itIsThePresent, myOrder, now, authorizations, domain, lowReference, orderNumber, "Artemis_ThirdParty_Deposit_USD");
			var tickets = lotteries.FindTicketsMatchingWith(new List<int> { *********, ********* });
			Assert.AreEqual(2, tickets.Count());

			tickets = lotteries.FindTicketsInRange(100001, 100008);
			Assert.AreEqual(8, tickets.Count());
		}

		[TestMethod]
		public void PurchaseTickets_TopicForCustomers()
		{
			CurrenciesTest.AddCurrencies();
			Integration.UseKafka = true;
			QueueInMemory queue = new QueueInMemory();
			Integration.Kafka = queue;

			Company company = new Company();
			company.Sales.CreateStore(1, "Test Store").MakeCurrent();
			company.Accounting = new MockAccounting();
			var lotteries = company.Lotto900();
			DateTime now = new DateTime(2023, 11, 23, 9, 00, 00);
			State state = lotteries.GetOrCreateState("OH", "Ohio", now, "Bart");
			var lottery = (LotteryPick<Pick3>)lotteries.GetOrCreateLottery(3, state);
			for (int i = 0; i <= 6; i++) lottery.Every(12, 14, i, now, "Bart");

			var ticketPick4BoxedProd = lotteries.GetOrCreateProductById(1);
			ticketPick4BoxedProd.Description = "Ticket pick 3 straight";
			ticketPick4BoxedProd.Price = 1;
			bool itIsThePresent = true;
			var domain = company.Sales.CreateDomain(false, 1, "localhost", Agents.ARTEMIS);
			var accountNumber = "406827";
			var player = company.GetOrCreateCustomerById(accountNumber).Player;

			domain = company.Sales.DomainFrom("localhost");
			player = company.Players.SearchPlayer("zTlBNBvsOf1nLb20zg8SSw==");
			var customer = company.CustomerByPlayer(player);
			var orderNumber = 30001;
			OrderCart myOrder = (OrderCart)company.NewOrder(customer, orderNumber, Currencies.CODES.USD);

			lotteries.NextDatesAccumulator.Calculate(domain, now, "3", "11/24/2023", "OH", "12:14 PM");
			var set = new PrizesSet(new List<string>() { "37.25" });
			var amount = 0.25m;
			myOrder = (OrderCart)company.CreateTicketOrderWithMultipleAuthorization(player, "OH", "11/24/2023", "MultipleInputSingleAmount", amount, new List<string> { "123" }, "B", myOrder, ticketPick4BoxedProd, lotteries.NextDatesAccumulator, domain, set.Prizes(new List<int>() { 0 }));
			company.AddOrders(myOrder);
			var lowReference = 100001;

			var authorizations = new AuthorizationsNumbers(*********, 1);
			company.PurchaseTickets(itIsThePresent, myOrder, now, authorizations, domain, lowReference, orderNumber, "Artemis_ThirdParty_Deposit_USD");
			var tickets = lotteries.FindTicketsMatchingWith(new List<int> { ********* });
			Assert.AreEqual(1, tickets.Count());

			var count = queue.Count(Integration.Kafka.TopicForCustomers);
			Assert.AreEqual(1, count);
			var msg = queue.Dequeue(Integration.Kafka.TopicForCustomers).ToString();
			var customerSaleMessage = new CustomerSaleMessage(msg);
			Assert.AreEqual(string.Empty, customerSaleMessage.CustomerIdentifier);
			Assert.AreEqual(accountNumber, customerSaleMessage.AccountNumber);
			Assert.AreEqual((int)Agents.INSIDER, customerSaleMessage.AgentId);
			Assert.AreEqual(now, customerSaleMessage.Date);
			Assert.IsTrue(customerSaleMessage.HasStoreId);
			Assert.AreEqual(1, customerSaleMessage.StoreId);
			Assert.AreEqual(amount, customerSaleMessage.Amount.Value);
			Assert.AreEqual(1, customerSaleMessage.Domain);
		}

		[TestMethod]
		public void PurchaseTickets_Performcmd_task8394_Buying10000()
		{
			Company company = new Company();
			CurrenciesTest.AddCurrencies();
			company.Sales.CreateStore(1, "Test Store").MakeCurrent();
			company.Accounting = new MockAccounting();
			var lotteries = company.Lotto900();
			DateTime now = new DateTime(2023, 11, 23, 9, 00, 00);
			State state = lotteries.GetOrCreateState("OH", "Ohio", now, "Bart");
			var lottery = (LotteryPick<Pick3>)lotteries.GetOrCreateLottery(3, state);
			for (int i = 0; i <= 6; i++) lottery.Every(12, 14, i, now, "Bart");
			State state2 = lotteries.GetOrCreateState("GA", "Georgia", now, "Bart");
			var lottery2 = (LotteryPick<Pick3>)lotteries.GetOrCreateLottery(3, state2);
			for (int i = 0; i <= 6; i++) lottery2.Every(12, 14, i, now, "Bart");

			var ticketPick4BoxedProd = lotteries.GetOrCreateProductById(1);
			ticketPick4BoxedProd.Description = "Ticket pick 3 straight";
			ticketPick4BoxedProd.Price = 1;
			bool itIsThePresent = true;
			var domain = company.Sales.CreateDomain(false, 1, "localhost", Agents.INSIDER);
			var player = company.GetOrCreateCustomerById("406827").Player;

			domain = company.Sales.DomainFrom("localhost");
			player = company.Players.SearchPlayer("zTlBNBvsOf1nLb20zg8SSw==");
			var customer = company.CustomerByPlayer(player);
			var orderNumber = 30001;
			OrderCart myOrder = (OrderCart)company.NewOrder(customer, orderNumber, Currencies.CODES.USD);

			lotteries.NextDatesAccumulator.Calculate(domain, now, "3", "11/24/2023,11/25/2023,11/26/2023,11/27/2023,11/28/2023", "OH,GA", "12:14 PM,12:14 PM", "False,False");
			var set = new PrizesSet(new List<string>() { "37.25", "224.75", "74.75" });
			List<string> numbers = Enumerable.Range(0, 1000)
								 .Select(i => i.ToString("D3"))
								 .ToList();
			List<int> indexesForPrizes = new List<int>();
			for (int i = 0; i < 1000; i++)
			{
				string number = i.ToString("D3");
				if (number.Distinct().Count() == 1)
				{
					indexesForPrizes.Add(1);
				}
				else if (number.Distinct().Count() == 2)
				{
					indexesForPrizes.Add(2);
				}
				else
				{
					indexesForPrizes.Add(0);
				}
			}
			indexesForPrizes = indexesForPrizes.Concat(indexesForPrizes).ToList();
			indexesForPrizes = indexesForPrizes.Concat(indexesForPrizes).Concat(indexesForPrizes).Concat(indexesForPrizes).Concat(indexesForPrizes).ToList();
			myOrder = (OrderCart)company.CreateTicketOrderWithMultipleAuthorization(player, "OH,GA", "11/24/2023,11/25/2023,11/26/2023,11/27/2023,11/28/2023", "false,false", "MultipleInputSingleAmount", 0.25m, numbers, "B", myOrder, ticketPick4BoxedProd, lotteries.NextDatesAccumulator, domain);
			company.AddOrders(myOrder);
			var lowReference = 100001;

			var authorizations = new AuthorizationsNumbers(*********, 10000);
			company.PurchaseTickets(itIsThePresent, myOrder, now, authorizations, domain, lowReference, orderNumber, "Artemis_ThirdParty_Deposit_USD");

			var tickets = lotteries.FindTicketsInRange(100001, 110000);
			Assert.AreEqual(10000, tickets.Count());

			var lastTicketNumber = tickets.Last().Wagers.First().TicketNumber;
			Assert.AreEqual(*********, lastTicketNumber);
		}

		Stopwatch watch = new Stopwatch();
		[TestMethod]
		public void PurchaseTickets_Buying900_Performcmd_bug7688()
		{
			QueueInMemory queue = new QueueInMemory();
			Integration.Kafka = queue;
			Integration.SavePreviousSettings(tempUseKafka: false, tempUseKafkaForAuto: false);

			Company company = new Company();
			company.Sales.CreateStore(1, "Test Store").MakeCurrent();
			company.Accounting = new MockAccounting();
			var lotteries = company.Lotto900();
			CurrenciesTest.AddCurrencies();

			DateTime now = new DateTime(2023, 11, 23, 9, 00, 00);
			State state = lotteries.GetOrCreateState("OH", "Ohio", now, "Bart");
			var lottery = (LotteryPick<Pick4>)lotteries.GetOrCreateLottery(4, state);
			for (int i = 0; i <= 6; i++) lottery.Every(12, 14, i, now, "Bart");

			var ticketPick4BoxedProd = lotteries.GetOrCreateProductById(1);
			ticketPick4BoxedProd.Description = "Ticket pick 3 straight";
			ticketPick4BoxedProd.Price = 1;
			bool itIsThePresent = true;
			var domain = company.Sales.CreateDomain(false, 1, "localhost", Agents.INSIDER);
			var player = company.GetOrCreateCustomerById("406827").Player;

			domain = company.Sales.DomainFrom("localhost");
			player = company.Players.SearchPlayer("zTlBNBvsOf1nLb20zg8SSw==");
			var customer = company.CustomerByPlayer(player);
			var orderNumber = 30001;
			OrderCart myOrder = (OrderCart)company.NewOrder(customer, orderNumber, Currencies.CODES.USD);

			var watch = Stopwatch.StartNew();
			lotteries.NextDatesAccumulator.Calculate(domain, now, "4", "11/24/2023", "OH", "12:14 PM", "False", "True", "True");
			watch.Stop();
			var t1 = watch.ElapsedMilliseconds;

			watch = Stopwatch.StartNew();
			myOrder = (OrderCart)company.CreateTicketOrderWithMultipleAuthorization(player, "OH", "11/24/2023", "false", "MultipleInputSingleAmount", 0.25m, new List<string> { "1000", "1001", "1002", "1003", "1004", "1005", "1006", "1007", "1008", "1009", "1010", "1011", "1012", "1013", "1014", "1015", "1016", "1017", "1018", "1019", "1020", "1021", "1022", "1023", "1024", "1025", "1026", "1027", "1028", "1029", "1030", "1031", "1032", "1033", "1034", "1035", "1036", "1037", "1038", "1039", "1040", "1041", "1042", "1043", "1044", "1045", "1046", "1047", "1048", "1049", "1050", "1051", "1052", "1053", "1054", "1055", "1056", "1057", "1058", "1059", "1060", "1061", "1062", "1063", "1064", "1065", "1066", "1067", "1068", "1069", "1070", "1071", "1072", "1073", "1074", "1075", "1076", "1077", "1078", "1079", "1080", "1081", "1082", "1083", "1084", "1085", "1086", "1087", "1088", "1089", "1090", "1091", "1092", "1093", "1094", "1095", "1096", "1097", "1098", "1099", "1100", "1101", "1102", "1103", "1104", "1105", "1106", "1107", "1108", "1109", "1110", "1111", "1112", "1113", "1114", "1115", "1116", "1117", "1118", "1119", "1120", "1121", "1122", "1123", "1124", "1125", "1126", "1127", "1128", "1129", "1130", "1131", "1132", "1133", "1134", "1135", "1136", "1137", "1138", "1139", "1140", "1141", "1142", "1143", "1144", "1145", "1146", "1147", "1148", "1149", "1150", "1151", "1152", "1153", "1154", "1155", "1156", "1157", "1158", "1159", "1160", "1161", "1162", "1163", "1164", "1165", "1166", "1167", "1168", "1169", "1170", "1171", "1172", "1173", "1174", "1175", "1176", "1177", "1178", "1179", "1180", "1181", "1182", "1183", "1184", "1185", "1186", "1187", "1188", "1189", "1190", "1191", "1192", "1193", "1194", "1195", "1196", "1197", "1198", "1199", "1200", "1201", "1202", "1203", "1204", "1205", "1206", "1207", "1208", "1209", "1210", "1211", "1212", "1213", "1214", "1215", "1216", "1217", "1218", "1219", "1220", "1221", "1222", "1223", "1224", "1225", "1226", "1227", "1228", "1229", "1230", "1231", "1232", "1233", "1234", "1235", "1236", "1237", "1238", "1239", "1240", "1241", "1242", "1243", "1244", "1245", "1246", "1247", "1248", "1249", "1250", "1251", "1252", "1253", "1254", "1255", "1256", "1257", "1258", "1259", "1260", "1261", "1262", "1263", "1264", "1265", "1266", "1267", "1268", "1269", "1270", "1271", "1272", "1273", "1274", "1275", "1276", "1277", "1278", "1279", "1280", "1281", "1282", "1283", "1284", "1285", "1286", "1287", "1288", "1289", "1290", "1291", "1292", "1293", "1294", "1295", "1296", "1297", "1298", "1299", "1300", "1301", "1302", "1303", "1304", "1305", "1306", "1307", "1308", "1309", "1310", "1311", "1312", "1313", "1314", "1315", "1316", "1317", "1318", "1319", "1320", "1321", "1322", "1323", "1324", "1325", "1326", "1327", "1328", "1329", "1330", "1331", "1332", "1333", "1334", "1335", "1336", "1337", "1338", "1339", "1340", "1341", "1342", "1343", "1344", "1345", "1346", "1347", "1348", "1349", "1350", "1351", "1352", "1353", "1354", "1355", "1356", "1357", "1358", "1359", "1360", "1361", "1362", "1363", "1364", "1365", "1366", "1367", "1368", "1369", "1370", "1371", "1372", "1373", "1374", "1375", "1376", "1377", "1378", "1379", "1380", "1381", "1382", "1383", "1384", "1385", "1386", "1387", "1388", "1389", "1390", "1391", "1392", "1393", "1394", "1395", "1396", "1397", "1398", "1399", "1400", "1401", "1402", "1403", "1404", "1405", "1406", "1407", "1408", "1409", "1410", "1411", "1412", "1413", "1414", "1415", "1416", "1417", "1418", "1419", "1420", "1421", "1422", "1423", "1424", "1425", "1426", "1427", "1428", "1429", "1430", "1431", "1432", "1433", "1434", "1435", "1436", "1437", "1438", "1439", "1440", "1441", "1442", "1443", "1444", "1445", "1446", "1447", "1448", "1449", "1450", "1451", "1452", "1453", "1454", "1455", "1456", "1457", "1458", "1459", "1460", "1461", "1462", "1463", "1464", "1465", "1466", "1467", "1468", "1469", "1470", "1471", "1472", "1473", "1474", "1475", "1476", "1477", "1478", "1479", "1480", "1481", "1482", "1483", "1484", "1485", "1486", "1487", "1488", "1489", "1490", "1491", "1492", "1493", "1494", "1495", "1496", "1497", "1498", "1499", "1500", "1501", "1502", "1503", "1504", "1505", "1506", "1507", "1508", "1509", "1510", "1511", "1512", "1513", "1514", "1515", "1516", "1517", "1518", "1519", "1520", "1521", "1522", "1523", "1524", "1525", "1526", "1527", "1528", "1529", "1530", "1531", "1532", "1533", "1534", "1535", "1536", "1537", "1538", "1539", "1540", "1541", "1542", "1543", "1544", "1545", "1546", "1547", "1548", "1549", "1550", "1551", "1552", "1553", "1554", "1555", "1556", "1557", "1558", "1559", "1560", "1561", "1562", "1563", "1564", "1565", "1566", "1567", "1568", "1569", "1570", "1571", "1572", "1573", "1574", "1575", "1576", "1577", "1578", "1579", "1580", "1581", "1582", "1583", "1584", "1585", "1586", "1587", "1588", "1589", "1590", "1591", "1592", "1593", "1594", "1595", "1596", "1597", "1598", "1599", "1600", "1601", "1602", "1603", "1604", "1605", "1606", "1607", "1608", "1609", "1610", "1611", "1612", "1613", "1614", "1615", "1616", "1617", "1618", "1619", "1620", "1621", "1622", "1623", "1624", "1625", "1626", "1627", "1628", "1629", "1630", "1631", "1632", "1633", "1634", "1635", "1636", "1637", "1638", "1639", "1640", "1641", "1642", "1643", "1644", "1645", "1646", "1647", "1648", "1649", "1650", "1651", "1652", "1653", "1654", "1655", "1656", "1657", "1658", "1659", "1660", "1661", "1662", "1663", "1664", "1665", "1666", "1667", "1668", "1669", "1670", "1671", "1672", "1673", "1674", "1675", "1676", "1677", "1678", "1679", "1680", "1681", "1682", "1683", "1684", "1685", "1686", "1687", "1688", "1689", "1690", "1691", "1692", "1693", "1694", "1695", "1696", "1697", "1698", "1699", "1700", "1701", "1702", "1703", "1704", "1705", "1706", "1707", "1708", "1709", "1710", "1711", "1712", "1713", "1714", "1715", "1716", "1717", "1718", "1719", "1720", "1721", "1722", "1723", "1724", "1725", "1726", "1727", "1728", "1729", "1730", "1731", "1732", "1733", "1734", "1735", "1736", "1737", "1738", "1739", "1740", "1741", "1742", "1743", "1744", "1745", "1746", "1747", "1748", "1749", "1750", "1751", "1752", "1753", "1754", "1755", "1756", "1757", "1758", "1759", "1760", "1761", "1762", "1763", "1764", "1765", "1766", "1767", "1768", "1769", "1770", "1771", "1772", "1773", "1774", "1775", "1776", "1777", "1778", "1779", "1780", "1781", "1782", "1783", "1784", "1785", "1786", "1787", "1788", "1789", "1790", "1791", "1792", "1793", "1794", "1795", "1796", "1797", "1798", "1799", "1800", "1801", "1802", "1803", "1804", "1805", "1806", "1807", "1808", "1809", "1810", "1811", "1812", "1813", "1814", "1815", "1816", "1817", "1818", "1819", "1820", "1821", "1822", "1823", "1824", "1825", "1826", "1827", "1828", "1829", "1830", "1831", "1832", "1833", "1834", "1835", "1836", "1837", "1838", "1839", "1840", "1841", "1842", "1843", "1844", "1845", "1846", "1847", "1848", "1849", "1850", "1851", "1852", "1853", "1854", "1855", "1856", "1857", "1858", "1859", "1860", "1861", "1862", "1863", "1864", "1865", "1866", "1867", "1868", "1869", "1870", "1871", "1872", "1873", "1874", "1875", "1876", "1877", "1878", "1879", "1880", "1881", "1882", "1883", "1884", "1885", "1886", "1887", "1888", "1889", "1890", "1891", "1892", "1893", "1894", "1895", "1896", "1897", "1898", "1899", "1900", "1901", "1902", "1903", "1904", "1905", "1906", "1907", "1908", "1909", "1910", "1911", "1912", "1913", "1914", "1915", "1916", "1917", "1918", "1919", "1920", "1921" }, "B", myOrder, ticketPick4BoxedProd, lotteries.NextDatesAccumulator, domain);
			watch.Stop();
			var t2 = watch.ElapsedMilliseconds;
			company.AddOrders(myOrder);
			var lowReference = 100001;

			watch = Stopwatch.StartNew();
			company.PurchaseTickets(itIsThePresent, myOrder, now, new List<int> { *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, 124914128, 124914129, 124914130, 124914131, 124914132, 124914133, 124914134, 124914135, 124914136, 124914137, 124914138, 124914139, 124914140, 124914141, 124914142, 124914143, 124914144, 124914145, 124914146, 124914147, 124914148, 124914149, 124914150, 124914151, 124914152, 124914153, 124914154, 124914155, 124914156, 124914157, 124914158, 124914159, 124914160, 124914161, 124914162, 124914163, 124914164, 124914165, 124914166, 124914167, 124914168, 124914169, 124914170, 124914171, 124914172, 124914173, 124914174, 124914175, 124914176, 124914177, 124914178, 124914179, 124914180, 124914181, 124914182, 124914183, 124914184, 124914185, 124914186, 124914187, 124914188, 124914189, 124914190, 124914191, 124914192, 124914193, 124914194, 124914195, 124914196, 124914197, 124914198, 124914199, 124914200, 124914201, 124914202, 124914203, 124914204, 124914205, 124914206, 124914207, 124914208, 124914209, 124914210, 124914211, 124914212, 124914213, 124914214, 124914215, 124914216, 124914217, 124914218, 124914219, 124914220, 124914221, 124914222, 124914223, 124914224, 124914225, 124914226, 124914227, 124914228, 124914229, 124914230, 124914231, 124914232, 124914233, 124914234, 124914235, 124914236, 124914237, 124914238, 124914239, 124914240, 124914241, 124914242, 124914243, 124914244, 124914245, 124914246, 124914247, 124914248, 124914249, 124914250, 124914251, 124914252, 124914253, 124914254, 124914255, 124914256, 124914257, 124914258, 124914259, 124914260, 124914261, 124914262, 124914263, 124914264, 124914265, 124914266, 124914267, 124914268, 124914269, 124914270, 124914271, 124914272, 124914273, 124914274, 124914275, 124914276, 124914277, 124914278, 124914279, 124914280, 124914281, 124914282, 124914283, 124914284, 124914285, 124914286, 124914287, 124914288, 124914289, 124914290, 124914291, 124914292, 124914293, 124914294, 124914295, 124914296, 124914297, 124914298, 124914299, 124914300, 124914301, 124914302, 124914303, 124914304, 124914305, 124914306, 124914307, 124914308, 124914309, 124914310, 124914311, 124914312, 124914313, 124914314, 124914315, 124914316, 124914317, 124914318, 124914319, 124914320, 124914321, 124914322, 124914323, 124914324, 124914325, 124914326, 124914327, 124914328, 124914329, 124914330, 124914331, 124914332, 124914333, 124914334, 124914335, 124914336, 124914337, 124914338, 124914339, 124914340, 124914341, 124914342, 124914343, 124914344, 124914345, 124914346, 124914347, 124914348, 124914349, 124914350, 124914351, 124914352, 124914353, 124914354, 124914355, 124914356, 124914357, 124914358, 124914359, 124914360, 124914361, 124914362, 124914363, 124914364, 124914365, 124914366, 124914367, 124914368, 124914369, 124914370, 124914371, 124914372, 124914373, 124914374, 124914375, 124914376, 124914377, 124914378, 124914379, 124914380, 124914381, 124914382, 124914383, 124914384, 124914385, 124914386, 124914387, 124914388, 124914389, 124914390, 124914391, 124914392, 124914393, 124914394, 124914395, 124914396, 124914397, 124914398, 124914399, 124914400, 124914401, 124914402, 124914403, 124914404, 124914405, 124914406, 124914407, 124914408, 124914409, 124914410, 124914411, 124914412, 124914413, 124914414, 124914415, 124914416, 124914417, 124914418, 124914419, 124914420, 124914421, 124914422, 124914423, 124914424, 124914425, 124914426, 124914427, 124914428, 124914429, 124914430, 124914431, 124914432, 124914433, 124914434, 124914435, 124914436, 124914437, 124914438, 124914439, 124914440, 124914441, 124914442, 124914443, 124914444, 124914445, 124914446, 124914447, 124914448, 124914449, 124914450, 124914451, 124914452, 124914453, 124914454, 124914455, 124914456, 124914457, 124914458, 124914459, 124914460, 124914461, 124914462, 124914463, 124914464, 124914465, 124914466, 124914467, 124914468, 124914469, 124914470, 124914471, 124914472, 124914473, 124914474, 124914475, 124914476, 124914477, 124914478, 124914479, 124914480, 124914481, 124914482, 124914483, 124914484, 124914485, 124914486, 124914487, 124914488, 124914489, 124914490, 124914491, 124914492, 124914493, 124914494, 124914495, 124914496, 124914497, 124914498, 124914499, 124914500, 124914501, 124914502, 124914503, 124914504, 124914505, 124914506, 124914507, 124914508, 124914509, 124914510, 124914511, 124914512, 124914513, 124914514, 124914515, 124914516, 124914517, 124914518, 124914519, 124914520, 124914521, 124914522, 124914523, 124914524, 124914525, 124914526, 124914527, 124914528, 124914529, 124914530, 124914531, 124914532, 124914533, 124914534, 124914535, 124914536, 124914537, 124914538, 124914539, 124914540, 124914541, 124914542, 124914543, 124914544, 124914545, 124914546, 124914547, 124914548, 124914549, 124914550, 124914551, 124914552, 124914553, 124914554, 124914555, 124914556, 124914557, 124914558, 124914559, 124914560, 124914561, 124914562, 124914563, 124914564, 124914565, 124914566, 124914567, 124914568, 124914569, 124914570, 124914571, 124914572, 124914573, 124914574, 124914575, 124914576, 124914577, 124914578, 124914579, 124914580, 124914581, 124914582, 124914583, 124914584, 124914585, 124914586, 124914587, 124914588, 124914589, 124914590, 124914591, 124914592, 124914593, 124914594, 124914595, 124914596, 124914597, 124914598, 124914599, 124914600, 124914601, 124914602, 124914603, 124914604, 124914605, 124914606, 124914607, 124914608, 124914609, 124914610, 124914611, 124914612, 124914613, 124914614, 124914615, 124914616, 124914617, 124914618, 124914619, 124914620, 124914621, 124914622, 124914623, 124914624, 124914625, 124914626, 124914627, 124914628, 124914629, 124914630, 124914631, 124914632, 124914633, 124914634, 124914635, 124914636, 124914637, 124914638, 124914639, 124914640, 124914641, 124914642, 124914643, 124914644, 124914645, 124914646, 124914647, 124914648, 124914649, 124914650, 124914651, 124914652, 124914653, 124914654, 124914655, 124914656, 124914657, 124914658, 124914659, 124914660, 124914661, 124914662, 124914663, 124914664, 124914665, 124914666, 124914667, 124914668, 124914669, 124914670, 124914671, 124914672, 124914673, 124914674, 124914675, 124914676, 124914677, 124914678, 124914679, 124914680, 124914681, 124914682, 124914683, 124914684, 124914685, 124914686, 124914687, 124914688, 124914689, 124914690, 124914691, 124914692, 124914693, 124914694, 124914695, 124914696, 124914697, 124914698, 124914699, 124914700, 124914701, 124914702, 124914703, 124914704, 124914705, 124914706, 124914707, 124914708, 124914709, 124914710, 124914711, 124914712, 124914713, 124914714, 124914715, 124914716, 124914717, 124914718, 124914719, 124914720, 124914721, 124914722, 124914723, 124914724, 124914725, 124914726, 124914727, 124914728, 124914729, 124914730, 124914731, 124914732, 124914733, 124914734, 124914735, 124914736, 124914737, 124914738, 124914739, 124914740, 124914741, 124914742, 124914743, 124914744, 124914745, 124914746, 124914747, 124914748, 124914749, 124914750, 124914751, 124914752, 124914753, 124914754, 124914755, 124914756, 124914757, 124914758, 124914759, 124914760, 124914761, 124914762, 124914763, 124914764, 124914765, 124914766, 124914767, 124914768, 124914769, 124914770, 124914771, 124914772, 124914773, 124914774, 124914775, 124914776, 124914777, 124914778, 124914779, 124914780, 124914781, 124914782, 124914783, 124914784, 124914785, 124914786, 124914787, 124914788, 124914789, 124914790, 124914791, 124914792, 124914793, 124914794, 124914795, 124914796, 124914797, 124914798, 124914799, 124914800, 124914801, 124914802, 124914803, 124914804, 124914805, 124914806, 124914807, 124914808, 124914809, 124914810, 124914811, 124914812, 124914813, 124914814, 124914815, 124914816, 124914817, 124914818, 124914819, 124914820, 124914821, 124914822, 124914823, 124914824, 124914825, 124914826, 124914827, 124914828, 124914829, 124914830, 124914831, 124914832, 124914833, 124914834, 124914835, 124914836, 124914837, 124914838, 124914839, 124914840, 124914841, 124914842, 124914843, 124914844, 124914845, 124914846, 124914847, 124914848, 124914849, 124914850, 124914851, 124914852, 124914853, 124914854, 124914855, 124914856, 124914857, 124914858, 124914859, 124914860, 124914861, 124914862, 124914863, 124914864, 124914865, 124914866, 124914867, 124914868, 124914869, 124914870, 124914871, 124914872, 124914873, 124914874, 124914875, 124914876, 124914877, 124914878, 124914879, 124914880, 124914881, 124914882, 124914883, 124914884, 124914885, 124914886, 124914887, 124914888, 124914889, 124914890, 124914891, 124914892, 124914893, 124914894, 124914895, 124914896, 124914897, 124914898, 124914899, 124914900, 124914901, 124914902, 124914903, 124914904, 124914905, 124914906, 124914907, 124914908, 124914909, 124914910, 124914911, 124914912, 124914913, 124914914, 124914915, 124914916, 124914917, 124914918, 124914919, 124914920, 124914921, 124914922, 124914923, 124914924, 124914925, 124914926, 124914927, 124914928, 124914929, 124914930, 124914931, 124914932, 124914933, 124914934, 124914935, 124914936, 124914937, 124914938, 124914939, 124914940, 124914941, 124914942, 124914943, 124914944, 124914945, 124914946, 124914947, 124914948, 124914949, 124914950, 124914951, 124914952, 124914953, 124914954, 124914955, 124914956, 124914957, 124914958, 124914959, 124914960, *********, *********, *********, *********, ********* }, domain, lowReference, orderNumber, "Artemis_ThirdParty_Deposit_USD");
			watch.Stop();
			var t3 = watch.ElapsedMilliseconds;

			var tickets = lotteries.FindTicketsMatchingWith(new List<int> { *********, ********* });
			Assert.AreEqual(2, tickets.Count());

			tickets = lotteries.FindTicketsInRange(100001, 100922);
			Assert.AreEqual(922, tickets.Count());
		}

		[TestMethod]
		public void PurchaseTickets_Buying900_Performqry_bug7688()
		{
			Company company = new Company();
			company.Sales.CreateStore(1, "Test Store").MakeCurrent();
			company.Accounting = new MockAccounting();
			var lotteries = company.Lotto900();
			DateTime now = new DateTime(2023, 11, 23, 9, 00, 00);
			State state = lotteries.GetOrCreateState("OH", "Ohio", now, "Bart");
			var lottery = (LotteryPick<Pick4>)lotteries.GetOrCreateLottery(4, state);
			for (int i = 0; i <= 6; i++) lottery.Every(12, 14, i, now, "Bart");

			var ticketPick4BoxedProd = lotteries.GetOrCreateProductById(1);
			ticketPick4BoxedProd.Description = "Ticket pick 3 straight";
			ticketPick4BoxedProd.Price = 1;
			var domain = company.Sales.CreateDomain(false, 1, "localhost", Agents.INSIDER);
			var player = company.GetOrCreateCustomerById("406827").Player;

			var customer = company.CustomerByPlayerId("zTlBNBvsOf1nLb20zg8SSw==");
			player = customer.Player;
			domain = company.Sales.DomainFrom("localhost");
			lotteries.IsBetAmountValidFor(4, player, domain, 0.25m);
			var nextDatesAccumulator = new NextDatesAccumulator(lotteries, domain, now, "4", "11/24/2023", "OH", "12:14 PM", "True", "True");
			var riskAccumulator = new ToWinAccumulator();
			TotalOrderAndExclusionsByToWin totalOrderAndExclusionsByToWin = new TotalOrderByToWin();

			watch = Stopwatch.StartNew();
			ExcludeSubtickets excludedSubtickets = new ExcludeSubtickets();
			totalOrderAndExclusionsByToWin = company.TotalTicketOrderAndExclusionsByToWin(domain, "OH", "11/24/2023", "MultipleInputSingleAmount", 0.25m, new List<string> { "1000", "1001", "1002", "1003", "1004", "1005", "1006", "1007", "1008", "1009", "1010", "1011", "1012", "1013", "1014", "1015", "1016", "1017", "1018", "1019", "1020", "1021", "1022", "1023", "1024", "1025", "1026", "1027", "1028", "1029", "1030", "1031", "1032", "1033", "1034", "1035", "1036", "1037", "1038", "1039", "1040", "1041", "1042", "1043", "1044", "1045", "1046", "1047", "1048", "1049", "1050", "1051", "1052", "1053", "1054", "1055", "1056", "1057", "1058", "1059", "1060", "1061", "1062", "1063", "1064", "1065", "1066", "1067", "1068", "1069", "1070", "1071", "1072", "1073", "1074", "1075", "1076", "1077", "1078", "1079", "1080", "1081", "1082", "1083", "1084", "1085", "1086", "1087", "1088", "1089", "1090", "1091", "1092", "1093", "1094", "1095", "1096", "1097", "1098", "1099", "1100", "1101", "1102", "1103", "1104", "1105", "1106", "1107", "1108", "1109", "1110", "1111", "1112", "1113", "1114", "1115", "1116", "1117", "1118", "1119", "1120", "1121", "1122", "1123", "1124", "1125", "1126", "1127", "1128", "1129", "1130", "1131", "1132", "1133", "1134", "1135", "1136", "1137", "1138", "1139", "1140", "1141", "1142", "1143", "1144", "1145", "1146", "1147", "1148", "1149", "1150", "1151", "1152", "1153", "1154", "1155", "1156", "1157", "1158", "1159", "1160", "1161", "1162", "1163", "1164", "1165", "1166", "1167", "1168", "1169", "1170", "1171", "1172", "1173", "1174", "1175", "1176", "1177", "1178", "1179", "1180", "1181", "1182", "1183", "1184", "1185", "1186", "1187", "1188", "1189", "1190", "1191", "1192", "1193", "1194", "1195", "1196", "1197", "1198", "1199", "1200", "1201", "1202", "1203", "1204", "1205", "1206", "1207", "1208", "1209", "1210", "1211", "1212", "1213", "1214", "1215", "1216", "1217", "1218", "1219", "1220", "1221", "1222", "1223", "1224", "1225", "1226", "1227", "1228", "1229", "1230", "1231", "1232", "1233", "1234", "1235", "1236", "1237", "1238", "1239", "1240", "1241", "1242", "1243", "1244", "1245", "1246", "1247", "1248", "1249", "1250", "1251", "1252", "1253", "1254", "1255", "1256", "1257", "1258", "1259", "1260", "1261", "1262", "1263", "1264", "1265", "1266", "1267", "1268", "1269", "1270", "1271", "1272", "1273", "1274", "1275", "1276", "1277", "1278", "1279", "1280", "1281", "1282", "1283", "1284", "1285", "1286", "1287", "1288", "1289", "1290", "1291", "1292", "1293", "1294", "1295", "1296", "1297", "1298", "1299", "1300", "1301", "1302", "1303", "1304", "1305", "1306", "1307", "1308", "1309", "1310", "1311", "1312", "1313", "1314", "1315", "1316", "1317", "1318", "1319", "1320", "1321", "1322", "1323", "1324", "1325", "1326", "1327", "1328", "1329", "1330", "1331", "1332", "1333", "1334", "1335", "1336", "1337", "1338", "1339", "1340", "1341", "1342", "1343", "1344", "1345", "1346", "1347", "1348", "1349", "1350", "1351", "1352", "1353", "1354", "1355", "1356", "1357", "1358", "1359", "1360", "1361", "1362", "1363", "1364", "1365", "1366", "1367", "1368", "1369", "1370", "1371", "1372", "1373", "1374", "1375", "1376", "1377", "1378", "1379", "1380", "1381", "1382", "1383", "1384", "1385", "1386", "1387", "1388", "1389", "1390", "1391", "1392", "1393", "1394", "1395", "1396", "1397", "1398", "1399", "1400", "1401", "1402", "1403", "1404", "1405", "1406", "1407", "1408", "1409", "1410", "1411", "1412", "1413", "1414", "1415", "1416", "1417", "1418", "1419", "1420", "1421", "1422", "1423", "1424", "1425", "1426", "1427", "1428", "1429", "1430", "1431", "1432", "1433", "1434", "1435", "1436", "1437", "1438", "1439", "1440", "1441", "1442", "1443", "1444", "1445", "1446", "1447", "1448", "1449", "1450", "1451", "1452", "1453", "1454", "1455", "1456", "1457", "1458", "1459", "1460", "1461", "1462", "1463", "1464", "1465", "1466", "1467", "1468", "1469", "1470", "1471", "1472", "1473", "1474", "1475", "1476", "1477", "1478", "1479", "1480", "1481", "1482", "1483", "1484", "1485", "1486", "1487", "1488", "1489", "1490", "1491", "1492", "1493", "1494", "1495", "1496", "1497", "1498", "1499", "1500", "1501", "1502", "1503", "1504", "1505", "1506", "1507", "1508", "1509", "1510", "1511", "1512", "1513", "1514", "1515", "1516", "1517", "1518", "1519", "1520", "1521", "1522", "1523", "1524", "1525", "1526", "1527", "1528", "1529", "1530", "1531", "1532", "1533", "1534", "1535", "1536", "1537", "1538", "1539", "1540", "1541", "1542", "1543", "1544", "1545", "1546", "1547", "1548", "1549", "1550", "1551", "1552", "1553", "1554", "1555", "1556", "1557", "1558", "1559", "1560", "1561", "1562", "1563", "1564", "1565", "1566", "1567", "1568", "1569", "1570", "1571", "1572", "1573", "1574", "1575", "1576", "1577", "1578", "1579", "1580", "1581", "1582", "1583", "1584", "1585", "1586", "1587", "1588", "1589", "1590", "1591", "1592", "1593", "1594", "1595", "1596", "1597", "1598", "1599", "1600", "1601", "1602", "1603", "1604", "1605", "1606", "1607", "1608", "1609", "1610", "1611", "1612", "1613", "1614", "1615", "1616", "1617", "1618", "1619", "1620", "1621", "1622", "1623", "1624", "1625", "1626", "1627", "1628", "1629", "1630", "1631", "1632", "1633", "1634", "1635", "1636", "1637", "1638", "1639", "1640", "1641", "1642", "1643", "1644", "1645", "1646", "1647", "1648", "1649", "1650", "1651", "1652", "1653", "1654", "1655", "1656", "1657", "1658", "1659", "1660", "1661", "1662", "1663", "1664", "1665", "1666", "1667", "1668", "1669", "1670", "1671", "1672", "1673", "1674", "1675", "1676", "1677", "1678", "1679", "1680", "1681", "1682", "1683", "1684", "1685", "1686", "1687", "1688", "1689", "1690", "1691", "1692", "1693", "1694", "1695", "1696", "1697", "1698", "1699", "1700", "1701", "1702", "1703", "1704", "1705", "1706", "1707", "1708", "1709", "1710", "1711", "1712", "1713", "1714", "1715", "1716", "1717", "1718", "1719", "1720", "1721", "1722", "1723", "1724", "1725", "1726", "1727", "1728", "1729", "1730", "1731", "1732", "1733", "1734", "1735", "1736", "1737", "1738", "1739", "1740", "1741", "1742", "1743", "1744", "1745", "1746", "1747", "1748", "1749", "1750", "1751", "1752", "1753", "1754", "1755", "1756", "1757", "1758", "1759", "1760", "1761", "1762", "1763", "1764", "1765", "1766", "1767", "1768", "1769", "1770", "1771", "1772", "1773", "1774", "1775", "1776", "1777", "1778", "1779", "1780", "1781", "1782", "1783", "1784", "1785", "1786", "1787", "1788", "1789", "1790", "1791", "1792", "1793", "1794", "1795", "1796", "1797", "1798", "1799", "1800", "1801", "1802", "1803", "1804", "1805", "1806", "1807", "1808", "1809", "1810", "1811", "1812", "1813", "1814", "1815", "1816", "1817", "1818", "1819", "1820", "1821", "1822", "1823", "1824", "1825", "1826", "1827", "1828", "1829", "1830", "1831", "1832", "1833", "1834", "1835", "1836", "1837", "1838", "1839", "1840", "1841", "1842", "1843", "1844", "1845", "1846", "1847", "1848", "1849", "1850", "1851", "1852", "1853", "1854", "1855", "1856", "1857", "1858", "1859", "1860", "1861", "1862", "1863", "1864", "1865", "1866", "1867", "1868", "1869", "1870", "1871", "1872", "1873", "1874", "1875", "1876", "1877", "1878", "1879", "1880", "1881", "1882", "1883", "1884", "1885", "1886", "1887", "1888", "1889", "1890", "1891", "1892", "1893", "1894", "1895", "1896", "1897", "1898", "1899", "1900", "1901", "1902", "1903", "1904", "1905", "1906", "1907", "1908", "1909", "1910", "1911", "1912", "1913", "1914", "1915", "1916", "1917", "1918", "1919", "1920", "1921" }, "B", excludedSubtickets, ticketPick4BoxedProd, nextDatesAccumulator, riskAccumulator, totalOrderAndExclusionsByToWin, "USD", string.Empty);
			watch.Stop();
			var t1 = watch.ElapsedMilliseconds;
			var existsDomainInMarketplace = company.Sales.CurrentStore.ExistsDomain(domain.Id);
		}

		[TestMethod]
		public void bug9329_purchasingPick2EndingAndBeginning()
		{
			QueueInMemory queue = new QueueInMemory();
			Integration.Kafka = queue;
			Integration.SavePreviousSettings(tempUseKafka: false, tempUseKafkaForAuto: false);

			Company company = new Company();
			company.Sales.CreateStore(1, "Test Store").MakeCurrent();
			company.Accounting = new MockAccounting();
			var lotteries = company.Lotto900();
			CurrenciesTest.AddCurrencies();

			DateTime now = new DateTime(2025, 2, 3, 9, 00, 00);
			LotteryTriz trizLottery = company.LotteryGamesPool.TrizLotteryGame.GetLottery();
			for (int i = 0; i <= 6; i++) trizLottery.Every(12, 15, i, now, "Game Admin");
			var pick2Ending = trizLottery.GetLotteryPick(2);
			var pick2Beginning = trizLottery.GetLotteryPick(2, false);
			var schedule2Ending = pick2Ending.GetSchedule(hour: 12, minute: 15);
			var schedule2Beginning = pick2Beginning.GetSchedule(hour: 12, minute: 15);
			var lotteryGame = company.LotteryGamesPool.TrizLotteryGame;

			var drawDate = new DateTime(2025, 2, 4, 12, 15, 00);
			var ticketPick4BoxedProd = lotteries.GetOrCreateProductById(6);
			ticketPick4BoxedProd.Description = "Ticket pick 3 straight";
			ticketPick4BoxedProd.Price = 1;
			bool itIsThePresent = true;
			var domain = company.Sales.CreateDomain(false, 1, "localhost", Agents.INSIDER);
			var player = company.GetOrCreateCustomerById("406827").Player;

			var riskProfile = company.LotteryGamesPool.RiskProfiles.GetRiskProfile("Preset Risk Profile");
			var risks = riskProfile.Risks.Risk;
			var riskPerLottery = risks.GetRiskPerLottery(2, trizLottery);
			var maxToWin = 45m;
			risks.UpdateToWin(domain, 2, maxToWin);
			var availableBetAmount = riskPerLottery.MaxAvailableBetAmount(schedule2Ending, PrizeCriteriaIds.PRIZE_CRITERIA_2_WAY, RuleType.Boxed, "36", drawDate, domain);
			Assert.AreEqual(0.5m, Math.Round(availableBetAmount, 2));

			var excludedSubtickets = new ExcludeSubtickets();
			var nextDatesAccumulator = new NextDatesAccumulator(lotteryGame, domain, now, "triz", "2/4/2025", "MX", "12:15 PM", "False", "False", "False");
			var riskAccumulator = new ToWinAccumulator();
			var totalOrderAndExclusionsByToWin = new TotalOrderAndExclusionsByToWin(1);
			totalOrderAndExclusionsByToWin = company.TotalTicketOrderAndExclusionsByToWin(domain, "MX", "2/4/2025", "False", "MultipleInputSingleAmount", 1m, new List<string> { "36" }, "B", excludedSubtickets, ticketPick4BoxedProd, nextDatesAccumulator, riskAccumulator, totalOrderAndExclusionsByToWin, "USD", "", applyToleranceFactor: false, issued: string.Empty, now);
			Assert.AreEqual(0, totalOrderAndExclusionsByToWin.Tickets.Count());
			Assert.AreEqual("", totalOrderAndExclusionsByToWin.ErrorMessage);

			var customer = company.CustomerByPlayer(player);
			var orderNumber = 300916;
			var myOrder = (OrderCart)company.NewOrder(customer, orderNumber, "USD");

			lotteryGame.NextDatesAccumulator.Calculate(domain, now, "triz", "2/4/2025", "MX,MX", "12:15 PM,12:15 PM", "False,False");
			myOrder = (OrderCart)company.PrepareTicketOrderWithMultipleAuthorization(player, "MultipleInputSingleAmount", "B", myOrder, lotteryGame.NextDatesAccumulator, domain).AddLeft(new string[] { "36" }, "MX", false, 1m).Add(new string[] { "36" }, "MX", false, 1m).Commit();
			company.AddOrders(myOrder);
			var lowReference = 1000205;
			var auths = new AuthorizationsNumbers(*********, 2);
			company.PurchaseTickets(itIsThePresent, myOrder, now, auths, domain, lowReference, orderNumber, "Artemis_ThirdParty_Deposit_USD");

			var subticket = new SubTicket<IPick>(new Pick2(3, 6));
			var total = riskPerLottery.AccumulatedToWinAt(drawDate);
			Assert.AreEqual(90m, total);
			total = riskPerLottery.AccumulatedToWinForNumber(subticket, drawDate);
			Assert.AreEqual(45m, total);
			availableBetAmount = riskPerLottery.MaxAvailableBetAmount(schedule2Ending, PrizeCriteriaIds.PRIZE_CRITERIA_2_WAY, RuleType.Boxed, "36", drawDate, domain);
			Assert.AreEqual(0m, Math.Round(availableBetAmount, 2));
			availableBetAmount = riskPerLottery.MaxAvailableBetAmount(schedule2Beginning, PrizeCriteriaIds.PRIZE_CRITERIA_2_WAY, RuleType.Boxed, "36", drawDate, domain);
			Assert.AreEqual(0m, Math.Round(availableBetAmount, 2));

			var toWinAccumulator = new ToWinAccumulator();
			var subtickets = riskPerLottery.SubticketsExceedingToWin((PrizesPicks)riskProfile.Prizes, schedule2Ending, drawDate, new List<SubTicket<IPick>> { subticket }, 1m, TicketType.P2B, domain, toWinAccumulator, false, applyToleranceFactor: false);
			Assert.AreEqual(1, subtickets.Count());

			excludedSubtickets = new ExcludeSubtickets();
			nextDatesAccumulator = new NextDatesAccumulator(lotteryGame, domain, now, "triz", "2/4/2025", "MX", "12:15 PM", "False", "False", "False");
			riskAccumulator = new ToWinAccumulator();
			totalOrderAndExclusionsByToWin = new TotalOrderAndExclusionsByToWin(1);
			totalOrderAndExclusionsByToWin = company.TotalTicketOrderAndExclusionsByToWin(domain, "MX", "2/4/2025", "False", "MultipleInputSingleAmount", 0.5m, new List<string> { "36" }, "B", excludedSubtickets, ticketPick4BoxedProd, nextDatesAccumulator, riskAccumulator, totalOrderAndExclusionsByToWin, "USD", "", applyToleranceFactor: false, issued: string.Empty, now);
			Assert.AreEqual(0, totalOrderAndExclusionsByToWin.Tickets.Count());
			Assert.AreEqual("Maximum amount per combination exceeded", totalOrderAndExclusionsByToWin.ErrorMessage);

			/*orderNumber = 300917;
            myOrder = (OrderCart)company.NewOrder(customer, orderNumber, "USD");
            lotteryGame.NextDatesAccumulator.Calculate(domain, now, "triz", "2/4/2025", "MX,MX", "12:15 PM,12:15 PM", "False,False");
            myOrder = (OrderCart)company.PrepareTicketOrderWithMultipleAuthorization(player, "MultipleInputSingleAmount", "B", myOrder, lotteryGame.NextDatesAccumulator, domain).AddLeft(new string[] { "36" }, "MX", false, 0.5m).Add(new string[] { "36" }, "MX", false, 0.5m).Commit();
            company.AddOrders(myOrder);
            lowReference = 1000206;
            auths = new AuthorizationsNumbers(*********, 2);
            company.PurchaseTickets(itIsThePresent, myOrder, now, auths, domain, lowReference, orderNumber, "Artemis_ThirdParty_Deposit_USD");*/
		}
	}
}