﻿using Elastic.Apm.Api;
using GamesEngine.RealTime.Events;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using town.connectors.drivers;

namespace GamesEngine.Settings
{
    public static class APMHelper
    {
        private static ITransaction transaction = Elastic.Apm.Agent.Tracer.CurrentTransaction ?? null;

        // Concurrent dictionary: key = span name
        private static ConcurrentDictionary<string, Span> spans = new ConcurrentDictionary<string, Span>();

        public static ITransaction StartTransaction(string transactionName, string transactionType)
        {
            transaction = Elastic.Apm.Agent.Tracer.StartTransaction(transactionName, transactionType);
            return transaction;
        }

        public static ITransaction CurrentTransaction()
        {
            transaction = Elastic.Apm.Agent.Tracer.CurrentTransaction ?? null;
            return transaction;
        }

        public static string CurrentTransactionId(ITransaction transaction)
        {
            if (transaction == null) return string.Empty;
            return transaction.Id;
        }

        public static void SetTransactionLabel(string key, string value)
        {
            if (transaction == null) return;
            transaction.SetLabel(key, value);
        }

        public static void StartSpan(string spanName, string spanType)
        {
            if (transaction == null || string.IsNullOrWhiteSpace(spanName)) return;

            if (!spans.TryGetValue(spanName, out var existingSpan))
            {
                var apmSpan = transaction.StartSpan(spanName, spanType);
                if (apmSpan != null)
                {
                    spans.TryAdd(spanName, new Span(apmSpan));
                }
            }
            else
            {
                existingSpan.StopWatchStart();
            }
        }

        public static void EndSpan(string spanName)
        {
            if (string.IsNullOrWhiteSpace(spanName)) return;

            if (spans.TryGetValue(spanName, out var span))
            {
                span.StopwatchEnd();
            }
        }

        public static void DisposeSpan(string spanName)
        {
            if (string.IsNullOrWhiteSpace(spanName)) return;

            if (spans.TryRemove(spanName, out var span))
            {
                span.EndSpan();
            }
        }

        public static void CaptureException(Exception ex, string parent = "")
        {
            if (transaction == null) return;
            transaction.CaptureException(ex, parent);
        }

        public static void EndTransaction()
        {
            if (transaction == null) return;
            transaction.End();
        }
    }

    public class Span
    {
        internal ISpan span { get; }
        internal int count;
        private Stopwatch watch;

        internal Span(ISpan span)
        {
            this.span = span ?? throw new ArgumentNullException(nameof(span));
            this.count = 0;
            this.watch = new Stopwatch();
        }

        internal void StopWatchStart()
        {
            watch.Start();
        }

        internal void StopwatchEnd()
        {
            count++;
            watch.Stop();
        }

        internal void EndSpan()
        {
            span.SetLabel("total_execution_count", count);
            span.SetLabel("total_execution_time_ms", watch.ElapsedMilliseconds);
            span.End();
        }
    }
}
